<template>
    <view class="demo-number-page">
        <list>
            <item title="基本用法" :note="String(val[0])">
                <link-number v-model="val[0]"/>
            </item>
            <item title="绑定值">
                <link-number v-model="val[0]"/>
            </item>
            <list-title>最大最小值</list-title>
            <item title="最大值10">
                <link-number v-model="val[0]" :max="10"/>
            </item>
            <item title="最小值-10">
                <link-number v-model="val[0]" :min="-10"/>
            </item>
            <item title="最大值10，最小值-10">
                <link-number v-model="val[0]" :min="-10" :max="10"/>
            </item>
            <list-title>计数器步长</list-title>
            <item title="step=10">
                <link-number v-model="val[0]" :step="10"/>
            </item>
            <item title="只能输入计数器步长的倍数（5的倍数）">
                <link-number v-model="val[0]" :step="5" stepStrictly/>
            </item>
            <item title="精确到小数点两位">
                <link-number v-model="val[0]" :precision="2"/>
            </item>
            <item title="隐藏操作按钮">
                <link-number v-model="val[0]" hideButton/>
            </item>
            <list-title>使用link-input展示</list-title>
            <item>
                <link-number v-model="val[0]" showType="input"/>
            </item>
            <list-title>禁用以及只读</list-title>
            <item title="禁用 showTyp=number">
                <link-number v-model="val[0]" disabled/>
            </item>
            <item title="禁用 showTyp=input">
                <link-number v-model="val[0]" showType="input" disabled/>
            </item>
            <item title="只读 showTyp=number">
                <link-number v-model="val[0]" readonly/>
            </item>
            <item title="只读 showTyp=input">
                <link-number v-model="val[0]" showType="input" readonly/>
            </item>

        </list>
    </view>
</template>

<script>
    export default {
        name: "demo-number-page",
        data() {
            return {
                val: {},
            }
        },
    }
</script>

<style lang="scss">

</style>