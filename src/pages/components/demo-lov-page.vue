<template>
    <view class="demo-lov-page">
        <list>
            <list-title>值列表过滤器</list-title>
            <item>
                {{'PotentialConsumer'|lov('ACCT_STAGE')}} - {{'FormalDealer'|lov('ACCT_STAGE')}} -{{'OrderConsumer'|lov('ACCT_STAGE')}}
            </item>
            <item>
                {{'09'|lov('INDUSTRY')}} - {{'20'|lov('INDUSTRY')}}
            </item>

            <item title="过滤数组">
                {{['09','20']|lov('INDUSTRY')}}
            </item>

            <list-title>link-lov：基本用法</list-title>
            <item title="基本选择" :note="formData.type1">
                <link-lov type="INDUSTRY" v-model="formData.type1"/>
            </item>
            <item title="无绑定值">
                <link-lov type="INDUSTRY"/>
            </item>
            <item title="排除值列表">
                <link-lov type="ACCT_STAGE" :excludeLovs="['FormalDealer','ShopConsumer']"/>
            </item>
            <item title="父值列表">
                <link-lov
                        type="REPORT_DIMENSION"
                        parentType="ORG_TYPE"
                        parentVal="BranchCompany"
                />
            </item>
            <item title="多选" :note="String(formData.multipleValue||'')">
                <link-lov type="ACCT_STAGE" v-model="formData.multipleValue" multiple/>
            </item>

            <list-title>link-lov：绑定显示值，而不是独立源代码</list-title>
            <item title="基本选择" :note="formData.val2">
                <link-lov type="INDUSTRY" v-model="formData.val2" reverse/>
            </item>
            <item title="排除值列表">
                <link-lov type="ACCT_STAGE" :excludeLovs="['正式','到店']" reverse/>
            </item>
            <item title="父值列表">
                <link-lov
                        type="REPORT_DIMENSION"
                        parentType="ORG_TYPE"
                        parentVal="BranchCompany"
                        reverse
                />
            </item>
            <item title="多选" :note="String(formData.val3||'')">
                <link-lov type="INDUSTRY" v-model="formData.val3" reverse multiple/>
            </item>

            <list-title>表单控制</list-title>
            <item title="禁用">
                <link-lov type="INDUSTRY" value="MiniApp" disabled/>
            </item>
            <item title="只读">
                <link-lov type="INDUSTRY" value="MiniApp" readonly/>
            </item>

        </list>
    </view>
</template>

<script>
    export default {
        name: "demo-lov-page",
        data() {
            return {
                formData: {
                    type1: '20',
                },
            }
        },
    }
</script>

<style lang="scss">

</style>