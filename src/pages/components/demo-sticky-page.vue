<template>
    <link-page class="demo-sticky-page">
        <list-title label="基本用法"/>
        <link-sticky top>
            <link-button block>吸附在顶部</link-button>
        </link-sticky>

        <link-sticky top :duration="122">
            <link-button block mode="text">吸附距离顶部 122rpx</link-button>
        </link-sticky>

        <list>
            <item v-for="item in 20" :title="`${item}`" :key="item"/>
            <item>
                {{JSON.stringify(showFlag)}}
                <link-switch v-model="showFlag" :trueValue="true" :falseValue="false"/>
            </item>
        </list>
        <link-sticky v-if="showFlag">
            <link-button mode="stroke" block>取消:{{JSON.stringify(showFlag)}}</link-button>
            <link-button block>确认</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    export default {
        name: "demo-sticky-page",
        data() {
            return {
                showFlag: true,
            }
        },
    }
</script>

<style lang="scss">
    .demo-sticky-page {
        .stick-box {
            position: sticky;
            top: 80px;
            z-index: 999;
        }
    }
</style>