<template>
    <link-page class="demo-calendar-page">
        <list>
            <item title="基本用法" :content="val[0]"/>
            <view style="background-color: white;color: #1F74FF;padding-bottom: 20px">
                <link-calendar v-model="val[0]">
                </link-calendar>
            </view>
            <item title="非卡片形式" :content="val[0]"/>
            <view style="background-color: white;color: #1F74FF">
                <link-calendar :card="false" v-model="val[0]">
                </link-calendar>
            </view>
            <item title="最大最小值" :content="val[0]"/>
            <view style="background-color: white;color: #1F74FF">
                <link-calendar v-model="val[0]" max="2020-11-25" min="2020-10-20">
                </link-calendar>
            </view>
            <item title="范围选择" :content="`start:${val[1]}`" :desc="`end:${val[2]}`"/>
            <view style="background-color: white;color: #1F74FF">
                <link-calendar
                        range
                        :start.sync="val[1]"
                        :end.sync="val[2]"
                        max="2020-11-25"
                        min="2020-10-20"
                >
                </link-calendar>
            </view>
        </list>
    </link-page>
</template>

<script>
    export default {
        name: "demo-calendar-page",
        data() {
            return {
                val: {
                    0: '2020-11-26',
                    1: '2020-11-11',
                    2: '2020-11-21',
                },
            }
        },
    }
</script>

<style lang="scss">

</style>