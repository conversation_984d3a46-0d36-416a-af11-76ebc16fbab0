<template>
    <view class="demo-picker-view-page">
        <link-form>
            <link-form-item label="客户分类">
                <link-address
                        :province.sync="formOption.acctType"
                        :city.sync="formOption.acctCategory"
                        :district.sync="formOption.subAcctType"
                        :custom="customOption"
                        :disabled="!cascadeData"
                />
            </link-form-item>
            {{JSON.stringify(formOption)}}
        </link-form>
    </view>
</template>

<script>

    export default {
        name: "demo-picker-view-page",
        components: {},
        data() {

            Promise.all([
                this.$lov.getLovByType('ACCT_TYPE'),
                this.$lov.getLovByType('ACCNT_CATEGORY'),
                this.$lov.getLovByType('SUB_ACCT_TYPE'),
            ]).then(([ACCT_TYPE, ACCNT_CATEGORY, SUB_ACCT_TYPE]) => {
                this.cascadeData = {
                    ACCT_TYPE: ACCT_TYPE.filter(item => item.val === 'Terminal' || item.val === 'Distributor'),
                    ACCNT_CATEGORY,
                    SUB_ACCT_TYPE
                };
                console.log(this.cascadeData)
            })

            return {
                cascadeData: null,
                formOption: {
                    acctType: null,
                    acctCategory: null,
                    subAcctType: null,
                },
            }
        },
        methods: {
            customOption(tag, parentValue) {
                console.log({
                    tag, parentValue
                })
                if (!this.cascadeData) {
                    return []
                }
                switch (tag) {
                    case 'p':
                        return this.cascadeData.ACCT_TYPE.map(lov => lov.name)
                    case 'c':
                        return this.cascadeData.ACCNT_CATEGORY.filter(item => item.parentName === parentValue).map(lov => lov.name)
                    case 'd':
                        return this.cascadeData.SUB_ACCT_TYPE.filter(item => item.parentName === parentValue).map(lov => lov.name)
                }
            },
        }
    }
</script>

<style lang="scss">
    .demo-picker-view-page {
        .column-item {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .my-custom-wx-component {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(black, 0.1);
        }
    }

    .custom-cover-bottom {
        color: white;
        height: 72px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
    }
</style>