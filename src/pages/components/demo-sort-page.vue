<template>
    <view class="demo-sort-page">
        <list>
            <item title="基本用法" :note="JSON.stringify(val[0]||'')" :arrow="false">
                <link-sort :options="sortOptions" v-model="val[0]"/>
            </item>
            <item title="无初始值" :note="JSON.stringify(val[1]||'')" :arrow="false">
                <link-sort :options="sortOptions" v-model="val[1]"/>
            </item>
            <item title="默认插槽" :note="JSON.stringify(val[2]||'')" :arrow="false">
                <link-sort :options="sortOptions" v-model="val[2]">
                    <link-button size="mini">open sort</link-button>
                </link-sort>
            </item>
            <item title="默认作用域插槽" :note="JSON.stringify(val[3]||'')" :arrow="false">
                <link-sort :options="sortOptions" v-model="val[3]">
                    <template slot-scope="{field,desc,option}">
                        <link-button size="mini">
                            {{!!option?option.label:'无排序'}}
                            <link-icon :icon="desc?'mp-desc':'mp-asc'" v-if="!!option"/>
                        </link-button>
                    </template>
                </link-sort>
            </item>
            <item title="手动控制打开以及关闭" :note="JSON.stringify(val[4]||'')" :arrow="false">
                <link-switch :trueValue="true" :falseValue="false" v-model="showFlag"/>
                <link-sort :options="sortOptions"
                           v-model="val[4]"
                           :show.sync="showFlag"
                           disabledHideOnSelectOption
                           :dropdownProps="{
                                disabledToggleOnTapReference:true,
                                disabledHideOnTapMask:true,
                            }"/>
            </item>
        </list>
    </view>
</template>

<script>
    export default {
        name: "demo-sort-page",
        data() {
            return {
                showFlag: false,
                val: {
                    0: {field: 'cost', desc: true},
                },
                sortOptions: [
                    {field: 'cost', label: '人天'},
                    {field: 'predict', label: '预算'},
                    {field: 'amount', label: '总价'},
                ]
            }
        },
    }
</script>

<style lang="scss">

</style>