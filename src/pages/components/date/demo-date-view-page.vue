<template>
    <view class="demo-date-view-page">
        <list>
            <list-title label="基础用法"/>
            <item :title="`双向绑定:${basicVal}`">
                <link-date v-model="basicVal"/>
            </item>

            <list-title label="年"/>
            <item :title="`双向绑定:${yearVal}`">
                <link-date view="Y" valueFormat="YYYY" displayFormat="YYYY年" v-model="yearVal"/>
            </item>
            <item title="有初始值">
                <link-date view="Y" valueFormat="YYYY" displayFormat="YYYY" value="2020"/>
            </item>
            <item title="无初始值">
                <link-date view="Y" valueFormat="YYYY" displayFormat="YYYY"/>
            </item>

            <list-title label="月"/>
            <item :title="`双向绑定:${monthVal}`">
                <link-date view="M" valueFormat="MM" displayFormat="MM月" v-model="monthVal"/>
            </item>
            <item title="有初始值">
                <link-date view="M" valueFormat="MM" displayFormat="MM" value="08"/>
            </item>
            <item title="无初始值">
                <link-date view="M" valueFormat="MM" displayFormat="MM"/>
            </item>

            <list-title label="年月"/>
            <item :title="`双向绑定:${yearMonthVal}`">
                <link-date view="YM" valueFormat="YYYYMM" displayFormat="YYYY年MM月" v-model="yearMonthVal"/>
            </item>
            <item title="有初始值">
                <link-date view="YM" valueFormat="YYYYMM" displayFormat="YYYY-MM" value="202008"/>
            </item>
            <item title="无初始值">
                <link-date view="YM" valueFormat="YYYYMM" displayFormat="YYYY-MM"/>
            </item>

            <list-title label="年月日"/>
            <item :title="`双向绑定:${dateVal}`">
                <link-date view="YMD" valueFormat="YYYYMMDD" displayFormat="YYYY年MM月DD日" v-model="dateVal"/>
            </item>
            <item title="有初始值">
                <link-date view="YMD" valueFormat="YYYYMMDD" displayFormat="YYYY-MM-DD" value="20200408"/>
            </item>
            <item title="无初始值">
                <link-date view="YMD" valueFormat="YYYYMMDD" displayFormat="YYYY-MM-DD"/>
            </item>

            <list-title label="年月日时"/>
            <item :title="`双向绑定:${dateHourVal}`">
                <link-date view="YMDH" valueFormat="YYYYMMDDHH" displayFormat="YYYY年MM月DD日 HH时" v-model="dateHourVal"/>
            </item>
            <item title="有初始值">
                <link-date view="YMDH" valueFormat="YYYYMMDDHH" displayFormat="YYYY-MM-DD HH" value="20200408"/>
            </item>
            <item title="无初始值">
                <link-date view="YMDH" valueFormat="YYYYMMDDHH" displayFormat="YYYY-MM-DD HH"/>
            </item>

            <list-title label="年月日时分"/>
            <item :title="`${YMDHm}`">
                <link-date view="YMDHm" valueFormat="YYYYMMDDHHmm" displayFormat="YYYY年MM月DD日 HH时mm分" v-model="YMDHm"/>
            </item>
            <item title="有初始值">
                <link-date view="YMDHm" valueFormat="YYYYMMDDHHmm" displayFormat="YYYY-MM-DD HH:mm" value="202004081533"/>
            </item>
            <item title="无初始值">
                <link-date view="YMDHm" valueFormat="YYYYMMDDHHmm" displayFormat="YYYY-MM-DD HH:mm"/>
            </item>

            <list-title label="年月日时分秒"/>
            <item :title="`${YMDHms}`">
                <link-date view="YMDHms" valueFormat="YYYYMMDDHHmmss" displayFormat="YYYY年MM月DD日 HH时mm分ss秒" v-model="YMDHms"/>
            </item>
            <item title="有初始值">
                <link-date view="YMDHms" valueFormat="YYYYMMDDHHmmss" displayFormat="YYYY-MM-DD HH:mm:ss" value="20200408153320"/>
            </item>
            <item title="无初始值">
                <link-date view="YMDHms" valueFormat="YYYYMMDDHHmmss" displayFormat="YYYY-MM-DD HH:mm:ss"/>
            </item>

            <list-title label="时"/>
            <item :title="`双向绑定:${H}`">
                <link-date view="H" valueFormat="HH" displayFormat="HH时" v-model="H"/>
            </item>
            <item title="有初始值">
                <link-date view="H" valueFormat="HH" displayFormat="HH" value="06"/>
            </item>
            <item title="无初始值">
                <link-date view="H" valueFormat="HH" displayFormat="HH"/>
            </item>

            <list-title label="时分"/>
            <item :title="`双向绑定:${Hm}`">
                <link-date view="Hm" valueFormat="HHmm" displayFormat="HH时mm分" v-model="Hm"/>
            </item>
            <item title="有初始值">
                <link-date view="Hm" valueFormat="HHmm" displayFormat="HH:mm" value="0608"/>
            </item>
            <item title="无初始值">
                <link-date view="Hm" valueFormat="HHmm" displayFormat="HH:mm"/>
            </item>

            <list-title label="时分秒"/>
            <item :title="`双向绑定:${Hms}`">
                <link-date view="Hms" valueFormat="HHmmss" displayFormat="HH时mm分ss秒" v-model="Hms"/>
            </item>
            <item title="有初始值">
                <link-date view="Hms" valueFormat="HHmmss" displayFormat="HH:mm:ss" value="060915"/>
            </item>
            <item title="无初始值">
                <link-date view="Hms" valueFormat="HHmmss" displayFormat="HH:mm:ss"/>
            </item>

        </list>
    </view>
</template>

<script>
    export default {
        name: "demo-date-view-page",
        data() {
            return {
                basicVal: '2020-06-30',
                yearVal: '2020',
                monthVal: '08',
                yearMonthVal: '202003',
                dateVal: '20200408',
                dateHourVal: '2020040815',
                YMDHm: '202004081533',
                YMDHms: '20200222222222',
                H: '07',
                Hm: '0708',
                Hms: '070818',
            }
        },
    }
</script>

<style lang="scss">

</style>
