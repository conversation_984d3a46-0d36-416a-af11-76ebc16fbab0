<template>
    <link-page>
        <demo-nav-list :menus="menus"/>
    </link-page>
</template>

<script>
    import Demo<PERSON>avList from "../demo-nav-list";

    export default {
        name: "demo-date-main-page",
        components: {DemoNavList},
        data() {
            return {
                menus: [
                    {name: 'date 过滤器', path: 'components/date/demo-date-filter-page'},
                    {name: 'link-date: 模式', path: 'components/date/demo-date-view-page'},
                    {name: 'link-date: 大小范围限制', path: 'components/date/demo-date-range-page'},
                    {name: 'link-date: 自定义选项', path: 'components/date/demo-date-custom-page'},
                ]
            }
        },
    }
</script>

<style lang="scss">

</style>