<template>
    <view class="demo-date-filter-page">
        <list>
            <item title="日期对象">
                <view>
                    {{dateObj|date}}
                </view>
            </item>

            <item title="日期时间">
                <view>
                    {{dateObj|date("YYYY年MM月DD日 HH时mm分ss秒")}}
                </view>
            </item>

            <item title="格式化日期字符串">
                <view>
                    {{dateString|date("YYYY年MM月DD日")}}
                </view>
            </item>

            <item title="特殊格式日期字符串">
                <view>
                    {{dateString2|date("YYYY年MM月DD日","YYYYMMDD")}}
                </view>
            </item>

            <item title="手动调用过滤器函数格式化值">
                <view>
                    {{dateFilterValue}}
                </view>
            </item>

            <list-title>周</list-title>
            <item title="基本用法">
                {{dateString|week}}
            </item>
            <item title="特殊格式日期字符串">
                {{dateString2|week({valueFormat:'YYYYMMDD'})}}
            </item>
            <item title="自定义前缀">
                {{dateString|week({prefix:'星期'})}}
            </item>
            <item title="自定义转化">
                {{dateString|week({prefix:' ',weekMap:['Sun','Mon','Tue','Wed','Thur','Fri','Sat']})}}
            </item>
        </list>
    </view>
</template>

<script>
    export default {
        name: "demo-date-filter-page",
        data() {
            console.log(this.$filter)
            console.log(this.$filter.date('2020-07-01', 'YYYY年MM月DD日'))
            return {
                dateObj: new Date(),
                dateString: '2020-06-30',
                dateString2: '20200630',

                dateVal1: '2020-05-31',
                dateVal0: null,

                dateFilterValue: this.$filter.date('2020-07-01', 'YYYY年MM月DD日')
            }
        },
    }
</script>

<style lang="scss">

</style>