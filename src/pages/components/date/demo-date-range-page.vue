<!--
navigationBarTitleText:"时间范围限制"
-->
<template>
    <link-page class="demo-date-custom-page">
        <list>
            <list-title label="基本用法"/>
            <item :title="val1">
                <link-date v-model="val1" max="2021-02-02" min="2019-02-02"/>
            </item>
            <list-title label="自定义格式的限制范围"/>
            <item :title="val2">
                <link-date v-model="val2" max="20210202" min="20190202" displayFormat="YYYY/MM/DD" valueFormat="YYYYMMDD"/>
            </item>

            <list-title label="日期范围"/>
            <item :title="dateVal">
                <link-date v-model="dateVal" max="2021-02-02" min="2019-02-02"/>
            </item>

            <list-title label="时间范围"/>
            <item :title="timeVal">
                <link-date view="Hms" v-model="timeVal" max="12:30:00" min="08:15:00" displayFormat="HH时mm分ss秒" valueFormat="HH:mm:ss"/>
            </item>

            <list-title label="日期时间范围"/>
            <item :title="datetimeVal">
                <link-date view="YMDHms"
                           v-model="datetimeVal"
                           max="2021-06-30 12:30:00"
                           min="2019-06-20 08:15:00"
                           displayFormat="YYYY年MM月DD日 HH时mm分ss秒 "
                           valueFormat="YYYY-MM-DD HH:mm:ss"
                           :maxWarn="customMaxDateWarnHandler"
                           :minWarn="customMinDateWarnHandler"
                />
            </item>

        </list>
    </link-page>
</template>

<script>
    export default {
        name: "demo-date-range-page",
        data() {
            return {
                val1: '2020-08-09',
                val2: '20200809',
                dateVal: '2020-08-09',
                timeVal: '09:00:00',
                datetimeVal: '2020-08-09 09:00:00',
            }
        },
        methods: {
            customMaxDateWarnHandler(displayString) {
                this.$showError(`最大时间为：${displayString}`)
            },
            customMinDateWarnHandler(displayString) {
                this.$showError(`最小时间为：${displayString}`)
            },
        },
    }
</script>

<style lang="scss">

</style>