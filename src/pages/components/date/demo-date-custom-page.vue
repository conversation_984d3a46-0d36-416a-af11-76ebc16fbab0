<!--
navigationBarTitleText:"日期时间自定义选项"
-->
<template>
    <view class="demo-date-custom-page">
        <list>
            <list-title label="单独自定义年份"/>
            <item :title="val1">
                <link-date v-model="val1" :custom="customYear"/>
            </item>
            <list-title label="年月联动自定义"/>
            <item :title="val2">
                <link-date v-model="val2" :custom="customYMD"/>
            </item>
            <list-title label="时间自定义：每五分钟为一个间隔"/>
            <item :title="val3">
                <link-date v-model="val3" :custom="customTime" view="Hm" displayFormat="HH时mm分" valueFormat="HH:mm"/>
            </item>
        </list>
    </view>
</template>

<script>
    import {DatePickerType} from "link-taro-component";

    export default {
        name: "demo-date-custom-page",
        data() {
            return {
                val1: null,
                val2: null,
                val3: null,
            }
        },
        methods: {
            customYear(pd, type) {
                if (type === DatePickerType.Y) {
                    return [
                        2020,
                        2021,
                        2022
                    ]
                }
            },
            customYMD(pd, type) {
                switch (type) {
                    case DatePickerType.Y:
                        return [
                            2020,
                            2021,
                            2022
                        ]
                    case DatePickerType.M:
                        return {
                            2020: [1, 2, 3, 4],
                            2021: [5, 6, 7, 8],
                            2022: [9, 10, 11, 12],
                        }[pd.year]
                }
            },
            customTime(pd, type) {
                switch (type) {
                    case DatePickerType.H:
                        return [
                            9, 10, 11, 12, 13, 14, 15, 16, 17, 18
                        ]
                    case DatePickerType.m:
                        let start = 0
                        let space = 5
                        let ret = []
                        while (start < 60) {
                            ret.push(start)
                            start += space
                        }
                        return ret
                }
            },
        }
    }
</script>

<style lang="scss">

</style>
