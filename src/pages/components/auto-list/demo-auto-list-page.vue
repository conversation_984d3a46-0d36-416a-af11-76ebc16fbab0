<template>
    <link-page class="demo-auto-list-page">
        <link-auto-list :option="option" @filter-bar-change="onFilterBarChange" :searchInputBinding="{props:{placeholder:'搜索名称'}}">
            <!--<view slot="searchRight" style="font-size: 24rpx;display: flex;align-items: center;padding: 0 12rpx">
                <link-icon icon="mp-trash"/>
                <view>删除</view>
            </view>-->
            <!--<view slot="top">
                top slots
            </view>

            <view slot="bottom">
                bottom slots
            </view>-->

            <!--<view slot="noMore">
                加载完毕
            </view>
            <view slot="noData">
                没有数据了
            </view>-->
            <link-filter-group slot="filterGroup">
                <link-filter-item label="val(降序)" :param="{sort:'val'}"/>
                <link-filter-item label="最近更新(升序)" :param="{sort:{field:'lastUpdated',desc:false}}"/>
                <link-filter-item label="TEST" :param="{filter:{property:'name',value:'测试',operator:'LIKE'}}"/>
                <link-filter-item label="报表" :param="{filter:{property:'name',value:'报表',operator:'LIKE'}}"/>
                <link-filter-item label="培训" :param="{filter:{property:'name',value:'培训',operator:'LIKE'}}"/>
                <link-filter-item label="设计" :param="{filter:{property:'name',value:'设计',operator:'LIKE'}}"/>
                <link-filter-item label="1573" :param="{filter:{property:'name',value:'1573',operator:'LIKE'}}"/>
            </link-filter-group>

            <template v-slot="{data,index}">
                <link-swipe-action :key="index">
                    <item :title="data.name || ' '" :desc="data.val" :data="data"/>
                    <link-swipe-option label="删除" @tap="option.deleteItem(data)" slot="option"/>
                </link-swipe-action>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>

export default {
    name: "demo-auto-list-page",
    props: {},
    data() {

        const option = new this.AutoList(this, {
            module: 'action/link/basic',
            itemPath: '/pages/components/auto-list/demo-auto-list-item-page',
            sortField: 'name',
            sortDesc: false,
            // loadOnStart: false,
            cacheParam: {cache: 'overall', expireSecond: 60},
            slots: {
                searchRight: () => (
                    <link-button mode="text" label="查询所售产品" onTap={this.searchSaleProds} style="padding-left: 8px"/>
                ),
                /*filterGroup: () => (
                    <link-filter-group>
                        <link-filter-item label="val(降序)" param={{sort: 'val'}}/>
                        <link-filter-item label="最近更新(升序)" param={{sort: {field: 'lastUpdated', desc: false}}}/>
                        <link-filter-item label="TEST" param={{filter: {property: 'name', value: '测试', operator: 'LIKE'}}}/>
                        <link-filter-item label="报表" param={{filter: {property: 'name', value: '报表', operator: 'LIKE'}}}/>
                        <link-filter-item label="培训" param={{filter: {property: 'name', value: '培训', operator: 'LIKE'}}}/>
                        <link-filter-item label="设计" param={{filter: {property: 'name', value: '设计', operator: 'LIKE'}}}/>
                        <link-filter-item label="1573" param={{filter: {property: 'name', value: '1573', operator: 'LIKE'}}}/>
                    </link-filter-group>
                )*/
            },
            /*slots: {
                noData: () => (
                    <view>没有数据了！！！</view>
                ),
                noMore: () => (
                    <view>加载完毕！！！</view>
                )
            },*/

            param: {
                // filtersRaw: [{"property": "type", "operator": "LIKE", "value": "ACT_CATEGORY", "id": "singleFiltersRaw0"}],
                accountId: 123,
                // rows:15,
            },

            // createPath:'/pages/component/demo-http-page',
            /*disabled: {
                creatable: () => {
                    // @ts-ignore
                    return this.isCreatable
                }
            },*/
            hooks: {
                async beforeGotoItem(param) {
                    console.log('beforeGotoItem', {...param.data})
                },
                async beforeCreateItem(param) {
                    console.log('beforeCreateItem', {...param.data})
                },
            },

            /*renderFunc: (h, {data, index}) => {
                return (
                    <item title={data.name} key={index} note={data.val}/>
                )
            },

            slots: {
                top: (h) => (<view>top render</view>),
                bottom: (h) => (<view>bottom render</view>),
            }*/
            /*sortOptions: [
                {label: '编号', field: 'id'},
                {label: '代码', field: 'val', desc: false},
            ],*/
            searchFields: ['name', 'val'],

            filterOption: [
                {
                    label: '安全性筛选',
                    type: 'oauth',
                    // field: 'myOauth',                   // 绑定字段默认为oauth，这个字段不是filtersRaw中的property，而是最终请求参数对象的oauth字段
                    // lov: 'ACCT_STAGE',                  // 默认的安全性菜单值列表类型
                    /*data: [
                        {name: '自定义安全性一', val: 'custom_1'},
                        {name: '自定义安全性二', val: 'custom_2'},
                    ]*/
                },
                // 普通文本模糊筛选
                {label: '客户姓名', field: 'acctName', type: 'text'},
                // 数字范围筛选
                {label: '客户年龄', field: 'acctAge', type: 'number'},
                // 日期筛选
                {label: '客户生日', field: 'acctBirthday', type: 'date'},
                // 值列表筛选
                {label: '客户级别', field: 'acctLevel', type: 'lov', lov: 'ACCT_LEVEL'},
                // 自定义筛选
                // 可以自己通过异步函数请求数据，然后 autoList.option.filterOption.options.push(自定义筛选选项)实现异步添加自定义筛选
                // 上面的 autoList = new AutoList({...})
                {
                    label: '客户负责人', field: 'acctMaster', type: 'select', data: [
                        {name: 'January', val: '1'},
                        {name: 'February', val: '2'},
                        {name: 'March', val: '3'},
                        {name: 'April', val: '4'},
                        {name: 'May', val: '5'},
                        {name: 'June', val: '6'},
                        {name: 'July', val: '7'},
                        {name: 'August', val: '8'},
                        {name: 'September', val: '9'},
                        {name: 'October', val: '10'},
                        {name: 'November', val: '11'},
                        {name: 'December', val: '12'},
                    ]
                },
            ],
            filterBar: {
                field: 'name',
                // lov: 'BURIED_TYPE',
                autoReload: false,              // 禁止点击自动刷新
                notNull: true,                  // 禁止反选为null
            }
        })

        return {
            option,
        }
    },
    // @ts-ignore
    onPullDownRefresh(...args) {
        console.log('page onPullDownRefresh')
    },
    onReachBottom(...args) {
        console.log('page onReachBottom')
    },
    methods: {
        searchSaleProds() {
            console.log('searchSaleProds')
        },
        testMethod() {

        },
        onFilterBarChange() {
            const {value} = this.option.option.filterBar
            console.log(value)
            this.option.option.filterOption = []
            this.option.reload()
        },
    },
}
</script>

<style lang="scss">
</style>
