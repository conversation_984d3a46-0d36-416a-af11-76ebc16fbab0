<template>
    <link-page class="demo-auto-list-item-page">
        <list>
            <link-form :option="option" :value="option.formData">
                <link-form-item label="名称" field="name" required>
                    <link-input v-model="option.formData.name"/>
                </link-form-item>
                <link-form-item label="值" field="val" required>
                    <link-input v-model="option.formData.val"/>
                </link-form-item>
                <link-form-item label="类型" field="type" required>
                    <link-input v-model="option.formData.type"/>
                </link-form-item>
            </link-form>
        </list>
    </link-page>
</template>

<script>
    export default {
        name: "demo-auto-list-item-page",
        props: {},
        data() {
            const config = this.pageParam

            console.log(config)

            const option = new this.FormOption(this, {
                ...config,
            })

            return {
                option: option,
            }
        },
        methods: {}
    }
</script>

<style lang="scss">
</style>