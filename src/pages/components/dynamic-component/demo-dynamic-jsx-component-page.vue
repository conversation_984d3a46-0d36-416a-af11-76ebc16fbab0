<template>
    <view class="demo-dynamic-jsx-component-page">
        <link-form v-model="formData">
            <link-form-item v-for="(option,index) in options" :key="index" :label="option.label" :required="option.required">
                <demo-dynamic-jsx-component :option="option" :formData="formData"/>
            </link-form-item>
        </link-form>
        <view style="word-break: break-all">
            {{JSON.stringify(formData)}}
        </view>
    </view>
</template>

<script>
    import DemoDynamicJsxComponent from './demo-dynamic-jsx-component'

    export default {
        name: "demo-dynamic-jsx-component-page",
        components: {DemoDynamicJsxComponent},
        data() {
            return {
                formData: {
                    acctName: '张三',
                    gender: 'MALE',
                },
                options: [
                    {label: '普通文本框', extFieldType: 'link-table-column-input', required: true, field: 'acctName'},
                    {label: '日期选择框', extFieldType: 'link-table-column-datepicker', required: false, field: 'birthday'},
                    {label: '时间选择框', extFieldType: 'link-table-column-timepicker', required: false, field: 'birthTime'},
                    {label: '值列表选择框', extFieldType: 'link-table-column-lov', required: true, field: 'gender', lov: 'GENDER'},
                    {label: '选择省份', extFieldType: 'link-table-column-address', required: true, field: 'province'},

                ]
            }
        },
    }
</script>

<style lang="scss">

</style>