import {set} from "link-taro-component";

interface DynamicComponentOption {
    field: string,
    extFieldType: string,
    lov?: string,
}

export const DynamicComponent
    : {
    [k: string]: (option: DynamicComponentOption, formData: any) => {
        component?: string,
        bind?: any,
        on?: any,
    }
}
    = {
    'link-table-column-input': () => {
        return {
            component: 'link-input',
        }
    },
    'link-table-column-datepicker': () => {
        return {
            component: 'link-date',
            bind: {
                displayFormat: 'YYYY年MM月DD日',
                valueFormat: 'YYYY-MM-DD',
            }
        }
    },
    'link-table-column-timepicker': () => {
        return {
            component: 'link-date',
            bind: {
                displayFormat: 'HH时mm分ss秒',
                valueFormat: 'HH:mm:ss',
            }
        }
    },
    'link-table-column-lov': (option) => {
        return {
            component: 'link-lov',
            bind: {
                type: option.lov,
            }
        }
    },
    'link-table-column-address': (option, formData) => {
        return {
            component: 'link-address',
            bind: {
                view: 'p',
                province: formData[option.field]
            },
            on: {
                'update:province': val => set(formData, option.field, val)
            },
        }
    },
}

export function getDynamicComponent(option: DynamicComponentOption, formData: any) {
    const {extFieldType} = option
    return DynamicComponent[extFieldType](option, formData)
}
