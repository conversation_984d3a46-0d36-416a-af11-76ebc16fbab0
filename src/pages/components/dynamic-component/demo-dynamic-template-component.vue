<template>
    <component :is="binding.component" v-bind="binding.bind" v-model="formData[option.field]" v-on="binding.on"/>
</template>

<script>
    import {getDynamicComponent} from "./DemoDynamicComponent";

    export default {
        props: {
            option: {},
            formData: {},
        },
        name: "demo-dynamic-template-component",
        data() {
            const binding = getDynamicComponent(this.option, this.formData)
            return {
                binding,
            }
        },
    }
</script>

<style lang="scss">

</style>