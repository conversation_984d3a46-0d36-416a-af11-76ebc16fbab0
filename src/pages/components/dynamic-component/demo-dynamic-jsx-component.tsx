import {defineComponent, set} from "link-taro-component";
import {VNode} from "vue/types/umd";

interface DynamicOption {
    field: string,
    extFieldType: string,
    lov?: string,
}

const DynamicComponents: {
    [k: string]: (option: DynamicOption, formData: any) => VNode
} = {
    'link-table-column-input'(option, formData) {
        return (
            <link-input v-model={formData[option.field]}/>
        )
    },
    'link-table-column-datepicker'(option, formData) {
        return (
            <link-date v-model={formData[option.field]} displayFormat="YYYY年MM月DD日" valueFormat="YYYY-MM-DD"/>
        )
    },
    'link-table-column-timepicker'(option, formData) {
        return (
            <link-date v-model={formData[option.field]} displayFormat="HH时mm分ss秒" valueFormat="HH:mm:ss"/>
        )
    },
    'link-table-column-lov'(option, formData) {
        return (
            <link-lov v-model={formData[option.field]} type={option.lov}/>
        )
    },
    'link-table-column-address'(option, formData) {
        return (
            <link-address
                view="p"
                province={formData[option.field]}
                {...{on: {'update:province': val => set(formData, option.field, val)}}}/>
        )
    }
}

export default defineComponent({
    props: {
        option: {type: Object, required: true},
        formData: {type: Object, require: true},
    },
    //@ts-ignore
    render(h) {
        return DynamicComponents[this.option.extFieldType].apply(this, [this.option, this.formData])
    },
})
