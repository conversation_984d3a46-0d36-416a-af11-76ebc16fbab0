<template>
    <view class="demo-loading-page">
        <list>
            <item title="基本用法">
                <link-loading/>
            </item>
            <item title="类型">
                <link-loading type="alpha"/>
                <link-loading type="beta"/>
                <link-loading type="gamma"/>
            </item>
            <item title="预定义的颜色">
                <link-loading status="primary"/>
                <link-loading status="success"/>
                <link-loading status="warn"/>
                <link-loading status="error"/>
                <link-loading status="info"/>
            </item>
            <item title="自定义大小颜色">
                <link-loading style="font-size: 40px;color: #1F74FF"/>
            </item>
        </list>
    </view>
</template>

<script>
    export default {
        name: "demo-loading-page",
        props: {},
        data() {
            return {}
        },
        methods: {},
    }
</script>

<style lang="scss">
</style>