<template>
    <link-page class="demo-fab-group-page">
        <list>
            <item v-for="item in 40" :key="item" :title="`item-${item}`" @tap="()=>tapListItem(item)"/>
        </list>
        <link-fab-group @tap-fab-item="onTapFabItemByGroup" :value="true">
            <link-fab-item icon="icon-fenxiang" label="分享" @tap-icon="()=>onTapFabItem('分享')"/>
            <link-fab-item icon="icon-icon-test" label="结算" @tap-icon="()=>onTapFabItem('结算')"/>
            <link-fab-item icon="icon-jiangpin" label="奖品" @tap-icon="()=>onTapFabItem('奖品')"/>
            <link-fab-item icon="icon-hexinzhongduan" label="门店" @tap-icon="()=>onTapFabItem('门店')"/>
        </link-fab-group>
    </link-page>
</template>

<script>
    export default {
        name: "demo-fab-group-page",
        methods: {
            tapListItem(i) {
                this.$message.warn(String(i))
            },
            onTapFabItem(tag) {
                console.log('onTapFabItem', tag)
                this.$message.primary(tag)
            },
            onTapFabItemByGroup({e, label, icon}) {
                console.log({e, label, icon})
            },
        }
    }
</script>

<style lang="scss">

</style>