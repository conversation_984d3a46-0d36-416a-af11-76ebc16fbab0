<template>
    <link-page class="demo-fab-group-slot-page">
        <list>
            <item v-for="item in 40" :key="item" :title="`item-${item}`" @tap="()=>tapListItem(item)"/>
        </list>
        <link-fab-group @tap-fab-item="onTapFabItemByGroup">
            <view slot-scope="{value}" slot="button">
                {{value?'关':'开'}}
            </view>
            <link-fab-item icon="icon-fenxiang" label="分享" @tap-icon="()=>onTapFabItem('分享')"/>
            <link-fab-item @tap-icon="()=>onTapFabItem('1')">
                <view slot="label">label-1</view>
                <view slot="icon">icon1</view>
            </link-fab-item>
            <link-fab-item @tap-icon="()=>onTapFabItem('2')">
                <view slot="label">label-2</view>
                <view slot="icon">icon2</view>
            </link-fab-item>
        </link-fab-group>
    </link-page>
</template>

<script>
    export default {
        name: "demo-fab-group-slot-page",
        methods: {
            tapListItem(i) {
                this.$message.warn(String(i))
            },
            onTapFabItem(tag) {
                console.log('onTapFabItem', tag)
                this.$message.primary(tag)
            },
            onTapFabItemByGroup({e, label, icon}) {
                console.log({e, label, icon})
            },
        }
    }
</script>

<style lang="scss">

</style>