<template>
    <link-page class="demo-fab-group-normal-page">
        <list>
            <item v-for="item in 40" :key="item" :title="`item-${item}`" @tap="()=>tapListItem(item)"/>
        </list>
        <link-sticky>
            <view class="link-fab-group-sticky">
                <link-fab-group groupButtonType="normal" buttonAlign="start">
                    <link-fab-item @tap-icon="()=>onTapFabItem('1')">
                        <view slot="icon" style="display: flex;flex-wrap: nowrap;align-items: center;width: 80px">
                            <link-icon icon="icon-yijianbaobei1"/>
                            <view style="white-space: nowrap">一键报备</view>
                        </view>
                    </link-fab-item>
                    <link-fab-item @tap-icon="()=>onTapFabItem('2')">
                        <view slot="icon" style="display: flex;flex-wrap: nowrap;align-items: center;width: 80px">
                            <link-icon icon="icon-yijianbaobei"/>
                            <view style="white-space: nowrap">分享</view>
                        </view>
                    </link-fab-item>
                </link-fab-group>
                <link-button mode="stroke">返回修改</link-button>
                <link-button shadow>提交</link-button>
            </view>
        </link-sticky>
    </link-page>
</template>

<script>
    export default {
        name: "demo-fab-group-normal-page",
        methods: {
            tapListItem(i) {
                this.$message.warn(String(i))
            },
            onTapFabItem(tag) {
                console.log('onTapFabItem', tag)
                this.$message.primary(tag)
            },
        }
    }
</script>

<style lang="scss">

</style>