<template>
    <view class="demo-fab-main-page">
        <demo-nav-list :menus="menus"/>
    </view>
</template>

<script>
    import DemoNavList from "../demo-nav-list";

    export default {
        name: "demo-fab-main-page",
        components: {DemoNavList},
        data() {
            return {
                menus: [
                    {name: 'link-fab-button 悬浮按钮', path: 'components/fab/demo-fab-button-page'},
                    {name: 'link-fab-group 悬浮按钮组', path: 'components/fab/demo-fab-group-page'},
                    {name: 'link-fab-group 手动控制开启以及关闭', path: 'components/fab/demo-fab-group-control-page'},
                    {name: 'link-fab-group 插槽', path: 'components/fab/demo-fab-group-slot-page'},
                    {name: 'link-fab-group 非固定按钮用法', path: 'components/fab/demo-fab-group-normal-page'},
                ]
            }
        },
    }
</script>

<style lang="scss">

</style>