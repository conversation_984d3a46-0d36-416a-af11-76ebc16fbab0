<template>
    <view class="demo-fab-button-page">
        <list>
            <list-title label="悬浮按钮会自动适配底部安全区，所以调试的时候，在iPhone x类型的设备上底部会多出68rpx"/>
            <item title="基本用法" @tap="()=>onClickItem(1)">
                <link-fab-button @tap="()=>onClickFab(2)"/>
            </item>
            <item title="举例底部有一定的距离" @tap="()=>onClickItem(2)">
                <link-fab-button :bottom="120" icon="mp-trash" @tap="()=>onClickFab(3)"/>
            </item>
        </list>
    </view>
</template>

<script>
    export default {
        name: "demo-fab-button-page",
        props: {},
        data() {
            return {}
        },
        methods: {
            onClickItem(tag) {
                console.log('onClickItem', tag)
            },
            onClickFab(tag) {
                console.log('onClickFab', tag)
            },
        },
    }
</script>

<style lang="scss">
</style>