<template>
    <view class="demo-link-dialog-page">
        <list>
            <list-title label="基本用法"/>
            <link-button block label="打开弹框" @tap="$refs.dialog.show()"/>
            <link-dialog ref="dialog">
                <view slot="head">
                    Apple ID
                </view>
                <view>
                    需要重新登录！
                </view>
                <link-button slot="foot">取消</link-button>
                <link-button slot="foot">确定</link-button>
            </link-dialog>
            <list-title label="纵向按钮"/>
            <link-button block label="打开弹框" @tap="$refs.dialogVerticalButton.show()"/>
            <link-dialog ref="dialogVerticalButton" verticalFootButton>
                <view slot="head">
                    Apple ID
                </view>
                <view>
                    需要重新登录！
                </view>
                <link-button slot="foot">忘记密码</link-button>
                <link-button slot="foot">取消</link-button>
                <link-button slot="foot">确定</link-button>
            </link-dialog>

            <list-title label="位置"/>
            <view>
                <link-button block label="默认位置" @tap="$refs.positionDefault.show()"/>
                <link-dialog ref="positionDefault">弹框内容</link-dialog>
            </view>
            <view>
                <link-button block label="顶部" @tap="$refs.positionTop.show()"/>
                <link-dialog ref="positionTop" position="top">
                    弹框内容
                </link-dialog>
            </view>
            <view>
                <link-button block label="底部" @tap="$refs.positionBottom.show()"/>
                <link-dialog ref="positionBottom" position="bottom">
                    弹框内容
                </link-dialog>
            </view>
            <view>
                <link-button block label="左侧" @tap="$refs.positionLeft.show()"/>
                <link-dialog ref="positionLeft" position="left">
                    弹框内容
                </link-dialog>
            </view>
            <view>
                <link-button block label="右侧" @tap="$refs.positionRight.show()"/>
                <link-dialog ref="positionRight" position="right">
                    弹框内容
                </link-dialog>
            </view>
            <view>
                <link-button block label="海报" @tap="$refs.positionPoster.show()"/>
                <link-dialog ref="positionPoster" position="poster">
                    <image src="https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1594122435095&di=bcf5969417892fe72e40aa9179749bcd&imgtype=0&src=http%3A%2F%2Fsc.admin5.com%2Fuploads%2Fallimg%2F130731%2F760-130I109234O59.jpg"
                           mode="widthFix"/>
                </link-dialog>
            </view>

            <list-title label="双向绑定控制显示弹框"/>
            <view>
                <link-button block :label="`openFlag:${openFlag}`" @tap="openFlag = !openFlag"/>
                <link-dialog position="top" v-model="openFlag">
                    弹框内容
                </link-dialog>
            </view>
            <list-title label="禁止点击遮罩关闭弹框"/>
            <view>
                <link-button block label="打开弹框" @tap="$refs.dialog2.show()"/>
                <link-dialog ref="dialog2" disabledHideOnClickMask title="提示">
                    只能点击按钮手动关闭弹框
                    <link-button slot="foot" @tap="$refs.dialog2.hide()">关闭</link-button>
                </link-dialog>
            </view>
            <list-title label="遮罩颜色"/>
            <view>
                <link-button block label="打开弹框" @tap="$refs.dialogMask.show()"/>
                <link-dialog ref="dialogMask" title="提示" maskColor="white">
                    弹框内容
                </link-dialog>
            </view>

            <item>
                <link-input></link-input>
            </item>
            <item>
                <textarea name="" id="" cols="30" rows="10"></textarea>
            </item>

            <!--<view class="test-dialog" @touchstart.stop.prevent="onTouchmove">

            </view>-->

        </list>
    </view>
</template>

<script>
    export default {
        name: "demo-link-dialog-page",
        data() {
            return {
                openFlag: false,

            }
        },
        methods: {
            onTouchmove(e) {
                e.stopPropagation()
                console.log('onTouchmove')
            },
        }
    }
</script>

<style lang="scss">
    .test-dialog {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(black, 0.3);
    }
</style>