<template>
    <view class="demo-link-switch-page">
        <list>
            <item :title="`基本用法${formData.val1}`">
                <link-switch v-model="formData.val1"/>
            </item>
            <item :title="`真假值${formData.val2}`">
                <link-switch v-model="formData.val2" :trueValue="true" :falseValue="false"/>
            </item>


            <item :title="`禁用${formData.val3}`">
                <link-switch v-model="formData.val3" disabled/>
            </item>
            <item :title="`只读${formData.val4}`">
                <link-switch v-model="formData.val4" readonly/>
            </item>

        </list>
    </view>
</template>

<script>
    export default {
        name: "demo-link-switch-page",
        data() {
            return {
                formData: {
                    val1: null,
                    val2: null,
                    val3: null,
                    val4: null,
                    val5: null,
                    val6: null,
                    val7: null,
                    val8: null,
                    val9: null,
                },
            }
        },
    }
</script>

<style lang="scss">

</style>