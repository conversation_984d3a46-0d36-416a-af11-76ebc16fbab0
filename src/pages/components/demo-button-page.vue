<template>
    <view class="demo-link-button-page.vue">
        <list>
            <list-title>基本用法</list-title>
            <view>
                <link-button class="basic-button" @tap="onTap">按钮</link-button>
            </view>

            <list-title>大小</list-title>
            <view>
                <link-button v-for="item in ['large','normal','mini']" :key="item" :label="item" :size="item"/>
            </view>
            <list-title>形状</list-title>
            <view>
                <link-button v-for="item in ['fillet','round','none']" :key="item" :label="item" :shape="item"/>
            </view>

            <list-title>状态</list-title>
            <view>
                <link-button v-for="item in status" :key="item" :label="item" :status="item"/>
            </view>

            <list-title>按钮模式</list-title>
            <view>
                <link-button v-for="item in ['fill','stroke','text']" :key="item" :label="item" :mode="item"/>
            </view>

            <list-title>块级按钮</list-title>
            <link-button block>按钮</link-button>

            <list-title>按钮图标</list-title>
            <view>
                <link-button label="按钮" icon="mp-plus"/>
            </view>
            <list-title>纯图标按钮</list-title>
            <view>
                <link-button icon="mp-plus"/>
                <link-button icon="mp-plus" mode="stroke"/>
                <link-button icon="mp-plus" mode="text"/>

                <link-button icon="mp-plus" shape="round"/>
                <link-button icon="mp-plus" mode="stroke" shape="round"/>
            </view>

            <list-title>禁用</list-title>
            <view>
                <link-button v-for="item in ['fill','stroke','text']" :key="item" :label="item" :mode="item" disabled/>
            </view>
            <list-title>加载按钮</list-title>
            <view>
                <link-button v-for="item in ['fill','stroke','text']" :key="item" :label="item" :mode="item" loading/>
            </view>

            <list-title>点击事件节流</list-title>
            <view>
                <link-button @tap="log">每秒钟最多触发两次点击事件</link-button>
            </view>
            <list-title>自动开启加载状态</list-title>
            <view>
                <link-button @tap="handleAsyncJob" autoLoading>任务完毕之后会自动关闭加载状态</link-button>
            </view>

        </list>
    </view>
</template>

<script>
    import {ComponentUtils} from "link-taro-component";

    export default {
        name: "demo-link-button-page.vue",
        data() {
            return {
                status: [
                    'primary',
                    'success',
                    'warn',
                    'error',
                    'info',
                ],
                log: () => {
                    console.log('tap', this.status)
                },
            }
        },
        methods: {
            async handleAsyncJob() {
                console.log(this.status)
                await ComponentUtils.delay(3000)
                console.log('done')
                this.$taro.showToast({title: 'complete'})
            },
            onTap() {
                console.log('tap')
            },
        }
    }
</script>

<style lang="scss">
    .demo-link-button-page {
        .link-list {
            & > view {
                box-sizing: border-box;
                padding: 12px 32px;
            }

            .link-button + .link-button {
                margin-top: 20px;
                margin-left: 20px;
            }
        }

        .basic-button {

        }
    }
</style>
