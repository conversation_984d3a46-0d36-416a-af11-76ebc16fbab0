<template>
    <link-page class="demo-step-horizontal-page">
        <list>
            <list-title>基本用法</list-title>
            <link-step-group :current="1">
                <link-step title="获取秘钥" content="通过应用appid获取请求秘钥"/>
                <link-step title="上传凭证" content="通过秘钥与用户信息生成用户凭证"/>
                <link-step title="身份验证" content="根据用户凭证以及人脸识别验证身份"/>
            </link-step-group>
            <list-title>自定义宽度</list-title>
            <link-alert status="success">当步骤太多，需要横向滚动时，必须设置stepWidth，单位尽量使用rpx</link-alert>
            <link-step-group stepWidth="187.5rpx" :current="4">
                <link-step title="获取秘钥" content="通过应用appid获取请求秘钥"/>
                <link-step title="上传凭证" content="通过秘钥与用户信息生成用户凭证"/>
                <link-step title="身份验证" content="根据用户凭证以及人脸识别验证身份"/>
                <link-step title="补充信息" content="补充用户额外的个人信息"/>
                <link-step title="创建账户" content="购买商品会从账户余额中扣除"/>
                <link-step title="系统审核" content="等待系统审核结果"/>
            </link-step-group>
            <list-title>步骤条状态：finish（已完成）、process（处理中）、wait（未开始）、error（失败）</list-title>
            <link-step-group>
                <link-step title="finish" content="已完成" status="finish"/>
                <link-step title="process" content="处理中" status="process"/>
                <link-step title="wait" content="未开始" status="wait"/>
            </link-step-group>
            <link-step-group>
                <link-step title="finish" content="已完成" status="finish"/>
                <link-step title="error" content="失败" status="error"/>
                <link-step title="wait" content="未开始" status="wait"/>
            </link-step-group>
            <link-alert status="warn">
                finish 的步骤中间不能穿插 wait, error, process 的步骤，否则会出现以下问题，需要自己调整样式
            </link-alert>
            <link-step-group :current="2" currentStatus="finish">
                <link-step title="finish" content="已完成" status="finish"/>
                <link-step title="error" content="失败" status="error"/>
                <link-step title="wait" content="未开始"/>
            </link-step-group>

            <list-title>通过stepGroup设置当前步骤状态</list-title>
            <link-alert>仅设置了current为最后一个节点</link-alert>
            <link-step-group :current="3">
                <link-step title="获取秘钥" content="通过应用appid获取请求秘钥"/>
                <link-step title="上传凭证" content="通过秘钥与用户信息生成用户凭证"/>
                <link-step title="身份验证" content="根据用户凭证以及人脸识别验证身份"/>
                <link-step title="补充信息" content="补充用户额外的个人信息"/>
            </link-step-group>
            <link-alert>设置了current以及currentStatus</link-alert>
            <link-step-group :current="3" currentStatus="finish">
                <link-step title="获取秘钥" content="通过应用appid获取请求秘钥"/>
                <link-step title="上传凭证" content="通过秘钥与用户信息生成用户凭证"/>
                <link-step title="身份验证" content="根据用户凭证以及人脸识别验证身份"/>
                <link-step title="补充信息" content="补充用户额外的个人信息"/>
            </link-step-group>

            <list-title>设置图标</list-title>
            <link-step-group :current="2">
                <link-step icon="icon-peie" title="获取秘钥" content="通过应用appid获取请求秘钥"/>
                <link-step icon="icon-renwucaiji" title="上传凭证" content="通过秘钥与用户信息生成用户凭证"/>
                <link-step icon="icon-jingxiaoshang" title="身份验证" content="根据用户凭证以及人脸识别验证身份"/>
                <link-step icon="icon-gongzuodan" title="补充信息" content="补充用户额外的个人信息"/>
            </link-step-group>
            <list-title>title以及content插槽</list-title>
            <link-step-group :current="2">
                <link-step icon="icon-peie">
                    <template slot="title">标题1</template>
                    <template slot="content">内容1</template>
                </link-step>
                <link-step icon="icon-renwucaiji">
                    <template slot="title">标题2</template>
                    <template slot="content">内容2</template>
                </link-step>
                <link-step icon="icon-jingxiaoshang">
                    <template slot="title">标题3</template>
                    <template slot="content">内容3</template>
                </link-step>
                <link-step icon="icon-gongzuodan">
                    <template slot="title">标题4</template>
                    <template slot="content">内容4</template>
                </link-step>
            </link-step-group>

        </list>
    </link-page>
</template>

<script>
    export default {
        name: "demo-step-horizontal-page"
    }
</script>

<style lang="scss">

</style>