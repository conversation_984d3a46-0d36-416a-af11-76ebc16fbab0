<template>
    <link-page class="demo-step-vertical-page">
        <list>
            <list-title>基本用法</list-title>
            <link-step-group vertical>
                <link-step title="打包完成" content="2020-09-18 15:20:30"/>
                <link-step title="扫描完成" content="2020-09-18 15:20:30"/>
                <link-step title="拣货完成" content="2020-09-18 15:20:30"/>
            </link-step-group>

            <list-title>状态</list-title>
            <link-alert>仅支持 process 以及 error两种状态</link-alert>
            <link-step-group vertical>
                <link-step title="打包完成(process)" content="2020-09-18 15:20:30" status="process"/>
                <link-step title="扫描完成(error)" content="2020-09-18 15:20:30" status="error"/>
                <link-step title="拣货完成" content="2020-09-18 15:20:30"/>
                <link-step title="您的订单已经打印完成" content="2020-09-18 15:00:24"/>
                <link-step title="您的订单已经下传上海仓库，准备出库" content="2020-09-18 14:56:13"/>
                <link-step title="已提交订单，等待第三方卖家确认" content="2020-09-18 14:42:20"/>
            </link-step-group>

            <list-title>icon, title, content 插槽</list-title>
            <link-step-group vertical>
                <link-step status="process">
                    <view slot="icon">包</view>
                    <view slot="title">
                        <link-loading type="gamma"/>
                        <text>打包明细</text>
                    </view>
                    <view slot="content">
                        打包时间：2020-09-18 15:20:30
                    </view>
                </link-step>
                <link-step title="扫描完成" content="2020-09-18 15:20:30" status="error">
                    <link-icon icon="icon-saoma" slot="icon"/>
                    <view slot="title">
                        <link-loading type="beta"/>
                        <text>扫描完成</text>
                    </view>
                    <view slot="content">
                        扫描时间：2020-09-18 15:20:30
                    </view>
                </link-step>
                <link-step title="拣货完成" content="2020-09-18 15:20:30"/>
            </link-step-group>
        </list>
    </link-page>
</template>

<script>
    export default {
        name: "demo-step-vertical-page"
    }
</script>

<style lang="scss">

</style>