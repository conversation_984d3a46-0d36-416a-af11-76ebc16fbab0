<template>
    <link-page class="demo-step-main-page">
        <demo-nav-list :menus="menus"/>
    </link-page>
</template>

<script>
    import DemoNavList from "../demo-nav-list";

    export default {
        name: "demo-step-main-page",
        components: {DemoNavList},
        data() {
            return {
                menus: [
                    {name: '横向步骤条', path: 'components/step/demo-step-horizontal-page'},
                    {name: '纵向步骤条', path: 'components/step/demo-step-vertical-page'},
                ],
            }
        },
    }
</script>

<style lang="scss">

</style>