<template>
    <view class="demo-alert-page">
        <list>
            <list-title>基本用法</list-title>
            <link-alert>
                请注意航班动态信息，建议先下载随身办进行健康申报
            </link-alert>
            <list-title>状态</list-title>
            <link-alert status="primary">请注意航班动态信息，建议先下载随身办进行健康申报。</link-alert>
            <link-alert status="success">您所乘坐的航班登机结束，正在排队等待起飞。</link-alert>
            <link-alert status="warn">您所乘坐的航班已经催促登记，请前往登机口乘机。</link-alert>
            <link-alert status="error">您所乘坐的航班已经停止办理登机。</link-alert>
            <link-alert status="info">如何改签或者退票？</link-alert>

            <list-title>去除默认图标</list-title>
            <link-alert :icon="null">
                <view style="display: flex;justify-content: space-between;width: 100%">
                    <view> <link-icon icon="icon-xiaoxi"/> 您有一条新的航班动态信息。</view>
                    <view style="color: #777">查看信息<link-icon icon="icon-chakanquanbu"/></view>
                </view>
            </link-alert>
        </list>
    </view>
</template>

<script>
    export default {
        name: "demo-alert-page"
    }
</script>

<style lang="scss">
    .demo-alert-page {
        .link-alert {
            margin: 40px 0;
        }
    }
</style>