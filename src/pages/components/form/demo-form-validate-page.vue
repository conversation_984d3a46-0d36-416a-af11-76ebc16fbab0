<template>
    <link-page class="demo-form-validate-page">
        <link-form ref="form" :value="formData" :rules="formRules">
            <list-title label="form-item 校验规则"/>
            <link-form-item field="formItemRequired" label="formItem必填校验" required :itemProps="{rightWidth:'400rpx'}">
                <link-input v-model="formData.formItemRequired"/>
            </link-form-item>
            <link-form-item field="formItemRequiredRule" label="formItem rule 必填校验" :rules="Validator.required('不能为空')">
                <link-input v-model="formData.formItemRequiredRule"/>
            </link-form-item>
            <link-form-item :field="['formItemRequired1','formItemRequired2']" label="formItem多值必填" required>
                <link-input v-model="formData.formItemRequired1" style="width: 150rpx"/>
                <link-input v-model="formData.formItemRequired2" style="width: 150rpx"/>
            </link-form-item>
            <link-form-item field="formItemCustom" label="formItem 自定义校验" :rules="customValidator">
                <link-input v-model="formData.formItemCustom"/>
            </link-form-item>
            <link-form-item field="formItemAsyncValidate" label="formItem rule 异步校验" :rules="asyncValidator">
                <link-input v-model="formData.formItemAsyncValidate"/>
            </link-form-item>

            <list-title label="form rules 校验规则"/>
            <link-form-item field="rulesRequired1" label="rules必填">
                <link-input v-model="formData.rulesRequired1"/>
            </link-form-item>
            <link-form-item :field="['rulesRequiredA','rulesRequiredB']" label="rules多值必填">
                <link-input v-model="formData.rulesRequiredA" style="width: 150rpx"/>
                <link-input v-model="formData.rulesRequiredB" style="width: 150rpx"/>
            </link-form-item>
            <link-form-item field="rulesRequiredAsync" label="rules 异步校验">
                <link-input v-model="formData.rulesRequiredAsync"/>
            </link-form-item>
            <link-form-item field="rulesRequiredAsyncError" label="异步校验，校验错误">
                <link-input v-model="formData.rulesRequiredAsyncError"/>
            </link-form-item>
            <link-form-item field="comboRules" label="组合校验">
                <link-input v-model="formData.comboRules"/>
            </link-form-item>
            <list-title label="form rules 预定义的校验函数"/>

            <link-form-item field="mobilePhone" label="电话号码校验">
                <link-input v-model="formData.mobilePhone"/>
            </link-form-item>
            <link-form-item field="email" label="邮箱校验">
                <link-input v-model="formData.email"/>
            </link-form-item>
            <link-form-item field="qq" label="qq校验">
                <link-input v-model="formData.qq"/>
            </link-form-item>
            <link-form-item field="cardId" label="身份证校验">
                <link-input v-model="formData.cardId"/>
            </link-form-item>
            <link-form-item field="numberType" label="数字校验">
                <link-input v-model="formData.numberType"/>
            </link-form-item>
            <link-form-item field="testReg" label="测试正则">
                <link-input v-model="formData.testReg"/>
            </link-form-item>
        </link-form>
        <list>
            <list-title label="form rules 手动校验"/>
            <link-button block @tap="logFormData" label="打印 formData"/>
            <link-button block @tap="$refs.form.logRules()" label="打印 rules"/>
            <link-button block @tap="validate" label="开启校验"/>
        </list>
        <list>
            <list-title>表单对象</list-title>
            <item v-for="(value,key) in formData" :key="key" :desc="value" :title="key"/>
        </list>
    </link-page>
</template>

<script>
    import {ComponentUtils} from "link-taro-component";
    import {taro} from "../../../utils/taro";

    export default {
        name: "demo-form-validate-page",
        data() {
            return {
                formData: {
                    formItemRequired: '12345',
                    formItemRequiredRule: '',
                    formItemRequired1: '',
                    formItemRequired2: '',
                    formItemCustom: '',
                    rulesRequired1: '',
                    formItemAsyncValidate: '',
                    rulesRequiredAsync: '',
                    rulesRequiredAsyncError: '',
                    rulesRequiredA: '',
                    rulesRequiredB: '',
                    comboRules: '',
                    mobilePhone: '',
                    email: '',
                    qq: '',
                    cardId: '',
                    numberType: '',
                    testReg: ''
                },
                showFlag: 'a',

                formRules: {
                    // 自定义必填校验
                    rulesRequired1: this.Validator.required(),
                    // 自定义异步校验
                    rulesRequiredAsync: this.asyncValidator,
                    // 自定义异步校验的时候，异步函数执行异常
                    rulesRequiredAsyncError: this.asyncValidatorError,
                    // 自定义必填校验，并且修改必填信息
                    rulesRequiredA: this.Validator.required('开始必填'),
                    // 同一个form-item内多字段校验
                    rulesRequiredB: this.Validator.required('结束必填'),
                    // 组合校验，值为多个函数组合的数组
                    comboRules: [this.Validator.required('不能为空！'), this.Validator.len({max: 5, min: 2, minMsg: '不能少于2个字符'})],
                    // 电话号码校验
                    mobilePhone: this.Validator.phone(),
                    // 邮箱校验
                    email: this.Validator.email(),
                    // qq号码校验
                    qq: this.Validator.qq(),
                    // 身份证号校验
                    cardId: this.Validator.cardId(),
                    // 数字校验，最大最小值
                    numberType: this.Validator.number({max: 5, min: 2}),
                    // 正则校验
                    testReg: this.Validator.regexp({reg: /^1[3456789]\d{9}$/, msg: '请输入正确的电话号码'}),
                }
            }
        },
        methods: {
            async validate() {
                await this.$refs.form.validate({loading: true})
                taro.showToast({title: '校验通过'})
            },
            logFormData() {
                console.log({...this.formData})
            },
            onInput(val) {
                console.log('input', val)
            },
            customValidator(value) {
                console.log('customValidator')
                if (value != '123') {
                    return '只能输入123！'
                }
            },
            async asyncValidator(value) {
                await ComponentUtils.delay(1000)
                if (value != '123') {
                    return '只能输入123！'
                }
            },
            async asyncValidatorError(value) {
                await ComponentUtils.delay(2000)
                if (!value) {
                    throw new Error('不能没有值')
                }
            },
        }
    }
</script>

<style lang="scss">

</style>
