<template>
    <link-page class="demo-form-edit-page">
        <list>
            <item title="禁用表单">
                <link-switch :trueValue="true" :falseValue="false" v-model="flag.disabled"/>
            </item>
            <item title="只读表单">
                <link-switch :trueValue="true" :falseValue="false" v-model="flag.readonly"/>
            </item>
        </list>
        <link-form :disabled="flag.disabled" :readonly="flag.readonly" ref="form" :value="formData">
            <list>
                <link-form-item label="文本框">
                    <link-input/>
                </link-form-item>
                <list-title>
                    以下组件会自动通知 link-form-item 显示点击箭头属性
                </list-title>
                <link-form-item label="开关按钮">
                    <link-switch v-model="showLovFlag" :trueValue="true" :falseValue="false"/>
                </link-form-item>
                <link-form-item label="值列表单选">
                    <link-lov type="BURIED_TYPE" v-if="showLovFlag"/>
                </link-form-item>
                <link-form-item label="值列表多选" required field="multipleValue">
                    <link-lov type="ACCT_STAGE" v-model="formData.multipleValue" multiple/>
                </link-form-item>
                <link-form-item label="日期选择框">
                    <link-date/>
                </link-form-item>
                <link-form-item label="对象选择框" required field="lovName">
                    <link-object
                            pageTitle="yes"
                            :value="formData.lovName"
                            :row="formData"
                            :option="basicOption"
                            :map="{lovName:'name',lovVal:'val'}">
                        <template v-slot="{data}">
                            <item :title="data.name" :desc="data.val" :key="data.id" :data="data"/>
                        </template>
                    </link-object>
                </link-form-item>
                <link-form-item label="地址选择框">
                    <link-address/>
                </link-form-item>

                <link-form-item label="单项选择" :note="formData.singleSelect" required field="singleSelect">
                    <link-select v-model="formData.singleSelect">
                        <link-select-option label="微信公众号" val="subscription"/>
                        <link-select-option label="微信开发者工具" val="development tools"/>
                        <link-select-option label="微信管理平台" val="manager platform"/>
                        <link-select-option label="企业微信" val="enterprise"/>
                    </link-select>
                </link-form-item>
                <link-form-item label="多项选择" :note="String(formData.multipleSelect||'')" required field="multipleSelect">
                    <link-select v-model="formData.multipleSelect" multiple>
                        <link-select-option label="微信公众号" val="subscription"/>
                        <link-select-option label="微信开发者工具" val="development tools"/>
                        <link-select-option label="微信管理平台" val="manager platform"/>
                        <link-select-option label="企业微信" val="enterprise"/>
                    </link-select>
                </link-form-item>

                <link-form-item label="标签输入框" vertical required field="tagInput">
                    <link-tag-input v-model="formData.tagInput"/>
                </link-form-item>

                <link-form-item label="文本域输入框" vertical required field="textarea">
                    <link-textarea v-model="formData.textarea"/>
                </link-form-item>

                <link-form-item label="数字输入框" required field="number">
                    <link-number v-model="formData.number"/>
                </link-form-item>

                <link-form-item label="数字键盘输入框" required field="numberKeyboard">
                    <link-number-keyboard v-model="formData.numberKeyboard"/>
                </link-form-item>

                <!--<list-title>
                    默认情况下，会自动通过 form-item获取placeholder，这里示例自定义placeholder
                </list-title>-->
                <link-form-item label="文本框">
                    <link-input placeholder="自定义placeholder"/>
                </link-form-item>
                <link-form-item label="值列表输入框">
                    <link-lov type="BURIED_TYPE" placeholder="自定义placeholder"/>
                </link-form-item>
            </list>
        </link-form>

        <link-sticky>
            <link-button block @tap="validate">校验</link-button>
            <link-button block @tap="cancelValidate">取消校验</link-button>
        </link-sticky>
    </link-page>
</template>

<script>

    export default {
        name: "demo-form-edit-page",
        data() {

            return {
                showLovFlag: true,
                formData: {},
                basicOption: new this.AutoList(this, {
                    module: 'action/link/basic',
                }),
                flag: {
                    disabled: false,
                    readonly: false,
                },
            }
        },
        methods: {
            async validate() {
                await this.$refs.form.validate()
                this.$message.success('校验通过')
            },
            cancelValidate() {
                this.$refs.form.cancelValidate()
            },
        },
        async onShow() {
            const scene = await this.$scene.ready()
            console.log('page scene', scene)
        },
    }
</script>

<style lang="scss">

</style>