<template>
    <view class="demo-form-control-page">
        <list>
            <link-form :disabled="flag.formDisabled" :readonly="flag.formReadonly">
                <list-title>禁用</list-title>
                <link-form-item label="禁用" :disabled="flag.disabled" arrow>
                    <link-switch v-model="flag.disabled" :trueValue="true" :falseValue="false" slot="title" :disabled="false" :readonly="false"/>
                    <view>
                        <link-input value="link-input"/>
                        <link-lov type="ACCT_STAGE" value="PotentialDealer"/>
                        <link-date/>
                    </view>
                </link-form-item>
                <link-form-item label="只读" :readonly="flag.readonly">
                    <view slot="top">只读</view>
                    <link-switch v-model="flag.readonly" :trueValue="true" :falseValue="false" slot="title" :disabled="false" :readonly="false"/>
                    <view>
                        <link-input value="link-input"/>
                        <link-lov type="ACCT_STAGE" value="PotentialDealer"/>
                        <link-date/>
                    </view>
                </link-form-item>
                <link-form-item label="其他常规表单组件">
                    <view>
                        <link-input value="link-input"/>
                        <link-lov type="ACCT_STAGE" value="PotentialDealer"/>
                        <link-date/>
                    </view>
                </link-form-item>
            </link-form>
            <item title="禁用整个表单">
                <link-switch v-model="flag.formDisabled" :trueValue="true" :falseValue="false"/>
            </item>
            <item title="只读整个表单">
                <link-switch v-model="flag.formReadonly" :trueValue="true" :falseValue="false"/>
            </item>
        </list>
    </view>
</template>

<script>
    export default {
        name: "demo-form-control-page",
        data() {
            return {
                flag: {
                    disabled: true,
                    readonly: true,
                    formDisabled: false,
                    formReadonly: false,
                },
            }
        },
    }
</script>

<style lang="scss">

</style>