<template>
    <view class="demo-form-main-page">
        <demo-nav-list :menus="menus"/>
    </view>
</template>

<script>
    import DemoNavList from "../demo-nav-list";

    export default {
        name: "demo-form-main-page",
        components: {DemoNavList},
        data() {
            return {
                menus: [
                    {name: '基本表单页面', path: 'components/form/demo-form-basic-page'},
                    {name: '表单校验', path: 'components/form/demo-form-validate-page'},
                    {name: '表单控制', path: 'components/form/demo-form-control-page'},
                    {name: '表单组件', path: 'components/form/demo-form-edit-page'},
                ]
            }
        },
    }
</script>

<style lang="scss">

</style>