<template>
    <view class="demo-input-page">
        <list>
            <list-title>基本用法</list-title>
            <item :title="`绑定值：${val[0]||''}`">
                <link-input v-model="val[0]"/>
                <view slot="desc">
                    前后空格无效：{{!!val[0]?val[0].length:'null'}}
                </view>
            </item>
            <list-title>手动获取焦点，3s之后自动失去焦点</list-title>
            <item>
                <link-button @tap="customFocus" slot="title" size="mini">get focus</link-button>
                <link-input ref="input1"/>
            </item>
            <list-title>前后图标</list-title>
            <item>
                <link-input prefixIcon="mp-search" suffixIcon="mp-save"/>
            </item>
            <list-title>块级输入框</list-title>
            <link-input block style="background-color: white"/>
            <list-title>自动获取焦点</list-title>
            <link-button block label="打开新页面" @tap="$nav.push('/components/demo-input-auto-focus-page')"/>
            <list-title>前置以及后置插槽</list-title>
            <link-input block style="background-color: white">
                <view slot="prepend" style="padding-right: 16rpx">
                    验证码
                </view>
                <view slot="append" style="padding-left: 16rpx;color: #1F74FF;font-size: 24rpx;border-left: solid 2rpx #f2f2f2">
                    发送验证码
                </view>
            </link-input>
        </list>
    </view>
</template>

<script>

    export default {
        name: "demo-input-page",
        props: {},
        data() {
            return {
                val: {},
            }
        },
        methods: {
            customFocus() {
                this.$refs.input1.focus()
                /*setTimeout(() => {
                    this.$refs.input1.blur()
                }, 3 * 1000)*/
            },
        },
    }
</script>

<style lang="scss">
</style>