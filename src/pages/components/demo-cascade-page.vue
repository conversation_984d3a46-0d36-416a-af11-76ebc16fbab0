<template>
    <link-page class="demo-cascade-page">
        <list>
            <list-title>$cascade</list-title>
            <item @tap="basicUsage" title="基本用法" :content="String(formData.cascadeValue)"/>
            <item @tap="asyncUsage" title="异步加载数据"/>
            <list-title>link-cascade</list-title>
            <link-alert>
                link-cascade 需要你手动传入显示的文本。这里演示了显示选中数据中最后一个节点的数据的显示文本，
                当选择完毕之后，需要手动将最后一个节点的数据保存起来，同样的初始化表单数据的时候，你也需要将显示值查出来。
                当显示多个文本的时候，会比较困难，这里不做深入讨论。
            </link-alert>
            <item :title="`显示值：${formData.cascadeLastName}`">
                <link-cascade v-model="formData.cascadeKeys"
                              :text="formData.cascadeLastName"
                              :option="cascadeOption"
                              @select="onCascadeSelected"/>
            </item>
            <item>
                <link-button @tap="submit" mode="text">log formData</link-button>
            </item>
        </list>
    </link-page>
</template>

<script>

    import {treeData} from "./data/tree.data";

    export default {
        name: "demo-cascade-page",
        data() {
            return {
                cascadeOption: {
                    data: treeData,
                    keyField: 'id',
                    childrenField: 'subs',
                    labelField: 'name',
                },
                orgOption: {
                    data: async () => {
                        const {list} = await this.$http.post('action/link/orgnization/treelist')
                        return list.children
                    },
                    keyField: 'id',
                    childrenField: 'children',
                    labelField: 'text',
                },
                formData: {
                    cascadeValue: null
                },
            }
        },
        methods: {
            async basicUsage() {
                const {keys, rows} = await this.$cascade({
                    ...this.cascadeOption,
                    keys: this.formData.cascadeValue,
                })
                this.formData.cascadeValue = keys
                this.$message.primary(rows.map(row => row.name).join(','))
            },
            async asyncUsage() {
                const {keys, rows} = await this.$cascade(this.orgOption)
                this.orgOption.keys = keys
                this.$message.primary(rows.map(row => row.text).join(','))
            },
            onCascadeSelected({keys, rows}) {
                console.log(keys, rows)
                this.formData = {
                    ...this.formData,
                    cascadeLastName: rows[rows.length - 1].name
                }
            },
            submit() {
                console.log(this.$utils.deepcopy(this.formData))
            },
        },
    }
</script>

<style lang="scss">

</style>