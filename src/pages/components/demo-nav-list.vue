<template>
    <list>
        <item v-for="item in menus" :title="item.name" :key="item.name" @tap="onTap(item)"></item>
    </list>
</template>

<script>

    import Taro from '@tarojs/taro'

    export default {
        name: "demo-nav-list",
        props: {
            menus: {type: Array, default: () => []},
        },
        setup() {
            return {
                onTap(item) {
                    this.$nav.push(item.path, item)
                },
            }
        },
    }
</script>

<style lang="scss">

</style>