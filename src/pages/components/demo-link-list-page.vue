<template>
    <link-page>
        <list>
            <item v-for="item in 30"
                  :key="item"
                  :title="`title-${item}`"
                  :content="`content-${item}`"
                  :note="`note-${item}`"
                  :desc="`desc-${item}`"
                  :arrow="item%2 === 0"
            />
        </list>
    </link-page>
</template>

<script>
    export default {
        name: "demo-link-list-page"
    }
</script>

<style lang="scss">

</style>