<template>
    <view class="demo-icon-page">
        <list>
            <view v-for="item in icons" :key="item.name">
                <list-title>{{item.name}}</list-title>

                <list>
                    <item v-for="icon in item.icons" :key="icon" :desc="icon">
                        <link-icon :icon="icon" slot="title"/>
                    </item>
                </list>
            </view>
        </list>
    </view>
</template>

<script>

    import icons from 'src/static/lib/icon/icon.json'

    export default {
        name: "demo-icon-page",
        props: {},
        data() {
            return {
                icons,
            }
        },
        methods: {},
    }
</script>

<style lang="scss">
    .demo-icon-wrapper {
        padding: 20px 10px;
    }
</style>