<template>
    <link-page class="demo-id-service-page">
        <list>
            <item @tap="request(1)">获取 1 个id</item>
            <item @tap="request(2)">获取 2 个id</item>
            <item @tap="request(5)">获取 5 个id</item>
            <item @tap="request(10)">获取 10 个id</item>
            <item @tap="request(22)">获取 22个id</item>
        </list>

        <view style="word-break: break-word" @tap="log">
            {{JSON.stringify(IdService.data.value)}}
        </view>
    </link-page>
</template>

<script>
    import {IdService} from "link-taro-component";

    export default {
        name: "demo-id-service-page",
        data() {
            return {
                IdService,
            }
        },
        methods: {
            async request(num) {
                const ids = await Promise.all(new Array(num).fill(null).map(() => this.$newId()))
                console.log(ids)
            },
            log() {
                console.log(this.IdService.data.value)
            },
        },
    }
</script>

<style lang="scss">

</style>
