<template>
    <view class="demo-filter-bar-page">
        <list>
            <list-title>基本用法：值列表</list-title>
            <link-filter-bar
                    :option="simpleLovOption"
                    v-model="simpleLovOption.value"
            />
            <list-title>值列表：父子值列表以及排除值列表</list-title>
            <link-filter-bar
                    :option="complexLovOption"
                    v-model="complexLovOption.value"
            />
            <list-title>自定义选项</list-title>
            <link-filter-bar
                    :option="customOption"
                    v-model="customOption.value"
            />
            <list-title>自定义异步选项</list-title>
            <link-filter-bar
                    :option="customAsyncOption"
                    v-model="customAsyncOption.value"
            />
            <list-title>基本用法：值列表</list-title>
            <link-filter-bar
                    :option="initValueOption"
                    v-model="initValueOption.value"
            />
        </list>
    </view>
</template>

<script>
    export default {
        name: "demo-filter-bar-page",
        data() {
            return {
                simpleLovOption: {
                    field: 'name',
                    lov: 'BURIED_TYPE',
                },
                complexLovOption: {
                    field: 'name',
                    lov: {
                        type: 'ACCT_SUB_TYPE',
                        parentType: 'ACTIVITY_COMPANY',
                        parentVal: '15523701445361664',
                        excludeLovs: [
                            'Following',
                            'NormalConsumer'
                        ],
                    },
                },
                customOption: {
                    options: [
                        {name: '已提交', val: 'submit'},
                        {name: '进行中', val: 'processing'},
                        {name: '已完成', val: 'done'},
                    ]
                },
                customAsyncOption: {
                    options: async () => {
                        // 这里模拟网络请求
                        await this.$utils.delay(1000)
                        return [
                            {name: '已提交', val: 'submit'},
                            {name: '进行中', val: 'processing'},
                            {name: '已完成', val: 'done'},
                        ]
                    }
                },
                initValueOption: {
                    value: 'Event',
                    lov: 'BURIED_TYPE',
                },
            }
        },
    }
</script>

<style lang="scss">
    .demo-filter-bar-page {
        .link-filter-bar {
            margin: 40px 0;
        }
    }
</style>