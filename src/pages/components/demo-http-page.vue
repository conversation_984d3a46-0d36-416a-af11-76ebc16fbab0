<template>
    <view class="demo-http-page">
        <list>
            <item title="基本请求" @tap="basicRequest"/>
            <item title="GET 请求" @tap="getRequest"/>
            <item title="自定义请求" @tap="customRequest"/>
            <item title="请求：手动处理错误" @tap="customHandleFail"/>
        </list>
    </view>
</template>

<script>

    export default {
        name: "demo-http-page",
        props: {},
        data() {
            return {}
        },
        methods: {
            /**
             * 基本用法，第一个参数为请求地址，如果检测不是全路径，则会拼接baseURL
             * 第二个参数为请求参数；如果是get，参数会变成querystring拼接在url后面，如果是post请求会在请求体中
             * 第三个参数为配置信息对象
             * <AUTHOR>
             * @date    2020/7/11 16:42
             */
            async basicRequest() {
                const data = await this.$http.post('link/attachment/preDefaultValue', {customParam: 'hello world'}, {
                    loadingMessage: '正在初始化数据',
                    errorMessage: '初始化数据失败',
                })
                this.$taro.showToast({title: '初始化成功'})

                console.log(data)
            },
            /**
             * get请求实例，这个示例请求接口会报错，是因为这个接口本来就不支持get请求；
             * <AUTHOR>
             * @date    2020/7/11 16:44
             */
            async getRequest() {
                const data = await this.$http.get('link/attachment/preDefaultValue', {customParam: 'hello world'}, {
                    loadingMessage: '正在初始化 GET 数据',
                    errorMessage: '初始化 GET 数据失败',
                })
                console.log(data)
            },
            /**
             * 自定义请求
             * <AUTHOR>
             * @date    2020/7/11 16:46
             */
            async customRequest() {
                const data = await this.$http({
                    url: 'link/attachment/preDefaultValue',
                    // url路由参数
                    params: {
                        paramsId: '(paramsId)'
                    },
                    // 请求体参数
                    data: {
                        dataId: '(dataId)'
                    },
                    // 请求头
                    header: {
                        Authorization: null,
                        test: '123'
                    }
                })
                console.log(data)
            },
            /**
             * 手动处理错误请求示例
             * <AUTHOR>
             * @date    2020/7/11 17:03
             */
            async customHandleFail() {
                const data = await this.$http.get('link/attachment/preDefaultValue', {customParam: 'hello world'}, {
                    loadingMessage: '正在初始化 GET 数据',
                    // 禁用自动处理错误逻辑，默认请求出错，会自动弹框显示错误，以及记录错误日志
                    autoHandleError: false,
                    handleFailed: (response) => {
                        console.log('手动处理错误', response)
                        // 弹框显示错误
                        this.$taro.showModal({
                            title: '请求出错！',
                            // 有的接口返回的错误提示不是 response.message，而是 response.result，这里要写准确了
                            content: response.message || response.result,
                            showCancel: false
                        })
                        // 手动记录错误日志
                        // todo 待完成
                    }
                })
                console.log(data)
            },
        },
    }
</script>

<style lang="scss">
</style>