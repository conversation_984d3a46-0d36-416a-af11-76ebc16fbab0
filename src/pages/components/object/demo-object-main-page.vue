<template>
    <view class="demo-object-main-page">
        <demo-nav-list :menus="menus"/>
    </view>
</template>

<script>
    import DemoNavList from "../demo-nav-list";

    export default {
        name: "demo-object-main-page",
        components: {DemoNavList},
        data() {
            return {
                menus: [
                    {name: 'ObjectService 对象选择服务', path: 'components/object/demo-object-service-page'},
                    {name: 'link-object 对象选择框', path: 'components/object/demo-link-object-page'}
                ]
            }
        },
    }
</script>

<style lang="scss">

</style>