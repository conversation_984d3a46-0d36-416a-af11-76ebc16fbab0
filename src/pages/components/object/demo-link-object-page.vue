<template>
    <link-page class="demo-link-object-page">
        <list>
            <list-title>数据</list-title>
            <item>
                <view slot="title">
                    {{ JSON.stringify(formData) }}
                </view>
            </item>

            <list-title>插槽</list-title>
            <item title="基本用法(作用域插槽)">
                <link-object
                    pageTitle="yes"
                    :value="formData.lovName"
                    :row="formData"
                    :option="basicOption"
                    :map="{lovName:'name',lovVal:'val'}">
                    <template v-slot="{data}">
                        <item :title="data.name" :desc="data.val" :key="data.id" :data="data"/>
                    </template>
                </link-object>
            </item>

            <item title="top 以及 bottom 插槽">
                <link-object
                    :value="formData.lovName"
                    :row="formData"
                    :option="basicOption"
                    :map="{lovName:'name',lovVal:'val'}">
                    <view slot="top">top slots</view>
                    <view slot="bottom">bottom slots</view>
                    <template v-slot="{data}">
                        <item :title="data.name" :desc="data.val" :key="data.id" :data="data"/>
                    </template>
                </link-object>
            </item>

            <item title="showInDialog">
                <link-object
                    showInDialog
                    pageTitle="yes"
                    :value="formData.lovName"
                    :row="formData"
                    :option="basicOption"
                    :map="{lovName:'name',lovVal:'val'}">
                    <template v-slot="{data}">
                        <item :title="data.name" :desc="data.val" :key="data.id" :data="data">
                            <link-checkbox slot="thumb" :val="data.id" toggleOnClickItem/>
                        </item>
                    </template>
                </link-object>
            </item>

            <list-title>渲染函数</list-title>
            <item title="基本用法">
                <link-object
                    :value="formData.lovName"
                    :row="formData"
                    :option="renderOption"
                    :map="{lovName:'name',lovVal:'val'}"/>
            </item>
            <item title="顶部底部插槽">
                <link-object
                    :value="formData.lovName"
                    :row="formData"
                    :option="renderSlotOption"
                    :map="{lovName:'name',lovVal:'val'}"/>
            </item>

            <list-title>跳转前后钩子函数</list-title>
            <item>
                <link-object
                    :value="formData.lovName"
                    :row="formData"
                    :option="basicOption"
                    :map="{lovName:'name',lovVal:'val'}"
                    :beforeSelect="beforeSelect"
                    :afterSelect="afterSelect"
                >
                    <template v-slot="{data}">
                        <item :title="data.name" :desc="data.val" :key="data.id" :data="data"/>
                    </template>
                </link-object>
            </item>

            <list-title>禁用以及只读</list-title>
            <item title="禁用">
                <link-object
                    disabled
                    :value="formData.lovName"
                    :row="formData"
                    :option="basicOption"
                    :map="{lovName:'name',lovVal:'val'}">
                    <template v-slot="{data}">
                        <item :title="data.name" :desc="data.val" :key="data.id" :data="data"/>
                    </template>
                </link-object>
            </item>
            <item title="只读">
                <link-object
                    readonly
                    :value="formData.lovName"
                    :row="formData"
                    :option="basicOption"
                    :map="{lovName:'name',lovVal:'val'}">
                    <template v-slot="{data}">
                        <item :title="data.name" :desc="data.val" :key="data.id" :data="data"/>
                    </template>
                </link-object>
            </item>

        </list>
    </link-page>
</template>

<script>
export default {
    name: "demo-link-object-page",
    data() {

        /**
         * 基本用法，渲染内容靠 link-object 的作用域插槽
         * <AUTHOR>
         * @date    2020/7/14 14:55
         */
        const basicOption = new this.AutoList(this, {
            module: 'action/link/basic',
            // cacheParam: {cache: 'private', expireSecond: 60},
        })

        /**
         * 渲染函数，基本用法
         * <AUTHOR>
         * @date    2020/7/14 14:55
         */
        const renderOption = new this.AutoList(this, {
            module: 'action/link/basic',
            renderFunc: (h, {data}) => {
                return (<item title={data.name} key={data.id} data={data}/>)
            }
        })

        /**
         * 渲染函数中，渲染顶部底部插槽
         * <AUTHOR>
         * @date    2020/7/14 14:55
         */
        const renderSlotOption = new this.AutoList(this, {
            module: 'action/link/basic',
            renderFunc: (h, {data}) => {
                return (<item title={data.name} key={data.id} data={data}/>)
            },
            slots: {
                top: () => <view>top slots</view>,
                bottom: () => <view>bottom slots</view>,
            }
        })

        const formData = {lovVal: null,}

        return {
            basicOption,
            renderOption,
            renderSlotOption,
            formData,

            beforeSelect: () => {
                if (Math.random() > 0.5) {
                    this.$taro.showToast({title: '随机禁止'})
                    return Promise.reject('随机禁止')
                }
            },
            afterSelect: (data) => {
                console.log('afterSelect', {...data})
            }
        }
    },
}
</script>

<style lang="scss">

</style>
