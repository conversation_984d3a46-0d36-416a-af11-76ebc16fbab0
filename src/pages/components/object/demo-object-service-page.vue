<template>
    <link-page class="demo-object-service-page">
        <list>
            <link-alert>跳转页面选择</link-alert>
            <list-title>单选</list-title>
            <item title="基本用法" @tap="basicUsage">
                {{val[0]}}
            </item>
            <list-title>多选</list-title>
            <item title="基本用法" @tap="multipleUsage">
                {{val[1]}}
            </item>
            <item title="禁用部分选项" note="显示值长度大于5的不可以选中" @tap="disabledUsage">
                {{val[2]}}
            </item>
            <item title="回退时redirect到指定页面" @tap="specificBackPage"/>
            <item title="打开的时候显示已经选中的选项" note="以下为已经选中的数据列表" @tap="selectedUsage"/>
            <view v-for="item in selected" :key="item.id">
                {{item.name}}
            </view>
            <link-alert>弹框选择</link-alert>
            <item title="基本用法" @tap="basicUsageOfDialog">
                {{val[0]}}
            </item>
            <item title="多选" @tap="multipleUsageOfDialog" content="选中值在上面的选中列表中"/>
        </list>
    </link-page>
</template>

<script>

    export default {
        name: "demo-object-service-page",
        data() {
            return {
                basicOption: new this.AutoList(this, {
                    module: 'action/link/basic',
                    sortOptions: [
                        {label: '编号', field: 'id'},
                        {label: '代码', field: 'val', desc: false},
                    ],
                    searchFields: ['name', 'val'],
                    filterOption: [
                        {
                            label: '安全性筛选',
                            type: 'oauth',
                        },
                        // 普通文本模糊筛选
                        {label: '客户姓名', field: 'acctName', type: 'text'},
                        // 数字范围筛选
                        {label: '客户年龄', field: 'acctAge', type: 'number'},
                        // 日期筛选
                        {label: '客户生日', field: 'acctBirthday', type: 'date'},
                        // 值列表筛选
                        {label: '客户级别', field: 'acctLevel', type: 'lov', lov: 'ACCT_LEVEL'},
                        // 自定义筛选
                        // 可以自己通过异步函数请求数据，然后 autoList.option.filterOption.options.push(自定义筛选选项)实现异步添加自定义筛选
                        // 上面的 autoList = new AutoList({...})
                        {
                            label: '客户负责人', field: 'acctMaster', type: 'select', data: [
                                {name: 'January', val: '1'},
                                {name: 'February', val: '2'},
                                {name: 'March', val: '3'},
                                {name: 'April', val: '4'},
                                {name: 'May', val: '5'},
                                {name: 'June', val: '6'},
                                {name: 'July', val: '7'},
                                {name: 'August', val: '8'},
                                {name: 'September', val: '9'},
                                {name: 'October', val: '10'},
                                {name: 'November', val: '11'},
                                {name: 'December', val: '12'},
                            ]
                        },
                    ],
                    filterBar: {
                        field: 'name',
                        // lov: 'BURIED_TYPE',
                        autoReload: false,              // 禁止点击自动刷新
                        notNull: true,                  // 禁止反选为null
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} title={data.name} data={data}>
                                {data.val}
                            </item>
                        )
                    }
                }),

                multipleOption: new this.AutoList(this, {
                    module: 'action/link/basic',
                    searchFields: ['name', 'val'],
                    param: {
                        filtersRaw: [
                            {property: 'name', id: 'name', value: '添加', operator: 'like'}
                        ]
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} title={data.name} data={data}>
                                <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                                {data.val}
                            </item>
                        )
                    }
                }),
                disabledOption: new this.AutoList(this, {
                    module: 'action/link/basic',
                    searchFields: ['name', 'val'],
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} title={data.name} data={data}>
                                <link-checkbox val={data.id} toggleOnClickItem slot="thumb" disabled={data.name.length > 5}/>
                                {data.val}
                            </item>
                        )
                    }
                }),

                basicOptionInDialog: new this.AutoList(this, {
                    module: 'action/link/basic',
                    sortOptions: [
                        {label: '编号', field: 'id'},
                        {label: '代码', field: 'val', desc: false},
                    ],
                    searchFields: ['name', 'val'],
                    filterOption: [
                        {
                            label: '安全性筛选',
                            type: 'oauth',
                        },
                    ],
                    filterBar: {
                        field: 'name',
                        // lov: 'BURIED_TYPE',
                        autoReload: false,              // 禁止点击自动刷新
                        notNull: true,                  // 禁止反选为null
                    },
                    slots: {
                        searchRight: () => (
                            <link-button mode="text" label="查询所售产品" onTap={this.searchSaleProds} style="padding-left: 8px"/>
                        )
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} title={data.name} data={data}>
                                <link-checkbox val={data.id} toggleOnClickItem/>
                            </item>
                        )
                    }
                }),
                val: {
                    0: null,
                    1: null,
                    2: null,
                },
                selected: [],
            }
        },
        methods: {
            async basicUsage() {
                const data = await this.$object(this.basicOption)
                this.val[0] = data.name
                console.log({...data})
            },
            async multipleUsage() {
                const list = await this.$object(this.multipleOption, {multiple: true})
                this.val[1] = list.map(item => item.name).join(',')
            },
            async disabledUsage() {
                const list = await this.$object(this.disabledOption, {multiple: true})
                this.val[2] = list.map(item => item.name).join(',')
            },
            async selectedUsage() {
                const list = await this.$object(this.multipleOption, {multiple: true, selected: this.selected.map(item => item.id)})
                this.selected = list
            },
            async specificBackPage() {
                this.$object(this.basicOption, {backPath: 'components/object/demo-object-redirect-page.vue',})
            },
            /*---------------------------------------弹框选择-------------------------------------------*/
            async basicUsageOfDialog() {
                const data = await this.$object(this.basicOptionInDialog, {
                    showInDialog: true,
                    /*beforeConfirm: async (row) => {
                        row.name = 'hello world'
                    },*/
                    autoListProps: {searchInputBinding: {props: {placeholder: '搜索名称以及代码'}}}
                })
                this.val[0] = data.name
                console.log({...data})
            },
            async multipleUsageOfDialog() {
                const list = await this.$object(this.multipleOption, {
                    multiple: true,
                    showInDialog: true,
                    selected: this.selected.map(item => item.id),
                    /*beforeConfirm: async (rows) => {
                        if (rows.length > 3) {
                            this.$showError('最多不能超过3条数据')
                            return Promise.reject()
                        }
                    },*/
                })
                this.selected = list
            },
            async searchSaleProds() {
                await this.$dialog.closeAll()
                await this.multipleUsageOfDialog()
            },
        },
        async mounted() {
            // await this.$utils.delay(1000)
            // this.basicUsageOfDialog()
        },
    }
</script>

<style lang="scss">

</style>
