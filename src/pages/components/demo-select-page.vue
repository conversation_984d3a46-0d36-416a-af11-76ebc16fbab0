<template>
    <view class="demo-select-page">
        <list>
            <list-title>$select：单选</list-title>
            <item title="单选：基础用法" @tap="singleSelect">{{val[0]}}</item>
            <item title="单选：自定义内容" @tap="singleCustomSelect">{{val[1]}}</item>

            <list-title>$select：多选</list-title>
            <item title="多选：基础用法" @tap="multipleSelect">{{val[2]}}</item>
            <item title="多选：自定义内容" @tap="multipleCustomSelect">{{val[3]}}</item>
            <item title="多选：禁用部分选项" @tap="multipleCustomDisabledSelect">{{val[4]}}</item>
            <item title="多选：已经选中的选项" @tap="multipleShowSelect">{{val[5]}}</item>

            <list-title>link-select</list-title>
            <item :title="val[6]" note="基础用法">
                <link-select v-model="val[6]">
                    <link-select-option v-for="(data,index) in complexSelectData" :label="data.tag" :val="data.code" :key="index"/>
                </link-select>
            </item>
            <item :title="val[6]" note="禁用部分选项">
                <link-select v-model="val[6]">
                    <link-select-option v-for="(data,index) in complexSelectData" :label="data.tag" :val="data.code" :key="index" :disabled="data.tag.length>5"/>
                </link-select>
            </item>

            <item :title="val[6]" note="自定义内容，并禁用选项">
                <link-select v-model="val[6]">
                    <link-select-option v-for="(data,index) in complexSelectData" :key="index" :label="data.tag" :val="data.code">
                        <item :data="data.code" :disabled="data.tag.length>5">
                            <view slot="title">{{index}}、{{data.tag}}</view>
                            <view slot="desc">{{data.code}}</view>
                        </item>
                    </link-select-option>
                </link-select>
            </item>

            <list-title>link-select: 多选</list-title>
            <item :title="String(val[7]||'')">
                <link-select v-model="val[7]" multiple>
                    <link-select-option v-for="(data,index) in complexSelectData" :label="data.tag" :val="data.code" :key="index"/>
                </link-select>
            </item>

            <item note="自定义内容，并且禁用部分选项">
                  <link-select v-model="val[7]" multiple>
                    <link-select-option v-for="(data,index) in complexSelectData" :label="data.tag" :val="data.code" :key="index">
                        <item :arrow="false">
                            <link-checkbox slot="thumb" :val="data.code" toggleOnClickItem :disabled="data.tag.length>5"/>
                            <view slot="title">{{index}}、{{data.tag}}</view>
                            <view slot="desc">{{data.code}}</view>
                        </item>
                    </link-select-option>
                </link-select>
            </item>

        </list>
    </view>
</template>

<script>
    export default {
        name: "demo-select-page",
        data() {
            const basicSelectData = [
                '微信公众号',
                '微信开发者工具',
                '微信管理平台',
                '企业微信'
            ]

            const complexSelectData = [
                {tag: '微信公众号', code: 'subscription'},
                {tag: '微信开发者工具', code: 'development tools'},
                {tag: '微信管理平台', code: 'manager platform'},
                {tag: '企业微信', code: 'enterprise'},
            ]

            return {
                basicSelectData,
                complexSelectData,
                selectedVals: [],
                val: {
                    0: null,
                    1: null,
                    2: null,
                    3: null,
                    4: null,
                    5: null,
                }
            }
        },
        methods: {
            async singleSelect() {
                const data = await this.$select(this.basicSelectData)
                this.val[0] = data
            },
            async singleCustomSelect() {
                const data = await this.$select(this.complexSelectData, {
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data}>
                                <view slot="title">{data.tag}</view>
                                <view slot="note">{data.code}</view>
                            </item>
                        )
                    }
                })
                this.val[1] = data.tag
            },
            async multipleSelect() {
                const list = await this.$select(this.basicSelectData, {
                    multiple: true,
                    renderFunc: (h, {data, index}) => (
                        <item key={index} title={data} arrow={false}>
                            <link-checkbox toggleOnClickItem val={data} slot="thumb"/>
                        </item>
                    )
                })
                this.val[2] = list.join(',')
            },
            async multipleCustomSelect() {
                const list = await this.$select(this.complexSelectData, {
                    multiple: true,
                    renderFunc: (h, {data, index}) => (
                        <item key={index} arrow={false}>
                            <link-checkbox toggleOnClickItem val={data.code} slot="thumb"/>
                            <view slot="title">{index + 1}、{data.tag}</view>
                            <view slot="desc">{data.code}</view>
                        </item>
                    )
                })
                const selectedList = this.complexSelectData.filter(({code}) => list.indexOf(code) > -1)

                console.log({
                    list,                       // 给 link-checkbox 绑定的 val属性值数组
                    selectedList,               // 根据 list 得到的 complexSelectData 中选中的记录数组
                })

                this.val[3] = selectedList.map(({tag}) => tag).join(',')
            },
            async multipleCustomDisabledSelect() {
                const list = await this.$select(this.complexSelectData, {
                    multiple: true,
                    renderFunc: (h, {data, index}) => (
                        <item key={index} arrow={false}>
                            <link-checkbox toggleOnClickItem val={data.code} slot="thumb" disabled={data.tag.length > 5}/>
                            <view slot="title">{index + 1}、{data.tag}</view>
                            <view slot="desc">{data.code}</view>
                        </item>
                    )
                })
                const selectedList = this.complexSelectData.filter(({code}) => list.indexOf(code) > -1)

                this.val[4] = selectedList.map(({tag}) => tag).join(',')
            },
            async multipleShowSelect() {
                const list = await this.$select(this.complexSelectData, {
                    multiple: true,
                    selected: this.selectedVals,
                    renderFunc: (h, {data, index}) => (
                        <item key={index} arrow={false}>
                            <link-checkbox toggleOnClickItem val={data.code} slot="thumb"/>
                            <view slot="title">{index + 1}、{data.tag}</view>
                            <view slot="desc">{data.code}</view>
                        </item>
                    )
                })
                const selectedList = this.complexSelectData.filter(({code}) => list.indexOf(code) > -1)
                this.val[5] = selectedList.map(({tag}) => tag).join(',')

                // 存起来，下一次打开选择界面的时候，传递给option
                this.selectedVals = list
            },
        }
    }
</script>

<style lang="scss">

</style>
