<template>
    <link-page class="demo-dropdown-group-page">
        <list>
            <link-alert>以下为$dropdown使用示例</link-alert>
            <item title="基本用法">
                <link-button @tap="basic">更多操作</link-button>
            </item>
            <item title="主题">
                <link-button @tap="showTheme('lite',$event)" style="margin-right: 20px">lite</link-button>
                <link-button @tap="showTheme('dark',$event)">dark</link-button>
            </item>
            <item title="位置"/>
            <view class="demo-position">
                <template v-for="placement in ['top','bottom','left','right']">
                    <view>
                        <template v-for="align in ['start','center','end']">
                            <link-button :key="`${placement}-${align}`" @tap="position(placement,align,$event)" size="mini">
                                {{`${placement}-${align}`}}
                            </link-button>
                        </template>
                    </view>
                </template>
            </view>
            <link-alert>link-dropdown-group使用示例</link-alert>
            <item title="基本用法">
                <link-dropdown-group>
                    <link-button>库存</link-button>
                    <view slot="dropdown">
                        <link-dropdown-menu label="库存盘点" @tap="$message.warn('库存盘点')"/>
                        <link-dropdown-menu label="库存查询" @tap="$message.warn('库存查询')"/>
                    </view>
                </link-dropdown-group>
            </item>
            <item title="其他属性">
                <link-dropdown-group theme="dark" placement="left" align="end">
                    <link-button>操作</link-button>
                    <view slot="dropdown">
                        <link-dropdown-menu label="库存盘点" @tap="$message.warn('库存盘点')"/>
                        <link-dropdown-menu label="库存查询" @tap="$message.warn('库存查询')" disabled/>
                        <link-dropdown-menu label="样品管理" @tap="$message.warn('样品管理')"/>
                        <link-dropdown-menu label="占用资金" @tap="$message.warn('占用资金')"/>
                    </view>
                </link-dropdown-group>
            </item>
            <item title="model绑定">
                <link-dropdown-group v-model="showFlag" placement="left" theme="dark">
                    <link-button>库存</link-button>
                    <view slot="dropdown">
                        <link-dropdown-menu label="库存盘点" @tap="$message.warn('库存盘点')"/>
                        <link-dropdown-menu label="库存查询" @tap="$message.warn('库存查询')"/>
                    </view>
                </link-dropdown-group>
                <link-switch v-model="showFlag" :trueValue="true" :falseValue="false"/>
            </item>
            <item title="禁用">
                <link-dropdown-group disabled>
                    <link-button>库存</link-button>
                    <view slot="dropdown">
                        <link-dropdown-menu label="库存盘点" @tap="$message.warn('库存盘点')"/>
                        <link-dropdown-menu label="库存查询" @tap="$message.warn('库存查询')"/>
                    </view>
                </link-dropdown-group>
            </item>
            <link-alert status="warn">
                link-dropdown-group相比较于$dropdown的缺点是，link-dropdown-group需要给target包裹一层节点，
                这个节点可能会影响css，比如步骤条 link-step 就不能用 link-dropdown-group；
            </link-alert>
            <link-alert>link-dropdown-menu的一些用法</link-alert>
            <item title="图标选项">
                <link-dropdown-group>
                    <link-button>库存</link-button>
                    <view slot="dropdown">
                        <link-dropdown-menu icon="icon-saoma" label="库存盘点" @tap="$message.primary('库存盘点')"/>
                        <link-dropdown-menu icon="icon-zhankai" label="库存查询" @tap="$message.warn('库存查询')"/>
                    </view>
                </link-dropdown-group>
            </item>
            <item title="插槽" desc="$dropdown中的link-dropdown-menu一样可以使用插槽">
                <link-dropdown-group placement="top">
                    <link-button>库存</link-button>
                    <view slot="dropdown">
                        <link-dropdown-menu @tap="$message.primary('库存盘点')">
                            <link-icon icon="icon-saoma"/>
                            <text>库存盘点。。</text>
                        </link-dropdown-menu>
                        <link-dropdown-menu @tap="$message.warn('库存查询')">
                            <link-icon icon="icon-zhankai"/>
                            <text>库存查询。。</text>
                        </link-dropdown-menu>
                    </view>
                </link-dropdown-group>
            </item>
        </list>
    </link-page>
</template>

<script>
    export default {
        name: "demo-dropdown-group-page",
        data() {
            return {
                showFlag: false,
            }
        },
        methods: {
            basic(e) {
                this.$dropdown({
                    target: e,
                    render: () => {
                        return (
                            <view>
                                <link-dropdown-menu label="库存盘点" onTap={() => this.$message.warn('库存盘点')}/>
                                <link-dropdown-menu label="库存查询" onTap={() => this.$message.primary('库存查询')}/>
                            </view>
                        )
                    },
                })
            },
            showTheme(theme, e) {
                this.$dropdown({
                    target: e,
                    theme,
                    render: () => {
                        return (
                            <view>
                                <link-dropdown-menu label="库存盘点" onTap={() => this.$message.warn('库存盘点')}/>
                                <link-dropdown-menu label="库存查询" onTap={() => this.$message.primary('库存查询')} disabled/>
                                <link-dropdown-menu label="样品管理" onTap={() => this.$message.warn('样品管理')}/>
                                <link-dropdown-menu label="占用资金" onTap={() => this.$message.primary('占用资金')}/>
                            </view>
                        )
                    },
                })
            },
            position(placement, align, e) {
                this.$dropdown({
                    target: e,
                    placement, align,
                    render: () => {
                        return (
                            <view>
                                <link-dropdown-menu label="库存盘点" onTap={() => this.$message.warn('库存盘点')}/>
                                <link-dropdown-menu label="库存查询" onTap={() => this.$message.primary('库存查询')}/>
                            </view>
                        )
                    },
                })
            },
        },
    }
</script>

<style lang="scss">
    .demo-dropdown-group-page {
        .demo-position {
            padding: 20PX 0;
            text-align: center;

            .link-button {
                margin-right: 6PX;
                margin-bottom: 6PX;
            }
        }
    }
</style>