<template>
    <view class="demo-segment-page">
        <list>
            <list-title>
                基本用法
            </list-title>
            <link-segment-group v-model="val[0]">
                <link-segment label="增值税专用发票"/>
                <link-segment label="普通发票"/>
                <link-segment label="个人发票"/>
            </link-segment-group>
            <list-title>
                自定义绑定值：{{val[1]}}
            </list-title>
            <link-segment-group v-model="val[1]">
                <link-segment label="增值税专用发票" val="special_invoice"/>
                <link-segment label="普通发票" val="normal_invoice"/>
                <link-segment label="个人发票" val="individual_invoice"/>
            </link-segment-group>

            <item title="顶部底部边距"/>
            <link-segment-group borderTop borderBottom value="普通发票">
                <link-segment label="增值税专用发票"/>
                <link-segment label="普通发票"/>
                <link-segment label="个人发票"/>
            </link-segment-group>
            <item title="底部内容"/>
            <list-title>插槽</list-title>
            <link-segment-group v-model="val[1]">
                <link-segment val="special_invoice">
                    <view>增值税专用发票</view>
                    <link-icon icon="icon-danwei"/>
                </link-segment>
                <link-segment val="normal_invoice">
                    <view>普通发票</view>
                    <link-icon icon="icon-beizhu"/>
                </link-segment>
                <link-segment val="individual_invoice">
                    <view>个人发票</view>
                    <link-icon icon="icon-wallet"/>
                </link-segment>
            </link-segment-group>
        </list>
    </view>
</template>

<script>
    export default {
        name: "demo-segment-page",
        data() {
            return {
                val: {
                    0: '普通发票',
                    1: 'special_invoice',
                },
            }
        },
    }
</script>

<style lang="scss">

</style>