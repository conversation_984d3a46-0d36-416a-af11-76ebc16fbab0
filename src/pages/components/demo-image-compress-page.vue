<template>
    <link-page class="demo-image-compress-page">
        <link-alert>单个文件压缩</link-alert>
        <link-button block shadow label="图片压缩服务" @tap="compressService"/>
        <link-button block shadow label="图片压缩服务:限制高度200px" @tap="compressServiceLimitHeight"/>
        <item title="压缩后的图片"/>
        <image v-if="image.compress.width"
               mode="aspectFill"
               :src="image.compress.path"
               :style="{
                    width:`${image.compress.width}px`,
                    height:`${image.compress.height}px`,
                }"/>
        <item title="原始图片"/>
        <image v-if="image.source.path"
               mode="aspectFill"
               :src="image.source.path"
               :style="{
                    width:`${image.compress.width}px`,
                    height:`${image.compress.height}px`,
                }"/>
        <link-alert>多个文件压缩</link-alert>
        <link-button block shadow label="图片压缩服务:批量压缩图片" @tap="multiCompress"/>
        <item title="压缩后的图片"/>
        <image v-for="img in images"
               mode="aspectFill"
               :src="img.compress.path"
               :style="{
                    width:`${img.compress.width}px`,
                    height:`${img.compress.height}px`,
                }"
        />
        <item title="原始图片"/>
        <image v-for="img in images"
               mode="aspectFill"
               :src="img.source.path"
               :style="{
                    width:`${img.compress.width}px`,
                    height:`${img.compress.height}px`,
                }"
        />

    </link-page>
</template>

<script>
    export default {
        name: "demo-image-compress-page",
        data() {
            return {
                image: {
                    source: {
                        path: null,
                        width: null,
                        height: null,
                    },
                    compress: {
                        path: null,
                        width: null,
                        height: null,
                    },
                },
                images: [],
            }
        },
        methods: {
            async compressService() {
                const resp = await this.$taro.chooseImage({
                    count: 1,
                    sourceType: ['album', 'camera'],
                })
                const {source, compress} = await this.$image.compress({
                    filePath: resp.tempFilePaths[0],
                })
                console.log({source, compress})
                this.image = {source, compress}
            },
            async compressServiceLimitHeight() {
                const resp = await this.$taro.chooseImage({
                    count: 1,
                    sourceType: ['album', 'camera'],
                })
                const {source, compress} = await this.$image.compress({
                    filePath: resp.tempFilePaths[0],
                    limitHeight: 200
                })
                console.log({source, compress})
                this.image = {source, compress}
            },
            async multiCompress() {
                const resp = await this.$taro.chooseImage({
                    count: 9,
                    sourceType: ['album', 'camera'],
                })
                console.log(resp)
                const ret = await Promise.all(resp.tempFilePaths.map(filePath => this.$image.compress({filePath})))
                this.images = ret
                console.log(ret)
            }
        },
    }
</script>

<style lang="scss">

</style>