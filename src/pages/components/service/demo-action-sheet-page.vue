<template>
    <link-page class="demo-action-sheet-page">
        <list>
            <item title="基本用法">
                <link-button @tap="demo1" mode="text">打开菜单</link-button>
            </item>
            <item title="标题以及取消事件">
                <link-button @tap="demo2" mode="text">打开菜单</link-button>
            </item>
            <item title="不要取消按钮">
                <link-button @tap="demo3" mode="text">打开菜单</link-button>
            </item>
            <item title="选项状态">
                <link-button @tap="demo4" mode="text">打开菜单</link-button>
            </item>
            <item title="自定义选项内容">
                <link-button @tap="demo5" mode="text">打开菜单</link-button>
            </item>
            <item title="Dialog属性控制" note="顶部菜单，并且无法取消，必须选中一个选项">
                <link-button @tap="demoDialogProps" mode="text">打开菜单</link-button>
            </item>
        </list>
    </link-page>
</template>

<script>
    export default {
        name: "demo-action-sheet-page",
        methods: {
            demo1() {
                this.$actionSheet(() => (
                    <link-action-sheet>
                        <link-action-sheet-item label="拍照上传" onTap={() => this.$message.primary('拍照上传')}/>
                        <link-action-sheet-item label="从相册选择" onTap={() => this.$message.warn('从相册选择')}/>
                    </link-action-sheet>
                ))
            },
            demo2() {
                this.$actionSheet(() => (
                    <link-action-sheet title="请选择一张图片" onCancel={() => this.$showError('取消')}>
                        <link-action-sheet-item label="拍照上传" onTap={() => this.$message.primary('拍照上传')}/>
                        <link-action-sheet-item label="从相册选择" onTap={() => this.$message.warn('从相册选择')}/>
                        <link-action-sheet-item label="从云端选择" onTap={() => this.$message.warn('从云端选择')}/>
                    </link-action-sheet>
                ))
            },
            demo3() {
                this.$actionSheet(() => (
                    <link-action-sheet cancelText={null}>
                        <link-action-sheet-item label="拍照上传" onTap={() => this.$message.primary('拍照上传')}/>
                        <link-action-sheet-item label="从相册选择" onTap={() => this.$message.warn('从相册选择')}/>
                    </link-action-sheet>
                ))
            },
            demo4() {
                this.$actionSheet(() => (
                    <link-action-sheet title="请选择一张图片" onCancel={() => this.$showError('取消')}>
                        <link-action-sheet-item label="拍照上传" status="primary" onTap={() => this.$message.primary('拍照上传')}/>
                        <link-action-sheet-item label="从相册选择" status="success" onTap={() => this.$message.success('从相册选择')}/>
                        <link-action-sheet-item label="从云端选择" status="warn" onTap={() => this.$message.warn('从云端选择')}/>
                        <link-action-sheet-item label="被禁用的选项" disabled onTap={() => this.$message.warn('被禁用的选项')}/>
                    </link-action-sheet>
                ))
            },
            demo5() {
                this.$actionSheet(() => (
                    <link-action-sheet>
                        <link-action-sheet-item onTap={() => this.$message.primary('扫码')}>
                            <link-icon icon="icon-saoma"/>
                            <text>扫码</text>
                        </link-action-sheet-item>
                        <link-action-sheet-item onTap={() => this.$message.warn('分享')}>
                            <link-icon icon="icon-fenxiang"/>
                            <text>分享</text>
                        </link-action-sheet-item>
                    </link-action-sheet>
                ))
            },
            demoDialogProps() {
                this.$actionSheet({
                    render: () => (
                        <link-action-sheet cancelText={null}>
                            <link-action-sheet-item label="拍照上传" onTap={() => this.$message.primary('拍照上传')}/>
                            <link-action-sheet-item label="从相册选择" onTap={() => this.$message.warn('从相册选择')}/>
                        </link-action-sheet>
                    ),
                    config: {
                        dialogProps: {
                            position: 'top',
                            disabledHideOnClickMask: true,
                        }
                    }
                })
            },
        },
        async mounted() {
            // await this.$utils.delay(1000)
            // this.demo1()
        }
    }
</script>

<style lang="scss">

</style>