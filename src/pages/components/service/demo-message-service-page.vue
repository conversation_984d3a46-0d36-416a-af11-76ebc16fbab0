<template>
    <link-page class="demo-message-service-page">
        <list>
            <item title="基本用法" @tap="basicUsage"/>
            <list-title>状态</list-title>
            <item title="基本（默认）" @tap="statusUsage('primary')"/>
            <item title="成功" @tap="statusUsage('success')"/>
            <item title="警告" @tap="statusUsage('warn')"/>
            <item title="失败" @tap="statusUsage('error')"/>
            <item title="提问" @tap="statusUsage('info')"/>
            <list-title>自定义渲染内容</list-title>
            <item>
                <link-button label="show message" @tap="customRender" slot="title"/>
                <link-input v-model="bindingText"/>
            </item>
            <list-title>手动控制消失</list-title>
            <item title="show message" @tap="customHandler"/>
            <item title="超长文本" @tap="longText"/>

        </list>
    </link-page>
</template>

<script>
    export default {
        name: "demo-message-service-page",
        data() {
            return {
                flag: false,
                bindingText: 'abc',
            }
        },
        methods: {
            basicUsage() {
                this.$message({message: '基本用法消息'})
            },
            statusUsage(type) {
                /**
                 * 实际上在用的时候，都是这样用的：
                 * this.$message.success('提示信息')
                 * this.$showError('提示信息')
                 *
                 * <AUTHOR>
                 * @date    2020/7/27 16:00
                 */
                this.$message[type]('带状态提示')
            },
            customRender() {
                this.$message({
                    showIcon: false,
                    closeOnTap: false,
                    render: (h) => {
                        return [
                            '绑定页面上的变量',
                            <link-input v-model={this.bindingText}/>
                        ]
                    }
                })
            },
            customHandler() {
                const option = this.$message({
                    message: '手动控制关闭',
                    duration: 0,
                    onTap: () => {
                        console.log('关闭消息')
                        option.close()
                    }
                })
            },
            longText() {
                this.$message({message: '基本用法消息基本用法消息基本用法消息基本用法消息基本用法消息基本用法消息基本用法消息基本用法消息基本用法消息基本用法消息基本用法消息基本用法消息'})
            },
        }
    }
</script>

<style lang="scss">

</style>