<template>
    <link-page class="demo-dialog-service-page">
        <list>
            <item title="基本用法" @tap="basicUsage"/>
            <item title="自定义弹框内容" @tap="customUsage" :desc="buttonText"/>
            <item title="确认对话框" @tap="confirmDialog"/>
            <item title="自定义对话框属性" @tap="customDialogProps"/>
            <item title="顶部底部插槽" @tap="dialogSlots"/>

            <list-title>dialog服务的实例默认是单例的，实例不够时，会自动创建新的dialog实例提供服务</list-title>
            <item title="同时打开多个弹框" @tap="multipleUsage"/>

        </list>
    </link-page>
</template>

<script>
    export default {
        name: "demo-dialog-service-page",
        data() {
            return {
                buttonText: '自定义按钮文本'
            }
        },
        methods: {
            basicUsage() {
                this.$dialog({
                    title: '提示',
                    content: '确认要清空搜索历史记录吗？',
                    cancelButton: true,
                    onConfirm: () => {
                        this.$message.primary('确定删除')
                    },
                    onCancel: () => {
                        this.$message.info('取消删除')
                    }
                })
            },
            customUsage() {
                this.$dialog({
                    title: '标题还是通过title设置',
                    content: (h) => {
                        return (
                            <view>
                                <link-button onTap={() => console.log('点击了自定义按钮')}>
                                    {this.buttonText}
                                </link-button>
                                <link-input v-model={this.buttonText}/>
                                值列表：{this.$filter.lov('PotentialConsumer', 'ACCT_STAGE')}
                            </view>
                        )
                    },
                })
            },
            multipleUsage() {
                this.$dialog({
                    title: '提示',
                    content: '确认要清空搜索历史记录吗？',
                    cancelButton: true,
                    onConfirm: () => {

                        this.$dialog({
                            title: '真的吗',
                            content: '真的要删除吗？',
                            cancelButton: true,
                            onConfirm: () => {
                                this.$message.primary('确定删除')
                            },
                            onCancel: () => {
                                this.$message.info('取消删除,2')
                            }
                        })

                        return false
                    },
                    onCancel: () => {
                        this.$message.info('取消删除,1')
                    }
                })
            },
            async confirmDialog() {
                await this.$dialog.confirm('确定要删除该记录吗？')
                this.$message.primary('确定删除')
            },
            customDialogProps() {
                this.$dialog({
                    title: '提示',
                    content: h => (
                        <view style="width:100%">尊敬的客户2看这个您好，欢迎加入国窖荟会员俱乐部，点击https://crmtest.lzlj.com/uh5/member/register.html?会员所属品牌公司id了解更多会员专属特权！</view>
                    ),
                    cancelButton: true,
                    dialogProps: {
                        borderRadius: false,            // 无圆角
                        verticalFootButton: true,       // foot 按钮纵向排列
                    },
                    onConfirm: () => {
                        this.$message.primary('confirm')
                    },
                    onCancel: () => {
                        this.$message.info('cancel')
                    }
                })
            },
            dialogSlots() {
                this.$dialog({
                    content: () => <view>content</view>,
                    slots: {
                        head: <view>head</view>,
                        foot: <view>foot</view>,
                    }
                })
            },
        }
    }
</script>

<style lang="scss">

</style>