<template>
    <demo-nav-list :menus="menus"/>
</template>

<script>
    import <PERSON><PERSON><PERSON><PERSON><PERSON>ist from "../demo-nav-list";
    export default {
        name: "demo-page-main-page",
        components: {DemoNavList},
        data() {
            return {
                menus: [
                    {name: '基本用法', path: 'components/page/demo-page-no-sticky-page'},
                    {name: '顶部Sticky', path: 'components/page/demo-page-top-sticky-page'},
                    {name: '底部Sticky', path: 'components/page/demo-page-bottom-sticky-page'},
                    {name: '顶部以及底部Sticky', path: 'components/page/demo-page-sticky-page'},
                ]
            }
        },
    }
</script>

<style lang="scss">

</style>