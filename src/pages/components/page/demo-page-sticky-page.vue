<template>
    <link-page class="demo-page-no-sticky-page">
        <link-sticky top>
            <link-button block>吸附在顶部</link-button>
        </link-sticky>
        <list>
            <item v-for="item in 30" :title="`${item}.`" :key="item"/>
        </list>
        <link-sticky>
            <link-button mode="text">取消</link-button>
            <link-button>确认</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    export default {
        name: "demo-page-no-sticky-page"
    }
</script>

<style lang="scss">

</style>