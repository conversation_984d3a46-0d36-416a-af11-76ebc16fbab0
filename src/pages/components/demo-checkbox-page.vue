<template>
    <link-page class="demo-checkbox-page">
        <list>
            <list-title>基本用法</list-title>
            <item :title="val[0]">
                <link-checkbox v-model="val[0]"/>
            </item>

            <list-title label="真假值"/>
            <item :title="JSON.stringify(val[1])">
                <link-checkbox v-model="val[1]" :trueValue="true" :falseValue="false"/>
            </item>

            <list-title label="点击 list-item的时候自动触发点击事件"/>
            <item :title="val[3]">
                <link-checkbox toggleOnClickItem v-model="val[3]"/>
            </item>

            <list-title>单选:{{String(val[98]||'')}}</list-title>
            <link-radio-group v-model="val[98]">
                <list>
                    <item v-for="item in 4" :key="item">
                        <link-checkbox :val="item" toggleOnClickItem slot="thumb"/>
                        <text slot="title">{{item}}</text>
                    </item>
                </list>
            </link-radio-group>

            <list-title>多选: {{JSON.stringify(val[99])}}</list-title>
            <link-checkbox-group v-model="val[99]">
                <list>
                    <link-sticky top>
                        <link-button label="打印数据" @tap="log"/>
                    </link-sticky>
                    <item v-for="item in 30" :key="item">
                        <link-checkbox :val="item" toggleOnClickItem slot="thumb"/>
                        <text slot="title">{{item}}</text>
                    </item>
                    <link-sticky>
                        <item>
                            <link-checkbox checkboxForAll slot="thumb" toggleOnClickItem/>
                            <text slot="title">全选</text>
                        </item>
                    </link-sticky>
                </list>
            </link-checkbox-group>

        </list>
    </link-page>
</template>

<script>
    export default {
        name: "demo-checkbox-page",
        props: {},
        data() {
            return {
                val: {},
            }
        },
        methods: {
            log() {
                console.log(this.val[99])
            },
        },
    }
</script>

<style lang="scss">
</style>