<template>
    <link-page>
        <list>
            <item title="基本用法">
                <link-input v-model="val[0]"/>
            </item>
            <link-textarea v-model="val[0]"/>
            <item title="禁用"></item>
            <link-textarea v-model="val[0]" disabled style="padding-bottom: 48rpx"/>
        </list>
        <list-title>在表单中使用文本域</list-title>
        <link-form>
            <link-form-item label="名称">
                <link-input/>
            </link-form-item>
            <link-form-item label="备注" vertical>
                <link-textarea v-model="val[0]"/>
            </link-form-item>
            <link-form-item label="地址">
                <link-input/>
            </link-form-item>
        </link-form>
    </link-page>
</template>

<script>
    export default {
        name: "demo-textarea-page",
        data() {
            return {
                val: {0: '一段非常非常长的描述。一段非常非常长的描述。一段非常非常长的描述。一段非常非常长的描述。'},
            }
        },
    }
</script>

<style lang="scss">

</style>