<template>
    <view class="demo-dropdown-page">
        <list>
            <item title="基本用法">
                <link-dropdown>
                    <link-button>reference</link-button>
                    <view slot="dropdown">
                        <link-button block>this is dropdown content</link-button>
                    </view>
                </link-dropdown>
            </item>
            <item title="禁用点击reference的时候开启/关闭dropdown">
                <link-switch v-model="showFlag" :trueValue="true" :falseValue="false"/>
                <link-dropdown
                        disabledToggleOnTapReference
                        disabledHideOnTapMask
                        v-model="showFlag">
                    <link-button size="mini">reference</link-button>
                    <view slot="dropdown">
                        <link-button block @tap="onTapButton">this is dropdown content</link-button>
                    </view>
                </link-dropdown>
            </item>
            <item title="禁用点击遮罩的时候关闭dropdown">
                <link-dropdown disabledHideOnTapMask>
                    <link-button>reference</link-button>
                    <view slot="dropdown">
                        <link-button block>this is dropdown content</link-button>
                    </view>
                </link-dropdown>
            </item>
        </list>
    </view>
</template>

<script>
    export default {
        name: "demo-dropdown-page",
        data() {
            return {
                showFlag: false,
                onTapButton: () => {
                    console.log('onTapButton')
                }
            }
        },
    }
</script>

<style lang="scss">

</style>
