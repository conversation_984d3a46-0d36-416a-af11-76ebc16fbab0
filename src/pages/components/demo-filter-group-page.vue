<template>
    <link-page class="demo-filter-group-page">
        <list>
            <item title="基本用法"/>
            <link-filter-group v-model="val[0]">
                <link-filter-item label="最近创建" :param="{sort:'created'}"/>
                <link-filter-item label="最近更新" :param="{sort:{field:'lastUpdated',desc:false}}"/>
                <link-filter-item label="附近1km" :param="{filter:{property:'distance',operator:'<',value:'1000'}}"/>
                <link-filter-item label="我跟进的终端" :param="{filter:[{property:'source',operator:'=',value:'me'},{property:'type',operator:'=',value:'terminal'}]}"/>
            </link-filter-group>
            <link-card v-if="!!val[0]" style="margin: 8px 0;">
                <view slot="title">绑定值</view>
                <view>{{JSON.stringify(val[0])}}</view>
            </link-card>
            <item title="超长列表可以横向滚动"/>
            <link-filter-group>
                <link-filter-item label="最近创建" :param="{sort:'created'}"/>
                <link-filter-item label="最近更新" :param="{sort:'lastUpdated'}"/>
                <link-filter-item label="附近1km" :param="{filter:{property:'distance',operator:'<',value:'1000'}}"/>
                <link-filter-item label="我跟进的终端" :param="{filter:[{property:'source',operator:'=',value:'me'},{property:'type',operator:'=',value:'terminal'}]}"/>
                <link-filter-item label="最近创建(copy)" :param="{sort:'created'}"/>
                <link-filter-item label="最近更新(copy)" :param="{sort:'lastUpdated'}"/>
                <link-filter-item label="附近1km(copy)" :param="{filter:{property:'distance',operator:'<',value:'1000'}}"/>
                <link-filter-item label="我跟进的终端(copy)" :param="{filter:[{property:'source',operator:'=',value:'me'},{property:'type',operator:'=',value:'terminal'}]}"/>
            </link-filter-group>

            <item title="初始化的时候就激活某一项"/>
            <link-filter-group v-model="val[0]">
                <link-filter-item label="最近创建" :param="{sort:'created'}"/>
                <link-filter-item label="最近更新(activeOnInit)" :param="{sort:{field:'lastUpdated',desc:false}}" activeOnInit/>
                <link-filter-item label="附近1km" :param="{filter:{property:'distance',operator:'<',value:'1000'}}"/>
                <link-filter-item label="我跟进的终端" :param="{filter:[{property:'source',operator:'=',value:'me'},{property:'type',operator:'=',value:'terminal'}]}"/>
            </link-filter-group>
            <link-alert>
                如果是与 link-auto-list 搭配使用，需要设置 AutoList的 loadOnStart 为false
            </link-alert>

            <link-card style="margin: 8px 0">
                <view slot="title">
                    link-filter-item 的label以及param属性说明
                </view>
                <link-card-item label="props.label"/>
                <link-card-item>
                    <view slot="label">
                        显示的文本，可以通过默认插槽自定义内容，但是
                        label仍然是一个必须属性，因为label作为 link-filter-item 的唯一标识
                    </view>
                </link-card-item>
                <link-card-separator status="info" type="solid"/>
                <link-card-item label="props.param">
                    <view slot="content">
                        查询参数，格式为 {sort,filter}；
                    </view>
                </link-card-item>
                <link-card-item>
                    <view slot="label">
                        sort 为字符串或者一个对象，字符串表示
                        排序字段为这个字符串，使用默认降序排序。对象则格式为{field:string,desc:boolean}。
                    </view>
                </link-card-item>
                <link-card-item>
                    <view slot="label">
                        filter 为 FiltersRaw对象或者该对象的数组。
                    </view>
                </link-card-item>

                <link-alert style="margin-top: 10px;">
                    filter 还可以是一个函数，当激活该filter-item时，会执行这个函数获取FiltersRaw
                </link-alert>
            </link-card>
            <link-card style="margin: 8px 0">
                <view slot="title">
                    link-filter-group model绑定值格式说明
                </view>
                <link-card-item label="model绑定值为一个对象，格式为 {label,param}， label为 link-filter-item的label值，param格式等同于 link-filter-item 的param属性对象格式"/>
            </link-card>
        </list>
    </link-page>
</template>

<script>
    export default {
        name: "demo-filter-group-page",
        data() {
            return {
                val: {},
            }
        },
    }
</script>

<style lang="scss">

</style>