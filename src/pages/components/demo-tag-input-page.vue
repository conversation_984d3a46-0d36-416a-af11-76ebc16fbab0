<template>
    <link-page class="demo-tag-input-page">
        <link-form :value="{}">
            <link-form-item vertical label="基本用法">
                <link-tag-input v-model="val[0]"/>
            </link-form-item>
            <link-form-item vertical label="带初始值">
                <link-tag-input v-model="val[1]"/>
            </link-form-item>
            <link-form-item vertical label="禁用">
                <link-tag-input v-model="val[1]" disabled/>
            </link-form-item>
            <link-form-item vertical label="只读">
                <link-tag-input v-model="val[1]" readonly/>
            </link-form-item>
        </link-form>
    </link-page>
</template>

<script>
    export default {
        name: "demo-tag-input-page",
        data() {
            return {
                val: {
                    1: [
                        '标签一',
                        '标签二',
                        '标签三'
                    ],
                },
            }
        },
    }
</script>

<style lang="scss">

</style>