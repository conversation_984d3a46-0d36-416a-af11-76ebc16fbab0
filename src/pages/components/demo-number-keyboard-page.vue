<template>
    <link-page class="demo-number-keyboard-page">
        <list>
            <list-title>服务调用</list-title>
            <item title="基本用法" @tap="openNumberKeyboard" :content="String(val[0])"/>
            <item title="保留三位小数" @tap="hideDecimalPoint" :content="String(val[1])"/>
            <item title="最大1000，最小,200" @tap="maxAndMinValue" :content="String(val[2])"/>
            <list-title>组件调用</list-title>
            <link-form>
                <link-form-item label="基本用法" :note="String(val[3])">
                    <link-number-keyboard v-model="val[3]"/>
                </link-form-item>
                <link-form-item label="保留两位小数" :note="String(val[4])">
                    <link-number-keyboard v-model="val[4]" :precision="2"/>
                </link-form-item>
                <link-form-item label="最大2000，最小100" :note="String(val[5])">
                    <link-number-keyboard v-model="val[5]" :max="2000" :min="100"/>
                </link-form-item>
                <link-form-item label="返回字符串值" desc="数字有最大值溢出丢失精度问题" :note="String(val[6])">
                    <link-number-keyboard v-model="val[6]" stringValue/>
                </link-form-item>
            </link-form>
        </list>
    </link-page>
</template>

<script>
    export default {
        name: "demo-number-keyboard-page",
        data() {
            return {
                val: {
                    0: null,
                    1: null,
                    2: null,
                },
            }
        },
        methods: {
            async openNumberKeyboard() {
                this.val[0] = await this.$numberKeyboard({initValue: this.val[0]})
            },
            async hideDecimalPoint() {
                this.val[1] = await this.$numberKeyboard({initValue: this.val[1], precision: 3})
            },
            async maxAndMinValue() {
                this.val[2] = await this.$numberKeyboard({initValue: this.val[2], max: 1000, min: 200})
            },
        },
    }
</script>

<style lang="scss">

</style>