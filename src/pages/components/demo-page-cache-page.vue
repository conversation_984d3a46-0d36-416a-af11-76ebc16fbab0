<template>
    <link-page class="demo-page-cache-manager">
        <link-form>
            <link-form-item label="用户名">
                <link-input v-model="formData.username"/>
            </link-form-item>
            <link-form-item label="密码">
                <link-input v-model="formData.password"/>
            </link-form-item>
            {{ JSON.stringify(formData) }}
        </link-form>
    </link-page>
</template>

<script>
import {PageCacheManager} from "../../utils/PageCacheManager";

export default {
    name: "demo-page-cache-manager",
    data() {
        const data = PageCacheManager.getInitialData({
            ctx: this,
            path: 'components/demo-page-cache-page.vue',
            title: '测试创建活动',
            initialData: {
                formData: {},
                currentTab: '页面三',
            },
            initializer: () => {
                console.log('执行页面初始化动作')
                this.formData = {
                    username: 'admin',
                    password: '123456'
                }
                console.log(this)
            }
        })
        return data
    },
}
</script>

<style lang="scss">

</style>
