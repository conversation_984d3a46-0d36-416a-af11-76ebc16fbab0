<template>
    <link-page class="nav-third-page">
        <list>
            <item title="参数" note='v-model="msg"'>
                <link-input v-model="msg"/>
            </item>
            <item title="返回一个页面" @tap="$nav.back()" desc="$nav.back()"/>
            <item title="带参数返回一个页面" @tap="$nav.back(msg)" desc="$nav.back(msg)"/>
            <item title="带参数返回两个页面" @tap="$nav.back(msg,2)" desc="$nav.back(msg,2)"/>
            <item title="回退到首页" @tap="$nav.backAll(msg)" desc="$nav.backAll(msg)"/>
            <item title="带参数重新启动到页面一" @tap="$nav.relaunch('components/nav/nav-first-page',{msg})" desc="$nav.relaunch('components/nav/nav-first-page',{msg})"/>
            <item title="派发全局事件" @tap="$bus.$emit('ljlz-accnt','ccc')"/>
        </list>
    </link-page>
</template>

<script>
    export default {
        name: "nav-third-page",
        props: {},
        data() {
            return {
                msg: '3333'
            }
        },
        methods: {},
    }
</script>

<style lang="scss">
</style>