<template>
    <link-page class="nav-first-page">
        <list>
            <item title="参数">
                <link-input v-model="pushParam"/>
            </item>
            <list-title>基本用法：带参数跳转页面</list-title>
            <item title="打开第二个页面" @tap="pushPage" desc="$nav.push"/>
            <item title="重定向到页面二" @tap="$nav.redirect('components/nav/nav-second-page',{msg:pushParam})" desc="$nav.push"/>
            <item title="得到的back参数">
                {{backParam}}
            </item>
            <item title="响应式对象">
                <link-input v-model="state.name"/>
            </item>
            <item title="派发全局事件" @tap="$bus.$emit('ljlz-accnt','aaa')"/>
        </list>
    </link-page>
</template>

<script>

    import {$globalBus} from "link-taro-component";

    /*$globalBus.$on('ljlz-accnt', (data) => {
        console.log('globalBus', data)
    })*/

    export default {
        name: "nav-first-page",
        data() {
            console.log('第一个页面初始化参数', this.pageParam.msg)
            return {
                pushParam: this.pageParam.msg || '123',
                backParam: '',
                state: {
                    name: 'hello world'
                },
            }
        },
        methods: {
            pushPage() {
                console.log('pushPage')
                this.$nav.push('components/nav/nav-second-page', {
                    msg: this.pushParam,
                    cb: (msg) => {
                        console.log('调用了页面一的cb函数', msg)
                        this.pushParam = msg
                    },
                    state: this.state,
                })
            },
            onBack(param) {
                console.log('页面一 onBack：', param)
                if (!!param) {
                    this.backParam = param
                }
            },
        },
        mounted() {
            this.$bus.$on('ljlz-accnt', data => {
                console.log('页面一：', data)
            })
        }
    }
</script>

<style lang="scss">

</style>
