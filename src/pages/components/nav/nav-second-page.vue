<template>
    <link-page class="nav-second-page">
        <list>
            <item title="参数" note="v-model='msg'">
                <link-input v-model="msg"/>
            </item>
            <item title="调用上一个页面跳转时传递的函数" @tap="execFunc" note="this.pageParam.cb(this.msg)"/>
            <item title="带参数back" @tap="backWithParam" desc="this.$nav.back(this.msg)"/>
            <item title="打开第三个页面" @tap="$nav.push('/pages/components/nav/nav-third-page')" desc="$nav.push"/>
            <item title="重定向到第三个页面" @tap="$nav.redirect('/pages/components/nav/nav-third-page')" desc="$nav.redirect"/>
            <item title="得到的back参数">
                {{backParam}}
            </item>
            <item title="响应式对象">
                <link-input v-model="state.name"/>
            </item>
            <item title="派发全局事件" @tap="$bus.$emit('ljlz-accnt','bbb')"/>
        </list>
    </link-page>
</template>

<script>
    export default {
        name: "nav-second-page",
        data() {
            console.log(this.pageParam)
            return {
                msg: this.pageParam.msg,
                backParam: '',
                state: this.pageParam.state || {},
            }
        },
        methods: {
            execFunc() {
                if (!!this.pageParam.cb) {
                    this.pageParam.cb(this.msg)
                }
            },
            backWithParam() {
                this.$nav.back(this.msg)
            },
            onBack(data) {
                console.log('页面二 onBack：', data)
                if (!!data) {
                    this.backParam = data
                }
            },
        },
        mounted() {
            this.$bus.$on('ljlz-accnt', data => {
                console.log('页面二：', data)
            })
        }
    }
</script>

<style lang="scss">

</style>