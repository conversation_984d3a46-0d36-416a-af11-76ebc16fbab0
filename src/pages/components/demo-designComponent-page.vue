<template>
    <view class="demo-designComponent-page">
        <link-checkbox-group v-model="val[0]">
            <list>
                <item v-for="item in list" :key="item.val" :title="item.name">
                    <link-checkbox :val="item.val" slot="thumb" toggleOnClickItem/>
                </item>
            </list>
        </link-checkbox-group>
        {{val[0]}}
    </view>
</template>

<script>
    export default {
        name: "demo-designComponent-page",
        data() {
            return {
                list: [
                    {name: '张三', val: 'zhangsan'},
                    {name: '李四', val: 'lisi'},
                    {name: '王五', val: 'wangwu'}
                ],
                val: {},
            }
        },
    }
</script>

<style lang="scss">

</style>