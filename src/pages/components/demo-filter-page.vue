<template>
    <link-page class="demo-filter-page">
        <list>
            <item title="$listFilter" @tap="openFilter"/>
            <item title="基本用法">
                <link-filter v-model="filterOption" @change="handleChange"/>
            </item>
        </list>
    </link-page>
</template>

<script>
    import {getFiltersRaw} from "link-taro-component";

    export default {
        name: "demo-filter-page",
        data() {
            return {
                filterOption: [
                    {
                        label: '安全性筛选',
                        type: 'oauth',
                        // field: 'oauth',                  // 绑定字段默认为oauth，这个字段不是filtersRaw中的property，而是最终请求参数对象的oauth字段
                        // lov:'APP_MENU_OAUTH',            // 默认的安全性菜单值列表类型
                    },

                    {label: '普通文本筛选', field: 'acctName', type: 'text'},
                    {label: '数字范围筛选', field: 'acctAge', type: 'number'},
                    {label: '时间范围筛选', field: 'acctBirthday', type: 'date'},
                    {label: '值列表筛选', field: 'acctLevel', type: 'lov', lov: 'ACCT_LEVEL'},
                    {label: '值列表筛选（单选）', field: 'acctLevel', type: 'lov', lov: 'ACCT_LEVEL', multiple: false},
                    {
                        label: '自定义筛选', field: 'acctMaster', type: 'select', data: [
                            {name: 'January', val: '1'},
                            {name: 'February', val: '2'},
                            {name: 'March', val: '3'},
                            {name: 'April', val: '4'},
                            {name: 'May', val: '5'},
                            {name: 'June', val: '6'},
                            {name: 'July', val: '7'},
                            {name: 'August', val: '8'},
                            {name: 'September', val: '9'},
                            {name: 'October', val: '10'},
                            {name: 'November', val: '11'},
                            {name: 'December', val: '12'},
                        ]
                    },
                    {
                        label: '自定义筛选（单选）', field: 'acctMaster', type: 'select', multiple: false, data: [
                            {name: 'January', val: '1'},
                            {name: 'February', val: '2'},
                            {name: 'March', val: '3'},
                            {name: 'April', val: '4'},
                            {name: 'May', val: '5'},
                            {name: 'June', val: '6'},
                            {name: 'July', val: '7'},
                            {name: 'August', val: '8'},
                            {name: 'September', val: '9'},
                            {name: 'October', val: '10'},
                            {name: 'November', val: '11'},
                            {name: 'December', val: '12'},
                        ]
                    },
                ]
            }
        },
        methods: {
            handleChange: (val) => {
                console.log(val)
                console.log(getFiltersRaw(val))
            },
            async openFilter() {
                const filters = await this.$listFilter(this.filterOption)
                console.log(filters)
            }
        }
    }
</script>

<style lang="scss">

</style>
