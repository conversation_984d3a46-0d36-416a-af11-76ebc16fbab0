<template>
    <view class="demo-address-page">
        <list>
            <list-title label="地址服务"/>
            <item title="获取所有地址数据" @tap="getAll"/>

            <list-title>地址选择框</list-title>
            <item>
                <link-address
                        :province.sync="formData.provinceName"
                        :city.sync="formData.cityName"
                        :district.sync="formData.districtName"
                />
            </item>
            <item>
                <link-address
                        :province.sync="formData.provinceName"
                        :city.sync="formData.cityName"
                        :district.sync="formData.districtName"
                />
            </item>

            <item title="有初始值">
                <link-address
                        :province.sync="initializedFormData.provinceName"
                        :city.sync="initializedFormData.cityName"
                        :district.sync="initializedFormData.districtName"
                />
            </item>

            <list-title>选择模式 view</list-title>
            <item title="选择：省" :note="formData2.provinceName">
                <link-address
                        :province.sync="formData2.provinceName"
                        view="p"/>
            </item>
            <item title="选择：市" :note="formData3.cityName">
                <link-address
                        province="湖北省"
                        :city.sync="formData3.cityName"
                        view="c"/>
            </item>
            <item title="选择：县" :note="formData3.districtName">
                <link-address
                        province="湖北省"
                        city="黄冈市"
                        :district.sync="formData3.districtName"
                        view="d"/>
            </item>
            <item title="选择：省市" :note="`${formData4.provinceName}/${formData4.cityName}`">
                <link-address
                        :province.sync="formData4.provinceName"
                        :city.sync="formData4.cityName"
                        view="pc"/>
            </item>
            <item title="选择：市县" :note="`${formData5.cityName}/${formData5.districtName}`">
                <link-address
                        province="湖北省"
                        :city.sync="formData5.cityName"
                        :district.sync="formData5.districtName"
                        view="cd"/>
            </item>
            <item title="选择：省市县" :note="`${formData6.provinceName}/${formData6.cityName}/${formData6.districtName}`">
                <link-address
                        :province.sync="formData6.provinceName"
                        :city.sync="formData6.cityName"
                        :district.sync="formData6.districtName"/>
            </item>


            <list-title>自定义选项</list-title>
            <item>
                <link-address
                        :province.sync="formData.provinceName"
                        :city.sync="formData.cityName"
                        :district.sync="formData.districtName"
                        :custom="customOption"
                />
            </item>
            <list-title>表单控制</list-title>

            <item title="只读">
                <link-address
                        readonly
                        :province.sync="formData.provinceName"
                        :city.sync="formData.cityName"
                        :district.sync="formData.districtName"
                />
            </item>
            <item title="禁用">
                <link-address
                        disabled
                        :province.sync="formData.provinceName"
                        :city.sync="formData.cityName"
                        :district.sync="formData.districtName"
                />
            </item>
        </list>
    </view>
</template>

<script>
    export default {
        name: "demo-address-page",
        data() {
            return {
                formData: {
                    provinceName: null,
                    cityName: null,
                    districtName: null,
                },
                initializedFormData: {
                    provinceName: '浙江省',
                    cityName: '杭州市',
                    districtName: '江干区',
                },
                formData2: {},
                formData3: {},
                formData4: {},
                formData5: {},
                formData6: {},
                formData7: {},
            }
        },
        methods: {
            async getAll() {
                // 测试同时多次获取地址数据
                const ret = await Promise.all([
                    this.$address.getData(),
                    this.$address.getData(),
                    this.$address.getData(),
                ])
                console.log('ret', ret)
            },
            customOption(tag, parentValue) {
                if (tag === 'p') {
                    return ['北京市', '上海市', '天津市', '重庆市']
                } else {
                    // console.log(tag, parentValue)
                    if (tag === 'd' && parentValue === '上海市') {
                        return ['下城区', '上城区']
                    }
                }
            },
        }
    }
</script>

<style lang="scss">

</style>