<template>
    <link-page class="demo-card-page">
        <list-title>基本用法，使用 link-card-list 包裹 link-card</list-title>
        <link-card-list>
            <link-card>
                卡片一
            </link-card>
            <link-card>
                卡片二
            </link-card>
        </link-card-list>

        <list-title>使用 link-card-item 实现行布局</list-title>

        <link-card-list>
            <link-card>
                <link-card-item label="物流方式" content="商家配送"/>
                <link-card-item label="计划配送时间" content="2020-12-12 12:30" arrow/>
                <link-card-item label="是否开票" content="否" arrowIcon="icon-moneycollect"/>
                <link-card-item>
                    <view slot="label">
                        <link-icon icon="mp-help" status="primary"/>
                        <text>帮助信息</text>
                    </view>
                    <view slot="content">
                        <text>自动提醒</text>
                        <link-icon icon="mp-message" status="warn"/>
                    </view>
                </link-card-item>
            </link-card>
        </link-card-list>

        <list-title>卡片标题</list-title>

        <link-card-list>
            <link-card>

                <view slot="title">
                    卡片标题
                </view>
                <template slot="operator">
                    <link-button mode="text" @tap="$message.primary('添加')">添加</link-button>
                    <link-button mode="text" status="error" @tap="$message.warn('删除')">删除</link-button>
                </template>

                <link-card-item label="物流方式" content="商家配送"/>
                <link-card-item label="计划配送时间" content="2020-12-12 12:30" arrow/>
                <link-card-item label="是否开票" content="否" arrowIcon="icon-moneycollect"/>
                <link-card-item>
                    <view slot="label">
                        <link-icon icon="mp-help" status="primary"/>
                        <text>帮助信息</text>
                    </view>
                    <view slot="content">
                        <text>自动提醒</text>
                        <link-icon icon="mp-message" status="warn"/>
                    </view>
                </link-card-item>
            </link-card>
        </link-card-list>

        <list-title>卡片分割线：link-card-separator</list-title>

        <link-card-list>
            <link-card>
                <link-card-item label="物流方式" content="商家配送"/>
                <link-card-separator/>
                <link-card-item label="计划配送时间" content="2020-12-12 12:30" arrow/>
                <link-card-item label="是否开票" content="否" arrowIcon="icon-moneycollect"/>
                <link-card-item>
                    <view slot="label">
                        <link-icon icon="mp-help" status="primary"/>
                        <text>帮助信息</text>
                    </view>
                    <view slot="content">
                        <text>自动提醒</text>
                        <link-icon icon="mp-message" status="warn"/>
                    </view>
                </link-card-item>
            </link-card>

            <link-card>
                <link-card-item label="banner"/>
                <link-card-separator status="primary"/>
                <link-card-separator status="success"/>
                <link-card-separator status="warn"/>
                <link-card-separator status="error"/>
                <link-card-separator status="info"/>

                <link-card-item label="solid"/>
                <link-card-separator status="primary" type="solid"/>
                <link-card-separator status="success" type="solid"/>
                <link-card-separator status="warn" type="solid"/>
                <link-card-separator status="error" type="solid"/>
                <link-card-separator status="info" type="solid"/>

                <link-card-item label="dash"/>
                <link-card-separator status="primary" type="dash"/>
                <link-card-separator status="success" type="dash"/>
                <link-card-separator status="warn" type="dash"/>
                <link-card-separator status="error" type="dash"/>
                <link-card-separator status="info" type="dash"/>
            </link-card>

        </link-card-list>

        <list-title>分割线两侧的凹槽</list-title>
        <link-card-list>
            <link-card>
                <link-card-item label="banner"/>
                <link-card-separator groove/>
                <link-card-item label="solid"/>
                <link-card-separator groove type="solid"/>
                <link-card-item label="dash"/>
                <link-card-separator groove type="dash"/>
            </link-card>

        </link-card-list>


        <list-title>卡片侧滑删除</list-title>

        <link-card-list>
            <link-swipe-action :value="true">
                <link-card>
                    <link-card-item label="label与content属性" content="商家配送"/>
                    <link-card-item label="显示箭头" content="2020-12-12 12:30" arrow/>
                    <link-card-item label="自定义箭头图标" content="否" arrowIcon="icon-moneycollect"/>
                    <link-card-item>
                        <view slot="label">
                            <link-icon icon="mp-help" status="primary"/>
                            <text>label插槽</text>
                        </view>
                        <view slot="content">
                            <text>content插槽</text>
                            <link-icon icon="mp-message" status="warn"/>
                        </view>
                    </link-card-item>
                </link-card>
                <link-swipe-option slot="option" @tap="$message.error('删除')" icon="mp-trash"/>
            </link-swipe-action>

            <link-swipe-action>
                <link-card>
                    <link-card-item label="物流方式" content="商家配送"/>
                    <link-card-item label="计划配送时间" content="2020-12-12 12:30" arrow/>
                    <link-card-item label="是否开票" content="否" arrowIcon="icon-moneycollect"/>
                    <link-card-item>
                        <view slot="label">
                            <link-icon icon="mp-help" status="primary"/>
                            <text>帮助信息</text>
                        </view>
                        <view slot="content">
                            <text>自动提醒</text>
                            <link-icon icon="mp-message" status="warn"/>
                        </view>
                    </link-card-item>
                </link-card>
                <link-swipe-option slot="option" @tap="$message.primary('帮助')" icon="mp-help" color="primary"/>
                <link-swipe-option slot="option" @tap="$message.error('删除')" icon="mp-trash"/>
            </link-swipe-action>

        </link-card-list>

    </link-page>
</template>

<script>
    export default {
        name: "demo-card-page"
    }
</script>

<style lang="scss">

</style>