<template>
    <view class="demo-search-page">
        <list-title>SearchService</list-title>
        <item title="基本用法">
            <link-button size="mini" @tap="basicUsage">
                {{!!val[0]?val[0]:'open search'}}
            </link-button>
        </item>
        <list-title>link-search-input</list-title>
        <item title="基本用法" :note="val[1]||''"/>
        <link-search-input v-model="val[1]"/>
        <item title="默认插槽" :note="val[2]||''"/>
        <link-search-input v-model="val[2]">
            <view style="padding-left:24rpx;color: #1F74FF;font-size: 28rpx;display: flex;align-items: center">
                <view>排序</view>
                <link-icon icon="mp-sort"/>
            </view>
        </link-search-input>
    </view>
</template>

<script>
    export default {
        name: "demo-search-page",
        data() {
            return {
                val: {
                    0: '',
                    1: '',
                    2: '',
                    3: '',
                    4: '',
                    5: '',
                    6: '',
                }
            }
        },
        methods: {
            async basicUsage() {
                this.val[0] = await this.$search()
                console.log(this.val)
            },
        }
    }
</script>

<style lang="scss">

</style>