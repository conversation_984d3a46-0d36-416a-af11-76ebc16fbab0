<template>
    <view class="demo-swipe-action-page">
        <list>
            <list-title>基本用法</list-title>
            <link-swipe-action>
                <item title="自定义内容" desc="自定义描述"/>
                <link-swipe-option slot="option" color="primary" @tap="showDetail">查看</link-swipe-option>
                <link-swipe-option slot="option" @tap="showDelete">删除</link-swipe-option>
            </link-swipe-action>

            <list-title>默认打开</list-title>
            <link-swipe-action :value="true">
                <item title="自定义内容" desc="自定义描述"/>
                <link-swipe-option slot="option" color="primary" @tap="showDetail">查看</link-swipe-option>
                <link-swipe-option slot="option" color="success" @tap="showActive">激活</link-swipe-option>
                <link-swipe-option slot="option" @tap="showDelete">删除</link-swipe-option>
            </link-swipe-action>

            <list-title>手动控制是否打开</list-title>
            <item title="开关">
                <link-switch v-model="showFlag" :trueValue="true" :falseValue="false"/>
            </item>
            <link-swipe-action v-model="showFlag">
                <item title="自定义内容" desc="自定义描述"/>
                <link-swipe-option slot="option" color="primary" @tap="showDetail">查看</link-swipe-option>
                <link-swipe-option slot="option" color="success" @tap="showActive">激活</link-swipe-option>
                <link-swipe-option slot="option" @tap="showDelete">删除</link-swipe-option>
            </link-swipe-action>
            <link-swipe-action disabledCloseOnTapOption>
                <item title="禁用点击之后，自动关闭" desc="自定义描述"/>
                <link-swipe-option slot="option" color="primary" @tap="showDetail">查看</link-swipe-option>
                <link-swipe-option slot="option" color="success" @tap="showActive">激活</link-swipe-option>
                <link-swipe-option slot="option" @tap="showDelete">删除</link-swipe-option>
            </link-swipe-action>
            <link-swipe-action disabledAutoCloseWhenOtherSwipeOpen>
                <item title="禁用其他SwipeAction打开自动，自动关闭" desc="自定义描述"/>
                <link-swipe-option slot="option" color="primary" @tap="showDetail">查看</link-swipe-option>
                <link-swipe-option slot="option" color="success" @tap="showActive">激活</link-swipe-option>
                <link-swipe-option slot="option" @tap="showDelete">删除</link-swipe-option>
            </link-swipe-action>

        </list>
        <list>
            <link-swipe-action v-for="item in list.slice(0,3)" :key="`${item.name}_${list.length}`">
                <view @touchstart="onTouchstart2">
                    <item :title="item.name" @tap="onTap(item)"/>
                </view>
                <link-swipe-option slot="option" @tap="deleteRow(item)">删除</link-swipe-option>
            </link-swipe-action>
        </list>
    </view>
</template>

<script>
    export default {
        name: "demo-swipe-action-page",
        data() {
            return {
                showFlag: false,
                list: [
                    {name: '一号'},
                    {name: '二号'},
                    {name: '三号'},
                    {name: '四号'},
                    {name: '五号'},
                    {name: '六号'},
                ],
            }
        },
        methods: {
            onTouchstart2() {
                console.log('onTouchstart2')
            },
            onTap(item) {
                console.log({...item})
            },
            deleteRow(item) {
                const index = this.list.indexOf(item)
                console.log(item, index)
                this.list.splice(index, 1)
            },

            showDetail() {
                this.$taro.showToast({title: '查看'})
            },
            showActive() {
                this.$taro.showToast({title: '激活'})
            },
            showDelete() {
                this.$taro.showToast({title: '删除'})
            },
        }
    }
</script>

<style lang="scss">

</style>