<template>
    <link-page class="visit-apply-item">
        <view class="visit-item-container">
            <view class="activity-item-wrap">
                <view class="item">
                    <view class="label">拜访申请名称</view>
                    <view class="content">{{visitApplyItem.title}}</view>
                </view>
                <view class="item">
                    <view class="label">拜访类型</view>
                    <view class="content">{{visitApplyItem.visitType | lov('VISIT_TYPE')}}</view>
                </view>
                <view class="item">
                    <view class="label">申请状态</view>
                    <view class="content">{{visitApplyItem.visitApplicationStatus | lov('VISIT_APPLICATION_STATUS')}}
                    </view>
                </view>
                <view class="item">
                    <view class="label">计划拜访开始日期</view>
                    <view class="content">{{visitApplyItem.oldVisitTime | date('YYYY-MM-DD HH:mm')}}</view>
                </view>
                <view class="item">
                    <view class="label">计划拜访结束日期</view>
                    <view class="content">{{visitApplyItem.oldVisitEndTime | date('YYYY-MM-DD HH:mm')}}</view>
                </view>
                <view class="item">
                    <view class="label">计划拜访人数</view>
                    <view class="content">{{visitApplyItem.amountReq}}</view>
                </view>
                <view class="item">
                    <view class="label">拜访对接人</view>
                    <view class="content">{{visitApplyItem.visitorName}}</view>
                </view>
            </view>
            <view class="activity-consumer">
                <!-- 拜访客户列表 -->
                <user-info @initCustInfoListInfo="initCustInfoListInfo"
                           :edit-flag="!approvalFlag && (visitApplyItem.visitApplicationStatus === 'ForSubmitted' || visitApplyItem.visitApplicationStatus === 'Rejected') && editFlag"
                           :parentId="visitApplyItem.id"
                           statusField="approvalStatus"
                           statusLov="VISIT_CUST_STATUS"
                           source="visitApplyItem"
                           :userInfoList="visitApplyItem.visitCustInfoList"></user-info>
            </view>
        </view>
        <link-sticky>
            <link-button block @tap="submit"
                         v-if="!approvalFlag &&(visitApplyItem.visitApplicationStatus === 'Rejected'
                     || visitApplyItem.visitApplicationStatus === 'ForSubmitted')">提交
            </link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    import {ROW_STATUS} from "../../../utils/constant";
    import TitleLine from "../../lzlj-II/fighting-fakes/components/title-line";
    import StatusButton from "../../lzlj/components/status-button";
    import ConsumerCommon from "../consumer-common";
    import UserInfo from "../visit-register/components/user-info";

    export default {
        name: "visit-apply-item-page",
        components: {StatusButton, TitleLine, UserInfo},
        mixins: [ConsumerCommon()],
        async created() {
            await this.initCustInfoListInfo();
            this.$bus.$on('initCustInfoListInfoVisitApply', async () => {
                await this.initCustInfoListInfo();
            })
        },
        data() {
            const config = this.pageParam;
            const formOption = new this.FormOption(this, config);
            const visitApplyItem = {
                visitCustInfoList: [],
                ...(config.data || {}),
            };
            return {
                visitApplyItem,
                formOption,
                approvalFlag: false,         // 审批
                editFlag: true,
            }
        },
        methods: {
            /**
             * @desc 新增消费者
             * <AUTHOR>
             * @date 2022/3/23 10:05
             **/
            async addConsumer() {
                this.newAccountItem.id = await this.$newId();
                this.$nav.push(this.editPath,{
                    data: this.newAccountItem,
                    pageFrom: 'visitApplyItem',
                    userInfo: this.userInfo,
                    callback: async (data) => {
                        this.accountOption.methods.reload();
                        const consumerData = {
                            'consumerId': data.id,
                            'visitSinglePerson': data.name,
                            'telephone': data.phoneNumber,
                            'company': data.companyName,
                            'companyId': data.belongToCompanyId,
                            'position': data.position,
                            'visitId': this.visitApplyItem.id,
                            'row_status': ROW_STATUS.NEW,
                            'approvalStatus': 'New'
                        };
                        this.$utils.showLoading();
                        await this.$http.post(this.$env.appURL + '/action/link/visitCustInfo/upsert', consumerData, {
                            autoHandleError: false,
                            handleFailed: (response) => {
                                this.$utils.hideLoading();
                                this.$showError('保存失败' + response.result);
                            }
                        });
                        await this.initCustInfoListInfo();
                        this.$utils.hideLoading();
                    }
                });
            },
            /**
             *  新建客户信息
             *  <AUTHOR>
             *  @date  2020-07-03
             */
            async addVisitCustInfo() {
                const list = await this.$object(this.accountOption, {
                    pageTitle: '消费者',
                    multiple: true
                });
                let consumerDataList = [];
                consumerDataList = list.map(item => ({
                    'consumerId': item.id,
                    'visitSinglePerson': item.acctName,
                    'telephone': item.mobilePhone1,
                    'company': item.company,
                    'companyId': item.companyId,
                    'position': item.position,
                    'visitId': this.visitApplyItem.id,
                    'approvalStatus': 'New'
                }));
                this.$utils.showLoading();
                await this.$http.post(this.$env.appURL + '/action/link/visitCustInfo/batchInsert', consumerDataList, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('保存失败' + response.result);
                    }
                });
                await this.initCustInfoListInfo();
                this.$utils.hideLoading();
            },
            /**
             *  请求客户信息
             *
             *  <AUTHOR>
             *  @date        2020-07-06
             */
            async initCustInfoListInfo() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/visitCustInfo/queryByExamplePage',
                    {
                        visitId: this.visitApplyItem.id,
                        queryProdsFlag: 'Y',
                        order: 'desc',
                        sort: 'created',
                        pageFlag: true,
                        rows: 100,
                        page: 1
                    });
                if (data.success) {
                    this.visitApplyItem.visitCustInfoList = data.rows;
                }
            },
            /**
             *  @description: 更新客户信息
             *  @author: songyanrong
             *  @date: 2020-07-06
             */
            async updateCust(item) {
                item.row_status = ROW_STATUS.UPDATE;
                this.$nav.push('/pages/lj-consumers/visit-register/visit-register-item-cust-info-edit-page', {
                    item: this.visitApplyItem,
                    visitRegisterItemCustInfoItem: item,
                    callback: () => {
                        this.initCustInfoListInfo();
                    }
                });
            },
            /**
             *  @description: 提交
             *  @author: songyanrong
             *  @date: 2020-07-06
             */
            async submit() {
                let params = [];
                if (this.visitApplyItem.visitCustInfoList.length > 0) {
                    this.visitApplyItem.visitCustInfoList.forEach((item) => {
                        if (item.approvalStatus === 'New' || item.approvalStatus === 'Rejected') {
                            params.push({'id': item.id, 'approvalStatus': 'Submitted'});
                        }
                    });
                }
                if (params.length < 1) {
                    await this.$taro.showToast({title: '至少提交一条数据！'});
                    return;
                }
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + '/action/link/visitCustInfo/batchUpdateVisitCustInfo', params);
                if (!data.success) {
                    this.$utils.hideLoading();
                    await this.$taro.showToast({title: '提交失败！' + data.result});
                } else {
                    this.$utils.hideLoading();
                    this.$taro.showToast({title: '提交成功！'});
                    this.$nav.back();
                }
            },
            /**
             *  @description: 删除拜访客户
             *  @author: songyanrong
             *  @date: 2020-07-06
             */
            async handleCustomerInfoDelete(item, index) {
                const data = await this.$http.post(this.$env.appURL + '/action/link/visitCustInfo/deleteById', item);
                this.visitApplyItem.visitCustInfoList.splice(index, 1);
                this.initCustInfoListInfo();
            }
        }
    }
</script>

<style lang="scss">
    .visit-apply-item {
        .visit-item-container {
            --background: #EEF3F5;

            .list-bottom-width-line {
                width: 100%;
                height: 20px;
                background-color: #EEF3F5;
            }

            .activity-item-wrap {
                padding: 20px 0 20px 28px;
                background: white;

                .item {
                    line-height: 56px;
                    font-size: 28px;

                    .label {
                        height: 56px;
                        line-height: 56px;
                        color: #6B7378;
                        width: 240px;
                        display: inline-block;
                    }

                    .content {
                        display: inline-block;
                        vertical-align: top;
                    }
                }
            }

            .lnk-form-header {
                height: 88px;
                width: 100%;
                padding-left: 32px;
                background-color: #EEF3F5;
                font-size: 28px;
                line-height: 88px;
            }

            .address-wrap {
                .address-wrap-content {
                    .location-item {
                        background-color: white;
                    }
                }
            }

            .activity-material-wrap {
                background: white;

                .item-content {
                    display: inherit;
                    padding-left: 32px;

                    .name {
                        width: 70%;
                    }

                    .num {
                        width: 30%;
                    }
                }
            }

            .activity-consumer {
                margin-top: 24px;
                .custInfo-item{
                    width: 100%;
                    color: #333333;
                    .status {
                        width: 100%;
                        justify-content: flex-end;
                        display: flex;
                        .status-button{
                            width: 10%;
                        }
                    }
                }
            }
        }
    }

</style>
