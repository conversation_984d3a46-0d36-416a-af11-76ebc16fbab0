<template>
    <link-page class="visit-apply-list-page">
        <link-auto-list :option="autoList" hideCreateButton>
            <view slot="top">
                <lnk-taps :taps="visitApplyOptions" v-model="visitApplyStatusActive"
                          @switchTab="onTap"></lnk-taps>
            </view>
            <link-filter-group slot="filterGroup">
                <link-filter-item label="拜访时间(升序)" :param="{sort:{field:'visitTime',desc:false}}"/>
                <link-filter-item label="创建时间(升序)" :param="{sort:{field:'created',desc:false}}"/>
                <link-filter-item label="最近更新(升序)" :param="{sort:{field:'lastUpdated',desc:false}}"/>
            </link-filter-group>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="visit-apply-list-item">
                    <view slot="note" class="item-container">
                        <view class="row-item">
                            <view class="left title">{{data.title}}</view>
                            <status-button>{{data.visitApplicationStatus | lov('VISIT_APPLICATION_STATUS')}}</status-button>
                        </view>
                        <view class="row-item">
                            <view class="left time">
                                {{data.oldVisitTime|date('YYYY-MM-DD HH:mm')}} ~ {{data.oldVisitEndTime|date('YYYY-MM-DD HH:mm')}}
                            </view>
                            <view class="right">
                                {{data.visitorName}}
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
    import LnkTaps from "../../core/lnk-taps/lnk-taps";
    import StatusButton from "../../lzlj/components/status-button";
    export default {
        name: "visit-apply-list-page",
        components: {StatusButton, LnkTaps},
        data() {
            const visitApplyOptions = [
                {name: '待提报', seq: '1', val: '[ForSubmitted,Rejected]'},
                {name: '审批中', seq: '2', val: '[EndSubmitted,UnderApproval,Submitted,Approved]'}
            ];
            const autoList = new this.AutoList(this, {
                module: this.$env.appURL + '/action/link/visit',
                url: {
                    queryByExamplePage: this.$env.appURL + '/action/link/visit/queryFieldsByExamplePage'
                },
                fetchItem: true,
                itemPath: '/pages/lj-consumers/visit-apply/visit-apply-item-page',
                searchFields: ['title'],
                filterOption: [
                    {label: '拜访类型', field: 'visitType', type: 'lov', lov: 'VISIT_LEVEL'}
                ],
                param: {
                    oauth: 'MY_ORG',
                    filtersRaw: [
                        {
                            id: 'visitApplicationStatus',
                            property: 'visitApplicationStatus',
                            value: '[ForSubmitted,Rejected]',
                            operator: 'in'
                        },
                        {id: 'type', property: 'type', value: 'VisitApply', operator: '='}
                    ]
                },
                stayFields: 'id,title,visitApplicationStatus,oldVisitTime,oldVisitEndTime,visitorName,visitType',
                sortOptions: null
            });
            return {
                autoList,
                visitApplyStatusActive: {},
                visitApplyOptions

            };
        },
        created () {
            this.visitApplyStatusActive = this.visitApplyOptions[0];
        },
        methods: {
            /**
             *  @description: tab页签切换
             *  @author: 马晓娟
             *  @date: 2020/11/17 14:36
             */
            onTap(item) {
                if (!this.$utils.isEmpty(item.val)) {
                    this.autoList.option.param['filtersRaw'] = [{
                        id: 'visitApplicationStatus',
                        property: 'visitApplicationStatus',
                        value: item.val,
                        operator: 'in'
                    }];
                } else {
                    this.autoList.option.param['filtersRaw'] = this.autoList.option.param['filtersRaw'].filter((item) => item.property !== 'visitApplicationStatus');
                }
                this.autoList.methods.reload();
            },
        }
    }
</script>

<style lang="scss">
    .visit-apply-list-page {
        .link-search-input{
            padding: 100px 24px;
        }
        .item-container{
            color: #262626;
            .row-item{
                display: flex;
                justify-content: space-between;
                border-bottom: 2px solid #F2F2F2;
                padding: 24px 0;
                .title{
                    font-size: 28px;
                    font-weight: bold;
                    line-height: 30px;
                }
            }
            .row-item:last-child{
                border-bottom: none;
            }

        }

        .right {
            text-align: right;
            max-width: 25%;
        }
        .visit-apply-list-item{
            background: #FFFFFF;
            margin: 24px;
            border-radius: 16px;
        }
    }
</style>
