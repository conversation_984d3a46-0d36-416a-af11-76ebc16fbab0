<template>
    <link-page class="activity-qrcode-list-page">
        <link-auto-list :option="activityQRCodeOption" hideCreateButton>
            <view slot="filterGroup" class="top-filter">
                <scroll-view scroll-x="true" class="top-filter-content">
                    <view class="top-filter-info">
                        <view v-for="(item,index) in timeFilterOption" class="time-list-item" :class="item.checked? 'timeChecked' : ''" @tap="chooseTime(item)">
                            <view class="time-item">{{item.name}}</view>
                        </view>
                    </view>
                </scroll-view>
            </view>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="activity-qrcode-list">
                    <view class="activity-qrcode-list-item" slot="note">
                        <view class="list-item">
                            <view class="activity-name">{{data.qrName}} <link-icon @tap="updateActivity('UPDATE', data)" icon="icon-edit"></link-icon></view>
                            <view class="status" v-if="data.status">
                                <status-button>{{data.status | lov('QR_CODE_STATUS')}}</status-button>
                            </view>
                        </view>
                        <view class="list-item">
                            <view class="interaction-name">
                                <view class="label">当前绑定互动</view>
                                <view class="data">{{data.interactionName || '无'}}</view>
                            </view>
                        </view>
                        <view class="list-item">
                            <view class="list-item_button">
                                <link-button size="mini" mode="stroke" @tap="gotoBindActivitySet(data)" v-if="data.status === 'Active'">活动绑定设置</link-button>
                            </view>
                            <view class="list-item_button">
                                <link-button size="mini" mode="stroke" @tap="shareQRCode(data)" v-if="data.status === 'Active'">活动码查看</link-button>
                            </view>
                            <view class="list-item_button">
                                <link-button size="mini" mode="stroke" @tap="confirmTips(data)" v-if="data.status === 'Active' || data.status === 'Inactive' || data.status === 'New'">{{data.status === 'Active' ? '失效' : '生效'}}</link-button>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
        <link-fab-button icon="icon-plus" :bottom="160" @tap="updateActivity('NEW')"></link-fab-button>
        <link-dialog ref="newOrUpdateInfo"
                     :noPadding="true"
                     v-model="dialogFlag"
                     height="25vh"
                     position="bottom">
            <view class="model-title">
                <view class="title">{{type === 'NEW' ? '新建二维码' : '编辑二维码'}}</view>
                <view class="iconfont icon-close" @tap="dialogFlag = false"></view>
            </view>
            <scroll-view scroll-y="true">
                <link-form ref="form">
                    <link-form-item label="二维码名称" field="qrcodeName">
                        <link-input type="text" v-model="qrcodeName"/>
                    </link-form-item>
                </link-form>
            </scroll-view>
            <link-sticky>
                <link-button block @tap="saveData">保存</link-button>
            </link-sticky>
        </link-dialog>
        <link-dialog ref="imageShare" position="poster" :initial="true">
            <view :style="{'background-image': 'url('+ $imageAssets.adornImg + ')'}" class="dialog-title">
                <view class="title-name">
                    {{ currentData.qrName }}
                </view>
            </view>
            <view style="width: 100%;height: 248px;text-align: center;background: white">
                <image style="padding-top: 44px;width: 200px;height: 200px;" :src="imageUrl"></image>
            </view>
            <view style="width: 100%;padding-top: 10px;padding-bottom: 5px;height: 35px;background: white">
                <view style="position: absolute;border: 1px #DADEE9 dashed;margin-top: 8px;width: 100%;"></view>
            </view>
            <view style="width: 100%;height: 60px;background: white;border-radius: 0 0 10px 10px">
                <view @tap="saveShareImg"
                      style="width: 100%;height: 100%;float: left;text-align:center;">
                    <image :src="$imageAssets.interSaveImg" style="width: 40px;height: 40px"/>
                </view>
            </view>
        </link-dialog>
    </link-page>
</template>
<script>
import StatusButton from "../../lzlj/components/status-button";
import {ROW_STATUS} from "../../../utils/constant";
export default {
    name: "activity-qrcode-list-page",
    components: {StatusButton},
    data () {
        const pageFrom = this.pageParam.pageFrom || '';
        const userInfo = this.$taro.getStorageSync('token').result;         // 获取用户信息
        return {
            pageFrom,                   // 记录从何处进入当前页面
            userInfo,
            type: 'NEW',                // 当前数据操作状态
            currentData: {},            // 当前数据行
            imageUrl: '',               // 二维码路径
            qrcodeName: '',             // 当前编辑对象
            dialogFlag: false,          // 修改更新数据弹窗是否展示
            timeFilterOption: [{name: '全部', val: 'ALL', checked: true},{name: '近3个月', val: 'Last3Month', checked: false},{name: '近半年', val: 'LastHalfYear', checked: false},{name: '近一年', val: 'LastYear', checked: false}],
            activityQRCodeOption: new this.AutoList(this, {  // 活动二维码列表option
                module: this.$env.appURL + '/interaction/link/qRHeader',
                searchFields: ['qrName'],
                param: {
                    oauth: 'MY_POSTN_ONLY',
                    filtersRaw: [
                        { id: 'qrType', property: 'qrType', operator: 'in', value: '[McAct]' },
                        { id: 'qrForm', property: 'qrForm', operator: '=', value: 'QrCode' }
                    ]
                }
            })
       }
    },
    methods: {
        /**
         * @desc 提示框
         * <AUTHOR>
         * @date 2023/3/22 13:49
         * @param data 当前选择数据行
         **/
        async confirmTips(data) {
            if (data.interactionName && data.status === 'Active') {
                this.$dialog({
                    title: '提示',
                    content: `当前二维码已绑定【${data.interactionName}】，请确认是否失效？`,
                    cancelButton: true,
                    onConfirm: async () => {
                        await this.invalidOrValidData(data);
                    },
                    onCancel: () => {
                    }
                })
            } else {
                await this.invalidOrValidData(data);
            }
        },
        /**
         * @desc 保存活动二维码
         * <AUTHOR>
         * @date 2023/3/14 11:15
         **/
        async saveShareImg() {
            this.$utils.showLoading();
            const that = this;
            wx.getImageInfo({
                src: that.imageUrl,
                success: function (ret) {
                    wx.saveImageToPhotosAlbum({
                        filePath: ret.path,
                        success: function (data) {
                            that.$message.success('图片已成功保存至相册！');
                        },
                        fail: function (err) {
                            if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
                                this.$message.info('打开设置窗口');
                                wx.openSetting({
                                    success(settingdata) {
                                        if (settingdata.authSetting['scope.writePhotosAlbum']) {
                                            that.$message.info('获取权限成功，请再次点击保存图片到相册。')
                                        } else {
                                            that.$message.info('获取权限失败,无法使用该功能，请授权再使用。');
                                        }
                                    }
                                })
                            }
                        }
                    })
                }
            });
            this.$utils.hideLoading();
        },
        /**
         * @desc 选择时间筛选范围
         * <AUTHOR>
         * @date 2023/3/14 11:05
         * @param item 当前选中时间范围
         **/
        chooseTime (item) {
            this.timeFilterOption.forEach((timeItem) => {
                timeItem.checked = timeItem.val === item.val;
            });
            let timeObj = {startDate: '', endDate: ''}
            if (item.val === 'Last3Month') {
                timeObj = this.$utils.getLast3Month();
            } else if (item.val === 'LastHalfYear') {
                timeObj = this.$utils.getLastHalfYear();
            } else if (item.val === 'LastYear') {
                timeObj = this.$utils.getLastYear();
            }
            let filter = this.activityQRCodeOption.option.param.filtersRaw.filter(filterItem=> filterItem.property === 'created');
            if (!!timeObj.startDate) {
                if (filter.length <=0) {
                    this.activityQRCodeOption.option.param.filtersRaw = [
                        ...this.activityQRCodeOption.option.param.filtersRaw,
                        {id: 'created', property: 'created', value: timeObj.startDate, operator: '>='},
                        {id: 'created', property: 'created', value: timeObj.endDate, operator: '<='}
                    ];
                } else {
                    this.activityQRCodeOption.option.param.filtersRaw.forEach((filterItem) => {
                        if (filterItem.property === 'created' && filterItem.operator === '>=') {
                            filterItem.value = timeObj.startDate;
                        } else if (filterItem.property === 'created' && filterItem.operator === '<=') {
                            filterItem.value = timeObj.endDate;
                        }
                    })
                }
            } else {
                this.activityQRCodeOption.option.param.filtersRaw = this.activityQRCodeOption.option.param.filtersRaw.filter(filterItem=> filterItem.property !== 'created');
            }
            this.activityQRCodeOption.methods.reload();
        },
        /**
         * @desc 分享活动二维码
         * <AUTHOR>
         * @date 2023/3/14 11:02
         * @param data 当前选中行数据
         **/
        async shareQRCode (data) {
            if (!!data.qrCodeUrl) {
                const version = new Date().getTime();
                if (data.qrCodeUrl.indexOf('https') !== -1) {
                    this.imageUrl = data.qrCodeUrl + '?' + version;
                } else {
                    this.imageUrl = this.$env.appImageURL + data.qrCodeUrl + '?' + version;
                }
                this.currentData = data;
            } else {
                this.$utils.showLoading();
                const requestData = await this.$http.post(this.$env.appURL + '/interaction/link/qRHeader/viewIcQrCode',
                    {id: data.id}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('生成活动码失败！' + response.result);
                    }
                });
                if (requestData.success) {
                    this.$utils.hideLoading();
                    if (requestData.codeUrl.indexOf('https') !== -1) {
                        this.imageUrl = requestData.codeUrl;
                    } else {
                        this.imageUrl = this.$env.appImageURL + requestData.codeUrl;
                    }
                }
            }
            if (this.imageUrl) {
                this.$refs.imageShare.show();
            }
        },
        /**
         * @desc 保存数据
         * <AUTHOR>
         * @date 2023/3/14 10:26
         **/
        async saveData () {
            let msg = '';
            let param = {};
            if (this.type === 'UPDATE') {
                msg = '更新';
                this.currentData.qrName = this.qrcodeName
                param = this.currentData;
                param.row_status = ROW_STATUS.UPDATE;
            } else {
                param = {
                    qrName: this.qrcodeName,
                    row_status: ROW_STATUS.NEW,
                    companyId: this.userInfo.coreOrganizationTile['l3Id']
                }
                msg = '新建';
            }
            if (this.$utils.isEmpty(this.qrcodeName)) {
                this.$message.warn('请维护活动二维码名称');
                return;
            }
            this.$utils.showLoading();
            const updateData = await this.$http.post(this.$env.appURL + '/interaction/link/qRHeader/createFrontQrCode', param, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError(msg + '失败！' + response.result);
                }
            });
            if (updateData.success) {
                this.$utils.hideLoading();
                this.dialogFlag = false;
                this.activityQRCodeOption.methods.reload();
            }
        },
        /**
         * @desc 新建编辑活动二维码
         * <AUTHOR>
         * @date 2023/3/13 17:59
         * @param data 当前选中行数据
         * @param type 标识当前数据是新建还是更新
         **/
        async updateActivity (type, data) {
            this.type = type;
            this.dialogFlag = true;
            if (type === 'NEW') {
                this.qrcodeName = '';
            } else {
                this.currentData = data;
                this.qrcodeName = data.qrName;
            }
        },
        /**
         * @desc 跳转活动绑定设置页面
         * <AUTHOR>
         * @date 2023/3/14 09:36
         * @param data 当前选中行数据
         **/
        gotoBindActivitySet (data) {
            this.$nav.push('/pages/lj-consumers/activity-qrcode/activity-qrcode-bind-page', {
                pageFrom: this.pageFrom,
                data: data
            });
        },
        /**
         * @desc 失效/生效活动绑定
         * <AUTHOR>
         * @date 2023/3/14 09:38
         * @param data 当前选中行数据
         **/
        async invalidOrValidData (data) {
            let status = '';
            let msg = '';
            if (data.status === 'Active') {
                status = 'Inactive';
                msg = '失效';
            } else {
                status = 'Active';
                msg = '生效';
            }
            this.$utils.showLoading();
             const updateData = await this.$http.post(this.$env.appURL + '/interaction/link/qRHeader/updateStatus', {
                 id: data.id,
                 status: status
             }, {
                 autoHandleError: false,
                 handleFailed: (response) => {
                     this.$utils.hideLoading();
                     this.$showError(msg + '失败！' + response.result);
                 }
             });
            if (updateData.success) {
                this.$utils.hideLoading();
                data.stauts = status;
                this.$message.success(msg + '成功！');
                this.activityQRCodeOption.methods.reload();
            }
        }
    }
}
</script>
<style lang="scss">
.activity-qrcode-list-page{
    .top-filter{
        flex: 1;
        overflow-x: hidden;
        .top-filter-content{
            width: 100%;
            .top-filter-info{
                display: flex;
                white-space: nowrap;
                font-size: 24px;
                padding: 8px 24px;
                align-items: center;
                flex-wrap: nowrap;
                .time-list-item{
                    padding: 8px 16px;
                    margin-right: 8px;
                    white-space: nowrap;
                    display: inline-block;
                    background-color: #f2f2f2;
                    color: #333333;
                    border-radius: 4px;
                }
                .timeChecked{
                    background-color: rgba(47, 105, 248, 0.1) !important;
                    color: #2f69f8 !important;
                }
            }
        }
    }
    .activity-qrcode-list {
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
        .activity-qrcode-list-item{
            flex: 1;
            .list-item{
                display: flex;
                justify-content: space-between;
                align-items: center;
                color: #262626;
                margin-bottom: 28px;
                .label{
                    font-size: 24px;
                    padding-right: 10px;
                    min-width: 23%;
                }
                .activity-name{
                    font-size: 32px;
                    font-weight: 500;
                    width: 70%;
                    /*deep*/ .link-icon{
                    width: 80px;
                    height: 60px;
                    justify-content: start !important;
                }
                }
                /*deep*/ .link-button-size-mini{
                    width: 190px;
                }
                .interaction-name{
                    display: flex;
                }
                &_button{
                    width: 33%;
                }
            }
            .list-item:last-child{
                margin-bottom: 0;
            }
        }
    }
    .model-title{
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 20px;
        .title{
            width: 56%;
        }
        .iconfont {
            font-size: 40px;
        }
    }
    .dialog-title{
        width: 90vw;
        height: 180px;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        border-radius: 20px 20px 0 0;
        background-color: white;
        .title-name {
            font-size: 32px;
            color: #FFFFFF;
            letter-spacing: 0;
            text-align: center;
            line-height: 180px;
        }
    }
    .button-private {
        display: inline;
        padding-left: 0;
        padding-right: 0;
        box-sizing: border-box;
        font-size: 18px;
        text-align: center;
        text-decoration: none;
        line-height: 2.55555556;
        border-radius: 5px;
        -webkit-tap-highlight-color: transparent;
        overflow: hidden;
        color: #000;
    }

    .button-private:after {
        width: 200%;
        height: 200%;
        position: absolute;
        top: 0;
        left: 0;
        border: none;
        -webkit-transform: scale(.5);
        transform: scale(.5);
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        box-sizing: border-box;
    }
}
</style>
