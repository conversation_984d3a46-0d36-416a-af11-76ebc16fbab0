<template>
    <link-page class="activity-qrcode-bind-page">
        <link-auto-list :option="bindListOption" hideCreateButton :searchInputBinding="{props:{placeholder:'互动活动名称/营销活动名称'}}">
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="activity-qrcode-list">
                    <view class="activity-qrcode-list-item" slot="note">
                        <view class="list-item">
                            <view class="activity-name">{{data.marketActivityName}}</view>
                            <view class="status">
                                <status-button>{{data.bindFlag | lov('IC_BIND_STATUS')}}</status-button>
                            </view>
                        </view>
                        <view class="list-item">
                            <view class="list-item-data">
                                <view class="label">创建人</view>
                                <view class="data">{{data.icCreatedName}}</view>
                            </view>
                        </view>
                        <view class="list-item">
                            <view class="list-item-data">
                                <view class="label">互动形式</view>
                                <view class="data">{{data.interactionFormat | lov('INTERACTION_FORMAT')}}</view>
                            </view>
                        </view>
                        <view class="list-item">
                            <view class="list-item-data">
                                <view class="label">互动名称</view>
                                <view class="data name">{{data.interactionName}}</view>
                            </view>
                        </view>
                        <view class="list-item">
                            <view class="list-item-data">
                                <view class="label">互动有效时间</view>
                                <view class="data">{{data.interactionBeginTime | date('YYYY-MM-DD HH:mm')}} - {{data.interactionEndTime | date('YYYY-MM-DD HH:mm')}}</view>
                            </view>
                        </view>
                        <view class="list-item">
                            <view class="list-item_button">
                                <link-button size="mini" mode="stroke" v-if="data.sourceFrom === 'WeChatWork'" @tap="gotoActivityInfo(data)">查看活动情况</link-button>
                            </view>
                            <view class="list-item_button">
                                <link-button size="mini" mode="stroke" @tap="confirmBind('Bind', data)" v-if="data.status === 'Active' && data.bindFlag=== 'Unbound'">确认绑定</link-button>
                                <link-button size="mini" mode="stroke" @tap="bindOrUnbindData('Unbound', data)" v-else-if="data.bindFlag === 'Ended' || data.bindFlag === 'Binding' || data.bindFlag === 'TobeBound'">解除绑定</link-button>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
        <link-fab-button icon="icon-plus" :bottom="160" @tap="addNewActivity"></link-fab-button>
    </link-page>
</template>
<script>
import StatusButton from "../../lzlj/components/status-button";
import {LovService, DateService} from "link-taro-component";
import {ROW_STATUS} from "../../../utils/constant";
export default {
    name: "activity-qrcode-bind-page",
    components: {StatusButton},
    data () {
        const pageFrom = this.pageParam.pageFrom || '';
        const headId = this.pageParam.data.id;
        return {
            pageFrom,
            hasBindData: [],            // 当前活动二维码是否绑定互动
            headId,                 // 活动二维码头ID
            interactionId: '',
            bindListOption: new this.AutoList(this, { // 活动绑定设置列表对象
                module: this.$env.appURL + '/interaction/link/qRLine',
                url: {
                    queryByExamplePage: this.$env.appURL + '/interaction/link/qRLine/queryInteractionByHeadIdPage'
                },
                searchFields: ['marketActivityName,interactionName'],
                param: {
                    sorters: [{id: "bindFlag","property":"bindFlag","direction":"asc"},{"id":"id","property":"id","direction":"desc"}],
                    headId: '',
                    filtersRaw: []
                },
                hooks: {
                    beforeLoad(option) {
                        if (!this.pageParam.data.id) return Promise.reject('查询出错了，请重新进入');
                        option.param.headId = this.headId;
                        delete option.param.sort;
                        delete option.param.order;
                    },
                    afterLoad (data) {
                        // 判断当前活动是否有绑定数据
                        this.hasBindData = data.rows.filter((item)=> item.bindFlag === 'Binding');
                    }
                }
            }),
            activityOption: new this.AutoList(this, { // 选择绑定活动列表
                module: this.$env.appURL + '/interaction/link/interaction',
                url: {
                    queryByExamplePage: this.$env.appURL + '/interaction/link/interaction/queryByExampleForCodeQwPage'
                },
                searchFields: ['interactionName'],
                filterOption: [
                    {label: '互动形式', field: 'interactionFormat', type: 'lov', lov: 'INTERACTION_FORMAT'},
                    {label: '创建时间', field: 'created', type: 'date'},
                ],
                param: {
                    // oauth: 'MY_POSTN_ONLY',
                    filtersRaw: [
                        {id: 'type', property: 'type', value: 'Activity'},
                        {id: 'status', property: 'status', value: 'Active'},
                        {id: 'interactionFormat', property: 'interactionFormat', value: '[SkipPage,RandomLottery,ScreenShakes]', operator: 'NOT IN'},
                        {id: 'marketActivityStatus', property: 'marketActivityStatus', value: '[New,Published,Processing,Publishing,Pending]', operator: 'in'},
                        {id: 'sourceType', property: 'sourceType', value: 'Channel', operator: '<>'}
                    ],
                    invitationMaking: 'DefaultTemplate', // actSupport，invitationMaking 这两个参数为了后端将h5载体的互动筛选掉
                    actSupport: 'Html5' // actSupport，invitationMaking 这两个参数为了后端将h5载体的互动筛选掉
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} arrow="false" style="padding: 14px 10px;">
                            <link-checkbox val={data.id} toggleOnClickItem slot="thumb" style="padding-right: 0;"/>
                            <view style="-webkit-box-sizing: border-box;box-sizing: border-box;display: flex;width: 100%;-webkit-flex-direction: row;-ms-flex-direction: row;flex-direction: row;align-items: center;">
                                <view style="width: 100%;display: flex;flex: 1;flex-direction: column;justify-content: space-between;align-items: flex-start;overflow: hidden;">
                                    <view style="width: 100%;line-height: 32px;display:flex;justify-content: space-between; align-items:center;">
                                        <view style="font-size: 14px;color: #262626;font-weight: 500;text-overflow: ellipsis;overflow: hidden;width: 80%;white-space:nowrap;">{data.marketActivityName}</view>
                                        <view style="background: #2F69F8;box-shadow: 0 1.5px 2px 0 rgba(47, 105, 248, 0.35);min-width: 30px;color: white;letter-spacing: 0;white-space: nowrap;text-align: right;text-decoration: none;height: 9px;transform: skew(-30deg, 0);display: flex;justify-content: center;align-items: center;border-radius: 3px;padding: 5px 8px;margin-right: 5px;">
                                            <view style="font-size: 10px; transform: skew(30deg, 0);">{LovService.filter(data.interactionFormat, 'INTERACTION_FORMAT')}</view>
                                        </view>
                                    </view>
                                    <view style="width: 100%;line-height: 32px;font-size: 12px;display: flex;color: #333333;">
                                        <view style="color: #999999;margin-right: 5px;">创建人</view>{ data.creator }</view>
                                    <view style="width: 100%;line-height: 32px;font-size: 12px;display: flex;color: #333333;">
                                        <view style="color: #999999;margin-right: 5px;display: flex;white-space: nowrap;">互动名称</view><view style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">{data.interactionName}</view></view>
                                    <view style="width: 100%;line-height: 32px;font-size: 12px;display: flex;color: #333333;">
                                        <view style="color: #999999;margin-right: 5px;display: flex;white-space:nowrap;">互动有效时间</view><view style="white-space:nowrap;">{DateService.format(data.beginTime, 'YYYY-MM-DD HH:mm')}至{DateService.format(data.endTime, 'YYYY-MM-DD HH:mm')}</view></view>
                                </view>
                            </view>
                        </item>
                    )
                }
            })
        }
    },
    methods: {
        /**
         * @desc 确认绑定前是否提示
         * <AUTHOR>
         * @date 2023/3/16 14:54
         **/
        async confirmBind (type, data) {
            if (this.hasBindData.length > 0) {
                this.$dialog({
                    title: '提示',
                    content: `当前二维码已绑定【${this.hasBindData[0].interactionName}】，请确认是否更换为当前互动`,
                    cancelButton: true,
                    onConfirm: async () => {
                        await this.bindOrUnbindData(type, data);
                    },
                    onCancel: () => {
                    }
                })
            } else {
                await this.bindOrUnbindData(type, data);
            }
        },
        /**
         * @desc 解除绑定或确认绑定
         * <AUTHOR>
         * @date 2023/3/14 09:53
         * @param type 绑定或解除绑定
         * @param data 选中当前行数据
         **/
        async bindOrUnbindData (type, data) {
            if (data && data.interactionId) {
                this.interactionId = data.interactionId;
            }
            let msg = '';
            let bindFlag = '';
            if (type === 'Unbound') {
                bindFlag = 'Unbound';
                msg = '解除绑定';
            } else {
                bindFlag = 'Binding';
                msg = '确认绑定';
            }
            this.$utils.showLoading();
            const updateData = await this.$http.post(this.$env.appURL + '/interaction/link/qRHeader/bindInteraction', {
                headId: this.headId,
                interactionId: this.interactionId,
                bindFlag: bindFlag
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError(msg + '失败！' + response.result);
                }
            });
            if (updateData.success) {
                this.$utils.hideLoading();
                this.$message.success(msg + '成功');
                if (this.pageFrom === 'InteractiveConfig') {
                    this.$bus.$emit('refreshInteractionItem');
                }
                this.bindListOption.methods.reload();
            }
        },
        /**
         * @desc 新建活动绑定数据
         * <AUTHOR>
         * @date 2023/3/14 09:55
         **/
        async addNewActivity () {
            const data = await this.$object(this.activityOption, {multiple: false});
            this.interactionId = data.id;
            const interactionData = {
                row_status: ROW_STATUS.NEW,
                bindFlag: 'Unbound',
                headId: this.headId,
                interactionId: data.id,
                interactionName: data.interactionName,
                marketActivityName: data.marketActivityName,
                interactionBeginTime: data.beginTime,
                interactionEndTime: data.endTime,
                operateTime: null,
                ownerName: null,
                cardsSetId: data.cardsSetId || ''
            }
            this.$utils.showLoading();
            const insertData = await this.$http.post(this.$env.appURL + '/interaction/link/qRLine/insert', interactionData, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError('添加失败！' + response.result);
                }
            });
            if (insertData.success) {
                this.$utils.hideLoading();
                if (this.pageFrom === 'InteractiveConfig') {
                    this.$bus.$emit('refreshInteractionItem');
                }
                this.bindListOption.methods.reload();
            }
        },
        /**
         * @desc 查看活动详情
         * <AUTHOR>
         * @date 2023/3/14 09:58
         * @param data 选中当前行数据
         **/
        async gotoActivityInfo (data) {
            if (data.actId) {
                const cacheData = await this.$http.post('action/link/marketAct/queryById', {
                    id: data.actId
                });
                if(cacheData.success){
                    this.$dataService.setMarketActivityItem(cacheData.result);
                    this.$nav.push('/pages/lj-market-activity/market-activity/market-activity-item-page', {
                        data: cacheData.result,
                        pageSource: "view" //标志界面来源 区别于"其他信息"界面的预览活动
                    })
                }
            }
        }
    }
}
</script>
<style lang="scss">
.activity-qrcode-bind-page{
    .activity-qrcode-list {
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
        .activity-qrcode-list-item{
            flex: 1;
            .list-item{
                display: flex;
                justify-content: space-between;
                align-items: center;
                color: #262626;
                margin-bottom: 28px;
                &_button{
                    width: 33%;
                }
                .activity-name{
                    font-size: 32px;
                    font-weight: 500;
                    width: 75%;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                }
                /*deep*/ .link-button-size-mini{
                             width: 190px;
                         }
                .list-item-data{
                    display: flex;
                    font-size: 24px;
                    width: 100%;
                    .label{
                        color: #8C8C8C;
                        padding-right: 10px;
                        white-space: nowrap;
                    }
                    .name {
                        text-overflow: ellipsis;
                        overflow: hidden;
                        white-space: nowrap;
                        width: 80%;
                    }
                }
            }
            .list-item:last-child{
                margin-bottom: 0;
            }
        }
    }
}
</style>
