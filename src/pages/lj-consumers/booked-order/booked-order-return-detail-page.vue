<!--
@description 退货订单详情页面
<AUTHOR>
@date 2024-06-05
@file booked-order-return-detail-page.vue
-->
<template>
    <link-page class="booked-order-return-detail-page">
        <link-form disabled :option="option"
                   ref="bookedOrderForm" :rules="formRules"
                   hideSaveButton
                   :value="option.formData">
            <link-form-item label="动销登记类型" required field="orderChildType">
                <link-lov v-model="option.formData.orderChildType" type="PLACE_ORDER_TYPE"></link-lov>
            </link-form-item>
            <link-form-item label="客户姓名" required field="acctName">
                <link-input v-model="option.formData.acctName"></link-input>
            </link-form-item>
            <link-form-item label="退货手机号" required field="contactTel">
                <link-input v-model="option.formData.contactTel"></link-input>
            </link-form-item>
            <link-form-item label="退货时间" required field="orderDate">
                <link-date display-format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                           v-model="option.formData.orderDate"></link-date>
            </link-form-item>
            <link-form-item label="退货单位" field="orderSuper">
                <view :style="{overflow: 'auto', whiteSpace: 'nowrap'}">{{option.formData.orderSuper}}</view>
            </link-form-item>
            <view class="order-line-list-wrapper" v-if="option.formData.orderChildType">
                <title-line label-name="订单明细"/>
                <view class="order-line-list">
                    <link-swipe-action v-for="(item,index) in option.formData.saleOrderItemList" :key="index"
                                       class="order-line">
                        <item :arrow="false" @tap="gotoOrderItemLine(item, index)">
                            <view class="order-line-item" slot="note">
                                <view class="row-item">
                                    <view class="row-1">
                                        <view class="prod-name">{{item.prodName}}</view>
                                        <view class="prod-unit">{{item.qty}}{{item.prodUnit |
                                            lov('PROD_UNIT')}}
                                        </view>
                                    </view>
                                    <view class="row-2">
                                        <view class="promotion-price">单价:￥{{item.promotionPrice}}</view>
                                    </view>
                                </view>
                                <view class="sub-row-item">
                                    <view class="label">物流码:</view>
                                    <view class="logistic-list" v-if="item.logCodeList && item.logCodeList.length > 0">
                                        <text class="logistic-code"
                                              v-for="(logistic,index) in item.logCodeList.slice(0, (!!item.selectedFlag ? item.logCodeList.length : 5))"
                                              :key="index">
                                            {{logistic.logCode}}
                                        </text>
                                        <view class="view-all" v-if="item.logCodeList.length > 5"
                                              @tap="viewAllLogCode(item,$event)">
                                            {{item.selectedFlag?'折叠显示物流码':'查看完整物流码'}}
                                        </view>
                                    </view>
                                </view>
                                <view class="third-row-item">
                                    <view class="label">
                                        合计金额:
                                    </view>
                                    <view class="info">
                                        {{item.amount | cny}}
                                    </view>
                                </view>
                            </view>
                        </item>
                    </link-swipe-action>
                </view>
            </view>
        </link-form>
        <view class="blank" v-if="option.formData.id"></view>
    </link-page>
</template>

<script>
import {ROW_STATUS} from "../../../utils/constant";
import ConsumerCommon from "../consumer-common";
import TitleLine from "../../lzlj/components/title-line";
import LnkImgLzlj from "../../core/lnk-img-lzlj/lnk-img-lzlj";

export default {
    name: "booked-order-return-detail-page",
    mixins: [ConsumerCommon()],
    components: {
        TitleLine,
        LnkImgLzlj
    },
    data() {
        const config = {
            ...this.pageParam,
            data: {
                ...this.pageParam.data,
            },
            hooks: {
                // onAfterUpsert: () => {
                //     this.$nav.back();
                // }
            }
        };
        const option = new this.FormOption(this, {
            ...config,
            operator: 'READ'
        });
        return {
            attachmentList: [],
            canEdit: false,
            showTypeFlag: false, // 是否展示用途大类和用途子类型值列表
            fullAddr: '',
            tableNumberData: [],
            showTicketFlag: false,
            companyName: '',// 公司名称
            option,
            formRules: {
                customMobilePhone: this.Validator.phone()
            },
            showBtn: false
        }
    },
    async created() {
        this.option.formData = this.pageParam.data;
        this.companyName = await this.$lov.getNameByTypeAndVal('ACTIVITY_COMPANY', this.userInfo.coreOrganizationTile.l3Id);
        await this.queryBookedOrderInfo();
        // 从列表进入则查询订单行及附件列表
        await this.getOrderLineList();
    },
    methods: {
        /**
         @param item：当前订单明细数据
         @param event：点击事件
         @desc: 查看所有物流码
         @author: wangbinxin
         @date 2023-10-25 11-21
         **/
        viewAllLogCode(item, event) {
            event.stopPropagation();
            item.selectedFlag = !item.selectedFlag;
        },
        /**
         * @desc 获取图片长度
         * <AUTHOR>
         * @date 2021/7/16 16:49
         **/
        getImgList(data) {
            this.attachmentList = data;
        },
        /**
         @param 订单明细数据
         @desc: 处理订单明细数据
         @author: wangbinxin
         @date 2023-10-17 15-26
         **/
        orderLineData(orderLine, index) {
            let arr = this.$utils.deepcopy(this.option.formData.saleOrderItemList) || [];
            if (typeof index === "number") {
                arr.splice(index, 1, orderLine);
            } else {
                arr = [...arr, orderLine];
            }
            this.$set(this.option.formData, 'saleOrderItemList', arr);
            const len = this.option.formData.saleOrderItemList.length;
            // 计算汇总金额
            this.option.formData.orderAmount = 0;
            for (let i = 0; i < len; i++) {
                if (this.option.formData.saleOrderItemList[i].promotionPrice >= 0 && this.option.formData.saleOrderItemList[i].qty >= 0) {
                    this.option.formData.saleOrderItemList[i]['amount'] = this.option.formData.saleOrderItemList[i].promotionPrice * this.option.formData.saleOrderItemList[i].qty;
                    this.option.formData.orderAmount += this.option.formData.saleOrderItemList[i]['amount'];
                }
            }
        },
        /**
         * 进入订单行详情
         * <AUTHOR>
         * @date 2020-06-24
         */
        gotoOrderItemLine(item, index) {
            const _this = this;
            item.row_status = ROW_STATUS.UPDATE;
            if (this.$utils.isEmpty(item.id)) {
                item.row_status = ROW_STATUS.NEW;
                item['headId'] = this.option.formData.id;
            }
            if (!item.logCodeList) item.logCodeList = [];
            item.logCodeList.forEach((logCode) => {
                logCode.row_status = ROW_STATUS.UPDATE;
            });
            this.$nav.push('/pages/lj-consumers/booked-order/booked-order-line-new-page', {
                orderItem: this.option.formData,
                index: index,
                saveFlag: !this.option.formData.id,
                orderLineItem: item,
                type: this.option.formData.orderChildType,
                title: '退货订单明细',
                callback: (orderLine, orderData) => {
                    this.option.formData = orderData;
                    this.orderLineData(orderLine, index);
                }
            });
        },
        /**
         * 获取订单行列表
         * <AUTHOR>
         * @date 2020-6-24
         */
        async getOrderLineList() {
            if (this.option.formData['source'] === 'RecognitionTicket') {
                const orderItemData = !this.$utils.isEmpty(this.option.formData.orderItemJson) ? JSON.parse(this.option.formData.orderItemJson) : [];
                this.$set(this.option.formData, 'saleOrderItemList', orderItemData);
                const len = this.option.formData.saleOrderItemList.length;
                // 计算汇总金额
                this.option.formData.orderAmount = 0;
                for (let i = 0; i < len; i++) {
                    if (this.option.formData.saleOrderItemList[i].promotionPrice >= 0 && this.option.formData.saleOrderItemList[i].qty >= 0) {
                        this.option.formData.saleOrderItemList[i]['amount'] = this.option.formData.saleOrderItemList[i].promotionPrice * this.option.formData.saleOrderItemList[i].qty;
                        this.option.formData.orderAmount += this.option.formData.saleOrderItemList[i]['amount'];
                    }
                }
            } else {
                const _this = this;
                const data = await this.$http.post(this.$env.appURL + '/action/link/saleorderitem/queryByExamplePage',
                    {
                        sort: 'created',
                        order: 'desc',
                        pageFlag: true,
                        rows: 500,
                        page: 1,
                        onlyCountFlag: false,
                        oauth: 'All',
                        filtersRaw: [{'id': 'headId', 'property': 'headId', 'value': _this.option.formData.id}]
                    }, {
                        autoHandleError: false,
                        handleFailed: (data) => {
                            if (!data.success) {
                                this.$showError('查询订单行信息失败！' + data.result);
                            }
                        }
                    });
                if (data.success) {
                    this.$set(this.option.formData, 'saleOrderItemList', data.rows);
                    const len = _this.option.formData.saleOrderItemList.length;
                    // 计算汇总金额
                    _this.option.formData.orderAmount = 0;
                    for (let i = 0; i < len; i++) {
                        _this.option.formData.orderAmount += _this.option.formData.saleOrderItemList[i].amount;
                    }
                }
            }
        },
        /**
         * 初始化获取数据
         * <AUTHOR>
         * @date 2024-06-07
         */
        async queryBookedOrderInfo() {
            const data = await this.$http.post(this.$env.appURL + '/link/saleorder/queryById', {id: this.option.formData.id}, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`查询订单失败：${response.result}`);
                }
            });
            if (data.success) {
                this.option.formData = data.result;
            }
        }
    }
}
</script>

<style lang="scss">
.booked-order-return-detail-page {
    .order-line-list-wrapper {
        margin-top: 24px;

        .order-line-list {
            .order-line {
                background: white !important;

                &:not(:last-of-type) {
                    margin-bottom: 20px;
                }
            }

            .order-line-item {
                .row-item {
                    padding: 24px;
                    border-bottom: 2px solid #F2F2F2;

                    .row-1 {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        margin-bottom: 20px;

                        .prod-name {
                            font-size: 32px;
                            color: #41484D;
                            width: 80%;
                        }

                        .prod-unit {
                            color: #9CA5A8;
                            width: 20%;
                            text-align: right;
                        }
                    }

                    .row-2 {
                        display: flex;
                        align-items: center;
                        justify-content: flex-end;

                        .prod-code {
                            color: #41484D;
                        }

                        .promotion-price {
                            color: #41484D;
                            font-weight: 500;
                        }
                    }
                }

                .sub-row-item {
                    border-bottom: 2px solid #F2F2F2;
                    padding: 28px 42px;
                    display: flex;
                    justify-content: space-between;

                    .label {
                        white-space: nowrap;
                        flex: 2;
                    }

                    .logistic-list {
                        flex: 7;

                        .logistic-code, .view-all {
                            color: #9CA5A8;
                            display: inline-block;
                            width: 480px;
                            white-space: nowrap;
                            overflow-x: auto; /* 当内容超出宽度时显示滚动条 */
                            line-height: 50px;
                        }

                        .view-all {
                            color: #0076FF;
                        }
                    }
                }

                .third-row-item {
                    padding: 28px 42px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .label {
                        color: #41484D;
                    }

                    .info {
                        font-size: 36px;
                        color: #41484D;
                    }
                }
            }
        }
    }

    .blank {
        height: 376px;
        width: 100%;
    }
}
</style>
