<!--
@desc 动销-审批页面
-->
<template>
    <link-page class="booked-order-item-page">
        <approval-history-point-v3 :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId) && apVersion === 'v3'"></approval-history-point-v3>
        <approval-history-point :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId) && ($utils.isEmpty(apVersion) || apVersion !== 'v3')"></approval-history-point>
        <link-form :value="orderItem" hideSaveButton hideEditButton :class="!approvalFlag ? 'link-form-edit' : ''">
            <view class="account-info-wrapper">
                <view class="row-item">
                    <text class="account-name">{{orderItem.acctName || orderItem.customConsignee}}</text>
                    <text class="position">{{orderItem.customPosition}}</text>
                    <text class="status"
                          :class="{'status-new':orderItem.status === 'New','status-registered':orderItem.status === 'Registered','status-inactive':orderItem.status === 'Inactive'}">
                        {{orderItem.status | lov('ORDER_STATUS')}}
                    </text>
                </view>
                <view class="sub-row-item">
                    <text class="company">{{orderItem.customCompany || orderItem.companyName}}</text>
                    <text class="mobile-phone">{{orderItem.customMobilePhone}}</text>
                </view>
            </view>
            <view class="order-info-wrapper">
                <view class="row-item" v-if="orderItem.orderType !== 'KaOrder'">
                    <text class="label">订单类型:</text>
                    <text class="info">{{orderItem.orderChildType | lov('ORDER_SUB_TYPE')}}</text>
                </view>
                <view class="row-item" v-if="showTypeFlag && orderItem.orderType !== 'KaOrder'">
                    <text class="label">用途大类:</text>
                    <text class="info">{{orderItem.serviceCategory | lov('USE_TYPE')}}</text>
                </view>
                <view class="row-item" v-if="showTypeFlag && orderItem.orderType !== 'KaOrder'">
                    <text class="label">用途子类型:</text>
                    <text class="info">{{orderItem.serviceType | lov('USE_CATEGORY')}}</text>
                </view>
                <view class="row-item" v-if="(companyName === '头曲公司' || companyName === '大成浓香' || companyName === '特曲公司' || companyName === '窖龄公司') && orderItem.orderType !== 'KaOrder'">
                    <text class="label">用酒桌数:</text>
                    <text class="info">{{orderItem.tableNumber}}</text>
                </view>
                <view class="row-item">
                    <text class="label">订单总金额:</text>
                    <text class="info">{{orderItem.orderAmount}}</text>
                </view>
                <view class="row-item">
                    <text class="label">订购时间:</text>
                    <text class="info">{{orderItem.orderDate | date('YYYY-MM-DD')}}</text>
                </view>
                <view class="row-item" v-if="orderItem.orderType !== 'KaOrder' && !bookOrderShipFLag">
                    <text class="label">出货单位:</text>
                    <text class="info">{{orderItem.fromStore}}</text>
                </view>
                <view class="row-item" v-if="orderItem.orderType === 'KaOrder'">
                    <text class="label">系统名称:</text>
                    <text class="info">{{orderItem.superSystem}}</text>
                </view>
                <view class="row-item" v-if="orderItem.orderType === 'KaOrder'">
                    <text class="label">门店:</text>
                    <text class="info">{{orderItem.orderSuper}}</text>
                </view>
                <view class="row-item" v-if="orderItem.orderType === 'KaOrder'">
                    <text class="label">门店地址:</text>
                    <text class="info">{{orderItem.superProvince}}{{orderItem.superCity}}{{orderItem.orderSuperAddr}}</text>
                </view>
                <view class="row-item" v-if="orderItem.orderChildType === 'InvoiceBased'">
                    <text class="label">发票编号:</text>
                    <text class="info">{{orderItem.invoicesNo}}</text>
                </view>
                <view class="row-item address-item" v-if="orderItem.orderType !== 'KaOrder'">
                    <text class="label">收货地址:</text>
                    <text class="info">{{orderItem.province}}{{orderItem.city}}{{orderItem.district}}{{orderItem.street}}&nbsp;{{orderItem.customAddr}}</text>
                </view>
                <view class="row-item">
                    <text class="label">下单业务员:</text>
                    <text class="info">{{orderItem.salesmenName}}{{orderItem.salesmenPhone}}</text>
                </view>
            </view>
            <view class="order-line-list-wrapper">
                <title-line label-name="订单明细" @tap="addNewOrderLine()" :buttonName="showBtn && canEdit && !approvalFlag? '添加': ''"/>
                <view class="order-line-list">
                    <view class="notification-add-order-line"
                          v-if="$utils.isEmpty(orderItem.saleOrderItemList)">
                        还没有订单明细，请添加
                    </view>
                    <link-swipe-action v-for="(item,index) in orderItem.saleOrderItemList" :key="index" class="order-line">
                        <link-swipe-option slot="option" @tap="handleDeleteOrderItemLine(item,index)"
                                           v-if="orderItem.status === 'New'||orderItem.status === 'Rejected'"
                                           :disabled="!(showBtn&&canEdit&&!approvalFlag)">删除</link-swipe-option>
                        <item :arrow="false" @tap="canEdit&&!approvalFlag && gotoOrderItemLine(item, index)">
                            <view class="order-line-item" slot="note">
                                <view class="row-item">
                                    <view class="row-1">
                                        <view class="prod-name">{{item.prodName}}</view>
                                        <view class="prod-unit">{{item.qty}}{{item.prodUnit |
                                            lov('PROD_UNIT')}}
                                        </view>
                                    </view>
                                    <view class="row-2">
                                        <view class="promotion-price">单价:￥{{item.promotionPrice}}</view>
                                    </view>
                                </view>
                                <view class="sub-row-item" v-if="orderItem.orderChildType === 'LogisticsCodeBased'">
                                    <view class="label">物流码:</view>
                                    <view class="logistic-list" v-if="item.logCodeList && item.logCodeList.length > 0">
                                        <text class="logistic-code"
                                              v-for="(logistic,index) in item.logCodeList.slice(0, (!!item.selectedFlag ? item.logCodeList.length : 5))"
                                              :key="index">
                                            {{logistic.logCode}}&nbsp;{{logistic.logType | lov('LOG_CODE_TYPE')}}&nbsp;{{logistic.logOrderStatus | lov('LOG_ORDER_STATUS')}}
                                        </text>
                                        <view class="view-all" v-if="item.logCodeList.length > 5 "
                                              @tap="viewAllLogCode(item,$event)">
                                            查看完整物流码
                                        </view>
                                    </view>
                                </view>
                                <view class="third-row-item">
                                    <view class="label">
                                        合计金额:
                                    </view>
                                    <view class="info">
                                        {{item.amount | cny}}
                                    </view>
                                </view>
                            </view>
                        </item>
                    </link-swipe-action>
                </view>
            </view>
            <view class="order-line-list-wrapper">
                <title-line label-name="订单明细"/>
                <link-form-item label="关联活动" :arrow="false">
                    <link-object :option="activityOption"
                                 :row="orderItem"
                                 :value="orderItem.mcActName" disabled
                                 :map="{mcActId:'id',mcActName:'activityName'}">
                        <template v-slot="{data}">
                            <item :title="data.activityName" :key="data.id" :data="data"/>
                        </template>
                    </link-object>
                </link-form-item>
            </view>
            <link-form-item label="订单备注" vertical>
                <link-textarea readonly v-model="orderItem.comments" style="padding-bottom: 48rpx"/>
            </link-form-item>
            <view class="approval-list-wrapper"
                  v-if="!!orderApprovalItem&&(orderApprovalItem.approvalOpera === '审批通过'||orderApprovalItem.approvalOpera === '审批拒绝')">
                <view class="header">
                    <text>审批记录</text>
                </view>
                <view class="item-content">
                    <view class="flow">
                        <view class="blue-pot"></view>
                        <view class="gray-line"></view>
                    </view>
                    <view class="width-95">
                        <view class="row">
                            <view class="name">{{orderApprovalItem.approvalOpera}}</view>
                            <view class="time gray">{{orderApprovalItem.created | date('YYYY-MM-DD HH:mm:ss')}}
                            </view>
                        </view>
                        <view class="row">
                            <view class="gray">{{orderApprovalItem.approvalUserName}}</view>
                        </view>
                        <view class="row">
                            <view class="gray">{{orderApprovalItem.approvalView}}</view>
                        </view>
                    </view>
                </view>
            </view>
            <view v-if="!!orderItem.id && orderItem.orderSource !== 'channelMember'">
            <title-line label-name="照片" />
<!--                <lnk-img-watermark :parentId="orderItem.id"-->
<!--                                   @imgUploadSuccess="imgUploadSuccess"-->
<!--                                   :module-type="orderItem.orderType === 'KaOrder' ? 'mp' : 'bookedOrder'"-->
<!--                                   :delFlag="true"-->
<!--                                   moduleName="动销登记"-->
<!--                                   ref="onlyShowImage"-->
<!--                                   :newFlag="true">-->
<!--                </lnk-img-watermark>-->
            <lnk-img-lzlj :delFlag="showBtn&&canEdit&&!approvalFlag" :newFlag="showBtn&&canEdit&&!approvalFlag"
                          :module-type="orderItem.orderType === 'KaOrder' ? 'mp' : 'bookedOrder' "
                          @call="getImgList"
                         :parent-id="orderItem.id"></lnk-img-lzlj>
            </view>
        </link-form>
        <link-sticky v-if="showBtn && !approvalFlag && orderItem.orderType !== 'KaOrder'">
            <link-button v-if="orderItem.status === 'Registered'" block @tap="handleOrderItemStatus('Inactive')">作废
            </link-button>
            <link-button v-if="orderItem.status === 'Rejected'"
                         block
                         @tap="inactiveRow()">作废</link-button>
            <link-button v-if="orderItem.status === 'New'" block
                         @tap="handleDeleteOrderItem()">删除
            </link-button>
            <link-button block @tap="handleOrderItemStatus('Registered')"
                         v-if="orderItem.orderChildType === 'LogisticsCodeBased' && (orderItem.status === 'New'||orderItem.status === 'Rejected')">
                登记
            </link-button>
            <link-button block @tap="handleSubmit()"
                         v-if="orderItem.orderChildType === 'InvoiceBased' && (orderItem.status === 'New'||orderItem.status === 'Rejected')">
                提交
            </link-button>
        </link-sticky>
        <link-fab-button v-if="showBtn&&canEdit&&!approvalFlag" @tap="gotoOrderEdit()" icon="icon-edit"/>
        <link-sticky>
            <approval-operator-v3 :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId) && apVersion === 'v3'"></approval-operator-v3>
            <approval-operator :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId) && ($utils.isEmpty(apVersion) || apVersion !== 'v3')"></approval-operator>
        </link-sticky>
        <view class="blank" :style="apVersion === 'v3'?'height: 270rpx;':''" v-if="!$utils.isEmpty(approvalId)"></view>
    </link-page>
</template>

<script>
    import {ROW_STATUS} from "../../../utils/constant";
    import LnkImgLzlj from "../../core/lnk-img-lzlj/lnk-img-lzlj";
    import TitleLine from "../../lzlj/components/title-line";
    import ApprovalHistoryPointV3 from "../../lzlj/approval-v3/components/approval-history-point";
    import ApprovalOperatorV3 from "../../lzlj/approval-v3/components/approval-operator";
    import ApprovalHistoryPoint from "../../lzlj/approval/components/approval-history-point";
    import ApprovalOperator from "../../lzlj/approval/components/approval-operator";
    import LnkImgWatermark from "../../core/lnk-img-watermark/lnk-img-watermark";

    export default {
        name: "booked-order-item-page",
        components: {
            LnkImgWatermark,
            TitleLine,
            LnkImgLzlj,
            ApprovalHistoryPointV3,
            ApprovalHistoryPoint,
            ApprovalOperatorV3,
            ApprovalOperator},
        data() {
            const userInfo = this.$taro.getStorageSync('token').result;         // 获取用户信息
            const orderApprovalItem = {
                created: null
            };
            return {
                showTypeFlag: false, // 是否展示用途大类和用途子类型
                userInfo,
                companyName: '',// 公司名称
                orderItem: {},
                type: this.pageParam.type,
                canEdit: false,
                approvalFlag: false,
                orderApprovalItem: orderApprovalItem,
                activityOption: new this.AutoList(this, {
                    module: this.$env.appURL + '/action/marketactivity/link/marketActivity',
                    searchFields: ['activityName'],
                    param: {
                        rows: 25,
                    }
                }),
                bookListId: null,
                approvalId: null,
                apVersion: null, // v2/v3审批
                attachmentList: [],
                showBtn: false,
                bookOrderShipFLag: false //当前登陆用户公司，是否为会员服务部的公司id
            }
        },
        async created() {
            let sceneObj = await this.$scene.ready();//that.$store.getters['scene/getScene'];//消息场景对象
            this.companyName = await this.$lov.getNameByTypeAndVal('ACTIVITY_COMPANY', this.userInfo.coreOrganizationTile.l3Id);
            let code = this.pageParam.source; // 页面来源
            const approval_from = sceneObj.query['approval_from'];
            this.queryCfg();
            if (code === 'bookList') {
                // 此场景貌似已经不存在？？
                this.orderItem = {
                    saleOrderItemList: [],
                    ...this.pageParam.data
                };
            } else if (code === 'approval') {
                // 审批列表而来
                this.approvalId = this.pageParam.data.id; // 审批传过来的审批数据ID
                this.approvalFlag = this.$utils.isNotEmpty(this.approvalId)
                this.bookListId = this.pageParam.data.flowObjId; // 审批传过来的业务对象ID
                this.apVersion = this.pageParam.data.apVersion;
                console.log('this.apVersion', this.apVersion)
                console.log(this.approvalId, this.bookListId)
                if(this.$utils.isNotEmpty(this.bookListId)){
                    await this.queryItemById()
                }else{
                    this.$utils.showAlert('请联系管理员，未获取到动销订单信息！', {icon: 'none'});
                    return
                }
            } else {
                if (approval_from === 'qw') {
                    // 从小程序审批消息而来-卡片
                    this.approvalId = sceneObj.query['approval_id'];
                    this.approvalFlag = this.$utils.isNotEmpty(this.approvalId)
                    this.bookListId = sceneObj.query['flowObjId'];
                    this.apVersion = sceneObj.query['apVersion'];
                    if(this.$utils.isNotEmpty( this.bookListId)){
                        await this.queryItemById()
                    } else {
                        this.$utils.showAlert('请联系管理员，未获取到动销订单信息！', {icon: 'none'});
                        return
                    }
                } else {
                    // 消费者详情-动销列表转跳过来
                    this.orderItem = {
                        saleOrderItemList: [],
                        ...this.pageParam.data
                    };
                }
            }
            const shipIdList = await this.$utils.getCfgProperty('HIDE_BOOKORDER_SHIP');
            // 会员服务不显示出货单位
            this.bookOrderShipFLag = shipIdList.includes(this.userInfo.coreOrganizationTile.l3Id) || shipIdList.includes(this.orderItem.companyId);
            // 状态为非Inactive时可以编辑
            this.canEdit = this.orderItem.status !== 'Inactive' && this.orderItem.status !== 'Registered' && this.orderItem.status !== 'Submitted' && this.orderItem.orderType !== 'KaOrder';
            // 从列表进入则查询订单行及附件列表
            await this.getOrderLineList();
            // 获取审批记录
            await this.getOrderApprovalItem();
            await this.getShowBtn();
        },
        methods: {
            /**
             * @desc 查询参数配置
             * <AUTHOR>
             * @date 2023/8/8 14:28
             **/
            async queryCfg () {
                const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', { key: 'USE_TYPE_USE_CATEGORY'});
                if (data.success && data.value) {
                    const companyIds = data.value.split(',');
                    this.showTypeFlag = companyIds.indexOf(this.userInfo.coreOrganizationTile['l3Id']) !== -1
                }
            },
            /**
             @param 图片list
             @desc: 图片上传成功后展示图片信息
             @author: wangbinxin
             @date 2022-05-30 11-26
             **/
            imgUploadSuccess(arr){
                this.imgList = arr;
            },
            /**
             * @desc 获取图片长度
             * <AUTHOR>
             * @date 2021/7/16 16:49
             **/
            getImgList (data) {
                this.attachmentList = data;
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/20
             * @methods queryItemById
             * @para
             * @description 获取动销表单信息
             */
            async queryItemById(){
                const data = await this.$http.post(this.$env.appURL + '/action/link/saleorder/queryById', {
                    id: this.bookListId
                }, {
                    autoHandleError: false,
                    handleFailed: (data) => {
                        if (!data.success) {
                            this.$showError('获取订单信息出错！' + data.result);
                        }
                    }
                });
                this.orderItem = {
                    saleOrderItemList: [],
                    ... data.result
                };
                console.log('找公司orderItem', this.orderItem)
            },
            async getOrderApprovalItem() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/flowRecord/queryByExamplePage', {
                    sort: 'created',
                    order: 'desc',
                    pageFlag: true,
                    rows: 500,
                    page: 1,
                    onlyCountFlag: false,
                    oauth: 'All',
                    filtersRaw: [{'id': 'flowObjId', 'property': 'flowObjId', 'value': this.orderItem.id}]
                }, {
                    autoHandleError: false,
                    handleFailed: (data) => {
                        if (!data.success) {
                            this.$showError('查询审批信息失败！' + data.result);
                        }
                    }
                });
                this.orderApprovalItem = data.rows[0];
            },
            /**
             * 获取订单行列表
             * <AUTHOR>
             * @date 2020-6-24
             */
            async getOrderLineList() {
                if (this.orderItem['source'] === 'RecognitionTicket') {
                    const orderItemData = !this.$utils.isEmpty(this.orderItem.orderItemJson) ? JSON.parse(this.orderItem.orderItemJson) : [];
                    this.$set(this.orderItem, 'saleOrderItemList', orderItemData);
                    const len = this.orderItem.saleOrderItemList.length;
                    // 计算汇总金额
                    this.orderItem.orderAmount = 0;
                    for (let i = 0; i < len; i++) {
                        if (this.orderItem.saleOrderItemList[i].promotionPrice >= 0 && this.orderItem.saleOrderItemList[i].qty >= 0) {
                            this.orderItem.saleOrderItemList[i]['amount'] = this.orderItem.saleOrderItemList[i].promotionPrice * this.orderItem.saleOrderItemList[i].qty;
                            this.orderItem.orderAmount += this.orderItem.saleOrderItemList[i]['amount'];
                        }
                    }
                } else {
                    const _this = this;
                    const data = await this.$http.post(this.$env.appURL + '/action/link/saleorderitem/queryByExamplePage',
                        {
                            sort: 'created',
                            order: 'desc',
                            pageFlag: true,
                            rows: 500,
                            page: 1,
                            onlyCountFlag: false,
                            oauth: 'All',
                            filtersRaw: [{'id': 'headId', 'property': 'headId', 'value': _this.orderItem.id}]
                        }, {
                            autoHandleError: false,
                            handleFailed: (data) => {
                                if (!data.success) {
                                    this.$showError('查询订单行信息失败！' + data.result);
                                }
                            }
                        });
                    if (data.success) {
                        this.$set(this.orderItem, 'saleOrderItemList', data.rows);
                        const len = _this.orderItem.saleOrderItemList.length;
                        // 计算汇总金额
                        _this.orderItem.orderAmount = 0;
                        for (let i = 0; i < len; i++) {
                            _this.orderItem.orderAmount += _this.orderItem.saleOrderItemList[i].amount;
                        }
                    }
                }
            },
            /**
             * 新建订单行
             * <AUTHOR>
             * @date 2020-6-24
             */
            async addNewOrderLine() {
                let flag = ''
                if (this.orderItem['source'] === 'RecognitionTicket') {
                    flag = 'Add'
                }
                const orderLineItem = {
                    id: await this.$newId(),
                    headId: this.orderItem.id,
                    row_status: ROW_STATUS.NEW,
                    logCodeList: []
                };
                this.$nav.push('/pages/lj-consumers/booked-order/booked-order-line-new-page', {
                    orderItem: this.orderItem,
                    flag: flag,
                    orderLineItem: orderLineItem,
                    type: this.orderItem.orderChildType,
                    callback: async (orderLine, orderData)=>{
                        this.orderItem = orderData;
                        await this.getOrderLineList();
                    }
                });
            },
            /**
             * 进入订单行详情
             * <AUTHOR>
             * @date 2020-06-24
             */
            gotoOrderItemLine(item, index) {
                const _this = this;
                item.row_status = ROW_STATUS.UPDATE;
                if (this.$utils.isEmpty(item.id)) {
                    item.row_status = ROW_STATUS.NEW;
                    item['headId'] = this.orderItem.id;
                }
                if (!item.logCodeList) item.logCodeList = [];
                item.logCodeList.forEach((logCode) => {
                    logCode.row_status = ROW_STATUS.UPDATE;
                });
                this.$nav.push('/pages/lj-consumers/booked-order/booked-order-line-new-page', {
                    orderItem: this.orderItem,
                    index: index,
                    orderLineItem: item,
                    type: this.orderItem.orderChildType,
                    callback: (orderLine, orderData) => {
                        this.orderItem = orderData;
                        _this.getOrderLineList();
                    }
                });
            },
            viewAllLogCode(item, event) {
                event.stopPropagation();
                item.selectedFlag = !item.selectedFlag;
            },
            /**
             * 登记/作废订单头
             * <AUTHOR>
             * @date 2020-07-07
             */
            async handleOrderItemStatus(type) {
                // 类型为发票且操作为登记时,校验图片数量
                if (this.orderItem.orderChildType === 'InvoiceBased' && type === 'Registered') {
                    if (this.attachmentList.length === 0) {
                        this.$message.primary('请上传发票附件!');
                        return;
                    }
                }
                if (this.$utils.isEmpty(this.orderItem.saleOrderItemList) && this.orderItem.saleOrderItemList.length <= 0) {
                    this.$message.primary('请添加订单明细!');
                    return;
                }
                const _this = this;
                const originalStatus = _this.orderItem.status;
                const operation = type === 'Registered' ? '登记' : '作废';
                const url = type === 'Registered' ? '/action/link/saleorder/registeredBookOrder' : '/action/link/saleorder/inactiveBookOrder';
                // 物流码校验必须要有
                if (this.orderItem.orderChildType === 'LogisticsCodeBased' && type === 'Registered' && !this.$utils.isEmpty(this.orderItem.saleOrderItemList)) {
                    for (let i = 0; i < this.orderItem.saleOrderItemList.length; i++) {
                        if (this.$utils.isEmpty(this.orderItem.saleOrderItemList[i].logCodeList)) {
                            await this.$message.primary('该订单【' + this.orderItem.saleOrderItemList[i].prodCode + '】【' + this.orderItem.saleOrderItemList[i].prodName + '】暂无物流码！');
                            return;
                        } else {
                            const qty = this.orderItem.saleOrderItemList[i]['qty'];
                            const len = this.orderItem.saleOrderItemList[i].logCodeList.length;
                            if (qty !== len) {
                                await this.$message.primary(`【${this.orderItem.saleOrderItemList[i].prodCode}】【${this.orderItem.saleOrderItemList[i].prodName}】下单数量为${qty}，物流码数量为${len},双方数量不一致，请检查核对！`);
                                return;
                            }
                            for (let j = 0; j < len; j++) {
                                if (this.$utils.isEmpty(this.orderItem.saleOrderItemList[i].logCodeList[j].logCode)) {
                                    await this.$message.primary('物流码列表中存在为空的记录,请填写后再保存订单行')
                                    return;
                                }
                            }
                        }
                    }
                }
                _this.orderItem.status = type;
                _this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + url, _this.orderItem, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError(`订单${operation}失败` + (response.result || response.message));
                        _this.orderItem.status = originalStatus;
                    }
                });
                if (data.success) {
                    _this.$utils.hideLoading();
                    this.$nav.back();
                }
            },
            /**
             * 作废订单头
             * <AUTHOR>
             * @date 2024-03-29
             */
            async inactiveRow() {
                const url = '/action/link/saleorder/rejectedOrderToInactive';
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + url, {id: this.orderItem.id}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('订单作废失败:' + (response.result || response.message));
                    }
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    this.$set(this.orderItem, 'status', 'Inactive');
                }
            },
            /**
             * 删除订单头
             * <AUTHOR>
             * @date 2020-07-07
             */
            async handleDeleteOrderItem() {
                const _this = this;
                this.$taro.showModal({
                    title: '提示',
                    content: '是否要删除订单信息',
                    success: async (res) => {
                        if (res.confirm) {
                            const data = await this.$http.post(this.$env.appURL + '/action/link/saleorder/deleteById', _this.orderItem, {
                                autoHandleError: false,
                                handleFailed: (data) => {
                                    if (!data.success) {
                                        this.$showError('订单删除失败！' + data.result);
                                    }
                                }
                            });
                            this.$nav.back();
                        } else if (res.cancel) {
                        }
                    }
                });
            },
            /**
             *  保存并提交
             *
             *  <AUTHOR>
             *  @date        2020-07-07
             */
            handleSubmit() {
              if (this.$utils.isEmpty(this.orderItem.saleOrderItemList) && this.orderItem.saleOrderItemList.length <= 0) {
                this.$message.primary('请添加订单明细!');
                return;
              }
              if (this.orderItem['source'] === 'RecognitionTicket') {
                  const data = this.orderItem.saleOrderItemList.filter((item) => this.$utils.isEmpty(item.prodId)
                      || this.$utils.isEmpty(item.prodUnit)
                      || this.$utils.isEmpty(item.qty)
                      || this.$utils.isEmpty(item.promotionPrice)
                  );
                  if (data.length > 0) {
                      this.$message.warn('订单行信息未完善，请检查!');
                      return;
                  }
              }
                // 类型为发票且操作为提交时,校验图片数量
                if (this.orderItem.orderChildType === 'InvoiceBased') {
                    if (this.attachmentList.length === 0) {
                        this.$message.primary('请上传发票附件!');
                        return;
                    }
                }
                const _this = this;
                this.$taro.showModal({
                    title: '提示',
                    content: '是否要提交订单信息?',
                    success: async (res) => {
                        if (res.confirm) {
                            const data = await this.$http.post(_this.$env.appURL + '/action/link/srorder/submitFlow', {id: _this.orderItem.id}, {
                                autoHandleError: false,
                                handleFailed: (data) => {
                                    if (!data.success) {
                                        this.$showError('订单提交失败！' + data.result);
                                    }
                                }
                            });
                            if (!data.success) {
                                await this.$message.primary(data.result)
                            }
                            this.$nav.back();
                        } else if (res.cancel) {
                        }
                    }
                });
            },
            /**
             * 删除订单行
             * <AUTHOR>
             * @date 2020-07-07
             * @param item 订单行对象
             * @param index 在数组中的索引位置
             */
            async handleDeleteOrderItemLine(item, index) {
                this.$utils.showLoading();
                if (this.$utils.isEmpty(item.id)) {
                    this.orderItem.saleOrderItemList.splice(index, 1);
                    const len = this.orderItem.saleOrderItemList.length;
                    // 计算汇总金额
                    this.orderItem.orderAmount = 0;
                    for (let i = 0; i < len; i++) {
                        if (this.orderItem.saleOrderItemList[i].promotionPrice >= 0 && this.orderItem.saleOrderItemList[i].qty >= 0) {
                            this.orderItem.saleOrderItemList[i]['amount'] = this.orderItem.saleOrderItemList[i].promotionPrice * this.orderItem.saleOrderItemList[i].qty;
                            this.orderItem.orderAmount += this.orderItem.saleOrderItemList[i]['amount'];
                        }
                    }
                } else {
                    const data = await this.$http.post(this.$env.appURL + '/action/link/saleorderitem/deleteById', item, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$utils.hideLoading();
                            this.$showError('订单行数剧删除更新失败！' + response.result);
                        }
                    });
                    if (data.success) {
                        this.$utils.hideLoading();
                        this.orderItem.saleOrderItemList.splice(index, 1);
                        const len = this.orderItem.saleOrderItemList.length;
                        // 计算汇总金额
                        this.orderItem.orderAmount = 0;
                        for (let i = 0; i < len; i++) {
                            this.orderItem.orderAmount += this.orderItem.saleOrderItemList[i].amount;
                        }
                    }
                }
                if (this.orderItem['source'] === 'RecognitionTicket') {
                    const updateData = {
                        id: this.orderItem.id,
                        orderItemJson: JSON.stringify(this.orderItem.saleOrderItemList),
                        updateFields: "orderItemJson"
                    };
                   const orderItem = await this.$http.post(this.$env.appURL + '/action/link/saleorder/update', updateData, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$utils.hideLoading();
                            this.$showError('订单数据更新失败！' + response.result);
                        }
                    });
                    this.orderItem = orderItem.newRow;
                    this.getOrderLineList();
                    this.$utils.hideLoading();
                }
                this.$message.success('订单行已删除!')
            },
            /**
             * 进入订单头编辑页面
             * <AUTHOR>
             * @date 2020-07-08
             */
            gotoOrderEdit() {
                const _this = this;
                this.orderItem.row_status = ROW_STATUS.UPDATE;
                this.$nav.push('/pages/lj-consumers/booked-order/booked-order-new-page', {
                    data: this.orderItem,
                    type: this.orderItem.orderChildType,
                    callback: (callbackParams) => {
                        if (callbackParams) {
                            _this.orderItem = callbackParams;
                            // 查询订单行list
                            _this.getOrderLineList();
                        }
                    }
                });
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/05/30
             * @methods: getShowBtn
             * @para:
             * @description: 是否展示操作按钮
             **/
            async getShowBtn () {
                const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', {key: 'BOOK_ORDER_SHOW'});
                if (data.success) {
                    if (this.orderItem.orderType === 'KaOrder') {
                        this.showBtn = true;
                    } else {
                        this.showBtn = data.value.split(',').includes(this.userInfo.coreOrganizationTile.l3Id);
                    }
                } else {
                    this.$message.error('查询是否展示按钮失败：'+ data.result);
                }
            }
        }
    }
</script>

<style lang="scss">
    .booked-order-item-page {
        background: #F2F2F2;
        font-size: 28px;
        .blank {
            height: 376px;
            width: 100%;
        }
        .link-form-edit{
            padding-bottom: 120px;
        }
        .account-info-wrapper {
            padding: 24px 36px;
            border-bottom: 2px solid #F2F2F2;
            background: white;

            .row-item {
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                -webkit-box-pack: start;
                -ms-flex-pack: start;
                justify-content: flex-start;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;

                .account-name {
                    font-weight: 600;
                    font-size: 32px;
                    display: inline-block;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                    flex: 3;
                }

                .position {
                    flex: 5;
                }

                .status {
                    flex: 2;
                    text-align: right;

                    &-new {
                        color: #0076FF;
                    }

                    &-registered {
                        color: green;
                    }

                    &-inactive {
                        color: red;
                    }
                }
            }

            .sub-row-item {
                display: flex;
                justify-content: space-between;
                color: #9CA5A8;
                margin-top: 20px;
            }
        }

        .order-info-wrapper {
            padding: 32px 40px;
            background: white;

            .row-item {
                color: #6B7378;
                margin: 20px 0;
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                -webkit-box-pack: start;
                -ms-flex-pack: start;
                justify-content: flex-start;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;

                &.address-item {
                    .info {
                        word-break: break-all;
                        word-wrap: break-word;
                        white-space: pre-wrap;
                    }
                }

                .label {
                    flex: 3;
                }

                .info {
                    flex: 7;
                }
            }
        }

        .order-line-list-wrapper {
            margin-top: 24px;
            .order-line-list {
                .order-line {
                    background: white !important;

                    &:not(:last-of-type) {
                        margin-bottom: 20px;
                    }
                }

                .order-line-item {
                    .row-item {
                        padding: 24px;
                        border-bottom: 2px solid #F2F2F2;
                        .row-1 {
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            margin-bottom: 20px;
                            .prod-name {
                                font-size: 32px;
                                color: #41484D;
                                width: 80%;
                            }

                            .prod-unit {
                                color: #9CA5A8;
                                width: 20%;
                                text-align: right;
                            }
                        }

                        .row-2 {
                            display: flex;
                            align-items: center;
                            justify-content: flex-end;

                            .prod-code {
                                color: #41484D;
                            }

                            .promotion-price {
                                color: #41484D;
                                font-weight: 500;
                            }
                        }
                    }

                    .sub-row-item {
                        border-bottom: 2px solid #F2F2F2;
                        padding: 28px 42px;
                        display: flex;
                        justify-content: space-between;

                        .label {
                            white-space: nowrap;
                            flex: 2;
                        }

                        .logistic-list {
                            flex: 7;
                            .logistic-code, .view-all {
                                color: #9CA5A8;
                                display: inline-block;
                                width: 480px;
                                white-space: nowrap;
                                overflow-x: auto; /* 当内容超出宽度时显示滚动条 */
                                line-height: 50px;
                            }

                            .view-all {
                                color: #0076FF;
                            }
                        }
                    }

                    .third-row-item {
                        padding: 28px 42px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        .label {
                            color: #41484D;
                        }

                        .info {
                            font-size: 36px;
                            color: #41484D;
                        }
                    }
                }

                .notification-add-order-line {
                    display: -webkit-box;
                    display: -ms-flexbox;
                    display: flex;
                    -webkit-box-pack: center;
                    -ms-flex-pack: center;
                    justify-content: center;
                    -webkit-box-align: center;
                    -ms-flex-align: center;
                    align-items: center;
                    background: white;
                    height: 15vh;
                    color: gray;
                }
            }
        }

        $main-color: #0076FF;

        .header {
            display: flex;
            justify-content: space-between;
            padding: 24px;
            align-items: center;
            background-color: #F2F2F2;
        }

        .approval-list-wrapper {
            margin-bottom: 24px;
            .item-content {
                background-color: white;
                padding: 32px 20px;
                width: 100%;
                display: flex;

                .flow {
                    width: 5%;

                    .blue-pot {
                        height: 18px;
                        width: 18px;
                        background-color: #0076ff;
                        border-radius: 50%;
                        margin-left: -6px
                    }

                    .gray-line {
                        height: 70%;
                        width: 4px;
                        background-color: lightgrey;
                        margin-top: 4px;
                    }
                }

                .width-95 {
                    width: 95%;

                    .row {
                        width: 100%;
                        display: flex;

                        .name {
                            display: inline-block;
                            width: 60%;
                            line-height: 28px;
                        }

                        .gray {
                            color: gray;
                        }

                        .time {
                            display: inline-block;
                            width: 40%;
                            line-height: 28px;
                            color: gray;
                            font-size: 26px;
                            text-align: center;
                        }
                    }
                }
            }
        }
    }
</style>
