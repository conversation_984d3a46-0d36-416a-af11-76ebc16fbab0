<!--
@description 退货订单列表页面
<AUTHOR>
@date 2024-06-05
@file booked-order-return-list-page
-->
<template>
    <link-page class="booked-order-return-list-page">
        <view class="blank"></view>
        <link-auto-list :option="autoList" hideCreateButton>
            <link-filter-group slot="filterGroup">
                <link-filter-item label="动销总金额(升序)" :param="{sort:{field:'orderAmount',desc:false}}"/>
                <link-filter-item label="订购时间(升序)" :param="{sort:{field:'orderDate',desc:false}}"/>
                <link-filter-item label="创建时间(升序)" :param="{sort:{field:'created',desc:false}}"/>
                <link-filter-item label="最近更新(升序)" :param="{sort:{field:'lastUpdated',desc:false}}"/>
            </link-filter-group>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="booked-order-return-list-item">
                    <view slot="note" class="item-container">
                        <view class="row-item">
                            <view class="accnt-name">{{data.acctName}}</view>
                            <view class="mobile-phone">{{data.contactTel}}</view>
                        </view>
                        <view class="sub-row-item">
                            <view class="order-info">
                                <view class="company">公司: {{data.companyName}}</view>
                                <view class="order-total-amount">退货总金额: {{data.orderAmount}}</view>
                                <view class="order-total-amount" @tap="copyData(data.orderNo)">订单ID: {{data.orderNo}}</view>
                                <view class="order-date">退单日期: {{data.orderDate | date('YYYY-MM-DD')}}</view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
import {ROW_STATUS} from "../../../utils/constant";
import LnkTaps from "../../core/lnk-taps/lnk-taps";
import ConsumerListCommon from "../consumer-list-common";

export default {
    name: "booked-order-return-list-page",
    components: {LnkTaps},
    mixins: [ConsumerListCommon()],
    data() {
        const userInfo = this.$taro.getStorageSync('token').result;
        const url = this.$env.appURL + '/action/link/saleorder/queryByExamplePage';
        const autoList = new this.AutoList(this, {
            module: this.$env.appURL + "/action/link/saleorder",
            itemPath: '/pages/lj-consumers/booked-order/booked-order-return-detail-page',
            url: {
                queryByExamplePage: url
            },
            // loadOnStart: false,
            fetchItem: true,
            param: {
                filtersRaw: [{
                    id: 'parentOrderId',
                    property: 'parentOrderId',
                    operator: '=',
                    value: this.pageParam.data.id
                }, {
                    id: 'orderChildType',
                    property: 'orderChildType',
                    operator: '=',
                    value: 'ReturnOrder'
                }]
            },
            stayFields: 'id,acctName,companyName',
            hooks: {
                async beforeGotoItem(param) {
                    param.source = 'bookList'
                }
            },
            searchFields: ['id', 'acctName', 'companyName'],
            sortOptions: null,
            filterOption: [
                {label: '订单状态', field: 'status', type: 'lov', lov: 'ORDER_STATUS'},
                {label: '退货时间', field: 'orderDate', type: 'date'},
            ]
        });
        return {
            url,
            autoList,
            userInfo,
        };
    },
    async created() {
    },
    methods: {
        /**
         * @desc 复制ID
         * <AUTHOR>
         * @date 2021/8/13 15:38
         **/
        copyData(data) {
            wx.setClipboardData({
                data: data,
                success: function () {
                    // 添加下面的代码可以复写复制成功默认提示文本`内容已复制`
                    wx.showToast({
                        title: '复制成功',
                        duration: 3000
                    });
                    wx.getClipboardData({
                        success: function (res) {
                        }
                    })
                }
            })
        },
        onBack() {
            this.autoList.methods.reload();
        }
    }
}
</script>
<style lang="scss">
.booked-order-return-list-page {
    @include flex();
    @include direction-column();

    .booked-order-return-list-item {
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
    }

    .item-container {
        color: #262626;
        position: relative;
    }

    .row-item {
        @include flex-center-center();
        @include space-between();
        margin-bottom: 20px;
        width: 100%;
        font-weight: 500;

        .accnt-name {
            font-weight: 500;
        }

        .mobile-phone {
        }
    }

    .sub-row-item {
        width: 100%;
        @include flex-center-center();
        @include space-between();

        .order-info {
            .company, .order-total-amount.order-date {
                margin: 10px 0;
                display: flex;
            }
        }

        .status {
            width: 30%;
            text-align: right;
            font-weight: 500;

            &-new {
                color: $main-color;
            }

            &-registered {
                color: green;
            }

            &-inactive {
                color: red;
            }
        }
    }
}
</style>
