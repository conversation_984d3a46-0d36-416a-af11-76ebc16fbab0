<template>
    <link-page class="booked-order-new-page">
        <link-form :disabled="formDisabled" :option="option"
                   ref="bookedOrderForm" :rules="formRules"
                   hideSaveButton
                   :value="option.formData">
            <link-form-item label="动销登记类型" required field="orderChildType">
                <link-lov v-model="option.formData.orderChildType" :parent-val="'BookedOrder'"
                          parent-type="ORDER_TYPE" type="ORDER_SUB_TYPE"></link-lov>
            </link-form-item>
            <link-form-item label="用途大类" :required="showTypeFlag" v-if="showTypeFlag" field="serviceCategory">
                <link-lov v-model="option.formData.serviceCategory" type="USE_TYPE" parent-type="ACTIVITY_COMPANY"
                          :parent-val="userInfo.coreOrganizationTile['l3Id']" @change="changeVal"></link-lov>
            </link-form-item>
            <link-form-item label="用途子类型" :required="showTypeFlag" v-if="showTypeFlag" field="serviceType">
                <link-lov v-model="option.formData.serviceType" :parent-val="option.formData.serviceCategory"
                          parent-type="USE_TYPE" type="USE_CATEGORY"></link-lov>
            </link-form-item>
            <link-form-item label="用酒桌数" :required="isTableNumber" v-if="isTableNumber" field="tableNumber">
                <link-number v-model="option.formData.tableNumber" :min="1"/>
            </link-form-item>
            <link-form-item label="客户姓名" required field="acctName">
                <link-object :option="accountOption"
                             :row="option.formData"
                             :value="option.formData.acctName"
                             :map="{
                                        acctId: 'id',
                                        acctName: 'acctName',
                                        customMobilePhone: 'mobilePhone1',
                                        customCompany: 'company',
                                        companyId: 'companyId',
                                        customConsignee: 'acctName',
                                        contact: 'acctName',
                                        contactTel: 'mobilePhone1'
                                    }"
                             :afterSelect="onAfterSelectAccount">
                    <template v-slot="{data}">
                        <item :arrow="false" :title="data.acctName" :note="data.company"
                              :desc="data.mobilePhone1" :key="data.id" :data="data"/>
                    </template>
                </link-object>
            </link-form-item>
            <link-form-item label="收货手机号" required field="customMobilePhone">
                <link-input v-model="option.formData.customMobilePhone"></link-input>
            </link-form-item>
            <link-form-item label="客户单位名称" readonly>
                <link-input v-model="option.formData.customCompany"></link-input>
            </link-form-item>
            <link-form-item label="订购时间" required field="orderDate">
                <link-date display-format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                           v-model="option.formData.orderDate" :max="orderDateMax"></link-date>
            </link-form-item>
            <link-form-item label="出货类型" required v-if="!bookOrderShipFLag">
                <link-lov v-model="acctType" :parent-val="userInfo.coreOrganizationTile['l3Id']"
                          @change="changeAcctType"
                          parent-type="ACTIVITY_COMPANY" type="SHIPMENT_TYPE"></link-lov>
            </link-form-item>
            <link-form-item label="出货单位" required field="fromStore" v-if="!bookOrderShipFLag">
                <link-object :option="terminalListOption"
                             :row="option.formData"
                             :value="option.formData.fromStore"
                             :map="{fromStore:'acctName', orderSuperId: 'id'}">
                </link-object>
            </link-form-item>
            <link-form-item :label="(companyName === '头曲公司' || companyName === '大成浓香') ? '用酒地址' : '收货地址'"
                            required
                            field="fullAddr">
                <link-object :option="addressOption"
                             :row="option.formData"
                             :value="option.formData.fullAddr"
                             :map="{}"
                             :afterSelect="onAfterSelectAddress"
                             :beforeSelect="onBeforeSelectAddress"
                />
            </link-form-item>
            <link-form-item label="发票编号"
                            v-if="option.formData.orderChildType === 'InvoiceBased'"
                            required
                            field="invoicesNo">
                <link-input v-model="option.formData.invoicesNo"></link-input>
            </link-form-item>
            <link-form-item label="下单业务员名称" readonly>
                <link-input v-model="option.formData.salesmenName"></link-input>
            </link-form-item>
            <link-form-item label="下单业务员电话" readonly>
                <link-input v-model="option.formData.salesmenPhone"></link-input>
            </link-form-item>
            <link-form-item label="关联活动">
                <link-object :option="activityOption"
                             :row="option.formData"
                             :value="option.formData.mcActName"
                             :map="{mcActId:'id',mcActName:'activityName'}">
                    <template v-slot="{data}">
                        <item :key="data.id" :data="data" :arrow="false">
                            <view slot="note">
                                <view class="cust-item-mess-info" style="font-size: 28rpx;color: #262626;">
                                    活动编码：{{data.activityNum}}
                                </view>
                                <view class="cust-item-mess-info" style="font-size: 28rpx;color: #262626;">
                                    活动名称：{{data.activityName}}
                                </view>
                                <view class="cust-item-mess-info" style="font-size: 28rpx;color: #262626;">
                                    活动时间：{{data.startTime | date('YYYY-MM-DD HH:mm')}}至{{data.endTime | date('YYYY-MM-DD
                                    HH:mm')}}
                                </view>
                                <view class="cust-item-mess-info" style="font-size: 28rpx;color: #262626;">
                                    提报人：{{data.creator}}
                                </view>
                            </view>
                        </item>
                    </template>
                </link-object>
            </link-form-item>
            <link-form-item label="系统来源" v-if="!canEdit" readonly>
                <link-lov v-model="option.formData.orderSource" type="BOOK_ORDER_SOURCE"></link-lov>
                </link-form-item>
            <link-form-item label="订单备注" vertical>
                <link-textarea v-model="option.formData.comments" :nativeProps="{maxlength:200}"></link-textarea>
            </link-form-item>
            <view class="order-line-list-wrapper" v-if="option.formData.orderChildType">
                <title-line label-name="订单明细" @tap="addNewOrderLine()" :buttonName="(showBtn && canEdit) ? '添加': ''"/>
                <view class="order-line-list">
                    <link-swipe-action v-for="(item,index) in option.formData.saleOrderItemList" :key="index"
                                       class="order-line">
                        <link-swipe-option slot="option" @tap="handleDeleteOrderItemLine(item,index)"
                                           v-if="(option.formData.status === 'New'||option.formData.status === 'Rejected') && showBtn"
                                           :disabled="!canEdit || !showBtn">删除
                        </link-swipe-option>
                        <item :arrow="false" @tap="canEdit && gotoOrderItemLine(item, index)">
                            <view class="order-line-item" slot="note">
                                <view class="row-item">
                                    <view class="row-1">
                                        <view class="prod-name">{{item.prodName}}</view>
                                        <view class="prod-unit">{{item.qty}}{{item.prodUnit |
                                            lov('PROD_UNIT')}}
                                        </view>
                                    </view>
                                    <view class="row-2">
                                        <view class="promotion-price">单价:￥{{item.promotionPrice}}</view>
                                    </view>
                                </view>
                                <view class="sub-row-item"
                                      v-if="option.formData.orderChildType === 'LogisticsCodeBased'">
                                    <view class="label">物流码:</view>
                                    <view class="logistic-list" v-if="item.logCodeList && item.logCodeList.length > 0">
                                        <text class="logistic-code"
                                              v-for="(logistic,index) in item.logCodeList.slice(0, (!!item.selectedFlag ? item.logCodeList.length : 5))"
                                              :key="index">
                                              {{logistic.logCode}}&nbsp;{{logistic.logType | lov('LOG_CODE_TYPE')}}&nbsp;{{logistic.logOrderStatus | lov('LOG_ORDER_STATUS')}}
                                        </text>
                                        <view class="view-all" v-if="item.logCodeList.length > 5"
                                              @tap="viewAllLogCode(item,$event)">
                                            {{item.selectedFlag?'折叠显示物流码':'查看完整物流码'}}
                                        </view>
                                    </view>
                                </view>
                                <view class="third-row-item">
                                    <view class="label">
                                        合计金额:
                                    </view>
                                    <view class="info">
                                        {{item.amount | cny}}
                                    </view>
                                </view>
                            </view>
                        </item>
                    </link-swipe-action>
                </view>
            </view>
            <view v-if="!!option.formData.id && option.formData.orderSource !== 'channelMember'">
                <title-line label-name="照片"/>
                <lnk-img-lzlj :delFlag="canEdit && showBtn" :newFlag="canEdit && showBtn"
                              :module-type="option.formData.orderType === 'KaOrder' ? 'mp' : 'bookedOrder' "
                              @call="getImgList"
                              :parent-id="option.formData.id"></lnk-img-lzlj>
            </view>
            <link-fab-button v-if="formDisabled && canEdit && showBtn" @tap="formDisabled=false" icon="icon-edit"/>
        </link-form>
        <link-sticky v-if="!formDisabled && (option.formData.orderType === 'KaOrder' || showBtn)">
            <link-button block
                         @tap="scanTicket"
                         v-if="showTicketFlag && option.formData.row_status === 'NEW'">小票识别</link-button>
            <link-button block @tap="saveOrder">保存</link-button>
        </link-sticky>
        <link-sticky v-if="option.formData.orderType !== 'KaOrder' && formDisabled && showBtn">
            <link-button v-if="option.formData.status === 'Registered' && option.formData.orderSource !== 'channelMember'"
                         block
                         @tap="InvalidOrderItemStatus('Inactive')">作废</link-button>
            <link-button v-if="option.formData.status === 'Rejected' && option.formData.orderSource !== 'channelMember'"
                         block
                         @tap="inactiveRow()">作废</link-button>
            <link-button v-if="option.formData.status === 'New'"
                         block
                         @tap="handleDeleteOrderItem()">删除</link-button>
            <link-button block
                         @tap="handleOrderItemStatus('Registered')"
                         v-if="option.formData.orderChildType === 'LogisticsCodeBased' && (option.formData.status === 'New'||option.formData.status === 'Rejected')">登记</link-button>
            <link-button block
                         @tap="handleSubmit()"
                         v-if="option.formData.orderChildType === 'InvoiceBased' && (option.formData.status === 'New'||option.formData.status === 'Rejected')">提交</link-button>
        </link-sticky>
        <view class="blank" v-if="option.formData.id"></view>
    </link-page>
</template>

<script>
    import {ROW_STATUS} from "../../../utils/constant";
    import ConsumerCommon from "../consumer-common";
    import TitleLine from "../../lzlj/components/title-line";
    import {loginService} from "../../../utils/login";
    import LnkImgLzlj from "../../core/lnk-img-lzlj/lnk-img-lzlj";

    export default {
        name: "booked-order-new-page",
        mixins: [ConsumerCommon()],
        components: {
            TitleLine,
            LnkImgLzlj
        },
        data() {
            let addressInfo = {fullAddr: ''};
            if (!!this.pageParam.data) {
                addressInfo.fullAddr = (this.pageParam.data.province || '') + (this.pageParam.data.city || '') + (this.pageParam.data.district || '') + (this.pageParam.data.street || '') + (this.pageParam.data.customAddr || '');
            }
            if (!this.pageParam.data.fromStore) {
                this.$set(this.pageParam.data, 'fromStore', '');
            }
            const config = {
                ...this.pageParam,
                data: {
                    ...addressInfo,
                    ...this.pageParam.data,
                },
                hooks: {
                    onAfterUpsert: () => {
                        this.$nav.back();
                    }
                }
            };
            const option = new this.FormOption(this, {
                ...config,
                operator: 'NEW'
            });
            const orderDateMax = this.$date.format(new Date());
            return {
                attachmentList: [],
                formDisabled: false,
                tagGroupNotShow: true,
                canEdit: false,
                showTypeFlag: false, // 是否展示用途大类和用途子类型值列表
                fullAddr: '',
                tableNumberData: [],
                isTableNumber: false,
                showTicketFlag: false,
                companyName: '',// 公司名称
                acctType: 'Terminal',
                option,
                orderDateMax,
                bookOrderShipFLag: false, //当前登陆用户公司，是否为会员服务部的公司id
                isGotoPreCreate: 'N',   // 是否跳转审批预览页面
                formRules: {
                    customMobilePhone: this.Validator.phone()
                },
                terminalListOption: new this.AutoList(this, {
                    module: this.$env.appURL + '/action/link/accnt/queryTerminalAndDealerPage',
                    url: {
                        queryByExamplePage: this.$env.appURL + '/action/link/accnt/queryTerminalAndDealerPage',
                    },
                    searchFields: ['acctName'],
                    filterOption: [{
                        label: '单位类型', field: 'acctCategory', type: 'lov', lov: 'ACCNT_CATEGORY'
                    }],
                    param: {
                        rows: 25,
                        companyId: '',
                        acctType: this.acctType,
                        filtersRaw: [
                            {id: 'acctStatus', property: 'acctStatus', value: 'Y', 'operator': '='}
                        ]
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={data.id} title={data.acctName} data={data}></item>
                        )
                    },
                    hooks: {
                        beforeLoad(option) {
                            if (this.$utils.isEmpty(this.acctType)) {
                                return new Promise(async (resolve, reject) => {
                                    reject(false);
                                });
                            }
                            option.param.acctType = this.acctType;
                            option.param.companyId = this.userInfo.coreOrganizationTile['l3Id'] || '';
                            if (this.acctType === 'Terminal' || this.acctType === 'Distributor') {
                                option.param.filtersRaw = [
                                    ...option.param.filtersRaw,
                                    {id: 'dataSource', property: 'dataSource', value: 'WeChatWork', operator: '='},
                                    {id: 'multiAcctMainFlag', property: 'multiAcctMainFlag', value: 'Y', operator: '='}
                                ]
                            }
                        }
                    }
                }),
                activityOption: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: this.$env.appURL + '/marketactivity/link/marketActivity/queryMarketActivityByAcctIdPage'
                    },
                    searchFields: ['activityName'],
                    param: {
                        page: 25,
                        attr1: 'Y',
                        filtersRaw: [
                            {
                                id: 'status',
                                property: 'status',
                                value: '[New, PublishFailed, Publishing, Inactive]',
                                operator: 'NOT IN'
                            },
                        ]
                    },
                    hooks: {
                        beforeLoad(option) {
                            option.param.consumerId = this.option.formData.acctId;
                            option.param.attr2 = this.option.formData.companyId;
                            if (this.$utils.isEmpty(option.param.consumerId) && this.$utils.isEmpty(this.option.formData.acctId)) {
                                option.param.consumerId = this.option.formData.acctId;
                            }
                            if (this.$utils.isEmpty(option.param.attr2) && this.$utils.isEmpty(this.option.formData.companyId)) {
                                option.param.attr2 = this.option.formData.companyId;
                            }
                            if (this.$utils.isEmpty(this.option.formData.acctId)) {
                                const msg = '请先选择客户再选择关联活动';
                                this.$taro.showToast({title: msg});
                                return Promise.reject(msg);
                            }
                        }
                    }
                }),
                addressOption: new this.AutoList(this, {
                    module: this.$env.appURL + '/action/link/acctaddress',
                    param: {
                        rows: 25,
                        filtersRaw: [
                            {id: 'addrType', property: 'addrType', value: '[ShipAddr,ConsumerAddr]', operator: 'IN'},
                        ]
                    },
                    hooks: {
                        //筛选参数，在页面上通过某种操作双向绑定得到的,AutoList的 beforeLoad 钩子函数里面设置新的筛选参数,
                        //.当没有筛选参数的时候， return Promise.reject() 阻止查询
                        beforeLoad(option) {
                            option.param.filtersRaw = [
                                ...option.param.filtersRaw,
                                {id: 'acctId', property: 'acctId', value: this.option.formData.acctId, operator: '='},
                            ];
                        },
                        beforeSelect: async () => {
                            if (this.$utils.isEmpty(this.option.formData.acctId)) {
                                const msg = '请先选择客户再选择地址';
                                this.$taro.showToast({title: msg});
                                return Promise.reject(msg)
                            }
                        }
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item
                                key={index}
                                title={data.province}
                                data={data}
                                arrow={false}
                            >
                                <view slot='note' style="width: 100%;display: flex;">
                                    <view style="width: 85%; display: flex; flex-direction: column;">
                                        <view style="margin-bottom: 5px; width: 90%;">
                                            {data.isDefault === 'Y' && <text style="">[默认地址]</text>}
                                            <text>{data.province}{data.city}{data.district}{data.street}</text>
                                        </view>
                                        <view style="color: gray; width: 90%;">
                                            <text>详细地址:</text>
                                            <text>{data.addr}</text>
                                        </view>
                                    </view>
                                    {(data.postnId === this.userInfo.postnId || this.userInfo.positionType === 'SysAdmin') &&
                                        <view
                                            style="width: 15%;height: 30px;line-height: 30px;display: flex; justify-content: center; background-color: #3e68ef; color: #ffffff; text-align: center; border-radius: 10%;"
                                            onTap={(e) => {
                                                e.stopPropagation();
                                                e.preventDefault();
                                                this.onTapEditAddressButton(e, data);
                                            }}
                                        >
                                            编辑
                                        </view>
                                    }
                                </view>
                            </item>
                        )
                    },
                    slots: {
                        other: (h) => (<link-fab-button onTap={this.onTapCreateAddressButton} icon="mp-plus"/>)
                    }
                }),
                showBtn: false
            }
        },
        async created() {
            await this.getShowBtn();
            this.companyName = await this.$lov.getNameByTypeAndVal('ACTIVITY_COMPANY', this.userInfo.coreOrganizationTile.l3Id);
            // 状态为非Inactive时可以编辑
            this.canEdit =!['Inactive', 'Registered', 'Submitted'].includes(this.option.formData.status) && this.option.formData.orderType !== 'KaOrder';
            if (this.option.formData['row_status'] === 'NEW') {
                await this.queryCfgNew();
            } else {
                this.formDisabled = true;
                await this.queryBookedOrderInfo();
                // 从列表进入则查询订单行及附件列表
                await this.getOrderLineList();
            }
            if (this.pageParam?.data?.acctType) {
                this.acctType = this.pageParam.data.acctType;
            }
            const shipIdList = await this.$utils.getCfgProperty('HIDE_BOOKORDER_SHIP');
            // 会员服务不显示出货单位，出货地址
            this.bookOrderShipFLag = shipIdList.includes(this.userInfo.coreOrganizationTile.l3Id);
        },
        methods: {
            /**
             @param item：当前订单明细数据
             @param event：点击事件
             @desc: 查看所有物流码
             @author: wangbinxin
             @date 2023-10-25 11-21
             **/
            viewAllLogCode(item, event) {
                event.stopPropagation();
                item.selectedFlag = !item.selectedFlag;
            },
            /**
             * @desc 获取图片长度
             * <AUTHOR>
             * @date 2021/7/16 16:49
             **/
            getImgList(data) {
                this.attachmentList = data;
            },
            /**
             *  保存并提交
             *
             *  <AUTHOR>
             *  @date        2020-07-07
             */
            handleSubmit() {
                if (this.$utils.isEmpty(this.option.formData.saleOrderItemList)) {
                    this.$message.primary('请添加订单明细!');
                    return;
                }
                if (this.option.formData['source'] === 'RecognitionTicket') {
                    const data = this.option.formData.saleOrderItemList.filter((item) => this.$utils.isEmpty(item.prodId)
                        || this.$utils.isEmpty(item.prodUnit)
                        || this.$utils.isEmpty(item.qty)
                        || this.$utils.isEmpty(item.promotionPrice)
                    );
                    if (data.length > 0) {
                        this.$message.warn('订单行信息未完善，请检查!');
                        return;
                    }
                }
                // 类型为发票且操作为提交时,校验图片数量
                if (this.option.formData.orderChildType === 'InvoiceBased') {
                    if (this.attachmentList.length === 0) {
                        this.$message.primary('请上传发票附件!');
                        return;
                    }
                }
                const _this = this;
                this.$taro.showModal({
                    title: '提示',
                    content: '是否要提交订单信息?',
                    success: async (res) => {
                        if (res.confirm) {
                            const needApproval = await this.needApproval();
                            if(!needApproval) {
                                // 不走审批的话，走原本的逻辑
                                const data = await this.$http.post(_this.$env.appURL + '/action/link/srorder/submitFlow', {id: _this.option.formData.id}, {
                                    autoHandleError: false,
                                    handleFailed: (data) => {
                                        if (!data.success) {
                                            this.$showError('订单提交失败！' + data.result);
                                        }
                                    }
                                });
                                if (!data.success) {
                                    await this.$message.primary(data.result)
                                }
                                this.$nav.back();
                            } else {
                                this.isGotoPreCreate = await this.gotoPreCreateFlow('InvoiceBased');
                                if(this.isGotoPreCreate === 'Y') {
                                //  250318如果是Y则不拉起预览页面，直接调智零提交接口，并且调智零提交接口的时候把这个字段传上去
                                    await this.approvalSubmit();
                                } else {
                                    //  250318如果是N则拉起预览页面
                                    this.$nav.push('/pages/lzlj/approval-v3/approval-flow-page.vue', {
                                        submitData: _this.option.formData,
                                        submitUser: _this.userInfo,
                                        flowObjId: _this.option.formData.id,
                                        // 审批类型编码
                                        approvalType: 'InvoiceBased'        // 发票类型
                                    });
                                }
                            }
                        } else if (res.cancel) {
                        }
                    }
                });
            },
            /**
             * @desc 判断是否需要唤起预览页
             * <AUTHOR>
             * @date 2025-03-18
             **/
            async gotoPreCreateFlow(approvalType) {
                const {success, result} = await this.$http.post('action/link/flow/v3/preCreateFlow', {
                    // 审批对象ID
                    objectId: this.option.formData.id,
                    // 审批类型编码
                    approvalType: approvalType,
                    // 业务对象JSON字符串
                    flowObjDetail: JSON.stringify(this.option.formData),
                    // flowObjId传订单id
                    flowObjId: this.option.formData.id,
                    // 提交用户id
                    submitUserId: this.userInfo.id,
                    // 提交用户职位id
                    submitPostnId: this.userInfo.postnId,
                    // 提交用户组织id
                    submitOrgId: this.userInfo.orgId
                });
                if(success) {
                    return result.jumpApproval;
                } else {
                    this.$showError('预生成审批记录失败！' + result);
                }
            },
            /**
             * @desc 判断是否是v3需要审批
             * <AUTHOR>
             * @date 2025-03-06
             **/
            async needApproval() {
                return new Promise(async (res, rej) => {
                    const {success, result} = await this.$http.post(this.$env.dmpURL + '/action/link/fieldTemApp/qwAppVersion', {
                        companyId: this.userInfo.coreOrganizationTile['l3Id'] || '',
                        applyType: this.option.formData.orderChildType
                    });
                    console.log('result', result)
                    if (success && result === 'v3') {
                        res(true);
                    } else {
                        res(false);
                    }
                });
            },
            /**
             * @desc 返回当前页回调
             * <AUTHOR>
             * @date 2025-03-06
             **/
            async onBack(param) {
                console.log('param', param);
                if (param && param.flag === 'flow' && this.$utils.isNotEmpty(param.previewResult)) {
                    // 发票-提交
                    if (param.approvalType === 'InvoiceBased') {
                        await this.approvalSubmit(param.previewResult, param.preApprovalId, param.nodeDtos);
                    }
                    // 物流码-登记
                    if (param.approvalType === 'LogisticsCodeBased') {
                        await this.confirmRegister(param.previewResult, param.preApprovalId, param.nodeDtos);
                    }
                }
            },
            /**
             * @desc 审批回调后执行提交接口
             * <AUTHOR>
             * @date 2025-03-06
             **/
            async approvalSubmit(previewResult, preApprovalId, nodeDtos) {
                const data = await this.$http.post(this.$env.appURL + '/action/link/saleorder/createQwFlowSubmit', {
                    id: this.option.formData.id,    //订单id
                    approvalRecordId: preApprovalId, //预生成审批记录id
                    nodeApprovers: nodeDtos,  //审批节点信息
                    jumpApproval: this.isGotoPreCreate //是否跳转预览页面
                });
                if (data.success) {
                    setTimeout(() => {
                        this.$message.success('订单提交成功！');
                    }, 1000);
                } else {
                    this.$showError('订单提交失败！' + data.result);
                }
                this.$nav.back();
            },
            /**
             * 作废订单头
             * <AUTHOR>
             * @date 2020-07-07
             */
            async InvalidOrderItemStatus(type) {
                const _this = this;
                const originalStatus = _this.option.formData.status;
                const operation = '作废';
                const url = '/action/link/saleorder/inactiveBookOrder';
                // 物流码校验必须要有
                _this.option.formData.status = type;
                _this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + url, _this.option.formData, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError(`订单${operation}失败` + (response.result || response.message));
                        _this.option.formData.status = originalStatus;
                    }
                });
                if (data.success) {
                    _this.$utils.hideLoading();
                    this.$nav.back();
                }
            },
            /**
             * 登记订单头
             * <AUTHOR>
             * @date 2020-07-07
             */
            async handleOrderItemStatus(type) {
                // 类型为发票且操作为登记时,校验图片数量
                if (this.option.formData.orderChildType === 'InvoiceBased' && type === 'Registered') {
                    if (this.attachmentList.length === 0) {
                        this.$message.primary('请上传发票附件!');
                        return;
                    }
                }
                if (this.$utils.isEmpty(this.option.formData.saleOrderItemList) || this.option.formData.saleOrderItemList.length <= 0) {
                    this.$message.primary('请添加订单明细!');
                    return;
                }
                // 物流码校验必须要有
                if (this.option.formData.orderChildType === 'LogisticsCodeBased' && type === 'Registered' && !this.$utils.isEmpty(this.option.formData.saleOrderItemList)) {
                    for (let i = 0; i < this.option.formData.saleOrderItemList.length; i++) {
                        if (this.$utils.isEmpty(this.option.formData.saleOrderItemList[i].logCodeList)) {
                            await this.$message.primary('该订单【' + this.option.formData.saleOrderItemList[i].prodCode + '】【' + this.option.formData.saleOrderItemList[i].prodName + '】暂无物流码！');
                            return;
                        } else {
                            const qty = this.option.formData.saleOrderItemList[i]['qty'];
                            const len = this.option.formData.saleOrderItemList[i].logCodeList.length;
                            if (qty !== len) {
                                await this.$message.primary(`【${this.option.formData.saleOrderItemList[i].prodCode}】【${this.option.formData.saleOrderItemList[i].prodName}】下单数量为${qty}，物流码数量为${len},双方数量不一致，请检查核对！`);
                                return;
                            }
                            for (let j = 0; j < len; j++) {
                                if (this.$utils.isEmpty(this.option.formData.saleOrderItemList[i].logCodeList[j].logCode)) {
                                    await this.$message.primary('物流码列表中存在为空的记录,请填写后再保存订单行')
                                    return;
                                }
                            }
                        }
                    }
                }
                const needApproval = await this.needApproval();
                if(!needApproval) {
                    // 不走审批的话，走原本的逻辑
                    this.confirmRegister();
                } else {
                    this.isGotoPreCreate = await this.gotoPreCreateFlow('LogisticsCodeBased');
                    if(this.isGotoPreCreate === 'Y') {
                        //  250318如果是Y则不拉起预览页面，直接调智零登记接口，并且调智零提交接口的时候把这个字段传上去
                        await this.confirmRegister();
                    } else {
                        //  250318如果是N则拉起预览页面
                        this.$nav.push('/pages/lzlj/approval-v3/approval-flow-page.vue', {
                            submitData: this.option.formData,
                            submitUser: this.userInfo,
                            flowObjId: this.option.formData.id,
                            // 审批类型编码
                            approvalType: 'LogisticsCodeBased'          // 物流码
                        });
                    }
                }
            },
            /**
             * 登记按钮逻辑
             * <AUTHOR>
             * @date 2024-03-29
             */
            async confirmRegister(previewResult, preApprovalId, nodeDtos) {
                const _this = this;
                const originalStatus = _this.option.formData.status;
                const operation = '登记';
                const url = '/action/link/saleorder/registeredBookOrder';
                _this.option.formData.status = 'Registered';
                const requestData = {
                    ..._this.option.formData,
                    jumpApproval: this.isGotoPreCreate
                };
                if (this.$utils.isNotEmpty(preApprovalId) && this.$utils.isNotEmpty(nodeDtos)) {
                    requestData.approvalRecordId = preApprovalId;
                    requestData.nodeApprovers = nodeDtos;
                }
                _this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + url, requestData, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError(`订单${operation}失败` + (response.result || response.message));
                        _this.option.formData.status = originalStatus;
                    }
                });
                if (data.success) {
                    _this.$utils.hideLoading();
                    setTimeout(() => {
                        this.$message.success('登记动销订单成功！');
                    }, 1000);
                    this.$nav.back();
                }
            },
            /**
             * 作废订单头
             * <AUTHOR>
             * @date 2024-03-29
             */
            async inactiveRow() {
                const url = '/action/link/saleorder/rejectedOrderToInactive';
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + url, {id: this.option.formData.id}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('订单作废失败:' + (response.result || response.message));
                    }
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    this.$nav.back();
                }
            },
            /**
             * 新建订单行
             * <AUTHOR>
             * @date 2020-6-24
             */
            async addNewOrderLine() {
                let flag = ''
                if (this.option.formData['source'] === 'RecognitionTicket') {
                    flag = 'Add'
                }
                const orderLineItem = {
                    headId: this.option.formData.id,
                    row_status: ROW_STATUS.NEW,
                    logCodeList: []
                };
                //this.$set(this.option.formData, 'saleOrderItemList', [{prodName: '测试数据'}]);
                //console.log(this.option.formData.saleOrderItemList);
                this.$nav.push('/pages/lj-consumers/booked-order/booked-order-line-new-page', {
                    orderItem: this.option.formData,
                    flag: flag,
                    orderLineItem: orderLineItem,
                    saveFlag: !this.option.formData.id,
                    type: this.option.formData.orderChildType,
                    callback: async (orderLine, orderData) => {
                        this.option.formData = orderData;
                        this.orderLineData(orderLine);
                    }
                });
            },
            /**
             @param 订单明细数据
             @desc: 处理订单明细数据
             @author: wangbinxin
             @date 2023-10-17 15-26
             **/
            orderLineData(orderLine, index) {
                let arr = this.$utils.deepcopy(this.option.formData.saleOrderItemList) || [];
                if (typeof index === "number") {
                    arr.splice(index, 1, orderLine);
                } else {
                    arr = [...arr, orderLine];
                }
                this.$set(this.option.formData, 'saleOrderItemList', arr);
                const len = this.option.formData.saleOrderItemList.length;
                // 计算汇总金额
                this.option.formData.orderAmount = 0;
                for (let i = 0; i < len; i++) {
                    if (this.option.formData.saleOrderItemList[i].promotionPrice >= 0 && this.option.formData.saleOrderItemList[i].qty >= 0) {
                        this.option.formData.saleOrderItemList[i]['amount'] = this.option.formData.saleOrderItemList[i].promotionPrice * this.option.formData.saleOrderItemList[i].qty;
                        this.option.formData.orderAmount += this.option.formData.saleOrderItemList[i]['amount'];
                    }
                }
            },
            /**
             * 删除订单头
             * <AUTHOR>
             * @date 2020-07-07
             */
            async handleDeleteOrderItem() {
                const _this = this;
                this.$taro.showModal({
                    title: '提示',
                    content: '是否要删除订单信息',
                    success: async (res) => {
                        if (res.confirm) {
                            const data = await this.$http.post(this.$env.appURL + '/action/link/saleorder/deleteById', _this.option.formData, {
                                autoHandleError: false,
                                handleFailed: (data) => {
                                    if (!data.success) {
                                        this.$showError('订单删除失败！' + data.result);
                                    }
                                }
                            });
                            this.$nav.back();
                        } else if (res.cancel) {
                        }
                    }
                });
            },
            /**
             * 删除订单行
             * <AUTHOR>
             * @date 2020-07-07
             * @param item 订单行对象
             * @param index 在数组中的索引位置
             */
            async handleDeleteOrderItemLine(item, index) {
                this.$utils.showLoading();
                if (this.$utils.isEmpty(item.id)) {
                    this.option.formData.saleOrderItemList.splice(index, 1);
                    const len = this.option.formData.saleOrderItemList.length;
                    // 计算汇总金额
                    this.option.formData.orderAmount = 0;
                    for (let i = 0; i < len; i++) {
                        if (this.option.formData.saleOrderItemList[i].promotionPrice >= 0 && this.option.formData.saleOrderItemList[i].qty >= 0) {
                            this.option.formData.saleOrderItemList[i]['amount'] = this.option.formData.saleOrderItemList[i].promotionPrice * this.option.formData.saleOrderItemList[i].qty;
                            this.option.formData.orderAmount += this.option.formData.saleOrderItemList[i]['amount'];
                        }
                    }
                } else {
                    const data = await this.$http.post(this.$env.appURL + '/action/link/saleorderitem/deleteById', item, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$utils.hideLoading();
                            this.$showError('订单行数剧删除更新失败！' + response.result);
                        }
                    });
                    if (data.success) {
                        this.$utils.hideLoading();
                        this.option.formData.saleOrderItemList.splice(index, 1);
                        const len = this.option.formData.saleOrderItemList.length;
                        // 计算汇总金额
                        this.option.formData.orderAmount = 0;
                        for (let i = 0; i < len; i++) {
                            this.option.formData.orderAmount += this.option.formData.saleOrderItemList[i].amount;
                        }
                    }
                }
                if (this.option.formData['source'] === 'RecognitionTicket') {
                    const updateData = {
                        id: this.option.formData.id,
                        orderItemJson: JSON.stringify(this.option.formData.saleOrderItemList),
                        updateFields: "orderItemJson"
                    };
                    const orderItem = await this.$http.post(this.$env.appURL + '/action/link/saleorder/update', updateData, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$utils.hideLoading();
                            this.$showError('订单数据更新失败！' + response.result);
                        }
                    });
                    this.option.formData = orderItem.newRow;
                    await this.getOrderLineList();
                    this.$utils.hideLoading();
                }
                this.$message.success('订单行已删除!');
                this.$utils.hideLoading();
            },
            /**
             * 进入订单行详情
             * <AUTHOR>
             * @date 2020-06-24
             */
            gotoOrderItemLine(item, index) {
                const _this = this;
                item.row_status = ROW_STATUS.UPDATE;
                if (this.$utils.isEmpty(item.id)) {
                    item.row_status = ROW_STATUS.NEW;
                    item['headId'] = this.option.formData.id;
                }
                if (!item.logCodeList) item.logCodeList = [];
                item.logCodeList.forEach((logCode) => {
                    logCode.row_status = ROW_STATUS.UPDATE;
                });
                this.$nav.push('/pages/lj-consumers/booked-order/booked-order-line-new-page', {
                    orderItem: this.option.formData,
                    index: index,
                    saveFlag: !this.option.formData.id,
                    orderLineItem: item,
                    type: this.option.formData.orderChildType,
                    title: '动销明细',
                    callback: (orderLine, orderData) => {
                        this.option.formData = orderData;
                        this.orderLineData(orderLine, index);
                    }
                });
            },
            /**
             * 获取订单行列表
             * <AUTHOR>
             * @date 2020-6-24
             */
            async getOrderLineList() {
                if (this.option.formData['source'] === 'RecognitionTicket') {
                    const orderItemData = !this.$utils.isEmpty(this.option.formData.orderItemJson) ? JSON.parse(this.option.formData.orderItemJson) : [];
                    this.$set(this.option.formData, 'saleOrderItemList', orderItemData);
                    const len = this.option.formData.saleOrderItemList.length;
                    // 计算汇总金额
                    this.option.formData.orderAmount = 0;
                    for (let i = 0; i < len; i++) {
                        if (this.option.formData.saleOrderItemList[i].promotionPrice >= 0 && this.option.formData.saleOrderItemList[i].qty >= 0) {
                            this.option.formData.saleOrderItemList[i]['amount'] = this.option.formData.saleOrderItemList[i].promotionPrice * this.option.formData.saleOrderItemList[i].qty;
                            this.option.formData.orderAmount += this.option.formData.saleOrderItemList[i]['amount'];
                        }
                    }
                } else {
                    const _this = this;
                    const data = await this.$http.post(this.$env.appURL + '/action/link/saleorderitem/queryByExamplePage',
                        {
                            sort: 'created',
                            order: 'desc',
                            pageFlag: true,
                            rows: 500,
                            page: 1,
                            onlyCountFlag: false,
                            oauth: 'All',
                            filtersRaw: [{'id': 'headId', 'property': 'headId', 'value': _this.option.formData.id}]
                        }, {
                            autoHandleError: false,
                            handleFailed: (data) => {
                                if (!data.success) {
                                    this.$showError('查询订单行信息失败！' + data.result);
                                }
                            }
                        });
                    if (data.success) {
                        this.$set(this.option.formData, 'saleOrderItemList', data.rows);
                        const len = _this.option.formData.saleOrderItemList.length;
                        // 计算汇总金额
                        _this.option.formData.orderAmount = 0;
                        for (let i = 0; i < len; i++) {
                            _this.option.formData.orderAmount += _this.option.formData.saleOrderItemList[i].amount;
                        }
                    }
                }
            },
            /**
             * 新增消费者
             * <AUTHOR>
             * @date 2020-08-07
             * */
            async addConsumer() {
                this.newAccountItem.id = await this.$newId();
                this.$nav.push(this.editPath, {
                    data: this.newAccountItem,
                    marketActivityId: this.parentId,
                    userInfo: this.userInfo,
                    pageFrom: 'BookedOrder',
                    callback: (data) => {
                        this.accountOption.methods.reload();
                        this.$set(this.option.formData, 'acctId', data.id);
                        this.$set(this.option.formData, 'acctName', data.name);
                        this.$set(this.option.formData, 'customMobilePhone', data.phoneNumber);
                        this.$set(this.option.formData, 'customCompany', data.companyName);
                        this.$set(this.option.formData, 'companyId', data.belongToCompanyId);
                        this.$set(this.option.formData, 'customConsignee', data.name);
                        this.$set(this.option.formData, 'contact', data.name);
                        this.$set(this.option.formData, 'contactTel', data.phoneNumber);
                    },
                })
            },
            /**
             * @desc 切换用途子类型
             * <AUTHOR>
             * @date 2022/11/21 14:11
             **/
            changeVal(val) {
                this.isTableNumber = this.tableNumberData.indexOf(val) !== -1
                if (val) {
                    this.option.formData.serviceType = '';
                    this.option.formData.tableNumber = '';
                }
            },
            async queryBookedOrderInfo() {
                const data = await this.$http.post(this.$env.appURL + '/link/saleorder/queryById', {id: this.option.formData.id}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$showError(`查询订单失败：${response.result}`);
                    }
                });
                if (data.success) {
                    this.option.formData = data.result;
                    this.option.formData['row_status'] = 'UPDATE';
                    this.isTableNumber = this.tableNumberData.indexOf(this.option.formData['serviceCategory']) !== -1
                }
            },
            /**
             * @desc 查询系统参数配置
             * <AUTHOR>
             * @date 2022/7/11 10:32
             **/
            async queryCfgNew() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfgList', [{key: 'RECEIPT_IDENTIFICATION'}, {key: 'TABLE_NUMBER'}, {key: 'USE_TYPE_USE_CATEGORY'}]);
                if (data.success) {
                    data.rows.forEach((item) => {
                        if (item.key === 'RECEIPT_IDENTIFICATION') {
                            const companyIds = item.value.split(',');
                            this.showTicketFlag = companyIds.indexOf(this.userInfo.coreOrganizationTile['l3Id']) !== -1
                        }
                        if (item.key === 'TABLE_NUMBER') {
                            this.tableNumberData = item.value.split(',');
                        }
                        if (item.key === 'USE_TYPE_USE_CATEGORY') {
                            const companyIds = item.value.split(',');
                            this.showTypeFlag = companyIds.indexOf(this.userInfo.coreOrganizationTile['l3Id']) !== -1
                        }
                    })
                }
            },
            //上传图片的函数
            async upLoadImage(tempfilepath) {
                var that = this;
                const authorization = await loginService.getToken();
                wx.uploadFile({ //调用微信里面上传文件的开放接口
                    url: this.$env.appURL + '/action/link/baidu/identifyReceiptByFile',//上传到服务器的接口
                    filePath: tempfilepath,//文件的路径，只能有一个，不支持数组等一次性包含多个文件路径的形式
                    header: {
                        "Content-Type": "multipart/form-data;charset=utf-8",
                        "Authorization": `bearer ${authorization}`,
                        "Accept": " */*",
                        "Accept-Encoding": "gzip, deflate, br"
                    },
                    name: 'receiptFile', //接口文档中给该文件的字段名
                    success: (response) => {
                        const data = JSON.parse(decodeURIComponent(response.data));
                        that.option.formData = Object.assign({}, that.option.formData, data.result);
                        that.option.formData['source'] = 'RecognitionTicket';  // 小票识别
                        that.$utils.hideLoading();
                    },
                    fail: function (error) {
                        that.$utils.hideLoading();
                        that.$message.error("图片识别失败:" + error);
                    }
                })
            },
            /**
             * @desc 小票识别
             * <AUTHOR>
             * @date 2022/7/4 16:02
             **/
            async scanTicket() {
                const resp = await this.$image.chooseImage({
                    count: 1,
                    sourceType: ['album', 'camera']
                })
                let path = resp.tempFilePaths[0]
                // 用户上传图片大于7M进行压缩
                if (resp.tempFiles && (resp.tempFiles[0].size > 7 * 1024 * 1024)) {
                    const {source, compress} = await this.$image.compress({
                        filePath: resp.tempFilePaths[0],
                        limitHeight: 2500
                    })
                    path = compress.path;
                }
                this.$utils.showLoading('小票识别中…');
                await this.upLoadImage(path);
                // const base64 = await wx.getFileSystemManager().readFileSync(path, 'base64');
                // console.log(path, '测试')
                // if (path && base64) {
                //     this.$utils.showLoading('小票识别中…');
                //     console.log(base64)
                //     const data = await this.$httpForm.post(this.$env.appURL + '/action/link/baidu/identifyReceiptByBase64Data', {imageData: base64}, {
                //         autoHandleError: false,
                //         handleFailed: (response) => {
                //             this.$utils.hideLoading();
                //             this.$showError("图片识别失败:"+response.result);
                //         }
                //     });
                //     if (data.success) {
                //         this.option.formData = Object.assign({}, this.option.formData, data.result);
                //         this.option.formData['source'] ='RecognitionTicket';  // 小票识别
                //         this.$utils.hideLoading();
                //     }
                // }
            },
            changeAcctType() {
                this.option.formData.fromStore = '';
                this.option.formData.orderSuperId = '';
            },
            /**
             * 点击地址选择页面的新建按钮，跳转到地址新建页面，新建地址数据
             * <AUTHOR>
             * @date    2020/7/20 16:18
             */
            async onTapCreateAddressButton() {
                // 跳转到地址新建界面，然后地址新建界面保存完毕之后，
                // 将新的地址数据unshift 到地址AutoList对象的list数组中，
                // 再返回上一个页面（地址选择列表页面）
                const id = await this.$newId();
                const isDefault = (this.addressOption.list || []).length === 0 ? 'Y' : 'N';
                const addressItem = {
                    id,
                    row_status: ROW_STATUS.NEW,
                    acctId: this.option.formData.acctId,
                    addrType: 'ShipAddr',
                    postnId: this.userInfo.postnId,
                    isDefault
                };
                this.$nav.push('/pages/lj-consumers/account/account-item-address-edit-page', {
                    isDefault,
                    item: addressItem,
                    callback: (data) => {
                        this.addressOption.list.unshift(data);
                    }
                });
            },
            /**
             * @desc 编辑地址按钮
             * <AUTHOR>
             * @date 2024-04-10
             *
             * */
            async onTapEditAddressButton(e, data) {
                console.log('onTapEditAddressButton data', data)
                data.row_status = ROW_STATUS.UPDATE;
                data.postnId = this.userInfo.postnId;
                this.$nav.push('/pages/lj-consumers/account/account-item-address-edit-page', {
                    item: data,
                    callback: () => {
                        this.addressOption.methods.reload();
                    }
                });
            },
            /**
             * 选择客户之后的处理动作，清空地址数据，需要重新选择地址数据
             * <AUTHOR>
             * @date    2020/7/20 16:09
             */
            onAfterSelectAccount(data) {
                this.option.formData = {
                    ...this.option.formData,
                    province: null,
                    city: null,
                    district: null,
                    street: null,
                    customAddr: null,
                    fullAddr: ''
                }
                if (data.province) {
                    this.option.formData.fullAddr = data.province + data.city + data.county + data.street;
                    this.option.formData.province = data.province;
                    this.option.formData.city = data.city;
                    this.option.formData.district = data.county;
                    this.option.formData.street = data.street ? data.street : '';
                }
                if (data.address) {
                    this.option.formData.fullAddr = this.option.formData.fullAddr + data.address;
                    this.option.formData.customAddr = data.address;
                }
            },
            /**
             * 选择地址之前
             * <AUTHOR>
             * @date    2020/7/20 15:49
             */
            onBeforeSelectAddress() {
                if (!this.option.formData.acctId) {
                    const msg = '请先选择客户！';
                    this.$taro.showModal({content: msg, showCancel: false})
                    return Promise.reject(msg)
                }
            },
            /**
             * 选择地址之后
             * <AUTHOR>
             * @date    2020/7/20 15:49
             */
            onAfterSelectAddress(selected) {
                const {province, city, district, street, addr} = selected;
                // 如果street不存在，则设置为空字符串
                const normalizedStreet = street || ''; // 如果 street 不存在，则设置为空字符串

                this.option.formData = {
                    ...this.option.formData,
                    province, city, district,
                    street: normalizedStreet,
                    customAddr: addr,
                    fullAddr: province + city + district + normalizedStreet + addr
                }
            },
            /**
             * 校验必输字段
             * <AUTHOR>
             * @date 2019-06-27
             */
            checkData() {
                if (this.isTableNumber && this.option.formData.tableNumber < 1) {
                    this.$message.warn('用酒桌数不能小于1!');
                    return false;
                }
                // 校验订购时间与当前时间
                const orderDate = new Date(this.option.formData.orderDate.replace(/-/g, '/'));
                const today = new Date(new Date().setHours(0, 0, 0));
                if (orderDate > today) {
                    this.$message.primary('订购日期不能大于当前日期!');
                    return false;
                }
                return true;
            },
            /**
             * 保存订单
             * <AUTHOR>
             * @date 2019-06-27
             */
            async saveOrder() {
                await this.$refs.bookedOrderForm.validate();
                if (!this.checkData()) {
                    return;
                }
                if (this.$utils.isEmpty(this.option.formData.tableNumber)) {
                    delete this.option.formData.tableNumber;
                }
                this.fullAddr = this.option.formData.fullAddr;
                delete this.option.formData.fullAddr;
                const itemList = this.$utils.deepcopy(this.option.formData.saleOrderItemList);
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + '/action/link/saleorder/upsert', this.option.formData, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError("订单保存失败:" + response.result);
                        this.option.formData.fullAddr = this.fullAddr;
                    }
                });
                if (data.success) {
                    this.option.formData = data.newRow;
                    this.option.formData['row_status'] = 'UPDATE';
                    this.$utils.hideLoading();
                    await this.saveOrderItem(data.newRow, itemList);
                    this.$message.success('订单信息保存成功!');
                }
                this.formDisabled = true;
            },
            /**
             @param 当前数据
             @desc:
             @author: wangbinxin
             @date 2023-10-17 17-45
             **/
            async saveOrderItem(row, itemList = this.option.formData.saleOrderItemList || []) {
                if (itemList.length < 1) return;
                const insertList = itemList.filter((item) => !item.id);
                if (insertList.length > 0) {
                    itemList.forEach((item) => item.headId = row.id);
                    await this.$http.post(this.$env.appURL + '/action/link/saleorderitem/batchUpsert', insertList, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$utils.hideLoading();
                            this.$showError('订单行数据保存失败！' + response.result);
                        }
                    });
                    await this.getOrderLineList();
                } else {
                    this.$set(this.option.formData, 'saleOrderItemList', itemList);
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/05/30
             * @methods: getShowBtn
             * @para:
             * @description: 是否展示操作按钮
             **/
            async getShowBtn () {
                const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', {key: 'BOOK_ORDER_SHOW'});
                if (data.success) {
                    if (this.option.formData.orderType === 'KaOrder') {
                        this.showBtn = true;
                    } else {
                        this.showBtn = data.value.split(',').includes(this.userInfo.coreOrganizationTile.l3Id);
                    }
                } else {
                    this.$message.error('查询是否展示按钮失败：'+ data.result);
                }
            }
        }
    }
</script>

<style lang="scss">
    .booked-order-new-page {
        .order-line-list-wrapper {
            margin-top: 24px;

            .order-line-list {
                .order-line {
                    background: white !important;

                    &:not(:last-of-type) {
                        margin-bottom: 20px;
                    }
                }

                .order-line-item {
                    .row-item {
                        padding: 24px;
                        border-bottom: 2px solid #F2F2F2;

                        .row-1 {
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            margin-bottom: 20px;

                            .prod-name {
                                font-size: 32px;
                                color: #41484D;
                                width: 80%;
                            }

                            .prod-unit {
                                color: #9CA5A8;
                                width: 20%;
                                text-align: right;
                            }
                        }

                        .row-2 {
                            display: flex;
                            align-items: center;
                            justify-content: flex-end;

                            .prod-code {
                                color: #41484D;
                            }

                            .promotion-price {
                                color: #41484D;
                                font-weight: 500;
                            }
                        }
                    }

                    .sub-row-item {
                        border-bottom: 2px solid #F2F2F2;
                        padding: 28px 42px;
                        display: flex;
                        justify-content: space-between;

                        .label {
                            white-space: nowrap;
                            flex: 2;
                        }

                        .logistic-list {
                            flex: 7;

                            .logistic-code, .view-all {
                                color: #9CA5A8;
                                display: inline-block;
                                width: 480px;
                                white-space: nowrap;
                                overflow-x: auto; /* 当内容超出宽度时显示滚动条 */
                                line-height: 50px;
                            }

                            .view-all {
                                color: #0076FF;
                            }
                        }
                    }

                    .third-row-item {
                        padding: 28px 42px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        .label {
                            color: #41484D;
                        }

                        .info {
                            font-size: 36px;
                            color: #41484D;
                        }
                    }
                }

                .notification-add-order-line {
                    display: -webkit-box;
                    display: -ms-flexbox;
                    display: flex;
                    -webkit-box-pack: center;
                    -ms-flex-pack: center;
                    justify-content: center;
                    -webkit-box-align: center;
                    -ms-flex-align: center;
                    align-items: center;
                    background: white;
                    height: 15vh;
                    color: gray;
                }
            }
        }

        .blank {
            height: 376px;
            width: 100%;
        }
    }
</style>
