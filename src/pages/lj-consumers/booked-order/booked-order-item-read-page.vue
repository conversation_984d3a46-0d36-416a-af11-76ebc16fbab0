<!--
消费者详情-生命旅程tab-跳转到动销详情
<AUTHOR>
@date 2024-06-24
@file booked-order-item-read-page.vue
-->
<template>
    <link-page class="booked-order-item-page">
        <link-form :value="orderItem" hideSaveButton hideEditButton>
            <view class="account-info-wrapper">
                <view class="row-item">
                    <text class="account-name">{{orderItem.acctName || orderItem.customConsignee}}</text>
                    <text class="position">{{orderItem.customPosition}}</text>
                    <text class="status"
                          :class="{'status-new':orderItem.status === 'New','status-registered':orderItem.status === 'Registered','status-inactive':orderItem.status === 'Inactive'}">
                        {{orderItem.status | lov('ORDER_STATUS')}}
                    </text>
                </view>
                <view class="sub-row-item">
                    <text class="company">{{orderItem.customCompany || orderItem.companyName}}</text>
                    <text class="mobile-phone">{{orderItem.customMobilePhone}}</text>
                </view>
            </view>
            <view class="order-info-wrapper">
                <view class="row-item" v-if="orderItem.orderType !== 'KaOrder'">
                    <text class="label">订单类型:</text>
                    <text class="info">{{orderItem.orderChildType | lov('ORDER_SUB_TYPE')}}</text>
                </view>
                <view class="row-item" v-if="showTypeFlag && orderItem.orderType !== 'KaOrder'">
                    <text class="label">用途大类:</text>
                    <text class="info">{{orderItem.serviceCategory | lov('USE_TYPE')}}</text>
                </view>
                <view class="row-item" v-if="showTypeFlag && orderItem.orderType !== 'KaOrder'">
                    <text class="label">用途子类型:</text>
                    <text class="info">{{orderItem.serviceType | lov('USE_CATEGORY')}}</text>
                </view>
                <view class="row-item" v-if="(companyName === '头曲公司' || companyName === '大成浓香' || companyName === '特曲公司' || companyName === '窖龄公司') && orderItem.orderType !== 'KaOrder'">
                    <text class="label">用酒桌数:</text>
                    <text class="info">{{orderItem.tableNumber}}</text>
                </view>
                <view class="row-item">
                    <text class="label">订单总金额:</text>
                    <text class="info">{{orderItem.orderAmount}}</text>
                </view>
                <view class="row-item">
                    <text class="label">订购时间:</text>
                    <text class="info">{{orderItem.orderDate | date('YYYY-MM-DD')}}</text>
                </view>
                <view class="row-item" v-if="orderItem.orderType !== 'KaOrder' && !bookOrderShipFLag">
                    <text class="label">出货单位:</text>
                    <text class="info">{{orderItem.fromStore}}</text>
                </view>
                <view class="row-item" v-if="orderItem.orderType === 'KaOrder'">
                    <text class="label">系统名称:</text>
                    <text class="info">{{orderItem.superSystem}}</text>
                </view>
                <view class="row-item" v-if="orderItem.orderType === 'KaOrder'">
                    <text class="label">门店:</text>
                    <text class="info">{{orderItem.orderSuper}}</text>
                </view>
                <view class="row-item" v-if="orderItem.orderType === 'KaOrder'">
                    <text class="label">门店地址:</text>
                    <text class="info">{{orderItem.superProvince}}{{orderItem.superCity}}{{orderItem.orderSuperAddr}}</text>
                </view>
                <view class="row-item" v-if="orderItem.orderChildType === 'InvoiceBased'">
                    <text class="label">发票编号:</text>
                    <text class="info">{{orderItem.invoicesNo}}</text>
                </view>
                <view class="row-item address-item" v-if="orderItem.orderType !== 'KaOrder'">
                    <text class="label">收货地址:</text>
                    <text class="info">{{orderItem.province}}{{orderItem.city}}{{orderItem.district}}{{orderItem.street}}&nbsp;{{orderItem.customAddr}}</text>
                </view>
                <view class="row-item">
                    <text class="label">下单业务员:</text>
                    <text class="info">{{orderItem.salesmenName}}{{orderItem.salesmenPhone}}</text>
                </view>
            </view>
            <view class="order-line-list-wrapper">
                <title-line label-name="订单明细"/>
                <view class="order-line-list">
                    <view class="notification-add-order-line"
                          v-if="$utils.isEmpty(orderItem.saleOrderItemList)">
                        还没有订单明细，请添加
                    </view>
                    <link-swipe-action v-for="(item,index) in orderItem.saleOrderItemList" :key="index" class="order-line">
<!--                        <item :arrow="false" @tap="canEdit&&!approvalFlag && gotoOrderItemLine(item, index)">-->
                        <item :arrow="false">
                            <view class="order-line-item" slot="note">
                                <view class="row-item">
                                    <view class="row-1">
                                        <view class="prod-name">{{item.prodName}}</view>
                                        <view class="prod-unit">{{item.qty}}{{item.prodUnit |
                                            lov('PROD_UNIT')}}
                                        </view>
                                    </view>
                                    <view class="row-2">
                                        <view class="promotion-price">单价:￥{{item.promotionPrice}}</view>
                                    </view>
                                </view>
                                <view class="sub-row-item" v-if="orderItem.orderChildType === 'LogisticsCodeBased'">
                                    <view class="label">物流码:</view>
                                    <view class="logistic-list" v-if="item.logCodeList && item.logCodeList.length > 0">
                                        <text class="logistic-code"
                                              v-for="(logistic,index) in item.logCodeList.slice(0, (!!item.selectedFlag ? item.logCodeList.length : 5))"
                                              :key="index">
                                            {{logistic.logCode}}&nbsp;{{logistic.logType | lov('LOG_CODE_TYPE')}}&nbsp;{{logistic.logOrderStatus | lov('LOG_ORDER_STATUS')}}
                                        </text>
                                        <view class="view-all" v-if="item.logCodeList.length > 5 "
                                              @tap="viewAllLogCode(item,$event)">
                                            查看完整物流码
                                        </view>
                                    </view>
                                </view>
                                <view class="third-row-item">
                                    <view class="label">
                                        合计金额:
                                    </view>
                                    <view class="info">
                                        {{item.amount | cny}}
                                    </view>
                                </view>
                            </view>
                        </item>
                    </link-swipe-action>
                </view>
            </view>
            <view class="order-line-list-wrapper">
                <link-form-item label="关联活动" :arrow="false">
                    <link-object :option="activityOption"
                                 :row="orderItem"
                                 :value="orderItem.mcActName" disabled
                                 :map="{mcActId:'id',mcActName:'activityName'}">
                        <template v-slot="{data}">
                            <item :title="data.activityName" :key="data.id" :data="data"/>
                        </template>
                    </link-object>
                </link-form-item>
            </view>
            <link-form-item label="订单备注" vertical>
                <link-textarea readonly v-model="orderItem.comments" style="padding-bottom: 48rpx"/>
            </link-form-item>
            <view>
                <title-line label-name="照片" />
                <lnk-img-lzlj :delFlag="false" :newFlag="false"
                              :module-type="orderItem.orderType === 'KaOrder' ? 'mp' : 'bookedOrder'"
                              @call="getImgList"
                              :parent-id="orderItem.id"></lnk-img-lzlj>
            </view>
        </link-form>
    </link-page>
</template>

<script>
import {ROW_STATUS} from "../../../utils/constant";
import LnkImgLzlj from "../../core/lnk-img-lzlj/lnk-img-lzlj";
import TitleLine from "../../lzlj/components/title-line";
import ApprovalHistoryPoint from "../../lzlj/approval/components/approval-history-point";
import ApprovalOperator from "../../lzlj/approval/components/approval-operator";
import LnkImgWatermark from "../../core/lnk-img-watermark/lnk-img-watermark";

export default {
    name: "booked-order-item-page",
    components: {
        LnkImgWatermark,
        TitleLine,
        LnkImgLzlj,
        ApprovalHistoryPoint,
        ApprovalOperator},
    data() {
        const userInfo = this.$taro.getStorageSync('token').result;         // 获取用户信息
        return {
            showTypeFlag: false, // 是否展示用途大类和用途子类型
            userInfo,
            companyName: '',// 公司名称
            orderItem: {},
            activityOption: new this.AutoList(this, {
                module: this.$env.appURL + '/action/marketactivity/link/marketActivity',
                searchFields: ['activityName'],
                param: {
                    rows: 25,
                }
            }),
            bookListId: null,
            approvalId: null,
            attachmentList: [],
            showBtn: false,
            bookOrderShipFLag: false //当前登陆用户公司，是否为会员服务部的公司id
        }
    },
    async created() {
        await this.queryCfg();
        await this.queryItemById();
        // 从列表进入则查询订单行及附件列表
        await this.getOrderLineList();
    },
    methods: {
        /**
         * @desc 查询参数配置
         * <AUTHOR>
         * @date 2023/8/8 14:28
         **/
        async queryCfg () {
            const shipIdList = await this.$utils.getCfgProperty('HIDE_BOOKORDER_SHIP');
            // 会员服务不显示出货单位
            this.bookOrderShipFLag = shipIdList.includes(this.userInfo.coreOrganizationTile.l3Id);
            const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', { key: 'USE_TYPE_USE_CATEGORY'});
            if (data.success && data.value) {
                const companyIds = data.value.split(',');
                this.showTypeFlag = companyIds.indexOf(this.userInfo.coreOrganizationTile['l3Id']) !== -1
            }
        },
        /**
         @param 图片list
         @desc: 图片上传成功后展示图片信息
         @author: wangbinxin
         @date 2022-05-30 11-26
         **/
        imgUploadSuccess(arr){
            this.imgList = arr;
        },
        /**
         * @desc 获取图片长度
         * <AUTHOR>
         * @date 2021/7/16 16:49
         **/
        getImgList (data) {
            this.attachmentList = data;
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/11/20
         * @methods queryItemById
         * @para
         * @description 获取动销表单信息
         */
        async queryItemById(){
            const data = await this.$http.post(this.$env.appURL + '/action/link/saleorder/queryHeadAndLineById', {
                id: this.pageParam.businessId
            }, {
                autoHandleError: false,
                handleFailed: (data) => {
                    if (!data.success) {
                        this.$showError('获取订单信息出错！' + data.result);
                    }
                }
            });
            if (data.success) {
                this.orderItem = data.order;
            }
            this.orderItem.saleOrderItemList = [];
        },
        /**
         * 获取订单行列表
         * <AUTHOR>
         * @date 2020-6-24
         */
        async getOrderLineList() {
            if (this.orderItem['source'] === 'RecognitionTicket') {
                const orderItemData = !this.$utils.isEmpty(this.orderItem.orderItemJson) ? JSON.parse(this.orderItem.orderItemJson) : [];
                this.$set(this.orderItem, 'saleOrderItemList', orderItemData);
                const len = this.orderItem.saleOrderItemList.length;
                // 计算汇总金额
                this.orderItem.orderAmount = 0;
                for (let i = 0; i < len; i++) {
                    if (this.orderItem.saleOrderItemList[i].promotionPrice >= 0 && this.orderItem.saleOrderItemList[i].qty >= 0) {
                        this.orderItem.saleOrderItemList[i]['amount'] = this.orderItem.saleOrderItemList[i].promotionPrice * this.orderItem.saleOrderItemList[i].qty;
                        this.orderItem.orderAmount += this.orderItem.saleOrderItemList[i]['amount'];
                    }
                }
            } else {
                const _this = this;
                const data = await this.$http.post(this.$env.appURL + '/action/link/saleorderitem/queryByExamplePage',
                    {
                        sort: 'created',
                        order: 'desc',
                        pageFlag: true,
                        rows: 500,
                        page: 1,
                        onlyCountFlag: false,
                        oauth: 'All',
                        filtersRaw: [{'id': 'headId', 'property': 'headId', 'value': _this.orderItem.id}]
                    }, {
                        autoHandleError: false,
                        handleFailed: (data) => {
                            if (!data.success) {
                                this.$showError('查询订单行信息失败！' + data.result);
                            }
                        }
                    });
                if (data.success) {
                    this.$set(this.orderItem, 'saleOrderItemList', data.rows);
                    const len = _this.orderItem.saleOrderItemList.length;
                    // 计算汇总金额
                    _this.orderItem.orderAmount = 0;
                    for (let i = 0; i < len; i++) {
                        _this.orderItem.orderAmount += _this.orderItem.saleOrderItemList[i].amount;
                    }
                }
            }
        },
        /**
         * 进入订单行详情
         * <AUTHOR>
         * @date 2020-06-24
         */
        gotoOrderItemLine(item, index) {
            const _this = this;
            item.row_status = ROW_STATUS.UPDATE;
            if (this.$utils.isEmpty(item.id)) {
                item.row_status = ROW_STATUS.NEW;
                item['headId'] = this.orderItem.id;
            }
            if (!item.logCodeList) item.logCodeList = [];
            item.logCodeList.forEach((logCode) => {
                logCode.row_status = ROW_STATUS.UPDATE;
            });
            this.$nav.push('/pages/lj-consumers/booked-order/booked-order-line-new-page', {
                orderItem: this.orderItem,
                index: index,
                orderLineItem: item,
                type: this.orderItem.orderChildType,
                callback: (orderLine, orderData) => {
                    this.orderItem = orderData;
                    _this.getOrderLineList();
                }
            });
        },
        viewAllLogCode(item, event) {
            event.stopPropagation();
            item.selectedFlag = !item.selectedFlag;
        }
    }
}
</script>

<style lang="scss">
.booked-order-item-page {
    background: #F2F2F2;
    font-size: 28px;
    .blank {
        height: 376px;
        width: 100%;
    }
    .link-form-edit{
        padding-bottom: 120px;
    }
    .account-info-wrapper {
        padding: 24px 36px;
        border-bottom: 2px solid #F2F2F2;
        background: white;

        .row-item {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-pack: start;
            -ms-flex-pack: start;
            justify-content: flex-start;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;

            .account-name {
                font-weight: 600;
                font-size: 32px;
                display: inline-block;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                flex: 3;
            }

            .position {
                flex: 5;
            }

            .status {
                flex: 2;
                text-align: right;

                &-new {
                    color: #0076FF;
                }

                &-registered {
                    color: green;
                }

                &-inactive {
                    color: red;
                }
            }
        }

        .sub-row-item {
            display: flex;
            justify-content: space-between;
            color: #9CA5A8;
            margin-top: 20px;
        }
    }

    .order-info-wrapper {
        padding: 32px 40px;
        background: white;

        .row-item {
            color: #6B7378;
            margin: 20px 0;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-pack: start;
            -ms-flex-pack: start;
            justify-content: flex-start;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;

            &.address-item {
                .info {
                    word-break: break-all;
                    word-wrap: break-word;
                    white-space: pre-wrap;
                }
            }

            .label {
                flex: 3;
            }

            .info {
                flex: 7;
            }
        }
    }

    .order-line-list-wrapper {
        margin-top: 24px;
        .order-line-list {
            .order-line {
                background: white !important;

                &:not(:last-of-type) {
                    margin-bottom: 20px;
                }
            }

            .order-line-item {
                .row-item {
                    padding: 24px;
                    border-bottom: 2px solid #F2F2F2;
                    .row-1 {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        margin-bottom: 20px;
                        .prod-name {
                            font-size: 32px;
                            color: #41484D;
                            width: 80%;
                        }

                        .prod-unit {
                            color: #9CA5A8;
                            width: 20%;
                            text-align: right;
                        }
                    }

                    .row-2 {
                        display: flex;
                        align-items: center;
                        justify-content: flex-end;

                        .prod-code {
                            color: #41484D;
                        }

                        .promotion-price {
                            color: #41484D;
                            font-weight: 500;
                        }
                    }
                }

                .sub-row-item {
                    border-bottom: 2px solid #F2F2F2;
                    padding: 28px 42px;
                    display: flex;
                    justify-content: space-between;

                    .label {
                        white-space: nowrap;
                        flex: 2;
                    }

                    .logistic-list {
                        flex: 7;
                        .logistic-code, .view-all {
                            color: #9CA5A8;
                            display: inline-block;
                            width: 480px;
                            white-space: nowrap;
                            overflow-x: auto; /* 当内容超出宽度时显示滚动条 */
                            line-height: 50px;
                        }

                        .view-all {
                            color: #0076FF;
                        }
                    }
                }

                .third-row-item {
                    padding: 28px 42px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .label {
                        color: #41484D;
                    }

                    .info {
                        font-size: 36px;
                        color: #41484D;
                    }
                }
            }

            .notification-add-order-line {
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                -webkit-box-pack: center;
                -ms-flex-pack: center;
                justify-content: center;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;
                background: white;
                height: 15vh;
                color: gray;
            }
        }
    }

    $main-color: #0076FF;

    .header {
        display: flex;
        justify-content: space-between;
        padding: 24px;
        align-items: center;
        background-color: #F2F2F2;
    }

}
</style>
