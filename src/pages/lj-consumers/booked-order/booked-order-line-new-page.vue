<template>
    <link-page class="booked-order-line-new-page">
        <link-form ref="form" :value="option.formData">
            <!--                <link-form-item label="拟购酒品牌" required :disabled="!canEdit">-->
            <!--                    <link-lov v-model="option.formData.prodGroup" type="PROD_BRAND"></link-lov>-->
            <!--                </link-form-item>-->
            <link-form-item label="拟购酒名称" required>
                <link-object :option="prodOption"
                             :row="option.formData"
                             :value="option.formData.prodName"
                             :disabled="!showBtn"
                             :map="{prodId: 'prodId',prodName: 'prodName', prodCode: 'prodCode'}">
                    <template v-slot="{data}">
                        <item :arrow="false" :title="data.prodName" :key="data.id" :data="data"/>
                    </template>
                </link-object>
            </link-form-item>
            <link-form-item label="单位" required>
                <view style="width: 80%; text-align: right;" @tap="pickProdUnit">
                    {{!$utils.isEmpty(option.formData.prodUnit) ? $filter.lov(option.formData.prodUnit , 'PROD_UNIT'):
                    '请选择单位'}}
                </view>
            </link-form-item>
            <link-form-item label="数量" required>
                <link-number :min="1" :disabled="!showBtn" v-model="option.formData.qty"></link-number>
            </link-form-item>
            <link-form-item label="转换系数"
                            v-if="!$utils.isEmpty(option.formData['prodUnit']) && !$utils.isEmpty(option.formData['changeRate'])">
                <view>{{'1' + ( $filter.lov(option.formData.prodUnit , 'PROD_UNIT') )+ '=' +
                    option.formData.changeRate +
                    '瓶'}}
                </view>
            </link-form-item>
            <link-form-item label="转换后数量" readonly v-show="option.formData.changeRate&&option.formData.qty">
                <link-input type="number" v-model="option.formData.changeRate * option.formData.qty"></link-input>
            </link-form-item>
            <link-form-item required :disabled="!canEdit || !showBtn">
                <view slot="title" style="display: flex; align-items: center">
                    <text style="width: 60%">单价</text>
                    <view style="font-size: 10px; color: #8C8C8C;">如单位选择“件”，请输入一件酒的单件价格；如单位选择“瓶”，请输入一瓶酒的单瓶价格</view>
                </view>
                <view @tap="setPromotionPriceInput" style="display: block;" class="promotion-price">
                    <link-input type="number"
                                ref="promotionPriceInputRef"
                                v-if="promotionPriceInputFlag"
                                class="promotion-price-input" style="padding-right: 16rpx;"
                                :disabled="!canEdit || !showBtn"
                                placeholder="请输入单价"
                                v-model="option.formData.promotionPrice"
                                @blur="onPromotionPriceBlur($event)"></link-input>
                    <text style="padding-right: 16rpx;"
                          v-if="!promotionPriceInputFlag && !$utils.isEmpty(option.formData.promotionPrice)">
                        <text @tap="onPromotionPriceBlur">{{'￥' + option.formData.promotionPrice}}</text>
                        <text v-if="!$utils.isEmpty(option.formData.prodUnit)">{{option.formData.prodUnit |
                            lov('PROD_UNIT')}}
                        </text>
                    </text>
                </view>
            </link-form-item>
            <link-form-item label="金额" readonly v-show="option.formData.promotionPrice&&option.formData.qty">
                <link-input v-model="option.formData.promotionPrice * option.formData.qty"></link-input>
            </link-form-item>
        </link-form>
        <view class="order-line-list-wrapper" v-if="type === 'LogisticsCodeBased'">
            <view class="header">
                <view class="header-left">物流码</view>
                <view class="header-right">
                    <link-icon v-if="canEdit && showBtn" @tap="scanCode()" style="margin-right: 40rpx;"
                               icon="icon-scan"/>
                    <link-icon v-if="canEdit && showBtn" @tap="addNewLogisticCode()" icon="icon-tupian1"/>
                </view>
            </view>
            <link-swipe-action v-for="(item,index) in option.formData.logCodeList" :key="item.id" class="logistic-list">
                <link-swipe-option slot="option" @tap="showBtn && deleteLogisticCode(item,index)">删除
                </link-swipe-option>
                <item style="width: 100%" :arrow="false">
                    <link-input v-model="item.logCode" :disabled="!showBtn"
                                @blur="checkLogCode(item)"></link-input>
                </item>
            </link-swipe-action>
        </view>
        <link-sticky v-if="showBtn">
            <link-button block @tap="saveOrderLine">保存</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    import Taro from "@tarojs/taro";
    import {ROW_STATUS} from "../../../utils/constant";
    import {LovService} from "link-taro-component";

    export default {
        name: "booked-order-line-new-page",
        data() {
            let _canEdit = false;
            if (this.pageParam.orderItem['status'] !== 'Inactive') {
                _canEdit = true;
            }
            const flag = this.pageParam['flag'] || '';
            const index = this.pageParam['index'];
            const orderLineItem = {
                prodGroup: null,
                prodUnit: null,
                prodName: null,
                prodId: null,
                changeRate: null,
                qty: null,
                promotionPrice: null,
                logCodeList: [],
                row_status: ROW_STATUS.NEW,
                ...this.pageParam.orderLineItem
            };
            let option = {};
            option.formData = orderLineItem;
            const userInfo = Taro.getStorageSync('token').result;
            return {
                flag,
                index,
                tips: '',
                codeScope: '',
                userInfo,
                option,
                canEdit: _canEdit,
                promotionPriceInputFlag: this.$utils.isEmpty(orderLineItem.promotionPrice),
                type: this.pageParam.type,
                orderItem: this.pageParam.orderItem,
                orderLineItem,
                prodOption: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: this.$env.appURL + '/action/link/interprodgroup/queryProductPageByLoginUser',
                    },
                    searchFields: ['prodName', 'prodCode'],
                    param: {
                        rows: 25,
                        groupType: 'BookedOrder'
                    },
                    hooks: {
                        beforeLoad(option) {
                            option.param.prodGroup = this.orderLineItem.prodGroup;
                            if (this.$utils.isEmpty(option.param.prodGroup)) {
                                option.param.prodGroup = this.orderLineItem.prodGroup
                            }
                        }
                    }
                }),
                prodUnitOption: new this.AutoList(this, {
                    module: this.$env.appURL + '/action/link/prodAlias',
                    param: {
                        rows: 25,
                        filtersRaw: [
                            {'id': 'sysSource', 'property': 'sysSource', 'value': 'MarketingPlatform', 'operator': '='},
                            {'id': 'activeFlg', 'property': 'activeFlg', 'value': 'Y', 'operator': '='},
                        ]
                    },
                    hooks: {
                        beforeLoad(option) {
                            if (this.$utils.isEmpty(this.orderLineItem.prodId)) {
                                this.$showError('请先选择产品!');
                                return Promise.reject(false)
                            }
                            option.param.filtersRaw = [
                                ...option.param.filtersRaw,
                                {
                                    'id': 'prodId',
                                    'property': 'prodId',
                                    'value': this.orderLineItem.prodId,
                                    'operator': '='
                                },
                            ];
                        }
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} desc={data.orgName}>
                                <text slot="title">{LovService.filter(data.unit, 'PROD_UNIT')}</text>
                            </item>
                        )
                    }
                }),
                showBtn: false
            }
        },
        async created() {
            this.$taro.setNavigationBarTitle({title: this.pageParam.title ? this.pageParam.title : '动销明细'});
            await this.getShowBtn();
            //初始化购酒品牌
            this.orderLineItem.prodGroup = this.userInfo.companyBrand;
            let codeTemp = await this.$lov.getLovByType('LOG_CODE_LENGTH');
            let tempTips = [];
            if (!this.$utils.isEmpty(codeTemp)) {
                for (let i = 0; i < codeTemp.length; i++) {
                    tempTips.push(codeTemp[i]['val']);
                }
            }
            this.codeScope = tempTips.sort();
            this.tips = tempTips.toString().replace(',', '或');
        },
        methods: {
            setPromotionPriceInput() {
                if (!(this.canEdit && this.showBtn)) return;
                this.promotionPriceInputFlag = true;
            },
            onPromotionPriceBlur($event) {
                if (this.$utils.isEmpty(this.option.formData.promotionPrice)) {
                    this.promotionPriceInputFlag = true;
                }
                this.promotionPriceInputFlag = false;
                if (!this.option.formData.hasOwnProperty('promotionPrice')) {
                    this.option.formData.promotionPrice = '';
                }
            },
            /**
             *  @description: 删除物流码信息
             *  @author: 马晓娟
             *  @date: 2020/11/18 11:21
             */
            async deleteLogisticCode(item, index) {
                if (item.row_status === ROW_STATUS.NEW) {
                    // 如果是前端新建的,则直接删除
                    this.orderLineItem.logCodeList.splice(index, 1);
                    this.$message.success('已删除')
                    return;
                }
                if (item.id) {
                    const data = await this.$http.post(this.$env.appURL + '/action/link/logCode/deleteById', item, {
                        fail() {
                            this.$showError('删除失败!请重试!')
                        }
                    });
                }
                this.orderLineItem.logCodeList.splice(index, 1);
            },
            /**
             * 物流码失去焦点事件
             * <AUTHOR>
             * @date 2020-07-07
             * @param item 物流码对象
             */
            async checkLogCode(item) {
                let lenTemp = item.logCode.length;
                if (this.tips.indexOf(lenTemp.toString()) !== -1 && lenTemp >= parseInt(this.codeScope[0], 10)) {
                    return true;
                }
                await this.$showError(`物流码限定必须为${this.tips}位`);
                return false;
            },
            /**
             *  @description: 扫码录入
             *  @author: 马晓娟
             *  @date: 2020/11/18 11:03
             */
            async scanCode() {
                const that = this;
                // 只允许从相机扫码
                await wx.scanCode({
                    onlyFromCamera: true,
                    success(res) {
                        that.relateBaseInfoFun(res.result);
                    }
                });
            },
            //扫码之后拿到码信息之后的操作
            async relateBaseInfoFun(mark) {
                const that = this;
                if (!!mark) {
                    const tempText = mark;
                    let text = tempText;
                    if (tempText.indexOf('http') !== -1 || tempText.indexOf('HTTP') !== -1) {
                        let index = tempText.lastIndexOf('\/');
                        if (tempText.indexOf('j=') !== -1) {
                            text = tempText.split('j=')[1]
                        } else {
                            text = tempText.substring(index + 1, tempText.length);
                        }
                    }
                    if (!that.checkLogCode({logCode: text})) {
                        return;
                    }
                    await this.checkProd(text);
                }
            },
            /**
             * @desc 根据产品编码和物流码匹配后端是否有相关产品数据
             * <AUTHOR>
             * @date 2022/5/11 16:52
             * @param logCode 物流码
             **/
            async checkProd(logCode) {
                this.$utils.showLoading();
                const data = await this.$httpForm.post(this.$env.appURL + '/action/link/logCode/queryProdInfoByCode', {
                    logCode: logCode,
                    prodCode: this.option.formData.prodCode
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('查询物流码数据失败！' + response.result);
                    }
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    if (data.result) {
                        this.option.formData.prodId = data.result.id;
                        this.option.formData.prodName = data.result.prodName;
                        this.option.formData.prodCode = data.result.prodCode;
                    }
                    // 处理扫码内容
                    if (!this.orderLineItem.logCodeList) this.orderLineItem.logCodeList = [];
                    // 如果第一条记录没有值,则填充到第一条记录中
                    if (!!this.orderLineItem.logCodeList[0] && !this.orderLineItem.logCodeList[0].logCode) {
                        this.orderLineItem.logCodeList[0].logCode = text;
                    } else {
                        // 否则则新建记录
                        const logisticCodeItem = {
                            orderLineId: this.orderLineItem.id,
                            logCode: logCode,
                            row_status: ROW_STATUS.NEW
                        };
                        this.orderLineItem.logCodeList.push(logisticCodeItem);
                    }
                    this.scanCode()
                }
            },
            /**
             *  @description: 选择单位
             *  @author: 马晓娟
             *  @date: 2020/11/18 11:01
             */
            async pickProdUnit() {
                if (!(this.canEdit && this.showBtn)) return;
                const data = await this.$object(this.prodUnitOption);
                this.$set(this.option.formData, 'prodUnit', data.unit);
                this.$set(this.option.formData, 'changeRate', data.prodRatio);
            },
            /**
             * 创建新的物流码
             * <AUTHOR>
             * @date 2020-07-07
             */
            addNewLogisticCode() {
                if (!this.option.formData.logCodeList) this.option.formData.logCodeList = [];
                const len = this.option.formData.logCodeList.length;
                for (let i = 0; i < len; i++) {
                    if (this.$utils.isEmpty(this.option.formData.logCodeList[i].logCode)) {
                        this.$message.primary('物流码列表中存在为空的记录,请输入后保存')
                        return;
                    }
                }
                const logisticCodeItem = {
                    orderLineId: this.orderLineItem.id,
                    logCode: '',
                    row_status: ROW_STATUS.NEW
                };
                this.option.formData.logCodeList.push(logisticCodeItem);
            },
            /**
             * 保存
             * <AUTHOR>
             * @date 2020-
             */
            async saveOrderLine() {
                if (!this.checkData()) {
                    return;
                }
                this.option.formData['headId'] = this.orderLineItem.headId;
                this.option.formData.netPrice = this.option.formData.promotionPrice;
                let orderItemJsonData = [];
                if (this.orderItem['source'] === 'RecognitionTicket' && !this.$utils.isEmpty(this.orderItem['orderItemJson'])) {
                    orderItemJsonData = JSON.parse(this.orderItem['orderItemJson']);
                }
                //如果saveFlag为true,不调用接口处理数据
                if (this.pageParam.saveFlag) {
                    this.pageParam.callback(this.option.formData, this.orderItem);
                    this.$nav.back();
                    return;
                }
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + '/action/link/saleorderitem/upsert', this.option.formData, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('订单行数据保存失败！' + response.result);
                    }
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    if (this.orderItem['source'] === 'RecognitionTicket') {
                        if (this.flag === 'Add') {
                            orderItemJsonData.push(data.newRow);
                        } else {
                            for (let i = 0; i < orderItemJsonData.length; i++) {
                                if (i === this.index) {
                                    orderItemJsonData[i] = data.newRow;
                                    break;
                                }
                            }
                        }
                        const updateData = {
                            id: this.orderItem.id,
                            orderItemJson: JSON.stringify(orderItemJsonData),
                            updateFields: "orderItemJson"
                        };
                        const orderItem = await this.$http.post(this.$env.appURL + '/action/link/saleorder/update', updateData, {
                            autoHandleError: false,
                            handleFailed: (response) => {
                                this.$utils.hideLoading();
                                this.$showError('订单数据更新失败！' + response.result);
                            }
                        });
                        this.orderItem = orderItem.newRow;
                    }
                    this.pageParam.callback(data.newRow, this.orderItem);
                    this.$nav.back();
                }

            },
            /**
             * 校验文件
             * <AUTHOR>
             * @date 2020-07-07
             */
            checkData() {

                if (this.$utils.isEmpty(this.option.formData['prodName'])) {
                    this.$message.primary('请选择产品!');
                    return false;
                }
                if (this.$utils.isEmpty(this.option.formData['prodUnit'])) {
                    this.$message.primary('请选择单位!');
                    return false;
                }
                if (this.$utils.isEmpty(this.option.formData['qty'])) {
                    this.$message.primary('请输入数量!');
                    return false;
                }
                if (parseInt(this.option.formData['qty'], 10) <= 0) {
                    this.$message.primary('请输入大于0的数量!');
                    return false;
                }

                if (this.option.formData.prodUnit === 'Jian') {
                    if (this.$utils.isEmpty(this.option.formData['changeRate'])) {
                        this.$message.primary('请输入转换系数!');
                        return false;
                    }
                }
                if (this.$utils.isEmpty(this.option.formData['promotionPrice'])) {
                    this.$message.primary('请输入单价!');
                    return false;
                }
                // 物流码校验正确性
                if (this.orderItem.orderChildType === 'LogisticsCodeBased') {
                    const len = this.option.formData.logCodeList.length;
                    for (let i = 0; i < len; i++) {
                        if (!this.checkLogCode(this.option.formData.logCodeList[i])) {
                            return false;
                        }
                    }
                }
                return true;
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/05/30
             * @methods: getShowBtn
             * @para:
             * @description: 是否展示操作按钮
             **/
            async getShowBtn() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', {key: 'BOOK_ORDER_SHOW'});
                if (data.success) {
                    if (this.orderItem.orderType === 'KaOrder') {
                        this.showBtn = true;
                    } else {
                        this.showBtn = data.value.split(',').includes(this.userInfo.coreOrganizationTile.l3Id);
                    }
                } else {
                    this.$message.error('查询是否展示按钮失败：' + data.result);
                }
            }
        }
    }
</script>

<style lang="scss">
    .booked-order-line-new-page {
        .header {
            display: flex;
            margin-top: 24px;
            justify-content: space-between;
            font-size: 28px;
            line-height: 44px;
            background: white;
            padding: 24px;
            border-bottom: 2px solid #F2F2F2;

            .header-right {
                display: flex;
                color: #4c8dff;

                .link-icon {
                    font-size: 44px;
                }
            }
        }

        .logistic-list {
            border-bottom: 2px solid #F2F2F2;

            .link-input {
                text-align: left;
            }
        }

        .promotion-price {
            width: 60%;
            height: 50px;
            text-align: right;
        }
    }
</style>
