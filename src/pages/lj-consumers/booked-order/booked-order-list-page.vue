<template>
    <link-page class="booked-order-list-page">
        <lnk-taps :taps="orderTypeOptions" v-model="orderTypeActive" @switchTab="onTap"
        :searchInputBinding="{props:{placeholder:'姓名/手机号/订单ID'}}"></lnk-taps>
        <view class="blank"></view>
        <link-auto-list :option="autoList" :hideCreateButton="orderTypeActive.val === 'KaOrder' || !showBtn">
            <link-filter-group slot="filterGroup">
                <link-filter-item label="动销总金额(升序)" :param="{sort:{field:'orderAmount',desc:false}}"/>
                <link-filter-item label="订购时间(升序)" :param="{sort:{field:'orderDate',desc:false}}"/>
                <link-filter-item label="创建时间(升序)" :param="{sort:{field:'created',desc:false}}"/>
                <link-filter-item label="最近更新(升序)" :param="{sort:{field:'lastUpdated',desc:false}}"/>
            </link-filter-group>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="booked-order-list-item">
                    <view slot="note" class="item-container">
                        <view class="row-item">
                            <view class="accnt-name">{{data.acctName || data.customConsignee}}</view>
                            <view class="mobile-phone">{{data.customMobilePhone}}</view>
                        </view>
                        <view class="sub-row-item">
                            <view class="order-info">
                                <view class="company">单位名称: {{data.customCompany || '--'}}</view>
                                <view class="order-total-amount">订单总金额: {{data.orderAmount}}</view>
                                <view class="order-total-amount">实际金额: {{data.realAmount}}</view>
                                <view class="order-total-amount" @tap="copyData(data.id)">订单ID: {{data.id}}</view>
                                <view class="order-date">订购时间: {{data.orderDate | date('YYYY-MM-DD')}}</view>
                            </view>
                            <view class="status">
                                <view :class="{'status-new':data.status === 'New','status-registered':data.status === 'Registered','status-inactive':data.status === 'Inactive'}">
                                    {{data.status | lov('ORDER_STATUS')}}
                                </view>
                                <view :class="data.isExistChildFlag === 'Y' ? 'return-order-button-active': 'return-order-button'"  @tap.stop="gotoReturn(data)">查看退货订单</view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
    import {ROW_STATUS} from "../../../utils/constant";
    import LnkTaps from "../../core/lnk-taps/lnk-taps";
    import ConsumerListCommon from "../consumer-list-common";

    export default {
        name: "booked-order-list-page",
        components: {LnkTaps},
        mixins: [ConsumerListCommon()],
        data() {
            const userInfo = this.$taro.getStorageSync('token').result;
            const url = this.$env.appURL + '/action/link/saleorder/queryFieldsByExamplePage';
            const autoList = new this.AutoList(this, {
                module: this.$env.appURL + "/action/link/saleorder",
                createPath: '/pages/lj-consumers/booked-order/booked-order-new-page',
                itemPath: '/pages/lj-consumers/booked-order/booked-order-new-page',
                url: {
                    queryByExamplePage: url
                },
                loadOnStart: false,
                fetchItem: true,
                param: {
                    oauth: this.pageOauth,
                    filtersRaw: [
                        {id: 'orderType', property: 'orderType', value: 'BookedOrder', operator: '='},
                        {
                            id: 'orderChildType',
                            property: 'orderChildType',
                            value: '[InvoiceBased,LogisticsCodeBased]',
                            operator: 'IN'
                        }
                    ],
                },
                stayFields: 'id,acctName,customMobilePhone,customCompany,orderAmount,orderDate,status,customConsignee,companyName,realAmount,isExistChildFlag',
                hooks: {
                    async beforeCreateItem(param) {
                        param.data = {
                            orderType: 'BookedOrder',
                            orderKind: 'TerminalOrder',
                            orderChildType: '',
                            acctName: '',
                            salesmanId: userInfo.postnId,
                            salesmenName: userInfo.firstName,
                            salesmenPhone: userInfo.contactPhone,
                            status: 'New',
                            row_status: ROW_STATUS.NEW,
                            acctType: 'Terminal',
                        };
                    },
                    async beforeGotoItem(param) {
                        param.source = 'bookList'
                    }
                },
                exactSearchFields: [
                {
                    field: 'acctName',
                    showValue: '姓名',
                    searchOnChange: true,
                    clearOnChange: true,
                    // exactSearch: true
                },{
                    field: 'customMobilePhone',
                    showValue: '手机号',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: true
                }, {
                    field: 'id',
                    showValue: '订单ID',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: true
                }
            ],
                searchFields: ['id','acctName', 'customMobilePhone', 'customCompany'],
                sortOptions: null,
                filterOption: [
                    {label: '订单状态', field: 'status', type: 'lov', lov: 'ORDER_STATUS'},
                    {label: '订购时间', field: 'orderDate', type: 'date'},
                    {label: '系统来源', field: 'orderSource', type: 'lov', lov: 'BOOK_ORDER_SOURCE'},
                ],
                slots: {
                    searchRight: () => (
                        this.orderTypeActive.val === 'KaOrder' ? '' :
                            <view class="filter-type-item"
                                  style="max-width: 224rpx;height: 72rpx;display: flex;align-items: center;justify-content: flex-end;padding-left: 30rpx;font-family: PingFangSC-Regular;font-size: 26rpx;color: #333333;line-height: 40rpx;font-weight: 400;"
                                  onTap={this.chooseOauthData}>{this.pageOauthName}
                                <link-icon icon="mp-desc" style="color: #CCCCCC; margin: 4rpx 0 0 8rpx;"/>
                            </view>
                    )
                }
            });
            return {
                url,
                autoList,
                userInfo,
                orderTypeOptions: [
                    {
                        defaultValue: "N",
                        id: "222323332756804060",
                        name: "动销订单",
                        seq: "1",
                        type: "orderType",
                        val: "BookedOrder"
                    },
                    {
                        defaultValue: "N",
                        id: "222323332756804060",
                        name: "KA订单",
                        seq: "2",
                        type: "orderType",
                        val: "KaOrder"
                    }
                ],
                orderTypeActive: {},
                showBtn: false
            };
        },
        async created() {
            this.orderTypeActive = this.orderTypeOptions[0];
            if (this.pageOauthList.length > 0) {
                this.pageOauthName = this.pageOauthList[0].name;
                this.pageOauth = this.pageOauthList[0].securityMode;
            } else {
                this.pageOauthName = '我的数据';
                this.pageOauth = 'MY_POSTN_ONLY';
            }
            this.autoList.option.param.oauth = this.pageOauth;
            this.autoList.methods.reload();
            this.getShowBtn();
        },
        methods: {
            /**
             * @desc 切换tab页签
             * <AUTHOR>
             * @date 2022/4/13 10:33
             **/
            async onTap() {
                if (this.orderTypeActive.val === 'KaOrder') {
                    this.pageOauthList = [{securityMode: 'ALL', name: '全部'}];
                    this.pageOauth = 'ALL';
                    this.pageOauthName = '全部';
                    this.autoList.option.url.queryByExamplePage = this.$env.appURL + '/action/link/saleorder/queryKaOrder4QWByPage';
                    this.autoList.option.param['attr7'] = this.userInfo.postnId;
                    this.autoList.option.param.oauth = 'ALL';
                } else {
                    this.pageOauthList = this.pageParam.secMenus || [{securityMode: 'MY_POSTN_ONLY', name: '我的数据'}];
                    this.pageOauth = this.pageOauthList[0].securityMode;
                    this.pageOauthName = this.pageOauthList[0].name;
                    delete this.autoList.option.param['attr7'];
                    this.autoList.option.param.oauth = this.pageOauth;
                    this.autoList.option.url.queryByExamplePage = this.url;
                    const filtersRawData = this.autoList.option.param.filtersRaw.filter((item) => item.property === 'orderChildType');
                    if (filtersRawData.length <= 0) {
                        this.autoList.option.param.filtersRaw = this.autoList.option.param.filtersRaw.concat({
                            id: 'orderChildType',
                            property: 'orderChildType',
                            value: '[InvoiceBased,LogisticsCodeBased]',
                            operator: 'IN'
                        });
                    }
                }
                this.autoList.option.param.filtersRaw.forEach((item, index) => {
                    if (item.property === 'orderType') {
                        item.value = this.orderTypeActive.val;
                    }
                    if (this.orderTypeActive.val === 'KaOrder' && item.property === 'orderChildType') {
                        this.autoList.option.param.filtersRaw.splice(index, 1);
                    }
                });
                await this.autoList.methods.reload();
            },
            /**
             * @desc 复制ID
             * <AUTHOR>
             * @date 2021/8/13 15:38
             **/
            copyData(data) {
                wx.setClipboardData({
                    data: data,
                    success: function () {
                        // 添加下面的代码可以复写复制成功默认提示文本`内容已复制`
                        wx.showToast({
                            title: '复制成功',
                            duration: 3000
                        });
                        wx.getClipboardData({
                            success: function (res) {
                            }
                        })
                    }
                })
            },
            onBack() {
                this.autoList.methods.reload();
            },
            /**
             * @desc 退货订单
             * <AUTHOR>
             * @date 2024-06-05
             **/
            gotoReturn(data) {
                if(data.isExistChildFlag === 'Y') {
                    this.$nav.push('/pages/lj-consumers/booked-order/booked-order-return-list-page', {
                        data: data
                    });
                } else {
                    return;
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/05/30
             * @methods: getShowBtn
             * @para:
             * @description: 是否展示操作按钮
             **/
            async getShowBtn () {
                const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', {key: 'BOOK_ORDER_SHOW'});
                if (data.success) {
                    this.showBtn = data.value.split(',').includes(this.userInfo.coreOrganizationTile.l3Id);
                } else {
                    this.$message.error('查询是否展示按钮失败：'+ data.result);
                }
            }
        }
    }
</script>

<style lang="scss">
    .booked-order-list-page {
        .blank {
            width: 100%;
            height: 96px;
            background: #F2F2F2;
        }

        @include flex();
        @include direction-column();

        .booked-order-list-item {
            background: #FFFFFF;
            margin: 24px;
            border-radius: 16px;
        }

        .item-container {
            color: #262626;
            position: relative;
        }

        .row-item {
            @include flex-center-center();
            @include space-between();
            margin-bottom: 20px;
            width: 100%;
            font-weight: 500;

            .accnt-name {
                font-weight: 500;
            }

            .mobile-phone {
            }
        }

        .sub-row-item {
            width: 100%;
            @include flex-center-center();
            @include space-between();

            .order-info {
                .company, .order-total-amount.order-date {
                    margin: 10px 0;
                    display: flex;
                }
            }

            .status {
                width: 30%;
                text-align: right;
                font-weight: 500;

                &-new {
                    color: $main-color;
                }

                &-registered {
                    color: green;
                }

                &-inactive {
                    color: red;
                }
                .return-order-button-active {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background: #2F69F8;
                    color: #FFFFFF;
                    font-size: 24px;
                    height: 40px;
                    width: 160px;
                    border-radius: 10px;
                    position: absolute; /* 设置为绝对定位 */
                    right: 0; /* 定位在右侧 */
                    bottom: 0; /* 定位在底部 */
                    line-height: 40px;
                }
                .return-order-button {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background: #9a979c;
                    color: #FFFFFF;
                    font-size: 24px;
                    height: 40px;
                    width: 160px;
                    border-radius: 10px;
                    position: absolute; /* 设置为绝对定位 */
                    right: 0; /* 定位在右侧 */
                    bottom: 0; /* 定位在底部 */
                    line-height: 40px;
                }
            }
        }
    }
</style>
