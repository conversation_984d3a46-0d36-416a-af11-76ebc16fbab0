<!--
执行情况-终止原因
<AUTHOR>
@date 2025-01-08
@file implement-end-reason.vue
-->
<template>
    <view class="implement-end-reason">
        <view class="end-reason" v-if="scheduleItem.status === 'End'">
            <view class="title">终止原因</view>
            <view class="reason-text">{{scheduleItem.endReason}}</view>
        </view>
        <view class="zero-view" v-if="scheduleItem.endReason"></view>
    </view>
</template>

<script>
export default {
    name: 'implement-end-reason',
    props: {
        scheduleItem: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data() {
        return {
        }
    },
    created() {

    },
    methods: {
    }
}
</script>

<style lang="scss">
.implement-end-reason {
    .zero-view {
        width: 100%;
        height: 30px;
    }
    .end-reason{
        padding: 34px;
        background-color: #fff;
        border-radius: 16px;
        margin: 24px;
        .title {
            font-size: 28px;
            font-weight: bold;
            color: #333333;
            padding-bottom: 10px;
        }
        .reason-text{
            font-size: 24px;
            color: #333333;
        }
    }
}
</style>
