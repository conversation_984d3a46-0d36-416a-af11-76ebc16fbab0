/**
 * @desc 根据状态返回颜色
 * <AUTHOR>
 * @date 2025-01-09
 * @param planStatus
 * @param planDeStatus
 * @param type ALL返回背景色和字体颜色
 **/
export const getColorByStatus = (planStatus: string, planDeStatus: string, type: string) => {
    // 若排期计划审核状态planStatus=新建审核中、新建审核拒绝、变更审核中、作废审核中均角标均展示审核状态planStatus；
    // 其他情况展示planDeStatus排期计划完成状态；
    const planStatusList = ['InsertReject', 'InsertRevering', 'UpdateRevering', 'InvalidRevering']
    let result = 'transparent'
    if (planStatusList.includes(planStatus)) {
        switch (planStatus) {
            // 新建审核拒绝
            case 'InsertReject':
                result = '#FF0000'
                break
            // 新建审核中
            case 'InsertRevering':
                result = '#0000FF'
                break
            // 变更审核中
            case 'UpdateRevering':
                result = '#0000FF'
                break
            // 作废审核中
            case 'InvalidRevering':
                result = '#0000FF'
                break
        }
    } else {
        switch (planDeStatus) {
            // 待执行
            case 'Execution':
                result = '#00afec'
                break
            // 执行中
            case 'Executing':
                result = '#63cd73'
                break
            // 未完成
            case 'Planing':
                result = '#ff994b'
                break
            // 已完成
            case 'Finished':
                result = '#13c2c2'
                break
            // 已作废
            case 'Invalid':
                result = '#8e97a6'
                break
            // 待审批-新建
            case 'New':
                result = '#8e97a6'
                break
            // 待审批-作废
            case 'Invalid':
                result = '#d9001b'
                break
            // 暂缓
            case 'Suspend':
                result = '#ff4d4f'
                break
            // 延期执行
            case 'ExeDelayed':
                result = '#ffc300'
                break
            // 提前执行
            case 'ExeEarly':
                result = '#6d7bfc'
                break
            // 变更
            case 'Change':
                result = '#ed8425'
                break
        }
    }
    if (type && type === 'ALL') {
        return `backgroundColor:${hexToRgb(result)};color:${result}`
    } else {
        return `backgroundColor:${result}`
    }
}

function hexToRgb(hex) {
    // 去除可能存在的 # 符号
    hex = hex.replace(/^#/, '');
    // 检查输入的十六进制颜色代码长度是否为 3 或 6
    if (hex.length === 3) {
        // 如果是 3 位的十六进制颜色代码，扩展为 6 位
        hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
    }

    if (hex.length === 6) {
        // 将十六进制字符串转换为对应的十进制整数
        const r = parseInt(hex.slice(0, 2), 16);
        const g = parseInt(hex.slice(2, 4), 16);
        const b = parseInt(hex.slice(4, 6), 16);

        return `rgb(${r}, ${g}, ${b}, 0.1)`;
    }
    return null;
}
