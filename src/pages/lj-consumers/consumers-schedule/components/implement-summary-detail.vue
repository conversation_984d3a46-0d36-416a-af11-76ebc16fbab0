<!--
详情页面-执行明细
<AUTHOR>
@date 2025-01-09
@file implement-summary-detail.vue
-->
<template>
    <view class="implement-summary-detail">
        <line-title title="执行明细"></line-title>
        <view class="zero-view"></view>
        <view class="form">
            <link-auto-list :option="summaryDetailList" hideCreateButton :key="9">
                <template slot-scope="{data,index}">
                    <item :key="index" :data="data" :arrow="false" class="form-item">
                        <view slot="note">
                            <view class="item-input">
                                <view class="label">业务名称</view>
                                <view class="text">{{ data.businessName }}</view>
                            </view>
                            <view class="item-input">
                                <view class="label">创建人</view>
                                <view class="text">{{ data.creatorName}}</view>
                            </view>
                            <view class="item-input">
                                <view class="label">实际执行时间</view>
                                <view class="text">{{ data.behaviorTime }}</view>
                            </view>
                        </view>
                    </item>
                </template>
            </link-auto-list>
        </view>
    </view>
</template>

<script>
import LineTitle from "../../../lzlj/components/line-title.vue";

export default {
    name: 'implement-summary-detail',
    components: {LineTitle},
    props: {
        rowId: {
            type: String,
            default: ''
        }
    },
    data() {
        const summaryDetailList = new this.AutoList(this, {
            url: {
                queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/send'
            },
            param: {
                dmpUrl: '/link/consumerSchedulePlan/queryPlanDetails',
                id: this.rowId
            },
            hooks: {
                beforeLoad(option) {
                    delete option.param.filtersRaw;
                    delete option.param.sort;
                    delete option.param.order;
                },
                afterLoad: async (data) => {
                }
            },
        });
        return {
            summaryDetailList
        }
    },
    created() {

    },
    methods: {
        /**
         * @desc 重新加载
         * <AUTHOR>
         * @date 2025-01-10
         **/
        reloadSummaryDetailList(rowId) {
            this.rowId = rowId;
            this.summaryDetailList.methods.reload();
        }
    }
}
</script>

<style lang="scss">
@import './common-styles.scss';
.implement-summary-detail {
    width: 100%;
    .form {
        width: 94%;
        margin: 0 auto;
        .form-item {
            border-radius: 16px;
            padding: 40px 28px 4px 28px;
            background: white;
            margin-bottom: 24px;
            font-family: PingFangSC-Regular;
            .item-input {
                line-height: 30px;
                margin-bottom: 30px;
                width: 100%;
                display: flex;
                .label {
                    align-self: flex-start;
                    color: #8c8c8c;
                    font-size: 28px;
                    display: flex;
                    align-items: center;
                    box-sizing: border-box;
                    width: 300px;
                }
                .text {
                    color: #262626;
                    width: 420px;
                    font-size: 28px;
                    text-align: right;
                }
            }
            .item-textarea {
                line-height: 30px;
                width: 100%;
                margin-bottom: 30px;
                .label {
                    color: #8c8c8c;
                    font-size: 28px;
                    display: flex;
                    align-items: center;
                    box-sizing: border-box;
                    width: 300px;
                    margin-bottom: 18px;
                }
                .text {
                    color: #262626;
                    width: 420px;
                    font-size: 28px;
                }
            }
            .link-item {
                flex-direction: column;
            }
        }
    }
}
</style>
