<!--
详情-消费者门槛
<AUTHOR>
@date 2025-01-07
@file detail-threshold.vue
-->
<template>
    <view class="detail-threshold">
        <view class="cell">
            <view class="cell-title">排期消费者门槛</view>
            <view class="cell-value" @tap="openDialog">查看</view>
        </view>
        <link-dialog ref="consumerThreshold" v-model="dialogFlag" title="排期消费者门槛" width="80%">
            <view class="consumer-threshold">
                <view class="consumer-threshold-content">
                    <line-title title="标签门槛"></line-title>
                    <view style="padding: 10px 0" v-if="tagList.length > 0">
                        <view class="schedule-tag-consumer-text" v-for="(item, index) in tagList" :key="index">
                            {{item.tagGroupName}}
                            {{ $lov.filter(item.tagJudgeName , 'TAG_VALUE__FILTER') }}
                            {{item.tagJudgeValue}}
                        </view>
                    </view>
                    <view style="padding: 10px 0" v-else>
                        <view class="no-data"> ———— 暂无数据 ————</view>
                    </view>
                    <line-title title="基础信息门槛"></line-title>
                    <view style="padding: 10px 0" v-if="consumerList.length > 0">
                        <view class="schedule-tag-consumer-text" v-for="(item, index) in consumerList" :key="index">
                            {{item.fieldName}}
                            {{item.judgeName}}
                            {{(item.fieldType=='Lov' && item.fieldColumnName=='loyaltyLevel') ?
                            $lov.filter(item.judgeContentList , 'ACCT_MEMBER_LEVEL') : (item.fieldType=='Lov' ?
                                $lov.filter(item.judgeContentList , item.fieldLovType) : item.judgeContent)}}
                            {{ (item.fieldType=='Lov' && $utils.isNotEmpty(item.positionType)) ? ('———— 可添加职位类型 '  + ' 包含' +  $lov.filter(item.positionTypeList , 'ASSIGN_POSITION')) : ''}}
                        </view>
                    </view>
                    <view style="padding: 10px 0" v-else>
                        <view class="no-data"> ———— 暂无数据 ————</view>
                    </view>
                    <line-title title="人群包"></line-title>
                    <view style="padding: 10px 0" v-if="customersItem.crowdName">
                        人群包<view class="schedule-tag-consumer-text" style="display: inline; margin-left: 40px; line-height: 36px">{{ customersItem.crowdName }}</view>
                    </view>
                    <view style="padding: 10px 0" v-else>
                        <view class="no-data"> ———— 暂无数据 ————</view>
                    </view>
                </view>
                <view class="button">
                    <link-button @tap="closeDialog">确定</link-button>
                </view>
            </view>
        </link-dialog>
    </view>
</template>

<script>
import LineTitle from "../../../echart/lzlj/components/line-title.vue";

export default {
    name: '',
    components: {LineTitle},
    data() {
        return {
            dialogFlag: false,                 // 是否开启弹窗
            tagList: [],                       // 标签筛选
            consumerList:[],                   //消费者基础信息筛选
            postnList: []                     //职位类型
        }
    },
    props: {
        scheduleId: {required: true},
        customersItem: {required: true}
    },
    async created() {
        await this.init();
    },
    methods: {
        /**
         * @Description: 初始化页面数据
         * @Author: 胡益阳
         * @Date: 2025/1/7
        */
        async init () {
            await Promise.all([
                this.initTags(),
                this.initConsumer()
            ])
        },
        /**
         * @Description: 打开弹窗
         * @Author: 胡益阳
         * @Date: 2025/1/7
        */
        openDialog () {
            this.dialogFlag = true
        },
        /**
         * @Description: 关闭弹窗
         * @Author: 胡益阳
         * @Date: 2025/1/7
        */
        closeDialog () {
            this.dialogFlag = false
        },
        /**
         * @description: 初始化tag
         * @author: zhaoxinyi
         **/
        async initTags(){
            try{
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerSchedule/queryConsumerScheDuleTags',
                    filtersRaw: [{property: "status", operator: "=", value: 'Y'}],
                    businessType: 'schedule',
                    businessId: '' || this.scheduleId
                });
                if(data.success) {
                    this.tagList = data.rows;
                } else {
                    this.$message.warn('查询标签筛选信息异常，请联系管理员', data.message);
                }
            } catch(e) {
                this.$message.error('查询标签筛选信息异常，请联系管理员', e);
            }
        },
        /**
         * @description: 初始化消费者筛选
         * @author: zhaoxinyi
         **/
        async initConsumer(){
            try{
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerSchedule/queryConsumerScheDuleAcct',
                    filtersRaw: [{property: "status", operator: "=", value: 'Y'}],
                    ruleType: 'schedule',
                    businessId: this.scheduleId
                });
                if (data.success) {
                    this.consumerList = data.rows;
                    this.consumerList.forEach(item => {
                        if (item.fieldType == 'Lov') {
                            if(!!item.judgeContent) {
                                item.judgeContentList = item.judgeContent.split(",")
                            }
                            if(!!item.positionType) {
                                item.positionTypeList = item.positionType.split(",")
                            }
                        }
                    })
                } else {
                    this.$message.warn('查询消费者基础信息异常，请联系管理员', data.message);
                }
            } catch(e) {
                console.log('查询消费者基础信息异常', e)
                this.$message.error('查询消费者基础信息异常，请联系管理员', e);
            }
        },
    }
}
</script>

<style lang="scss">
.detail-threshold {
    margin-top: 30px;
    width: 100%;
    display: flex;
    justify-content: center;
    .cell {
        box-sizing: border-box;
        width: 94%;
        border-radius: 16px;
        padding: 40px 28px;
        background-color: #ffffff;
        display: flex;
        justify-content: space-between;
        .cell-title {
            font-weight: bold;
            font-size: 28px;
        }
        .cell-value {
            font-size: 28px;
            color: #6D96FA;
        }
    }
    .consumer-threshold {
        width: 100%;
        .consumer-threshold-content {
            height: 50vh;
            overflow: auto;
            .schedule-tag-consumer-text {
                margin: 20px 0;
            }
            .no-data {
                text-align: center;
                color: #cecece;
                margin-top: 32px;
                font-size: 24px;
                padding-bottom: 32px;
            }
        }
        .button {
            display: flex;
            justify-content: center;
            .link-button {
                width: 200px;
                height: 60px;
                font-size: 28px;
            }
        }
    }
}
</style>
