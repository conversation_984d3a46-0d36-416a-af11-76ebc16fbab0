<!--
执行情况-目标-待提交目标排期计划
<AUTHOR>
@date 2025-01-08
@file implement-target-submit.vue
-->
<template>
    <view class="implement-target-submit" v-show="submitTabFlag">
        <line-title title="待提交目标排期计划" v-if="showflag"></line-title>
        <view class="zero-view" v-if="showflag"></view>
        <link-auto-list :option="targetList" hideCreateButton :key="6" v-if="showflag">
            <template slot-scope="{data,index}">
                <link-swipe-action>
                    <item :key="index" :data="data" :arrow="false"
                          style="padding: 14px 14px 2px 14px;"
                          class="overview-item">
                        <view slot="note">
                            <view class="account-label" style="background: #8e97a6">
                                <view class="label" v-if="['New', 'Invalid'].includes(data.planDeStatus)">{{'待提交-'}}{{ data.planDeStatus | lov('SCHE_PLAN_STATUS') }}</view>
                                <view class="label" v-else>{{'待提交-变更'}}</view>
                            </view>
                            <view class="item-input" style="margin-top: 25px">
                                <view class="label">{{ '目标大类' }}</view>
                                <view class="text">{{ data.planScheType | lov('SCHE_TYPE') }}</view>
                            </view>
                            <view class="item-input" v-if="!!data.planScheChild">
                                <view class="label">{{ '目标小类' }}</view>
                                <view class="text">{{ data.planScheChild | lov('SHCE_CHILD_TYPE') }}</view>
                            </view>
                            <!--lzlj-zhyx06-10693 隐藏动作的预估执行时间-->
                            <!--                            <view class="item-input">-->
                            <!--                                <view class="label">{{ '预估执行时间' }}</view>-->
                            <!--                                <view class="text">{{ data.beginTime ? (data.beginTime).substr(0, 10) : '' }} - -->
                            <!--                                    {{ data.endTime ? (data.endTime).substr(0, 10) : '' }}-->
                            <!--                                </view>-->
                            <!--                            </view>-->
                            <view class="item-input">
                                <view class="label">{{ '目标值' }}</view>
                                <view class="text">{{ data.planNum }}</view>
                            </view>
                            <view class="item-input" style="flex-direction: column" v-if="!!data.planDesc">
                                <view class="label">目标计划说明</view>
                                <link-textarea disabled placeholder="请输入目标计划说明，最多输入100字" style="padding: 0;margin-top: 10px"
                                               v-model="data.planDesc" :nativeProps="{maxlength:100}"></link-textarea>
                            </view>
                        </view>
                    </item>
                    <!-- isAddApprove——N且审核状态不为添加审核中可编辑删除    其他审核状态都不能编辑/删除 -->
                    <!-- isAddApprove——Y且审核状态为空/添加审核拒绝，不可以编辑，可以删除   其他审核状态都不能编辑/删除 -->
                    <!--排期计划审核状态planStatus处于新建审核中、变更审核中、作废审核中的排期计划不展示任何按钮-->
                    <!--除了作废，其他的状态都展示修改按钮-->
                    <link-swipe-option slot="option" color="primary"
                                       v-if="data.planDeStatus !== 'Invalid'
                                       && isPrincipal
                                       && consumerTimeFlag
                                       && !['InsertRevering', 'UpdateRevering', 'InvalidRevering'].includes(data.planStatus)"
                                       @tap="editScheduling(data)">修改
                    </link-swipe-option>
                    <link-swipe-option slot="option" color="error"
                                       v-if="data.planStatus === 'InsertReject'
                                             && isPrincipal
                                             && consumerTimeFlag
                                             && !['InsertRevering', 'UpdateRevering', 'InvalidRevering'].includes(data.planStatus)"
                                       @tap="inValidScheduling(data)">作废
                    </link-swipe-option>
                    <link-swipe-option slot="option" color="error"
                                       v-if="isPrincipal
                                       && consumerTimeFlag
                                       && !['InsertRevering', 'UpdateRevering', 'InvalidRevering'].includes(data.planStatus)
                                       && (!!data.planDeStatus && !['New', 'Invalid'].includes(data.planDeStatus))"
                                       @tap="recallScheduling(data)">撤回变更
                    </link-swipe-option>
                    <link-swipe-option slot="option" color="error"
                                       v-if="isPrincipal
                                       && consumerTimeFlag
                                       && !['InsertRevering', 'UpdateRevering', 'InvalidRevering'].includes(data.planStatus)
                                       && (!!data.planDeStatus && data.planDeStatus === 'Invalid')"
                                       @tap="recallScheduling(data)">撤回作废
                    </link-swipe-option>
                    <link-swipe-option slot="option" color="error"
                                       v-if="data.planDeStatus === 'New'
                                       && isPrincipal
                                       && consumerTimeFlag
                                       && !['InsertRevering', 'UpdateRevering', 'InvalidRevering'].includes(data.planStatus)"
                                       @tap="deleteScheduling(data)">删除
                    </link-swipe-option>
                </link-swipe-action>
            </template>
        </link-auto-list>
    </view>
</template>

<script>
import LineTitle from "../../../lzlj/components/line-title.vue";

export default {
    name: 'implement-target-submit',
    components: {LineTitle},
    props: {
        scheduleItem: {
            type: Object,
            default: () => {
                return {};
            }
        },
        oauth: {
            type: String,
            default: ''
        },
        headData: {
            type: Object,
            default: () => {
                return {};
            }
        },
        isPrincipal: {
            type: Boolean,
            default: false
        },
        consumerTimeFlag: {
            type: Boolean,
            default: false
        }
    },
    data() {
        const targetList = new this.AutoList(this, {
            url: {
                queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/send'
            },
            // loadOnStart: false,
            param: {
                dmpUrl:  '/link/consumerSchedulePlan/queryApprovalPlan',
                consumerScheduleId: this.scheduleItem.id,
                scheduleId: this.scheduleItem.scheduleId,
                // planStatus: 'Y',
                rows: 20,
                scheRuleType: 'ScheTarget'
            },
            hooks: {
                beforeLoad(option) {
                    delete option.param.filtersRaw;
                    delete option.param.sort;
                    delete option.param.order;
                },
                afterLoad: async (data) => {
                    this.targetListNum = data.rows.length;
                    this.$emit('updateTargetListNum', this.targetListNum);
                    if(data.rows.length === 0) {
                        this.showflag = false;
                    } else {
                        this.showflag = true;
                        this.submitTabFlag = true;
                    }
                }
            },
        });
        return {
            targetList,
            targetListNum: null,
            showflag: true, // 是否显示
            submitTabFlag: false
        }
    },
    created() {

    },
    methods: {
        /**
         * @desc 重新加载
         * <AUTHOR>
         * @date 2025-01-10
         **/
        reloadTargetList() {
            this.targetList.methods.reload();
        },
        /**
         * @desc 撤回排期
         * <AUTHOR>
         * @date 2025-01-09
         **/
        async recallScheduling(data) {
            const res = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                dmpUrl: '/link/consumerSchedulePlan/cancelStorage',
                id: data.id
            });
            if (res.success) {
                this.$message.success('撤回排期成功');
                this.targetList.methods.reload();
                // 刷新父组件<implement-target-option-none> ref="targetList2"的数据
                this.$emit('reloadTargetList2');
            } else {
                this.$utils.hideLoading();
                this.$message.warn('撤回排期失败', res.message);
            }
        },
        /**
         * @desc 作废排期计划
         * <AUTHOR>
         * @date 2024-08-05
         **/
        async inValidScheduling(data) {
            try {
                const res = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerSchedulePlan/cancel',
                    id: data.id
                });
                if (res.success) {
                    this.$message.success('保存成功，若需审批，提交审批并通过后作废排期计划');
                    this.targetList.methods.reload();
                    this.$emit('reloadTargetInvalid');
                } else {
                    this.$utils.hideLoading();
                    this.$message.warn('作废运营排期失败', res.message);
                }
            } catch (e) {
                console.log('e', e);
                this.$utils.hideLoading();
                // this.$message.error('作废运营排期异常，请联系管理员', e);
            }
        },
        /**
         * @desc 修改排期
         * <AUTHOR>
         * @date 2025-01-09
         **/
        async editScheduling(data) {
            this.$emit('editScheduling', data);
        },
        /**
         * @desc 删除排期
         * <AUTHOR>
         * @date 2024-08-05
         **/
        async deleteScheduling(data) {
            try {
                const res = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerSchedulePlan/delete',
                    id: data.id
                });
                if (res.success) {
                    this.$message.success('删除运营排期成功');
                    this.targetList.methods.reload();
                } else {
                    this.$utils.hideLoading();
                    this.$message.warn('删除运营排期失败', res.message);
                }
            } catch (e) {
                console.log('e', e);
                this.$utils.hideLoading();
            }
        }
    }
}
</script>

<style lang="scss">
@import './common-styles.scss';
.implement-target-submit {
}
</style>
