.zero-view {
    width: 100%;
    height: 30px;
}
.detail-list-item {
    background: #FFFFFF;
    margin: 24px;
    border-radius: 16px;
    overflow: hidden;
    position: relative;
    display: flex;
    justify-content: space-between;

    .detail-item {
        margin: 24px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        width: 65%;
        overflow-x: auto;

        .detail-item-info {
            display: flex;
            flex-direction: column;

            .detail-item-info-item {
                height: 44px;
                display: flex;
                align-items: center;
                margin-bottom: 8px;

                .label {
                    min-width: 112px;
                    margin-right: 24px;
                    font-size: 28px;
                    color: #999999;
                    line-height: 44px;
                    font-weight: 400;
                }

                .label-1 {
                    min-width: 140px;
                    margin-right: 24px;
                    font-size: 28px;
                    color: #999999;
                    line-height: 44px;
                    font-weight: 400;
                }

                .label-2 {
                    min-width: 260px;
                    margin-right: 24px;
                    font-size: 28px;
                    color: #999999;
                    line-height: 44px;
                    font-weight: 400;
                }

                .detail-item-info-text {
                    font-size: 28px;
                    color: #333333;
                    line-height: 44px;
                    font-weight: 400;
                    flex-shrink: 0;
                    //white-space: nowrap;
                }

                .detail-item-info-phone {
                    font-size: 28px;
                    color: #317DF7;
                    line-height: 44px;
                    font-weight: 400;
                    flex-shrink: 0;
                    //white-space: nowrap;
                }
            }
        }
    }

    .tag-button {
        width: 35%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: sticky; /* 使按钮始终可见 */
        right: 0; /* 将按钮固定在右侧 */
        flex-direction: column;

        .tag-item {
            display: flex;
            justify-content: center;
            align-items: center;
            background: #eaefff;
            color: #546ee2;
            font-size: 22px;
            border-radius: 12px;
            margin: 12px;
            padding: 8px 12px;
        }
    }
}

.overview-item {
    overflow: hidden;
    margin: 24px 12px;
    border-radius: 16px;
    background: white;
    font-family: PingFangSC-Regular;

    .item-input {
        line-height: 30px;
        margin-bottom: 30px;
        width: 100%;
        display: flex;

        .icon-box {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background-color: #2f69f8;
        }

        .cell-box {
            width: 100%;
            display: flex;
            align-items: center;
            .cell-icon {
                width: 72px;
                height: 72px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 26px;
                background: linear-gradient(to bottom, #80C0FF, #2F68EB);
                color: #FFFFFF;
            }
            .cell-title {
                max-width: 150px;
                margin-left: 16px;
                padding: 5px 12px;
                border-radius: 4px;
                font-size: 24px;
                color: #6A6D75;
                background-color: #eff2f6;
                //    超出隐藏
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            //.cell-desc {
            //    margin-left: 16px;
            //    font-size: 26px;
            //    color: #6A6D75;
            //    max-width: 110px;
            //    overflow: hidden;
            //    text-overflow: ellipsis;
            //    white-space: nowrap;
            //}
            .cell-time {
                color: #9EA1AE;
                font-size: 24px;
                margin: 0 10px;
                max-width: 266px;
                // 超出长度滑动展示
                flex-shrink: 0;
                overflow: auto;
                white-space: nowrap; /* 防止换行 */
            }
            .cell-status {
                display: flex;
                align-items: center;
                padding: 10px;
                background-color: #20c1bd;
                border-radius: 18px;
                color: #FFFFFF;
                font-size: 20px;
                // 超出长度滑动展示
                flex-shrink: 0;
                overflow: auto;
                white-space: nowrap; /* 防止换行 */
                //放在父盒子的最右边，向右对齐
                margin-left: auto;
                .dot {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    background-color: #FFFFFF;
                    margin-right: 4px;
                }
            }
        }
        .cell-label {
            padding: 20px 0 20px 90px;
            font-size: 28px;
            color: #212223;
        }

        .label {
            align-self: flex-start;
            color: #8c8c8c;
            font-size: 28px;
            display: flex;
            align-items: center;
            box-sizing: border-box;
            width: 300px;
        }

        .text {
            color: #262626;
            width: 420px;
            font-size: 28px;
            text-align: right;
        }
    }
}
.account-label {
    position: absolute;
    margin-right: 32px;
    right: -38px;
    top: 0;
    border-radius: 6px;
    min-width: 180px;
    height: 48px;
    transform: skew(30deg, 0deg);

    .label {
        font-size: 24px;
        color: #FFFFFF;
        text-align: center;
        line-height: 48px;
        font-weight: 400;
        transform: skew(-30deg, 0);
    }

}
.link-auto-list .link-auto-list-no-more .link-auto-list-no-data > image, .link-auto-list .link-auto-list-loading-more .link-auto-list-no-data > image {
    width: 0px !important;
    height: 0px !important;
    //border-radius: 750rpx;
    display: none;
    margin-bottom: 24rpx;
}

.main-content {
    display: flex;
    flex-direction: column;
    margin: 16px;
    max-width: calc(100% - 32px);
}


.link-swipe-action {
    width: 100% !important;
}

/*deep*/
.link-swipe-option-container .link-swipe-option {
    width: 60px;
    height: 80% !important;
    border-radius: 30px;
    font-size: 24px !important;
    margin: 60px 10px;
}

.link-dialog-foot-custom {
    width: auto !important;
}

.end-scheduling {
    width: 100vw;
    bottom: 80px;
    margin: 0 auto;
    position: fixed;
    display: flex;
    justify-content: center;
}
.planDesc {
    display: flex;
    flex-direction: column;
    width: calc(100% - 64px);
    padding: 16px 32px;
    background-color: #fff;

    .planDesc-title {
        font-size: 28px;
        margin-bottom: 10px;
        text-align: left;
        color: #333333;
    }

    .comments {
        width: 90%;
    }
}
