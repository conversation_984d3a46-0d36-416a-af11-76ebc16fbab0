<!--
详情-排期动作/目标规划
<AUTHOR>
@date 2025-01-07
@file detail-planning.vue
-->
<template>
    <view class="detail-planning">
        <view class="schedule-detail-type" :style="customersItem.scheActionType !== 'ActionNone' || customersItem.scheTargetType !== 'TargetNone' ? { padding: '20px 14px;' } : {}">
            <!-- 运营明细-动作 -->
            <view v-if="customersItem.scheActionType !== 'ActionNone'">
                <view class="schedule-title">排期动作规划</view>
                <view class="schedule-detail-form-wrapper">
                    <view class="schedule-detail-form">
                        <view class="form-title">
                            <view class="customer"> 动作大类</view>
                            <view class="customer"> 动作小类</view>
                            <view class="customer"> 目标值</view>
                            <view class="customer"> 说明</view>
                        </view>
                        <view class="form-content" v-for="(item, index) in actionInfoData" :key="index">
                            <view class="customer"> {{ item.scheduleType | lov('SCHE_TYPE') }}</view>
                            <view class="customer"> {{ item.scheduleChildType | lov('SHCE_CHILD_TYPE') }}</view>
                            <view class="customer"> {{ item.targetNum }}</view>
                            <view class="customer" v-if="item.scheduleDeDesc" style="color: #2F69F8;"
                                  @tap="gotoScheduleDeDesc(item.scheduleDeDesc ? item.scheduleDeDesc : '')"> {{
                                    '查看'
                                }}
                            </view>
                            <view class="customer" v-else> {{ '暂无说明' }}</view>
                        </view>
                    </view>
                </view>
                <!-- 点击加载更多 -->
                <view class="load-more" v-if="$utils.isNotEmpty(actionInfoData)" @tap="loadMore1()">
                    {{ loadFinish1 ? ' ———— 加载完成 ———— ' : '点击加载更多' }}
                </view>
                <!-- 暂无数据 -->
                <view class="no-data" v-else> ———— 暂无数据 ————</view>
                <view class="zero-view"></view>
            </view>

            <!-- 运营明细-目标 -->
            <view v-if="customersItem.scheTargetType !== 'TargetNone'">
                <view class="schedule-title">排期目标规划</view>
                <view class="schedule-detail-form-wrapper">
                    <view class="schedule-detail-form">
                        <view class="form-title">
                            <view class="customer"> 目标大类</view>
                            <view class="customer"> 目标小类</view>
                            <view class="customer"> 目标值</view>
                            <view class="customer"> 说明</view>
                        </view>
                        <view class="form-content" v-for="(item, index) in targetInfoData" :key="index">
                            <view class="customer"> {{ item.scheduleType | lov('SCHE_TYPE') }}</view>
                            <view class="customer"> {{ item.scheduleChildType | lov('SHCE_CHILD_TYPE') }}</view>
                            <view class="customer"> {{ item.targetNum }}</view>
                            <view class="customer" v-if="item.scheduleDeDesc" style="color: #2F69F8;"
                                  @tap="gotoScheduleDeDesc(item.scheduleDeDesc ? item.scheduleDeDesc : '')"> {{
                                    '查看'
                                }}
                            </view>
                            <view class="customer" v-else> {{ '暂无说明' }}</view>
                        </view>
                    </view>
                </view>
                <!-- 点击加载更多 -->
                <view class="load-more" v-if="$utils.isNotEmpty(targetInfoData)" @tap="loadMore2()">
                    {{ loadFinish2 ? ' ———— 加载完成 ———— ' : '点击加载更多' }}
                </view>
                <!-- 暂无数据 -->
                <view class="no-data" v-else> ———— 暂无数据 ————</view>
                <view class="zero-view"></view>
            </view>
        </view>
        <link-dialog ref="ScheduleDeDescFlag" v-model="ScheduleDeDescFlag" title="排期动作规划说明" width="80%">
            <view class="schedule-detail-desc">
                <view class="desc-text">{{ tempScheduleDesc }}</view>
                <view class="desc-button">
                    <link-button @tap="closeDialog">确定</link-button>
                </view>
            </view>
        </link-dialog>
    </view>
</template>

<script>
export default {
    name: 'schedule-detail-type',
    props: {
        customersItem: {
            type: Object,
            default: () => {
                return {}
            }
        },
        scheduleId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            pagingPram1: {       // 动作明细-分页参数
                page: 1,
                rows: 5
            },
            pagingPram2: {       // 目标明细-分页参数
                page: 1,
                rows: 5
            },
            actionInfoData: [],  // 排期动作规划
            targetInfoData: [],  // 排期目标规划
            loadFinish1: false,  // 动作-分页数据是否已经请求完
            loadFinish2: false,  // 动作-分页数据是否已经请求完
            isDebouncing1: false, // 动作-跟踪是否正在防抖
            isDebouncing2: false, // 动作-跟踪是否正在防抖
            tempScheduleDesc: '',  // 运营明细说明
            ScheduleDeDescFlag: false,  // 运营明细弹窗
        }
    },
    async created() {
        // 查动作/目标明细
        await this.initActionCustomer();
        await this.initTargetCustomer();
    },
    methods: {
        /**
         * @desc 初始化-动作运营明细
         * <AUTHOR>
         * @date 2024-09-29
         **/
        async initActionCustomer() {
            try{
                const data = await this.$http.post( this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerScheduleDetail/queryByExamplePage',
                    filtersRaw: [
                        {id: 'scheduleId', property: 'scheduleId', value: this.scheduleId, operator: '='},
                        {id: 'scheduleDeStatus', property: 'scheduleDeStatus', value: 'Y', operator: '='}
                    ],
                    scheRuleType: 'ScheAction',
                    rows: 5,
                    page: this.pagingPram1.page
                });
                if(data.success) {
                    const actionInfoData = data.rows;
                    // 加载更多
                    if(actionInfoData.length > 0 && this.pagingPram1.page > 1) {
                        this.actionInfoData = this.actionInfoData.concat(actionInfoData);
                    } else {
                        // 第一次加载/筛选条件变化
                        this.actionInfoData = actionInfoData;
                    }
                    this.loadFinish1 = this.pagingPram1.rows * this.pagingPram1.page >= data.total;
                } else {
                    this.$message.warn('查询运营明细异常，请联系管理员', data.message);
                }
            } catch(e) {
                this.$message.error('查询运营明细异常，请联系管理员', e);
            }
        },
        /**
         * @desc 初始化-目标运营明细
         * <AUTHOR>
         * @date 2024-09-29
         **/
        async initTargetCustomer() {
            try{
                const data = await this.$http.post( this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerScheduleDetail/queryByExamplePage',
                    filtersRaw: [
                        {id: 'scheduleId', property: 'scheduleId', value: this.scheduleId, operator: '='},
                        {id: 'scheduleDeStatus', property: 'scheduleDeStatus', value: 'Y', operator: '='}
                    ],
                    scheRuleType: 'ScheTarget',
                    rows: 5,
                    page: this.pagingPram2.page
                });
                if(data.success) {
                    const targetInfoData = data.rows;
                    // 加载更多
                    if(targetInfoData.length > 0 && this.pagingPram2.page > 1) {
                        this.targetInfoData = this.targetInfoData.concat(targetInfoData);
                    } else {
                        // 第一次加载/筛选条件变化
                        this.targetInfoData = targetInfoData;
                    }
                    this.loadFinish2 = this.pagingPram2.rows * this.pagingPram2.page >= data.total;
                } else {
                    this.$message.warn('查询运营明细异常，请联系管理员', data.message);
                }
            } catch(e) {
                this.$message.error('查询运营明细异常，请联系管理员', e);
            }
        },
        /**
         * @desc 动作-总览加载更多
         * <AUTHOR>
         * @date 2024-06-20
         **/
        async loadMore1 () {
            if (this.isDebouncing1 || this.loadFinish1) return;
            this.isDebouncing1 = true;
            this.pagingPram1.page++;
            await this.initActionCustomer();
            setTimeout(() => {
                this.isDebouncing1 = false;
            }, 2000); // 2000毫秒后，允许再次触发 loadMore 函数
        },
        /**
         * @desc 目标-总览加载更多
         * <AUTHOR>
         * @date 2024-06-20
         **/
        async loadMore2 () {
            if (this.isDebouncing2 || this.loadFinish2) return;
            this.isDebouncing2 = true;
            this.pagingPram2.page++;
            await this.initTargetCustomer();
            setTimeout(() => {
                this.isDebouncing2 = false;
            }, 2000); // 2000毫秒后，允许再次触发 loadMore 函数
        },
        /**
         * @desc 查询运营详情
         * <AUTHOR>
         * @date 2024-09-29
         **/
        gotoScheduleDeDesc(desc) {
            // desc = '这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明这是一段测试说明';
            this.tempScheduleDesc = desc;
            this.ScheduleDeDescFlag = true;
        },
        /**
         * @desc 查询运营详情
         * <AUTHOR>
         * @date 2024-09-29
         **/
        closeDialog() {
            this.ScheduleDeDescFlag = false;
        }
    }
}
</script>

<style lang="scss">
.detail-planning {
    width: 94%;
    margin-left: 3%;

    .zero-view {
        width: 100%;
        height: 30px;
    }

    .schedule-detail-type {
        margin: auto;
        border-radius: 16px;
        //padding: 40px 28px;
        background: white;
        font-family: PingFangSC-Regular;

        .schedule-title {
            color: #262626;
            font-size: 28px;
            margin-bottom: 20px;
            font-weight: bold;

        }

        .schedule-detail-form-wrapper {
            overflow-x: auto;
        }

        .schedule-detail-form {
            display: flex;
            flex-direction: column;
            width: 120%;
            padding-bottom: 4px;
        }

        .form-title, .form-content {
            display: flex;
            justify-content: space-between;
            height: 64px;
            line-height: 64px;
            border-top: 1px solid #e8e8e8;
            border-left: 1px solid #e8e8e8;
            border-bottom: 1px solid #e8e8e8;
            width: 120%;

            .customer {
                color: #262626;
                font-size: 28px;
                width: 30%;
                text-align: center;
                border-right: 1px solid #e8e8e8;
                flex-shrink: 0;
                overflow: auto;
                white-space: nowrap; /* 防止换行 */
                .link-button.link-button-size-normal.link-button-size-normal {
                    font-size: 26px;
                }

                .link-button.link-button-size-normal {
                    height: 22PX;
                }

                .link-button.link-button-block {
                    display: -webkit-flex;
                    display: -ms-flexbox;
                    display: flex;
                    margin: 12px 32px;
                }
            }
        }

        .form-title .customer, .form-content .customer {
            flex: 1; /* Ensure columns have the same width */
        }

        .load-more {
            //width: 90%;
            text-align: center;
            color: #6D96FA;
            margin-top: 32px;
            font-size: 24px;
            padding-bottom: 32px;
        }

        .no-data {
            //width: 90%;
            text-align: center;
            color: #cecece;
            margin-top: 32px;
            font-size: 24px;
            padding-bottom: 32px;
        }
    }

}
</style>
