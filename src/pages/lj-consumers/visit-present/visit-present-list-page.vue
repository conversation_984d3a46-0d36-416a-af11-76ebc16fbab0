<template>
    <link-page class="visit-present-list-page">
        <link-auto-list :option="autoList" :searchInputBinding="{props:{placeholder:'客户名称/手机号/礼赠ID'}}">
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="visit-present-list-item">
                    <view slot="note" class="item-container">
                        <view class="row-item">
                            <view class="accnt-name">{{data.consumerName}}</view>
                            <view class="mobile-phone">{{data.consumerPosition}}</view>
                        </view>
                        <view class="sub-row-item">
                            <view class="present-info">
                                <view class="company">创建人: {{data.createdName}}</view>
                                <view class="present-id" @tap="copyData(data.id)">礼赠ID: {{data.id}}</view>
                            </view>
                            <view class="sub-row-item-info">
                                <view class="status"
                                      :class="{'status-new':data.status === 'New','status-registered':data.status === 'Registered','status-inactive':data.status === 'Inactive'}">
                                    {{data.status | lov('VISIT_PRESENT_STATUS')}}
                                </view>
                                <view class="status">
                                    {{data.preType | lov('PRE_TYPE')}}
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
import {ROW_STATUS} from "../../../utils/constant";
import ConsumerListCommon from "../consumer-list-common";
export default {
    name: "visit-present-list-page",
    mixins: [ConsumerListCommon()],
    data() {
        const autoList = new this.AutoList(this, {
            module: this.$env.appURL + "/action/link/present",
            createPath: '/pages/lj-consumers/visit-present/visit-present-item-page',
            itemPath: '/pages/lj-consumers/visit-present/visit-present-item-page',
            param: {
                oauth: this.pageOauth,
                filtersRaw: [],
            },
            loadOnStart: false,
            hooks: {
                async beforeCreateItem(param) {
                    const id = await this.$newId();
                    param.data.id = id;
                    param.source = 'presentList'
                },
                async beforeGotoItem(param) {
                    param.source = 'presentList'
                    if (param.data.status !== 'Registered' && param.data.status !== 'Inactive') {
                        param.operator = ROW_STATUS.UPDATE;
                    } else {
                        param.operator = 'READ';
                    }
                }
            },
            searchFields: ['consumerName', 'consumerPhone', 'id'],
            sortOptions: null,
            filterOption: [
                {label: '礼赠状态', field: 'status', type: 'lov', lov: 'VISIT_PRESENT_STATUS'},
                {label: '礼赠类型', field: 'preType', type: 'lov', lov: 'PRE_TYPE'}
            ],
            slots: {
                searchRight: () => (
                    <view class="filter-type-item" style="max-width: 224rpx;height: 72rpx;display: flex;align-items: center;justify-content: flex-end;padding-left: 30rpx;font-family: PingFangSC-Regular;font-size: 26rpx;color: #333333;line-height: 40rpx;font-weight: 400;" onTap={this.chooseOauthData}>{this.pageOauthName}<link-icon icon="mp-desc" style="color: #CCCCCC; margin: 4rpx 0 0 8rpx;"/></view>
                )
            }
        });
        return {
            autoList
        };
    },
    methods: {
        /**
         * @desc 复制ID
         * <AUTHOR>
         * @date 2021/8/13 15:38
         **/
        copyData (data) {
            wx.setClipboardData({
                data: data,
                success: function () {
                    // 添加下面的代码可以复写复制成功默认提示文本`内容已复制`
                    wx.showToast({
                        title: '复制成功',
                        duration: 3000
                    });
                    wx.getClipboardData({
                        success: function (res) {
                        }
                    })
                }
            })
        },
        onBack () {
            this.autoList.methods.reload();
        }
    },
    created () {
        if (this.pageOauthList.length > 0) {
            this.pageOauthName = this.pageOauthList[0].name;
            this.pageOauth = this.pageOauthList[0].securityMode;
        } else {
            this.pageOauthName = '我的数据';
            this.pageOauth = 'MY_POSTN_ONLY';
        }
        this.autoList.option.param.oauth = this.pageOauth;
        this.autoList.methods.reload();
    }
}
</script>

<style lang="scss">
.visit-present-list-page {
    @include flex();
    @include direction-column();
    .visit-present-list-item{
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
    }
    .item-container{
        color: #262626;
    }
    .row-item {
        @include flex-center-center();
        @include space-between();
        margin-bottom: 20px;
        width: 100%;
        font-weight: 500;
        .accnt-name {
            font-weight: 500;
        }
    }

    .sub-row-item {
        width: 100%;
        @include flex-center-center();
        @include space-between();

        .present-info {
            .company, .present-id {
                margin: 10px 0;
                display: flex;
            }
        }
        .sub-row-item-info{
            width: 30%;
        }

        .status {
            width: 100%;
            text-align: right;
            font-weight: 500;

            &-new {
                color: $main-color;
            }

            &-registered {
                color: green;
            }

            &-inactive {
                color: red;
            }
        }
    }
}
</style>
