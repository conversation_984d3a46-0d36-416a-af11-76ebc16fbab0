<template>
    <link-page class="visit-present-item-page">
        <approval-history-point :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)"></approval-history-point>
        <link-form :option="option" ref="form" hideSaveButton :value="option.formData">
            <link-form-item label="礼赠类型" field="preType" required :readonly="approvalFlag">
                <link-lov v-model="option.formData.preType"  type="PRE_TYPE" :excludeLovs="['PublicRelationPre']" @change="preTypeChange"></link-lov>
            </link-form-item>
            <link-form-item label="客户名称" required :readonly="approvalFlag">
                <link-object :option="accountOption" pageTitle="选择礼赠客户"
                             :row="option.formData"
                             :value="option.formData.consumerName"
                             :map="{consumerId:'id',consumerName:'acctName',consumerPhone:'mobilePhone1',
                             companyName:'company',companyId:'companyId',consumerPosition:'position'}"
                >
                    <template v-slot="{data}">
                        <item :arrow="false" :title="data.acctName" :note="data.company"
                              :content="data.mobilePhone1"
                              :desc="data.position" :key="data.id" :data="data"/>
                    </template>
                </link-object>
            </link-form-item>
            <link-form-item label="手机号" readonly>
                <link-input v-model="option.formData.consumerPhone"></link-input>
            </link-form-item>
            <link-form-item label="单位名称" readonly>
                <link-input v-model="option.formData.companyName"></link-input>
            </link-form-item>
            <link-form-item label="职务" readonly>
                <link-input v-model="option.formData.consumerPosition"></link-input>
            </link-form-item>
            <link-form-item label="礼赠时间" :readonly="approvalFlag">
                <link-date v-model="option.formData.preTime" view="YMDHm"
                           :max="maxDate"
                           display-format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm:ss"></link-date>
            </link-form-item>
            <link-form-item label="状态" readonly>
                <link-lov v-model="option.formData.status" type="VISIT_PRESENT_STATUS"></link-lov>
            </link-form-item>
            <link-form-item label="类型" required :readonly="approvalFlag">
                <link-select v-model="option.formData.type">
                    <link-select-option v-for="(data,index) in typeList" :label="data.name" :val="data.val" :key="index"/>
                </link-select>
            </link-form-item>
            <link-form-item label="关联活动" :readonly="approvalFlag">
                <link-object :option="activityOption"
                             :row="option.formData"
                             :value="option.formData.actName"
                             :map="{activityId:'id',actName:'activityName'}">
                    <template v-slot="{data}">
                        <item :key="data.id" :data="data" :arrow="false">
                            <view slot="note">
                                <view style="font-size: 14px;color: #262626; display: flex;justify-content: space-between;">
                                    活动编码：{{data.id}}
                                    <view style="color: #0076ff;">{{data.status | lov('MC_STATUS')}}</view>
                                </view>
                                <view style="font-size: 14px;color: #262626; display: flex;justify-content: space-between;">
                                    活动名称：{{data.activityName}}
                                </view>
                                <view style="font-size: 14px;color: #262626; display: flex;justify-content: space-between;">
                                    活动时间：{{data.startTime | date('YYYY-MM-DD HH:mm')}}至{{data.endTime | date('YYYY-MM-DD HH:mm')}}
                                </view>
                                <view style="font-size: 14px;color: #262626; display: flex;justify-content: space-between;">
                                    提报人：{{data.createdName}}
                                </view>
                            </view>
                        </item>
                    </template>
                </link-object>
            </link-form-item>
            <link-form-item label="关联拜访" :readonly="approvalFlag">
                <link-object :option="visitOption"
                             :row="option.formData"
                             :value="option.formData.visitName"
                             :map="{visitId:'id',visitName:'title'}">
                    <template v-slot="{data}">
                        <item :key="data.id" :data="data" :arrow="false">
                            <view slot="note">
                                <view style="font-size: 14px;color: #262626; display: flex;justify-content: space-between;">
                                    <view class="left">
                                        {{data.title}}
                                    </view>
                                    <view class="right" style="color: #0076ff;display: flex;flex-direction: column;align-items: flex-end;">
                                        <view>{{data.visitType | lov('VISIT_TYPE')}}</view>
                                        <view>{{data.visitStatus | lov('VISIT_STATUS')}}</view>
                                    </view>
                                </view>
                                <view style="display: flex;justify-content: space-between;">
                                    <view class="iconfont icon-style icon-time-circle" style="font-size: 14px;color: #262626;">
                                        {{data.visitTime|date('YYYY-MM-DD HH:mm', 'YYYY-MM-DD HH:mm:ss')}}
                                    </view>
                                    <view class="iconfont icon-style icon-gerenzhongxin" style="font-size: 14px;color: #262626;">
                                        {{data.visitorName}}
                                    </view>
                                </view>
                                <view style="font-size: 14px;color: #262626; display: flex;justify-content: space-between;">【拜访ID】{{data.id}}</view>
                                <view style="font-size: 14px;color: #262626; display: flex;justify-content: space-between;">【拜访单位】{{data.visitCompany}}</view>
                            </view>
                        </item>
                    </template>
                </link-object>
            </link-form-item>
            <view class="lnk-form-header">
                <title-line label-name="礼赠明细" :button-name="(option.formData.status !== 'UnderApproval' && option.formData.status !== 'Inactive' && option.formData.status !== 'Registered' && !approvalFlag)? '添加' : ''" @tap="upsertGift({})"/>
                <list>
                    <link-swipe-action v-for="(item,index) in visitGiftList.list" :key="item.id">
                        <link-swipe-option slot="option" v-if="option.formData.status !== 'Registered' && option.formData.status !== 'Inactive'" @tap="handleGiftDelete(item,index)">删除</link-swipe-option>
                        <item style="background-color: white;" :arrow="false" @tap="upsertGift(item, index)">
                            <view style="width:100%;display: flex; flex-direction:column;">
                                <view class="item" style="width: 100%">
                                    <view style="width: 100%">
                                        <view style="width: 90%;float: left;padding-left: 30rpx">{{item.prodName}}</view>
                                        <view>{{item.qty}}</view>
                                    </view>
                                </view>
                                <view class="sub-row-item">
                                    <view class="label" style="width: 30%;float: left;padding-left: 30rpx;">物流码:</view>
                                    <view class="logistic-list">
                                        <view class="logistic-code"
                                              v-for="(logistic,index) in item['logCodeList']" :key="index">
                                            {{logistic['logCode']}}
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </item>
                    </link-swipe-action>
                </list>
            </view>
            <view>
                <view class="visit-present-img" v-if="!!bussinessId">
                    <view class="title">
<!--                        <text style="color: red;font-size: 18px">*</text>-->
                        附件</view>
                    <lnk-img-watermark :parentId="bussinessId"
                                       moduleType="visitPresent"
                                       :useAlbumFlag="true"
                                       :delFlag="option.editing === true"
                                       :isZlFlag="true"
                                       :newFlag="option.editing === true"
                                       @imgUploadSuccess="imageArrLength"
                                       @imgDeleteSuccess="imageArrLength">
                    </lnk-img-watermark>
                </view>
            </view>
        </link-form>
        <link-sticky>
            <link-button block mode="stroke" v-if="
                !approvalFlag &&
                !consumerTouchFlag &&
                (option.editing !== true && option.operator !=='NEW' && option.formData.status !== 'Inactive') &&
                ((option.formData.preType === 'NormalCustomerPre' && (option.formData.status === 'New' || option.formData.status === 'Rejected' || option.formData.status === 'Registered')) ||
                (option.formData.preType === 'HighValuePre' && (option.formData.status === 'New' || option.formData.status === 'Rejected')))" @tap="handleInvalid('Inactive')">作废</link-button>
            <link-button block @tap="saveVisitPresent" v-if="option.editing === true || option.operator ==='NEW'">保存</link-button>
            <link-button block @tap="handleStatus('Registered')" v-if="option.formData.preType === 'NormalCustomerPre' && option.formData.status === 'New' && option.editing !== true">登记</link-button>
            <link-button block @tap="handleStatus('Submit')" v-if="option.formData.preType === 'HighValuePre' && (option.formData.status === 'New' || option.formData.status === 'Rejected') && option.editing !== true && !approvalFlag">提交</link-button>
        </link-sticky>
        <link-sticky>
            <approval-operator :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)"></approval-operator>
        </link-sticky>
        <view class="blank" v-if="!$utils.isEmpty(approvalId)"></view>
    </link-page>
</template>

<script>
import {ROW_STATUS} from "../../../utils/constant";
import TitleLine from "../../lzlj-II/fighting-fakes/components/title-line";
import ConsumerCommon from "../consumer-common";
import LnkImgWatermark from "../../core/lnk-img-watermark/lnk-img-watermark.vue";
import ApprovalHistoryPoint from "../../lzlj/approval/components/approval-history-point.vue";
import ApprovalOperator from "../../lzlj/approval/components/approval-operator.vue";

export default {
    name: "visit-present-item-page",
    components: {ApprovalOperator, ApprovalHistoryPoint, LnkImgWatermark, TitleLine},
    mixins: [ConsumerCommon()],
    data () {
        const userInfo = this.$taro.getStorageSync('token').result
        const config = {
            ...this.pageParam,
            data: {
                ...this.pageParam.data,
            }
        };
        let bussinessId = '';
        if(this.pageParam.source === 'presentList' || this.pageParam.source === 'consumer_touch_record') {
            bussinessId = this.pageParam.data.id;
        } else if(this.pageParam.source === 'approval') {
            bussinessId = this.pageParam.data.flowObjId;
        }
        const visitGiftList = new this.AutoList(this, {
            module: this.$env.appURL + '/link/visitProdLine',
            param: {
                rows: 25,
                filtersRaw: []
            },
            hooks: {
                beforeLoad (options) {
                    if (this.$utils.isEmpty(this.bussinessId)) return Promise.reject('查询失败，请返回重试！')
                    options.param.filtersRaw = [
                        ...options.param.filtersRaw,
                        {
                            'id': 'bussinessId',
                            'property': 'bussinessId',
                            'value': this.bussinessId,
                            'operator': '='
                        },
                    ];
                },
                afterLoad(data) {
                    data.rows.forEach((item) => {
                        item.row_status = ROW_STATUS.UPDATE;
                    })
                    this.giftItemList = data.rows;
                }
            }
        });//赠酒列表
        const option = new this.FormOption(this, {
            ...config,
            module: this.$env.appURL + "/action/link/present",
            hooks: {
                async onAfterInitNew(data) {
                    data.status = 'New';
                    option.editing = true;
                },
                async onBeforeEnableEdit(data) {
                    if(this.typeList.length === 0) {
                        this.$showError('组织未生成礼赠类型，请先去礼赠映射表维护');
                    }
                },
                async onBeforeQueryById(data) {
                    if(this.pageParam.source === 'approval') {
                        data.param.id = this.pageParam.data.flowObjId;
                    }
                },
                async onAfterQueryById (data) {
                    // 编辑按钮的显示
                    if(this.pageParam.source !== 'approval' && (data.result.preType === 'NormalCustomerPre' || (data.result.preType === 'HighValuePre' && (data.result.status === 'New' || data.result.status === 'Rejected')))) {
                        data.row_status = ROW_STATUS.UPDATE;
                        bussinessId = data.id;
                    } else {
                        option.editing = false;
                        option.operator = 'READ';
                    }
                    let sceneObj = await this.$scene.ready();//that.$store.getters['scene/getScene'];//消息场景对象
                    if (sceneObj.query['approval_from'] === 'qw') {
                        data.param.id = sceneObj.query['flowObjId'];
                        this.bussinessId = sceneObj.query['flowObjId'];
                        this.approvalId = sceneObj.query['approval_id'];
                        this.approvalFlag = this.$utils.isNotEmpty(this.approvalId);
                        option.editing = false;
                        option.operator = 'READ';
                    }
                    await visitGiftList.methods.reload();
                }
            }
        });
        const visitOption = new this.AutoList(this, {
            module: this.$env.appURL + '/action/link/visit',
            searchFields: ['title', 'visitorName', 'visitorTel', 'visitCompany'],
            filterOption: [{
                label: '拜访类型', field: 'visitType', type: 'lov', lov: 'VISIT_TYPE'
            }, {
                label: '拜访状态', field: 'visitStatus', type: 'lov', lov: 'VISIT_STATUS'
            }, {
                label: '拜访时间', field: 'visitTime', type: 'date'
            }],
            sortOptions: null,
            param: {
                oauth: 'MY_POSTN',
                type: 'VisitRecord',
                attr3: '',
                filtersRaw: [
                    {'id': 'visitStatus', 'property': 'visitStatus', 'value': 'Registered', 'operator': '='},
                    {'id': 'visitType', 'property': 'visitType', 'value': 'NormalCustomerVisit', 'operator': '='}]
            },
            hooks: {
                beforeLoad(option) {
                    if (option.param.filtersRaw.length >0 && option.param.filtersRaw[0].id === 'searchValue_0') {
                        option.param.attr3 = option.param.filtersRaw[0].value;
                        option.param.filtersRaw[0].value = '';
                    }

                }
            }
        });
        return {
            bussinessId,
            userInfo,
            option,
            giftItemList: [],
            maxDate: this.$date.format(new Date(), 'YYYY-MM-DD HH:mm:ss'),
            visitOption,
            visitGiftList,
            activityOption: new this.AutoList(this, {
                url: {
                    queryByExamplePage: this.$env.appURL + '/marketactivity/link/marketActivity/queryMarketActivityByAcctIdPage'
                },
                searchFields: ['activityName'],
                param: {
                    page: 25,
                    attr1: 'Y',
                    filtersRaw: [{id: 'status', property: 'status', value: '[New, PublishFailed, Publishing, Inactive]', operator: 'NOT IN'}]
                },
                hooks: {
                    beforeLoad(option) {
                        option.param.consumerId = this.option.formData.consumerId;
                        option.param.attr2 = this.option.formData.companyId;
                        if (this.$utils.isEmpty(option.param.consumerId) && this.$utils.isEmpty(this.option.formData.consumerId)) {
                            option.param.consumerId = this.option.formData.consumerId;
                        }
                        if (this.$utils.isEmpty(option.param.attr2) && this.$utils.isEmpty(this.option.formData.companyId)) {
                            option.param.attr2 = this.option.formData.companyId;
                        }
                        if (this.$utils.isEmpty(this.option.formData.consumerId)) {
                            const msg = '请先选择客户再选择关联活动';
                            this.$message.warn(msg);
                            return Promise.reject(msg);
                        }
                    }
                }
            }),
            formRules: [],
            typeList: [],
            imgLength: 0,   // 附件图片数量
            approvalId: null,
            approvalFlag: false,
            consumerTouchFlag: this.pageParam.source === 'consumer_touch_record'
        }
    },
    watch: {
        'option.formData.preType': {
            immediate: true,
            handler: function (val) {
                if (val === 'HighValuePre') {
                    this.accountOption.option.param.filtersRaw = this.accountOption.option.param.filtersRaw.concat([{"id":"identityId","property":"identityId","operator":"not null","value":""}]);
                } else {
                    this.accountOption.option.param.filtersRaw  = this.accountOption.option.param.filtersRaw.filter(item => item.property !== 'identityId');
                    this.filtersRawTemp = this.filtersRawTemp.filter(item => item.property !== 'identityId');
                }
            }
        }
    },
    mounted() {
        // 新建时preType默认赋值为普通用户
        if (this.$utils.isEmpty(this.option.formData.preType)) {
            this.$set(this.option.formData, 'preType', 'NormalCustomerPre')
        }
    },
    async created () {
        await this.queryTypeLov();
        let sceneObj = await this.$scene.ready();//that.$store.getters['scene/getScene'];//消息场景对象
        let code = this.pageParam.source; // 页面来源
        const approval_from = sceneObj.query['approval_from'];
        // 从消费者详情来的
        if(code === 'consumer_touch_record') {
            this.option.editing = false;
            this.option.operator = 'READ';
        }
        // 从审批来的
        if(code === 'approval') {
            this.approvalId = this.pageParam.data.id; // 审批传过来的审批数据ID
            this.approvalFlag = this.$utils.isNotEmpty(this.approvalId)
            this.bussinessId = this.pageParam.data.flowObjId;
            this.option.editing = false;
        }
        // 从卡片来的
        if(approval_from === 'qw') {
            this.approvalId = sceneObj.query['approval_id'];
            this.approvalFlag = this.$utils.isNotEmpty(this.approvalId)
            this.bussinessId = sceneObj.query['flowObjId'];
            if(this.$utils.isNotEmpty( this.bussinessId)){
                await this.queryItemById()
                await this.visitGiftList.methods.reload();
            }else{
                this.$utils.showAlert('请联系管理员，未获取到礼赠审批信息！', {icon: 'none'});
                return
            }
        }
    },
    methods: {
        async queryItemById() {
            const data = await this.$http.post(this.$env.appURL + '/action/link/present/queryById', {
                id: this.bussinessId
            }, {
                autoHandleError: false,
                handleFailed: (data) => {
                    if (!data.success) {
                        this.$showError('获取礼赠信息出错！' + data.result);
                    }
                }
            });
            if (data.success) {
                this.option.formData = data.result;
                this.option.editing = false;
                this.option.operator = 'READ';
            }
        },
        /**
         * 获取附件图片长度
         * <AUTHOR>
         * @date 2024-05-22
         * @param param
         */
        async  imageArrLength(param) {
            if(!this.$utils.isEmpty(param)){
                this.imgLength = param.length;
            }
        },
        /**
         * @desc 切换礼赠类型清空消费者信息
         * <AUTHOR>
         * @date 2024-05-20
         **/
        async preTypeChange() {
            this.$set(this.option.formData, 'consumerId', '');
            this.$set(this.option.formData, 'consumerPhone', '');
            this.$set(this.option.formData, 'consumerName', '');
            this.$set(this.option.formData, 'companyName', '');
            this.$set(this.option.formData, 'companyId', '');
            this.$set(this.option.formData, 'consumerPosition', '');
        },
        /**
         * @desc 查询类型字段type的可选值
         * <AUTHOR>
         * @date 2023/02/01 11:07
         **/
        async queryTypeLov() {
            // const orgId = this.$taro.getStorageSync('token').result.orgId
            // const filter = [{id: 'orgId', property: 'orgId', value: orgId, operator: '='}]
            await Promise.all([
                this.$http.post(this.$env.appURL + '/link/presentMap/queryParentOrgMapPage',
                    {
                        pageFlag: true,
                        rows: 5,
                        page: 1,
                        order: 'desc',
                        sort: 'created',
                        // filtersRaw: filter
                    }, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$showError('查询礼赠配置表失败' + response.result);
                        }
                    }),
                this.$lov.getLovByType('CSM_GIFT_TYPE')
            ]).then(([responseData, CSM_GIFT_TYPE]) => {
                responseData.rows.forEach(item => {
                    CSM_GIFT_TYPE.forEach(lovItem => {
                        if(item.type === lovItem.val) {
                            this.typeList.push(lovItem)
                        }
                    })
                })
            })
            if(this.typeList.length === 0 && this.option.editing) {
                this.$showError('组织未生成礼赠类型，请先去礼赠映射表维护');
            }
        },
        /**
         * @desc 状态变更
         * <AUTHOR>
         * @date 2022/5/6 11:07
         **/
        async handleStatus (status) {
            if (this.giftItemList.length <= 0) {
                this.$showError('请添加礼赠明细信息！');
                return
            }
            if(status === 'Submit') {
                this.$dialog({
                    title: '提示',
                    content: '是否要提交超高价值会员礼赠信息？',
                    cancelButton: true,
                    onConfirm: async () => {
                        await this.onSubmit();
                    },
                    onCancel: () => {}
                })
            }
            if(status === 'Registered') {
                await this.onRegistered();
            }
        },
        /**
         * @desc 登记
         * <AUTHOR>
         * @date 2024-06-06
         **/
        async onRegistered() {
            await this.saveGiftListData();
            try{
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + '/action/link/present/updateStatus', {
                    id: this.option.formData.id,
                    status: 'Registered'
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('登记失败！' + response.result);
                    }
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    setTimeout(() => {
                        this.$message.success('登记成功！');
                    }, 1000);
                    this.$nav.back();
                }
            } catch (e) {
                console.log('礼赠等级失败或捕获的错误信息', e);
                this.$utils.hideLoading();
                this.$showError('登记失败' + e.result ? e.result : e);
            }
        },
        /**
         * @desc 提交
         * <AUTHOR>
         * @date 2024-06-06
         **/
        async onSubmit() {
            await this.saveGiftListData();
            try{
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + '/action/link/present/updateStatus', {
                    id: this.option.formData.id,
                    status: 'Submit'
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('提交失败！' + response.result);
                    }
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    setTimeout(() => {
                        this.$message.success('提交成功！');
                    }, 1000);
                    this.$nav.back();
                }
            } catch (e) {
                this.$utils.hideLoading();
                this.$message.warn('提交失败' + e);
            }
        },
        /**
         * @desc 作废
         * <AUTHOR>
         * @date 2024-05-23
         **/
        async handleInvalid() {
            const data = await this.$http.post(this.$env.appURL + '/action/link/present/invalid', {
                id: this.option.formData.id,
                source: "QW"
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError('作废失败！' + response.result);
                }
            });
            if (data.success) {
                this.$utils.hideLoading();
                setTimeout(() => {
                    this.$message.success('作废成功！');
                }, 1000);
                this.$nav.back();
            }
        },
        /**
         * @desc 新增消费者
         * <AUTHOR>
         * @date 2022/3/23 10:05
         **/
        async addConsumer() {
            this.newAccountItem.id = await this.$newId();
            this.$nav.push(this.editPath, {
                data: this.newAccountItem,
                pageFrom: 'visitPresent',
                userInfo: this.userInfo,
                callback: async (data) => {
                    if (data) {
                        this.$set(this.option.formData, 'consumerId', data.id);
                        this.$set(this.option.formData, 'consumerPhone', data.phoneNumber);
                        this.$set(this.option.formData, 'consumerName', data.name);
                        this.$set(this.option.formData, 'companyName', data.companyName);
                        this.$set(this.option.formData, 'companyId', data.belongToCompanyId);
                        this.$set(this.option.formData, 'consumerPosition', data.position);
                    }
                }
            });
        },
        /**
         *  @description: 新建更新赠品信息
         *  @author: 马晓娟
         *  @date: 2020/5/29 14:20
         */
        upsertGift(item, index) {
            if(this.approvalFlag) {
                return;
            }
            const itemTemp = this.$utils.deepcopy(item);
            const that = this;
            if (this.option.formData.status === 'Registered' || this.option.formData.status === 'Inactive') return;
            if (this.$utils.isEmpty(item)) {
                item = {
                    bussinessId: this.option.formData.id,
                    accntChannelId: this.option.formData.consumerId,
                    lytRightsType: 'PresentWine',
                    row_status: ROW_STATUS.NEW
                };
            }
            this.$nav.push('/pages/lj-consumers/visit-register/visit-register-cust-gift-page', {
                item: item,
                companyId: this.option.formData.companyId,
                callback: (data) => {
                    that.bussinessId = data.bussinessId;
                    if (this.$utils.isEmpty(data.row_status)) {
                        setTimeout(()=> {
                            that.visitGiftList.methods.reload();
                        }, 500);
                        return;
                    }
                    if (data.row_status === ROW_STATUS.NEW && this.$utils.isEmpty(itemTemp)) {
                        this.giftItemList.unshift(data);
                    } else {
                        this.giftItemList.splice(index, 1, data);
                    }
                    this.visitGiftList.list = this.giftItemList;
                }
            });
        },
        /**
         * @desc 删除赠品数据
         * <AUTHOR>
         * @date 2022/5/6 10:20
         **/
        async handleGiftDelete(item, index) {
            this.$utils.showLoading();
            if (item.row_status !== ROW_STATUS.NEW) {
                const data = await this.$http.post(this.$env.appURL + '/action/link/visitProdLine/deleteById', item, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('保存礼赠数据失败！' + response.result);
                    }
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    await this.$message.primary('删除成功');
                }
            }
            this.giftItemList.splice(index, 1);
            this.visitGiftList.list = this.giftItemList;
            this.$utils.hideLoading();
        },
        /**
         * @desc 保存礼赠数据
         * <AUTHOR>
         * @date 2022/4/26 17:09
         **/
        async saveVisitPresent () {
            if (this.$utils.isEmpty(this.option.formData.consumerId)) {
                this.$message.primary('请选择客户');
                return false;
            }
            if (this.$utils.isEmpty(this.option.formData.type) && this.typeList.length === 0) {
                this.$showError('组织未生成礼赠类型，请先去礼赠映射表维护');
                return false;
            } else if(this.$utils.isEmpty(this.option.formData.type)) {
                this.$message.primary('请选择类型字段');
                return false;
            }
            if (this.option.formData.preType === 'HighValuePre' && this.imgLength === 0) {
                this.$message.warn('请上传附件');
                return false;
            }
            this.$utils.showLoading();
            this.option.formData.row_status = this.option.operator;
            const data = await this.$http.post(this.$env.appURL + '/action/link/present/upsert', this.option.formData, {
                autoHandleError: false,
                    handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError('保存礼赠数据失败！' + response.result);
                }
            });
            if (data.success) {
                this.$utils.hideLoading();
                this.option.formData = data.newRow;
                this.option.formData['row_status'] = ROW_STATUS.UPDATE;
                this.$message.success('保存礼赠数据成功！');
                await this.saveGiftListData();
                this.option.editing = false;
                this.option.operator = ROW_STATUS.UPDATE;
            }
        },
        /**
         * @desc 保存赠品数据
         * <AUTHOR>
         * @date 2022/5/6 10:01
         **/
        async saveGiftListData () {
            if (this.giftItemList.length <=0) return;
            this.$utils.showLoading();
            this.giftItemList.forEach((item)=> {
                item['bussinessId'] = this.option.formData.id;
            })
            const data = await this.$http.post(this.$env.appURL + '/action/link/visitProdLine/batchUpsert', this.giftItemList, {
                autoHandleError: false,
                    handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError('保存赠品数据失败！' + response.result);
                }
            });
            if (data.success) {
                this.$utils.hideLoading();
                this.giftItemList = [];
                this.bussinessId = this.option.formData.id;
                await this.visitGiftList.methods.reload();
                this.$message.success('保存赠品数据成功！');
            }
        }
    }
}
</script>

<style lang="scss">
.visit-present-item-page {
    .lnk-form-header {
        width: 100%;
        margin-top: 24px;
        background-color: #F2F2F2;
    }
    .blank {
        height: 200px;
        width: 100%;
    }
    .visit-present-img {
        padding-left: 32px;
        background-color: white;
        font-size: 28px;
        line-height: 88px;
        margin-bottom: 160px;
    }
    .sub-row-item {
        display: flex;
        color: #9CA5A8;
        margin-top: 10px;
    }
}
</style>
