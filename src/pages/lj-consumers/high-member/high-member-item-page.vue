<template>
    <link-page class="high-member-item-page">
        <link-form :value="formData">
            <link-form-item label="消费者姓名">{{formData.acctName}}</link-form-item>
            <link-form-item label="手机号">{{formData.mobilePhone}}</link-form-item>
            <link-form-item label="身份等级">{{formData.identityLevel|lov('ACCT_SUB_TYPE')}}</link-form-item>
            <link-form-item label="跟进业代">{{formData.followFlag === 'N' ? '' : formData.fstName}}</link-form-item>
            <link-form-item label="1V1服务人员">{{formData.servicePersonalName}}</link-form-item>
            <link-form-item label="身份有效结束时间">{{formData.endTime | date('YYYY-MM-DD HH:mm:ss')}}</link-form-item>
            <link-form-item label="身份建设来源">{{formData.fromType | lov('FROM_TYPE')}}</link-form-item>
            <link-form-item v-if="formData.fromType === 'SeedInvite'" label="邀约人员">{{formData.empName}}</link-form-item>
            <link-sticky>
                <link-button block @tap="gotoAccountItem" v-if="pageFrom!=='AccountItem'">消费者详情</link-button>
                <link-button block @tap="applyChange" v-if="formData.fromType!=='MembershipAgent' && formData.fromType !=='AuthorityAgent'">身份等级变更</link-button>
                <link-button block @tap="applyTransfer" v-if="formData.fromType!=='MembershipAgent' && formData.fromType !=='AuthorityAgent'">分配他人跟进</link-button>
            </link-sticky>
        </link-form>
        <link-dialog ref="transferDialog"
                     :noPadding="true"
                     v-model="dialogFlag"
                     height="30vh"
                     position="bottom"
                     borderRadius="32rpx 32rpx 0 0">
            <view class="model-title">
                <view class="title">请选择职位</view>
                <view class="iconfont icon-close" @tap="dialogFlag = false"></view>
            </view>
            <scroll-view scroll-y="true">
                <link-form ref="form" :value="selectedPostn">
                    <link-form-item label="职位" field="postnName">
                        <link-input v-model="selectedPostn.postnName" @tap="gotoPostnList()" placeholder="请选择职位"
                                    suffixIcon="icon-right" :readonly="Boolean('true')"> >
                        </link-input>
                    </link-form-item>
                </link-form>
            </scroll-view>
            <link-sticky>
                <link-button block @tap="confirmTransfer">确认转交</link-button>
            </link-sticky>
        </link-dialog>
        <water-mark></water-mark>
    </link-page>
</template>
<script>
import {ComponentUtils} from "link-taro-component";
import waterMark from '../../lzlj/components/water-mark';

export default {
    name: 'high-member-item-page',
    components: {waterMark},
    data () {
        const userInfo = this.$taro.getStorageSync('token').result;         // 获取用户信息
        const pageFrom = this.pageParam.pageFrom || '';
        const formData = {
            ...this.pageParam.data
        };
        console.log(this.pageParam.data)
        return {
            pageFrom,
            formData,
            userInfo,
            postnList: new this.AutoList(this, {
                url: {
                    queryByExamplePage: this.$env.appURL + '/action/link/position/queryByIdentityEmp'
                },
                param: {
                    sort: '',
                    order: 'desc',
                    oauth: 'ALL',
                    pageFlag: true,
                    onlyCountFlag: false,
                    rows: 50,
                    page: 1,
                    attr1: 'attr1',
                    attr2: '',
                    filtersRaw: []
                },
                searchFields: ['fstName', 'postnName', 'orgName'],
                exactSearchFields: [
                    {
                        field: 'fstName',
                        showValue: '姓名',
                        searchOnChange: true,
                        clearOnChange: true,
                        exactSearch: true
                    }, {
                        field: 'empTel',
                        showValue: '手机号',
                        searchOnChange: true,
                        clearOnChange: true,
                        exactSearch: true
                    }
                ],
                sortOptions: null,
                hooks: {
                    async beforeLoad(options) {
                        for (let i = 0; i < options.param.filtersRaw.length; i++) {
                            if (options.param.filtersRaw[i].property === 'fstName') {
                                options.param.filtersRaw[i].operator = 'like';
                            }
                        }
                        //options.param.attr2 = this.isBusinessAgent ? this.userInfo.coreOrganizationTile['l3Id'] : await this.$lov.getValByTypeAndName('ACTIVITY_COMPANY', companyName)
                        options.param.sort = 'created';
                        options.param.attr2 = this.formData.basicOrgId;
                        const lovData = await this.$lov.getLovByType('ASSIGN_POSITION');
                        let str = '';
                        lovData.forEach((item) => {
                            str = item.val + ',' + str
                        });
                        str = str.substring(0, str.length - 1);
                        options.param.filtersRaw = [
                            ...options.param.filtersRaw,
                            {id: 'positionType', property: 'positionType', value: `[${str}]`, operator: 'IN'},
                            {id: 'isEffective', property: 'isEffective', value: 'Y', operator: '='}
                        ];
                        if (this.formData.followFlag === 'Y') {
                            options.param.filtersRaw.push(
                                {id: 'postnId', property: 'postnId', value: this.formData.consumerPostnId, operator: '<>'}
                            )
                        }
                    }
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} title={data.fstName} data={data} note={data.postnName}
                              desc={data.orgName}>
                        </item>
                    )
                }
            }),
            dialogFlag: false,
            selectedPostn: {}                //选中职位对象
        }
    },
    created () {
        // this.queryPostnList()
    },
    methods:{
        /**
         * @createdBy  张丽娟
         * @date  2021/3/17
         * @methods confirmTransfer
         * @description 确认转交职位
         */
        confirmTransfer: ComponentUtils.debounce(async function () {
            if (this.$utils.isEmpty(this.selectedPostn.postnName)) {
                this.$message.warn('请选择职位')
                return
            }
            this.$utils.showLoading("提交中");
            const data = await this.$http.post(this.$env.dmpURL + '/link/consumer/forceDistribution', {
                id: this.formData.consumerId,
                interfaceSource: 'Artificial',
                postnId: this.selectedPostn.postnId
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError(`提交转交申请失败：${response.result}`);
                    this.dialogFlag = false;
                }
            })
            if (data.success) {
                this.$utils.hideLoading();
                await this.getNewData();
                this.dialogFlag = false;
                this.$message.success('转交成功!');
            }

        }, 1000),
        /**
         * @createdBy 黄鹏
         * @date 2024/02/26
         * @methods: getNewData
         * @para:
         * @description: 转交成功后获取新数据
         **/
        async getNewData () {
            const data = await this.$http.post(this.$env.appURL + '/link/highIdentity/queryById', {
                id: this.formData.id
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError(`获取新数据失败：${response.result}`);
                    this.dialogFlag = false;
                }
            })
            if (data.success) {
                this.$set(this, 'formData', data.result);
            }
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/3/17
         * @methods gotoPostnList
         * @description 跳转到转交列表返回选中职位数据
         */
        async gotoPostnList() {
            const data = await this.$object(this.postnList, {
                pageTitle:'选择职位',
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} title={data.fstName} data={data} note={data.deptName}
                              desc={data.postnName}>
                        </item>
                        // <item key={index} data={data} arrow="false">
                        //     <view slot="note">
                        //         <view
                        //             style="display: flex;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                        //             <view style="color: #8C8C8C;min-width: 50px;font-size: 12px;">111职位名称:</view>
                        //             <view
                        //                 style="font-family: PingFangSC-Regular,serif;font-size: 12px;color: #000000;letter-spacing: 0;padding-left: 8px;width: calc(100% - 50px);overflow: hidden; white-space: nowrap;text-overflow: ellipsis">{data.postnName}</view>
                        //         </view>
                        //         <view
                        //             style="display: flex;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                        //             <view style="color: #8C8C8C;min-width: 50px;font-size: 12px;">职位编码:</view>
                        //             <view
                        //                 style="font-family: PingFangSC-Regular,serif;font-size: 12px;color: #000000;letter-spacing: 0;padding-left: 8px;width: calc(100% - 50px);overflow: hidden; white-space: nowrap;text-overflow: ellipsis">{data.integrationId
                        //             }</view>
                        //         </view>
                        //         <view
                        //             style="display: flex;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                        //             <view style="color: #8C8C8C;min-width: 50px;font-size: 12px;">所属部门:</view>
                        //             <view
                        //                 style="font-family: PingFangSC-Regular,serif;font-size: 12px;color: #000000;letter-spacing: 0;padding-left: 8px;width: calc(100% - 50px);overflow: hidden; white-space: nowrap;text-overflow: ellipsis">{data.deptName
                        //             }</view>
                        //
                        //         </view>
                        //     </view>
                        // </item>
                    )
                }
            })
            this.selectedPostn = data
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/3/17
         * @methods queryPostnList
         * @description 获取职位数组
         */
        async queryPostnList() {
            console.log(this.formData)
            let companyName = this.userInfo.coreOrganizationTile.brandCompanyCode === '5600' ? '鼎昊公司' : '国窖公司';
            const attr2 = await this.$lov.getValByTypeAndName('ACTIVITY_COMPANY', companyName);
            const lovData = await this.$lov.getLovByType('ASSIGN_POSITION');
            let str = '';
            lovData.forEach((item) => {
                str = item.val + ',' + str
            });
            str = str.substring(0, str.length - 1);
            const data = await this.$http.post(this.$env.appURL + '/action/link/position/queryByIdentityEmp', {
                pageFlag: true,
                rows: 50,
                page: 1,
                attr1: 'attr1',
                // attr1: 'queryPostnUnderOrg',
                attr2: attr2,
                filtersRaw: [

                    {id: 'positionType', property: 'positionType', value: `[${str}]`, operator: 'IN'},
                    {id: 'postnId', property: 'postnId', value: this.formData.postnId, operator: '<>'},
                    {id: 'isEffective', property: 'isEffective', value: 'Y', operator: '='}

                    // {id: 'positionType', property: 'positionType', value: `[${str}]`, operator: 'IN'},
                    // {id: 'userId', property: 'userId', value: this.userInfo.id, operator: '='},
                    // {id: 'isEffective', property: 'isEffective', value: 'Y', operator: '='}
                ]
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`查询职位数据失败：${response.result}`);
                }
            });
            if (data.success) {
                this.postnList = data.rows;
                if (this.postnList.length > 0) {
                    this.selectedPostn = Object.assign({}, {
                        postnName: this.postnList[0].postnName,
                        postnId: this.postnList[0].postnId,
                    })
                }
            }
        },
        /**
         * @desc 跳转消费者详情页面
         * <AUTHOR>
         * @date 2023/5/31 15:21
         **/
        gotoAccountItem () {
            this.$nav.push('/pages/lj-consumers/account/account-item-page', {
                data: {id: this.formData.consumerId,identityLevel:this.formData.identityLevel,endTime:this.formData.endTime},
                pageFrom: 'HighMemberItemEdit'
            })
        },
        /**
         * @desc 申请变更
         * <AUTHOR>
         * @date 2023/5/31 15:29
         **/
        applyChange () {
            this.formData.presentIdentityLevel = this.formData.identityLevel;
            //this.formData.identityLevel = null;
            const newData = {
                ...this.formData
            }
            newData.identityLevel = null
            let pageFrom = 'HighMemberItem';
            let applyType = 'change';
            if (this.pageFrom === 'AccountItem') {
                pageFrom = 'AccountItem';
                applyType = 'salesmanChange'
            }
            this.$nav.push('/pages/lj-consumers/high-member/high-member-item-edit-page', {
                data: newData,
                accountItem: this.pageParam.accountItem || {},
                pageFrom: pageFrom,
                applyType: applyType
            })
        },
        /**
         * @desc 申请转交
         * <AUTHOR>
         * @date 2023/5/31 15:30
         **/
        applyTransfer () {
            this.dialogFlag = true;
        }
    }
}
</script>
<style lang="scss">
.high-member-item-page{
    .model-title{
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 20px;
        .title{
            width: 56%;
        }
        .iconfont {
            font-size: 40px;
        }
    }
}
</style>
