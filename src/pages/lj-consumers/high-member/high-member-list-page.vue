<template>
    <link-page class="high-member-list-page">
        <link-auto-list :option="autoList" :hideCreateButton="!editFlag" v-if="editFlag">
            <view slot="top" class="top-tab-filter">
                <view class="lnk-tabs-content" style="width: 80%;">
                    <view class="lnk-tabs-item" :style="tab.seq === classifyListActive.seq ? 'color:#2f69f8;' : ''"
                          v-for="(tab, index) in classifyList" :key="index" @tap="switchTab(tab)" style="width: 25%">
                        <view class="label-name-line">
                            <text class="label-name-text">{{tab.name}}</text>
                            <view class="line" v-if="tab.seq === classifyListActive.seq"></view>
                        </view>
                    </view>
                </view>
            </view>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="account-list-item">
                    <view class="account-list" slot="note">
                        <view class="list-cell">
                            <view class="media-list" @tap="gotoItem(data)">
                                <view class="media-list-name" @tap="gotoItem(data)">
                                    {{ data.acctName }}
                                </view>
                                <view class="media-list-label">
                                    <view class="loyalty-level">{{ data.subAcctType | lov('ACCT_SUB_TYPE') }}</view>
                                    <view class="loyalty-level">{{ data.loyaltyLevel | lov('ACCT_MEMBER_LEVEL') }}
                                    </view>
                                    <view class="black-gold-account" v-if="data.identityLevel">{{data.identityLevel | lov('ACCT_SUB_TYPE')}}</view>
                                </view>
                                <view class="media-list-info">
                                    <view class="media-list-info-item">
                                        <view class="label">联系方式</view>
                                        <view class="media-list-info-phone">
                                            {{ data.mobilePhone }}
                                        </view>
                                    </view>
                                    <view class="media-list-info-item">
                                        <view class="label">身份等级</view>
                                        <view class="media-list-info-text">{{ data.identityLevel | lov('ACCT_SUB_TYPE') }}</view>
                                    </view>
                                    <view class="media-list-info-item">
                                        <view class="label">身份有效结束时间</view>
                                        <view class="media-list-info-text">{{ data.endTime | date('YYYY-MM-DD HH:mm:ss')}}</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                        <view class="account-label"><view class="label">{{ data.fstName }}跟进</view></view>
                    </view>
                </item>
            </template>
        </link-auto-list>
        <water-mark></water-mark>
    </link-page>
</template>
<script>
import StatusButton from "../../lzlj/components/status-button";
import LnkTaps from "../../core/lnk-taps/lnk-taps";
import waterMark from '../../lzlj/components/water-mark';

export default {
    name: "high-member-list-page",
    components: {LnkTaps, StatusButton, waterMark},
    data() {
        const pageFrom = this.pageParam.pageFrom || '';
        const userInfo = this.$taro.getStorageSync('token').result;
        let editFlag = true;
        let param = {
            oauth: 'ALL',
            sort: 'lastUpdated',
            filtersRaw: [
                {id: 'fromType', property: 'fromType', value: '[SeedInvite,AuthorityAgent,MembershipAgent]', operator: 'IN'},
                {id: 'identityStatus', property: 'identityStatus', value: '[InApproval,Effective]', operator: 'IN'},
            ]
        };
        const autoList = new this.AutoList(this, {
            module: this.$env.appURL + '/action/link/highIdentity',
            createPath: '/pages/lj-consumers/high-member/high-member-item-edit-page',
            filterOption: [
                {label: '身份等级', field: 'identityLevel', type: 'select', data:[]}
            ],
            exactSearchFields: [
                {
                    field: 'acctName',
                    showValue: '姓名',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: true
                }, {
                    field: 'mobilePhone',
                    showValue: '手机号',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: true
                }, {
                    field: 'fstName',
                    showValue: '跟进业代',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: true
                }
            ],
            loadOnStart: false,
            param: param,
            sortOptions: null,
            hooks: {
                async beforeLoad(options) {
                    for (let i = 0; i < options.param.filtersRaw.length; i++) {
                        if (options.param.filtersRaw[i].property === 'acctName' || options.param.filtersRaw[i].property === 'fstName') {
                            options.param.filtersRaw[i].operator = 'like';
                        }
                    }
                    if (this.classifyListActive.val === 'MY') {
                        options.param.filtersRaw = [
                            ...options.param.filtersRaw,
                            {id: 'consumerPostnId', property: 'consumerPostnId', value: this.userInfo.postnId},
                            {id: 'servicePersonal', property: 'servicePersonal', value: this.userInfo.postnId, operator: '='}
                        ];
                    } else if (this.classifyListActive.val === 'Others') {
                        options.param.filtersRaw = [
                            ...options.param.filtersRaw,
                            {id: 'consumerPostnId', property: 'consumerPostnId', value: this.userInfo.postnId, operator: '<>'},
                            {id: 'servicePersonal', property: 'servicePersonal', value: this.userInfo.postnId, operator: '='}
                        ];
                    } else {
                        options.param.filtersRaw = [
                            ...options.param.filtersRaw,
                            {id: 'servicePersonal', property: 'servicePersonal', value: this.userInfo.postnId, operator: '='}
                        ];
                    }

                },
                async beforeCreateItem(param) {
                    const id = await this.$newId();
                    param.pageFrom = 'HighMemberList';
                    param.data = {
                        id: id
                    };
                }
            }
        });
        return {
            editFlag,
            pageFrom,
            classifyList: [
                {val: 'ALL', name: '全部', seq: '1'},
                {val: 'MY', name: '我的跟进', seq: '2'},
                {val: 'Others', name: '业代跟进', seq: '3'},
            ],
            classifyListActive: {},
            autoList,
            userInfo
        };
    },
    async created() {
        this.classifyListActive = this.classifyList[0];
        await this.checkIsInMemberEmp();
    },
    methods: {
        /**
         * @desc 校验是否在权限人员列表
         * <AUTHOR>
         * @date 2023/6/14 14:42
         **/
        async checkIsInMemberEmp () {
           const data = await this.$http.post(this.$env.appURL + '/action/link/identityEmp/queryByExamplePage', {
               filtersRaw: [
                   {id: 'waitstaffType', property: 'waitstaffType', value: 'ServiceStaff'},
                   {id: 'waitstaffStatus', property: 'waitstaffStatus', value: 'Y'},
                   {id: 'waitstaffPostnId', property: 'waitstaffPostnId', value: this.userInfo.postnId},
               ]
           }, {
               autoHandleError: false,
               handleFailed: (response) => {
                   this.$utils.hideLoading();
                   this.$showError(`查询权限人员失败：${response.result}`);
               }
           });
           if (data.success) {
              if (data.rows.length > 0) {
                  this.queryIdentityLevelRange();
                  this.autoList.methods.reload();
              } else {
                  this.$showError('当前职位暂无数据权限');
                  this.editFlag = false;
              }
           }
        },
        /**
         * @desc 查询身份等级可选范围
         * <AUTHOR>
         * @date 2023/6/6 15:34
         **/
        async queryIdentityLevelRange () {
            const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', {key: 'Identity_level_range'});
            if (data.success) {
                const includesLovs = data.value.split('/');
                const filterOptionData = []
                for (const item of includesLovs) {
                    const name = await this.$lov.getNameByTypeAndVal('ACCT_SUB_TYPE', item)
                    const obj = {name: name, val: item}
                    filterOptionData.push(obj)
                }
                this.autoList.option.filterOption[0].data = filterOptionData
            }
        },
        /**
         * @desc 级联分类分级
         * <AUTHOR>
         * @date 2022/6/1 10:14
         **/
        async switchTab(item) {
            this.classifyListActive = item;
            this.autoList.list = [];
            this.autoList.methods.reload();
        },
        /**
         * @desc 跳转详情
         * <AUTHOR>
         * @date 2022/4/19 18:55
         **/
        gotoItem(data) {
            if (!this.editFlag) return;
            this.$nav.push('/pages/lj-consumers/high-member/high-member-item-page', {
                data: data,
                pageFrom: 'HighMemberList'
            });
        }

    }
}
</script>

<style lang="scss">
.high-member-list-page {
    /*deep*/
    .link-item {
        padding: 12px;
        overflow: hidden;
    }

    .link-item-body-left{
        overflow: visible;
    }

    .account-list {
        background-color: #FFFFFF;
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: column;

        .list-cell {
            position: relative;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;

            .media-list {
                position: relative;
                padding: 32px 24px 24px 24px;
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                width: 100%;

                .media-list-name {
                    height: 48px;
                    font-size: 32px;
                    color: #212223;
                    line-height: 48px;
                    font-weight: 600;
                }

                .media-list-label {
                    height: 36px;
                    margin: 16px 0;
                    display: flex;

                    .loyalty-level {
                        min-width: 80px;
                        padding: 0 15px;
                        margin-right: 16px;
                        background: #F0F5FF;
                        border-radius: 4px;
                        font-size: 22px;
                        color: #3F66EF;
                        letter-spacing: 0;
                        text-align: center;
                        line-height: 36px;
                        font-weight: 400;
                    }

                    .important-account {
                        width: 112px;
                        margin-right: 16px;
                        background: #FFF1EB;
                        border-radius: 4px;
                        font-size: 22px;
                        color: #FF461E;
                        line-height: 36px;
                        font-weight: 400;
                        text-align: center;
                    }

                    .black-gold-account {
                        width: auto;
                        padding: 0 5px;
                        background: #262626;
                        border-radius: 4px;
                        font-size: 22px;
                        color: #F0BE94;
                        line-height: 36px;
                        font-weight: 400;
                        text-align: center;
                    }
                }

                .media-list-info {
                    display: flex;
                    flex-direction: column;

                    .media-list-info-item {
                        height: 44px;
                        display: flex;
                        align-items: center;
                        margin-bottom: 8px;

                        .label {
                            width: auto;
                            margin-right: 24px;
                            font-size: 28px;
                            color: #999999;
                            line-height: 44px;
                            font-weight: 400;
                            white-space: nowrap;
                        }

                        .media-list-info-text {
                            width: auto;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            font-size: 28px;
                            color: #333333;
                            line-height: 44px;
                            font-weight: 400;
                        }

                        .media-list-info-phone {
                            font-size: 28px;
                            color: #317DF7;
                            line-height: 44px;
                            font-weight: 400;
                        }
                    }
                }
            }
        }
    }

    .account-label {
        position: absolute;
        right: -28px;
        top: -4px;
        padding: 8px 48px 4px 32px;
        background: #2F69F8;
        transform: skew(30deg, 0deg);

        .label {
            font-size: 24px;
            color: #FFFFFF;
            text-align: center;
            line-height: 40px;
            font-weight: 400;
            transform: skew(-30deg, 0);
        }
    }

    .account-list-item {
        background: #FFFFFF;
        margin: 24px;
        height: 372px;
        border-radius: 16px;
    }

    .link-auto-list-query-bar-filter-group {
        margin-top: 88px;
    }

    .top-tab-filter {
        position: relative;
    }
    .lnk-tabs-content {
        position: absolute;
        top: -84px;
        display: flex;
        justify-content: space-around;
        flex-wrap: nowrap;
    }

    .lnk-tabs-item {
        height: 92px;
        line-height: 92px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .label-name-line {
            font-size: 28px;
            margin-left: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .line {
                height: 8px;
                width: 56px;
                border-radius: 16px 16px 0 0;
                background-color: #2f69f8;
                box-shadow: 0 3px 8px 0 rgba(47,105,248,0.63);
                margin-top: -8px;
            }
        }
    }

    .list-taps .lnk-tabs{
        top: 96px !important;
        border-top: none;
    }

    .link-auto-list-top-bar {
        border-bottom: none;
    }
}
</style>
