import {defineComponent, getCurrentInstance, reactive, set} from "link-taro-component";
import {VNode} from "vue/types/umd";
import {getNewActivityBasicLinkObjectProps} from "@/pages/lj-consumers/list-report/activity-detail-basic-auto-list";

interface NewActivityDynamicOption {
  ctrlCode: string,                           //组件名
  //配置信息
  values: {
    field: string,                          //绑定字段
    fieldName: string,                      //字段显示值
    showField: string,                      // 对象前端显示字段，后端关联ID查询返回
    province: string,
    fieldType: string,
    city: string,
    district: string,
    lovType?: string,                       //值列表类型
    view?: string,                          //日期的视图类型
    displayFormat?: string,                 //显示值格式化字符串
    valueFormat?: string,                   //实际值格式化字符串
    option?: string,                        //link-object配置对象名称
  },
}

const newActivityDynamicComponents: {
  [k: string]: (
    option: NewActivityDynamicOption,
    formData: any,
    state: { linkObjectBinding: null | ReturnType<typeof getNewActivityBasicLinkObjectProps> }
  ) => VNode | null
} = {
  'link-input'(option, formData) {
    return (
      <link-input v-model={formData[option.values.field]}/>
    )
  },
  'link-date'(option, formData) {
    return (
      <link-date v-model={formData[option.values.field]}
                 view={option.values.view}
                 displayFormat={option.values.displayFormat}
                 valueFormat={option.values.valueFormat}/>
    )
  },
  'link-lov'(option, formData) {
    return (
      <link-lov v-model={formData[option.values.field]}
                type={option.values.lovType}/>
    )
  },
  'link-address'(option, formData) {
    return (
      <link-address
        view="pcd"
        province={formData[option.values.province]}
        city={formData[option.values.city]}
        district={formData[option.values.district]}
        {...{on: {'update:province': val => set(formData, option.values.province, val),
            'update:city': val => set(formData, option.values.city, val),
            'update:district': val => set(formData, option.values.district, val)}}}/>
    )
  },
  'link-object'(option, formData, state) {
    return !!state.linkObjectBinding ? (
      <link-object
        pageTitle={`请选择${option.values.fieldName}`}
        value={formData[option.values.showField]}
        row={formData}
        map={state.linkObjectBinding!.map}
        option={state.linkObjectBinding!.autoList}
      />
    ) : null
  },
  'link-number'(option, formData) {
    return (
      <link-number v-model={formData[option.values.field]}
                showType="input"/>
    )
  },
  'view-line'(option, formData) {
    return (
      <view style={'width: 100%;height: 25px;'}/>
    )
  },
  'link-textarea'(option, formData) {
    return (
      <link-textarea
        v-model={formData[option.values.field]}
      />
    )
  },
}

export default defineComponent({
  props: {
    option: {type: Object as any as new() => NewActivityDynamicOption, required: true},
    formData: {type: Object, require: true},
      listTagParam: {type: Object, require: false, default: {}}
  },
  setup(props) {

    const ctx = getCurrentInstance()!

    const state = reactive({
      linkObjectBinding: !props.option.values.option ? null : getNewActivityBasicLinkObjectProps(ctx, props.option.values.option)
    })

    return () => newActivityDynamicComponents[props.option.ctrlCode].apply(ctx, [props.option, props.formData, state])
  },
})
