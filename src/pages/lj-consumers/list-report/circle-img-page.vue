<template>
  <link-page class="circle-img-page">
    <scroll-view scroll-y="true" class="scroll-view">
      <image @longPress="saveShareImg" class="long-image" :src="url" mode="widthFix"></image>
    </scroll-view>
    <!-- <view class="sticky-btn" @tap="saveShareImg">
        <link-icon size="2.8" color="#0076ff" icon="icon-baocunkuaizhao" />
        <link-icon icon="icon-fankui" color="#0076ff"  size="2.8em"/>
    </view> -->
    <link-sticky>
        <link-button @tap="saveShareImg">保存图片</link-button>
    </link-sticky>
  </link-page>
</template>

<script>
export default {
  name: "circle-img-page",
  data () {
    const url = this.pageParam.url;
    return {
      url,
    }
  },
  methods:{
        /**
         * @desc 保存活动二维码
         * <AUTHOR>
         * @date 2023/3/14 11:15
         **/
         async saveShareImg() {
            this.$utils.showLoading();
            const that = this;
            wx.getImageInfo({
                src: that.url,
                success: function (ret) {
                    wx.saveImageToPhotosAlbum({
                        filePath: ret.path,
                        success: function (data) {
                            that.$message.success('图片已成功保存至相册！');
                        },
                        fail: function (err) {
                            if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
                                this.$message.info('打开设置窗口');
                                wx.openSetting({
                                    success(settingdata) {
                                        if (settingdata.authSetting['scope.writePhotosAlbum']) {
                                            that.$message.info('获取权限成功，请再次点击保存图片到相册。')
                                        } else {
                                            that.$message.info('获取权限失败,无法使用该功能，请授权再使用。');
                                        }
                                    }
                                })
                            }
                        }
                    })
                }
            });
            this.$utils.hideLoading();
        },
  }
}
</script>

<style lang="scss">
.circle-img-page {
  .scroll-view {
    width: 100%;
    height: 100%;
  }
.sticky-btn{
    position:   fixed;
    bottom: 400px;
    right: 24px;
    width: 120px;
    height: 120px;
    border-radius: 60px;
}
  .long-image {
    width: 100%;
    height: auto; /* 高度自适应 */
    display: block; /* 去除图片底部间隙 */
  }
}
</style>
