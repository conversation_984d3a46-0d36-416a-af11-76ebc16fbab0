<template>
    <link-page class="reissue-application-page">
        <approval-history-point :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)"></approval-history-point>
        <link-form :value="formData" :rules="formRules" hideSaveButton hideEditButton :readonly="!editable" ref="reissueForm">
            <link-form-item label="姓名">
                <link-input v-model="acctName" readonly></link-input>
            </link-form-item>
            <link-form-item label="手机号码">
                <link-input v-model="phone" readonly></link-input>
            </link-form-item>
            <link-form-item label="补发电话" field="consigneeTel">
                <link-input placeholder="请输入补发电话" v-model="formData.consigneeTel"/>
            </link-form-item>
            <link-form-item required :arrow="false" label="补发省市区乡镇街道" :field="['consigneeProvince', 'consigneeCity', 'consigneeDistrict', 'consigneeStreet']">
                <view class="address-placeholder" style="color: #e0e0e0"
                      v-if="$utils.isEmpty(formData.consigneeProvince) && $utils.isEmpty(formData.consigneeCity) && $utils.isEmpty(formData.consigneeDistrict) && $utils.isEmpty(formData.consigneeStreet)"
                      @tap="getLocation">
                    请选择所在地区
                    <link-icon icon="icon-location" class="link-location" style="color: #3e68ef"/>
                </view>
                <view class="address-color" @tap="getLocation">
                    <text v-if="!$utils.isEmpty(formData.consigneeProvince)">{{ formData.consigneeProvince }}</text>
                    <text v-if="!$utils.isEmpty(formData.consigneeCity)">/{{ formData.consigneeCity }}</text>
                    <text v-if="!$utils.isEmpty(formData.consigneeDistrict)">/{{ formData.consigneeDistrict }}</text>
                    <text v-if="!$utils.isEmpty(formData.consigneeStreet)">/{{ formData.consigneeStreet }}</text>
                </view>
            </link-form-item>
            <link-form-item label="补发详细地址"
                            field="consigneeAddress"
                            vertical style="border-bottom: 1px solid rgb(247,247,247);">
                <link-textarea v-model="formData.consigneeAddress"/>
            </link-form-item>
            <link-form-item label="补发产品" required field="prodId">
                <link-object :option="prodOption"
                             :row="formData"
                             pageTitle="选择补发产品"
                             disabled
                             :map="{prodId:'prodId', prodName: 'prodShowName', attr1: 'id'}"
                             :value="formData.prodName">
                </link-object>
            </link-form-item>
            <link-form-item label="补发瓶数" required field="consigneeBottle">
                <link-number :min= "1" v-model="formData.consigneeBottle" disabled></link-number>
            </link-form-item>
            <link-form-item label="活动名称" v-if="!$utils.isEmpty(approvalId)">
                <link-input v-model="activityName" readonly></link-input>
            </link-form-item>
            <link-form-item label="活动类型" v-if="!$utils.isEmpty(approvalId)">
                <link-lov v-model="activityType" readonly type="MC_TYPE"></link-lov>
            </link-form-item>
            <link-form-item label="活动时间" v-if="!$utils.isEmpty(approvalId)">
               {{actStartTime | date('YYYY-MM-DD HH:mm:ss')}}至{{actEndTime | date('YYYY-MM-DD HH:mm:ss')}}
            </link-form-item>
            <link-form-item label="补发理由" vertical field="relogReasons" required>
                <link-textarea v-model="formData.relogReasons"/>
            </link-form-item>
            <link-form-item label="补发状态" v-if="!$utils.isEmpty(approvalId)">
                <link-lov v-model="formData.status" readonly type="LOGISTICS_STATUS"></link-lov>
            </link-form-item>
            <link-form-item vertical required label="照片" v-if="formData.id">
                <lnk-img-lzlj :parentId="formData.id" :moduleType="'reissueApplication'" :delFlag="editable"
                              @call="getImgList"
                              :readonly="!$utils.isEmpty(approvalId)"
                              :newFlag="editable"></lnk-img-lzlj>
            </link-form-item>
            <view class="zero-view" v-if="!$utils.isEmpty(approvalId)"></view>
        </link-form>
        <link-sticky>
            <link-button block @tap="submitApply()" v-if="editable && $utils.isEmpty(approvalId)">提交申请</link-button>
        </link-sticky>
        <link-sticky>
            <approval-operator :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)"></approval-operator>
        </link-sticky>
    </link-page>
</template>

<script>
import LnkImgLzlj from "../../core/lnk-img-lzlj/lnk-img-lzlj";
import ApprovalHistoryPoint from "../../lzlj/approval/components/approval-history-point";
import ApprovalOperator from "../../lzlj/approval/components/approval-operator";
import {reverseTMapGeocoder} from "../../../utils/locations-tencent";
export default {
    components: {ApprovalOperator, ApprovalHistoryPoint, LnkImgLzlj},
    name: "reissue-application-page",
    data() {
        return {
            prodOption: new this.AutoList(this, {
                module: '/marketactivity/link/interListProd',
                url: {
                    queryByExamplePage: this.$env.appURL + '/marketactivity/link/interListProd/queryValidProd'
                },
                searchFields: ['prodShowName'],
                param: {
                    mcInterListId: '',
                    // typeLevel: '',
                    filtersRaw: [{id: 'tnterProdType', value: 'SubmitProducts', property: 'tnterProdType', operator: '='}]
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item arrow={false} key={index} title={data.prodShowName} data={data}
                              content={data.prodCode} desc={data.prodGroupName}>
                        </item>
                    )
                },
                hooks: {
                    beforeLoad (option) {
                        // option.param.typeLevel = this.subAcctType
                        option.param.mcInterListId = this.interListLine.mcInterListId
                    }
                }
            }),
            formRules: {
                reConsigneeTel: this.Validator.phone(),
                consigneeTel: this.Validator.phone()
            },
            interListLine: {},
            editable: false,        // 可编辑
            actEndTime: '',         // 活动结束时间
            actStartTime: '',       // 活动开始时间
            activityName: '',       // 活动名称
            activityType: '',       // 活动类型
            attachmentList: [],     // 是否上传照片
            acctName: '',           // 用户姓名
            phone: '',              // 用户手机号
            formData: {id: ''},           // 补发申请对象
            listApplyId: null,
            approvalId: null,
            subAcctType: ''             //消费者K序列等级
        }
    },
    async onShow(){
        const location = this.$locations.QQGetLocation();
        if(location){
            let addressInfo =  await reverseTMapGeocoder(location.latitude, location.longitude, '消费者');
            try {
                this.$utils.showLoading()
                const addrCode = addressInfo['originalData'].result.addressComponent.adcode;
                const data = await this.$http.post(this.$env.appURL +'/action/link/alladdress/queryEffectiveByDistrictCode',{addrCode: addrCode});
                if(data.success) {
                    if(data.adcodeFlag){
                        this.$set(this.formData, 'consigneeProvince', data.province);
                        this.$set(this.formData, 'consigneeCity', data.city);
                        this.$set(this.formData, 'consigneeDistrict', data.district);
                        this.$set(this.formData, 'consigneeStreet', addressInfo['originalData'].result.addressComponent.street ? addressInfo['originalData'].result.addressComponent.street : '');
                        this.$set(this.formData, 'consigneeAddress', addressInfo['result'].formatted_addresses.standard_address);
                        this.$utils.hideLoading();
                    }else {
                        this.$set(this.formData, 'consigneeProvince', addressInfo['originalData'].result.addressComponent.province);
                        this.$set(this.formData, 'consigneeCity', addressInfo['originalData'].result.addressComponent.city);
                        this.$set(this.formData, 'consigneeDistrict', addressInfo['originalData'].result.addressComponent.district);
                        this.$set(this.formData, 'consigneeStreet', addressInfo['originalData'].result.addressComponent.street ? addressInfo['originalData'].result.addressComponent.street : '');
                        this.$set(this.formData, 'consigneeAddress', addressInfo['result'].formatted_addresses.standard_address);
                        this.$utils.hideLoading();
                    }
                    if(this.pageParam.invalidateLocString.indexOf(this.formData.consigneeProvince) !== -1){
                        this.$message.warn('该地区尚未开通配送服务，请切换其他地区选择！');
                    }
                }
            } catch (e) {
                this.$utils.hideLoading();
            }
        }
    },

    async created() {
        let id = '';
        if (this.pageParam.pageFrom === 'reissueApplication') {
            this.editable = this.pageParam.editable;
            this.subAcctType = this.pageParam.interListLine.subAcctType
            this.acctName = this.pageParam.interListLine.acctName;
            this.phone = this.pageParam.interListLine.phone;
            this.interListLine = this.pageParam.interListLine;
            if( this.pageParam.interListLine.logStatus === 'Delivered') {
                id = await this.$newId();
                this.formData['prodId'] = this.pageParam.interListLine.prodId;
                this.formData['prodName'] = this.pageParam.interListLine.prodName;
                // this.formData['attr1'] = this.pageParam.interListLine.id;
                this.formData['id'] = id;
                this.formData['consigneeBottle'] = 1;
            } else {
                await this.queryLogisticsDataByHeadId(this.pageParam.interListLine.id);
            }
        }
        let sceneObj = await this.$scene.ready();//that.$store.getters['scene/getScene'];//消息场景对象
        let code = this.pageParam.source;//页面来源
        const approval_from = sceneObj.query['approval_from'];
        if (code === 'approval') {
            this.approvalId = this.pageParam.data.id;//审批传过来的审批数据id
            id = this.pageParam.data.flowObjId;//审批传过来的补发申请ID
            if (this.$utils.isNotEmpty(id)) {
                await this.queryLogisticsData(id);
            } else {
                this.$utils.showAlert('请联系管理员，未获取到名单提报信息', {icon: 'none'});
                return
            }
        } else {
            if (approval_from === 'qw') { //从小程序审批消息而来(消费者发运消息通知,补发申请流程相关通知)
                this.approvalId = sceneObj.query['approval_id'];//审批传过来的审批数据id
                id = sceneObj.query['flowObjId'];//审批传过来的补发申请ID
                if (this.$utils.isNotEmpty(id)) {
                    await this.queryLogisticsData(id);
                } else { // 消费者发运消息通知没有flowObjId 属于正常
                    // this.$utils.showAlert('请联系管理员，未获取到名单提报信息！', {icon: 'none'}); // 发运消息通知下钻到此页面不能报错
                    return
                }
            }
        }
    },
    methods:{
        /**
        * @desc 获取定位
        * <AUTHOR>
        * @date 2024/3/20
        **/
        async getLocation() {
            if(!this.editable) {
                return;
            }
            const addressInfo = await this.$locations.getAddress();
            await this.$locations.chooseLocation(addressInfo.wxMarkerData[0].latitude, addressInfo.wxMarkerData[0].longitude);
        },
        /**
         * @desc 查询物流数据
         * <AUTHOR>
         * @date 2022/4/6 17:52
         **/
        async queryLogisticsData (id) {
            this.$utils.showLoading();
            const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/logistics/queryById',
                {
                   id: id
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('物流数据查询失败！' + response.result);
                    }
                });
            if (data.success) {
                this.formData = data.result;
                this.$utils.hideLoading();
                await this.queryInterListLine(this.formData.headId);
            }
        },
        /**
         * @desc 根据嘉宾行ID查询补发申请详情
         * <AUTHOR>
         * @date 2022/4/6 15:25
         **/
        async queryLogisticsDataByHeadId (headId) {
            this.$utils.showLoading();
            const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/logistics/queryByExamplePage',
                {
                    filtersRaw: [{id: 'headId', property: 'headId', value: headId, operator: '='},{id: 'type', property: 'type', value: 'ReDelivery'}],
                    pageFlag: true,
                    rows: 1,
                    page: 1,
                    order: 'DESC',
                    sort: 'created'
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('补发详情数据查询失败！' + response.result);
                    }
                });
            if (data.success) {
                this.formData = data.rows[0];
                this.$utils.hideLoading();
            }
        },
        /**
         * @desc 查询名单行数据
         * <AUTHOR>
         * @date 2022/4/6 16:11
         **/
        async queryInterListLine (id) {
            this.$utils.showLoading();
            const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/interListLineNew/queryById',{id: id}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('名单行数据查询失败！' + response.result);
                    }
                });
            if (data.success) {
                this.acctName = data.result.acctName;
                this.phone = data.result.phone;
                this.activityType = data.result.activityType;
                this.actStartTime = data.result.actStartTime;
                this.actEndTime = data.result.actEndTime;
                this.activityName = data.result.interListName || data.result.activityName;
                this.interListLine = data.result;
                this.$utils.hideLoading();
            }
        },
        /**
         * @desc 获取图片长度
         * <AUTHOR>
         * @date 2021/7/16 16:49
         **/
        getImgList (data) {
            this.attachmentList = data;
        },
        /**
         * @desc 提交申请
         * <AUTHOR>
         * @date 2022/4/1 11:48
         **/
        async submitApply(){
            await this.$refs.reissueForm.validate();
            if (this.$utils.isEmpty(this.formData.relogReasons)) {
                this.$message.warn('请填写补发理由');
                return false;
            }
            if (this.attachmentList.length === 0) {
                this.$message.warn('请上传照片!');
                return;
            }
            if(this.pageParam.invalidateLocString.indexOf(this.formData.consigneeProvince) !== -1){
                this.$message.warn('该地区尚未开通配送服务，请切换其他地区选择！');
                return;
            }
            this.formData.headId = this.pageParam.interListLine.id
            this.$utils.showLoading();
            const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/logistics/createLogFlow', this.formData, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError('补发申请失败！' + response.result);
                }
            });
            if (data.success) {
                this.$utils.hideLoading();
                this.$utils.showAlert('提交成功！', {icon: 'none'});
                this.$nav.back()
            }
        }
    }
}
</script>

<style lang="scss">
.reissue-application-page {
    .zero-view {
        width: 100%;
        height: 360px;
    }
    .lnk-form-header-img {
        width: 100%;
        padding-left: 32px;
        background-color: white;
        font-size: 28px;
        line-height: 88px;
    }
}
</style>
