import {AutoList} from "link-taro-component";
import {Vue} from "vue/types/vue";
/**
 * @desc 通过optionName获取link-object绑定配置对象信息
 * <AUTHOR>
 * @date 2022/11/30 14:40
 **/
export function getNewActivityBasicLinkObjectProps(context: Vue, optionName: string): { map: object, autoList: AutoList } | null {
    switch (optionName) {
        case 'productOption':
            return {
                map: {prodId: 'prodId', prodName: 'prodShowName', attr1: 'id'},
                autoList: new AutoList(context, {
                    module: null as any,
                    url: {
                        queryByExamplePage: context.$env.appURL + '/marketactivity/link/interListProd/queryValidProd'
                    },
                    sortOptions: null as any,
                    searchFields: ['prodShowName'],
                    param: {
                        mcInterListId: context.formData.mcInterListId,
                        attr1: context.formData.accntChannelId,
                        // typeLevel:context.formData.subAcctType,
                        companyId:context.listTagParam.companyId,
                        prodTag:context.listTagParam.prodTag,
                        filtersRaw: [
                            {id: 'tnterProdType', value: 'SubmitProducts', property: 'tnterProdType', operator: '='}
                        ]
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item arrow={false} key={index} title={data.prodShowName} data={data}
                                  content={data.prodCode} desc={data.prodGroupName}>
                            </item>
                        )
                    }
                }),
            }
    }
    console.error(`无法识别optionName：${optionName}`);

    return null

}
