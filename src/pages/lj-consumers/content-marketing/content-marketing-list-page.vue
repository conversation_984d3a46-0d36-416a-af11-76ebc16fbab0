<template>
    <link-page class="content-marketing-list-page">
        <lnk-taps :taps="contentTabsOption" v-model="contentTabActive" @switchTab="switchTab" class="classify"/>
        <link-auto-list :option="contentOption" hideCreateButton>
            <view slot="filterGroup" class="top-filter">
                <scroll-view scroll-x="true" class="top-filter-content">
                    <view class="top-filter-info">
                        <view v-for="(item,index) in timeFilterOption" class="time-list-item" :class="item.checked? 'timeChecked' : ''" @tap="chooseTime(item)">
                            <view class="time-item">{{item.name}}</view>
                        </view>
                        <view class="time-list-item" :class="isChosen? 'timeChecked' : ''">
                            <view class="time-item" @tap="chooseBrandData">品牌分组</view>
                        </view>
                    </view>
                </scroll-view>
            </view>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="content-list-item">
                    <view class="content-list" slot="note">
                        <view class="content-list__left" v-if="contentTabActive.val !== 'cmText'" @tap="gotoItem(data)">
                            <image :src="data.imageUrl" mode="scaleToFill"/>
                        </view>
                        <view class="content-list__right" :style="{width: contentTabActive.val === 'cmText' ? '100%' : '', paddingLeft: contentTabActive.val === 'cmText' ? '0' : ''}">
                            <view @tap="gotoItem(data)">
                                <view class="content-data-info">
                                    <view class="content-title">{{data.contentTitle}}</view>
                                    <status-button>{{data.brandId | lov('CM_BRAND')}}</status-button>
                                </view>
                                <view class="content-info">{{data.introduction}}</view>
                                <view class="tag-list">
                                    <view class="tag-item" v-for="(item, tagIndex) in data.tagList" :key="tagIndex">#{{item.tagName}}#</view>
                                </view>
                            </view>
                            <view class="content-button" v-if="buttonName" @tap="shareData(data)">
                                <button @tap="otherType(data)" class="other-type">{{buttonName}}</button>
                                <button open-type="share" :data-id="data.id" :data-title="data.contentTitle" :data-url="data.imageUrl" @tap="shareData(data)">分享</button>
                            </view>
                            <view class="content-button" v-else style="display: flex;justify-content: flex-end;" @tap="shareData(data)">
                                <button open-type="share" :data-id="data.id" :data-title="data.contentTitle" :data-url="data.imageUrl">分享</button>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>

    import LnkTaps from "../../core/lnk-taps/lnk-taps";
    import {LovService} from "link-taro-component";
    import StatusButton from "../../lzlj/components/status-button";
    export default {
        name: 'ContentMarketingListPage',
        components: {StatusButton, LnkTaps},
        data () {
            const userInfo = this.$taro.getStorageSync('token').result;
            return {
                contentItemData: {},
                buttonName: '',
                isChosen: false,
                userInfo,
                token: this.$taro.getStorageSync('token').token,
                brandOption: new this.AutoList(this, { // 品牌分组
                    module: this.$env.appURL + '/marketactivity/link/wxPlatformMapping',
                    param: {
                        oauth: 'ALL',
                        filtersRaw: [
                            {id: 'status', property: 'status', value: 'Y'}
                        ]
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} className="select-box" arrow="false">
                                <link-checkbox val={data.id} toggleOnClickItem slot="thumb"></link-checkbox>
                                <view slot="title" style="display: flex;">{LovService.filter(data.brandId, 'CM_BRAND')}</view>
                            </item>)
                    },
                    hooks: {
                        beforeLoad (option) {
                            option.param.filtersRaw = [
                                ...option.param.filtersRaw,
                                {id: 'companyId', property: 'companyId', value: this.userInfo.coreOrganizationTile['l3Id']}
                            ];
                        }
                    }
                }),
                timeFilterOption: [{name: '全部', val: 'ALL', checked: true},{name: '近3个月', val: 'Last3Month', checked: false},{name: '近半年', val: 'LastHalfYear', checked: false},{name: '近一年', val: 'LastYear', checked: false}],
                contentTabsOption: [],
                contentTabActive: {},
                contentOption: new this.AutoList(this, {
                    module: this.$env.appURL + '/marketactivity/link/contentManage',
                    searchFields: ['contentTitle'],
                    param: {
                        companyId: '',
                        oauth: 'ALL',
                        sort: 'created',
                        filtersRaw: [
                            {id: 'status', property: 'status', value: 'Y'}
                        ]
                    },
                    loadOnStart: false,
                    sortOptions: null,
                    hooks: {
                        async beforeLoad (options) {
                            if (this.contentTabActive.val) {
                                options.param.companyId = this.userInfo.coreOrganizationTile['l3Id'];
                                options.param.filtersRaw = [
                                    ...options.param.filtersRaw,
                                    {id: 'contentType', property: 'contentType', value: this.contentTabActive.val}
                                ]
                            }
                        },
                        async afterLoad(data) {
                            data.rows.forEach(item=> {
                                if (item.tagId) {
                                    const tagList = JSON.parse(item.tagId);
                                    item.tagList = tagList.splice(0,3);
                                }
                            })
                        }
                    }
                })
            }
        },
        methods: {
            shareData (data) {
                this.contentItemData = data
            },
            /**
             * @desc 按钮名称
             * <AUTHOR>
             * @date 2022/9/9 16:40
             **/
            showButtonName () {
                if (this.contentTabActive.val === 'cmH5') { // h5
                    this.buttonName = '复制链接';
                } else if (this.contentTabActive.val === 'cmPoster') { // 海报
                    this.buttonName = '保存至相册';
                } else if (this.contentTabActive.val === 'cmTweet') { // 推文
                    this.buttonName = '';
                } else if (this.contentTabActive.val === 'cmVideo') { // 视频
                    this.buttonName = '下载';
                } else if (this.contentTabActive.val === 'cmVoice') { // 音频
                    this.buttonName = '复制链接';
                } else if (this.contentTabActive.val === 'cmText') { // 文字
                    this.buttonName = '复制';
                }
            },
            /**
             * @desc 复制链接，保存至相册等
             * <AUTHOR>
             * @date 2022/8/29 15:22
             **/
            otherType (contentItemData) {
                this.contentItemData = contentItemData;
                if (this.$utils.isEmpty(contentItemData)) {
                    return;
                }
                if (contentItemData.contentType === 'cmTweet') {
                    return;
                }
                const that = this;
                if (contentItemData.contentType === 'cmH5' || contentItemData.contentType === 'cmText') { // h5
                    this.insertDownloadAndShareTime('cmCopy');
                    wx.setClipboardData({
                        data: contentItemData.contentType === 'cmH5' ? contentItemData.url : contentItemData.wordContent,
                        success: function () {
                            // 添加下面的代码可以复写复制成功默认提示文本`内容已复制`
                            wx.showToast({
                                title: '复制成功',
                                duration: 3000
                            });
                        }
                    })
                } else if (contentItemData.contentType === 'cmPoster') { // 海报
                    this.insertDownloadAndShareTime('cmDownload');
                    this.$utils.showLoading();
                    wx.getImageInfo({
                        src: contentItemData.imageUrl,
                        success: function (ret) {
                            wx.saveImageToPhotosAlbum({
                                filePath: ret.path,
                                success: function (data) {
                                    that.$utils.hideLoading();
                                    that.$message.success("图片保存成功,可前往手机相册查看")
                                },
                                fail: function (err) {
                                    that.$utils.hideLoading();
                                    if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
                                        that.$message.info('打开设置窗口');
                                        wx.openSetting({
                                            success(settingdata) {
                                                if (settingdata.authSetting['scope.writePhotosAlbum']) {
                                                    that.$message.info('获取权限成功，请再次点击保存图片到相册。')
                                                } else {
                                                    that.$message.info('获取权限失败,无法使用该功能，请授权再使用。');
                                                }
                                            }
                                        })
                                    }
                                }
                            })
                        }
                    });
                } else if (contentItemData.contentType === 'cmVideo') { // 视频
                    this.insertDownloadAndShareTime('cmDownload');
                    this.$utils.showLoading('下载中…');
                    wx.downloadFile({
                        url: contentItemData.mediumUrl, //仅为示例，并非真实的资源
                        success (res) {
                            // 只要服务器有响应数据，就会把响应内容写入文件并进入 success 回调，业务需要自行判断是否下载到了想要的内容
                            if (res.statusCode === 200) {
                                wx.saveVideoToPhotosAlbum({
                                    filePath: res.tempFilePath,
                                    success: function (data) {
                                        that.$utils.hideLoading();
                                        that.$message.success("保存成功,可前往手机相册查看")
                                    },
                                    fail: function (err) {
                                        that.$utils.hideLoading();
                                        if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
                                            that.$message.info('打开设置窗口');
                                            wx.openSetting({
                                                success(settingdata) {
                                                    if (settingdata.authSetting['scope.writePhotosAlbum']) {
                                                        that.$message.info('获取权限成功，请再次点击保存图片到相册。')
                                                    } else {
                                                        that.$message.info('获取权限失败,无法使用该功能，请授权再使用。');
                                                    }
                                                }
                                            })
                                        }
                                    }
                                })
                            }
                        },
                        fail: function (err) {
                            console.log(err)
                            that.$utils.hideLoading();
                            that.$message.warn(err.errMsg);
                        }
                    })
                } else if (contentItemData.contentType === 'cmVoice') {
                    this.insertDownloadAndShareTime('cmCopy');
                    wx.setClipboardData({
                        data: contentItemData.mediumUrl,
                        success: function () {
                            // 添加下面的代码可以复写复制成功默认提示文本`内容已复制`
                            wx.showToast({
                                title: '复制成功',
                                duration: 3000
                            });
                        }
                    })
                }
            },
            /**
             * @desc 选择分组
             * <AUTHOR>
             * @date 2022/9/7 17:27
             **/
            async chooseBrandData () {
                this.isChosen = !this.isChosen
                if (this.isChosen) {
                    const list = await this.$object(this.brandOption, {
                        pageTitle: '请选择品牌',
                        showInDialog: true,
                        multiple: false
                    });
                    this.contentOption.option.param['filtersRaw'] = [
                        ...this.contentOption.option.param['filtersRaw'],
                        {id: 'mappingId', property: 'mappingId', value: list.id}
                    ];
                    this.contentOption.methods.reload();
                } else {
                    for (let i = 0; i < this.contentOption.option.param.filtersRaw.length; i ++) {
                        if (this.contentOption.option.param.filtersRaw[i].property === 'mappingId') {
                            this.contentOption.option.param.filtersRaw.splice(i,1);
                            break;
                        }
                    }
                    this.contentOption.methods.reload();
                }
            },
            /**
             * @desc 时间筛选
             * <AUTHOR>
             * @date 2022/8/26 10:34
             **/
            chooseTime (item) {
                this.timeFilterOption.forEach((timeItem) => {
                    timeItem.checked = timeItem.val === item.val;
                });
                let timeObj = {startDate: '', endDate: ''}
                if (item.val === 'Last3Month') {
                    timeObj = this.$utils.getLast3Month();
                } else if (item.val === 'LastHalfYear') {
                    timeObj = this.$utils.getLastHalfYear();
                } else if (item.val === 'LastYear') {
                    timeObj = this.$utils.getLastYear();
                }
                let filter = this.contentOption.option.param.filtersRaw.filter(filterItem=> filterItem.property === 'created');
                if (!!timeObj.startDate) {
                    if (filter.length <=0) {
                        this.contentOption.option.param.filtersRaw = [
                            ...this.contentOption.option.param.filtersRaw,
                            {id: 'created', property: 'created', value: timeObj.startDate, operator: '>='},
                            {id: 'created', property: 'created', value: timeObj.endDate, operator: '<='}
                        ];
                    } else {
                        this.contentOption.option.param.filtersRaw.forEach((filterItem) => {
                            if (filterItem.property === 'created' && filterItem.operator === '>=') {
                                filterItem.value = timeObj.startDate;
                            } else if (filterItem.property === 'created' && filterItem.operator === '<=') {
                                filterItem.value = timeObj.endDate;
                            }
                        })
                    }
                } else {
                    this.contentOption.option.param.filtersRaw = this.contentOption.option.param.filtersRaw.filter(filterItem=> filterItem.property !== 'created');
                }
                this.contentOption.methods.reload();
            },
            /**
             * @desc tab切换
             * <AUTHOR>
             * @date 2022/8/24 11:29
             **/
            switchTab () {
                this.contentOption.methods.reload();
                this.showButtonName();
            },
            /**
             * @desc 查看详情
             * <AUTHOR>
             * @date 2022/8/26 16:50
             **/
            gotoItem (item) {
                this.$nav.push('/pages/lj-consumers/content-marketing/content-marketing-item-page',{ data: item});
            },
            /**
             * @desc 记录下载量
             * <AUTHOR>
             * @date 2022/9/2 09:45
             * @param operType 操作类型
             **/
            async insertDownloadAndShareTime (operType) {
                await this.$http.post(this.$env.appURL + '/marketactivity/link/contentRecord/insertWithoutAuth', {
                    manageId: this.contentItemData.id,
                    operType: operType,
                }, {header: {Authorization: this.authorization}}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$showError(`插入行为记录失败：${response.result}`);
                    }
                });
            }
        },
        async mounted() {
            const cmType = await this.$lov.getLovByType('CM_TYPE');
            this.contentTabsOption = cmType.map((item, index) => {
                return {
                    val: item.val,
                    seq: index,
                    name: item.name
                }
            });
            this.contentTabActive = this.contentTabsOption[0];
            await this.contentOption.methods.reload();
            this.showButtonName();
        },
        onShareAppMessage (res) {
            if(res.target.dataset.id>0){
                this.contentItemData['id'] = res.target.dataset.id;
                this.insertDownloadAndShareTime('cmShare');
                return {
                    title:  res.target.dataset.title,  // 分享名称
                    path: `/pages/lj-consumers/content-marketing/content-marketing-item-page?contentItemId=${res.target.dataset.id}&empId=${this.userInfo.id}&token=${this.token}`,  // 点击分享后的链接要来到的页面的路径已经对应需要的参数
                    imageUrl: res.target.dataset.url,
                    // 分享成功之后的操作
                    success: function (res) {
                        console.log("分享成功:" + JSON.stringify(res));
                    },
                    // 分享失败之后的操作
                    fail: function (res) {
                        console.log("分享失败:" + JSON.stringify(res));
                    }
                }
            }
        }
    };
</script>
<style lang="scss">
.content-marketing-list-page{
    background-color: #F2F2F2;
    .top-filter{
        flex: 1;
        overflow-x: hidden;
        .top-filter-content{
            width: 100%;
            .top-filter-info{
                display: flex;
                white-space: nowrap;
                font-size: 24px;
                padding: 8px 24px;
                align-items: center;
                flex-wrap: nowrap;
                .time-list-item{
                    padding: 8px 16px;
                    margin-right: 8px;
                    white-space: nowrap;
                    display: inline-block;
                    background-color: #f2f2f2;
                    color: #333333;
                    border-radius: 4px;
                }
                .timeChecked{
                    background-color: rgba(47, 105, 248, 0.1) !important;
                    color: #2f69f8 !important;
                }
            }
        }
    }
    .classify{
        height: 100px;
    }
    .content-list-item {
        background: #FFFFFF;
        width: 95%;
        margin: 24px auto auto auto;
        border-radius: 16px;
        .content-list{
            display: flex;
            align-items: center;
            &__left{
                image {
                    width: 200px;
                    height: 200px;
                    border-radius: 16px;
                }
            }
            &__right{
                width: calc(100% - 200px);
                padding-left: 24px;
                .content-data-info{
                    display: flex;
                    justify-content: space-between;
                }
                .content-title {
                    font-size: 28px;
                    color: #333333;
                    text-align: left;
                    line-height: 40px;
                    font-weight: 600;
                    margin-bottom: 16px;
                    width: 70%;
                }
                .content-info {
                    margin-bottom: 16px;
                    display: -webkit-box;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                }
                .tag-list{
                    display: flex;
                    flex-wrap: wrap;
                    .tag-item{
                        font-size: 24px;
                        color: #999999;
                        line-height: 24px;
                        font-weight: 400;
                        margin-right: 16px;
                        margin-bottom: 10px;
                    }
                }
                .content-button{
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                    margin-top: 18px;
                    button {
                        width: 144px;
                        height: 56px;
                        padding: 0;
                        background: #2F69F8;
                        border-radius: 8px;
                        font-size: 24px;
                        color: #FFFFFF;
                        line-height: 56px;
                        border: none;
                    }
                    .other-type{
                        background: white;
                        color: #2F69F8;
                        border: 1px solid rgba(47,105,248,1);
                        border-radius: 8px;
                    }
                    button:after{
                        border: none;
                    }
                }
            }
        }
    }
    .type {
        width: 160px;
        .choose {
            margin: 15px 0;
            height: 50px;
            line-height: 50px;
            border-radius: 6px;
            padding: 0 20px;
        }
        .chosen {
            background: #EDF3FF;
            color: #2F69F8;
        }
    }

}
</style>
