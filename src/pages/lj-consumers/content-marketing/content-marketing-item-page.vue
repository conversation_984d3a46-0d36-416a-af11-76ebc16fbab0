<template>
    <link-page class="content-marketing-item-page" :style="{background: !showInfo? 'white;' :''}">
        <view v-if="contentItemData.contentType === 'cmTweet'">
            <web-view :src="contentItemData.url"></web-view>
        </view>
        <link-form class="content-marketing-item" :value="contentItemData" v-else>
            <view class="content-info" v-if="!invalid">
                <view class="content-info-data">
                    <view class="word-content" v-html="contentItemData.wordContent" v-if="contentItemData.wordContent"></view>
                    <view class="content-img" v-if="contentItemData.mediumUrl">
                        <video id="myVideo" :src="contentItemData.mediumUrl" @binderror="videoErrorCallback"
                               :show-center-play-btn="false"
                               v-if="contentItemData.contentType === 'cmVideo'"
                               :show-play-btn="true" ></video>
                        <view class="audio-data" v-if="contentItemData.contentType === 'cmVoice'">
                            <view class="audio-img" :style="{'background-image': 'url(' +contentItemData.imageUrl + ')'}">
                                <view class="iconfont icon-bofang" @tap="playMusic" v-if="!playFlag"></view>
                                <view class="iconfont icon-24gf-pause2" @tap="stopMusic" v-else></view>
                            </view>
                            <view class="audio-title">{{contentItemData.mediumName}}</view>
                        </view>
                    </view>
                    <view class="content-img" v-if="contentItemData.imageUrl && contentItemData.contentType !== 'cmVideo' && contentItemData.contentType !== 'cmVoice' && contentItemData.contentType !== 'cmTweet'">
                        <image :src="contentItemData.imageUrl" mode="widthFix"/>
                    </view>
                    <view class="check-code" v-if="qrCodeUrl">
                        <view>请扫描下方二维码查看详情</view>
                        <view style="display: flex;justify-content: center;">
                        <image :src="qrCodeUrl" mode="widthFix" :show-menu-by-longpress="true"/>
                        </view>
                    </view>
                </view>
                <view class="content-info-brief"  v-if="showInfo">
                    <view v-if="showInfo" class="content-title">{{contentItemData.contentTitle}}</view>
                    <view class="content-info-item" v-if="contentItemData.introduction">
                        <view class="content-info-item__left">简介：</view>
                        <view class="content-info-item__right" v-html="contentItemData.introduction"></view>
                    </view>
                    <view  class="content-info-item" v-if="contentItemData.tagList && contentItemData.tagList.length >0">
                        <view class="content-info-item__left">标签：</view>
                        <view class="content-info-item__right"><view class="tag-item" v-for="(item, tagIndex) in contentItemData.tagList" :key="tagIndex">#{{item.tagName}}#</view></view>
                    </view>
                </view>
            </view>
            <view class="invalid-content-info" v-else>
               <view>当前素材已失效</view>
            </view>
            <link-sticky v-if="showInfo">
                <link-button mode="stroke"  block v-if="contentItemData.contentType !== 'cmTweet'" @tap="otherType">{{buttonName}}</link-button>
                <link-button open-type="share" block>分享</link-button>
            </link-sticky>
        </link-form>
    </link-page>
</template>

<script>
    import Taro from "@tarojs/taro";
    export default {
        name: 'ContentMarketingListPage',
        data () {
            const userInfo = this.$taro.getStorageSync('token').result;
            const contentItemData = {
                ...this.pageParam.data
            };
            return {
                cmAudio: {},
                playFlag: false,
                qrCodeUrl: '',
                invalid: false,
                userInfo,
                showInfo: false,
                authorization: 'bearer corpwxmp', // token认证参数
                buttonName: '',
                shareFlag: false, // 是否分享中
                contentItemData
            }
        },
        destroyed () {
            if (!this.$utils.isEmpty(this.cmAudio)) {
                this.cmAudio.stop();
                this.cmAudio.destroy();
            }
        },
        async onLoad (options) {
            // 隐藏返回首页按钮
            wx.hideHomeButton();
            // 页面由分享带参小程序进入
            const code = await this.getCode();
            Taro.setStorageSync('code', code);
            // 获取系统用户token
            const token = Boolean(options.token) ? options.token : Taro.getStorageSync('token').token;
            if (token) {
                this.authorization = `bearer ${token}`;
            }
            if (options.contentItemId) {
                this.showInfo = false;
                await this.queryContentItemInfo(options.contentItemId);
                await this.insertDownloadAndShareTime('cmView');
                if (wx.hideShareMenu) wx.hideShareMenu();
            } else {
                this.showInfo = true;
                if (this.contentItemData.contentType !== 'cmTweet') {
                    if (wx.hideShareMenu) wx.hideShareMenu();
                }
            }
        },
        async created () {
            if (!this.$utils.isEmpty(this.contentItemData)) {
                wx.setNavigationBarTitle({title: this.contentItemData.contentTitle});
                if (this.contentItemData.contentType === 'cmH5') { // h5
                    this.buttonName = '复制链接';
                    await this.newCode();
                } else if (this.contentItemData.contentType === 'cmPoster') { // 海报
                    this.buttonName = '保存至相册';
                } else if (this.contentItemData.contentType === 'cmTweet') { // 推文
                } else if (this.contentItemData.contentType === 'cmVideo') { // 视频
                    this.buttonName = '下载';
                } else if (this.contentItemData.contentType === 'cmVoice') { // 音频
                    this.buttonName = '复制链接';
                } else if (this.contentItemData.contentType === 'cmText') { // 文字
                    this.buttonName = '复制';
                }
                this.handleContent();
            }
        },
        methods: {
            playMusic (){
                this.playFlag = true;
                this.cmAudio.play();
                this.cmAudio.onEnded(()=> {
                    this.playFlag = false;
                })
            },
            stopMusic () {
                this.playFlag = false;
                this.cmAudio.pause();
            },
            /**
             * @desc 生成二维码
             * <AUTHOR>
             * @date 2022/9/7 16:12
             **/
            async newCode () {
                const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/contentManage/getH5QrCode', {url: this.contentItemData.url}, {noToken: true, header: {Authorization: this.authorization}}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$showError(`生成二维码失败：${response.result}`);
                    }
                });
                if (data.success) {
                    this.qrCodeUrl = data.url;
                }
            },
            /**
             * @desc 处理推文展示数据
             * <AUTHOR>
             * @date 2022/9/7 15:00
             **/
            handleContent () {
                if (this.contentItemData.wordContent) {
                    this.contentItemData.wordContent = this.contentItemData.wordContent.replace(/data-src/ig, 'src');
                    this.contentItemData.wordContent = this.contentItemData.wordContent.replace(/&nbsp;/ig, '');
                }
                if (this.contentItemData.introduction) {
                    this.contentItemData.introduction = this.contentItemData.introduction.replace(/\r|\n|↵/g, '<br/>').replace(/\s/g, '&nbsp;')
                }
                if (this.contentItemData.contentType === 'cmVoice') {
                    this.cmAudio = wx.createInnerAudioContext();
                    this.cmAudio.src = this.contentItemData.mediumUrl;
                }
                const that = this;
                return new Promise((resolve, reject) => {
                    Taro.downloadFile({

                        url: that.qrCodeUrl,

                        success: (res) => resolve(
                            that.qrCodeUrl = res.tempFilePath
                        ),
                        fail: (err) => reject(err)

                    })

                })
            },
            // /**
            //  * @desc 分享朋友圈
            //  * <AUTHOR>
            //  * @date 2022/9/6 17:13
            //  **/
            // shareMoments () {
            //     const that = this;
            //     wx.qy.shareToExternalMoments( {
            //             text: {
            //                 content:"测试数据",    // 文本内容
            //             },
            //             attachments: [
            //                 {
            //                     msgtype: "image",    // 消息类型，必填
            //                     image: {
            //                         imgUrl: that.contentItemData.imageUrl,        // 图片的imgUrl,跟图片mediaid填其中一个即可
            //                     },
            //                 }
            //             ]},function(res) {
            //                 console.log(res, '测试数据')
            //             if (res.err_msg == "shareToExternalMoments:ok") {
            //             }
            //         }
            //     );
            // },
            /**
             * @desc 记录下载量
             * <AUTHOR>
             * @date 2022/9/2 09:45
             * @param operType 操作类型
             **/
            async insertDownloadAndShareTime (operType) {
                await this.$http.post(this.$env.appURL + '/marketactivity/link/contentRecord/insertWithoutAuth', {
                    manageId: this.contentItemData.id,
                    operType: operType,
                }, {noToken: true, header: {Authorization: this.authorization}}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$showError(`插入行为记录失败：${response.result}`);
                    }
                });
            },
            /**
             * 获取微信用户登录的code
             * <AUTHOR>
             * @data 2021-6-30
             */
            async getCode() {
                return new Promise((resolve) => {
                    wx.login({
                        success: function (res) {
                            if (res.code) {
                                resolve(res.code);
                            }
                        }
                    });
                })
            },
            /**
             * @desc 查询详情
             * <AUTHOR>
             * @date 2022/8/29 17:01
             **/
            async queryContentItemInfo (id) {
                const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/contentManage/queryByIdWithoutAuth', {id},{noToken: true, header: {Authorization: this.authorization}}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$showError(`查询素材详情失败：${response.result}`);
                    }
                });
                if (data.success) {
                    if (data.result.status === 'N') {
                        this.invalid = true;
                    } else {
                        this.contentItemData = data.result;
                        if (data.result.contentType === 'cmH5') {
                            await this.newCode();
                        }
                    }
                    this.$taro.setNavigationBarTitle({title: data.result.contentTitle});
                    if (data.result.contentType === 'cmTweet') {
                        document.title =  data.result.contentTitle
                    }
                }
                this.handleContent();
            },
            videoErrorCallback(e) {
                console.log('视频错误信息:')
                console.log(e.detail.errMsg)
            },
            /**
             * @desc 复制链接，保存至相册等
             * <AUTHOR>
             * @date 2022/8/29 15:22
             **/
            otherType () {
                const that = this;
                if (this.contentItemData.contentType === 'cmH5' || this.contentItemData.contentType === 'cmText') { // h5
                    this.insertDownloadAndShareTime('cmCopy');
                    wx.setClipboardData({
                        data: this.contentItemData.contentType === 'cmH5' ? this.contentItemData.url : this.contentItemData.wordContent,
                        success: function () {
                            // 添加下面的代码可以复写复制成功默认提示文本`内容已复制`
                            wx.showToast({
                                title: '复制成功',
                                duration: 3000
                            });
                        }
                    })
                } else if (this.contentItemData.contentType === 'cmPoster') { // 海报
                    this.insertDownloadAndShareTime('cmDownload');
                    this.$utils.showLoading();
                    wx.getImageInfo({
                        src: this.contentItemData.imageUrl,
                        success: function (ret) {
                            wx.saveImageToPhotosAlbum({
                                filePath: ret.path,
                                success: function (data) {
                                    that.$utils.hideLoading();
                                    that.$message.success("图片保存成功,可前往手机相册查看")
                                },
                                fail: function (err) {
                                    that.$utils.hideLoading();
                                    if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
                                        that.$message.info('打开设置窗口');
                                        wx.openSetting({
                                            success(settingdata) {
                                                if (settingdata.authSetting['scope.writePhotosAlbum']) {
                                                    that.$message.info('获取权限成功，请再次点击保存图片到相册。')
                                                } else {
                                                    that.$message.info('获取权限失败,无法使用该功能，请授权再使用。');
                                                }
                                            }
                                        })
                                    }
                                }
                            })
                        }
                    });
                } else if (this.contentItemData.contentType === 'cmVideo') { // 视频
                    this.insertDownloadAndShareTime('cmDownload');
                    this.$utils.showLoading('下载中…');
                    wx.downloadFile({
                        url: this.contentItemData.mediumUrl, //仅为示例，并非真实的资源
                        success (res) {
                            // 只要服务器有响应数据，就会把响应内容写入文件并进入 success 回调，业务需要自行判断是否下载到了想要的内容
                            if (res.statusCode === 200) {
                                wx.saveVideoToPhotosAlbum({
                                    filePath: res.tempFilePath,
                                    success: function (data) {
                                        that.$utils.hideLoading();
                                        that.$message.success("保存成功,可前往手机相册查看")
                                    },
                                    fail: function (err) {
                                        that.$utils.hideLoading();
                                        if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
                                            that.$message.info('打开设置窗口');
                                            wx.openSetting({
                                                success(settingdata) {
                                                    if (settingdata.authSetting['scope.writePhotosAlbum']) {
                                                        that.$message.info('获取权限成功，请再次点击保存图片到相册。')
                                                    } else {
                                                        that.$message.info('获取权限失败,无法使用该功能，请授权再使用。');
                                                    }
                                                }
                                            })
                                        }
                                    }
                                })
                            }
                        },
                        fail: function (err) {
                            console.log(err)
                            that.$utils.hideLoading();
                            that.$message.warn(err.errMsg);
                        }
                    })
                } else if (this.contentItemData.contentType === 'cmVoice') {
                    this.insertDownloadAndShareTime('cmCopy');
                    wx.setClipboardData({
                        data: this.contentItemData.mediumUrl,
                        success: function () {
                            // 添加下面的代码可以复写复制成功默认提示文本`内容已复制`
                            wx.showToast({
                                title: '复制成功',
                                duration: 3000
                            });
                        }
                    })
                }
            },
            /**
             * shareCard
             * 分享名片海报
             * <AUTHOR>
             * @data 2021-6-15
             */
            shareCard() {
                this.$refs.chooseType.hide();
                this.cardShare = true;
            },
            /**
             * shareApplet
             * 分享小程序
             * <AUTHOR>
             * @data 2021-6-17
             */
            shareApplet() {
                if (this.$device.systemInfo.model.indexOf('iPhone') !== -1) {
                    setTimeout(() => {
                        this.shareFlag = false;
                    }, 600);
                }
                this.$refs.chooseType.hide();
            }

        },
        onShareAppMessage (res) {
            if (!this.$utils.isEmpty(this.cmAudio)) {
               this.cmAudio.onPause(() => {
                   this.playFlag = false;
               })
            }
            this.insertDownloadAndShareTime('cmShare');
            console.log('/pages/lj-consumers/content-marketing/content-marketing-item-page?contentItemId=' + this.contentItemData.id + '&empId=' + this.userInfo.id)
            return {
                title: this.contentItemData.contentTitle,
                imageUrl: this.contentItemData.imageUrl,
                path: '/pages/lj-consumers/content-marketing/content-marketing-item-page?contentItemId=' + this.contentItemData.id + '&empId=' + this.userInfo.id
            }
        }
    };
</script>
<style lang="scss">
.content-marketing-item-page{
    .content-marketing-item{
        padding: 0 0 24px;
    }
   .content-info{
       .content-info-data {
           padding: 24px;
           background: white;
       }
       .content-info-brief {
           padding: 0 24px;
           background: white;
           margin-top: 24px;
           .content-title{
               display: flex;
               //align-items: center;
               //justify-content: center;
               word-break: break-all;
               line-height: 120px;
               font-size: 32px;
               font-weight: 500;
               border-bottom: 1px solid #F2F2F2;
           }
           .content-info-item {
               display: flex;
               font-size: 28px;
               color: #999999;
               align-items: baseline;
               min-height: 88px;
               border-bottom: 1px solid #F2F2F2;
               &__left {
                   width: 15%;
                   line-height: 88px;
               }
               &__right {
                   word-break: break-all;
                   width: 85%;
                   display: flex;
                   flex-wrap: wrap;
                   align-items: center;
                   .tag-item{
                       line-height: 50px;
                       font-weight: 400;
                       margin-right: 16px;
                       margin-bottom: 10px;
                   }
               }
           }
       }
       .content-img {
           width: 100%;
           justify-content: center;
           display: flex;
           image{
               width: 100% !important;
           }
           video {
               width: 100% !important;
           }
           audio {
               width: 100% !important;
           }
           .audio-data {
               width: 100%;
               height: 160px;
               border: 1px solid #e5e5e5;
               display: flex;
               align-items: center;
               .audio-img{
                   width: 20%;
                   height: 160px;
                   display: flex;
                   justify-content: center;
                   align-items: center;
                   background-size: 100% 100% !important;
                   .iconfont {
                       font-size: 52px;
                       color: white;
                   }
               }
               .audio-title {
                   font-size: 28px;
                   padding: 0 20px;
               }
           }
       }
   }
    .content-button{
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: fixed;
        bottom: 80px;
        width: 75%;
        margin: 0 10%;
    }
    .tweet {
        justify-content: center !important;
    }
    .invalid-content-info{
        line-height: 40vh;
        text-align: center;
    }
    .word-content{
        white-space: pre-wrap;
        width: 98%;
        image{
            width: 640px !important;
        }
    }
    .check-code{
        margin-top: 20px;
        text-align: center;
        image {
            width: 600px;
            height: 600px;
        }
    }
}
</style>
