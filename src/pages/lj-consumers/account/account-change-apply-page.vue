<template>
    <link-page class="account-change-apply-page">
        <approval-history-point-v3 ref="apprHisPointV3" :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId) && apVersion === 'v3'"></approval-history-point-v3>
        <approval-history-point :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId) && ($utils.isEmpty(apVersion) || apVersion !== 'v3')"></approval-history-point>
        <link-form  :value="formData">
            <view v-if="!$utils.isEmpty(approvalId) && consumerData && approvalType === 'ConsumerUpdate'">
                <line-title title="消费者基础信息"/>
                <link-swipe-action>
                    <item :data="consumerData" :arrow="false" class="account-list-item">
                        <view class="account-list" slot="note">
                            <view class="list-cell">
                                <view class="media-list" @tap="checkConsumer">
                                    <view class="media-list-name" @tap="checkConsumer">
                                        <view class="acct-name">{{ consumerData.acctName || consumerData.consumerName || consumerData.name }}</view>
                                        <view class="terminal-label" v-if="consumerData.terminalFlag === 'Y'">终端</view>
                                        <view class="is-certify" v-if="consumerData.certify"><image :src="consumerData.certify === 'certified' ? $imageAssets.storeStatusVerifiedImage : $imageAssets.storeStatusUnverifiedImage"></image></view>
                                    </view>
                                    <view class="media-list-time" v-if="consumerData.certify === 'certified' && !$utils.isEmpty(consumerData.certifyTime) && consumerData.certifyTime !== '9999-12-31 23:59:59'">
                                        <view class="time">{{ `${consumerData.certifyTime}认证到期` }}</view>
                                    </view>
                                    <view class="media-list-label">
                                        <view class="loyalty-level" v-if="consumerData.type">{{ consumerData.type | lov('ACCT_SUB_TYPE')}} </view>
                                        <view class="loyalty-level" v-else>{{consumerData.subAcctType | lov('ACCT_SUB_TYPE')}}</view>
                                        <view class="loyalty-level">{{ consumerData.loyaltyLevel | lov('ACCT_MEMBER_LEVEL') }}</view>
                                        <view class="important-account" v-if="consumerData.impFlag === 'Y'">重点客户</view>
                                        <view class="black-gold-account" v-if="consumerData.identityLevel">{{consumerData.identityLevel | lov('ACCT_SUB_TYPE')}}</view>
                                    </view>
                                    <view class="media-list-info">
                                        <view class="media-list-info-item">
                                            <view class="label">联系方式</view>
                                            <view class="media-list-info-phone" @tap.stop="makePhoneCall(consumerData)">
                                                {{ consumerData.mobilePhone1 || consumerData.mobilePhone || consumerData.phoneNumber}}
                                            </view>
                                        </view>
                                        <view class="media-list-info-item">
                                            <view class="label">单位</view>
                                            <view class="media-list-info-text">{{ consumerData.company || consumerData.companyName }}</view>
                                        </view>
                                        <view class="media-list-info-item">
                                            <view class="label">职务</view>
                                            <view class="media-list-info-text">{{ consumerData.position }}</view>
                                        </view>
                                        <view class="media-list-info-item">
                                            <view class="label">所属客户</view>
                                            <view class="media-list-info-text">{{ consumerData.belongToStore }}</view>
                                        </view>
                                        <view class="media-list-info-item">
                                            <view class="label" style="width: auto">是否宴席合伙人</view>
                                            <view class="media-list-info-text">{{ consumerData.attr06 | lov('YES_OR_NO') }}</view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view class="account-label"><view class="label">{{ consumerData.fstName }}跟进</view></view>
                        </view>
                    </item>
                </link-swipe-action>
            </view>
            <line-title :title="titleName"/>
            <view class="change-list" v-if="(formData.businessData && formData.businessData.length > 0) || (imgList && imgList.length > 0)">
                <view class="change-data">
                    <view class="change-new-data-item">
                        <view class="new-label">申请编号</view>
                        <view class="new-data">{{ flowObjId }}</view>
                    </view>
                </view>
                <view class="change-data" v-if="approvalType === 'DigitalCertify'">
                    <view class="change-new-data-item">
                        <view class="new-label">申请理由</view>
                    </view>
                    <view class="change-new-data-item">
                        <link-textarea placeholder="请填写申请理由，字数不超过500字" v-model="formData.appExplain" :nativeProps="{maxlength: 500}" readonly></link-textarea>
                    </view>
                </view>
                <view class="change-data" v-if="approvalType === 'DigitalCertify'">
                    <view class="change-new-data-item">
                        <view class="new-label">附件（用于审批）</view>
                    </view>
                    <view class="change-new-data-item">
                        <lnk-img-watermark :parentId="formData.id"
                                           moduleType="consumerCertify"
                                           isZlFlag
                                           isCount
                                           :addLength="9"
                                           filePathKey="consumerCertify/"
                                           ref="consumerCertify">
                        </lnk-img-watermark>
                    </view>
                </view>
                <view class="change-data" v-for="(item, index) in formData.businessData" :key="index">
                    <view class="change-new-data-item" v-if="item.label && approvalType === 'ConsumerUpdate' && item.type !== 'LabelField' || formData.applyType === 'UpdateApproval'">
                        <view class="new-label">{{item.label}}</view>
                        <view class="new-data" v-if="item.field === 'positionLevel'">{{ item.oldValue | lov('POSITION_LEVEL') }}</view>
                        <view class="new-data" v-else-if="item.field === 'subordinateIndustry'">{{ item.oldValue | lov('INDUSTRY') }}</view>
                        <view class="new-data" v-else-if="item.field === 'companyProperty'">{{ item.oldValue | lov('COMPANY_TYPE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'socialStatus'">{{ item.oldValue | lov('ACCT_SOCIAL_STATUS') }}</view>
                        <view class="new-data" v-else-if="item.field === 'acctRank'">{{ item.oldValue | lov('ACCT_RANK') }}</view>
                        <view class="new-data" v-else-if="item.field === 'socialCircle'">{{ item.oldValue | lov('SOCIAL_CIRCLE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'positionGrade'">{{ item.oldValue | lov('POSITION_GRADE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'enterpriseLevel'">{{ item.oldValue | lov('ENTERPRISE_LEVEL') }}</view>
                        <view class="new-data" v-else-if="item.field === 'enterpriseSize'">{{ item.oldValue | lov('ENTERPRISE_SIZE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'personnelType'">{{ item.oldValue | lov('PERSONNEL_TYPE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'type'">{{item.oldValue | lov('ACCT_SUB_TYPE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'memberType'">{{item.oldValue | lov('ACCT_MEMBER_TYPE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'affiliatedUnitType'">{{ item.oldValue | lov('AFFI_UNIT_TYPE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'unit'">{{ item.oldValue | lov('UNIT') }}</view>
                        <view class="new-data" v-else-if="item.field === 'office'">{{ item.oldValue | lov('OFFICE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'loyaltyLevel'">{{ item.oldValue | lov('ACCT_MEMBER_LEVEL') }}</view>
                        <view class="new-data" v-else-if="item.field === 'certify'">{{ item.oldValue | lov('CERTIFY') }}</view>
                        <view class="new-data" v-else-if="item.field === 'bringInto'">{{ item.oldValue | lov('BRINGINTO') }}</view>
                        <view class="new-data" v-else-if="item.field === 'bringInto'">{{ item.oldValue | lov('BRINGINTO') }}</view>
                        <view class="new-data" v-else-if="item.field === 'sourceFrom'">{{ item.oldValue | lov('SOURCE_FROM') }}</view>
                        <view class="new-data" v-else-if="item.field === 'empFlag'">{{ item.oldValue | lov('IMP_FLAG') }}</view>
                        <view class="new-data" v-else-if="item.field === 'terminalFlag'">{{ item.oldValue | lov('YES_OR_NO_TYPE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'birthType'">{{ item.oldValue | lov('BIRTHDAY_TYPE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'gender'">{{ item.oldValue | lov('GENDER') }}</view>
                        <view class="new-data" v-else-if="item.field === 'schedulFlag'">{{ item.oldValue | lov('YES_OR_NO_TYPE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'impFlag'">{{ item.oldValue | lov('YES_OR_NO_TYPE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'followFlag'">{{ item.oldValue | lov('FOLLOW_FLAG') }}</view>
                        <view class="new-data" v-else-if="item.field === 'accntSourceFrom'">{{ item.oldValue | lov('ACCNT_SOURCE_FROM') }}</view>
                        <view class="new-data" v-else-if="item.field === 'acctLevel'">{{ item.oldValue | lov('ACCT_LEVEL') }}</view>
                        <view class="new-data" v-else-if="item.oldValue && (item.oldValue['province'] || item.oldValue['province'] === '')">{{ item.oldValue['province'] }}{{ item.oldValue['city'] }}{{ item.oldValue['district'] }}{{ item.oldValue['street'] }}</view>
                        <view class="new-data" v-else-if="item.field === 'belongToBrand'">{{ item.oldValue | lov('BRAND') }}</view>
                        <view class="new-data" v-else-if="item.field === 'attr06'">{{ item.oldValue | lov('YES_OR_NO') }}</view>
                        <view class="new-data" v-else-if="item.field === 'attr07'">{{ item.oldValue | lov('YES_OR_NO') }}</view>
                        <view class="new-data" v-else>{{ item.oldValue }}</view>
                    </view>
                    <view class="change-new-data-item" v-if="item.label && item.type !== 'LabelField'">
                        <view class="old-label">{{item.label}}<view class="change" v-if="approvalType === 'ConsumerUpdate' || formData.applyType === 'UpdateApproval'">(变更后)</view></view>
                        <view class="new-data" v-if="item.field === 'positionLevel'">{{ item.value | lov('POSITION_LEVEL') }}</view>
                        <view class="new-data" v-else-if="item.field === 'subordinateIndustry'">{{ item.value | lov('INDUSTRY') }}</view>
                        <view class="new-data" v-else-if="item.field === 'companyProperty'">{{ item.value | lov('COMPANY_TYPE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'socialStatus'">{{ item.value | lov('ACCT_SOCIAL_STATUS') }}</view>
                        <view class="new-data" v-else-if="item.field === 'acctRank'">{{ item.value | lov('ACCT_RANK') }}</view>
                        <view class="new-data" v-else-if="item.field === 'socialCircle'">{{ item.value | lov('SOCIAL_CIRCLE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'positionGrade'">{{ item.value | lov('POSITION_GRADE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'enterpriseLevel'">{{ item.value | lov('ENTERPRISE_LEVEL') }}</view>
                        <view class="new-data" v-else-if="item.field === 'enterpriseSize'">{{ item.value | lov('ENTERPRISE_SIZE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'personnelType'">{{ item.value | lov('PERSONNEL_TYPE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'memberType'">{{ item.value | lov('MEMBER_TYPE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'affiliatedUnitType'">{{ item.value | lov('AFFI_UNIT_TYPE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'unit'">{{ item.value | lov('UNIT') }}</view>
                        <view class="new-data" v-else-if="item.field === 'office'">{{ item.value | lov('OFFICE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'type'">{{item.value | lov('ACCT_SUB_TYPE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'loyaltyLevel'">{{ item.value | lov('ACCT_MEMBER_LEVEL') }}</view>
                        <view class="new-data" v-else-if="item.field === 'certify'">{{ item.value | lov('CERTIFY') }}</view>
                        <view class="new-data" v-else-if="item.field === 'bringInto'">{{ item.value | lov('BRINGINTO') }}</view>
                        <view class="new-data" v-else-if="item.field === 'sourceFrom'">{{ item.value | lov('SOURCE_FROM') }}</view>
                        <view class="new-data" v-else-if="item.field === 'empFlag'">{{ item.value | lov('IMP_FLAG') }}</view>
                        <view class="new-data" v-else-if="item.field === 'terminalFlag'">{{ item.value | lov('YES_OR_NO_TYPE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'birthType'">{{ item.value | lov('BIRTHDAY_TYPE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'gender'">{{ item.value | lov('GENDER') }}</view>
                        <view class="new-data" v-else-if="item.field === 'schedulFlag'">{{ item.value | lov('YES_OR_NO_TYPE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'impFlag'">{{ item.value | lov('YES_OR_NO_TYPE') }}</view>
                        <view class="new-data" v-else-if="item.field === 'followFlag'">{{ item.value | lov('FOLLOW_FLAG') }}</view>
                        <view class="new-data" v-else-if="item.value && (item.value['province'] || item.value['province'] === '')">{{ item.value['province'] }}{{ item.value['city'] }}{{ item.value['district'] }}{{ item.value['street'] }}</view>
                        <view class="new-data" v-else-if="item.field === 'acctLevel'">{{ item.value | lov('ACCT_LEVEL') }}</view>
                        <view class="new-data" v-else-if="item.field === 'belongToBrand'">{{ item.value | lov('BRAND') }}</view>
                        <view class="new-data" v-else-if="item.field === 'attr06'">{{ item.value | lov('YES_OR_NO') }}</view>
                        <view class="new-data" v-else-if="item.field === 'attr07'">{{ item.value | lov('YES_OR_NO') }}</view>
                        <view class="new-data" v-else>{{ item.value }}</view>
                    </view>
                </view>
                <view class="change-data" v-if="imgList && imgList.length > 0">
                    <view class="change-new-data-item">
                        <view class="new-label">附件</view>
                    </view>
                    <view class="change-new-data-item">
                        <lnk-img-watermark :parentId="formData.businessId"
                                           moduleType="consumerattachment"
                                           isZlFlag
                                           :pathKeyArray="imgList"
                                           filePathKey="/consumerattachment/"
                                           ref="fakeTerminalPhoto">
                        </lnk-img-watermark>
                    </view>
                </view>
                <view class="label-info" v-for="(item, index) in tagLabels" :key="index + 'tag'">
                    <view class="group-label">
                        <view class="label-data">{{item.tagGroupName}}</view>
                    </view>
                    <view class="label-data-info" v-if="item['itemTags']">
                        <view v-for="(itemTag, itemIndex) in item['itemTags']" :key="itemIndex" class="label-data-item">
                            <view v-if="itemTag['ifActive'] !== 'N'" class="label-item" >
                                {{itemTag.tagName}}
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </link-form>
        <link-fab-button :bottom="200" icon="icon-edit" @tap="gotoItem" v-if="isEdit&&flowObj.appStatus === 'Reject' && (approvalType === 'ConsumerInsert' || approvalType === 'ConsumerUpdate')"></link-fab-button>
        <!-- <link-sticky> -->
        <approval-operator-v3 :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId) && apVersion === 'v3'" :fixed="true"></approval-operator-v3>
        <approval-operator :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId) && ($utils.isEmpty(apVersion) || apVersion !== 'v3')" :fixed="true"></approval-operator>
        <!-- </link-sticky> -->
        <view class="blank" :style="apVersion === 'v3'?'height: 270rpx;':''" v-if="!$utils.isEmpty(approvalId)"></view>
        <water-mark></water-mark>
    </link-page>
</template>

<script>
import LineTitle from "../../lzlj/components/line-title";
import ApprovalHistoryPoint from "../../lzlj/approval/components/approval-history-point";
import ApprovalOperator from "../../lzlj/approval/components/approval-operator";
import {ROW_STATUS} from "../../../utils/constant";
import waterMark from "../../lzlj/components/water-mark";
import LnkImgWatermark from "../../core/lnk-img-watermark/lnk-img-watermark.vue";
import ApprovalHistoryPointV3 from "../../lzlj/approval-v3/components/approval-history-point.vue";
import ApprovalOperatorV3 from "../../lzlj/approval-v3/components/approval-operator.vue";

export default {
    name: "account-change-apply-page",
    components: {ApprovalOperatorV3, ApprovalHistoryPointV3, LnkImgWatermark, ApprovalOperator, ApprovalHistoryPoint, LineTitle, waterMark},
    data () {
        const userInfo = this.$taro.getStorageSync('token').result;         // 获取用户信息
        return {
            userInfo,
            isEdit: false,
            flowObj: {},
            pageFrom: '',
            tagLabels: [],
            consumerTagsList: [],
            approvalType: '',  // 审批类型
            consumerData: {}, // 消费者信息
            flowObjId: '',
            approvalFlag: false,
            imgList: [], // 消费者图片
            approvalId: '',
            formData: {},
            apVersion: null, // v2/v3审批
            businessDataOrigin: [],   // 未经筛选的businessData
            cfgArray: [], // 企业参数配置信息-新K涉及公司范围
            newKFields: [
                {field: 'memberType', showFlag: false, type: 'MEMBER_TYPE', label: '会员类型', memberTypeList: []},
                {field: 'affiliatedUnitType', showFlag: false, type: 'AFFI_UNIT_TYPE', label: '所属单位类型', affiliatedUnitTypeList: []},
                {field: 'unit', showFlag: false, type: 'UNIT', label: '单位', unitList: []},
                {field: 'office', showFlag: false, type: 'OFFICE', label: '职务层级', officeList: []},
            ]
        }
    },
    computed: {
        /**
         * @desc 提示名称
         * <AUTHOR>
         * @date 2023/4/25 15:03
         **/
        titleName () {
            if (this.formData.applyType === 'UpdateApproval') {
                return '消费者变更申请';
            } else if (this.formData.applyType === 'FollowApproval') {
                return '认领申请';
            } else if (this.formData.applyType === 'DigitalCertify') {
                return '消费者认证申请';
            } else {
                return '消费者新建申请';
            }
        }
    },
    async created() {
        // 查询企业参数配置-参数键New_Type_Company
        const obj = await this.$utils.getCfgProperty('New_Type_Company');
        this.cfgArray = obj.split(',')
        console.log('cfgArray', obj, this.cfgArray);
        let sceneObj = await this.$scene.ready();//that.$store.getters['scene/getScene'];//消息场景对象
        this.companyName = await this.$lov.getNameByTypeAndVal('ACTIVITY_COMPANY', this.userInfo.coreOrganizationTile.l3Id);
        console.log(sceneObj,'sceneObj', this.pageParam);
        let code = this.pageParam.source; // 页面来源
        const approval_from = sceneObj.query['approval_from'];
        this.pageFrom = this.pageParam['pageFrom'] || ''
       if (code === 'approval') {
            this.approvalType = this.pageParam.data.approvalType;
            this.approvalId = this.pageParam.data.id; // 审批传过来的审批数据ID
            this.approvalFlag = this.$utils.isNotEmpty(this.approvalId)
            this.flowObjId = this.pageParam.data.flowObjId; // 审批传过来的业务对象ID
            this.apVersion = this.pageParam.data.apVersion;
            if(this.$utils.isNotEmpty(this.flowObjId)){
                await this.queryItemById()
            }else{
                this.$utils.showAlert('请联系管理员，未获取到字段变更信息！', {icon: 'none'});
                return
            }
        } else if (this.pageFrom === 'AccountItem') {
           this.approvalType = this.pageParam.data.applyType;
           this.flowObjId = this.pageParam.data.id;   // 申请编号
           await this.queryItemById();
       } else {
            if (approval_from === 'qw') { // 从小程序审批消息而来
                this.approvalId = sceneObj.query['approval_id'];
                this.approvalType =  sceneObj.query['approval_type'];
                this.approvalFlag = this.$utils.isNotEmpty(this.approvalId)
                this.flowObjId = sceneObj.query['flowObjId'];
                this.apVersion = sceneObj.query['apVersion'];
                if(this.$utils.isNotEmpty( this.flowObjId)){
                    await this.queryItemById();
                }else{
                    this.$utils.showAlert('请联系管理员，未获取到字段变更信息！', {icon: 'none'});
                    return
                }
            } else {
                if (this.pageParam.data) {
                    this.flowObjId = this.pageParam.data.id;   // 申请编号
                    await this.queryItemById();
                }
            }
        }
       if (this.approvalType === 'ConsumerInsert') {
           wx.setNavigationBarTitle({
               title: '消费者新建审批'
           })
       } else if (this.approvalType === 'ConsumerUpdate') {
           wx.setNavigationBarTitle({
               title: '消费者变更审批'
           })
       } else if (this.approvalType === 'FollowApproval') {
           wx.setNavigationBarTitle({
               title: '消费者认领审批'
           })
       } else if (this.approvalType === 'DigitalCertify') {
           wx.setNavigationBarTitle({
               title: '消费者数字化审批'
           })
       }
        //用户操作（同意、拒绝、撤回、转交申请）后更新
        this.$bus.$on('refreshApprovalDetail', async()=> {
            console.log('refreshApprovalDetail')
            await this.$refs.apprHisPointV3.initFlowRecord()
            await this.$refs.apprHisPointV3.$refs.apprProcess.initFlowRecord()
        });
    },
    methods: {
        /**
         * @desc 查询消费者标签
         * <AUTHOR>
         * @date 2022/8/8 16:49
         **/
        async queryConsumerTags (id) {
            return new Promise(async (resolve) => {
                if (this.$utils.isEmpty(id)) {
                    this.$showError('请检查消费者数据是否正确');
                    resolve([])
                    return;
                }
                const data = await this.$http.post(this.$env.dmpURL + '/link/consumerTags/queryTagsByConsumerIdNew', {acctId: id, ifActive: 'Y', pageFlag: false}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        resolve([])
                        this.$showError(`查询标签数据失败：${response.result}`);
                    }
                });
                if (data.success) {
                    resolve(data.rows);
                }else{
                    resolve([])
                }
            })
        },
        /**
         * @desc 跳转详情
         * <AUTHOR>
         * @date 2023/2/20 14:10
         **/
        async gotoItem () {
            let pageFrom = '';
            this.consumerData.row_status = ROW_STATUS.UPDATE;
            const consumerData = this.$utils.deepcopy(this.consumerData);
            if (this.businessDataOrigin) {
                const standardData = [];
                const customData = [];
                this.businessDataOrigin.forEach((item) => {
                    if (item.field.startsWith('custom')) {
                        customData.push(item);
                    } else {
                        standardData.push(item);
                    }
                })
                // 标准字段显示申请更改的值
                standardData.forEach((item) => {
                    this.$set(consumerData, item.field, item.value === '空值' ? '' : item.value);
                })
                // 如果k序列等级变更，需要查申请记录表中的动态字段值
                const find = standardData.find((item) => item.field === 'typeId');
                if (find) {
                    const data = await this.$http.post(this.$env.appURL + '/action/link/mapConType/queryById', {id: find.value}, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$showError(`查询变更申请记录失败：${response.result}`);
                        }
                    });
                    const record = data.result || {};
                    const arr = ['socialStatus', 'acctRank', 'socialCircle', 'positionGrade', 'enterpriseLevel', 'enterpriseSize', 'personnelType'];
                    arr.forEach((field) => {
                        if (record[field]) {
                            this.$set(consumerData, field, record[field]);
                        }
                    })
                }
                // 自定义字段显示申请更改的值
                if (consumerData.extendField) {
                    const extendField = JSON.parse(consumerData.extendField);
                    customData.forEach((item) => {
                        this.$set(extendField, item.field, item.value === '空值' ? '' : item.value);
                    })
                    consumerData.extendField = JSON.stringify(extendField);
                }
            }
            let param = {
                data: consumerData,
                userInfo: this.userInfo
            };
            if (this.approvalType === 'ConsumerInsert') {
                pageFrom = 'IneffectiveAccount';
                param = {
                    orgId: this.flowObj.orgId,
                    businessDataId: this.flowObj.id,
                    appStatus: this.flowObj.appStatus,
                    pageFrom: 'IneffectiveAccount',
                    approvalId: this.approvalId,
                    apVersion: this.apVersion
                };
            }
            this.$nav.push('/pages/lj-consumers/account/account-item-edit-page', param);
        },
        onBack () {
            this.queryItemById();
        },
        /**
         * @desc 查看消费者详情数据
         * <AUTHOR>
         * @date 2023/7/19 09:48
         **/
        async checkConsumer () {
            this.$nav.push('/pages/lj-consumers/account/account-item-page', {
                pageFrom: 'AccountChangeApply',
                data: this.consumerData
            })
        },
        /**
         * 消费者附件查询
         * @author:  胡益阳
         * @date:  2024/7/25
         */
        queryCusAttachment () {
            try {
                this.$http.post(`${this.$env.appURL}/action/link/sendDmp/send`,
                    {
                        pageFlag: false,
                        rows: 50,
                        page: 1,
                        dmpUrl: '/link/cusAttachment/queryByExamplePage',
                        order: 'desc',
                        sort: 'created',
                        filtersRaw: [
                            {id: 'sourceId', operator: '=', property: 'sourceId', value: this.formData.businessId},
                            {id: 'uploadStatus', operator: 'in', property: 'uploadStatus', value: '[Effective]'}
                        ]
                    }
                ).then((res) => {
                    if (res.success) {
                        this.imgList = res.rows;
                    }
                })
            } catch (e) {}
        },
        /**
         * @desc 查询变更记录
         * <AUTHOR>
         * @date 2022/11/18 10:43
         **/
        async queryItemById() {
            if (this.$utils.isEmpty(this.flowObjId)) {
                return;
            }
            const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {dmpUrl: '/link/fieldTemApp/queryById',id: this.flowObjId}, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`查询变更数据失败：${response.result}`);
                }
            });
            this.flowObj = data.result;
            this.isEdit = data.result.postnId === this.userInfo.postnId;
            data.result.businessData = JSON.parse(data.result.businessData);
            /* 系统来源入口 accntSourceFrom
            品牌公司 belongToCompany
            渠道消费者编号 id
            单位id corporateId
            推荐消费者id recommenderId
            所属客户id belongToStoreId
            内部推荐人id empId
            来源业务对象id attr03
            审核状态 auditStatus
            数据来源 dataSource
            考核终端ID assessTerminal
            消费者类型 consumerDataType */
            this.businessDataOrigin = this.$utils.deepcopy(data.result.businessData);
            console.log('data.result.businessData', data.result.businessData);
            data.result.businessData = data.result.businessData.filter(item =>
                item.field !== 'accntSourceFrom'
                && item.field !== 'belongToCompany'
                && item.field !== 'id'
                && item.field !== 'corporateId'
                && item.field !== 'recommenderId'
                && item.field !== 'belongToStoreId'
                && item.field !== 'empId'
                && item.field !== 'attr03'
                && item.field !== 'auditStatus'
                && item.field !== 'dataSource'
                && item.field !== 'assessTerminal'
                && item.field !== 'typeId'
                && item.field !== 'consumerDataType')
            const consumerTagsList = data.result.businessData.filter(item => item.type === 'LabelField');
            if(this.approvalType === 'FollowApproval'){
                this.consumerTagsList = await this.queryConsumerTags(data.result.businessId)
            }else{
                this.consumerTagsList = consumerTagsList.length > 0 ? consumerTagsList[0]['value'] : [];
            }
            if (this.consumerTagsList.length > 0) {
                let map = {};
                for (let i = 0; i < this.consumerTagsList.length; i++) {
                    let ai = this.consumerTagsList[i]
                    if (!map[ai.tagGroupId]) {
                        map[ai.tagGroupId] = [ai]
                    } else {
                        map[ai.tagGroupId].push(ai)
                    }
                }
                let res = []
                Object.keys(map).forEach(key => {
                    res.push({
                        tagGroupId: key,
                        // tagGroupName: map[key][0].l1TagName?`${map[key][0].l1TagName} > ${map[key][0].l2TagName} > ${map[key][0].tagGroupName}`:map[key][0]['tagGroupName'],
                        tagGroupName: map[key][0]['tagGroupName'].indexOf('>') !== -1 ? map[key][0]['tagGroupName'].slice(map[key][0]['tagGroupName'].lastIndexOf('>') + 1) : map[key][0]['tagGroupName'],
                        itemTags: map[key]
                    })
                })
                this.tagLabels = res;
            }
            this.formData = data.result;
            if (this.formData.applyType !== 'UpdateApproval') {
                this.formData.businessData = this.formData.businessData.filter((item)=> item.value !== '空值' && !this.$utils.isEmpty(item.value));
            }
            this.formData.businessData.forEach((item)=> {
                if (Array.isArray(item.value) && item.type !== 'LabelField' && item.value.length > 0) {
                    item.value = item.value.join(',')
                }
                if (Array.isArray(item.oldValue) && item.type !== 'LabelField' && item.oldValue.length > 0) {
                    item.oldValue = item.oldValue.join(',')
                }
            })
            this.formData.businessData.forEach((item) => {
                if (item.field === 'belongToBrand' && typeof item.value === 'string') {
                    item.value = item.value.split('/');
                }
            })
            if (this.formData.businessId && this.approvalType === 'ConsumerUpdate') {
                await this.queryConsumerById(this.formData.businessId);
            }
            this.queryCusAttachment()
            // 企业参数配置项中不包含职位公司id 对应逻辑
            if (this.cfgArray.includes(this.userInfo.coreOrganizationTile['l3Id'])) {

                switch (this.approvalType) {
                    // 隐藏type（影响力K序列等级）字段
                    case "ConsumerInsert":
                    case "FollowApproval":
                        this.formData.businessData = this.formData.businessData.filter(item => item.field !== 'type' && item.field!== 'socialCircle' && item.field!== 'positionGrade' && item.field!== 'enterpriseLevel' && item.field!== 'enterpriseSize' && item.field!== 'personnelType')
                        break;
                    case "ConsumerUpdate":
                        // this.formData.businessData = this.formData.businessData.filter(item => item.field !== 'type')
                        break;
                }
            }
            console.log('formData.businessData', this.formData.businessData);
        },
        /**
         * @desc 查询消费者信息
         * <AUTHOR>
         * @date 2022/11/21 17:10
         **/
        async queryConsumerById (accountId) {
            const data = await this.$http.post(this.$env.dmpURL + '/action/link/cdcPubConsumer/queryMpById', {
                id: accountId,
                inTerminal: 'Y'
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`查询消费者信息失败：${response.result}`);
                }
            });
            if (data.success) {
                this.consumerData = data.result;
            }
        },
        /**
         * @desc 拨打电话
         * <AUTHOR>
         * @date 2022/4/19 18:53
         **/
        makePhoneCall(data) {
            const number = data.mobilePhone1 || data.mobilePhone ||  data.phoneNumber;
            if (Boolean(number)) {
                wx.makePhoneCall({
                    phoneNumber: number
                });
            }
        }
    }
}
</script>

<style lang="scss">
.account-change-apply-page{
    /*deep*/.line-title {
        margin-bottom: 20px;
    }
    .change-list{
        font-size: 28px;
        background: white;
        padding: 24px 0;
        .change-data{
            .change-new-data-item{
                padding: 24px 24px 0;
                display: flex;
                justify-content: space-between;
                .new-label, .old-label{
                    min-width: 40%;
                }
                .old-label{
                    display: flex;
                }
                .change{
                    color: red;
                }
                .new-data{
                    color: #999;
                }
                .link-textarea {
                    width: 100%;
                }
            }
        }
    }
    .blank {
        height: 376px;
        width: 100%;
    }
    .label-info{
        background: white;
        font-size: 28px;
        .group-label {
            display: flex;
            justify-content: space-between;
            margin: 0 28px;
            padding: 28px 0;
            border-bottom: 1px solid #efefef;
        }
        .label-data-info{
            margin: 0 28px;
            padding: 0 0 28px;
            border-bottom: 1px solid #efefef;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            height: auto;
            .label-item{
                background-color: #2f69f8;
                color: white;
                display: inline-flex;
                align-items: center;
                flex-wrap: nowrap;
                font-size: 24px;
                border-radius: 8px;
                padding: 8px 16px;
                margin-right: 24px;
                margin-top: 24px;
            }
        }
    }
    .account-list-item{
        background: #FFFFFF;
        margin: 0px 24px;
        //height: 372px;
        border-radius: 16px;
    }
    .account-list{
        background-color: #FFFFFF;
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: column;

        .list-cell {
            position: relative;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;

            .media-list {
                position: relative;
                padding: 16px 24px;
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                width: 100%;

                .media-list-name {
                    height: 48px;
                    font-size: 32px;
                    color: #212223;
                    line-height: 48px;
                    font-weight: 600;
                    display: flex;
                    .acct-name {
                        overflow: hidden;
                        max-width: 360px;
                        text-overflow:ellipsis;
                        white-space: nowrap;
                    }
                    .is-certify {
                        margin-left: 20px;
                        width: 120px;
                        height: 48px;
                        image {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }
                .media-list-time {
                    .time {
                        color: black;
                        font-weight: bold;
                    }
                }

                .terminal-label {
                    margin: auto 0;
                    margin-left: 16px;
                    height: 36px;
                    width: 80px;
                    background: #F0F5FF;
                    border-radius: 4px;
                    font-size: 22px;
                    color: #3F66EF;
                    letter-spacing: 0;
                    text-align: center;
                    line-height: 36px;
                    font-weight: 400;
                }

                .media-list-label {
                    height: 36px;
                    margin: 16px 0;
                    display: flex;

                    .loyalty-level {
                        min-width: 80px;
                        padding: 0 15px;
                        margin-right: 16px;
                        background: #F0F5FF;
                        border-radius: 4px;
                        font-size: 22px;
                        color: #3F66EF;
                        letter-spacing: 0;
                        text-align: center;
                        line-height: 36px;
                        font-weight: 400;
                        white-space: nowrap; /* 防止文本换行 */
                        overflow: hidden; /* 隐藏超出的内容 */
                        text-overflow: ellipsis; /* 使用省略号表示超出的内容 */
                    }

                    .important-account {
                        width: 112px;
                        margin-right: 16px;
                        background: #FFF1EB;
                        border-radius: 4px;
                        font-size: 22px;
                        color: #FF461E;
                        line-height: 36px;
                        font-weight: 400;
                        text-align: center;
                        white-space: nowrap; /* 防止文本换行 */
                        overflow: hidden; /* 隐藏超出的内容 */
                        text-overflow: ellipsis; /* 使用省略号表示超出的内容 */
                    }

                    .black-gold-account {
                        width: auto;
                        padding: 0 5px;
                        background: #262626;
                        border-radius: 4px;
                        font-size: 22px;
                        color: #F0BE94;
                        line-height: 36px;
                        font-weight: 400;
                        text-align: center;
                        white-space: nowrap; /* 防止文本换行 */
                        overflow: hidden; /* 隐藏超出的内容 */
                        text-overflow: ellipsis; /* 使用省略号表示超出的内容 */
                    }
                }

                .media-list-info {
                    display: flex;
                    flex-direction: column;

                    .media-list-info-item {
                        height: 44px;
                        display: flex;
                        align-items: center;
                        margin-bottom: 8px;

                        .label {
                            width: 112px;
                            margin-right: 24px;
                            font-size: 28px;
                            color: #999999;
                            line-height: 44px;
                            font-weight: 400;
                        }

                        .media-list-info-text {
                            /*width: 520px;*/
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            font-size: 28px;
                            color: #333333;
                            line-height: 44px;
                            font-weight: 400;
                        }

                        .media-list-info-phone {
                            font-size: 28px;
                            color: #317DF7;
                            line-height: 44px;
                            font-weight: 400;
                        }
                    }
                }
            }
        }
    }
    .account-label {
        position: absolute;
        right: -28px;
        top: 4px;
        padding: 8px 48px 4px 32px;
        background: #2F69F8;
        transform: skew(30deg, 0deg);

        .label {
            font-size: 24px;
            color: #FFFFFF;
            text-align: center;
            line-height: 40px;
            font-weight: 400;
            transform: skew(-30deg, 0);
        }
    }}

    /*deep*/
    .link-item {
        padding: 0px;
        overflow: hidden;
    }

    .link-item-body-left{
        overflow: visible;
    }

    .approval-operator {
        .link-item {
            padding: 28px
        }
    }

</style>
