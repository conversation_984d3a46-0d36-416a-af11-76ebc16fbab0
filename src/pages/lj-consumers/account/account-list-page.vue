<!--
@createdBy 黄鹏
@date 2024/01/11
@description: ---  消费者列表/人群包详情
-->
<template>
    <link-page class="account-list-page">
        <link-auto-list :option="autoList" :hideCreateButton="!editFlag" :searchInputBinding="{props:{placeholder:'姓名/手机号/渠道消费者编号'}}">
            <view slot="top" class="top-tab-filter" v-if="pageFrom!=='PeopleScreen'">
                <view class="lnk-tabs-content" style="top: -260rpx;">
                    <view class="lnk-tabs-item" :style="tab.active ? 'color:#2f69f8;' : ''"
                          v-for="(tab, index) in classifyForm.first" :key="index" @tap="switchTab(tab)">
                        <view class="label-name-line">
                            <text class="label-name-text">{{tab.name}}</text>
                            <text class="label-name-tips" v-if="tab.field === 'certifing' && tab.active">{{ consumerNum }}</text>
                            <view class="line" v-if="tab.active"></view>
                        </view>
                    </view>
                </view>
                <view class="lnk-tabs-content" style="top: -180rpx">
                    <view class="lnk-tabs-item" :style="tab.active ? 'color:#2f69f8;' : ''"
                          v-for="(tab, index) in classifyForm.second" :key="index" @tap="switchTab(tab)">
                        <view class="label-name-line">
                            <text class="label-name-text">{{tab.name}}</text>
                            <view class="line" v-if="tab.active"></view>
                        </view>
                    </view>
                </view>
            </view>
            <view slot="filterGroup" class="top-filter" :key="pageOauth" v-if="pageFrom!=='PeopleScreen'">
                <scroll-view scroll-x="true" class="top-filter-content">
                    <view class="top-filter-info type" v-if="editFlag || pageFrom === 'ConsumerOverview'">
                        <view class="filter-type-item choose" @tap="chooseStoreData" :class="isStoreChosen ? ' chosen' : ''">所属客户<link-icon icon="mp-desc"/></view>
                            <!--暂时注释标签筛选 230913-->
<!--                        <view class="filter-type-item choose" @tap="tagDialogFlag=true;" :class="this.tagIdList.length ? ' chosen' : ''">标签<link-icon icon="mp-desc"/></view>-->
                        <view class="filter-type-item choose" v-if="pageOauth !== 'MY_POSTN_ONLY' && pageOauth !== 'MY_POSTN'" @tap="orgDialogFlag=true;" :class="isOrgChosen ? ' chosen' : ''">区域<link-icon icon="mp-desc"/></view>
                        <view class="filter-type-item choose" v-if="pageOauth !== 'MY_POSTN_ONLY'" @tap="postnDialogFlag = true;" :class="isPostChosen ? ' chosen' : ''">跟进人<link-icon icon="mp-desc"/></view>
                        <view class="line"></view>
                    </view>
                    <view class="top-filter-info" v-else-if="pageFrom === 'noticeDetail'">
                        <view class="tag-list">
                            <view v-for="(item,index) in timeFilterOption" class="tag-list-item"
                                  :class="item.checked? 'tagChecked' : ''" @tap="chooseTime(item)">
                                <view class="tag-item">{{ item.name }}</view>
                            </view>
                        </view>
                    </view>
                </scroll-view>
            </view>
            <view slot="top" class="top-total" v-if="showConsumerNum">合计：{{ consumerNum }}</view>
            <view slot="top" v-if="classifyItemList.length > 0">
                <scroll-view scroll-x="true" class="classify-filter-content">
                    <view class="classify-filter-list">
                        <view v-for="(item,index) in classifyItemList" :class="item.seq === classifyItemListActive.seq ? 'classify-filter-item label-checked' : 'classify-filter-item'" @tap="switchTabItem(item)">
                            {{ item.name }}
                        </view>
                    </view>
                </scroll-view>
            </view>
            <template slot-scope="{data,index}">
                <link-swipe-action>
                    <item :key="index" :data="data" :arrow="false" class="account-list-item">
                        <view class="account-list" slot="note">
                            <view class="list-cell">
                                <view class="media-list" :style="{'background-image': 'url(' + $imageAssets.consumerListItemBg + ')'}"
                                      @tap="gotoItem(data)">
                                    <view class="media-list-name" @tap="gotoItem(data)">
                                        <view class="acct-name">{{ data.acctName || data.consumerName || data.name }}</view>
                                        <view class="is-certify" v-if="data.certify">
                                            <view v-if="data.certify === 'certified'" style="height: 100%">
                                                <view class="certify-status" v-if="!$utils.isEmpty(data.certifyTime) && data.certifyTime !== '9999-12-31 23:59:59'" :style="{'background-image': 'url(' + $imageAssets.certifyTimeBg + ')'}">
                                                    <view class="certify-time">{{ `已认证-${data.certifyTime.split(' ')[0]}到期` }}</view>
                                                </view>
                                                <view class="certify-status" v-else :style="{'background-image': 'url(' + $imageAssets.storeStatusVerifiedImage + ')'}"></view>
                                            </view>
                                            <view class="certify-status" v-else :style="{'background-image': 'url(' + $imageAssets.storeStatusUnverifiedImage + ')'}"></view>
                                        </view>
                                    </view>
                                    <view class="media-list-label">
<!--                                        六到模型-->
                                        <view class="six-model" :class="data.sixToModel" v-if="data.sixToModel">{{data.sixToModel | lov('SIX_TO_MODEL')}}</view>
<!--                                        k序列等级-->
                                        <view class="loyalty-level" v-if="pageFrom === 'PeopleScreen'">{{ data.type | lov('ACCT_SUB_TYPE')}} </view>
                                        <view class="loyalty-level" v-else>{{data.subAcctType | lov('ACCT_SUB_TYPE')}}</view>
<!--                                        会员等级-->
                                        <view class="loyalty-level">{{ data.loyaltyLevel | lov('ACCT_MEMBER_LEVEL') }}</view>
<!--                                        超高身份-->
                                        <view class="black-gold-account" v-if="data.identityLevel && pageFrom!=='PeopleScreen'">
                                            {{data.identityLevel|lov('ACCT_SUB_TYPE')}}  {{(data.highTradePoint && data.isSuperHighFlag === 'Y') ? data.highTradePoint + '分' : (data.tradePoint && ($utils.isEmpty(data.isSuperHighFlag) || data.isSuperHighFlag === 'N')) ?  data.tradePoint + '分'  : ''}}
                                        </view>
                                    </view>
                                    <view class="media-list-basic">
                                        <view class="media-list-info">
                                            <view class="media-list-info-item">
                                                <view class="label">联系方式</view>
                                                <view class="media-list-info-phone" @tap.stop="makePhoneCall(data)">
                                                    {{ data.mobilePhone1 || data.mobilePhone ||  data.phoneNumber}}
                                                </view>
                                            </view>
                                            <view class="media-list-info-item">
                                                <view class="label">单位</view>
                                                <view class="media-list-info-text">{{ data.company || data.companyName }}</view>
                                            </view>
                                            <view class="media-list-info-item">
                                                <view class="label">职务</view>
                                                <view class="media-list-info-text">{{ data.position }}</view>
                                            </view>
                                            <view class="media-list-info-item">
                                                <view class="label">所属客户</view>
                                                <view class="media-list-info-text">{{ data.belongToStore }}</view>
                                            </view>
                                        </view>
                                        <view class="label-flag">{{getLabelFlag(data)}}</view>
                                    </view>
                                    <view class="account-label-info" :style="{'background-image': 'url(' + $imageAssets.consumerFollowerBg + ')'}">
                                        <view class="label">{{ data.tradePoint ? data.tradePoint + '分' : '' }}</view>
                                        <view class="label">跟进人：{{ data.fstName }}</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </item>
                    <link-swipe-option slot="option" @tap="invalidAccount(data)" v-if="editFlag">取消跟进</link-swipe-option>
                </link-swipe-action>
            </template>
        </link-auto-list>
        <link-sticky class="bottom-sticky" v-if="pageFrom === 'PeopleScreen'">
            <view class="check-button" @tap="checkLabels('label')">查看标签</view>
            <view class="check-button" @tap="checkLabels('condition')">圈选条件</view>
        </link-sticky>
        <!-- <link-fab-button :bottom="350" icon="icon-shenpi" @tap="gotofanweiList" v-if="pageFrom!=='PeopleScreen'"></link-fab-button> -->
        <link-fab-button :bottom="200" icon="icon-kehufeidan" @tap="gotoIneffectiveList" v-if="pageFrom!=='PeopleScreen'"></link-fab-button>
        <post-select ref="postSelect" :user-info="userInfo" :page-oauth="pageOauth" :show.sync="postnDialogFlag" @changePostnFilter="changeFilter"/>
        <tag-info :show.sync="tagDialogFlag" :tag-group-list="tagGroupList" @choose="chooseTagGroup"/>
        <org-select ref="orgSelect" :user-info="userInfo" :show.sync="orgDialogFlag" @changeOrgFilter="changeFilter"/>
        <water-mark></water-mark>
    </link-page>
</template>

<script>
import {ROW_STATUS} from "../../../utils/constant";
import StatusButton from "../../lzlj/components/status-button";
import LnkTaps from "../../core/lnk-taps/lnk-taps";
import ConsumerCommon from '../consumer-common';
import TagInfo from './components/tag-info/tag-info';
import OrgSelect from './components/org-select/org-select';
import PostSelect from './components/postn-select/postn-select';
import waterMark from '../../lzlj/components/water-mark';
import Taro from "@tarojs/taro";
import {PostnService} from "../../../utils/PostnService";
import {isEmpty} from "../../../components/painter/libs/util";

export default {
    name: "account-list-page",
    components: {LnkTaps, StatusButton, TagInfo, OrgSelect, PostSelect, waterMark},
    mixins: [ConsumerCommon()],
    data() {
        const pageFrom = this.pageParam.pageFrom || '';
        const page = Taro.getCurrentPages();
        const checkStartDate = this.pageParam.created || '';
        const pageOauthList = this.pageParam.secMenus || [{securityMode: 'MY_POSTN_ONLY', name: '我的数据'}];
        const sceneObj = this.$store.getters['scene/getScene'];
        const approval_from = sceneObj.query['approval_from'];
        const applyTo = sceneObj.query['applyTo'];
        const userInfo = this.$taro.getStorageSync('token').result;
        let filtersRawTemp = [
            {id: 'companyId', property: 'companyId', value: userInfo.coreOrganizationTile['l3Id'], operator: '='},
            {id: 'consumerType', property: 'consumerType', value: 'ChannelConsumer', operator: '='},
            {id: 'accntChannel', property: 'accntChannel', value: 'MarketingPlatform', operator: '='},
            {id: 'empFlag', property: 'empFlag', value: 'N', operator: '='},
            {id: 'followFlag', property: 'followFlag', value: 'Y', operator: '='}
        ];
        let editFlag = true;
        let url = this.$env.appURL + '/action/link/sendDmp/consumerListSend';
        let filterOption = [
            {label: '消费者性别', field: 'gender', type: 'lov', lov: 'GENDER'},
            {label: '是否注册会员', field: 'memberId', type: 'select',multiple: false, data: [
                    {name: '是', val: '1'},
                    {name: '否', val: '0'}]
            },
            {
                label: '所在地区',
                type: 'address',
                field: '["province", "city", "county"]'
            },
            {label: '生日类型', field: 'birthType', type: 'lov', lov: 'BIRTHDAY_TYPE', multiple: false},
            {label: '消费者生日', field: 'birthdayMd', type: 'date', view: 'MD', format: 'MM-DD'},
            {label: '是否重点跟进', field: 'impFlag', type: 'select',multiple: false, data: [
                    {name: '是', val: 'Y'},
                    {name: '否', val: 'N'}]
            },
            {label: '是否终端用户', field: 'terminalFlag', type: 'lov', lov: 'ACTIVE_FLAG'},
            {label: '推荐纳入浪潮计划投放', field: 'bringInto', type: 'lov', lov: 'QUERY_BRINGINTO', multiple: false},
            {label: '客户来源', field: 'sourceFrom', type: 'lov', lov: 'SOURCE_FROM'},
            {label: '系统来源入口', field: 'accntSourceFrom', type: 'lov', lov: 'ACCNT_SOURCE_FROM'},
        ];
        let pageOauth = 'MY_POSTN_ONLY';
        if (pageOauthList.length > 0) {
            pageOauth = pageOauthList[0].securityMode;
        }
        let param = {
            oauth: pageOauth,
            sort: 'lastUpdated',
            filtersRaw: filtersRawTemp,
            stayFields: 'id,acctName,mobilePhone1,subAcctType,fstName,loyaltyLevel,acctRank,companyId,impFlag'
        };
        const date = this.$date.filter(new Date(), 'MM-dd');
        let acctName = 'acctName'
        let mobilePhone = 'mobilePhone1'
        let id = 'id'
        if (pageFrom === 'noticeDetail') {
            url = this.$env.appURL + '/action/link/consumerBirth/queryBirthdayPage'
            editFlag = false;
            filterOption = [];
            filtersRawTemp = [];
            param = {
                attr1: date,
                attr2: date
            }
            acctName = 'consumerName'
            mobilePhone = 'mobilePhone'
        } else if (pageFrom === 'ConsumerOverview') {
            editFlag = false;
            url = this.$env.dmpURL + '/link/buildDetail/queryConsumerDTOPage';
            param = {
                reportId: this.pageParam.parentId,
                timeRange: this.pageParam.filterInfo.timeRange,
                dataRange: this.pageParam.filterInfo.dataRange,
                summaryType: this.pageParam.filterInfo.summaryType
            }
        } else if (pageFrom === 'PeopleScreen') {
            editFlag = false;
            filterOption = null;
            filtersRawTemp = [];
            url = this.$env.dmpURL + '/link/cdcConSrcLine/queryByExamplePage';
            param = {
                filtersRaw: [
                    {id: 'headId', property: 'headId',value: this.pageParam.headId, operator: '='},
                    {id: 'postnId', property: 'postnId',value: userInfo.postnId, operator: '='}
                ]
            };
            acctName = 'name';
            mobilePhone = 'phoneNumber';
        }
        let loadOnStart = true
        if (pageFrom === 'warningPage') {
            loadOnStart = false;
            param.oauth = 'ALL';
        } else if (approval_from === 'qw' && applyTo === 'dj') {
            // filtersRawTemp = filtersRawTemp.filter(item => item.property !== 'followFlag');
            // filtersRawTemp = this.$utils.deepcopy(filtersRawTemp).concat([
            //     {id: 'tag', property: 'tag', operator: 'NOT NULL', value: ''}
            // ])
            // param.filtersRaw = filtersRawTemp
            // param.djAttr01 = 'dangJian'
            loadOnStart = false;
        } else if (approval_from === 'qw' && page.length < 2 && applyTo !== 'dj') {
            filtersRawTemp = this.$utils.deepcopy(filtersRawTemp).concat([
                {id: 'postnId', property: 'postnId', value: sceneObj.query['postn_id'], operator: '='}
            ])
            loadOnStart = false;
        } else {
            loadOnStart = editFlag || pageFrom === 'PeopleScreen';
        }
        // 路由小于2代表从qw进来的
        if (page.length < 2) loadOnStart = false;
        const autoList = new this.AutoList(this, {
            module: this.$env.appURL + '/action/link/consumer',
            createPath: '/pages/lj-consumers/account/account-item-edit-page',
            url: {
                queryByExamplePage: url
            },
            filterOption: filterOption,
            loadOnStart: loadOnStart,
            disabled:{
                creatable: (() => {
                    return this.addFlag
                })
            },
            exactSearchFields: [
                {
                    field: acctName,
                    showValue: '姓名',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: true
                }, {
                    field: mobilePhone,
                    showValue: '手机号',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: true
                },{
                    field: id,
                    showValue: '渠道消费者编号',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: true
                }
            ],
            param: param,
            sortOptions: null,
            hooks: {
                async beforeCreateItem(param) {
                    const id = await this.$newId();
                    param.data = {
                        id: id,
                        row_status: ROW_STATUS.NEW,
                        consumerDataType: 'ChannelConsumer',
                        dataSource: 'MarketingPlatform',
                        dataType: 'Consumer',
                        accntSourceFrom: 'WeChatWork',
                        orgId: this.userInfo.orgId,
                        fstName: '',
                        postnId: this.userInfo.postnId,
                        belongToCompanyId: this.userInfo.coreOrganizationTile['l3Id'],
                        type: "ToBeFollowed",
                        birthType: 'Yang',
                        brandPreference: "",
                        hobby: "",
                        terminalFlag: 'N',
                    };
                    param.userInfo = this.userInfo;
                    param.pageFrom = "Account";
                },
                async beforeLoad(option) {
                    if (pageFrom === 'noticeDetail') {
                        option.param.sort = 'birth';
                    }
                    option.param.filtersRaw.forEach((val) => {
                        if(val.property === 'birthdayMd') {
                            val.value = val.value.slice(0,5);
                        }
                    });
                    for (let i = 0; i < option.param.filtersRaw.length; i++) {
                        if (option.param.filtersRaw[i].property === 'acctName' || option.param.filtersRaw[i].property === 'consumerName' || option.param.filtersRaw[i].property === 'belongToBrand') {
                            option.param.filtersRaw[i].operator = 'like';
                        }
                        // 为注册会员
                        if (option.param.filtersRaw[i].property === 'memberId' && option.param.filtersRaw[i].value === '1') {
                            option.param.filtersRaw[i].value = '';
                            option.param.filtersRaw[i].operator = 'not null';
                        }
                        // 非注册会员
                        if (option.param.filtersRaw[i].property === 'memberId' && option.param.filtersRaw[i].value === '0') {
                            option.param.filtersRaw[i].value = '';
                            option.param.filtersRaw[i].operator = 'IS NULL';
                        }
                        if (option.param.filtersRaw[i].property === 'birthType' && option.param.filtersRaw[i].value === 'NULL') {
                            option.param.filtersRaw[i].value = '';
                            option.param.filtersRaw[i].operator = 'IS NULL';
                        }
                        if (option.param.filtersRaw[i].property === 'bringInto' && option.param.filtersRaw[i].value === 'NULL') {
                            option.param.filtersRaw[i].value = '';
                            option.param.filtersRaw[i].operator = 'IS NULL';
                        }
                    }
                    this.queryConsumerNum(option.param)
                }
            },
            slots: {
                searchRight: () => (
                    <view class="filter-type-item" style="max-width: 224rpx;height: 72rpx;display: flex;align-items: center;justify-content: flex-end;padding-left: 30rpx;font-family: PingFangSC-Regular;font-size: 26rpx;color: #333333;line-height: 40rpx;font-weight: 400;" onTap={this.chooseOauthData}>{this.pageOauthName}<link-icon icon="mp-desc" style="color: #CCCCCC; margin: 4rpx 0 0 8rpx;"/></view>
                )
            }
        });

        return {
            pageOauth,
            pageOauthName: '',
            page,
            pageOauthList,
            checkStartDate, // 消息详情时间参数
            timeFilterOption: [{name: '前一天', val: 'nextDay', checked: false}, {
                name: '前三天',
                val: 'nextThreeDay',
                checked: false
            }], // {name: '当月', val: 'month', checked: false}
            editFlag,
            pageFrom,
            approval_from,
            applyTo,
            consumerNum: 0,
            certifingNum: 0,                          // 即将到期的消费者数量
            showConsumerNum: false,                   // 是否展示合计
            orgDialogFlag: false,                     // 组织筛选弹窗
            tagDialogFlag: false,                     // 标签筛选弹窗
            postnDialogFlag: false,                   // 跟进人筛选弹窗
            tagIdList: [],
            tagGroupId: '',
            tagsItemOption: new this.AutoList(this, { // 标签选择
                url: {
                    queryByExamplePage: this.$env.dmpURL + '/link/portalAccntTagGroup/queryTagItemsByTagGroup'
                },
                param: {
                    validFlag: 'Y',
                },
                searchFields: null,
                hooks: {
                    beforeLoad(option) {
                        delete option.param.sort;
                        delete option.param.order;
                        option.param.filtersRaw = [
                            ...option.param.filtersRaw,
                            {id: 'headId', property: 'headId', value: this.tagGroupId, operator: '='}
                        ]
                    }
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} className="select-box" arrow="false">
                            <link-checkbox val={data.id} toggleOnClickItem slot="thumb" loyaltyLevel></link-checkbox>
                            <view slot="title">{data.tagName}</view>
                        </item>)
                }
            }),
            tagGroupList: [],
            isGuoJiao: false,     // 判断是否查询国窖系公司的分类分级值列表
            filtersRawTemp,
            classifyForm: {
                first: [
                    {val: 'ALL', name: '全部', seq: '1', field: 'firstAll', active: true, type: 'first'},
                    {val: 'certified', name: '已认证', seq: '2', field: 'certify', active: false, type: 'first'},
                    {val: 'uncertified', name: '未认证', seq: '3', field: 'certify', active: false, type: 'first'},
                    {val: 'certifing', name: '即将到期', seq: '4', field: 'certifing', active: false, type: 'first'}
                ],
                second: [
                    {val: 'secondAll', name: '全部', seq: '1', field: 'secondAll', active: true, type: 'second'},
                    {val: 'subAcctType', name: 'K序列', seq: '4', field: 'subAcctType', active: false, type: 'second'},
                    {val: 'ACCT_MEMBER_LEVEL', name: 'V序列', seq: '5', field: 'loyaltyLevel', active: false, type: 'second'}
                ]
            },
            classifyListActive: {},
            classifyItemListActive: {},
            classifyItemList: [],
            autoList,
            isStoreChosen: false,       // 所属客户选择
            isOrgChosen: false,         // 区域选择
            isPostChosen: false,        // 跟进人选择
            userInfo,
            filterBrandList: [],        // 筛选-所属品牌列表（有父子值列表映射关系）
            filterOption,
            addFlag: true,     // 是否可以新建消费者
            tagGroupNotShow :true

        };
    },
    destroyed () {
        // this.$store.commit('coordinate/setCoordinate', this.coordinate)
    },
    async created() {
        this.getTemplateData();  // 进入列表页面获取消费者模板数据
        this.classifyListActive = this.classifyForm.first[0];
        // 不展示消费者添加按钮：不在值列表【ASSIGN_POSITION】中，且值列表状态为有效的，其余的职位类型进入【消费者列表】【市场活动添加消费者，新建按钮】【礼赠添加消费者新建】【动销添加消费者新建】【拜访添加消费者新建】【名单提报添加消费者新建】
        const lovData = await this.$lov.getLovByType('ASSIGN_POSITION');
        let sceneObj = await this.$scene.ready(); //消息场景对象
        const approval_from = sceneObj.query['approval_from'];
        const assignList = lovData.map(item => item.val);
        // 新酒业(鼎昊公司)不展示新建
        if(!assignList.includes(this.userInfo.positionType)||this.userInfo.coreOrganizationTile.brandCompanyCode === '1210') {
            this.addFlag = false;
        }
        if (this.pageFrom === 'noticeDetail') {
            await this.chooseTime(this.pageParam.type);
        } else if (this.pageFrom === 'ConsumerOverview') {
            this.autoList.option.loadOnStart = true;
        }
        // 查询党建共建消费者-applyTo=dj
        if (approval_from === 'qw' && sceneObj.query['applyTo'] === 'dj') {
            this.pageOauthName = '我的数据';
            await this.queryPosition();
            this.filtersRawTemp = this.filtersRawTemp.filter(item => item.property !== 'followFlag' && item.property !== 'empFlag');
            this.filtersRawTemp = this.$utils.deepcopy(this.filtersRawTemp).concat([
                {id: 'tag', property: 'tag', operator: 'NOT NULL', value: ''},
                {id: 'terminalFlag', property: 'terminalFlag', operator: '<>', value: 'Y'},
                {id: 'empFlag', property: 'empFlag', operator: '<>', value: 'Y'},
            ])
            this.autoList.option.param.filtersRaw = this.filtersRawTemp
            this.autoList.option.param.djAttr01 = 'dangJian'
            await this.autoList.methods.reload();
        } else if ((approval_from === 'qw' && this.page.length < 2) || this.pageFrom === 'warningPage') {
            this.pageOauthName = '我的数据';
            this.classifyForm.first = [
                {val: 'ALL', name: '全部', seq: '1', field: 'firstAll', active: false, type: 'first'},
                {val: 'certified', name: '已认证', seq: '2', field: 'certify', active: false, type: 'first'},
                {val: 'uncertified', name: '未认证', seq: '3', field: 'certify', active: false, type: 'first'},
                {val: 'certifing', name: '即将到期', seq: '4', field: 'certifing', active: true, type: 'first'}
            ];
            this.classifyListActive = this.classifyForm.first[3];
            await this.queryPosition();
            await this.switchTab(this.classifyForm.first[3]);
        } else if (this.pageOauthList.length > 0) {
            this.pageOauthName = this.pageOauthList[0].name;
        } else{
            this.pageOauthName = '我的数据';
        }
        const brandList =  await this.$lov.getLovByParentTypeAndValue({type: 'BRAND', parentType: 'ACTIVITY_COMPANY', parentVal: this.userInfo.coreOrganizationTile['l3Id']});
        if(brandList.length > 0) {
            this.autoList.option.filterOption.splice(8, 0, {label: '所属品牌', field: 'belongToBrand', type: 'lov', lov: 'BRAND', multiple: false, lovOption: {parentType: 'ACTIVITY_COMPANY', parentVal: this.userInfo.coreOrganizationTile['l3Id']}})
        }
        this.autoList.option.filterOption.push(
            {label: '是否筛选模型', field: 'consumerRule', lov:'CONSUMER_RULE',type:'lov',
        })
    },
    mounted () {
        // const todoListNumber = this.$taro.getStorageSync('todoListNumber');
        // if (todoListNumber['unDoAccount'] && todoListNumber['unDoAccount'] > 0) {
        //     this.$showError('请及时标记已认证未纳入浪潮的消费者');
        // }
        this.$bus.$on('consumerListFresh', data => {
            this.autoList.methods.reload();
        })
        this.initLevelData()
        this.queryTagGroups();

    },
    methods: {
        /**
         * @Description: 获取职位数据
         * @Author: 胡益阳
         * @Date: 2024/11/8
        */
        async queryPosition () {
            try {
                let sceneObj = await this.$scene.ready(); //消息场景对象
                const data = await this.$http.post('action/link/position/postnOfUserForMp')
                if (data.success) {
                    const result = data.result.find(item => item.postnId === sceneObj.query['postn_id'])
                    if (!isEmpty(result)) {
                        await this.pickerChange(result.postnId)
                    } else {
                        this.$showError('匹配职位ID失败！');
                    }
                } else {
                    this.$showError('查询职位数据出错！' + data.result);
                }
            } catch (err) {
                return Promise.reject(err)
            }
        },
        /**
         * 切换职位
         * @author:  胡益阳
         * @date:  2024/10/15
         */
        async pickerChange (postnid) {
            let result = await PostnService.change(postnid);
            if(!result)console.error('切换失败')
            // 更新职位之后，需要清除小程序模板缓存（有些模板是根据不同职位与组织去匹配的）
            this.$taro.removeStorageSync('link_qw_mp_tmpl');
            // 更新职位之后，需要清除首页待办消息提醒
            this.$taro.removeStorageSync('todoListNumber');
            //@@PAGE_CACHE代表是否表单续填写, 当不用续填且首次进入页面时存在活动缓存,则清空活动缓存;
            let PAGECACHE = this.$taro.getStorageSync('@@PAGE_CACHE');
            let MarketActivity = this.$taro.getStorageSync('MarketActivity');
            if (this.$utils.isNotEmpty(MarketActivity) && this.$utils.isEmpty(PAGECACHE)) {
                this.$taro.removeStorageSync('MarketActivity');
            }
            // 职位切换成功后清空消费者模板缓存数据
            this.$taro.removeStorageSync('template');
            this.filtersRawTemp.forEach((item) => {
                if(item.property === 'companyId') {
                    item.value = result.coreOrganizationTile['l3Id']
                }
            })
        },
        /**
         * 消费者计数查询
         * @author:  胡益阳
         * @date:  2024/6/18
         */
        async queryConsumerNum (e) {
            try {
                const param = this.$utils.deepcopy(e)
                // 查询企业参数配置-参数键STATISTICAL_POSITION_ID
                const obj = await this.$utils.getCfgProperty('STATISTICAL_POSITION_ID');
                const cfgArray = obj.split(',')
                this.showConsumerNum = cfgArray.includes(this.userInfo.positionType);
                if (!cfgArray.includes(this.userInfo.positionType)) return;
                param.dmpUrl = '/link/cdcPubConsumer/queryConsumerDTOCount'
                const value = param.filtersRaw.filter((item) => { return item.property === 'certify' })
                if (value.length === 0) {
                    param.countTypeList = [
                        {
                            key: 'ALL'
                        }
                    ]
                } else {
                    param.countTypeList = [
                        {
                            field: value[0].property,
                            value: value[0].value,
                            key: value[0].id
                        }
                    ]
                }
                this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', param,{
                    autoHandleError: false
                }).then((data) =>{
                    if (data.success){
                        this.consumerNum = data.result[param.countTypeList[0].key];
                    }
                })
            } catch (e) {
                console.error(e)
            }
        },
        /**
         * @desc 查看标签和圈选条件
         * <AUTHOR>
         * @date 2023/9/13 16:49
         * @param type 查看类型
         **/
        checkLabels(type) {
            this.$nav.push('/pages/lj-consumers/people-screen/people-screen-item-page', {id: this.pageParam.headId, type: type})
        },
        /**
         * @createdBy 曾宇
         * @date 2023/4/11
         * @methods: chooseOauthData
         * @description: 选择页面安全性
         **/
        chooseOauthData() {
            this.$actionSheet(() => (
                <link-action-sheet title="请选择数据范围" onCancel={() => {}}>
                    {this.pageOauthList.map((item) => {return <link-action-sheet-item label={item.name} onTap={() => this.pageOauthChange(item)}/>})}
                </link-action-sheet>
            ));
        },
        /**
         * @createdBy 曾宇
         * @date 2023/4/11
         * @methods: pageOauthChange
         * @para: oauth 安全性
         * @description: 页面安全性切换
         **/
        pageOauthChange(oauth) {
            this.$utils.showLoading();
            this.autoList.list = [];
            this.pageOauth = oauth.securityMode;
            this.pageOauthName = oauth.name;
            this.autoList.option.param.oauth = oauth.securityMode;
            this.$refs.postSelect.resetSelectData();
            this.$refs.orgSelect.resetSelectData();
            this.isOrgChosen = false;
            this.isPostChosen = false;
            this.clearFilter('orgId');
            this.clearFilter('postnId');
            this.autoList.methods.reload();
            this.$utils.hideLoading();
        },
        /**
         * @createdBy 曾宇
         * @date 2023/4/11
         * @methods: clearFilter
         * @description: 清除区域 跟进人筛选条件
         **/
        clearFilter(type) {
            const checkFlagIndex = this.autoList.option.param.filtersRaw.findIndex((item) => item.property === type);
            if (checkFlagIndex > -1) {
                this.autoList.option.param.filtersRaw.splice(checkFlagIndex, 1);
            }
        },
        /**
         * @createdBy 曾宇
         * @date 2023/4/10
         * @methods: changeFilter
         * @description: 组织 跟进人选择
         **/
        async changeFilter(item) {
            const dataList = this.$utils.deepcopy(item);
            let filtersRaw = this.autoList.option.param.filtersRaw;
            if(dataList.data.length === 0) {
                const index = filtersRaw.findIndex((val) => val.property === dataList.type);
                if (index > -1) {
                    filtersRaw.splice(index, 1);
                    this.autoList.option.param.filtersRaw = filtersRaw;
                    this.autoList.methods.reload();
                }
                if (dataList.type === 'orgId') {
                    this.isOrgChosen = false;
                } else {
                    this.isPostChosen = false;
                }
                return;
            }
            if (dataList.type === 'orgId') {
                this.isOrgChosen = true;
            } else {
                this.isPostChosen = true;
            }
            let list = [];
            dataList.data.forEach((item) => {
                list.push(item.id);
            });
            list = `[${list.toString()}]`;
            if (!filtersRaw.some((item1) => {return item1.property === item.type})) {
                filtersRaw.push({id: dataList.type, property: dataList.type, value: list, operator: 'in'});
            } else {
                for (let i = 0; i < filtersRaw.length; i++) {
                    if (filtersRaw[i].property === dataList.type) {
                        filtersRaw[i].value = list;
                        filtersRaw[i].operator = 'in';
                        break;
                    }
                }
            }
            this.autoList.option.param.filtersRaw = filtersRaw;
            this.autoList.methods.reload();
        },
        /**
         * @desc 跳转失效列表
         * <AUTHOR>
         * @date 2023/2/20 10:58
         **/
        gotoIneffectiveList() {
            this.$nav.push('/pages/lj-consumers/ineffective-account/ineffective-account-list-page'); // 以前的
        },
        /**
         * @desc 跳转泛微审批列表列表
         * <AUTHOR>
         * @date 2023/2/20 10:58
         **/
        gotofanweiList() {
            this.$nav.push('/pages/lj-consumers/ineffective-account-fanwei/ineffective-account-list-page'); // f泛微的流程接口
        },
        /**
         * @desc 筛选时间
         * <AUTHOR>
         * @date 2022/11/28 15:03
         **/
        async chooseTime(item) {
            this.timeFilterOption.forEach((timeItem) => {
                timeItem.checked = timeItem.val === item.val;
            });
            const nowDate = new Date(this.checkStartDate.replace(/-/g, '/')); // 当前时间
            let attr1 = this.$date.format(nowDate, 'MM-DD'); // 开始时间
            let attr2 = ''; // 结束时间
            switch (item.val) {
                case 'nextDay':
                    let nextDate = new Date(nowDate.getTime() + 24 * 60 * 60 * 1000); //后一天
                    attr2 = this.$date.format(nextDate, 'MM-DD');
                    attr1 = attr2
                    break;
                case 'nextThreeDay':
                    let nextThreeDate = new Date(nowDate.getTime() + 24 * 60 * 60 * 1000 * 3); //后三天
                    attr2 = this.$date.format(nextThreeDate, 'MM-DD');
                    attr1 = attr2;
                    break;
                case 'month':
                    const dateTime = this.$utils.getCurrentMonthDate();
                    attr1 = this.$date.format(dateTime.startDate, 'MM-DD');
                    attr2 = this.$date.format(dateTime.startDate, 'MM-DD');
                    break;
            }
            this.autoList.option.param.attr1 = attr1;
            this.autoList.option.param.attr2 = attr2;
            await this.autoList.methods.reload();
        },
        /** 根据标签组选择对应的标签值
         * @desc
         * <AUTHOR>
         * @date 2022/8/22 11:14
         **/
        async chooseTagGroup(item) {
            const data = this.$utils.deepcopy(item);
            if (data.length === 0) {
                this.tagIdList = [];
            } else {
                this.tagIdList = data.map(i => ({
                    tagGroupId: i.headId,
                    tagId: i.id
                }));
            }
            if (this.tagIdList.length > 0) {
                this.autoList.option.param['tagIdList'] = this.tagIdList.map((item) => {
                    return item.tagId
                });
                this.autoList.methods.reload();
            } else {
                delete this.autoList.option.param['tagIdList'];
                this.autoList.methods.reload();
            }
        },
        /**
         * @desc 选择门店
         * <AUTHOR>
         * @date 2022/7/5 09:58
         **/
        async chooseStoreData() {
            console.log('this.pageOauth', this.pageOauth);
            const data = await this.chooseStoreList(this.pageOauth);
            if (data) {
                this.isStoreChosen = true;
                this.autoList.option.param['belongToStoreIdList'] = data;
                this.autoList.methods.reload();
            } else {
                this.isStoreChosen = false;
                delete this.autoList.option.param['belongToStoreIdList'];
                this.autoList.methods.reload();
            }
        },
        /**
         * @desc 查询类型数据
         * <AUTHOR>
         * @date 2022/6/1 15:27
         **/
        async queryTypeList() {
            const data = await this.$http.post(this.$env.appURL + '/action/link/mapConType/queryByExamplePage', {
                oauth: 'ALL',
                pageFlag: true,
                rows: 500,
                page: 1,
                distinctFields: 'type',
                filtersRaw: [
                    {
                        id: 'companyId',
                        property: 'companyId',
                        value: this.userInfo.coreOrganizationTile['l3Id'],
                        operator: '='
                    },
                    {id: 'status', property: 'status', value: 'Active', operator: '='}]
            });
            if (data.success) {
                for (const item of data.rows) {
                    let name = await this.$lov.getNameByTypeAndVal('ACCT_SUB_TYPE', item.type)
                    this.$set(item, 'name', name);
                }
                data.rows.forEach((item, index) => {
                    this.$set(item, 'seq', index + 1)
                });
                this.classifyItemList = data.rows;
            }
        },
        /**
         * @desc 级联分类分级
         * <AUTHOR>
         * @date 2022/6/1 10:14
         **/
        async switchTab(item) {
            this.classifyForm[item.type].forEach((k) => {k.active = k.val === item.val})
            this.classifyListActive = item;
            if (item.val === 'ACCT_MEMBER_LEVEL') {
                const lovData = await this.$lov.getLovByType(item.val);
                if (this.isGuoJiao || this.userInfo.coreOrganizationTile['l3Id'] === '68359110689030144') {
                    this.classifyItemList = await this.$lov.getLovByParentTypeAndValue({
                        type: item.val,
                        parentType: 'ACCT_MEMBER_LEVEL_COMPANY',
                        parentVal: this.userInfo.coreOrganizationTile['l3Id']
                    });
                } else {
                    this.classifyItemList = lovData.filter(item => !item.parentId);
                }
                this.classifyItemListActive = {};
                // this.autoList.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp);
                // this.autoList.list = [];
                // this.autoList.methods.reload();
            } else if (item.val === 'subAcctType') {
                await this.queryTypeList();
                // this.autoList.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp);
                // this.autoList.list = [];
                // this.autoList.methods.reload();
            } else if (item.val === 'secondAll') {
                this.classifyItemListActive = {};
            } else if (item.val === 'certifing') {
                this.classifyItemList = [];
                this.classifyItemListActive = {};
                this.autoList.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp).concat([
                    {id: 'certify', property: 'certify', value: 'certified', operator: '='},
                    {id: 'certifyTime1', property: 'certifyTime', operator: 'NOT NULL'},
                    {id: 'certifyTime2', property: 'certifyTime', value: '9999-12-31 23:59:59', operator: '<>'}
                ]);
                this.autoList.list = [];
                await this.autoList.methods.reload();
                this.certifingNum = this.autoList.list.length
            } else if (item.val === 'certified') {
                // let itemList = await this.$lov.getLovByType('BRINGINTO');
                // itemList.push({val: 'unDeal', seq: 3, name: '待处理'});
                // this.classifyItemList = itemList.map(i => {
                //     if (i.val === 'Y') {
                //         return {
                //             val: i.val,
                //             name: '已纳入浪潮投放计划',
                //             seq: 1
                //         }
                //     } else if (i.val === 'N') {
                //         return {
                //             val: i.val,
                //             name: '未纳入浪潮投放计划',
                //             seq: 2
                //         }
                //     } else {
                //         return i;
                //     }
                // });
                this.classifyItemList = [];
                this.classifyItemListActive = {};
                this.autoList.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp).concat([{
                    id: 'certify',
                    property: 'certify',
                    value: 'certified',
                    operator: '='
                }]);
                this.autoList.list = [];
                this.autoList.methods.reload();
            } else if (item.val === 'uncertified') {
                this.classifyItemList = [];
                this.classifyItemListActive = {};
                this.autoList.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp).concat([{
                    id: 'certify',
                    property: 'certify',
                    value: 'uncertified',
                    operator: '='
                }]);
                this.autoList.list = [];
                this.autoList.methods.reload();
            } else {
                this.classifyItemList = [];
                this.classifyItemListActive = {};
                this.autoList.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp);
                this.autoList.list = [];
                this.autoList.methods.reload();
            }
        },
        /**
         * @desc 筛选数据
         * <AUTHOR>
         * @date 2022/6/1 10:19
         **/
        switchTabItem(item) {
            this.classifyItemListActive = item;
            let filtersRaw = this.autoList.option.param.filtersRaw;
            if (this.classifyListActive.field === 'subAcctType') {
                if (filtersRaw.length === 0) {
                    filtersRaw.push({id: 'subAcctType', property: 'subAcctType', value: item.type, operator: '='})
                } else {
                    if (!filtersRaw.some((item1) => {
                        return item1.property === 'subAcctType'
                    })) {
                        filtersRaw.push({id: 'subAcctType', property: 'subAcctType', value: item.type, operator: '='})
                    }
                    for (let i = 0; i < filtersRaw.length; i++) {
                        if (filtersRaw[i].property === 'subAcctType') {
                            filtersRaw[i].value = item.type;
                            filtersRaw[i].operator = '=';
                        }
                        if (filtersRaw[i].property === 'loyaltyLevel') {
                            filtersRaw.splice(i, 1);
                        }
                    }
                }
            } else if (this.classifyListActive.field === 'loyaltyLevel') {
                if (!filtersRaw.some((item1) => {
                    return item1.property === 'loyaltyLevel'
                })) {
                    filtersRaw.push({id: 'loyaltyLevel', property: 'loyaltyLevel', value: item.val, operator: '='})
                } else {
                    for (let i = 0; i < filtersRaw.length; i++) {
                        if (filtersRaw[i].property === 'loyaltyLevel') {
                            filtersRaw[i].value = item.val;
                            filtersRaw[i].operator = '=';
                            break;
                        }
                    }
                }
            } else if (this.classifyListActive.val === 'certified') {
                if (item.val !== 'unDeal') {
                    if (!filtersRaw.some((item1) => {
                        return item1.property === 'bringInto'
                    })) {
                        filtersRaw.push({id: 'bringInto', property: 'bringInto', value: item.val, operator: '='})
                    } else {
                        for (let i = 0; i < filtersRaw.length; i++) {
                            if (filtersRaw[i].property === 'bringInto') {
                                filtersRaw[i].value = item.val;
                                filtersRaw[i].operator = '=';
                                break;
                            }
                        }
                    }
                } else {
                    if (!filtersRaw.some((item1) => {
                        return item1.property === 'bringInto'
                    })) {
                        filtersRaw.push({id: 'bringInto', property: 'bringInto', value: '', operator: 'IS NULL'});
                    } else {
                        for (let i = 0; i < filtersRaw.length; i++) {
                            if (filtersRaw[i].property === 'bringInto') {
                                filtersRaw[i].value = '';
                                filtersRaw[i].operator = 'IS NULL';
                                break;
                            }
                        }
                    }
                }
            }
            this.autoList.list = [];
            this.autoList.option.param.filtersRaw = filtersRaw;
            this.autoList.methods.reload();
        },
        /**
         * @desc 将该消费者状态改为未分配
         * <AUTHOR>
         * @date 2022/4/22 16:10
         **/
        invalidAccount(item) {
            this.$taro.showModal({
                title: '提示',
                content: '取消后无法继续跟进消费者，是否确认取消跟进？',
                success: async (res) => {
                    if (res.confirm) {
                        this.$utils.showLoading();
                        const data = await this.$http.post(this.$env.dmpURL + '/link/consumer/unassignedUpdate', {
                            id: item.id,
                            interfaceSource: 'Artificial'
                        }, {
                            autoHandleError: false,
                            handleFailed: (response) => {
                                this.$utils.hideLoading();
                                this.$showError('取消跟进消费者失败！' + response.result);
                            }
                        });
                        if (data.success) {
                            this.$utils.hideLoading();
                            this.$message.success('取消跟进成功！');
                            this.autoList.methods.reload();
                        }
                    }
                }
            });
        },
        /**
         * @desc 跳转详情
         * <AUTHOR>
         * @date 2022/4/19 18:55
         **/
        gotoItem(data) {
            if (!this.editFlag) return;
            this.$nav.push('/pages/lj-consumers/account/account-item-page', {
                data: {...data,pageOauth:this.pageOauth},
                "pageOauth":this.pageOauth
            });
        },
        /**
         * @desc 拨打电话
         * <AUTHOR>
         * @date 2022/4/19 18:53
         **/
        makePhoneCall(data) {
            const number = data.mobilePhone1 || data.mobilePhone ||  data.phoneNumber;
            if (Boolean(number)) {
                wx.makePhoneCall({
                    phoneNumber: number
                });
            }
        },
         /**
          * @createdBy 黄鹏
          * @date 2024/10/10
          * @methods: getLabelFlag
          * @para:
          * @description: 终端和重点客户同时出现时，优先展示终端
          **/
         getLabelFlag (data) {
             if (data.impFlag === 'Y' && this.pageFrom!=='PeopleScreen' && data.terminalFlag === 'Y') {
                 return '终端';
             } else {
                 if (data.impFlag === 'Y' && this.pageFrom!=='PeopleScreen') {
                     return '重点客户';
                 } else if (data.terminalFlag === 'Y') {
                     return '终端';
                 } else {
                     return '';
                 }
             }
         }
    }
}
</script>

<style lang="scss">
.account-list-page {
    .link-input.link-input-text-align-left {
        text-align: left;
        max-width: 490px;
    }
    .classify {
        height: 100px;
    }

    .classify-height {
        height: 200px;
    }

    .top-filter {
        flex: 1;
        overflow-x: hidden;

        .top-filter-content {
            width: 100%;

            .top-filter-info {
                display: flex;
                white-space: nowrap;
                font-size: 24px;
                padding: 8px 24px;
                align-items: center;
                flex-wrap: nowrap;

                .filter-type-item {
                    max-width: 124px;
                    height: 40px;
                    margin: 12px 4px;
                    font-size: 26px;
                    color: #333333;
                    line-height: 40px;
                    font-weight: 400;
                    text-align: center;

                    .link-icon {
                        width: 16px;
                        height: 12px;
                        color: #CCCCCC;
                        margin-left: 8px;
                    }
                }

                .line {
                    width: 2px;
                    height: 32px;
                    position: absolute;
                    right: 4px;
                    top: 24px;
                    background-color: #CCCCCC;
                    border: 1px solid rgba(204,204,204,1);
                }
            }
        }

        .type {
            //width: 160px;

            .choose {
                margin: 15px 0;
                height: 50px;
                line-height: 50px;
                border-radius: 6px;
                padding: 0 20px;
            }

            .chosen {
                background: #EDF3FF;
                color: #2F69F8!important;
            }
        }

        .tag-list {
            display: flex;

            .tag-list-item {
                padding: 8px 16px;
                margin-right: 8px;
                white-space: nowrap;
                display: inline-block;
                background-color: #f2f2f2;
                color: #333333;
                border-radius: 4px;
            }

            .tagChecked {
                background-color: rgba(47, 105, 248, 0.1) !important;
                color: #2f69f8 !important;
            }
        }
    }

    .top-total {
        box-sizing: border-box;
        width: calc(100% - 68px);
        line-height: 44px;
        height: 60px;
        font-size: 26px;
        margin-left: 20px;
        padding: 8px 24px;
        color: #333333;
    }

    .search-right-item {
        width: 212px;
        height: 72px;
        display: flex;
        align-items: center;
        margin-left: 40px;
        box-sizing: border-box;
        font-size: 26px;
        color: #333333;
        line-height: 40px;
        font-weight: 400;
    }

    .classify-filter-content {
        .classify-filter-list {
            height: 72px;
            display: flex;
            align-items: center;

            .classify-filter-item {
                min-width: 170px;
                height: 56px;
                padding: 0 10px;
                box-sizing: border-box;
                margin: 0 0 16px 24px;
                font-size: 24px;
                color: #333333;
                letter-spacing: 0;
                text-align: center;
                line-height: 56px;
                font-weight: 400;
                border: 1px solid rgba(221,221,221,1);
                border-radius: 28px;
            }

            .label-checked {
                color: #3F66EF;
                border: 0.5px solid rgba(63,102,239,1);
            }
        }
    }

    /*deep*/
    .link-item {
        padding: 0;
        overflow: hidden;
    }

    .link-item-body-left{
        overflow: visible;
    }

    .account-list {
        background-color: #FFFFFF;
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: column;

        .list-cell {
            position: relative;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;

            .media-list {
                position: relative;
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                width: 100%;

                .media-list-name {
                    height: 40px;
                    padding: 28px 28px 0 28px;
                    display: flex;
                    align-items: center;
                    .acct-name {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 600;
                        font-size: 28px;
                        color: #212223;
                        line-height: 40px;
                        text-align: left;
                        font-style: normal;
                        overflow: hidden;
                        max-width: 360px;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                    .is-certify {
                        margin-left: 8px;
                        height: 44px;
                        .certify-status {
                            min-width: 120px;
                            height: 100%;
                            background-size: 100% 100%;
                            background-repeat: no-repeat;
                            .certify-time{
                                margin-left: 46px;
                                margin-right: 20px;
                                height: 44px;
                                font-family: PingFangSC, PingFang SC;
                                font-weight: 500;
                                font-size: 20px;
                                color: #212223;
                                line-height: 44px;
                                text-align: left;
                                font-style: normal;
                                background: linear-gradient(0deg, #FFF1CF 0%, #FFCF96 100%);
                                -webkit-background-clip: text;/*将设置的背景颜色限制在文字中*/
                                -webkit-text-fill-color: transparent;
                            }
                        }
                    }
                }

                .media-list-label {
                    height: 36px;
                    margin: 16px 0;
                    padding: 0 28px;
                    display: flex;
                    width: calc(100vw - 96px);

                    .six-model{
                        margin-right: 12px;
                        padding: 0 12px;
                        height: 36px;
                        width: auto;
                        border-radius: 4px;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 22px;
                        line-height: 36px;
                        text-align: left;
                        font-style: normal;
                        &.Hear{
                            color: #167EE3;
                            background: linear-gradient( 335deg, #CEE5FD 0%, #EAF8FF 37%, #C0E4FF 100%);
                        }
                        &.RepeatBuy{
                            color: #B27300;
                            background: linear-gradient( 335deg, #FEE5B7 0%, #FFF9EA 37%, #F3D59F 100%);
                        }
                        &.Drink{
                            color: #554BFF;
                            background: linear-gradient( 335deg, #E0DDFF 0%, #F1F0FF 37%, #D6D3F9 100%);
                        }
                        &.BecomeFan{
                            color: #E051D5;
                            background: linear-gradient( 326deg, #FFD2FC 0%, #FFF9FF 49%, #FFD2FC 100%);
                        }
                        &.See{
                            color: #E68536;
                            background: linear-gradient( 142deg, #FFD9BB 0%, #FFF8F2 45%, #FFE5D1 100%);
                        }
                        &.Buy{
                            color: #05C49B;
                            background: linear-gradient( 142deg, #A2EFDE 0%, #EDFDFA 45%, #C5F2E8 100%);
                        }
                    }

                    .loyalty-level {
                        margin-right: 12px;
                        padding: 0 12px;
                        height: 36px;
                        width: auto;
                        border-radius: 4px;
                        border: 1px solid #764412;
                        transform: rotateZ(360deg);
                        opacity: 0.8;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 22px;
                        color: #764412;
                        line-height: 36px;
                        text-align: left;
                        font-style: normal;
                        white-space: nowrap; /* 防止文本换行 */
                        overflow: hidden; /* 隐藏超出的内容 */
                        text-overflow: ellipsis; /* 使用省略号表示超出的内容 */
                    }

                    .black-gold-account {
                        margin-right: 12px;
                        padding: 0 12px;
                        height: 36px;
                        background: #262626;
                        border-radius: 4px;
                        width: auto;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 22px;
                        color: #F0BE94;
                        line-height: 36px;
                        text-align: left;
                        font-style: normal;
                        white-space: nowrap; /* 防止文本换行 */
                        overflow: hidden; /* 隐藏超出的内容 */
                        text-overflow: ellipsis; /* 使用省略号表示超出的内容 */
                    }
                }

                .media-list-basic{
                    padding: 0 28px;
                    display: flex;
                    align-items: flex-end;
                    justify-content: space-between;
                    .media-list-info {
                        width: 516px;
                        display: flex;
                        flex-direction: column;

                        .media-list-info-item {
                            height: 44px;
                            display: flex;
                            align-items: center;
                            margin-bottom: 8px;

                            .label {
                                width: 112px;
                                height: 44px;
                                margin-right: 24px;
                                font-family: PingFangSC, PingFang SC;
                                font-weight: 400;
                                font-size: 28px;
                                color: #9EA1AE;
                                line-height: 44px;
                                text-align: left;
                                font-style: normal;
                            }

                            .media-list-info-text {
                                width: calc(100% - 136px);
                                height: 44px;
                                overflow-x: scroll;
                                white-space: nowrap;
                                font-family: PingFangSC, PingFang SC;
                                font-weight: 400;
                                font-size: 28px;
                                color: #333333;
                                line-height: 44px;
                                text-align: left;
                                font-style: normal;
                            }

                            .media-list-info-phone {
                                width: calc(100% - 136px);
                                height: 44px;
                                overflow-x: scroll;
                                font-family: PingFangSC, PingFang SC;
                                font-weight: 400;
                                font-size: 28px;
                                color: #3F66EF;
                                line-height: 44px;
                                text-align: left;
                                font-style: normal;
                            }
                        }
                    }
                    .label-flag{
                        width: 134px;
                        font-family: YouSheBiaoTiHei;
                        font-size: 72px;
                        color: #333333;
                        line-height: 60px;
                        margin-bottom: 12px;
                        text-align: center;
                        background: linear-gradient(90deg, #F4EDE7 0%, #F7E8DC 100%);
                        -webkit-background-clip: text;/*将设置的背景颜色限制在文字中*/
                        -webkit-text-fill-color: transparent;
                    }
                }
            }
        }
    }

    .account-label-info {
        height: 60px;
        padding: 0 28px;
        margin: 0 6px 6px 6px;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .label {
            height: 44px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 24px;
            color: #9EA1AE;
            line-height: 44px;
            text-align: center;
            font-style: normal;
        }
    }

    .account-list-item {
        background: #FFFFFF;
        margin: 24px 24px 0 24px;
        border-radius: 16px;
    }

    .link-auto-list-query-bar-filter-group {
        margin-top: 176px;
    }

    .top-tab-filter {
        position: relative;
    }
    .lnk-tabs-content {
        width: 100%;
        box-sizing: border-box;
        padding: 0 36px;
        position: absolute;
        display: flex;
        justify-content: flex-start;
        flex-wrap: nowrap;
    }

    .lnk-tabs-item {
        margin-right: 70px;
        height: 92px;
        line-height: 92px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .label-name-line {
            position: relative;
            font-size: 28px;
            margin-left: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .label-name-tips {
                position: absolute;
                top: 0;
                right: 0;
                color: red;
                transform: translate(100%, -20%);
            }
            .line {
                height: 8px;
                width: 56px;
                border-radius: 16px 16px 0 0;
                background-color: #2f69f8;
                box-shadow: 0 3px 8px 0 rgba(47,105,248,0.63);
                margin-top: -8px;
            }
        }
    }

    .list-taps .lnk-tabs{
        top: 96px !important;
        border-top: none;
    }

    .link-auto-list-top-bar {
        border-bottom: none;
    }

    /*deep*/
    .link-swipe-option-container .link-swipe-option {
        width: 100px;
        height: 70px !important;
        border-radius: 80px;
        font-size: 28px !important;
    }
    /*deep*/
    .icon-kehufeidan {
        font-size: 1.3em !important;
    }
    .bottom-sticky {
        width: 100%;
        border-top: 1px solid #F2F2F2;
        height: 166px;
        .link-sticky-content {
            justify-content: flex-end !important;
            height: 166px;
        }
        .check-button{
            margin-right: 20px;
            width: 160px;
            height: 60px;
            border: 1px solid rgba(153,153,153,1);
            border-radius: 8px;
            font-size: 28px;
            color: #333333;
            text-align: center;
            line-height: 60px;
            font-weight: 400;
        }
    }
}
</style>
