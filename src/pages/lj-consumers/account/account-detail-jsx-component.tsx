import {defineComponent, getCurrentInstance, set} from "link-taro-component";
import {VNode} from "vue/types/umd";

interface NewAccountDetailOption {
    ctrlCode: string,                           //组件名
    //配置信息
    values: {
        field: string,                          //绑定字段
        fieldName: string,                      //字段显示值
        province: string,
        fieldType: string,
        city: string,
        district: string,
        lovType?: string,                       //值列表类型
        view?: string,                          //日期的视图类型
        displayFormat?: string,                 //显示值格式化字符串
        valueFormat?: string,                   //实际值格式化字符串
        option?: string,                        //link-object配置对象名称
    },
}

const newAccountDetailComponents: {
    [k: string]: (
        option: NewAccountDetailOption,
        formData: any,
        state: { linkObjectBinding: null }
    ) => VNode | null
} = {
    'link-input'(option, formData) {
        return (
            <link-input v-model={formData[option.values.field]} placeholder={option.values.placeholder}/>
        )
    },
    'link-date'(option, formData) {
        return (
            <link-date v-model={formData[option.values.field]}
                       view={option.values.view}
                       placeholder={option.values.placeholder}
                       displayFormat={option.values.displayFormat}
                       valueFormat={option.values.valueFormat}/>
        )
    },
    'link-lov'(option, formData) {
        return (
            <link-lov v-model={formData[option.values.field]}
                      type={option.values.lovType} placeholder={option.values.placeholder}/>
        )
    },
    'link-select'(option, formData) {
        return (
            <link-select v-model={formData[option.values.field]} multiple={option.values.multiple} placeholder={option.values.placeholder}>
                {option.values.complexSelectData.map(item => {
                    return <link-select-option  label={item.name} val={item.name}/>
                })}
            </link-select>
        )
    },
    'link-address'(option, formData) {
        return (
            <link-address
                view="pcd"
                placeholder={option.values.placeholder}
                province={formData[option.values.field]['province']}
                city={formData[option.values.field]['city']}
                district={formData[option.values.field]['district']}
                {...{on: {'update:province': val => set(formData[option.values.field], 'province', val),
                        'update:city': val => set(formData[option.values.field], 'city', val),
                        'update:district': val => set(formData[option.values.field], 'district', val)}}}/>
        )
    },
    'link-number'(option, formData) {
        return (
            <link-number v-model={formData[option.values.field]}
                         placeholder={option.values.placeholder}
                         showType="input"/>
        )
    },
    'view-line'(option, formData) {
        return (
            <view style={'width: 100%;height: 25px;'}/>
        )
    },
    'link-textarea'(option, formData) {
        return (
            <link-textarea placeholder={option.values.placeholder}
                v-model={formData[option.values.field]}
            />
        )
    },
}

export default defineComponent({
    props: {
        option: {type: Object as any as new() => NewAccountDetailOption, required: true},
        formData: {type: Object, require: true},
    },
    setup(props) {

        const ctx = getCurrentInstance()!

        return () => newAccountDetailComponents[props.option.ctrlCode].apply(ctx, [props.option, props.formData])
    }
})
