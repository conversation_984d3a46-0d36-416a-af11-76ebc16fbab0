<template>
  <link-dialog ref="dialog" v-model="showModelDetail" @hide="comfirm" @cancel="comfirm">
    <view slot="head">
      <view>
        <view class="menu-stair">
          <view class="line">
            <view class="line-top"></view>
            <view class="line-bottom"></view>
          </view>
          <view class="stair-title">模型基本信息</view>
        </view>
      </view>
    </view>
    <view class="con-info" style="padding: 0;">
      <view class="dia-item">
        <view class="dia-item-name">模型ID</view>
        <view class="dia-item-value">{{detailData.id}}</view>
      </view>
      <view class="dia-item">
        <view class="dia-item-name">模型名称</view>
        <view class="dia-item-value">{{detailData.ruleName||'--'}}</view>
      </view>
      <view class="dia-item">
        <view class="dia-item-name">公司</view>
        <view class="dia-item-value">{{detailData.companyName}}</view>
      </view>
      <view class="dia-item">
        <view class="dia-item-name">关联人群包</view>
        <view class="dia-item-value">{{detailData.crowdName}}</view>
      </view>
      <view class="dia-item">
        <view class="dia-item-name">说明</view>
        <view class="dia-item-value clamp-4-lines"></view>
        <view style="margin-top: 8rpx;width: 100%;">
          <link-textarea v-model="detailData.comments" disabled></link-textarea>
        </view>
      </view>
    </view>
    <link-button slot="foot" @tap="comfirm">确定</link-button>
  </link-dialog>
</template>

<script>
// import AccountDetailJsxComponent from './account-detail-jsx-component';

export default {
//   components: { AccountDetailJsxComponent },
  props: {
    value: false,
    modelDetailData: {
        default:()=>({})
    },
  }, data () {
    return {
      showModelDetail: false,
      detailData: {},
    }
  },
  watch: {
    value (n) {
      this.showModelDetail = n;
    },
    modelDetailData (n) {
      n && Object.keys(n).forEach(e => {
        this.$set(this.detailData, e, n[e])
      })
      this.getRuleInfo();
      !this.detailData.companyName && this.getModelInfo();
    }
  },
  methods: {
    async getRuleInfo () {
      const { rows, success } = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send',
        {
          "page": 1,
          "pageFlag": true,
          "onlyCountFlag": false,
          "filtersRaw": [{ "id": "paramFiltersRaw0", "property": "ruleId", "value": this.modelDetailData&&this.modelDetailData.id }],
          "rows": 30,
          "dmpUrl": "/link/cusOpenBotRuleItem/queryByExamplePage",
          "oauth": "ALL",
          "sort": "created",
          "order": "desc"
        }
      );
      // 展示生效内容
      const item = rows.find(e => e.status === 'Effective') || {}
      this.$set(this.detailData, 'ruleLineName', item['ruleLineName'] || '--');
      this.$set(this.detailData, 'crowdName', item['crowdName'] || '--');
      this.$set(this.detailData, 'rulelineComments', item['comments'] || '--');

    },
    async getModelInfo () {
      const { rows, success } = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send',
        {
          "page": 1,
          "pageFlag": true,
          "onlyCountFlag": false,
          "filtersRaw": [{ "id": "paramFiltersRaw0", "property": "id",operator:"=", "value": this.modelDetailData&&this.modelDetailData.id }],
          "rows": 30,
          "dmpUrl": "/link/cusOpenBotRule/queryByExamplePage",
          "oauth": "ALL",
          "sort": "created",
          "order": "desc"
        }
      );
      // 展示生效内容
      const item = rows.find(e => !!e) || {}
      this.$set(this.detailData, 'companyId', item['companyId'] || '--');
      this.$set(this.detailData, 'comments', item['comments'] || '--');
      this.$set(this.detailData, 'companyName', item['companyName'] || '--');
    },
    comfirm () {
      this.showModelDetail = false;
      this.$emit('input', false)
    }
  }
}
</script>

<style lang="scss">
.dia-item {
  display: flex;
  margin-bottom: 24rpx;
  align-items: center;
  flex-wrap: wrap;
  .link-textarea{
    padding: 0;
    background-color: #eee;
    border-radius: 4rpx;
  }
  .dia-item-name {
    font-size: 28rpx;
    color: #999999;
    line-height: 44rpx;
    min-width: 120rpx;
  }
  .dia-item-value {
    flex: 1;
    text-align: right;
    color: #333333;
    line-height: 1.1;
  }
}
.menu-stair {
    // margin-left: 0;
    width: 500rpx;
  //   padding-top: 24px;
  @include flex-start-center;

  .line {
    .line-top {
      width: 8px;
      height: 16px;
      background: #3fe0e2;
    }

    .line-bottom {
      width: 8px;
      height: 16px;
      background: #2f69f8;
    }
  }

  .stair-title {
    margin-left: 16px;
    font-family: PingFangSC-Semibold, serif;
    font-size: 32px;
    color: #262626;
    letter-spacing: 1px;
    line-height: 32px;
  }
}
.clamp-4-lines {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
  overflow: hidden;
  max-width: 400rpx;
}
.con-info {
  width: 100%;
  .rights-item {
    width: 100%;
    min-height: 56px;
    font-size: 28px;
    margin-bottom: 28rpx;
    line-height: 1.2;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .rights-item-name {
      width: 300px;
      color: #595959;
    }

    .rights-item-value {
      color: #262626;
    }

    .rights-item-value1 {
      width: calc(100% - 300px);
      color: #262626;
      white-space: nowrap;
    }

    .rights-item-value2 {
      color: #569bf5;
    }

    .rights-item-value-tip {
      color: #59595996;
      font-size: 18px;
      margin-left: 18px;
    }
  }
}
</style>
