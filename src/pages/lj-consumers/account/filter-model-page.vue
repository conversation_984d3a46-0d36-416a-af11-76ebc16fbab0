<template>
  <link-page class="booked-order-list-page">
    <lnk-taps :taps="orderTypeOptions" v-model="orderTypeActive" @switchTab="onTap"></lnk-taps>
    <view class="blank"></view>
    <view class="blank" v-show="orderTypeActive.val !== 'fiterModelTab'" style="background: #fff;"></view>
    <link-auto-list v-show="orderTypeActive.val !== 'fiterModelTab'" :option="autoList" :hideCreateButton="orderTypeActive.val === 'fiterModelTab' || !showBtn">
      <view slot="top" class="top-tab-filter">
        <view class="lnk-tabs-content" style="top: -220rpx;">
          <view class="lnk-tabs-item" :style="tab.active ? 'color:#2f69f8;' : ''" v-for="(tab, index) in classifyForm" :key="index" @tap="switchTab(tab)">
            <view class="label-name-line">
              <text class="label-name-text">{{tab.name}}</text>
              <view class="line" v-if="tab.active"></view>
            </view>
          </view>
        </view>
      </view>

      <template slot-scope="{data,index}">
        <link-swipe-action>
          <item :key="index" :data="data" :arrow="false" class="account-list-item">
            <view class="account-list" slot="note">
              <view class="list-cell">
                <view class="media-list" @tap="gotoItem(data)">
                  <view class="media-list-name">
                    <view class="acct-name">{{ data.acctName || data.consumerName || data.name }}</view>
                    <view class="terminal-label" v-if="data.terminalFlag === 'Y'">终端</view>
                    <view class="is-certify" v-if="data.certify">
                      <image :src="data.certify === 'certified' ? $imageAssets.storeStatusVerifiedImage : $imageAssets.storeStatusUnverifiedImage"></image>
                    </view>
                  </view>
                  <view class="media-list-time" v-if="data.certify === 'certified' && !$utils.isEmpty(data.certifyTime) && data.certifyTime !== '9999-12-31 23:59:59'">
                    <view class="time">{{ `${data.certifyTime.split(' ')[0]}认证到期` }}</view>
                  </view>
                  <view class="media-list-info">
                    <link-button class="btn" size="mini" v-show="!classifyForm[1].val" @tap="toPeople(data)">分配</link-button>
                    <view class="media-list-info-item">
                      <view class="label">联系方式</view>
                      <view class="media-list-info-phone" @tap.stop="makePhoneCall(data)">
                        {{ data.mobilePhone1 || data.mobilePhone ||  data.phoneNumber }}
                      </view>
                    </view>
                    <view class="media-list-info-item">
                      <view class="label">开瓶终端</view>
                      <view class="media-list-info-text">{{ data.terminalName }}</view>
                    </view>
                    <view class="media-list-info-item">
                      <view class="label">终端负责人</view>
                      <view class="media-list-info-text">{{ data.terminalFstName }}</view>
                    </view>
                    <view class="media-list-info-item">
                      <view class="label-2">开瓶数量(大单品)</view>
                      <view class="media-list-info-text">{{ data.openBottles }}</view>
                    </view>
                    <view class="media-list-info-item">
                      <view class="label">模型名称</view>
                      <view class="media-list-info-text">{{ data.ruleName }}</view>
                    </view>
                  </view>
                </view>
              </view>
              <view class="account-label">
                <view class="label">{{ data.fstName }}跟进</view>
              </view>
            </view>
          </item>
        </link-swipe-action>
      </template>
    </link-auto-list>
    <link-auto-list v-show="orderTypeActive.val === 'fiterModelTab'" :option="filterList" :hideCreateButton="true">
      <template slot-scope="{data,index}">
        <link-swipe-action>
          <item :key="index" :data="data" :arrow="false" class="account-list-item">
            <view class="account-list" slot="note">
              <view class="list-cell">
                <view class="media-list" @tap.stop="toModelDetail(data)">
                  <view class="media-list-label">
                    ID:&nbsp;&nbsp;{{ data.id }}
                  </view>
                    <link-button class="btn" size="mini" @tap.stop="openModelDetail(data)">查看详情</link-button>
                    <view class="media-list-label">
                      模型名称:&nbsp;&nbsp;{{ data.ruleName }}
                    </view>
                </view>
              </view>
            </view>
          </item>
        </link-swipe-action>
      </template>
    </link-auto-list>
    <DetailModel v-model="showModelDetail" :modelDetailData="modelDetailData"></DetailModel>
  </link-page>
</template>

<script>
import { ROW_STATUS } from "../../../utils/constant";
import LnkTaps from "../../core/lnk-taps/lnk-taps";
import ConsumerListCommon from "../consumer-list-common";
import DetailModel from "./filter-model-detail-dialog.vue";

export default {
  name: "booked-order-list-page",
  components: { LnkTaps, DetailModel },
  mixins: [ConsumerListCommon()],
  data () {
    const userInfo = this.$taro.getStorageSync('token').result;
    const url = this.$env.appURL + '/action/link/sendDmp/send';
    const autoList = new this.AutoList(this, {
      module: this.$env.appURL + '/action/link/cusOpenBotResult',
      queryByExamplePage: '/action/link/sendDmp/send',
      url: {
        queryByExamplePage: url
      },
      loadOnStart: false,
      fetchItem: true,
      param: {
        oauth: this.pageOauth,
        dmpUrl: '/link/cusOpenBotResult/queryByExamplePage',
        sort: 'lastUpdated',
        order: 'desc',
        filtersRaw: [
          { id: 'pushStatus', property: 'pushStatus', value: '[DataPush,DataDistribution]', operator: 'IN' },
        ],
      },
      stayFields: 'id,acctName,customMobilePhone,customCompany,orderAmount,orderDate,status,customConsignee,companyName,realAmount,isExistChildFlag',
      hooks: {
        async beforeLoad (option) {
        //   if (this.postnId) {
            option.param.filtersRaw = [
              ...option.param.filtersRaw,
              { id: 'followFlag', property: 'followFlag', value: this.classifyForm.find(e=>e.active).val, operator: '=' },
              { id: 'postnId', property: 'postnId', value: this.postnId, operator: '=' },
            ];
        //   }
        },
        async beforeCreateItem (param) {
          param.data = {
            orderType: 'BookedOrder',
            orderKind: 'TerminalOrder',
            orderChildType: '',
            acctName: '',
            salesmanId: userInfo.postnId,
            salesmenName: userInfo.firstName,
            salesmenPhone: userInfo.contactPhone,
            status: 'New',
            row_status: ROW_STATUS.NEW,
            acctType: 'Terminal',
          };
        },
        async beforeGotoItem (param) {
          param.source = 'bookList'
        }
      },
      searchFields: ['name'],
      sortOptions: null,
      //   filterOption: [
      //     { label: '订单状态', field: 'status', type: 'lov', lov: 'ORDER_STATUS' },
      //     { label: '订购时间', field: 'orderDate', type: 'date' },
      //     { label: '系统来源', field: 'orderSource', type: 'lov', lov: 'BOOK_ORDER_SOURCE' },
      //   ],
      slots: {
        searchRight: () => (
          this.orderTypeActive.val === 'fiterModelTab' ? '' :
            <view class="filter-type-item"
              style="max-width: 224rpx;height: 72rpx;display: flex;align-items: center;justify-content: flex-end;padding-left: 30rpx;font-family: PingFangSC-Regular;font-size: 26rpx;color: #333333;line-height: 40rpx;font-weight: 400;"
              onTap={this.chooseOauthData}>{this.pageOauthName}
              <link-icon icon="mp-desc" style="color: #CCCCCC; margin: 4rpx 0 0 8rpx;" />
            </view>
        )
      }
    });
    const filterList = new this.AutoList(this, {
      module: this.$env.appURL + '/action/link/cusOpenBotRule',
      queryByExamplePage: '/action/link/sendDmp/send',
      insert: '/link/sendDmp/send',
      url: {
        queryByExamplePage: url
      },
      loadOnStart: false,
      fetchItem: true,
      param: {
        oauth: this.pageOauth,
        dmpUrl: '/link/cusOpenBotRule/queryByExamplePage',
        sort: 'lastUpdated',
        order: 'desc',
        filtersRaw: [
          { id: 'singleFiltersRaw0', property: 'type', value: 'ConsumerModel', operator: '=' },
        ],
      },
      hooks: {
        async beforeCreateItem (param) {
          param.data = {
            orderType: 'BookedOrder',
            orderKind: 'TerminalOrder',
            orderChildType: '',
            acctName: '',
            salesmanId: userInfo.postnId,
            salesmenName: userInfo.firstName,
            salesmenPhone: userInfo.contactPhone,
            status: 'New',
            row_status: ROW_STATUS.NEW,
            acctType: 'Terminal',
          };
        },
        async beforeGotoItem (param) {
          param.source = 'bookList'
        }
      },
      searchFields: ['name', 'ruleName'],
      sortOptions: null,
      slots: {
        searchRight: () => (
          this.orderTypeActive.val === 'fiterModelTab' ? '' :
            <view class="filter-type-item"
              style="max-width: 224rpx;height: 72rpx;display: flex;align-items: center;justify-content: flex-end;padding-left: 30rpx;font-family: PingFangSC-Regular;font-size: 26rpx;color: #333333;line-height: 40rpx;font-weight: 400;"
              onTap={this.chooseOauthData}>{this.pageOauthName}
              <link-icon icon="mp-desc" style="color: #CCCCCC; margin: 4rpx 0 0 8rpx;" />
            </view>
        )
      }
    });
    const empOption = new this.AutoList(this, {
      module: this.$env.appURL + '/link/position',
      param: {
        rows: 25,
        filtersRaw: []
      },
      searchFields: ['fstName', 'postnName', 'orgName'],
      sortOptions: null,
      hooks: {
        async beforeLoad (option) {
          option.param.oauth = 'MY_ORG';
          const lovData = await this.$lov.getLovByType('ASSIGN_POSITION');
          let str = '';
          lovData.forEach((item) => {
            str = item.val + ',' + str
          });
          str = str.substring(0, str.length - 1);
          option.param.filtersRaw = [
            ...option.param.filtersRaw,
            { id: 'positionType', property: 'positionType', value: `[${str}]`, operator: 'IN' },
            { id: 'isEffective', property: 'isEffective', value: 'Y', operator: '=' }
          ];
        }
      },
      renderFunc: (h, { data, index }) => {
        return (
          <item key={index} title={data.fstName} data={data} note={data.postnName}
            desc={data.orgName}>
          </item>
        )
      }
    });
    return {
      classifyForm: [
        // { val: 'all', name: '全部', seq: '1', field: 'followFlag', active: false, type: 'first' },
        { val: 'N', name: '未分配', seq: '2', field: 'followFlag', active: true, type: 'first' },
        { val: 'Y', name: '已分配', seq: '3', field: 'followFlag', active: false, type: 'second' },
      ],
      url,
      postnId: '', //从消息通知过来会带职位id
      empOption,
      autoList,
      filterList,
      userInfo,
      orderTypeOptions: [
        {
          defaultValue: "N",
          id: "222323332756804060",
          name: "目标消费者",
          seq: "1",
          type: "orderType",
          val: "BookedOrder"
        },
        {
          defaultValue: "N",
          id: "222323332756804060",
          name: "筛选模型",
          seq: "2",
          type: "orderType",
          val: "fiterModelTab"
        }
      ],
      orderTypeActive: {},
      modelDetailData: {},
      showModelDetail: false,
      showBtn: false
    };
  },
  async onLoad (options) {
    this.postnId = options.postnId

  },
  async created () {
    console.log(this.pageParam,'------pageParam------')
    this.orderTypeActive = this.orderTypeOptions[0];
    if (this.pageOauthList.length > 0) {
      this.pageOauthName = this.pageOauthList[0].name;
      this.pageOauth = this.pageOauthList[0].securityMode;
    } else {
      this.pageOauthName = '我的数据';
      this.pageOauth = 'MY_POSTN_ONLY';
    }
    this.autoList.option.param.oauth = this.pageOauth;
    this.autoList.methods.reload();
    this.getShowBtn();
  },
  methods: {
        /**
         * @desc 跳转详情
         * <AUTHOR>
         * @date 2024-04-22
         **/
        gotoItem(data) {
            const pageFrom = this.tapsActive.val === 'unFollow' ? 'OpenBottle' : 'OpenFollowed';
            this.$nav.push('/pages/lj-consumers/account/account-item-page', {
                data: data,
                pageFrom: pageFrom
            });
        },
    /**
        * @createdBy 曾宇
        * @date 2023/4/11
        * @methods: chooseOauthData
        * @description: 选择页面安全性
        **/
    chooseOauthData () {
      this.$actionSheet(() => (
        <link-action-sheet title="请选择数据范围" onCancel={() => { }}>
          {this.pageOauthList.map((item) => { return <link-action-sheet-item label={item.name} onTap={() => this.pageOauthChange(item)} /> })}
        </link-action-sheet>
      ));
    },
    /**
        * @createdBy 杨剑飘
        * @date 2023/4/11
        * @methods: chooseOauthData
        * @description: 模型详情弹框
        **/
    openModelDetail (data) {
      this.showModelDetail = true;
      Object.keys(data).forEach(e=>{
        this.$set(this.modelDetailData,e,data[e])
      })
    },
    toModelDetail (data) {
      console.log(data,'shuju 跳转钱')
      this.$nav.push('/pages/lj-consumers/account/filter-model-detail-page', {
        modelInfo: {...data},
        Oauth:this.pageOauth,
      });
    },
    /**
         * @desc 分配未分配切换
         * <AUTHOR>
         * @date 2024-09-14
         **/
    async switchTab (item) {
      this.classifyForm.forEach(e => e.active = false)
      item.active = true;
      await this.autoList.methods.reload();
    },
    /**
     * @desc 切换tab页签
     * <AUTHOR>
     * @date 2022/4/13 10:33
     **/
    async onTap () {
      if (this.orderTypeActive.val === 'fiterModelTab') {
        await this.filterList.methods.reload();
        return
      } else {
        await this.autoList.methods.reload();
        return
      }
    },
    /**
     * @desc 复制ID
     * <AUTHOR>
     * @date 2021/8/13 15:38
     **/
    copyData (data) {
      wx.setClipboardData({
        data: data,
        success: function () {
          // 添加下面的代码可以复写复制成功默认提示文本`内容已复制`
          wx.showToast({
            title: '复制成功',
            duration: 3000
          });
          wx.getClipboardData({
            success: function (res) {
            }
          })
        }
      })
    },
    onBack () {
      this.autoList.methods.reload();
    },
    // 分配
    toPeople (item) {
      console.log('====================================');
      console.log(item);
      console.log('====================================');
      this.redistribution(item);
    },
    /**
      * @desc 分配
      * <AUTHOR>
      * @date 2024-09-19
      **/
    async redistribution (data) {
      const item = await this.$object(this.empOption);
      const res = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
        dmpUrl: '/link/consumer/cusOpenBotDistribution',
        id: data.consumerId,
        postnId: item.postnId,
        recommenderAcctId: data.terminalId,
        sourceFrom: 'BottleOpen',
        cusBotResultId: data.id,
        interfaceSource: 'Artificial'
      }, {
        autoHandleError: false,
        handleFailed: (res) => {
          // console.log('报错handleFailed', res)
          setTimeout(() => {
            this.$message.warn('重新分配失败！' + res.result);
          }, 1000);
        }
      });
      if (res.success) {
        setTimeout(() => {
          this.$message.success('重新分配成功！');
        }, 1000);
        await this.$utils.handleTODOListNumber('del', 'unOpenBottle');
        this.autoList.methods.reload();
      }
    },
    /**
     * @createdBy 黄鹏
     * @date 2024/05/30
     * @methods: getShowBtn
     * @para:
     * @description: 是否展示操作按钮
     **/
    async getShowBtn () {
      const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', { key: 'BOOK_ORDER_SHOW' });
      if (data.success) {
        this.showBtn = data.value.split(',').includes(this.userInfo.coreOrganizationTile.l3Id);
      } else {
        this.$message.error('查询是否展示按钮失败：' + data.result);
      }
    }
  }
}
</script>

<style lang="scss">
.btn {
  position: absolute;
  right: 24px;
  top: 56px;
}
.account-list-item {
  background: #ffffff;
  margin: 24px;
  //height: 372px;
  border-radius: 16px;
  .account-list {
    background-color: #ffffff;
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: column;

    .list-cell {
      position: relative;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .media-list {
        position: relative;
        padding: 40px 24px 24px 24px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        width: 100%;

        .media-list-name {
          height: 48px;
          font-size: 32px;
          color: #212223;
          line-height: 48px;
          font-weight: 600;
          display: flex;
          .acct-name {
            overflow: hidden;
            max-width: 360px;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .is-certify {
            margin-left: 20px;
            width: 120px;
            height: 48px;
            image {
              width: 100%;
              height: 100%;
            }
          }
        }
        .media-list-time {
          .time {
            color: black;
            font-weight: bold;
          }
        }

        .terminal-label {
          margin: auto 0;
          margin-left: 16px;
          height: 36px;
          width: 80px;
          background: #f0f5ff;
          border-radius: 4px;
          font-size: 22px;
          color: #3f66ef;
          letter-spacing: 0;
          text-align: center;
          line-height: 36px;
          font-weight: 400;
        }

        .media-list-label {
          height: 36px;
          margin: 16px 0;
          display: flex;

          .loyalty-level {
            min-width: 80px;
            padding: 0 15px;
            margin-right: 16px;
            background: #f0f5ff;
            border-radius: 4px;
            font-size: 22px;
            color: #3f66ef;
            letter-spacing: 0;
            text-align: center;
            line-height: 36px;
            font-weight: 400;
          }

          .important-account {
            width: 112px;
            margin-right: 16px;
            background: #fff1eb;
            border-radius: 4px;
            font-size: 22px;
            color: #ff461e;
            line-height: 36px;
            font-weight: 400;
            text-align: center;
          }

          .black-gold-account {
            width: auto;
            padding: 0 5px;
            background: #262626;
            border-radius: 4px;
            font-size: 22px;
            color: #f0be94;
            line-height: 36px;
            font-weight: 400;
            text-align: center;
          }
        }

        .media-list-info {
          display: flex;
          flex-direction: column;
          position: relative;

          .media-list-info-item {
            height: 44px;
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .label {
              width: 162px;
              margin-right: 24px;
              font-size: 28px;
              color: #999999;
              line-height: 44px;
              font-weight: 400;
            }
            .label-2 {
              min-width: 260px;
              margin-right: 24px;
              font-size: 28px;
              color: #999999;
              line-height: 44px;
              font-weight: 400;
            }

            .media-list-info-text {
              width: 420px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              font-size: 28px;
              color: #333333;
              line-height: 1.1;
              font-weight: 400;
            }

            .media-list-info-phone {
              font-size: 28px;
              color: #317df7;
              line-height: 44px;
              font-weight: 400;
            }
          }
        }
      }
    }
  }
}
.top-tab-filter {
  position: relative;
}
.lnk-tabs-content {
  width: 100%;
  box-sizing: border-box;
  padding: 0 36px;
  position: absolute;
  display: flex;
  justify-content: flex-start;
  flex-wrap: nowrap;
}

.lnk-tabs-item {
  margin-right: 70px;
  height: 92px;
  line-height: 92px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .label-name-line {
    position: relative;
    font-size: 28px;
    margin-left: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .label-name-tips {
      position: absolute;
      top: 0;
      right: 0;
      color: red;
      transform: translate(100%, -20%);
    }
    .line {
      height: 8px;
      width: 56px;
      border-radius: 16px 16px 0 0;
      background-color: #2f69f8;
      box-shadow: 0 3px 8px 0 rgba(47, 105, 248, 0.63);
      margin-top: -8px;
    }
  }
}

.booked-order-list-page {
  .blank {
    width: 100%;
    height: 96px;
    background: #f2f2f2;
  }

  @include flex();
  @include direction-column();

  .booked-order-list-item {
    background: #ffffff;
    margin: 24px;
    border-radius: 16px;
  }

  .item-container {
    color: #262626;
    position: relative;
  }

  .row-item {
    @include flex-center-center();
    @include space-between();
    margin-bottom: 20px;
    width: 100%;
    font-weight: 500;

    .accnt-name {
      font-weight: 500;
    }

    .mobile-phone {
    }
  }

  .sub-row-item {
    width: 100%;
    @include flex-center-center();
    @include space-between();

    .order-info {
      .company,
      .order-total-amount.order-date {
        margin: 10px 0;
        display: flex;
      }
    }

    .status {
      width: 30%;
      text-align: right;
      font-weight: 500;

      &-new {
        color: $main-color;
      }

      &-registered {
        color: green;
      }

      &-inactive {
        color: red;
      }
      .return-order-button-active {
        display: flex;
        justify-content: center;
        align-items: center;
        background: #2f69f8;
        color: #ffffff;
        font-size: 24px;
        height: 40px;
        width: 160px;
        border-radius: 10px;
        position: absolute; /* 设置为绝对定位 */
        right: 0; /* 定位在右侧 */
        bottom: 0; /* 定位在底部 */
        line-height: 40px;
      }
      .return-order-button {
        display: flex;
        justify-content: center;
        align-items: center;
        background: #9a979c;
        color: #ffffff;
        font-size: 24px;
        height: 40px;
        width: 160px;
        border-radius: 10px;
        position: absolute; /* 设置为绝对定位 */
        right: 0; /* 定位在右侧 */
        bottom: 0; /* 定位在底部 */
        line-height: 40px;
      }
    }
  }
}
.model-dia {
  display: flex;
  flex-direction: column;
  position: relative;

  .model-dia-item {
    height: 44px;
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .model-label {
      width: 92px;
      margin-right: 24px;
      font-size: 28px;
      color: #999999;
      line-height: 44px;
      font-weight: 400;
    }

    .model-dia-text {
      width: 320px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 28px;
      color: #333333;
      line-height: 1.1;
      font-weight: 400;
    }
  }
}
</style>
