<!--
* 消费者认证申请/审批
* @data: 2024/8/14
* @author: 胡益阳
-->
<template>
    <link-page class="account-authentication-apply-page">
        <approval-history-point :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)"></approval-history-point>
        <line-title title="消费者认证申请" v-if="!isApplyFlag"/>
        <link-form ref="authenticationForm" :rules="formRulesData" v-model="form">
            <link-form-item label="手机号" required readonly>
                <link-input v-model="form.consumerPhoneNumber"></link-input>
            </link-form-item>
            <link-form-item label="消费者姓名" readonly>
                <link-input v-model="form.consumerName"></link-input>
            </link-form-item>
            <link-form-item label="申请理由" vertical required :readonly="!isApplyFlag" field="appExplain">
                <link-textarea placeholder="请填写申请理由，字数不超过500字" v-model="form.appExplain" :nativeProps="{maxlength: 500}"></link-textarea>
            </link-form-item>
            <link-form-item label="附件（用于审批）" vertical v-if="fieldTemAppId">
                <lnk-img-watermark :parentId="fieldTemAppId"
                                   moduleType="consumerCertify"
                                   :delFlag="isApplyFlag"
                                   :album="true"
                                   isZlFlag
                                   isCount
                                   :addLength="9"
                                   filePathKey="consumerCertify/"
                                   ref="consumerCertify"
                                   :newFlag="isApplyFlag">
                </lnk-img-watermark>
            </link-form-item>
            <link-sticky v-if="isApplyFlag">
                <link-button block mode="stroke" @tap="cancel">取消</link-button>
                <link-button block @tap="submit">提交</link-button>
            </link-sticky>
        </link-form>
        <link-form v-if="!isApplyFlag">
            <line-title title="消费者信息"/>
            <link-form-item label="所属系统" readonly v-if="!cfgArray.includes(belongToCompanyId)">
                <link-lov type="SOCIAL_CIRCLE" v-model="consumerData.socialCircle"/>
            </link-form-item>
            <link-form-item label="影响力（K序列）等级" readonly>
                <link-lov type="ACCT_SUB_TYPE" v-model="consumerData.type"/>
            </link-form-item>
            <link-form-item label="影响力（V序列）等级" readonly>
                <link-lov type="ACCT_MEMBER_LEVEL" v-model="consumerData.loyaltyLevel"/>
            </link-form-item>
            <link-form-item label="单位名称" readonly>
                <link-input v-model="consumerData.companyName"></link-input>
            </link-form-item>
            <link-form-item label="照片" vertical readonly>
                <lnk-img-watermark
                    :parentId="fieldTemAppId"
                    moduleType="consumerattachment"
                    :pathKeyArray="imgList"
                    :addLength="8"
                    filePathKey="/consumerattachment/"
                    ref="fakeTerminalPhoto">
                </lnk-img-watermark>
            </link-form-item>

            <link-form-item readonly></link-form-item>
            <view class="button-box">
                <link-button block @tap="goInformationDetail" :status="informationStatus? 'info' : 'primary'">查看单位信息</link-button>
                <link-button block @tap="goAccountDetail">查看消费者信息</link-button>
            </view>
            <!-- <link-sticky>
                <link-button block @tap="goInformationDetail" :status="informationStatus? 'info' : 'primary'">查看单位信息</link-button>
                <link-button block @tap="goAccountDetail">查看消费者信息</link-button>
            </link-sticky> -->
        </link-form>
        <link-sticky>
            <approval-operator :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)" @approvalInfoResult="approvalInfoResult"></approval-operator>
        </link-sticky>
        <view class="blank" v-if="!$utils.isEmpty(approvalId)"></view>
    </link-page>
</template>

<script>
import LnkImgWatermark from '../../core/lnk-img-watermark/lnk-img-watermark'
import LineTitle from "../../lzlj/components/line-title.vue";
import ApprovalOperator from "../../lzlj/approval/components/approval-operator.vue";
import ApprovalHistoryPoint from "../../lzlj/approval/components/approval-history-point.vue";
import {ComponentUtils} from "link-taro-component";

export default {
    name: 'account-authentication-apply-page',
    components: {ApprovalHistoryPoint, ApprovalOperator, LineTitle, LnkImgWatermark},
    data () {
        const pageFrom = this.pageParam.pageFrom || this.pageParam.source
        const options = this.pageParam.data
        const userInfo = this.$taro.getStorageSync('token').result;
        return {
            imgList: [],
            form: {
                appExplain: ''
            },
            fieldTemAppId: '',          // 消费者认证审批记录ID
            approvalId: null,
            flowObjId: '',
            formRulesData: {
                appExplain: this.Validator.required('请输入申请理由')
            },                          // 表单校验规则
            consumerData: {},           // 消费者信息
            pageFrom,                   // 页面来源
            options,                     // 页面参数
            userInfo,                    // 用户信息
            belongToCompanyId: '',        // 消费者所属公司ID
            cfgArray: [], // 企业参数配置信息-新K涉及公司范围
        }
    },
    async created() {
        // 查询企业参数配置-参数键New_Type_Company
        const obj = await this.$utils.getCfgProperty('New_Type_Company');
        this.cfgArray = obj.split(',')
        console.log('essential-cfgArray', this.cfgArray, this.userInfo.coreOrganizationTile['l3Id']);
        let sceneObj = await this.$scene.ready();//that.$store.getters['scene/getScene'];//消息场景对象
        const approval_from = sceneObj.query['approval_from'];
        if (this.pageFrom === 'AccountItem') {
            await this.queryFieldTemAppId()
            wx.setNavigationBarTitle({
                title: '消费者认证申请'
            })
        } else if (this.pageFrom === 'approval') {
            const flowObjDetail = JSON.parse(this.pageParam.data.flowObjDetail)
            this.fieldTemAppId = flowObjDetail.id
            this.approvalId = this.pageParam.data.id  //审批传过来的审批数据ID
            this.form = {
                appExplain: flowObjDetail.appExplain,
                consumerPhoneNumber: flowObjDetail.mobilePhone,
                consumerName: flowObjDetail.name
            }
            this.queryConsumerById(flowObjDetail.businessId)
            wx.setNavigationBarTitle({
                title: '消费者认证审批'
            })
        } else {
            if (approval_from === 'qw') {
                wx.setNavigationBarTitle({
                    title: '消费者认证审批'
                })
                this.approvalId = sceneObj.query['approval_id'];
                this.flowObjId = sceneObj.query['flowObjId'];
            }
        }
        if (this.pageParam.accountItem) {
            this.form = {
                consumerPhoneNumber: this.pageParam.accountItem.phoneNumber,
                consumerName:  this.pageParam.accountItem.name
            }
        }
        console.log("数字认证审批消费者信息", this.pageFrom, this.pageParam, this.consumerData, this.options);
    },
    methods: {
        /**
         * 消费者附件查询
         * @author:  胡益阳
         * @date:  2024/7/25
         */
            queryCusAttachment () {
            try {
                this.$http.post(`${this.$env.appURL}/action/link/sendDmp/send`,
                    {
                        pageFlag: false,
                        rows: 50,
                        page: 1,
                        dmpUrl: '/link/cusAttachment/queryByExamplePage',
                        order: 'desc',
                        sort: 'created',
                        filtersRaw: [
                            {id: 'sourceId', operator: '=', property: 'sourceId', value: this.consumerData.id},
                            {id: 'uploadStatus', operator: 'in', property: 'uploadStatus', value: '[Effective, NewlyBuild]'}
                        ]
                    }
                ).then((res) => {
                    if (res.success) {
                        this.imgList = res.rows;
                    }
                })
            } catch (e) {}
        },
        /**
         * @Author: 胡益阳
         * @Date: 2024/8/22
         * @Description: 查询审批详情
         */
        approvalInfoResult (e) {
            const flowObjDetail = JSON.parse(e.flowObjDetail)
            this.fieldTemAppId = flowObjDetail.id
            this.form = {
                appExplain: flowObjDetail.appExplain,
                consumerPhoneNumber: flowObjDetail.mobilePhone,
                consumerName: flowObjDetail.name
            }
            this.queryConsumerById(flowObjDetail.businessId)
        },
        /**
         * @Author: 胡益阳
         * @Date: 2024/8/22
         * @Description: 前往企业信息
         */
        goInformationDetail () {
            if (this.informationStatus) {
                return
            }
            // 根据单位ID查询企业信息
            this.$http.post(`${this.$env.baseURL}/action/link/company/queryByExamplePage`, {
                filtersRaw: [
                    {id: 'id', property: 'id', value: this.consumerData.corporateId, operator: '='}
                ],
                oauth: 'All'
            }).then(res => {
                if (res.rows.length > 0) {
                    this.$nav.push('/pages/lzlj/corporate-relation/information/information-item-page', {
                        data: res.rows[0],
                        source: 'AuthenticationApply'
                    })
                }
            })
        },
        /**
         * @Author: 胡益阳
         * @Date: 2024/8/22
         * @Description: 查看消费者详情
         */
        goAccountDetail () {
            this.$nav.push('/pages/lj-consumers/account/account-item-page', {
                data: this.consumerData,
                pageFrom: 'AuthenticationApply'
            })
        },
        /**
         * @Author: 胡益阳
         * @Date: 2024/8/22
         * @Description: 查询消费者认证审批记录id
         */
        async queryFieldTemAppId () {
            await this.$http.post(this.$env.appURL + '/action/link/consumer/preDefaultValue', {}, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`查询失败：${response.result}`);
                }
            }).then((res) => {
                this.fieldTemAppId = res.result.id
            })
        },
        /**
         * 点击取消
         * @author:  胡益阳
         * @date:  2024/8/20
         */
        cancel () {
            this.$nav.back();
        },
        /**
         * 提交
         * @author:  胡益阳
         * @date:  2024/8/20
         */
        async submit() {
            try {
                if (!this.form.appExplain) {
                    this.$message.warn('请输入申请理由');
                    return;
                }
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerCertify/submit',
                    fieldTemAppId: this.fieldTemAppId,
                    businessId: this.pageParam.accountItem.id,
                    appExplain: this.form.appExplain
                })
                if (data.success) {
                    this.$message.success('提交认证申请成功！')
                    setTimeout(() => {
                        this.$nav.back();
                    }, 800)
                } else {
                    this.$showError(`提交失败：${data.result}`)
                }
            } catch (e) {
                console.log('认证失败：', e)
            }
        },
        /**
         * 查询消费者信息
         * @author:  胡益阳
         * @date:  2024/8/15
         */
        async queryConsumerById (accountId) {
            this.$utils.showLoading();
            const data = await this.$http.post(this.$env.dmpURL + '/action/link/cdcPubConsumer/queryMpById', {
                id: accountId,
                inTerminal: 'Y'
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError(`查询消费者信息失败：${response.result}`);
                }
            });
            if (data.success) {
                this.$utils.hideLoading();
                this.consumerData = data.result;
                console.log('进入异步获取消费者信息：', data.result);
                this.belongToCompanyId = data.result.belongToCompanyId; // 获取所属公司用来决定老K所属系统是否展示
                this.queryCusAttachment()
            }
        },
    },
    computed: {
        /**
         * 通过页面来源判断是申请/审批--数据是否可编辑
         * @author:  胡益阳
         * @date:  2024/8/14
         */
        isApplyFlag () {
            return this.pageFrom === 'AccountItem'
        },
        /**
         * @Author: 胡益阳
         * @Date: 2024/8/22
         * @Description: 企业信息是否置灰
         */
        informationStatus () {
            return this.$utils.isEmpty(this.consumerData.corporateId)
        }
    },
    watch: {}
}
</script>

<style lang="scss">
.account-authentication-apply-page {
    /*deep*/.line-title {
    margin-bottom: 20px;
}
.button-box {
    width: 100%;
    display: flex;
    justify-content: space-around;
}
.blank {
        height: 480px;
        width: 100%;
    }
}
</style>
