<template>
    <link-page class="account-list-page">
        <link-auto-list :option="autoList" :hideCreateButton="!editFlag" :searchInputBinding="{props:{placeholder:'姓名/手机号'}}">
            <view slot="top" class="top-tab-filter" v-if="pageFrom!=='PeopleScreen'">
                <view class="lnk-tabs-content" style="width: 100%;">
                    <view class="lnk-tabs-item" :style="tab.seq === classifyListActive.seq ? 'color:#2f69f8;' : ''"
                          v-for="(tab, index) in classifyList" :key="index" @tap="switchTab(tab)" style="width: 25%">
                        <view class="label-name-line">
                            <text class="label-name-text">{{tab.name}}</text>
                            <view class="line" v-if="tab.seq === classifyListActive.seq"></view>
                        </view>
                    </view>
                </view>
            </view>
            <view slot="filterGroup" class="top-filter" :key="pageOauth" v-if="pageFrom!=='PeopleScreen'">
                <scroll-view scroll-x="true" class="top-filter-content">
                    <view class="top-filter-info type" v-if="editFlag || pageFrom === 'ConsumerOverview'">
                        <view class="filter-type-item choose" @tap="chooseStoreData" :class="isStoreChosen ? ' chosen' : ''">所属客户<link-icon icon="mp-desc"/></view>
                            <!--暂时注释标签筛选 230913-->
<!--                        <view class="filter-type-item choose" @tap="tagDialogFlag=true;" :class="this.tagIdList.length ? ' chosen' : ''">标签<link-icon icon="mp-desc"/></view>-->
                        <view class="filter-type-item choose" v-if="pageOauth !== 'MY_POSTN_ONLY' && pageOauth !== 'MY_POSTN'" @tap="orgDialogFlag=true;" :class="isOrgChosen ? ' chosen' : ''">区域<link-icon icon="mp-desc"/></view>
                        <view class="filter-type-item choose" v-if="pageOauth !== 'MY_POSTN_ONLY'" @tap="postnDialogFlag = true;" :class="isPostChosen ? ' chosen' : ''">跟进人<link-icon icon="mp-desc"/></view>
                        <view class="line"></view>
                    </view>
                    <view class="top-filter-info" v-else-if="pageFrom === 'noticeDetail'">
                        <view class="tag-list">
                            <view v-for="(item,index) in timeFilterOption" class="tag-list-item"
                                  :class="item.checked? 'tagChecked' : ''" @tap="chooseTime(item)">
                                <view class="tag-item">{{ item.name }}</view>
                            </view>
                        </view>
                    </view>
                </scroll-view>
            </view>
            <view slot="top" v-if="classifyItemList.length > 0">
                <scroll-view scroll-x="true" class="classify-filter-content">
                    <view class="classify-filter-list">
                        <view v-for="(item,index) in classifyItemList" :class="item.seq === classifyItemListActive.seq ? 'classify-filter-item label-checked' : 'classify-filter-item'" @tap="switchTabItem(item)">
                            {{ item.name }}
                        </view>
                    </view>
                </scroll-view>
            </view>
            <template slot-scope="{data,index}">
                <link-swipe-action>
                    <item :key="index" :data="data" :arrow="false" class="account-list-item">
                        <view class="account-list" slot="note">
                            <view class="list-cell">
                                <view class="media-list" @tap="gotoItem(data)">
                                    <view class="media-list-name" @tap="gotoItem(data)">
                                        <view class="acct-name">{{ data.consumerName }}</view>
                                        <view class="is-certify" v-if="data.isAuthentication"><image :src="data.isAuthentication === '1' ? $imageAssets.storeStatusVerifiedImage : $imageAssets.storeStatusUnverifiedImage"></image></view>
                                    </view>
                                    <view class="media-list-label">
                                        <view class="loyalty-level" v-if="pageFrom === 'PeopleScreen'">{{ data.type | lov('ACCT_SUB_TYPE')}} </view>
                                        <view class="loyalty-level" v-else>{{data.kTypeName}}</view>
                                        <view class="loyalty-level">{{ data.vTypeName}}</view>
                                        <view class="important-account" v-if="data.impFlag === 'Y' && pageFrom!=='PeopleScreen'">重点客户</view>
                                        <view class="black-gold-account" v-if="data.identityLevel && pageFrom!=='PeopleScreen'">{{data.identityLevel|lov('ACCT_SUB_TYPE')}}</view>
                                    </view>
                                    <view class="media-list-info">
                                        <view class="media-list-info-item">
                                            <view class="label">联系方式</view>
                                            <view class="media-list-info-phone" @tap.stop="makePhoneCall(data)">
                                                {{ data.phone }}
                                            </view>
                                        </view>
                                        <view class="media-list-info-item">
                                            <view class="label">单位</view>
                                            <view class="media-list-info-text">{{ data.cmsCompanyName }}</view>
                                        </view>
                                        <view class="media-list-info-item">
                                            <view class="label">职务</view>
                                            <view class="media-list-info-text">{{ data.postnName }}</view>
                                        </view>
                                        <view class="media-list-info-item">
                                            <view class="label">所属客户</view>
                                            <view class="media-list-info-text">{{ data.acctName }}</view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view class="account-label"><view class="label">{{ data.followStaffName }}跟进</view></view>
                        </view>
                    </item>
                    <link-swipe-option slot="option" @tap="invalidAccount(data)" v-if="editFlag">取消跟进</link-swipe-option>
                </link-swipe-action>
            </template>
        </link-auto-list>
        <link-sticky class="bottom-sticky" v-if="pageFrom === 'PeopleScreen'">
            <view class="check-button" @tap="checkLabels('label')">查看标签</view>
            <view class="check-button" @tap="checkLabels('condition')">圈选条件</view>
        </link-sticky>
        <link-fab-button :bottom="200" icon="icon-kehufeidan" @tap="gotoIneffectiveList" v-if="pageFrom!=='PeopleScreen'"></link-fab-button>
        <post-select ref="postSelect" :user-info="userInfo" :page-oauth="pageOauth" :show.sync="postnDialogFlag" @changePostnFilter="changeFilter"/>
        <tag-info :show.sync="tagDialogFlag" :tag-group-list="tagGroupList" @choose="chooseTagGroup"/>
        <org-select ref="orgSelect" :user-info="userInfo" :show.sync="orgDialogFlag" @changeOrgFilter="changeFilter"/>
        <water-mark></water-mark>
    </link-page>
</template>

<script>
import {ROW_STATUS} from "../../../utils/constant";
import StatusButton from "../../lzlj/components/status-button";
import LnkTaps from "../../core/lnk-taps/lnk-taps";
import ConsumerCommon from '../consumer-common';
import TagInfo from './components/tag-info/tag-info';
import OrgSelect from './components/org-select/org-select';
import PostSelect from './components/postn-select/postn-select';
import waterMark from "../../lzlj/components/water-mark";

export default {
    name: "account-list-page",
    components: {LnkTaps, StatusButton, TagInfo, OrgSelect, PostSelect, waterMark},
    mixins: [ConsumerCommon()],
    data() {
        const pageFrom = this.pageParam.pageFrom || '';
        const checkStartDate = this.pageParam.created || '';
        const pageOauthList = this.pageParam.secMenus || [{securityMode: 'MY_POSTN_ONLY', name: '我的数据'}];
        const userInfo = this.$taro.getStorageSync('token').result;
        let filtersRawTemp = [
            {id: 'companyId', property: 'companyId', value: userInfo.coreOrganizationTile['l3Id'], operator: '='},
            {id: 'consumerType', property: 'consumerType', value: 'ChannelConsumer', operator: '='},
            {id: 'accntChannel', property: 'accntChannel', value: 'MarketingPlatform', operator: '='},
            {id: 'empFlag', property: 'empFlag', value: 'N', operator: '='},
            {id: 'followFlag', property: 'followFlag', value: 'Y', operator: '='}
        ];
        let editFlag = true;
        let url = this.$env.appURL + '/action/link/sendDmp/consumerListSend';
        let filterOption = [
            {label: '客户性别', field: 'gender', type: 'lov', lov: 'GENDER'},
            {label: '消费者生日', field: 'birthdayMd', type: 'date', view: 'MD', format: 'MM-DD'},
            {label: '推荐纳入浪潮计划投放', field: 'bringInto', type: 'lov', lov: 'QUERY_BRINGINTO', multiple: false},
        ];
        let pageOauth = 'MY_POSTN_ONLY';
        if (pageOauthList.length > 0) {
            pageOauth = pageOauthList[0].securityMode;
        }
        let param = {
            oauth: pageOauth,
            sort: 'lastUpdated',
            filtersRaw: filtersRawTemp,
            stayFields: 'id,acctName,mobilePhone1,subAcctType,fstName,loyaltyLevel,acctRank,companyId,impFlag'
        };
        const date = this.$date.filter(new Date(), 'MM-dd');
        let acctName = 'acctName'
        let mobilePhone = 'mobilePhone1'
        if (pageFrom === 'noticeDetail') {
            url = this.$env.appURL + '/action/link/consumerBirth/queryBirthdayPage'
            editFlag = false;
            filterOption = [];
            filtersRawTemp = [];
            param = {
                attr1: date,
                attr2: date
            }
            acctName = 'consumerName'
            mobilePhone = 'mobilePhone'
        } else if (pageFrom === 'ConsumerOverview') {
            editFlag = false;
            url = this.$env.appURL + "/action/link/sendDmpSr/send";
            param = {
                dmpSrUrl: '/link/boardDetail/csmDetail',
                companyId: this.pageParam.companyId, // 公司ID
                dataAccess: this.pageParam.filterInfo.dataRange, // 数据权限
                timeRange: this.pageParam.filterInfo.timeRange, // 数据时间范围
                kTypeName: this.pageParam.kTypeName, // k序列名称
                vTypeName: this.pageParam.vTypeName, // V序列名称
            }
            // 各业务消费者建设总览下钻(TeamBuildOverview)
            if (this.pageParam.filterInfo.staffId) {
                param.staffId = this.pageParam.filterInfo.staffId;
                param.postnId = this.pageParam.filterInfo.postnId;
            }
            // 各区域消费者建设总览(AreaBuildOverview)
            if (this.pageParam.filterInfo.orgId) {
                param.orgId = this.pageParam.filterInfo.orgId;
            }
            // 更改filterOption--todo
            filterOption = [
                {label: '客户性别', field: 'gender', type: 'select', data: [
                    {name: '男', val: '1'}, {name: '女', val: '2'},{name: '未知', val: '0'}
                ]},
                {label: '消费者生日', field: 'birthdayMd', type: 'date', view: 'MD', format: 'MM-DD'},
                {label: '推荐纳入浪潮计划投放', field: 'isLangchaoplan', type: 'select', multiple: false, data: [
                    {name: '是', val: '1'}, {name: '否', val: '0'},{name: '空', val: 'NULL'}
                ]},
            ];
            acctName = 'consumerName'
            mobilePhone = 'phone'
        } else if (pageFrom === 'PeopleScreen') {
            editFlag = false;
            filterOption = null;
            filtersRawTemp = [];
            url = this.$env.dmpURL + '/link/cdcConSrcLine/queryByExamplePage';
            param = {
                filtersRaw: [
                    {id: 'headId', property: 'headId',value: this.pageParam.headId, operator: '='},
                    {id: 'postnId', property: 'postnId',value: userInfo.postnId, operator: '='}
                ]
            };
            acctName = 'name';
            mobilePhone = 'phoneNumber';
        }
        const autoList = new this.AutoList(this, {
            module: this.$env.appURL + '/action/link/consumer',
            createPath: '/pages/lj-consumers/account/account-item-edit-page',
            url: {
                queryByExamplePage: url
            },
            filterOption: filterOption,
            loadOnStart: editFlag || pageFrom === 'PeopleScreen',
            exactSearchFields: [
                {
                    field: acctName,
                    showValue: '姓名',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: true
                }, {
                    field: mobilePhone,
                    showValue: '手机号',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: true
                }
            ],
            param: param,
            sortOptions: null,
            hooks: {
                async beforeCreateItem(param) {
                    const id = await this.$newId();
                    param.data = {
                        id: id,
                        row_status: ROW_STATUS.NEW,
                        consumerDataType: 'ChannelConsumer',
                        dataSource: 'MarketingPlatform',
                        dataType: 'Consumer',
                        accntSourceFrom: 'WeChatWork',
                        orgId: this.userInfo.orgId,
                        fstName: '',
                        postnId: this.userInfo.postnId,
                        belongToCompanyId: this.userInfo.coreOrganizationTile['l3Id'],
                        type: "ToBeFollowed",
                        birthType: 'Yang',
                        brandPreference: "",
                        hobby: "",
                        terminalFlag: 'N',
                    };
                    param.userInfo = this.userInfo;
                    param.pageFrom = "Account";
                },
                beforeLoad(option) {
                    if (pageFrom === 'noticeDetail') {
                        option.param.sort = 'birth';
                    }
                    option.param.filtersRaw.forEach((val) => {
                        if(val.property === 'birthdayMd') {
                            val.value = val.value.slice(0,5);
                        }
                    });
                    if (pageFrom === 'ConsumerOverview') {
                        // 清空通过filtersRaw整理出来的筛选条件
                        delete option.param.consumerName;
                        delete option.param.phone;
                        delete option.param.gender;
                        delete option.param.birthFrom;
                        delete option.param.birthTo;
                        delete option.param.isLangchaoplan;
                    }
                    for (let i = 0; i < option.param.filtersRaw.length; i++) {
                        /* 消费者看板进来 */
                        if (pageFrom === 'ConsumerOverview') {
                            /* 消费者名称 */
                            if (option.param.filtersRaw[i].property === 'consumerName') {
                                option.param.consumerName = option.param.filtersRaw[i].value;
                            }
                            /* 手机号 */
                            if (option.param.filtersRaw[i].property === 'phone') {
                                option.param.phone = option.param.filtersRaw[i].value;
                            }
                            /* 性别 */
                            if (option.param.filtersRaw[i].property === 'gender') {
                                option.param.gender = option.param.filtersRaw[i].operator === 'in' ? option.param.filtersRaw[i].value.slice(1, -1).split(',') : [option.param.filtersRaw[i].value];
                            }
                            /* 消费者生日--开始日期 */
                            if (option.param.filtersRaw[i].property === 'birthdayMd' && option.param.filtersRaw[i].operator === '>=') {
                                option.param.birthFrom = option.param.filtersRaw[i].value;
                            }
                            /* 消费者生日--结束日期 */
                            if (option.param.filtersRaw[i].property === 'birthdayMd' && option.param.filtersRaw[i].operator === '<=') {
                                option.param.birthTo = option.param.filtersRaw[i].value;
                            }
                            /* 是否纳入浪潮计划 */
                            if (option.param.filtersRaw[i].property === 'isLangchaoplan') {
                                option.param.isLangchaoplan = option.param.filtersRaw[i].value;
                            }
                        } else {
                            if (option.param.filtersRaw[i].property === 'acctName' || option.param.filtersRaw[i].property === 'consumerName') {
                                option.param.filtersRaw[i].operator = 'like';
                            }
                            if (option.param.filtersRaw[i].property === 'bringInto' && option.param.filtersRaw[i].value === 'NULL') {
                                option.param.filtersRaw[i].value = '';
                                option.param.filtersRaw[i].operator = 'IS NULL';
                            }
                        }
                    }
                    if (pageFrom === 'ConsumerOverview') delete option.param.filtersRaw;
                }
            },
            slots: {
                searchRight: () => (
                    <view class="filter-type-item" style="max-width: 224rpx;height: 72rpx;display: flex;align-items: center;justify-content: flex-end;padding-left: 30rpx;font-family: PingFangSC-Regular;font-size: 26rpx;color: #333333;line-height: 40rpx;font-weight: 400;" onTap={this.chooseOauthData}>{this.pageOauthName}<link-icon icon="mp-desc" style="color: #CCCCCC; margin: 4rpx 0 0 8rpx;"/></view>
                )
            }
        });
        return {
            pageOauth,
            pageOauthName: '',
            pageOauthList,
            checkStartDate, // 消息详情时间参数
            timeFilterOption: [{name: '前一天', val: 'nextDay', checked: false}, {
                name: '前三天',
                val: 'nextThreeDay',
                checked: false
            }], // {name: '当月', val: 'month', checked: false}
            editFlag,
            pageFrom,
            orgDialogFlag: false,                     // 组织筛选弹窗
            tagDialogFlag: false,                     // 标签筛选弹窗
            postnDialogFlag: false,                   // 跟进人筛选弹窗
            tagIdList: [],
            tagGroupId: '',
            tagsItemOption: new this.AutoList(this, { // 标签选择
                url: {
                    queryByExamplePage: this.$env.dmpURL + '/link/portalAccntTagGroup/queryTagItemsByTagGroup'
                },
                param: {
                    validFlag: 'Y',
                },
                searchFields: null,
                hooks: {
                    beforeLoad(option) {
                        delete option.param.sort;
                        delete option.param.order;
                        option.param.filtersRaw = [
                            ...option.param.filtersRaw,
                            {id: 'headId', property: 'headId', value: this.tagGroupId, operator: '='}
                        ]
                    }
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} className="select-box" arrow="false">
                            <link-checkbox val={data.id} toggleOnClickItem slot="thumb" loyaltyLevel></link-checkbox>
                            <view slot="title">{data.tagName}</view>
                        </item>)
                }
            }),
            tagGroupList: [],
            isGuoJiao: false,     // 判断是否查询国窖系公司的分类分级值列表
            filtersRawTemp,
            classifyList: [
                {val: 'ALL', name: '全部', seq: '1', field: ''},
                {val: 'certified', name: '已认证', seq: '2', field: 'certify'},
                {val: 'uncertified', name: '未认证', seq: '3', field: 'certify'},
                {val: 'subAcctType', name: 'K序列', seq: '4', field: 'subAcctType'},
                {val: 'ACCT_MEMBER_LEVEL', name: 'V序列', seq: '5', field: 'loyaltyLevel'}
            ],
            classifyListActive: {},
            classifyItemListActive: {},
            classifyItemList: [],
            autoList,
            isStoreChosen: false,       // 所属客户选择
            isOrgChosen: false,         // 区域选择
            isPostChosen: false,        // 跟进人选择
            userInfo,
            customerOption: new this.AutoList(this, { // 所属客户
                    module: this.$env.appURL + "/action/link/sendDmpSr/send",
                    url: {
                        queryByExamplePage: this.$env.appURL + '/action/link/sendDmpSr/send'
                    },
                    param: {
                        dmpSrUrl: '/link/boardDetail/csmAcctDetail',
                        postnId: '',
                        oauth: 'ALL',
                    },
                    searchFields: ['acctName'],
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} className="select-box" arrow="false">
                                <link-checkbox val={data.acctId} toggleOnClickItem slot="thumb"></link-checkbox>
                                <view slot="title" style="display: flex;">{data.acctName} <view style="margin-left: 1em;background: #EDF3FF;color: #2F69F8;border: 1px solid #EDF3FF;font-size:12px;padding: 3px;border-radius: 3px;">{data.acctTypeName}</view></view>
                                <view slot="note">{data.acctAddress}</view>
                            </item>)
                    },
                    hooks: {
                        beforeLoad (option) {
                            delete option.param.order;
                            delete option.param.sort;
                            option.param.postnId = this.userInfo.postnId;
                            option.param.dataAccess = this.pageParam.filterInfo.dataRange;
                            option.param.timeRange = this.pageParam.filterInfo.timeRange;
                            if (option.param.filtersRaw.length > 0) {
                                option.param.acctName = option.param.filtersRaw[0].value;
                            }
                            delete option.param.filtersRaw;
                        }
                    }
                })
        };
    },
    async created() {
        this.classifyListActive = this.classifyList[0];
        if (this.pageFrom === 'noticeDetail') {
            await this.chooseTime(this.pageParam.type);
        }
        if (this.pageOauthList.length > 0) {
            this.pageOauthName = this.pageOauthList[0].name;
        } else {
            this.pageOauthName = '我的数据';
        }
        if (this.pageFrom === 'ConsumerOverview') {
            this.autoList.option.loadOnStart = true;
        }
    },
    // mounted () {
    //     const todoListNumber = this.$taro.getStorageSync('todoListNumber');
    //     if (todoListNumber['unDoAccount'] && todoListNumber['unDoAccount'] > 0) {
    //         this.$showError('请及时标记已认证未纳入浪潮的消费者');
    //     }
    // },
    methods: {
        /**
         * @desc 查看标签和圈选条件
         * <AUTHOR>
         * @date 2023/9/13 16:49
         * @param type 查看类型
         **/
        checkLabels(type) {
            this.$nav.push('/pages/lj-consumers/people-screen/people-screen-item-page', {id: this.pageParam.headId, type: type})
        },
        /**
         * @createdBy 曾宇
         * @date 2023/4/11
         * @methods: chooseOauthData
         * @description: 选择页面安全性
         **/
        chooseOauthData() {
            this.$actionSheet(() => (
                <link-action-sheet title="请选择数据范围" onCancel={() => {}}>
                    {this.pageOauthList.map((item) => {return <link-action-sheet-item label={item.name} onTap={() => this.pageOauthChange(item)}/>})}
                </link-action-sheet>
            ));
        },
        /**
         * @createdBy 曾宇
         * @date 2023/4/11
         * @methods: pageOauthChange
         * @para: oauth 安全性
         * @description: 页面安全性切换
         **/
        pageOauthChange(oauth) {
            this.$utils.showLoading();
            this.autoList.list = [];
            this.pageOauth = oauth.securityMode;
            this.pageOauthName = oauth.name;
            this.autoList.option.param.oauth = oauth.securityMode;
            this.$refs.postSelect.resetSelectData();
            this.$refs.orgSelect.resetSelectData();
            this.isOrgChosen = false;
            this.isPostChosen = false;
            this.clearFilter('orgId');
            this.clearFilter('postnId');
            this.autoList.methods.reload();
            this.$utils.hideLoading();
        },
        /**
         * @createdBy 曾宇
         * @date 2023/4/11
         * @methods: clearFilter
         * @description: 清除区域 跟进人筛选条件
         **/
        clearFilter(type) {
            const checkFlagIndex = this.autoList.option.param.filtersRaw.findIndex((item) => item.property === type);
            if (checkFlagIndex > -1) {
                this.autoList.option.param.filtersRaw.splice(checkFlagIndex, 1);
            }
        },
        /**
         * @createdBy 曾宇
         * @date 2023/4/10
         * @methods: changeFilter
         * @description: 组织 跟进人选择
         **/
        async changeFilter(item) {
            const dataList = this.$utils.deepcopy(item);
            let filtersRaw = this.autoList.option.param.filtersRaw;
            if(dataList.data.length === 0) {
                const index = filtersRaw.findIndex((val) => val.property === dataList.type);
                if (index > -1) {
                    filtersRaw.splice(index, 1);
                    this.autoList.option.param.filtersRaw = filtersRaw;
                    this.autoList.methods.reload();
                }
                if (dataList.type === 'orgId') {
                    this.isOrgChosen = false;
                } else {
                    this.isPostChosen = false;
                }
                return;
            }
            if (dataList.type === 'orgId') {
                this.isOrgChosen = true;
            } else {
                this.isPostChosen = true;
            }
            let list = [];
            dataList.data.forEach((item) => {
                list.push(item.id);
            });
            list = `[${list.toString()}]`;
            if (!filtersRaw.some((item1) => {return item1.property === item.type})) {
                filtersRaw.push({id: dataList.type, property: dataList.type, value: list, operator: 'in'});
            } else {
                for (let i = 0; i < filtersRaw.length; i++) {
                    if (filtersRaw[i].property === dataList.type) {
                        filtersRaw[i].value = list;
                        filtersRaw[i].operator = 'in';
                        break;
                    }
                }
            }
            this.autoList.option.param.filtersRaw = filtersRaw;
            this.autoList.methods.reload();
        },
        /**
         * @desc 跳转失效列表
         * <AUTHOR>
         * @date 2023/2/20 10:58
         **/
        gotoIneffectiveList() {
            this.$nav.push('/pages/lj-consumers/ineffective-account/ineffective-account-list-page');
        },
        /**
         * @desc 筛选时间
         * <AUTHOR>
         * @date 2022/11/28 15:03
         **/
        async chooseTime(item) {
            this.timeFilterOption.forEach((timeItem) => {
                timeItem.checked = timeItem.val === item.val;
            });
            const nowDate = new Date(this.checkStartDate.replace(/-/g, '/')); // 当前时间
            let attr1 = this.$date.format(nowDate, 'MM-DD'); // 开始时间
            let attr2 = ''; // 结束时间
            switch (item.val) {
                case 'nextDay':
                    let nextDate = new Date(nowDate.getTime() + 24 * 60 * 60 * 1000); //后一天
                    attr2 = this.$date.format(nextDate, 'MM-DD');
                    attr1 = attr2
                    break;
                case 'nextThreeDay':
                    let nextThreeDate = new Date(nowDate.getTime() + 24 * 60 * 60 * 1000 * 3); //后三天
                    attr2 = this.$date.format(nextThreeDate, 'MM-DD');
                    attr1 = attr2;
                    break;
                case 'month':
                    const dateTime = this.$utils.getCurrentMonthDate();
                    attr1 = this.$date.format(dateTime.startDate, 'MM-DD');
                    attr2 = this.$date.format(dateTime.startDate, 'MM-DD');
                    break;
            }
            this.autoList.option.param.attr1 = attr1;
            this.autoList.option.param.attr2 = attr2;
            await this.autoList.methods.reload();
        },
        /** 根据标签组选择对应的标签值
         * @desc
         * <AUTHOR>
         * @date 2022/8/22 11:14
         **/
        async chooseTagGroup(item) {
            const data = this.$utils.deepcopy(item);
            if (data.length === 0) {
                this.tagIdList = [];
            } else {
                this.tagIdList = data.map(i => ({
                    tagGroupId: i.headId,
                    tagId: i.id
                }));
            }
            if (this.tagIdList.length > 0) {
                this.autoList.option.param['tagIdList'] = this.tagIdList.map((item) => {
                    return item.tagId
                });
                this.autoList.methods.reload();
            } else {
                delete this.autoList.option.param['tagIdList'];
                this.autoList.methods.reload();
            }
        },
        /**
         * 选择所属客户--门店
         * @Author:付常涛
         * @Date: 2023/11/16 14:50:58
         */
        async chooseStoreData() {
            const data = await this.chooseStoreList();
            if (data) {
                this.isStoreChosen = true;
                this.autoList.option.param['acctName'] = data;
                this.autoList.methods.reload();
            } else {
                this.isStoreChosen = false;
                delete this.autoList.option.param['acctName'];
                this.autoList.methods.reload();
            }
        },
        /**
         * 选择所属客户数据
         * @Author:付常涛
         * @Date: 2023/11/16 14:48:45
         */
        async chooseStoreList () {
            this.isStoreChosen = !this.isStoreChosen
            if (this.isStoreChosen) {
                const list = await this.$object(this.customerOption, {
                    pageTitle: '请选择所属客户',
                    // showInDialog: true, // 跳转新页面
                    multiple: false
                });
                return list.acctName;
            } else {
                return false
            }
        },
        /**
         * @desc 查询类型数据
         * <AUTHOR>
         * @date 2022/6/1 15:27
         **/
        async queryTypeList() {
            const data = await this.$http.post(this.$env.appURL + '/action/link/mapConType/queryByExamplePage', {
                oauth: 'ALL',
                pageFlag: true,
                rows: 500,
                page: 1,
                distinctFields: 'type',
                filtersRaw: [
                    {
                        id: 'companyId',
                        property: 'companyId',
                        value: this.userInfo.coreOrganizationTile['l3Id'],
                        operator: '='
                    },
                    {id: 'status', property: 'status', value: 'Active', operator: '='}]
            });
            if (data.success) {
                for (const item of data.rows) {
                    let name = await this.$lov.getNameByTypeAndVal('ACCT_SUB_TYPE', item.type)
                    this.$set(item, 'name', name);
                }
                data.rows.forEach((item, index) => {
                    this.$set(item, 'seq', index + 1)
                });
                this.classifyItemList = data.rows;
            }
        },
        /**
         * 全部、已/未认证、K/V序列查询
         * @Author:付常涛
         * @Date: 2023/11/16 15:10:50
         * @param item  点击的级联层级Item
         */
        async switchTab(item) {
            this.classifyListActive = item;
            // 重置K/V序列
            this.autoList.option.param['kTypeName'] = this.pageParam.kTypeName; // k序列名称
            this.autoList.option.param['vTypeName'] = this.pageParam.vTypeName; // k序列名称
            if (item.val === 'ACCT_MEMBER_LEVEL') {
                /* V序列 */
                const lovData = await this.$lov.getLovByType(item.val);
                if (this.isGuoJiao) {
                    this.classifyItemList = await this.$lov.getLovByParentTypeAndValue({
                        type: item.val,
                        parentType: 'ACCT_MEMBER_LEVEL_COMPANY',
                        parentVal: this.userInfo.coreOrganizationTile['l3Id']
                    });
                } else {
                    this.classifyItemList = lovData.filter(item => !item.parentId);
                }
                this.classifyItemListActive = {};
                this.autoList.list = [];
                delete this.autoList.option.param['isAuthentication']; // 删除是否认证param
                this.autoList.methods.reload();
            } else if (item.val === 'subAcctType') {
                /* K序列 */
                await this.queryTypeList();
                this.autoList.list = [];
                delete this.autoList.option.param['isAuthentication']; // 删除是否认证param
                this.autoList.methods.reload();
            } else if (item.val === 'IMP_FLAG') {
                this.classifyItemList = [];
                this.classifyItemListActive = {};
                this.autoList.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp).concat([{
                    id: 'impFlag',
                    property: 'impFlag',
                    value: 'Y',
                    operator: '='
                }]);
                this.autoList.list = [];
                this.autoList.methods.reload();
            } else if (item.val === 'certified') {
                // let itemList = await this.$lov.getLovByType('BRINGINTO');
                // itemList.push({val: 'unDeal', seq: 3, name: '待处理'});
                // this.classifyItemList = itemList.map(i => {
                //     if (i.val === 'Y') {
                //         return {
                //             val: i.val,
                //             name: '已纳入浪潮投放计划',
                //             seq: 1
                //         }
                //     } else if (i.val === 'N') {
                //         return {
                //             val: i.val,
                //             name: '未纳入浪潮投放计划',
                //             seq: 2
                //         }
                //     } else {
                //         return i;
                //     }
                // });
                /* 已认证 */
                this.classifyItemList = [];
                this.classifyItemListActive = {};
                this.autoList.option.param.isAuthentication = '1';
                this.autoList.list = [];
                this.autoList.methods.reload();
            } else if (item.val === 'uncertified') {
                /* 未认证 */
                this.classifyItemList = [];
                this.classifyItemListActive = {};
                this.autoList.option.param.isAuthentication = '0';
                this.autoList.list = [];
                this.autoList.methods.reload();
            } else {
                /* 全部 */
                delete this.autoList.option.param['isAuthentication']; // 删除是否认证param
                this.classifyItemList = [];
                this.classifyItemListActive = {};
                this.autoList.list = [];
                this.autoList.methods.reload();
            }
        },
        /**
         * 在Tab下，继续筛选数据
         * @Author:付常涛
         * @Date: 2023/11/16 15:21:04
         * @param item  选择的筛选项
         */
        switchTabItem(item) {
            this.classifyItemListActive = item;
            let filtersRaw = this.autoList.option.param.filtersRaw;
            if (this.classifyListActive.field === 'subAcctType') {
                /* K序列 */
                this.autoList.option.param.kTypeName = item.name;
            } else if (this.classifyListActive.field === 'loyaltyLevel') {
                /* V序列 */
                this.autoList.option.param.vTypeName = item.name;
            } else if (this.classifyListActive.val === 'certified') {
                if (item.val !== 'unDeal') {
                    if (!filtersRaw.some((item1) => {
                        return item1.property === 'bringInto'
                    })) {
                        filtersRaw.push({id: 'bringInto', property: 'bringInto', value: item.val, operator: '='})
                    } else {
                        for (let i = 0; i < filtersRaw.length; i++) {
                            if (filtersRaw[i].property === 'bringInto') {
                                filtersRaw[i].value = item.val;
                                filtersRaw[i].operator = '=';
                                break;
                            }
                        }
                    }
                } else {
                    if (!filtersRaw.some((item1) => {
                        return item1.property === 'bringInto'
                    })) {
                        filtersRaw.push({id: 'bringInto', property: 'bringInto', value: '', operator: 'IS NULL'});
                    } else {
                        for (let i = 0; i < filtersRaw.length; i++) {
                            if (filtersRaw[i].property === 'bringInto') {
                                filtersRaw[i].value = '';
                                filtersRaw[i].operator = 'IS NULL';
                                break;
                            }
                        }
                    }
                }
            }
            this.autoList.list = [];
            // this.autoList.option.param.filtersRaw = filtersRaw;
            this.autoList.methods.reload();
        },
        /**
         * @desc 将该消费者状态改为未分配
         * <AUTHOR>
         * @date 2022/4/22 16:10
         **/
        invalidAccount(item) {
            this.$taro.showModal({
                title: '提示',
                content: '取消后无法继续跟进消费者，是否确认取消跟进？',
                success: async (res) => {
                    if (res.confirm) {
                        this.$utils.showLoading();
                        const data = await this.$http.post(this.$env.dmpURL + '/link/consumer/unassignedUpdate', {
                            id: item.id,
                            interfaceSource: 'Artificial'
                        }, {
                            autoHandleError: false,
                            handleFailed: (response) => {
                                this.$utils.hideLoading();
                                this.$showError('取消跟进消费者失败！' + response.result);
                            }
                        });
                        if (data.success) {
                            this.$utils.hideLoading();
                            this.$message.success('取消跟进成功！');
                            this.autoList.methods.reload();
                        }
                    }
                }
            });
        },
        /**
         * @desc 跳转详情
         * <AUTHOR>
         * @date 2022/4/19 18:55
         **/
        gotoItem(data) {
            if (!this.editFlag) return;
            this.$nav.push('/pages/lj-consumers/account/account-item-page', {
                data: data
            });
        },
        /**
         * @desc 拨打电话
         * <AUTHOR>
         * @date 2022/4/19 18:53
         **/
        makePhoneCall(data) {
            const number = data.phone || data.mobilePhone1 || data.mobilePhone ||  data.phoneNumber;
            if (Boolean(number)) {
                wx.makePhoneCall({
                    phoneNumber: number
                });
            }
        }

    }
}
</script>

<style lang="scss">
.account-list-page {
    .classify {
        height: 100px;
    }

    .classify-height {
        height: 200px;
    }

    .top-filter {
        flex: 1;
        overflow-x: hidden;

        .top-filter-content {
            width: 100%;

            .top-filter-info {
                display: flex;
                white-space: nowrap;
                font-size: 24px;
                padding: 8px 24px;
                align-items: center;
                flex-wrap: nowrap;

                .filter-type-item {
                    max-width: 124px;
                    height: 40px;
                    margin: 12px 4px;
                    font-size: 26px;
                    color: #333333;
                    line-height: 40px;
                    font-weight: 400;
                    text-align: center;

                    .link-icon {
                        width: 16px;
                        height: 12px;
                        color: #CCCCCC;
                        margin-left: 8px;
                    }
                }

                .line {
                    width: 2px;
                    height: 32px;
                    position: absolute;
                    right: 4px;
                    top: 24px;
                    background-color: #CCCCCC;
                    border: 1px solid rgba(204,204,204,1);
                }
            }
        }

        .type {
            //width: 160px;

            .choose {
                margin: 15px 0;
                height: 50px;
                line-height: 50px;
                border-radius: 6px;
                padding: 0 20px;
            }

            .chosen {
                background: #EDF3FF;
                color: #2F69F8!important;
            }
        }

        .tag-list {
            display: flex;

            .tag-list-item {
                padding: 8px 16px;
                margin-right: 8px;
                white-space: nowrap;
                display: inline-block;
                background-color: #f2f2f2;
                color: #333333;
                border-radius: 4px;
            }

            .tagChecked {
                background-color: rgba(47, 105, 248, 0.1) !important;
                color: #2f69f8 !important;
            }
        }
    }

    .search-right-item {
        width: 212px;
        height: 72px;
        display: flex;
        align-items: center;
        margin-left: 40px;
        box-sizing: border-box;
        font-size: 26px;
        color: #333333;
        line-height: 40px;
        font-weight: 400;
    }

    .classify-filter-content {
        .classify-filter-list {
            height: 72px;
            display: flex;
            align-items: center;

            .classify-filter-item {
                min-width: 170px;
                height: 56px;
                padding: 0 10px;
                box-sizing: border-box;
                margin: 0 0 16px 24px;
                font-size: 24px;
                color: #333333;
                letter-spacing: 0;
                text-align: center;
                line-height: 56px;
                font-weight: 400;
                border: 1px solid rgba(221,221,221,1);
                border-radius: 28px;
            }

            .label-checked {
                color: #3F66EF;
                border: 0.5px solid rgba(63,102,239,1);
            }
        }
    }

    /*deep*/
    .link-item {
        padding: 12px;
        overflow: hidden;
    }

    .link-item-body-left{
        overflow: visible;
    }

    .account-list {
        background-color: #FFFFFF;
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: column;

        .list-cell {
            position: relative;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;

            .media-list {
                position: relative;
                padding: 32px 24px 24px 24px;
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                width: 100%;

                .media-list-name {
                    height: 48px;
                    font-size: 32px;
                    color: #212223;
                    line-height: 48px;
                    font-weight: 600;
                    display: flex;
                    .acct-name {
                        overflow: hidden;
                        max-width: 360px;
                        text-overflow:ellipsis;
                        white-space: nowrap;
                    }
                    .is-certify {
                        margin-left: 20px;
                        width: 120px;
                        height: 48px;
                        image {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }

                .media-list-label {
                    height: 36px;
                    margin: 16px 0;
                    display: flex;

                    .loyalty-level {
                        min-width: 80px;
                        padding: 0 15px;
                        margin-right: 16px;
                        background: #F0F5FF;
                        border-radius: 4px;
                        font-size: 22px;
                        color: #3F66EF;
                        letter-spacing: 0;
                        text-align: center;
                        line-height: 36px;
                        font-weight: 400;
                    }

                    .important-account {
                        width: 112px;
                        margin-right: 16px;
                        background: #FFF1EB;
                        border-radius: 4px;
                        font-size: 22px;
                        color: #FF461E;
                        line-height: 36px;
                        font-weight: 400;
                        text-align: center;
                    }

                    .black-gold-account {
                        width: auto;
                        padding: 0 5px;
                        background: #262626;
                        border-radius: 4px;
                        font-size: 22px;
                        color: #F0BE94;
                        line-height: 36px;
                        font-weight: 400;
                        text-align: center;
                    }
                }

                .media-list-info {
                    display: flex;
                    flex-direction: column;

                    .media-list-info-item {
                        height: 44px;
                        display: flex;
                        align-items: center;
                        margin-bottom: 8px;

                        .label {
                            width: 112px;
                            margin-right: 24px;
                            font-size: 28px;
                            color: #999999;
                            line-height: 44px;
                            font-weight: 400;
                        }

                        .media-list-info-text {
                            width: 520px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            font-size: 28px;
                            color: #333333;
                            line-height: 44px;
                            font-weight: 400;
                        }

                        .media-list-info-phone {
                            font-size: 28px;
                            color: #317DF7;
                            line-height: 44px;
                            font-weight: 400;
                        }
                    }
                }
            }
        }
    }

    .account-label {
        position: absolute;
        right: -28px;
        top: -4px;
        padding: 8px 48px 4px 32px;
        background: #2F69F8;
        transform: skew(30deg, 0deg);

        .label {
            font-size: 24px;
            color: #FFFFFF;
            text-align: center;
            line-height: 40px;
            font-weight: 400;
            transform: skew(-30deg, 0);
        }
    }

    .account-list-item {
        background: #FFFFFF;
        margin: 24px;
        height: 372px;
        border-radius: 16px;
    }

    .link-auto-list-query-bar-filter-group {
        margin-top: 88px;
    }

    .top-tab-filter {
        position: relative;
    }
    .lnk-tabs-content {
        position: absolute;
        top: -176px;
        display: flex;
        justify-content: space-around;
        flex-wrap: nowrap;
    }

    .lnk-tabs-item {
        height: 92px;
        line-height: 92px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .label-name-line {
            font-size: 28px;
            margin-left: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .line {
                height: 8px;
                width: 56px;
                border-radius: 16px 16px 0 0;
                background-color: #2f69f8;
                box-shadow: 0 3px 8px 0 rgba(47,105,248,0.63);
                margin-top: -8px;
            }
        }
    }

    .list-taps .lnk-tabs{
        top: 96px !important;
        border-top: none;
    }

    .link-auto-list-top-bar {
        border-bottom: none;
    }

    /*deep*/
    .link-swipe-option-container .link-swipe-option {
        width: 100px;
        height: 70px !important;
        border-radius: 80px;
        font-size: 28px !important;
    }
    /*deep*/
    .icon-kehufeidan {
        font-size: 1.3em !important;
    }
    .bottom-sticky {
        width: 100%;
        border-top: 1px solid #F2F2F2;
        height: 166px;
        .link-sticky-content {
            justify-content: flex-end !important;
            height: 166px;
        }
        .check-button{
            margin-right: 20px;
            width: 160px;
            height: 60px;
            border: 1px solid rgba(153,153,153,1);
            border-radius: 8px;
            font-size: 28px;
            color: #333333;
            text-align: center;
            line-height: 60px;
            font-weight: 400;
        }
    }
}
</style>
