
<template>
  <link-page class="account-list-page">
    <link-auto-list :option="autoList" hideCreateButton>
      <view slot="filterGroup" class="top-filter" key="w1w">
        <view class="con-info">
          <view class="rights-item">
            <view class="rights-item-name">模型ID</view>
            <view class="rights-item-value">{{urlParam.id}}</view>
          </view>
          <view class="rights-item">
            <view class="rights-item-name width155">模型名称</view>
            <view class="rights-item-value pad-48 one-row">{{urlParam.ruleName||'--'}}</view>
          </view>
        </view>
        <view class="detail-btn">
          <link-button class="btn" size="mini" @tap="openModelDetail()">查看详情</link-button>
        </view>
      </view>

      <template slot-scope="{data,index}">
        <view>
        <view v-if="index===0" class="menu-stair">
            <view class="line">
            <view class="line-top"></view>
            <view class="line-bottom"></view>
            </view>
            <view class="stair-title">消费者</view>
        </view>
        <link-swipe-action>
          <item :key="index" :data="data" :arrow="false" class="account-list-item">
            <view class="account-list" slot="note">
              <view class="list-cell">
                <view class="media-list" @tap="gotoItem(data)">
                  <view class="media-list-name" @tap="gotoItem(data)">
                    <view class="acct-name">{{ data.acctName || data.consumerName || data.name }}</view>
                    <view class="terminal-label" v-if="data.terminalFlag === 'Y'">终端</view>
                    <view class="is-certify" v-if="data.certify">
                      <image :src="data.certify === 'certified' ? $imageAssets.storeStatusVerifiedImage : $imageAssets.storeStatusUnverifiedImage"></image>
                    </view>
                  </view>
                  <view class="media-list-time" v-if="data.certify === 'certified' && !$utils.isEmpty(data.certifyTime) && data.certifyTime !== '9999-12-31 23:59:59'">
                        <view class="time">{{ `${data.certifyTime.split(' ')[0]}认证到期` }}</view>
                  </view>
                  <view class="media-list-label">
                    <view class="loyalty-level">{{ data.type | lov('ACCT_SUB_TYPE')}} </view>
                    <view class="loyalty-level">{{ data.loyaltyLevel | lov('ACCT_MEMBER_LEVEL') }}</view>
                    <view class="important-account" v-if="data.impFlag === 'Y' && pageFrom!=='PeopleScreen'">重点客户</view>
                    <view class="black-gold-account" v-if="data.identityLevel && pageFrom!=='PeopleScreen'">{{data.identityLevel|lov('ACCT_SUB_TYPE')}}</view>
                  </view>
                  <view class="media-list-info">
                    <view class="media-list-info-item">
                      <view class="label">联系方式</view>
                      <view class="media-list-info-phone" @tap.stop="makePhoneCall(data)">
                        {{ data.mobilePhone1 || data.mobilePhone ||  data.phoneNumber}}
                      </view>
                    </view>
                    <view class="media-list-info-item">
                      <view class="label">单位</view>
                      <view class="media-list-info-text">{{ data.company || data.companyName }}</view>
                    </view>
                    <view class="media-list-info-item">
                      <view class="label">职务</view>
                      <view class="media-list-info-text">{{ data.position }}</view>
                    </view>
                    <view class="media-list-info-item">
                      <view class="label">所属客户</view>
                      <view class="media-list-info-text">{{ data.belongToStore }}</view>
                    </view>
                  </view>
                </view>
              </view>
              <view class="account-label">
                <view class="label">{{ data.fstName }}跟进</view>
              </view>
            </view>
          </item>
        </link-swipe-action>
    </view>
      </template>
    </link-auto-list>
    <DetailModel v-model="showModelDetail" :modelDetailData="modelDetailData"></DetailModel>
    <water-mark></water-mark>
  </link-page>
</template>

<script>
import ConsumerCommon from '../consumer-common';
import waterMark from '../../lzlj/components/water-mark';
import DetailModel from "./filter-model-detail-dialog.vue";

export default {
  name: "account-list-page",
  components: { waterMark, DetailModel },
  mixins: [ConsumerCommon()],
  data () {
    const urlParam = this.pageParam.modelInfo||{};
    const pageFrom = this.pageParam.pageFrom || '';
    const Oauth = this.pageParam.Oauth || '';
    console.log(Oauth,'Oauth')
    const pageOauthList = this.pageParam.secMenus || [{ securityMode: 'MY_POSTN_ONLY', name: '我的数据' }];
    const userInfo = this.$taro.getStorageSync('token').result;
    // let url = this.$env.appURL + '/action/link/sendDmp/consumerListSend';
    const url = this.$env.appURL + '/action/link/sendDmp/send';

    let pageOauth = Oauth || pageOauthList[0].securityMode || 'MY_POSTN_ONLY';
    const autoList = new this.AutoList(this, {

      module: this.$env.appURL + '/action/link/cusOpenBotResult/queryByDistinct',
      queryByExamplePage: '/action/link/sendDmp/send',
      url: {
        queryByExamplePage: url
      },
      loadOnStart: false,
      fetchItem: true,
      param: {
        oauth: this.pageOauth || pageOauth,
        dmpUrl: '/link/cusOpenBotResult/queryByDistinct',
        sort: 'lastUpdated',
        order: 'desc',
        filtersRaw: [
          { id: 'ruleId', property: 'ruleId', value: this.pageParam.modelInfo.id, operator: '=' },
          { id: 'pushStatus', property: 'pushStatus', value: '[DataPush, DataDistribution]', operator: 'in' }
        ],
      }
    });

    return {
      pageOauth,
      pageOauthList,
      pageFrom,
      modelDetailData: {},
      showModelDetail: false,
      urlParam,
      autoList,
      userInfo,
    };
  },
  async created () {
        this.autoList.methods.reload();
  },
  methods: {
    openModelDetail () {
      this.showModelDetail = true;
      Object.keys(this.urlParam).forEach(e => {
        this.$set(this.modelDetailData, e, this.urlParam[e])
      })
    },
    /**
     * @desc 跳转详情
     * <AUTHOR>
     * @date 2022/4/19 18:55
     **/
    gotoItem (data) {
      // this.$nav.push('/pages/lj-consumers/account/account-item-page', {
      //     data: {...data,pageOauth:this.pageOauth},
      //     "pageOauth":this.pageOauth
      // });
    },
    /**
     * @desc 拨打电话
     * <AUTHOR>
     * @date 2022/4/19 18:53
     **/
    makePhoneCall (data) {
      const number = data.mobilePhone1 || data.mobilePhone || data.phoneNumber;
      if (Boolean(number)) {
        wx.makePhoneCall({
          phoneNumber: number
        });
      }
    }

  }
}
</script>

<style lang="scss">
.width155{
    width: 155rpx!important;
}
.pad-48{
    padding-right: 48rpx;
    // flex:1;
    width: 531rpx;
}
.one-row{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.con-info {
    padding: 28rpx;
    width: 518rpx;
  .rights-item {
    width: 100%;
    min-height: 56px;
    font-size: 24px;
    margin-bottom: 14rpx;
    line-height: 1.2;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .rights-item-name {
      width: 100px;
      color: #595959;
    }

    .rights-item-value {
      color: #262626;
    }

    .rights-item-value1 {
      width: calc(100% - 300px);
      color: #262626;
      white-space: nowrap;
    }

    .rights-item-value2 {
      color: #569bf5;
    }

    .rights-item-value-tip {
      color: #59595996;
      font-size: 18px;
      margin-left: 18px;
    }
  }
}
.menu-stair {
  margin-left: 24px;
  padding-top: 24px;
  @include flex-start-center;

  .line {
    .line-top {
      width: 8px;
      height: 16px;
      background: #3fe0e2;
    }

    .line-bottom {
      width: 8px;
      height: 16px;
      background: #2f69f8;
    }
  }

  .stair-title {
    margin-left: 16px;
    font-family: PingFangSC-Semibold, serif;
    font-size: 32px;
    color: #262626;
    letter-spacing: 1px;
    line-height: 32px;
  }
}
.detail-btn {
//   position: absolute;
//   right: 16rpx;
//   top: 20rpx;
width:166px;
display: flex;
align-items: center;
justify-content: center;
}
.account-list-page {
  .classify {
    height: 100px;
  }

  .classify-height {
    height: 200px;
  }

  .top-filter {
    flex: 1;
    overflow-x: hidden;
    display: flex;
    .top-filter-content {
      width: 100%;
      padding-top: 16rpx;
      .top-filter-info {
        display: flex;
        white-space: nowrap;
        font-size: 24px;
        padding: 8px 24px;
        align-items: center;
        flex-wrap: nowrap;

        .filter-type-item {
          max-width: 124px;
          height: 40px;
          margin: 12px 4px;
          font-size: 26px;
          color: #333333;
          line-height: 40px;
          font-weight: 400;
          text-align: center;

          .link-icon {
            width: 16px;
            height: 12px;
            color: #cccccc;
            margin-left: 8px;
          }
        }

        .line {
          width: 2px;
          height: 32px;
          position: absolute;
          right: 4px;
          top: 24px;
          background-color: #cccccc;
          border: 1px solid rgba(204, 204, 204, 1);
        }
      }
    }

    .type {
      .choose {
        margin: 15px 0;
        height: 50px;
        line-height: 50px;
        border-radius: 6px;
        padding: 0 20px;
      }

      .chosen {
        background: #edf3ff;
        color: #2f69f8 !important;
      }
    }

    .tag-list {
      display: flex;

      .tag-list-item {
        padding: 8px 16px;
        margin-right: 8px;
        white-space: nowrap;
        display: inline-block;
        background-color: #f2f2f2;
        color: #333333;
        border-radius: 4px;
      }

      .tagChecked {
        background-color: rgba(47, 105, 248, 0.1) !important;
        color: #2f69f8 !important;
      }
    }
  }

  .top-total {
    box-sizing: border-box;
    width: calc(100% - 68px);
    line-height: 44px;
    height: 60px;
    font-size: 26px;
    margin-left: 20px;
    padding: 8px 24px;
    color: #333333;
  }

  .search-right-item {
    width: 212px;
    height: 72px;
    display: flex;
    align-items: center;
    margin-left: 40px;
    box-sizing: border-box;
    font-size: 26px;
    color: #333333;
    line-height: 40px;
    font-weight: 400;
  }

  .classify-filter-content {
    .classify-filter-list {
      height: 72px;
      display: flex;
      align-items: center;

      .classify-filter-item {
        min-width: 170px;
        height: 56px;
        padding: 0 10px;
        box-sizing: border-box;
        margin: 0 0 16px 24px;
        font-size: 24px;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        line-height: 56px;
        font-weight: 400;
        border: 1px solid rgba(221, 221, 221, 1);
        border-radius: 28px;
      }

      .label-checked {
        color: #3f66ef;
        border: 0.5px solid rgba(63, 102, 239, 1);
      }
    }
  }

  /*deep*/
  .link-item {
    padding: 12px;
    overflow: hidden;
  }

  .link-item-body-left {
    overflow: visible;
  }

  .account-list {
    background-color: #ffffff;
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: column;

    .list-cell {
      position: relative;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .media-list {
        position: relative;
        padding: 40px 24px 24px 24px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        width: 100%;

        .media-list-name {
          height: 48px;
          font-size: 32px;
          color: #212223;
          line-height: 48px;
          font-weight: 600;
          display: flex;
          .acct-name {
            overflow: hidden;
            max-width: 360px;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .is-certify {
            margin-left: 20px;
            width: 120px;
            height: 48px;
            image {
              width: 100%;
              height: 100%;
            }
          }
        }
        .media-list-time {
          .time {
            color: black;
            font-weight: bold;
          }
        }

        .terminal-label {
          margin: auto 0;
          margin-left: 16px;
          height: 36px;
          width: 80px;
          background: #f0f5ff;
          border-radius: 4px;
          font-size: 22px;
          color: #3f66ef;
          letter-spacing: 0;
          text-align: center;
          line-height: 36px;
          font-weight: 400;
        }

        .media-list-label {
          height: 36px;
          margin: 16px 0;
          display: flex;

          .loyalty-level {
            min-width: 80px;
            padding: 0 15px;
            margin-right: 16px;
            background: #f0f5ff;
            border-radius: 4px;
            font-size: 22px;
            color: #3f66ef;
            letter-spacing: 0;
            text-align: center;
            line-height: 36px;
            font-weight: 400;
          }

          .important-account {
            width: 112px;
            margin-right: 16px;
            background: #fff1eb;
            border-radius: 4px;
            font-size: 22px;
            color: #ff461e;
            line-height: 36px;
            font-weight: 400;
            text-align: center;
          }

          .black-gold-account {
            width: auto;
            padding: 0 5px;
            background: #262626;
            border-radius: 4px;
            font-size: 22px;
            color: #f0be94;
            line-height: 36px;
            font-weight: 400;
            text-align: center;
          }
        }

        .media-list-info {
          display: flex;
          flex-direction: column;

          .media-list-info-item {
            height: 44px;
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .label {
              width: 112px;
              margin-right: 24px;
              font-size: 28px;
              color: #999999;
              line-height: 44px;
              font-weight: 400;
            }

            .media-list-info-text {
              width: 520px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              font-size: 28px;
              color: #333333;
              line-height: 44px;
              font-weight: 400;
            }

            .media-list-info-phone {
              font-size: 28px;
              color: #317df7;
              line-height: 44px;
              font-weight: 400;
            }
          }
        }
      }
    }
  }

  .account-label {
    position: absolute;
    right: -28px;
    top: -12px;
    padding: 8px 48px 4px 32px;
    background: #2f69f8;
    transform: skew(30deg, 0deg);

    .label {
      font-size: 24px;
      color: #ffffff;
      text-align: center;
      line-height: 40px;
      font-weight: 400;
      transform: skew(-30deg, 0);
    }
  }

  .account-list-item {
    background: #ffffff;
    margin: 24px;
    border-radius: 16px;
  }

  .link-auto-list-query-bar-filter-group {
    height: 180rpx;
    .top-filter {
      height: 180rpx !important;
    }
  }

  .top-tab-filter {
    position: relative;
  }
  .lnk-tabs-content {
    width: 100%;
    box-sizing: border-box;
    padding: 0 36px;
    position: absolute;
    display: flex;
    justify-content: flex-start;
    flex-wrap: nowrap;
  }

  .lnk-tabs-item {
    margin-right: 70px;
    height: 92px;
    line-height: 92px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .label-name-line {
      position: relative;
      font-size: 28px;
      margin-left: 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .label-name-tips {
        position: absolute;
        top: 0;
        right: 0;
        color: red;
        transform: translate(100%, -20%);
      }
      .line {
        height: 8px;
        width: 56px;
        border-radius: 16px 16px 0 0;
        background-color: #2f69f8;
        box-shadow: 0 3px 8px 0 rgba(47, 105, 248, 0.63);
        margin-top: -8px;
      }
    }
  }

  .list-taps .lnk-tabs {
    top: 96px !important;
    border-top: none;
  }

  .link-auto-list-top-bar {
    border-bottom: none;
  }

  /*deep*/
  .link-swipe-option-container .link-swipe-option {
    width: 100px;
    height: 70px !important;
    border-radius: 80px;
    font-size: 28px !important;
  }
  /*deep*/
  .icon-kehufeidan {
    font-size: 1.3em !important;
  }
  .bottom-sticky {
    width: 100%;
    border-top: 1px solid #f2f2f2;
    height: 166px;
    .link-sticky-content {
      justify-content: flex-end !important;
      height: 166px;
    }
    .check-button {
      margin-right: 20px;
      width: 160px;
      height: 60px;
      border: 1px solid rgba(153, 153, 153, 1);
      border-radius: 8px;
      font-size: 28px;
      color: #333333;
      text-align: center;
      line-height: 60px;
      font-weight: 400;
    }
  }
}
</style>
