<template>
    <link-page class="account-item-address-edit-page">
        <link-form>
            <link-form-item :arrow="false" label="所在地区" required>
                <view class="address-placeholder" style="color: #e0e0e0"
                      v-if="$utils.isEmpty(addressItem.province) && $utils.isEmpty(addressItem.city) && $utils.isEmpty(addressItem.district) && $utils.isEmpty(addressItem.street)"
                      @tap="getLocation">
                    请选择所在地区
                    <link-icon icon="icon-location" class="link-location" style="color: #3e68ef"/>
                </view>
                <view class="address-color" @tap="getLocation">
                    <text v-if="!$utils.isEmpty(addressItem.province)">{{ addressItem.province }}</text>
                    <text v-if="!$utils.isEmpty(addressItem.city)">/{{ addressItem.city }}</text>
                    <text v-if="!$utils.isEmpty(addressItem.district)">/{{ addressItem.district }}</text>
                    <text v-if="!$utils.isEmpty(addressItem.street)">/{{ addressItem.street }}</text>
                    <link-icon v-if="!$utils.isEmpty(addressItem.province)" icon="icon-location" class="link-location" style="color: #3e68ef"/>
                </view>
            </link-form-item>
            <link-form-item label="详细地址"
                            required
                            vertical style="border-bottom: 1px solid rgb(247,247,247);">
                <link-textarea v-model="addressItem.addr"/>
            </link-form-item>
            <link-form-item label="主要地址">
                <link-switch v-model="addressItem.isDefault" :disabled="isDefault === 'Y'"/>
            </link-form-item>
        </link-form>
        <link-sticky>
            <link-button block @tap="saveAddress">保存</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
import {reverseTMapGeocoder} from "../../../utils/locations-tencent";

    export default {
        name: "account-item-address-edit",
        data() {
            const addressItem = this.pageParam.item || {};
            const isDefault = this.pageParam.isDefault || 'N';
            return {
                addressItem,
                isDefault
            }
        },
        async onShow(){
            const location = this.$locations.QQGetLocation();
            if(location){
                let addressInfo =  await reverseTMapGeocoder(location.latitude, location.longitude, '终端所在地址');
                try {
                    this.$utils.showLoading()
                    const addrCode = addressInfo['originalData'].result.addressComponent.adcode;
                    const data = await this.$http.post(this.$env.appURL +'/action/link/alladdress/queryEffectiveByDistrictCode',{addrCode: addrCode})
                    if(data.success) {
                        if(data.adcodeFlag){
                            this.$set(this.addressItem, 'province', data.province);
                            this.$set(this.addressItem, 'city', data.city);
                            this.$set(this.addressItem, 'district', data.district);
                            this.$set(this.addressItem, 'street', addressInfo['originalData'].result.addressComponent['street'] ? addressInfo['originalData'].result.addressComponent['street'] : '');
                            this.$set(this.addressItem, 'addr', addressInfo['originalData'].result['formatted_address'].replace(this.addressItem.province +this.addressItem.city + this.addressItem.district, '') + ((addressInfo['originalData'].result['poiRegions'][0] && addressInfo['originalData'].result['poiRegions'][0].name) || ''));
                            this.$utils.hideLoading();
                        }else {
                            this.$set(this.addressItem, 'province', addressInfo['originalData'].result.addressComponent['province']);
                            this.$set(this.addressItem, 'city', addressInfo['originalData'].result.addressComponent['city']);
                            this.$set(this.addressItem, 'district', addressInfo['originalData'].result.addressComponent['district']);
                            this.$set(this.addressItem, 'street', addressInfo['originalData'].result.addressComponent['street'] ? addressInfo['originalData'].result.addressComponent['street'] : '');
                            this.$set(this.addressItem, 'addr', addressInfo['originalData'].result['formatted_address'].replace(this.addressItem.province +this.addressItem.city + this.addressItem.district, '') + ((addressInfo['originalData'].result['poiRegions'][0] && addressInfo['originalData'].result['poiRegions'][0].name) || ''));
                            this.$utils.hideLoading();
                        }
                    }
                } catch (e) {
                    this.$utils.hideLoading();
                }
               }
        },
        async created() {
            this.$locations.QQClearLocation();
        },
        methods: {
            /**
             *  @description: 获取定位
             *  @author: 马晓娟
             *  @date: 2020/10/20 20:05
             */
            async getLocation() {
                const addressInfo = await this.$locations.getAddress();
                await this.$locations.chooseLocation(addressInfo.wxMarkerData[0].latitude, addressInfo.wxMarkerData[0].longitude);
            },
            async saveAddress() {
                if (this.$utils.isEmpty(this.addressItem.province) || this.$utils.isEmpty(this.addressItem.city) || this.$utils.isEmpty(this.addressItem.district)) {
                    this.$taro.showToast({title: '请选择省市区!'});
                    return;
                }
                if (this.$utils.isEmpty(this.addressItem.addr)) {
                    this.$taro.showToast({title: '请输入详细地址!'});
                    return;
                }
                if(!this.addressItem.row_status) {
                    this.addressItem.row_status = 'NEW';
                }
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + '/action/link/acctaddress/upsert', this.addressItem, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('保存失败！' + response.result);
                    }
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    if (this.addressItem.isDefault === 'Y') {
                        this.addressItem['id'] = data.newRow.id;
                        await this.setMainAddr();
                    }
                    const newData = data.newRow;
                    if(this.addressItem.row_status === 'NEW'){
                        this.pageParam.callback(newData);
                    }
                    if(this.addressItem.row_status === 'UPDATE'){
                    //更新地址列表this.addressOption.methods.reload();
                        this.pageParam.callback();
                    }
                    this.$nav.back();
                }

            },
            /**
             * 设置主要地址
             * <AUTHOR>
             * @date 2019-07-22
             */
            async setMainAddr() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/acctaddress/setDefaultAddr', this.addressItem, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError(`设置主要地址失败：${response.result}`);
                }});
            }
        }
    }
</script>

<style lang="scss">
.account-item-address-edit-page{

    .address-color {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        justify-content: flex-end;
        /*deep*/
        .link-input-inner {
            overflow-x: auto;
            width: 300px;
        }
    }
}
</style>
