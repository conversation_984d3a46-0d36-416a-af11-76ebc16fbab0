<template>
    <view class="consumer-tag-info">
        <link-dialog ref="positionBottom" position="bottom" height="85vh" class="dialog-bottom" noPadding v-model="show">
            <view class="model-title">
                <view class="iconfont icon-left" @tap="dialogHide"></view>
                <view class="title" style="padding-left:0;">请选择标签</view>
                <view class="iconfont icon-close" @tap="dialogHide"></view>
            </view>
            <view class="dialog-content" style="height: calc(100% - 44px)">
                <scroll-view scroll-y="true" :style="{'height': 'calc(100% - 75px)'}">
                    <view class="tags-item" v-for="(tagGroup,index) in tagGroupList">
                        <view class="tag-header">
                            <view class="tag-title">
                                <view v-if="tagGroup.required && tagGroup.required === 'Y'" class="required">*</view>
                                <view class="tag-title-text">{{tagGroup.tagGroupName}}</view>
                                <link-icon :icon="tagGroup.checked ? 'mp-asc' : 'mp-desc'" @tap="showTagList(tagGroup)"/>
                            </view>
                            <view class="tag-item-btn" v-if="!tagGroup.editFlag || tagGroup.tagUniqVerify === 'TagItem'">
                                <link-button mode="text" :label="tagGroup.selectAllFlag ? '取消' : '全选'" @tap="selectAllTagGroup(tagGroup)"/>
                            </view>
                        </view>
                        <scroll-view scroll-y="true" class="classify-filter-content">
                            <view class="classify-filter-list" v-show="tagGroup.checked && tagGroup.tagItemList.length > 0">
                                <view v-for="(item,tagIndex) in tagGroup.tagItemList" :class="item.checked ? 'classify-filter-item label-checked' : 'classify-filter-item'" @tap="chooseTag(tagGroup,item)">
                                    {{ item.tagName }}
                                </view>
                            </view>
                        </scroll-view>
                        <view v-show="tagGroup.checked && tagGroup.tagItemList.length >= 20" class="show-more" @tap="showMoreTags(tagGroup)">{{ tagGroup.showMore ? '加载完成' : '展示更多' }}</view>
                    </view>
                </scroll-view>
                <view class="link-dialog-foot-custom">
                    <link-button shadow @tap="filterTag" label="确定" style="width:100vw"/>
                </view>
            </view>
        </link-dialog>
    </view>
</template>

<script>
export default {
    name: 'tag-info',
    props: {
        tagGroupList: {
            type: Array,
            default: []
        },
        tagLabels: {
            type: Array
        },
        show: false
    },
    data() {
        return {
            selectData: [],         //选中的数据
            selectCopyData: []      //选中的备份数据
        }
    },
    methods: {
        /**
         * @createdBy 曾宇
         * @date 2023/4/11
         * @methods: showMoreTags
         * @para: group 标签组数据
         * @description: 加载更多标签值
         **/
        async showMoreTags(group) {
            const data = await this.$http.post(this.$env.dmpURL + '/link/portalAccntTagGroup/queryTagItemsByTagGroup', {
                pageFlag: false,
                validFlag: 'Y',
                filtersRaw: [{id: 'headId', property: 'headId', value: group.id, operator: '='}]
            });
            if (data.rows.length > 0) {
                data.rows.forEach((item) => {
                    item.checked = false;
                });
                group.tagItemList = data.rows;
                if (data.total === group.tagItemList.length) group.showMore = true;
            }
        },
        /**
         * @createdBy 曾宇
         * @date 2023/4/10
         * @methods: showTagList
         * @para: tagGroup 标签组
         * @description: 展示标签值
         **/
        showTagList(tagGroup) {
            tagGroup.checked = !tagGroup.checked;
        },
        /**
         * @createdBy 曾宇
         * @date 2023/4/10
         * @methods: selectAllTagGroup
         * @description: 标签组全选
         **/
        selectAllTagGroup(tagGroup) {
            tagGroup.tagItemList.forEach((item) => {
                item.checked = !tagGroup.selectAllFlag;
            });
            tagGroup.selectAllFlag = !tagGroup.selectAllFlag;
        },
        /**
         * @createdBy 曾宇
         * @date 2023/4/10
         * @methods: chooseTag
         * @description: 选择标签
         **/
        chooseTag(tagGroup, item) {
            if (tagGroup.editFlag && tagGroup.tagUniqVerify === 'TagGroup') {
                tagGroup.tagItemList.forEach((tag) => {
                    if (item.tagId === tag.tagId) {
                        tag.checked = !tag.checked;
                        if (tag.checked && tag.id) delete tag.id;
                    } else {
                        tag.checked = false;
                    }
                });
            } else {
                item.checked = !item.checked;
                if (item.checked && item.id) delete item.id;
                const allFlag = tagGroup.tagItemList.find((tag) => !tag.checked);
                tagGroup.selectAllFlag = !allFlag;
            }
        },
        /**
         * @createdBy 曾宇
         * @date 2023/4/10
         * @methods: filterTag
         * @description: 确认标签筛选
         **/
        filterTag() {
            this.selectData = [];
            let requiredFlag = false;
            for(let i = 0; i< this.tagGroupList.length; i++) {
                let list = [];
                let itemTags = [];
                this.tagGroupList[i].tagItemList.forEach((tag) => {
                    if (this.tagGroupList[i].editFlag) {
                        this.tagLabels.forEach((group) => {
                            const flag = group.itemTags.find((item) => item.tagId === tag.tagId);
                            if (flag && !tag.checked){
                                tag.ifActive = 'N';
                                tag.id = flag.id;
                                list.push(tag);
                            }
                        });
                        if (tag.checked) {
                            tag.ifActive = 'Y';
                            list.push(tag);
                        }
                    } else {
                        const checkFlag = this.selectData.find((item) => item.id === tag.tagId);
                        if (!checkFlag && tag.checked) {
                            this.selectData.push({headId: this.tagGroupList[i].id, id: tag.tagId});
                        }
                    }
                });
                if (this.tagGroupList[i].editFlag && list.length > 0) {
                    itemTags = list.map(
                        j => ({
                            tagGroupId: this.tagGroupList[i].id,
                            tagGroupName: this.tagGroupList[i].tagGroupName,
                            tagId: j.tagId,
                            id: j.id,
                            tagName: j.tagName,
                            tagUniqVerify: this.tagGroupList[i].tagUniqVerify,
                            ifActive: j.ifActive
                        })
                    );
                    const val = {
                        tagGroupId: this.tagGroupList[i].id,
                        tagGroupName: this.tagGroupList[i].tagGroupName,
                        tagUniqVerify: this.tagGroupList[i].tagUniqVerify,
                    }
                    this.$set(val, 'itemTags', itemTags);
                    this.selectData.push(val);
                }
                const requiredList = list.filter((item) => item.ifActive === 'Y');
                if (this.tagGroupList[i].editFlag && this.tagGroupList[i].required && this.tagGroupList[i].required === 'Y' && requiredList.length === 0) {
                    requiredFlag = true;
                    this.$message.info(`请选择【${this.tagGroupList[i].tagGroupName}】标签`);
                    return;
                }
            }
            if (requiredFlag) return;
            console.log(this.selectData, 'selectdata')
            this.selectCopyData = this.$utils.deepcopy(this.selectData);
            this.$emit('choose', this.selectData);
            this.$emit('update:show', false);
        },
        /**
         * @createdBy 曾宇
         * @date 2023/4/10
         * @methods:dialogHide
         * @description: 关闭弹窗
         **/
        dialogHide(){
            this.selectData = this.$utils.deepcopy(this.selectCopyData);
            this.tagGroupList.forEach((group) => {
                group.tagItemList.forEach((tag) => {
                    if(!group.editFlag) {
                        const checkFlag = this.selectData.find((item) => item.id === tag.tagId);
                        tag.checked = !!checkFlag;
                    } else {
                        tag.checked = false;
                    }
                });
                if(this.tagLabels && this.tagLabels.length > 0) {
                    this.tagLabels.forEach((groups) => {
                        groups.itemTags.forEach((tag) => {
                            const flag = group.tagItemList.find((label) => label.tagId === tag.tagId);
                            if(flag) {
                                flag.checked = true;
                            }
                        })
                    })
                }
                const allFlag = group.tagItemList.find((tag) => !tag.checked);
                group.selectAllFlag = !allFlag;
            });
            this.$emit('update:show', false);
        }
    }
}
</script>

<style lang="scss">
.consumer-tag-info {
    .link-dialog-foot-custom{
        width: auto !important;
    }

    .link-dialog-body{
        position: relative;
    }

    .link-dialog-content {
        border-radius: 48px 48px 0 0;
    }

    .dialog-bottom{
        .dialog-content{
            padding: 0 20px;
            position: relative;

            .tags-item {
                display: flex;
                flex-direction: column;
                margin: 24px 32px;

                .tag-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 24px;

                    .tag-title {
                        display: flex;

                        .required {
                            color: red;
                            font-size: 32px;
                            line-height: 64px;
                            margin-right: 4px;
                        }

                        .tag-title-text {
                            font-size: 32px;
                            color: #333333;
                            line-height: 48px;
                            font-weight: 600;
                        }

                        .link-icon {
                            font-size: 32px;
                            color: #CCCCCC;
                            margin: 8px 0 0 8px;
                        }
                    }
                }

                .classify-filter-content {
                    padding-bottom: 16px;
                    .classify-filter-list {
                        height: 192px;
                        display: flex;
                        align-items: center;
                        flex-wrap: wrap;

                        .classify-filter-item {
                            min-width: 120px;
                            height: 72px;
                            box-sizing: border-box;
                            margin: 0 0 16px 24px;
                            font-family: PingFangSC-Regular;
                            font-size: 24px;
                            color: #333333;
                            letter-spacing: 0;
                            text-align: center;
                            line-height: 72px;
                            font-weight: 400;
                            border: 2px solid rgba(221,221,221,1);
                            border-radius: 36px;
                        }

                        .label-checked {
                            min-width: 120px;
                            height: 72px;
                            box-sizing: border-box;
                            margin: 0 0 16px 24px;
                            font-size: 24px;
                            color: #3F66EF;
                            letter-spacing: 0;
                            text-align: center;
                            line-height: 72px;
                            font-weight: 400;
                            background: #FFFFFF;
                            border: 1px solid rgba(63,102,239,1);
                            border-radius: 36px;
                        }
                    }
                }

                .show-more {
                    height: 36px;
                    margin-top: 24px;
                    font-size: 24px;
                    text-align: center;
                    color: #333333;
                    line-height: 36px;
                    font-weight: 400;
                }
            }
        }
        .model-title {
            display: flex;
            .title {
                font-family: PingFangSC-Regular;
                font-size: 32px;
                color: #212223;
                letter-spacing: 0;
                font-weight: 400;
                text-align: center;
                line-height: 112px;
                height: 112px;
                width: 90%;
                padding-left: 0!important;
                //margin-right: 80px;
            }
            .icon-left{
                width: 48px;
                height: 48px;
                color: #BFBFBF;
                font-size: 48px;
                line-height: 48px;
                margin: 32px;
            }
            .icon-close {
                width: 48px;
                height: 48px;
                color: #BFBFBF;
                font-size: 48px;
                line-height: 48px;
                margin: 32px;
            }
        }
    }
}
</style>
