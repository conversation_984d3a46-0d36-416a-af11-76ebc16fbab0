<template>
    <link-page class="essential-info">
        <!--消费者快速识别手机号、姓名、身份证号、地址信息等-->
        <view class="distinguish"
              v-if="formData.row_status === 'NEW' && !formOverallReadonly && pageFrom !== 'IneffectiveAccount'">
            <!--            <view class="tips"></view>-->
            <view class="distinguish-info">
                <textarea class="distinguish-content" v-model="copyContent"
                          placeholder="请粘贴或输入文本，点击“识别”自动识别姓名，手机号，性别，地址（标准地址：省市区+详细地址），身份证号。示例：泸小二，136XXXX0000，女，四川省泸州市龙马潭区某小区，610XXXXXXXXXX1110"/>
                <view class="distinguish-button">
                    <link-button size="mini" block @tap="distinguish">识别</link-button>
                </view>
            </view>
        </view>
        <!--消费者基础字段信息-->
        <link-form :rules="formRulesData" :value="formData" ref="accountItemEditForm">
            <line-title title="基本信息"/>
            <view v-for="(item, index) in standarFieldsData" :key="index">
                <link-form-item label="手机号码" :required="item.required === 'Y'" :field="item.field"
                                v-if="item.field === 'phoneNumber'"
                                :readonly="formData.row_status!=='NEW'||pageFrom==='IneffectiveAccount'"
                                :note="item.note">
                    <link-input type="number" v-model="formData.phoneNumber" @input="onKeyPhone"
                                :disabled="formOverallReadonly"/>
                </link-form-item>
                <link-form-item label="姓名" :required="item.required === 'Y'" :field="item.field"
                                v-if="item.field === 'name'" :note="item.note">
                    <link-input type="text" :index="2" v-model="formData.name"
                                :readonly="readonlyFlag || item['readonly']" :disabled="formOverallReadonly"/>
                </link-form-item>
                <link-form-item label="性别" :required="item.required === 'Y'" :field="item.field" arrow
                                v-if="item.field === 'gender'" :readonly="readonlyFlag || item['readonly']">
                    <link-lov type="GENDER" v-model="formData.gender" :disabled="formOverallReadonly"/>
                </link-form-item>
                <link-form-item label="单位名称" :required="item.required === 'Y'" :field="item.field"
                                class="company-row" v-if="item.field === 'companyName'"
                                :readonly="readonlyFlag || item['readonly']" :note="item.note">
                    <link-input type="text" v-model="formData.companyName" placeholder="请填写或选择单位名称"
                                @blur="writeCompany()"
                                :disabled="formOverallReadonly || disableCompanyFlag"
                                :nativeProps="{maxlength: 65}"/>
                    <view class="ent-wrap" @tap="getEntInfo()">
                        <view class="iconfont icon-sousuo"></view>
                        <view class="choose-ent">选择企业</view>
                    </view>
                </link-form-item>
                <link-form-item label="职务" :required="item.required === 'Y'" :field="item.field"
                                v-if="item.field === 'position'" :readonly="readonlyFlag || item['readonly']"
                                :note="item.note">
                    <link-input type="text" v-model="formData.position"
                                class="input"
                                :nativeProps="{maxlength: 65}"
                                :disabled="formOverallReadonly"/>
                </link-form-item>
                <link-form-item label="职务等级" arrow :required="item.required === 'Y'" :field="item.field"
                                v-if="item.field === 'positionLevel'" :readonly="readonlyFlag || item['readonly']"
                                :note="item.note">
                    <link-lov type="POSITION_LEVEL" v-model="formData.positionLevel" :disabled="formOverallReadonly"/>
                </link-form-item>
                <!--                <link-form-item label="转交职位" :required="item.required === 'Y'" :field="item.field" v-if="transferPostnFlag&& item.field === 'postnName'" :readonly="Boolean('true') || item['readonly']" :note="item.note">-->
                <!--                    <link-input type="text" v-model="formData.postnName" @tap="gotoPostnList()"  placeholder="请选择职位"  suffixIcon="icon-right" :disabled="formOverallReadonly"></link-input>-->
                <!--                </link-form-item>-->
                <link-form-item :required="item.required === 'Y'" :field="item.field" v-if="item.showFlag"
                                :label="item.label" :readonly="item['readonly']" :note="item.note"
                                :disabled="formOverallReadonly">
                    <item slot="custom" :title="item.label"
                          @tap="selectData(item.field, item.key, item.type, item['readonly'])" :style="{backgroundColor: ['socialCircle','positionGrade','enterpriseLevel','enterpriseSize','personnelType'].includes(item.field) && item.readonly ? '#e0e0e0': ''}">
                        <view v-if="formData[item.field]" :style="{color: formOverallReadonly ? '': '#3b4144'}">
                            {{ formData[item.field]| lov(item.type) }}
                        </view>
                        <view style="color: #e0e0e0;" v-else-if="!formOverallReadonly">
                            请选择{{ item.label }}
                        </view>

                    </item>
                </link-form-item>
                <view v-if="item.hide !== 'Y'">
                    <link-form-item label="影响力（K序列）等级" :required="item.required === 'Y'" :field="item.field"
                                    v-if="item.field === 'type'" :readonly="true" :note="item.note" :arrow="false">
                        {{ formData.type|lov('ACCT_SUB_TYPE') }}
                    </link-form-item>
                </view>
                <link-form-item label="是否认证" required :field="item.field" arrow
                                v-if="!!formData['certify'] && item.field === 'certify'" :readonly="true">
                    <link-lov type="CERTIFY" v-model="formData.certify" :disabled="true"/>
                </link-form-item>
                <link-form-item label="推荐纳入浪潮计划投放" arrow v-if="item.field === 'bringInto'"
                                :readonly="readonlyFlag || item['readonly']" :required="item.required === 'Y'"
                                :field="item.field" :note="item.note">
                    <link-lov type="BRINGINTO" v-model="formData.bringInto" :disabled="formOverallReadonly"/>
                </link-form-item>
                <link-form-item :arrow="false" label="所在地区" :readonly="item['readonly']" :required="item.required === 'Y' && !invaliLocFlag"
                                :field="['province', 'city', 'county', 'street']" v-if="item.field === 'province'"
                                :note="item.note">
                    <view class="address-placeholder" style="color: #e0e0e0"
                          v-if="$utils.isEmpty(formData.province) && $utils.isEmpty(formData.city) && $utils.isEmpty(formData.county) && $utils.isEmpty(formData.street)"
                          @tap="getLocation('storeAddr')">
                        请选择所在地区
                        <link-icon icon="icon-location" class="link-location"/>
                    </view>
                    <view class="address-color" @tap="getLocation('storeAddr')">
                        <text v-if="!$utils.isEmpty(formData.province)">{{ formData.province }}</text>
                        <text v-if="!$utils.isEmpty(formData.city)">/{{ formData.city }}</text>
                        <text v-if="!$utils.isEmpty(formData.county)">/{{ formData.county }}</text>
                        <text v-if="!$utils.isEmpty(formData.street)">/{{ formData.street }}</text>
                        <link-icon v-if="!$utils.isEmpty(formData.province)" icon="icon-location" class="link-location"/>
                    </view>
                </link-form-item>
                <link-form-item label="详细地址"
                                v-if="item.field === 'detailedAddress'"
                                :required="item.required === 'Y'"
                                :field="item.field"
                                :note="item.note"
                                :readonly="item['readonly']"
                                vertical style="border-bottom: 1px solid rgb(247,247,247);">
                    <link-textarea v-model="formData.detailedAddress" :disabled="formOverallReadonly"/>
                </link-form-item>
                <link-form-item :label="item.label" :required="item.required === 'Y'" :field="item.field"
                                v-if="item.field === 'assessTerminal'" :readonly="item['readonly']" :note="item.note">
                    <link-object :option="finalOption" pageTitle="考核终端"
                                 :row="formData"
                                 :disabled="formOverallReadonly"
                                 :beforeSelect="beforeSelectAssess"
                                 :value="formData.assessTerminalName"
                                 :map="{assessTerminal:'id', assessTerminalName: 'acctName'}">
                    </link-object>
                </link-form-item>
                <link-form-item :label="item.label" :required="item.required === 'Y'" :field="item.field"
                                v-if="item.field === 'schedulFlag'" :readonly="item['readonly']" :note="item.note">
                    <link-switch v-model="formData.schedulFlag" :disabled="formOverallReadonly"/>
                </link-form-item>
                <link-form-item label="客户来源"
                                :readonly="item['readonly']"
                                :required="item.required === 'Y'"
                                :field="item.field"
                                :note="item.note"
                                v-if="item.field === 'sourceFrom'">
                    <link-lov type="SOURCE_FROM" v-model="formData.sourceFrom" :disabled="formOverallReadonly"
                              :index="31" @change="changeSourceFrom" :excludeLovs="['BottleOpen']"></link-lov>
                </link-form-item>
                <view v-if="item.field === 'interReference'" class="other-object-data">
                    <title-line label-name="推荐人（员工）" :button-name="formOverallReadonly ? '' : '添加'"
                                @tap="addRecommend"></title-line>
                    <view v-if="recommendAccountList.length > 0">
                        <link-swipe-action v-for="(itemRe, indexRe) in recommendAccountList" :key="indexRe">
                            <link-swipe-option slot="option" @tap="handleAccountDelete(itemRe,indexRe)">删除
                            </link-swipe-option>
                            <item :arrow="false">
                                <view style="width:100%;">
                                    <view class="terminal-item">
                                        <view class="terminal-item__left">{{ itemRe.recommenderName }}</view>
                                        <view class="terminal-item__right" v-if="itemRe.mainFlag !== 'Y'">设为主要
                                        </view>
                                    </view>
                                    <view class="terminal-item">
                                        <view class="terminal-item__left">{{ itemRe.recommenderPosition }}</view>
                                        <view class="terminal-item__right">
                                            <link-switch class="row-2"
                                                         :disabled="itemRe.mainFlag === 'Y' || formOverallReadonly"
                                                         @change="switchChangeRecommend(itemRe)"
                                                         v-model="itemRe.mainFlag"/>
                                        </view>
                                    </view>
                                </view>
                            </item>
                        </link-swipe-action>
                    </view>
                </view>
                <link-form-item label="推荐人（客户）" :required="item.required === 'Y'" :field="item.field"
                                v-if="item.field=== 'sourceFrom'&& (formData.sourceFrom === 'StoreRecommend' || formData.sourceFrom === 'DealerOut'|| formData.sourceFrom === 'DistributorOut')"
                                :readonly="item['readonly']" :note="item.note">
                    <link-object :option="finalOption" pageTitle="选择推荐人（客户）"
                                 :row="formData"
                                 :disabled="formOverallReadonly"
                                 :beforeSelect="beforeSelectRecommend"
                                 :value="formData.recommenderAcctName"
                                 :map="{recommenderAcctId:'id', recommenderAcctName: 'acctName', acctLevel: 'acctLevel'}">
                    </link-object>
                </link-form-item>
                <link-form-item label="客户规划等级" :field="item.field" :required="item.required === 'Y'"
                                v-if="item.field=== 'acctLevel' && formData.acctLevel!==null && formData.sourceFrom === 'StoreRecommend'"
                                :readonly="item['readonly']" :note="item.note">
                    <view class="terminal-item__right">{{ formData.acctLevel|lov('ACCT_LEVEL') }}</view>
                </link-form-item>
                <view v-if="item.field=== 'sourceFrom'" class="other-object-data">
                    <view style="height: 12px"></view>
                    <link-form-item label="推荐人（消费者）" field="recommender"
                                    :required="formData.sourceFrom === 'ConsumerRecommend'" :readonly="item['readonly']"
                                    arrow :note="item.note">
                        <link-object :option="recommendCustomerListOption" pageTitle="选择推荐人（消费者）"
                                     :row="formData"
                                     :beforeSelect="beforeSelectCustom"
                                     :disabled="formOverallReadonly"
                                     :value="formData.recommender"
                                     :map="{recommenderId:'id', recommender: 'acctName'}">
                        </link-object>
                    </link-form-item>
                </view>
                <view class="other-object-data" v-if="item.field === 'belongToStore'">
                    <title-line label-name="所属客户" :button-name="formOverallReadonly ? '' : '添加'"
                                @tap="addTerminal"></title-line>
                    <link-swipe-action v-for="(itemStore, indexStore) in terminalList" :key="indexStore">
                        <link-swipe-option slot="option" @tap="handleTerminalDelete(itemStore,indexStore)"
                                           v-if="formData.sourceFrom === 'StoreRecommend' || formData.sourceFrom === 'DealerOut'|| formData.sourceFrom === 'DistributorOut'">
                            删除
                        </link-swipe-option>
                        <item :arrow="false">
                            <view style="width:100%;">
                                <view class="terminal-item">
                                    <view class="terminal-item__left" style="width: 100%">{{ itemStore.acctName }}
                                    </view>
                                </view>
                                <view class="terminal-item">
                                    <view class="terminal-item__left" v-if="itemStore.acctLevel">
                                        {{ itemStore.acctLevel | lov('ACCT_LEVEL') }}
                                    </view>
                                    <view class="terminal-item__left" v-else>-</view>
                                    <view class="terminal-item__right" v-if="itemStore.mainFlag !== 'Y'">设为主要</view>
                                </view>
                                <view class="terminal-item">
                                    <view class="terminal-item__left">{{ itemStore.acctCode }}</view>
                                    <view class="terminal-item__right">
                                        <link-switch class="row-2"
                                                     :disabled="itemStore.mainFlag === 'Y' || formOverallReadonly"
                                                     @change="switchChange(itemStore)" v-model="itemStore.mainFlag"/>
                                    </view>
                                </view>
                            </view>
                        </item>
                    </link-swipe-action>
                </view>
                <link-form-item label="身份证号" :required="item.required === 'Y'" :field="item.field"
                                v-if="item.field === 'idCardNumber'" :readonly="item['readonly']" :note="item.note">
                    <link-input type="text" v-model="formData.idCardNumber" :disabled="formOverallReadonly"
                                @change="getBirthDay"/>
                </link-form-item>
                <link-form-item label="生日" :required="item.required === 'Y'" :field="item.field"
                                v-if="item.field === 'birth'" :readonly="readonlyFlag || item['readonly']"
                                :note="item.note">
                    <link-date v-model="formData.birth"
                               :disabled="formOverallReadonly"
                               display-format="YYYY-MM-DD" value-format="YYYY-MM-DD"/>
                </link-form-item>
                <link-form-item label="生日类型" :required="item.required === 'Y'" :field="item.field"
                                v-if="item.field === 'birthType'"
                                :readonly="(formData.row_status!=='NEW' && readonlyFlag )|| item['readonly']"
                                :note="item.note">
                    <link-lov type="BIRTHDAY_TYPE" v-model="formData.birthType"
                              :disabled="formOverallReadonly"></link-lov>
                </link-form-item>
                <link-form-item label="是否重点跟进" :required="item.required === 'Y'" :field="item.field"
                                v-if="item.field === 'impFlag'" :readonly="item['readonly']">
                    <link-switch v-model="formData.impFlag"
                                 :disabled="formOverallReadonly || ((formData.empFlag === 'Y' || formData.terminalFlag === 'Y') && formData.impFlag === 'N')"
                                 @change="showTips" :note="item.note"/>
                </link-form-item>
                <link-form-item label="是否终端标识" :required="item.required === 'Y'" :field="item.field"
                                v-if="item.field === 'terminalFlag'"
                                :readonly="formData.inTerminal === 'Y' || item['readonly']" :note="item.note">
                    <link-switch v-model="formData.terminalFlag" :disabled="formOverallReadonly"/>
                </link-form-item>
                <link-form-item label="所属行业" arrow :required="item.required === 'Y'" :field="item.field"
                                v-if="item.field === 'subordinateIndustry'" :readonly="item['readonly']"
                                :note="item.note">
                    <link-lov type="INDUSTRY" v-model="formData.subordinateIndustry"
                              :disabled="formOverallReadonly"></link-lov>
                </link-form-item>
                <link-form-item label="单位性质" arrow :required="item.required === 'Y'" :field="item.field"
                                v-if="item.field === 'companyProperty'" :readonly="item['readonly']" :note="item.note">
                    <link-lov type="COMPANY_TYPE" v-model="formData.companyProperty"
                              :disabled="formOverallReadonly"></link-lov>
                </link-form-item>
                <link-form-item label="备注" :required="item.required === 'Y'" :field="item.field"
                                v-if="item.field === 'comments'" :readonly="item['readonly']" :note="item.note">
                    <link-input v-model="formData.comments" :disabled="formOverallReadonly"></link-input>
                </link-form-item>
                <link-form-item label="所属品牌" :required="item.required === 'Y'" :field="item.field"
                                v-if="item.field === 'belongToBrand'" :readonly="item['readonly']" :note="item.note">
                    <link-select v-model="formData.belongToBrand" multiple>
                        <link-select-option v-for="(data,index) in brandList" :label="data.name" :val="data.val" :key="index" :disabled="formOverallReadonly"/>
                    </link-select>
                </link-form-item>
                <link-form-item label="是否宴席合伙人" arrow :required="item.required === 'Y'" :field="item.field"
                                v-if="item.field === 'attr06'" :readonly="item['readonly']" :note="item.note">
                    <link-lov type="YES_OR_NO" v-model="formData.attr06"
                              :disabled="formOverallReadonly"></link-lov>
                </link-form-item>
                <link-form-item label="是否1客户" arrow :required="item.required === 'Y'" :field="item.field"
                                v-if="item.field === 'attr07'" :readonly="item['readonly']" :note="item.note">
                    <link-lov type="YES_OR_NO" v-model="formData.attr07"
                              :disabled="formOverallReadonly"></link-lov>
                </link-form-item>
                <link-form-item label="跟进人" :required="item.required === 'Y'" :field="item.field" v-if="item.field === 'fstName'" :readonly="item['readonly']" :note="item.note">
                    <link-input readonly v-model="formData.fstName" :disabled="formOverallReadonly"></link-input>
                </link-form-item>
                <link-form-item label="身份分" :required="item.required === 'Y'" :field="item.field"
                                v-if="item.field === 'identityScore'" :readonly="true" :note="item.note" :arrow="false">
                    {{ formData.identityScore }}
                </link-form-item>
            </view>
            <!--未生效消费者列表点击过来，需要满足审批拒绝或新建才可以点击提交申请-->
            <link-sticky v-if="pageFrom === 'IneffectiveAccount' && (appStatus === 'Reject' || appStatus === 'New')">
                <link-button block @tap="cancelAndBack">取消</link-button>
                <link-button block @tap="submitApproval">提交</link-button>
            </link-sticky>
            <!-- 鼎昊公司不能保存新建 -->
            <link-sticky v-else-if="pageFrom !== 'IneffectiveAccount'&& userInfo.coreOrganizationTile.brandCompanyCode !== '1210'">
                <link-button block @tap="saveAccount">保存</link-button>
                <link-button v-if="pageFrom !== 'ListApply' && pageFrom !== 'ListReport'" block
                             @tap="saveAccount('New')">保存并新建
                </link-button>
            </link-sticky>
        </link-form>
    </link-page>
</template>

<script>
import {ROW_STATUS} from "../../../../../utils/constant";
import {PreloadImg} from "../../../../../utils/service/PreloadImg";
import {LovService} from "link-taro-component";
import LineTitle from "../../../../lzlj/components/line-title";
import TitleLine from "../../../../lzlj/components/title-line";

export default {
    name: 'EssentialInfo',
    components: {TitleLine, LineTitle},
    props: {
        isEditFlag: {      // 判断是否在当前登录用户职位是否在ASSIGN_POSITION范围内
            type: Boolean,
            default: false
        },
        fromCache: {       // 缓存是否有信息
            type: String,
            default: ''
        },
        appStatus: {       // 未生效消费者审批状态
            type: String,
            default: ''
        },
        pageFrom: {        // 页面来源
            type: String,
            default: ''
        },
        cacheData: {       // 当小程序突然中断，可以获取到缓存信息继续进行编辑
            type: Object,
            default: {}
        },
        readonlyFlag: {     // 是否只读标识
            type: Boolean,
            default: false
        },
        standarFields: { // 消费者标准字段
            type: Array,
            default: []
        },
        formOverallReadonly: { // 页面是否只读
            type: Boolean,
            default: false
        },
        essentialData: { // 消费者基础字段对象
            type: Object,
            default: {}
        },
        formRules: {    // 表单校验规则
            type: Object,
            default: {}
        },
        kvALLFields: {
            type: Array,
            default: []
        },
        newKFields: {
            type: Array,
            default: []
        },
        allConTypeList: {
            type: Object,
            default: {}
        },
        basicAddressFlag: {
            type: Boolean,
            default: false
        },
        invaliLocFlag: {
            type: Boolean,
            default: false
        },
        invalidateLocString:{
            type: String,
            default: ''
        }
    },
    data() {
        const userInfo = this.$taro.getStorageSync('token').result;
        return {
            brandList: [],                         // 品牌列表
            nameReg: '',                           // 姓名正则表达式
            idCardReg: '',                         // 身份证号正则表达式
            phoneReg: '',                          // 手机号正则表达式
            companyName: '',                       // 公司名称
            mobilePhone: '',                       // 消费者手机号
            formData: {},                          // 消费者基础信息字段
            copyContent: '',                       // 页面顶部复制内容
            mainStoreId: '',                       // 是否主要所属终端id,用于消费者新建的时候，主要所属门店逻辑判断
            mainRecommend: '',                     // 是否主要推荐人ID,用于消费者新建时，主要推荐人逻辑判断
            recommendAccountList: this.cacheData.recommendAccountList,      // 内部推荐人列表
            terminalList: this.cacheData.terminalList,                      // 所属门店
            corporateName: '',                     // 公司名称
            userInfo,                              // 用户信息
            postnList: [],                         // 职位数组
            disableCompanyFlag: true,             // 公司名称是否禁用
            transferPostnFlag: false,              // 转交职位开关，只有鼎昊，新建时才有
            entOption: new this.AutoList(this, {   // 选择企业信息
                module: this.$env.appURL + '/action/link/entDatabase',
                param: {
                    oauth: 'ALL',
                    filtersRaw: [{id: 'dataStatus', property: 'dataStatus', value: 'Valid', operator: '='}]
                },
                searchFields: ['enterpriseName'],
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} title={data.enterpriseName} data={data} desc={data.creditNo}
                                  content={data.operName} note={data.address} arrow={false}>
                    </item>)
                },
                slots: {
                    top: (h) => (
                        <view style="height: 40px; color: #3e68ef; text-align: center; border: 1px solid #3e68ef; display: flex; justify-content: center; align-items: center; margin: 10px; border-radius: 10px;"
                             onTap={this.noCompany}
                        >
                            暂无相关企业
                        </view>
                    ),
                    other: (h) => (<link-fab-button onTap={this.createCompany} icon="mp-plus"/>)

                }
            }),
            recommendAccntOption: new this.AutoList(this, {  // 选择推荐人列表
                url: {queryByExamplePage: this.$env.appURL + '/action/link/user/queryByExamplePage'},
                searchFields: ['firstName', 'contactPhone'],
                param: {
                    rows: 25,
                    filtersRaw: [{id: 'empType', property: 'empType', value: 'VendorEmployee', operator: '='}],
                },
                renderFunc: (h, {data, index}) => {
                    return (<item key={index} title={data.firstName} data={data} note={data.postnName}
                                  desc={data.contactPhone}></item>)
                }
            }),
            finalOption: new this.AutoList(this, {      // 选择考核终端列表
                url: {queryByExamplePage: this.$env.appURL + '/action/link/mvg/queryLeftListPage'},
                searchFields: ['acctName'],
                param: {
                    mvgMapperName: 'consumerAccnt',
                    attr3: '',
                    attr4: '',                          // 公司ID
                    attr5: '',
                    rows: 25,
                    mvgParentId: '',
                    filtersRaw: []
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} className="select-box" arrow="false">
                            <link-checkbox val={data.id} toggleOnClickItem slot="thumb"></link-checkbox>
                            <view slot="title">{data.acctName}</view>
                                <view slot="note">{data.province}{data.city}{data.district}{data.address}</view>
                        </item>)
                },
                hooks: {
                    beforeLoad(option) {
                        if (this.formData.sourceFrom === 'DealerOut') {
                            option.param.attr3 = 'Dealer'
                            option.param.filtersRaw = [
                                ...option.param.filtersRaw,
                                {
                                    id: 'companyId',
                                    property: 'companyId',
                                    value: this.userInfo.coreOrganizationTile['l3Id']
                                }
                            ]
                        } else if (this.formData.sourceFrom === 'DistributorOut') {
                            option.param.filtersRaw = [
                                ...option.param.filtersRaw,
                                {"id": "attr1", "property": "attr1", "value": "WeChatWork"},
                                {"id": "attr2", "property": "attr2", "value": "Y"},
                                {
                                    id: 'companyId',
                                    property: 'companyId',
                                    value: this.userInfo.coreOrganizationTile['l3Id']
                                }
                            ]
                            option.param.attr3 = 'Distributor'
                        } else {
                            option.param.filtersRaw = [
                                ...option.param.filtersRaw,
                                {"id": "attr1", "property": "attr1", "value": "WeChatWork"},
                                {"id": "attr2", "property": "attr2", "value": "Y"},
                                {
                                    id: 'companyId',
                                    property: 'companyId',
                                    value: this.userInfo.coreOrganizationTile['l3Id']
                                }
                            ]
                            option.param.attr3 = 'Terminal'
                        }
                        option.param.attr5 = this.userInfo.id;
                        option.param.attr4 = this.userInfo.coreOrganizationTile['l3Id'];
                    }
                }
            }),
            belongToStoreOption: new this.AutoList(this, {      // 选择考核终端列表
                url: {queryByExamplePage: this.$env.appURL + '/action/link/mvg/queryLeftListPage'},
                searchFields: ['acctName'],
                param: {
                    oauth: 'ALL',
                    mvgParentId: '',                    // 消费者ID
                    mvgMapperName: 'consumerAccnt',
                    attr4: '',                          // 公司ID
                    rows: 25,
                    attr5: '',
                    filtersRaw: [
                        {id: 'attr1', property: 'attr1', value: 'WeChatWork'},
                        {id: 'attr2', property: 'attr2', value: 'Y'}
                    ]
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} className="select-box" arrow="false">
                            <link-checkbox val={data.id} toggleOnClickItem slot="thumb"></link-checkbox>
                            <view slot="title">{data.acctName}</view>
                            <view slot="note">
                                <view>{data.acctLevelName || '-'}</view>
                                <view>{data.province}{data.city}{data.county}{data.address}</view>
                            </view>
                        </item>)
                },
                hooks: {
                    beforeLoad(option) {
                        option.param.filtersRaw = [
                            ...option.param.filtersRaw,
                            {id: 'companyId', property: 'companyId', value: this.userInfo.coreOrganizationTile['l3Id']}
                        ]
                        option.param.attr4 = this.userInfo.coreOrganizationTile['l3Id'];
                        option.param.attr5 = this.userInfo.id;
                    },
                    afterLoad(data) {
                        data.rows.forEach((item) => {
                            const find = this.acctLevelArrayList.find((temp) => temp.val === item.acctLevel);
                            item.acctLevelName = find ? find.name : '';
                        })
                    }
                }
            }),
            recommendCustomerListOption: new this.AutoList(this, {      // 选择推荐消费者列表
                module: this.$env.appURL + '/action/link/consumer',
                url: {
                    queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/consumerListSend'
                },
                filterOption: [
                    {label: '客户性别', field: 'gender', type: 'lov', lov: 'GENDER'}
                ],
                exactSearchFields: [
                    {
                        field: 'acctName',
                        showValue: '姓名',
                        searchOnChange: true,
                        clearOnChange: true,
                        exactSearch: true
                    }, {
                        field: 'mobilePhone1',
                        showValue: '手机号',
                        searchOnChange: true,
                        clearOnChange: true,
                        exactSearch: true
                    }
                ],
                param: {
                    rows: 25,
                    oauth: 'MY_POSTN_ONLY',
                    filtersRaw: [
                        {
                            id: 'companyId',
                            property: 'companyId',
                            value: userInfo.coreOrganizationTile['l3Id'],
                            operator: '='
                        },
                        {id: 'consumerType', property: 'consumerType', value: 'ChannelConsumer', operator: '='},
                        {id: 'accntChannel', property: 'accntChannel', value: 'MarketingPlatform', operator: '='},
                        {id: 'empFlag', property: 'empFlag', value: 'N', operator: '='},
                        {id: 'followFlag', property: 'followFlag', value: 'Y', operator: '='}
                    ]
                },
                hooks: {
                    beforeLoad(option) {
                        for (let i = 0; i < option.param.filtersRaw.length; i++) {
                            if (option.param.filtersRaw[i].property === 'acctName') {
                                option.param.filtersRaw[i].operator = 'like';
                            }
                        }
                    }
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} arrow="false">
                            <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                            <view
                                style="-webkit-box-sizing: border-box;box-sizing: border-box;display: flex;width: 100%;-webkit-flex-direction: row;-ms-flex-direction: row;flex-direction: row;align-items: center;">
                                <image style=" height: 47px;width: 47px;margin-right: 10px;"
                                       src={PreloadImg.headImgAccount(data)}/>
                                <view
                                    style="width: 70%;display: flex;flex: 1;flex-direction: column;justify-content: space-between;align-items: flex-start;overflow: hidden;">
                                    <view
                                        style="width: 100%;line-height: 18px;font-size: 15px;color: #262626;">{data.acctName}</view>
                                    <view
                                        style="width: 100%;line-height: 32px;font-size: 14px;display: flex;color: #333333;margin: 5px;">
                                        <view
                                            style="width: 72px;font-size: 12px;font-weight: 400;color: #41A2F6;line-height: 18px;background: #E9F5FF;border-radius: 2px;opacity: 1;text-align: center;">{LovService.filter(data.loyaltyLevel, 'ACCT_MEMBER_LEVEL')}</view>
                                        {data.impFlag === 'Y' ? <view
                                            style="margin-left: 8px;width: 56px;font-size: 12px;font-weight: 400;color: #FF7553;line-height: 18px;background: #FEEDEB;border-radius: 2px;text-align: center;opacity: 1;">重点客户</view> : ''}
                                    </view>
                                    <view
                                        style="width: 100%;line-height: 32px;font-size: 14px;display: flex;color: #333333;">
                                        <view style="color: #999999;margin-right: 12px;">负责人</view>
                                        {data.fstName}</view>
                                    <view
                                        style="width: 100%;line-height: 32px;font-size: 14px;display: flex;color: #333333;">
                                        <view style="color: #999999;margin-right: 12px;">手机号</view>
                                        {data.mobilePhone1}</view>
                                </view>
                                <view
                                    style="display: flex;flex-direction: column;justify-content: space-between; height:100px;">
                                    <view
                                        style="background: #2F69F8;box-shadow: 0 1.5px 2px 0 rgba(47, 105, 248, 0.35);min-width: 30px;color: white;letter-spacing: 0;text-align: right;text-decoration: none;height: 9px;transform: skew(-30deg, 0);display: flex;justify-content: center;align-items: center;border-radius: 3px;padding: 5px 8px;margin-right: 5px;">
                                        <view
                                            style="font-size: 10px; transform: skew(30deg, 0);">{LovService.filter(data.subAcctType, 'ACCT_SUB_TYPE')}</view>
                                    </view>
                                </view>
                            </view>
                        </item>)
                }
            }),
            saving: false,                          // 正在保存消费者数据
            acctLevelArrayList: [],
            cfgArray: [], // 企业参数配置信息-新K涉及公司范围
        }
    },
    computed: {
        formRulesData() {
            return this.formRules;
        },
        kvAllData() {
            let kvData = {};
            this.kvALLFields.forEach((item) => {
                kvData[item.field + 'List'] = item[item.field + 'List']
            })
            return kvData;
        },
        newKAllData() {
            let newKData = {};
            this.newKFields.forEach((item) => {
                newKData[item.field + 'List'] = item[item.field + 'List'];
            })
            return newKData;
        },
        standarFieldsData() {
            if (!this.formOverallReadonly && (this.pageFrom === 'IneffectiveAccount' || this.formData.row_status === ROW_STATUS.UPDATE)) {
                console.log("这是什么条件???", this.formOverallReadonly, this.pageFrom, this.formData, this.essentialData, ROW_STATUS.UPDATE);
                for (let i = 0; i < this.kvALLFields.length; i++) {
                    if (this.kvALLFields[i].showFlag && this.kvALLFields[i][this.kvALLFields[i].field + 'List'].length > 0) {
                        let data = this.kvALLFields[i][this.kvALLFields[i].field + 'List'].filter((item) => item[this.kvALLFields[i].field] === this.formData[this.kvALLFields[i].field]);
                        this.chooseData(data[0], i + 1, 'kvALLFields', 'edit');
                    }
                }
                for (let j = 0; j < this.newKFields.length; j++) {
                    if (this.newKFields[j].showFlag && this.newKFields[j][this.newKFields[j].field + 'List'].length > 0) {
                        let data = this.newKFields[j][this.newKFields[j].field + 'List'].filter((item) => item[this.newKFields[j].field] === this.formData[this.newKFields[j].field]);
                        this.chooseData(data[0], j + 1, 'newKFields', 'edit');
                    }
                }
            }
            this.standarFields.forEach((item) => {
                for (let i = 0; i < this.kvALLFields.length; i++) {
                    if (item.field === this.kvALLFields[i].field) {
                        item.showFlag = this.kvALLFields[i].showFlag;
                        item.type = this.kvALLFields[i].type;
                    }
                }
                for (let j = 0; j < this.newKFields.length; j++) {
                    if (item.field === this.newKFields[j].field) {
                        item.showFlag = this.newKFields[j].showFlag;
                        item.type = this.newKFields[j].type;
                    }
                }
            });
            // console.log("standarFields-111", this.standarFields);
            return this.standarFields;
        }
    },
    async created() {
        this.acctLevelArrayList = await this.$lov.getLovByType('ACCT_LEVEL');
        this.brandList = await this.$lov.getLovByParentTypeAndValue({type: 'BRAND', parentType: 'ACTIVITY_COMPANY', parentVal: this.userInfo.coreOrganizationTile['l3Id']});
        // 查询企业参数配置-参数键New_Type_Company
        const obj = await this.$utils.getCfgProperty('New_Type_Company');
        this.cfgArray = obj.split(',')
        console.log('essential-cfgArray', this.cfgArray, this.userInfo.coreOrganizationTile['l3Id']);
    },
    methods: {
        // 设置默认值
        setDefaultValStandrfieldsData(){
        //  if(this.standarFieldsData.find(e=>e.field=='attr08')&&!this.formData.attr08){
            !this.formData.attr08&&this.$set(this.formData, 'attr08', 'N');
        //  }
        },
        /**
         * @desc 选择暂无企业信息
         * <AUTHOR>
         * @date 2024-04-11
         **/
        noCompany() {
            this.$set(this.formData, 'corporateId', '');
            this.$set(this.formData, 'companyName', '');
            this.disableCompanyFlag = false;
            this.$nav.back();
        },
        /**
         * @desc 新建企业信息-跳转到企微企业信息页面
         * <AUTHOR>
         * @date 2024-04-11
         **/
        createCompany() {
            this.$nav.push('pages/lzlj/corporate-relation/information/information-item-edit-page', {
                pageFrom: 'fromConsumer',
                callback: () => {
                    this.entOption.methods.reload();
                }
            });
        },
        /**
         * @desc 监听客户来源字段
         * <AUTHOR>
         * @date 2023/5/31 16:50
         **/
        changeSourceFrom() {
            this.$set(this.formData, 'recommenderAcctId', '');
            this.$set(this.formData, 'recommenderAcctName', '');
        },
        /**
         * @desc 选择推荐人数据
         * <AUTHOR>
         * @date 2023/5/31 16:36
         **/
        beforeSelectRecommend() {
            this.$set(this.formData, 'recommenderAcctId', '');
            this.$set(this.formData, 'recommenderAcctName', '');
        },
        /**
         * @desc 选择推荐消费者数据
         * <AUTHOR>
         * @date 2023/6/6 18:02
         **/
        beforeSelectCustom() {
            this.$set(this.formData, 'recommenderId', '');
            this.$set(this.formData, 'recommender', '');
        },
        /**
         * @desc 选择数据
         * <AUTHOR>
         * @date 2023/5/8 22:36
         * @param  field 选择的字段
         * @param key 对应的下一层级key
         * @param type 值列表类型
         * @param readonly 只读不可点击
         **/
        async selectData(field, key, type, readonly) {
            if (readonly) return;
            if (this.formOverallReadonly) return;
            if(!this.cfgArray.includes(this.userInfo.coreOrganizationTile['l3Id'])) {
                const data = await this.$select(this.kvAllData[field + 'List'], {
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data}>
                                <view slot="title">{LovService.filter(data[field], type)}</view>
                            </item>
                        )
                    }
                })
                if (data) {
                    let tempAllField = this.kvALLFields;
                    tempAllField.forEach((item, index) => {
                        if (index >= key) {
                            item.showFlag = false;
                            item[item.field + 'List'] = [];
                            this.formData[item.field] = null;
                        }
                    });
                    this.formData['type'] = null;
                    this.formData['typeId'] = null;
                    this.$emit('update:kvALLFields', tempAllField);
                }
                this.formData[field] = data[field];
                if (data.itemList && data.itemList.length === 1 && data.itemList[0]['type']) {
                    this.formData.type = data.itemList[0]['type'];
                    this.formData.typeId = data.itemList[0].id;
                    return;
                }
                console.log(data, key, 'list', this.kvALLFields)
                this.chooseData(data, key);
            } else {
                // 弹出选择框
                const data = await this.$select(this.newKAllData[field + 'List'], {
                    renderFunc: (h, { data, index }) => (
                        <item key={index} data={data}>
                            <view slot="title">{LovService.filter(data[field], type)}</view>
                        </item>
                    )
                });

                if (!data) return;
                console.log("selectData-row-111", data);

                // 清空当前层及其后续层的数据，并重置对应的表单值
                let fields = this.newKFields;
                fields.forEach((item, index) => {
                    if (index >= key) {
                        item.showFlag = false;
                        item[item.field + "List"] = [];
                        this.$set(this.formData, item.field, null);
                    }
                });
                // 重置表单中的类型及身份分相关字段
                this.$set(this.formData, 'type', null);
                this.$set(this.formData, 'typeId', null);
                this.$set(this.formData, 'identityScore', 0.00);
                this.$emit('update:newKFields', fields);


                // 赋值当前选中项
                this.$set(this.formData, field, data[field]);

                const currentField = fields.find(f => f.field === field);
                console.log('currentField', currentField);
                if(currentField) {
                    currentField.showFlag = true;
                    this.$emit('update:newKFields', fields);
                }

                const firstItem = data.itemList[0];
                console.log("selectData-赋值", this.formData, firstItem);

                if (firstItem.identityScore) {
                    const identityScore = parseFloat(firstItem.identityScore).toFixed(2);
                    console.log("selectData-identityScore", identityScore);
                    this.$set(this.formData, 'identityScore', identityScore);
                    this.$set(this.formData, 'typeId', firstItem.id);
                    this.$set(this.formData, 'type', null);
                }

                // 若未到最后一层，则继续处理后续层级
                this.chooseData(data, key);
            }
        },
        /**
         * @desc 选择数据
         * <AUTHOR>
         * @date 2023/5/8 16:43
         * @param data 选择对应数据
         * @param key 对应的下一层级key
         * @param type 操作类型
         **/
        chooseData(data, key, type) {
            let tempData = data;
            if(!this.cfgArray.includes(this.userInfo.coreOrganizationTile['l3Id'])) {
                let tempKeyData = this.kvALLFields.slice(key, this.kvALLFields.length);
                for (let i = 0; i < tempKeyData.length; i++) {
                    let tempKey = Number(i) + Number(key);
                    if (tempKey < this.kvALLFields.length) {
                        tempData = this.dealData(tempData, tempKey, type, key);
                    }
                }
                let filterData = tempKeyData.filter((item) => !!item.showFlag);
                if (filterData.length < 1) {
                    if (tempData && tempData.itemList && tempData.itemList.length === 1 && tempData.itemList[0]['type']) {
                        this.formData.type = tempData.itemList[0]['type'];
                        this.formData.typeId = tempData.itemList[0].id;
                    }
                }
            } else {
                let tempKeyData = this.newKFields.slice(key, this.newKFields.length);
                console.log(tempKeyData, 'newKFields?!?');
                for (let i = 0; i < tempKeyData.length; i++) {
                    let tempKey = Number(i) + Number(key);
                    console.log(tempKey, 'tempKey');
                    if (tempKey < this.newKFields.length) {
                        tempData = this.dealData(tempData, tempKey, type, key);
                        console.log(tempData, 'tempData');
                    }
                }
                let filterData = tempKeyData.filter((item) => !!item.showFlag);
                if (filterData.length < 1) {
                    if (tempData && tempData.itemList && tempData.itemList.length === 1 && tempData.itemList[0]['identityScore']) {
                        this.formData.identityScore = parseFloat(tempData.itemList[0]['identityScore']).toFixed(2);
                        this.formData.typeId = tempData.itemList[0].id;
                        this.formData.type = null;
                    }
                }
            }
        },
        /**
         * @desc 处理数据
         * <AUTHOR>
         * @date 2023/5/8 17:48
         * @param data 选择对应数据
         * @param index 对应的下一层级key
         * @param key 上一层key
         * @param type 操作类型
         **/
        dealData(data, key, type) {
            if(!this.cfgArray.includes(this.userInfo.coreOrganizationTile['l3Id'])) {
                let tempAllField = this.kvALLFields;
                if (data && data.itemList && data.itemList.length > 0) {
                    let tempItemList = data.itemList;
                    if ((tempItemList.length === 1 && tempItemList[0][tempAllField[key].field] === '空值') || (type === 'edit' && tempItemList[0][tempAllField[key].field] === this.formData[tempAllField[key].field])) {
                        tempAllField[key].showFlag = type === 'edit' && tempItemList[0][tempAllField[key].field] === this.formData[tempAllField[key].field];
                        this.$emit('update:kvALLFields', tempAllField);
                        if (tempItemList[0]['itemList'] && tempItemList[0]['itemList'].length > 0) {
                            return tempItemList[0];
                        }
                    } else if (tempItemList.length > 1) {
                        tempAllField[key].showFlag = true;
                        tempAllField[key][tempAllField[key].field + 'List'] = tempItemList;
                        this.$emit('update:kvALLFields', tempAllField);
                    } else if (tempItemList.length === 1 && tempItemList[0][tempAllField[key].field] !== '空值') {
                        tempAllField[key].showFlag = true;
                        tempAllField[key][tempAllField[key].field + 'List'] = tempItemList;
                        this.$emit('update:kvALLFields', tempAllField);
                        if (tempItemList[0]['itemList'] && tempItemList[0]['itemList'].length > 0) {
                            return tempItemList[0];
                        }
                    }
                }
            } else {
                let tempAllField = this.newKFields;
                if (data && data.itemList && data.itemList.length > 0) {
                    let tempItemList = data.itemList;
                    // let tempItemList = data.itemList.filter((item) => item[tempAllField[key].field] !== '空值');
                    if ((tempItemList.length === 1 && tempItemList[0][tempAllField[key].field] === '空值') || (type === 'edit' && tempItemList[0][tempAllField[key].field] === this.formData[tempAllField[key].field])) {
                        tempAllField[key].showFlag = type === 'edit' && tempItemList[0][tempAllField[key].field] === this.formData[tempAllField[key].field];
                        console.log(tempAllField, 'tempAllField');
                        this.$emit('update:newKFields', tempAllField);
                        if (tempItemList[0]['itemList'] && tempItemList[0]['itemList'].length > 0) {
                            return tempItemList[0];
                        }
                    } else if (tempItemList.length > 1) {
                        tempAllField[key].showFlag = true;
                        tempAllField[key][tempAllField[key].field + 'List'] = tempItemList.filter(item => item[tempAllField[key].field] !== '空值');
                        this.$emit('update:newKFields', tempAllField);
                    } else if (tempItemList.length === 1 && tempItemList[0][tempAllField[key].field] !== '空值') {
                        tempAllField[key].showFlag = true;
                        tempAllField[key][tempAllField[key].field + 'List'] = tempItemList.filter(item => item[tempAllField[key].field] !== '空值');
                        this.$emit('update:newKFields', tempAllField);
                        if (tempItemList[0]['itemList'] && tempItemList[0]['itemList'].length > 0) {
                            return tempItemList[0];
                        }
                    }
                }
            }
        },
        /**
         * @desc 查询手机号正则表达式
         * <AUTHOR>
         * @date 2022/6/7 17:21
         **/
        async queryCfg() {
            const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfgList', [{key: 'phone_code_regexp'}, {key: 'id_card_code_regexp'}, {key: 'name_code_regexp'}]);
            if (data.success) {
                data.rows.forEach((item) => {
                    if (item.key === 'phone_code_regexp') {
                        this.phoneReg = new RegExp(item.value);
                    }
                    if (item.key === 'id_card_code_regexp') {
                        this.idCardReg = new RegExp(item.value);
                    }
                    if (item.key === 'name_code_regexp') {
                        this.nameReg = new RegExp(item.value);
                    }
                })
                this.$emit('update:formRules', Object.assign({}, this.formRules,
                    {
                        name: this.Validator.regexp({reg: this.nameReg, msg: '"姓名"输入至少2个字且为中文 '}),
                        phoneNumber: this.Validator.regexp({reg: this.phoneReg, msg: '请输入正确的手机号'}),
                        idCardNumber: this.Validator.regexp({reg: this.idCardReg, msg: '请输入正确的身份证号'}),
                    }));
            }
        },
        /**
         * @desc 弹出提示框
         * <AUTHOR>
         * @date 2023/3/15 14:15
         **/
        showTips() {
            if (this.formData.impFlag === 'Y' && (this.formData.empFlag === 'Y' || this.formData.terminalFlag === 'Y')) {
                this.$message.warn('消费者为员工/终端人员，不可标识为重点跟进');
            }
        },
        /**
         * @desc 提交审批
         * <AUTHOR>
         * @date 2023/3/16 15:53
         **/
        async submitApproval() {
            await this.$refs.accountItemEditForm.validate();
            if (!this.checkData()) {
                return;
            }
            this.$emit('submitApproval', this.formData);
        },
        /**
         * 检验数据
         * <AUTHOR>
         * @date 2020-10-13
         */
        checkData() {
            // 若类型为空/“未分配”时则可选择此消费者且更新消费者的类型为待跟进，所属业代为当前登录人。
            if (!this.formData.type || this.formData.type === 'Unassigned') {
                this.formData.type = 'ToBeFollowed';
                this.formData.postnId = this.userInfo.postnId;
            }
            if (!this.$utils.isEmpty(this.mainStoreId)) {
                this.formData['belongToStoreId'] = this.mainStoreId;
            }
            if (this.formData.sourceFrom === 'InnerRecommend') {
                this.formData['empId'] = this.mainRecommend;
            }
            if (this.$utils.isEmpty(this.formData.birth)) {
                delete this.formData.birth;
            }
            if (this.$utils.isEmpty(this.formData.recommenderAcctId) && (this.formData.sourceFrom === 'StoreRecommend' || this.formData.sourceFrom === 'DealerOut' || this.formData.sourceFrom === 'DistributorOut')) {
                this.$message.warn('请选择推荐人（客户）');
                return false;
            }
            if (this.formData.impFlag === 'Y' && (this.formData.empFlag === 'Y' || this.formData.terminalFlag === 'Y')) {
                this.$message.warn('消费者为员工/终端人员，不可标识为重点跟进');
                return false;
            }
            // if (this.formData.sourceFrom === 'StoreRecommend' && this.terminalList.length <= 0) {
            //     this.$message.warn('请添加所属客户');
            //     return false;
            // }
            if (this.formData.sourceFrom === 'InnerRecommend' && this.recommendAccountList.length <= 0) {
                this.$message.warn('请添加推荐人（员工）');
                return false;
            }
            return true;
        },
        /**
         * @desc 保存消费者
         * <AUTHOR>
         * @date 2023/3/16 15:45
         * @param type
         **/
        async saveAccount(type) {
            if (this.$utils.isEmpty(this.formData.idCardNumber)) {
                this.formData.idCardNumber = null;
            }
            if (this.$utils.isEmpty(this.formData.impFlag)) {
                this.formData.impFlag = 'N';
            }

            console.log('保存消费者', type, this.formData);
            const tempKVData = this.kvALLFields.filter((item) => !item.showFlag);
            if (tempKVData.length > 0) {
                tempKVData.forEach((item) => {
                    if (this.formData[item.field]) {
                        delete this.formData[item.field];
                    }
                })
            }
            const tempNewKData = this.newKFields.filter((item) => !item.showFlag);
            if (tempNewKData.length > 0) {
                tempNewKData.forEach((item) => {
                    if (this.formData[item.field]) {
                        delete this.formData[item.field];
                    }
                })
            }
            await this.$refs.accountItemEditForm.validate();
            this.formData.accntSourceFrom = 'WeChatWork';
            if (!this.checkData()) {
                return;
            }
            if (type === 'New') {
                this.terminalList = [];
                this.recommendAccountList = [];
            }
            // 当客户来源为终端/分销商/经销商sourceFrom=DistributorOut/DealerOut/StoreRecommend） 企微新建消费者，保存时，若<所属客户>belongToStoreId为空，则将<推荐人（客户）>recommenderAcctId的值赋值给所属客户。
            if ((this.formData.sourceFrom === 'DistributorOut' || this.formData.sourceFrom === 'DealerOut' || this.formData.sourceFrom === 'StoreRecommend') && this.$utils.isEmpty(this.formData.belongToStoreId)) {
                this.formData.belongToStoreId = this.formData.recommenderAcctId;
                this.formData.belongToStore = this.formData.recommenderAcctName;
                let terminalList = {'consumerId': this.formData.id, 'accntId': this.formData.recommenderAcctId};
                if (this.terminalList.length < 1 && this.formData.row_status === ROW_STATUS.NEW) {
                    terminalList.mainFlag = 'Y';
                }
                await this.$http.post(this.$env.appURL + '/action/link/interCustTerminal/insert', terminalList, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$showError(`添加失败：${response.result}`);
                    }
                });
            }
            // 1秒之内只准掉一次消费者保存接口
            if (this.saving) {
                return;
            } else {
                this.saving = true;
                setTimeout(() => {
                    this.saving = false;
                }, 1000)
            }
            this.$emit('saveAccount', type, this.formData);
        },
        /**
         * @desc 取消并返回
         * <AUTHOR>
         * @date 2023/2/20 17:49
         **/
        cancelAndBack() {
            this.$nav.back();
        },
        /**
         * @desc 选择考核终端筛选逻辑
         * <AUTHOR>
         * @date 2022/11/15 17:22
         **/
        beforeSelectAssess() {
            this.finalOption.option.param.acctType = 'Terminal';
        },
        /**
         * @desc 识别文本字段
         * <AUTHOR>
         * @date 2022/7/7 10:00
         **/
        distinguish() {
            //斜杠,反斜杠,空格,乘号,英文逗号,中文逗号,中文括号,中文分号,连字符,加号,【,】,顿号,下划线,中文冒号,|,[,],&
            //还有乘号“×”，而非“xX”【和字母不同】
            //英文分号，英文冒号
            let b = this.copyContent.split(/[+|\\\\|/|,| |(|)|*|×|，|（|）|；|;|\\-|【|】|、|：|:|\\|\\[|\\]|&]+/)
            const data = b.filter(function (curV) { //filter过滤非空字符，
                return curV.trim() != '';
            });
            let addressReg = "(?<province>[^省]+省|.+自治区|[^澳门]+澳门|[^香港]+香港|[^市]+市)?(?<city>[^自治州]+自治州|[^特别行政区]+特别行政区|[^市]+市|.*?地区|.*?行政单位|.+盟|市辖区|[^县]+县)(?<county>[^县]+县|[^市]+市|[^镇]+镇|[^区]+区|[^乡]+乡|.+场|.+旗|.+海域|.+岛)?(?<detailedAddress>.*)";
            data.forEach(async (item) => {
                if (this.phoneReg.test(item)) {
                    this.$set(this.formData, 'phoneNumber', item);
                    this.mobilePhone = item;
                } else if (item === '女' || item === '男') {
                    const gender = await this.$lov.getValByTypeAndName('GENDER', item);
                    this.$set(this.formData, 'gender', gender);
                } else if (item.match(addressReg)) {
                    const addressData = item.match(addressReg);
                    this.formData = Object.assign({}, this.formData, addressData.groups);
                } else if (this.idCardReg.test(item)) {
                    this.formData.idCardNumber = item;
                } else if (this.nameReg.test(item)) {
                    this.$set(this.formData, 'name', item);
                }
            })
            if (this.formData.phoneNumber) {
                this.onKeyPhone(this.mobilePhone);
            }
        },
        /**
         *  @description: 获取定位
         *  @author: 马晓娟
         *  @date: 2020/10/20 20:05
         */
        async getLocation() {
            if (this.formOverallReadonly) {
                return
            }
            const addressInfo = await this.$locations.getAddress();
            await this.$locations.chooseLocation(addressInfo.wxMarkerData[0].latitude, addressInfo.wxMarkerData[0].longitude);
            this.$emit('updateBasicAddrFlag', !this.basicAddressFlag);
        },
        /**
         * @desc 获取生日信息
         * <AUTHOR>
         * @date 2021/6/4 10:42
         **/
        getBirthDay() {
            let birthday = ''
            if (this.formData.idCardNumber.length === 18) {
                birthday = this.formData.idCardNumber.substr(6, 8)
            } else if (this.formData.idCardNumber.length === 15) {
                birthday = "19" + this.formData.idCardNumber.substr(6, 6);
            }
            birthday = birthday.replace(/(.{4})(.{2})/, "$1-$2-");
            if (!this.$utils.isEmpty(birthday)) {
                this.formData.birth = birthday;
                this.formData.birthType = 'Yang';
            } else {
                delete this.formData.birth;
            }
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/3/17
         * @methods queryPostnList
         * @description 获取职位数组
         */
        async queryPostnList() {
            const attr2 = await this.$lov.getValByTypeAndName('ACTIVITY_COMPANY', '国窖公司');
            const lovData = await this.$lov.getLovByType('ASSIGN_POSITION');
            let str = '';
            lovData.forEach((item) => {
                str = item.val + ',' + str
            });
            str = str.substring(0, str.length - 1);
            const data = await this.$http.post(this.$env.appURL + '/link/position/queryByExamplePage', {
                pageFlag: true,
                rows: 50,
                page: 1,
                attr1: 'queryPostnUnderOrg',
                attr2: attr2,
                filtersRaw: [
                    {id: 'positionType', property: 'positionType', value: `[${str}]`, operator: 'IN'},
                    {id: 'userId', property: 'userId', value: this.userInfo.id, operator: '='},
                    {id: 'isEffective', property: 'isEffective', value: 'Y', operator: '='}
                ]
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`查询职位数据失败：${response.result}`);
                }
            });
            if (data.success) {
                this.postnList = data.rows;
                if (this.postnList.length > 0) {
                    this.formData = Object.assign(this.formData, {
                        postnName: this.postnList[0].postnName,
                        postnId: this.postnList[0].postnId
                    })
                }
            }
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/3/17
         * @methods gotoPostnList
         * @description 跳转到转交列表返回选中职位数据
         */
        async gotoPostnList() {
            const data = await this.$select(this.postnList, {
                pageTitle: '选择职位',
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} arrow="false">
                            <view slot="note">
                                <view
                                    style="display: flex;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                                    <view style="color: #8C8C8C;min-width: 50px;font-size: 12px;">职位名称:</view>
                                    <view
                                        style="font-family: PingFangSC-Regular,serif;font-size: 12px;color: #000000;letter-spacing: 0;padding-left: 8px;width: calc(100% - 50px);overflow: hidden; white-space: nowrap;text-overflow: ellipsis">{data.postnName}</view>
                                </view>
                                <view
                                    style="display: flex;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                                    <view style="color: #8C8C8C;min-width: 50px;font-size: 12px;">职位编码:</view>
                                    <view
                                        style="font-family: PingFangSC-Regular,serif;font-size: 12px;color: #000000;letter-spacing: 0;padding-left: 8px;width: calc(100% - 50px);overflow: hidden; white-space: nowrap;text-overflow: ellipsis">{data.integrationId
                                    }</view>
                                </view>
                                <view
                                    style="display: flex;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                                    <view style="color: #8C8C8C;min-width: 50px;font-size: 12px;">所属部门:</view>
                                    <view
                                        style="font-family: PingFangSC-Regular,serif;font-size: 12px;color: #000000;letter-spacing: 0;padding-left: 8px;width: calc(100% - 50px);overflow: hidden; white-space: nowrap;text-overflow: ellipsis">{data.deptName
                                    }</view>
                                </view>
                            </view>
                        </item>
                    )
                }
            })
            this.formData = Object.assign({}, this.formData, {postnId: data.postnId, postnName: data.postnName})
        },
        /**
         * @desc 设置主要推荐人
         * <AUTHOR>
         * @date 2021/9/16 14:22
         **/
        async switchChangeRecommend(item) {
            if (this.formOverallReadonly) {
                return
            }
            try {
                if (item.mainFlag === 'Y' && this.formData.row_status !== ROW_STATUS.NEW) {
                    this.$utils.showLoading();
                    const data = await this.$http.post(this.$env.appURL + '/action/link/consumer/updateTargetEmpId', {
                        id: this.formData.id,
                        empId: item.userId
                    }, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$utils.hideLoading();
                            this.$showError(`设置失败：${response.result}`);
                        }
                    });
                    if (data.success) {
                        this.$utils.hideLoading();
                        this.$message.success('设置成功！');
                        this.mainRecommend = item.userId;
                        this.recommendAccountList.forEach((recommendItem, index) => {
                            this.recommendAccountList[index].mainFlag = 'N';
                            if (recommendItem.id === item.id) {
                                this.recommendAccountList[index].mainFlag = 'Y';
                            }
                        });
                        //更新主要推荐人后需要更新消费者信息上的主要推荐人信息
                        this.$set(this.formData, 'empId', item.userId);
                        this.$set(this.formData, 'interReference', item.recommenderName);
                    }
                } else if (this.formData.row_status === ROW_STATUS.NEW) {
                    this.recommendAccountList.forEach((recommendItem, index) => {
                        this.recommendAccountList[index].mainFlag = 'N';
                        if (recommendItem.id === item.id) {
                            this.recommendAccountList[index].mainFlag = 'Y';
                            this.mainRecommend = item.userId;
                            this.formData.empId = item.userId;
                            this.formData.interReference = item.recommenderName;
                        }
                    });
                }
            } catch (e) {
                this.$utils.hideLoading();
                this.$showError('设置失败！');
            }
        },
        /**
         *  @description: 所属门店设为主要
         *  @author: 马晓娟
         *  @date: 2021/1/15 上午9:27
         */
        async switchChange(item) {
            if (this.formOverallReadonly) {
                return
            }
            try {
                if (item.mainFlag === 'Y' && this.formData.row_status !== ROW_STATUS.NEW) {
                    this.$utils.showLoading();
                    const data = await this.$http.post(this.$env.appURL + '/action/link/consumer/updateMainTerminal', {
                        id: this.formData.id,
                        belongToStoreId: item.accntId
                    }, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$utils.hideLoading();
                            this.$showError(`设置失败：${response.result}`);
                        }
                    });
                    if (data.success) {
                        this.$utils.hideLoading();
                        this.$message.success('设置成功！');
                        this.terminalList.forEach((terminalItem, index) => {
                            this.terminalList[index].mainFlag = 'N';
                            if (terminalItem.id === item.id) {
                                this.terminalList[index].mainFlag = 'Y';
                            }
                        });
                        //更新主要门店后需要更新消费者信息上的主要门店信息
                        this.$set(this.formData, 'belongToStore', item.acctName);
                        this.$set(this.formData, 'belongToStoreId', item.accntId);
                    }
                } else if (this.formData.row_status === ROW_STATUS.NEW) {
                    this.terminalList.forEach((terminalItem, index) => {
                        this.terminalList[index].mainFlag = 'N';
                        if (terminalItem.id === item.id) {
                            this.terminalList[index].mainFlag = 'Y';
                            this.mainStoreId = item.accntId;
                            this.formData.belongToStoreId = item.accntId;
                            this.formData.belongToStore = item.acctName;
                        }
                    });
                }
            } catch (e) {
                this.$utils.hideLoading();
                this.$showError('设置失败！');
            }
        },
        /**
         *  @description: 删除内部推荐人数据
         *  @author: 马晓娟
         *  @date: 2020/7/6 21:05
         */
        async handleAccountDelete(item) {
            if (this.formOverallReadonly) {
                return
            }
            if (item.mainFlag !== 'Y') {
                try {
                    const data = await this.$http.post(this.$env.appURL + '/action/link/interCustUser/deleteById', item, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$showError(`删除内部推荐人数据失败：${response.result}`);
                        }
                    });
                    if (data.success) {
                        await this.$message.success('删除成功');
                        this.initAccountList();
                    }
                } catch (e) {
                    await this.$showError('删除内部推荐人信息失败,请稍后再试');
                }
            } else {
                await this.$message.primary('设为主要内部推荐人数据不可删除');
            }
        },
        /**
         *  @description: 新增内部推荐人
         *  @author: 马晓娟
         *  @date: 2020/11/16 19:12
         */
        async addRecommend() {
            if (this.formOverallReadonly) {
                return
            }
            const list = await this.$object(this.recommendAccntOption, {
                pageTitle: "选择推荐人（员工）",
            });
            const recommendList = {'consumerId': this.formData.id, 'userId': list.id};
            if (this.recommendAccountList.length < 1 && this.formData.row_status === ROW_STATUS.NEW) {
                recommendList.mainFlag = 'Y';
            }
            const data = await this.$http.post(this.$env.appURL + '/action/link/interCustUser/insert', recommendList, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`添加失败：${response.result}`);
                }
            });
            if (data.success) {
                this.initAccountList();
                await this.$message.success('添加成功!');
            }
        },
        /**
         *  @description: 删除门店数据
         *  @author: 马晓娟
         *  @date: 2020/7/6 17:19
         */
        async handleTerminalDelete(item) {
            if (this.formOverallReadonly) {
                return
            }
            if (item.mainFlag !== 'Y') {
                try {
                    const data = await this.$http.post(this.$env.appURL + '/action/link/interCustTerminal/deleteById', item, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$showError(`删除所属门店失败：${response.result}`);
                        }
                    });
                    if (data.success) {
                        this.$message.success('删除成功');
                        this.initTerminal();
                    }
                } catch (e) {
                    await this.$showError('删除所属门店信息失败,请稍后再试');
                }
            } else {
                await this.$message.primary('设为主要门店数据不可删除');
            }

        },
        /**
         *  @description: 新增门店信息
         *  @author: 马晓娟
         *  @date: 2020/11/16 16:55
         */
        async addTerminal() {
            if (this.formOverallReadonly) {
                return
            }
            const list = await this.$object(this.belongToStoreOption, {pageTitle: "选择所属客户",});
            let terminalList = {'consumerId': this.formData.id, 'accntId': list.id};
            if (this.terminalList.length < 1 && this.formData.row_status === ROW_STATUS.NEW) {
                terminalList.mainFlag = 'Y';
                this.mainStoreId = list.id;
            }
            const data = await this.$http.post(this.$env.appURL + '/action/link/interCustTerminal/insert', terminalList, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`添加失败：${response.result}`);
                }
            });
            if (data.success) {
                this.initTerminal();
                this.$message.success('添加成功!');
            }
        },
        /**
         *  @description: 手动输入则将之前选择的企业信息置空
         *  @author: syr
         *  @date: 2020/11/11
         */
        writeCompany() {
            if (this.formData['companyName'] !== this.corporateName) {
                this.formData['corporateId'] = null;
            }
        },
        /**
         *  @description: 查询企业信息
         *  @author: syr
         *  @date: 2020/11/11
         */
        async getEntInfo() {
            if (this.formOverallReadonly) {
                return
            }
            if (!this.readonlyFlag) {
                const data = await this.$object(this.entOption, {pageTitle: '选择企业', multiple: false});
                this.$set(this.formData, 'corporateId', data.id);
                this.$set(this.formData, 'companyName', data.enterpriseName);
                this.corporateName = data.enterpriseName;
            }
        },
        /**
         * 推荐消费者
         *  <AUTHOR>
         *  @date        2020-07-14
         * */
        async chooseRecommendCustomer() {
            const item = await this.$object(this.recommendCustomerListOption, {multiple: false});
            this.$set(this.formData, 'recommenderId', item.id);
            this.$set(this.formData, 'recommender', item.acctName);
        },
        /**
         * @desc 输入完手机号后触发接口调用
         * <AUTHOR>
         * @date 2022/3/23 16:30
         * @param event input输入框输入的数据
         **/
        onKeyPhone(event) {
            if (this.formData.row_status === ROW_STATUS.NEW) {
                if (this.formData.phoneNumber && this.formData.phoneNumber.length === 11) {
                    if (this.phoneReg.test(this.formData.phoneNumber)) {
                        this.mobilePhone = event;
                        this.ineffectiveAccountValid(this.mobilePhone);
                    }
                }
            }
        },
        /**
         * @desc 根据手机号，查询是否已经有审批记录数据
         * <AUTHOR>
         * @date 2023/3/2 19:53
         * @param mobilePhone 手机号
         **/
        async ineffectiveAccountValid(mobilePhone) {
            this.$utils.showLoading();
            const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                dmpUrl: '/link/fieldTemApp/queryByExamplePage',
                oauth: 'ALL',
                filtersRaw: [
                    {id: 'applyType', property: 'applyType', value: 'NewApproval'},
                    {id: 'mobilePhone', property: 'mobilePhone', value: mobilePhone},
                    {id: 'companyId', property: 'companyId', value: this.userInfo.coreOrganizationTile['l3Id']},
                    {id: 'appStatus', property: 'appStatus', value: 'Reviewing'}
                ]
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError(`查询未生效消费者数据失败：${response.result}`);
                }
            });
            this.$utils.hideLoading();
            if (data.success && data.rows.length > 0) {
                this.$emit('showOldCustomPoster', data.rows[0], 'inValid');
            } else {
                // 查询老客户
                this.getRegularConsumer(this.formData.phoneNumber);
            }
        },
        /**
         * 根据手机号,查询老客户
         * <AUTHOR>
         * @date 2020-07-14
         * @param mobilePhone 手机号
         */
        async getRegularConsumer(mobilePhone) {
            // 根据手机号,获取客户信息
            try {
                this.$utils.showLoading()
                const data = await this.$http.post(this.$env.dmpURL + '/action/link/cdcPubConsumer/queryConsumerForAppPage', {
                    attr1: this.userInfo.postnId,
                    attr2: this.userInfo.orgId,
                    filtersRaw: [
                        {id: 'phoneNumber', property: 'phoneNumber', value: mobilePhone, operator: '='},
                        {id: 'dataSource', property: 'dataSource', value: this.formData.dataSource, operator: '='},
                        {
                            id: 'belongToCompanyId',
                            property: 'belongToCompanyId',
                            value: this.userInfo['coreOrganizationTile']['l3Id'],
                            operator: '='
                        },
                    ],
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError(`查询老客数据失败：${response.result}`);
                    }
                });
                // 如果客户信息大于等于1,则显示老客户窗口,选择老客户
                if (data.success) {
                    if (data.rows.length >= 1) {
                        this.companyName = '';
                        for (let key in data.rows[0]) {
                            if (this.$utils.isEmpty(data.rows[0][key])) {
                                data.rows[0][key] = null;
                            }
                        }
                        console.log(data.rows[0]);
                        const tempAccount = data.rows[0];
                        for (let key in tempAccount) {
                            this.$set(this.formData, key, tempAccount[key]);
                        }
                        this.$set(this.formData, 'row_status', ROW_STATUS.UPDATE);
                        this.companyName = await this.$lov.getNameByTypeAndVal('ACTIVITY_COMPANY', this.formData['belongToCompanyId']);
                        if (tempAccount['inTerminal'] === 'Y') {
                            this.formData['terminalFlag'] = 'Y'
                        }
                        this.$utils.hideLoading()
                        //消费者模块
                        if (this.pageFrom === 'Activity' || this.pageFrom === 'ListApply' || this.pageFrom === 'masterActivity' || this.pageFrom === 'visitRegisterItem' || this.pageFrom === 'BookedOrder' || this.pageFrom === 'visitApplyItem' || this.pageFrom === 'ListReport' || this.pageFrom === 'visitPresent' || this.pageFrom === 'scheduleDetail') {
                            this.$emit('update:isFromAccountList', false);
                            if (!this.isEditFlag) {
                                // 选择此客户弹窗
                                console.log('弹窗1')
                                this.$emit('showOldCustomPoster', this.formData);

                            } else if (tempAccount['followFlag'] === 'Y' && tempAccount.postnId !== this.userInfo.postnId && this.isEditFlag) {
                                // 弹出选择此客户、申请分配并选择此客户 弹窗
                                console.log('弹窗3')
                                // if(!this.formData.corporateId && !!this.formData.companyName) {
                                //     this.disableCompanyFlag = false;
                                // }
                                this.$emit('showOldCustomPoster', this.formData);
                            }
                        } else {
                            this.$emit('update:isFromAccountList', true);
                            if (!this.isEditFlag) {
                                this.$taro.showModal({
                                    title: '提示',
                                    content: '当前职位不能跟进消费者!',
                                    showCancel: false,
                                    success: async (res) => {
                                        if (res.confirm) {
                                            this.$nav.back();
                                        }
                                    }
                                });
                            } else if (tempAccount['followFlag'] === 'Y' && tempAccount.postnId !== this.userInfo.postnId && this.isEditFlag) {
                                // 弹出申请分配此客户弹窗
                                console.log('弹窗4')
                                // if(!this.formData.corporateId && !!this.formData.companyName) {
                                //     this.disableCompanyFlag = false;
                                // }
                                this.$emit('showOldCustomPoster', this.formData);
                            }
                        }
                        //鼎昊职位老客校验出来数据，前端页面做单独处理，禁用所有字段的编辑
                        this.$emit('update:formOverallReadonly', this.userInfo.coreOrganizationTile.l3Code === 'GS100318');
                        let customFieldsData = {};
                        if (this.$utils.isNotEmpty(this.formData.extendField)) {
                            customFieldsData = JSON.parse(this.formData.extendField);
                        }
                        // 分配不可编辑单位，认领可编辑
                        if(!this.formData.corporateId && !!this.formData.companyName && this.formData.followFlag === 'N') {
                            this.disableCompanyFlag = false;
                        }
                        this.$emit('initData', customFieldsData, this.formData);
                    }
                    this.$utils.hideLoading()
                }
            } catch (e) {
                this.$utils.hideLoading()
                await this.$showError(`查询老客户失败：${e}`);
            }
        },
        /**
         *  @description: 初始化门店数据
         *  @author: songyanrong
         *  @date: 2020-10-13
         */
        async initTerminal() {
            if (this.$utils.isEmpty(this.formData.id)) {
                return;
            }
            let filtersRaw = [{
                id: 'consumerId',
                property: 'consumerId',
                operator: '=',
                value: this.formData.id
            }];
            if (this.companyName === '鼎昊公司' && this.formData.row_status === ROW_STATUS.UPDATE) {
                if (this.$utils.isEmpty(this.formData.belongToStoreId) || this.$utils.isEmpty(this.formData.sourceId)) {
                    return;
                }
                filtersRaw = [{
                    id: 'accntId',
                    property: 'accntId',
                    operator: '=',
                    value: this.formData.belongToStoreId
                }, {
                    id: 'consumerId',
                    property: 'consumerId',
                    operator: '=',
                    value: this.formData.sourceId
                }];
            }
            const data = await this.$http.post(this.$env.appURL + '/action/link/interCustTerminal/queryByExamplePage', {
                order: 'asc',
                sort: 'created',
                pageFlag: true,
                rows: 100,
                page: 1,
                filtersRaw: filtersRaw
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`查询所属门店数据失败：${response.result}`);
                }
            });
            if (data.rows.length === 0) {
                this.terminalList = [];
                return
            }
            if (data.rows.length === 1) {
                data.rows[0].mainFlag = 'Y';
                this.mainStoreId = data.rows[0].accntId;
                this.formData.belongToStore = data.rows[0].acctName;
            }
            let index = data.rows.findIndex(item => item.mainFlag === 'Y'); //根据 已知mainFlag = 'Y' 获取在数组中的位置(index)；
            if (index === -1 && !this.$utils.isEmpty(this.mainStoreId) && !this.fromCache) {//无缓存情况
                index = data.rows.findIndex(item => item.accntId === this.mainStoreId);
            } else if (index === -1 && this.fromCache) {//有缓存
                let index2 = this.cacheData.terminalList.findIndex(item => item.mainFlag === 'Y');
                if (index2 !== -1) {
                    data.rows.forEach((item) => {
                        if (item.id === this.cacheData.terminalList[index2].id) {
                            this.$set(item, 'mainFlag', this.cacheData.terminalList[index2].mainFlag);
                        }
                    })
                }
            }
            if (index > -1 && data.rows[index].mainFlag === 'N') {
                data.rows[index].mainFlag = 'Y';
                this.mainStoreId = data.rows[index].accntId;
                this.formData.belongToStore = data.rows[index].acctName;
            }
            this.terminalList = data.rows;
        },
        /**
         *  @description: 初始化内部推荐人数据
         *  @author: songyanrong
         *  @date: 2020-10-13
         */
        async initAccountList() {
            if (this.$utils.isEmpty(this.formData.id)) {
                return;
            }
            let filtersRaw = [{
                id: 'consumerId',
                property: 'consumerId',
                operator: '=',
                value: this.formData.id
            }];
            if (this.formData.sourceFrom === 'InnerRecommend' && this.companyName === '鼎昊公司' && this.formData.row_status === ROW_STATUS.UPDATE) {
                if (this.$utils.isEmpty(this.formData.empId) || this.$utils.isEmpty(this.formData.sourceId)) {
                    return;
                }
                filtersRaw = [{
                    id: 'userId',
                    property: 'userId',
                    operator: '=',
                    value: this.formData.empId
                }, {
                    id: 'consumerId',
                    property: 'consumerId',
                    operator: '=',
                    value: this.formData.sourceId
                }];
            }
            const data = await this.$http.post(this.$env.appURL + '/action/link/interCustUser/queryByExamplePage', {
                filtersRaw: filtersRaw,
                pageFlag: true,
                rows: 100,
                page: 1,
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`查询内部推荐人数据失败：${response.result}`);
                }
            });
            if (data.rows.length === 0) {
                this.recommendAccountList = [];
                return
            }
            if (this.formData.row_status === ROW_STATUS.NEW && data.rows.length === 1) {
                data.rows[0].mainFlag = 'Y';
                this.mainRecommend = data.rows[0].userId;
            }
            let index = data.rows.findIndex(item => item.mainFlag === 'Y'); //根据 已知mainFlag = 'Y' 获取在数组中的位置(index)；
            if (index === -1 && !this.$utils.isEmpty(this.mainRecommend) && !this.fromCache) {//无缓存情况
                index = data.rows.findIndex(item => item.userId === this.mainRecommend);
            } else if (index === -1 && this.fromCache) {//有缓存
                let index2 = this.cacheData.recommendAccountList.findIndex(item => item.mainFlag === 'Y');
                if (index2 !== -1) {
                    data.rows.forEach((item) => {
                        if (item.id === this.cacheData.recommendAccountList[index2].id) {
                            this.$set(item, 'mainFlag', this.cacheData.recommendAccountList[index2].mainFlag);
                        }
                    })
                }
            }
            if (index > -1 && data.rows[index].mainFlag === 'N') {
                data.rows[index].mainFlag = 'Y';
                this.mainRecommend = data.rows[0].userId;
            }
            this.recommendAccountList = data.rows;
        }
    },
    watch: {
        recommendAccountList: {
            handler(newVal) {
                this.cacheData.recommendAccountList = newVal;
            },
            deep: true
        },
        terminalList: {
            handler(newVal) {
                this.cacheData.terminalList = newVal;
            },
            deep: true
        },
        essentialData: {
            async handler(newVal) {
                console.log('essentialData', newVal);
                this.formData = newVal;
                if (newVal.phoneNumber) {
                    this.mobilePhone = this.formData.phoneNumber;
                }
                if(newVal.belongToBrand && typeof this.formData.belongToBrand === 'string' && this.formData.row_status === 'UPDATE') {
                    this.formData.belongToBrand = this.formData.belongToBrand.split('/');
                }
                if(newVal.belongToBrand && typeof this.formData.belongToBrand === 'string' && this.pageFrom === 'IneffectiveAccount') {
                    this.formData.belongToBrand = this.formData.belongToBrand.split('/');
                }
                this.corporateName = this.formData.companyName || '';
                // 获取当前消费者公司名称
                if (this.formData.row_status === ROW_STATUS.NEW && this.pageFrom !== 'IneffectiveAccount') {
                    this.companyName = await this.$lov.getNameByTypeAndVal('ACTIVITY_COMPANY', this.userInfo.coreOrganizationTile['l3Id']);
                } else {
                    this.companyName = await this.$lov.getNameByTypeAndVal('ACTIVITY_COMPANY', this.formData.belongToCompanyId);
                    // 初始化内部推荐人数据
                    this.initAccountList();
                    // 初始化终端数据
                    this.initTerminal();
                }
                // 查询系统参数
                this.queryCfg();
                this.setDefaultValStandrfieldsData()
            }
        },
        'formData.corporateId': {
            handler(newVal) {
                if (!!newVal) {
                    this.disableCompanyFlag = true;
                }
            }
        }
    }
};
</script>
<style lang="scss">
.essential-info {
    .distinguish {
        padding: 24px;
        background: white;
        color: #333333;
        font-size: 28px;

        .distinguish-info {
            border: 1px solid #e5e5e5;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }

        .tips {
            margin-bottom: 10px;
        }

        .distinguish-content {
            height: 150px;
            width: 92%;
            padding: 4% 4% 2%;
            line-height: 30px;
            font-size: 26px;
        }

        textarea::placeholder {
            word-break: break-all;
            font-size: 24px;
        }

        .distinguish-button {
            width: 20%;
            display: flex;
            justify-content: right;
        }
    }

    /*deep*/
    .icon-location {
        font-size: 32px;
        color: #2F69F8;
        width: 40px;
    }

    .address-color {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        justify-content: flex-end;
        /*deep*/
        .link-input-inner {
            overflow-x: auto;
            width: 300px;
        }
    }

    /*deep*/
    .line-title {
        margin-bottom: 24px;
    }

    .ent-wrap {
        color: $main-color;
        white-space: nowrap;
        display: inline-block;
        height: 60px;
        line-height: 60px;

        .icon-sousuo {
            float: left;
            line-height: 60px;
            font-size: 24px;
            width: 10%
        }

        .choose-ent {
            float: right;
            font-size: 24px;
            width: 80%;
            margin-left: 10px;
        }
    }

    .company-row {
        .link-item-body-left {
            width: 160px;
            flex: none !important;
        }

        .link-item-body-right {
            .input {
                flex: 1 !important;
            }

            .ent-wrap {
                width: 156px;
            }
        }
    }
}
</style>
