<!--
消费者详情-生命旅程
<AUTHOR>
@date 2024-06-13
@file
-->
<template>
    <view class="life-journey">
        <multi-filter-bar :currentTabs="currentTabs" :tabs="tabs" :is-scroll="true"  @filterTab="detailTabChange" @getConsumerInfo="getConsumerInfo" @reset="reset"></multi-filter-bar>
            <scroll-view scroll-y="true" class="main-content">
                <view class="timeline">
                    <view v-for="(data, index) in journalList" :key="index">
                        <view class="year" v-if="index === 0 || data.dayTime.substring(0, 4)  !== journalList[index - 1].dayTime.substring(0, 4)">
                            {{data.dayTime.substring(0, 4)}}
                        </view>
                        <view class="month" v-if="index === 0 || data.dayTime.substring(5, 7)  !== journalList[index - 1].dayTime.substring(5, 7)">
                            {{data.dayTime.substring(5, 7)}}月
                        </view>
                        <view class="event">
                            <view class="dot-container">
                                <view class="dot"></view>
                                <!--如果是最后一条数据，不显示-->
                                <view class="line" v-if="index !== journalList.length - 1" :style="{'height': data.journeyType === 'Sales' ? 65 + (data.content.length) * 28 + 'px' : 65 + (data.content.length - 1) * 28 + 'px'}"></view>
                            </view>
                            <!-- 蓝色卡片 -->
                            <view class="content cont-blue" v-if="blueList.includes(data.journeyType)" @tap="gotoSales(data)">
                                <view class="content-top top-blue">
                                    <view class="title text-blue">{{data.journeyType | lov('JOURNEY_TYPE')}} </view>
                                    <view class="date-info text-blue">{{data.behaviorTime}}</view>
                                    <link-icon class="icon blue" :icon="iconList[data.journeyType]"></link-icon>
                                </view>
                                <view v-for="(item, index1) in data.content" :key="index1">
                                    <view class="details"><text style="color: #a2a2a2">{{item.fieldName}} :</text>   {{item.fieldValue}}</view>
                                    <view class="details" style="color: #3a64ed" v-if="data.journeyType === 'Sales'">
                                        查看全部产品
                                        <link-icon icon="icon-chakanquanbu"></link-icon>
                                    </view>
                                </view>
                            </view>
                            <!-- 粉色卡片 -->
                            <view class="content cont-pink" v-if="pinkList.includes(data.journeyType)">
                                <view class="content-top top-pink">
                                    <view class="title text-pink">{{data.journeyType | lov('JOURNEY_TYPE')}} </view>
                                    <view class="date-info text-pink">{{data.behaviorTime}}</view>
                                    <link-icon class="icon pink" :icon="iconList[data.journeyType]"></link-icon>
                                </view>
                                <view v-for="(item, index1) in data.content" :key="index1">
                                    <view class="details"><text style="color: #a2a2a2">{{item.fieldName}} :</text>  {{item.fieldValue}}</view>
                                </view>
                            </view>
                            <!-- 金色卡片 -->
                            <view class="content cont-gold" v-if="goldList.includes(data.journeyType)">
                                <view class="content-top top-gold">
                                    <view class="title text-gold">{{data.journeyType | lov('JOURNEY_TYPE')}} </view>
                                    <view class="date-info text-gold">{{data.behaviorTime}}</view>
                                    <link-icon class="icon gold" :icon="iconList[data.journeyType]"></link-icon>
                                </view>
                                <view v-for="(item, index1) in data.content" :key="index1">
                                    <view class="details"><text style="color: #a2a2a2">{{item.fieldName}} :</text>  {{item.fieldValue}}</view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <!-- 点击加载更多 -->
                <view class="load-more" v-if="$utils.isNotEmpty(journalList)" @tap="loadMore()">{{ loadFinish ? ' ———— 加载完成 ———— ' : '点击加载更多' }}</view>
                <!-- 暂无数据 -->
                <view class="no-data" v-else> ———— 暂无数据 ———— </view>
            </scroll-view>
    </view>
</template>

<script>
import {ComponentUtils} from "link-taro-component";
import MultiFilterBar from "./components/multi-filter-bar.vue";
import subTabs from "../sub-tabs/sub-tabs.vue";
export default {
    name: 'life-journey',
    components: {subTabs, MultiFilterBar},
    props: {
        accountItem: {
            type: Object,
            required: true
        }
    },
    data() {
        let iconList = {
            Created: 'icon-jiandang',           // 建档
            FollowChange: 'icon-genjin',        // 跟进状态变更
            TypeChange: 'icon-kxulie',          // K序列变更
            LoyaltyChange: 'icon-vxulie',       // V序列变更
            Activity: 'icon-huodong',          // 活动
            Sales: 'icon-chanshengdongxiao',    // 动销
            Visit: 'icon-lizengbaifang',        // 拜访
            Gifts: 'icon-lizeng',               // 礼赠
            Introduction: 'icon-zhuanjieshao',  // 转介绍
            LangChaoJiHua: 'icon-xianxiasanjielangchao', // 线下三节浪潮
            OpenBottle: 'icon-kaipingsaoma',    // 开瓶扫码
            Certification: 'icon-renzheng',     // 认证状态变更
            Equity: 'icon-quanyi',              // 权益
            PointRedemption: 'icon-jifenduihuan', // 积分兑换
            MedalCollection: 'icon-dengjibiangeng',   // 勋章领取
            OnlineInteraction: 'icon-xianshanghudong',  // 线上互动
            ThreeCourtesies: 'icon-sanjieliyu'  // 三节礼遇
        }
        return {
            currentTabs: [],    // 顶部/底部筛选条件
            tabs: [],           // 顶部tab
            journalList: [],    // 生命旅程列表
            blueList: [],       // 蓝色卡片
            goldList: [],       // 金色卡片
            pinkList: [],       // 粉色卡片
            iconList,           // 图标
            pagingPram: {       // 分页参数
                page: 1,
                rows: 20
            },
            loadFinish: false,  // 分页数据是否已经请求完
            isDebouncing: false // 跟踪是否正在防抖
        }
    },
    async created() {
        const tabsList = await this.$lov.getLovByType('JOURNEY_CHILD_TYPE')
        // 根据值列表的seq排序
        tabsList.sort((a, b) => Number(a.seq) - Number(b.seq))
        tabsList.forEach(item => {
            this.tabs.push({
                name: item.name,
                val: item.val
            })
        });
        // 建档、跟进状态变更、动销、拜访、礼赠、转介绍、认证状态变更、权益、积分兑换、勋章领取、线上互动、三节礼遇
        this.blueList = ['Created', 'FollowChange', 'Sales', 'Visit', 'Gifts', 'Introduction', 'Certification', 'Equity', 'PointRedemption', 'MedalCollection', 'OnlineInteraction', 'ThreeCourtesies'];
        // K序列变更、V序列变更
        this.pinkList = ['TypeChange', 'LoyaltyChange'];
        // 开瓶扫码、线下三节浪潮、活动
        this.goldList = ['OpenBottle', 'LangChaoJiHua', 'Activity'];
        await this.getConsumerInfo();
    },
    methods: {
        /**
         * 获取消费者-生命旅程信息
         *  <AUTHOR>
         *  @date 2024-06-20
         **/
        async getConsumerInfo(currentTabs, startTime, endTime, type) {
            try {
                console.log('参数', currentTabs, startTime, endTime)
                let params = {}
                if (currentTabs && currentTabs.length > 0) {
                    currentTabs = `[${currentTabs.join(',')}]`
                    params.filtersRaw = [
                        {id: 'journeySubType', property: 'journeySubType', value: currentTabs, operator: 'in'}
                    ]
                } else {
                    params.filtersRaw = []
                }
                if (startTime) {
                    startTime = startTime + ' 00:00:00'
                    params.filtersRaw.push({id: 'behaviorTime1', property: 'behaviorTime', value: startTime, operator: '>=', type: 'date'})
                }
                if (endTime) {
                    endTime = endTime + ' 23:59:59'
                    params.filtersRaw.push({id: 'behaviorTime2', property: 'behaviorTime', value: endTime, operator: '<=', type: 'date'})
                }
                if(type === 'top' || type === 'bottom') {
                    this.pagingPram.page = 1;
                }
                const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send", {
                    dmpSrUrl: '/link/consumerLifeJourney/query',
                    consumerId: this.accountItem.id,
                    // consumerId: '681508094128349115',       // 融合测试数据
                    // consumerId: '291515421390343402',       // 预生产测试数据
                    sort: 'behaviorTime',
                    order: 'desc',             // 降序
                    // order: 'asc',            // 升序
                    page: this.pagingPram.page,
                    ...params
                });
                if (data.success) {
                    const journalList = data.rows;
                    journalList.forEach(item => {
                        item.content = JSON.parse(item.content)
                    });
                    // 加载更多
                    if(journalList.length > 0 && this.pagingPram.page > 1) {
                        this.journalList = this.journalList.concat(journalList);
                    } else {
                        // 第一次加载/筛选条件变化
                        this.journalList = journalList;
                    }
                    console.log('生命旅程信息1', this.journalList)
                    this.loadFinish = this.pagingPram.rows * this.pagingPram.page >= data.total;
                } else {
                    this.$message.warn('当前sr数据库链接异常，请联系管理员', data.message);
                }
            } catch (e) {
                this.$message.error('当前sr数据库链接异常，请联系管理员', e);
                console.log('e', e)
            }
        },
        /**
         * 动销跳转
         *  <AUTHOR>
         *  @date 2024-06-19
         **/
        gotoSales(data) {
            console.log('data', data)
            if(data.journeyType === 'Sales') {
                // 跳转到动销详情
                this.$nav.push('/pages/lj-consumers/booked-order/booked-order-item-read-page', {
                    source: 'lifeJourney',
                    businessId: data.businessId
                    // businessId: '710914444427002267' // 测试数据
                })
            } else {
                return;
            }
        },
        /**
         * 点击/取消点击-顶部tab/底部筛选条件
         *  <AUTHOR>
         *  @date 2024-06-20
         **/
        async detailTabChange(val, startTime, endTime, type) {
            if (this.currentTabs.includes(val)) {
                this.currentTabs = this.currentTabs.filter(item => item !== val)
            } else {
                this.currentTabs.push(val)
            }
            if(type === 'top') {
                await this.getConsumerInfo(this.currentTabs, startTime, endTime, 'top');
            }
        },
        /**
         * 重置稍选条件
         *  <AUTHOR>
         *  @date 2024-06-20
         **/
        async reset() {
            this.currentTabs = [];
            this.pagingPram.page = 1;
            await this.getConsumerInfo(this.currentTabs, '', '');
        },
        /**
         * @desc 加载更多
         * <AUTHOR>
         * @date 2024-06-20
         **/
        async loadMore (type) {
            if (this.isDebouncing || this.loadFinish) return;
            this.isDebouncing = true;
            this.pagingPram.page++;
            await this.getConsumerInfo(this.currentTabs);
            setTimeout(() => {
                this.isDebouncing = false;
            }, 2000); // 2000毫秒后，允许再次触发 loadMore 函数
        }
    }
}
</script>

<style lang="scss">
.lnk-tabs-container .lnk-tabs .lnk-tabs-item .label-name-line {
    padding-right: 20px !important
}
.life-journey {
    .main-content {
        margin-top: 14px;
        background-color: #fff;
        display: flex;
        overflow: auto;
        min-height: calc(100vh - 200px);
        border-radius: 32px 32px 0 0;
        bottom: 0;
        padding: 40px 0 0 40px;
        max-width: calc(100% - 40px);

        .timeline {
            max-width: 90vw;
            position: relative;
        }

        .year {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .month {
            font-size: 26px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #888;
        }

        .event {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            position: relative;
        }

        .dot-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-right: 40px;
        }

        .dot {
            width: 14px;
            height: 14px;
            border: 5px solid #3e68ef; /* 设置边框颜色 */
            border-radius: 50%; /* 设置为圆形 */
            background-color: transparent; /* 设置背景颜色为透明 */
            margin-top: 5px;
        }

        .line {
            flex-grow: 1;
            border-left-style: dotted; /* 设置左边框样式为虚线 */
            border-color: #dce7fc; /* 设置边框颜色 */
            border-width: 6px; /* 设置边框宽度 */
            background-color: transparent; /* 设置背景颜色为透明 */
            margin-top: 16px;
            min-height: 130px;
        }

        .content {
            flex: 1;
            background-color: white;
            border-radius: 16px;
            position: relative;
            min-width: 300px; /* Set a minimum width */
        }
        .cont-blue {
            border: 4px solid #dce7fc;
        }
        .cont-pink {
            border: 4px solid #efcab4;
        }
        .cont-gold {
            border: 4px solid #dec5a3;
        }

        .content-top {
            display: flex;
            flex-direction: column;
            padding: 20px;
            border-radius: 14px 14px 0 0;
            height: 65px;
        }
        .top-blue {
            background: linear-gradient(to right bottom, #fefeff, #e0eaff);
        }
        .top-pink {
            background: linear-gradient(to right bottom, #f0dcc5, #dfb89d);
        }
        .top-gold {
            background: linear-gradient(to right bottom, #f6e6c3, #e1c695);
        }

        .title {
            font-size: 24px;
            font-weight: bold;
        }
        .date-info {
            font-size: 22px;
            margin-top: 5px;
        }
        .text-blue {
            color: #555;
        }
        .text-pink{
            color: #602D12;
        }
        .text-gold {
            color: #683D01;
        }

        .details {
            font-size: 24px;
            color: #555;
            margin-top: 5px;
            padding: 10px 16px;
            border-radius: 0 0 16px 16px;
            overflow-x: auto;
            white-space: nowrap;
        }

        .icon {
            font-size: 30px;
            color: #ffffff;
            position: absolute;
            right: 40px;
            top: 20px;
            border-radius: 50%;
            height: 60px;
            width: 60px;
            align-items: center;
            text-align: center;
            justify-content: center;
        }
        .blue {
            background: #5d8eff;
        }
        .gold {
            background: #dcb946;
        }
        .pink {
            background: #c39d88;
        }
        .load-more{
            width: 90%;
            text-align: center;
            color: #6D96FA;
            margin-top: 32px;
            font-size: 24px;
            padding-bottom: 32px;
        }
        .no-data {
            width: 90%;
            text-align: center;
            color: #cecece;
            margin-top: 32px;
            font-size: 24px;
            padding-bottom: 32px;
        }
    }
}
</style>
