<!--
@createdBy 黄鹏
@date 2024/10/12
@description: --- 消费者六到模型
-->
<template>
    <view class="six-model">
        <view v-for="(data, index) in sixModelList" :key="index" class="six-model-content">
            <view class="year" v-if="showYear(index, data)">
                {{data.dayTime.substring(0, 4)}}
            </view>
            <view class="day">
                <view class="month" v-if="showMonth(index, data)">
                    {{data.dayTime.substring(5, 7)}}月
                </view>
                <view class="dot-container">
                    <view class="line" :class="upperLineClass(index, data)"></view>
                    <view class="dot"></view>
                    <view class="line" :class="lowerLineClass(index, data)"></view>
                </view>
                <view class="day-value">{{data.behaviorTime.slice(5,10)}}</view>
                <view class="single-item" :class="data.sixToModel"
                      :style="{'background-image': 'url(' + $imageAssets[data.sixToModel] + ')'}">
                    <view class="single-item-content">
                        <view class="single-item-value">{{data.sixToModel | lov('SIX_TO_MODEL')}}</view>
                        <view class="single-item-desc">来源：{{data.sixToModel | lov('SIX_TO_MODEL_A')}}</view>
                    </view>
                    <view class="single-item-icon"
                          :style="{'background-image': 'url(' + $imageAssets[data.sixToModel+'Icon'] + ')'}"></view>
                </view>
            </view>
        </view>
        <!-- 点击加载更多 -->
        <view class="load-more" v-if="$utils.isNotEmpty(sixModelList)"
              @tap="loadMore()">{{ loadFinish ? ' ———— 加载完成————' : '点击加载更多' }}
        </view>
        <!-- 暂无数据 -->
        <view class="no-data" v-else> ———— 暂无数据 ————</view>
    </view>
</template>

<script>
    export default {
        name: "six-model",
        props: {
            accountItem: {
                type: Object,
                required: true
            }
        },
        data() {
            return {
                sixModelList: [],
                pageSize: 20,
                currentPage: 1,
                loadFinish: false,
                loading: false
            };
        },
        async created() {
            await this.getSixModelInfo();
        },
        methods: {
            /**
             * @createdBy 黄鹏
             * @date 2024/10/12
             * @methods: getSixModelInfo
             * @para:
             * @description: 获取六到模型数据
             **/
            async getSixModelInfo() {
                try {
                    const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmp/send", {
                        dmpUrl: '/link/sixToModel/queryByExamplePage',
                        consumerId: this.accountItem.id,
                        sort: 'behaviorTime',
                        order: 'desc',
                        page: this.currentPage
                    }, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$showError(`查询六到模型数据失败：${response.result}`);
                            this.loading = false;
                        }
                    });
                    if (data.success) {
                        if (this.currentPage > 1) {
                            this.sixModelList = this.sixModelList.concat(data.rows);
                        } else {
                            this.sixModelList = data.rows;
                        }
                        this.loadFinish = this.pageSize * this.currentPage >= data.total;
                        this.loading = false;
                    }
                } catch (e) {
                    this.loading = false;
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/10/12
             * @methods: loadMore
             * @para:
             * @description: 加载更多
             **/
            async loadMore() {
                if (this.loading || this.loadFinish) return;
                this.loading = true;
                this.currentPage++;
                await this.getSixModelInfo();
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/10/14
             * @methods: showYear
             * @para:
             * @description: 显示年份
             * **/
            showYear(index, data) {
                return index === 0 || data.dayTime.substring(0, 4) !== this.sixModelList[index - 1].dayTime.substring(0, 4);
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/10/14
             * @methods: showMonth
             * @para:
             * @description: 显示月份
             * **/
            showMonth(index, data) {
                if (index === 0) {
                    return true;
                } else {
                    const differentYear = data.dayTime.substring(0, 4) !== this.sixModelList[index - 1].dayTime.substring(0, 4);
                    const differentMonth = data.dayTime.substring(5, 7) !== this.sixModelList[index - 1].dayTime.substring(5, 7);
                    return differentYear || differentMonth;
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/10/14
             * @methods: upperLineClass
             * @para:
             * @description: 时间线样式
             * **/
            upperLineClass(index, data) {
                if (index === 0) {
                    return [data.sixToModel, 'hidden'];
                } else {
                    const differentYear = data.dayTime.substring(0, 4) !== this.sixModelList[index - 1].dayTime.substring(0, 4);
                    const differentMonth = data.dayTime.substring(5, 7) !== this.sixModelList[index - 1].dayTime.substring(5, 7);
                    return [data.sixToModel, differentYear || differentMonth ? 'hidden' : ''];
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/10/14
             * @methods: lowerLineClass
             * @para:
             * @description: 时间线样式
             * **/
            lowerLineClass(index, data) {
                if (index === this.sixModelList.length - 1) {
                    return [data.sixToModel, 'hidden'];
                } else {
                    const differentYear = data.dayTime.substring(0, 4) !== this.sixModelList[index + 1].dayTime.substring(0, 4);
                    const differentMonth = data.dayTime.substring(5, 7) !== this.sixModelList[index + 1].dayTime.substring(5, 7);
                    return [data.sixToModel, differentYear || differentMonth ? 'hidden' : ''];
                }
            }
        }
    };
</script>

<style lang="scss">
    .six-model {
        width: 100%;
        height: calc(100% - 120px);
        padding: 24px 0;
        background-color: #f2f2f2;
        overflow: auto;

        .six-model-content {
            padding: 0 24px;

            .year {
                height: 44px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 32px;
                color: #000000;
                line-height: 44px;
                text-align: left;
                font-style: normal;
            }

            .day {
                height: 100%;
                min-height: 144px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding-left: 20px;
                position: relative;

                .month {
                    position: absolute;
                    left: 0;
                    top: 12px;
                    height: 40px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 28px;
                    color: #333333;
                    line-height: 40px;
                    text-align: left;
                    font-style: normal;
                    padding-left: 16px;
                }

                .dot-container {
                    width: 36px;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    align-items: center;

                    .dot {
                        width: 24px;
                        height: 24px;
                        border-radius: 24px;
                        background: #F2F2F2;
                        border: 6px solid #3F66EF;
                    }

                    .line {
                        height: 70px;
                        border-left: 6px dashed #CFDAFF;

                        &.hidden {
                            visibility: hidden;
                        }

                        &.See {
                            height: 119px;
                        }

                        &.Drink {
                            height: 86px;
                        }

                        &.BecomeFan {
                            height: 86px;
                        }
                    }

                }

                .day-value {
                    height: 36px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 28px;
                    color: #999999;
                    line-height: 36px;
                    text-align: right;
                    font-style: normal;
                }

                .single-item {
                    width: 480px;
                    height: 95px;
                    padding: 24px 32px;
                    margin: 16px 0;
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    &.See {
                        height: 195px;
                    }

                    &.Drink {
                        height: 130px;
                    }

                    &.BecomeFan {
                        height: 130px;
                    }

                    .single-item-content {
                        flex: 1;

                        .single-item-value {
                            height: 56px;
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 600;
                            font-size: 32px;
                            color: #FFFFFF;
                            line-height: 56px;
                            text-align: left;
                            font-style: normal;
                        }

                        .single-item-desc {
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 400;
                            font-size: 24px;
                            color: #FFFFFF;
                            line-height: 36px;
                            text-align: left;
                            font-style: normal;
                        }
                    }

                    .single-item-icon {
                        width: 90px;
                        height: 90px;
                        margin-left: 32px;
                        background-size: 100% 100%;
                        background-repeat: no-repeat;
                    }
                }
            }
        }

        .load-more {
            width: 100%;
            text-align: center;
            color: #6D96FA;
            margin-top: 32px;
            font-size: 24px;
            padding-bottom: 32px;
        }

        .no-data {
            width: 100%;
            text-align: center;
            color: #cecece;
            margin-top: 32px;
            font-size: 24px;
            padding-bottom: 32px;
        }
    }
</style>
