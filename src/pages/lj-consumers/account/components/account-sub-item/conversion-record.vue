<!--
@createdBy 黄鹏
@date 2024/03/06
@description: --- 转换记录
-->
<template>
    <view class="conversion-record">
        <view class="sub-tab-con">
            <sub-tabs :currentTab="currentTab" :tabs="tabs" :is-scroll="true" @switchTab="detailTabChange"></sub-tabs>
        </view>
        <view class="content-con">
            <select-button :current-dimension="currentDimension" @change-btn="changeDimension"></select-button>
            <view class="board-con" v-if="currentTab === 'board'">
                <view class="tip-con">
                    <view class="tips" @tap="queryInfo">
                        <link-icon icon="mp-info-lite" status="info"/>
                        <view class="tips-content">数据指标说明</view>
                    </view>
                    <view class="deadline">截止至{{boardObj.endTime}}数据统计</view>
                </view>
                <view class="board-row">
                    <view class="board-item" v-for="(item, index) in boardItems" :key="index">
                        <view class="board-desc">{{item.name}}</view>
                        <view class="board-desc">{{boardObj[item.field] || '0'}}</view>
                        <view class="board-desc">{{item.unit}}</view>
                    </view>
                </view>
            </view>
            <view class="order-con" v-else-if="currentTab === 'order'">
                <view class="order-total" v-if="currentDimension === 'sum'">{{orderAmount}}</view>
                <view class="order-total" v-else>本财年仅查看在本财年登记过的数据，其他数据点击合计查看</view>
                <link-auto-list :option="orderOption" :key="currentTab + currentDimension">
                    <template slot-scope="{data,index}">
                        <view class="item-con" @tap="gotoOrderItem(data)">
                            <view class="time">{{data.orderDate | date('YYYY-MM-DD')}}</view>
                            <view class="item-row">
                                <view class="item-desc">客户姓名</view>
                                <view class="item-value">{{data.acctName}}</view>
                            </view>
                            <view class="item-row">
                                <view class="item-desc">手机号</view>
                                <view class="item-value">{{data.customMobilePhone}}</view>
                            </view>
                            <view class="item-row">
                                <view class="item-desc">公司</view>
                                <view class="item-value">{{data.customCompany}}</view>
                            </view>
                            <view class="item-row">
                                <view class="item-desc">订单总金额</view>
                                <view class="item-value red">{{data.orderAmount}}</view>
                            </view>
                            <view class="app-status"
                                  :style="genStyle(data.status)">
                                {{ data.status | lov('ORDER_STATUS') }}
                            </view>
                        </view>
                    </template>
                </link-auto-list>
            </view>
            <view class="common-con" v-else-if="currentTab === 'open'">
                <link-auto-list :option="openOption" :key="currentTab + currentDimension">
                    <template slot-scope="{data,index}">
                        <view class="common-item-con">
                            <view class="common-desc">
                                <view class="common-title">产品名称</view>
                                <view class="common-name"> {{ data.matName }}</view>
                            </view>
                            <view class="common-desc">
                                <view class="common-title">出库客户</view>
                                <view class="common-name"> {{ data.acctName }}</view>
                            </view>
                            <view class="common-desc">
                                <view class="common-title">开瓶时间</view>
                                <view class="common-name"> {{ data.scanTime | date('YYYY-MM-DD HH:mm:ss') }}</view>
                            </view>
                        </view>
                    </template>
                </link-auto-list>
            </view>
            <view class="common-con" v-else-if="currentTab === 'conversion'">
                <link-auto-list :option="conversionOption" :key="currentTab + currentDimension">
                    <template slot-scope="{data,index}">
                        <view class="common-item-con">
                            <view class="common-desc">
                                <view class="common-title">消费者姓名</view>
                                <view class="common-name"> {{ data.acctName }}</view>
                            </view>
                            <view class="common-desc">
                                <view class="common-title">影响力（K序列）等级</view>
                                <view class="common-name1"> {{ data.subAcctType | lov('ACCT_SUB_TYPE') }}</view>
                            </view>
                            <view class="common-desc">
                                <view class="common-title">跟进人</view>
                                <view class="common-name"> {{ data.fstName }}</view>
                            </view>
                        </view>
                    </template>
                </link-auto-list>
            </view>
        </view>
    </view>
</template>

<script>
    import subTabs from '../sub-tabs/sub-tabs';
    import selectButton from './components/select-button';

    export default {
        name: "conversion-record",
        components: {selectButton, subTabs},
        props: {
            accountItem: {
                type: Object,
                required: true
            }
        },
        data() {
            return {
                currentTab: 'board',
                tabs: [
                    {name: "转化看板", val: "board"},
                    {name: "动销订单", val: "order"},
                    {name: "开瓶信息", val: "open"},
                    {name: "转介绍", val: "conversion"}
                ],
                currentDimension: 'year',
                btns: [{name: '本财年', id: 'year'}, {name: '合计', id: 'sum'}],
                orderOption: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: this.$env.appURL + '/action/link/saleorder/queryByExamplePage'
                    },
                    param: {
                        filtersRaw: [
                            {id: 'orderType', property: 'orderType', value: 'BookedOrder', operator: '='},
                            {id: 'acctId', property: 'acctId', value: this.accountItem.id, operator: '='}
                        ]
                    },
                    sortOptions: null,
                    // loadOnStart: false,
                    hooks: {
                        beforeLoad(option) {
                            if (this.currentDimension === 'year') {
                                const arr = this.getFyFilter();
                                option.param.filtersRaw = option.param.filtersRaw.concat(arr);
                            }
                        }
                    }
                }),
                openOption: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: this.$env.appURL + '/action/link/sendDmpSr/send'
                    },
                    param: {
                        dmpSrUrl: '/link/scanCodeWinningDetail/queryByExamplePage',
                        consumerId: this.accountItem.id,
                        filtersRaw: []
                    },
                    sortField: 'scanTime',
                    sortOptions: null,
                    // loadOnStart: false,
                    hooks: {
                        beforeLoad(option) {
                            if (this.currentDimension === 'year') {
                                const arr = this.getFyFilter('scanTime');
                                option.param.filtersRaw = option.param.filtersRaw.concat(arr);
                            }
                        }
                    }
                }),
                conversionOption: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/consumerListSend'
                    },
                    param: {
                        oauth: 'MY_POSTN_ONLY',
                        filtersRaw: [
                            {id: 'consumerType', property: 'consumerType', value: 'ChannelConsumer', operator: '='},
                            {
                                id: 'xAttr58',
                                property: 'xAttr58',
                                value: this.accountItem.id,
                                operator: '='
                            },
                            {id: 'accntChannel', property: 'accntChannel', value: 'MarketingPlatform', operator: '='},
                        ]
                    },
                    sortOptions: null,
                    // loadOnStart: false,
                    hooks: {
                        beforeLoad(option) {
                            if (this.currentDimension === 'year') {
                                const arr = this.getFyFilter('created');
                                option.param.filtersRaw = option.param.filtersRaw.concat(arr);
                            }
                        }
                    }
                }),
                boardItems: [
                    {name: '动销数量', field: 'bookedPinQty', unit: '瓶'},
                    {name: '开瓶数量', field: 'openBotQty', unit: '瓶'},
                    {name: '转介绍数量', field: 'recommendCmsQty', unit: '人'}
                ],
                boardObj: {},
                orderAmount: ''
            }
        },
        created() {
            this.getBoardData();
        },
        methods: {
            /**
             * @createdBy 黄鹏
             * @date 2024/02/04
             * @methods: getBoardData
             * @para:
             * @description: 看板数据
             **/
            async getBoardData() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmpSr/send', {
                    consumerId: this.accountItem.id,
                    dataType: this.currentDimension === 'year' ? 'thisFiscalYear' : 'allTime',
                    dmpSrUrl: '/link/ConsumerReachMetrics/queryConsumerReachMetrics'
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$showError(`查询看板数据失败：${response.result}`);
                    }
                });
                this.boardObj = data.result || {};
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/02/04
             * @methods: detailTabChange
             * @para:
             * @description: 消费者详情tab切换
             **/
            detailTabChange(val, changeTab = true) {
                if (changeTab) {
                    this.currentTab = val;
                    this.currentDimension = this.btns[0].id;
                }
                if (this.$utils.isEmpty(this.accountItem.id)) {
                    return;
                }
                switch (val) {
                    case 'board':
                        this.getBoardData();
                        break;
                    case 'order':
                        this.initOrderAmount();
                        // this.orderOption.methods.reload();
                        break;
                    case 'open':
                        // this.openOption.methods.reload();
                        break;
                    case 'conversion':
                        // this.conversionOption.methods.reload();
                        break;
                    default:
                        break;
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/02/04
             * @methods: changeDimension
             * @para:
             * @description: 维度切换
             **/
            changeDimension(val) {
                this.currentDimension = val;
                this.detailTabChange(this.currentTab, false);
            },
            /**
             * @createdBy 黄鹏
             * @date 2023/03/05
             * @methods: getFyFilter
             * @para:
             * @description: 财年筛选参数
             **/
            getFyFilter(field = 'auditTime') {
                const year = new Date().getFullYear();
                let arr = [];
                if (new Date().getTime() < new Date(`${year}/11/01 00:00:00`).getTime()) {
                    arr = [
                        {id: field, property: field, operator: '>=', value: `${year - 1}-11-01 00:00:00`},
                        {id: field + '1', property: field, operator: '<=', value: `${year}-10-31 23:59:59`}
                    ]
                } else {
                    arr = [
                        {id: field, property: field, operator: '>=', value: `${year}-11-01 00:00:00`},
                        {id: field + '1', property: field, operator: '<=', value: `${year + 1}-10-31 23:59:59`}
                    ]
                }
                return arr;
            },
            /**
             @desc: 查询数据指标说明
             @author: wangbinxin
             @date 2023-10-20 10-05
             **/
            async queryInfo() {
                this.$utils.showLoading();
                const deployNo = this.currentDimension === 'year' ? 'TRANSFORM_BOARD_YEAR' : 'TRANSFORM_BOARD_ALL';
                const data = await this.$http.post(this.$env.dmpURL + '/link/reportTaskConfig/queryByExamplePage', {deployNo}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('查询失败' + response.result);
                    }
                });
                this.$utils.hideLoading();
                if (data.success) {
                    const description = data.rows[0]?.content;
                    this.$emit('show-rule', description, '数据指标说明');
                }
            },
            /**
             * @desc 初始化动销订单总金额
             * <AUTHOR>
             * @date 2022/4/14 11:00
             **/
            async initOrderAmount() {
                const data = await this.$http.post(this.$env.appURL + '/link/saleorder/sumAmountByAcctId', {
                    id: this.accountItem.id
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$showError(`查询动销订单总金额失败：${response.result}`);
                    }
                });
                if (data.success) {
                    this.orderAmount = '公司动销金额（元）：' + (data.result || 0);
                }
            },
            /**
             *  进入动销详情
             *  <AUTHOR>
             *  @date        2020-06-23
             */
            gotoOrderItem(item) {
                let type = item.orderChildType === 'InvoiceBased' ? 'bill' : '';
                this.$nav.push('/pages/lj-consumers/booked-order/booked-order-item-page', {
                    data: item,
                    type: type
                })
            },
            /**
             *  判断状态背景图
             *  <AUTHOR>
             *  @date        2024-03-15
             */
            genStyle(status) {
                let bgKey = '';
                switch (status) {
                    case 'Inactive':
                        bgKey = 'statusBgRed';
                        break;
                    case 'Registered':
                        bgKey = 'statusBgOrange';
                        break;
                    case 'New':
                        bgKey = 'statusBgBlue';
                        break;
                    case 'Submitted':
                        bgKey = 'statusBgGreen';
                        break;
                    case 'Rejected':
                        bgKey = 'statusBgGrey';
                        break;
                    default:
                        bgKey = 'statusBgBlue';
                        break;
                }
                return {'background-image': 'url(' + this.$imageAssets[bgKey] + ')'};
            }
        }
    }
</script>

<style lang="scss">
    .conversion-record {

        .sub-tab-con {
            margin: 24px 24px 0 24px;
        }

        .content-con {
            display: block;
            background-color: #f1f1f1;
            border-radius: 32px 32px 0 0;
            box-sizing: border-box;
            padding: 24px;

            .board-con {
                .tip-con {
                    position: relative;
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 24px;

                    .deadline {
                        font-size: 24px;
                        line-height: 40px;
                        color: #666;
                    }

                    .buttons {
                        display: flex;
                    }

                    .select-button {
                        background: #FFFFFF;
                    }

                    .tips {
                        display: flex;
                        font-size: 24px;
                        color: #666;
                        line-height: 40px;
                        box-sizing: border-box;
                        align-items: center;

                        .link-icon.link-icon-status-info {
                            margin-top: 4px;
                            margin-right: 8px;
                        }
                    }
                }

                .board-row {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .board-item {
                        width: 32%;
                        box-sizing: border-box;
                        padding: 24px 0;
                        background-color: white;
                        border-radius: 18px;
                        display: flex;
                        align-items: center;
                        justify-content: flex-start;
                        flex-direction: column;

                        .board-desc {
                            height: 40px;
                            line-height: 40px;
                            font-size: 26px;
                            color: #333333;
                        }

                    }
                }

            }

            .order-con {

                .order-total {
                    margin-bottom: 24px;
                    font-size: 24px;
                    color: #666;
                }

                .item-con {
                    position: relative;
                    box-sizing: border-box;
                    padding: 24px;
                    background-color: white;
                    border-radius: 24px;
                    margin-bottom: 24px;

                    .time {
                        font-size: 28px;
                        line-height: 56px;
                        color: #212223;
                        font-weight: 600;
                    }

                    .item-row {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        font-size: 28px;
                        line-height: 56px;
                        font-weight: 400;

                        .item-desc {
                            color: #999;
                        }

                        .item-value {
                            width: calc(100% - 200px);
                            text-align: right;
                            color: #000;
                            overflow: scroll;
                            white-space: nowrap;

                            &.red {
                                color: #DD3424;
                            }
                        }
                    }

                    .app-status {
                        position: absolute;
                        top: 0;
                        right: 0;
                        background-size: cover;
                        background-repeat: no-repeat;
                        line-height: 48px;
                        box-sizing: border-box;
                        padding: 0 16px 0 32px;
                        color: white;
                        font-size: 24px;
                        font-weight: bold;
                    }
                }
            }

            .open-con {

            }

            .common-con {
                .common-item-con {
                    box-sizing: border-box;
                    padding: 24px;
                    background-color: white;
                    border-radius: 24px;
                    margin-bottom: 24px;

                    .common-desc {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        font-size: 28px;
                        line-height: 68px;

                        .common-title {
                            color: #999;
                        }

                        .common-name {
                            width: calc(100% - 150px);
                            text-align: right;
                            color: #000;
                            overflow: scroll;
                            white-space: nowrap;
                        }
                        .common-name1 {
                            width: calc(100% - 300px);
                            text-align: right;
                            color: #000;
                            overflow: scroll;
                            white-space: nowrap;
                        }
                    }
                }

            }
        }

    }
</style>
