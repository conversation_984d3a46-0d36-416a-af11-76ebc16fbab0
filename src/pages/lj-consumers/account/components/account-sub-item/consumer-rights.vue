<!--
@createdBy 黄鹏
@date 2023/03/05
@description: ---  权益投放
-->
<template>
    <view class="consumer-rights">
        <view class="tips" @tap="showRightsRule">
            <link-icon icon="mp-info-lite" status="info"/>
            <view class="tips-content">查看权益规则</view>
        </view>
        <link-card v-if="rightsDetail">
            <view class="rights-item">
                <view class="rights-item-name">是否注册会员</view>
                <view class="rights-item-value">{{rightsDetail.registerMember ? '是' : '否'}}</view>
                <view class="rights-item-value-tip" v-if="!rightsDetail.registerMember">请引导消费者注册会员</view>
            </view>
            <view class="rights-item">
                <view class="rights-item-name">可兑换积分</view>
                <view class="rights-item-value2"
                      @tap="goToMemberPoint"
                      v-if="rightsDetail.groupMemberPointValue">{{rightsDetail.groupMemberPointValue}}
                </view>
                <view class="rights-item-value" v-else>{{'0'}}</view>
                <view class="rights-item-value-tip">查看积分明细</view>
            </view>
            <view class="rights-item">
                <view class="rights-item-name">最高等级</view>
                <view class="rights-item-value">{{rightsDetail.jhTierName || '-'}}</view>
            </view>
            <view class="rights-item">
                <view class="rights-item-name">最高等级所属公司</view>
                <scroll-view scroll-x="true" scroll-y="false" enable-flex class="rights-item-value1">
                    <view>{{rightsDetail.companyName || '-'}}</view>
                </scroll-view>
            </view>
            <view class="rights-item">
                <view class="rights-item-name">当年累计权益积分</view>
                <view class="rights-item-value2"
                      @tap="goToGrantPoint"
                      v-if="rightsDetail.accrualGrantPoint">{{rightsDetail.accrualGrantPoint}}
                </view>
                <view class="rights-item-value" v-else>{{'0'}}</view>
                <view class="rights-item-value-tip">查看权益积分明细</view>
            </view>
        </link-card>
        <view v-else class="no-rights">暂无对应权益投放活动</view>
    </view>
</template>

<script>

    export default {
        name: "consumer-rights",
        props: {
            accountItem: {
                type: Object,
                required: true
            }
        },
        data() {
            return {
                rightsDetail: null
            }
        },
        created() {
            this.initConsumerRights();
        },
        methods: {
            /**
             * @createdBy 黄鹏
             * @date 2-23/01/05
             * @methods: initConsumerRights
             * @para:
             * @description: 查询权益投放
             **/
            async initConsumerRights() {
                const data = await this.$http.post(this.$env.appURL + '/loyalty/loyalty/kvGrant/queryQwKv', {
                    companyId: this.accountItem.belongToCompanyId,
                    mobilePhone: this.accountItem.phoneNumber
                }, {
                    autoHandleError: false,
                    handleFailed: (data) => {
                        if (!data.success) {
                            if (data.detailMessage.indexOf('MEM-EQUITY-001') !== -1) {
                                // 该公司未配置kv活动
                                this.rightsDetail = null;
                            } else {
                                this.$showError('查询权益投放失败！' + data.detailMessage);
                            }
                        }
                    }
                });
                if (data.success) {
                    this.rightsDetail = data.result || {};
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/01/05
             * @methods: showRightsRule
             * @para:
             * @description: 查看权益规则
             **/
            async showRightsRule() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/reportTaskConfig/queryByExamplePage',
                    page: 1,
                    rows: 1,
                    oauth: 'ALL',
                    filtersRaw: [{id: 'deployNo', property: 'deployNo', value: 'CONSUMER_KV_RULE'}]
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$showError(`查询权益规则失败：${response.result}`);
                    }
                });
                if (data.success) {
                    const reportRecord = data.rows;
                    if (!reportRecord.length) {
                        this.$taro.showToast(`请维护权益规则`);
                        return;
                    }
                    const rightsRuleDetail = reportRecord[0].content;
                    this.$emit('show-rule', rightsRuleDetail, '权益规则');
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/01/08
             * @methods: goToMemberPoint
             * @para:
             * @description: 可兑换积分详情页面
             **/
            goToMemberPoint() {
                this.$nav.push('/pages/lzlj-II/point-detail/member-point-detail-page', {
                    totalPoint: this.rightsDetail.groupMemberPointValue,
                    channelMemberId: this.accountItem.memberId
                })
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/01/08
             * @methods: goToGrantPoint
             * @para:
             * @description: 当年累计权益积分详情页面
             **/
            goToGrantPoint() {
                this.$nav.push('/pages/lzlj-II/point-detail/grant-point-detail-page', {
                    totalPoint: this.rightsDetail.accrualGrantPoint,
                    headId: this.rightsDetail.headId,
                    mobilePhone: this.accountItem.phoneNumber
                })
            }
        }
    }
</script>

<style lang="scss">
    .consumer-rights {
        .tips {
            display: flex;
            font-size: 24px;
            color: #666;
            line-height: 80px;
            box-sizing: border-box;
            padding: 0 24px;
            align-items: center;

            .link-icon.link-icon-status-info {
                margin-top: 4px;
                margin-right: 8px;
            }
        }

        .rights-item {
            width: 100%;
            height: 56px;
            font-size: 28px;
            line-height: 56px;
            display: flex;
            align-items: center;
            justify-content: flex-start;

            .rights-item-name {
                width: 300px;
                color: #595959;
            }

            .rights-item-value {
                color: #262626;
            }

            .rights-item-value1 {
                width: calc(100% - 300px);
                color: #262626;
                white-space: nowrap;
            }

            .rights-item-value2 {
                color: #569bf5;
            }

            .rights-item-value-tip {
                color: #59595996;
                font-size: 18px;
                margin-left: 18px;
            }
        }

        .no-rights {
            line-height: 48px;
            color: #59595996;
            text-align: center;
        }
    }
</style>
