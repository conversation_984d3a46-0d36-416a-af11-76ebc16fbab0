<!--
@createdBy 杨剑飘
@date 2024/09/23
@description: ---  申请记录
-->
<template>
  <view class="apply-record">
    <link-auto-list :option="opt">
      <template slot-scope="{data,index}">
        <view class="item-con" @tap="gotoChangeApplyItem(data)">
          <view class="item-info">

          <view class="item-row">
            <view class="item-desc">模型ID</view>
            <view class="item-value">{{data.id}}</view>
          </view>
          <view class="item-row">
            <view class="item-desc">模型名称</view>
            <view class="item-value wrap-pre one-row">{{data.ruleName}}</view>
          </view>
        </view>
        <view class="view-detail">
            <link-button class="check" size="mini"  @tap="gotoChangeApplyItem(data)">
                查看详情
            </link-button>
        </view>
        </view>
      </template>
    </link-auto-list>
    <filterModelDeatail v-model="dialogShow" :modelDetailData="detailData"></filterModelDeatail>
  </view>
</template>

<script>
import filterModelDeatail from "@/pages/lj-consumers/account/filter-model-detail-dialog.vue";
export default {
  name: "filter-model",
  components: { filterModelDeatail },
  props: {
    pageAuth: {
      type: String,
      required: true
    },
    accountItem: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      opt: new this.AutoList(this, {
        url: {
          queryByExamplePage: this.$env.appURL + '/link/sendDmp/send'
        },
        param: {
          dmpUrl: '/link/cusOpenBotResult/queryRulesByConsumer',
          filtersRaw: [],
          consumerId: this.accountItem.id,
          consumerRule: 'ConsumerModel', oauth: this.pageAuth || 'ALL',
        },
        sortField: 'created',
        sortOptions: null,
        detailData: null,
        hooks: {
          beforeLoad (option) {
          }
        }
      }),
      detail: {},
      dialogShow: false,
    }
  },
  created () {
    if (!this.$utils.isEmpty(this.accountItem.id)) {
      this.opt.methods.reload();
    }
  },
  methods: {
    /**
     * @desc 跳转变更详情
     * <AUTHOR>
     * @date 2022/11/9 16:08
     **/
    gotoChangeApplyItem (item) {
      this.dialogShow = true;
      this.detailData = item;
    //   this.$nav.push('/pages/lj-consumers/account/filter-model-detail-page', {
    //     modelInfo: {...item},
    //     Oauth:this.pageAuth,
    //   });
    }
  }
}
</script>

<style lang="scss">
.one-row{
    display: inline-block;
    font-size: 24px;
    overflow: hidden;
    text-overflow: -o-ellipsis-lastline;
    text-overflow: ellipsis;
    display: -webkit-box;
    white-space: nowrap;
    -webkit-box-orient: vertical;
}
.apply-record {
  overflow: hidden;
  margin: 24px;

  .item-con {
    position: relative;
    box-sizing: border-box;
    padding: 24px;
    background-color: white;
    border-radius: 24px;
    margin-bottom: 24px;

    .time {
      font-size: 28px;
      line-height: 56px;
      color: #212223;
      font-weight: 600;
    }

    .item-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 28px;
      line-height: 56px;
      font-weight: 400;

      .item-desc {
        color: #999;
      }

      .item-value {
        flex: 1;
        text-align: right;
        color: #000;
        overflow: scroll;
        white-space: pre-wrap;
      }
    }

    .check {
        margin-left: 50%;
        transform: translateX(-50%);
    }
  }
}
</style>
