<!--
@createdBy 黄鹏
@date 2024/03/06
@description: --- 奖励记录
-->
<template>
    <view class="award-record">
        <view class="sub-tab-con">
            <sub-tabs :currentTab="currentTab" :tabs="tabs" :is-scroll="true" @switchTab="detailTabChange"></sub-tabs>
        </view>
        <view class="content-con">
            <select-button :current-dimension="currentDimension" @change-btn="changeDimension"></select-button>
            <view class="board-con" v-if="currentTab === 'board'">
                <view class="tip-con">
                    <view class="tips" @tap="queryInfo">
                        <link-icon icon="mp-info-lite" status="info"/>
                        <view class="tips-content">数据指标说明</view>
                    </view>
                    <view class="deadline">截止至{{boardObj.endTime}}数据统计</view>
                </view>
                <view class="board-row">
                    <view class="board-item" v-for="(item, index) in boardItems" :key="index">
                        <view class="board-desc">{{item.name}}</view>
                        <view class="board-desc">{{boardObj[item.field] || '0'}}</view>
                        <view class="board-desc">{{item.unit}}</view>
                    </view>
                </view>
            </view>
            <view class="common-con" v-else-if="currentTab === 'PHYSICAL'">
                <link-auto-list :option="openOption" :key="currentTab + currentDimension">
                    <template slot-scope="{data,index}">
                        <view class="common-item-con">
                            <view class="common-desc">
                                <view class="common-title">活动名称</view>
                                <view class="common-name"> {{ data.actName }}</view>
                            </view>
                            <view class="common-desc">
                                <view class="common-title">中奖时间</view>
                                <view class="common-name"> {{ data.winningTime | date('YYYY-MM-DD HH:mm:ss') }}</view>
                            </view>
                            <view class="common-desc">
                                <view class="common-title">奖项名称</view>
                                <view class="common-name"> {{ data.prizeName }}</view>
                            </view>
                        </view>
                    </template>
                </link-auto-list>
            </view>
            <view class="common-con" v-else-if="currentTab === 'RED_PACKET'">
                <link-auto-list :option="openOption" :key="currentTab + currentDimension">
                    <template slot-scope="{data,index}">
                        <view class="common-item-con">
                            <view class="common-desc">
                                <view class="common-title">活动名称</view>
                                <view class="common-name"> {{ data.actName }}</view>
                            </view>
                            <view class="common-desc">
                                <view class="common-title">中奖时间</view>
                                <view class="common-name"> {{ data.winningTime | date('YYYY-MM-DD HH:mm:ss') }}</view>
                            </view>
                            <view class="common-desc">
                                <view class="common-title">奖项名称</view>
                                <view class="common-name"> {{ data.prizeName }}</view>
                            </view>
                        </view>
                    </template>
                </link-auto-list>
            </view>
            <view class="common-con" v-else-if="currentTab === 'POINT'">
                <link-auto-list :option="openOption" :key="currentTab + currentDimension">
                    <template slot-scope="{data,index}">
                        <view class="common-item-con">
                            <view class="common-desc">
                                <view class="common-title">活动名称</view>
                                <view class="common-name"> {{ data.actName }}</view>
                            </view>
                            <view class="common-desc">
                                <view class="common-title">中奖时间</view>
                                <view class="common-name"> {{ data.winningTime | date('YYYY-MM-DD HH:mm:ss')  }}</view>
                            </view>
                            <view class="common-desc">
                                <view class="common-title">奖项名称</view>
                                <view class="common-name"> {{ data.prizeName }}</view>
                            </view>
                        </view>
                    </template>
                </link-auto-list>
            </view>
            <view class="common-con" v-else-if="currentTab === 'sceneCode'">
                <link-auto-list :option="sceneOption" :key="currentTab + currentDimension">
                    <template slot-scope="{data,index}">
                        <view class="common-item-con">
                            <view class="common-desc">
                                <view class="common-title">活动名称</view>
                                <view class="common-name"> {{ data.activityName }}</view>
                            </view>
                            <view class="common-desc">
                                <view class="common-title">中奖时间</view>
                                <view class="common-name"> {{ data.winningTime | date('YYYY-MM-DD HH:mm:ss') }}</view>
                            </view>
                            <view class="common-desc">
                                <view class="common-title">奖项名称</view>
                                <view class="common-name"> {{ data.awardName }}</view>
                            </view>
                        </view>
                    </template>
                </link-auto-list>
            </view>
        </view>
    </view>
</template>

<script>
    import subTabs from '../sub-tabs/sub-tabs';
    import selectButton from './components/select-button';

    export default {
        name: "award-record",
        components: {selectButton, subTabs},
        props: {
            accountItem: {
                type: Object,
                required: true
            }
        },
        data() {
            return {
                currentTab: 'board',
                tabs: [
                    {name: "中奖看板", val: "board"},
                    {name: "开瓶（实物）", val: "PHYSICAL"},
                    {name: "开瓶（红包）", val: "RED_PACKET"},
                    {name: "开瓶（积分）", val: "POINT"},
                    {name: "场景码", val: "sceneCode"}
                ],
                currentDimension: 'year',
                btns: [{name: '本财年', id: 'year'}, {name: '合计', id: 'sum'}],
                openOption: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: this.$env.appURL + '/action/link/sendDmpSr/send'
                    },
                    param: {
                        dmpSrUrl: '/link/scanCodeWinningDetail/queryByExamplePage',
                        consumerId: this.accountItem.id,
                        filtersRaw: []
                    },
                    sortField: 'winningTime',
                    sortOptions: null,
                    // loadOnStart: false,
                    hooks: {
                        async beforeLoad(option) {
                            const winnerTypes = await this.$lov.getLovByType('COUPONS_TYPE');
                            console.log("COUPONS_TYPE", winnerType, this);
                            // 1 红包 ；2  实物 ；3 积分
                            let winnerType = '';
                            winnerTypes.forEach(item => {
                                console.log('item', item);
                                if (this.currentTab === item.val) {
                                    winnerType = item.val;
                                }
                            });

                            option.param.winningType = winnerType;
                            // 1 红包 ；2  实物 ；3 积分
                            // let type = '';
                            // if (this.currentTab === 'PHYSICAL') {
                            //     type = '2';
                            // } else if (this.currentTab === 'RED_PACKET') {
                            //     type = '1';
                            // } else if (this.currentTab === 'POINT') {
                            //     type = '3';
                            // }
                            // option.param.winningType = type;
                            if (this.currentDimension === 'year') {
                                const arr = this.getFyFilter('winningTime');
                                option.param.filtersRaw = option.param.filtersRaw.concat(arr);
                            }
                        }
                    }
                }),
                sceneOption: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: this.$env.appURL + '/marketactivity/link/sceneCodeWinningDetail/queryByExamplePage'
                    },
                    param: {
                        consumerId: this.accountItem.id,
                        filtersRaw: []
                    },
                    sortField: 'winningTime',
                    sortOptions: null,
                    // loadOnStart: false,
                    hooks: {
                        beforeLoad(option) {
                            if (this.currentDimension === 'year') {
                                const arr = this.getFyFilter('winningTime');
                                option.param.filtersRaw = option.param.filtersRaw.concat(arr);
                            }
                        }
                    }
                }),
                boardItems: [
                    {name: '开瓶（实物/红包）', field: 'realWinningQty', unit: '次'},
                    {name: '开瓶（积分）', field: 'pointsWinningQty', unit: '次'},
                    {name: '场景码奖品', field: 'sceneCodeWinningQty', unit: '次'}
                ],
                boardObj: {}
            }
        },
        created() {
            this.getBoardData();
        },
        methods: {
            /**
             * @createdBy 黄鹏
             * @date 2024/02/04
             * @methods: getBoardData
             * @para:
             * @description: 看板数据
             **/
            async getBoardData() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmpSr/send', {
                    consumerId: this.accountItem.id,
                    dataType: this.currentDimension === 'year' ? 'thisFiscalYear' : 'allTime',
                    dmpSrUrl: '/link/ConsumerReachMetrics/queryConsumerReachMetrics'
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$showError(`查询看板数据失败：${response.result}`);
                    }
                });
                this.boardObj = data.result || {};
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/02/04
             * @methods: detailTabChange
             * @para:
             * @description: 消费者详情tab切换
             **/
            detailTabChange(val, changeTab = true) {
                if (changeTab) {
                    this.currentTab = val;
                    this.currentDimension = this.btns[0].id;
                }
                if (this.$utils.isEmpty(this.accountItem.id)) {
                    return;
                }
                switch (val) {
                    case 'board':
                        this.getBoardData();
                        break;
                    case 'PHYSICAL':
                    case 'RED_PACKET':
                    case 'POINT':
                        // this.openOption.methods.reload();
                        break;
                    case 'sceneCode':
                        // this.sceneOption.methods.reload();
                        break;
                    default:
                        break;
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/02/04
             * @methods: changeDimension
             * @para:
             * @description: 维度切换
             **/
            changeDimension(val) {
                this.currentDimension = val;
                this.detailTabChange(this.currentTab, false);
            },
            /**
             * @createdBy 黄鹏
             * @date 2023/03/05
             * @methods: getFyFilter
             * @para:
             * @description: 财年筛选参数
             **/
            getFyFilter(field = 'auditTime') {
                const year = new Date().getFullYear();
                let arr = [];
                if (new Date().getTime() < new Date(`${year}/11/01 00:00:00`).getTime()) {
                    arr = [
                        {id: field, property: field, operator: '>=', value: `${year - 1}-11-01 00:00:00`},
                        {id: field + '1', property: field, operator: '<=', value: `${year}-10-31 23:59:59`}
                    ]
                } else {
                    arr = [
                        {id: field, property: field, operator: '>=', value: `${year}-11-01 00:00:00`},
                        {id: field + '1', property: field, operator: '<=', value: `${year + 1}-10-31 23:59:59`}
                    ]
                }
                return arr;
            },
            /**
             @desc: 查询数据指标说明
             @author: wangbinxin
             @date 2023-10-20 10-05
             **/
            async queryInfo() {
                this.$utils.showLoading();
                const deployNo = this.currentDimension === 'year' ? 'WIN_BOARD_YEAR' : 'WIN_BOARD_ALL';
                const data = await this.$http.post(this.$env.dmpURL + '/link/reportTaskConfig/queryByExamplePage', {deployNo}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('查询失败' + response.result);
                    }
                });
                this.$utils.hideLoading();
                if (data.success) {
                    const description = data.rows[0]?.content;
                    this.$emit('show-rule', description, '数据指标说明');
                }
            }
        }
    }
</script>

<style lang="scss">
    .award-record {

        .sub-tab-con {
            margin: 24px 24px 0 24px;
        }

        .content-con {
            display: block;
            background-color: #f1f1f1;
            border-radius: 32px 32px 0 0;
            box-sizing: border-box;
            padding: 24px;

            .board-con {
                .tip-con {
                    position: relative;
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 24px;

                    .deadline {
                        font-size: 24px;
                        line-height: 40px;
                        color: #666;
                    }

                    .buttons {
                        display: flex;
                    }

                    .select-button {
                        background: #FFFFFF;
                    }

                    .tips {
                        display: flex;
                        font-size: 24px;
                        color: #666;
                        line-height: 40px;
                        box-sizing: border-box;
                        align-items: center;

                        .link-icon.link-icon-status-info {
                            margin-top: 4px;
                            margin-right: 8px;
                        }
                    }
                }

                .board-row {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .board-item {
                        width: 32%;
                        box-sizing: border-box;
                        padding: 24px 0;
                        background-color: white;
                        border-radius: 18px;
                        display: flex;
                        align-items: center;
                        justify-content: flex-start;
                        flex-direction: column;

                        .board-desc {
                            height: 40px;
                            line-height: 40px;
                            font-size: 26px;
                            color: #333333;
                        }

                    }
                }

            }

            .common-con {
                .common-item-con {
                    box-sizing: border-box;
                    padding: 24px;
                    background-color: white;
                    border-radius: 24px;
                    margin-bottom: 24px;

                    .common-desc {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        font-size: 28px;
                        line-height: 68px;

                        .common-title {
                            color: #999;
                        }

                        .common-name {
                            width: calc(100% - 150px);
                            text-align: right;
                            color: #000;
                            overflow: scroll;
                            white-space: nowrap;
                        }
                    }
                }

            }
        }

    }
</style>
