<!--
消费者详情-价值评估模型tab
<AUTHOR>
@date 2024-11-14
@file value-assessment.vue
-->
<template>
  <view class="value-assessment">
    <view class="valueList-content">
      <scroll-view v-if="valueListFlag" scroll-y="true">
        <!-- 价值评估模型列表 -->
        <link-auto-list :option="valueList" hideCreateButton :key="1">
          <template slot-scope="{data,index}">
            <item :key="index" :data="data" :arrow="false" class="valueList-item">
                <view class="logo-img">
                    <link-icon class="media-list-logo" icon="icon-kucunpandian1"/>
                    <view v-if="data.whetherShow === 'Y'" class="main">主</view>
                </view>
              <view class="guest-list-item-content">
                <view class="guest-list-item-content-row1">
                  <view class="consumer-name">
                    {{ '模型总分：' + (data.modelTotalPoint ? (data.modelTotalPoint + '分') : '') }}
                  </view>
                  <view class="rights-item-value-tip" @tap="gotoResult(data)">{{ '查看结果明细' }}</view>
                </view>
                <view class="guest-list-item-content-row1">
                  <view class="consumer-name">
                    {{ '模型名称：' + (data.modelName ? data.modelName : '') }}
                  </view>
                  <view class="rights-item-value-tip" @tap="gotoRuleList(data)">{{ '查看规则' }}</view>
                </view>
                <view class="guest-list-item-content-row1">
                  <view class="consumer-name">
                    {{ '模型有效期：' + (data.modelTime ? data.modelTime : '') }}
                  </view>
                </view>
                <view class="guest-list-item-content-row1">
                  <view class="consumer-name">
                    {{ '模型状态：' }}{{ data.modelStatus | lov('CUS_MODEL_STATUS') }}
                  </view>
                </view>
              </view>
            </item>
          </template>
        </link-auto-list>
      </scroll-view>
      <!-- 规则说明/结果明细/评分明细顶部 -->
      <view class="detail-title" v-if="!valueListFlag && (ruleListFlag || resultDetailFlag || scoreDetailFlag)">
        <view class="guest-list-item">
          <view class="logo-img">
            <image class="media-list-logo" :src="valueItem | headImgAccount(valueItem)"/>
            <view v-if="valueItem.whetherShow === 'Y'" class="main">主</view>
            <view class="value-score">{{ valueItem.modelTotalPoint + (valueItem.modelTotalPoint ? '分' : '') }}
            </view>
          </view>
          <view class="guest-list-item-content">
            <view class="guest-list-item-content-row1">
              <view class="consumer-name">
                {{ '模型名称：' + (valueItem.modelName ? valueItem.modelName : '') }}
              </view>
              <view v-if="ruleListFlag" class="rights-item-value-tip" @tap="gotoModelList('back')">
                {{ '查看模型' }}
              </view>
              <view v-if="resultDetailFlag || scoreDetailFlag" class="rights-item-value-tip" @tap="gotoRuleList('back')">
                {{ '查看规则' }}
              </view>
            </view>
            <view class="guest-list-item-content-row2">
              {{ '模型有效期：' + (valueItem.modelTime ? valueItem.modelTime : '') }}
            </view>
            <view class="guest-list-item-content-row2">
              {{ '模型说明：' + (valueItem.comments ? valueItem.comments : '') }}
            </view>
            <view class="guest-list-item-content-row2">
              {{ '模型状态：' }}{{ valueItem.modelStatus | lov('CUS_MODEL_STATUS') }}
            </view>
          </view>
        </view>
      </view>
      <!-- 规则列表 -->
      <scroll-view v-if="ruleListFlag" scroll-y="true" class="main-content" :style="{ height: `calc(100vh - 200px)`}">
        <line-title title="规则说明"></line-title>
        <link-auto-list :option="ruleList" hideCreateButton :key="2">
          <template slot-scope="{data,index}">
            <item :key="index" :data="data" :arrow="false" class="overview-item">
              <view slot="note">
                <view class="item-input">
                  <view class="label1">{{ '规则名称' }}</view>
                  <view class="text1">{{ data.ruleName }}</view>
                </view>
                <view class="item-input">
                  <view class="label1">{{ '动作类型' }}</view>
                  <view class="text1">{{ data.businessType | lov('BUSSINESS_TYPE') }}</view>
                </view>
                <view class="item-input">
                  <view class="label1">{{ '加分分值' }}</view>
                  <view class="text1">{{ data.addPoint }}</view>
                </view>
                <view class="item-input">
                  <view class="label1">{{ '扣分分值' }}</view>
                  <view class="text1">{{ data.redemptionPoint }}</view>
                </view>
                <view class="item-input">
                  <view class="label1" style="color: #EA3232">{{ '加分上限' }}</view>
                  <view class="text1" style="color: #EA3232">{{ data.maxPoint }}</view>
                </view>
                <view class="item-input">
                  <view class="label1">{{ '状态' }}</view>
                  <view class="text1">{{ data.ruleStatus | lov('CUS_MODEL_STATUS') }}</view>
                </view>
              </view>
            </item>
          </template>
        </link-auto-list>
      </scroll-view>
      <!-- 模型结果明细 -->
      <scroll-view v-if="resultDetailFlag" scroll-y="true" class="main-content"
                   :style="{ height: `calc(100vh - 200px)`}">
        <line-title title="模型结果明细"></line-title>
        <link-auto-list :option="resultDetailList" hideCreateButton :key="3">
          <template slot-scope="{data,index}">
            <item :key="index" :data="data" :arrow="false" class="overview-item">
              <view slot="note">
                <view class="item-input">
                  <view class="label2">{{ '规则名称' }}</view>
                  <view class="text2">{{ data.ruleName }}</view>
                </view>
                <view class="item-input">
                  <view class="label2">{{ '动作类型' }}</view>
                  <view class="text2">{{ data.businessType | lov('BUSSINESS_TYPE') }}</view>
                </view>
                <view class="item-input">
                  <view class="label2" style="color: #EA3232">{{ '累计分值' }}</view>
                  <view class="text3" style="color: #EA3232">{{ data.ruleTotalPoint }}</view>
                  <view class="rights-item-value-tip" @tap="gotoScoreDetail(data)">{{ '查看评分明细' }}</view>
                </view>
                <view class="item-input">
                  <view class="label2" style="color: #EA3232">{{ '加分上限' }}</view>
                  <view class="text2" style="color: #EA3232">{{ data.maxPoint }}</view>
                </view>
              </view>
            </item>
          </template>
        </link-auto-list>
      </scroll-view>
      <!-- 评分明细 -->
      <scroll-view v-if="scoreDetailFlag" scroll-y="true" class="score-list"
                   :style="{ height: `calc(100vh - 200px)`}">
        <line-title title="评分明细"></line-title>
        <link-auto-list :option="scoreDetailList" hideCreateButton :key="4">
          <template slot-scope="{data,index}">
            <item :key="index" :data="data" class="detail-list-item" :arrow="false">
              <view class="detail-item">
                <view class="detail-item-info">
                  <view class="detail-item-info-item">
                    <view class="label">{{ '业务名称' }}</view>
                    <view class="detail-item-info-text">{{ data.businessName }}</view>
                  </view>
                  <view class="detail-item-info-item">
                    <view class="label">{{ '执行时间' }}</view>
                    <view class="detail-item-info-text">{{ data.businessTime }}</view>
                  </view>
                  <view class="detail-item-info-item">
                    <view class="label">{{ '执行人' }}</view>
                    <view class="detail-item-info-text">{{ data.businessCreated }}</view>
                  </view>
                  <view class="detail-item-info-item">
                    <view class="label">{{ '所属公司' }}</view>
                    <view class="detail-item-info-text">{{ data.sourceCompanyName }}</view>
                  </view>
                  <view class="detail-item-info-item">
                    <view class="label">{{ '备注' }}</view>
                    <view class="detail-item-info-text">{{ data.remark }}</view>
                  </view>
                </view>
              </view>
              <view class="tag-button">
                <view class="tag-item" v-if="data.businessType">
                  {{ data.businessType | lov('BUSSINESS_TYPE') }}
                </view>
                <view class="tag-item" v-if="data.pointValue">
                  {{ data.transactionType === 'Accrual' ? '+' : '-' }}{{ data.pointValue }}
                </view>
              </view>
            </item>
          </template>
        </link-auto-list>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import LineTitle from "../../../../lzlj/components/line-title.vue";
import StatusButton from "../../../../lzlj/components/status-button.vue";

export default {
  name: 'value-assessment',
  components: {StatusButton, LineTitle},
  props: {
    accountItem: {
      type: Object,
      required: true
    }
  },
  data() {
    // 价值评估模型列表
    const valueList = new this.AutoList(this, {
      url: {
        queryByExamplePage: this.$env.appURL + '/action/link/cusModelResult/queryResultObjByExamplePage'
      },
      loadOnStart: false,
      param: {},
      hooks: {
        async beforeLoad(option) {
            if(this.accountItem.identityId) {
                option.param.identityId = this.accountItem.identityId;
                option.param.consumerId = this.accountItem.id;
            } else {
                const filtersRaw = [
                    {id: 'consumerId', property: 'consumerId', value: this.accountItem.id, operator: '='}
                ]
                option.param.filtersRaw = filtersRaw.concat(option.param.filtersRaw);
            }
        }
      },
      // sortOptions: null,
    });
    // 规则列表
    const ruleList = new this.AutoList(this, {
      url: {
        queryByExamplePage: this.$env.appURL + '/action/link/cusModelRule/queryByExamplePage'
      },
      loadOnStart: false,
      param: {},
      hooks: {
        async beforeLoad(option) {
          const filtersRaw = [
            {id: 'modelId', property: 'modelId', value: this.valueItem.modelId, operator: '='}
          ]
          option.param.filtersRaw = filtersRaw.concat(option.param.filtersRaw);
        }
      },
      // sortOptions: null,
    });
    // 结果明细列表
    const resultDetailList = new this.AutoList(this, {
      url: {
        queryByExamplePage: this.$env.appURL + '/action/link/cusModelResultDetail/queryByExamplePage'
      },
      loadOnStart: false,
      param: {},
      hooks: {
          async beforeLoad(option) {
              const identityFlag = this.$utils.isEmpty(this.valueItem.consumerId) && this.valueItem.identityId
              const filtersRaw = [
                  {id: 'modelResultId', property: 'modelResultId', value: this.valueItem.id, operator: '='},
                  {id: identityFlag ? 'identityId' : 'consumerId', property: identityFlag ? 'identityId' : 'consumerId', value: identityFlag ? this.valueItem.identityId : this.accountItem.id, operator: '='}
              ];
              option.param.filtersRaw = filtersRaw.concat(option.param.filtersRaw);
          }
      },
      // sortOptions: null,
    });
    // 评分明细列表
    const scoreDetailList = new this.AutoList(this, {
      url: {
        queryByExamplePage: this.$env.appURL + '/action/link/transactionBusQuery/queryByExamplePage'
      },
      loadOnStart: false,
      param: {},
      hooks: {
        async beforeLoad(option) {
            const identityFlag = this.$utils.isEmpty(this.valueItem.consumerId) && this.valueItem.identityId
            const filtersRaw = [
                {id: 'modelId', property: 'modelId', value: this.valueItem.modelId, operator: '='},
                {id: 'ruleId', property: 'ruleId', value: this.ruleId, operator: '='},
                {id: identityFlag ? 'identityId' : 'consumerId', property: identityFlag ? 'identityId' : 'consumerId', value: identityFlag ? this.valueItem.identityId : this.accountItem.id, operator: '='}
            ];
            option.param.filtersRaw = filtersRaw.concat(option.param.filtersRaw);
        }
      }
      // sortOptions: null,
    });
    return {
      valueList, // 价值评估模型列表
      ruleList,   // 规则列表
      resultDetailList, // 结果明细列表
      scoreDetailList, // 评分明细列表
      valueListFlag: true, //是否展示价值评估模型列表
      ruleListFlag: false, //是否展示规则列表
      valueItem: {}, // 价值评估模型数据
      resultDetailFlag: false, // 是否展示结果明细
      scoreDetailFlag: false, // 是否展示评分明细
      ruleId: '' // 规则id
    }
  },
  async mounted() {
    await this.valueList.methods.reload();
  },
  methods: {
    /**
     * @desc 跳转到价值评估模型列表页
     * <AUTHOR>
     * @date 2024-11-25
     **/
    gotoModelList(data) {
      this.valueListFlag = true;
      this.ruleListFlag = false;
      // 刷新价值评估模型列表
      this.valueList.methods.reload();
    },
    /**
     * @desc 跳转到规则列表
     * <AUTHOR>
     * @date 2024-11-25
     **/
    gotoRuleList(data) {
      if (data !== 'back') {
        this.valueItem = data;
      }
      console.log('data', data);
      console.log('valueItem', this.valueItem);
      this.valueListFlag = false;
      this.scoreDetailFlag = false;
      this.resultDetailFlag = false;
      this.ruleListFlag = true;
      // 刷新规则列表
      this.ruleList.methods.reload();
    },
    /**
     * @desc 返回价值评估模型列表页
     * <AUTHOR>
     * @date 2024-11-25
     **/
    backValueList() {
      this.valueListFlag = true;
      this.ruleListFlag = false;
      // 刷新价值评估模型列表
    },
    /**
     * @desc 跳转到结果明细
     * <AUTHOR>
     * @date 2024-11-25
     **/
    async gotoResult(data) {
      console.log('data', data);
      this.valueItem = data;
      this.valueListFlag = false;
      this.resultDetailFlag = true;
      // 刷新结果明细
      await this.resultDetailList.methods.reload();
    },
    /**
     * @desc 跳转到评分明细页
     * <AUTHOR>
     * @date 2024-11-25
     */
    async gotoScoreDetail(data) {
      console.log('data', data);
      this.ruleId = data.ruleId;
      this.resultDetailFlag = false;
      this.scoreDetailFlag = true;
      // 刷新评分明细
      await this.scoreDetailList.methods.reload();
    }
  }
}
</script>

<style lang="scss">
.value-assessment {
  //height: 90%;
  .valueList-content {
    display: flex;
    flex-direction: column;
    margin: 24px;
    border-radius: 16px;
    max-width: calc(100% - 32px);

    .valueList-item {
      @include flex;
      @include flex-start-center;
      //overflow: hidden;

      .logo-img {
        width: 15%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-right: 20px;
        align-items: center;
        position: relative; /* 添加相对定位 */
      }

      .media-list-logo {
        font-size: 100px;
        color: #c7e5fa;
      }

      .main {
        position: absolute; /* 绝对定位 */
        top: 0; /* 位置在右上角 */
        right: 0;
        color: #b02423;
        border-radius: 50%; /* 可选：圆形背景 */
        padding: 2px 2px 4px 4px; /* 可选：内边距 */
        font-weight: bold;
        font-size: 20px;
        border: 4px solid #b02423; /* 红色边框 */
        width: 24px; /* 圆形的宽度 */
        height: 24px; /* 圆形的高度 */
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .guest-list-item-content {
        width: 80%;
        padding-right: 16px;
        display: flex;
        flex-direction: column;

        .guest-list-item-content-row1 {
          width: 100%;
          display: flex;
          line-height: 48px;

          .consumer-name {
            display: block;
            margin-bottom: 4px;
            font-size: 26px;
            color: #333333;
            max-width: calc(100% - 130px);
            white-space: nowrap;
            overflow: auto;
          }

          .rights-item-value-tip {
            color: #2f69f8;
            font-size: 22px;
            margin-left: 18px;
            line-height: 48px;
            min-width: 130px;
          }
        }
      }
    }

    .perform-case-list-item {
      background: #FFFFFF;
      //margin: 24px;
      border-radius: 16px;
    }

    .content-middle {
      width: 95%;
      display: flex;
      align-items: center;
      font-size: 28px;
      letter-spacing: 0;
      font-family: PingFangSC-Regular;
      line-height: 52px;
      overflow-x: auto;

      .lable-name {
        color: #8C8C8C;
        margin-right: 10px;
        min-width: 180px;
        flex-shrink: 0;
      }

      .name {
        color: #262626;
        width: 70%;
        //    超出部分滑动显示
        flex-shrink: 1;
        white-space: nowrap;
        overflow: auto;
      }
    }
  }

  .detail-title {
    padding: 34px;
    background-color: #fff;
    border-radius: 16px;
    margin: 24px;

    .guest-list-item {
      @include flex;
      @include flex-start-center;
      //overflow: hidden;

      .logo-img {
        width: 15%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-right: 20px;
        align-items: center;
        position: relative; /* 添加相对定位 */
      }

      .media-list-logo {
        height: 94px;
        width: 94px;

        image {
          height: 100%;
          width: 100%;
        }
      }

        .main {
            position: absolute; /* 绝对定位 */
            top: -10px; /* 位置在右上角 */
            right: -10px;
            color: #b02423;
            //background-color: transparent; /* 可选：添加背景色 */
            border-radius: 50%; /* 可选：圆形背景 */
            padding: 2px 2px 4px 4px; /* 可选：内边距 */
            font-weight: bold;
            font-size: 20px;
            border: 4px solid #b02423; /* 红色边框 */
            width: 24px; /* 圆形的宽度 */
            height: 24px; /* 圆形的高度 */
            display: flex;
            align-items: center;
            justify-content: center;
        }

      .guest-list-logo {
        border-radius: 50%;
        width: 80px;
        height: 80px;
        overflow: hidden;
      }

      .guest-list-item-content {
        width: 80%;
        padding-right: 16px;
        display: flex;
        flex-direction: column;

        .guest-list-item-content-row1 {
          width: 100%;
          display: flex;

          .consumer-name {
            display: block;
            margin-bottom: 4px;
            font-size: 26px;
            color: #333333;
            max-width: calc(100% - 130px);
            white-space: nowrap;
            overflow: auto;
          }
        }

        .guest-list-item-content-row2 {
          display: block;
          max-width: 100%;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;
          font-size: 24px;
          line-height: 42px;
          //white-space: nowrap;
          //overflow: hidden;
          //text-overflow: ellipsis;
          // 超出宽度滑动显示
          white-space: nowrap;
          overflow: auto;
        }

        .guest-list-item-content-row3 {
          display: block;
          max-width: calc(100% - 100px);
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;
          font-size: 22px;
          line-height: 42px;
          // 超出宽度滑动显示
          white-space: nowrap;
          overflow: auto;
        }
        .rights-item-value-tip {
          color: #2f69f8;
          font-size: 22px;
          margin-left: 18px;
          line-height: 36px;
          min-width: 100px;
        }
      }
    }
  }

  .main-content {
    display: flex;
    flex-direction: column;
    margin: 16px;
    max-width: calc(100% - 32px);

    .overview-item {
      margin: 20px 0px;
      border-radius: 16px;
      background: white;
      font-family: PingFangSC-Regular;
      padding-bottom: 0px;

      .item-input {
        line-height: 30px;
        margin-bottom: 30px;
        width: 100%;
        display: flex;

        .label1 {
          align-self: flex-start;
          color: #8c8c8c;
          font-size: 28px;
          display: flex;
          align-items: center;
          box-sizing: border-box;
          width: 300px;
        }

        .label2 {
          align-self: flex-start;
          color: #8c8c8c;
          font-size: 28px;
          display: flex;
          align-items: center;
          box-sizing: border-box;
          width: 160px;
        }

        .text1 {
          color: #262626;
          width: 420px;
          font-size: 28px;
          text-align: right;
        }

        .text2 {
          color: #262626;
          max-width: 450px;
          font-size: 28px;
          align-self: flex-start;
          white-space: nowrap;
          overflow: auto;
        }

        .text3 {
          color: #262626;
          max-width: 320px;
          font-size: 28px;
          align-self: flex-start;
          white-space: nowrap;
          overflow: auto;
        }

        .rights-item-value-tip {
          color: #2F69F8;
          font-size: 22px;
          margin-left: 18px;
          line-height: 30px;
          min-width: 130px;
        }
      }
    }
  }


  .score-list {
    .detail-list-item {
      background: #FFFFFF;
      margin: 24px;
      border-radius: 16px;
      overflow: hidden;
      position: relative;
      display: flex;
      justify-content: space-between;

      .detail-item {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        width: 65%;
        overflow-x: auto;

        .detail-item-info {
          display: flex;
          flex-direction: column;

          .detail-item-info-item {
            height: 44px;
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .label {
              min-width: 112px;
              margin-right: 24px;
              font-size: 28px;
              color: #999999;
              line-height: 44px;
              font-weight: 400;
            }

            .label-1 {
              min-width: 140px;
              margin-right: 24px;
              font-size: 28px;
              color: #999999;
              line-height: 44px;
              font-weight: 400;
            }

            .detail-item-info-text {
              font-size: 28px;
              color: #333333;
              line-height: 44px;
              font-weight: 400;
              flex-shrink: 0;
              //white-space: nowrap;
            }
          }
        }
      }

      .tag-button {
        max-width: 35%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: sticky; /* 使按钮始终可见 */
        right: 0; /* 将按钮固定在右侧 */
        flex-direction: column;
        margin-left: 24px;

        .tag-item {
          display: flex;
          width: 90%;
          justify-content: center;
          align-items: center;
          background: #eaefff;
          color: #546ee2;
          font-size: 22px;
          border-radius: 12px;
          margin: 12px;
          padding: 8px 12px;
          flex-shrink: 1;
          white-space: nowrap;
          overflow: auto;
        }
      }
    }
  }
  .link-item .link-item-body-left .link-item-content, .link-item .link-item-body-left .link-item-desc, .link-item .link-item-body-right .link-item-content, .link-item .link-item-body-right .link-item-desc {
    display: flex;
    align-items: center;
    justify-content: flex-start !important;
  }
}
</style>
