<!--
消费者详情-裂变模型（网络关系）
<AUTHOR>
@date 2024-07-30
@file net-relation.vue
-->
<template>
  <view class="net-relation">
    <view class="net-tip">
      数据截止时间:<text v-if="detailData.warehousingTime">
        {{ dataTime }} 23:59:59 </text><text v-else>--</text>
    </view>

    <view class="custemer-p">
      <view class="custemer custemer-h">
        <view class="custemer-icon">
          <link-icon icon="icon-xiaofeizhezhuti" class="parent-custemer"></link-icon>
        </view>
        <view class="custemer-name">{{ consumerName || 'ㅤ  ' }}</view>
        <view class="custemer-count">总计:{{ total }}人</view>
      </view>
    </view>
    <view style="display: flex; justify-content: center" v-show="modelData.length">
      <view class="down-line"> </view>
    </view>
    <view class="custemer-children">
      <!-- <view class="fix-btn left" @tap="leftClick"> -->
      <view class="fix-btn left" @tap="leftClick" v-show="page > 1 && total > 4">
        <link-icon style="color: #3f66ef" icon="icon-chakan"></link-icon>
      </view>
      <view class="fix-btn right" @tap="rightClick" v-show="page * 4 < total && total > 4">
        <!-- <view class="fix-btn right" @tap="rightClick"> -->
        <link-icon style="color: #3f66ef" icon="icon-chakan"></link-icon>
      </view>
      <!-- 所有树形子模块 -->
      <view class='custemer-children-p' v-for="(item, i) in modelData" @tap="select(item)" :key="i">
        <view class="lines" style="display: flex; justify-content: center">
          <view class="left-line" :style="{ visibility: i == 0 ? 'hidden' : '' }">
          </view>
          <view class="down-line"> </view>
          <view class="right-line" :style="{
              visibility: 1 + i == modelData.length ? 'hidden' : '',
            }">
          </view>
        </view>
        <view :class="['custemer', 'custemer-c', item.selected && 'selected',modelData.length==4&&'w9']" :style="{ margin: `0 ${20 / modelData.length}rpx` }">
          <view class="custemer-icon">
            <link-icon icon="icon-xiaofeizhe1" style="color: gray"></link-icon>
          </view>
          <view class="custemer-name">{{ item.name || 'ㅤ  ' }}</view>
          <view style="display: flex; justify-content: center">
            <view :class="[
                'custemer-count',
                item.certify == 'certified' ? 'success' : 'error',
              ]">{{ item.certify | lov("CERTIFY") }}</view>
          </view>
          <view class="custemer-count">
            {{ item.type | lov("ACCT_SUB_TYPE") }}</view>
          <view class="custemer-count">
            {{ item.loyaltyLevel | lov("ACCT_MEMBER_LEVEL") }}</view>

        </view>
        <view class="lines" style="display: flex; justify-content: center" v-if="item.selected">
          <view class="down-line"> </view>
        </view>
      </view>
    </view>

    <view class="custemer-detail" v-show="modelData.length">
      <view class="detail">
        <view class="title-area">
          <view class="border-img"></view>
          <view class="title1">购酒情况</view>
          <view class="title2">累计购酒:</view>
          <view class="title3-count">{{ detailData.ordBotQty || "--" }}瓶</view>
        </view>
        <view class="info-area">
          <view class="info-label">最近一次购酒</view>
          <view class="info-v">{{ detailData.reBuyBotQty || "--" }}瓶</view>
          <view class="down-line2"></view>
          <view class="info-v">{{ detailData.reBuyTime }}</view>
        </view>
      </view>
      <view class="detail">
        <view class="title-area">
          <view class="border-img"></view>
          <view class="title1">开瓶情况</view>
          <view class="title2">累计开瓶扫码:</view>
          <view class="title3-count">{{ detailData.ordOpenQty || "--" }}瓶</view>
        </view>
        <view class="info-area ">
          <view class="qr-all">
            <view class="text-tips" :style="{top: topPosition,visibility:scanTip&&detailData.reScanPro.length>=14?'':'hidden'}" id="scanTip">
              {{ detailData.reScanPro }}
            </view>
            <text class="info-label qr-last">最近一次扫码</text>
            <text class="info-v qr-last-content" @tap="tipTap('scanTip')">{{ detailData.reScanPro || "--" }}</text>
          </view>
          <view class="down-line2"></view>
          <text class="info-v">
            {{ detailData.reScanTime }}
          </text>
        </view>
        <view class="info-area">
          <view class="qr-all">
            <view class="text-tips" :style="{top: topPosition,visibility:mainTip &&detailData.mainOpenAcctName.length>=14?'':'hidden'}" id="mainTip">
              {{ detailData.mainOpenAcctName }}
            </view>
            <view class="text-tips" :style="{top: topPosition,visibility:iconFlag?'':'hidden'}" id="iconFlag">
              {{ nodes }}
            </view>
            <text class="info-label">主要开瓶终端 </text>
            <link-icon @tap="tipTap('iconFlag')" size="1.8em" color="#DADBE2" icon="icon-banbenxinxi"></link-icon>
            <text class="info-v" @tap="tipTap('mainTip')">{{ detailData.mainOpenAcctName || "--" }}</text>
          </view>
          <view class="down-line2"></view>
          <view class="info-v">{{
            detailData.mainOpenAcctLevel | lov("ACCT_LEVEL")
          }}</view>
        </view>
      </view>
      <view class="detail">
        <view class="title-area">
          <view class="border-img"></view>
          <view class="title1">转介绍消费者</view>
          <view class="title2">转介绍人数:</view>
          <view class="title3-count">{{ detailData.recommenderAcctNum || "--" }}人</view>
        </view>
        <view class="info-area">
          <view class="info-label">转介绍消费者累计购酒</view>
          <view class="info-v">{{ detailData.recommenderBuyQty || "--" }}瓶</view>
        </view>
        <view class="info-area">
          <view class="info-label">转介绍消费者累计开瓶</view>
          <view class="info-v">{{ detailData.recommenderOpenQty || "--" }}瓶</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "net-relation",
  props: { pageSecurity: null, accountItem: null }, //acctName
  data () {
    return {
      iconFlag: false,
      scanTip: false,
      mainTip: false,
      nodes: "累计开瓶数量最高的终端名称",
      modelData: [],
      detailData: {},
      total: "--",
      page: 1,
      topPosition: '-999px'
    };
  },
  computed: {
    consumerId () {
      return this.accountItem && this.accountItem.id;
    },
    consumerName () {
      return this.accountItem && this.accountItem.name;
    },
    dataTime(){
        var today = new Date(); today.setTime(today.getTime()-24*60*60*1000);
        var yesterday = today.getFullYear()+"-" + (today .getMonth()+1) + "-" + today.getDate();
        return yesterday
    }
  },
  created () {
    console.log(this.accountItem, "this.accountItem");
    this.getModelData((row) => {
      this.select(row[0]); // 默认选中第一项
    });
  },
  methods: {
    tipTap (type = 'iconFlag') {
      this.iconFlag = false;
      this.scanTip = false;
      this.mainTip = false;
      this[type] = true;
      this.$nextTick(e => {
        const query = wx.createSelectorQuery();
        query
          .select(`#${type}`)
          .boundingClientRect((ret) => {
            this.topPosition = -ret.height + 'px';
          })
          .exec();
      })
      setTimeout(() => {
        this[type] = false
      }, 4333);
    },
    rightClick () {
      if (this.page * 4 >= this.total) return;
      this.page = this.page + 1;
      this.getModelData((row) => {
        this.select(row[0]); // 默认选中第一项
      });
    },
    leftClick () {
      if (this.page < 1) return;
      this.page = this.page - 1;
      this.getModelData((row) => {
        this.select(row[0]); // 默认选中第一项
      });
    },
    /**
     * @description: 获裂变模型转介绍裂变信息列表
     * @author: yangjianpiao
     **/
    async getModelData (initRowFunction) {
      const params = {
        consumerId: this.consumerId,
        // consumerId: '297421750218919987',
        oauth: this.pageSecurity,
        order: "desc",
        page: this.page,
        rows: 4,
        // sort: 'behaviorTime',
        totalFlag: true,
      };
      try {
        this.$utils.showLoading();
        const data = await this.$http.post(
          this.$env.dmpURL +
          "/link/consumerFissionControllerSR/queryByConsumerIdPage",
          params
        );
        this.$utils.hideLoading();
        if (!data.success) {
          this.$nav.error("获取裂变模型失败: " + data.result);
        } else {
          this.total = data.total > 0 ? data.total : "--";
          if (!data.rows || !data.rows.length) {
          }
          this.modelData = data.rows;
          initRowFunction && initRowFunction(data.rows);
        }
      } catch (e) {
        console.log("错误:" + e);
      }
    },
    /**
     * @description: 获裂变模型客户详细信息
     * @author: yangjianpiao
     **/
    async getDetail (referralConId) {
      //   referralConId = "442345867105946514"; // test
      const params = {
        referralConId,
        oauth: this.pageSecurity,
        order: "desc",
        totalFlag: true,
      };
      this.$utils.showLoading();
      try {
        const data = await this.$http.post(
          this.$env.dmpURL +
          "/link/consumerFissionControllerSR/queryByReferralConId",
          params
        );
        this.$utils.hideLoading();
        if (!data.success) {
          this.$nav.error("获取裂变模型失败: " + data.result);
        } else {
          if (!data.result || !data.result.result) {
            // test
            data.result = { result: {} };
          }
          this.detailData = data.result.result;
        }
      } catch (e) {
        console.log("错误:" + e);
        this.$utils.hideLoading();
      }
    },
    select (row) {
      this.modelData.forEach((e) => this.$set(e, "selected", false));
      row.selected = true;
      this.getDetail(row.referralConId);
    },
  },
};
</script>

<style lang="scss">
.fix-btn {
  width: 44px;
  height: 44px;
  background: rgba(63, 102, 239, 0.08);
  box-shadow: 0px 4 8px 0px rgba(7, 23, 50, 0.12);
  border: 2px solid #3f66ef;
  position: absolute;
  top: 43%;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  &.left {
    transform: rotate(180deg);
    left: 4px;
  }
  &.right {
    right: 4px;
  }
}
.net-relation {
  background: #f2f2f2;
  padding-bottom: 24px;
  .net-tip {
    padding-left: 20px;
    padding-top: 24px;
    font-weight: 400;
    font-size: 24px;
    color: #6a6d75;
  }
  .custemer-children {
    display: flex;
    justify-content: center;
    position: relative;
    .custemer-children-p {
    }
  }
  .arrow-up {
  }
  .left-line {
    height: 35px;
    flex: 1;
    border-top: 3px solid rgba(148, 168, 236, 0.32);
  }
  .right-line {
    flex: 1;
    height: 35px;
    border-top: 3px solid rgba(148, 168, 236, 0.32);
  }
  .down-line {
    // width: 2px;
    height: 35px;
    border: 3px solid rgba(148, 168, 236, 0.32);
  }
  .custemer-p {
    display: flex;
    justify-content: center;
    margin-top: 96px;
  }
  .parent-custemer {
    color: #59a2ff;
    font-size: 68px;
  }
  .custemer {
    box-shadow: 2px 2px 2px 2px #eee;
    &.custemer-h {
      width: 200px;
      height: 184px;
    }
    &.w9{
        width: 150px!important;
    }
    &.custemer-c {
      width: 168px;
      height: 268px;
      text-align: center;
      margin: 0 5px;
      .success {
        width: 104px;
        height: 40px;
        background: #e8f8ee;
        border-radius: 8px;
        color: #00984b;
        line-height: 40px;
        vertical-align: middle;
      }
      .error {
        width: 104px;
        height: 40px;
        background: #fff4f2;
        border-radius: 8px;
        line-height: 40px;
        color: #f22232;
        vertical-align: middle;
      }
      &.selected {
        border: 3px solid rgb(119, 151, 246);
      }
    }
    .custemer-icon {
      text-align: center;
      font-size: 56px;
      padding-top: 12px;
    }
    .font-size-44 {
      font-size: 44px;
    }
    width: 168px;
    height: 268px;
    background: #f8fafe;
    border-radius: 24px;
    &.active {
      border: 2px solid;
      border-image: linear-gradient(
          135deg,
          rgba(63, 101, 233, 1),
          rgba(119, 151, 246, 1),
          rgba(72, 108, 236, 1)
        )
        2 2;
    }
    .custemer-name {
      font-weight: 600;
      font-size: 28px;
      color: #333333;
      line-height: 36px;
      text-align: center;
      padding-bottom: 8px;
      white-space: nowrap; /* 防止文本换行 */
      overflow: hidden; /* 隐藏超出部分 */
      text-overflow: ellipsis; /* 显示省略号 */
      padding-left: 8px;
    }
    .custemer-count {
      font-weight: 400;
      font-size: 24px;
      color: #9ea1ae;
      line-height: 32px;
      text-align: center;
    }
  }
  .custemer-detail {
    margin: 0 auto;
    width: 560px;
    min-height: 532px;
    background: #ffffff;
    box-shadow: 4px 8 20px 0px rgba(47, 34, 15, 0.14);
    border-radius: 12px;
    overflow: hidden;
    .detail {
      margin: 24px 24px 0 24px;
      border-bottom: 1px dashed #cccccc;
      padding-bottom: 16px;
      &:last-child {
        border-bottom: none;
      }
      .title-area {
        display: flex;
        align-items: center;
        padding-top: 4px;
        .border-img {
          width: 6px;
          height: 24px;
          background: #3f66ef;
          border-radius: 4px;
        }
        .title1 {
          font-weight: 600;
          font-size: 28px;
          color: #3f66ef;
          line-height: 40px;
          text-align: left;
          padding: 0 16px;
        }
        .title2 {
          font-weight: 400;
          font-size: 24px;
          color: #94a8ec;
          line-height: 36px;
          text-align: center;
        }
        .title3-count {
          font-weight: 400;
          font-size: 28px;
          color: #3f66ef;
          line-height: 40px;
          text-align: left;
          padding-left: 14px;
        }
      }
      .info-area {
        position: relative;
        display: flex;
        align-items: center;
        padding-top: 16px;
        .text-tips {
          position: absolute;
          top: -142px;
          left: 44px;
          background: rgba(0, 0, 0, 0.9);
          color: #fff;
          padding: 8px;
          width: 420px;
          border-radius: 8px;
          &#iconFlag {
            left: -26px;
            top: -56px;
            width: 382px;
            text-align: center;
          }
          &::after {
            content: ' ';
            width: 0;
            position: absolute;
            left: 50%;
            height: 0;
            bottom: -15px;
            transform: rotate(180deg); /* 居中箭头 */
            border-left: 16px solid transparent; /* 左边框透明 */
            border-right: 16px solid transparent; /* 右边框透明 */
            border-bottom: 16px solid rgba(0, 0, 0, 0.9); /* 下边框颜色，即箭头的颜色 */
          }
        }
        .qr-last {
          min-width: 168px;
        }
        .qr-all {
          width: 315px;
          text-overflow: ellipsis;
          white-space: normal;
          display: -webkit-box;
          overflow: hidden;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }
        .qr-last-content {
        }
        .info-label {
          font-weight: 400;
          font-size: 28px;
          color: #9ea1ae;
          line-height: 40px;
          text-align: left;
        }
        .info-v {
          font-weight: 400;
          font-size: 28px;
          color: #333333;
          line-height: 40px;
          text-align: left;
          padding: 0 16px;
        }
      }
      .down-line2 {
        width: 2px;
        height: 28px;
        background: #dadbe2;
        margin: 0 4px;
      }
    }
  }
}
</style>
