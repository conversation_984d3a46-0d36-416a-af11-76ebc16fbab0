<!--
@createdBy 黄鹏
@date 2024/03/06
@description: ---  申请记录
-->
<template>
    <view class="apply-record">
        <link-auto-list :option="recordOption">
            <template slot-scope="{data,index}">
                <view class="item-con" @tap="gotoChangeApplyItem(data)">
                    <view class="time">{{data.created | date('YYYY-MM-DD')}}</view>
                    <view class="item-row">
                        <view class="item-desc">申请人</view>
                        <view class="item-value">{{data.createdByName}}</view>
                    </view>
                    <view class="item-row">
                        <view class="item-desc">申请类型</view>
                        <view class="item-value">{{data.applyType | lov('APPROVAL_OBJECT_TYPE')}}</view>
                    </view>
                    <view class="app-status"
                          :style="{'background-image': 'url(' + (data.appStatus === 'Pass' ? $imageAssets.statusBgRed : $imageAssets.statusBgOrange) + ')'}">
                        {{ data.appStatus | lov('UPTATE_AUDIT_STATUS') }}
                    </view>
                </view>
            </template>
        </link-auto-list>
    </view>
</template>

<script>
    export default {
        name: "apply-record",
        props: {
            accountItem: {
                type: Object,
                required: true
            }
        },
        data() {
            return {
                recordOption: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/send'
                    },
                    param: {
                        dmpUrl: '/link/fieldTemApp/queryByExamplePage',
                        filtersRaw: [
                            {id: ' businessId', operator: '=', property: ' businessId', value: this.accountItem.id}
                        ]
                    },
                    sortField: 'created',
                    sortOptions: null,
                    // loadOnStart: false,
                    hooks: {
                        beforeLoad(option) {
                        }
                    }
                })
            }
        },
        created() {
            if (!this.$utils.isEmpty(this.accountItem.id)) {
                this.recordOption.methods.reload();
            }
        },
        methods: {
            /**
             * @desc 跳转变更详情
             * <AUTHOR>
             * @date 2022/11/9 16:08
             **/
            gotoChangeApplyItem(item) {
                this.$nav.push('/pages/lj-consumers/account/account-change-apply-page', {
                    data: item,
                    pageFrom: 'AccountItem'
                })
            }
        }
    }
</script>

<style lang="scss">
    .apply-record {
        overflow: hidden;
        margin: 24px;

        .item-con {
            position: relative;
            box-sizing: border-box;
            padding: 24px;
            background-color: white;
            border-radius: 24px;
            margin-bottom: 24px;

            .time {
                font-size: 28px;
                line-height: 56px;
                color: #212223;
                font-weight: 600;
            }

            .item-row {
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-size: 28px;
                line-height: 56px;
                font-weight: 400;

                .item-desc {
                    color: #999;
                }

                .item-value {
                    flex: 1;
                    text-align: right;
                    color: #000;
                    overflow: scroll;
                    white-space: nowrap;
                }
            }

            .app-status {
                position: absolute;
                top: 0;
                right: 0;
                background-size: 100% 100%;
                background-repeat: no-repeat;
                line-height: 48px;
                box-sizing: border-box;
                padding: 0 16px 0 32px;
                color: white;
                font-size: 24px;
            }
        }
    }
</style>
