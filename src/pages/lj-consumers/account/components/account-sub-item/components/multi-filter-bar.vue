<!--
@createdBy 何春霞
@date 2024-06-13
@description: 消费者-生命旅程-tab筛选栏
-->
<template>
    <view class="sub-tabs-con">
        <view class="sub-tabs-scroll">
            <view :class="currentTabs.length > 0 && currentTabs.includes(tab.val) ? 'sub-tab-item-active' : 'sub-tab-item'"
                  v-for="(tab, index) in tabs"
                  :key="index"
                  @tap="tabSwitch(tab, 'top')">{{tab.name}}
            </view>
        </view>
        <view class="all-tab-btn" @tap="showDialog" :style="currentTabs.length > 0 || startTime || endTime ? 'color: #3a64ed' : ''">
            筛选<link-icon icon="icon-shaixuan"></link-icon>
        </view>
        <link-dialog ref="filterDialog"
                     position="bottom"
                     height="75vh"
                     class="dialog-bottom"
                     noPadding
                     v-model="filterDialogFlag"
                     borderRadius="32rpx 32rpx 0 0">
            <view class="model-title">
                <view class="title">筛选</view>
                <view class="iconfont icon-close" @tap="filterDialogFlag = false"></view>
            </view>
            <!--时间-->
            <view class="filter-item">
                <view class="filter-item-title">时间</view>
                <view class="link-filter-item-date">
                    <link-date class="filter-start-date" view="YMD" v-model="startTime" valueFormat="YYYY-MM-DD" displayFormat="YYYY-MM-DD" placeholder="开始时间" :max="endTime"/>
                    <view class="link-filter-separator">至</view>
                    <link-date class="filter-end-date" view="YMD" v-model="endTime" valueFormat="YYYY-MM-DD" displayFormat="YYYY-MM-DD" placeholder="结束时间" :min="startTime"/>
                </view>
            </view>
            <!--节点类型-->
            <view class="filter-item">
                <view class="filter-item-title">节点类型</view>
                <view class="filter-item-content">
                <!-- 点击到的背景为浅蓝色，字体为蓝色-->
                    <view :class="currentTabs.length > 0 && currentTabs.includes(tab.val) ? 'filter-item-content-item-active' : 'filter-item-content-item'"
                          v-for="(tab, index) in tabs"
                          :key="index"
                          @tap="tabSwitch(tab, 'bottom')">{{tab.name}}
                    </view>
                </view>
            </view>
            <view class="link-dialog-foot-custom">
                <link-button slot="foot" style="width: 45vw" mode="stroke" @tap="reset">重置</link-button>
                <link-button slot="foot" style="width: 45vw" mode="fill" @tap="confirm">确定</link-button>
            </view>
        </link-dialog>
<!--        <view v-else class="sub-tabs">-->
<!--            <view :class="tab.val.includes(currentTabs) ? 'sub-tab-item-active' : 'sub-tab-item'"-->
<!--                  :style="tab.val.includes(currentTabs) ? 'background-color: #3a64ed' : ''"-->
<!--                  v-for="(tab, index) in tabs"-->
<!--                  :key="index"-->
<!--                  @tap="tabSwitch(tab)">{{tab.name}}-->
<!--            </view>-->
<!--        </view>-->
    </view>
</template>

<script>
import {ComponentUtils} from "link-taro-component";

export default {
    name: "sub-tabs",
    data() {
        return {
            zIndex: ComponentUtils.nextIndex(),
            filterDialogFlag: false,    // 筛选弹窗
            startTime: '',  // 开始时间
            endTime: '',    // 结束时间
        }
    },
    props: {
        tabs: {
            type: Array,
            default: () => []
        },
        currentTabs: {
            type: Array,
            default: () => {
                return [];
            }
        },
        isScroll: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        /**
         * 切换tab
         *  <AUTHOR>
         *  @date 2024-06-20
         *  @param tab 当前选中 type 顶部还是底部
         **/
        async tabSwitch(tab, type) {
            await this.$emit('filterTab', tab.val, this.startTime, this.endTime, type);
        },
        /**
         * 显示筛选弹窗
         *  <AUTHOR>
         *  @date 2024-06-20
         **/
        async showDialog() {
            this.$refs.filterDialog.show()
        },
        /**
         * 重置稍选条件
         * <AUTHOR>
         * @date 2024-06-20
         **/
        async reset() {
            this.startTime = ''
            this.endTime = ''
            await this.$emit('reset')
        },
        /**
         * 确定筛选条件
         * <AUTHOR>
         * @date 2024-06-20
         * @param tab 当前选中
         **/
        async confirm() {
            await this.$emit('getConsumerInfo', this.currentTabs, this.startTime, this.endTime, 'bottom');
            this.filterDialogFlag = false
        }
    }
}
</script>

<style lang="scss">
.sub-tabs-con {
    max-width: calc(100% - 14px);
    padding: 14px 0 14px 14px;
    display: flex;
    .sub-tabs {
        height: 68px;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;

        .sub-tab-item {
            height: 48px;
            font-size: 24px;
            line-height: 44px;
            color: #333333;
            padding: 0 18px;
            border: 1px solid #DDDDDD;
            border-radius: 8px;
            background-color: #E6E6E6;
        }

        .sub-tab-item-active {
            height: 48px;
            font-size: 28px;
            line-height: 49px;
            color: #fff;
            padding: 0 18px;
            border-radius: 8px;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-color: #3a64ed;
        }
    }

    .sub-tabs-scroll {
        width: 100%;
        height: 68px;
        white-space: nowrap;
        overflow: scroll;

        .sub-tab-item {
            width: fit-content;
            height: 48px;
            display: inline-block;
            font-size: 24px;
            line-height: 44px;
            color: #333333;
            padding: 0 18px;
            margin-right: 24px;
            border: 1px solid #DDDDDD;
            border-radius: 8px;
            background-color: #E6E6E6;
        }

        .sub-tab-item-active {
            width: fit-content;
            display: inline-block;
            height: 48px;
            font-size: 24px;
            line-height: 44px;
            color: #fff;
            padding: 0 18px;
            margin-right: 24px;
            border-radius: 8px;
            background-size: 100% 100%;
            background: linear-gradient(to right, #4968e7, #699df7);
        }
    }

    .all-tab-btn{
        width: 120px;
        text-align: right;
        font-size: 28px;
        color: #333333;
        letter-spacing: 0;
        line-height: 28px;
        padding: 10px 12px 0px 12px;
        margin-right: 10px;
    }

    .model-title {
        display: flex;
        margin-left: 24px;

        .title {
            font-size: 32px;
            color: #262626;
            letter-spacing: 0;
            text-align: center;
            line-height: 80px;
            height: 80px;
            width: 85%;
            padding-left: 40px;
        }

        .icon-close {
            color: #BFBFBF;
            font-size: 36px;
            line-height: 80px;
            height: 80px;
        }
    }

    .filter-item{
        padding: 0 24px;
        .filter-item-title{
            font-size: 28px;
            color: #595959;
            letter-spacing: 0;
            line-height: 28px;
            padding: 24px 0;
        }
        .filter-item-content{
            display: flex;
            flex-wrap: wrap;
            height: 460px;
            overflow: auto; /* 添加滚动条 */
            .filter-item-content-item-active {
                font-size: 28px;
                background-color: #f1f5fe;
                color: #3f68ee;
                letter-spacing: 0;
                line-height: 28px;
                padding: 10px 20px;
                margin-right: 20px;
                margin-bottom: 30px;
                border-radius: 8px;
                min-width: 40px;
                text-align: center;
            }
            .filter-item-content-item{
                font-size: 28px;
                color: #5d5d5d;
                letter-spacing: 0;
                line-height: 28px;
                padding: 10px 20px;
                margin-right: 20px;
                margin-bottom: 30px;
                border-radius: 8px;
                background-color: #f8f8f8;
                min-width: 40px;
                text-align: center;
            }
        }

        .link-filter-item-date {
            display: flex;
            align-items: center;
            .filter-start-date {
                flex: 4;
                background-color: #f8f8f8;
                border-radius: 8px;
            }
            .filter-end-date {
                flex: 4;
                background-color: #f8f8f8;
                border-radius: 8px;
            }
            .link-input {
                flex: 1;
                overflow: hidden;

                .link-input-content {
                    width: 100%;

                    input {
                        text-align: center;
                    }
                }
            }

            .link-filter-separator {
                flex: 2;
                text-align: center;
                color: #5d5d5d;
            }
        }
    }

    .link-dialog-foot-custom{
        position: absolute;
        bottom: 30px;
        left: 0;
        right: 0;
        display: flex;
        justify-content: space-around;
        padding: 0px !important;
    }

}
</style>
