<!--
@createdBy 黄鹏
@date 2023/03/01
@description: --- 切换按钮
-->
<template>
    <view class="select-btn-con"
          :style="currentBtn === btns[0].id ? {'background-image': 'url(' + $imageAssets.tabLeftChecked + ')'} : {'background-image': 'url(' + $imageAssets.tabRightChecked + ')'}">
        <view class="btn-item"
              :class="item.id === currentBtn ? 'active' : ''"
              v-for="(item, index) in btns"
              @tap="selectBtn(item.id)">{{item.name}}</view>
    </view>
</template>

<script>
    export default {
        name: "select-button",
        props: {
            btns: {
                type: Array,
                default: () => {
                    return [{name: '本财年', id: 'year'}, {name: '合计', id: 'sum'}]
                }
            },
            currentDimension: {
                type: String,
                default: ''
            }
        },
        data () {
          return{
              currentBtn: ''
          }
        },
        created() {
            this.currentBtn = this.currentDimension || this.btns[0].id;
        },
        watch: {
            currentDimension (val) {
                this.currentBtn = val;
            }
        },
        methods: {
            selectBtn (id) {
                this.currentBtn = id;
                this.$emit('change-btn', id);
            }
        }
    }
</script>

<style lang="scss">
.select-btn-con{
    width: 100%;
    height: 72px;
    border-radius: 24px;
    margin-bottom: 24px;
    background-color: white;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    .btn-item{
        width: 50%;
        height: 72px;
        display: inline-block;
        text-align: center;
        line-height: 72px;
        font-size: 28px;
        font-weight: 600;
        color: #6A6D75;
        &.active{
            color: #fff;
        }
    }
}
</style>
