<template>
    <view class="org-select">
        <link-dialog ref="positionBottom" position="bottom" height="85vh" class="dialog-bottom" noPadding v-model="show">
            <view class="model-title">
                <view class="iconfont icon-left" v-if="!(autoList.list.length>0 && autoList.list[0].id === userInfo.orgId) && searchFlag" @tap="goBackOrg"></view>
                <view class="title" style="padding-left:0;">请选择区域</view>
                <view class="iconfont icon-close" @tap="dialogHide"></view>
            </view>
            <view class="dialog-content" style="height: calc(100% - 44px)">
                <scroll-view scroll-y="true" :style="{'height': 'calc(100% - 75px)'}">
                    <link-auto-list :option="autoList" hideCreateButton :scrollContent="true">
                        <template slot-scope="{data,index}">
                            <view slot="note">
                                <item :key="index" :data=data @tap="gotoItemOrg(data)" style="padding-top: 0;padding-bottom:0">
                                    <link-radio-group v-model="data.tempOrgId">
                                        <item :arrow="false">
                                            <link-checkbox :val=data.id slot="thumb" toggleOnClickItem @tap="tempOrgInfo(data, index)"/>
                                        </item>
                                    </link-radio-group>
                                    <view class="list-item">
                                        {{data.text}}
                                    </view>
                                </item>
                            </view>
                        </template>
                    </link-auto-list>
                </scroll-view>
                <view class="link-dialog-foot-custom">
                    <link-radio-group v-model="selectAllFlag" class="select-all">
                        <item :arrow="false">
                            <link-checkbox val="all" slot="thumb" toggleOnClickItem @tap="selectAll"/>
                            <text slot="title">全选</text>
                        </item>
                    </link-radio-group>
                    <link-button shadow @tap="chooseOrg" label="确定" style="width:100vw"/>
                </view>
            </view>
        </link-dialog>
    </view>
</template>

<script>
export default {
    name: "org-select",
    props: {
        userInfo: {},
        show: false,
        showCompanyArr: ''
    },
    data() {
        const accessGroupOauth = this.$utils.getMenuAccessGroup('','');
        const autoList = new this.AutoList(this, {
            url: {queryByExamplePage: this.$env.appURL + '/link/orgnization/queryByExamplePage'},
            searchFields: ['text'],
            loadOnStart: false,
            param: {
                oauth: 'MY_ORG',
                filtersRaw: []
            },
            hooks: {
                beforeLoad(option) {
                    let listIndex = option.param.filtersRaw.map(item => item.property);
                    if (listIndex.includes('[text]')) {
                        this.searchFlag = false;
                        // var index = listIndex.findIndex((val) => {
                        //   return val == 'parentOrgId'
                        // });
                        // option.param.filtersRaw.splice(index, 1);
                        option.param.filtersRaw = [
                            {id: 'isEffective', property: 'isEffective', value: 'Y'},
                            {id: 'text', property: '[text]', value: this.autoList.option.searchText, operator: 'or like'}
                        ]
                        option.param.oauth = 'MY_ORG'
                    }else{
                        this.searchFlag = true;
                    }
                },
                afterLoad(data) {
                    data.rows.forEach((item) => {
                        const flag = this.selectData.find((val) => item.id === val.id);
                        if(!flag) {
                            item.tempOrgId = null;
                        } else {
                            item.tempOrgId = item.id;
                        }
                    });
                    const checkAllFlag = data.rows.find((item) => !item.tempOrgId);
                    if (!checkAllFlag) this.selectAllFlag = 'all';
                }
            },
            sortOptions: null
        });
        return {
            searchFlag: true,
            tempOrgId: null, //选中的组织
            selectAllFlag: '',   // 全选标记
            accessGroupOauth,
            autoList,
            selectData: [],
            selectCopyData: [],
            orgIdArr: [], //可返回的组织数组
        }
    },
    watch: {
        async show(val){
            this.autoList.option.searchText = "";
            if(val){
                this.selectCopyData = this.$utils.deepcopy(this.selectData);
                let filtersRaw = [{id: 'isEffective', property: 'isEffective', value: 'Y'}];
                if( this.orgIdArr.length === 0 ){
                    filtersRaw.push({id: 'parentOrgId', property: 'parentOrgId', value: this.userInfo.orgId, operator: '='});
                    this.orgIdArr.push({orgId: this.userInfo.orgId, orgName: this.userInfo.orgName, orgType: this.userInfo.orgType});
                } else {
                    const index = this.orgIdArr.length - 1;
                    filtersRaw.push({id: 'parentOrgId', property: 'parentOrgId', value: this.orgIdArr[index].orgId, operator: '='});
                }
                if (this.orgIdArr[this.orgIdArr.length - 1].orgType === 'Company' && !!this.showCompanyArr) {
                    filtersRaw.push({id: 'id', property: 'id', value: this.showCompanyArr, operator: 'in'})
                }
                await this.search(filtersRaw);
            } else {
                this.autoList.list = [];
                this.selectAllFlag = '';
            }
        }
    },
    methods: {
        /**
         * @createdBy 曾宇
         * @date 2023/4/17
         * @methods: resetSelectData
         * @description: 重置选择数据
         **/
        resetSelectData() {
            this.selectData = [];
            this.selectCopyData = [];
        },
        /**
         * @createdBy 曾宇
         * @date 2023/4/10
         * @methods: selectAll
         * @description: 当前页面数据全选
         **/
        selectAll() {
            if(this.selectAllFlag === 'all') {
                this.$nextTick(() => {
                    this.selectAllFlag = '';
                    this.autoList.list.forEach((item) => {
                        item.tempOrgId = null;
                    });
                });
            } else {
                this.$nextTick(() => {
                    this.selectAllFlag = 'all';
                    this.autoList.list.forEach((item) => {
                        item.tempOrgId = item.id;
                        const checkFlag = this.selectData.find((data) => item.id === data.id);
                        if(!checkFlag) this.selectData.push(item);
                    });
                });
            }
        },
        /**
         * @createdBy 曾宇
         * @date 2023/1/16
         * @methods: search
         * @description: 关键字搜索
         **/
        async search(filtersRaw){
            this.autoList.option.param.filtersRaw = filtersRaw;
            if(!this.$utils.isEmpty(this.accessGroupOauth)){
                //2021-07-29配了访问组安全性-查询组织的安全性为访问组安全性
                this.orgOption.option.param.oauth = this.accessGroupOauth;
            }
            await this.autoList.methods.reload();
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/11/9
         * @methods goBackOrg
         * @para
         * @description 返回上一级组织列表
         */
        async goBackOrg(){
            let filtersRaw = [{id: 'isEffective', property: 'isEffective', value: 'Y'}]
            if (this.$utils.isEmpty(this.accessGroupOauth)) {
                if (this.orgIdArr.length === 0) return;
                if (this.orgIdArr.length === 1) {
                    filtersRaw.push({id: 'orgId', property: 'orgId', value: this.userInfo.orgId, operator: '='})
                    filtersRaw.push({id: 'orgType', property: 'orgType', value: this.userInfo.orgType})
                } else {
                    filtersRaw.push({
                        id: 'parentOrgId',
                        property: 'parentOrgId',
                        value: this.orgIdArr[this.orgIdArr.length - 2].orgId,
                        operator: '='
                    })
                    if (this.orgIdArr[this.orgIdArr.length - 2].orgType === 'Company') {
                        if(!!this.showCompanyArr) {
                            filtersRaw.push({id: 'id', property: 'id', value: this.showCompanyArr, operator: 'in'})
                        }
                        filtersRaw.push({id: 'orgType', property: 'orgType', value: 'BranchCompany'})
                    }
                }
                this.orgIdArr.pop()
            }
            await this.search(filtersRaw)
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/11/9
         * @methods gotoItemOrg
         * @para
         * @description 跳转到子组织
         */
        async gotoItemOrg(data){
            if(!this.searchFlag)return;
            let filtersRaw = [
                {id: 'isEffective', property: 'isEffective', value: 'Y'},
                {id: 'parentOrgId', property: 'parentOrgId', value: data.id, operator: '='}
            ]
            if(data.orgType === 'Company') {
                filtersRaw.push({id: 'orgType', property: 'orgType', value: 'BranchCompany'})
                if(!!this.showCompanyArr) {
                    filtersRaw.push({id: 'id', property: 'id', value: this.showCompanyArr, operator: 'in'})
                }
            }
            this.autoList.option.param.filtersRaw = filtersRaw;
            if(!this.$utils.isEmpty(this.autoList.option.param.oauth)){
                delete this.autoList.option.param.oauth;
            }
            let  orgParam = this.$utils.deepcopy(this.autoList.option.param);
            orgParam.onlyCountFlag = true;
            let orgData = await this.$http.post(this.$env.appURL + '/link/orgnization/queryByExamplePage', orgParam);
            if (orgData.rows[0].total === 0){
                this.$message.info(`当前组织下没有下级组织`);
                return
            }
            this.autoList.methods.reload()
            this.orgIdArr.push({orgId: data.id,orgName : data.text, orgType: data.orgType})
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/11/9
         * @methods tempOrgInfo
         * @para
         * @description 存储选中的行信息
         */
        tempOrgInfo(data, index){
            if(Boolean(data.tempOrgId)) {
                this.$nextTick(() => {
                    const checkFlagIndex = this.selectData.findIndex((item) => item.id === data.id);
                    if(checkFlagIndex > -1) this.selectData.splice(checkFlagIndex, 1);
                    data.tempOrgId = null;
                    this.autoList.list[index].tempOrgId = null;
                    this.selectAllFlag = '';
                });
            } else {
                this.$nextTick(() => {
                    data.tempOrgId = data.id;
                    this.autoList.list[index].tempOrgId = data.id;
                    this.selectData.push(this.autoList.list[index]);
                    const checkAllFlag = this.autoList.list.find((item) => !item.tempOrgId);
                    if (!checkAllFlag) this.selectAllFlag = 'all';
                });
            }
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/10/28
         * @methods chooseOrg
         * @para
         * @description 片区弹框、品牌公司弹框确认按钮 (点确定之后的组织，再次打开弹框，会打开所在层级，只勾选没有点确认，则不会)
         */
        chooseOrg(){
            if (this.selectCopyData.length === 0) this.selectCopyData = this.$utils.deepcopy(this.selectData);
            this.$emit('changeOrgFilter', {data: this.selectData, type: 'orgId'});
            this.$emit('update:show', false);
        },
        dialogHide(){
            this.selectData = this.$utils.deepcopy(this.selectCopyData);
            this.$emit('update:show', false);
        }
    }
}
</script>

<style lang="scss">
.org-select{
    .link-dialog-foot-custom{
        width: auto !important;
        display: flex;

        .select-all {
            width: 180px;
        }
    }
    .link-dialog-body{
        position: relative;
    }
    .link-dialog-content {
        border-radius: 48px 48px 0 0;
    }
    .link-auto-list .link-auto-list-top-bar{
        border:none;
        .link-search-input {
            padding: 24px 24px 24px 0;
        }
    }
    .link-item .link-item-body-right{
        margin: 0 24px;
    }
    .link-radio-group{
        width: 70px;
        .link-item{
            padding:24px 24px 24px 0;
            .link-item-thumb{
                padding-right: 0;
            }
            .link-item-icon{
                display:none;
            }
        }
        .link-item-active{
            background-color: #f6f6f6;
        }
    }
    .list-item{
        flex: 1;
    }
    .link-radio-group .link-item:active,.link-item-active{
        background-color: #f6f6f6;
    }
    .link-auto-list-no-more{
        display: none;
    }
    .dialog-bottom{
        .dialog-content{
            padding: 0 20px;
            position: relative;
            //.link-button{
            //  position: absolute;
            //  bottom: 0
            //}
        }
        .model-title {
            display: flex;
            .title {
                font-family: PingFangSC-Regular;
                font-size: 32px;
                color: #212223;
                letter-spacing: 0;
                font-weight: 400;
                text-align: center;
                line-height: 112px;
                height: 112px;
                width: 90%;
                padding-left: 0!important;
                //margin-right: 80px;
            }
            .icon-left{
                width: 48px;
                height: 48px;
                margin: 16px;
                color: #BFBFBF;
                font-size: 48px;
                line-height: 48px;
                margin: 32px;
            }
            .icon-close {
                width: 48px;
                height: 48px;
                margin: 16px;
                color: #BFBFBF;
                font-size: 48px;
                line-height: 48px;
                margin: 32px;
            }
        }
    }
}
</style>
