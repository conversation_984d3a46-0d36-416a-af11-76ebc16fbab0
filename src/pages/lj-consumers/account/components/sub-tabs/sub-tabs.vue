<!--
@createdBy 黄鹏
@date 2024/02/04
@description: ---  tab  滚动/不滚动
-->
<template>
    <view class="sub-tabs-con">
        <view v-if="isScroll" class="sub-tabs-scroll">
            <view :class="currentTab === tab.val ? 'sub-tab-item-active' : 'sub-tab-item'"
                  :style="currentTab === tab.val ? {'background-image': 'url(' + $imageAssets.btnCheckedBg + ')'} : ''"
                  v-for="(tab, index) in tabs"
                  :key="index"
                  @tap="tabSwitch(tab)">{{tab.name}}
            </view>
        </view>
        <view v-else class="sub-tabs">
            <view :class="currentTab === tab.val ? 'sub-tab-item-active' : 'sub-tab-item'"
                  :style="currentTab === tab.val ? {'background-image': 'url(' + $imageAssets.btnCheckedBg + ')'} : ''"
                  v-for="(tab, index) in tabs"
                  :key="index"
                  @tap="tabSwitch(tab)">{{tab.name}}
            </view>
        </view>
    </view>
</template>

<script>
    export default {
        name: "sub-tabs",
        data() {
            return {}
        },
        props: {
            tabs: {
                type: Array,
                default: () => []
            },
            currentTab: {
                type: String,
                default: ''
            },
            isScroll: {
                type: Boolean,
                default: false
            }
        },
        methods: {
            tabSwitch(tab) {
                this.$emit('switchTab', tab.val);
            }
        }
    }
</script>

<style lang="scss">
    .sub-tabs-con {
        width: 100%;

        .sub-tabs {
            width: 100%;
            height: 68px;
            display: flex;
            align-items: flex-start;
            justify-content: space-between;

            .sub-tab-item {
                height: 48px;
                font-size: 24px;
                line-height: 44px;
                color: #333333;
                padding: 0 18px;
                // border: 1px solid #DDDDDD;
                border-radius: 8px;
                background-color: #E6E6E6;
            }

            .sub-tab-item-active {
                height: 58px;
                font-size: 28px;
                line-height: 49px;
                color: #fff;
                padding: 0 12px;
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }
        }

        .sub-tabs-scroll {
            width: 100%;
            height: 68px;
            white-space: nowrap;
            overflow-x: scroll;

            .sub-tab-item {
                position: relative;
                width: fit-content;
                height: 58px;
                display: inline-block;
                font-size: 24px;
                line-height: 54px;
                color: #333333;
                padding: 0 21px;
                margin-right: 34px;
                // border: 1px solid #DDDDDD;
                border-radius: 8px;
                background-color: #E6E6E6;
                &:last-child{
                    margin-right: 0!important;
                }
            }

            .sub-tab-item-active {
                width: fit-content;
                display: inline-block;
                height: 64px;
                font-size: 28px;
                line-height: 49px;
                color: #fff;
                padding: 0 12px;
                margin-right: 34px;
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }
        }
    }
</style>
