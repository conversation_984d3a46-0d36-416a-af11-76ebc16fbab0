export default  function consumerListCommon () {
    return {
        data () {
            const pageOauthList = this.pageParam.secMenus || [{securityMode: 'MY_POSTN_ONLY', name: '我的数据'}];
            let pageOauth = 'MY_POSTN_ONLY';
            return {
                pageOauth,
                pageOauthName: '',
                pageOauthList,
            }
        },
        methods: {
            /**
             * @createdBy 曾宇
             * @date 2023/4/11
             * @methods: chooseOauthData
             * @description: 选择页面安全性
             **/
            chooseOauthData() {
                this.$actionSheet(() => (
                    <link-action-sheet title="请选择数据范围" onCancel={() => {}}>
                        {this.pageOauthList.map((item) => {return <link-action-sheet-item label={item.name} onTap={() => this.pageOauthChange(item)}/>})}
                    </link-action-sheet>
                ));
            },
            /**
             * @createdBy 曾宇
             * @date 2023/4/11
             * @methods: pageOauthChange
             * @para: oauth 安全性
             * @description: 页面安全性切换
             **/
            pageOauthChange(oauth) {
                this.autoList.list = [];
                this.pageOauth = oauth.securityMode;
                this.pageOauthName = oauth.name;
                this.autoList.option.param.oauth = oauth.securityMode;
                this.autoList.methods.reload();
            }
        }
    }
}
