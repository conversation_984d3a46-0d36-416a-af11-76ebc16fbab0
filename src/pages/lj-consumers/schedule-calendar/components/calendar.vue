<template>
    <view class="calendar">
        <!-- 月份切换 -->
        <link-date view="YM" valueFormat="YYYY-MM" displayFormat="YYYY年MM月" @change="dateChange">
            <view class="header">
                <view class="month">{{ yearMonthVal }}</view>
                <link-icon icon="icon-pullDown" style="margin-left: 5px"/>
            </view>
        </link-date>
        <!-- 星期标题 -->
        <view class="week-row">
            <view v-for="(week, index) in weeks" :key="index" class="week-cell">{{ week }}</view>
        </view>
        <!-- 日期格子 -->
        <view class="day-grid">
            <view
                v-for="(day, index) in visibleDays"
                :key="index"
                :class="['day-cell',
                    { current: day.isCurrentMonth, today: day.isToday, other: !day.isCurrentMonth,
                    selected: day.fullDate === selectedDate,
                    between: day.fullDate > localStartDate && day.fullDate < localEndDate,
                    'between first': day.fullDate === localStartDate,
                    'between last': day.fullDate === localEndDate
                }]"
                @tap.stop="onSelectDate(day.fullDate)"
            >
                <view class="day-item">
                    {{ day.day }}
                </view>
                <text v-if="day.events" class="event-mark"></text>
            </view>
        </view>
        <view class="toggle-collapse" @tap="toggleCollapse">
            {{ `${isCollapsed ? '展开' : '收起'}` }}
            <link-icon icon="icon-pullDown"/>
        </view>
    </view>
</template>

<script>
export default {
    props: {
        showPrevMonth: {
            type: Boolean,
            default: false
        },
        showNextMonth: {
            type: Boolean,
            default: false
        },
        // 接收事件日期数组
        events: {
            type: Array,
            default: () => []
        },
        // 接收开始日期和结束日期
        startDate: {
            type: String,
            default: ''
        },
        endDate: {
            type: String,
            default: ''
        }
    },
    data() {
        const yearMonthVal = `${String(new Date().getFullYear())}-${this.padZero(new Date().getMonth() + 1)}`
        return {
            year: new Date().getFullYear(),
            month: new Date().getMonth() + 1,
            weeks: ['一', '二', '三', '四', '五', '六', '日'],
            days: [],
            yearMonthVal: yearMonthVal,
            showToday: true,
            selectedDate: '',
            isCollapsed: true,
            currentRow: 0,
            localStartDate: this.startDate,
            localEndDate: this.endDate
        };
    },

    computed: {
        visibleDays() {
            if (!this.isCollapsed) {
                return this.days;
            }
            const start = this.currentRow * 7;
            return this.days.slice(start, start + 7);
        }
    },

    methods: {
        /**
         * @Description: 日期补0
         * @Author: 胡益阳
         * @Date: 2025/2/17
        */
        padZero(date) {
            return String(date).padStart(2, '0');
        },
        /**
         * @Description: 处理日期变化，更新年月值并重新生成日历
         * @Author: 胡益阳
         * @Date: 2025/2/17
        */
        dateChange(val) {
            this.yearMonthVal = val;
            this.generateCalendar(new Date(val))
            this.$emit('changeYearMonth', val)
        },
        /**
         * @Description: 根据传入的日期生成日历数据
         * @Author: 胡益阳
         * @Date: 2025/2/17
        */
        generateCalendar(date) {
            const currentYear = date.getFullYear();
            const currentMonth = date.getMonth() + 1;
            const daysArray = [];

            // 当月信息
            const firstDay = new Date(currentYear, currentMonth - 1, 1);
            const startWeekDay = firstDay.getDay() === 0 ? 7 : firstDay.getDay(); // 周几（1-7）
            const totalDays = new Date(currentYear, currentMonth, 0).getDate(); // 当月总天数

            // 上月信息
            const prevYear = currentMonth === 1 ? currentYear - 1 : currentYear;
            const prevMonth = currentMonth === 1 ? 12 : currentMonth - 1;
            const prevMonthDays = new Date(prevYear, prevMonth, 0).getDate();

            // 填充上月日期
            if (this.showPrevMonth) {
                for (let i = startWeekDay - 1; i > 0; i--) {
                    daysArray.push({
                        day: prevMonthDays - i + 1,
                        isCurrentMonth: false,
                        fullDate: `${prevYear}-${this.padZero(prevMonth)}-${this.padZero(prevMonthDays - i + 1)}`,
                        events: this.events.includes(`${prevYear}-${this.padZero(prevMonth)}-${this.padZero(prevMonthDays - i + 1)}`)
                    });
                }
            } else {
                // 如果不显示上月日期，填充空白日期以对齐当前月份的第一天
                for (let i = 0; i < startWeekDay - 1; i++) {
                    daysArray.push({
                        day: '',
                        isCurrentMonth: false,
                        fullDate: null,
                        events: false
                    });
                }
            }

            // 填充当月日期
            for (let i = 1; i <= totalDays; i++) {
                const fullDate = `${currentYear}-${this.padZero(currentMonth)}-${this.padZero(i)}`;
                daysArray.push({
                    day: i,
                    isCurrentMonth: true,
                    fullDate: fullDate,
                    events: this.events.includes(fullDate)
                });
            }

            // 填充下月日期
            const nextYear = currentMonth === 12 ? currentYear + 1 : currentYear;
            const nextMonth = currentMonth === 12 ? 1 : currentMonth + 1;
            const remain = 42 - daysArray.length;
            if (this.showNextMonth) {
                for (let i = 1; i <= remain; i++) {
                    const fullDate = `${nextYear}-${this.padZero(nextMonth)}-${this.padZero(i)}`;
                    daysArray.push({
                        day: i,
                        isCurrentMonth: false,
                        fullDate: fullDate,
                        events: this.events.includes(fullDate)
                    });
                }
            }

            this.days = daysArray;
            this.calculateCurrentRow(); // 计算当前日期所在的行
        },
        /**
         * @Description: 计算当前日期所在的行
         * @Author: 胡益阳
         * @Date: 2025/2/17
        */
        calculateCurrentRow() {
            const today = new Date();
            const todayFullDate = `${today.getFullYear()}-${this.padZero(today.getMonth() + 1)}-${this.padZero(today.getDate())}`;
            const index = this.days.findIndex(day => day.fullDate === todayFullDate);
            if (index !== -1) {
                this.currentRow = Math.floor(index / 7);
            }
        },
        /**
         * @Description: 选择日期，更新选中日期并触发事件
         * @Author: 胡益阳
         * @Date: 2025/2/17
        */
        onSelectDate(date) {
            if (this.localStartDate && this.localEndDate) {
                // 如果已经选择了开始和结束日期，重置开始日期并清空结束日期
                this.localStartDate = date;
                this.localEndDate = '';
            } else if (!this.localStartDate) {
                this.localStartDate = date;
            } else if (date > this.localStartDate) {
                this.localEndDate = date;
            } else {
                this.localEndDate = this.localStartDate;
                this.localStartDate = date;
            }
            this.$emit('dateRangeSelect', { startDate: this.localStartDate, endDate: this.localEndDate }); // 触发时间段选择事件
        },
        /**
         * @Description: 切换日历的展开和收起状态
         * @Author: 胡益阳
         * @Date: 2025/2/17
        */
        toggleCollapse() {
            this.isCollapsed = !this.isCollapsed;
        }
    },

    mounted() {
        const today = new Date();
        this.generateCalendar(today);
    },

    watch: {
        startDate(newVal) {
            this.localStartDate = newVal;
        },
        endDate(newVal) {
            this.localEndDate = newVal;
        },
        events(newVal) {
            if (newVal && this.yearMonthVal) {
                this.generateCalendar(new Date(this.yearMonthVal));
            }
        }
    }
};
</script>

<style lang="scss">
.calendar {
    background: #fff;
}

.header {
    display: flex;
    align-items: center;
    padding: 20rpx 45px;
    font-size: 32rpx;
}

.month {
    font-weight: bold;
}

.week-row {
    display: flex;
    padding: 0 16px;
    border-bottom: 1px solid #F4F5FB;
}

.week-cell {
    flex: 1;
    text-align: center;
    padding: 20rpx 0;
    font-size: 28rpx;
    color: #B4B5BC;
}

.day-grid {
    display: flex;
    flex-wrap: wrap;
    padding: 24px 16px 20px 16px;
}

.toggle-collapse {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 20px;
    font-size: 28px;
    color: #999999;
}

.day-cell {
    flex: 0 0 14.2857142857%;
    font-size: 28px;
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: relative;
}

.day-item {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 300ms;
}

.day-cell.current .day-item  {
    color: #656A7B;
}

.day-cell.other .day-item  {
    color: #eaeaea; /* 非当前月份的日期置灰 */
}

.day-cell.selected .day-item {
    background-color: #2f69f8;
    color: #fff;
    box-shadow: 0 8px 8px 0 rgba(47, 105, 248, 0.3);
    transition: all 300ms;
}

.day-cell.between .day-item {
    background-color: #e0e0e0; // 时间段中间日期的背景颜色
    color: #2f69f8;
    box-shadow: 0 8px 8px 0 rgba(47, 105, 248, 0.1);
    transition: all 300ms;
}

.day-cell.between.first .day-item,
.day-cell.between.last .day-item {
    background-color: #2f69f8;
    color: #fff;
    box-shadow: 0 8px 8px 0 rgba(47, 105, 248, 0.3);
    transition: all 300ms;
}

.event-mark {
    position: absolute;
    bottom: 10px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #3f66ef;
}
</style>
