<template>
    <view class="standard-labels-view">
        <view class="no-labels" v-if="tagGroupsData.length === 0 && statisticData.length === 0">暂无标签</view>
        <view class="labels-view" v-else>
            <view v-if="type.indexOf('label') > -1">
                <line-title title="自定义标签" v-if="type === 'label,statistic'"/>
                <view>
                    <view v-for="(tagGroup, index) in tagGroupsData"
                          :key="tagGroup.tagGroupId"
                          style="width: 100%;">
                        <view class="tag-header">
                            <view class="tag-title">
                                <view class="tag-title-text">{{tagGroup.tagGroupName}}</view>
                            </view>
                        </view>
                        <view class="classify-filter-content">
                            <view class="classify-filter-list">
                                <view v-for="(tagItem, tagIndex) in tagGroup.valueList"
                                      :key="tagIndex"
                                      class="classify-filter-item">
                                    {{tagItem.tagName}}
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view v-if="type.indexOf('statistic') > -1">
                <line-title title="标签" v-if="type === 'label,statistic'"/>
                <view class="third-level-data">
                    <view v-for="(tagGroup, index) in statisticData"
                          :key="tagGroup.fieldEnName"
                          style="width: 100%;">
                        <view class="tag-header">
                            <view class="tag-title">
                                <view class="tag-title-text">{{tagGroup.fieldCnName}}</view>
                            </view>
                        </view>
                        <view class="classify-filter-content">
                            <view class="classify-filter-list">
                                <view v-for="(tagItem, tagIndex) in tagGroup.tags"
                                      :key="tagIndex"
                                      class="classify-filter-item">
                                    {{tagItem}}
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    import LnkTaps from '../../../core/lnk-taps/lnk-taps'
    import LineTitle from "../../../lzlj/components/line-title";

    export default {
        name: "standard-labels-view-new",
        components: {LnkTaps, LineTitle},
        props: {
            // 查看标签类型
            type: {
                type: String,
                default: 'label'
            },
            // 已选标签id数组
            tagList: {
                type: Array,
                default: () => []
            },
            // 已选标签对象数组
            tagGroups: {
                type: Array,
                default: () => []
            },
            // 消费者手机号码
            phoneNumber: {
                type: String,
                default: ''
            },
            // 消费者所属公司id
            companyId: {
                type: String,
                default: ''
            },
            // 消费者id
            consumerId: {
                type: String,
                default: ''
            }
        },
        data() {
            const userInfo = this.$taro.getStorageSync('token').result;
            return {
                userInfo,
                tagGroupsData: [],   // 一级标签
                matchData: [],
                statisticDataAll: [],
                statisticData: [],
                pageSize: 20,
                currentPage: 1
            }
        },
        watch: {
            tagList (val) {
                if (val) {
                    this.treeFilter();
                }
            }
        },
        async created() {
            if (this.tagGroups.length > 0) {
                // 审批页面标签展示
                this.processTagGroups()
            } else {
                // 消费者详情页面标签展示
                await this.queryAccntTagGroup()
            }
            if (this.type !== 'label') {
                await this.getCnName()
                // 统计值标签展示
                await this.queryStatistic()
            }
        },
        methods: {
            /**
             @desc: 将 tagGroups 转为树形结构
             @author: wangbinxin
             @date 2023-09-20 15-42
             **/
            processTagGroups() {
                const obj = {};
                this.tagGroups.forEach((item) => {
                    if (obj[item.tagGroupId]) {
                        obj[item.tagGroupId].valueList.push({tagId: item.tagId, tagName: item.tagName})
                    } else {
                        obj[item.tagGroupId] = {
                            tagGroupId: item.tagGroupId,
                            tagGroupName: item.tagGroupName.indexOf('>') !== -1 ? item.tagGroupName.slice(item.tagGroupName.lastIndexOf('>') + 1) : item.tagGroupName,
                            valueList: [{tagId: item.tagId, tagName: item.tagName}]
                        }
                    }
                })
                this.tagGroupsData = Object.values(obj);
            },
            /**
             @param arr 全量标签租数据
             @desc: 对树形数据进行筛选，只展示当前tagList里的数据
             @author: wangbinxin
             @date 2023-09-16 10-30
             **/
            treeFilter() {
                const showTagGroup = [];
                this.tagGroupsAll.forEach((tagGroup) => {
                    const valueList = tagGroup.valueList.filter((tag) => this.tagList.includes(tag.tagId));
                    if (valueList?.length > 0) {
                        showTagGroup.push({
                            tagGroupId: tagGroup.tagGroupId,
                            tagGroupName: tagGroup.tagGroupName,
                            valueList: valueList
                        })
                    }
                })
                this.tagGroupsData = showTagGroup;
            },
            /**
             @desc: 查询统计标签
             @author: wangbinxin
             @date 2023-09-25 11-04
             **/
            async queryStatistic() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmpSr/queryTagDetailInformation', {
                    // dmpSrUrl: '/link/consumerList/queryTagDetailInformation',
                    // // row_id: '0181f1183dd742baa62f6ce9df13dc29_58933208015699968',
                    // jt_phone: this.phoneNumber,
                    // cms_company_id: this.companyId
                    id: this.consumerId
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$message.error(`查询标签数据失败：${response.result}`);
                    }
                });
                if (data.success && data.rows) {
                    this.matchData.forEach((item) => {
                        if (data.rows[item.fieldEnName] || data.rows[item.fieldEnName] === 0) {
                            item.tags = String(data.rows[item.fieldEnName]).split(',');
                        } else {
                            item.tags = '';
                        }
                    })
                    this.statisticDataAll = this.matchData.filter((item) => item.tags);
                    this.statisticData = this.statisticDataAll.slice(0, this.pageSize * this.currentPage);
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/01/04
             * @methods: loadMore
             * @para:
             * @description: 分页加载数据
             **/
            loadMore () {
                console.log(1234)
                this.currentPage += 1;
                this.statisticData = this.statisticDataAll.slice(0, this.pageSize * this.currentPage);
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/01/03
             * @methods: getCnName
             * @para:
             * @description: 获取标签中英文对应数据
             **/
            async getCnName() {
                const tableId = await this.$utils.getCfgProperty('TAG_MATCH');
                const data = await this.$http.post(this.$env.appURL + '/action/link/cirSelSchdField/queryByExamplePage', {
                    filtersRaw: [
                        {id: 'deleteFlag', property: 'deleteFlag', value: 'N'},
                        {id: 'tableId', property: 'tableId', value: tableId},
                        {id: 'labelType', property: 'labelType', value: '[1, 3, 7]', operator: 'in'}
                    ],
                    rows: 10000
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$message.error(`查询标签中英文对应数据失败：${response.result}`);
                    }
                });
                if (data.success) {
                    this.matchData = data.rows.map((item) => {
                        return {
                            fieldCnName: item.fieldCnName,
                            fieldEnName: item.fieldEnName
                        }
                    })
                }
            },
            /**
             * @desc 查询标签组数据
             * <AUTHOR>
             * @date 2023/9/6 10:06
             **/
            async queryAccntTagGroup() {
                const data = await this.$http.post(this.$env.appURL + '/link/tagOrgTree/queryTreeTag', {
                    filtersRaw: [
                        {id: 'deleteFlag', property: 'deleteFlag', value: 'N'},
                        {id: 'publishStatus', property: 'publishStatus', value: '2'},
                        {id: 'labelType', property: 'labelType', value: '7'}
                    ]
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$message.error(`查询标签组数据失败：${response.result}`);
                    }
                });
                if (data.success) {
                    // 取出所有最后一层的层级id
                    const tagGroups = [];
                    const fn = (arr) => {
                        arr.forEach((item) => {
                            if (item.children && item.children.length > 0) {
                                fn(item.children);
                            } else {
                                tagGroups.push({
                                    tagGroupId: item.tagGroupId,
                                    tagGroupName: item.tagGroupName,
                                    isMultipleValue: item.isMultipleValue
                                });
                            }
                        })
                    }
                    fn(data.rows);
                    // 根据层级id查询标签值
                    this.getTags(tagGroups);
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/01/02
             * @methods: getTags
             * @para:
             * @description: 根据标签组id获取标签值数据
             **/
            async getTags(tagGroups) {
                const tagGroupIds = tagGroups.map((item) => item.tagGroupId);
                const data = await this.$http.post(this.$env.appURL + '/link/crowdTagItem/queryByExamplePage', {
                    filtersRaw: [{
                        id: 'tagGroupId',
                        property: 'tagGroupId',
                        value: `[${tagGroupIds.join(',')}]`,
                        operator: 'in'
                    }, {
                        id: 'validFlag',
                        property: 'validFlag',
                        value: 'Y',
                        operator: '='
                    }],
                    rows: 1000
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$message.error(`查询标签值数据失败：${response.result}`);
                    }
                });
                if (data.success) {
                    const tags = data.rows.map((row) => {
                        return {
                            tagName: row.tagValueName,
                            tagId: row.tagValueId,
                            tagGroupId: row.tagGroupId
                        }
                    })
                    tagGroups.forEach((tagGroup) => {
                        tags.forEach((tag) => {
                            if (tag.tagGroupId === tagGroup.tagGroupId) {
                                if (tagGroup.valueList) {
                                    tagGroup.valueList.push(tag);
                                } else {
                                    tagGroup.valueList = [tag];
                                }
                            }
                        })
                    })
                    this.tagGroupsAll = tagGroups;
                    this.treeFilter();
                }
            }
        }
    }
</script>

<style lang="scss">
    .standard-labels-view {
        /*width: 100%;*/
        /*height: 100vh;*/
        background-color: white;

        .no-labels{
            text-align: center;
            background-color: #f2f2f2;
            line-height: 80px;
            color: #555;
        }

        .labels-view {
            padding: 0 24px;

            .line-title {
                margin-bottom: 20px;

                .stair-title {
                    margin-left: 16px;
                    font-size: 32px;
                    font-weight: bold;
                    font-family: "Microsoft YaHei";
                    color: #262626;
                    line-height: 32px;
                    white-space: nowrap;
                    min-width: 40%;
                    width: auto;
                }
            }

            .third-level-data {
                padding-bottom: calc(env(safe-area-inset-bottom) + 24px);
            }

            .other-bottom {
                padding-bottom: calc(env(safe-area-inset-bottom) + 80px + 24px + 40px) !important;
            }

            .tag-header {
                display: block;
                margin-bottom: 24px;
                margin-top: 24px;
                width: 100%;

                .tag-title {
                    display: flex;
                    margin-left: 24px;

                    .required {
                        color: red;
                        font-size: 32px;
                        line-height: 64px;
                        margin-right: 4px;
                    }

                    .tag-title-text {
                        font-size: 28px;
                        color: #333333;
                        line-height: 24px;
                        font-weight: 600;
                    }

                    .link-icon {
                        font-size: 32px;
                        color: #CCCCCC;
                        margin: 8px 0 0 8px;
                    }
                }
            }

            .classify-filter-content {
                /*padding-bottom: 16px;*/
                display: block;
                width: 100%;

                .classify-filter-list {
                    display: flex;
                    align-items: center;
                    flex-wrap: wrap;

                    .classify-filter-item {
                        min-width: 120px;
                        min-height: 48px;
                        box-sizing: border-box;
                        margin: 0 20px 12px 0;
                        font-size: 24px;
                        color: #333333;
                        letter-spacing: 0;
                        text-align: center;
                        line-height: 48px;
                        font-weight: 400;
                        background: #f8f8f8;
                        border-radius: 8px;
                        padding: 0 10px;
                    }

                    .label-checked {
                        min-width: 120px;
                        min-height: 48px;
                        box-sizing: border-box;
                        margin: 0 20px 12px 0;
                        font-size: 20px;
                        color: #3F66EF;
                        letter-spacing: 0;
                        text-align: center;
                        line-height: 48px;
                        font-weight: 400;
                        background: #F0F5FF;
                        border-radius: 8px;
                    }
                }
            }
        }

        .line-title {
            margin-bottom: 24px;
        }

        .standard-labels-content {
            width: 100%;
            background: #FFFFFF;
            border: 1px solid rgba(255, 255, 255, 1);
            border-radius: 16px;
            margin-bottom: 24px;

            .tag-header {
                background-image: linear-gradient(180deg, #EFF3FF 0%, rgba(238, 241, 252, 0.00) 83%);
                border-radius: 16px;
                height: 96px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 24px;

                .tag-title-text {
                    font-size: 32px;
                    color: #333333;
                    font-weight: 600;
                }
            }
        }

        .classify-filter-content {
            display: block;
            width: calc(100% - 48px);
            padding: 0 24px;

            .scroll-view-data {
                white-space: nowrap;

                .secondary-labels {
                    width: 100%;
                    display: flex;
                    height: 80px;

                    .secondary-labels-default {
                        border: 1px solid rgba(221, 221, 221, 1);
                        border-radius: 30px;
                        height: 40px;
                        font-size: 24px;
                        color: #333333;
                        text-align: center;
                        font-weight: 400;
                        line-height: 40px;
                        width: auto;
                        padding: 6px 24px;
                        margin-right: 20px;
                    }

                    .secondary-labels-active {
                        color: #2F54EB !important;
                        border-color: #2F54EB !important;
                    }
                }
            }

            .tag-group {
                .tag-item-title {
                    font-size: 28px;
                    color: #333333;
                    line-height: 44px;
                    font-weight: 500;
                }

                .tag-list {
                    display: flex;
                    flex-wrap: wrap;

                    .tag-list-item {
                        padding: 0 16px;
                        height: 56px;
                        width: auto;
                        min-width: 108px;
                        box-sizing: border-box;
                        margin: 0 0 16px 24px;
                        font-size: 24px;
                        letter-spacing: 0;
                        text-align: center;
                        line-height: 56px;
                        font-weight: 400;
                        background: #f8f8f8;
                        border-radius: 8px;
                        color: #666666;
                    }
                }
            }
        }
    }
</style>
