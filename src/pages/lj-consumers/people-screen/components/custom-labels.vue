<template>
    <view class="custom-labels">
        <view class="labels-box">
            <view class="labels-content">
                <view class="labels-tips">
                    <view class="condition">请输入圈选条件</view>
                    <view class="save" @tap="saveCondition">保存至圈选条件</view>
                </view>
                <view class="labels-choice-box">
                    <view class="labels-choice-item">
                        <view class="labels-item-title">标签选择</view>
                        <view class="picker-content" @tap="pickerChange('label')">
                            <view class="picker-data">
                                <view class="picker" v-if="currentData.label">{{currentData.label}}</view>
                                <view class="picker no-data" v-else>请选择</view>
                            </view>
                            <view class="iconfont icon-right"></view>
                        </view>
                    </view>
                    <view class="labels-choice-item">
                        <view class="labels-item-title">圈选条件</view>
                        <view class="picker-content" @tap="pickerChange('condition')">
                            <view class="picker-data">
                                <view class="picker" v-if="currentCondition.label">{{currentCondition.label}}</view>
                                <view class="picker no-data" v-else>请选择</view>
                            </view>
                            <view class="iconfont icon-right"></view>
                        </view>
                    </view>
                </view>
                <view class="labels-choice-box-info">
                    <view class="labels-item-title">圈选值</view>
                    <view class="picker-content">
                        <!--单行文本、加密文本-->
                        <link-input block style="text-align: left;background: #f2f2f2;"
                                    :placeholder="'请输入'"
                                    v-model="labelItem.firstData" v-if="currentData.dataType === 'SinglelineText' || currentData.dataType === 'BZencryption' || currentData.dataType === 'BZtext' || currentData.dataType === 'Default'"/>
                        <!--多行文本-->
                        <link-textarea block v-model="labelItem.firstData"
                                       :placeholder="'请输入'"
                                       style="text-align: left;background: #f2f2f2;"
                                       v-if="currentData.dataType === 'MultilineText'"/>
                        <link-number block v-model="labelItem.firstData"
                                     :placeholder="'请输入'"
                                     style="text-align: left;background: #f2f2f2;"
                                     v-if="(currentData.dataType === 'Number' || currentData.dataType === 'BZdigit') && currentCondition.label !== '介于'"/>
                        <link-date view="YMDHms"
                                   valueFormat="YYYY-MM-DD HH:mm:ss"
                                   displayFormat="YYYY-MM-DD HH:mm:ss"
                                   v-model="labelItem.firstData"
                                   v-if="(currentData.dataType === 'DateAndTime' || currentData.dataType === 'BZtime') && currentCondition.label !== '介于'"/>
                        <link-date view="YMD"
                                   valueFormat="YYYY-MM-DD"
                                   displayFormat="YYYY-MM-DD"
                                   v-model="labelItem.firstData"
                                   v-if="(currentData.dataType === 'Date' || currentData.dataType === 'BZdate') && currentCondition.label !== '介于'"/>
                        <view v-if="currentCondition.label === '介于'" class="show-two">
                            <!--数字框-->
                            <link-number block v-model="labelItem.firstData"
                                         :placeholder="'请输入'"
                                         class="left-data"
                                         style="text-align: left;background: #f2f2f2;"
                                         v-if="currentData.dataType === 'Number' || currentData.dataType === 'BZdigit'"/>
                            <!--日期、日期时间-->
                            <link-date view="YMD"
                                       valueFormat="YYYY-MM-DD"
                                       displayFormat="YYYY-MM-DD"
                                       class="left-data"
                                       v-model="labelItem.firstData"
                                       v-if="currentData.dataType === 'Date' || currentData.dataType === 'BZdate'"/>
                            <link-date view="YMDHms"
                                       valueFormat="YYYY-MM-DD HH:mm:ss"
                                       displayFormat="YYYY-MM-DD HH:mm:ss"
                                       class="left-data"
                                       v-model="labelItem.firstData"
                                       v-if="currentData.dataType === 'DateAndTime' || currentData.dataType === 'BZtime'"/>
                            <view class="bot">-</view>
                            <link-number block v-model="labelItem.secondData"
                                         :placeholder="'请输入'"
                                         class="right-data"
                                         style="text-align: left;background: #f2f2f2;"
                                         v-if="currentData.dataType === 'Number' || currentData.dataType === 'BZdigit'"/>
                            <link-date view="YMD"
                                       valueFormat="YYYY-MM-DD"
                                       displayFormat="YYYY-MM-DD"
                                       v-model="labelItem.secondData"
                                       class="right-data"
                                       v-if="currentData.dataType === 'Date' || currentData.dataType === 'BZdate'"/>
                            <link-date view="YMDHms"
                                       valueFormat="YYYY-MM-DD HH:mm:ss"
                                       displayFormat="YYYY-MM-DD HH:mm:ss"
                                       class="right-data"
                                       v-model="labelItem.secondData"
                                       v-if="currentData.dataType === 'DateAndTime' || currentData.dataType === 'BZtime'"/>
                        </view>
                        <!--地址框-->
                        <link-address
                            v-if="(currentData.dataType === 'ProvincialCity' || currentData.dataType === 'BZPAC') && currentData.field === 'province'"
                            block
                            view="p"
                            :placeholder="'请选择'"
                            style="text-align: left;background: #f2f2f2;"
                            :province.sync="labelItem.province"
                        />
                        <link-address
                            v-if="(currentData.dataType === 'ProvincialCity' || currentData.dataType === 'BZPAC') && currentData.field === 'city'"
                            block
                            view="c"
                            :placeholder="'请选择'"
                            style="text-align: left;background: #f2f2f2;"
                            :province.sync="labelItem.province"
                            :city.sync="labelItem.city"
                        />
                        <link-address
                            v-if="(currentData.dataType === 'ProvincialCity' || currentData.dataType === 'BZPAC') && currentData.field === 'county'"
                            block
                            view="d"
                            :placeholder="'请选择'"
                            style="text-align: left;background: #f2f2f2;"
                            :province.sync="labelItem.province"
                            :city.sync="labelItem.city"
                            :district.sync="labelItem.district"
                        />
                        <!--值列表-->
                        <link-lov block
                                  :type="currentData.lovType"
                                  :placeholder="'请选择'"
                                  v-model="labelItem.firstData"
                                  :multiple="currentData.dataType === 'BZmultipleChoice'"
                                  style="text-align: left;background: #f2f2f2;"
                                  v-if="showLovFlag && currentData.lovType && (currentData.dataType === 'BZradio' || currentData.dataType === 'BZmultipleChoice') && currentData.field !== 'type' && currentData.field !== 'loyaltyLevel' && currentData.field !== 'acctLevel'"/>
                        <link-lov block
                                  :type="currentData.lovType"
                                  :placeholder="'请选择'"
                                  v-model="labelItem.firstData"
                                  :multiple="currentData.dataType === 'BZmultipleChoice'"
                                  :parentVal="userInfo.coreOrganizationTile.brandCompanyCode" parentType="BRAND_COM_NAME"
                                  style="text-align: left;background: #f2f2f2;"
                                  v-if="showLovFlag && (currentData.dataType === 'BZradio' || currentData.dataType === 'BZmultipleChoice') && currentData.field === 'acctLevel'"/>
                        <!--单选、多选-->
                        <link-select block v-model="labelItem.firstData" v-if="(currentData.dataType === 'BZradio' || currentData.dataType === 'BZmultipleChoice') && currentData.field === 'type'" :placeholder="'请选择'" :multiple="currentData.dataType === 'BZmultipleChoice'">
                            <link-select-option v-for="(data,index) in classifyItemList" :label="data.name" :val="data.type" :key="index"/>
                        </link-select>
                        <link-select block v-model="labelItem.firstData" v-if="(currentData.dataType === 'BZradio' || currentData.dataType === 'BZmultipleChoice') && currentData.field === 'loyaltyLevel'" :placeholder="'请选择'" :multiple="currentData.dataType === 'BZmultipleChoice'">
                            <link-select-option v-for="(data,index) in loyaltyList" :label="data.name" :val="data.val" :key="index"/>
                        </link-select>
                        <!--多选-->
<!--                        <link-select block v-model="labelItem.firstData" v-if="currentData.dataType === 'Checkbox' || currentData.dataType === 'BZmultipleChoice'" multiple :placeholder="'请选择'">-->
<!--                            <link-select-option v-for="(data,index) in currentData.choices" :label="data.name" :val="data.name" :key="index"/>-->
<!--                        </link-select>-->
                    </view>
                </view>
            </view>
            <view class="label-result">
                <view class="title-line-info">
                    <view class="title">圈选条件</view>
                    <view class="operator" v-if="operatorFlag">
                        <link-icon icon="mp-trash" @tap="deleteTag"/>
                    </view>
                    <view class="operator" v-else>
                        <link-button mode="text" label="全部删除" @tap="deleteAll"/>
                        <view class="line"></view>
                        <link-button mode="text" label="完成" @tap="cancel"/>
                    </view>
                </view>
                <view class="label-result-content">
                    <view class="label-result-item" v-for="(item,index) in conditionShowList" :key="index" :class="{'label-result-item-active': currentIndex === index}">
                       <view class="text-info" @tap="checkCurrentData(item,index)">{{item.showData}}</view><link-icon icon="mp-close" v-if="showDeleteIcon" @tap="deleteTagItem(item,index)"/>
                    </view>
                </view>
            </view>
        </view>
        <link-sticky>
            <link-button block mode="stroke" @tap="saveData('save')">保存</link-button>
            <link-button  block @tap="saveData('saveAndGenerate')">保存并生成</link-button>
        </link-sticky>
        <link-dialog :noPadding="true"
                     class="dialog-bottom"
                     v-model="dialogFlag"
                     :height="dialogHeight"
                     position="bottom"
                     borderRadius="32rpx 32rpx 0 0">
            <view class="model-title">
                <view class="title">{{dialogLabelTitle}}</view>
                <view class="iconfont icon-close" @tap="dialogFlag = false"></view>
            </view>
            <scroll-view scroll-y="true" :style="{'height': 'calc(100% - 121px)'}">
                <link-radio-group v-if="inputType === 'label'">
                    <item :arrow="false" v-for="(item,index) in customLabelData" :key="index">
                        <link-checkbox :val="item.field" slot="thumb" toggleOnClickItem @tap="chooseData(item)"/>
                        <text slot="title">{{item.label}}</text>
                    </item>
                </link-radio-group>
                <link-radio-group v-else>
                    <item :arrow="false" v-for="(item,index) in conditionOperatorList[currentData.dataType]" :key="index">
                        <link-checkbox :val="item.operator" slot="thumb" toggleOnClickItem @tap="chooseData(item)"/>
                        <text slot="title">{{item.label}}</text>
                    </item>
                </link-radio-group>
            </scroll-view>
            <view class="link-dialog-foot-custom">
                <link-button shadow @tap="confirm" label="确定" style="width:100vw"/>
            </view>
        </link-dialog>
        <link-dialog :noPadding="true"
                     class="dialog-bottom"
                     v-model="dialogSaveFlag"
                     height="55vh"
                     position="bottom"
                     borderRadius="32rpx 32rpx 0 0">
            <view class="model-title">
                <view class="title">{{ dialogTitle }}</view>
                <view class="iconfont icon-close" @tap="dialogSaveFlag = false"></view>
            </view>
            <scroll-view scroll-y="true" :style="{'height': 'calc(100% - 121px)'}">
                <link-form :value="formData" ref="crowdFormData" :rules="formRules">
                    <link-form-item label="人群包名称" required field="crowdSelection">
                        <link-input type="text" v-model="formData.crowdSelection" placeholder="请输入且不超过30字"/>
                    </link-form-item>
                    <link-form-item label="人群包描述"  vertical style="border-bottom: 1px solid rgb(247,247,247);" required field="description">
                        <link-textarea v-model="formData.description" :nativeProps="{maxlength:200}"/>
                    </link-form-item>
                </link-form>
            </scroll-view>
            <view class="link-dialog-foot-custom">
                <link-button shadow @tap="saveAllData" label="确定" style="width:100vw"/>
            </view>
        </link-dialog>
    </view>
</template>
<script>
import LineTitle from "../../../lzlj/components/line-title.vue";
import {ROW_STATUS} from "../../../../utils/constant";

export default {
    name: 'custom-labels',
    components: {LineTitle},
    props: {
        allData: {
            type: Object,
            default: {}
        },
        dialogTitle: {
            type: String,
            default: '新建人群包'
        }
    },
    data () {
        const userInfo = this.$taro.getStorageSync('token').result;
        return  {
            showLovFlag: false,
            loyaltyList: [],
            classifyItemList: [],
            isGuoJiao: false,                      // 是否国窖
            userInfo,
            lscspJson: '',
            saveType: '',                          // 保存类型：保存、保存并生成
            formData: {},
            formRules: {
                description: this.Validator.required(),
                crowdSelection: [this.Validator.required(), this.Validator.len({max: 30, maxMsg: '人群包名称字数超出长度30字！请检查'})]
            },
            dialogSaveFlag: false,
            currentIndex: null,
            conditionShowList: [],                  // 圈选结果展示
            conditionResultList: [],                // 圈选结果列表
            currentInfo: {},                        // dialog弹窗当前选择的对象
            dialogHeight: '65vh',                   // dialog弹窗默认高度
            inputType: '',                          // dialog弹窗渲染类型
            dialogFlag: false,                      // dialog弹窗是否显示
            dialogLabelTitle: '请选择',                   // dialog弹窗标题
            currentCondition: {},                   // 当前选中的条件
            conditionOperatorList: {                // 圈选条件
                'Default': [{label: '等于', operator: '='}],
                'BZencryption': [{label: '等于', operator: '='}],
                'BZtext': [{label: '等于', operator: 'like'}],
                'BZdigit': [
                        {label: '等于', operator: '='},
                        {label: '大于', operator: '>'},
                        {label: '小于', operator: '<'},
                        {label: '大于等于', operator: '>='},
                        {label: '小于等于', operator: '<='},
                        {label: '介于', operator: '>=,<='}
                    ],
                'Number': [
                    {label: '等于', operator: '='},
                    {label: '大于', operator: '>'},
                    {label: '小于', operator: '<'},
                    {label: '大于等于', operator: '>='},
                    {label: '小于等于', operator: '<='},
                    {label: '介于', operator: '>=,<='}
                ],
                'BZtime': [
                        {label: '等于', operator: '='},
                        {label: '大于', operator: '>'},
                        {label: '小于', operator: '<'},
                        {label: '大于等于', operator: '>='},
                        {label: '小于等于', operator: '<='},
                        {label: '介于', operator: '>=,<='}
                    ],
                'DateAndTime': [
                    {label: '等于', operator: '='},
                    {label: '大于', operator: '>'},
                    {label: '小于', operator: '<'},
                    {label: '大于等于', operator: '>='},
                    {label: '小于等于', operator: '<='},
                    {label: '介于', operator: '>=,<='}
                ],
                'BZdate': [
                        {label: '等于', operator: '='},
                        {label: '大于', operator: '>'},
                        {label: '小于', operator: '<'},
                        {label: '大于等于', operator: '>='},
                        {label: '小于等于', operator: '<='},
                        {label: '介于', operator: '>=,<='}
                    ],
                'Date': [
                    {label: '等于', operator: '='},
                    {label: '大于', operator: '>'},
                    {label: '小于', operator: '<'},
                    {label: '大于等于', operator: '>='},
                    {label: '小于等于', operator: '<='},
                    {label: '介于', operator: '>=,<='}
                ],
                'BZPAC': [{label: '等于', operator: '='}],
                'ProvincialCity':  [{label: '等于', operator: '='}],
                'BZradio': [{label: '等于', operator: '='}],
                'BZmultipleChoice': [{label: '包含', operator: 'IN'}],
                'Radio': [{label: '等于', operator: '='}],
                'Checkbox': [{label: '包含', operator: 'IN'}]
            },
            showDeleteIcon: false,              // 是否展示删除标签图标
            operatorFlag: true,                 // 圈选结果删除图标是否展示
            currentData: {},                    // 当前选中的圈选标签对象
            labelItem: {province: '', city: '', district: '', firstData: '', secondData: ''},
            customLabelData: [],                // 全部圈选标签对象
        }
    },
    async created () {
        this.$utils.showLoading();
        if (this.allData) {
            this.lscspJson = this.allData['lscspJson'] || '{}';
            const tempData = JSON.parse(this.lscspJson);
            if (tempData.qps) {
                this.conditionResultList = tempData.qps;
                this.conditionResultList.forEach((item)=> {
                    if (item.property === 'province') {
                        this.labelItem.province = item.value;
                    }
                    if (item.property === 'city') {
                        this.labelItem.city = item.value;
                    }
                    if (item.property === 'county') {
                        this.labelItem.district = item.value;
                    }
                })
            }
            if (tempData.qpsShow) {
                this.conditionShowList = tempData.qpsShow;
            }
            this.formData = this.allData;
        }
        await this.checkFieldsInfo();
        this.$utils.hideLoading();
    },
    watch: {
        allData: {
            deep: true,
            handler(newVal) {
                this.formData = newVal;
                if (newVal) {
                    this.lscspJson = newVal['lscspJson'] || '';
                }
            }
        }
    },
    methods: {
        /**
         * @desc 查询参数配置
         * <AUTHOR>
         * @date 2022/6/7 17:21
         **/
        async queryCfg() {
            const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', {key: 'GUOJIAO_ACCT_MEMBER_LEVEL'});
            if (data.success) {
                const companyIds = data.value.split(',');
                this.isGuoJiao = companyIds.indexOf(this.userInfo.coreOrganizationTile['l3Id']) !== -1
            }
            const lovData = await this.$lov.getLovByType('ACCT_MEMBER_LEVEL');
            if (this.isGuoJiao) {
                this.loyaltyList = await this.$lov.getLovByParentTypeAndValue({
                    type: 'ACCT_MEMBER_LEVEL',
                    parentType: 'ACCT_MEMBER_LEVEL_COMPANY',
                    parentVal: this.userInfo.coreOrganizationTile['l3Id']
                });
            } else {
                this.loyaltyList = lovData.filter(item => !item.parentId);
            }
        },
        /**
         * @desc 查询类型数据
         * <AUTHOR>
         * @date 2022/6/1 15:27
         **/
        async queryTypeList() {
            const data = await this.$http.post(this.$env.appURL + '/action/link/mapConType/queryByExamplePage', {
                oauth: 'ALL',
                pageFlag: true,
                rows: 500,
                page: 1,
                distinctFields: 'type',
                filtersRaw: [
                    {
                        id: 'companyId',
                        property: 'companyId',
                        value: this.userInfo.coreOrganizationTile['l3Id'],
                        operator: '='
                    },
                    {id: 'status', property: 'status', value: 'Active', operator: '='}]
            });
            if (data.success) {
                for (const item of data.rows) {
                    let name = await this.$lov.getNameByTypeAndVal('ACCT_SUB_TYPE', item.type)
                    this.$set(item, 'name', name);
                }
                data.rows.forEach((item, index) => {
                    this.$set(item, 'seq', index + 1)
                });
                this.classifyItemList = data.rows;
            }
        },
        /**
         * @desc 取消删除
         * <AUTHOR>
         * @date 2023/9/14 14:24
         **/
        cancel () {
            this.showDeleteIcon = false;
            this.operatorFlag = true;
        },
        /**
         * @desc 选中当前的圈选条件进行编辑
         * <AUTHOR>
         * @date 2023/9/7 21:09
         * @param item
         * @param index
         **/
        checkCurrentData (item, index) {
            this.showLovFlag = false;
            this.currentIndex = index;
            this.currentData = this.customLabelData.filter(item1=> item1.field === item.id)[0];
            this.currentCondition = this.conditionOperatorList[item.dataType].filter((item1)=> item1.operator === item.operator)[0];
            if (this.currentData.lovType) {
                this.showLovFlag = true;
            }
            if (this.currentCondition.label === '介于') {
                const tempData = this.conditionResultList.filter(item2=>item2.proterty=== this.currentData.field);
                this.labelItem.firstData = tempData[0];
                this.labelItem.secondData = tempData[1];
            } else {
                if (this.currentData.field === 'type' && this.classifyItemList.length < 1) {
                    this.queryTypeList();
                }
                if (this.currentData.field === 'loyaltyLevel' && this.loyaltyList.length < 1) {
                    this.queryCfg();
                }
                if (this.currentData.field === 'province') {
                    this.labelItem.province = this.conditionResultList[index]['value'];
                }
                if (this.currentData.field === 'city') {
                    const province = this.conditionResultList.filter((item)=> item.property === 'province');
                    if (province.length > 0) {
                        this.labelItem.province = province[0]['value'];
                    }
                    this.labelItem.city = this.conditionResultList[index]['value'];
                }
                if (this.currentData.field === 'county') {
                    const province = this.conditionResultList.filter((item)=> item.property === 'province');
                    const city = this.conditionResultList.filter((item)=> item.property === 'city');
                    if (province.length > 0) {
                        this.labelItem.province = province[0]['value'];
                    }
                    if (city.length > 0) {
                        this.labelItem.city = city[0]['value'];
                    }
                    this.labelItem.district = this.conditionResultList[index]['value'];
                }
                if (this.currentData.dataType === 'BZmultipleChoice' && this.currentData.lovType) {
                    const tempData = this.conditionResultList[index]['value'].substring(1, this.conditionResultList[index]['value'].length-1);
                    this.labelItem.firstData = tempData.split(',');
                } else if (this.currentData.field === 'registeredMember') {
                    if (this.conditionResultList[index]['operator'] === 'NOT NULL') {
                        this.labelItem.firstData = 'Y';
                    } else {
                        this.labelItem.firstData = 'N';
                    }
                } else {
                    this.labelItem.firstData = this.conditionResultList[index]['value'];
                }
            }
        },
        /**
         * @desc 选择数据
         * <AUTHOR>
         * @date 2023/9/7 19:36
         * @param item 选择对象
         **/
        chooseData (item) {
            this.currentInfo = item;
        },
        /**
         * @desc 确认
         * <AUTHOR>
         * @date 2023/9/7 19:29
         **/
        confirm () {
            this.dialogFlag = false;
            this.showLovFlag = false;
            if (this.inputType === 'label') {
                this.currentData = this.currentInfo;
                this.currentCondition = {};
                this.labelItem.firstData = '';
                this.labelItem.secondData = '';
            } else {
                this.currentCondition = this.currentInfo;
                if (this.currentData.lovType) {
                    this.showLovFlag = true;
                }
            }
            if (this.currentData.field === 'type' && this.classifyItemList.length < 1) {
                this.queryTypeList();
            }
            if (this.currentData.field === 'loyaltyLevel' && this.loyaltyList.length < 1) {
                this.queryCfg();
            }
        },
        /**
         * @desc 保存圈选条件
         * <AUTHOR>
         * @date 2023/9/7 16:08
         **/
        async saveCondition () {
            if (this.$utils.isEmpty(this.currentData.label)) {
                this.$message.warn('请选择标签！');
                return;
            }
            if (this.$utils.isEmpty(this.currentCondition.label)) {
                this.$message.warn('请选择圈选条件！');
                return;
            }
            if (this.$utils.isEmpty(this.labelItem.province) && this.labelItem.city) {
                this.labelItem.city = '';
                this.$message.warn('请先选择省份！');
                return;
            }
            if ((this.$utils.isEmpty(this.labelItem.province) || this.$utils.isEmpty(this.labelItem.city)) && this.labelItem.district) {
                this.labelItem.district = '';
                this.$message.warn('请先选择省市！');
                return;
            }
            if (this.$utils.isEmpty(this.labelItem.firstData) && this.$utils.isEmpty(this.labelItem.province)) {
                this.$message.warn('请填写圈选值！');
                return;
            }
            this.currentIndex = null;
            let flag = false;
            let showData = this.currentData.label + this.currentCondition.label + '"' + this.labelItem.firstData + '"';
            if (this.labelItem.province && this.currentData.field === 'province') {
                this.labelItem.city = '';
                this.labelItem.district = '';
                showData = this.currentData.label + this.currentCondition.label + '"' + this.labelItem.province + '"';
            } else if (this.labelItem.city && this.currentData.field === 'city') {
                showData = this.currentData.label + this.currentCondition.label + '"' + this.labelItem.city + '"';
                this.labelItem.district = '';
            } else if (this.labelItem.district && this.currentData.field === 'county') {
                showData = this.currentData.label + this.currentCondition.label + '"' + this.labelItem.district + '"';
            }
            if (this.currentCondition.label === '介于') {
                showData = this.currentData.label + this.currentCondition.label + '"' + this.labelItem.firstData + '"' +'至' +  '"' + this.labelItem.secondData + '"';
            } else if (this.currentData.lovType && this.currentData.dataType === 'BZradio') {
                const valueName = await this.$lov.getNameByTypeAndVal(this.currentData.lovType, this.labelItem.firstData);
                showData = this.currentData.label + this.currentCondition.label + '"' + valueName + '"';
            } else if (this.currentData.lovType && this.currentData.dataType === 'BZmultipleChoice') {
                let tempArrData = []
                for (let i = 0; i < this.labelItem.firstData.length; i++) {
                    const tempValue = await this.$lov.getNameByTypeAndVal(this.currentData.lovType, this.labelItem.firstData[i]);
                    tempArrData.push(tempValue);
                }
                showData = this.currentData.label + this.currentCondition.label + '"' + tempArrData + '"';
            }
            this.conditionShowList.forEach((item, index)=> {
                if (item.id === this.currentData.field) {
                    flag = true;
                    this.conditionShowList.splice(index, 1, {id: this.currentData.field, showData, dataType: this.currentData.dataType, operator: this.currentCondition.operator});
                    this.conditionResultList.splice(index, 1, {id: this.currentData.field, property: this.currentData.field, value: this.labelItem.firstData || this.labelItem.province || this.labelItem.city || this.labelItem.district, operator: this.currentCondition.operator});
                    this.currentData = [];
                    this.currentCondition = [];
                } else if (this.currentData.field === 'province') {
                    this.conditionResultList = this.conditionResultList.filter(item=> item.property !== 'city' && item.property!=='county');
                    this.conditionShowList = this.conditionShowList.filter(item=> item.id !== 'city' && item.id !== 'county');
                } else if (this.currentData.field === 'city') {
                    this.conditionResultList = this.conditionResultList.filter(item=> item.property!=='county');
                    this.conditionShowList = this.conditionShowList.filter(item=>item.id !== 'county');
                }
            });
            if (flag) {
                if (this.labelItem.district) {
                    this.labelItem = {province: '', city: '', district: '', firstData: '', secondData: ''};
                } else {
                    this.labelItem.firstData = '';
                    this.labelItem.secondData = '';
                }
                return;
            }
            this.conditionShowList.push({id: this.currentData.field, showData, dataType: this.currentData.dataType, operator: this.currentCondition.operator});
            if (this.currentCondition.label === '介于') {
                const operator = this.currentCondition.operator.split(',');
                this.conditionResultList.push({id: this.currentData.field, property: this.currentData.field, value: this.labelItem.firstData, operator: operator[0]});
                this.conditionResultList.push({id: this.currentData.field + '_02', property: this.currentData.field, value: this.labelItem.secondData, operator: operator[1]});
            } else if (this.currentCondition.label === '包含') {
                this.conditionResultList.push({id: this.currentData.field, property: this.currentData.field, value: '[' + this.labelItem.firstData.toString() + ']', operator: this.currentCondition.operator});
            } else if (this.currentData.field === 'province') {
                this.conditionResultList.push({id: this.currentData.field, property: this.currentData.field, value:  this.labelItem.province, operator: this.currentCondition.operator});
            } else if (this.currentData.field === 'city') {
                this.conditionResultList.push({id: this.currentData.field, property: this.currentData.field, value:  this.labelItem.city, operator: this.currentCondition.operator});
            } else if (this.currentData.field === 'county') {
                this.conditionResultList.push({id: this.currentData.field, property: this.currentData.field, value:  this.labelItem.district, operator: this.currentCondition.operator});
            } else if (this.currentData.field === 'registeredMember') {
                if (this.labelItem.firstData === 'Y') {
                    this.conditionResultList.push({id: 'memberId', property: 'memberId', value:  '', operator: 'NOT NULL'});
                } else {
                    this.conditionResultList.push({id: 'memberId', property: 'memberId', value:  '', operator: 'IS NULL'});
                }
            } else{
                this.conditionResultList.push({id: this.currentData.field, property: this.currentData.field, value:  this.labelItem.firstData, operator: this.currentCondition.operator});
            }
            this.currentData = [];
            this.currentCondition = [];
            if (this.labelItem.district) {
                this.labelItem = {province: '', city: '', district: '', firstData: '', secondData: ''};
            } else {
                this.labelItem.firstData = '';
                this.labelItem.secondData = '';
            }
            let tempLsc = [];
            if (this.lscspJson) {
                tempLsc = JSON.parse(this.lscspJson);
            }
            const condition = {tag: tempLsc['tag'] || [], qps: this.conditionResultList, qpsShow: this.conditionShowList};
            this.$store.commit('consumerLabel/setConsumerLabel', condition);
        },
        /**
         * @desc 删除某一个标签
         * <AUTHOR>
         * @date 2023/9/7 15:38
         * @param item 被删除的标签对象
         * @param index 标识
         **/
        deleteTagItem (item, index) {
            if (item.id === 'province') {
                this.conditionShowList = this.conditionShowList.filter((item3)=> item3.id !== 'city' && item3.id !== 'province' && item3.id !== 'county');
                this.labelItem.province = '';
                this.labelItem.city = '';
                this.labelItem.district = '';
            } else if (item.property === 'city') {
                this.conditionShowList = this.conditionShowList.filter((item2)=> item2.id !== 'city' && item2.id !== 'province');
                this.labelItem.city = '';
                this.labelItem.district = '';
            } else {
                this.conditionShowList.splice(index, 1);
            }
            if (item.operator === '>=,<=') {
                this.conditionResultList.forEach((item1, index)=> {
                    if (item1.property === item.id) {
                        this.conditionResultList.splice(index, 1);
                    }
                })
            } else {
                if (item.property === 'province') {
                    this.conditionResultList = this.conditionResultList.filter((item2)=> item2.property !== 'city' && item2.property !== 'province' && item2.property !== 'county');
                } else if (item.property === 'city') {
                    this.conditionResultList = this.conditionResultList.filter((item2)=> item2.property !== 'city' && item2.property !== 'province');
                } else {
                    this.conditionResultList.splice(index, 1);
                }
            }
            let tempLsc = [];
            if (this.lscspJson) {
                tempLsc = JSON.parse(this.lscspJson);
            }
            const condition = {tag: tempLsc['tag'] || [], qps: this.conditionResultList, qpsShow: this.conditionShowList};
            this.$store.commit('consumerLabel/setConsumerLabel', condition);
        },
        /**
         * @desc 删除标签,展示所有已存标签的删除图标
         * <AUTHOR>
         * @date 2023/9/7 15:36
         **/
        deleteTag () {
            this.showDeleteIcon = true;
            this.operatorFlag = false;
        },
        /**
         * @desc 全部删除
         * <AUTHOR>
         * @date 2023/9/7 15:32
         **/
        deleteAll () {
            this.conditionResultList = [];
            this.conditionShowList = [];
            this.labelItem = {province: '', city: '', district: '', firstData: '', secondData: ''};
            let tempLsc = [];
            if (this.lscspJson) {
                tempLsc = JSON.parse(this.lscspJson);
            }
            const condition = {tag: tempLsc['tag'] || [], qps: this.conditionResultList, qpsShow: this.conditionShowList};
            this.$store.commit('consumerLabel/setConsumerLabel', condition);
        },
        /**
         * @desc 保存数据
         * <AUTHOR>
         * @date 2023/9/7 23:42
         **/
        async saveAllData () {
            await this.$refs.crowdFormData.validate();
            if (this.$utils.isEmpty(this.formData.crowdSelection)) {
                this.$message.warn('请输入人群包名称');
                return;
            }
            if (this.$utils.isEmpty(this.formData.description)) {
                this.$message.warn('请输入人群包描述');
                return;
            }
            this.dialogSaveFlag = false;
            this.formData.status = 'saved';
            if (this.formData.lscspJson) {
                delete this.formData.lscspJson;
            }
            let tempLsc = [];
            if (this.lscspJson) {
                tempLsc = JSON.parse(this.lscspJson);
            }
            if (this.conditionResultList.length < 1 && (!tempLsc['tag'] || tempLsc['tag'].length < 1)) {
                this.$message.warn('请至少选择一个筛选条件!');
                return;
            }
            if (this.formData.row_status === ROW_STATUS.NEW) {
                this.conditionResultList.push({id: 'postnId', property: 'postnId', value: this.userInfo.postnId, operator: '='});
            }
            const condition = {tag: tempLsc['tag'] || [], qps: this.conditionResultList, qpsShow: this.conditionShowList};
            let param = Object.assign({},  {lscspJson: JSON.stringify(condition)}, this.formData);
            if (this.saveType === 'saveAndGenerate') {
                let flag = '';
                let executeUrl = '/link/cdcConScreen/insertExecute';
                if (this.formData.row_status === ROW_STATUS.UPDATE) {
                    executeUrl = '/link/cdcConScreen/update';
                    flag = 'update';
                }
                delete this.formData.row_status;
                const otherData = await this.$http.post(this.$env.dmpURL + executeUrl, param, {
                    autoHandleError: false,
                    handleFailed: (res)=> {
                        this.$message.error('保存失败！' + res.result);
                    }
                });
                if (otherData.success) {
                    this.$message.success('保存成功！');
                    if (otherData.newRow){
                        this.formData = otherData.newRow;
                    }
                    if (flag === 'update') {
                        await this.$http.post(this.$env.dmpURL + '/link/cdcConScreen/execute', {id: this.formData.id}, {
                            autoHandleError: false,
                            handleFailed: (res) => {
                                this.$message.error('执行失败！' + res.result);
                            }
                        });
                    }
                    this.$nav.back();
                }
            } else {
                let url = '/link/cdcConScreen/insert';
                if (this.formData.row_status === ROW_STATUS.UPDATE) {
                    url = '/link/cdcConScreen/update'
                }
                delete this.formData.row_status;
                const data = await this.$http.post(this.$env.dmpURL + url, param, {
                    autoHandleError: false,
                    handleFailed: (res)=> {
                        this.$message.error('保存失败！' + res.result);
                    }
                });
                if (data.success) {
                    this.$message.success('保存成功！');
                    data.newRow.row_status = ROW_STATUS.UPDATE;
                    this.$emit('update:allData', data.newRow);
                    this.$nav.back();
                }
            }
        },
        /**
         * @desc 保存数据或保存并且生成数据
         * <AUTHOR>
         * @date 2023/9/6 16:03
         * @param type 保存方法
         **/
        async saveData (type) {
            this.dialogSaveFlag = true;
            this.saveType = type;
        },
        /**
         * @desc 点击显示弹窗
         * <AUTHOR>
         * @date 2023/9/7 19:39
         * @param  type 弹窗类型
         **/
        pickerChange(type) {
            if (type === 'label') {
                this.dialogHeight = '65vh';
                this.dialogLabelTitle = '标签选择';
            } else {
                if (this.$utils.isEmpty(this.currentData.label)) {
                    this.$message.warn('请选择标签！');
                    return;
                }
                if (this.currentData.lovType) {
                    this.showLovFlag = false;
                }
                this.dialogFlag = '50vh';
                this.dialogLabelTitle = '条件选择';
            }
            this.inputType = type;
            this.currentInfo = {};
            this.dialogFlag = true;
        },
        /**
         * @desc 解析字段配置信息
         * <AUTHOR>
         * @date 2023/9/6 16:53
         **/
        async checkFieldsInfo () {
            this.tempList = [];
            const data = await this.$http.post(this.$env.appURL + '/link/fieldTemplate/queryJsonFileUrl', {
                applicationType: 'CsmType'
            });
            const jsonData = await this.$http.get(data.result);
            if (jsonData.success) {
                const fieldData = JSON.parse(jsonData.result.canvasCfg);
                const standarFields = fieldData.filter((item) => item.fieldType === 'StandarField');
                let tempData = [];
                let custStandTag = [];
                let custOtherTag = [];
                if (standarFields.length > 0) {
                    tempData = fieldData.filter((item) => item.fieldType === 'StandarField')[0]['widgets'];
                    tempData.forEach((item, index)=> {
                            custStandTag.push({
                                dataType: item.dataType || 'Default',
                                field: item.field,
                                lovType: item.lovType || '',
                                label: item.label
                            });
                    });
                    const lovData = tempData.filter((tp) => tp.lovType && (tp.dataType==='BZmultipleChoice' || tp.dataType === 'BZradio'));
                    if(lovData.length > 0){
                        await this.$lov.getLovByTypeArray(lovData.map((td) => td.lovType));
                    }
                }
                // const customListData = fieldData.filter((item) => item.fieldType === 'CustomField');
                // if (customListData.length > 0) {
                //     customListData.forEach((custItem)=> {
                //         const configListData = custItem['widgets'];
                //         configListData.forEach((item,index) => {
                //             custOtherTag.push({
                //                 field: item.field,
                //                 label: item.label,
                //                 dataType: item.type,
                //                 choices: item.choices
                //             })
                //         });
                //     })
                // }
                this.customLabelData = custStandTag.concat(custOtherTag);
            }
        },
    }
}
</script>
<style lang="scss">
.custom-labels {
    width: 100%;
    display: flex;
    background-color: #f2f2f2;
    flex-wrap: wrap;
    .link-dialog-foot-custom{
        width: auto !important;
    }
    /*deep*/.link-textarea-content {
                height:fit-content !important;
            }
    .labels-box{
        border-radius: 16px;
        width: 700px;
        background-color: #ffffff;
        margin: 125px 25px 25px 25px;
        padding-bottom: 30px;
        .labels-content{
            margin: 32px 24px;
            .labels-tips{
                width: 100%;
                height: 48px;
                line-height: 48px;
                .condition{
                    float: left;
                    font-size: 32px;
                    color: #333333;
                    font-weight: 600;
                }
                .save{
                    float: right;
                    font-size: 24px;
                    color: #2F69F8;
                    font-weight: 400;
                }
            }
            .labels-choice-box{
                width: 100%;
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 24px;
            }
            .labels-choice-box-info{
                width: 100%;
                margin: 24px 0;
                .picker-content{
                    margin-top: 16px;
                    /*deep*/ .link-input-content {
                    background-color: #f2f2f2 !important;
                }
                    /*deep*/ .link-textarea-content {
                                 border: none !important;
                             }
                    /*deep*/ .link-number{
                                 height: 76px !important;
                             }
                    /*deep*/ .link-number-input-wrapper {
                                 display: flex;
                                 align-items: center;
                             }
                    /*deep*/ .link-button-mode-text {
                                 font-size: 24px !important;
                                 color: #333333 !important;
                             }
                    .link-input{
                        border-radius: 8px;
                        height: 76px;
                        text-align: left !important;
                    }
                    .link-input-content{
                        width: 100%;
                        height: 76px;
                        border-radius: 8px;
                        padding-left: 20px;
                    }
                    .link-lov {
                        border-radius: 8px;
                    }
                    .show-two{
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        .left-data{
                            width: 48%;
                        }
                        .right-data{
                            width: 48%;
                        }
                        .bot{
                            height: 76px;
                            line-height: 76px;
                        }
                    }
                }
            }
            .labels-choice-item{
                margin-right: 16px;
                display: flex;
                flex-wrap: wrap;
                margin-top: 24px;
                width: 50%;
                .picker-content {
                    width: 100%;
                    height: 76px;
                    line-height: 76px;
                    color: #BBBBBB;
                    background: #f2f2f2;
                    margin-top: 16px;
                    font-size: 28px;
                    border-radius: 8px;
                    padding-left: 10px;
                    @include flex-start-center;
                }
            }
            .labels-item-title{
                font-size: 24px;
                color: #999999;
                height: 22px;
                line-height: 22px;
                font-weight: 400;
            }
            .picker-data{
                width: 90%;
                .picker {
                    font-size: 28px;
                    color: #333333;
                    line-height: 44px;
                    font-weight: 400;
                }
                .no-data{
                    color: #BBBBBB !important;
                }
            }
        }

        .label-result{
            .title-line-info{
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 24px;
                .title{
                    font-size: 28px;
                    color: #333333;
                    font-weight: 400;
                }
                .operator{
                    color: #BBBBBB;
                    display: flex;
                    align-items: center;
                    .line{
                        width: 1px;
                        background: rgba(204,204,204,1);
                        height: 16px;
                        margin-right: -20px;
                    }
                }
            }
            .label-result-content{
                display: flex;
                flex-wrap: wrap;
                padding: 0 12px;
                .label-result-item{
                    background: #F8F8F8;
                    border-radius: 8px;
                    width: auto;
                    font-size: 28px;
                    color: #333333;
                    line-height: 44px;
                    font-weight: 400;
                    padding: 14px 32px;
                    margin: 8px 8px 0;
                    display: flex;
                    align-items: center;
                    .link-icon{
                        width: 40px;
                        color: #999999;
                    }
                }
                .label-result-item-active{
                    color: #2F69F8 !important;
                    background: #F0F5FF !important;
                }
            }
        }
    }
    .model-title{
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 20px;
        .title{
            width: 56%;
        }
        .iconfont {
            font-size: 40px;
        }
    }
}
</style>
