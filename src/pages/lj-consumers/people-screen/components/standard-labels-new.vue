<!--
@createdBy 黄鹏
@date 23023/12/28
@description: --- 自定义标签选择弹框 - 新版袋鼠云
-->
<template>
    <view class="standard-labels">
        <link-search-input v-if="search" v-model="searchData" @change="searchConfirm"/>
        <view v-else style="width: 100%;" :style="showGenerate ? 'height: 92rpx' : 'height: 24rpx'"/>
        <line-title title="自定义标签圈选条件" v-if="showGenerate"/>
        <view class="third-level-data" :class="showGenerate? '' : 'other-bottom'">
            <view v-for="(tagGroup, groupIndex) in tagGroups"
                  :key="groupIndex + 'thr'"
                  v-if="!tagGroup.hide"
                  style="width: 100%;">
                <view class="tag-header">
                    <view class="tag-title">
                        <view class="tag-title-text">{{tagGroup.tagGroupName}}</view>
                    </view>
                </view>
                <view class="classify-filter-content">
                    <view class="classify-filter-list">
                        <view v-for="(tagItem, tagIndex) in tagGroup.valueList"
                              :key="tagIndex"
                              class="classify-filter-item"
                              :class="tagItem.checked ? 'label-checked' : ''"
                              @tap="chooseTag(tagItem, tagIndex, tagGroup)">
                            {{tagItem.tagName}}
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <link-sticky>
            <link-button block mode="stroke" @tap="saveData('save')">保存</link-button>
            <link-button v-if="showGenerate" block @tap="saveData('saveAndGenerate')">保存并生成</link-button>
        </link-sticky>
        <link-dialog :noPadding="true"
                     class="dialog-bottom"
                     v-model="dialogSaveFlag"
                     height="55vh"
                     position="bottom"
                     borderRadius="32rpx 32rpx 0 0">
            <view class="model-title">
                <view class="title">{{ dialogTitle }}</view>
                <view class="iconfont icon-close" @tap="dialogSaveFlag = false"></view>
            </view>
            <scroll-view scroll-y="true" :style="{'height': 'calc(100% - 121px)'}">
                <link-form :value="formData" ref="crowdFormData" :rules="formRules">
                    <link-form-item label="人群包名称" required field="crowdSelection">
                        <link-input type="text" v-model="formData.crowdSelection" placeholder="请输入且不超过30字"/>
                    </link-form-item>
                    <link-form-item label="人群包描述" vertical style="border-bottom: 1px solid rgb(247,247,247);" required
                                    field="description">
                        <link-textarea v-model="formData.description" :nativeProps="{maxlength:200}"/>
                    </link-form-item>
                </link-form>
            </scroll-view>
            <view class="link-dialog-foot-custom">
                <link-button shadow @tap="saveAllData" label="确定" style="width:100vw"/>
            </view>
        </link-dialog>
    </view>
</template>
<script>
    import LineTitle from "../../../lzlj/components/line-title.vue";
    import {ROW_STATUS} from "../../../../utils/constant";

    export default {
        name: 'standard-labels-new',
        components: {LineTitle},
        props: {
            search: {
                type: Boolean,
                default: true
            },   // 是否展示搜索框
            check: {
                type: Boolean,
                default: false
            },   // 是否判断层级
            allData: {
                type: Object,
                default: () => {
                }
            },
            dialogTitle: {
                type: String,
                default: '新建人群包'
            },   // 人群包圈选保存弹框title
            tagGroupList: {
                type: [Array, String],
                default: ''
            },   // ?? 代码里未搜索到相关应用场景
            tagList: {
                type: Array,
                default: () => []
            },   // 已选择的标签id
            showGenerate: {
                type: Boolean,
                default: true
            }   // 是否生成，如果只是保存直接emit当前选择的数据
        },
        data() {
            const userInfo = this.$taro.getStorageSync('token').result;
            return {
                lscspJson: '',
                searchData: '',            // 当前搜索值
                saveType: '',              // 保存类型：保存、保存并生成
                formData: {},              // 人群圈选弹框表格
                formRules: {
                    description: this.Validator.required(),
                    crowdSelection: [this.Validator.required(), this.Validator.len({
                        max: 30,
                        maxMsg: '人群包名称字数超出长度30字！请检查'
                    })]
                },          // 人群圈选弹框表格校验规则
                dialogSaveFlag: false,     // 人群圈选弹框
                userInfo,                  // 当前登陆人token，登陆人信息
                initialData: [],           // 存储原始数据，覆盖深层标签的数据
                tagGroupsData: [],         // 一级标签
                tagGroups: []              // 三级标签
            }
        },
        async created() {
            if (this.allData) {
                this.lscspJson = this.allData['lscspJson'] || '';
            }
            this.formData = this.allData;
            if (!this.tagGroupList) {
                await this.queryAccntTagGroup();
            }
        },
        watch: {
            allData: {
                deep: true,
                handler(newVal) {
                    this.formData = newVal;
                    if (newVal) {
                        this.lscspJson = newVal['lscspJson'] || '{}';
                    }
                }
            },
            tagGroupList: {
                deep: true,
                immediate: true,
                handler(newVal) {
                    if (newVal) {
                        if (typeof newVal === 'object' && newVal.length > 0) {
                            // 哪个场景会传这个属性？？？代码未搜索到
                            let arr = this.treeFilter(newVal, node => node.valueList.length > 0);
                            this.initialData = this.$utils.deepcopy(arr);
                            this.chooseTagData(arr);
                            this.tagGroupsData = this.$utils.deepcopy(arr);
                        } else {
                            this.tagGroupsData = [];
                            this.initialData = [];
                        }
                    }
                }
            }
        },
        methods: {
            /**
             * @createdBy 黄鹏
             * @date 2023/12/28
             * @methods: searchConfirm
             * @para:
             * @description: 确认搜索
             **/
            searchConfirm(val) {
                if (!val) {
                    this.tagGroups.forEach((item) => {
                        this.$set(item, 'hide', false)
                    });
                    return
                }

                this.tagGroups.forEach((item) => {
                    const con1 = item.tagGroupName === val;
                    const con2 = item.valueList.find((it) => it.tagName === val);
                    if (con2) {
                        item.valueList.forEach((temp) => {
                            if (item.isMultipleValue) {
                                // 多选
                                if (temp.tagName === val) {
                                    // 搜索出来数据做选中
                                    temp.checked = true;
                                }
                            } else {
                                //单选
                                temp.checked = temp.tagName === val;
                            }
                        });
                    }
                    item.hide = !Boolean(con1 || con2);
                })
            },
            /**
             @desc: 人群圈选切换tab临时保存数据
             @author: wangbinxin
             @date 2023-09-19 19-35
             **/
            setTagData() {
                let tempLsc = [];
                if (this.lscspJson) {
                    tempLsc = JSON.parse(this.lscspJson);
                }
                let tempArr = [];
                this.tagGroups.forEach((item) => {
                    item.valueList.forEach((tag) => {
                        if (tag.checked) {
                            tempArr.push(tag.tagId);
                        }
                    })
                })
                this.$store.commit('consumerLabel/setTagList', tempArr);
                const labelData = {tag: tempArr, qps: tempLsc['qps'] || [], qpsShow: tempLsc['qpsShow'] || []};
                this.$store.commit('consumerLabel/setConsumerLabel', labelData);
            },
            /**
             @param tagList: 数组数据
             @param data: tree
             @desc: 将当前选择的数据展示出来
             @author: wangbinxin
             @date 2023-09-17 12-37
             **/
            chooseTagData(data, tagList = this.tagList) {
                this.tagGroups.forEach((tagGroup) => {
                    this.tagList.forEach((chosenTagId) => {
                        tagGroup.valueList.forEach((tag) => {
                            if (tag.tagId === chosenTagId) {
                                this.$set(tag, 'checked', true);
                            }
                        })
                    })
                })
            },
            /**
             @param 标签数据
             @desc: 只展示四层的数据,同时处理数据展示父级名称
             @author: wangbinxin
             @date 2023-09-13 16-54
             **/
            treeFilter(tree, func) {
                // 使用map复制一下节点，避免修改到原树
                return tree.map(node => ({...node})).filter(node => {
                    if (this.searchData) node.checked = true;
                    let valueFlag = func(node);
                    if (node.itemList && node.itemList.length > 0 && !valueFlag) {
                        node.itemList = node.itemList && this.treeFilter(node.itemList, func);
                    }
                    if (this.searchData && !valueFlag && node.valueList && node.valueList.length > 0) {
                        node.valueList = this.treeFilter(node.valueList, func);
                        valueFlag = valueFlag || (node.valueList && node.valueList.length);
                    }
                    valueFlag = valueFlag || (node.itemList && node.itemList.length);
                    return valueFlag;
                })
            },
            /**
             * @desc 人群圈选保存数据
             * <AUTHOR>
             * @date 2023/9/7 23:42
             **/
            async saveAllData() {
                await this.$refs.crowdFormData.validate();
                if (this.$utils.isEmpty(this.formData.crowdSelection)) {
                    this.$message.warn('请输入人群包名称');
                    return;
                }
                if (this.$utils.isEmpty(this.formData.description)) {
                    this.$message.warn('请输入人群包描述');
                    return;
                }
                let tempLsc = {qps: []};
                if (this.lscspJson) {
                    tempLsc = JSON.parse(this.lscspJson);
                }
                let tempArr = [];
                this.tagGroups.forEach((item) => {
                    item.valueList.forEach((tag) => {
                        if (tag.checked) {
                            tempArr.push(tag.tagId);
                        }
                    })
                })
                if (tempArr.length < 1 && (!tempLsc['qps'] || tempLsc['qps'].length < 1)) {
                    this.$message.warn('请至少选择一个筛选条件!');
                    return;
                }
                this.dialogSaveFlag = false;
                this.formData.status = 'saved';
                if (this.formData.lscspJson) {
                    delete this.formData.lscspJson;
                }
                if (this.formData.row_status === ROW_STATUS.NEW) {
                    if (!tempLsc['qps']) {
                        tempLsc['qps'] = [];
                    }
                    tempLsc['qps'] = tempLsc['qps'].concat([
                        {
                            id: 'empFlag',
                            property: 'empFlag',
                            value: 'Y',
                            operator: '<>'
                        }, {
                            id: 'terminalFlag',
                            property: 'terminalFlag',
                            value: 'Y',
                            operator: '<>'
                        }, {
                            id: 'postnId',
                            property: 'postnId',
                            value: this.userInfo.postnId,
                            operator: '='
                        }
                    ])
                }
                const labelData = {tag: tempArr, qps: tempLsc['qps'] || [], qpsShow: tempLsc['qpsShow'] || []};
                let param = Object.assign({}, {lscspJson: JSON.stringify(labelData)}, this.formData);
                if (this.saveType === 'saveAndGenerate') {
                    let flag = '';
                    let executeUrl = '/link/cdcConScreen/insertExecute';
                    if (this.formData.row_status === ROW_STATUS.UPDATE) {
                        executeUrl = '/link/cdcConScreen/update';
                        flag = 'update';
                    }
                    delete this.formData.row_status;
                    const otherData = await this.$http.post(this.$env.dmpURL + executeUrl, param, {
                        autoHandleError: false,
                        handleFailed: (res) => {
                            this.$message.error('保存失败！' + res.result);
                        }
                    });
                    if (otherData.success) {
                        this.$message.success('保存成功！');
                        if (otherData.newRow) {
                            this.formData = otherData.newRow;
                        }
                        if (flag === 'update') {
                            await this.$http.post(this.$env.dmpURL + '/link/cdcConScreen/execute', {id: this.formData.id}, {
                                autoHandleError: false,
                                handleFailed: (res) => {
                                    this.$message.error('执行失败！' + res.result);
                                }
                            });
                        }
                        this.$nav.back();
                    }
                } else {
                    param.status = 'saved';
                    let url = '/link/cdcConScreen/insert';
                    if (this.formData.row_status === ROW_STATUS.UPDATE) {
                        url = '/link/cdcConScreen/update'
                    }
                    delete this.formData.row_status;
                    const data = await this.$http.post(this.$env.dmpURL + url, param, {
                        autoHandleError: false,
                        handleFailed: (res) => {
                            this.$message.error('保存失败！' + res.result);
                        }
                    });
                    if (data.success) {
                        this.$message.success('保存成功！');
                        this.formData = {};
                        data.newRow.row_status = ROW_STATUS.UPDATE;
                        this.$emit('update:allData', data.newRow);
                        this.$nav.back();
                    }
                }
            },
            /**
             * @desc 保存数据或保存并且生成数据
             * <AUTHOR>
             * @date 2023/9/6 16:03
             * @param type 保存方法
             **/
            async saveData(type) {
                if (this.showGenerate) {
                    // 人群圈选页面
                    this.dialogSaveFlag = true;
                    this.saveType = type;
                } else {
                    const arr = this.tagGroups.map((item) => {
                        const obj = {
                            tagGroupId: item.tagGroupId,
                            tagGroupName: item.tagGroupName,
                            isMultipleValue: item.isMultipleValue
                        };
                        if (item.itemList?.length > 0) {
                            obj.itemList = item.itemList.filter((temp) => temp.checked) || [];
                        } else if (item.valueList?.length > 0) {
                            obj.valueList = item.valueList.filter((temp) => temp.checked) || [];
                        }
                        return obj;
                    })
                    const saveData = arr.filter((item) => item.itemList?.length > 0 || item.valueList?.length > 0);
                    this.$emit('save', saveData);
                }
            },
            /**
             * @desc 选择标签
             * <AUTHOR>
             * @date 2023/9/6 15:53
             * @param tagItem 被选中的标签
             * @param index 数组下标
             * @param parentData 父对象
             **/
            chooseTag(tagItem, index, parentData) {
                if (parentData.isMultipleValue) {
                    this.$set(tagItem, 'checked', !tagItem.checked);
                } else {
                    if (tagItem.checked) {
                        this.$set(tagItem, 'checked', !tagItem.checked);
                    } else {
                        parentData.valueList.forEach((item) => {
                            this.$set(item, 'checked', item.tagId === tagItem.tagId);
                        });
                    }
                }
            },
            /**
             * @desc 查询标签
             * <AUTHOR>
             * @date 2023/9/6 10:06
             **/
            async queryAccntTagGroup() {
                // /link/crowdTagGroup/queryByExamplePage
                const data = await this.$http.post(this.$env.appURL + '/link/tagOrgTree/queryTreeTag', {
                    filtersRaw: [
                        {id: 'deleteFlag', property: 'deleteFlag', value: 'N'},
                        {id: 'publishStatus', property: 'publishStatus', value: '2'},
                        {id: 'labelType', property: 'labelType', value: '7'}
                    ]
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$message.error(`查询标签组数据失败：${response.result}`);
                    }
                });
                if (data.success) {
                    // 取出所有最后一层的层级id
                    const tagGroups = [];
                    const fn = (arr) => {
                        arr.forEach((item) => {
                            if (item.children && item.children.length > 0) {
                                fn(item.children);
                            } else {
                                tagGroups.push({
                                    tagGroupId: item.tagGroupId,
                                    tagGroupName: item.tagGroupName,
                                    isMultipleValue: item.isMultipleValue === 'true'
                                });
                            }
                        })
                    }
                    fn(data.rows);
                    // 根据层级id查询标签值
                    this.getTags(tagGroups);
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/01/02
             * @methods: getTags
             * @para:
             * @description: 根据标签组id获取标签值数据
             **/
            async getTags(tagGroups) {
                const tagGroupIds = tagGroups.map((item) => item.tagGroupId);
                const data = await this.$http.post(this.$env.appURL + '/link/crowdTagItem/queryByExamplePage', {
                    filtersRaw: [{
                        id: 'tagGroupId',
                        property: 'tagGroupId',
                        value: `[${tagGroupIds.join(',')}]`,
                        operator: 'in'
                    }, {
                        id: 'validFlag',
                        property: 'validFlag',
                        value: 'Y',
                        operator: '='
                    }],
                    rows: 1000
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$message.error(`查询标签值数据失败：${response.result}`);
                    }
                });
                if (data.success) {
                    const tags = data.rows.map((row) => {
                        return {
                            tagName: row.tagValueName,
                            tagId: row.tagValueId,
                            tagGroupId: row.tagGroupId
                        }
                    })
                    tagGroups.forEach((tagGroup) => {
                        tags.forEach((tag) => {
                            if (tag.tagGroupId === tagGroup.tagGroupId) {
                                if (tagGroup.valueList) {
                                    tagGroup.valueList.push(tag);
                                } else {
                                    tagGroup.valueList = [tag];
                                }
                            }
                        })
                    })
                    this.tagGroups = tagGroups;
                    // 选中数据
                    this.chooseTagData();
                }
            }
        }
    }
</script>
<style lang="scss">
    .standard-labels {
        width: 100%;
        background-color: #fff;
        padding-bottom: 20px;
        /*deep*/
        .link-textarea-content {
            height: fit-content !important;
        }

        .line-title {
            margin-bottom: 20px;

            .stair-title {
                margin-left: 16px;
                font-size: 30px;
                font-weight: bold;
                font-family: "Microsoft YaHei";
                color: #262626;
                line-height: 32px;
                white-space: nowrap;
                min-width: 40%;
                width: auto;
            }
        }

        .scroll-view-data {
            white-space: nowrap;
        }

        .secondary-labels {
            width: 100%;
            display: flex;
            height: 80px;

        }

        .labels-default {
            display: inline-block;
            width: 25%;
            height: 78px;
            text-align: center;
            line-height: 78px;
            font-size: 24px;
            color: #999999;
        }

        .secondary-labels-active {
            color: #2F69F8 !important;
        }

        .secondary-labels-default {
            width: auto;
            white-space: nowrap;
            height: 78px;
            text-align: center;
            line-height: 78px;
            font-size: 24px;
            color: #999999;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-right: 20px;
            //border-bottom: 1px solid #E5E5E5;
            margin-right: 20px;

            .tag-name {
                line-height: 73px;
            }

            .up {
                border: 1px solid #E5E5E5;
                border-width: 0 1px 1px 0;
                display: inline-block;
                padding: 6px;
                transform: rotate(-135deg);
                -webkit-transform: rotate(-135deg);
                width: 5px;
                height: 5px;
            }
        }

        .secondary-labels-default:first-child {
            padding-left: 20px;
        }

        .secondary-third-labels {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
        }

        .third-labels-scroller {
            margin-top: 18px;
        }

        .third-labels {
            display: inline-block;
            white-space: nowrap;
        }

        .third-labels-active {
            height: 56px;
            line-height: 56px;
            text-align: center;
            color: #2F69F8;
            border: 1px solid #2F69F8;
            border-radius: 30px;
            padding: 0 24px;
            font-size: 24px;
            margin-left: 24px;
            display: inline-block;
        }

        .third-labels-default {
            height: 56px;
            line-height: 56px;
            text-align: center;
            color: #333333;
            border: 1px solid #DDDDDD;
            border-radius: 30px;
            padding: 0 24px;
            font-size: 24px;
            margin-left: 24px;
            display: inline-block;
        }

        .third-level-data {
            padding-bottom: calc(env(safe-area-inset-bottom) + 24px);
        }

        .other-bottom {
            padding-bottom: calc(env(safe-area-inset-bottom) + 80px + 24px + 40px) !important;
        }

        .tag-header {
            display: block;
            margin-bottom: 24px;
            margin-top: 24px;
            width: 100%;

            .tag-title {
                display: flex;
                margin-left: 24px;

                .required {
                    color: red;
                    font-size: 32px;
                    line-height: 64px;
                    margin-right: 4px;
                }

                .tag-title-text {
                    font-size: 28px;
                    color: #333333;
                    line-height: 24px;
                    font-weight: 600;
                }

                .link-icon {
                    font-size: 32px;
                    color: #CCCCCC;
                    margin: 8px 0 0 8px;
                }
            }
        }

        .classify-filter-content {
            padding-bottom: 16px;
            display: block;
            width: calc(100% - 48px);
            margin: auto;

            .classify-filter-list {
                display: flex;
                align-items: center;
                flex-wrap: wrap;

                .classify-filter-item {
                    min-width: 120px;
                    min-height: 48px;
                    box-sizing: border-box;
                    margin: 0 20px 12px 0;
                    font-size: 24px;
                    color: #333333;
                    letter-spacing: 0;
                    text-align: center;
                    line-height: 48px;
                    font-weight: 400;
                    background: #f8f8f8;
                    border-radius: 8px;
                    padding: 0 10px;
                }

                .label-checked {
                    min-width: 120px;
                    min-height: 48px;
                    box-sizing: border-box;
                    margin: 0 20px 12px 0;
                    font-size: 20px;
                    color: #3F66EF;
                    letter-spacing: 0;
                    text-align: center;
                    line-height: 48px;
                    font-weight: 400;
                    background: #F0F5FF;
                    border-radius: 8px;
                }
            }
        }

        .model-title {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding: 20px;

            .title {
                width: 56%;
            }

            .iconfont {
                font-size: 40px;
            }
        }

        .link-dialog-foot-custom {
            width: auto !important;
        }
    }
</style>
