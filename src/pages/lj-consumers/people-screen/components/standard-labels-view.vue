<template>
    <view class="standard-labels-view">
        <lnk-taps v-if="type !== 'label'" :taps="tabOptions" v-model="tabActive" @switchTab="switchTab"></lnk-taps>
        <view class="labels-view">
            <view v-if="type.indexOf('label') > -1 && tabActive.val === 'label'">
                <line-title title="专营公司标签" v-if="type === 'label'"/>
                <view v-for="(item, index) in tagGroupsData" :key="index" class="standard-labels-content">
                    <view class="tag-header">
                        <view class="tag-title-text">{{item.tag1Name}} <text style="padding-left: 4px;font-size: 30rpx;color: #bbbbbb">| {{item.tagGroupName}}</text></view>
                        <link-icon :icon="item.show ? 'icon-down1' : 'icon-shouqi1'" @tap="showTagList(item)"/>
                    </view>
                    <view  class="classify-filter-content" v-show="item.show">
                        <view class="tag-group" v-for="(item3, index) in item.itemList" :key="index + 'tag'">
                            <view class="tag-item-title">
                                {{item3.tagGroupName}}
                                <link-icon :icon="item3.show ? 'mp-asc' : 'mp-desc'" @tap="showTagList(item3)"/>
                            </view>
                            <view class="tag-list" v-show="item3.show">
                                <view class="tag-list-item" v-for="(lineItem, lineIndex) in item3[tagGroups.length > 0 ? 'itemList':'valueList']" :key="lineIndex + 'tagItem'">{{lineItem.tagName}}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view v-if="type.indexOf('statistic') > -1 && tabActive.val === 'statistic'">
                <link-card-list>
                    <link-card>
                        <link-card-item v-for="item in statisticData" :key="item.key" :label="item.name" :content="item.val"/>
                    </link-card>
                </link-card-list>
            </view>
        </view>
    </view>
</template>

<script>
import LnkTaps from '../../../core/lnk-taps/lnk-taps'
import LineTitle from "../../../lzlj/components/line-title";

export default {
    name: "standard-labels-view",
    components: {LnkTaps, LineTitle},
    props: {
        type: {
          type: String,
          default: 'label'
        },
        // 已选数据
        tagList: {
            type: Array,
            default: () => []
        },
        // 展示数据
        tagGroups: {
            type: Array,
            default: () => []
        },
        consumerId: {
            type: String,
            default: ''
        }
    },
    data () {
        const userInfo = this.$taro.getStorageSync('token').result;
        return {
            userInfo,
            tagGroupsData: [],   // 一级标签
            secondLevelData: [], // 二级标签
            thirdLevelData: [],   // 三级标签
            tabOptions: [
                {name: '专营公司标签', seq: '1', val: 'label'},
                {name: '统计信息标签', seq: '2', val: 'statistic'}
            ],
            tabActive: {name: '专营公司标签', seq: '1', val: 'label'},
            statisticLabel: [{name: "企业名称", key:"enterprise"},
                {name: "注册资本", key:"registeredCapital"},
                {name: "所属经销商", key:"belongToDealer"},
                {name: "所属终端/门店", key:"store"},
                {name: "所属业务人员", key:"fstName"},
                {name: "出生年份", key:"year"},
                {name: "年龄", key:"age"},
                {name: "首购金额", key:"fpAmount"},
                {name: "首购件数", key:"fpNumber"},
                {name: "首购品项", key:"fpBrand"},
                {name: "首购时间", key:"fpTime"},
                {name: "平均每单购买件数", key:"afbNumber"},
                {name: "平均每单购买金额", key:"afpAmount"},
                {name: "最近一年购买件数", key:"oneYearNumber"},
                {name: "最近一年购买金额", key:"oneYearAmount"},
                {name: "最近一年购买次数", key:"oneYearTimes"},
                {name: "最近一年购买品项", key:"oneYearBrand"},
                {name: "最近三年购买件数", key:"threeYearNumber"},
                {name: "最近三年购买金额", key:"threeYearAmount"},
                {name: "最近三年购买次数", key:"threeYearTimes"},
                {name: "最近三年购买品项", key:"threeYearBrand"},
                {name: "历史累计购买次数", key:"historyTimes"},
                {name: "历史累计购买金额", key:"historyAmount"},
                {name: "历史累计购买件数", key:"historyNumber"},
                {name: "历史累计购买品项", key:"historyBrand"},
                {name: "最近购买时间", key:"lastBuyTime"},
                {name: "首次开瓶时间", key:"firstOpenTime"},
                {name: "最近开瓶时间", key:"lastOpenTime"},
                {name: "最近一年开瓶数", key:"oneYearOpen"},
                {name: "最近三年开瓶数", key:"threeYearOpen"},
                {name: "历史累计开瓶数", key:"historyOpen"},
                {name: "累计转介绍人数", key:"introducePeople"},
                {name: "累计转介绍购买人数", key:"introduceBuyPeople"},
                {name: "累计转介绍购买总件数", key:"introduceBuyNumber"},
                {name: "累计转介绍购买总金额", key:"introduceBuyAmount"},
                {name: "最近参与活动时间", key:"lastActTime"},
                {name: "最近拜访时间", key:"lastVisitTime"},
                {name: "参与过线上活动的次数", key:"actNumber"}],
            statisticData: []
        }
    },
    async created () {
        if(this.tagGroups.length > 0){
            this.processTagGroups()
        }else{
            await this.queryAccntTagGroup()
        }
        if(this.type !== 'label'){
            await this.queryStatistic()
        }
    },
    methods: {
        switchTab(val) {
            this.tabActive = val;
            if(val.val === 'statistic' && this.statisticData.length < 1){
                this.$message.warn('未获取到统计值标签数据！');
            }
        },
        /**
         @desc: 将 tagGroups 转为树形结构
         @author: wangbinxin
         @date 2023-09-20 15-42
         **/
        processTagGroups(){
            let arr = [];
            const copydata = this.$utils.deepcopy(this.tagGroups);
            copydata.forEach((tag) => {
                let obj = {};
                for (let i = 4; i > 0; i--) {
                    let id = i < 3?`l${i}TagId`:(i === 4?'tagId':'tagGroupId');
                    let name = i < 3?`l${i}TagName`:(i === 4?'tagName':'tagGroupName');
                    obj.id = tag[id];
                    obj.tagGroupName = tag[name];
                    obj.tagName = tag[name];
                    obj.show = true;
                    if(i > 1)obj = { itemList: [obj]};
                }
                const fn = (tree, data) => {
                    let list = tree.filter((item) => item.id === data.id)[0];
                    if(list){
                        list.itemList && fn(list.itemList, data['itemList'][0]);
                    }else{
                        tree.push(data);
                    }
                }
                fn(arr, obj)
            });
            let secarr = [];
            arr.forEach((item) => {
                if(item.itemList){
                    item.itemList.forEach(it => {
                        it.tag1Name = item.tagGroupName;
                        secarr.push(it);
                    })
                }
            })
            this.tagGroupsData = secarr;
        },
        /**
         @param tree 树形数据
         @param func 处理数据
         @desc: 对树形数据进行筛选，只展示当前tagList里的数据
         @author: wangbinxin
         @date 2023-09-16 10-30
         **/
        treeFilter (tree, func) {
            // 使用map复制一下节点，避免修改到原树
            return tree.map(node => ({ ...node })).filter(node => {
                node.show = true;
                if(node.itemList && node.itemList.length > 0){
                    node.itemList = node.itemList && this.treeFilter(node.itemList, func);
                }else{
                    if(node.valueList && node.valueList.length > 0){
                        node.valueList = node.valueList && this.treeFilter(node.valueList, func);
                    }
                }
                return func(node) || (node.itemList && node.itemList.length) || (node.valueList && node.valueList.length);
            })
        },
        /**
         @desc: 查询统计标签
         @author: wangbinxin
         @date 2023-09-25 11-04
         **/
        async queryStatistic () {
            const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                dmpUrl: '/link/csmStat/queryById',
                id: this.consumerId
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$message.error(`查询标签数据失败：${response.result}`);
                }
            });
            this.statisticData = [];
            if (data.success && data.result) {
                const labels = data.result;
                let arr = [];
                Object.keys(labels).forEach((label) => {
                    for (let i = 0; i < this.statisticLabel.length; i++) {
                        if(label === this.statisticLabel[i].key){
                            arr.push(Object.assign(this.statisticLabel[i], {val: labels[label]}));
                            break;
                        }
                    }
                })
                this.statisticData = arr;
            }
        },
        /**
         * @desc 查询标签数据
         * <AUTHOR>
         * @date 2023/9/13 19:43
         **/
        async queryAccntTagGroup () {
            const data = await this.$http.post(this.$env.appURL + '/link/accntTagGroup/queryLevelData', {
                companyId: this.userInfo.coreOrganizationTile['l3Id']
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$message.error(`查询标签数据失败：${response.result}`);
                }
            });
            if (data.success) {
                const tagGroupsData = data.result['quDaoTag'];
                let arr = this.treeFilter(tagGroupsData, node=>this.tagList.includes(node.id));
                let secarr = [];
                arr.forEach((item) => {
                    if(item.itemList){
                        item.itemList.forEach(it => {
                            it.tag1Name = item.tagGroupName;
                            secarr.push(it);
                        })
                    }
                })
                this.tagGroupsData = secarr;
            }
        },
        /**
         * @desc 选择当前标签
         * <AUTHOR>
         * @date 2023/9/12 11:53
         * @param item 被选中的标签
         **/
        chooseTab (item) {
            this.secondLevelData.forEach((item1) => {
                item1.checked = item1.id === item.id;
            })
            this.thirdLevelData = item.itemList;
        },
        /**
         * @desc 展开折叠
         * <AUTHOR>
         * @date 2023/9/7 12:02
         * @param item 当前折叠的对象
         * @param type 类型
         **/
        showTagList (item, type) {
            item.show = !item.show;
        }
    }
}
</script>

<style lang="scss">
.standard-labels-view{
    .labels-view{
        padding: 0 24px;
    }
    .line-title{
        margin-bottom: 24px;
    }
    .standard-labels-content{
        width: 100%;
        background: #FFFFFF;
        border: 1px solid rgba(255,255,255,1);
        border-radius: 16px;
        margin-bottom: 24px;
        .tag-header{
            background-image: linear-gradient(180deg, #EFF3FF 0%, rgba(238,241,252,0.00) 83%);
            border-radius: 16px;
            height: 96px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            .tag-title-text{
                font-size: 32px;
                color: #333333;
                font-weight: 600;
            }
        }
    }
    .classify-filter-content {
        display: block;
        width: calc(100% - 48px);
        padding: 0 24px 16px 24px;
        .scroll-view-data{
            white-space: nowrap;
            .secondary-labels{
                width: 100%;
                display: flex;
                height: 80px;
                .secondary-labels-default{
                    border: 1px solid rgba(221,221,221,1);
                    border-radius: 30px;
                    height: 40px;
                    font-size: 24px;
                    color: #333333;
                    text-align: center;
                    font-weight: 400;
                    line-height: 40px;
                    width: auto;
                    padding: 6px 24px;
                    margin-right: 20px;
                }
                .secondary-labels-active{
                    color: #2F54EB !important;
                    border-color: #2F54EB !important;
                }
            }
        }
        .tag-group{
            .tag-item-title{
                font-size: 28px;
                color: #333333;
                line-height: 44px;
                font-weight: 500;
            }
            .tag-list{
                display: flex;
                flex-wrap: wrap;
                .tag-list-item{
                    padding: 0 16px;
                    height: 56px;
                    width: auto;
                    min-width: 108px;
                    box-sizing: border-box;
                    margin: 0 0 16px 24px;
                    font-size: 24px;
                    letter-spacing: 0;
                    text-align: center;
                    line-height: 56px;
                    font-weight: 400;
                    background: #f8f8f8;
                    border-radius: 8px;
                    color: #666666;
                }
            }
        }
    }
}
</style>
