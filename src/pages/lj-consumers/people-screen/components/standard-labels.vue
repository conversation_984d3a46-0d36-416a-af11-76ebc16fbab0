<template>
    <view class="standard-labels">
        <link-search-input v-if="search" v-model="searchData" @change="searchConfirm"/>
        <view v-else style="width: 100%;" :style="showGenerate ? 'height: 92rpx' : 'height: 24rpx'"/>
        <line-title title="专营公司标签"/>
        <scroll-view class="scroll-view-data" scroll-x="true" :scroll-into-view="tagScroll">
            <view class="secondary-labels">
                <view v-for="(item,index) in tagGroupsData" class="secondary-labels-default" :class="item.checked?'secondary-labels-active':''" @tap="chooseTab(item, index)" :key="index" :id="'scroll'+index">
                    <view class="tag-name">
                        {{item.tagGroupName}}
                        <link-icon v-if="selectItem.id === item.id && item.checked" icon="icon-down1" style="color:#2f69f8"/>
                    </view>
                </view>
            </view>
        </scroll-view>
        <scroll-view class="third-labels-scroller" scroll-x="true">
            <view class="third-labels">
                <view :class="lineItem.checked?'third-labels-active':'third-labels-default'"  v-for="(lineItem,lineIndex) in secondLevelData" @tap="chooseSecondTab(lineItem, lineIndex)" :key="lineIndex">
                    {{lineItem.tagGroupName}}
                    <link-icon v-if="secondSelect.id === lineItem.id && lineItem.checked" icon="icon-yiwanchengbuzhou" style="color:#2f69f8"/>
                </view>
            </view>
        </scroll-view>
        <view class="third-level-data" :class="showGenerate? '' : 'other-bottom'">
            <view v-for="(thirdItem, thirdIndex) in thirdLevelData" :key="thirdIndex + 'thr'" style="width: 100%;">
                <view class="tag-header">
                    <view class="tag-title">
                        <view class="tag-title-text">{{thirdItem.tagGroupName}}</view>
                        <link-icon :icon="itemShow ? 'mp-asc' : 'mp-desc'" @tap="showTagList()"/>
                    </view>
                </view>
                <view  class="classify-filter-content" v-show="itemShow">
                    <view class="classify-filter-list">
                        <view v-for="(tagItem, tagIndex) in thirdItem.valueList" :key="tagIndex" class="classify-filter-item" :class="tagItem.checked ? 'label-checked' : ''" @tap="chooseTag(tagItem, tagIndex, thirdItem)">
                            {{tagItem.tagName}}
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <link-sticky>
            <link-button block mode="stroke" @tap="saveData('save')">保存</link-button>
            <link-button v-if="showGenerate" block @tap="saveData('saveAndGenerate')">保存并生成</link-button>
        </link-sticky>
        <link-dialog :noPadding="true"
                     class="dialog-bottom"
                     v-model="dialogSaveFlag"
                     height="55vh"
                     position="bottom"
                     borderRadius="32rpx 32rpx 0 0">
            <view class="model-title">
                <view class="title">{{ dialogTitle }}</view>
                <view class="iconfont icon-close" @tap="dialogSaveFlag = false"></view>
            </view>
            <scroll-view scroll-y="true" :style="{'height': 'calc(100% - 121px)'}">
                <link-form :value="formData" ref="crowdFormData" :rules="formRules">
                    <link-form-item label="人群包名称" required field="crowdSelection">
                        <link-input type="text" v-model="formData.crowdSelection" placeholder="请输入且不超过30字"/>
                    </link-form-item>
                    <link-form-item label="人群包描述"  vertical style="border-bottom: 1px solid rgb(247,247,247);" required field="description">
                        <link-textarea v-model="formData.description" :nativeProps="{maxlength:200}"/>
                    </link-form-item>
                </link-form>
            </scroll-view>
            <view class="link-dialog-foot-custom">
                <link-button shadow @tap="saveAllData" label="确定" style="width:100vw"/>
            </view>
        </link-dialog>
    </view>

</template>
<script>
import LineTitle from "../../../lzlj/components/line-title.vue";
import {ROW_STATUS} from "../../../../utils/constant";

export default {
    name: 'standard-labels',
    components: {LineTitle},
    props: {
        search: {
            type: Boolean,
            default: true
        },
        check: {
           type: Boolean,
           default: false
        },
        allData: {
            type: Object,
            default: () => {}
        },
        dialogTitle: {
            type: String,
            default: '新建人群包'
        },
        tagGroupList: {
            type: [Array, String],
            default: ''
        },
        tagList: {
            type: Array,
            default: () => []
        },
        showGenerate: {
            type: Boolean,
            default: true
        } // 是否生成，如果只是保存直接emit当前选择的数据
    },
    data () {
        const userInfo = this.$taro.getStorageSync('token').result;
        return {
            tagScroll: '', // 当前scroll-view位置
            lscspJson: '',
            currentTabId: '',                       // 当前选中的一级标签对象ID
            searchData: '',
            saveType: '',                          // 保存类型：保存、保存并生成
            formData: {},
            formRules: {
                description: this.Validator.required(),
                crowdSelection: [this.Validator.required(), this.Validator.len({max: 30, maxMsg: '人群包名称字数超出长度30字！请检查'})]
            },
            dialogSaveFlag: false,
            chooseAllTagData: [],  // 被选中的标签数据
            userInfo,
            isCheck:true,
            itemShow:true,
            initialData: [], // 存储原始数据，覆盖深层标签的数据
            tagGroupsData: [], // 一级标签
            searchGroupsData: [], //搜索出来的标签
            selectItem: {}, // 当前选择一级标签
            secondLevelData: [], // 二级标签
            secondSelect: {}, // 上次选择二级标签
            thirdLevelData: [] // 三级标签
        }
    },
    async created () {
        if (this.allData) {
            this.lscspJson = this.allData['lscspJson'] || '';
        }
        this.formData = this.allData;
        if(!this.tagGroupList){
            await this.queryAccntTagGroup();
        }
        const selectItem = this.$store.getters['consumerLabel/getSelectItem'];
        if(selectItem && Object.keys(selectItem).length > 0){
            this.afterChoose(selectItem);
        }
        const secondSelect = this.$store.getters['consumerLabel/getSecondSelect'];
        if(secondSelect && Object.keys(secondSelect).length > 0){
            this.secondSelect = secondSelect;
        }
    },
    watch: {
        allData: {
            deep: true,
            handler(newVal) {
                this.formData = newVal;
                if (newVal) {
                    this.lscspJson = newVal['lscspJson'] || '{}';
                }
            }
        },
        tagGroupList: {
            deep: true,
            immediate: true,
            handler(newVal) {
                if(newVal){
                    if(typeof newVal === 'object' && newVal.length > 0){
                        let arr = this.treeFilter(newVal, node => node.valueList.length > 0);
                        this.initialData = this.$utils.deepcopy(arr);
                        this.chooseTagData(arr);
                        this.tagGroupsData = this.$utils.deepcopy(arr);
                    }else{
                        this.tagGroupsData = [];
                        this.initialData = [];
                    }
                }
            }
        }
    },
    methods: {
        searchConfirm(val){
            if(!val){
                this.clearSearch();
                return
            }
            if (this.searchGroupsData.length > 0){
                this.clearSearch();
            }
            this.searchGroupsData = this.$utils.deepcopy(this.tagGroupsData);
            const arr = this.treeFilter(this.tagGroupsData, node => node.tagName === val || node.tagGroupName === val);
            this.tagGroupsData = arr;
            if(arr.length > 0){
                this.afterChoose(arr[0]);
            }else{
                this.secondLevelData = [];
                this.thirdLevelData = [];
            }
        },
        /**
         @desc: 清空查询后所做操作
         @author: wangbinxin
         @date 2023-09-21 17-48
         **/
        clearSearch() {
            if(this.tagGroupsData.length > 0){
                const choose = this.$utils.deepcopy(this.tagGroupsData)[0].id;
                let arr = this.saveTreeData(this.tagGroupsData);
                this.chooseTagData(this.searchGroupsData, arr);
                this.tagGroupsData = this.$utils.deepcopy(this.searchGroupsData);
                for (let i = 0; i < this.tagGroupsData.length; i++) {
                    if(this.tagGroupsData[i].id === choose){
                        this.afterChoose(this.tagGroupsData[i]);
                        this.$nextTick(() => {
                            this.tagScroll = `scroll${i}`;
                        });
                        return;
                    }
                }
            }else{
                this.tagGroupsData = this.$utils.deepcopy(this.searchGroupsData);
                this.afterChoose(this.selectItem);
            }
        },
        /**
         @param tree 树结构数据 ids 保存的id
         @desc: 处理要重新渲染的数据
         @author: wangbinxin
         @date 2023-09-19 20-43
         **/
        saveTreeData(tree,ids = []){
            // 使用map复制一下节点，避免修改到原树
            tree.forEach((node) => {
                if(node.checked){
                    ids.push(node.id)
                }
                let arr = node.itemList?node.itemList.concat(node.valueList):null;
                arr && this.saveTreeData(arr, ids);
            });
            return ids;
        },
        /**
         @desc: 临时保存数据
         @author: wangbinxin
         @date 2023-09-19 19-35
         **/
        setTagData(){
            if (this.searchData){
                this.clearSearch()
            }
            let data = this.$utils.deepcopy(this.tagGroupsData);
            this.checkedData(data);
            this.$store.commit('consumerLabel/setTagList', this.saveTreeData(data));
            this.$store.commit('consumerLabel/setSelectItem', this.selectItem);
            this.$store.commit('consumerLabel/setSecondSelect', this.secondSelect);
            let tempLsc = [];
            if (this.lscspJson) {
                tempLsc = JSON.parse(this.lscspJson);
            }
            let tempArr = [];
            this.chooseAllTagData.forEach((item) => {
                if (item.valueList.length > 0) {
                    item.valueList.forEach((item1)=> {
                        tempArr.push(item1.tagId);
                    })
                }
            });
            const labelData = {tag: tempArr, qps: tempLsc['qps'] || [], qpsShow: tempLsc['qpsShow'] || []};
            this.$store.commit('consumerLabel/setConsumerLabel', labelData);
        },
        /**
         @param tagList: 数组数据
         @param data: tree
         @desc: 将当前选择的数据展示出来
         @author: wangbinxin
         @date 2023-09-17 12-37
         **/
        chooseTagData(data, tagList = this.tagList){
            tagList.forEach(val => {
                const fn = (data) => {
                    for (let i = 0; i < data.length; i++) {
                        if (data[i] && data[i].id === val) {
                            data[i].checked = true;
                            // 如果data是单选，去除已经checkd的数据，重新赋值
                            if(data[i].tagUniqVerify === 'TagGroup'){
                                if(data[i].itemList.length > 0 || data[i].valueList.length > 0){
                                    if(data[i].itemList.length){
                                        data[i].itemList.forEach(dti => {
                                            dti.checked = false;
                                        })
                                    }else{
                                        data[i].valueList.forEach(dtv => {
                                            dtv.checked = false;
                                        })
                                    }
                                }
                            }
                            return [data[i].id];
                        }
                        let arr = data[i].itemList || data[i].valueList?data[i].itemList.concat(data[i].valueList):[];
                        if (data[i] && arr.length > 0) {
                            let d = fn(arr, val)
                            if (d) {
                                data[i].checked = true;
                                return d.concat(data[i].id)
                            }
                        }
                    }
                }
                fn(data);
            })
        },
        /**
         @param 标签数据
         @desc: 只展示四层的数据,同时处理数据展示父级名称
         @author: wangbinxin
         @date 2023-09-13 16-54
         **/
        treeFilter (tree, func) {
            // 使用map复制一下节点，避免修改到原树
            return tree.map(node => ({ ...node })).filter(node => {
                if(this.searchData)node.checked = true;
                let valueFlag = func(node);
                if(node.itemList && node.itemList.length > 0 && !valueFlag){
                    node.itemList = node.itemList && this.treeFilter(node.itemList, func);
                }
                if(this.searchData && !valueFlag && node.valueList && node.valueList.length > 0){
                    node.valueList = this.treeFilter(node.valueList, func);
                    valueFlag = valueFlag||(node.valueList && node.valueList.length);
                }
                valueFlag = valueFlag||(node.itemList && node.itemList.length);
                return valueFlag;
            })
        },
        showTagList() {
            this.itemShow = !this.itemShow;
        },
        /**
         * @desc 保存数据
         * <AUTHOR>
         * @date 2023/9/7 23:42
         **/
        async saveAllData () {
            await this.$refs.crowdFormData.validate();
            if (this.$utils.isEmpty(this.formData.crowdSelection)) {
                this.$message.warn('请输入人群包名称');
                return;
            }
            if (this.$utils.isEmpty(this.formData.description)) {
                this.$message.warn('请输入人群包描述');
                return;
            }
            let tempLsc = {qps: []};
            if (this.lscspJson) {
                tempLsc = JSON.parse(this.lscspJson);
            }
            let tempArr = [];
            this.chooseAllTagData.forEach((item) => {
                if (item.valueList.length > 0) {
                    item.valueList.forEach((item1)=> {
                        tempArr.push(item1.tagId);
                    })
                }
            });
            if (tempArr.length < 1 && (!tempLsc['qps'] || tempLsc['qps'].length < 1)) {
                this.$message.warn('请至少选择一个筛选条件!');
                return;
            }
            this.dialogSaveFlag = false;
            this.formData.status = 'saved';
            if (this.formData.lscspJson) {
                delete this.formData.lscspJson;
            }
            if (this.formData.row_status === ROW_STATUS.NEW) {
                if (!tempLsc['qps']) {
                    tempLsc['qps'] = [];
                }
                tempLsc['qps'].push({id: 'postnId', property: 'postnId', value: this.userInfo.postnId, operator: '='});
            }
            const labelData = {tag: tempArr, qps: tempLsc['qps'] || [], qpsShow: tempLsc['qpsShow'] || []};
            let param = Object.assign({},  {lscspJson: JSON.stringify(labelData)}, this.formData);
            if (this.saveType === 'saveAndGenerate') {
                let flag = '';
                let executeUrl = '/link/cdcConScreen/insertExecute';
                if (this.formData.row_status === ROW_STATUS.UPDATE) {
                    executeUrl = '/link/cdcConScreen/update';
                    flag = 'update';
                }
                delete this.formData.row_status;
                const otherData = await this.$http.post(this.$env.dmpURL + executeUrl, param, {
                    autoHandleError: false,
                    handleFailed: (res)=> {
                        this.$message.error('保存失败！' + res.result);
                    }
                });
                if (otherData.success) {
                    this.$message.success('保存成功！');
                    if (otherData.newRow){
                        this.formData = otherData.newRow;
                    }
                    if (flag === 'update') {
                        await this.$http.post(this.$env.dmpURL + '/link/cdcConScreen/execute', {id: this.formData.id}, {
                            autoHandleError: false,
                            handleFailed: (res) => {
                                this.$message.error('执行失败！' + res.result);
                            }
                        });
                    }
                    this.$nav.back();
                }
            } else {
                param.status = 'saved';
                let url = '/link/cdcConScreen/insert';
                if (this.formData.row_status === ROW_STATUS.UPDATE) {
                    url = '/link/cdcConScreen/update'
                }
                delete this.formData.row_status;
                const data = await this.$http.post(this.$env.dmpURL + url, param, {
                    autoHandleError: false,
                    handleFailed: (res)=> {
                        this.$message.error('保存失败！' + res.result);
                    }
                });
                if (data.success) {
                    this.$message.success('保存成功！');
                    this.formData = {};
                    data.newRow.row_status = ROW_STATUS.UPDATE;
                    this.$emit('update:allData', data.newRow);
                    this.$nav.back();
                }
            }
        },
        /**
         * @desc 保存数据或保存并且生成数据
         * <AUTHOR>
         * @date 2023/9/6 16:03
         * @param type 保存方法
         **/
        async saveData (type) {
            if(this.searchData){
                this.clearSearch();
            }
            this.chooseAllTagData = [];
            let data = this.$utils.deepcopy(this.tagGroupsData);
            let checkedData = this.checkedData(data).toString();
            if(this.showGenerate){
                this.dialogSaveFlag = true;
                this.saveType = type;
            }else{
                if(this.check && checkedData.indexOf('false') > -1){
                    this.$message.warn('请保证层级正确');
                    return;
                }
                this.$emit('save', this.chooseAllTagData)
            }
        },
        /**
         @desc: 处理数据
         @author: wangbinxin
         @date 2023-09-13 22-42
         **/
        checkedData(data, str, checkList = [], paernt = {}){
            let arr = data.filter((item) => item.checked)
            if(arr.length < 1){
                return false;
            }else{
                if(str === 'valueList'){
                    return true;
                }
                arr.forEach((item) => {
                    if(item.tagLevel === 'oneLevel'){
                        paernt = {}
                        paernt.l1TagId = item.id;
                        paernt.l1TagName = item.tagGroupName;
                    }else if(item.tagLevel === 'twoLevel'){
                        paernt.l2TagId = item.id;
                        paernt.l2TagName = item.tagGroupName;
                    }
                    if(item.itemList.length > 0 || item.valueList.length > 0){
                        if(item.itemList.length > 0){
                            let b = this.checkedData(item.itemList, 'itemList', [],paernt);
                            checkList.push(b);
                        }else{
                            let a = this.checkedData(item.valueList, 'valueList', [], paernt);
                            checkList.push(a);
                            let copyItem = this.$utils.deepcopy(item);
                            copyItem = Object.assign(copyItem, paernt);
                            copyItem.valueList = copyItem.valueList.filter((cp) => cp.checked);
                            this.chooseAllTagData.push(copyItem)
                        }
                    }
                });
            }
            return checkList;
        },
        /**
         * @desc 选择标签
         * <AUTHOR>
         * @date 2023/9/6 15:53
         * @param tagItem 被选中的标签
         * @param index 数组下标
         * @param parentData 父对象
         **/
        chooseTag(tagItem, index, parentData) {
            if(parentData.tagUniqVerify) {
                tagItem.checked = !tagItem.checked;
                parentData.valueList.splice(index, 1, tagItem)
                if (parentData.tagUniqVerify !== 'TagItem' && tagItem.checked) {
                    parentData.valueList.forEach((item)=> {
                        if (item.id !== tagItem.id) {
                            item.checked = false;
                        }
                    });
                }
                let arr = parentData.valueList.filter((item) => item.checked);
                parentData.checked = arr.length > 0;
            }else{
                this.$message.warn('数据维护错误，请检查数据后在操作');
            }
        },
        /**
         * @desc 一级标签切换
         * <AUTHOR>
         * @date 2023/9/6 15:27
         * @param tab 选中的一级标签对象
         **/
        chooseTab (tab, index) {
            if(tab.checked && this.selectItem.id !== tab.id){
                this.afterChoose(tab);
                return;
            }
            tab.checked = !tab.checked;
            if(tab.checked){
                //如果是单选先置空
                this.afterChoose(tab);
            }else{
                const data = this.initialData.filter((item) => item.id === tab.id)[0]
                this.tagGroupsData.splice(index, 1, this.$utils.deepcopy(data));
                this.selectItem = {};
                this.secondLevelData = [];
                this.thirdLevelData = [];
            }
        },
        afterChoose(tab){
            this.selectItem = tab;
            this.secondLevelData = tab['itemList'];
            this.thirdLevelData = [];
            const currentIndex = this.secondLevelData.findIndex((item)=> {
                return item.checked;
            });
            if (currentIndex > -1) {
                this.thirdLevelData = this.secondLevelData[currentIndex]['itemList'];
            }
            this.currentTabId = tab.id;
        },
        /**
         * @desc 选择二级标签
         * <AUTHOR>
         * @date 2023/9/6 15:39
         * @param tabItem 被选中的二级标签
         **/
        chooseSecondTab (tabItem, index) {
            if(this.selectItem.tagUniqVerify){
                if(!tabItem.checked){
                    this.secondSelect = tabItem;
                }
                // 如果是多选，checked为false时，直接赋值。checked为true时，判断当前forcus是哪条数据，如果是当前数据，取消选择，如果不是，forcus当前数据，展示当前数据内容
                if(this.selectItem.tagUniqVerify === 'TagItem'){
                    if(tabItem.checked){
                        if(this.secondSelect.id !== tabItem.id){
                            this.secondSelect = tabItem;
                            this.thirdLevelData = tabItem['itemList'];
                            return;
                        }
                    }
                }else{
                    this.secondLevelData.forEach((item) => {
                        if(item.id !== tabItem.id) item.checked = false;
                    })
                }
                tabItem.checked = !tabItem.checked;
                //如果是单选，那么清空之前的数据。如果是多选，保留原来数据，除非取消选择
                if(!tabItem.checked || this.selectItem.tagUniqVerify !== 'TagItem'){
                    let select = this.initialData.filter((dt)=>dt.id===this.selectItem.id)[0];
                    let selectTabItem = select.itemList.filter((st) => st.id === tabItem.id)[0];
                    tabItem.itemList = this.$utils.deepcopy(selectTabItem.itemList)
                }
                this.secondLevelData.splice(index, 1, tabItem);
                this.thirdLevelData = tabItem.checked?tabItem.itemList:[];
            } else {
                this.$message.warn('数据维护错误，请检查数据后在操作')
            }
        },
        /**
         * @desc 查询标签
         * <AUTHOR>
         * @date 2023/9/6 10:06
         **/
        async queryAccntTagGroup () {
            this.chooseAllTagData = [];
            const data = await this.$http.post(this.$env.appURL +  '/link/accntTagGroup/queryLevelData', {
                companyId: this.userInfo.coreOrganizationTile['l3Id']
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$message.error(`查询标签数据失败：${response.result}`);
                }
            });
            if (data.success) {
                let arr = this.treeFilter(data.result['quDaoTag'], node => node.valueList.length > 0);
                this.initialData = this.$utils.deepcopy(arr);
                this.chooseTagData(arr);
                this.tagGroupsData = this.$utils.deepcopy(arr);
            }
        }
    }
}
</script>
<style lang="scss">
.standard-labels {
    width: 100%;
    background-color: #fff;
    padding-bottom: 20px;
    /*deep*/.link-textarea-content {
    height:fit-content !important;
}
    .line-title{
        margin-bottom: 20px;
    }
    .scroll-view-data {
        white-space: nowrap;
    }
    .secondary-labels{
        width: 100%;
        display: flex;
        height: 80px;

    }
    .labels-default{
        display: inline-block;
        width: 25%;
        height: 78px;
        text-align: center;
        line-height: 78px;
        font-size: 24px;
        color: #999999;
    }
    .secondary-labels-active{
        color: #2F69F8 !important;
    }
    .secondary-labels-default {
        width: auto;
        white-space: nowrap;
        height: 78px;
        text-align: center;
        line-height: 78px;
        font-size: 24px;
        color: #999999;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-right: 20px;
        //border-bottom: 1px solid #E5E5E5;
        margin-right: 20px;
        .tag-name{
            line-height: 73px;
        }
        .up {
            border: 1px solid #E5E5E5;
            border-width: 0 1px 1px 0;
            display: inline-block;
            padding: 6px;
            transform: rotate(-135deg);
            -webkit-transform: rotate(-135deg);
            width: 5px;
            height: 5px;
        }
    }
    .secondary-labels-default:first-child{
        padding-left: 20px;
    }
    .secondary-third-labels{
        width: 100%;
        display: flex;
        flex-wrap: wrap;
    }
    .third-labels-scroller{
        margin-top: 18px;
    }
    .third-labels{
        display: inline-block;
        white-space: nowrap;
    }
    .third-labels-active{
        height: 56px;
        line-height: 56px;
        text-align: center;
        color:#2F69F8;
        border:1px solid #2F69F8;
        border-radius: 30px;
        padding:0 24px;
        font-size: 24px;
        margin-left:  24px;
        display: inline-block;
    }
    .third-labels-default{
        height: 56px;
        line-height: 56px;
        text-align: center;
        color:#333333;
        border:1px solid #DDDDDD;
        border-radius: 30px;
        padding:0 24px;
        font-size: 24px;
        margin-left:  24px;
        display: inline-block;
    }
    .third-level-data {
        padding-bottom: calc(env(safe-area-inset-bottom) + 24px + 40px);
    }
    .other-bottom{
        padding-bottom: calc(env(safe-area-inset-bottom) + 80px + 24px + 40px) !important;
    }
    .tag-header {
        display: block;
        margin-bottom: 24px;
        margin-top: 54px;
        width: 100%;
        .tag-title {
            display: flex;
            margin-left: 24px;
            .required {
                color: red;
                font-size: 32px;
                line-height: 64px;
                margin-right: 4px;
            }

            .tag-title-text {
                font-size: 32px;
                color: #333333;
                line-height: 48px;
                font-weight: 600;
            }

            .link-icon {
                font-size: 32px;
                color: #CCCCCC;
                margin: 8px 0 0 8px;
            }
        }
    }
    .classify-filter-content {
        padding-bottom: 16px;
        display: block;
        width: 100%;
        .classify-filter-list {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            .classify-filter-item {
                min-width: 120px;
                min-height: 72px;
                box-sizing: border-box;
                margin: 0 0 16px 24px;
                font-size: 24px;
                color: #333333;
                letter-spacing: 0;
                text-align: center;
                line-height: 72px;
                font-weight: 400;
                background: #f8f8f8;
                border-radius: 8px;
                padding: 0 10px;
            }

            .label-checked {
                min-width: 120px;
                min-height: 72px;
                box-sizing: border-box;
                margin: 0 0 16px 24px;
                font-size: 24px;
                color: #3F66EF;
                letter-spacing: 0;
                text-align: center;
                line-height: 72px;
                font-weight: 400;
                background: #F0F5FF;
                border-radius: 8px;
            }
        }
    }
    .model-title{
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 20px;
        .title{
            width: 56%;
        }
        .iconfont {
            font-size: 40px;
        }
    }
    .link-dialog-foot-custom{
        width: auto !important;
    }
}
</style>
