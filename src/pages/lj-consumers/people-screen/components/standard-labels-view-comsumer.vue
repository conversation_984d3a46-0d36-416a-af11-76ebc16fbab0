<template>
  <view class="label-comsumer">
    <view class="no-labels" style="background-color: #f2f2f2;" v-if="revisionTagData_noself.length === 0 && revisionTagData.length === 0">暂无标签</view>
    <view class="labels-view" v-else>
      <!-- <lnk-taps :taps="subObjOptions" v-model="subObjActive" @switchTab="switchTab"></lnk-taps> -->
      <selectButton :btns="subObjOptions"  :current-dimension="subObjActive.seq" @change-btn="switchTab"></selectButton>
      <view v-show="subObjActive.seq==='1'" class="pt12">
        <sub-tabs v-show="revisionTagData_noself.length" :currentTab="showFirst" :tabs="tabs_noself" :is-scroll="true" @switchTab="showFirstClick"></sub-tabs>
        <view v-for="(items, index) in revisionTagData_noself" :key="items.tagName + index" class="tag-content">
          <view class="classify-filter-list"  v-show="showFirst==index">
            <view v-for="(tagItem, tagIndex) in items.children" :key="tagIndex" :class="['classify-filter-item']">
                <view class="item-head">
                    <view class="left-icon">{{ tagIndex+1 }}</view>
                    <view class="cneter-cont">
                        {{tagItem.tagName}}
                        <!-- （{{ items.tagTwoName }}） -->
                    </view>
                    <view class="right-cont">{{ tagItem.children.length }}类标签</view>
                </view>
                <view :class="['scrollitem',tagItem.collp&&'h400']" >
                <view :id="tagItem.domId">
                    <view v-for="(itm, i) in tagItem.children" :key="i" class="item-list">
                        <view class="tag4label">
                            {{itm.tagGroupName}}:
                        </view>
                        <view v-for="(tag,i) in itm.tags" :key="i" class="tag4tags">
                            {{tag}}
                        </view>
                    </view>
                </view>
                </view>
                <view class="collp" v-show="tagItem.showCollp" @tap="collpClick(tagItem)" @click="collpClick(tagItem)">{{  tagItem.collp ? '收起':'展开' }}
                    <link-icon icon="mp-arrow-top" :class="[tagItem.collp ? '':'rotate180']"></link-icon>
                </view>
            </view>
          </view>
        </view>
        <view class="no-labels" v-if="revisionTagData_noself.length === 0">暂无标签</view>
      </view>
      <view v-show="subObjActive.seq==='2'" class="pt12">
       <sub-tabs v-show="revisionTagData.length" :currentTab="showSelfTab" :tabs="tabs_self" :is-scroll="true" @switchTab="showSelfTabClick"></sub-tabs>
        <view v-for="(items, index) in revisionTagData" :key="items.tagName + index" class="tag-content">
          <view class="classify-filter-list"  v-show="showSelfTab==index">
            <view v-for="(tagItem, tagIndex) in items.children" :key="tagIndex" :class="['classify-filter-item']">

                <view class="item-head">
                    <view class="left-icon">{{ tagIndex+1 }}</view>
                    <view class="cneter-cont">
                        {{tagItem.tagName}}
                        <!-- （{{ items.tagTwoName }}） -->
                    </view>
                    <view class="right-cont">{{ tagItem.children.length }}类标签</view>
                </view>
                <view :class="['scrollitem',tagItem.collp&&'h400']" >
                <view :id="tagItem.domId">
                    <view v-for="(itm, i) in tagItem.children" :key="i" class="item-list">
                        <view class="tag4label">
                            {{itm.tagGroupName}}:
                        </view>
                        <view v-for="(tag,i) in itm.tags" :key="i" class="tag4tags">
                            {{tag.tagName}}
                        </view>
                    </view>
                </view>
                </view>
                <view class="collp" v-show="tagItem.showCollp" @tap="collpClick(tagItem)" @click="collpClick(tagItem)">{{  tagItem.collp ? '收起':'展开' }}
                    <link-icon icon="mp-arrow-top" :class="[tagItem.collp ? '':'rotate180']"></link-icon>
                </view>
            </view>
          </view>
        </view>
        <view class="no-labels" v-if="revisionTagData.length === 0">暂无自定义标签</view>
      </view>
    </view>
  </view>
</template>

<script>
import LnkTaps from '../../../core/lnk-taps/lnk-taps'
import LineTitle from "../../../lzlj/components/line-title";
import subTabs from '../../account/components/sub-tabs/sub-tabs';
import selectButton from 'src/pages/lj-consumers/account/components/account-sub-item/components/select-button.vue'
export default {
  name: "label-comsumer",
  components: { LnkTaps, LineTitle, subTabs,selectButton },
  props: {
    // 查看标签类型
    type: {
      type: String,
      default: 'label'
    },
    // 已选标签id数组
    tagList: {
      type: Array,
      default: () => []
    },
    // 已选标签对象数组
    tagGroups: {
      type: Array,
      default: () => []
    },
    // 消费者手机号码
    phoneNumber: {
      type: String,
      default: ''
    },
    // 消费者所属公司id
    companyId: {
      type: String,
      default: ''
    },
    // 消费者id
    consumerId: {
      type: String,
      default: ''
    }
  },
  computed:{
    tabs_noself(){
        return this.revisionTagData_noself.map((e,ind)=>({name:e.tagName,val:String(ind)}))
    },
    tabs_self(){
        return this.revisionTagData.map((e,ind)=>({name:e.tagName,val:String(ind)}))
    },
  },
  data () {
    const userInfo = this.$taro.getStorageSync('token').result;
    return {
      userInfo,
      tagGroupsData: [],   // 一级标签
      matchData: [],
      statisticDataAll: [],
      currentSelectTags: [],
      revisionTagData: [],
      revisionTagData_noself: [],
      statisticData: [],
      originTreeData_Self: null,
      showFirst: '0',
      showSelfTab: '0',
      originTreeData: null,
      originTagInfo_notag: null,
      pageSize: 20,
      currentPage: 1,
      subObjActive: { name: "标签列表", seq: "1", val: "standardLabel" },
      subObjOptions: [
        { name: "标签列表", seq: "1", id:'1', val: "standardLabel" },
        { name: "自定义标签", seq: "2", id:'2', val: "customLabel" },
      ]
    }
  },
  watch: {
    tagList (val) {
      if (val) {
        this.treeFilter();
      }
    }
  },
  async created () {
    await this.getCnName(); // 标签

    // 消费者详情页面标签展示
    await this.queryTreeData('selfTag');// 自定义标签
    await this.queryTagsByConsumerIdNew();
    this.initCollpClick();
  },
  methods: {
    // 设置是否展示展开/收起
    setHideCollp(activeData,ind){
        const activeTag = this[activeData][ind]
        if(!activeTag)return
        const that = this;
        const query = wx.createSelectorQuery();
        this.$nextTick(e=>{
            setTimeout(function () {
                activeTag.children.forEach((ele,i)=>{
                    query.select(`#${ele.domId}`).boundingClientRect((ret) => {
                        that.$set(that[activeData][ind]['children'][i],'showCollp',ret.height > 89);
                    }).exec()
                })
            }, 200)
        })
    },
    // 初始化 第一个展开
    initCollpClick(){
        this.revisionTagData.forEach(e=>{
            e.children&&e.children[0]&&this.collpClick(e.children[0])
        })
        this.revisionTagData_noself.forEach(e=>{
            e.children&&e.children[0]&&this.collpClick(e.children[0])
        })
        this.setHideCollp('revisionTagData_noself',0);
    },
    collpClick(tagItem){
        this.$set(tagItem,'collp',!tagItem.collp)
    },
    showFirstClick(index){
        this.setHideCollp('revisionTagData_noself',index)
        this.showFirst=index
    },
    showSelfTabClick(index){
        this.setHideCollp('revisionTagData',index)
        this.showSelfTab=index
    },
    switchTab (val, key) {
      this.subObjActive = this.subObjOptions.find(e=>e.id==val);
      this.setHideCollp(val.seq==1?'revisionTagData_noself':'revisionTagData',0);
    },
    /**
     @desc: 将  转为树形结构
     @author: wangbinxin
     @date 2023-09-20 15-42
     **/
    async queryTagsByConsumerIdNew () {

      const data = await this.$http.post(this.$env.dmpURL + '/link/consumerTags/queryTagsByConsumerIdNew', {
        acctId: this.consumerId,
        ifActive: 'Y',
        pageFlag: false
      }, {
        autoHandleError: false,
        handleFailed: (response) => {
          this.$message.error(`查询标签数据失败：${response.result}`);
        }
      });
      const consumerTagsList = this.$utils.deepcopy(data.rows);
      this.originSelectTags = consumerTagsList;
      const map = {};
      for (let i = 0; i < consumerTagsList.length; i++) {
        const item = consumerTagsList[i];
        if (!map[item.tagGroupId]) {
          map[item.tagGroupId] = [item];
        } else {
          map[item.tagGroupId].push(item);
        }
      }
      this.currentSelectTags = map;
      const result = this.filterConsumerTags(this.originTreeData_Self, map);
      if (result && result.length > 0) {
        result[0].tagsFold = true;
      }
      result&&result.forEach(e=>e.children.forEach(el=>{
            el.domId = 'domId'+(Math.random()*10000000).toFixed() // 加dom用于怕断高度限制是否展示(展开/收起)
            el.showCollp = false; // 初始化时带上
          }));
      console.log('777', result);
      this.revisionTagData = result;
    },
    /**
* @description 标签UI改版--数据重新构建逻辑
* @Author: 胡政民
* @Date: 2024/10/31
* @param: arr - 待处理的数组
* @param: keys - 用于过滤的键值对对象
* @param: level - 递归层级，默认为 1
*/
    filterConsumerTags (arr, keys, level = 1) {
      // 递归处理 arr 数组
      return arr.map(tag => {
        const filteredChildren = tag.children ? this.filterConsumerTags(tag.children, keys, level + 1) : [];

        // 如果 tag 对象中包含 tagGroupId 属性并且 keys 中存在对应的 key
        const hasMatchingTag = tag.tagGroupId && keys.hasOwnProperty(tag.tagGroupId);

        // 如果有匹配项，或 filteredChildren 结果不为空，则保留该标签
        if (hasMatchingTag || filteredChildren.length > 0) {
          let obj = {};
          switch (level) {
            case 1:
              const tagTwoName = filteredChildren.map(child => child.tagName).join('、');
              obj = { tagsFold: false, tagTwoName }; // 添加 1.tagsFold 属性 用于控制标签是否展开 2.tagTwoName: 用于展示二级标签名称
              // 恢复折叠面板操作前的展开状态
              this.revisionTagData.map(item => {
                if (item.tagName === tag.tagName) {
                  obj.tagsFold = item.tagsFold;
                }
              })
              break;
            case 3:
              const tags = [];
              keys[tag.tagGroupId].map(item => { tags.push({ ...item }) });
              obj = { tags }; // 自定义 tags 数组 用于展示标签值
              break;
          }
          return { ...tag, children: filteredChildren, level, ...obj };
        }

        return null;
      }).filter(tag => tag !== null); // 过滤掉不匹配的项
    },
    /**
     @param arr 全量标签租数据
     @desc: 对树形数据进行筛选，只展示当前tagList里的数据
     @author: wangbinxin
     @date 2023-09-16 10-30
     **/
    treeFilter () {
      const showTagGroup = [];
      this.tagGroupsAll.forEach((tagGroup) => {
        const valueList = tagGroup.valueList.filter((tag) => this.tagList.includes(tag.tagId));
        if (valueList?.length > 0) {
          showTagGroup.push({
            tagGroupId: tagGroup.tagGroupId,
            tagGroupName: tagGroup.tagGroupName,
            valueList: valueList
          })
        }
      })
      this.tagGroupsData = showTagGroup;
    },
    /**
     @desc: 查询统计标签
     @author: wangbinxin
     @date 2023-09-25 11-04
     **/
    async getStastics () {
      const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmpSr/queryTagDetailInformation', {
        id: this.consumerId
      }, {
        autoHandleError: false,
        handleFailed: (response) => {
          this.$utils.hideLoading();
          this.$message.error(`查询标签数据失败：${response.result}`);
        }
      });
      if (data.success && data.rows) {
        // 保留接口返回需要展示的标签键值对
        this.originTagInfo_notag = data.rows;

        this.matchData.forEach((item) => {
          if (data.rows[item.fieldEnName] || data.rows[item.fieldEnName] === 0) {
            item.tags = String(data.rows[item.fieldEnName]).split(',');
          } else {
            item.tags = '';
          }
        })
        this.statisticDataAll = this.matchData.filter((item) => item.tags);
        this.statisticData = this.statisticDataAll.slice(0, this.pageSize);

        // 查询出的数据不满this.pageSize条，不显示“加载更多”，显示“已全部加载完成”
        if (this.statisticData.length === this.statisticDataAll.length) {
          this.showLoadMore = false;
        }

        await this.queryTreeData();
      }
    },
    /**
     * @createdBy 黄鹏
     * @date 2024/01/04
     * @methods: loadMore
     * @para:
     * @description: 分页加载数据
     **/
    loadMore () {
      this.currentPage += 1;
      this.statisticData = this.statisticDataAll.slice(0, this.pageSize * this.currentPage);
    },
    /**
     * @createdBy 黄鹏
     * @date 2024/01/03
     * @methods: getCnName
     * @para:
     * @description: 获取标签中英文对应数据
     **/
    async getCnName () {
      this.$utils.showLoading();
      const tableId = await this.$utils.getCfgProperty('TAG_MATCH');
      const data = await this.$http.post(this.$env.appURL + '/action/link/cirSelSchdField/queryByExamplePage', {
        filtersRaw: [
          { id: 'deleteFlag', property: 'deleteFlag', value: 'N' },
          { id: 'tableId', property: 'tableId', value: tableId },
          { id: 'labelType', property: 'labelType', value: '[1, 3, 7]', operator: 'in' }
        ],
        rows: 10000
      }, {
        autoHandleError: false,
        handleFailed: (response) => {
          this.$utils.hideLoading();
          this.$message.error(`查询标签中英文对应数据失败：${response.result}`);
        }
      });
      if (data.success) {
        this.matchData = data.rows.map((item) => {
          return {
            fieldCnName: item.fieldCnName,
            dsyTagGroupId: item.dsyFieldId,
            fieldEnName: item.fieldEnName
          }
        });
        await this.getStastics();

      }
    },
    /**
     * @desc 查询标签组数据
     * <AUTHOR>
     * @date 2023/9/6 10:06
     **/
    async queryTreeData (sefTag) {
      this.$utils.showLoading();

      const data = await this.$http.post(this.$env.appURL + '/link/tagOrgTree/queryTreeTag', {
        filtersRaw: [
          sefTag && { id: 'deleteFlag', property: 'deleteFlag', value: 'N' },
          sefTag && { id: 'publishStatus', property: 'publishStatus', value: '2' },
          { id: 'labelType', property: 'labelType', value: sefTag ? '7' : '1' }
        ].filter(e => !!e)
      }, {
        autoHandleError: false,
        handleFailed: (response) => {
          this.$utils.hideLoading();
          this.$message.error(`查询标签组数据失败：${response.result}`);
        }
      });
      if (data.success) {
        this.$utils.hideLoading();
        const consumerTagsList = JSON.parse(JSON.stringify(data.rows)); // 深度复制以保留原始数据
        if (sefTag) {
          this.originTreeData_Self = consumerTagsList;
        } else {

          /**
          * @description 标签UI改版--数据重新构建逻辑
          * @Author: 胡政民
          * @Date: 2024/10/30
          * @param: arr - 待处理的数组
          * @param: keys - 用于过滤的键值对对象
          * @param: level - 递归层级，默认为 1
          */
          const filterConsumerTags_noself = (arr, keys, level = 1) => {
            // 递归处理 arr 数组
            return arr.map(tag => {
              const filteredChildren = tag.children ? filterConsumerTags_noself(tag.children, keys, level + 1) : [];

              // 如果 tag 对象中包含 dsyTagEnName 属性并且 keys 中存在对应的 key
              const hasMatchingTag = tag.dsyTagEnName && keys.hasOwnProperty(tag.dsyTagEnName);

              // 如果有匹配项，或 filteredChildren 结果不为空，则保留该标签
              if (hasMatchingTag || filteredChildren.length > 0) {
                let obj = {};
                switch (level) {
                  case 1:
                    const tagTwoName = filteredChildren.map(child => child.tagName).join('、');
                    obj = { tagsFold: false, tagTwoName }; // 添加 1.tagsFold 属性 用于控制标签是否展开 2.tagTwoName: 用于展示二级标签名称
                    break;
                  case 3:
                    obj = { tags: keys[tag.dsyTagEnName].split(',') }; // 自定义 tags 数组 用于展示标签值
                    break;
                }
                return { ...tag, children: filteredChildren, level, ...obj };
              }

              return null;
            }).filter(tag => !!tag); // 过滤掉不匹配的项
          };

          // 示例调用
          const result = filterConsumerTags_noself(consumerTagsList, this.originTagInfo_notag);
          if (result && result.length > 0) {
            result[0].tagsFold = true;
          }
          result&&result.forEach(e=>e.children.forEach(el=>{
            el.domId = 'domId'+(Math.random()*10000000).toFixed() // 加dom用于怕断高度限制是否展示(展开/收起)
            el.showCollp = false; // 初始化时带上
        }));
          this.revisionTagData_noself = result;
          console.log("666", result);
          this.originTreeData = consumerTagsList
        }
      }
    },
    /**
     * @createdBy 黄鹏
     * @date 2024/01/02
     * @methods: getTags
     * @para:
     * @description: 根据标签组id获取标签值数据
     **/
    async getTags (tagGroups) {
      const tagGroupIds = tagGroups.map((item) => item.tagGroupId);
      const data = await this.$http.post(this.$env.appURL + '/link/crowdTagItem/queryByExamplePage', {
        filtersRaw: [{
          id: 'tagGroupId',
          property: 'tagGroupId',
          value: `[${tagGroupIds.join(',')}]`,
          operator: 'in'
        }, {
          id: 'validFlag',
          property: 'validFlag',
          value: 'Y',
          operator: '='
        }],
        rows: 1000
      }, {
        autoHandleError: false,
        handleFailed: (response) => {
          this.$message.error(`查询标签值数据失败：${response.result}`);
        }
      });
      if (data.success) {
        const tags = data.rows.map((row) => {
          return {
            tagName: row.tagValueName,
            tagId: row.tagValueId,
            tagGroupId: row.tagGroupId
          }
        })
        tagGroups.forEach((tagGroup) => {
          tags.forEach((tag) => {
            if (tag.tagGroupId === tagGroup.tagGroupId) {
              if (tagGroup.valueList) {
                tagGroup.valueList.push(tag);
              } else {
                tagGroup.valueList = [tag];
              }
            }
          })
        })
        this.tagGroupsAll = tagGroups;
        this.treeFilter();
      }
    }
  }
}
</script>

<style lang="scss">
.label-comsumer {
    .rotate180{
        transform: rotate(180deg);
    }
  .no-labels {
    text-align: center;
    background-color: #fff;
    line-height: 80px;
    color: #555;
    font-size: 24px;
  }
  .pt12 {
    padding-top: 12rpx;
    padding-bottom: 144px;
  }
  .labels-view {
    padding: 24px 24px 0 24px;

    .third-level-data {
      padding-bottom: calc(env(safe-area-inset-bottom) + 24px);
    }

    .tag-header {
      display: inline-block;
      margin-bottom: 24px;
      margin-top: 24px;

      .tag-title {
        display: inline-block;
        margin-left: 24px;
        width: auto;
        height: 60rpx;
        background: #E6E6E6;
        border-radius: 6rpx;
        position: relative;font-weight: 400;
        font-size: 24rpx;
        color: #333333;
        line-height: 44rpx;
        vertical-align: middle;
        padding: 8rpx 28rpx;
        &.active{
            background: linear-gradient(to right,#2F69F8,#5CA2FC);
            color: white;
            font-size: 28rpx;
            &::after{
                content:' ';
                width: 8rpx;
                height: 8rpx;
                position: absolute;
                bottom: 10rpx;
                left: 50%;
                transform: rotate(45deg);
            }
        }
      }
    }

    .tag-content {
      display: block;
      width: 100%;
      .classify-filter-list {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        padding-top: 12rpx;
        .classify-filter-item {
          width: 100%;
          padding: 1px;
        //   background: -webkit-gradient(linear, left top, left bottom, from(#D0DDFF), to(#D2EBFF));
          box-sizing: border-box;
          font-size: 28px;
          color: #333333;
          letter-spacing: 0;
          text-align: center;
          line-height: 48px;
          font-weight: 400;
          border-radius: 32px;
          position: relative;
          overflow: hidden;
          border:2px solid #D0DDFF;
          margin-bottom: 16px;

          .collp{
            font-weight: 400;
            font-size: 24px;
            color: #BBBBBB;
            background: #fff;
            position: absolute;
            bottom: 0;
            padding: 12px 0px 12px 12px;
            right: 20px;
            z-index: 2;
            vertical-align: middle;
						border-radius: 0 0 42px 0;
          }
          .item-head{
            background: linear-gradient(90deg, #E2ECFF 0%, #D2EBFF 100%);
            display: flex;
            align-items: center;
            padding:0 26px;
            height: 76px;
            border-radius: 32px 32px 0 0;
            .left-icon{
                width: 46px;
                height: 46px;line-height: 46px;
                background: radial-gradient(92.85% 92.85% at 15.38% 19.23%, #77ADFF 0%, #4B75FF 100%);
                border-radius: 8px;
                box-shadow: 0px 4px 8px 0px rgba(13,46,165,0.2);
                margin-right: 22px;font-family: Helvetica;font-style: italic;
                display: inline-block;
                vertical-align: middle;
                color: #fff;
                font-family: Helvetica;
                font-size: 32px;
            }
            .cneter-cont{
                font-weight: 600;
                font-size: 28px;
                color: #2553F5;
                flex: 1;
                text-align: left;
                white-space: nowrap; /* 禁止换行 */
                overflow: hidden; /* 隐藏超出内容 */
                text-overflow: ellipsis; /* 超出部分显示省略号 */
            }
            .right-cont{
                font-weight: 400;
                font-size: 24px;
                color: #6191FF;
                line-height: 36px;
                text-align: center;
            }
          }
          .scrollitem{
            // height: 132px;
            max-height: 132px;
            position: relative;
            overflow: hidden;
            border-radius: 0 0 32px 32px;
            padding:10px 97px 6px 30px;
            background-color: white;

            &.h400{
                max-height: 325px;
                height: auto;
                padding-bottom: 12px;
                overflow-y: auto;
            }
            .item-list{
                text-align: left;
                background: #fff;
                &:first{
                    padding-top: 12px;
                }
                .tag4label{
                    font-weight: 400;
                    font-size: 28px;
                    display: inline-block;
                    color: #333333;
                    line-height: 1.8;
                    margin: 8rpx 0 15rpx 0;
                }
                .tag4tags{
                    display: inline-block;
                    min-width: 66px;
                    padding: 2px 24px;
                    font-weight: 400;
                    font-size: 24px;
                    color: #666;
                    background: #F0F0F0;
                    border-radius: 28px;
                    text-align: center;
                    margin-left: 8px;
                    margin-top: 4px;
                    margin-bottom: 12px;
                }
            }
        }

        }
      }
    }
  }
}
</style>
