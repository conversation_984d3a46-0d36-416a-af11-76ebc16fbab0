<template>
    <view class="custom-labels-view">
        <line-title title="自定义圈选条件"/>
        <view class="custom-labels-content">
            <view class="custom-title">
                <view class="custom-title-item">标签选择</view>
                <view class="custom-title-item">圈选条件</view>
                <view class="custom-title-item">圈选值</view>
            </view>
            <view class="custom-list" v-for="(item, index) in customList" :key="index">
                <view class="custom-item">{{item.label}}</view>
                <view class="custom-item">{{item.operator}}</view>
                <view class="custom-item">{{item.value}}</view>
            </view>
        </view>
    </view>
</template>

<script>
import LineTitle from "../../../lzlj/components/line-title.vue";
export default {
    name: "custom-labels-view",
    components: {LineTitle},
    props: {
        qpsList: {
            type: Array,
            default: []
        },
        qpsShowList: {
            type: Array,
            default: []
        }
    },
    data () {
        return {
            conditionOperatorList: {                // 圈选条件
                'Default': [{label: '等于', operator: '='}],
                'BZencryption': [{label: '等于', operator: '='}],
                'BZtext': [{label: '等于', operator: 'like'}],
                'BZdigit': [
                    {label: '等于', operator: '='},
                    {label: '大于', operator: '>'},
                    {label: '小于', operator: '<'},
                    {label: '大于等于', operator: '>='},
                    {label: '小于等于', operator: '<='},
                    {label: '介于', operator: '>=,<='}
                ],
                'Number': [
                    {label: '等于', operator: '='},
                    {label: '大于', operator: '>'},
                    {label: '小于', operator: '<'},
                    {label: '大于等于', operator: '>='},
                    {label: '小于等于', operator: '<='},
                    {label: '介于', operator: '>=,<='}
                ],
                'BZtime': [
                    {label: '等于', operator: '='},
                    {label: '大于', operator: '>'},
                    {label: '小于', operator: '<'},
                    {label: '大于等于', operator: '>='},
                    {label: '小于等于', operator: '<='},
                    {label: '介于', operator: '>=,<='}
                ],
                'DateAndTime': [
                    {label: '等于', operator: '='},
                    {label: '大于', operator: '>'},
                    {label: '小于', operator: '<'},
                    {label: '大于等于', operator: '>='},
                    {label: '小于等于', operator: '<='},
                    {label: '介于', operator: '>=,<='}
                ],
                'BZdate': [
                    {label: '等于', operator: '='},
                    {label: '大于', operator: '>'},
                    {label: '小于', operator: '<'},
                    {label: '大于等于', operator: '>='},
                    {label: '小于等于', operator: '<='},
                    {label: '介于', operator: '>=,<='}
                ],
                'Date': [
                    {label: '等于', operator: '='},
                    {label: '大于', operator: '>'},
                    {label: '小于', operator: '<'},
                    {label: '大于等于', operator: '>='},
                    {label: '小于等于', operator: '<='},
                    {label: '介于', operator: '>=,<='}
                ],
                'BZPAC': [{label: '等于', operator: '='}],
                'ProvincialCity':  [{label: '等于', operator: '='}],
                'BZradio': [{label: '等于', operator: '='}],
                'BZmultipleChoice': [{label: '包含', operator: 'IN'}],
                'Radio': [{label: '等于', operator: '='}],
                'Checkbox': [{label: '包含', operator: 'IN'}]
            },
            customList: []
        }
    },
    created () {
        let arr = [];
        this.qpsShowList.forEach((item)=> {
            const operatorArr = this.conditionOperatorList[item.dataType];
            const operator = operatorArr.filter((oItem)=> oItem.operator === item.operator)[0];
            const qpsItem = item.showData.split(operator.label);
            arr.push({label: qpsItem[0], operator: operator.label, value:qpsItem[1]});
        });
        this.customList = arr;
    }
}
</script>

<style lang="scss">
.custom-labels-view{
    margin: 24px;
    background: white;
    border-radius: 16px;
    padding: 0 20px 24px 20px;
    .line-title{
        margin-bottom: 24px;
    }
    .custom-labels-content{
        .custom-title {
            background: #EAF0FA;
            font-size: 24px;
            color: #333333;
            letter-spacing: 0;
            text-align: center;
            font-weight: 600;
            height: 80px;
            display: flex;
            align-items: center;
            .custom-title-item:first-child{
                width: calc(30% - 24px);
                padding-left: 24px;
            }
            .custom-title-item{
                width: 20%;
            }
            .custom-title-item:last-child{
                width: calc(50% - 24px);
                padding-right: 24px;
            }
        }
        .custom-list{
            display: flex;
            align-items: center;
            font-size: 24px;
            color: #333333;
            letter-spacing: 0;
            line-height: 32px;
            font-weight: 400;
            min-height: 98px;
            border-bottom: 1px solid rgba(237,241,244,1);
            border-left: 1px solid rgba(237,241,244,1);
            border-right: 1px solid rgba(237,241,244,1);
            .custom-item{
                width: 20%;
                text-align: center;
            }
            .custom-item:first-child {
                width: calc(30% - 24px);
                padding-left: 24px;
            }
            .custom-item:last-child{
                width: calc(50% - 24px);
                padding-right: 24px;
            }
        }
        .custom-list:nth-child(even) {
            background: #FBFCFF;
        }
    }
}
</style>
