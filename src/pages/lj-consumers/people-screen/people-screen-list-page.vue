<template>
    <link-page class="people-screen-list-page">
        <link-auto-list :option="option"  :searchInputBinding="{props:{placeholder:'搜索名称'}}">
            <template v-slot="{data,index}">
                <link-swipe-action :key="index">
                    <item :key="index" :data="data" :arrow="false" class="people-screen-item">
                        <view class="people-content">
                            <view @tap="gotoItem(data)">
                                <view class="content-item-title">
                                    <view class="content-item-title-left">{{data.crowdSelection}}</view>
                                    <view class="content-item-title-right">{{data.status | lov('CROWD_SELECTION_STATE')}}</view>
                                </view>
                                <view class="content-item-desc">
                                    <text style="color:#999999;margin-right: 10px;">更新时间</text>
                                    <text style="color:#333333;">{{data.lastUpdated | date('YYYY-MM-DD HH:mm:ss')}}</text>
                                </view>
                                <view class="content-item-desc">
                                    <text style="color:#999999;margin-right: 10px;">创建时间</text>
                                    <text style="color:#333333;">{{data.created | date('YYYY-MM-DD HH:mm:ss')}}</text>
                                </view>
                                <view class="content-item-desc">
                                    <text style="color:#999999;margin-right: 10px;">人群包描述</text>
                                    <text style="color:#333333;">{{data.description}}</text>
                                </view>
                                <view class="content-item-desc" v-if="data.consumerTotal && data.consumerTotal>=0">
                                    <text style="color:#999999;margin-right: 10px;">消费者数量</text>
                                    <text style="color:#333333;">{{data.consumerTotal}}</text>
                                </view>
                            </view>
                            <!--计算完成和计算失败可更新 -->
                            <view class="content-item-buttons" v-if="data.status === 'finish' || data.status === 'failed'">
                                <link-button slot="foot" @tap="refreshOrFailure(data, 'update')" class="update-button">更新</link-button>
                            </view>
                        </view>
                    </item>
                    <!--计算中则不允许删除-->
                    <link-swipe-option slot="option" @tap="refreshOrFailure(data, 'delete')" v-if="data.status !== 'calculate'">
                        <link-icon icon="icon-shanchu" style="width:96px;height:96px;"/>
                    </link-swipe-option>
                </link-swipe-action>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>

import {ROW_STATUS} from "../../../utils/constant";

export default {
    name: "people-screen-list-page",
    components: {},
    props: {},
    data() {
        const option = new this.AutoList(this, {
            module: this.$env.dmpURL + '/link/cdcConScreen',
            createPath: '/pages/lj-consumers/people-screen/people-screen-item-edit-page',
            sortDesc: false,
            param: {
                oauth: 'MY_POSTN_ONLY',
                filtersRaw: [
                    {id: 'status', property: 'status', value: '[calculate,finish,failed,saved,queuing]', operator: 'IN'}
                ]
            },
            filterOption: [
                {label: '筛选结果状态', field: 'status', type: 'lov', lov: 'CROWD_SELECTION_STATE'}
            ],
            hooks: {
                async beforeLoad (option) {
                    option.param.sort = 'lastUpdated';
                    option.param.order = 'desc';
                },
                async beforeCreateItem(param) {
                    const id = await this.$newId();
                    param.data = {
                        id: id,
                        row_status: ROW_STATUS.NEW
                    }
                }
            },
            searchFields: ['crowdSelection'],
        })
        return {
            option,
        }
    },
    methods: {
        /**
         * @desc 返回刷新数据
         * <AUTHOR>
         * @date 2023/9/14 15:55
         **/
        onBack () {
            this.option.methods.reload();
        },
        /**
         * @desc 跳转详情
         * <AUTHOR>
         * @date 2023/9/8 11:46
         * @param data 选中的对象值
         **/
        async gotoItem (data) {
            if (data.status === 'finish') { // 计算成功则跳转至人群包消费者列表
                this.$nav.push('/pages/lj-consumers/account/account-list-page', {pageFrom: 'PeopleScreen', headId: data.id});
            } else if (data.status === 'saved') { // 已保存状态跳转至标签编辑页
                data.row_status = ROW_STATUS.UPDATE;
                this.$nav.push('/pages/lj-consumers/people-screen/people-screen-item-edit-page', {data: data});
            }
        },
        /**
         * @desc 更新数据或者失效数据
         * <AUTHOR>
         * @date 2023/9/8 00:02
         * @param item 更新的对象
         * @param type 状态
         **/
        async refreshOrFailure (item, type) {
            let url = '/link/cdcConScreen/update'
            item.status = 'failure';
            let message = '删除';
            let param = item;
            if (type === 'update') {
                item.status = 'saved';
                message = '更新';
                url = '/link/cdcConScreen/execute';
                param = {id: item.id};
            }
            const data = await this.$http.post(this.$env.dmpURL + url, param, {
                autoHandleError: false,
                handleFailed: (res) => {
                    this.$message.error(message + '失败！' + res.result);
                }
            });
            if (data.success) {
                this.$message.success(message + '成功！');
                this.option.methods.reload();
            }
        },
    },
}
</script>

<style lang="scss">
.people-screen-list-page {
    background-color: #F2F2F2;
    .people-screen-item {
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
    }
    .people-content {
        background-color: #FFFFFF;
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: column;
        .content-item-title {
            margin: 32px 0px 24px 24px;
            line-height: 48px;
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            .content-item-title-left {
                width: 75%;
                font-size: 32px;
                color: #212223;
                font-weight: 600;
            }
            .content-item-title-right {
                width: 20%;
                text-align: right;
                font-size: 28px;
                color: #3F66EF;
                font-weight: 400;
            }
        }
        .content-item-desc{
            margin: 8px 24px 0 24px;
            line-height: 44px;
            width: 100%;
            font-size: 28px;
            font-weight: 400
        }
        .content-item-buttons {
            height: 60px;
            margin: 40px 0;
            .analysis-button {
                width: 80px;
                height: 60px;
                font-size: 28px;
                color: #2F69F8;
                line-height: 60px;
                float: right;
                background-color: #ffffff;
                border: 1px solid #2F69F8;
                margin-left: 16px;
            }
            .update-button {
                width: 80px;
                height: 60px;
                font-size: 28px;
                color: #333333;
                line-height: 60px;
                float: right;
                background-color: #ffffff;
                border: 1px solid #999999;
                margin-left: 16px;
            }
        }
    }

    .link-swipe-option-container .link-swipe-option {
        width: 96px!important;
        height: 96px !important;
        border-radius: 50%;
        font-size: 50px !important;
        padding: 0!important;
    }
}
</style>
