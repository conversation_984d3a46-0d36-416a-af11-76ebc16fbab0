<template>
    <link-page class="people-screen-item-edit-page">
        <lnk-taps :taps="subObjOptions" v-model="subObjActive" @switchTab="switchTab"></lnk-taps>
        <view class="tab-label" v-if="subObjActive.name === '标签列表'">
            <standard-labels ref="labelRef"
                             :allData.sync="allData"
                             :dialogTitle="allData.row_status === 'UPDATE'? '编辑人群包' : '新建人群包'"
                             :tag-list="tagList"></standard-labels>
        </view>
        <custom-labels v-if="subObjActive.name === '自定义圈选'"
                       :allData.sync="allData"
                       :dialogTitle="allData.row_status === 'UPDATE'? '编辑人群包' : '新建人群包'"></custom-labels>
    </link-page>
</template>

<script>
    import LnkTaps from "../../core/lnk-taps/lnk-taps.vue";
    import LineTitle from "../../lzlj/components/line-title.vue";
    import StandardLabels from "./components/standard-labels-new.vue";
    import CustomLabels from "./components/custom-labels.vue";

    export default {
        name: "people-screen-item-edit-page",
        components: {CustomLabels, StandardLabels, LineTitle, LnkTaps},
        props: {},
        data() {
            const allData = this.pageParam.data;
            return {
                allData,
                tagList: [],
                subObjActive: {name: "标签列表", seq: "1", val: "basic"},
                subObjOptions: [
                    {name: "标签列表", seq: "1", val: "basic"},
                    {name: "自定义圈选", seq: "2", val: "activity"},
                ]
            }
        },
        created() {
            const tempData = JSON.parse(this.allData['lscspJson'] || '{}');
            if (tempData.tag && tempData.tag.length > 0) this.tagList = tempData.tag;
            this.$store.commit('consumerLabel/setConsumerLabel', tempData);
            // 清空选择数据
            this.$store.commit('consumerLabel/setTagList', []);
            // this.$store.commit('consumerLabel/setSelectItem', {});
            // this.$store.commit('consumerLabel/setSecondSelect', {});
        },
        methods: {
            switchTab(val, key) {
                this.subObjActive = val;
                if (val.val !== 'basic') {
                    this.$refs.labelRef.setTagData()
                } else {
                    this.tagList = this.$store.getters['consumerLabel/getTagList'];
                }
                const tempLabelData = this.$store.getters['consumerLabel/getConsumerLabel'];
                if (tempLabelData) {
                    this.allData.lscspJson = JSON.stringify(tempLabelData);
                }
            }
        },
    }
</script>

<style lang="scss">
    .people-screen-item-edit-page {
        .tab-label {
            margin-top: 100px;

        }
    }
</style>
