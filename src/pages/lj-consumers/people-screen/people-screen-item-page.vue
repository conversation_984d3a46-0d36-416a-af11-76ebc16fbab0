<template>
    <link-page class="people-screen-item-page">
        <lnk-taps :taps="subObjOptions"
                  v-model="subObjActive"
                  @switchTab="switchTab"
                  v-if="!type"></lnk-taps>
        <!-- 消费者标签 -->
        <StandardLabelsComsumer v-if="type === 'label,statistic'"
                  ref="standardLabels"
                  :type="type"
                  :tagList="tagList"
                  :tagGroups="tagGroups"
                  :consumerId="pageParam.consumerId"
                  :phoneNumber="pageParam.phoneNumber"
                  :companyId="pageParam.companyId"/>

        <!-- 人群圈选的标签 -->
        <standard-labels-view v-else-if="type.indexOf('label') > -1"
                              ref="standardLabels"
                              :type="type"
                              :tagList="tagList"
                              :tagGroups="tagGroups"
                              :consumerId="pageParam.consumerId"
                              :phoneNumber="pageParam.phoneNumber"
                              :companyId="pageParam.companyId"/>
        <custom-labels-view v-if="type === 'condition' && qpsList.length > 0"
                            :qpsList="qpsList"
                            :qpsShowList="qpsShowList"/>
    </link-page>
</template>

<script>
    import LnkTaps from "../../core/lnk-taps/lnk-taps";
    import StandardLabelsView from "./components/standard-labels-view-new";
    import StandardLabelsComsumer from "./components/standard-labels-view-comsumer";
    import CustomLabelsView from "./components/custom-labels-view";

    export default {
        name: "people-screen-item-page",
        components: {CustomLabelsView, StandardLabelsView, StandardLabelsComsumer, LnkTaps},
        data() {
            const id = this.pageParam.id;
            const type = this.pageParam.type;
            return {
                id,
                type,
                tagGroups: [],
                tagList: [],
                qpsList: [],
                qpsShowList: [],
                subObjActive: {name: "标签列表", seq: "1", val: "standardLabel"},
                subObjOptions: [
                    {name: "标签列表", seq: "1", val: "standardLabel"},
                    {name: "自定义圈选", seq: "2", val: "customLabel"},
                ]
            }
        },
        onReachBottom () {
            if (this.type.indexOf('statistic') !== -1 && this.$refs.standardLabels) {
                this.$refs.standardLabels.loadMore();
            }
        },
        onPullDownRefresh () {
            wx.stopPullDownRefresh();
        },
        created() {
            if (this.pageParam.title) {
                this.$taro.setNavigationBarTitle({title: this.pageParam.title});
            }
            if (this.pageParam.tagList) {
                this.tagGroups = this.pageParam.tagList;
                wx.setNavigationBarTitle({
                    title: '消费者标签'
                })
            } else {
                this.queryItemInfo();
            }
        },
        methods: {
            /**
             * @createdBy 黄鹏
             * @date 2024/01/04
             * @methods: switchTab
             * @para:
             * @description: tab切换
             **/
            switchTab(val, key) {
                this.subObjActive = val;
            },
            /**
             * @desc 查询详情信息
             * <AUTHOR>
             * @date 2023/9/13 19:32
             **/
            async queryItemInfo() {
                const data = await this.$http.post(this.$env.dmpURL + '/link/cdcConScreen/queryById', {id: this.id}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$message.error(`查询标签数据失败：${response.result}`);
                    }
                });
                if (data.success) {
                    if (data.result.lscspJson) {
                        const lscspJson = JSON.parse(data.result.lscspJson);
                        if (lscspJson.tag && lscspJson.tag.length > 0) {
                            this.tagList = lscspJson.tag;
                        } else {
                            if (this.type === 'label') {
                                // this.$message.error('未维护数据！');
                                this.tagList = [];
                            }
                        }
                        if (lscspJson.qps && lscspJson.qps.length > 0) {
                            this.qpsList = lscspJson.qps;
                            this.qpsShowList = lscspJson.qpsShow;
                        } else {
                            if (this.type === 'condition') {
                                this.$message.error('未维护数据！');
                            }
                        }
                    }
                }
            }
        }
    }
</script>

<style lang="scss">
    .people-screen-item-page {
        .lnk-tabs-container {
            height: 100px;
        }
    }
</style>
