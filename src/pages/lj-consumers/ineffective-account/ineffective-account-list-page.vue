<template>
    <link-page class="ineffective-account-list-page">
        <lnk-taps :taps="statusList" v-model="statusListActive" @switchTab="switchTab" class="status"></lnk-taps>
        <link-auto-list :option="ineffectiveAccountOption" hideCreateButton>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="ineffective-account-list">
                    <view class="ineffective-account-list-item" slot="note">
                        <view class="media-list">
                            <image class="media-list-logo" :src="data|headImgAccount(data)"
                                   @tap="gotoItem(data)"/>
                            <view class="media-list-body" style="width: 70%" @tap="gotoItem(data)">
                                <view class="media-list-text-top">{{ data.name }}</view>
                                <view class="media-list-text-bottom"><view class="label">手机号</view>{{data.mobilePhone}}</view>
                            </view>
                            <view class="media-list-right">
                                <status-button :type="data.appStatus">
                                    <view>{{ data.appStatus | lov('UPTATE_AUDIT_STATUS') }}</view>
                                </status-button>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
        <link-fab-button :bottom="200" icon="icon-xiaofeizhe" @tap="gotoAccountList"></link-fab-button>
    </link-page>
</template>

<script>

    import LnkTaps from "../../core/lnk-taps/lnk-taps";
    import StatusButton from "../../lzlj/components/status-button";
    export default {
        name: 'IneffectiveAccount',
        components: {StatusButton, LnkTaps},
        data () {
            const userInfo = this.$taro.getStorageSync('token').result;
            return {
                userInfo,
                ineffectiveAccountOption: new this.AutoList(this, {
                    module: this.$env.appURL + '/action/link/fieldTemApp',
                    url: {
                        queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/send'
                    },
                    filterOption: null,
                    loadOnStart: false,
                    exactSearchFields: [
                        {
                            field: 'name',
                            showValue: '姓名',
                            searchOnChange: true,
                            clearOnChange: true,
                            exactSearch: true
                        }, {
                            field: 'mobilePhone',
                            showValue: '手机号',
                            searchOnChange: true,
                            clearOnChange: true,
                            exactSearch: true
                        }
                    ],
                    param: {
                        dmpUrl: '/link/fieldTemApp/queryByExamplePage',
                        oauth: 'MY_POSTN_ONLY',
                        sort: 'lastUpdated',
                        filtersRaw: [
                            {id: 'applyType', property: 'applyType', value: 'NewApproval', operator: '='}
                        ]
                    },
                    sortOptions: null,
                    hooks: {
                        beforeLoad (option) {
                            for (let i = 0; i < option.param.filtersRaw.length; i++) {
                                if (option.param.filtersRaw[i].property === 'name') {
                                    option.param.filtersRaw[i].operator = 'like';
                                }
                            }
                        }
                    }
                }),
                statusList: [
                    {name: '全部', value: 'ALL', seq: 0}
                ],
                statusListActive: {name: '全部', value: 'ALL', seq: 0}
            }
        },
        async created() {
            const statusListData = await this.$lov.getLovByType('UPTATE_AUDIT_STATUS');
            this.statusList = statusListData.map((item, index) => ({
                name: item.name,
                value: item.val,
                seq: index + 1
            }))
            this.statusList.unshift({name: '全部', value: 'ALL', seq: 0});
            this.ineffectiveAccountOption.methods.reload();
        },
        methods: {
            /**
             * @desc 跳转详情
             * <AUTHOR>
             * @date 2023/2/20 14:10
             **/
            gotoItem (data) {
                this.$nav.push('/pages/lj-consumers/account/account-item-edit-page', {
                    orgId: data.orgId,
                    businessDataId: data.id,
                    appStatus: data.appStatus,
                    pageFrom: 'IneffectiveAccount'
                });
            },
            /**
             * @desc tab页签切换
             * <AUTHOR>
             * @date 2023/2/20 10:45
             **/
            switchTab () {
                let filtersRawItem = {id: 'appStatus', property: 'appStatus', value: this.statusListActive.value, operator: '='};
                let index = this.ineffectiveAccountOption.option.param.filtersRaw.findIndex((item)=> item.property === 'appStatus');
                if (this.statusListActive.value !== 'ALL') {
                    if (index !== -1) {
                        this.ineffectiveAccountOption.option.param.filtersRaw.splice(index, 1, filtersRawItem);
                    } else {
                        this.ineffectiveAccountOption.option.param.filtersRaw.push(filtersRawItem);
                    }
                } else {
                    if (index !== -1) {
                        this.ineffectiveAccountOption.option.param.filtersRaw.splice(index, 1);
                    }
                }
                this.ineffectiveAccountOption.methods.reload();
            },
            /**
             * @desc 跳转消费者列表
             * <AUTHOR>
             * @date 2023/2/20 14:04
             **/
            gotoAccountList () {
                this.$nav.push('/pages/lj-consumers/account/account-list-page');
            }
        }
    };
</script>
<style lang="scss">
.ineffective-account-list-page{
    .status {
        height: 100px;
    }
    .ineffective-account-list{
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
    }
    .ineffective-account-list-item {
        background-color: #FFFFFF;
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: column;

        .media-list {
            padding: 11px 15px;
            box-sizing: border-box;
            display: flex;
            width: 100%;
            flex-direction: row;
            justify-content: space-between;

            .status-button {
                .status-button-lov {
                    width: 100%;
                }
            }

            .media-list-logo {
                height: 94px;
                width: 94px;
                margin-right: 20px;

                image {
                    height: 100%;
                    width: 100%;
                }
            }

            .media-list-body {
                display: flex;
                flex: 1;
                flex-direction: column;
                justify-content: space-between;
                align-items: flex-start;
                overflow: hidden;

                .media-list-text-top {
                    width: 100%;
                    line-height: 36px;
                    font-size: 30px;
                    color: #262626;
                }
                .media-list-text-bottom {
                    width: 100%;
                    line-height: 42px;
                    font-size: 28px;
                    display: flex;
                    color: #333333;
                    .label{
                        color: #999999;
                        margin-right: 24px;
                    }
                }
            }

            .media-list-right {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
            }
        }
    }

}
</style>
