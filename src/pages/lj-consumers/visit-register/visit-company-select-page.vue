<template>
  <link-page class="visit-company-select-page">
    <lnk-taps :taps="visitSelectOptions" v-model="visitSelectActive"></lnk-taps>
    <view v-if="visitSelectActive.seq === '1'" :key="0">
      <view class="blank"></view>
      <link-auto-list :option="visitCaseOption" class="visit-case">
        <template slot-scope="{data,index}">
          <item :key="index" :data="data" :arrow="false" class="visit-apply-list-item">
            <view slot="note">
              <view class="visit-apply-item">
                <link-checkbox :val=data.id slot="thumb" toggleOnClickItem @tap="ontapCheckBox(data)"/>
                <view class="visit-apply-item-content">
                  <view style="display: flex;justify-content: space-between; padding-bottom: 24rpx">
                    <view class="left">{{data.title}}</view>
                    <view class="right" style="color: #0076ff">{{data.visitorName}}</view>
                  </view>
                  <view style="flex-flow: row;display: flex;padding-top: 20rpx">
                    拜访单位：{{data.visitCompany}}
                  </view>
                  <view style="flex-flow: row;display: flex;padding-top: 20rpx">
                    统一社会信用代码：{{data.creditNo}}
                  </view>
                </view>
              </view>
            </view>
          </item>
        </template>
      </link-auto-list>
    </view>
    <view v-if="visitSelectActive.seq === '2'" :key="1">
      <view class="blank"></view>
      <link-auto-list :option="enterpriseOption" class="enterprise">
        <template slot-scope="{data,index}">
          <item :key="index" :data="data" :arrow="false" class="visit-apply-list-item">
            <view slot="note">
              <view class="visit-apply-item">
                <link-checkbox :val=data.id slot="thumb" toggleOnClickItem @tap="ontapCheckBox(data)"/>
                <view class="visit-apply-item-content">
                  <view style="display: flex;justify-content: space-between; padding-bottom: 24rpx">
                    <view class="left">{{data.enterpriseName}}</view>
                    <view class="right" style="color: #0076ff">{{data.dataType | lov('ENT_DATA_TYPE')}}</view>
                  </view>
                  <view style="flex-flow: row;display: flex;padding-top: 20rpx">
                    {{data.creditNo}}
                  </view>
                </view>
              </view>
            </view>
          </item>
        </template>
      </link-auto-list>
    </view>
  </link-page>
</template>

<script>
  import LnkTaps from "../../core/lnk-taps/lnk-taps";

  export default {
    name: "visit-company-select-page",
    components: {LnkTaps},
    data() {
      const visitRegisterItem = this.pageParam.item;
      console.log(visitRegisterItem);
      const visitSelectOptions = [
        {name: '拜访方案', seq: '1', val: 'visitCase'},
        {name: '拜访企业', seq: '2', val: 'visitCompany'}
      ];
      const visitCaseOption = new this.AutoList(this, {
        module: this.$env.appURL + '/action/link/visit',
        searchFields: ['visitCompany'],
        param: {
          oauth: 'MY_POSTN',
          rows: 25,
          filtersRaw: [
            {id: 'type', property: 'type', value: 'visitApply', operator: '='},
            {id: 'visitType', property: 'visitType', value: visitRegisterItem.visitType, operator: '='},
            {id: 'visitApplicationStatus', property: 'visitApplicationStatus', value: 'Approved', operator: '='}
          ]
        }
      });
      return {
        visitSelectOptions,
        visitRegisterItem,
        visitSelectActive: {},
        visitCaseOption,
        enterpriseOption: new this.AutoList(this, {
          module: this.$env.appURL + '/action/link/entDatabase',
          searchFields: ['enterpriseName'],
          param: {
            oauth: 'ALL',
            rows: 25,
            filtersRaw: [
              {
                id: 'dataStatus',
                property: 'dataStatus',
                value: 'Valid',
                operator: '='
              }
            ]
          }
        })
      }
    },
    created() {
      this.visitSelectActive = this.visitSelectOptions[0];
    },
    methods: {
      /**
       *  @description: 选中数据
       *  @author: 马晓娟
       *  @date: 2020/11/17 18:11
       */
      ontapCheckBox(item) {
        const data = {visitCompany: item, type: this.visitSelectActive.val};
        this.pageParam.callback(data);
        this.$nav.back();
      }
    }
  }
</script>

<style lang="scss">
  .visit-company-select-page {
    .blank {
      width: 100%;
      height: 96px;
      background: #F2F2F2;
    }

    .visit-apply-item {
      display: flex;
      align-items: center;
      .link-checkbox{
        width: 10%;
      }
      .visit-apply-item-content{
        width: 90%;
        padding-left: 24px;
      }
    }
  }
</style>
