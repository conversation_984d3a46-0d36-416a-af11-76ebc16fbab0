<template>
    <link-page class="visit-item-al-info-page">
        <link-form>
            <link-form-item label="客户名称" required
                            v-if="pageFrom !== 'visitApplyItem'">
                <link-input v-model="visitRegisterItemCustInfoItem.visitSinglePerson" readonly placeholder="选择拜访客户" @tap="gotoSelectAccount"></link-input>
            </link-form-item>
            <link-form-item label="客户名称" required v-if="pageFrom === 'visitApplyItem'">
                <link-object :option="accountListOption" pageTitle="选择拜访客户"
                             :row="visitRegisterItemCustInfoItem"
                             :value="visitRegisterItemCustInfoItem.visitSinglePerson"
                             :map="{consumerId:'id',visitSinglePerson:'acctName',telephone:'mobilePhone1',
                             company:'company',companyId:'companyId',position:'position'}"
                >
                    <template v-slot="{data}">
                        <item :arrow="false" :title="data.acctName" :note="data.company"
                              :content="data.mobilePhone1"
                              :desc="data.position" :key="data.id" :data="data"/>
                    </template>
                </link-object>
            </link-form-item>
            <link-form-item label="手机号" readonly>
                <link-input v-model="visitRegisterItemCustInfoItem.telephone"></link-input>
            </link-form-item>
            <link-form-item label="公司" readonly>
                <link-input v-model="visitRegisterItemCustInfoItem.company"></link-input>
            </link-form-item>
            <link-form-item label="职务" readonly>
                <link-input v-model="visitRegisterItemCustInfoItem.position"></link-input>
            </link-form-item>
            <link-form-item label="慰问品标准" arrow v-if="visitRegisterItem.visitType==='NormalCustomerVisit'">
                <link-lov v-model="visitRegisterItemCustInfoItem.giftStandar"
                          type="VISIT_GIFT_STANDRAD"></link-lov>
            </link-form-item>
            <link-form-item label="慰问品" v-if="visitRegisterItem.visitType==='PublicRelationVisit'">
                <link-input v-model="visitRegisterItemCustInfoItem.giftAlcohol"></link-input>
            </link-form-item>
            <link-form-item label="慰问品数量">
                <link-input v-model="visitRegisterItemCustInfoItem.giftNum" type="number"></link-input>
            </link-form-item>
            <view class="lnk-form-header">
                <title-line label-name="活动档期"/>
            </view>
            <link-form-item label="活动档期">
                <link-object :option="resourceListOption"
                             :row="visitRegisterItemCustInfoItem"
                             :value="visitRegisterItemCustInfoItem.actProdGroupName"
                             :map="{actProdGroupId:'id',actProdGroupName:'name'}"
                             :beforeSelect="beforeSelect">
                    <template v-slot="{data}">
                        <item :arrow="false" :title="data.name" :key="data.id" :data="data"/>
                    </template>
                </link-object>
            </link-form-item>
            <link-form-item label="最近一次拜访时间">
               <view style="color: #333">{{recentVisitTime | date('YYYY-MM-DD HH:mm')}}</view>
            </link-form-item>
            <view class="lnk-form-header">
                <title-line label-name="拜访赠酒" button-name="添加" @tap="addVisitCustInfo({})"/>
                <list>
                    <link-swipe-action v-for="(item,index) in visitGiftList.list" :key="item.id">
                        <link-swipe-option slot="option" @tap="handleCustGiftDelete(item,index)">删除</link-swipe-option>
                        <item style="background-color: white;" :arrow="false">
                            <view style="width:100%;display: flex; flex-direction:column;">
                                <view class="item" style="width: 100%">
                                    <view style="width: 100%">
                                        <view style="width: 90%;float: left;padding-left: 30rpx">{{item.prodName}}
                                        </view>
                                        <view>{{item.qty}}</view>
                                    </view>
                                </view>
                                <view class="sub-row-item">
                                    <view class="label" style="width: 30%;float: left;padding-left: 30rpx;">物流码:</view>
                                    <view class="logistic-list">
                                        <view class="logistic-code"
                                              v-for="(logistic,index) in item['logCodeList']" :key="index">
                                            {{logistic['logCode']}}
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </item>
                    </link-swipe-action>
                </list>
            </view>
        </link-form>
        <link-sticky>
            <link-button block @tap="saveVisitCustInfo">保存</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    import {ROW_STATUS} from "../../../utils/constant";
    import TitleLine from "../../lzlj-II/fighting-fakes/components/title-line";

    export default {
        name: "visit-register-item-cust-info-edit-page",
        components: {TitleLine},
        data() {
            const userInfo = this.$taro.getStorageSync('token').result;
            let visitRegister;
            if (this.pageParam.hasOwnProperty('visitRegisterItemCustInfoItem')) {
                visitRegister = {...this.pageParam.visitRegisterItemCustInfoItem};
                if (visitRegister.row_status !== 'NEW') {
                    visitRegister.row_status = ROW_STATUS.UPDATE;
                }
            } else {
                visitRegister = {
                    id: 'no match Id',
                    row_status: 'NEW',
                    visitId: 'no match Id',
                    companyId: null,
                };
            };
            const visitRegisterItemCustInfoItem = {
                //预定义字段
                id: null,
                row_status: null,
                telephone: null,
                company: null,
                isApplyFlag: null,
                giftStandar: null,
                giftAlcohol: null,
                giftNum: null,
                companyId: null,
                consumerId: null,
                actProdGroupId: null,
                tabType: null,
                visitAccntId: null,
                bizLineId: null,
                //解构对象 覆盖已有对象
                ...visitRegister
            };
            const visitRegisterItem = {
                id: null,
                visitType:null,
                visitCustInfoList: [],
                ...this.pageParam.item
            };
            const bussinessId = visitRegisterItemCustInfoItem.id;
            return {
                userInfo,
                recentVisitTime: '',
                visitType: '',
                bussinessId,
                visitGiftList: new this.AutoList(this, {
                    module: this.$env.appURL + '/link/visitProdLine',
                    param: {
                        rows: 25,
                    },
                    hooks: {
                        //筛选参数，在页面上通过某种操作双向绑定得到的,AutoList的 beforeLoad 钩子函数里面设置新的筛选参数,
                        //.当没有筛选参数的时候， return Promise.reject() 阻止查询
                        beforeLoad(option) {
                            if (this.visitRegisterItemCustInfoItem.id) {
                                option.param.filtersRaw = [
                                    ...option.param.filtersRaw,
                                    {
                                        id: 'bussinessId',
                                        property: 'bussinessId',
                                        value: this.bussinessId,
                                        operator: '='
                                    },
                                ];
                            } else {
                                return Promise.reject();
                            }

                        },
                    }
                }),//赠酒列表
                visitRegisterItem,
                // 是否新建
                newVisitFlag: visitRegisterItem.row_status === 'NEW',
                pageFrom: this.pageParam.pageFrom,
                visitRegisterItemCustInfoItem,
                accountListOption: new this.AutoList(this, {
                    module: this.$env.appURL + '/action/link/consumer',
                    url: {
                        queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/consumerListSend'
                    },
                    exactSearchFields: [
                        {
                            field: 'acctName',
                            showValue: '姓名',
                            searchOnChange: true,
                            clearOnChange: true,
                            exactSearch: true
                        }, {
                            field: 'mobilePhone1',
                            showValue: '手机号',
                            searchOnChange: true,
                            clearOnChange: true,
                            exactSearch: true
                        }
                    ],
                    filterOption: [
                        {label: '客户性别', field: 'gender', type: 'lov', lov: 'GENDER'}
                    ],
                    param: {
                        rows: 25,
                        oauth: 'MY_POSTN_ONLY',
                        filtersRaw: [
                            {id: 'companyId', property: 'companyId', value: userInfo.coreOrganizationTile['l3Id'], operator: '='},
                            {id: 'consumerType', property: 'consumerType', value: 'ChannelConsumer', operator: '='},
                            {id: 'accntChannel', property: 'accntChannel', value: 'MarketingPlatform', operator: '='},
                            {id: 'empFlag', property: 'empFlag', value: 'N', operator: '='},
                            {id: 'followFlag', property: 'followFlag', value: 'Y', operator: '='}
                        ]
                    },
                    forceShowTab: true,
                    forceShowTabContent: true,
                    slots: {
                        other: () => <link-fab-button onTap={this.addConsumer}></link-fab-button>
                    }
                }),
                resourceListOption: new this.AutoList(this, {
                    module: this.$env.appURL + '/action/link/productgroup',
                    searchFields:['name'],
                    param: {
                        rows: 25,
                        filtersRaw: [
                            {id: 'type', property: 'type', value: 'PresentWine', operator: '='},
                        ]
                    },
                    hooks: {
                        //筛选参数，在页面上通过某种操作双向绑定得到的,AutoList的 beforeLoad 钩子函数里面设置新的筛选参数,
                        //.当没有筛选参数的时候， return Promise.reject() 阻止查询
                        beforeLoad(option) {
                            option.param.filtersRaw = [
                                ...option.param.filtersRaw,
                                {
                                    id: 'companyId',
                                    property: 'companyId',
                                    value: this.visitRegisterItemCustInfoItem.companyId,
                                    operator: '='
                                },
                            ];
                            for (let i = 0; i < option.param.filtersRaw.length; i++) {
                                if (option.param.filtersRaw[i].property === 'acctName') {
                                    option.param.filtersRaw[i].operator = 'like';
                                }
                            }
                        },
                    }
                }),
            }
        },
        async created() {
            if (!this.pageParam.hasOwnProperty('visitRegisterItemCustInfoItem')) {
                this.visitRegisterItemCustInfoItem.id = await this.$newId();
                this.bussinessId = this.visitRegisterItemCustInfoItem.id;
            }
            this.visitType = this.visitRegisterItem.visitType;
            if (this.bussinessId) {
                this.visitGiftList.methods.reload();
            }
            const source = this.pageParam.source
            if (source === 'oldCustomerItem' || source === 'accountApply') {
                const consumerItem = this.pageParam.data
                this.visitRegisterItemCustInfoItem = {
                    ...this.pageParam.visitRegisterItemCustInfoItem,
                    consumerId: consumerItem.id,
                    visitSinglePerson: consumerItem.name,
                    telephone: consumerItem.phoneNumber,
                    company: consumerItem.companyName,
                    companyId: consumerItem.belongToCompanyId,
                    position: consumerItem.position
                }
                this.visitRegisterItem = {...this.pageParam.visitRegisterItem}
                this.resourceListOption.option.param['filtersRaw'] = [
                    {id: 'searchValue', property: '[name]', value: '', operator: 'or like'},
                    {id: 'companyId', property: 'companyId', value: this.visitRegisterItemCustInfoItem.companyId, operator: '='},
                    {id: 'type', property: 'type', value: 'PresentWine', operator: '='},
                ];
                this.resourceListOption.methods.reload();
                // 查询已选客户最近一次拜访时间
                await this.queryRecentVisitTime();
            }
        },
        methods: {
           /**
            * @desc 新增消费者
            * <AUTHOR>
            * @date 2022/3/23 10:05
            **/
            async addConsumer() {
                const id = await this.$newId();
                const accountItem = {
                    id: id,
                    row_status: ROW_STATUS.NEW,
                    consumerDataType: 'ChannelConsumer',
                    dataSource: 'MarketingPlatform',
                    dataType: 'Consumer',
                    accntSourceFrom: 'SalesAssistant',
                    orgId: this.userInfo.orgId,
                    fstName: this.userInfo.firstName,
                    postnId: this.userInfo.postnId,
                    belongToCompanyId: this.userInfo.coreOrganizationTile['l3Id'] || '',
                    type: "ToBeFollowed",
                    birthType: 'Yang',
                    brandPreference: "",
                    hobby: "",
                    terminalFlag : 'N',
                    listOfTags: {
                        accountId: id,
                        list: []
                    }
                };
               this.$nav.push('/pages/lj-consumers/account/account-item-edit-page', {
                    data: accountItem,
                    pageFrom: 'visitApplyItem',
                    visitRegisterItemCustInfoItem: this.visitRegisterItemCustInfoItem,
                    visitRegisterItem: this.visitRegisterItem,
                    userInfo: this.userInfo,
                    callback: (data) => {
                        this.accountListOption.methods.reload()
                    }
                });
            },
            viewAllLogCode(item, event) {
                event.stopPropagation();
                item.selectedFlag = !item.selectedFlag;
            },
            /**
             * @desc 查询已选拜访客户的最近一次拜访时间
             * <AUTHOR>
             * @date 2021/6/17 19:57
             **/
            async queryRecentVisitTime () {
                try {
                    const data = await this.$http.post(this.$env.appURL + '/link/visitCustInfo/getLastVisitTime', {
                        consumerId: this.visitRegisterItemCustInfoItem.consumerId
                    }, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$showError(`查询最近一次拜访时间失败：${response.result}`);
                        }
                    });
                    if (data.success) {
                        if (data.result) {
                            this.recentVisitTime = data.result.visitTime;
                            console.log(this.recentVisitTime)
                        }
                    }
                } catch (e) {
                    this.$showError('查询最近一次拜访时间失败！' );
                }

            },
            /**
             * @desc 选择档期前先选择客户
             * <AUTHOR>
             * @date 2021/6/17 19:31
             **/
            async beforeSelect () {
                if (this.$utils.isEmpty(this.visitRegisterItemCustInfoItem.companyId)) {
                    const msg = '请先选择客户';
                    this.$message.primary(msg);
                    return Promise.reject(msg)
                }
            },
            /**
             *  @description: 选择客户
             *  @author: 马晓娟
             *  @date: 2020/6/12 11:18
             */
            async gotoSelectAccount (type) {
                const that = this;
                await this.$nav.push('/pages/lj-consumers/visit-register/visit-account-select-page', {
                    type: type,
                    item: this.visitRegisterItemCustInfoItem,
                    visitRegisterItem: this.visitRegisterItem,
                    callback: async (callbackParams)=> {
                        if (callbackParams['visitRegisterItemCustInfoItem']) {
                            that.visitRegisterItemCustInfoItem = callbackParams['visitRegisterItemCustInfoItem'];
                            that.accountFromType = callbackParams['visitRegisterItemCustInfoItem'].tabType;
                            that.resourceListOption.option.param['filtersRaw'] = [
                                {id: 'searchValue', property: '[name]', value: '', operator: 'or like'},
                                {id: 'companyId', property: 'companyId', value: that.visitRegisterItemCustInfoItem.companyId, operator: '='},
                                {id: 'type', property: 'type', value: 'PresentWine', operator: '='},
                            ];
                            that.resourceListOption.methods.reload();
                            if (that.accountFromType === 'giftList') {
                                that.bussinessId = that.visitRegisterItemCustInfoItem['visitAccntId']
                                that.visitGiftList.option.param['filtersRaw'] =  [
                                    {'id': 'prodId', 'property': 'prodId', 'operator': 'not null', 'value': ''}
                                ];
                                await that.visitGiftList.methods.reload();
                                if (that.visitGiftList.list.length > 0) {
                                    await that.saveSource();
                                }
                            }
                        }
                        // 查询已选客户最近一次拜访时间
                        await this.queryRecentVisitTime();
                    }
                });
            },
            /**
             *  @description: 选择礼赠名单客户的数据，需要将默认带出的礼赠酒品数据存入数据库
             *  @author: 马晓娟
             *  @date: 2020/6/16 16:17
             */
            async saveSource() {
                let params = [];
                    this.visitGiftList.list.forEach((item) => {
                        params.push({
                            accntChannelId: item.accntChannelId,
                            bussinessId: this.visitRegisterItemCustInfoItem.id,
                            lytRightsType: 'PresentWine',
                            prodId: item.prodId,
                            prodName: item.prodName,
                            qty: item.qty,
                            row_status: 'NEW'
                        });
                    });

                    const data = await this.$http.post(this.$env.appURL + 'action/link/visitProdLine/batchInsert', params, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$showError(`保存失败：${response.result}`);
                        }
                    });
            },
            /**
             * 创建新的物流码
             * <AUTHOR>
             * @date 2020-07-07
             */
            addNewLogisticCode() {
                if (!this.option.formData.logCodeList) this.option.formData.logCodeList = [];
                const len = this.option.formData.logCodeList.length;
                for (let i = 0; i < len; i++) {
                    if (this.$utils.isEmpty(this.option.formData.logCodeList[i].logCode)) {
                        this.$message.primary('物流码列表中存在为空的记录,请输入后保存')
                        return;
                    }
                }
                const logisticCodeItem = {
                    orderLineId: this.orderLineItem.id,
                    logCode: '',
                    row_status: ROW_STATUS.NEW
                };
                this.option.formData.logCodeList.push(logisticCodeItem);
            },
            /**
             *  跳转编辑赠酒详情
             *
             *  <AUTHOR>
             *  @date        2020-07-06
             */
            addVisitCustInfo(item) {
                if (this.$utils.isEmpty(item)) {
                    if (!this.visitRegisterItemCustInfoItem.actProdGroupId) {
                        this.$message.primary('请先选择活动档期');
                        return;
                    }
                    item = {
                        bussinessId: this.visitRegisterItemCustInfoItem.id,
                        accntChannelId: this.visitRegisterItemCustInfoItem.consumerId,
                        lytRightsType: 'PresentWine',
                        row_status: ROW_STATUS.NEW,
                    };
                } else {
                    item.row_status = ROW_STATUS.UPDATE;
                }
                item['visitType'] = this.visitType;
                item['parentStatus'] = this.visitRegisterItemCustInfoItem.row_status;
                this.$nav.push('/pages/lj-consumers/visit-register/visit-register-cust-gift-page', {
                    item: item,
                    visitRegisterItemCustInfoItem: this.visitRegisterItemCustInfoItem,
                    callback: () => {
                        this.bussinessId = this.visitRegisterItemCustInfoItem.id;
                        this.visitGiftList.methods.reload();
                    }
                });
            },
            /**
             *  删除
             *
             *  <AUTHOR>
             *  @date        2020-07-06
             */
            async handleCustGiftDelete(item, index) {
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + '/action/link/visitProdLine/deleteById', item, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError(`删除失败：${response.message}`);
                    }
                });
                this.$utils.hideLoading();
                    if (data.success) {
                        this.visitGiftList.methods.reload();
                        await this.$message.primary('删除成功');
                    }
            },
            /**
             *  @description: 保存拜访客户数据
             *  @author: songyanrong
             *  @date: 2020-07-06
             */
            async saveVisitCustInfo() {
                if (this.$utils.isEmpty(this.visitRegisterItemCustInfoItem.consumerId)) {
                    this.$message.primary('请选择客户');
                    return false;
                }
                if (this.pageFrom === 'visitApplyItem') {
                    this.visitRegisterItemCustInfoItem.approvalStatus = 'New';
                }
                if (this.newVisitFlag) {
                    // 主对象新建 子对象新建 将item加到原数组中
                    if (this.newVisitFlag && this.pageParam.hasOwnProperty('visitRegisterItemCustInfoItem')) {
                        this.visitRegisterItem.visitCustInfoList.push(this.visitRegisterItemCustInfoItem);
                    }
                    this.pageParam.callback();
                    await this.$nav.back();
                } else {
                    this.visitRegisterItemCustInfoItem.visitId = this.visitRegisterItem.id;
                    delete this.visitRegisterItemCustInfoItem.tabType;
                    delete this.visitRegisterItemCustInfoItem.visitAccntId;
                    this.$utils.showLoading();
                    await this.$http.post(this.$env.appURL + '/action/link/visitCustInfo/upsert', this.visitRegisterItemCustInfoItem, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.visitRegisterItemCustInfoItem.consumerId = '';
                            this.visitRegisterItemCustInfoItem.visitSinglePerson= '';
                            this.visitRegisterItemCustInfoItem.telephone = '';
                            this.visitRegisterItemCustInfoItem.company = '';
                            this.visitRegisterItemCustInfoItem.companyId = '';
                            this.visitRegisterItemCustInfoItem.position = '';
                            this.$utils.hideLoading();
                            this.$showError('保存失败' + response.result);
                        }
                    });
                    this.$utils.hideLoading();
                    const source = this.pageParam.source
                    if (source === 'oldCustomerItem') {
                        this.$bus.$emit(this.pageFrom === 'visitApplyItem' ? 'initCustInfoListInfoVisitApply' : 'initCustInfoListInfo');
                        this.$nav.back(null, 3)
                    } else if (source === 'accountApply') {
                        this.$bus.$emit(this.pageFrom === 'visitApplyItem' ? 'initCustInfoListInfoVisitApply' : 'initCustInfoListInfo');
                        this.$nav.back(null, 4)
                    } else {
                        this.pageParam.callback();
                        await this.$nav.back();
                    }
                }
            },
        }
    }
</script>

<style lang="scss">
    .visit-item-al-info-page {
        .lnk-form-header {
            width: 100%;
            margin-top: 24px;
            background-color: #F2F2F2;
        }
        .sub-row-item {
            display: flex;
            color: #9CA5A8;
            margin-top: 10px;
        }
    }
</style>
