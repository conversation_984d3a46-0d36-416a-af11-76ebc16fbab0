<template>
    <view class="user-info">
        <!-- 标题 -->
        <title-line label-name="拜访消费者" @tap="addVisitCustInfo()" :buttonName="editFlag? '添加' : ''"/>
        <!-- 列表 -->
        <list style="margin-bottom: 24rpx">
            <link-swipe-action v-for="(custInfo,index) in userInfoList.slice(0,5)" :key="custInfo.id"
                               style="padding: 30rpx 30rpx 0rpx; box-sizing: border-box;">
                <link-swipe-option v-if="editFlag" slot="option" @tap="handleCustomerInfoDelete(custInfo,index)">删除
                </link-swipe-option>
                <item style="width: 100%" :arrow="false">
                    <view class="visit-account-item media-list">
                        <view class="media-list-name">{{ custInfo.visitSinglePerson }}</view>
                        <view class="media-list-label">
                            <view class="loyalty-level">{{ custInfo.subAcctType | lov('ACCT_SUB_TYPE') }}</view>
                            <view class="loyalty-level">{{ custInfo.loyaltyLevel | lov('ACCT_MEMBER_LEVEL') }}</view>
                            <view class="important-account" v-if="custInfo.impFlag === 'Y'">重点客户</view>
                            <!--                                        <view class="black-gold-account" v-if="data.impFlag === 'Y'">黑金</view>-->
                        </view>
                        <view class="media-list-info">
                            <view class="media-list-info-item">
                                <view class="label">联系方式</view>
                                <view class="media-list-info-phone">{{ custInfo.telephone }}</view>
                            </view>
                            <view class="media-list-info-item">
                                <view class="label">单位</view>
                                <view class="media-list-info-text">{{ custInfo.company }}</view>
                            </view>
                            <view class="media-list-info-item">
                                <view class="label">职务</view>
                                <view class="media-list-info-text">{{ custInfo.position }}</view>
                            </view>
                            <view class="media-list-info-item">
                                <view class="label">所属客户</view>
                                <view class="media-list-info-text">{{ custInfo.belongToStore }}</view>
                            </view>
                        </view>
<!--                        <view class="visit-account-avatar">-->
<!--                            <image :src="custInfo | headImgAccount(custInfo)"></image>-->
<!--                        </view>-->
<!--                        <view class="visit-account-info">-->
<!--                            <view><text>{{custInfo.visitSinglePerson}}</text></view>-->
<!--                            <view class="text-box">-->
<!--                                <view class="loyalty-level">{{ custInfo.loyaltyLevel | lov('ACCT_MEMBER_LEVEL') }}</view>-->
<!--                                <view v-if="custInfo.impFlag === 'Y'" class="important-account">重点客户</view>-->
<!--                            </view>-->
<!--                            <view>负责人<text>{{custInfo.fstName}}</text></view>-->
<!--                            <view>手机号<text>{{custInfo.telephone}}</text></view>-->
<!--                        </view>-->
                        <view class="account-label"><view class="label">{{ custInfo.fstName }}跟进</view></view>
<!--                        <view class="visit-account-logo">-->
<!--                            <status-button><view>{{custInfo[statusField] | lov(statusLov)}}</view></status-button>-->
<!--                        </view>-->
                    </view>
                </item>
            </link-swipe-action>
            <!-- 查看更多 -->
            <view @tap="gotoUserList()" v-if="userInfoList.length>5" class="more-box">
                <view class="more">
                    查看全部({{userInfoList.length}})>>
                </view>
            </view>
        </list>
    </view>
</template>

<script>
import TitleLine from "../../../lzlj-II/fighting-fakes/components/title-line";
import StatusButton from "@/pages/lzlj/components/status-button";
import ConsumerCommon from "../../consumer-common";
import {ROW_STATUS} from "../../../../utils/constant";

export default {
    name: 'user-info',
    components: {TitleLine, StatusButton},
    mixins: [ConsumerCommon()],
    data() {
        return {}
    },
    props: {
        userInfoList: {             // 用户信息数组
            type: Array,
            default: []
        },
        editFlag: {                 // 是否可编辑
            type: Boolean,
            default: true
        },
        parentId: {                 // 头id
            type: String,
            default: 0
        },
        statusField: {              // 右上角状态采用的字段
            type: String,
            default: 'type'
        },
        statusLov: {                // 右上角状态采用的值列表
            type: String,
            default: 'ACCT_SUB_TYPE'
        },
        source: {                   // 组件的来源
            type: String,
            default: 'visitRegisterItem'   // 拜访登记visitRegisterItem, 拜访申请 visitApplyItem
        }
    },
    methods: {
        /**
         * @desc 新增消费者
         * <AUTHOR>
         * @date 2022/3/23 10:05
         **/
        async addConsumer() {
            this.newAccountItem.id = await this.$newId();
            this.$nav.push(this.editPath, {
                data: this.newAccountItem,
                pageFrom: this.source,
                userInfo: this.userInfo,
                callback: async (data) => {
                    this.accountOption.methods.reload();
                    const consumerData = {
                        'consumerId': data.id,
                        'visitSinglePerson': data.name,
                        'telephone': data.phoneNumber,
                        'company': data.companyName,
                        'companyId': data.belongToCompanyId,
                        'position': data.position,
                        'visitId': this.parentId,
                        'row_status': ROW_STATUS.NEW
                    };
                    if(this.source === 'visitApplyItem') {
                        consumerData.approvalStatus = 'New'
                    }
                    // this.$utils.showLoading();
                    await this.$http.post(this.$env.appURL + '/action/link/visitCustInfo/upsert', consumerData, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$utils.hideLoading();
                            this.$showError('保存失败' + response.result);
                        }
                    });
                    this.$emit('initCustInfoListInfo');
                    this.$utils.hideLoading();
                }
            });
        },
        /**
         * 删除拜访客户
         * <AUTHOR>
         * @date 2023-01-31
         * */
        async handleCustomerInfoDelete(item,index) {
            // 如果有拜访申请行数据，需要释放
            if (!this.$utils.isEmpty(item.bizLineId) && this.source === 'visitRegisterItem') {
                await this.updateVisitApplyLine(item.bizLineId, null);
            }
            const data = await this.$http.post(this.$env.appURL + '/action/link/visitCustInfo/deleteById', item, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`删除拜访客户失败：${response.result}`);
                }
            });
            if (data.success) {
                this.$emit('initCustInfoListInfo');
            }
        },
        /**
         *  @description: 更新拜访申请客户行数据
         *  @author: songyanrong
         *  @date: 2020-07-07
         */
        async updateVisitApplyLine(id, bizLineId) {
            try {
                await this.$http.post(this.$env.appURL + '/action/link/visitCustInfo/updateBizLineId', {
                    id: id,
                    bizLineId: bizLineId
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$showError(`更新拜访申请行数据失败：${response.result}`);
                    }
                });
            } catch (e) {
                await this.$message.primary(`'更新拜访申请行数据失败！' + ${e}`);
                await this.$message.primary(`'拜访登记新增拜访客户，更新拜访申请行数据失败' + ${e}`);
            }
        },
        /**
         * 添加更多拜访客户
         * <AUTHOR>
         * @date 2023-01-31
         * */
        async addVisitCustInfo() {
            const list = await this.$object(this.accountOption, {
                pageTitle: '消费者',
                multiple: true
            });
            let consumerDataList = [];
            consumerDataList = list.map(item => {
                const consumerData = {
                    'consumerId': item.id,
                    'visitSinglePerson': item.acctName,
                    'telephone': item.mobilePhone1,
                    'company': item.company,
                    'companyId': item.companyId,
                    'position': item.position,
                    'visitId': this.parentId
                }
                if (this.source === 'visitApplyItem') {
                    consumerData.approvalStatus = 'New'
                }
                return consumerData;
            });

            await this.$http.post(this.$env.appURL + '/action/link/visitCustInfo/batchInsert', consumerDataList, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError('保存失败' + response.result);
                }
            });
            this.$emit('initCustInfoListInfo');
            this.$utils.hideLoading();
        },
        /**
         * 查看更多拜访客户
         * <AUTHOR>
         * @date 2023-01-31
         * */
        gotoUserList() {
            this.$nav.push('/pages/lj-consumers/visit-register/visit-customer-list-page', {
                visitId: this.parentId,
                editFlag: this.editFlag,
                callback: () => {
                    this.$emit('initCustInfoListInfo');
                }
            })
        },
    }
}
</script>

<style lang="scss">
.user-info {
    /*deep*/
    .link-swipe-option-container .link-swipe-option {
        width: 100px;
        height: 70px !important;
        border-radius: 80px;
        font-size: 28px !important;
    }

    .link-item {
        overflow: hidden;
    }

    .link-item-body-right{
        overflow: visible;
    }

    .media-list {
        position: relative;
        padding: 32px 24px 24px 24px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        width: 100%;

        .media-list-name {
            height: 48px;
            font-family: PingFangSC-Semibold;
            font-size: 32px;
            color: #212223;
            line-height: 48px;
            font-weight: 600;
        }

        .media-list-label {
            height: 36px;
            margin: 16px 0;
            display: flex;

            .loyalty-level {
                min-width: 80px;
                padding: 0 15px;
                margin-right: 16px;
                background: #F0F5FF;
                border-radius: 4px;
                font-family: PingFangSC-Regular;
                font-size: 22px;
                color: #3F66EF;
                letter-spacing: 0;
                text-align: center;
                line-height: 36px;
                font-weight: 400;
            }

            .important-account {
                width: 112px;
                margin-right: 16px;
                background: #FFF1EB;
                border-radius: 4px;
                font-family: PingFangSC-Regular;
                font-size: 22px;
                color: #FF461E;
                line-height: 36px;
                font-weight: 400;
                text-align: center;
            }

            .black-gold-account {
                width: 68px;
                background: #262626;
                border-radius: 4px;
                font-family: PingFangSC-Regular;
                font-size: 22px;
                color: #F0BE94;
                line-height: 36px;
                font-weight: 400;
                text-align: center;
            }
        }

        .media-list-info {
            display: flex;
            flex-direction: column;

            .media-list-info-item {
                height: 44px;
                display: flex;
                align-items: center;
                margin-bottom: 8px;

                .label {
                    width: 112px;
                    margin-right: 24px;
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #999999;
                    line-height: 44px;
                    font-weight: 400;
                }

                .media-list-info-text {
                    width: 520px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #333333;
                    line-height: 44px;
                    font-weight: 400;
                }

                .media-list-info-phone {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #317DF7;
                    line-height: 44px;
                    font-weight: 400;
                }
            }
        }

        .account-label {
            position: absolute;
            right: -36px;
            top: -28px;
            padding: 4px 48px 4px 32px;
            background: #2F69F8;
            transform: skew(30deg, 0deg);
            border-top-right-radius: 16px;

            .label {
                font-family: PingFangSC-Regular;
                font-size: 24px;
                color: #FFFFFF;
                text-align: center;
                line-height: 40px;
                font-weight: 400;
                transform: skew(-30deg, 0);
            }
        }
    }
    // 列表样式
    .visit-account-item {
        display: flex;
        justify-content: space-between;
        position: relative;
        width: 100%;
        .visit-account-avatar {
            width: 25%;
            image {
                height: 94rpx;
                width: 94rpx;
            }
        }
        .visit-account-info {
            width: 50%;
            box-sizing: border-box;
            view {
                margin-bottom: 20rpx;
                &:not(:first-child) text {
                    margin-left: 20rpx;
                }
                text {
                    font-weight: 500;
                    color: #262626;
                }
            }
            .text-box {
                display: flex;
                .loyalty-level {
                    width: 144px;
                    font-size: 24px;
                    font-weight: 400;
                    color: #41A2F6;
                    line-height: 36px;
                    background: #E9F5FF;
                    border-radius: 4px;
                    opacity: 1;
                    text-align: center;
                }
                .important-account {
                    margin-left: 16px;
                    width: 112px;
                    font-size: 24px;
                    font-weight: 400;
                    color: #FF7553;
                    line-height: 36px;
                    background: #FEEDEB;
                    border-radius: 4px;
                    text-align: center;
                    opacity: 1;
                }
            }
        }
        .visit-account-logo {
            width: 25%;
        }
    }

    // 查看更多样式
    .more-box {
        width: 100%;
        height: 50rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 10rpx;
        .more {
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #2F69F8;
            letter-spacing: 0;
            line-height: 76px;
            background-color: #f2f2f2;
        }
    }
    // 每个单元添加圆角
    /*deep*/.link-item {
                border-radius: 16rpx;
            }
}
</style>
