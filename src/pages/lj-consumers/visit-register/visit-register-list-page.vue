<template>
    <link-page class="visit-register-list-page">
        <link-auto-list :option="autoList" hideCreateButton :searchInputBinding="{props:{placeholder:'名称/姓名/手机号/公司'}}">
            <link-filter-group slot="filterGroup">
                <link-filter-item label="拜访时间(升序)" :param="{sort:{field:'visitTime',desc:false}}"/>
                <link-filter-item label="创建时间(升序)" :param="{sort:{field:'created',desc:false}}"/>
                <link-filter-item label="最近更新(升序)" :param="{sort:{field:'lastUpdated',desc:false}}"/>
            </link-filter-group>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false"  class="visit-register-list-item">
                    <view slot="note">
                        <view style="border-bottom: solid 1px #f1f3f6;display: flex; flex-flow: row;">
                            <view class="left">
                                {{data.title}}
                            </view>
                            <view class="right" style="color: #0076ff">
                                <view>{{data.visitType | lov('VISIT_TYPE')}}</view>
                                <view>{{data.visitStatus | lov('VISIT_STATUS')}}</view>
                            </view>
                        </view>
                        <view style="display: flex; flex-flow: row;padding-top: 5px">
                            <view class="left iconfont icon-style icon-time-circle" style="font-size: 12px">
                                {{data.visitTime|date('YYYY-MM-DD HH:mm', 'YYYY-MM-DD HH:mm:ss')}}
                            </view>
                            <view class="right iconfont icon-style icon-gerenzhongxin" style="font-size: 12px">
                                {{data.visitorName}}
                            </view>
                        </view>
                        <view>
                            <text class="left visit-company" @tap="copyData(data.id)">
                                【拜访ID】{{data.id}}
                            </text>
                        </view>
                        <view>
                            <text class="left visit-company">
                                【拜访单位】{{data.visitCompany}}
                            </text>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
        <link-fab-button @tap="newVisitRegister()"/>
        <link-dialog ref="chooseType">
            <view slot="head">
                操作确认
            </view>
            <view>
                请选择登记类型
            </view>
            <link-button slot="foot" @tap="add('VisitPlan')">拜访计划</link-button>
            <link-button slot="foot" @tap="add('VisitActivity')">拜访登记</link-button>
        </link-dialog>
    </link-page>
</template>

<script>
    import {ROW_STATUS} from "../../../utils/constant";
    import ConsumerListCommon from "../consumer-list-common";

    export default {
        name: "visit-register-list-page",
        mixins: [ConsumerListCommon()],
        data() {
            const userInfo = this.$taro.getStorageSync('token').result;
            const autoList = new this.AutoList(this, {
                module: this.$env.appURL + '/action/link/visit',
                url: {
                    queryByExamplePage: this.$env.appURL + '/action/link/visit/queryFieldsByExamplePage'
                },
                loadOnStart: false,
                fetchItem: true,
                itemPath: '/pages/lj-consumers/visit-register/visit-register-item-page',
                searchFields: ['title', 'visitorName', 'visitorTel', 'visitCompany'],
                filterOption: [{
                    label: '拜访类型', field: 'visitType', type: 'lov', lov: 'VISIT_TYPE'
                }, {
                    label: '拜访状态', field: 'visitStatus', type: 'lov', lov: 'VISIT_STATUS'
                }, {
                    label: '拜访时间', field: 'visitTime', type: 'date'
                }],
                sortOptions: null,
                param: {
                    oauth: this.pageOauth,
                    type: 'VisitRecord',
                    attr3: ''
                },
                stayFields: 'id,visitType,visitStatus,visitTime,visitCompany,visitorName,title',
                hooks: {
                    beforeLoad(option) {
                        if (option.param.filtersRaw.length >0 && option.param.filtersRaw[0].id === 'searchValue_0') {
                            option.param.attr3 = option.param.filtersRaw[0].value;
                            option.param.filtersRaw[0].value = '';
                        }

                    }
                },
                slots: {
                    searchRight: () => (
                        <view class="filter-type-item" style="max-width: 224rpx;height: 72rpx;display: flex;align-items: center;justify-content: flex-end;padding-left: 30rpx;font-family: PingFangSC-Regular;font-size: 26rpx;color: #333333;line-height: 40rpx;font-weight: 400;" onTap={this.chooseOauthData}>{this.pageOauthName}<link-icon icon="mp-desc" style="color: #CCCCCC; margin: 4rpx 0 0 8rpx;"/></view>
                    )
                }
            });
            return {
                autoList,
                type: '',//类型
                visitTime: '',
                userInfo,
            };
        },
        created() {
            if (this.pageOauthList.length > 0) {
                this.pageOauthName = this.pageOauthList[0].name;
                this.pageOauth = this.pageOauthList[0].securityMode;
            } else {
                this.pageOauthName = '我的数据';
                this.pageOauth = 'MY_POSTN_ONLY';
            }
            this.autoList.option.param.oauth = this.pageOauth;
            this.autoList.methods.reload();
        },
        methods: {
            /**
             * @desc 复制ID
             * <AUTHOR>
             * @date 2021/8/13 15:38
             **/
            copyData (data) {
                wx.setClipboardData({
                    data: data,
                    success: function () {
                        // 添加下面的代码可以复写复制成功默认提示文本`内容已复制`
                        wx.showToast({
                            title: '复制成功',
                            duration: 3000
                        });
                        wx.getClipboardData({
                            success: function (res) {
                            }
                        })
                    }
                })
            },
            async add(input) {
                if (input === 'VisitActivity') {
                    this.visitTime = this.$filter.date(new Date(), 'YYYY-MM-DD HH:mm');
                }
                this.type = input;
                const id = await this.$newId();
                const data = {
                    row_status: ROW_STATUS.NEW,
                    id: id,
                    type: 'VisitRecord',
                    visitStatus: 'New',
                    visitorName: this.userInfo.firstName,
                    visitCompany: '',
                    visitorId: this.userInfo.postnId,
                    visitType: 'NormalCustomerVisit',
                    visitTime: this.visitTime,
                    visitAccompanyList: [],
                    visitCustInfoList: [],
                    imgList: []
                };
                this.$nav.push('/pages/lj-consumers/visit-register/visit-register-item-new-page', {
                    data: data,
                    type: this.type
                });
                this.$refs.chooseType.hide();
            },
            newVisitRegister() {
                this.$refs.chooseType.show();
            },
            onBack () {
                this.autoList.methods.reload();
            }
        },
    }
</script>

<style lang="scss">
    .visit-register-list-page {
        .left {
            padding-left: 20px;
            text-align: left;
            width: 60%;
            float: left;
            color: black;
        }

        .visit-company {
            width: 90vw;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }

        .right {
            padding-right: 20px;
            text-align: right;
            width: 40%;
            float: left;
        }
        .visit-register-list-item{
            background: #FFFFFF;
            margin: 24px;
            border-radius: 16px;
        }
    }
</style>
