<template>
    <link-page class="visit-register-cust-gift-page">
        <list>
            <link-form>
                <link-form-item label="赠送酒品" required>
                    <link-object :option="resourceListOption"
                                 :row="giftItem"
                                 :value="giftItem.prodName"
                                 :map="{prodId:'prodId',prodName:'prodName', prodCode: 'prodCode', prodUnit: ''}">
                        <template v-slot="{data}">
                            <item :arrow="false" :title="data.prodName" :key="data.id" :data="data"/>
                        </template>
                    </link-object>
                </link-form-item>
                <link-form-item label="单位" required arrow @tap="pickProdUnit">
                    <view>{{giftItem.prodUnit|lov('PROD_UNIT')}}</view>
                </link-form-item>
                <link-form-item label="数量" required>
                    <link-input v-model="giftItem.qty" type="number"></link-input>
                </link-form-item>
                <view class="order-line-list-wrapper">
                    <view class="header">
                        <view class="header-left">物流码</view>
                        <view class="header-right">
                            <link-icon @tap="scanCode()" style="margin-right: 40rpx;" icon="icon-scan"/>
                            <link-icon @tap="addNewLogisticCode()" icon="icon-tupian1"/>
                        </view>
                    </view>
                    <link-swipe-action v-for="(item,index) in giftItem.logCodeList" :key="item.id" class="logistic-list">
                        <link-swipe-option slot="option" @tap="deleteLogisticCode(item,index)">删除
                        </link-swipe-option>
                        <item style="width: 100%" :arrow="false">
                            <link-input v-model="item.logCode" @blur="checkLogCode(item)"></link-input>
                        </item>
                    </link-swipe-action>
                </view>
            </link-form>
        </list>
        <link-sticky>
            <link-button block @tap="save">保存</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    import {LovService} from "link-taro-component";
    import {ROW_STATUS} from "../../../utils/constant";

    export default {
        name: "visit-register-cust-gift-page",
        data() {
            const userInfo = this.$taro.getStorageSync('token').result;         // 获取用户信息
            const companyId = this.pageParam.companyId ||  userInfo.coreOrganizationTile['l3Id'];
            const giftItem = {
                qty: null,
                prodName: null,
                prodId: null,
                prodUnit: null,
                logCodeList: [],
                ...this.pageParam.item
            };
            return {
                companyId,
                giftItem,
                resourceListOption: new this.AutoList(this, {
                    module: this.$env.appURL + '/action/link/interprodgroup',
                    searchFields: ['prodName'],
                    param: {
                        rows: 25,
                        filtersRaw: [
                            {id: 'companyId', property: 'companyId', value: companyId},
                            {id: 'isEffective', property: 'isEffective', value: 'Y'},
                            {id: 'groupType', property: 'groupType', value: 'PresentWine'}
                        ]
                    }
                }),
                prodUnitOption: new this.AutoList(this, {
                    module: this.$env.appURL + '/action/link/prodAlias',
                    loadOnStart: false,
                    param: {
                        rows: 25,
                        filtersRaw: [
                            {'id': 'prodId', 'property': 'prodId', 'value': giftItem.prodId, 'operator': '='},
                            {'id': 'sysSource', 'property': 'sysSource', 'value': 'MarketingPlatform', 'operator': '='},
                            {'id': 'activeFlg', 'property': 'activeFlg', 'value': 'Y', 'operator': '='},
                        ]
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} arrow="false">
                                <view
                                    style="padding: 11rpx 15rpx;-webkit-box-sizing: border-box;box-sizing: border-box;display: flex;width: 100%;-webkit-flex-direction: row;-ms-flex-direction: row;flex-direction: row;">{LovService.filter(data.unit, 'PROD_UNIT')}</view>
                            </item>
                        )
                    }
                }),
                tips: '',
                codeScope: ''
            }
        },
        async created () {
            let codeTemp = await this.$lov.getLovByType('LOG_CODE_LENGTH');
            let tempTips = [];
                if (!this.$utils.isEmpty(codeTemp)) {
                    for (let i = 0; i < codeTemp.length; i++) {
                    tempTips.push(codeTemp[i]['val']);
                }
                }
                this.codeScope = tempTips.sort();
                this.tips = tempTips.toString().replace(',', '或');
        },
        methods: {
            /**
             *  @description: 保存礼赠
             *  @author: 马晓娟
             *  @date: 2020/11/18 10:05
             */
            async save() {
                if (this.$utils.isEmpty(this.giftItem.prodId)) {
                    this.$message.primary('请选择赠送酒品');
                    return false;
                }
                if (!/^(0|([1-9]\d*))$/.test(this.giftItem.qty)) {
                    this.$message.primary('数量必须为大于零的整数');
                    return false;
                }
                if (this.$utils.isEmpty(this.giftItem.prodUnit)) {
                    this.$message.primary('请选择单位');
                    return false;
                }
                // 物流码校验正确性
                const len = this.giftItem.logCodeList.length;
                let ary = !!this.giftItem && !!this.giftItem.logCodeList && this.giftItem.logCodeList || [];
                let nary = ary.sort();
                for (let i = 0; i < nary.length - 1; i++) {
                    if (nary[i]['logCode'] === nary[i + 1]['logCode']) {
                        this.$message.primary('物流码不可以重复，请重新输入！');
                        return false;
                    }
                }
                for (let i = 0; i < len; i++) {
                    if (!this.checkLogCode(this.giftItem.logCodeList[i])) {
                        return false;
                    }
                }
                this.$utils.showLoading();
                if (this.giftItem.bussinessId) {
                    await this.saveGiftListData();
                    return;
                } else {
                    this.pageParam.callback(this.giftItem);//保存之后重新查询客户信息的拜访赠酒数据
                }
                this.$nav.back();
                this.$utils.hideLoading();
            },
            /**
             * @desc 保存礼赠明细数据
             * <AUTHOR>
             * @date 2022/5/9 14:37
             **/
            async saveGiftListData () {
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + '/action/link/visitProdLine/upsert', this.giftItem, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('保存赠品数据失败！' + response.result);
                    }
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    this.$message.success('保存赠品数据成功！');
                    this.giftItem = data.newRow;
                    this.pageParam.callback(this.giftItem);
                    this.$nav.back();
                }
            },
            /**
             *  @description: 扫码录入
             *  @author: 马晓娟
             *  @date: 2020/11/18 11:03
             */
            async scanCode() {
                const that = this;
                // 只允许从相机扫码
                await wx.scanCode({
                    onlyFromCamera: true,
                    success(res) {
                        that.relateBaseInfoFun(res.result);
                    }
                });
            },
            //扫码之后拿到码信息之后的操作
            async relateBaseInfoFun(mark) {
                const that = this;
                if (!!mark) {
                    const tempText = mark;
                    let text = tempText;
                    if (tempText.indexOf('http') !== -1 || tempText.indexOf('HTTP') !== -1) {
                        let index = tempText.lastIndexOf('\/');
                        if (tempText.indexOf('j=') !== -1) {
                            text = tempText.split('j=')[1]
                        } else {
                            text = tempText.substring(index + 1, tempText.length);
                        }
                    }
                    if (!that.checkLogCode({logCode: text})) {
                        return;
                    }
                    await this.checkProd(text);
                }
            },
            /**
             * @desc 根据产品编码和物流码匹配后端是否有相关产品数据
             * <AUTHOR>
             * @date 2022/5/11 16:52
             * @param logCode 物流码
             **/
            async checkProd (logCode) {
                this.$utils.showLoading();
                const data = await this.$httpForm.post(this.$env.appURL + '/action/link/logCode/queryProdInfoByCode', {logCode: logCode, prodCode: this.giftItem.prodCode}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('查询物流码数据失败！' + response.result);
                    }
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    if (data.result) {
                        this.giftItem.prodId = data.result.id;
                        this.giftItem.prodName = data.result.prodName;
                        this.giftItem.prodCode = data.result.prodCode;
                    }
                    // 处理扫码内容
                    if (!this.giftItem.logCodeList) this.giftItem.logCodeList = [];
                    // 如果第一条记录没有值,则填充到第一条记录中
                    if (!!this.giftItem.logCodeList[0] && !this.giftItem.logCodeList[0].logCode) {
                        this.giftItem.logCodeList[0].logCode = logCode;
                    } else {
                        // 否则则新建记录
                        const logisticCodeItem = {
                            orderLineId: this.giftItem.id,
                            logCode: logCode,
                            row_status: ROW_STATUS.NEW
                        };
                        this.giftItem.logCodeList.push(logisticCodeItem);
                    }
                    this.scanCode();
                }
            },
            /**
             *  @description: 选择单位
             *  @author: 马晓娟
             *  @date: 2020/11/18 10:05
             */
            async pickProdUnit() {
                if (this.$utils.isEmpty(this.giftItem.prodId)) {
                    this.$message.primary('请选择赠送酒品');
                    return
                }
                this.prodUnitOption.option.param.filtersRaw[0]['value'] = this.giftItem.prodId;
                this.prodUnitOption.methods.reload();
                const data = await this.$object(this.prodUnitOption);
                this.$set(this.giftItem, 'prodUnit', data.unit);
            },
            /**
             * 创建新的物流码
             * <AUTHOR>
             * @date 2019-06-27
             */
            addNewLogisticCode() {
                if (!this.giftItem.logCodeList) this.giftItem.logCodeList = [];
                const len = this.giftItem.logCodeList.length;
                for (let i = 0; i < len; i++) {
                    if (this.$utils.isEmpty(this.giftItem.logCodeList[i].logCode)) {
                        this.$showError('物流码列表中存在为空的记录,请输入后保存');
                        return;
                    }
                }
                const logisticCodeItem = {
                    logCode: '',
                    row_status: ROW_STATUS.NEW
                };
                this.giftItem.logCodeList.push(logisticCodeItem);
            },
            /**
             * 物流码失去焦点事件
             * <AUTHOR>
             * @date 2019-07-22
             * @param item 物流码对象
             */
            checkLogCode(item) {
                let lenTemp = item.logCode.length;
                if (this.tips.indexOf(lenTemp.toString()) !== -1 && lenTemp >= parseInt(this.codeScope[0], 10)) {
                    return true;
                }
                this.$showError(`物流码限定必须为${this.tips}位`);
                return false;
            },
            /**
             *  @description: 删除物流码信息
             *  @author: 马晓娟
             *  @date: 2020/11/18 10:04
             */
            async deleteLogisticCode(item, index) {
                if (item.row_status === ROW_STATUS.NEW) {
                    // 如果是前端新建的,则直接删除
                    this.giftItem.logCodeList.splice(index, 1);
                    this.$message.success('已删除');
                    return;
                }
                if (item.id) {
                    const data = await this.$http.post(this.$env.appURL + '/action/link/logCode/deleteById', item, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$showError(`删除失败：${response.result}`);
                        }
                    });
                }
                this.giftItem.logCodeList.splice(index, 1);
            }
        }
    }
</script>
<style lang="scss">
    .visit-register-cust-gift-page{
        .header{
            display: flex;
            margin-top: 24px;
            justify-content: space-between;
            font-size: 28px;
            line-height: 44px;
            background: white;
            padding: 24px;
            border-bottom: 2px solid #F2F2F2;
            .header-right{
                display: flex;
                color: #4c8dff;
                .link-icon{
                    font-size: 44px;
                }
            }
        }
        .logistic-list{
            border-bottom: 2px solid #F2F2F2;
            .link-input{
                text-align: left;
            }
        }
    }

</style>
