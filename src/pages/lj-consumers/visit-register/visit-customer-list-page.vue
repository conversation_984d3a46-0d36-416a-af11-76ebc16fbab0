<template>
    <link-page class="visit-customer-list-page">
        <view class="view">
            <link-auto-list :option="autoList">
                <template slot-scope="{data,index}">
                    <link-swipe-action>
                        <link-swipe-option slot="option" @tap="handleCustomerInfoDelete(data,index)"
                                           v-if="editFlag">
                            删除
                        </link-swipe-option>
                        <item :key="index" :data="data" :arrow="false" class="market-activity-list-item">
                            <view slot="note">
                                <view class="consumers-list" slot="note">
                                    <view class="list-cell">
                                        <view class="media-list">
                                            <view class="media-list-name">
                                                {{ data.visitSinglePerson }}
                                            </view>
                                            <view class="media-list-label">
                                                <view class="loyalty-level">{{ data.type | lov('ACCT_SUB_TYPE') }}</view>
                                                <view class="loyalty-level">{{ data.loyaltyLevel | lov('ACCT_MEMBER_LEVEL') }}
                                                </view>
                                                <view class="important-account" v-if="data.impFlag === 'Y'">重点客户</view>
                                                <!--<view class="black-gold-account" v-if="data.impFlag === 'Y'">黑金</view>-->
                                            </view>
                                            <view class="media-list-info">
                                                <view class="media-list-info-item">
                                                    <view class="label">联系方式</view>
                                                    <view class="media-list-info-phone">
                                                        {{ data.telephone }}
                                                    </view>
                                                </view>
                                                <view class="media-list-info-item">
                                                    <view class="label">单位</view>
                                                    <view class="media-list-info-text">{{ data.company }}</view>
                                                </view>
                                                <view class="media-list-info-item">
                                                    <view class="label">职务</view>
                                                    <view class="media-list-info-text">{{ data.position }}</view>
                                                </view>
                                                <view class="media-list-info-item">
                                                    <view class="label">所属客户</view>
                                                    <view class="media-list-info-text">{{ data.belongToStore }}</view>
                                                </view>
                                            </view>
                                        </view>
                                    </view>
                                    <view class="account-label"><view class="label">{{ data.salesmanName }}跟进</view></view>
                                </view>
                            </view>
                        </item>
                    </link-swipe-action>
                </template>
            </link-auto-list>
        </view>
    </link-page>
</template>

<script>

export default {
    name: "consumer-list-page",
    data() {
        const visitId = this.pageParam.visitId;             // 查询id
        const editFlag = this.pageParam.editFlag;           // 是否可编辑
        const autoList = new this.AutoList(this, {
            url: {
                queryByExamplePage: this.$env.appURL + '/action/link/visitCustInfo/queryByExamplePage'
            },
            searchFields: ['acctNameSecret', 'telephoneNotEncrypted'],
            param: {
                visitId: visitId,
                queryProdsFlag: 'Y',
                order: 'desc',
                sort: 'created',
                rows: 25,
                page: 1
            },
            sortOptions: null,
            exactSearchFields: [{
                field: 'acctNameSecret',
                showValue: '客户姓名', // 展示名,用于显示的字段=
                exactSearch: true,
                searchOnChange: true,
                clearOnChange: true
            }, {
                field: 'telephoneNotEncrypted',
                showValue: '客户手机号', // 展示名,用于显示的字段=
                exactSearch: true,
                searchOnChange: true,
                clearOnChange: true
            }],
            hooks: {
                beforeLoad (option) {
                    for (let i = 0; i < option.param.filtersRaw.length; i++) {
                        if (option.param.filtersRaw[i].property === 'acctNameSecret') {
                            option.param.filtersRaw[i].operator = 'like';
                        }
                    }
                }
            }
        });
        return {
            visitId,
            editFlag,
            autoList,
            currentConsumerData:{},
            matchTypeColor: { // 匹配状态显示颜色
                Interaction: '#32CD32', // 新增-绿色
                Absent: '#FF0000', // 未到场-红色
                Success: '#2F69F8' // 匹配成功-蓝色
            }
        }
    },
    async created() {
        console.log(this.pageParam)
    },
    methods: {
        /**
         * 删除拜访客户
         * <AUTHOR>
         * @date 2023-01-31
         * */
        async handleCustomerInfoDelete(item,index) {
            // 如果有拜访申请行数据，需要释放
            if (!this.$utils.isEmpty(item.bizLineId)) {
                await this.updateVisitApplyLine(item.bizLineId, null);
            }
            const data = await this.$http.post(this.$env.appURL + '/action/link/visitCustInfo/deleteById', item, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`删除拜访客户失败：${response.result}`);
                }
            });
            if (data.success) {
                await this.autoList.methods.reload();
                this.pageParam.callback();
            }
        },
        /**
         *  @description: 更新拜访申请客户行数据
         *  @author: songyanrong
         *  @date: 2020-07-07
         */
        async updateVisitApplyLine(id, bizLineId) {
            try {
                await this.$http.post(this.$env.appURL + '/action/link/visitCustInfo/updateBizLineId', {
                    id: id,
                    bizLineId: bizLineId
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$showError(`更新拜访申请行数据失败：${response.result}`);
                    }
                });
            } catch (e) {
                await this.$message.primary(`'更新拜访申请行数据失败！' + ${e}`);
                await this.$message.primary(`'拜访登记新增拜访客户，更新拜访申请行数据失败' + ${e}`);
            }
        }
    }
}
</script>

<style lang="scss">
.visit-customer-list-page {
    .view {
        //background: white;

        .item-header {
            height: 88px;
            width: 100%;
            padding-left: 32px;
            font-size: 28px;
            line-height: 88px;
            color: #262626;
            letter-spacing: 0;
            border-bottom: 2px solid #F2F2F2;
        }

        .market-activity-list-item {
            background: #FFFFFF;
            margin: 24px;
            height: 372px;
            border-radius: 16px;
        }

        .link-item {
            padding: 12px;
            overflow: hidden;
        }

        .link-item-body-left{
            overflow: visible;
        }

        .consumers-list {
            background-color: #FFFFFF;
            position: relative;
            width: 100%;
            display: flex;
            flex-direction: column;

            .list-cell {
                position: relative;
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                align-items: center;

                .media-list {
                    position: relative;
                    padding: 32px 24px 24px 24px;
                    box-sizing: border-box;
                    display: flex;
                    flex-direction: column;
                    width: 100%;

                    .media-list-name {
                        height: 48px;
                        font-family: PingFangSC-Semibold;
                        font-size: 32px;
                        color: #212223;
                        line-height: 48px;
                        font-weight: 600;
                    }

                    .media-list-label {
                        height: 36px;
                        margin: 16px 0;
                        display: flex;

                        .loyalty-level {
                            min-width: 80px;
                            padding: 0 15px;
                            margin-right: 16px;
                            background: #F0F5FF;
                            border-radius: 4px;
                            font-family: PingFangSC-Regular;
                            font-size: 22px;
                            color: #3F66EF;
                            letter-spacing: 0;
                            text-align: center;
                            line-height: 36px;
                            font-weight: 400;
                        }

                        .important-account {
                            width: 112px;
                            margin-right: 16px;
                            background: #FFF1EB;
                            border-radius: 4px;
                            font-family: PingFangSC-Regular;
                            font-size: 22px;
                            color: #FF461E;
                            line-height: 36px;
                            font-weight: 400;
                            text-align: center;
                        }

                        .black-gold-account {
                            width: 68px;
                            background: #262626;
                            border-radius: 4px;
                            font-family: PingFangSC-Regular;
                            font-size: 22px;
                            color: #F0BE94;
                            line-height: 36px;
                            font-weight: 400;
                            text-align: center;
                        }
                    }

                    .media-list-info {
                        display: flex;
                        flex-direction: column;

                        .media-list-info-item {
                            height: 44px;
                            display: flex;
                            align-items: center;
                            margin-bottom: 8px;

                            .label {
                                width: 112px;
                                margin-right: 24px;
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #999999;
                                line-height: 44px;
                                font-weight: 400;
                            }

                            .media-list-info-text {
                                width: 520px;
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #333333;
                                line-height: 44px;
                                font-weight: 400;
                            }

                            .media-list-info-phone {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #317DF7;
                                line-height: 44px;
                                font-weight: 400;
                            }
                        }
                    }

                    .media-list-logo {
                        height: 94px;
                        width: 94px;
                        margin-right: 20px;

                        image {
                            height: 100%;
                            width: 100%;
                        }
                    }

                    .media-list-body {
                        display: flex;
                        flex: 1;
                        flex-direction: column;
                        justify-content: space-between;
                        align-items: flex-start;
                        overflow: hidden;

                        .media-list-text-top {
                            width: 100%;
                            line-height: 36px;
                            font-size: 30px;
                            color: #262626;
                        }

                        .member-info {
                            margin: 18px 0;
                        }

                        .media-list-text-bottom {
                            width: 100%;
                            line-height: 42px;
                            font-size: 28px;
                            display: flex;
                            color: #333333;

                            .label {
                                color: #999999;
                                margin-right: 24px;
                            }

                            .loyalty-level {
                                width: 144px;
                                font-size: 24px;
                                font-weight: 400;
                                color: #41A2F6;
                                line-height: 36px;
                                background: #E9F5FF;
                                border-radius: 4px;
                                opacity: 1;
                                text-align: center;
                            }

                            .important-account {
                                margin-left: 16px;
                                width: 112px;
                                font-size: 24px;
                                font-weight: 400;
                                color: #FF7553;
                                line-height: 36px;
                                background: #FEEDEB;
                                border-radius: 4px;
                                text-align: center;
                                opacity: 1;
                            }
                        }
                    }

                    .media-list-right {
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;

                        .phone-call {
                            width: 180px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            padding-left: 20px;

                            .iconfont {
                                width: 72px;
                                height: 72px;
                                opacity: 1;
                                border: 1px solid #EEEEEE;
                                font-size: 40px;
                                color: #bcbcbc;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                border-radius: 50%;
                            }
                        }
                    }
                }
            }

            .account-label {
                position: absolute;
                right: -28px;
                top: -4px;
                padding: 8px 48px 4px 32px;
                background: #2F69F8;
                transform: skew(30deg, 0deg);

                .label {
                    font-family: PingFangSC-Regular;
                    font-size: 24px;
                    color: #FFFFFF;
                    text-align: center;
                    line-height: 40px;
                    font-weight: 400;
                    transform: skew(-30deg, 0);
                }
            }
        }

        .more {
            font-family: PingFangSC-Regular;
            width: 100%;
            text-align: center;
            font-size: 28px;
            color: #2F69F8;
            letter-spacing: 0;
            line-height: 76px;
            background-color: #f2f2f2;
        }
    }
}
</style>
