<template>
<link-page class="visit-account-select-page">
  <lnk-taps :taps="allCategory" v-model="currentStatus"></lnk-taps>
  <view v-if="currentStatus.seq === '1'" :key="0">
    <view class="blank"></view>
    <link-auto-list :option="consumerOption" class="visit-case">
      <template slot-scope="{data,index}">
        <item :key="index" :data="data" :arrow="false" class="visit-apply-list-item">
          <view slot="note">
            <view class="visit-apply-item">
              <link-checkbox :val=data.id slot="thumb" toggleOnClickItem @tap="ontapCheckBox(data)"/>
              <view class="visit-apply-item-content">
                <view style="display: flex;justify-content: space-between; padding-bottom: 24rpx">
                  <view class="left">{{data.acctName}}</view>
                  <view class="right">{{data.mobilePhone1}}</view>
                </view>
                <view style="display: flex;justify-content: space-between; padding-bottom: 24rpx">
                  <view class="left">{{data.company}}</view>
                  <view class="right">{{data.position}}</view>
                </view>
              </view>
            </view>
          </view>
        </item>
      </template>
    </link-auto-list>
  </view>
  <view v-if="currentStatus.seq === '2'" :key="1">
    <view class="blank"></view>
    <link-auto-list :option="giftListOption" class="enterprise">
      <template slot-scope="{data,index}">
        <item :key="index" :data="data" :arrow="false" class="visit-apply-list-item">
          <view slot="note">
            <view class="visit-apply-item">
              <link-checkbox :val=data.id slot="thumb" toggleOnClickItem @tap="ontapCheckBox(data)"/>
              <view class="visit-apply-item-content">
                <view style="display: flex;justify-content: space-between; padding-bottom: 24rpx">
                  <view class="left">{{data.visitSinglePerson}}</view>
                  <view class="right">{{data.telephone}}</view>
                </view>
                <view style="display: flex;justify-content: space-between; padding-bottom: 24rpx">
                  <view class="left">{{data.visitTitle}}</view>
                  <view class="right">{{data.company}}</view>
                </view>
              </view>
            </view>
          </view>
        </item>
      </template>
    </link-auto-list>
  </view>
</link-page>
</template>

<script>
  import LnkTaps from "../../core/lnk-taps/lnk-taps";
  import {ROW_STATUS} from "../../../utils/constant";
  export default {
    name: "visit-account-select-page",
    components: {LnkTaps},
    data () {
      const userInfo = this.$taro.getStorageSync('token').result;
      const visitRegisterItemCustInfoItem = this.pageParam.item;
      const visitRegisterItem = this.pageParam.visitRegisterItem;
      const allCategory = [
        {name: '消费者', seq: '1', val: 'consumer'},
        {name: '礼赠名单', seq: '2', val: 'giftList'}
      ];
      const consumerOption = new this.AutoList(this, {
        module: this.$env.appURL + '/action/link/consumer',
        createPath: '/pages/lj-consumers/account/account-item-edit-page',
        url: {
            queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/consumerListSend'
        },
        param: {
          oauth: 'MY_POSTN_ONLY',
          rows: 25,
          filtersRaw: [
            {id: 'companyId', property: 'companyId', value: userInfo.coreOrganizationTile['l3Id'], operator: '='},
            {id: 'consumerType', property: 'consumerType', value: 'ChannelConsumer', operator: '='},
            {id: 'accntChannel', property: 'accntChannel', value: 'MarketingPlatform', operator: '='},
            {id: 'empFlag', property: 'empFlag', value: 'N', operator: '='},
            {id: 'followFlag', property: 'followFlag', value: 'Y', operator: '='}
          ]
        },
        filterOption: [
            {label: '客户性别', field: 'gender', type: 'lov', lov: 'GENDER'}
        ],
          exactSearchFields: [
              {
                  field: 'acctName',
                  showValue: '姓名',
                  searchOnChange: true,
                  clearOnChange: true,
                  exactSearch: true
              }, {
                  field: 'mobilePhone1',
                  showValue: '手机号',
                  searchOnChange: true,
                  clearOnChange: true,
                  exactSearch: true
              }
          ],
        hooks: {
          beforeLoad (option) {
            for (let i = 0; i < option.param.filtersRaw.length; i++) {
                if (option.param.filtersRaw[i].property === 'acctName') {
                    option.param.filtersRaw[i].operator = 'like';
                }
            }
          },
          async beforeCreateItem(param) {
              const id = await this.$newId();
              param.data = {
                  id: id,
                  row_status: ROW_STATUS.NEW,
                  consumerDataType: 'ChannelConsumer',
                  dataSource: 'MarketingPlatform',
                  dataType: 'Consumer',
                  accntSourceFrom: 'SalesAssistant',
                  orgId: userInfo.orgId,
                  fstName: '',
                  postnId: userInfo.postnId,
                  belongToCompanyId: userInfo.coreOrganizationTile['l3Id'],
                  type: "ToBeFollowed",
                  birthType: 'Yang',
                  brandPreference: "",
                  hobby: "",
                  terminalFlag : 'N',
                  loyaltyLevel: 'None'
              };
              param.userInfo = userInfo;
              param.pageFrom = "VisitAccount";
              param.visitRegisterItemCustInfoItem = visitRegisterItemCustInfoItem;
              param.visitRegisterItem = visitRegisterItem;
          }
        }
      });
      return {
        allCategory,
        visitRegisterItemCustInfoItem,
        currentStatus: {},
        consumerOption,
        giftListOption: new this.AutoList(this, {
          url: {
            queryByExamplePage: this.$env.appURL + '/action/link/interVisitOrg/queryVisitCustInfoPage',
          },
          searchFields: ['visitTitle', 'telephoneSecret', 'visitSpSecret'],
          param: {
            oauth: 'MY_ORG_ONLY',
            rows: 25,
            filtersRaw: [
              {id: 'bizLineId', property: 'bizLineId', value: '', operator: 'IS NULL'},
              {id: 'visitType', property: 'visitType', value: 'VisitApply'},
              {id: 'visitApplicationStatus', property: 'visitApplicationStatus', value: 'Approved'},
              {id: 'approvalStatus', property: 'approvalStatus', value: 'Approved'},
            ]
          }
        })
      }
    },
    created() {
      this.currentStatus = this.allCategory[0];
    },
    methods: {
        onBack(){
            this.consumerOption.methods.reload();
        },
      /**
       *  @description: 选中数据
       *  @author: 马晓娟
       *  @date: 2020/11/17 18:11
       */
      ontapCheckBox(item) {
        if (this.currentStatus.val === 'consumer') {
          this.$set(this.visitRegisterItemCustInfoItem, 'consumerId', item.id);
          this.$set(this.visitRegisterItemCustInfoItem, 'visitSinglePerson', item.acctName);
          this.$set(this.visitRegisterItemCustInfoItem, 'telephone', item.mobilePhone1);
          this.$set(this.visitRegisterItemCustInfoItem, 'company', item.company);
          this.$set(this.visitRegisterItemCustInfoItem, 'companyId', item.companyId);
          this.$set(this.visitRegisterItemCustInfoItem, 'position', item.position);
        } else {
          this.$set(this.visitRegisterItemCustInfoItem, 'consumerId', item.consumerId);
          this.$set(this.visitRegisterItemCustInfoItem, 'visitSinglePerson', item.acctName);
          this.$set(this.visitRegisterItemCustInfoItem, 'telephone', item.telephone);
          this.$set(this.visitRegisterItemCustInfoItem, 'company', item.company);
          this.$set(this.visitRegisterItemCustInfoItem, 'companyId', item.companyId);
          this.$set(this.visitRegisterItemCustInfoItem, 'position', item.position);
          this.$set(this.visitRegisterItemCustInfoItem, 'bizLineId', item.id);
          this.$set(this.visitRegisterItemCustInfoItem, 'giftStandar', item.giftStandar);  // 慰问品标准
          this.$set(this.visitRegisterItemCustInfoItem, 'actProdGroupId', item.actProdGroupId); // 活动档期id
          this.$set(this.visitRegisterItemCustInfoItem, 'actProdGroupName', item.actProdGroupName);  // 活动档期名称
          this.$set(this.visitRegisterItemCustInfoItem, 'visitAccntId', item.id);  // 拜访客户id
        }
        this.visitRegisterItemCustInfoItem['tabType'] = this.currentStatus.val;
        this.pageParam.callback({'visitRegisterItemCustInfoItem': this.visitRegisterItemCustInfoItem});
        this.$nav.back();
      }
    }
  }
</script>

<style lang="scss">
  .visit-account-select-page{
    .blank {
      width: 100%;
      height: 96px;
      background: #F2F2F2;
    }

    .visit-apply-item {
      display: flex;
      align-items: center;
      .link-checkbox{
        width: 10%;
      }
      .visit-apply-item-content{
        width: 90%;
        padding-left: 24px;
      }
    }
  }
</style>
