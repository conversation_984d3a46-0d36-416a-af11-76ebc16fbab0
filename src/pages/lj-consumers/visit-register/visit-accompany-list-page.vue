<template>
    <link-page class="visit-customer-list-page">
        <view class="view">
            <link-auto-list :option="autoList">
                <template slot-scope="{data,index}">
                    <link-swipe-action>
                        <link-swipe-option slot="option" @tap="handleAccompanyDelete(data,index)"
                                           v-if="editFlag">
                            删除
                        </link-swipe-option>
                        <item :key="index" :data="data" :arrow="false" class="market-activity-list-item">
                            <view slot="note">
                                <view class="consumers-list" slot="note">
                                    <view class="list-cell">
                                        <view class="media-list">
                                            <view class="media-list-body">
                                                <view class="body-left">
                                                    <view class="media-list-text-top">
                                                        <view class="name">{{data.accompanyName}}</view>
                                                    </view>
                                                    <view class="media-list-tel">
                                                        <view style="line-height: 27px;width:25%;float:left;color: #8C8C8C">手机号</view>
                                                        <view style="line-height: 27px;padding-left: 9px">
                                                            {{data.accompanyTel}}
                                                        </view>
                                                    </view>
                                                </view>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </item>
                    </link-swipe-action>
                </template>
            </link-auto-list>
        </view>
    </link-page>
</template>

<script>

export default {
    name: "consumer-list-page",
    data() {
        const visitId = this.pageParam.visitId;             // 查询id
        const editFlag = this.pageParam.editFlag;           // 是否可编辑
        const type = this.pageParam.type;                   // 查询类型
        const autoList = new this.AutoList(this, {
            url: {
                queryByExamplePage: this.$env.appURL + '/action/link/visitAccompany/queryByExamplePage'
            },
            searchFields: ['accompanyName', 'accompanyTel'],
            param: {
                visitId: visitId,
                queryProdsFlag: 'Y',
                order: 'desc',
                sort: 'created',
                rows: 25,
                page: 1,
                filtersRaw: [{id: 'accompanyType', property: 'accompanyType', value: type, operator: '='}],
            },
            sortOptions: null
        });
        return {
            visitId,
            editFlag,
            type,
            autoList,
        }
    },
    async created() {
        console.log(this.pageParam)
    },
    methods: {
        /**
         *  @description: 删除陪同人员或者协访领导
         *  @author: songyanrong
         *  @date: 2020-07-17
         */
        async handleAccompanyDelete(item) {
            const data = await this.$http.post(this.$env.appURL + '/action/link/visitAccompany/deleteById', item, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`删除失败：${response.result}`);
                }
            });
            if (data.success) {
                await this.autoList.methods.reload();
                this.pageParam.callback(this.type);
            }
        }
    }
}
</script>

<style lang="scss">
.visit-customer-list-page {
    .view {
        background: white;

        .item-header {
            height: 88px;
            width: 100%;
            padding-left: 32px;
            font-size: 28px;
            line-height: 88px;
            color: #262626;
            letter-spacing: 0;
            border-bottom: 2px solid #F2F2F2;
        }

        .consumers-list {
            background-color: #FFFFFF;
            position: relative;
            width: 100%;
            display: flex;
            flex-direction: column;

            .list-cell {
                position: relative;
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                align-items: center;

                .media-list {
                    padding: 11px 15px;
                    box-sizing: border-box;
                    display: flex;
                    width: 100%;
                    flex-direction: row;

                    .media-list-body {
                        flex: 1;
                        flex-direction: column;
                        justify-content: space-between;
                        align-items: flex-start;
                        overflow: hidden;

                        .body-left {
                            width: 75%;
                            float: left;

                            .media-list-text-top {
                                width: 100%;

                                .name {
                                    font-family: PingFangSC-Semibold;
                                    font-size: 28px;
                                    color: #262626;
                                    letter-spacing: 0;
                                    line-height: 44px;
                                    width: 100%;
                                    float: left;
                                    height: 44px;
                                    overflow: hidden;
                                }
                            }

                            .media-list-tag {
                                width: 100%;
                                line-height: 32px;
                                display: flex;
                                color: #333333;
                                margin: 10px 0 0 0;
                                .loyalty-level{
                                    width: 144px;
                                    font-size: 24px;
                                    font-weight: 400;
                                    color: #41A2F6;
                                    line-height: 36px;
                                    background: #E9F5FF;
                                    border-radius: 4px;
                                    opacity: 1;
                                    text-align: center;
                                }
                                .media-list-important {
                                    padding: 4px 8px;
                                    font-size: 24px;
                                    font-weight: 400;
                                    color: #FF7553;
                                    line-height: 36px;
                                    background: #FEEDEB;
                                    border-radius: 4px;
                                    text-align: center;
                                }
                            }

                            .media-list-text-bottom {
                                font-family: PingFangSC-Regular;
                                display: flex;
                                justify-content: flex-start;
                                width: 100%;
                                font-size: 28px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 56px;
                                height: 56px;
                            }
                            .media-list-tel {
                                font-family: PingFangSC-Regular;
                                display: flex;
                                justify-content: flex-start;
                                width: 100%;
                                font-size: 28px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 56px;
                                height: 56px;
                            }
                        }

                        .body-right {
                            width: 25%;
                            float: left;

                            .status-view {
                                width: 120px;
                                transform: skewX(-10deg);
                                border-radius: 4px;
                                background: #2F69F8;
                                box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                                height: 36px;
                                .status {
                                    font-size: 20px;
                                    color: #FFFFFF;
                                    letter-spacing: 2px;
                                    text-align: center;
                                    line-height: 36px;
                                }
                            }
                        }
                    }
                }
            }
        }

        .more {
            font-family: PingFangSC-Regular;
            width: 100%;
            text-align: center;
            font-size: 28px;
            color: #2F69F8;
            letter-spacing: 0;
            line-height: 76px;
            background-color: #f2f2f2;
        }
    }
}
</style>
