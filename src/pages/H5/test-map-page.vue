<template>
    <link-page>
        <web-view :src="url"></web-view>
    </link-page>
</template>

<script>
export default {
    name: "testMap",
    data() {
        return {
            url: this.pageParam.url || 'https://msptest.lzlj.com/pages/H5/index.html',
        }
    },
    onLoad(options){
        this.url =decodeURIComponent(options.url)
        console.log(options.url,'-----pageparam.url2')
    }
}
</script>

<style scoped>

</style>
