<template>
    <link-page class="mkt-check-page">
        <lnk-taps :taps="marketActivityOptions" v-model="marketActivityStatusActive"
                  @switchTab="onTap"></lnk-taps>
        <link-auto-list :option="autoList" :searchInputBinding="{props:{placeholder:'活动编码/活动名称/执行案编码'}}">
            <link-filter-group slot="filterGroup">
<!--                <link-filter-item label="创建时间(降序)" :param="{sort:{field:'created',desc:true}}"/>-->
<!--                <link-filter-item label="更新创建时间(降序)" :param="{sort:{field:'lastUpdated',desc:true}}"/>-->
<!--                <link-filter-item label="活动开始日期(降序)" :param="{sort:{field:'startTime',desc:true}}"/>-->
<!--                <link-filter-item label="现金申请金额(降序)" :param="{sort:{field:'activityAmount',desc:true}}"/>-->
                <link-sort
                    :options="sortOptions"
                    :value="sortVal[0]"
                    @input="sortChange"/>
            </link-filter-group>
            <template slot-scope="{data,index}">
                <link-swipe-action>
                    <link-swipe-option v-if="marketActivityStatusActive.val==='dispatch'" slot="option" @tap="beforeSendBack(data)">
                        退回
                    </link-swipe-option>
                    <item :key="index" :data="data" :arrow="false" class="market-activity-list-item"
                          @tap="gotoItem(data)">
                        <view slot="note">
                            <view class="media-list">
                                <view class="media-top">
                                    <view class="num-view">
                                        <view class="num">{{data.activityNum}}</view>
                                    </view>
                                    <view class="tab-info">
                                        <view class="iconBar">
                                            <view  style="color: #EA3232; flex: 1">
                                                <view v-if="data.executeStatus==='Overdue'&& data.activityStatus!=='Inactive' && data.activityStatus!=='new'&& positionFlag">
                                                    <link-icon class="icon" icon="icon-chaoqi"/><text>超期</text>
                                                </view>
                                            </view>
                                            <view style="color: #EC974A;flex: 1">
                                                <view v-if="data.isVirtualAct==='virtualAct'&& data.activityStatus!=='Inactive' && data.activityStatus!=='new'&& positionFlag" >
                                                    <link-icon class="icon" icon="icon-xuni"/><text>虚拟</text>
                                                </view>
                                            </view>
                                        </view>
                                        <view  class="ai-error" v-if="data.errorInfo === 'Y'">
                                            <link-icon icon="icon-chaoqi" />
                                            <text>识别结果异常</text>
                                        </view>
                                    </view>
                                    <status-button :label="data.activityStatus| lov('MC_STATUS')"></status-button>
                                </view>
                            </view>
                            <view class="content-middle">
                                <view class="name">{{data.activityName.length > 20 ? data.activityName.substring(0,20) + '...' : data.activityName }}</view>
                            </view>
                            <view class="content-middle-line">
                                <view class="data">
                                    <view class="title">审批状态 :</view>
                                    <view class="val" :class="data.aproStatus">{{data.aproStatus| lov('APRO_STATUS')}}</view>
                                </view>
                                <view class="sum">
                                    <view class="title">稽核状态 :</view>
                                    <view class="val">{{data.whetherAudit| lov('WHETHER_AUDIT')}}</view>
                                </view>
                            </view>
                            <view class="content-middle-line">
                                <view class="data">
                                    <view class="title">活动时间 :</view>
                                    <view class="val">{{data.startTime|date('YYYY-MM-DD')}}</view>
                                </view>
                                <view class="sum">
                                    <view class="title">提报人 :</view>
                                    <view class="val">{{data.executor}}</view>
                                </view>
                            </view>
                            <view class="content-middle-line">
                                <view class="data">
                                    <view class="title">执行案编码 :</view>
                                    <view class="val">{{data.exeCaseCode}}</view>
                                </view>
                                <view class="sum">
                                    <view class="title">申报金额 :</view>
                                    <view class="val">{{data.activityAmount}}</view>
                                </view>
                            </view>
                        </view>
                    </item>
                </link-swipe-action>
            </template>
        </link-auto-list>

        <link-dialog ref="mktCheckDialog">
            <view slot="head">
                提示:请填写退回原因
            </view>
            <view style="width: 100%">
                <link-textarea mode="textarea" v-model="sendBackRemark" />
            </view>
            <link-button slot="foot" @tap="$refs.mktCheckDialog.hide()">取消</link-button>
            <link-button slot="foot" @tap="sendBack">确定</link-button>
        </link-dialog>

    </link-page>
</template>

<script>
import {ROW_STATUS} from "../../../utils/constant";
import Taro from "@tarojs/taro";
import StatusButton from "../../lzlj/components/status-button";
import LnkTaps from "../../core/lnk-taps/lnk-taps";
import {ComponentUtils,getFiltersRaw} from "link-taro-component";

export default {
    name: "mkt-check-page",
    components: {LnkTaps, StatusButton},
    data() {
        const autoList = new this.AutoList(this, {
            module: '',
            url:{
                queryByExamplePage: 'action/link/dispatch/queryDispatchCheckActPage'
            },
            param: {
                attr1: 'NoCheck',
                filtersRaw:[],
            },
            sortField: 'startTime',
            sortDesc: 'desc',
            hooks: {
                beforeLoad(option) {
                    this.autoList.list = [];
                }
            },
            filterOption: [
                {label: '抽检状态', field: 'whetherAudit', type: 'lov', lov: 'WHETHER_AUDIT', multiple: false},
                {label: '方案类型', field: 'isVirtualAct', type: 'lov', lov: 'SCENARIO_TYPE', multiple: false},
                {label: '活动开始时间', field: 'startTime', type: 'date'},
                {label: '活动结束时间', field: 'endTime', type: 'date'},
            ],
            searchFields: ['activityNum','activityName','exeCaseCode'],
            sortOptions: null,
        });
        const marketActivityOptions = [
            {name: '派单', seq: '1', val: 'dispatch', property: 'customField',},
            {name: '未抽检', seq: '2', val: 'NoCheck', property: 'customField',},
            {name: '已抽检', seq: '3', val: 'check', property: 'customField',},
            {name: '全部', seq: '4', val: 'all', property: 'customField',},
        ];
        const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
        return {
            mktObject:{},
            sendBackRemark:'',
            marketActivityOptions,
            autoList,
            userInfo,
            positionFlag:false,
            searchVal: '',
            sortVal: {
                0: {field: 'created', desc: true, type: 'desc'},
            },
            marketActivityStatusActive: {name: '未抽检', seq: '2', val: 'NoCheck', property: 'customField'},
            sortOptions: [
                {label: '活动开始日期', field: 'startTime', desc: false},
                {label: '现金申请金额', field: 'activityAmount', desc: false},
            ],
            filterOption: [
                {label: '抽检状态', field: 'whetherAudit', type: 'lov', lov: 'WHETHER_AUDIT', multiple: false},
                {label: '方案类型', field: 'isVirtualAct', type: 'lov', lov: 'SCENARIO_TYPE', multiple: false},
                {label: '活动开始时间', field: 'startTime', type: 'date'},
                {label: '活动结束时间', field: 'endTime', type: 'date'},
            ],
            copyActivityParam: null,
        };
    },
    watch: {
        searchVal(newVal, oldVal) {
            if (newVal !== oldVal) {
                const searchObj = {
                    id: "searchValue",
                    operator: "or like",
                    property: "[activityNum,activityName,exeCaseCode]",
                    value: newVal
                };
                this.autoList.option.param['filtersRaw'] = this.autoList.option.param['filtersRaw'].filter((item) => item.property !== "[activityNum,activityName,cashApplyAmount,executor]");
                this.autoList.option.param['filtersRaw'].push(searchObj);
                this.autoList.methods.reload()
            }
        },
        filterOption() {
            this.filterList(getFiltersRaw(this.filterOption))
        }
    },
    mounted() {

    },
    async created() {
        this.positionFlag=this.userInfo.positionType === 'FinanceStaff' || this.userInfo.positionType === 'AuditStaff' || this.userInfo.positionType === 'InspectionStaff';

    },
    methods: {
        beforeSendBack(data){
            this.mktObject = data;
            this.$refs.mktCheckDialog.show();
        },
        async onBack(param){
          await this.autoList.methods.reload()
        },
        async sendBack(){
            if(this.$utils.isNotEmpty(this.sendBackRemark)){
                try {
                    this.$utils.showLoading();
                    const data = await this.$http.post('action/link/dispatch/marketBackButton', {
                        actId: this.mktObject.id,
                        remark: this.sendBackRemark
                    });
                    this.$utils.hideLoading();
                    if (data.success) {
                        this.$message.success('退回成功');
                        this.$refs.mktCheckDialog.hide();
                        await this.autoList.methods.reload();
                    } else {
                        this.$showError('退回失败');
                    }
                    this.sendBackRemark = '';
                }catch (e){
                    this.$utils.hideLoading();
                    this.$refs.mktCheckDialog.hide();
                    this.$showError('退回异常');
                }
            }else{
                this.$showError('退回原因必填');
            }
        },
        async filterList(param) {
            const that = this;
            this.autoList.option.param = {
                attr1: this.marketActivityStatusActive.val,
                filtersRaw:[],
            };
            if (this.$utils.isEmpty(param)) {
                this.copyActivityParam.filtersRaw = this.copyActivityParam.filtersRaw.filter((item) => !(item.property === 'whetherAudit' || item.property === 'isVirtualAct'));
                this.autoList.option.param = this.copyActivityParam;
                await this.autoList.methods.reload();
            } else {
                that.autoList.option.param.filtersRaw = that.autoList.option.param.filtersRaw.filter((item) => !(item.property === 'whetherAudit' || item.property === 'isVirtualAct'));
                param.forEach(item => {
                    that.autoList.option.param.filtersRaw.push(item);
                });
                await that.autoList.methods.reload();
            }
            this.copyActivityParam = this.$utils.deepcopy(this.autoList.option.param);
        },

        async onTap(item) {
            this.autoList.option.param.attr1  = item.val;
            this.autoList.list = [];
            await this.autoList.methods.reload();
        },
        async gotoItem(data) {
            // const cacheData = await this.$http.post('action/link/marketAct/queryById', {
            //     id: data.id
            // });
            // this.$dataService.setMarketActivityItem(cacheData.result);
            this.$nav.push('/pages/lj-market-activity/work-order/activity-approva-audit-page', {
                data: data,
                link: 'check'
            })
        },

        sortChange(val) {
            this.autoList.sortField = val.field;
            this.autoList.sortDesc = val.desc;
            this.autoList.methods.reload();
        },

    }
}
</script>

<style lang="scss">
@import "../../../styles/list-card";
.mkt-check-page {
    background-color: #F2F2F2;
    font-family: PingFangSC-Regular;
    .tab-info {
        width: 30%;
        line-height: normal;
        .iconBar{
            display:flex;
        }
        .ai-error {
            color: #EA3232;
        }
    }
    .link-sticky-top{
        top: 94px !important;
    }
    .sort-search-filter-container {
        height: 100px;
        margin-bottom: 22px;

        .sort-search-filter {
            position: fixed;
            width: 100%;
            background: #ffffff;
            margin-top: 2px;
            border-top: 1px solid #F2F2F2;

            .sort-filter-view {
                @include flex-start-center;
                @include space-between;
                border-top: 1px solid #f2f2f2;

                .link-sort {
                    padding: 32px 0 24px 24px;
                    width: 50%;
                }

                .search-filter {
                    @include flex-start-center;

                    .icon-sousuo {
                        font-size: 28px;
                        color: #595959;
                        padding: 38px 24px 30px 24px;
                    }

                    .link-filter {
                        padding: 32px 24px 24px 16px;
                    }
                }
            }
        }
    }

    /*deep*/
    .lnk-tabs-container {
        height: 92px;
    }

    .market-activity-list-item {
        background: #FFFFFF;
        width: 95%;
        margin: 24px auto auto auto;
        border-radius: 16px;

        .media-list {
            @include media-list;

            .media-top {
                width: 100%;
                @include flex-start-center;
                @include space-between;
                height: 80px;
                line-height: 80px;

                .left-content {
                    font-family: PingFangSC-Semibold;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 32px;
                    padding-top: 20px;

                }

                .right-content {
                    font-family: PingFangSC-Semibold;
                    font-size: 32px;
                    color: #FF5A5A;
                    letter-spacing: 0;
                    text-align: right;
                    line-height: 32px;
                    padding-top: 20px;
                }

                .num-view {
                    border-radius: 8px;
                    line-height: 50px;
                    background: #FFFFFF;
                    .num {
                        font-size: 28px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        line-height: 40px;
                        padding: 2px 8px;
                    }
                }

                .status-view {
                    width: 120px;
                    transform: skewX(-10deg);
                    border-radius: 4px;
                    background: #2F69F8;
                    box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                    height: 36px;

                    .status {
                        font-size: 20px;
                        color: #FFFFFF;
                        letter-spacing: 2px;
                        text-align: center;
                        line-height: 36px;
                    }
                }
            }
        }

        .content-middle {
            width: 100%;
            @include flex-start-center;
            @include space-between;
            height: 80px;
            line-height: 80px;

            .content {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
            }

            .name {
                font-family: PingFangSC-Semibold;
                font-size: 32px;
                color: #262626;
                letter-spacing: 0;
                line-height: 32px;
            }
        }

        .content-middle-line {
            width: 100%;

            .data {
                width: 60%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 24px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    float: left;
                    margin-right: 14px;
                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 24px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                }

                .Submitted, .Feedback{
                    color: #2F69F8;
                }

                .Approve, .FeedbackApro{
                    color: #2EB3C2;
                }

                .Refused, .Refeedback{
                    color: #FF5A5A;
                }

            }

            .sum {
                width: 40%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 24px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    float: left;
                    margin-right: 14px;
                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 24px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                }
            }

            .sum-2 {
                width: 100%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    float: left;
                    margin-right: 5px;
                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                }
            }
        }
    }
}
</style>

