<template>
    <link-page class="cloud-classify-page">
        <view v-for="(item,index) in classifyList" class="classify-v" @tap="gotoSubClassPage(item)" :key="index" :class="{'cf-l':index%2 == 0}">
            <view class="classify-view">
                <view class="classify-img">
                    <image :src="item.imgUrl" lazy-load="true"></image>
                </view>
                <view class="classify-title">{{item.costType}}</view>
            </view>
        </view>
        <view v-if="$utils.isEmpty(classifyList)">
            <view class="no-data" :style="{'background-image': 'url(' + $imageAssets.noDataImage + ')'}"></view>
            <view class="no-data-msg">暂无数据</view>
        </view>
    </link-page>
</template>

<script>
    export default {
        name: "cloud-classify-page",
        data() {
            return {
                classifyList: []
            }
        },
        async created() {
            await this.queryCloudClassifyData();
        },
        methods: {
            /**
             * 跳转至小类界面
             * <AUTHOR>
             * @date 2020-08-17
             * */
            async gotoSubClassPage(item) {
                this.$nav.push('/pages/lj-market-activity/cloud/cloud-subclass-page', {
                    title: item.costType,
                    costTypeCode: item.costTypeCode
                })
            },
            /**
             * 查询云空间图片classify
             * <AUTHOR>
             * @date 2020-09-02
             * */
            async queryCloudClassifyData() {
                const data = await this.$http.post('action/link/marketAct/groupByActType');
                this.classifyList = data.rows;
                this.classifyList.forEach(async (item) => {
                    let imgUrl = await this.$image.getSignedUrl(item.coverImg);
                    this.$set(item, 'imgUrl', imgUrl);
                })
            }
        }
    }
</script>

<style lang="scss">
    .cloud-classify-page {
        background: white;

        .no-data {
            width: 368px;
            height: 368px;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            -moz-background-size: 100% 100%;
            margin: auto;
            margin-top: 100px;
        }

        .no-data-msg {
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #8C8C8C;
            letter-spacing: 0;
            text-align: center;
            line-height: 38px;
            padding: 50px;
        }
        .cf-l{
            clear: left;
        }
        .classify-v {
            width: 42%;
            float: left;
            margin: 24px 24px 0 24px;

            .classify-view {
                .classify-img {
                    width: 340px;
                    height: 340px;

                    image {
                        width: 100%;
                        height: 100%;
                        border-radius: 16px;
                    }
                }

                .classify-title {
                    font-family: PingFangSC-Semibold;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 32px;
                    padding-top: 24px;
                }
                .classify-num {
                    font-family: PingFangSC-Regular;
                    font-size: 24px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    text-align: left;
                    line-height: 24px;
                    padding-top: 16px;
                }
            }
        }
    }
</style>
