<template>
    <link-page class="cloud-pic-item-page">
        <view style="width: 100%;height: 12px"></view>
        <view class="pic-view">
            <view class="pic-v" v-for="(item,index) in imgList" :key="index">
                <view class="img-v">
                    <image :src="item.imgUrl"  @tap="clickImg(index)" lazy-load="true"></image>
                </view>
                <view class="title-v">
                    <view class="title">
                        {{item.moduleType | lov('FEEDBACK_PHOTO_TYPE')}}
                    </view>
                </view>
            </view>
        </view>
    </link-page>
</template>

<script>
    export default {
        name: "cloud-pic-item-page",
        data() {
            const activityItem = this.pageParam.data;
            this.$taro.setNavigationBarTitle({title: activityItem.activityName});
            return {
                imgList:[],
                originalPath: [],           // 原图片url数组列表
                activityItem
            }
        },
        async created() {
            await this.queryActivityPic();
        },
        methods:{
            async queryActivityPic(){
                if (this.$utils.isEmpty(this.activityItem.id)) {
                    return;
                }
                const data = await this.$http.post('action/link/attachment/queryByExamplePage', {
                    uploadType: 'cos',
                    sort: 'created',
                    order: 'desc',
                    headId: this.activityItem.id,
                    rows: 2000,
                    queryFields: 'id,uploadType,attachmentPath,moduleType,headId,dataSource,created,smallurl'
                });
                this.imgList = data.rows;
                this.imgList.forEach(async (item) => {
                    let imgUrl = await this.$image.getSignedUrl(item.attachmentPath)
                    this.$set(item, 'imgUrl', imgUrl);
                })
            },
            /**
             * 点击图片预览，直接调用imgService服务
             * <AUTHOR>
             * @date 2020-07-09
             * @param index 当前图片对象所属下标
             */
            clickImg (index) {
                // 获取原图
                this.imgList.forEach(async (item) => {
                    let imgUrl = await this.$image.getSignedUrl(item.attachmentPath)
                    this.$set(item, 'originalImgUrl', imgUrl);
                    this.originalPath.push(item.originalImgUrl)
                    if (this.imgList.length === this.originalPath.length) {
                        const inOptions = {
                            current: this.originalPath[index],
                            urls: this.originalPath
                        }
                        this.$image.previewImages(inOptions)
                    }
                })
                if (this.originalPath.length !== 0) {
                    const inOptions = {
                        current: this.originalPath[index],
                        urls: this.originalPath
                    }
                    this.$image.previewImages(inOptions)
                }
            },
        }
    }
</script>

<style lang="scss">
    .cloud-pic-item-page {
        background: white;

        .pic-view {
            margin: 0 0 24px 24px;

            .pic-v {
                width: 232px;
                height: 232px;
                float: left;
                margin: 0 4px 4px 0;
                position: relative;

                .img-v {
                    width: 100%;
                    height: 100%;
                    position: absolute;

                    image {
                        width: 100%;
                        height: 100%;
                    }
                }

                .title-v {
                    background-image: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, rgba(0, 0, 0, 0.40) 86%);
                    width: 100%;
                    height: 56px;
                    line-height: 56px;
                    position: absolute;
                    bottom: 0;


                    .title {
                        font-family: PingFangSC-Medium;
                        font-size: 24px;
                        color: #FFFFFF;
                        letter-spacing: 0;
                        text-align: center;
                        line-height: 56px;
                    }
                }

            }
        }
    }
</style>
