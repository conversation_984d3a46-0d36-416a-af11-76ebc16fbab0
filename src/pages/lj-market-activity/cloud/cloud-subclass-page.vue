<template>
    <link-page class="cloud-subclass-page">
        <link-auto-list :option="autoList">
            <view slot="top" class="type">
                <link-search-input v-model="searchVal" :placeholder="'活动名称'"/>
                <link-filter v-model="filterOption" style="float: right;padding-right: 20px;margin-bottom: 10px"/>
            </view>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="subclass-v" @tap="gotoPicItem(data)">
                    <view slot="note">
                        <view class="subclass-list">
                            <view class="list-cell">
                                <view class="media-list">
                                    <view class="media-list-logo">
                                        <image :src="$imageAssets.fileImage"></image>
                                    </view>
                                    <view class="content">
                                        <view class="content-top">
                                            <view class="title">{{data.activityName}}</view>
                                        </view>
                                        <view class="content-middle">
                                            <view class="num">{{data.attachmentQty}}张</view>
                                            <view class="date">{{data.created | date('YYYY-MM-DD')}}</view>
                                        </view>
                                    </view>
                                    <view class="list-icon-v">
                                        <link-icon icon="mp-arrow-right" style="color: #BFBFBF;font-size: 14px"/>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
    import Taro from "@tarojs/taro";
    import {ComponentUtils} from "link-taro-component";
    import {getFiltersRaw} from "link-taro-component";

    export default {
        name: "cloud-subclass-page",
        data() {
            const title = this.pageParam.title;
            const costTypeCode = this.pageParam.costTypeCode;
            const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
            this.$taro.setNavigationBarTitle({title: title});
            const autoList = new this.AutoList(this, {
                module: '',
                param: {},
                sortOptions: null,
            });
            return {
                searchVal: '',
                autoList,
                userInfo,
                subClassifyList: [],
                title,
                costTypeCode,
                zIndex: ComponentUtils.nextIndex(),
                filterOption:[
                    {label: '创建时间', field: 'created', type: 'date'},
                ],
                copyCloubParam: null,
            }
        },
        watch: {
            searchVal(newVal, oldVal) {
                if (newVal !== oldVal) {
                    const searchObj = {
                        id: "searchValue",
                        operator: "or like",
                        property: "[activityName]",
                        value: newVal
                    };
                    this.autoList.option.param['filtersRaw'] = this.autoList.option.param['filtersRaw'].filter((item) => item.property !== "[activityName]");
                    if(!this.$utils.isEmpty(newVal)){
                        this.autoList.option.param['filtersRaw'].push(searchObj);
                    }
                    this.copyCloubParam = this.$utils.deepcopy(this.autoList.option.param);
                    this.autoList.methods.reload()
                }
            },
            filterOption() {
                this.filterList(getFiltersRaw(this.filterOption))
            }
        },
        async created() {
            //财务人员：FinanceStaff；稽核人员：AuditStaff
            if (!(this.userInfo.positionType === 'FinanceStaff' || this.userInfo.positionType === 'AuditStaff')) {
                this.autoList.option.module = 'action/link/marketAct';
            } else {
                this.autoList.option.url = {
                    queryByExamplePage: 'action/link/marketAct/queryByExamplePage'
                };
            }
            this.autoList.option.param = {
                attr1: 'cloud',
                filtersRaw: [
                    {id: 'costTypeCode', property: 'costTypeCode', value: this.costTypeCode, operator: '='},
                ],
                sort: 'created',
                order: 'desc',
            };
            await this.autoList.methods.reload();
        },
        methods: {
            /**
             * 筛选函数
             * <AUTHOR>
             * @date 2020-11-19
             * @param param
             */
            async filterList(param) {
                const that = this;
                if (this.$utils.isEmpty(param)) {
                    this.copyCloubParam.filtersRaw = this.copyCloubParam.filtersRaw.filter((item) => item.property !== 'created');
                    this.autoList.option.param = this.copyCloubParam;
                    await this.autoList.methods.reload();
                } else {
                    let listIndex = that.autoList.option.param.filtersRaw.map(item => item.property);
                    param.forEach(item => {
                        if (listIndex.includes(item.property)) {
                            let index = that.autoList.option.param.filtersRaw.findIndex(val => val.property === item.property && val.operator === item.operator);
                            that.autoList.option.param.filtersRaw[index].value = item.value;
                        } else {
                            that.autoList.option.param.filtersRaw.push(item);
                        }
                    });
                    await that.autoList.methods.reload();
                }
                this.copyCloubParam = this.$utils.deepcopy(this.autoList.option.param);
            },
            gotoPicItem(item) {
                this.$nav.push('/pages/lj-market-activity/cloud/cloud-pic-item-page', {
                    data: item
                })
            },
        }
    }
</script>

<style lang="scss">
    .cloud-subclass-page {
        /*deep*/
        .link-item{
            padding: 0 !important;
        }
        .subclass-v {
            background: white;
            border-radius: 16px;
            margin: 0 24px 24px 24px;

            .subclass-list {
                width: 100%;
                height: 152px;

                .list-cell {
                    width: 100%;
                    height: 152px;
                    float: left;

                    .media-list {
                        @include flex;
                        padding: 24px 16px 24px 24px;
                        height: 100%;

                        .media-list-logo {
                            border-radius: 16px;
                            width: 100px;
                            height: 100px;
                            overflow: hidden;

                            image {
                                width: 64px;
                                height: 60px;
                                padding: 23px 20px;
                            }

                            .icon-style {
                                font-size: 28px;
                                background-image: linear-gradient(90deg, #2F69F8 0%, #76CCFF 100%);
                                -webkit-background-clip: text;
                                color: transparent
                            }
                        }

                        .content {
                            width: 80%;

                            .content-top {
                                @include flex-start-center;
                                @include space-between;
                                margin-left: 24px;

                                .title {
                                    font-family: PingFangSC-Medium;
                                    font-size: 32px;
                                    color: #262626;
                                    letter-spacing: 0;
                                    line-height: 32px;
                                    margin-top: 16px;
                                }
                            }

                            .content-middle {
                                @include flex-start-center;
                                margin-left: 24px;
                                height: 40px;
                                line-height: 40px;

                                .num {
                                    font-family: PingFangSC-Regular;
                                    font-size: 24px;
                                    color: #8C8C8C;
                                    letter-spacing: 0;
                                    text-align: left;
                                    line-height: 24px;
                                    width: 50%;
                                    float: left;
                                }

                                .date {
                                    font-family: PingFangSC-Regular;
                                    font-size: 24px;
                                    color: #8C8C8C;
                                    letter-spacing: 0;
                                    text-align: right;
                                    line-height: 24px;
                                    width: 45%;
                                    float: left;
                                }
                            }
                        }

                        .list-icon-v {
                            float: left;
                            line-height: 100px;
                        }
                    }
                }
            }
        }
    }
</style>
