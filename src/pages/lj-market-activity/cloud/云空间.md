# 云空间
------
### 初始文档
```
创建时间：2022/02/11 02:15
创建人：  宋燕荣
```
```
总体前端比较简单仅展示，核心在后端查询数据的安全性和获取第一个界面的封面图片。
```
> 包含三层界面
```
一、按照费用小类进行分组，并返回最新的附件图片或活动小类名称。
查询当前用户权限内可以查看的市场活动上传过的照片以费用小类分组，最新上传的一张照片当封面。
二、从每一个小类的分组进去查看当前小类包含的市场活动。展示市场活动名称、上传的图片张数(不区分谁上传，当前活动的所有照片)、时间。
三、从每一个市场活动行点击进去展示所有照片。展示照片和照片类型，点击可以查看大图。
```
> 云空间第一层界面数据查询的安全性
```
后端处理：
1、稽核人员安全性
2、内部人员安全性
3、组织安全性
```
> 第二层界面数据查询
```
通过第一层界面传递到第二层界面的费用小类限制条件去调用市场活动标准查询接口，查询市场活动数据。
列表展示市场活动的信息以及当前市场活动有多少图片
```

> 第三层界面数据查询
```
点击市场活动的列进入到具体图片展示界面，查询当前活动的所有图片信息并通过env.cosUploadUrl拼接腾讯云的图片key获取完整地址。
```
------ 市场活动-云空间-内容结束 ------
