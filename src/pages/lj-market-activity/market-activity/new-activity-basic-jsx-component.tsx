import {defineComponent, getCurrentInstance, reactive, set} from "link-taro-component";
import {VNode} from "vue/types/umd";
import {getNewActivityBasicLinkObjectProps} from "@/pages/lj-market-activity/market-activity/new-activity-basic-auto-list";

interface NewActivityDynamicOption {
    ctrlCode: string,                           //组件名
    //配置信息
    values: {
        field: string,                          //绑定字段
        fieldName: string,                      //字段显示值

        lovType?: string,                       //值列表类型
        view?: string,                          //日期的视图类型
        displayFormat?: string,                 //显示值格式化字符串
        valueFormat?: string,                   //实际值格式化字符串
        option?: string,                        //link-object配置对象名称
        min?: string,                           //最小值
    },
}

const newActivityDynamicComponents: {
    [k: string]: (
        option: NewActivityDynamicOption,
        formData: any,
        state: { linkObjectBinding: null | ReturnType<typeof getNewActivityBasicLinkObjectProps> }
    ) => VNode | null
} = {
    'link-input'(option, formData) {
        return (
            <link-input v-model={formData[option.values.field]}/>
        )
    },
    'link-date'(option, formData) {
        return (
            <link-date v-model={formData[option.values.field]}
                       view={option.values.view}
                       displayFormat={option.values.displayFormat}
                       valueFormat={option.values.valueFormat}/>
        )
    },
    'link-lov'(option, formData) {
        return (
            <link-lov v-model={formData[option.values.field]}
                      type={option.values.lovType}/>
        )
    },
    'link-textarea'(option, formData) {
        return (
            <link-textarea v-model={formData[option.values.field]}/>
        )
    },
    'link-address'(option, formData) {
        return (
            <link-address
                view="p"
                province={formData[option.values.field]}
                {...{on: {'update:province': val => set(formData, option.values.field, val)}}}/>
        )
    },
    'link-address-c'(option, formData) {
        return (
            <link-address
                view="pc"
                province={formData.provinceName}
                city={formData[option.values.field]}
                {...{on: {'update:city': val => set(formData, option.values.field, val)}}}/>
        )
    },
    'link-address-d'(option, formData) {
        return (
            <link-address
                view="pcd"
                province={formData.provinceName}
                city={formData.cityName}
                district={formData[option.values.field]}
                {...{on: {'update:district': val => set(formData, option.values.field, val)}}}/>
        )
    },
    'link-object'(option, formData, state) {
        return !!state.linkObjectBinding ? (
            <link-object
                pageTitle={`请选择${option.values.fieldName}`}
                value={formData[option.values.field]}
                row={formData}
                map={state.linkObjectBinding!.map}
                option={state.linkObjectBinding!.autoList}
            />
        ) : null
    },
    'view-line'(option, formData) {
        return (
            <view style={'width: 100%;height: 25px;'}/>
        )
    },
    'link-number-keyboard'(option, formData) {
        return (
            <link-input v-model={formData[option.values.field]} type="number"/>
        )
    },
    'link-number-keyboard1'(option, formData) {
        return (
            <link-number-keyboard v-model={formData[option.values.field]} min={Number(option.values.min)}/>
        )
    },
}

export default defineComponent({
    props: {
        option: {type: Object as any as new() => NewActivityDynamicOption, required: true},
        formData: {type: Object, require: true},
    },
    setup(props) {

        const ctx = getCurrentInstance()!

        const state = reactive({
            linkObjectBinding: !props.option.values.option ? null : getNewActivityBasicLinkObjectProps(ctx, props.option.values.option)
        });
        return () => newActivityDynamicComponents[props.option.ctrlCode] && newActivityDynamicComponents[props.option.ctrlCode].apply(ctx, [props.option, props.formData, state])
    },
})
