<template>
    <link-page class="invitation-info-page">
        <view class="bg-view" :style="{'background-image': 'url(' + bgImage + ')'}">
            <view class="container" style="color: rgb(255, 255, 255);">
                <view class="title">
                    <view class="small-point"></view>
                    <view class="large-point-left"></view>
                    <view class="activity-name">{{activityItem.actIndeSourCode|lov('MC_TYPE')}}邀请函</view>
                    <view class="large-point-right"></view>
                    <view class="small-point"></view>
                </view>
                <view class="divider"></view>
                <view v-for="option in invitationInfo">
                    <invitation-info-page-jsx :formData="activityItem" :option="option" @saveImg="saveImgLocal"
                                              v-if="!$utils.isEmpty(activityItem)&&!$utils.isEmpty(invitationInfo)&&(option.ctrlCode !== 'bgImage')"/>
                </view>
            </view>
        </view>
        <view v-if="painterFlag">
            <painter :palette="paletteData" @imgOK="onImgOK"></painter>
        </view>
    </link-page>
</template>

<script>
    import invitationInfoPageJsx from './invitation-info-page-jsx';

    export default {
        name: "invitation-info-page",
        components: {
            invitationInfoPageJsx
        },
        data() {
            const painterFlag = false;
            let proImageLength = 0;
            const activityItem = {
                reportDate: "",
                exeCaseCode: '',
                activityName: '',
                noPerformComments: '',
                activityType: '',
                startTime: "",
                endTime: "",
                restaurant: "",
                restaurantPhone: "",
                roomNum: "",
                tableNum: "",
                dinnersNum: "",
                wineNameCount: "",
                executivesName: "",
                executivesPhone: "",
                actExecutivesName: "",
                actExecutivesPhone: "",
                firstName: "",
                contactPhone: "",
                regionalHead: "",
                regionalHeadPhone: "",
                cashApplyAmount: "",
                prodApplyAmount: "",
                cashFillAmount: "",
                prodFillAmount: "",
                ...this.pageParam.activityItem
            };
            const invitationInfo = this.pageParam.invitationInfo || [];
            const bgImageObj = invitationInfo.filter(item => item.ctrlCode === 'bgImage' && item.img.isBackground)[0];
            const bgImage = bgImageObj['img'].attachmentPath;
            const proImageList = invitationInfo.filter(item => item.ctrlCode === 'proImage');
            const activityDetailFieldList = invitationInfo.filter(item => item.ctrlCode === 'activityDetail')[0].subControlList;
            const introductionCall = invitationInfo.filter(item => item.ctrlCode === 'introductionCall')[0].base.placeholder;
            const introduction = invitationInfo.filter(item => item.ctrlCode === 'introduction')[0].base.placeholder;
            if (!this.$utils.isEmpty(proImageList)) {
                proImageLength = proImageList.length;
            }
            return {
                painterFlag,
                introductionCall,//活动说明-称呼
                introduction,//活动说明
                activityDetailFieldList,//配置的活动信息展示字段
                proImageLength,//宣传图的数量
                activityItem,//活动对象
                bgImage,//背景图
                invitationInfo,//邀请函配置信息
                proImageList,//邀请宣传图
                paletteData: {},                                                                                    // 绘制水印对象
                painterWidth: 0,                                                                                    // 绘制图片宽度
                painterHeight: 0,                                                                                   // 绘制图片高度
                actIndeSourCodeName: '',
            }
        },
        async created() {
            this.painterWidth = this.$device.systemInfo.screenWidth;
            this.painterHeight = this.$device.systemInfo.windowHeight;
            this.actIndeSourCodeName = await this.$lov.getNameByTypeAndVal('MC_TYPE', this.activityItem.actIndeSourCode);
        },
        methods: {
            /**
             * 绘制完成
             * <AUTHOR>
             * @date 2020-09-28
             */
            async onImgOK(e) {
                this.saveShareImg(e);
                this.painterFlag = false;
            },
            /**
             * 保存分享图
             * <AUTHOR>
             * @date 2020-07-28
             */
            async saveShareImg(e) {
                this.$utils.showLoading("图片生成中...");
                const that = this;
                wx.getImageInfo({
                    src: e.detail.path,
                    success: function (ret) {
                        wx.saveImageToPhotosAlbum({
                            filePath: ret.path,
                            success: function (data) {
                                that.$utils.hideLoading();
                                that.$message.success("图片保存成功,可前往手机相册查看")
                            },
                            fail: function (err) {
                                if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
                                    that.$message.info('打开设置窗口');
                                    wx.openSetting({
                                        success(settingdata) {
                                            if (settingdata.authSetting['scope.writePhotosAlbum']) {
                                                that.$message.info('获取权限成功，请再次点击保存图片到相册。')
                                            } else {
                                                that.$message.info('获取权限失败,无法使用该功能，请授权再使用。');
                                            }
                                        }
                                    })
                                }
                            }
                        })
                    }
                });
            },
            painterPhone() {
                this.painterFlag = true;
                const viewsConfig = [
                    {
                        type: 'rect',
                        css: {
                            width: '4px',
                            height: '4px',
                            background: '#ffffff',
                            color: '#ffffff',
                            top: `${(this.painterHeight / 23) + 1}px`,
                            left: `${this.painterWidth / 4}px`,
                        }
                    },
                    {
                        type: 'rect',
                        css: {
                            width: '6px',
                            height: '6px',
                            background: '#ffffff',
                            color: '#ffffff',
                            top: `${this.painterHeight / 23}px`,
                            left: `${this.painterWidth / 7 * 2}px`,
                        }
                    },
                    {
                        type: 'text',
                        text: this.actIndeSourCodeName + '邀请函',
                        css: {
                            fontFamily: 'PingFangSC-Semibold',
                            color: '#FFFFFF',
                            top: `${this.painterHeight / 33}px`,
                            left: `${this.painterWidth / 2}px`,
                            align: 'center',
                            fontSize: '20px',
                        },
                    },
                    {
                        type: 'rect',
                        css: {
                            width: '6px',
                            height: '6px',
                            background: '#ffffff',
                            color: '#ffffff',
                            top: `${this.painterHeight / 23}px`,
                            right: `${this.painterWidth / 7 * 2}px`,
                        }
                    },
                    {
                        type: 'rect',
                        css: {
                            width: '4px',
                            height: '4px',
                            background: '#ffffff',
                            color: '#ffffff',
                            top: `${(this.painterHeight / 23) + 1}px`,
                            right: `${this.painterWidth / 4}px`,
                        }
                    },
                    {
                        type: 'rect',
                        css: {
                            width: this.painterWidth + 'px',
                            height: '2px',
                            color: 'rgba(255,255,255,0.5)',
                            top: `${this.painterHeight / 13}px`,
                        }
                    },
                    {
                        type: 'rect',
                        css: {
                            width: this.painterWidth + 'px',
                            height: '1px',
                            color: 'rgba(255,255,255,0.3)',
                            top: `${this.painterHeight / 12}px`,
                        }
                    },
                ];
                let dynamicHeight = this.painterHeight / 10;
                for (let i = 0; i < this.invitationInfo.length; i++) {
                    const ctrlCode = this.invitationInfo[i].ctrlCode;
                    const subtitleCont = this.invitationInfo[i].values.demo;
                    const attachmentPathCont = this.invitationInfo[i].img.attachmentPath;
                    switch (ctrlCode) {
                        case 'introductionCall'://称呼
                            const introductionCallObj = {
                                type: 'text',
                                text: this.introductionCall,
                                css: {
                                    fontFamily: 'PingFangSC-Regular',
                                    color: '#FFFFFF',
                                    width: `${this.painterWidth / 1.1}px`,
                                    top: `${dynamicHeight}px`,
                                    left: `${this.painterWidth / 16}px`,
                                    fontSize: '14px',
                                    whiteSpace: 'pre-line',
                                    letterSpacing: '0',
                                    lineHeight: '22px',
                                },
                            };
                            viewsConfig.push(introductionCallObj);
                            dynamicHeight = `${Number(dynamicHeight) + 28}`;
                            break;
                        case 'introduction'    ://活动介绍说明
                            const introductionObj = {
                                type: 'text',
                                text: this.introduction,
                                css: {
                                    fontFamily: 'PingFangSC-Regular',
                                    color: '#FFFFFF',
                                    width: `${this.painterWidth / 1.1}px`,
                                    top: `${dynamicHeight}px`,
                                    left: `${this.painterWidth / 16}px`,
                                    fontSize: '14px',
                                    whiteSpace: 'pre-line',
                                    letterSpacing: '0',
                                    lineHeight: '22px',
                                },
                            };
                            viewsConfig.push(introductionObj);
                            dynamicHeight = `${Number(dynamicHeight) + 140}`;
                            break;
                        case 'activityDetail'://活动信息
                            //活动信息背景
                            const activityBg = {
                                type: 'rect',
                                css: {
                                    width: `${this.painterWidth / 1.1}px`,
                                    height: '280px',
                                    background: 'rgba(255,255,255,0.12)',
                                    top: `${dynamicHeight}px`,
                                    borderRadius: '12px',
                                    color: 'rgba(255,255,255,0.12)',
                                    left: `${this.painterWidth / 19}px`,
                                }
                            };
                            viewsConfig.push(activityBg);
                            dynamicHeight = `${Number(dynamicHeight) + 10}`;
                            let activityFieldIndex = 0;
                            for (let i = 0; i < this.activityDetailFieldList.length; i++) {
                                const item = this.activityDetailFieldList[i];
                                const activityField = `${item.ctrlName}：${this.activityItem[item.ctrlCode]}`;
                                viewsConfig.push({
                                    type: 'text',
                                    text: activityField,
                                    css: {
                                        fontFamily: 'PingFangSC-Regular',
                                        color: '#FFFFFF',
                                        width: `${this.painterWidth / 1.1}px`,
                                        top: `${Number(dynamicHeight) + Number(activityFieldIndex)}px`,
                                        fontSize: '14px',
                                        whiteSpace: 'pre-line',
                                        letterSpacing: '0',
                                        left: `${(this.painterWidth / 10)}px`,
                                        lineHeight: '16px',
                                    },
                                });
                                activityFieldIndex += 30;
                            }
                            dynamicHeight = `${Number(this.painterHeight / 2.7) + Number(activityFieldIndex) + 25}`;
                            break;
                        case 'subTitle':
                            const subTitle = {
                                type: 'text',
                                text: subtitleCont,
                                css: {
                                    fontFamily: 'PingFangSC-Semibold',
                                    color: '#FFFFFF',
                                    width: `${this.painterWidth / 1.1}px`,
                                    top: `${dynamicHeight}px`,
                                    fontSize: '16px',
                                    whiteSpace: 'pre-line',
                                    letterSpacing: '0',
                                    left: `${(this.painterWidth / 8)}px`,
                                    lineHeight: '16px',
                                },
                            };
                            viewsConfig.push(subTitle);
                            dynamicHeight = `${Number(dynamicHeight) + 45}`;
                            break;
                        case 'proImage':
                            let proImageIndex = 0;
                            const attachmentPath = {
                                type: 'image',
                                url: attachmentPathCont,
                                css: {
                                    top: `${dynamicHeight}px`,
                                    left: `${(this.painterWidth - this.painterWidth / 1.1) / 2}px`,
                                    width: `${this.painterWidth / 1.1}px`,
                                    height: `${this.painterHeight / 2.5}px`,
                                },
                            };
                            viewsConfig.push(attachmentPath);
                            dynamicHeight = `${Number(dynamicHeight) + Number(this.painterWidth / 1.5)}`;
                            break;
                        default:
                            break;
                    }
                }
                this.paletteData = {
                    width: `${this.painterWidth}px`,
                    height: `${dynamicHeight}px`,//`${this.painterHeight * 1.5 * this.proImageLength}px`,
                    background: this.bgImage,
                    views: viewsConfig,
                }
            },
            //生成图片
            saveImgLocal() {
                this.painterPhone();
            }
        }
    }
</script>

<style lang="scss">
    .invitation-info-page {
        .bg-view {
            background-size: 100% 100%;
            height: 100%;
            width: 100%;
            max-width: 1280px;
            min-width: 640px;
            margin: 0 auto;

            .container {
                height: 100%;
                overflow: auto;

                .title {
                    padding: 32px;
                    text-align: center;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .small-point {
                        width: 8px;
                        height: 8px;
                        background: #fff;
                    }

                    .large-point-left {
                        width: 12px;
                        height: 12px;
                        background: #fff;
                        margin: 0 24px 0 8px;
                    }

                    .large-point-right {
                        width: 12px;
                        height: 12px;
                        background: #fff;
                        margin: 0 8px 0 24px;
                    }

                    .activity-name {
                        font-family: PingFangSC-Semibold;
                        font-size: 40px;
                        color: #FFFFFF;
                        letter-spacing: 0;
                        text-align: center;
                        line-height: 40px;
                    }
                }

                .divider {
                    height: 8px;
                    box-sizing: content-box;
                    border-top: 4px solid rgba(255, 255, 255, 0.50);
                    border-bottom: 2px solid rgba(255, 255, 255, 0.30);
                }

                .activity-introduction-call {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    padding: 30px 48px 0px 48px;
                    white-space: pre-line;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    line-height: 44px;
                }

                .activity-introduction {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    padding: 0px 48px 40px 48px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    line-height: 44px;
                    white-space: pre-line;
                }

                .activity-detail-content {
                    margin: 0 24px;
                    padding: 24px 40px;
                    background: rgba(255, 255, 255, 0.12);
                    border-radius: 24px;

                    .activity-detail-item {
                        margin-bottom: 32px;
                        line-height: 28px;
                        font-size: 28px;
                    }
                }

                .activity-sub-title {
                    font-family: PingFangSC-Semibold;
                    font-size: 32px;
                    line-height: 32px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    margin: 40px auto;
                    text-align: center;
                }

                .activity-pro-img {
                    text-align: center;
                    width: 100%;
                    margin: 40px 0;
                }
            }
        }
    }
</style>
