<template>
    <link-page class="sweep-repetition-code-page">
        <view class="sweep-repetition-code-img">
            <image :src="$imageAssets.sweepRepetitionCode"></image>
        </view>
        <view class="sweep-repetition-code-title">
            <view>重复扫码!</view>
        </view>
        <view class="sweep-repetition-code-msg">
            <view class="msg">{{msg}}</view>
        </view>
        <view class="sweep-repetition-code-end">
        </view>
    </link-page>
</template>

<script>
    export default {
        name: "sweep-repetition-code-page",
        data() {
            const data = this.pageParam.data;//重复扫码的返回信息
            const msg = this.pageParam.msg;//重复扫码的返回提示信息
            const type = this.pageParam.type;//扫码类型，outbound : 出库
            const activityItem = this.pageParam.activityItem;//活动对象
            return {
                msg,
                data,
                type,
                insertSweepRepetitionData: {},//新建插入的扫码记录对象
                activityItem,
            }
        },
        async onShow() {
            if (this.$utils.isNotEmpty(this.type) && (this.type === 'outbound'|| this.type === 'zhuanzeng' || this.type === 'kaiping')) {
                await this.insertSweepRepetitionCode();
            }
        },
        methods: {
            /**
             * 处理扫码记录对象字段
             * @auther songyanrong
             * @date 2021-10-27
             * */
            async dealSweepRepetitionData() {
                let scanRecordStatus = "";
                let scanType = "";
                let scanSubType = "";
                if (this.type === 'outbound') {
                    scanRecordStatus = 'Repeatdelivery';//重复出库 扫码状态 SCAN_RECORD_STATUS
                    scanType = 'ActProdScan';//活动物资扫码 扫码类型 SCAN_TYPE
                    scanSubType = 'OutScan';//出库扫码 扫码子类型 SCAN_SUB_TYPE
                }
                if(this.type === 'zhuanzeng'){
                    scanRecordStatus = 'Repeatgifted';//重复赠送 扫码状态 值列表类型: SCAN_RECORD_STATUS
                    scanType = 'ActProdScan';//活动物资扫码 扫码类型 值列表类型:SCAN_TYPE
                    scanSubType = 'GiftScan';//转赠扫码 扫码子类型 值列表类型: SCAN_SUB_TYPE
                }
                if(this.type === 'kaiping'){
                    scanRecordStatus = 'Repeatbottled';//重复开瓶 扫码状态 值列表类型: SCAN_RECORD_STATUS
                    scanType = 'ActProdScan';//活动物资扫码 扫码类型 值列表类型:SCAN_TYPE
                    scanSubType = 'OpenScan';//开瓶扫码 扫码子类型 值列表类型: SCAN_SUB_TYPE
                }
                if(this.pageParam.addressFlag){
                    this.insertSweepRepetitionData = {
                        prodId: this.data['prodId'],//产品id
                        prodQrCodeId: this.data['id'],//产品码id
                        prodQrCode: this.type === 'kaiping' ? this.data['qrCodeIn'] : this.data['qrCodeOut'],//产品码
                        prodQrCodeType:  this.type === 'kaiping' ? 1 : 2,//产品码类型按照盖外码赋值为2
                        materialLineId: this.data['materialLineId'],//物资行id
                        descriptionType: 'MatchSuccessfully',//匹配状态:匹配成功 MATCH_STATUS
                        isEffective: 'N',//是否有效字段
                        packagingMaterial: this.data['packagingMaterial'],//是否包材
                        scanRecordStatus: scanRecordStatus,
                        scanType: scanType,
                        scanSubType: scanSubType,
                        province: this.pageParam.addressData.province,
                        city:this.pageParam.addressData.city,
                        district:this.pageParam.addressData.district,
                        //districtCode:this.pageParam.addressData.adcode,
                        scanAddr:this.pageParam.addressDataFull,
                        actId: this.activityItem.id,
                        scanSource: 'StaffSystem'
                    }
                }
                else{
                    this.insertSweepRepetitionData = {
                        prodId: this.data['prodId'],//产品id
                        prodQrCodeId: this.data['id'],//产品码id
                        prodQrCode: this.type === 'kaiping' ? this.data['qrCodeIn'] : this.data['qrCodeOut'],//产品码
                        prodQrCodeType:  this.type === 'kaiping' ? 1 : 2,//产品码类型按照盖外码赋值为2
                        materialLineId: this.data['materialLineId'],//物资行id
                        descriptionType: 'MatchSuccessfully',//匹配状态:匹配成功 MATCH_STATUS
                        isEffective: 'N',//是否有效字段
                        packagingMaterial: this.data['packagingMaterial'],//是否包材
                        scanRecordStatus: scanRecordStatus,
                        scanType: scanType,
                        scanSubType: scanSubType,
                        actId: this.activityItem.id,
                        scanSource: 'StaffSystem'
                    }
                }
                // await Promise.all(
                //     this.insertSweepRepetitionData = {
                //         prodId: this.data['prodId'],//产品id
                //         prodQrCodeId: this.data['id'],//产品码id
                //         prodQrCode: this.data['qrCodeOut'],//产品码
                //         prodQrCodeType: 2,//产品码类型按照盖外码赋值为2
                //         materialLineId: this.data['materialLineId'],//物资行id
                //         descriptionType: 'MatchSuccessfully',//匹配状态:匹配成功 MATCH_STATUS
                //         isEffective: 'N',//是否有效字段
                //         packagingMaterial: this.data['packagingMaterial'],//是否包材
                //         scanRecordStatus: scanRecordStatus,
                //         scanType: scanType,
                //         scanSubType: scanType,
                //     }
                // );
            },
            /**
             * 生成重复扫码记录
             * @auther songyanrong
             * @date 2021-10-27
             * */
            async insertSweepRepetitionCode() {
                await this.dealSweepRepetitionData();
                await this.$http.post('action/link/actScanRecord/insert', this.insertSweepRepetitionData);
                this.$message.success('重复扫码数据创建成功');
                //重新查询扫码数据
                this.$bus.$emit("initCodeScanRecordList");
            }
        }
    }
</script>

<style lang="scss">
    .sweep-repetition-code-page {
        background: white;

        .sweep-repetition-code-img {
            width: 100%;

            image {
                width: 100%;
            }
        }

        .sweep-repetition-code-title {
            font-family: PingFangSC-Medium;
            font-size: 36px;
            color: #262626;
            letter-spacing: 0;
            text-align: center;
            line-height: 36px;
        }

        .sweep-repetition-code-msg {
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #262626;
            letter-spacing: 0;
            text-align: center;
            line-height: 100px;
        }

        .sweep-repetition-code-end {
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #262626;
            letter-spacing: 0;
            text-align: center;
        }
    }
</style>
