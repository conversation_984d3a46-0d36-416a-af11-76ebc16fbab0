# 市场活动Q&A

* 常见的疑惑点解释

```
* Q&A：
Q：为什么执行案模块模块新建活动和费用不和市场活动新建活动和费用用同一套界面？
A：新建活动和费用数据的顺序都是：先新建市场活动，接下来再保存费用数据。市场活动的基础信息界面和费用信息界面加了*暂存功能*，
由于
1、市场活动新建之后可以下一步逐步走到最后一个流程页面。
2、费用信息作为市场活动对象的子对象。
3、市场活动新建是先维护活动信息保存之后再维护费用信息。
4、执行案新建市场活动是先前端维护好费用信息之后传到活动信息录入界面。
5、更新缓存和对比缓存与当前界面信息的差异。鉴于以上情况所以缓存市场活动的信息做整体缓存，只缓存一个大的数据对象（意思就是活动对象里放了费用的子对象）
tips:暂存功能：信息录入过程中被其他事情打断（接打电话，跳出应用去其他应用等等）会缓存当前录入的信息，当用户回到应用首页会弹窗提醒用户有未保存的数据是否继续操作，选择是就跳转到缓存页面继续录入，选择否就清除缓存。
```
