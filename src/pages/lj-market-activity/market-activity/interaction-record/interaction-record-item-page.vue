<template>
    <link-page class="interaction-record-item-page">
        <link-form>
            <link-form-item label="姓名">
                {{interactionRecordItem.appAcctName}}
            </link-form-item>
            <link-form-item label="昵称">
                {{interactionRecordItem.nickName}}
            </link-form-item>
            <link-form-item label="手机号码">
                {{interactionRecordItem.noEncrypedTel}}
            </link-form-item>
            <link-form-item :label="interactionRecordItem.interactionFormat === 'Invitation' ? '报名时间' : '签到时间'">
                {{interactionRecordItem.created | date('YYYY-MM-DD')}}
            </link-form-item>
            <view v-for="(item,index) in fieldsList" :key="index">
                <link-form-item :label="item.fieldName" v-if="item.fieldType === 'ListOfValues'">
                    {{interactionRecordItem[item.codeName]|lov(item.lovType)}}
                </link-form-item>
                <link-form-item :label="item.fieldName" v-else-if="item.fieldType === 'Date'">
                    {{ interactionRecordItem[item.codeName]|date('YYYY-MM-DD')}}
                </link-form-item>
                <link-form-item :label="item.fieldName" v-else>
                    {{interactionRecordItem[item.codeName]}}
                </link-form-item>
            </view>
        </link-form>
    </link-page>
</template>

<script>
export default {
    name: "interaction-record-item-page",
    data () {
        const interactionRecordItem = {
            ...this.pageParam.data
        };
        if (interactionRecordItem.interactionFormat === 'Invitation') {
            this.$taro.setNavigationBarTitle({title: '邀请用户详情'});
        }
        return {
            interactionRecordItem,
            fieldsList: []
        }
    },
    created () {
        this.queryFieldsInfo()
    },
    methods: {
        /**
         * @desc 查询配置字段
         * <AUTHOR>
         * @date 2021/5/11 15:50
         **/
        async queryFieldsInfo () {
            const data = await this.$http.post(this.$env.appURL + '/interaction/link/registInfo/queryByExamplePage', {
                pageFlag: false,
                sort: 'fieldSeq',
                order: 'asc',
                interactionId: this.interactionRecordItem.interactionId
            })
            if (data.success) {
                if (data.rows.length > 0) {
                    this.fieldsList = data.rows.filter((item)=> item.codeName !== 'phone' && item.codeName !=='acctName')
                    this.fieldsList.forEach((item)=> {
                        if (item.extraJson) {
                            item.codeName = item.extraJson
                        }
                    })
                }
            } else {
                this.$showError('查询配置字段信息失败' + data.result)
            }
        }
    }
}
</script>

<style lang="scss">
.interaction-record-item-page{

}
</style>
