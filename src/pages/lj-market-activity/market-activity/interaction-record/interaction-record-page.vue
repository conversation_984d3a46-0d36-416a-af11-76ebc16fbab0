<template>
    <link-page class="sign-in-record-page">
        <link-auto-list :option="questionRecordList" hideCreateButton v-if="evetype === 'Questionnaire'">
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" class="sign-in-list-item">
                    <view class="sign-in-list" slot="note">
                        <view class="list-cell">
                            <view class="media-list">
                                <image class="media-list-logo" :src="data.wxHeadimgurl"/>
                                <view class="media-list-body">
                                    <view class="list-item">
                                        <view class="left">{{data.appNickName}}</view>
                                        <view class="right">{{data.phone}}</view>
                                    </view>
                                    <view class="list-item">
                                        <view class="left">{{data.submitState | lov('SURVEY_SUBMIT_STATE')}}</view>
                                        <view class="right" v-if="data.surveySubTime">{{data.surveySubTime | date('YYYY-MM-DD')}}</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
        <link-auto-list :option="interactionRecordList" hideCreateButton v-else>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" class="sign-in-list-item">
                    <view class="sign-in-list" slot="note">
                        <view class="list-cell">
                            <view class="media-list">
                                <image class="media-list-logo" :src="data.avatarUrl"/>
                                <view class="media-list-body">
                                    <view class="list-item">
                                        <view class="left">{{data.appAcctName}}</view>
                                        <view class="right">{{data.noEncrypedTel}}</view>
                                    </view>
                                    <view class="list-item">
                                        <view class="left">{{data.nickName}}</view>
                                        <view class="right">{{data.created | date('YYYY-MM-DD')}}</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
export default {
    name: "interaction-record-page",
    data () {
        const interactionId = this.pageParam.data.id;
        const surveyId = this.pageParam.data.surveyId;
        const evetype = this.pageParam.data.evetype;
        if (evetype === 'Invitation') {
            this.$taro.setNavigationBarTitle({title: '邀请记录'});
        } else if (evetype === 'Questionnaire') {
            this.$taro.setNavigationBarTitle({title: '问卷明细'});
        }
        const interactionRecordList = new this.AutoList(this, {
            itemPath: '/pages/lj-market-activity/market-activity/interaction-record/interaction-record-item-page',
            url: {queryByExamplePage: this.$env.appURL + '/interaction/link/actionInfo/queryByWechatPage'},
            param: {
                filtersRaw: [
                    {id: 'interactionId', property: 'interactionId', value: interactionId, operator: '='}
                ]
            },
            sortOptions: null
        });
        const questionRecordList  = new this.AutoList(this, {
            url: {queryByExamplePage: this.$env.appURL + '/interaction/link/activityCrowd/querySurveyByExamplePage'},
            param: {
                filtersRaw: [
                    {id: 'surveyId', property: 'surveyId', value: surveyId, operator: '='}
                ]
            },
            sortOptions: null
        });
        return {
            surveyId,
            questionRecordList,
            interactionRecordList,
            evetype
        }
    }
}
</script>

<style lang="scss">
.sign-in-record-page {
    padding-top: 10px;
    .sign-in-list-item{
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
    }
    .sign-in-list {
        background-color: #FFFFFF;
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: column;

        .list-cell {
            position: relative;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;

            .media-list {
                padding: 11px 15px;
                box-sizing: border-box;
                display: flex;
                width: 100%;
                flex-direction: row;
                justify-content: space-between;

                .media-list-logo {
                    height: 120px;
                    width: 120px;
                    margin-right: 20px;
                    border-radius: 50%;

                    image {
                        height: 100%;
                        width: 100%;
                    }
                }

                .media-list-body {
                    display: flex;
                    flex: 1;
                    flex-direction: column;
                    justify-content: space-between;
                    align-items: flex-start;
                    overflow: hidden;
                    .list-item{
                        display: flex;
                        justify-content: space-between;
                        width: 100%;
                        height: 60px;
                        line-height: 60px;
                        .left{
                            width: 55%;
                            overflow: hidden;
                            text-overflow:ellipsis;
                            white-space: nowrap;
                            margin-right: 5%;
                        }
                        .right{
                            width: 40%;
                        }
                    }
                }
            }
        }
    }
}
</style>
