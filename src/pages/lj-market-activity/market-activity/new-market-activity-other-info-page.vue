<template>
    <link-page class="new-market-activity-other-info-page">
        <ma-navigation-bar :backVisible="true"
                           :zIndex="zIndex"
                           :backgroundImg="$imageAssets.homeMenuBgImage"
                           :title="navigationBarTitle"
                           :titleColor="navigationBarTitleColor"
                           :navBackgroundColor="navBackgroundColor"
                           :udf="udfBack">
        </ma-navigation-bar>
        <lnk-no-auth v-if="authFlag"></lnk-no-auth>
        <view v-else>
            <!--消费者-->
            <consumers :parentId="activityItem.id" :haveMarginFlag="false" :operationFlag="true"
                       v-if="consumersFlag" :pageSource="pageSource" :parentData="activityItem"></consumers>
            <!--内部人员-->
            <insiders :parentId="activityItem.id" :haveMarginFlag="false" :parentData="activityItem" :operationFlag="true"
                      v-if="insidersFlag" :pageSource="pageSource"></insiders>
            <!--参与终端/经销商-->
            <terminal :parentData="activityItem" :parentId="activityItem.id" :haveMarginFlag="false" v-if="terminalFlag"
                      :pageSource="pageSource" :operationFlag="true"></terminal>
            <!--活动动销 需传是否有边框 title 展示数量字段 实际 actualNumFlag or预计 estimatedNumFlag or 申请applyNumFlag -->
            <production-pin :parentData="activityItem" :parentType="'act'"
                            :haveMarginFlag="false" :title="'活动动销'" :pageSource="pageSource"
                            :estimatedNumFlag="true" v-if="productionPinFlag"></production-pin>
        </view>
        <link-sticky>
            <link-button mode="stroke" block @tap="lastStep">上一步</link-button>
            <link-button block @tap="preview">预览</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    import Consumers from "./components/consumers";
    import ProductionPin from "./components/production-pin";
    import Insiders from "./components/insiders";
    import InteractiveConfig from "./components/interactive-config";
    import Terminal from "./components/terminal";
    import {env} from "../../../../env";
    import LnkNoAuth from "../../core/lnk-no-auth/lnk-no-auth";
    import {ComponentUtils} from "link-taro-component";
    import MaNavigationBar from "../ma-navigation-bar/ma-navigation-bar";
    import Taro from "@tarojs/taro";

    export default {
        name: "new-market-activity-other-info-page",
        components: {
            MaNavigationBar,
            LnkNoAuth,
            Consumers,
            Insiders,
            InteractiveConfig,
            ProductionPin,
            Terminal
        },
        data() {
            const activityItem = ({...this.pageParam.data});
            const productionPinList = [];//预生产动销列表
            const pageSource = 'other';
            let submitFlag = false;
            //活动状态为‘新建‘，审批状态为‘未提交’’已拒绝‘,活动提交按钮可点
            if (activityItem.status === 'New' && (activityItem.aproStatus === 'New' || activityItem.aproStatus === 'Refused')) {
                submitFlag = true;
            }
            //场景来源服务于自定义导航栏。
            //1、市场活动新建 newMarketActivity 2、执行案新建市场活动 caseNewMarketActivity 3、其他 other
            let sceneSourceForNavigation = "other";//默认other
            if(!this.$utils.isEmpty(this.pageParam.sceneSourceForNavigation)){
                sceneSourceForNavigation = this.pageParam.sceneSourceForNavigation;
            }
            let consumersRequire = false;//根据类型配置消费者子对象是否必输 默认不必输
            return {
                navigationBarTitle: '其他信息',
                navigationBarTitleColor: '#ffffff',
                navBackgroundColor: 'transparent',
                zIndex: ComponentUtils.nextIndex(),
                sceneSourceForNavigation,
                submitFlag,
                pageSource,
                activityItem,
                productionPinList,
                authFlag: false,
                consumersFlag: false,//消费者是否展示
                insidersFlag: false,//内部人员是否展示
                terminalFlag: false,//参与终端/经销商是否展示
                productionPinFlag: false,//活动动销是否展示
                consumersRequire,
            }
        },
        async created() {
            await this.$utils.delay(1000);
            await this.queryActicityTypeModules();
        },
        methods: {
            /**
             * 自定义返回函数
             * @songyanrong
             * @date 2020-12-02
             * */
            udfBack() {
                if (this.sceneSourceForNavigation === 'other') {
                    this.$nav.back();
                } else if (this.sceneSourceForNavigation === 'newMarketActivity') {
                    let pages = Taro.getCurrentPages();    //获取当前页面信息栈
                    let targetIndex = pages.findIndex(function (item) {
                        return item.route === "pages/lj-market-activity/market-activity/market-activity-list-page";
                    });
                    if (targetIndex === -1) {
                        return this.$nav.backAll()
                    }
                    const num = Number(pages.length - (Number(targetIndex) + 1));
                    setTimeout(() => {
                        this.$bus.$emit('marketActivityListRefresh');
                        this.$nav.back(null, num);
                    }, 1000)
                } else if (this.sceneSourceForNavigation === 'caseNewMarketActivity') {
                    let pages = Taro.getCurrentPages();    //获取当前页面信息栈
                    let targetIndex = pages.findIndex(function (item) {
                        return item.route === "pages/lj-market-activity/perform-case/perform-case-item-page";
                    });
                    if (targetIndex === -1) {
                        return this.$nav.backAll()
                    }
                    const num = Number(pages.length - (Number(targetIndex) + 1));
                    setTimeout(() => {
                        this.$bus.$emit('initActivityList');
                        this.$nav.back(null, num);
                    }, 1000)
                }
            },
            /**
             * 查询当前费用类型需要展示的组件模块数据
             * */
            async queryActicityTypeModules() {
                if (this.$utils.isEmpty(this.activityItem.costTypeCode)) {
                    this.$showError("费用类型信息不完整，请更新活动数据或重新查询",{customFlag:true});
                    return;
                }
                let data;
                if(this.$utils.isNotEmpty(this.activityItem.actIndeSourCode)) {
                    data = await this.$utils.getQwMpTemplate('newActivity', '', this.activityItem.costTypeCode, this.activityItem.actIndeSourCode);
                } else {
                    data = await this.$utils.getQwMpTemplate('newActivity', '', this.activityItem.costTypeCode);
                }
                if (!data.success) {
                    this.authFlag = true;
                    return;
                }
                this.authFlag = false;
                let resultOpt = JSON.parse(data.result);
                this.fieldRows = JSON.parse(resultOpt.conf);
                for (let i = 0; i < this.fieldRows.length; i++) {
                    //活动详情-基础信息-展示
                    if (this.fieldRows[i].ctrlCode === 'viewActivityBasic') {
                        this.subControlList = this.fieldRows[i].subControlList;
                    }
                    //活动详情-基础信息-编辑
                    if (this.fieldRows[i].ctrlCode === 'editActivityBasic') {
                        this.editSubControlList = this.fieldRows[i].subControlList;
                    }
                    const comExit = this.fieldRows[i].props.filter((item1) => item1.key === 'clickFun');
                    //消费者组件是否有配置
                    const consumersExit = this.fieldRows[i].props.filter((item1) => item1.val === 'addConsumers');
                    if(this.$utils.isNotEmpty(consumersExit)){
                        this.consumersRequire = this.fieldRows[i].base.require;
                    }
                    if(!this.$utils.isEmpty(comExit)){
                        switch (comExit[0].val) {
                            case 'addConsumers': // 消费者
                                this.consumersFlag = true;
                                break;
                            case 'addInsiders': // 内部人员
                                this.insidersFlag = true;
                                break;
                            case 'addTerminal': // 参与终端/经销商
                                this.terminalFlag = true;
                                break;
                            case 'addProductionPin' : //活动动销
                                this.productionPinFlag = true;
                                break;
                            default:
                                break;
                        }
                    }
                }
            },
            /**
             * 活动提交
             * <AUTHOR>
             * @date 2020-08-10
             * */
            async submit() {
                const data = await this.$http.post('action/link/marketAct/commit', {
                    id: this.activityItem.id
                });
                if (data.success) {
                    this.$message.success("活动提交成功。",{customFlag:true})
                }
                this.activityItem = data.rows;
                if (this.activityItem.status === 'New' && (this.activityItem.aproStatus === 'New' || this.activityItem.aproStatus === 'Refused')) {
                    this.submitFlag = true;
                } else {
                    this.submitFlag = false;
                }
            },
            /**
             * 添加预生产动销
             * <AUTHOR>
             * @date 2020-08-07
             * */
            addPrePio() {

            },
            /**
             * 上一步
             * <AUTHOR>
             * @date 2020-08-06
             * */
            lastStep() {
                this.$bus.$emit("marketActivity");
                this.$nav.back();
            },
            /**
             * 预览
             * <AUTHOR>
             * @date 2020-08-10
             * */
            async preview() {
                if(this.consumersRequire){
                    const consumerDataFlag = await this.queryActivityConsumer();
                    if (!consumerDataFlag) {
                        this.$message.warn("请维护消费者数据！",{customFlag:true});
                        return false;
                    }
                }
                this.$nav.push('/pages/lj-market-activity/market-activity/market-activity-item-page', {
                    pageSource: 'preview',
                    data: this.activityItem,
                    sceneSourceForNavigation: this.sceneSourceForNavigation,
                })
            },
            /**
             * 查询活动下消费者信息
             * */
            async queryActivityConsumer() {
                const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/interCust/queryByAppCustPage', {
                    order: 'desc',
                    sort: 'matchStatus',
                    mcActId: this.activityItem.id,
                    rows: 3,
                    totalFlag: true,
                    filtersRaw: [
                        {id: 'empFlag', property: 'empFlag', value: 'N', operator: '='},
                        {id: 'terminalFlag', property: 'terminalFlag', value: 'N', operator: '='}
                    ]
                });
                if (this.$utils.isEmpty(data.rows)) {
                    return false;
                } else {
                    return true;
                }
            },
        }
    }
</script>

<style lang="scss">
    .new-market-activity-other-info-page {
        .btm-v {
            width: 100%;
            height: 112px;
            margin-bottom: 32px;

            .btm-left {
                width: 15%;
                height: 96px;
                float: left;
                border: 2px solid #2F69F8;
                border-radius: 8px;
                line-height: 96px;
                text-align: center;
                color: #2F69F8;
                margin: 16px 0 0 24px;
            }

            .btm-center {
                width: 35%;
                height: 96px;
                float: left;
                margin: 16px 0 0 24px;
                border: 2px solid #2F69F8;
                border-radius: 8px;
                line-height: 96px;

                .center-title {
                    font-family: PingFangSC-Regular;
                    font-size: 32px;
                    color: #2F69F8;
                    letter-spacing: 0;
                    text-align: center;
                    line-height: 96px;
                }
            }

            .btm-right {
                width: 35%;
                height: 96px;
                float: left;
                line-height: 96px;
                margin: 16px 0 0 24px;
                background: #2F69F8;
                box-shadow: 0 8px 24px 0 rgba(47, 105, 248, 0.50);
                border-radius: 8px;

                .right-title {
                    font-family: PingFangSC-Regular;
                    font-size: 32px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    text-align: center;
                    line-height: 96px;
                }
            }
        }
    }
    //自定义tabbar导致弹窗展示过大
    .link-dialog-content{
        height: 80vh !important;
    }
</style>
