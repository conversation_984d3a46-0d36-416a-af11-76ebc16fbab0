# 市场活动状态流转
```
创建时间：2022/01/10 20:15
创建人：  宋燕荣
模块路径：src/pages/lj-market-activity/market-activity
```

* 关键字段
```
1、活动状态 别名：status 表字段：STATUS 值列表：MC_STATUS
2、审批状态 别名：aproStatus 表字段：APRO_STATUS 值列表：APRO_STATUS
```
* 活动状态，活动审批状态变更的触发操作及赋值逻辑
```
 1）新建活动，未提交审批：活动状态=新建，活动审批状态=未提交
 2）新建活动，提交审批：活动状态=新建，活动审批状态=提报待审批
 3）新建活动，提交审批，审批拒绝：活动状态=新建，活动审批状态=已拒绝
 4）新建活动，提交审批，审批通过，当前时间距离活动开始时间超过3小时：活动状态=已发布，活动审批状态=申请审批通过；
 5）新建活动，提交审批，审批通过，当前时间距离活动开始时间小于3小时：活动状态=进行中，活动审批状态=申请审批通过；
 6）针对活动状态=已发布，活动审批状态=申请审批通过的活动，每1小时定时工作流，判断到当前时间距离活动开始时间小于3小时，更新活动状态=进行中，活动审批状态=申请审批通过；
 7）针对活动状态=进行中的活动，每晚凌晨1点定时工作流，判断当前时间超过活动结束时间，更新活动状态=执行结束；
 8）针对活动状态=进行中/执行结束，活动审批状态=申请审批通过/反馈驳回/反馈撤回，提交执行反馈：活动状态=进行中/执行结束，活动审批状态=反馈待审批；
 9）针对活动状态=进行中/执行结束，活动审批状态=申请审批通过/反馈驳回/反馈撤回，提交执行反馈，审批通过：活动状态=已实发，活动审批状态=反馈审批通过；
 10）针对活动状态=进行中/执行结束，活动审批状态=申请审批通过/反馈驳回/反馈撤回，提交执行反馈，审批拒绝：活动状态=进行中/执行结束，活动审批状态=反馈驳回；
 11）针对活动状态=进行中/执行结束，活动审批状态=申请审批通过/反馈驳回/反馈撤回，提交执行反馈，提交人撤回审批流：活动状态=进行中/执行结束，活动审批状态=反馈撤回；
 12）针对活动状态=新建/已发布/进行中/执行结束，活动审批状态=未提交/提报待审批/已拒绝/申请审批通过/反馈待审批/反馈驳回/反馈撤回的活动，作废活动，活动状态=已作废；
```
------ 市场活动-状态流转-内容结束 ------
