# 市场活动-安全性
```
创建时间：2022/02/09 02:15
创建人：  宋燕荣
```
### 市场活动列表查询安全性
**优先原则：优先访问组安全性，没配置访问组安全性时走职位或者组织安全性**
#### 访问组安全性
配置方式：PC端 【系统管理 / APP菜单】菜单
搜索菜单名称为：市场活动，类型：二级菜单。
下挂的安全性菜单 安全性类型为：访问组安全性。访问组类型：区域/组织。配置：访问组JSON配置
参数应用方式：如果配置了访问组安全性，前端查询时传oauth为：accessGroupOauth对象，后端根据accessGroupOauth的值去匹配配置拿到访问组的JSON配置信息拼接到sql里。
#### 职位或组织安全性
```
1、稽核人员：AuditStaff
    配置方式：PC端 【系统管理 / 参数配置】菜单。
    参数名称：稽核人员职位类型
    参数键：jiHeSecurityRel
    参数值：{"jiHe":["AuditStaff","FinanceStaff","InspectionStaff"]}
    参数说明：根据数组中的值，判断当前登录用户是否为稽核人员。
    参数应用方式：后端beforQueryExample方法里根据当前用户的职位类型判断，如果当前用户的职位类型属于参数值当中，那么走稽核人员安全性查询活动数据。
2、内部人员
    配置方式：PC端 【系统管理 / 参数配置】菜单。
    参数名称：活动职位-内部用户安全性关系
    参数键：InnerSecurityRel
    参数值：{"innerUser":["Salesman","SalesSupervisor","GroupBuyManager","AccountManager","CustServiceManager","VipManager","CustServiceSpecialist","CustServiceSupervisor","SalesTeamLeader","SalesChannelManger"]}
    参数说明：当前登录用户职位在维护的数组中是则按照活动上内部人员维护的职位安全性查询。
    参数应用方式：后端beforQueryExample方法里根据当前用户的职位类型判断，如果当前用户的职位类型属于参数值当中，那么走内部人员安全性查询活动数据。

```
#### 除以上俩种之外其他走组织安全性
------ 市场活动-安全性-内容结束 ------
