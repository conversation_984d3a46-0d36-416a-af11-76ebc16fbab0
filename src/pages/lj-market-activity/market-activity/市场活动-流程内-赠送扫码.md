# 市场活动-流程内-赠送扫码


------
* 初始文档
```
创建时间：2022年3月1日
创建人：  吕志平
```
* 模块介绍
> 在执行反馈阶段,满足校验条件后开放转赠扫码功能.
>

* 涉及对象
> * 市场活动-本系统
> * 扫码记录-本系统
> * 产品行(物资行)-本系统


* 是否共用
> 否


* 数据存储
> * 数据来源 本系统数据库
> * 存储方式 本系统数据库
> * 是否同步 否

* 缓存机制
> * 是否缓存 否

* 安全性
> * 无

* 状态流转
> 触发流程 : 当选择活动进行扫码操作时下列三种状态字段会根据条件变更
#### 一 scanRecordStatus ，扫码状态 值列表(SCAN_RECORD_STATUS)
> * 1.产品码状态=“已出库”且扫码状态=正常出库
    2.存在是否有效字段值为“有效”且产品码id匹配的扫码记录.
    3.行物资的（总开瓶数 + 赠出瓶数 + 已入库数）小于 总出库瓶数
    满足上述条件给扫码记录的扫码状态(scanRecordStatus)赋值为“正常赠送”(Normalgifted)
> * 1.当产品码状态=已赠送时，且存在该产品码的扫码状态=正常赠送的扫码，
    2.存在是否有效字段值为“有效”记录时,扫码状态(scanRecordStatus)赋值为“重复赠送”(Repeatgifted)
#### 二 isEffective ，是否有效
> * 1.产品码状态=“已出库”且扫码状态=正常出库
    2.存在是否有效字段值为“有效”且产品码id匹配的扫码记录.
    3.匹配的行物资的（总开瓶数 + 赠出瓶数 + 已入库数）小于 总出库瓶数
    满足上述条件给扫码记录的是否有效字段(isEffective)赋值为“有效”(Y)
> * 1.产品码状态=“已出库”且扫码状态=正常出库
    2.存在是否有效字段值为“有效”且产品码id匹配的扫码记录.
    3.产品码不匹配的行物资的产品码时,且选中的行物资信息满足（总开瓶数 + 赠出瓶数 + 已入库数）小于 总出库瓶数
    4.判断产品二维码中的产品是否属于当前操作人员职位对应的组织所属品牌公司中产品分组类型为品鉴酒的产品，是则是否有效字段(isEffective)赋值为“有效（Y）”，否则赋值为“无效（N）”；
#### 三 descriptionType，扫码记录的匹配状态  值列表(MATCH_STATUS)
> * 1.产品码状态=“已出库”且扫码状态=正常出库
    2.存在是否有效字段值为“有效”且产品码id匹配的扫码记录.
    3.匹配的行物资的（总开瓶数 + 赠出瓶数 + 已入库数）小于 总出库瓶数
    满足上述条件给扫码记录的匹配状态字段(descriptionType)赋值为“匹配成功”(MatchSuccessfully)
> * 1.产品码状态=“已出库”且扫码状态=正常出库
    2.存在是否有效字段值为“有效”且产品码id匹配的扫码记录.
    3.产品码不匹配的行物资的产品码时,且选中的行物资信息满足（总开瓶数 + 赠出瓶数 + 已入库数）小于 总出库瓶数
    满足上述条件给扫码记录的匹配状态字段(descriptionType)赋值为“匹配失败”(MatchFailed)
* 状态流转
> 触发流程 : 当选择扫码记录时删除时,会根据当前扫码记录的是否有效字段和扫码状态字段对产品码状态进行回退
> * 1.是否有效字段=“有效”且扫码状态="正常出库"且当前删除记录的扫码状态字段='正常赠送'时
    当完成扫码记录删除，要去根据扫码记录上的扫码产品id，关联产品二维码，若删除的数据为有效数据，则更新二维码状态为“已出库”；更新赠出瓶数-1（首先更新费用实际表的赠出瓶数字段，然后再更新到费用申请表的赠出瓶数字段）；同时将执行反馈中的产品支付的实际瓶数-1；若删除的数据为无效数据，则不做数据的变动。


* 涉及组件
> * link-auto-list
> * AutoList
> * link-dialog
> * link-swipe-action
> * link-sticky



## 模块实现
###涉及页面
#### 一 费用信息

#####  1、vue页面路径
> * 1、页面完整路径
>
>  src/pages/lj-market-activity/market-activity/market-activity-cost-list-new-page.vue

##### 2、页面实现功能
> * (1).对于流程内扫码功能的影响为scene来区实际费用 actual 还是申请费用 apply.


##### 3、页面涉及组件
> 3.1 扫码弹框
>#####  3.1.1、vue页面路径
> src/pages/lj-market-activity/market-activity/components/tasting-wine-scan-code.vue
>#####  3.1.2、实现功能
> * (1).只在执行反馈阶段满足校验条件,才展示转赠扫码
> * (2).根据扫码返回结果支持跳转到重复扫码页面,打开物资行选择框,提示扫码失败,或者扫码成功.
> * (3).通过校验后调用新增接口新建扫码记录信息.


## 配置页面
> * 扫码功能配置(pc端菜单)
> 根据组织加活动类型+费用小类 以及开瓶赠送控制字段 来控制是否展示赠送扫码


------ 市场活动-流程内-赠送扫码-内容结束 ------


