import {AutoList,LovService,FilterService,DateService} from "link-taro-component";
import {Vue} from "vue/types/vue";
import {ROW_STATUS} from "@/utils/constant";
import {PreloadImg} from "@/utils/service/PreloadImg";
import Taro from "@tarojs/taro";

/**
 * 通过optionName获取link-object绑定配置对象信息
 * <AUTHOR>
 * @date    2020/9/15 19:08
 */
export function getNewActivityBasicLinkObjectProps(context: Vue, optionName: string): { map: object, autoList: AutoList } | null {
    // @ts-ignore
    const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息

    switch (optionName) {
        /**
         * 购买产品
         * */
        case 'productOption':
            return {
                map: {productId: 'id', productName: 'prodName'},
                autoList: new AutoList(context, {
                    module: null as any,
                    url: {
                        queryByExamplePage: 'action/link/product/queryPriceListProdByOrgIdPage'
                    },
                    searchFields: ['prodName','prodCode'],
                    sortOptions: null as any,
                    hooks: {
                        beforeLoad(option) {
                            delete option.param.order;
                            delete option.param.sort;
                        },
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} arrow="false">
                                <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                                <view
                                    style="display: flex;flex: 1;flex-direction: column;justify-content: space-between;align-items: flex-start;overflow: hidden;">
                                    <view style="margin:12px;">
                                        <view style="background: #A6B4C7;border-radius: 4px;line-height: 20px;">
                                            <view
                                                style="font-size: 14px;color: #FFFFFF;letter-spacing: 0;line-height: 20px;padding: 1px 4px;">{data.prodCode}
                                            </view>
                                        </view>
                                    </view>
                                    <view style="margin-left:12px;width:100%">
                                        <view
                                            style="font-family: PingFangSC-Semibold;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 16px;">
                                            {data.prodName}
                                        </view>
                                    </view>
                                    <view style="margin:12px 0 0 12px;width:100%">
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;width: 50%;float: left;">箱价:{FilterService.cny(data.prodCost)}
                                        </view>
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 14px;color: #000000;letter-spacing: 0;text-align: left;line-height: 14px;">瓶价:¥6000.00
                                        </view>
                                    </view>
                                </view>
                            </item>
                        )
                    },
                })
            };
    }
    console.error(`无法识别optionName：${optionName}`);

    return null

}
