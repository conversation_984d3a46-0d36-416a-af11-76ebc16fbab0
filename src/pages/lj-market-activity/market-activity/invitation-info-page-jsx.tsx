import {defineComponent, set} from "link-taro-component";
import {VNode} from "vue/types/umd";
import {Vue} from "vue/types/vue";

interface invitationInfoOption {
    ctrlCode: string,
    base: {
        placeholder: string
    },
    subControlList: [
        {
            ctrlName: string,
            ctrlCode: string
        }
    ],
    img: {
        attachmentPath: string
    }
    values: {
        demo: string
    }
}

const invitationInfoComponents: {
    [k: string]: (option: invitationInfoOption, formData: any) => VNode
} = {
    'introductionCall'(option, formData) {
        return (
            <view
                style={'font-family: PingFangSC-Regular;font-size: 14px;padding: 15px 24px 0px 24px;white-space: pre-line;color: #FFFFFF;letter-spacing: 0;line-height: 22px;'}>
                {this.option.base.placeholder}
            </view>
        )
    },
    'introduction'(option, formData) {
        return (
            <view
                style={'font-family: PingFangSC-Regular;font-size: 14px;padding: 0px 24px 20px 24px;color: #FFFFFF;letter-spacing: 0;line-height: 22px;white-space: pre-line;'}>
                {this.option.base.placeholder}
            </view>
        )
    },
    'subTitle'(option, formData) {
        return (
            <view
                style={'font-family: PingFangSC-Semibold;font-size: 16px;line-height: 16px;color: #FFFFFF;letter-spacing: 0;margin: 20px auto;text-align: center;'}>
                {this.option.values.demo}
            </view>
        )
    },
    'proImage'(option, formData){
        return(
            <view style={'text-align: center;width: 100%;margin: 40px 0'} onTap={saveImgLocal => {
                this.$emit('saveImg')
            }}>
                <image src={option.img.attachmentPath} style={'border-radius: 12px;'}></image>
            </view>
        )
    },
    'activityDetail'(option, formData) {
        return (
            <view
                style={'margin: 0 12px;padding: 12px 20px;background: rgba(255, 255, 255, 0.12);border-radius: 12px;'}>
                {this.option.subControlList.map((data) => (
                    <view style={'margin-bottom: 16px;line-height: 14px;font-size: 14px;'}>
                        {data.ctrlName}：{this.formData[data.ctrlCode]}
                    </view>
                ))}
            </view>
        )
    },
};
export default defineComponent({
    props: {
        option: {type: Object, required: true},
        formData: {type: Object, require: true},
    },
    //@ts-ignore
    render(h) {
        return invitationInfoComponents[this.option.ctrlCode].apply(this, [this.option, this.formData])
    },
})
