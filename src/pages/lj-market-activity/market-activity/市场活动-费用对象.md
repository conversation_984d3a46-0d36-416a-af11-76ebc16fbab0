# 市场活动-费用对象
```
创建时间：2022/01/10 20:15
创建人：  宋燕荣
```
### 逻辑功能实现
* 说明：下面所有页面实现功能数字序号都对应以下22列。
```
1、新建活动
2、活动作废
3、活动列表展示、按状态快捷查询、筛选、排序、模糊查询
4、提交活动申请
5、一键报备
6、分享海报
7、创建名单
8、名单提报名额调整
9、活动稽核
10、执行反馈
11、提交费用审批
12、清除执行案编码功能及操作权限【活动阶段和场景】
13、执行反馈阶段维护业务场景图片，拍照加水印【时间、地址、一些业务信息】、相册选择
14、新增、删除、编辑、查看费用申请-现金类兑付方式数据
15、新增、编辑、查看费用实际-现金类兑付方式数据
16、新增、删除、编辑、查看费用申请-产品类兑付方式数据
17、新增、编辑、查看费用实际-产品类兑付方式数据
18、维护、切换、清除互动信息
19、选择消费者关联到当前活动、新建消费者
20、选择内部人员关联到当前活动
21、选择参与终端/经销商关联到当前活动
22、新增、更新、删除活动动销数据
```
#### 14 & 15 & 16 & 17
* 市场活动的费用分为俩个阶段
```
1、申请费用阶段
- 申请费用在活动申请提交之前必须维护，手动维护。可新增、更新、删除
    · 如果活动基础信息选择了执行案编码那么选择执行案明细带出兑付方式类型。进而维护当前兑付方式类型的产品类或现金类费用数据
    · 如果活动基础信息没有选择执行案编码那么直接通过选择兑付方式值列表值。进而维护当前兑付方式类型的产品类或现金类费用数据
2、实际费用阶段 活动详情界面点击执行反馈按钮，会把申请费用复制一份为实际费用，后续流程中实际费用还可以继续新增更新，但不可以删除。
- 费用实际如果没有关联执行案明细那么费用提交(也叫执行反馈提交)时需要关联执行案明细
    · 如果当前市场活动没有选择执行案编码，那么需要先选择执行案，再选择当前选择的执行案下的执行案明细。选定之后，将执行案关键信息更新到市场活动对象上，将执行案明细关键信息更新到当前费用实际对象上。
    · 如果当前市场活动有执行案编码，那么之前选择当前执行案编码下的执行案明细数据，将选择的执行案明细关键信息更新到当前费用实际上。
```

* 费用信息展示和编辑场景
> 请查看市场活动-涉及组件.md
```
相关组件：
1、cost-new 费用组合组件
2、prod-new 兑付方式产品类组件
3、cash-new 兑付方式现金类组件
费用信息界面：
1、市场活动模块：src/pages/lj-market-activity/market-activity/market-activity-cost-list-new-page.vue
2、执行案模块：src/pages/lj-market-activity/perform-case/perform-case-cost-list-new-page.vue
这俩个界面为什么不共用？查看市场活动-Q&A.md
```
* 关于汇总金额字段
```
活动费用相关字段：
1、现金申请金额
2、现金审批金额
3、现金实际填报金额
4、现金实发金额
5、产品申请金额
6、产品审批金额
7、产品实际填报金额
8、产品实发金额
9、实发金额-总
10、申请金额-总
重点：费用的这些金额字段都是后端计算存表。
- 前端展示直接查询返回数据，如果添加了新的费用需要展示最新的金额数据结果，前端需要计算但仅作为展示不会回传给后端，数据库存值是后端计算后存入。
```
* 费用信息表数据结构说明
```
申请费用和实际费用的表数据核心结构一致都有几个关键性的字段
1、活动ID
2、执行案明细ID、执行案明细编码
3、兑付方式类型、兑付方式ID、兑付方式编码、兑付方式名称
4、物资名称
```
* 小程序前端界面展示核心
```
1、区分兑付方式类型：现金／产品。使用不同的组件 现金类 Money ：<cash-new> 产品类 Product <prod-new>
2、根据不同的兑付方式分组展示、维护费用信息。
3、关于汇总字段查看组件细节
```
* 费用相关接口

|  序号 |  作用  |  接口   |  参数示例  |
| :--- | :------ | :------ | :------ |
| 1 | 查询-费用申请数据 | action/link/actMaterial/queryAndGroupData | {"filtersRaw":[{"id":"actId","property":"actId","value":"活动ID","operator":"="}]} |
| 2 | 查询-执行反馈按钮操作时查询费用实际数据 | action/link/actualFee/queryAndGroupData/edit | {"filtersRaw":[{"id":"actId","property":"actId","value":"活动ID","operator":"="}]} |
| 3 | 查询-执行反馈详情界面查询费用实际数据 | action/link/actualFee/queryAndGroupData/detail | {"filtersRaw":[{"id":"actId","property":"actId","value":"活动ID","operator":"="}]} |
| 4 | 查询-执行反馈阶段=费用实际。赠送\开瓶后需要更新产品的实际数量 | action/link/actualFee/queryAndGroupData/edit | {filtersRaw: [{id: "actId", property: "actId", value: "活动ID", operator: "="}],queryType : 'actualQtyUpdate'} |
| 5 | 查询-选择费用兑付方式-1：无执行案时 | action/link/payment/queryByExamplePage | {"filtersRaw":[{"id":"effectiveFlag_0","property":"effectiveFlag","value":"Y","operator":"="}]} |
| 6 | 查询-选择费用兑付方式-2：有执行案时 | action/link/costDetail/queryUnSelectPage | {"attr5":"Y","attr6":"","filtersRaw":[{"id":"actId_0","property":"actId","value":"活动ID","operator":"="},{"id":"actProgId_1","property":"actProgId","value":"执行案ID","operator":"="},{"id":"costStatus_2","property":"costStatus","value":"Efficient","operator":"="}],"oauth":"MY_ORG","page":1,"rows":25,"sort":"created","order":"desc"} |
| 7 | 查询-现金类可添加的明细类型 | action/link/basic/queryByExamplePage | {"filtersRaw":[{"id":"type_0","property":"type","operator":"=","value":"CASH_FEE"},{"id":"activeFlag_1","property":"activeFlag","operator":"=","value":"Y"}]} |
| 8 | 查询-产品类可添加的产品信息 | action/link/product/queryUnSelectByOrgIdPage | {"filtersRaw":[{"id":"attr1_0","property":"attr1","value":"活动ID"},{"id":"attr2_1","property":"attr2","value":"兑付方式编码"},{"id":"source_2","property":"source","value":"查询场景"}],"attr3":"市场活动的销售城市ID"} |
| 9 | 更新-费用申请-现金类-小计 | action/link/actIntegration/updateActIdAndCityAfterSelectedFee | {"marketAct":{"id":"活动ID","rowVersion":当前活动数据的版本号,"row_status":"UPDATE","updateFields":"id,rowVersion,remark"},"actMaterialList":[{"id":"费用ID","actualTranPrice":小计值,"row_status":"UPDATE","updateFields":"id,actualTranPrice"}]} |
| 10| 更新-费用实际-产品类-信息 | action/link/actIntegration/updateActIdAndCityAfterSelectedFee | {"marketAct":{"id":"活动ID","rowVersion":当前活动数据的版本号,"row_status":"UPDATE","updateFields":"id,remark,rowVersion"},"actMaterialList":[{"id":"费用ID","unit":"单位","actualTranPrice":实价,"materialSource":"物资来源","qty":申请数量,"actualQty":实际数量,"row_status":"UPDATE","updateFields":"id,unit,actualTranPrice,qty,actualQty,materialSource"}]} |
| 11| 更新-费用申请-现金类-小计 | action/link/actIntegration/updateActIdAndCityAfterSelectedFee | {"marketAct":{"id":"活动ID","rowVersion":当前活动数据的版本号,"row_status":"UPDATE","updateFields":"id,rowVersion,remark"},"actualFeeList":[{"id":"费用ID","actualTranPrice":实价,"row_status":"UPDATE","updateFields":"id,actualTranPrice"}]} |
| 12| 更新-费用申请-产品类-信息 | action/link/actIntegration/updateActIdAndCityAfterSelectedFee | {"marketAct":{"id":"活动ID","rowVersion":当前活动数据的版本号,"row_status":"UPDATE","updateFields":"id,remark,rowVersion"},"actualFeeList":[{"id":"费用ID","unit":"单位","actualTranPrice":"实价","qty":申请数量,"actualQty":实际数量,"row_status":"UPDATE","updateFields":"id,unit,actualTranPrice,qty,actualQty,materialSource"}]} |
| 13| 新建-费用申请 | action/link/actIntegration/updateActIdAndCityAfterSelectedFee | {"marketAct":{"executivesId":"","id":"436604321974385738","row_status":"UPDATE","province":"四川省","provinceId":"1-510000","city":"泸州市","cityId":"2-510500","district":"龙马潭区","districtId":"3-510504","rowVersion":1,"companyId":"68359110689030144","updateFields":"id,executivesId,province,provinceId,city,cityId,district,districtId,rowVersion,jiheSecField"},"actMaterialList":[{"id":"","prodName":"Cash","actualTranPrice":1,"costId":null,"actId":"436604321974385738","feePayCode":"100024","feePayType":"现金","row_status":"NEW","payType":"Money","qty":1,"actualQty":1},{"prodCode":"40014844","prodName":"52泸州老窖特曲酒老字号（2018版）二维码装500ml*6","qty":1,"price":"328.00","actualTranPrice":"328.00","prodId":"105427803343627764","actId":"436604321974385738","feePayCode":"100014","feePayType":"产品兑付-小酒","costId":null,"materialSource":"SpecialDealer","unit":"Ping","row_status":"NEW","materialPrice":"328.00","sellingPrice":"1298.00","payType":"Product"}]} |
| 14| 新建-费用实际 | action/link/actIntegration/updateActIdAndCityAfterSelectedFee | {"marketAct":{"executivesId":"279277086735532209","id":"414066126764179188","row_status":"UPDATE","rowVersion":16,"companyId":"68359110689030144","updateFields":"id,executivesId,province,provinceId,city,cityId,district,districtId,rowVersion,jiheSecField","executivesName":"仿真测试","availableBalance":0},"actualFeeList":[{"id":"","prodName":"Mealticket","actualTranPrice":2,"costId":"272378469478830105","actId":"414066126764179188","feePayCode":"100001","feePayType":"现金支付","row_status":"NEW"},{"prodCode":"40014844","prodName":"52泸州老窖特曲酒老字号（2018版）二维码装500ml*6","qty":0,"actualQty":1,"price":"328.00","actualTranPrice":"328.00","prodId":"105427803343627764","actId":"414066126764179188","feePayCode":"100006","feePayType":"产品支付-品鉴装支付","costId":"272378469478830106","materialSource":"SpecialDealer","unit":"Ping","row_status":"NEW","materialPrice":"328.00","sellingPrice":"1298.00","payType":"Product"},{"id":"416362595826594201","costId":"272378469478830105","actualTranPrice":1,"row_status":"UPDATE","updateFields":"id,actualTranPrice"}]} |
| 15| 更新-关联执行案明细 | action/link/actIntegration/updateActAndActualFee | {"marketAct":{"id":"421604645595049993","exeCaseId":"366946932515148113","executivesId":"365435860025744065","province":"四川省","provinceId":"1-510000","city":"泸州市","cityId":"2-510500","district":"龙马潭区","districtId":"3-510504","noPerformComments":"","rowVersion":8,"companyId":"362323365614457283","updateFields":"id,exeCaseId,executivesId,province,provinceId,city,cityId,district,districtId,noPerformComments,rowVersion,jiheSecField"},"actualFeeList":[{"costId":"366962433514017378","id":"436612935487909908","updateFields":"id,costId"}]} |


* 数据示例
```
1、费用申请的数据
查询接口：action/link/actMaterial/queryAndGroupData
{
    "success": true,
    "rows": {
        "Money": [
            {
                "feeReimCode": "A2011007004",
                "costCode": "Z112716-001",
                "feePayCode": "202011110001",
                "feeReim": "国窖壹伍柒叁（上海）商贸有限公司",
                "costTypeName": "其他销售奖励",
                "feePayId": "268779070207299814",
                "dataList": [
                    {
                        "id": "416602249398251744",
                        "createdBy": "278459884125819148",
                        "created": "2021-12-24 14:28:36",
                        "lastUpdated": "2022-02-10 15:12:09",
                        "lastUpdatedBy": "329951518210137232",
                        "corpid": "0",
                        "orgId": "W-2EUCVR",
                        "postnId": "W-2EUCY8",
                        "loginCorpId": "0",
                        "total": -1,
                        "login": "274272585485914157",
                        "basicOrgId": "W-2EUCVR",
                        "basicPostnId": "W-2EUCY8",
                        "selectedFlag": false,
                        "reloadCache": "N",
                        "exportTotal": -1,
                        "lastId": "-1",
                        "actId": "416600504244826317",
                        "activityNum": "SCHD211224933004",
                        "costId": "274913528266100816",
                        "costCode": "Z112716-001",
                        "feePayCode": "202011110001",
                        "feePayId": "268779070207299814",
                        "feePayType": "现金支付",
                        "prodName": "MealFee",
                        "actualTranPrice": 200.0,
                        "qty": 1,
                        "unit": "CNY",
                        "subTotalMount": 200.0,
                        "payType": "Money",
                        "actualQty": "1.0000000",
                        "payTypeCostId": "202011110001274913528266100816",
                        "feeReim": "国窖壹伍柒叁（上海）商贸有限公司",
                        "wineTasting": "N",
                        "bottledSum": "0",
                        "giftedSum": "0"
                    }
                ],
                "costId": "274913528266100816",
                "costTypeCode": "100026",
                "availableBalance": "94650.00",
                "feePayType": "现金支付",
                "applyAmount": "200.00"
            }
        ],
        "Product": [
            {
                "feeReimCode": "A2012041001",
                "costCode": "Z112716-002",
                "feePayCode": "134345",
                "feeReim": "泸州中海酒类销售有限公司",
                "costTypeName": "其他销售奖励",
                "feePayId": "240787233687207966",
                "dataList": [
                    {
                        "id": "416602249465360609",
                        "createdBy": "278459884125819148",
                        "created": "2021-12-24 14:28:36",
                        "lastUpdated": "2022-02-10 15:12:09",
                        "lastUpdatedBy": "329951518210137232",
                        "corpid": "0",
                        "orgId": "W-2EUCVR",
                        "postnId": "W-2EUCY8",
                        "loginCorpId": "0",
                        "total": -1,
                        "login": "274272585485914157",
                        "basicOrgId": "W-2EUCVR",
                        "basicPostnId": "W-2EUCY8",
                        "selectedFlag": false,
                        "reloadCache": "N",
                        "exportTotal": -1,
                        "lastId": "-1",
                        "actId": "416600504244826317",
                        "activityNum": "SCHD211224933004",
                        "costId": "274913528266100817",
                        "costCode": "Z112716-002",
                        "feePayCode": "134345",
                        "feePayId": "240787233687207966",
                        "feePayType": "产品-品鉴装支付",
                        "prodName": "52国窖1573/国内装375ml*6",
                        "prodId": "W-2ATC3M",
                        "prodCode": "40001229",
                        "price": 980.0,
                        "actualTranPrice": 0.0,
                        "qty": 1,
                        "unit": "Ping",
                        "subTotalMount": 0.0,
                        "payType": "Product",
                        "materialSource": "SpecialDealer",
                        "sellingPrice": "5500.0000000",
                        "materialPrice": "980.0000000",
                        "payTypeCostId": "134345274913528266100817",
                        "feeReim": "泸州中海酒类销售有限公司",
                        "wineTasting": "N",
                        "bottledSum": "0",
                        "giftedSum": "0"
                    }
                ],
                "costId": "274913528266100817",
                "costTypeCode": "100026",
                "availableBalance": "0.00",
                "feePayType": "产品-品鉴装支付",
                "applyAmount": "0.00"
            }
        ]
    }
}
```

```
2、费用实际的数据
{
    "success": true,
    "rows": {
        "Money": [
            {
                "actualTotal": "0.00",
                "feePayId": "278614513664921619",
                "costId": "274913528261906506",
                "costTypeCode": "100043",
                "availableBalance": "94054.00",
                "applyAmount": "0.00",
                "feeReimCode": "A0010155956",
                "costCode": "Z112713-002",
                "feePayCode": "100007",
                "feeReim": "拓宏酒业",
                "costTypeName": "品鉴会",
                "dataList": [
                    {
                        "id": "354288280348790953",
                        "createdBy": "274312196195225712",
                        "created": "2021-07-05 15:37:31",
                        "lastUpdated": "2021-07-07 18:20:50",
                        "lastUpdatedBy": "W-30DB903",
                        "corpid": "0",
                        "orgId": "W-2EUCVR",
                        "postnId": "W-2EUCY8",
                        "loginCorpId": "0",
                        "total": -1,
                        "login": "274272585485914157",
                        "basicOrgId": "W-2EUCVR",
                        "basicPostnId": "W-2EUCY8",
                        "selectedFlag": false,
                        "reloadCache": "N",
                        "exportTotal": -1,
                        "lastId": "-1",
                        "actId": "354283655293964384",
                        "costId": "274913528261906506",
                        "costCode": "Z112713-002",
                        "resourceId": "354288280348790953",
                        "feePayCode": "100007",
                        "feePayId": "278614513664921619",
                        "feePayType": "现金案折扣",
                        "prodName": "MealFee",
                        "actualTranPrice": 0.0,
                        "unit": "CNY",
                        "subTotalMount": 0.0,
                        "payType": "Money",
                        "applyAmount": 0.0,
                        "payTypeCostId": "100007274913528261906506",
                        "feeReim": "拓宏酒业",
                        "wineTasting": "N",
                        "allDataFlag": "N",
                        "bottledSum": "0",
                        "giftedSum": "0",
                        "storageSum": "0",
                        "outQty": 0,
                        "giftQty": 0,
                        "inQty": 0,
                        "openQty": 0,
                        "estInQty": 0,
                        "totalSumQty": 0
                    }
                ],
                "feePayType": "现金案折扣"
            }
        ],
        "Product": [
            {
                "actualTotal": "980.00",
                "feePayId": "278614514927407125",
                "costId": "",
                "costTypeCode": "",
                "availableBalance": 0.0,
                "applyAmount": "980.00",
                "feeReimCode": "",
                "feePayCode": "100011",
                "feeReim": "",
                "costTypeName": "",
                "dataList": [
                    {
                        "id": "354288280403316906",
                        "createdBy": "274312196195225712",
                        "created": "2021-07-05 15:37:31",
                        "lastUpdated": "2021-07-07 18:20:50",
                        "lastUpdatedBy": "W-30DB903",
                        "corpid": "0",
                        "orgId": "W-2EUCVR",
                        "postnId": "W-2EUCY8",
                        "loginCorpId": "0",
                        "total": -1,
                        "login": "274272585485914157",
                        "basicOrgId": "W-2EUCVR",
                        "basicPostnId": "W-2EUCY8",
                        "selectedFlag": false,
                        "reloadCache": "N",
                        "exportTotal": -1,
                        "lastId": "-1",
                        "actId": "354283655293964384",
                        "costId": "",
                        "resourceId": "354288280403316906",
                        "feePayCode": "100011",
                        "feePayId": "278614514927407125",
                        "feePayType": "产品兑付-销售折扣",
                        "prodName": "43国窖1573酒2010版500ml*6",
                        "prodId": "184268788055539713",
                        "prodCode": "40003986",
                        "price": 980.0,
                        "actualTranPrice": 980.0,
                        "qty": 1,
                        "actualQty": 1,
                        "unit": "Ping",
                        "applyUnit": "Ping",
                        "subTotalMount": 980.0,
                        "payType": "Product",
                        "applyAmount": 980.0,
                        "materialSource": "SpecialDealer",
                        "sellingPrice": "5500.0000000",
                        "materialPrice": "980.0000000",
                        "payTypeCostId": "100011",
                        "wineTasting": "N",
                        "allDataFlag": "N",
                        "bottledSum": "0",
                        "giftedSum": "0",
                        "storageSum": "0",
                        "outQty": 0,
                        "giftQty": 0,
                        "inQty": 0,
                        "openQty": 0,
                        "estInQty": 0,
                        "totalSumQty": 0
                    }
                ],
                "feePayType": "产品兑付-销售折扣"
            }
        ]
    }
}
```
------ 市场活动-费用对象-内容结束 ------
