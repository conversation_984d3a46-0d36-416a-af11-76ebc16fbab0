<template>
    <link-page class="new-acticity-basic-page">
        <ma-navigation-bar :backVisible="true"
                           :zIndex="zIndex"
                           :backgroundImg="$imageAssets.homeMenuBgImage"
                           :title="navigationBarTitle"
                           :titleColor="navigationBarTitleColor"
                           :navBackgroundColor="navBackgroundColor"
                           :udf="udfBack">
        </ma-navigation-bar>
        <link-form :option="option" ref="form" :value="option.formData"
                   :rules="formRules" hideSaveButton>
            <view class="activity-type-view">
                <view style="width: 100%;height: 12px"></view>
                <view class="activity-type" :style="{'background-image': 'url(' + $imageAssets.activityBgImage + ')'}">
                    <view class="type-title">
                        <text>{{ activityItem.costLargeType }}/{{ activityItem.costMiddleType }}/{{ activityItem.costType }}</text>
                    </view>
                </view>
            </view>
            <view v-for="(item,index) in editSubControlList" :key="`mktAct${index}`">
                <!--非执行反馈环节时编辑基础信息-【除执行案编码外所有字段】-->
                <link-form-item :key="index" :label="fieldNameChange(item.values.field, item.base.label)"
                                :readonly="item.values.readonlyFlag === 'true' ? true : false"
                                :required="item.base.require"
                                v-if="!['view-line', 'view-blank', 'agree-detail'].includes(item.ctrlCode)
                                && pageSource !== 'executiveFeedback'
                                && (item.ctrlCode !== 'dispaly-collect' || (item.ctrlCode === 'dispaly-collect' && agreementItem.agrType === 'display' && collectItem.id))
                                && item.values.field !== 'exeCaseCode'
                                && (item.values.field !== 'programName' || (item.values.field === 'programName' && option.formData.targetPopulation === 'Member'))
                                && (item.values.field !== 'memberTier' || (item.values.field === 'memberTier' && option.formData.targetPopulation === 'Member'))
                                && (item.values.field !== 'inviterName' || (item.values.field === 'inviterName' && option.formData.targetPopulation === 'Member'))
                                && (item.values.field !== 'actListOwner' || (item.values.field === 'actListOwner' && option.formData.status !== 'New'))
                                && (item.values.field !== 'registNum' || (item.values.field === 'registNum' && option.formData.status !== 'New'))">
                    <!--非无执行案说明、活动执行主体【改为现在 子公司/经销商】、费用垫付对象字段、陈列品项、协议、活动场地、场地地址、供应商、宴席推荐终端
                    活动类型【所有活动都有类型字段-前端写死,表里存的actIndeSourCode，查询也查了activityType(前期培训的活动详情里的活动类型改动模版太多所以这个字读啊也一起判断)】
                    、宴席主家、受益对象、销售城市、销售区县、购买产品、对接人、提报名额-->
<!--                    如获取到信息忠诚度计划信息，则将忠诚度计划赋值到忠诚度计划字段上，同时前端将字段忠诚度计划、会员等级、邀约人字段显示出来，忠诚度计划字段不允许修改；-->
                    <new-activity-basic-jsx-component :option="item" :formData="option.formData" v-if="!(item.values.field === 'actExecutivesName'
                     || item.values.field === 'executivesName'
                     || item.values.field === 'displayItem'
                     || item.values.field === 'protocolName'
                     || item.values.field === 'actIndeSourCode'
                     || item.values.field === 'activityType'
                     || item.values.field === 'masterName'
                     || item.values.field === 'beneficiaryName'
                     || item.values.field === 'restaurant'
                     || item.values.field === 'restaurantAddr'
                     || item.values.field === 'supplier'
                     || item.values.field === 'targetTerName'
                     || item.values.field === 'salesCity'
                     || item.values.field === 'salesDistrict'
                     || item.values.field === 'productName'
                     || item.values.field === 'noPerformComments'
                     || (item.values.field === 'targetPopulation')
                     || (item.values.field === 'programName')
                     || (item.values.field === 'memberTier')
                     || (item.values.field === 'inviterName')
                     || (item.values.field === 'actListOwner')
                     || (item.values.field === 'registNum')
                     || item.values.field === 'enterpriseName'
                     || item.values.field === 'visitLevel'
                     || item.ctrlCode === 'dispaly-collect'
                     )"/>
                    <!--                        无执行案说明-->
                    <link-input v-model="option.formData[item.values.field]"
                                :readonly="$utils.isNotEmpty(option.formData.exeCaseCode)"
                                v-if="item.values.field === 'noPerformComments'"></link-input>
                    <!--子公司/经销商-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                v-if="item.values.field === 'actExecutivesName'"
                                @tap="pickActExecutives(item.base.label,item.values.readonlyFlag)"
                                :suffixIcon="item.values.readonlyFlag === 'true'?'':'mp-arrow-right'"></link-input>
                    <!--费用垫付对象-->
                    <link-input v-model="option.formData[item.values.field]" readonly
                                v-if="item.values.field === 'executivesName'"></link-input>
                    <!--受益对象-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                @tap="pickBeneficiaryName(item.base.label,item.values.readonlyFlag)"
                                v-if="item.values.field === 'beneficiaryName'" :suffixIcon="item.values.readonlyFlag === 'true'?'':'mp-arrow-right'"
                                suffixIcon="mp-arrow-right"></link-input>
                    <!--企业信息-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                @tap="pickEnterpriseName(item.base.label, item.values.readonlyFlag)"
                                v-if="item.values.field === 'enterpriseName'"
                                :suffixIcon="item.values.readonlyFlag === 'true' ? '' : 'mp-arrow-right'"></link-input>
                    <!--拜访等级-->
                    <view v-if="item.values.field === 'visitLevel'" style="color: #3b4144;">
                        {{option.formData[item.values.field] | lov(`${item.values.lovType}`)}}
                    </view>
                    <!--陈列品项-->
                    <link-address v-if="item.values.field === 'displayItem'"
                                  :province.sync="prodLargeClassView"
                                  :city.sync="displayItemView"
                                  :custom="productTypeOption"/>
                    <!--协议-->
<!--                    <link-input v-model="option.formData[item.values.field]" inputReadonly-->
<!--                                v-if="item.values.field === 'protocolName'"-->
<!--                                @tap="pickProtocol(item.base.label)" suffixIcon="mp-arrow-right"></link-input>-->
                    <view v-if="item.values.field === 'protocolName'" @tap="pickProtocol(item.base.label)" style="color: #c6c6c6">
                        选择协议<link-icon icon="icon-right"></link-icon>
                    </view>
                    <!--陈列采集记录-->
                    <view v-if="item.ctrlCode === 'dispaly-collect'" style="color: #1F74FF;" @tap="collectDialogFlag = true">查看</view>
                    <!--                    宴席主家-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                v-if="item.values.field === 'masterName'"
                                @tap="pickMasterName(item.base.label, item.values.field)" suffixIcon="mp-arrow-right"></link-input>
                    <!--                    活动类型-->
                    <view style="width: 100%"
                          v-if="item.values.field === 'actIndeSourCode' || item.values.field === 'activityType'"
                          @tap="pickActIndeSourCode(item)">
                        <view style="color: #3b4144;width: 90%;float: left;text-align: right;">
                            {{ option.formData.actIndeSourCode|lov('MC_TYPE') }}
                        </view>
                        <view class="iconfont icon-right" v-if="item.values.readonlyFlag !== 'true'"
                              style="float: right;line-height: 18px;font-size: 16px;width: 10%"></view>
                    </view>
                    <!--活动场地-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                @tap="pickVenue(item.base.label)" v-if="item.values.field === 'restaurant'"
                                suffixIcon="mp-arrow-right"></link-input>
                    <!--场地地址-->
                    <link-input v-if="item.values.field === 'restaurantAddr'"
                                v-model="option.formData[item.values.field]"></link-input>
                    <!--                    供应商-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                v-if="item.values.field === 'supplier'"
                                @tap="pickSupplierName(item.base.label)" suffixIcon="mp-arrow-right"></link-input>
                    <!--                    宴席推荐客户-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                v-if="item.values.field === 'targetTerName'"
                                @tap="pickTargetTerName(item.base.label)" suffixIcon="mp-arrow-right"></link-input>
                    <!--销售城市-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                @tap="pickSalesCityName(item.base.label,item.values.readonlyFlag)"
                                v-if="item.values.field === 'salesCity'"
                                :suffixIcon="item.values.readonlyFlag === 'true'?'':'mp-arrow-right'"></link-input>
                    <!--销售区县-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                @tap="pickSalesDistrictName(item.base.label,item.values.readonlyFlag)"
                                v-if="item.values.field === 'salesDistrict'"
                                :suffixIcon="item.values.readonlyFlag === 'true'?'':'mp-arrow-right'"></link-input>
                    <!--购买产品-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                @tap="pickProductName(item.base.label,item.values.readonlyFlag)"
                                v-if="item.values.field === 'productName'"
                                :suffixIcon="item.values.readonlyFlag === 'true'?'':'mp-arrow-right'"></link-input>
                     <!--活动目标人群-->
                    <link-lov v-model="option.formData[item.values.field]"
                              :type="item.values.lovType"
                              ref="targetPopulation"
                              @picker-change="pickTargetPopulationChange"
                              v-if="item.values.field === 'targetPopulation'"></link-lov>
<!--                    忠诚度 lytProgramOption-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                v-if="item.values.field === 'programName' && option.formData.targetPopulation === 'Member'"
                                suffixIcon="mp-arrow-right"></link-input>
<!--                    会员等级 memberTierOption-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                @tap="pickMemberTier(item.base.label)" v-if="item.values.field === 'memberTier' && option.formData.targetPopulation === 'Member'"
                                suffixIcon="mp-arrow-right"></link-input>
<!--                    邀约人 inviterOption-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                @tap="pickInviterId(item.base.label)" v-if="item.values.field === 'inviterName' && option.formData.targetPopulation === 'Member'"
                                suffixIcon="mp-arrow-right"></link-input>
                    <!--对接人-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                @tap="pickActName(item.base.label)" v-if="item.values.field === 'actListOwner'"
                                suffixIcon="mp-arrow-right"></link-input>
                    <!--提报名额-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                v-if="item.values.field === 'registNum'"></link-input>
                </link-form-item>
                <!--非执行反馈环境时编辑基础信息-【执行案编码】.需要加清除字段功能-->
                <link-form-item :key="index" :label="item.base.label" :arrow="true"
                                :readonly="true"
                                :required="item.base.require"
                                v-if="item.ctrlCode !== 'view-line' && item.ctrlCode !== 'view-blank'
                                && pageSource !== 'executiveFeedback' &&  item.values.field === 'exeCaseCode'">
                    <input type="text" v-model="option.formData[item.values.field]" disabled
                           @tap="pickExeCaseCode(item.base.label,'apply')"/>
                    <view class="ent-wrap" @tap="clearExeCaseCode()"
                          v-if="!$utils.isEmpty(option.formData[item.values.field])">
                        <view class="iconfont icon-close"
                              style="float: left;line-height: 18px;font-size: 16px;width: 10%"></view>
                    </view>
                </link-form-item>
                <!--固定存在的业务场景-字段顺序在活动类型后面-->
                <link-form-item v-if="item.values.field === 'actIndeSourCode' || item.values.field === 'activityType'"
                                label="业务场景" required class="bus-scene-row">
                    <link-lov v-model="option.formData.busScene" type="TMPL_SUB_BIZ_TYPE" :multiple="true" :excludeLovs="excludebusSceneData"
                              :excludeShowNameFlag="true" parent-type="LNK_AUTO_TEMPLATE_TYPE" :parent-val="'businessScenario'"></link-lov>
                    <view class="ent-wrap" @tap="removeBusScene()" v-if="!$utils.isEmpty(option.formData.busScene)">
                        <view class="iconfont icon-close"
                              style="float: left;line-height: 30px;font-size: 16px;width: 100%;text-align: center"></view>
                    </view>
                </link-form-item>
                <!--执行反馈时【除执行案编码外所有字段-->
                <link-form-item :key="index" :label="item.base.label"
                                :readonly="item.values.readonlyFlag === 'true' ? true : false"
                                :required="item.base.require"
                                v-if="!['view-line', 'view-blank', 'agree-detail'].includes(item.ctrlCode)
                                && pageSource === 'executiveFeedback'
                                && (item.ctrlCode !== 'dispaly-collect' || (item.ctrlCode === 'dispaly-collect' && agreementItem.agrType === 'display' && collectItem.id))
                                && item.values.field !== 'exeCaseCode'
                                && (item.values.field !== 'programName' || (item.values.field === 'programName' && option.formData.targetPopulation === 'Member'))
                                && (item.values.field !== 'memberTier' || (item.values.field === 'memberTier' && option.formData.targetPopulation === 'Member'))
                                && (item.values.field !== 'inviterName' || (item.values.field === 'inviterName' && option.formData.targetPopulation === 'Member'))">
                    <!--非执行案编码、无执行案说明、活动执行主体字段【改为子公司/经销商】、陈列品项、协议、活动场地、场地地址、供应商、宴席推荐终端
                    活动类型【所有活动都有类型字段-前端写死】、宴席主家、受益对象、销售城市、销售区县、购买产品-->
                    <new-activity-basic-jsx-component :option="item" :formData="option.formData"
                                                      v-if="!(item.values.field === 'exeCaseCode'
                                                      || item.values.field === 'actExecutivesName'
                                                      || item.values.field === 'noPerformComments'
                    || item.values.field === 'displayItem'
                    || item.values.field === 'protocolName'
                    || item.values.field === 'actIndeSourCode'
                    || item.values.field === 'activityType'
                    || item.values.field === 'masterName'
                    || item.values.field === 'beneficiaryName'
                    || item.values.field === 'restaurant'
                    || item.values.field === 'restaurantAddr'
                    || item.values.field === 'supplier'
                    || item.values.field === 'targetTerName'
                    || item.values.field === 'salesCity'
                    || item.values.field === 'salesDistrict'
                    || item.values.field === 'productName'
                    || item.values.field === 'noPerformComments'
                    || item.values.field === 'targetPopulation'
                    || item.values.field === 'programName'
                    || item.values.field === 'memberTier'
                    || item.values.field === 'inviterName'
                    || item.values.field === 'enterpriseName'
                    || item.values.field === 'visitLevel'
                    || item.ctrlCode === 'dispaly-collect'
                    )"/>
                    <!--子公司/经销商-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                v-if="item.values.field === 'actExecutivesName'"
                                @tap="pickActExecutives(item.base.label,item.values.readonlyFlag)" :suffixIcon="item.values.readonlyFlag === 'true'?'':'mp-arrow-right'"></link-input>
                    <!--                        无执行案说明-->
                    <link-input v-model="option.formData[item.values.field]" :readonly="$utils.isNotEmpty(option.formData.exeCaseCode)"
                                v-if="item.values.field === 'noPerformComments'"></link-input>
                    <!--陈列品项-->
                    <link-address v-if="item.values.field === 'displayItem'"
                                  :province.sync="prodLargeClassView"
                                  :city.sync="displayItemView"
                                  :custom="productTypeOption"/>
                    <!--协议-->
<!--                    <link-input v-model="option.formData[item.values.field]" inputReadonly-->
<!--                                v-if="item.values.field === 'protocolName'"-->
<!--                                @tap="pickProtocol(item.base.label)" suffixIcon="mp-arrow-right"></link-input>-->
                    <view v-if="item.values.field === 'protocolName'" @tap="pickProtocol(item.base.label)" style="color: #c6c6c6">
                        选择协议<link-icon icon="icon-right"></link-icon>
                    </view>
                    <!--陈列采集记录-->
                    <view v-if="item.ctrlCode === 'dispaly-collect'" style="color: #1F74FF;" @tap="collectDialogFlag = true">查看</view>
                    <!--                    宴席买家-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                v-if="item.values.field === 'masterName'"
                                @tap="pickMasterName(item.base.label, item.values.field)" suffixIcon="mp-arrow-right"></link-input>
                    <!--                    活动类型-->
                    <view style="width: 100%"
                          v-if="item.values.field === 'actIndeSourCode' || item.values.field === 'activityType'"
                          @tap="pickActIndeSourCode(item)">
                        <view style="color: #3b4144;width: 90%;float: left;text-align: right;">
                            {{ option.formData.actIndeSourCode|lov('MC_TYPE') }}
                        </view>
                        <view class="iconfont icon-right" v-if="item.values.readonlyFlag !== 'true'"
                              style="float: left;line-height: 18px;font-size: 16px;width: 10%"></view>
                    </view>
                    <!--活动场地-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                @tap="pickVenue(item.base.label)" v-if="item.values.field === 'restaurant'"
                                suffixIcon="mp-arrow-right"></link-input>
                    <!--场地地址-->
                    <link-input v-if="item.values.field === 'restaurantAddr'"
                                v-model="option.formData[item.values.field]"></link-input>
                    <!--                    供应商-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                v-if="item.values.field === 'supplier'"
                                @tap="pickSupplierName(item.base.label)" suffixIcon="mp-arrow-right"></link-input>
                    <!--                    宴席推荐终端-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                v-if="item.values.field === 'targetTerName'"
                                @tap="pickTargetTerName(item.base.label)" suffixIcon="mp-arrow-right"></link-input>
                    <!--受益对象-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                @tap="pickBeneficiaryName(item.base.label,item.values.readonlyFlag)"
                                v-if="item.values.field === 'beneficiaryName'"
                                :suffixIcon="item.values.readonlyFlag === 'true'?'':'mp-arrow-right'"></link-input>
                    <!--企业信息-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                @tap="pickEnterpriseName(item.base.label, item.values.readonlyFlag)"
                                v-if="item.values.field === 'enterpriseName'"
                                :suffixIcon="item.values.readonlyFlag === 'true' ? '' : 'mp-arrow-right'"></link-input>
                    <!--拜访等级-->
                    <view v-if="item.values.field === 'visitLevel'" style="color: #3b4144;">
                        {{option.formData[item.values.field] | lov(`${item.values.lovType}`)}}
                    </view>
                    <!--销售城市-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                @tap="pickSalesCityName(item.base.label,item.values.readonlyFlag)"
                                v-if="item.values.field === 'salesCity'"
                                :suffixIcon="item.values.readonlyFlag === 'true'?'':'mp-arrow-right'"></link-input>
                    <!--销售区县-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                @tap="pickSalesDistrictName(item.base.label,item.values.readonlyFlag)"
                                v-if="item.values.field === 'salesDistrict'"
                                :suffixIcon="item.values.readonlyFlag === 'true'?'':'mp-arrow-right'"></link-input>
                    <!--购买产品-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                @tap="pickProductName(item.base.label,item.values.readonlyFlag)"
                                v-if="item.values.field === 'productName'"
                                :suffixIcon="item.values.readonlyFlag === 'true'?'':'mp-arrow-right'"></link-input>
                    <!--活动目标人群-->
                    <link-lov v-model="option.formData[item.values.field]"
                              :type="item.values.lovType"
                              @picker-change="pickTargetPopulationChange"
                              v-if="item.values.field === 'targetPopulation'"></link-lov>
                    <!--                    忠诚度 lytProgramOption-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                v-if="item.values.field === 'programName' && option.formData.targetPopulation === 'Member'"
                                suffixIcon="mp-arrow-right"></link-input>
                    <!--                    会员等级 memberTierOption-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                @tap="pickMemberTier(item.base.label)" v-if="item.values.field === 'memberTier' && option.formData.targetPopulation === 'Member'"
                                suffixIcon="mp-arrow-right"></link-input>
                    <!--                    邀约人 inviterOption-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                @tap="pickInviterId(item.base.label)" v-if="item.values.field === 'inviterName' && option.formData.targetPopulation === 'Member'"
                                suffixIcon="mp-arrow-right"></link-input>
                </link-form-item>
                <!-- 协议详情 -->
                <view class="agree-item" @tap="agreeDialogFlag = true">
                    <agree-item :data="agreementItem" source="newActivity" v-if="option.formData.protocolId && item.ctrlCode === 'agree-detail'"/>
                </view>
                <!--执行反馈时【执行案编码】-->
                <link-form-item :key="index" :label="item.base.label"
                                :readonly="item.values.readonlyFlag === 'true' ? true : false"
                                :required="item.base.require"
                                v-if="item.ctrlCode !== 'view-line' && item.ctrlCode !== 'view-blank'
                                && pageSource === 'executiveFeedback'&& item.values.field === 'exeCaseCode'">
                    <!--执行案编码字段 受eidtExeCaseCodeFlag（具体逻辑查看下面赋值逻辑备注）
                    releaseFeeFlag ：是否释放申请费用占用执行案明细的金额。Y的时候 不允许编辑
                    情景下是否可以编辑执行案编码控制-->
                    <link-input v-model="option.formData[item.values.field]" inputReadonly
                                v-if="item.values.field === 'exeCaseCode' && eidtExeCaseCodeFlag"
                                @tap="pickExeCaseCode(item.base.label,'actual')"
                                :suffixIcon="option.formData.releaseFeeFlag === 'Y'?'':'mp-arrow-right'"></link-input>
                    <link-input v-model="option.formData[item.values.field]" readonly
                                v-if="item.values.field === 'exeCaseCode' && !eidtExeCaseCodeFlag"></link-input>
                </link-form-item>
            </view>
        </link-form>
        <link-sticky>
            <!--pageSource为空时是新建活动的情况 其余场景编辑基础信息时pageSource都有值-->
            <link-button block mode="stroke" @tap="lastStep">上一步</link-button>
            <!--活动状态=新建，审批状态=未提交或者已拒绝时可以创建名单-->
            <link-button block @tap="createdActivityListReport"
                         v-if="option.formData.status === 'New' && (option.formData.aproStatus === 'Refused')">创建名单</link-button>
            <link-button block @tap="nextStep('next')">{{ $utils.isEmpty(pageSource) ? '下一步' : '保存' }}</link-button>
        </link-sticky>
        <link-dialog ref="updateExeCase">
            <view slot="head">
                提示
            </view>
            <view>
                {{ applyDailogText }}
            </view>
            <link-button slot="foot" @tap="$refs.updateExeCase.hide()">取消</link-button>
            <link-button slot="foot" @tap="associated">确认切换</link-button>
        </link-dialog>
        <link-dialog ref="clearExeCaseCode">
            <view slot="head">
                提示
            </view>
            <view>
                清除执行案编码，将同步清掉该活动下已关联了相关执行案明细的费用申请明细及活动上关联信息，是否确定清空执行案编码？
            </view>
            <link-button slot="foot" @tap="$refs.clearExeCaseCode.hide()">取消</link-button>
            <link-button slot="foot" @tap="clearFee()">确认</link-button>
        </link-dialog>
        <link-dialog ref="updateExeCaseForActualFee">
            <view slot="head">
                提示
            </view>
            <view>
                更新执行案编码，费用实际已关联的执行案明细信息将被清空，需重新关联，请确认！
            </view>
            <link-button slot="foot" @tap="$refs.updateExeCaseForActualFee.hide()">取消</link-button>
            <link-button slot="foot" @tap="associatedForActualFee">确认切换</link-button>
        </link-dialog>
        <link-dialog ref="udfBackDialog">
            <view slot="head">
                提示
            </view>
            <view>
                即将离开页面，是否保存信息？
            </view>
            <link-button slot="foot" @tap="directReturn">否</link-button>
            <link-button slot="foot" @tap="nextStep('back')">是</link-button>
        </link-dialog>
        <link-dialog ref="salesAddressConfirmDialog">
            <view slot="head">
                提示
            </view>
            <view>
                进入下一步后，销售大区/片区/城市/区县将不可再修改，是否确认？
            </view>
            <link-button slot="foot" @tap="$refs.salesAddressConfirmDialog.hide()">取消</link-button>
            <link-button slot="foot" @tap="confirmSave">确认</link-button>
        </link-dialog>
        <link-dialog ref="cacheOthersOperatedInfoDialog" disabledHideOnClickMask title="提示">
            {{cacheOthersOperatedInfoMsg}}
            <link-button slot="foot" @tap="closeCurrentPage">确定</link-button>
        </link-dialog>
        <link-dialog ref="timeTipDialog" title="提示" disabledHideOnClickMask>
            {{timeTip}}
            <link-button slot="foot" @tap="changeTime">确定</link-button>
        </link-dialog>
        <link-dialog ref="memberTierOptionDialog"
                     :noPadding="true"
                     v-model="memberTierDialogFlag"
                     position="bottom"
                     borderRadius="32rpx 32rpx 0 0">
            <view class="model-title">
                <view class="title">会员等级</view>
                <view class="iconfont icon-close" @tap="memberTierDialogFlag = false"></view>
            </view>
            <scroll-view scroll-y="true">
                <view class="select-box" v-for="(item, index) in memberTierData" :key="index" @tap="selectMemberTier(item)">
                    <view class="select-right">
                        <view class="iconfont icon-yiwanchengbuzhou" v-if="item.selectedFlag"></view>
                        <view class="iconfont icon-circle-outline" v-else></view>
                    </view>
                    <view class="select-left">
                        <view class="select-row">{{item.tierName}}</view>
                    </view>
                </view>
                <view class="blank"></view>
            </scroll-view>
            <link-sticky class="bottom-btn">
                <view class="all-select" @tap="allSelect">
                    <view class="iconfont icon-yiwanchengbuzhou" v-if="allSelectFlag"></view>
                    <view class="iconfont icon-circle-outline" v-else></view>
                    <view class="all-select-text">全选</view>
                </view>
                <link-button class="sure-btn" size="normal" @tap="sureAddMemberTier" autoLoading>确定</link-button>
            </link-sticky>
        </link-dialog>
        <link-dialog ref="activityListReportDialog" disabledHideOnClickMask
                     borderRadius="32rpx 32rpx 0 0">
            <view slot="head">
                名单提报名额调整
            </view>
            <scroll-view scroll-y="true">
                <view v-for="(item, index) in actListOwnerData" :key="index">
                    <view style="width: 100%;height: 28px;line-height: 28px">
                        <view style="width: 50%;float: left">{{item.firstName}}</view>
                        <view style="width: 50%;float: left">
                            <link-number-keyboard v-model="item.submitPlaces" placeholder="请输入提报名额(人)"></link-number-keyboard>
                        </view>
                    </view>
                </view>
            </scroll-view>
            <link-button slot="foot" @tap="$refs.activityListReportDialog.hide()" autoLoading>取消</link-button>
            <link-button slot="foot" @tap="saveSubmitPlaces " autoLoading>保存</link-button>
            <link-button slot="foot" @tap="saveIssueSubmitPlaces" autoLoading>保存并下发</link-button>
        </link-dialog>

        <!-- 协议详情 -->
        <agree-detail :show.sync="agreeDialogFlag" :agreementItem="agreementItem" :agreeProdList="agreeProdList"/>
        <!-- 陈列采集详情 -->
        <exhibit-collect-detail :show.sync="collectDialogFlag" :data="collectItem"/>
    </link-page>
</template>

<script lang="jsx">
import {ROW_STATUS} from "../../../utils/constant";
import NewActivityBasicJsxComponent from "./new-activity-basic-jsx-component";
import {ComponentUtils, DateService, FilterService, LovService} from "link-taro-component";
import MaNavigationBar from "../ma-navigation-bar/ma-navigation-bar";
import {PageCacheManager} from "../../../utils/PageCacheManager";
import Taro from "@tarojs/taro";
import {env} from "../../../../env";
import AgreeItem from '../../terminal/terminal/components/agree-item';
import AgreeDetail from '../../terminal2/display-check/components/agree-detail';
import ExhibitCollectDetail from '../../terminal2/history-display/components/exhibit-collect-detail';

export default {
    name: "new-activity-basic-page",
    components: {
        MaNavigationBar,
        NewActivityBasicJsxComponent,
        AgreeItem,
        AgreeDetail,
        ExhibitCollectDetail
    },
    data() {
        const pageFrom = this.pageParam.pageFrom;//页面来源 ： 从basic-info组件编辑【basicInfo】还是其他
        const activityItem = this.pageParam.data;
        const editSubControlList = this.pageParam.editSubControlList;//基础信息-编辑子组件
        const pageSource = this.pageParam.pageSource;//页面来源
        //场景来源服务于自定义导航栏。
        //1、市场活动新建 newMarketActivity 2、执行案新建市场活动 caseNewMarketActivity 3、其他 other
        let sceneSourceForNavigation = "other";//默认other
        if (!this.$utils.isEmpty(this.pageParam.sceneSourceForNavigation)) {
            sceneSourceForNavigation = this.pageParam.sceneSourceForNavigation;
        }
        const config = {
            data: {
                ...this.pageParam.data
            },
        };
        const option = new this.FormOption(this, {
            ...config,
            operator: 'NEW'
        });
        const maintenanceModules = this.pageParam.maintenanceModules;//新建活动配置的需要维护的模块-用于判别是否配置了互动模块，控制界面下一步跳转页面
        let eidtExeCaseCodeFlag = false;//是否可以编辑执行案编码
        /** 1、页面来源 executiveFeedback 执行反馈
         * 2、活动状态 MC_STATUS : 进行中、已结束
         * 3、审批状态 APRO_STATUS : 申请审批通过、反馈驳回 的活动能够编辑；
         * 4、当前活动下的申请费用没有关联执行案明细
         * 备注：1234为且的关系
         *  2021-04-19更新对活动状态=已发布，活动审批状态=申请审批通过的数据放开权限
         */
        if (pageSource === 'executiveFeedback' && (activityItem.status === 'Processing' || activityItem.status === 'Closed' ||activityItem.status === 'Published')
            && (activityItem.aproStatus === 'Approve' || activityItem.aproStatus === 'Refeedback')
        ) {
            eidtExeCaseCodeFlag = true;
        }
        const scene = 'apply';//申请费用
        const chooseCaseData = {};//选择的执行案数据
        const cfgPropertyObj = {};//企业参数配置 片区组织ID 相关信息
        const applyDailogText = "";
        let cacheTitle = "市场活动-基础信息";
        if(pageSource === 'executiveFeedback'){
            cacheTitle = "执行反馈-基础信息";
        }
        let costListData = {
            prodPayList: [],
            cashPayList: [],
        };
        const cacheData = PageCacheManager.getInitialData({
            ctx: this,
            path: 'lj-market-activity/market-activity/new-activity-basic-page.vue',
            title: cacheTitle,
            initialData: {
                option,
                costListData: costListData,
            },
        })
        if (cacheData.costListData !== costListData) {
            costListData = {...cacheData.costListData}
        }
        const busSceneOld = "";//业务场景-用途：执行反馈环境更新了业务场景后重新查询需要上传的照片模板
        const agreementOauth = this.$utils.isPostnOauth() === 'MY_POSTN'? 'MULTI_POSTN': this.$utils.isPostnOauth();//查询协议安全性调整
        const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
        return {
            cacheData, // 缓存的数据
            agreeDialogFlag: false, // 协议详情弹窗是否显示
            collectDialogFlag: false, // 陈列采集记录弹窗是否显示
            agreementItem: {}, // 协议内容
            agreeProdList: [], // 协议-产品业务小类
            collectItem: {}, // 陈列采集
            matchTypeColor: { // 协议有效性状态显示颜色
                Y: '#2F68F7', // 有效-蓝色
                N: '#FF0000' // 无效-红色
            },
            prodLargeClassView: '',
            displayItemView: '',
            timer: null,   // 计时器
            changeTimeFlag: true, //时间改变控制返回
            scType: '', //场景类型
            timeTip: '', //互动时间提示内容
            initStartTime: '', //用于保存开始时间
            initEndTime: '',//用于保存结束时间
            addArr: [],//会员等级要添加的数据
            deleteArr: [],//会员等级要删除的数据
            allSelectFlag: false,//会员等级全选
            memberTierData: [],//会员等级数据
            memberTierDialogFlag: false, //会员等级弹框
            actListOwnerData: [],//活动对接人数据
            cacheOthersOperatedInfoMsg:"",
            costListData,
            userInfo,//用户信息
            // 费用价格是否展示标识
            priceShowFlag: false,
            applyDailogText,
            navigationBarTitle: '基础信息',
            navigationBarTitleColor: '#ffffff',
            navBackgroundColor: 'transparent',
            zIndex: ComponentUtils.nextIndex(),
            sceneSourceForNavigation,
            cfgPropertyObj,
            pageFrom,
            displayItemName: [],
            chooseCaseData,
            scene,
            eidtExeCaseCodeFlag,
            maintenanceModules,
            pageSource,
            editSubControlList,
            activityItem,
            fieldRows: [],
            option,
            formRules: {},
            /*缓存的值列表数据*/
            cacheLovs: {
                /*陈列品项大类值列表类型*/
                PROD_L_CLASS: null,
                /*陈列品相子类值列表类型*/
                PROD_M_CLASS: null,
            },
            /*
            * 查询当前活动 关联过执行案费用 的 实际费用数据
            * 用于控制切换执行案时判断 若 actualList 不为空
            * 提示语【更新执行案编号，费用实际已关联的执行案明细信息将被清空，需重新关联，请确认！】
            * */
            actualList: [],
            /*
            * 查询当前活动 关联过执行案费用 的 申请费用数据
            * 用于控制是否允许重新选择执行案 若 applyList 不为空 则不能
            * */
            applyList: [],
            salesAddressConfirmFlag: false,//销售地址确认框
            noSubAccountOrgCode : 'noMatchId',
            onlySaveDataFlag: false,//仅仅保存数据，不跳转到下一页面，只用于创建名单时。
            busSceneOld,
            excludebusSceneData:this.pageParam.excludebusSceneData,//业务场景需要排除的数据
            removeBusSceneMsg:"删除业务场景将影响已上传的照片查看，是否确认删除？",//清除业务场景时的提示对象.
            enterpriseOption: new this.AutoList(this, {
                module: '/action/link/company',
                searchFields: ['companyName'],
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} arrow="false">
                            <view style="width: 100%;padding: 5px 0 0 14px" slot="note">
                                <view style="font-family: PingFangSC-Semibold;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 16px;margin-bottom: 5px;">
                                    {data.creditCode}
                                </view>
                                <view style="font-family: PingFangSC-Regular;font-size: 15px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;line-height: 16px;margin-bottom: 5px">
                                    {data.companyName}
                                </view>
                            </view>
                        </item>
                    )
                }
            }),
            //活动类型
            mcTypeOption: new this.AutoList(this, {
                module: 'action/link/basic',
                param: {
                    filtersRaw: [{
                        id: 'type',
                        property: 'type',
                        operator: '=',
                        value: 'MC_TYPE'
                    }, {
                        id: 'activeFlag',
                        property: 'activeFlag',
                        operator: '=',
                        value: 'Y'
                    }]
                },
                searchFields: ['name'],
                sortOptions: null,
                hooks: {
                    beforeLoad(option) {
                        option.param.order = 'asc';
                        option.param.sort = "seq";
                    },
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} arrow="false">
                            <view
                                style="display: -webkit-box;display: -ms-flexbox;display: flex;padding: 12px 8px 12px 12px;width:100%">
                                <view
                                    style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px;">
                                    {data.name}
                                </view>
                            </view>
                        </item>
                    )
                }
            }),
            //执行案
            caseOption: new this.AutoList(this, {
                module: 'action/link/actProg',
                sortOptions: null,
                searchFields: ['executionCode', 'executionName'],
                param: {
                    // EXECUTION_STATE 执行案状态 全部生效和部分生效
                    // attr5    当前时间是否在 执行案有效结束时间+5天的范围内  参数Y
                    // attr6    当前时间是否在执行案有效结束时间内  参数filterTime
                    attr6: '',
                    attr5: '',
                    filtersRaw: [
                        {
                            id: 'executionState',
                            property: 'executionState',
                            value: '[AllEfficient,PartEfficient]',
                            operator: 'in'
                        },
                    ]
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item arrow="false"
                              key={index}
                              data={data}>
                            <view
                                style="width: 100%;background: white;height: 105px;"
                                slot='note'>
                                <view style="width: 90%;float: left;">
                                    <view style="display: flex;margin: auto;">
                                        <view
                                            style="-ms-flex-align: center;-webkit-align-items: center;align-items: center;-ms-flex-pack: justify;-webkit-justify-content: space-between;  justify-content: space-between;height: 80rpx;line-height: 80rpx;padding-left: 32rpx;display: flex;width:100%;">
                                            <view
                                                style="background: #A6B4C7;border-radius: 8rpx;line-height: 40rpx;">
                                                <view
                                                    style="font-size: 28rpx;color: #FFFFFF;letter-spacing: 0;line-height: 40rpx;padding: 2rpx 8rpx;">{data.executionCode}
                                                </view>
                                            </view>
                                        </view>
                                    </view>
                                    <view
                                        style=" -ms-flex-align: center;-webkit-align-items: center;align-items: center;-ms-flex-pack: justify;-webkit-justify-content: space-between;justify-content: space-between;height: 80rpx;line-height: 80rpx;padding-left: 32rpx;display: flex;width: 100%;">
                                        <view
                                            style="font-family: PingFangSC-Semibold;font-size: 32rpx;color: #262626;letter-spacing: 0;line-height: 32rpx;">{data.executionName}
                                        </view>
                                    </view>
                                    <view style="padding-left: 32rpx;">
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 28rpx;color: #8C8C8C;letter-spacing: 0;line-height: 28rpx;float: left;width:50%">
                                            <view style="float:left;padding-right:5px"> {this.priceShowFlag?`可用余额`:''}</view>
                                            <view style="color: #000000"> {this.priceShowFlag?data.availableBalance:''}</view>
                                        </view>
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 28rpx;color: #8C8C8C;letter-spacing: 0;text-align: left;line-height: 28rpx;">
                                            <view style="float:left;padding-right:5px">{this.priceShowFlag?`审批金额`:''}</view>
                                            <view style="color: #000000">{this.priceShowFlag?data.approvalAmount:''}</view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </item>
                    )
                },
            }),
            //子公司／经销商
            //查询客户表上客户大类【ACCT_TYPE】=【Dealer】经销商
            actExecutivesOption: new this.AutoList(this, {
                url: {
                    queryByExamplePage: 'action/link/accnt/querySunCompByCityOrgPage'
                },
                param: {
                    filtersRaw: [
                        //状态
                        {
                            id: 'acctStatus',
                            property: 'acctStatus',
                            value: 'Y',
                            operator: '='
                        },
                    ],
                    attr3: 'Dealer'
                },
                sortOptions: null,
                searchFields: ['billTitle', 'acctName'],
                hooks: {
                    beforeLoad(option) {
                        delete option.param.order;
                        delete option.param.sort;
                    },
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item arrow="false"
                              key={index}
                              data={data}>
                            <view style="width: 100%;padding: 5px 0 0 14px" slot='note'>
                                <view style="margin-bottom: 5px">
                                    <view
                                        style="font-family: PingFangSC-Semibold;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 16px;float:left">
                                        {data.billTitle}
                                    </view>
                                    <view
                                        style="font-family: PingFangSC-Regular;font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;line-height: 16px;padding-left: 5px;float: left;">
                                        {data.mobilePhone}
                                    </view>
                                </view>
                                <view style="color: gray;clear:both;padding-top:10px">
                                    <view
                                        style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px">
                                        {LovService.filter(data.acctType, 'ACCT_TYPE')}-{LovService.filter(data.channelManageMode, 'CHANNEL_MANAGE_MODE')}
                                    </view>
                                </view>
                                <view style="color: gray;clear:both;padding-top:10px">
                                    <view
                                        style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px">
                                        {data.addrDetailAddr}
                                    </view>
                                </view>
                            </view>
                        </item>
                    )
                }
            }),
            //受益对象
            //查询客户表上客户大类【ACCT_TYPE】=【Dealer】经销商&【Distributor】 分销商&【Terminal】  终端的数据
            beneficiaryOption: new this.AutoList(this, {
                url: {
                    queryByExamplePage: 'export/link/accnt/queryAccntByCityOrgPage'
                },
                param: {
                    filtersRaw: [
                        //状态
                        {
                            id: 'acctStatus',
                            property: 'acctStatus',
                            value: 'Y',
                            operator: '='
                        },
                    ],
                    limitAccntFlag: 'Y', // 大成浓香公司对市场活动选择“受益客户”时的需求为：“受益客户”只能选到有效且已认证的终端
                    attr2: option.formData.salesCityId,
                    attr3: 'Dealer,Terminal,Distributor'
                },
                sortOptions: null,
                searchFields: ['billTitle', 'acctName'],
                hooks: {
                    beforeLoad(option) {
                        delete option.param.order;
                        delete option.param.sort;
                    },
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item arrow="false"
                              key={index}
                              data={data}>
                            <view style="/* width: 100% */;padding: 5px 0 0 14px" slot='note'>
                                <view style="margin-bottom: 5px">
                                    <view
                                        style="font-family: PingFangSC-Semibold;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 16px;float:left">
                                        客户名称 : {data.acctName}
                                    </view>
                                    <view
                                        style="font-family: PingFangSC-Regular;font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;line-height: 16px;padding-left: 5px;float: left;">
                                        {data.mobilePhone}
                                    </view>
                                </view>
                                <view style="color: gray;clear:both;padding-top:10px">
                                    <view
                                        style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px">
                                        {LovService.filter(data.acctType, 'ACCT_TYPE')} {LovService.filter(data.channelManageMode, 'CHANNEL_MANAGE_MODE')} {LovService.filter(data.acctStage, 'TERMINAL_STATUS')}
                                    </view>
                                </view>
                                <view style="color: gray;clear:both;padding-top:10px">
                                    <view
                                        style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px">
                                        营业执照名称 : {data.billTitle}
                                    </view>
                                </view>
                                <view style="color: gray;clear:both;padding-top:10px">
                                    <view
                                        style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 18px">
                                        {data.acctType === 'Terminal' ? data.addrDetailAddr : data.billDetailAddr}
                                    </view>
                                </view>
                            </view>
                        </item>
                    )
                }
            }),
            //协议
            protocolOption: new this.AutoList(this, {
                url: {
                    queryByExamplePage: 'action/link/agreement/queryByExamplePage'
                },
                param: {
                    // oauth: agreementOauth,//'MY_POSTN_ONLY',
                    filtersRaw: [],
                },
                sortOptions: null,
                searchFields: ['agrName'],
                hooks: {
                    beforeLoad({param}) {
                        param.filtersRaw = [
                            ...param.filtersRaw,
                            {id: 'agrType', property: 'agrType', value: '[display,storeSign,Cabinet,others]', operator: 'IN'},
                            {id: 'accntId', property: 'accntId', value: this.option.formData.beneficiaryId},
                        ]
                    }
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item arrow={false} key={index} data={data}>
                            <view style="width: 100%;background: white; display: flex;" slot='note'>
                                {data.agrPicKey
                                    ? <image style="margin-right: 16rpx;width: 128rpx;height: 128rpx;" src={env.cosUploadUrl + data.agrPicKey}></image>
                                    : <image style="margin-right: 16rpx;width: 128rpx;height: 128rpx;" src={this.$imageAssets.terminalDefaultImage}></image>
                                }
                                <view style="flex: 1; color: #000;">
                                    <view style="display: flex;justify-content: space-between;margin-bottom: 20rpx;">
                                        <view style="flex: 1;overflow: hidden;font-weight: 600;margin-right: 22rpx;white-space: nowrap;">
                                            {data.agrNumber ? <text>{data.agrNumber}</text> : ''}
                                            {data.agrNumber && data.agrName ? <text style="margin: 0 8rpx;">—</text> : ''}
                                            <text>{data.agrName}</text>
                                        </view>
                                        <view style={"border-radius: 4rpx;transform: skewX(-30deg);margin-right: 10rpx;max-height: 38rpx;background:" + this.matchTypeColor[data.isEffective]}>
                                            <view style="font-size: 22rpx;color: #FFFFFF;letter-spacing: 2rpx;text-align: center;padding: 4rpx 8rpx;transform: skewX(30deg);">
                                                {data.isEffective === 'Y' ? '有效' : '无效'}
                                            </view>
                                        </view>
                                    </view>
                                    <view style="display: flex;justify-content: space-between;margin-bottom: 20rpx;">
                                        <view className="item-left">
                                            <view className="item-right">
                                                <text style="padding: 0 10rpx;font-size: 22rpx;color: #3F66EF;line-height: 36rpx;background: #F0F5FF;border-radius: 4rpx;text-align: center;margin-right: 16rpx;">{LovService.filter(data.agrType, 'AGR_TYPE')}</text>
                                                <text style="padding: 0 10rpx;font-size: 22rpx;color: #FF461E;line-height: 36rpx;background: #FFF1EB;border-radius: 4rpx;text-align: center;margin-right: 16rpx;">{LovService.filter(data.agrStatus, 'AGR_STATUS')}</text>
                                            </view>
                                        </view>
                                    </view>
                                    {data.agrType === 'storeSign' ?
                                        <view style="display: flex;justify-content: space-between;margin-bottom: 20rpx;">
                                            <view className="item-left">
                                                <link-icon icon="icon-hexinzhongduan" style="font-size: 30rpx;margin-right: 8rpx;"/>
                                                <text className="store-title">{data.signBoard}</text>
                                            </view>
                                        </view> : ''}
                                    <view style="display: flex;justify-content: space-between;margin-bottom: 20rpx;">
                                        <view className="item-left">
                                            <link-icon icon="icon-index" style="font-size: 30rpx;margin-right: 8rpx;"/>
                                            <text>{DateService.filter(data.startTime, 'YYYY/MM/DD')}</text>
                                            <text style="margin: 0 8px;">至</text>
                                            <text>{DateService.filter(data.endTime, 'YYYY/MM/DD')}</text>
                                        </view>
                                        <view className="item-right">{data.createdName}</view>
                                    </view>
                                </view>
                            </view>
                        </item>
                    )
                },
                // @ts-ignore
                slots: {
                    other: (h) => (<link-fab-button onTap={this.onTapCreateProtocolButton} icon="mp-plus"/>)
                }
            }),
            //宴席主家
            masterOption: new this.AutoList(this, {
                module: this.$env.appURL + '/action/link/consumer',
                url: {
                    queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/consumerListSend'
                },
                param: {
                    rows: 25,
                    filtersRaw: [
                        {id: 'companyId', property: 'companyId', value: userInfo.coreOrganizationTile['l3Id'], operator: '='},
                        {id: 'consumerType', property: 'consumerType', value: 'ChannelConsumer', operator: '='},
                        {id: 'accntChannel', property: 'accntChannel', value: 'MarketingPlatform', operator: '='},
                        {id: 'empFlag', property: 'empFlag', value: 'N', operator: '='},
                        {id: 'followFlag', property: 'followFlag', value: 'Y', operator: '='}
                    ],
                    oauth: 'MY_POSTN_ONLY',
                },
                sortOptions: null,
                exactSearchFields: [{
                    field: 'acctName',
                    showValue: '消费者姓名', // 展示名,用于显示的字段=
                    exactSearch: true,
                    searchOnChange: true,
                    clearOnChange: true
                }, {
                    field: 'mobilePhone1',
                    showValue: '消费者手机号', // 展示名,用于显示的字段=
                    exactSearch: true,
                    searchOnChange: true,
                    clearOnChange: true
                }],
                filterOption: [
                    {label: '消费者姓名', field: 'acctName', type: 'text'},
                    {label: '消费者手机号', field: 'mobilePhone1', type: 'text'},
                    {label: '客户性别', field: 'gender', type: 'lov', lov: 'GENDER'}
                ],
                searchFields: ['acctName', 'mobilePhone1'],
                hooks: {
                    beforeLoad (option) {
                        for (let i = 0; i < option.param.filtersRaw.length; i++) {
                            if (option.param.filtersRaw[i].property === 'acctName') {
                                option.param.filtersRaw[i].operator = 'like';
                            }
                        }
                    }
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} arrow={false} style="margin: 12px;border-radius: 8px; position: relative;overflow: hidden;padding: 20px 14px 14px 14px;">
                            <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                            <view style="width: 100%;">
                                <view style="width: 100%;font-size: 16px;color: #212223;line-height: 24px;font-weight: 600;margin-bottom: 8px;">{data.acctName}</view>
                                <view style={"background: #2F69F8;color: #fff;transform: skew(30deg, 0);display: flex;border-bottom-left-radius: 7px;position: absolute;right: -5px;top: 0;"}>
                                    <text style={"font-size: 12px;transform: skew(-30deg, 0);padding: 4px 16px;"}>{data.fstName}跟进</text>
                                </view>

                                <view style="display: flex;margin-bottom: 8px;">
                                    <view style="padding:0 5px;font-size: 11px;font-weight: 400;color: #3F66EF;line-height: 18px;background: #F0F5FF;border-radius: 2px;text-align: center; margin-right: 8px;">{LovService.filter(data.subAcctType, 'ACCT_SUB_TYPE')}</view>
                                    <view style="padding:0 5px;font-size: 11px;font-weight: 400;color: #3F66EF;line-height: 18px;background: #F0F5FF;border-radius: 2px;text-align: center; margin-right: 8px;">{ LovService.filter(data.loyaltyLevel, 'ACCT_MEMBER_LEVEL') }</view>
                                    {data.impFlag === 'Y' ? <view style="padding:0 5px;font-size: 11px;font-weight: 400;color: #FF461E;line-height: 18px;background: #FFF1EB;border-radius: 2px;text-align: center; margin-right: 8px;">重点客户</view> : ''}
                                </view>
                                <view style="width: 100%;font-size: 14px;display: flex;color: #317DF7;margin-bottom: 4px;line-height: 22px;">
                                    <view style="color: #999999;width: 66px;">联系方式</view>
                                    <view> {data.mobilePhone1}</view>
                                </view>
                                <view style="width: 100%;font-size: 14px;display: flex;color: #333333;margin-bottom: 4px;line-height: 22px;">
                                    <view style="color: #999999; width:66px;">单位</view>
                                    <view> {data.company}</view>
                                </view>
                                <view style="width: 100%;font-size: 14px;display: flex;color: #333333;margin-bottom: 4px;line-height: 22px;">
                                    <view style="color: #999999;width: 66px;">职务</view>
                                    <view> {data.position}</view>
                                </view>
                                <view style="width: 100%;font-size: 14px;display: flex;color: #333333;line-height: 22px;">
                                    <view style="color: #999999;width: 66px;">所属客户</view>
                                    <view> {data.belongToStore}</view>
                                </view>
                            </view>
                        </item>
                    )
                },
                slots: {
                    other: () => <link-fab-button onTap={this.addConsumer}></link-fab-button>,
                    filterGroup: () => (
                        <link-filter-group>
                            <link-filter-item label="创建时间(升序)" param={{sort: {field: 'created', desc: false}}}/>
                            <link-filter-item label="最近更新(升序)" param={{sort: {field: 'lastUpdated', desc: false}}}/>
                            <view  onTap={this.chooseStoreList}
                                   style={this.isChosen ? 'padding: 8rpx 16rpx;'+
                                       'margin-right: 8rpx;' +
                                       'white-space: nowrap;' +
                                       'display: inline-block;' +
                                       'background: #EDF3FF;' +
                                       'color: #2F69F8;'+
                                       'border-radius: 4rpx;':'padding: 8rpx 16rpx;'+
                                       'margin-right: 8rpx;' +
                                       'white-space: nowrap;' +
                                       'display: inline-block;' +
                                       'background-color: #f2f2f2;' +
                                       'color: #333333;' +
                                       'border-radius: 4rpx;'}
                            >所属客户</view>
                        </link-filter-group>
                    )
                }
            }),
            customerOption: new this.AutoList(this, {
                module: this.$env.appURL + '/action/link/accnt',
                url: {
                    queryByExamplePage: this.$env.appURL + '/link/interCustTerminal/queryAccntPage'
                },
                param: {
                    postnId: '',
                    oauth: 'ALL',
                },
                searchFields: ['acctName'],
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} className="select-box" arrow="false">
                            <link-checkbox val={data.accntId} toggleOnClickItem slot="thumb"></link-checkbox>
                            <view slot="title" style="display: flex;">{data.acctName} <view style="margin-left: 1em;background: #EDF3FF;color: #2F69F8;border: 1px solid #EDF3FF;font-size:12px;padding: 3px;border-radius: 3px;">{LovService.filter(data.acctType, 'ACCT_TYPE')}</view></view>
                            <view slot="note">{data.province}{data.city}{data.district}{data.address}</view>
                        </item>)
                },
                hooks: {
                    beforeLoad (option) {
                        delete option.param.order;
                        delete option.param.sort;
                        option.param.postnId = this.userInfo.postnId;
                    }
                }
            }),
            isChosen: false,
            //活动场地
            activityVenueOption: new this.AutoList(this, {
                url: {
                    queryByExamplePage: 'export/link/accnt/queryAccntByCityOrgPage'
                },
                param: {
                    filtersRaw: [
                        //是否为活动场地
                        {id: 'appreciationFlag', property: 'appreciationFlag', value: 'Y', operator: '='},
                        {id: 'acctStatus', property: 'acctStatus', value: 'Y', operator: '='},
                    ],
                    attr2: option.formData.salesCityId
                },
                sortOptions: null,
                searchFields: ['acctName'],
                hooks: {
                    beforeLoad(option) {
                        delete option.param.order;
                        delete option.param.sort;
                    },
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item arrow="false"
                              key={index}
                              data={data}>
                            <view style="width: 100%;padding: 5px 0 0 14px" slot='note'>
                                <view style="margin-bottom: 5px">
                                    <view
                                        style="font-family: PingFangSC-Semibold;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 16px;float:left">
                                        {data.acctName}
                                    </view>
                                    <view
                                        style="font-family: PingFangSC-Regular;font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;line-height: 16px;padding-left: 5px;float: left;">
                                        {data.mobilePhone}
                                    </view>
                                </view>
                                <view style="color: gray;clear:both;padding-top:10px">
                                    <view
                                        style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px">
                                        {data.addrDetailAddr}
                                    </view>
                                </view>
                                { !data.latitude || !data.longitude ? (
                                <view style="padding-top:10px;display:flex;align-items:center;">
                                    <view>
                                        <link-icon icon="mp-info-lite" status="info"/>
                                    </view>
                                    <view
                                        style="font-family: PingFangSC-Regular;font-size: 12px;color: #262626;letter-spacing: 0;line-height: 12px">
                                        缺失经纬度，请完善
                                    </view>
                                </view>):''}
                            </view>
                        </item>
                    )
                },
                // @ts-ignore
                slots: {
                    other: (h) => (<link-fab-button onTap={this.onTapCreateVenueButton} icon="mp-plus"/>)
                }
            }),
            //供应商
            supplierOption: new this.AutoList(this, {
                module: null,
                url: {
                    queryByExamplePage: 'export/link/accnt/queryAccntByCityOrgPage'
                },
                param: {
                    filtersRaw: [
                        {
                            "id": "acctType",
                            "property": "acctType",
                            "value": "Supplier"
                        },//状态
                        {
                            id: 'acctStatus',
                            property: 'acctStatus',
                            value: 'Y',
                            operator: '='
                        },],
                    attr2: option.formData.salesCityId,
                    isCompanyCostFlag: 'N',
                    acctType: 'Supplier'
                },
                sortOptions: null,
                searchFields: ['acctName'],
                hooks: {
                    beforeLoad(option) {
                        delete option.param.order;
                        delete option.param.sort;
                    },
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item arrow="false"
                              key={index}
                              data={data}>
                            <view style="width: 100%;padding: 5px 0 0 14px" slot='note'>
                                <view style="margin-bottom: 5px">
                                    <view
                                        style="font-family: PingFangSC-Semibold;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 16px;float:left">
                                        {data.acctName}
                                    </view>
                                    <view
                                        style="font-family: PingFangSC-Regular;font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;line-height: 16px;padding-left: 5px;float: left;">
                                        {data.mobilePhone}
                                    </view>
                                </view>
                                <view style="color: gray;clear:both;padding-top:10px">
                                    <view
                                        style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px">
                                        {data.billDetailAddr}
                                    </view>
                                </view>
                            </view>
                        </item>
                    )
                },
            }),
            //宴席推荐客户。2021-02-25增加查询经销商、分销商的数据
            targetTerminalOption: new this.AutoList(this, {
                module: null,
                url: {
                    queryByExamplePage: 'action/link/accnt/queryFollowUpTerminal'
                },
                searchFields: ['acctName'],
                sortOptions: null,
                param: {
                    attr1: option.formData.salesCityId,
                    attr3: 'Dealer,Terminal,Distributor'
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} arrow="false">
                            <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                            <view
                                style="display: -webkit-box;display: -ms-flexbox;display: flex;padding: 12px 8px 12px 12px;width:100%">
                                <image
                                    style="box-shadow: 0 2px 15px 0 rgba(0, 44, 152, 0.22);border-radius: 8px;width: 64px;height: 64px;overflow: hidden;"
                                    src={data.storeUrl}></image>
                                <view style="width: 80%;">
                                    <view
                                        style="display: -webkit-box;display: -ms-flexbox;display: flex;-webkit-box-pack: start;-ms-flex-pack: start;justify-content: flex-start;-webkit-box-align: center;-ms-flex-align: center;align-items: center;-webkit-box-pack: justify;-ms-flex-pack: justify;justify-content: space-between;margin-left: 12px;">
                                        <view
                                            style="font-family: PingFangSC-Semibold, serif;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 16px;">
                                            {data.acctType === 'Terminal' ? data.acctName : data.billTitle} </view>
                                        <view
                                            style="margin-right: -3px;width: 60px;height: 22px;">
                                            {LovService.filter(data.acctStage, 'TERMINAL_STATUS')}
                                        </view>
                                    </view>
                                    <view
                                        style="display: -webkit-box;display: -ms-flexbox;display: flex;-webkit-box-pack: start;-ms-flex-pack: start;justify-content: flex-start;-webkit-box-align: center;-ms-flex-align: center;align-items: center;margin-left: 12px;margin-top: 12px;">
                                        {data.acctCategory && <view
                                            style="border: 1px solid #2F69F8;border-radius: 8px;font-size: 10px;padding-left: 9px;padding-right: 9px;line-height: 20px;height: 20px;color: #2F69F8;margin-right: 5px;"> {
                                            LovService.filter(data.acctCategory, 'ACCNT_CATEGORY')
                                        } </view>}
                                        {
                                            (data.acctLevel || data.capacityLevel) && <view
                                                style="border: 1px solid #2F69F8;border-radius: 8px;font-size: 10px;padding-left: 9px;padding-right: 9px;line-height: 20px;height: 20px;color: #2F69F8;margin-right: 5px;">
                                                {
                                                    data.acctLevel && <view style="float: left">
                                                        {LovService.filter(data.acctLevel, 'ACCT_LEVEL')}
                                                    </view>}
                                                {
                                                    data.capacityLevel && <view
                                                        style="padding-left: 2px;float:left"> |
                                                        {LovService.filter(data.capacityLevel, 'CAPACITY_LEVEL')}
                                                    </view>}

                                            </view>}
                                    </view>
                                    <view
                                        style="display: -webkit-box;display: -ms-flexbox;display: flex;margin-left: 12px;margin-top: 12px;">
                                        <view
                                            style="color: #8C8C8C;font-size:12px;width: 40px;"> 业代
                                        </view>
                                        <view
                                            style="font-family: PingFangSC-Regular,serif;font-size: 12px;color: #000000;letter-spacing: 0;padding-left: 4px;"> {
                                            data.trackerNames
                                        } </view>
                                    </view>
                                    <view
                                        style="margin-left: 12px;margin-top: 10px;font-family: PingFangSC-Regular,serif;font-size: 12px;color: #262626;letter-spacing: 0;line-height: 12px;">
                                        <view> {data.addrDetailAddr} </view>
                                    </view>
                                </view>
                            </view>
                        </item>
                    )
                },
            }),
            //销售城市
            salesCityOption: new this.AutoList(this, {
                module: 'action/link/orgnization',
                param: () => {
                    // 没有销售区域id则取销售大区id，没有销售大区id则取公司id
                    let attr2 = option.formData.salesRegionId ?
                        option.formData.salesRegionId : option.formData.salesBigAreaId ?
                        option.formData.salesBigAreaId : option.formData.companyId;
                    return {
                        filtersRaw: [
                            {id: 'orgType', property: 'orgType', operator: '=', value: 'SalesCity'},
                            {id: 'id', property: 'isEffective', operator: '=', value: 'Y'}
                        ],
                        attr2
                    }
                },
                searchFields: ['text'],
                sortOptions: null,
                hooks: {
                    beforeLoad(option) {
                        delete option.param.order;
                        delete option.param.sort;
                    },
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item
                            key={index}
                            data={data}
                            arrow="false">
                            <view
                                style="display: -webkit-box;display: -ms-flexbox;display: flex;padding: 12px 8px 12px 12px;width:100%">
                                <view
                                    style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px;">
                                    {data.text}
                                </view>
                            </view>
                        </item>
                    )
                }
            }),
            //销售区县
            salesDistrictOption: new this.AutoList(this, {
                module: 'action/link/orgnization',
                param: {
                    filtersRaw: [{
                        id: 'orgType',
                        property: 'orgType',
                        operator: '=',
                        value: 'SalesArea'
                    },{
                        id: 'id',
                        property: 'isEffective',
                        operator: '=',
                        value: 'Y'
                    }],
                    attr2: option.formData.salesCityId
                },
                searchFields: ['text'],
                sortOptions: null,
                hooks: {
                    beforeLoad(option) {
                        delete option.param.order;
                        delete option.param.sort;
                    },
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item
                            key={index}
                            data={data}
                            arrow="false">
                            <view
                                style="display: -webkit-box;display: -ms-flexbox;display: flex;padding: 12px 8px 12px 12px;width:100%">
                                <view
                                    style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px;">
                                    {data.text}
                                </view>
                            </view>
                        </item>
                    )
                }
            }),
            //购买产品
            productNameOption: new this.AutoList(this, {
                module: null,
                url: {
                    queryByExamplePage: 'action/link/product/queryPriceListProdByOrgIdPage'
                },
                param: {
                    attr2: option.formData.salesCityId
                },
                searchFields: ['prodName', 'prodCode'],
                sortOptions: null,
                hooks: {
                    beforeLoad(option) {
                        delete option.param.order;
                        delete option.param.sort;
                    },
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item
                            key={index}
                            data={data}
                            arrow="false">
                            <link-checkbox
                                val={data.id}
                                toggleOnClickItem
                                slot="thumb"/>
                            <view
                                style="display: flex;flex: 1;flex-direction: column;justify-content: space-between;align-items: flex-start;overflow: hidden;">
                                <view
                                    style="margin:12px;">
                                    <view
                                        style="background: #A6B4C7;border-radius: 4px;line-height: 20px;">
                                        <view
                                            style="font-size: 14px;color: #FFFFFF;letter-spacing: 0;line-height: 20px;padding: 1px 4px;"> {
                                            data.prodCode
                                        }
                                        </view>
                                    </view>
                                </view>
                                <view
                                    style="margin-left:12px;width:100%">
                                    <view
                                        style="font-family: PingFangSC-Semibold;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 16px;">
                                        {data.prodName}
                                    </view>
                                </view>
                                <view
                                    style="margin:12px 0 0 12px;width:100%">
                                    <view
                                        style="font-family: PingFangSC-Regular;font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;width: 50%;float: left;"> 箱价
                                        :
                                        {
                                            FilterService.cny(data.salePrice)
                                        }
                                    </view>
                                    <view
                                        style="font-family: PingFangSC-Regular;font-size: 14px;color: #000000;letter-spacing: 0;text-align: left;line-height: 14px;"> 瓶价
                                        :
                                        {
                                            FilterService.cny(data.saleMiniPrice)
                                        }
                                    </view>
                                </view>
                            </view>
                        </item>
                    )
                },
            }),
            //会员等级
            memberTierOptionSelectedArr:[],//选中的会员等级
            //邀约人
            inviterOption: new this.AutoList(this, {
                module: null,
                url: {
                    queryByExamplePage: this.$env.appURL + '/loyalty/loyalty/member/queryByExamplePage'
                },
                param: {
                    filtersRaw: [{id: 'empFlag', property: 'empFlag', value: 'Y'}]
                },
                searchFields: ['name', 'mobilePhone'],
                sortOptions: null,
                renderFunc: (h, {data, index}) => {
                    return (
                        <item arrow="false"
                              key={index}
                              data={data}>
                            <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                            <view style="width: 100%;padding: 5px 0 0 14px" slot='note'>
                                <view style="margin-bottom: 5px;display: flex;">
                                    <view
                                        style="font-family: PingFangSC-Regular;font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;line-height: 16px;padding-right: 10px;">
                                        会员编号
                                    </view>
                                    <view
                                        style="font-family: PingFangSC-Semibold;font-size: 14px;color: #333;letter-spacing: 0;line-height: 16px">
                                        {data.memberNumber}
                                    </view>
                                </view>
                                <view style="margin-bottom: 5px;display: flex;">
                                    <view
                                        style="font-family: PingFangSC-Regular;font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;line-height: 16px;padding-right: 10px;">
                                        会员名称
                                    </view>
                                    <view
                                        style="font-family: PingFangSC-Semibold;font-size: 14px;color: #333;letter-spacing: 0;line-height: 16px;">
                                        {data.name}
                                    </view>

                                </view>
                                <view style="margin-bottom: 5px;display: flex;">
                                    <view
                                        style="font-family: PingFangSC-Regular;font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;line-height: 16px;padding-right: 10px;">
                                        手机号
                                    </view>
                                    <view
                                        style="font-family: PingFangSC-Semibold;font-size: 14px;color: #333;letter-spacing: 0;line-height: 16px;">
                                        {data.mobilePhone}
                                    </view>

                                </view>
                            </view>
                        </item>
                    )
                },
            }),
        }
    },
    watch: {
        displayItemView(val) {
            this.option.formData.prodLargeClass = this.prodLargeClassView
            this.option.formData.displayItem = this.displayItemView
        },
        'option.formData.busScene'(val) {
            const text = Array.isArray(this.option.formData.busScene);
            if (!text) {
                if (!!this.option.formData.busScene) {this.option.formData.busScene = JSON.parse(this.option.formData.busScene)}
            }
        },
    },
    async onShow() {
        const text = Array.isArray(this.option.formData.busScene);
        if (!text) {
            if (!!this.option.formData.busScene) {this.option.formData.busScene = JSON.parse(this.option.formData.busScene)}
        }
    },
    async created() {
        if (this.cacheData.option !== this.option) {
            this.option.formData = cacheData.option.formData
        }
        this.priceShowFlag = await this.$utils.getPriceDesensitize(this.userInfo);
        this.caseOption.option.param.attr6 = 'filterTime'; //执行案有效时间判别
        const that = this;
        this.timer = setTimeout(async () => {
            //有缓存的情况，根据缓存的市场活动ID查询数据，对比数据库中数据和本地缓存数据的最后更新人是否一致，不一致的话弹窗提示。缓存数据没有ID时不查询。
            if(that.pageParam.__fromCache){
                await that.cacheQueryActicityInfo(that.option.formData);
            }
        }, 1500);
        this.userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
        await this.prodTypeData();
        // this.getMasterNamePickOptionOuath();
        this.queryCfgProperty();
        this.busSceneOld = this.option.formData.busScene;//业务场景-用途：执行反馈环境更新了业务场景后重新查询需要上传的照片模板
        this.initStartTime=this.option.formData.startTime;
        this.initEndTime=this.option.formData.endTime;
        this.prodLargeClassView = this.option.formData.prodLargeClass;
        this.displayItemView = this.option.formData.displayItem;
    },
    destroyed () {
        // 清除计时器
        clearInterval(this.timer);
    },
    mounted() {
        setTimeout(()=>{
            if(this.$utils.isNotEmpty(this.$refs.targetPopulation)){
                this.$set(this.option.formData, 'targetPopulation', 'Member');
                this.getTargetPopulation();
            }
        },1000)
        this.$bus.$on('newProtocolData', (data) => {
            this.protocolOption.list.unshift(data);
        });
        this.$bus.$on('marketActivity', async () => {
            await this.queryActivityActJsonById();
            this.refreshMarketActivity();
        }) 
        // 查看活动时更新宴席主家
        if (this.pageParam.master && this.pageParam.master.masterId) {
            this.$set(this.option.formData, 'masterId', this.pageParam.master.masterId);
            this.$set(this.option.formData, 'masterName', this.pageParam.master.masterName);
            this.$bus.$emit('viewUpdateMaster', {id: '', name: ''})
        }
        if (this.option.formData.protocolId) {
            this.queryAgreeDetail();
        }
    },
    methods: {
        /**
         * 宴席主家展示字段名称处理
         * <AUTHOR>
         * @date	2025/1/2
         */
        fieldNameChange(field, label) {
            if (field === 'masterName') {
                return ['1216'].includes(this.userInfo.coreOrganizationTile.brandCompanyCode) ? '关键人' : label;
            } else {
                return label
            }
		},
        /**
         * 获取协议数据
         * <AUTHOR>
         * @date	2023/7/13 14:54
         */
        async queryAgreeDetail() {
            // 查询协议
            const data = await this.$http.post('action/link/agreement/queryById', {id: this.option.formData.protocolId});
            if (data.success) {
                if (!this.$utils.isEmpty(data.result.agrPicKey)) {
                    let urlData = await this.$image.getSignedUrl(data.result.agrPicKey);
                    this.$set(data.result, 'storeUrl', urlData);
                } else {
                    this.$set(data.result, 'storeUrl', this.$imageAssets.terminalDefaultImage);
                }
                this.agreementItem = data.result;
                // 查询产品业务小类
                const {success, rows} = await this.$http.post('action/link/agreementProduct/queryByExamplePage', {
                    filtersRaw: [{id: 'headId', property: 'headId', value: this.agreementItem.id}]
                });
                if (success) {
                    this.agreeProdList = rows;
                }
                if (this.agreementItem.agrType !== 'display') {
                    this.collectItem = {};
                    return;
                }
                // 获取关联的陈列采集
                const collect = await this.$http.post('action/link/visitDisplay/queryByExamplePage', {
                    rows: 1,
                    sort: 'created',
                    order: 'desc',
                    filtersRaw: [{id: 'agreementId', property: 'agreementId', value: this.agreementItem.id}]
                });
                if (collect.success && collect.rows && collect.rows.length) {
                    this.collectItem = collect.rows[0];
                    const arr = JSON.parse(this.collectItem.operateItem);
                    for(let i = 0; i < arr.length; i++) {
                        arr[i] = await this.$lov.getNameByTypeAndVal('PROD_BUS_S_CLASS', arr[i]);
                    }
                    this.collectItem.operateItem = arr.join(',')
                    if (!this.$utils.isEmpty(this.collectItem.promotionMaterial)) {
                        this.collectItem.promotionMaterial = JSON.parse(this.collectItem.promotionMaterial);
                    }
                } else {
                    this.collectItem = {};
                }
            }
        },
        //返回上一步时获取缓存中最新活动信息
        async refreshMarketActivity() {
            if (!this.$utils.isEmpty(this.$dataService.getMarketActivityItem())) {
                this.option.formData = this.$dataService.getMarketActivityItem()
                this.activityItem = this.$dataService.getMarketActivityItem()
                //不是从基础信息组件编辑而来 需要转化界面显示
                if (this.pageFrom !== 'basicInfo') {
                    //显示显示值-陈列品项 【产品大类、中类】
                    if (!this.$utils.isEmpty(this.option.formData.prodLargeClass)) {
                        const prodLargeClass = await this.$lov.getNameByTypeAndVal('PROD_L_CLASS', this.option.formData.prodLargeClass);
                        this.$set(this.option.formData, 'prodLargeClass', prodLargeClass);
                    }
                    if (!this.$utils.isEmpty(this.option.formData.displayItem)) {
                        const displayItem = await this.$lov.getNameByTypeAndVal('PROD_M_CLASS', this.option.formData.displayItem);
                        this.$set(this.option.formData, 'displayItem', displayItem);
                    }
                }
            }
        },
        /**
         * <AUTHOR>
         * @date  2021年12月22日09:44:38
         * @description  时间修改提示框返回代码。
         */
        changeTime(){
            this.$refs.timeTipDialog.hide();
            if (!this.$utils.isEmpty(this.pageSource) && !(this.$utils.isEmpty(this.scType) && this.onlySaveDataFlag)) {
                //缓存跳转而来的直接返回首页，上个页面的函数是无效的，没办法缓存。
                if(this.pageParam.__fromCache){
                    this.$nav.backAll();
                } else {
                    this.pageParam.callback(this.option.formData);
                    this.$nav.back();
                }
            }else if (this.scType === 'back') {
                let pages = Taro.getCurrentPages();    //获取当前页面信息栈
                const targetIndex = pages.findIndex(function (item) {
                    return item.route === "pages/lj-market-activity/market-activity/market-activity-list-page";
                });
                if (targetIndex === -1) {
                    return this.$nav.backAll()
                }
                const num = Number(pages.length - (Number(targetIndex) + 1));
                setTimeout(() => {
                    this.$bus.$emit('marketActivityListRefresh');
                    this.$nav.back(null, num);
                }, 1000)
            }
        },
        /**
         * @createdBy  songyanrong
         * @date  2021/4/12
         * @methods
         * @param
         * @description 有缓存的情况查询市场活动信息，如果市场活动被其他人操作提交那么弹框告知，用户点确定，返回首页且清除缓存。
         */
        async cacheQueryActicityInfo(item){
            if(this.$utils.isEmpty(item.id)){
                return ;
            }
            const data = await this.$http.post('action/link/marketAct/queryById', {
                id: item.id,
                attr1: 'baseField',
            });
            const newActivityItem = data.result;
            if (!this.$utils.isEmpty(newActivityItem)) {
                //更新人非当前用户且更新时间不一致。或者更新人不一致。
                if((newActivityItem.lastUpdatedBy !== this.userInfo.id && newActivityItem.lastUpdated !== this.option.formData.lastUpdated)
                    ||  newActivityItem.lastUpdatedBy !== this.option.formData.lastUpdatedBy){
                    this.cacheOthersOperatedInfoMsg = `当前市场活动已被${newActivityItem.lastUpdByName}用户于${newActivityItem.lastUpdated}更新,请前往市场活动列表查看并确认。`;
                    this.$refs.cacheOthersOperatedInfoDialog.show();
                }
            }
        },
        closeCurrentPage(){
            this.$refs.cacheOthersOperatedInfoDialog.hide();
            Taro.removeStorageSync('@@PAGE_CACHE');
            this.$nav.backAll();
        },
         /**
         * @createdBy  张丽娟
         * @date  2021/4/8
         * @methods getMasterNamePickOptionOuath
         * @para
         * @description 获取宴席主家安全性
         */
        async getMasterNamePickOptionOuath() {
            const param = {filtersRaw: [{id: 'moduleCode', property: 'moduleCode', operator: 'LIKE', value: 'corpwx_consumer_menu'}]};
            const url = 'action/link/appMenu/queryByExamplePage';
            const {success, rows} = await this.$http.post(url, param);
            if(success){
                if(rows.length > 0){
                    const parentId = rows[0].id;
                    let securityMode = 'MY_ORG';
                    if (this.userInfo.positionType === 'Salesman'
                        || this.userInfo.positionType === 'AccountManager'
                        || this.userInfo.positionType === 'CustServiceSpecialist'
                        || this.userInfo.positionType === 'SalesTeamLeader'
                        || this.userInfo.positionType === 'SalesManager') {
                        securityMode = 'MY_POSTN';
                    }
                    const param = {
                        parentId,
                        securityMode
                    };
                    const securityMenuOauth = await this.getSecurityMenuOauth(param);
                    this.masterOption.option.param['oauth'] = securityMenuOauth;
                }
            }else{
                this.$showError('请维护安全性菜单',{customFlag:true});
            }
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/4/8
         * @methods getSecurityMenuOauth
         * @para
         * @description 获取安全性菜单
         */
        async getSecurityMenuOauth(param) {
            const url = 'action/link/appSecurityMenu/queryByExamplePage';
            const {success, rows} = await this.$http.post(url, param);
            if (!success || !rows || rows.length === 0) {
                return;
            }
            return `ALL${rows[0].id}`;
        },
        /**
         * 新增消费者-宴席主家
         * <AUTHOR>
         * @date 2020-08-07
         *
         * */
        async addConsumer() {
            const id = await this.$newId();
            const accountItem = {
                id: id,
                row_status: ROW_STATUS.NEW,
                consumerDataType: 'ChannelConsumer',
                dataSource: 'MarketingPlatform',
                dataType: 'Consumer',
                accntSourceFrom: 'SalesAssistant',
                orgId: this.userInfo.orgId,
                fstName: this.userInfo.firstName,
                postnId: this.userInfo.postnId,
                belongToCompanyId: this.userInfo.coreOrganizationTile['l3Id'] || '',
                type: "ToBeFollowed",
                birthType: 'Yang',
                brandPreference: "",
                hobby: "",
                terminalFlag: 'N',
                listOfTags: {
                    accountId: id,
                    list: []
                }
            };
            let pathFlag = await this.$utils.getCfgProperty('lj_consumers_flag')
            if(pathFlag==='Y'){
                this.$nav.push('/pages/lj-consumers/account/account-item-edit-page.vue', {
                    data: accountItem,
                    marketActivityId: this.option.formData.id,
                    userInfo: this.userInfo,
                    pageFrom: 'masterActivity',
                    callback: (data) => {
                        this.$set(this.option.formData, 'masterId', data.id);
                        this.$set(this.option.formData, 'masterName', data.name);
                    },
                })
            }else {
            this.$nav.push('/pages/lj-consumers/account-old/account-item-edit-page.vue', {
                data: accountItem,
                marketActivityId: this.option.formData.id,
                userInfo: this.userInfo,
                pageFrom: 'masterActivity',
                callback: (data) => {
                    this.$set(this.option.formData, 'masterId', data.id);
                    this.$set(this.option.formData, 'masterName', data.name);
                },
            })
            }
        },
        /**
         * 自定义返回函数
         * @songyanrong
         * @date 2020-12-02
         * */
        udfBack() {
            if (this.sceneSourceForNavigation === 'other') {
                this.$nav.back();
            } else {
                this.$refs.udfBackDialog.show();
            }
        },
        directReturn() {
            let pages = Taro.getCurrentPages();    //获取当前页面信息栈
            const targetIndex = pages.findIndex(function (item) {
                return item.route === "pages/lj-market-activity/market-activity/market-activity-list-page";
            });
            if (targetIndex === -1) {
                return this.$nav.backAll()
            }
            const num = Number(pages.length - (Number(targetIndex) + 1));
            setTimeout(() => {
                this.$bus.$emit('marketActivityListRefresh');
                this.$nav.back(null, num);
            }, 1000)
        },
        /**
         * 上一步
         * <AUTHOR>
         * @date 2020-08-03
         * */
        lastStep() {
            this.$nav.back();
        },
        /**
         * 选择活动类型
         * <AUTHOR>
         * @date 2020-11-26
         * */
        async pickActIndeSourCode(item) {
            if (item.values.readonlyFlag === 'true') {
                return false;
            }
            const mcTypeData = await this.$object(this.mcTypeOption, {multiple: false, pageTitle: '活动类型'});
            this.$set(this.option.formData, 'actIndeSourCode', mcTypeData.val);
        },
        async onTapCreateVenueButton() {
            // 将新的地址数据unshift 到地址AutoList对象的list数组中，
            // 再返回上一个页面（活动场地选择列表页面）
            const id = await this.$newId();
            const venueItem = {
                id: id,
                row_status: ROW_STATUS.NEW,
                appreciationFlag: 'Y',
                acctType: 'Supplier',//客户大类默认-供应商
                acctCategory: 'Venue',//客户中类默认-活动场地 Venue 值列表 ACCNT_CATEGORY
            };
            this.$nav.push('/pages/lj-market-activity/market-activity/new-venue-page', {
                data: venueItem,
                marketActivityItem: this.option.formData,
                callback: (data) => {
                    this.activityVenueOption.methods.reload();
                }
            });
        },
        /**
        * 查询参数配置
        * <AUTHOR>
        * @date 2020-12-08
        * */
        async queryCfgPropertyObj() {
            const data = await this.$http.post('action/link/cfgProperty/queryByExamplePage', {
                filtersRaw: [{id: 'key', property: 'key', value: 'McActAreaCodeId', operator: '='}]
            });
            this.cfgPropertyObj = data.rows[0];
        },
        /**
        * 选择活动场地
        * <AUTHOR>
        * @date 2020-12-08
        * 当活动的 销售片区ID=参数配置里的ID时 attr2取活动销售片区的ID 否则取活动销售城市的ID
        * */
        async pickVenue(title) {
            let attr2 = "";
            await this.queryCfgPropertyObj();
            if (!this.$utils.isEmpty(this.cfgPropertyObj)) {
                if (!this.$utils.isEmpty(this.cfgPropertyObj.value)) {
                    const result = this.cfgPropertyObj.value.split(",");
                    const exist = result.filter((item) => item === this.option.formData.salesRegionId);
                    if (!this.$utils.isEmpty(exist)) {
                        attr2 = this.option.formData.salesRegionId;
                    } else {
                        attr2 = this.option.formData.salesCityId;
                    }
                } else {
                    attr2 = this.option.formData.salesCityId;
                }
            }
            this.activityVenueOption.option.param['attr2'] = attr2;
            const venueData = await this.$object(this.activityVenueOption, {multiple: false, pageTitle: title});
            // 校验场地经纬度
            if( this.$utils.isEmpty(venueData.latitude) || this.$utils.isEmpty(venueData.longitude)){
                await this.$utils.delay(1000)
                this.$message.warn('当前场地经纬度缺失，请先完善场地地址信息或者新建场地。',{duration:2000});
                return
            }
            this.$set(this.option.formData, 'actPlaceId', venueData.id);
            this.$set(this.option.formData, 'actPlaceCode', venueData.acctCode);
            this.$set(this.option.formData, 'restaurant', venueData.acctName);
            this.$set(this.option.formData, 'restaurantAddr', venueData.addrDetailAddr);
            this.$set(this.option.formData, 'restaurantLon', venueData.longitude);
            this.$set(this.option.formData, 'restaurantLat', venueData.latitude);
            this.$set(this.option.formData, 'restaurantPhone', venueData.mobilePhone);
        },
        /*需要几个字段 原因 用市场活动新建之后 会有很多下一步 返回到基础信息点下一步再继续往下走 营销的数据会被清掉*/
        async queryActivityActJsonById() {
            if (!this.$utils.isEmpty(this.option.formData['id'] && this.option.formData.row_status !== ROW_STATUS.NEW)) {
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.option.formData['id']
                });
                // //如果是本人更新的那么可以继续操作
                // if(data.result['lastUpdatedBy'] === this.userInfo.id){
                //     this.option.formData['rowVersion'] = data.result['rowVersion'];
                // }
                // 获取缓存中的活动版本号
                if(!this.$utils.isEmpty(this.$dataService.getMarketActivityItem())){
                    this.option.formData['rowVersion'] = this.$dataService.getMarketActivityItem()['rowVersion']
                } else {
                    this.option.formData['rowVersion'] = data.result['rowVersion'];
                }
                this.option.formData['actJson'] = data.result['actJson'];
                this.option.formData['templateId'] = data.result['templateId'];
                this.option.formData['templateName'] = data.result['templateName'];
            }
        },
        /**
         * 处理产品大类
         * <AUTHOR>
         * @date 2020-09-14
         */
        async prodTypeData() {
            const newCacheLovs = {}
            await Promise.all(
                Object.keys(this.cacheLovs).map(async (lovType)=>{
                    newCacheLovs[lovType] = await this.$lov.getLovByType(lovType)
                })
            )
            this.cacheLovs = newCacheLovs
        },
        /**
         * 产品类型二级联动
         * <AUTHOR>
         * @date 2020-11-02
         * @param tag
         * @param parentValue
         */
        productTypeOption(tag, parentValue) {
            if (!this.cacheLovs.PROD_L_CLASS || !this.cacheLovs.PROD_M_CLASS) {return []}
            if (tag === 'p') {
                return this.cacheLovs.PROD_L_CLASS.map(i => i.name)
            } else if (tag === 'c') {
                const parentLovMeta = this.cacheLovs.PROD_L_CLASS.find(i => i.name === parentValue)
                if (!parentLovMeta) {return []}
                return this.cacheLovs.PROD_M_CLASS.filter(i => i.parentVal === parentLovMeta.val).map(i => i.name)
            }
        },
        /**
        * 供应商
        * <AUTHOR>
        * @date 2020-12-14
        * */
        async pickSupplierName(title) {
            if(!this.option.formData.salesCityId){
                this.$message.warn('请先选择销售城市',{customFlag:true})
                return
            }
            this.supplierOption.option.param['attr2'] = this.option.formData.salesCityId;
            this.supplierOption.option.param['saleCityId'] = this.option.formData.salesCityId;
            const supplierData = await this.$object(this.supplierOption, {multiple: false, pageTitle: title});
            this.$set(this.option.formData, 'supplierId', supplierData.id);
            this.$set(this.option.formData, 'supplier', supplierData.acctName);
        },
        /**
        * 宴席推荐客户
        * <AUTHOR>
        * @date 2020-12-14
        * */
        async pickTargetTerName(title) {
            await this.queryCfgProperty();
            if (this.userInfo.coreOrganizationTile
                && this.userInfo.coreOrganizationTile.l3Code
                && this.noSubAccountOrgCode.indexOf(this.userInfo.coreOrganizationTile.l3Code) > -1) {
                // 若条件未加上，则添加条件
                const filter = this.targetTerminalOption.option.param.filtersRaw.find(item => item.id === 'multiAcctMainFlag');
                if (!filter) {
                    this.targetTerminalOption.option.param.filtersRaw.push({
                        id: 'multiAcctMainFlag',
                        property: 'multiAcctMainFlag',
                        value: 'Y'
                    })
                }
            }
            this.targetTerminalOption.option.param['attr1'] = this.option.formData.salesCityId;
            this.actExecutivesOption.option.param['companyId'] = this.option.formData.companyId;
            const targetTerminalData = await this.$object(this.targetTerminalOption, {
                multiple: false,
                pageTitle: title
            });
            /*
            * 当客户大类=【Dealer】&【Distributor】时，企微前端查询出来显示在列表的数据及选择后显示在的活动头上的数据，取客户的营业执照名称【billTitle 】显示
            * 当客户大类=【Terminal】时，企微前端查出来显示在列表的数据及选择后显示在活动头上的数据，取客户的门头店招名称【acctName】；
            * */
            if (targetTerminalData.acctType === 'Terminal') {
                this.$set(this.option.formData, 'targetTerName', targetTerminalData.acctName);
                this.$set(this.option.formData, 'targetTerminal', targetTerminalData.id);
            } else if (targetTerminalData.acctType === 'Dealer' || targetTerminalData.acctType === 'Distributor') {
                this.$set(this.option.formData, 'targetTerName', targetTerminalData.billTitle);
                this.$set(this.option.formData, 'targetTerminal', targetTerminalData.id)
            }
        },
        /**
        * 选择执行案
        * <AUTHOR>
        * @date 2020-10-22
        * type:actual 实际 apply 申请
        * */
        async pickExeCaseCode(title, type) {
            //2021-08-04考虑多人操作的场景...
            const data = await this.$http.post('action/link/marketAct/queryById', {
                id: this.option.formData.id
            });
            //当执行案状态为审批通过  未提报  则有效时间+5天
            try{if(data.result.status === "Processing") {
                this.caseOption.option.param.attr6 = '';
                this.caseOption.option.param.attr5 = 'Y';
            }}catch (e){}
            if (this.$utils.isNotEmpty(data.result) && !((data.result.status === 'New'
                && (data.result.aproStatus === 'New' || data.result.aproStatus === 'Refused')
            ) || ((data.result.status === 'Processing' || data.result.status === 'Closed' || data.result.status === 'Published')
                && (data.result.aproStatus === 'Approve' || data.result.aproStatus === 'Refeedback' || data.result.aproStatus === 'RefeedWithdraw')
            ))) {
                this.$message.warn('活动已被更新，当前活动状态和审批状态不允许切换执行案，请返回列表重新查询活动数据。',{customFlag:true});
                return ;
            }
            // 费用实际时 releaseFeeFlag ：是否释放申请费用占用执行案明细的金额。Y的时候 不允许编辑
            if (type === 'actual' && this.option.formData.releaseFeeFlag === 'Y') {
                return false;
            }
            this.chooseCaseData = await this.$object(this.caseOption, {multiple: false, pageTitle: title});
            if (this.option.formData.row_status === ROW_STATUS.NEW) {
                this.$set(this.option.formData, 'exeCaseCode', this.chooseCaseData.executionCode);
                this.$set(this.option.formData, 'exeCaseId', this.chooseCaseData.id);
                this.$set(this.option.formData, 'executionName', this.chooseCaseData.executionName);
                this.$set(this.option.formData, 'progComment', this.chooseCaseData.progComment);
            } else {
                if (type === 'apply') {
                    if (this.$utils.isEmpty(this.option.formData.exeCaseId)) {
                        this.$set(this.option.formData, 'exeCaseCode', this.chooseCaseData.executionCode);
                        this.$set(this.option.formData, 'exeCaseId', this.chooseCaseData.id);
                        this.$set(this.option.formData, 'executionName', this.chooseCaseData.executionName);
                        this.$set(this.option.formData, 'progComment', this.chooseCaseData.progComment);
                    } else {
                        if (this.chooseCaseData.id !== this.option.formData.exeCaseId) {
                            this.applyDailogText = "更换执行案编码，将同步清掉该活动下原已关联的执行案明细的费用申请明细及活动上关联信息，是否确认更换";
                            this.$refs.updateExeCase.show();
                        }
                    }
                } else if (type === 'actual') {
                    if (this.$utils.isEmpty(this.option.formData.exeCaseId)) {
                        this.$set(this.option.formData, 'exeCaseCode', this.chooseCaseData.executionCode);
                        this.$set(this.option.formData, 'exeCaseId', this.chooseCaseData.id);
                        this.$set(this.option.formData, 'executionName', this.chooseCaseData.executionName);
                        this.$set(this.option.formData, 'progComment', this.chooseCaseData.progComment);
                    } else {
                        if (this.chooseCaseData.id !== this.option.formData.exeCaseId) {
                            this.$refs.updateExeCaseForActualFee.show();
                        }
                    }
                }
            }
        },
        /**
         * 清空执行案不使用this.$dialog
         * <AUTHOR>
         * @date 2022年1月25日14:55:03
         * */
        async clearFee(){
            const that = this;
            that.$utils.showLoading();
            const updataActivity = {
                id: that.option.formData.id,
                exeCaseCode: "",
                exeCaseId: "",
                rowVersion: that.option.formData.rowVersion,
                updateFields: "id,exeCaseCode,exeCaseId,rowVersion"
            };
            const dealData = {
                marketAct:updataActivity,
                actMaterial:{
                    actId: that.option.formData.id
                }
            }
            const data = await that.$http.post('action/link/actIntegration/clearActProdCode', dealData,{
                handleFailed: (error) => {
                    that.$utils.hideLoading();
                    this.$refs.clearExeCaseCode.hide();
                }
            });
            that.$bus.$emit("initActivityProdAndCostList");
            that.option.formData = {...data.result};
            this.$dataService.setMarketActivityItem(data.result);
            //clearActProdCode接口清除执行案时省市区县id同时清空  校验返回新行省id为空 受益人不为空 满足后取受益人的省市区保存
            if(this.$utils.isEmpty(data.result.provinceId)&&!this.$utils.isEmpty(data.result.beneficiaryId)) {
                let param = {
                    filtersRaw: [
                        {id: 'acctStatus', property: 'acctStatus', value: 'Y', operator: '='},
                        {id: 'id', property: 'id', value: data.result.beneficiaryId, operator: '='},
                    ],
                    attr2: this.option.formData.salesCityId,
                    attr3: 'Dealer,Terminal,Distributor'
                };

                if (this.userInfo.coreOrganizationTile
                    && this.userInfo.coreOrganizationTile.l3Code
                    && this.noSubAccountOrgCode.indexOf(this.userInfo.coreOrganizationTile.l3Code) > -1) {
                    // 若条件未加上，则添加条件
                    const filter = param.filtersRaw.find(item => item.id === 'multiAcctMainFlag');
                    if (!filter) {
                        param.filtersRaw.push({
                            id: 'multiAcctMainFlag',
                            property: 'multiAcctMainFlag',
                            value: 'Y'
                        })
                    }
                }

                param['attr2'] = this.option.formData.salesCityId;
                param['companyId'] = this.option.formData.companyId;
                const beneficiaryData = await that.$http.post('export/link/accnt/queryAccntByCityOrgPage', param, {
                    handleFailed: (error) => {
                        that.$utils.hideLoading();
                        this.$refs.clearExeCaseCode.hide();
                    }
                });
                //查询省市区id赋值
                if (!this.$utils.isEmpty(beneficiaryData.rows[0].province)) {
                    let addressOption = {
                        rowVersion: that.option.formData.rowVersion,
                        companyId: this.option.formData.companyId,
                        updateFields: "id,province,provinceId,city,cityId,district,districtId,jiheSecField,rowVersion"
                    }
                    const addressData = await this.$http.post('action/link/alladdress/queryIdByName', {
                        stateName: beneficiaryData.rows[0].province,
                        cityName: beneficiaryData.rows[0].city,
                        countyName: beneficiaryData.rows[0].district
                    });
                    if (addressData.success) {
                        this.$set(addressOption, 'id', this.option.formData.id);
                        this.$set(addressOption, 'province', beneficiaryData.rows[0].province);
                        this.$set(addressOption, 'city', beneficiaryData.rows[0].city);
                        this.$set(addressOption, 'district', beneficiaryData.rows[0].district);
                        this.$set(addressOption, 'provinceId', addressData.stateId);
                        this.$set(addressOption, 'cityId', addressData.cityId);
                        this.$set(addressOption, 'districtId', addressData.countyId);
                        const data = await this.$http.post('action/link/marketAct/update', addressOption);
                        if (!this.$utils.isEmpty(data.newRow)) {
                            that.option.formData = {...data.newRow};
                            this.$dataService.setMarketActivityItem(data.newRow);
                        }
                    }
                }
            }
            that.costListData = {
                prodPayList: [],
                cashPayList: [],
            }
            that.$utils.hideLoading();
            this.$refs.clearExeCaseCode.hide();
        },
        /**
         * 清空执行案编码
         * <AUTHOR>
         * @date 2020-11-26
         * */
        async clearExeCaseCode() {
            const that = this;
            if (that.option.formData.row_status !== ROW_STATUS.NEW) {
                //2021-08-04考虑多人操作的场景...
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.option.formData.id
                });
                if (!((data.result.status === 'New'
                    && (data.result.aproStatus === 'New' || data.result.aproStatus === 'Refused')
                ) || ((data.result.status === 'Processing' || data.result.status === 'Closed' || data.result.status === 'Published')
                    && (data.result.aproStatus === 'Approve' || data.result.aproStatus === 'Refeedback' || data.result.aproStatus === 'RefeedWithdraw')
                ))) {
                    this.$message.warn('活动已被更新，当前活动状态和审批状态不允许清空执行案编码，请返回列表重新查询活动数据。',{customFlag:true});
                    return ;
                }
                this.$refs.clearExeCaseCode.show();
            } else {
                that.$set(that.option.formData, 'exeCaseCode', "");
                that.$set(that.option.formData, 'exeCaseId', "");
                that.$set(that.option.formData, 'executionName', "");
                that.$set(that.option.formData, 'progComment', "");
            }
        },
        /*
        * 新建协议-选择协议类型
        * @auther songyanrong
        * @date 2020-11-09
        * */
        onTapCreateProtocolButton() {
            this.$nav.push("/pages/lj-market-activity/market-activity/new-agreement-type-page", {
                data: this.option.formData,
            })
        },
        /*
        * 选择协议
        *
        * @auther songyanrong
        * @date 2020-11-09
        * */
        async pickProtocol(title) {
            if (this.$utils.isEmpty(this.option.formData.beneficiaryId)) {
                this.$showError('请先选择受益客户！');
                return;
            }
            const protocolData = await this.$object(this.protocolOption, {multiple: false, pageTitle: title});
            this.$set(this.option.formData, 'protocolId', protocolData.id);
            this.$set(this.option.formData, 'protocolName', protocolData.agrName);
            this.$set(this.option.formData, 'protocolType', protocolData.agrType);
            this.$set(this.option.formData, 'protocolStartTime', protocolData.startTime);
            this.$set(this.option.formData, 'protocolEndTime', protocolData.endTime);
            await this.queryAgreeDetail();
        },
        /*
            * 选择宴席买家
            *
            * @auther songyanrong
            * @date 2020-11-19
            * */
        async pickMasterName(title, field) {
            const MasterData = await this.$object(this.masterOption, {multiple: false, pageTitle: this.fieldNameChange(field, title)});
            this.$set(this.option.formData, 'masterId', MasterData.id);
            this.$set(this.option.formData, 'masterName', MasterData.acctName);
        },
        /**
         * @desc 选择所属客户数据
         **/
        async chooseStoreList () {
            this.isChosen = !this.isChosen
            if (this.isChosen) {
                const list = await this.$object(this.customerOption, {
                    pageTitle: '请选择所属客户',
                    showInDialog: true,
                    multiple: false,
                    autoListProps: {searchInputBinding: {props: {placeholder: '搜索所属客户名称'}}}
                });
                this.masterOption.option.param['belongToStoreIdList'] = [list.id];
                this.masterOption.methods.reload();
            } else {
                delete this.masterOption.option.param['belongToStoreIdList'];
                this.masterOption.methods.reload();
            }
        },
        /*
        * 选择子公司/经销商
        * @auther songyanrong
        * @date 2020-10-22
        * */
        async pickActExecutives(title, readonlyFlag) {
            if (!this.$utils.isEmpty(readonlyFlag) && readonlyFlag === 'true') {
                return false;
            }
            this.actExecutivesOption.option.param['attr2'] = this.option.formData.salesCityId;
            this.actExecutivesOption.option.param['companyId'] = this.option.formData.companyId;
            const actExecutivesData = await this.$object(this.actExecutivesOption, {
                multiple: false,
                pageTitle: title
            });
            /*
            * 当客户大类=【Dealer】&【Distributor】时，企微前端查询出来显示在列表的数据及选择后显示在的活动头上的数据，
            * 取客户的营业执照名称【billTitle 】渠道管理模式，经销商的省市区县详细地址拼接值
            * */
            if (actExecutivesData.acctType === 'Dealer') {
                this.$set(this.option.formData, 'actExecutivesName', actExecutivesData.billTitle);
                this.$set(this.option.formData, 'actExecutivesId', actExecutivesData.id)
            }
        },
        /**
         * 选择企业信息
         * <AUTHOR>
         * @date	2023/6/7 17:47
         */
        async pickEnterpriseName(title, readonlyFlag) {
            if (!this.$utils.isEmpty(readonlyFlag) && readonlyFlag === 'true') {
                return false;
            }
            const enterpriseData = await this.$object(this.enterpriseOption, {multiple: false, pageTitle: title});
            this.$set(this.option.formData, 'enterpriseName', enterpriseData.companyName);
            this.$set(this.option.formData, 'enterpriseId', enterpriseData.id);
            this.$set(this.option.formData, 'visitLevel', enterpriseData.visitLevel);
        },
        clearProtocol() {
            this.option.formData.protocolId = ''
            this.option.formData.protocolName = ''
            this.option.formData.protocolType = ''
            this.option.formData.protocolStartTime = null
            this.option.formData.protocolEndTime = null
            this.agreementItem = {};
            this.agreeProdList = [];
            this.collectItem = {};
        },
        /*
        * 选择受益对象
        * @auther songyanrong
        * @date 2020-11-25
        * */
        async pickBeneficiaryName(title, readonlyFlag) {
            if (!this.$utils.isEmpty(readonlyFlag) && readonlyFlag === 'true') {
                return false;
            }
            await this.queryCfgProperty();
            if (this.userInfo.coreOrganizationTile
                && this.userInfo.coreOrganizationTile.l3Code
                && this.noSubAccountOrgCode.indexOf(this.userInfo.coreOrganizationTile.l3Code) > -1) {
                // 若条件未加上，则添加条件
                const filter = this.beneficiaryOption.option.param.filtersRaw.find(item => item.id === 'multiAcctMainFlag');
                if (!filter) {
                    this.beneficiaryOption.option.param.filtersRaw.push({
                        id: 'multiAcctMainFlag',
                        property: 'multiAcctMainFlag',
                        value: 'Y'
                    })
                }
            }
            this.beneficiaryOption.option.param['attr2'] = this.option.formData.salesCityId;
            this.beneficiaryOption.option.param['companyId'] = this.option.formData.companyId;
            const beneficiaryData = await this.$object(this.beneficiaryOption, {multiple: false, pageTitle: title});
            /*
            * 当客户大类=【Dealer】&【Distributor】时，企微前端查询出来显示在列表的数据及选择后显示在的活动头上的数据，取客户的营业执照名称【billTitle 】显示
            * 当客户大类=【Terminal】时，企微前端查出来显示在列表的数据及选择后显示在活动头上的数据，取客户的门头店招名称【acctName】；
            * */
            if (beneficiaryData.id !== this.option.formData.beneficiaryId) {
                this.clearProtocol();
            }
            if (beneficiaryData.acctType === 'Terminal') {
                this.$set(this.option.formData, 'beneficiaryName', beneficiaryData.acctName);
                this.$set(this.option.formData, 'beneficiaryId', beneficiaryData.id);
            } else if (beneficiaryData.acctType === 'Dealer' || beneficiaryData.acctType === 'Distributor') {
                this.$set(this.option.formData, 'beneficiaryName', beneficiaryData.billTitle);
                this.$set(this.option.formData, 'beneficiaryId', beneficiaryData.id)
            }
            if (this.$utils.isEmpty(this.option.formData.exeCaseId)||this.$utils.isEmpty(this.option.formData.province)){
                this.$set(this.option.formData, 'province', beneficiaryData.province);
                this.$set(this.option.formData, 'city', beneficiaryData.city);
                this.$set(this.option.formData, 'district', beneficiaryData.district);
                if (!this.$utils.isEmpty(beneficiaryData.province)){
                    const addressData = await  this.$http.post('action/link/alladdress/queryIdByName', {
                        stateName: beneficiaryData.province,
                        cityName: beneficiaryData.city,
                        countyName: beneficiaryData.district
                    });
                    if(addressData.success){
                        this.$set(this.option.formData, 'provinceId', addressData.stateId);
                        this.$set(this.option.formData, 'cityId', addressData.cityId);
                        this.$set(this.option.formData, 'districtId', addressData.countyId);
                    }
                }
            }
        },
        /*
        * 选择销售城市
        * @auther songyanrong
        * @date 2021-01-07
        * */
        async pickSalesCityName(title, readonlyFlag) {
            if ((!this.$utils.isEmpty(readonlyFlag) && readonlyFlag === 'true') || this.option.formData.row_status !== ROW_STATUS.NEW) {
                return false;
            }
            // this.salesCityOption.option.param['attr2'] = this.option.formData.salesRegionId;
            const salesCityData = await this.$object(this.salesCityOption, {multiple: false, pageTitle: title});
            if (salesCityData.id !== this.option.formData.salesCityId) {
                //切换销售城市，新建时可以编辑销售城市，活动保存之后就不允许改了【PC配置的逻辑】
                //需清掉活动头上的销售区县ID 名称、活动场地id 名称 编码、场地地址、经度、维度、酒店电话、子公司/经销商id 名称、受益对象ID 名称、购买产品id 名称
                this.$set(this.option.formData, 'salesCityId', salesCityData.id);
                this.$set(this.option.formData, 'salesCity', salesCityData.text);
                this.$set(this.option.formData, 'salesDistrictId', null);
                this.$set(this.option.formData, 'salesDistrict', null);
                this.$set(this.option.formData, 'actPlaceId', null);
                this.$set(this.option.formData, 'actPlaceCode', null);
                this.$set(this.option.formData, 'restaurant', null);
                this.$set(this.option.formData, 'restaurantAddr', null);
                this.$set(this.option.formData, 'restaurantLon', null);
                this.$set(this.option.formData, 'restaurantLat', null);
                this.$set(this.option.formData, 'restaurantPhone', null);
                this.$set(this.option.formData, 'actExecutivesName', null);
                this.$set(this.option.formData, 'actExecutivesId', null);
                this.$set(this.option.formData, 'beneficiaryName', null);
                this.$set(this.option.formData, 'beneficiaryId', null);
                this.$set(this.option.formData, 'productId', null);
                this.$set(this.option.formData, 'productName', null);
            }
        },
        /**
        * 选择销售区县
        * <AUTHOR>
        * @date 2021-01-07
        * */
        async pickSalesDistrictName(title, readonlyFlag) {
            if ((!this.$utils.isEmpty(readonlyFlag) && readonlyFlag === 'true') || this.option.formData.row_status !== ROW_STATUS.NEW) {
                return false;
            }
            this.salesDistrictOption.option.param['attr2'] = this.option.formData.salesCityId;
            const salesDistrictData = await this.$object(this.salesDistrictOption, {
                multiple: false,
                pageTitle: title
            });
            this.$set(this.option.formData, 'salesDistrictId', salesDistrictData.id);
            this.$set(this.option.formData, 'salesDistrict', salesDistrictData.text);
        },
        /**
         * 选择购买产品
         * <AUTHOR>
         * @date 2021-01-12
         * */
        async pickProductName(title, readonlyFlag) {
            if (!this.$utils.isEmpty(readonlyFlag) && readonlyFlag === 'true') {
                return false;
            }
            this.productNameOption.option.param['attr2'] = this.option.formData.salesCityId;
            const productNameData = await this.$object(this.productNameOption, {
                multiple: false,
                pageTitle: title
            });
            this.$set(this.option.formData, 'productId', productNameData.id);
            this.$set(this.option.formData, 'productName', productNameData.prodName);
        },
        /**
        * 确认更新执行案-费用申请使用
        * <AUTHOR>
        * @date 2020-10-22
        * */
        async associated() {
            this.$utils.showLoading();
            //删除其下挂的费用申请对象，清掉活动上的省市区、活动的费用垫付对象
            const updataActivity = {
                id: this.option.formData.id,
                exeCaseId: this.chooseCaseData.id,
                rowVersion: this.option.formData.rowVersion,
                updateFields: "id,exeCaseId,rowVersion"
            };
            const dealData = {
                marketAct:updataActivity,
                actMaterial:{
                    actId: this.option.formData.id
                }
            };
            const data = await this.$http.post('action/link/actIntegration/clearActProdCode', dealData);
            this.option.formData = {...data.result};
            this.$dataService.setMarketActivityItem(data.result);
            //clearActProdCode 接口更新执行案后 将省市区县id同时清空  校验返回新行省id为空 受益人不为空 满足后取受益人的省市区保存
            if(this.$utils.isEmpty(data.result.provinceId)&&!this.$utils.isEmpty(data.result.beneficiaryId)) {
                let param = {
                    filtersRaw: [
                        {id: 'acctStatus', property: 'acctStatus', value: 'Y', operator: '='},
                        {id: 'id', property: 'id', value: data.result.beneficiaryId, operator: '='},
                    ],
                    attr2: this.option.formData.salesCityId,
                    attr3: 'Dealer,Terminal,Distributor'
                };

                if (this.userInfo.coreOrganizationTile
                    && this.userInfo.coreOrganizationTile.l3Code
                    && this.noSubAccountOrgCode.indexOf(this.userInfo.coreOrganizationTile.l3Code) > -1) {
                    // 若条件未加上，则添加条件
                    const filter = param.filtersRaw.find(item => item.id === 'multiAcctMainFlag');
                    if (!filter) {
                        param.filtersRaw.push({
                            id: 'multiAcctMainFlag',
                            property: 'multiAcctMainFlag',
                            value: 'Y'
                        })
                    }
                }
                param['attr2'] = this.option.formData.salesCityId;
                param['companyId'] = this.option.formData.companyId;
                const beneficiaryData = await this.$http.post('export/link/accnt/queryAccntByCityOrgPage', param, {
                });
                //查询省市区id赋值
                if (!this.$utils.isEmpty(beneficiaryData.rows[0].province)) {
                    let addressOption = {
                        rowVersion: this.option.formData.rowVersion,
                        companyId: this.option.formData.companyId,
                        updateFields: "id,province,provinceId,city,cityId,district,districtId,jiheSecField,rowVersion"
                    }
                    const addressData = await this.$http.post('action/link/alladdress/queryIdByName', {
                        stateName: beneficiaryData.rows[0].province,
                        cityName: beneficiaryData.rows[0].city,
                        countyName: beneficiaryData.rows[0].district
                    });
                    if (addressData.success) {
                        this.$set(addressOption, 'id', this.option.formData.id);
                        this.$set(addressOption, 'province', beneficiaryData.rows[0].province);
                        this.$set(addressOption, 'city', beneficiaryData.rows[0].city);
                        this.$set(addressOption, 'district', beneficiaryData.rows[0].district);
                        this.$set(addressOption, 'provinceId', addressData.stateId);
                        this.$set(addressOption, 'cityId', addressData.cityId);
                        this.$set(addressOption, 'districtId', addressData.countyId);
                        const data = await this.$http.post('action/link/marketAct/update', addressOption);
                        if (!this.$utils.isEmpty(data.newRow)) {
                            this.option.formData = {...data.newRow};
                            this.$dataService.setMarketActivityItem(data.newRow);
                        }
                    }
                }
            }
            this.costListData = {
                prodPayList: [],
                cashPayList: [],
            }
            this.$refs.updateExeCase.hide();
            this.$utils.hideLoading();
        },
        /**
        * 确认更新执行案-费用实际使用
        * <AUTHOR>
        * @date 2020-10-22
        * */
        async associatedForActualFee() {
            //清掉费用实际的costID
            /**
             * 费用实际已关联的执行案明细信息将被清空，需重新关联后,需要重新查询下费用实际的数据
             * 目的是：查询最新没有关联执行案明细的数据展示出关联执行案明细按钮
             * */
            const updataActivity = {
                id: this.option.formData.id,
                exeCaseId: this.chooseCaseData.id,
                rowVersion: this.option.formData.rowVersion,
                updateFields: "id,exeCaseId,rowVersion"
            };
            const dealData = {
                actualFee:{
                    actId: this.activityItem.id
                },
                marketAct:updataActivity
            }
            const data = await this.$http.post('action/link/actIntegration/ackUpdateActProd', dealData);
            this.option.formData = {...data.result};
            this.$dataService.setMarketActivityItem(data.result);
            //ackUpdateActProd接口更新执行案后 将省市区县id同时清空  校验返回新行省id为空 受益人不为空 满足后取受益人的省市区保存
            if(!this.$utils.isEmpty(data.result.beneficiaryId)) {
                let param = {
                    filtersRaw: [
                        {id: 'acctStatus', property: 'acctStatus', value: 'Y', operator: '='},
                        {id: 'id', property: 'id', value: data.result.beneficiaryId, operator: '='},
                    ],
                    attr2: this.option.formData.salesCityId,
                    attr3: 'Dealer,Terminal,Distributor'
                };

                if (this.userInfo.coreOrganizationTile
                    && this.userInfo.coreOrganizationTile.l3Code
                    && this.noSubAccountOrgCode.indexOf(this.userInfo.coreOrganizationTile.l3Code) > -1) {
                    // 若条件未加上，则添加条件
                    const filter = param.filtersRaw.find(item => item.id === 'multiAcctMainFlag');
                    if (!filter) {
                        param.filtersRaw.push({
                            id: 'multiAcctMainFlag',
                            property: 'multiAcctMainFlag',
                            value: 'Y'
                        })
                    }
                }
                param['attr2'] = this.option.formData.salesCityId;
                param['companyId'] = this.option.formData.companyId;
                const beneficiaryData = await this.$http.post('export/link/accnt/queryAccntByCityOrgPage', param, {
                });
                //查询省市区id赋值
                if (!this.$utils.isEmpty(beneficiaryData.rows[0].province)) {
                    let addressOption = {
                        rowVersion: this.option.formData.rowVersion,
                        companyId: this.option.formData.companyId,
                        updateFields: "id,province,provinceId,city,cityId,district,districtId,jiheSecField,rowVersion"
                    }
                    const addressData = await this.$http.post('action/link/alladdress/queryIdByName', {
                        stateName: beneficiaryData.rows[0].province,
                        cityName: beneficiaryData.rows[0].city,
                        countyName: beneficiaryData.rows[0].district
                    });
                    if (addressData.success) {
                        this.$set(addressOption, 'id', this.option.formData.id);
                        this.$set(addressOption, 'province', beneficiaryData.rows[0].province);
                        this.$set(addressOption, 'city', beneficiaryData.rows[0].city);
                        this.$set(addressOption, 'district', beneficiaryData.rows[0].district);
                        this.$set(addressOption, 'provinceId', addressData.stateId);
                        this.$set(addressOption, 'cityId', addressData.cityId);
                        this.$set(addressOption, 'districtId', addressData.countyId);
                        const data = await this.$http.post('action/link/marketAct/update', addressOption);
                        if (!this.$utils.isEmpty(data.newRow)) {
                            this.option.formData = {...data.newRow};
                            this.$dataService.setMarketActivityItem(data.newRow);
                        }
                    }
                }
            }
            this.$set(this.option.formData, 'exeCaseCode', this.chooseCaseData.executionCode);
            this.$set(this.option.formData, 'exeCaseId', this.chooseCaseData.id);
            this.$bus.$emit("initActivityProdAndCostList");
            this.$refs.updateExeCaseForActualFee.hide();
        },
        /**
         * 新建确认保存
         * <AUTHOR>
         * @date 2021-01-14
         * */
        async confirmSave() {
            this.salesAddressConfirmFlag = true;
            this.$refs.salesAddressConfirmDialog.hide();
            if(this.onlySaveDataFlag){
                await this.nextStep();
            } else {
                await this.nextStep('next');
            }
        },
        checkData(){
            if (this.$utils.isEmpty(this.option.formData.busScene)) {
                this.$message.warn('请选择业务场景',{customFlag:true});
                return false;
            }
            if (this.option.formData.busScene.includes('DisplayData') && !this.option.formData.busScene.includes('EnterDisplay4')) {
                this.$showError('陈列协议图片资料不能单独保存，需要和进场陈列、电商平台资源位/展示位一起选择');
                return false;
            }
            const exeCaseCodeFiledExist = this.editSubControlList.filter((item) => item.values.field === 'exeCaseCode');
            const noPerformCommentsFiledExist = this.editSubControlList.filter((item) => item.values.field === 'noPerformComments');
            const startTimeFiledExist = this.editSubControlList.filter((item) => item.values.field === 'startTime');
            const endTimeFiledExist = this.editSubControlList.filter((item) => item.values.field === 'endTime');
            const ordererPhoneFiledExist = this.editSubControlList.filter((item) => item.values.field === 'ordererPhone');//订餐人联系电话
            const signerPhoneFiledExist = this.editSubControlList.filter((item) => item.values.field === 'signerPhone');//签收人联系电话
            const eventPhoneFiledExist = this.editSubControlList.filter((item) => item.values.field === 'eventPhone');//事件负责人联系电话
            const executorPhoneFiledExist = this.editSubControlList.filter((item) => item.values.field === 'executorPhone');//执行人联系电话
            const targetPhoneFiledExist = this.editSubControlList.filter((item) => item.values.field === 'targetPhone');//推荐人联系电话
            const targetPopulationFiledExist = this.editSubControlList.filter((item) => item.values.field === 'targetPopulation');//活动目标人群
            const restaurantLon = this.editSubControlList.filter((item) => item.values.field === 'restaurantLon');
            const restaurantLat = this.editSubControlList.filter((item) => item.values.field === 'restaurantLat');
            if (!this.$utils.isEmpty(startTimeFiledExist) && !this.$utils.isEmpty(endTimeFiledExist)) {
                if (this.option.formData['endTime'] <= this.option.formData['startTime']) {
                    this.$message.warn('活动结束时间必须晚于活动开始时间，请确认',{customFlag:true});
                    return false;
                }
            }
            if( !this.$utils.isEmpty(restaurantLon) && !this.$utils.isEmpty(restaurantLat) ){
                if(restaurantLon[0].base.require && restaurantLat[0].base.require ) {
                    if (this.$utils.isEmpty(this.option.formData.restaurantLon) || this.$utils.isEmpty(this.option.formData.restaurantLat)) {
                        this.$showError('场地经纬度信息缺失，请重新维护活动供应商地址信息后选择！',{customFlag:true});
                        return false;
                    }
                }
            }
            if( !this.$utils.isEmpty(restaurantLon)) {
                if (restaurantLon[0].base.require) {
                    if (this.$utils.isEmpty(this.option.formData.restaurantLon)) {
                        this.$showError('场地经度信息缺失，请重新维护活动供应商地址信息后选择！',{customFlag:true});
                        return false;
                    }
                }
            }
            if( !this.$utils.isEmpty(restaurantLat) ) {
                if (restaurantLat[0].base.require) {
                    if (this.$utils.isEmpty(this.option.formData.restaurantLat)) {
                        this.$showError('场地纬度信息缺失，请重新维护活动供应商地址信息后选择！',{customFlag:true});
                        return false;
                    }
                }
            }
            if (!this.$utils.isEmpty(exeCaseCodeFiledExist) && !this.$utils.isEmpty(noPerformCommentsFiledExist)) {
                if (this.$utils.isEmpty(this.option.formData.exeCaseCode) && this.$utils.isEmpty(this.option.formData.noPerformComments)) {
                    this.$message.warn('基础信息【执行案编码】以及【无执行案说明】二者不能同时为空，请确认',{customFlag:true});
                    return false;
                }
                if (!this.$utils.isEmpty(this.option.formData.exeCaseCode) && !this.$utils.isEmpty(this.option.formData.noPerformComments)) {
                    this.option.formData.noPerformComments = "";
                }
            }
            if (!this.$utils.isEmpty(ordererPhoneFiledExist) && !this.$utils.isEmpty(this.option.formData.ordererPhone)) {
                if (!/^1[3456789]\d{9}$/.test(this.option.formData.ordererPhone)) {
                    this.$message.warn(`${ordererPhoneFiledExist[0].values['fieldName']}格式不正确,请输入11位有效手机号码`,{customFlag:true});
                    return false;
                }
            }
            if (!this.$utils.isEmpty(signerPhoneFiledExist) && !this.$utils.isEmpty(this.option.formData.signerPhone)) {
                if (!/^1[3456789]\d{9}$/.test(this.option.formData.signerPhone)) {
                    this.$message.warn(`${signerPhoneFiledExist[0].values['fieldName']}格式不正确,请输入11位有效手机号码`,{customFlag:true});
                    return false;
                }
            }
            if (!this.$utils.isEmpty(eventPhoneFiledExist) && !this.$utils.isEmpty(this.option.formData.eventPhone)) {
                if (!/^1[3456789]\d{9}$/.test(this.option.formData.eventPhone)) {
                    this.$message.warn(`${eventPhoneFiledExist[0].values['fieldName']}格式不正确,请输入11位有效手机号码`,{customFlag:true});
                    return false;
                }
            }
            if (!this.$utils.isEmpty(executorPhoneFiledExist) && !this.$utils.isEmpty(this.option.formData.executorPhone)) {
                if (!/^1[3456789]\d{9}$/.test(this.option.formData.executorPhone)) {
                    this.$message.warn(`${executorPhoneFiledExist[0].values['fieldName']}格式不正确,请输入11位有效手机号码`,{customFlag:true});
                    return false;
                }
            }
            if (!this.$utils.isEmpty(targetPhoneFiledExist) && !this.$utils.isEmpty(this.option.formData.targetPhone)) {
                if (!/^1[3456789]\d{9}$/.test(this.option.formData.targetPhone)) {
                    this.$message.warn(`${targetPhoneFiledExist[0].values['fieldName']}格式不正确,请输入11位有效手机号码`,{customFlag:true});
                    return false;
                }
            }
            if (this.option.formData.activityName.length > 100) {
                this.$message.warn(`活动名称字数上限为100字，请修改后保存`,{customFlag:true});
                return false;
            }
            for (let i = 0; i < this.editSubControlList.length; i++) {
                const data = this.editSubControlList[i];
                // 如果必输,则校验必输的值是否为空
                if (!!data.base['require']) {
                    if (!this.option.formData[data.values['field']]) {
                        const prefix = ['DraggableText'].includes(data['uiType']) ? '请输入' : '请选择';
                        this.$message.warn(`${prefix}${data.base.label}`,{customFlag:true});
                        return false;
                    }
                }
                // 如果是配置的link-number-keyboard组件，用户觉得组件使用的官方的键盘弹出慢，
                // 在newActivityDynamicComponents调整为link-input 类型为number。
                // 需要加校验【iOS手机弹出的键盘还可以切换，安卓就是数字键盘】
                if(data.ctrlCode === 'link-number-keyboard') {
                    if (!this.$utils.isEmpty(this.option.formData[data.values['field']])) {
                        const regexp = /^[0-9]*$/;
                        const flag = regexp.test(this.option.formData[data.values['field']]);
                        if(!flag) {
                            this.$message.warn(`${data.base.label}应为纯数字，请检查。`,{customFlag:true});
                            return false;
                        }
                    }
                }
                if (this.option.formData.actualNum && (this.option.formData.actualNum <= 0 || this.option.formData.actualNum > 99999)) {
                    this.$showError('消费者人数须大于0且小于等于99999！');
                    return false;
                }
                if (this.option.formData.dinnersNum && (this.option.formData.dinnersNum <= 0 || this.option.formData.dinnersNum > 99999)) {
                    this.$showError('用餐人数须大于0且小于等于99999！');
                    return false;
                }
                if (this.option.formData.tableNum && (this.option.formData.tableNum <= 0 || this.option.formData.tableNum > 1000)) {
                    this.$showError('桌数须大于0且小于等于1000！');
                    return false;
                }
            }
        },
        /**
         * 下一步
         * <AUTHOR>
         * @date 2020-08-05
         * scenarioType:next/back next标志正常流程往下走 back点左上角返回需要保存信息后返回
         * */
        async nextStep(scenarioType) {
            if(!this.$utils.isEmpty(scenarioType)){
               this.onlySaveDataFlag = false;
            }
            if (scenarioType === 'back') {
                this.$refs.udfBackDialog.hide();
            }
            try {
                this.$utils.showLoading();
                if(!this.checkData() && this.checkData() !== undefined){
                    return false;
                }
                //弹窗确认
                if (this.option.formData.row_status === ROW_STATUS.NEW && !this.salesAddressConfirmFlag) {
                    this.$refs.salesAddressConfirmDialog.show();
                    return false;
                }
                //弹窗确认 开始时间修改 与 互动模板时间 提示逻辑
                if (Boolean(this.option.formData.actJson)&& !this.$utils.isEmpty(this.pageSource)&&(this.initStartTime !== this.option.formData.startTime || this.initEndTime !== this.option.formData.endTime)) {
                    this.scType=scenarioType;
                    this.changeTimeFlag = false;
                    if(this.option.formData.status === 'New' && (this.option.formData.aproStatus === 'New' || this.option.formData.aproStatus === 'Refused')){
                        this.timeTip= '活动时间已发生变化，如需同步调整互动时间，可在互动配置模块处编辑调整';
                        this.$refs.timeTipDialog.show();
                    }else if(this.option.formData.status === 'Published' && this.option.formData.aproStatus === 'Approve'){
                        this.timeTip= '活动时间已发生变化，如需同步调整互动时间，请在PC端>营销互动管理模块处编辑调整';
                        this.$refs.timeTipDialog.show();
                    }else{
                        this.timeTip= '活动已开始，互动时间不能再修改，请按时完成互动';
                        this.$refs.timeTipDialog.show();
                    }
                }
                //如果活动已保存在数据库 防止用户是新建活动之后 下一步下一步下一步又返回基础信息点下一步清掉营销模板信息，所以这里要查询下 拿到
                if (!this.$utils.isEmpty(this.option.formData['id'] && this.option.formData.row_status !== ROW_STATUS.NEW)) {
                    await this.queryActivityActJsonById();
                }
                //活动名称：受益终端&费用小类&活动开始时间
                if (!this.$utils.isEmpty(this.option.formData['id']) && this.option.formData.row_status !== ROW_STATUS.NEW ) {
                    this.option.formData.row_status = ROW_STATUS.UPDATE;
                }
                //存源代码-陈列品项 【产品大类、中类】
                if (!this.$utils.isEmpty(this.option.formData.prodLargeClass)) {
                    this.option.formData.prodLargeClass = await this.$lov.getValByTypeAndName('PROD_L_CLASS', this.option.formData.prodLargeClass);
                }
                if (!this.$utils.isEmpty(this.option.formData.displayItem)) {
                    this.option.formData.displayItem = await this.$lov.getValByTypeAndName('PROD_M_CLASS', this.option.formData.displayItem);
                }
                this.option.formData.activityType = this.option.formData.actIndeSourCode;
                if(this.$utils.isEmpty(this.option.formData.dispatchStatus)){
                    this.option.formData.dispatchStatus = 'Undispatch'; //2022年5月20日 抽检改造-活动创建前赋值
                }
                const data = await this.$http.post('action/link/marketAct/upsert', this.option.formData);
                //如果编辑基础信息时，更新了业务场景需要重新查询业务场景图片
                const updateBusScensPicFlag = this.$utils.equals(this.busSceneOld,this.option.formData.busScene);
                if(!updateBusScensPicFlag){
                    this.$bus.$emit("refershBusScensPic",this.option.formData.busScene);
                }
                if(this.$utils.isEmpty(this.option.formData.id) && this.addArr.length > 0 || this.deleteArr.length>0){
                    await this.addMemberTier(data.newRow.id,this.addArr,this.deleteArr)
                }
                this.$utils.hideLoading();
                this.option.formData = {...data.newRow};
                this.$dataService.setMarketActivityItem(data.newRow);
                //不是从基础信息组件编辑而来 需要转化界面显示
                if (this.pageFrom !== 'basicInfo') {
                    //显示显示值-陈列品项 【产品大类、中类】
                    if (!this.$utils.isEmpty(this.option.formData.prodLargeClass)) {
                        const prodLargeClass = await this.$lov.getNameByTypeAndVal('PROD_L_CLASS', this.option.formData.prodLargeClass);
                        this.$set(this.option.formData, 'prodLargeClass', prodLargeClass);
                    }
                    if (!this.$utils.isEmpty(this.option.formData.displayItem)) {
                        const displayItem = await this.$lov.getNameByTypeAndVal('PROD_M_CLASS', this.option.formData.displayItem);
                        this.$set(this.option.formData, 'displayItem', displayItem);
                    }
                }
                // executiveFeedback 执行反馈-编辑基础信息-编辑之后返回
                // 2020/10/14拓展除新建活动外 其余情况编辑基础信息 通通保存返回。 pageSource为空时是新建活动的情况 其余场景编辑基础信息时pageSource都有值
                // 2021-07-02当pageSource有值，但scenarioType为空，onlySaveDataFlag为true时，为创建名单场景不需要直接返回而是需要弹窗确认名单提报
                // 2021-12-22 如果时间被改变会与互动配置有判断逻辑 此时changeTimeFlag 会为false 页面返回由changeTime处理
                if (!this.$utils.isEmpty(this.pageSource) && !(this.$utils.isEmpty(scenarioType) && this.onlySaveDataFlag) && this.changeTimeFlag) {
                    //缓存跳转而来的直接返回首页，上个页面的函数是无效的，没办法缓存。
                    if(this.pageParam.__fromCache){
                        this.$nav.backAll();
                    } else {
                        this.pageParam.callback(this.option.formData);
                        this.$nav.back();
                    }
                } else {
                    // 2021-12-22 如果时间被改变会与互动配置有判断逻辑 此时changeTimeFlag 会为false 页面返回由changeTime处理
                    if (scenarioType === 'next' && this.changeTimeFlag ) {
                        this.$dataService.setMarketActivityItem(data.newRow);
                         this.$nav.push('/pages/lj-market-activity/market-activity/market-activity-cost-list-new-page', {
                            costListData: this.costListData,//用于缓存费用的情况
                            data: data.newRow,
                            activityId: data.newRow.id,
                            operant: 'NEW',
                            scene: this.scene,
                            pageSource: 'view',
                            /**
                             * 控制是否可以操作 cash-new 和 prod-new组件的删除、新建、以及删除兑付方式的操作
                             * 只允许在编辑界面删除兑付方式、删除明细 、新增明细。其他界面可以编辑小计 和 编辑某个产品信息
                             * */
                            operateFlag: true,
                            maintenanceModules: this.maintenanceModules,
                            sceneSourceForNavigation: this.sceneSourceForNavigation,
                            interactiveConfigRequire: this.pageParam.interactiveConfigRequire,
                            callback: (data) => {
                                this.option.formData = {...data}
                            },
                        });
                    }
                    // 2021-12-22 如果时间被改变会与互动配置有判断逻辑 此时changeTimeFlag 会为false 页面返回由changeTime处理
                    else if (scenarioType === 'back'&& this.changeTimeFlag) {
                        let pages = Taro.getCurrentPages();    //获取当前页面信息栈
                        const targetIndex = pages.findIndex(function (item) {
                            return item.route === "pages/lj-market-activity/market-activity/market-activity-list-page";
                        });
                        if (targetIndex === -1) {
                            return this.$nav.backAll()
                        }
                        const num = Number(pages.length - (Number(targetIndex) + 1));
                        setTimeout(() => {
                            this.$bus.$emit('marketActivityListRefresh');
                            this.$nav.back(null, num);
                        }, 1000)
                    } else if (this.$utils.isEmpty(scenarioType) && this.onlySaveDataFlag){
                        //  创建名单时
                        const param = {
                            pageFlag: false,
                            filtersRaw: [
                                {id: 'mcActId', property: 'mcActId', value: this.option.formData.id},
                            ]
                        };
                        const data = await this.$http.post('action/link/maInterUser/queryByExamplePage', param);
                        data.rows.forEach(item => {
                            this.$set(item, 'row_status', ROW_STATUS.UPDATE);
                        });
                        this.actListOwnerData = data.rows;
                        if(this.$utils.isEmpty(this.actListOwnerData)){
                            this.$message.warn('请先选择对接人',{customFlag:true});
                            return false;
                        }
                        this.$refs.activityListReportDialog.show();
                    }
                }
            } catch (e) {
                this.$utils.hideLoading();
                console.log('保存市场活动报错！' + JSON.stringify(e));
            } finally {
                this.$utils.hideLoading();
            }
        },

        /**
         * 查询系统配置参数
         * 用于控制指定公司组织下用户查询时不查multiAcctMainFlag=N 的数据。
         * 受益客户、活动新建陈列协议客户名称、参与终端/经销商、宴席推荐终端四个字段（子对象）使用
         * */
        async queryCfgProperty(){
            const data = await this.$http.post('action/link/cfgProperty/queryByExamplePage', {
                filtersRaw: [
                    {
                        id: 'key',
                        property: 'key',
                        value: 'MC_NO_SUB_ACCOUNT_ORG_CODE',
                    }
                ]
            });
            if (data.success && data.rows && data.rows.length) {
                this.noSubAccountOrgCode = data.rows[0].value
            } else {
                this.noSubAccountOrgCode = 'noMatchId'
            }
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/5/14
         * @methods 选择目标人群后
         * @para
         * @description  根据公司id查询带出默认的忠诚度计划
         * 如获取到信息忠诚度计划信息，则将忠诚度计划赋值到忠诚度计划字段上，同时前端将字段忠诚度计划、会员等级、邀约人字段显示出来，忠诚度计划字段不允许修改；

         （2）如查不到忠诚度计划，弹窗告诉他“提示该公司没有会员体系，无法创建会员活动，目标人群将更新为普通消费者”，只给一个确认按钮，点确认则更新目标人群为普通消费者OrdinaryConsumers；

         （3）反之当用户主动选择普通消费者时，则不需要掉接口也不需要展示后续忠诚度计划、会员等级、邀约人字段给用户填写；
         */
        async pickTargetPopulationChange(obj){
            if(obj.val === 'Member'){
                try {
                    const data = await this.$http.post(this.$env.appURL + '/loyalty/loyalty/programPartner/getProgramId', {
                        id: this.option.formData.companyId,
                        attr1: 'Y'
                    });
                    if (data.success) {
                        this.$set(this.option.formData, 'programId', data.result.programId);
                        this.$set(this.option.formData, 'programName', data.result.programName);
                        if(data.noloyaltysystem){
                            this.$dialog({
                                title: '提示',
                                content: `该公司没有会员体系，无法创建会员活动，目标人群将更新为普通消费者！`,
                                cancelButton: false,
                                confirmText: '确认',
                                onConfirm: () => {
                                    this.$set(this.option.formData, 'targetPopulation', 'OrdinaryConsumers');
                                    this.$set(this.option.formData, 'programId', null);
                                    this.$set(this.option.formData, 'programName', null);
                                    this.$set(this.option.formData, 'memberTier', null);
                                    this.$set(this.option.formData, 'memberTierId', null);
                                    this.$set(this.option.formData, 'inviterName', null);
                                    this.$set(this.option.formData, 'inviterId', null);
                                },
                            })
                        }
                    }else{
                        this.$showError('查询忠诚度计划失败！', {icon: 'none',customFlag:true});
                        this.$set(this.option.formData, 'targetPopulation', 'OrdinaryConsumers');
                        this.$set(this.option.formData, 'programId', null);
                        this.$set(this.option.formData, 'programName', null);
                        this.$set(this.option.formData, 'memberTier', null);
                        this.$set(this.option.formData, 'memberTierId', null);
                        this.$set(this.option.formData, 'inviterName', null);
                        this.$set(this.option.formData, 'inviterId', null);
                    }
                }catch (e){
                    this.$showError('查询忠诚度计划失败！', {icon: 'none',customFlag:true});
                    this.$set(this.option.formData, 'targetPopulation', 'OrdinaryConsumers');
                    this.$set(this.option.formData, 'programId', null);
                    this.$set(this.option.formData, 'programName', null);
                    this.$set(this.option.formData, 'memberTier', null);
                    this.$set(this.option.formData, 'memberTierId', null);
                    this.$set(this.option.formData, 'inviterName', null);
                    this.$set(this.option.formData, 'inviterId', null);
                }
            }else if(obj.val ==='OrdinaryConsumers'){
                this.$set(this.option.formData, 'targetPopulation', 'OrdinaryConsumers');
                this.$set(this.option.formData, 'programId', null);
                this.$set(this.option.formData, 'programName', null);
                this.$set(this.option.formData, 'memberTier', null);
                this.$set(this.option.formData, 'memberTierId', null);
                this.$set(this.option.formData, 'inviterName', null);
                this.$set(this.option.formData, 'inviterId', null);
            }
        },
        /**
         * @createdBy  lilangtao
         * @date  2022/9/6
         * @methods 获取默认忠诚度
         * @para
         * @description
           */
         async getTargetPopulation(){
             try{
            const data = await this.$http.post(this.$env.appURL + '/loyalty/loyalty/programPartner/getProgramId',{id: this.option.formData.companyId,attr1: 'Y'});
            if (data.success) {
                this.$set(this.option.formData, 'programId', data.result.programId);
                this.$set(this.option.formData, 'programName', data.result.programName);
                if(data.noloyaltysystem){
                    this.$dialog({
                        title: '提示',
                        content: `该公司没有会员体系，无法创建会员活动，目标人群将更新为普通消费者！`,
                        cancelButton: false,
                        confirmText: '确认',
                        onConfirm: () => {
                            this.$set(this.option.formData, 'targetPopulation', 'OrdinaryConsumers');
                            this.$set(this.option.formData, 'programId', null);
                            this.$set(this.option.formData, 'programName', null);
                            this.$set(this.option.formData, 'memberTier', null);
                            this.$set(this.option.formData, 'memberTierId', null);
                            this.$set(this.option.formData, 'inviterName', null);
                            this.$set(this.option.formData, 'inviterId', null);
                        },
                    })
                }
            }else{
                this.$showError('查询忠诚度计划失败！', {icon: 'none',customFlag:true});
                this.$set(this.option.formData, 'targetPopulation', 'OrdinaryConsumers');
                this.$set(this.option.formData, 'programId', null);
                this.$set(this.option.formData, 'programName', null);
                this.$set(this.option.formData, 'memberTier', null);
                this.$set(this.option.formData, 'memberTierId', null);
                this.$set(this.option.formData, 'inviterName', null);
                this.$set(this.option.formData, 'inviterId', null);
            }
             }catch (e) {
                 this.$showError('查询忠诚度计划失败！', {icon: 'none',customFlag:true});
                 this.$set(this.option.formData, 'targetPopulation', 'OrdinaryConsumers');
                 this.$set(this.option.formData, 'programId', null);
                 this.$set(this.option.formData, 'programName', null);
                 this.$set(this.option.formData, 'memberTier', null);
                 this.$set(this.option.formData, 'memberTierId', null);
                 this.$set(this.option.formData, 'inviterName', null);
                 this.$set(this.option.formData, 'inviterId', null);
             }
        },

        /**
         * @createdBy  张丽娟
         * @date  2021/5/18
         * @methods pickMemberTier
         * @para
         * @description 选择会员等级
         */
        async pickMemberTier(){
            let selectedData = []
            let notSelectedData = []
            if(this.$utils.isEmpty(this.option.formData.id)){
                selectedData = this.addArr;
                let deepNotSelectedData = await this.fetchMemberTierOptionNotSelected()
                notSelectedData = [...deepNotSelectedData].filter((x) => [...selectedData].every((y) => y.id !== x.id));
            }else{
                selectedData = await this.fetchMemberTierOptionSelected()
                notSelectedData = await this.fetchMemberTierOptionNotSelected()
            }
            selectedData.forEach((item)=>{
                item.selectedFlag = true
            })
            notSelectedData.forEach((item)=>{
                item.selectedFlag = false
            })
            this.memberTierData = selectedData.concat(notSelectedData)
            this.memberTierDialogFlag = true
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/5/18
         * @methods addMemberTier
         * @para
         * @description 更新会员等级数据
         */
        async addMemberTier(id,addArr,deleteArr){
            let arr  = []
            addArr.forEach((item)=>{
                let paramAdd= {
                    defField: "isDefault",
                    mvgMapperName: "loyaltyTier",
                    mvgParentId: id || null,
                    mvgSubsetId: item.id,
                    row_status: "NEW"
                }
                arr.push(paramAdd)
            })
            deleteArr.forEach((item)=>{
                let paramDelete= {
                    defField: "isDefault",
                    mvgMapperName: "loyaltyTier",
                    mvgParentId: id || null,
                    mvgSubsetId: item.id,
                    row_status: "DELETE"
                }
                arr.push(paramDelete)
            })
            try{
                const dataRight = await this.$http.post(this.$env.appURL + '/marketactivity/link/mvg/mvgModify', arr);
                if(dataRight.success){
                    this.memberTierDialogFlag = false;
                }else{
                    this.$showError('更新会员等级失败！', {icon: 'none',customFlag:true});
                    this.memberTierDialogFlag = false;
                }
            }catch (e) {
                this.$showError('更新会员等级失败！', {icon: 'none',customFlag:true});
            }
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/5/18
         * @methods fetchMemberTierOptionSelected
         * @para
         * @description mvg-选中的会员等级
         */
        async fetchMemberTierOptionSelected(){
                let param = {
                    mvgParentId: this.option.formData.id || null,
                    mvgMapperName: 'loyaltyTier',
                    order: 'desc',
                    pageFlag: false,
                    sort: 'id',
                }
                const dataRight = await this.$httpForm.post(this.$env.appURL + '/marketactivity/link/mvg/queryRightListPage', param);
                if(dataRight.success){
                    this.memberTierOptionSelectedArr = [...dataRight.rows]
                    return dataRight.rows
                }else{
                    this.$showError('查询消费者等级失败！', {icon: 'none',customFlag:true});
                }
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/5/18
         * @methods fetchMemberTierOptionNotSelected
         * @para
         * @description mvg -未选的会员等级
         */
        async fetchMemberTierOptionNotSelected(){
            let filtersRaw = [{id: 'programId', property: 'programId', value: this.option.formData.programId}]
            let leftParam = {
                mvgParentId: this.option.formData.id || null,
                mvgMapperName: 'loyaltyTier',
                order: 'desc',
                pageFlag: false,
                sort: 'id',
                filtersRaw: JSON.stringify(filtersRaw)
            }
            const dataLeft = await this.$httpForm.post(this.$env.appURL + '/marketactivity/link/mvg/queryLeftListPage', leftParam);
            if (dataLeft.success) {
                this.memberTierOptionNotSelectedArr = dataLeft.rows
                return dataLeft.rows
            }else{
                this.$showError('查询消费者等级失败！', {icon: 'none',customFlag:true});
            }
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/5/18
         * @methods pickInviterId
         * @para
         * @description 选择邀约人
         */
        async pickInviterId(){
            const salesDistrictData = await this.$object(this.inviterOption, {
                multiple: false,
                pageTitle: '邀约人'
            });
            const inviterName = salesDistrictData.name || salesDistrictData.mobilePhone;
            this.$set(this.option.formData, 'inviterId', salesDistrictData.id);
            this.$set(this.option.formData, 'inviterName', inviterName);
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/5/18
         * @methods selectMemberTier
         * @para
         * @description 选择会员等级
         */
        selectMemberTier(val) {
            val.selectedFlag = !val.selectedFlag;
            let selectNum = this.memberTierData.filter(item => item.selectedFlag === false);
            this.allSelectFlag = selectNum.length === 0;
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/5/18
         * @methods allSelect
         * @para
         * @description 全选
         */
        allSelect() {
            this.allSelectFlag = !this.allSelectFlag;
            this.memberTierData.forEach(item => {
                item.selectedFlag = this.allSelectFlag
            })
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/5/18
         * @methods sureAddMemberTier
         * @para
         * @description 确认添加会员等级
         */
        async sureAddMemberTier() {
            let selectedRows = this.memberTierData.filter(item => item.selectedFlag === true);
            if (selectedRows.length === 0) {
                this.$utils.showAlert('请选择会员等级', {icon: 'none'});
                return
            }
            let addArr = []
            let deleteArr = []
            // 新建-无市场活动id时，
            if(this.$utils.isEmpty(this.option.formData.id)){
                addArr = selectedRows;
            }else{
                addArr = [...selectedRows].filter((x) => [...this.memberTierOptionSelectedArr].every((y) => y.id !== x.id));
                deleteArr = [...this.memberTierOptionSelectedArr].filter((x) => [...selectedRows].every((y) => y.id !== x.id));
            }
            this.addArr = addArr;
            this.deleteArr = deleteArr;
            if(this.$utils.isNotEmpty(this.option.formData.id)){
                await this.addMemberTier(this.option.formData.id,this.addArr,this.deleteArr)
            }
            this.$set(this.option.formData, 'memberTierId', selectedRows[0].id);
            this.$set(this.option.formData, 'memberTier', selectedRows[0].tierName);
            this.memberTierDialogFlag = false
        },
        /**
         * @createdBy  宋燕荣
         * @date  2021/6/29
         * @methods pickActName
         * @para
         * @description 选择对接人
         */
        pickActName(){
            this.$nav.push('/pages/lj-market-activity/market-activity/activity-list-owner-page',{
                activityItem : this.option.formData,
                callback: (data) => {
                    this.$set(this.option.formData, 'actListOwnerId', data.id);
                    this.$set(this.option.formData, 'actListOwner', data.name);
                },
            });
        },
        /**
         * @createdBy  宋燕荣
         * @date  2021/6/29
         * @methods createdList
         * @para
         * @description 创建名单
         */
        async createdActivityListReport(){
            if (this.$utils.isEmpty(this.option.formData.id)) {
                return false;
            }
            if(!this.checkData() && this.checkData() !== undefined) {
                return false;
            }
            this.onlySaveDataFlag = true;
            //需要先保存市场活动。
            await this.nextStep();
        },
        /**
         * @createdBy  宋燕荣
         * @date  2021/6/29
         * @methods createdList
         * @para
         * @description 名额提报调整-保存按钮
         */
        async saveSubmitPlaces(){
            //2022年4月14日 接口调整到智零
            let activityReport = [];             // 活动提报总人数
            // 计算字段，活动提报名额统计
            this.actListOwnerData.forEach((item)=> {
                activityReport.push({
                    id: item.id,
                    mcActId: this.activityItem.id,
                    row_status: ROW_STATUS.UPDATE,
                    submitPlaces: item.submitPlaces ? item.submitPlaces : 0,
                    userPostnId: item.userPostnId
                })
            });
            const data = await this.$http.post(this.$env.appURL +'/marketactivity/link/maInterUser/newSaveSubmitPlaces',activityReport);
            if(data.success){
                this.$message.success('活动名单保存成功',{customFlag:true});
                const actdata = await this.$http.post('action/link/marketAct/update', {
                    id: this.activityItem.id,
                    registNum: data.allSubmitPlaces,
                    rowVersion: this.option.formData.rowVersion,
                    updateFields: "id,registNum,rowVersion",
                });
                this.option.formData = {...actdata.newRow};
                this.$dataService.setMarketActivityItem(actdata.newRow);
                this.$set(this.activityItem,'registNum',data.allSubmitPlaces);
            }else{
                this.$showError('活动名单保存失败',{customFlag:true});
            }
            this.$refs.activityListReportDialog.hide();
        },
        /**
         * @createdBy  宋燕荣
         * @date  2021/6/29
         * @methods createdList
         * @para
         * @description 名额提报调整-保存并下发
         */
        async saveIssueSubmitPlaces(){
            //2022年4月14日 接口调整到智零
            let activityReport = [];             // 活动提报总人数
            this.actListOwnerData.forEach((item)=> {
                activityReport.push({
                    id: item.id,
                    mcActId: this.activityItem.id,
                    row_status: ROW_STATUS.UPDATE,
                    submitPlaces: item.submitPlaces ? item.submitPlaces : 0,
                    userPostnId: item.userPostnId
                })
            });
            const data = await this.$http.post(this.$env.appURL +'/marketactivity/link/maInterUser/newSaveSubmitPlaces',activityReport);
            if(data.success){
                const submit = await this.$http.post(this.$env.appURL +'/marketactivity/link/marketActivity/newIssuedByList',{id:this.activityItem.id})
                if(submit.success){
                    this.$message.success('已成功下发活动名单',{customFlag:true});
                    const actdata = await this.$http.post('action/link/marketAct/update', {
                        id: this.activityItem.id,
                        registNum: data.allSubmitPlaces,
                        rowVersion: this.option.formData.rowVersion,
                        updateFields: "id,registNum,rowVersion",
                    });
                    this.option.formData = {...actdata.newRow};
                    this.$dataService.setMarketActivityItem(actdata.newRow);
                    this.$set(this.activityItem,'registNum',data.allSubmitPlaces);
                }else{
                    this.$showError('下发活动名单失败',{customFlag:true});
                }
            }else{
                this.$showError('保存名单失败',{customFlag:true});
            }
            this.$refs.activityListReportDialog.hide();
        },
        async getCurrentActivityBusSceneData(){
            let msg = "";
            let busSceneString = this.$utils.deepcopy(this.option.formData.busScene).toString();
            busSceneString = '[' + busSceneString + ']';
            const data = await this.$http.post('action/link/basic/queryByExamplePage', {
                filtersRaw: [
                    {"id": "type", "property": "type", "value": 'TMPL_SUB_BIZ_TYPE'},
                    {"id": "activeFlag", "property": "activeFlag", "value": 'Y'},
                    {"id": "useFlag", "property": "useFlag", "value": 'N'},
                    {"id": "parentVal", "property": "parentVal", "value": 'businessScenario'},
                    {"id": "val", "property": "val", "value": busSceneString, "operator": 'in'},
                ]
            });
            if(!this.$utils.isEmpty(data.rows)){
                let b = "您删除的业务场景中";
                let c = "";
                for (let i = 0; i < this.option.formData.busScene.length; i++) {
                    const a = data.rows.filter(item => item.val === this.option.formData.busScene[i]);
                    if (!this.$utils.isEmpty(a)) {
                        c = c + "【" + a[0].name + "】";
                    }
                }
                msg = b + c + "当前已取消应用，删除后只能选择本财年有效应用的业务场景进行执行反馈，是否确认删除？";
                return msg;
            } else {
                msg = this.$utils.deepcopy(this.removeBusSceneMsg);
                return msg;
            }
        },
        async removeBusScene(){
            if(this.$utils.isEmpty(this.option.formData.busScene)){
                return false;
            }
            const msg = await this.getCurrentActivityBusSceneData();
            const that = this;
            if (this.option.formData.row_status !== ROW_STATUS.NEW) {
                this.$dialog({
                    title: '提示',
                    content: msg,
                    cancelButton: true,
                    onConfirm: async () => {
                        that.$utils.showLoading();
                        const updataActivity = {
                            id: that.option.formData.id,
                            rowVersion: that.option.formData.rowVersion,
                            busScene:"",
                            updateFields: "id,busScene,rowVersion"
                        };
                        const data = await that.$http.post('action/link/marketAct/update', updataActivity,{
                            handleFailed: (error) => {
                                that.$utils.hideLoading();
                            }
                        });
                        that.option.formData = {...data.newRow};
                        this.$dataService.setMarketActivityItem(data.newRow);
                        that.$utils.hideLoading();
                    },
                    onCancel: () => {
                    }
                });
            } else {
                that.$set(that.option.formData, 'busScene', "");
            }
        }
    }
}
</script>

<style lang="scss">
.new-acticity-basic-page {
    .model-title {
        display: flex;
        margin-left: 24px;

        .title {
            font-family: PingFangSC-Regular, serif;
            font-size: 32px;
            color: #262626;
            letter-spacing: 0;
            text-align: center;
            line-height: 80px;
            height: 80px;
            width: 90%;
            padding-left: 40px;
        }

        .icon-close {
            color: #BFBFBF;
            font-size: 32px;
            line-height: 80px;
            height: 80px;
        }
    }

    .select-box {
        @include flex-start-center;
        padding: 20px;
        margin-left: 24px;
        margin-right: 24px;
        border-bottom: 1px solid #F2F2F2;

        .select-left {
            width: 100%;
            font-family: PingFangSC-Regular, serif;
            font-size: 28px;
            color: #262626;
            letter-spacing: 0;
            .select-row{
                display: flex;
                .store-name {
                    width: 80%;
                    padding-bottom: 28px;
                }
                .unit{
                    text-align: right;
                }
            }
        }

        .select-right {
            margin-right: 24px;

            .iconfont {
                font-size: 40px;
                color: #bfbfbf;
            }

            .icon-yiwanchengbuzhou {
                font-size: 40px;
                color: #2F69F8;
            }
        }
    }
    .activity-type-view {
        .activity-type {
            height: 144px;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            -moz-background-size: 100% 100%;
            margin: 0 24px 24px 24px;

            .type-title {
                text-align: center;
                color: white;
                font-size: 32px;
                width: 100%;
                padding-top: 40px;
            }
        }
    }

    .zero-view {
        width: 100%;
        height: 30px;
    }
    .bus-scene-row{
        .link-item-body-left{
            width: 160px;
            flex: none!important;
        }
        .link-item-body-right{
            .input{
                flex: 1!important;
            }
            .ent-wrap{
                width: 80px;
            }
        }
    }
}
</style>
