<template>
    <link-page class="market-activity-item-page">
        <approval-history-point :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)"></approval-history-point>
        <!--模板无权限-->
        <lnk-no-auth v-if="authFlag"></lnk-no-auth>
        <view v-else>
            <!--顶部-->
            <lnk-taps :taps="tapsOptions" v-model="tapsActive" @switchTab="switchTab"
                      v-if="$utils.isEmpty(approvalId)"></lnk-taps>
            <!--基础信息-->
            <basic-info :pageSource="code" :activityItem="activityItem" @updateActivity="updateActivityFun"
                        :subControlList="subControlList" id="basic-info-v"
                        :editSubControlList="editSubControlList" v-if="basicInfoFlag"></basic-info>
            <!--费用信息-->
            <cost-new ref="costNew" :parentData="activityItem" :scene="'apply'" :pageSource="code" v-if="costNewFlag"
                      id="cost-info-v"></cost-new>
            <!--互动配置-->
            <interactive-config :parentData="activityItem" :btnShow="true" :titleShow="true" id="interactive-config-v"
                                :interactiveConfigRequire="interactiveConfigRequire"
                                :pageSource="code" v-if="interactiveConfigFlag"></interactive-config>
            <!--消费者-->
            <consumers :parentId="activityItem.id" :haveMarginFlag="true" :addBtnFlag="addBtnFlag" id="consumers-v"
                       :updateAnalysisData.sync="updateAnalysisData"
                       v-if="consumersFlag" :pageSource="code" :parentData="activityItem"
                       :operationFlag="operationFlag"></consumers>
            <!--内部人员-->
            <insiders :haveMarginFlag="true" :addBtnFlag="addBtnFlag" :parentId="activityItem.id"
                      :operationFlag="operationFlag"
                      v-if="insidersFlag" :pageSource="code" :parentData="activityItem" id="insiders-v"></insiders>
            <!--参与终端/经销商-->
            <terminal :parentId="activityItem.id" :haveMarginFlag="true" :addBtnFlag="addBtnFlag" id="terminal-v"
                      :operationFlag="operationFlag"
                      v-if="terminalFlag" :pageSource="code" :parentData="activityItem"></terminal>
            <!--活动动销 需传是否有边框 title 展示数量字段 实际 actualNumFlag or预计 estimatedNumFlag or 申请 applyNumFlag -->
            <production-pin :haveMarginFlag="true" :parentData="activityItem" v-if="productionPinFlag"
                            :parentType="'act'" :title="'活动动销'" id="pre-pio-pin-v"
                            :estimatedNumFlag="true" :pageSource="code"></production-pin>
            <!--执行反馈-->
            <execution-feedback :pageSource="code" :scene="'actual'" id="execution-feedback-v"
                                :collectId="collectId" :signInId="signInId"
                                :parentData="activityItem" v-if="executionFeedbackFlag"></execution-feedback>
            <!--稽核记录-->
            <audit :parentId="activityItem.id" :pageSource="code" v-if="auditFlag" id="audit-list-v"></audit>
            <!--报销进度-->
            <progress-new :parentId="activityItem.id" :pageSource="code" v-if="progressNewFlag"
                          id="progress-v"></progress-new>
            <!--审批历史-->
            <approval-records :parentId="activityItemId" id="approval-records-v"></approval-records>
            <!--参与情况-->
            <participation v-if="activityItem.id" :mcActId="activityItem.id" id="activity-analysis-v"
                           :updateAnalysisData="updateAnalysisData"/>
        </view>
        <link-fab-group v-if="code === 'view'">
            <link-fab-item icon="icon-yiwanchengbuzhou" label="开通手动编辑" @tap-icon="()=> isOpenFlag()"
                           v-if="visiblePersonnelId.includes(userInfo.username) && !enableManualEditing && isRequired"/>
            <link-fab-item icon="icon-yijianbaobei1" label="一键报备" @tap-icon="()=>reportedKey()"/>
            <link-fab-item icon="icon-fenxiang" label="分享海报" @tap-icon="()=>activityInvitation()"/>
            <link-fab-item icon="icon-guanlian" label="关联执行案" @tap-icon="()=>associatedExecution()"
                           v-if="associatedExecutionFlag"/>
            <link-fab-item icon="icon-huodongjihe" label="活动稽核" v-if="activityAuditFlag"
                           @tap-icon="()=>activityApprovaAudit()"/>
            <link-fab-item icon="icon-zhihangfankui" label="执行反馈" @tap-icon="()=>executionFeedback()"
                           v-if="executionFeedbackBtnFlag && !isSalesAreaManager"/>
        </link-fab-group>
        <view style="width: 100%;height: 64px"
              v-if="($utils.isEmpty(approvalId) && code === 'preview') || submitFlag"></view>
        <view v-if="!$utils.isEmpty(approvalId)" style="width: 100%;height: 176px"></view>
        <link-sticky>
            <view class="link-fab-group-sticky" v-if="$utils.isEmpty(approvalId) && code === 'preview'">
                <link-fab-group groupButtonType="normal" buttonAlign="start">
                    <link-fab-item @tap-icon="()=>reportedKey()">
                        <view slot="icon" style="display: flex;flex-wrap: nowrap;align-items: center;width: 80px">
                            <link-icon icon="icon-yijianbaobei1"/>
                            <view style="white-space: nowrap;font-size: 12px">一键报备</view>
                        </view>
                    </link-fab-item>
                    <link-fab-item @tap-icon="()=>activityInvitation()">
                        <view slot="icon" style="display: flex;flex-wrap: nowrap;align-items: center;width: 80px">
                            <link-icon icon="icon-fenxiang"/>
                            <view style="white-space: nowrap;font-size: 12px">分享</view>
                        </view>
                    </link-fab-item>
                </link-fab-group>
                <link-button @tap="submit" v-if="submitFlag && !isSalesAreaManager" autoLoading>提交</link-button>
                <link-button @tap="createdActivityListReport"
                             v-if="activityListReportFlag && activityItem.status !== 'New'" autoLoading>创建名单
                </link-button>
            </view>
            <link-button block @tap="submit" v-if="code === 'view' && submitFlag && !isSalesAreaManager" autoLoading>
                提交
            </link-button>
            <link-button block @tap="createdActivityListReport"
                         v-if="code === 'view' && activityListReportFlag && activityItem.status !== 'New'" autoLoading>
                创建名单
            </link-button>
            <approval-operator :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)"></approval-operator>
        </link-sticky>
        <link-dialog ref="synNcMakOrderFlagDialog" disabledHideOnClickMask :initial="true">
            <view slot="head">
                活动物料申请单是否同步到NC系统
            </view>
            <view style="width: 100%;text-align: center;">
                <link-form>
                    <link-form-item label="请选择是否同步">
                        <link-lov v-model="activityItem.synNcMakOrderFlag" type="IS_FLAG"></link-lov>
                    </link-form-item>
                </link-form>
            </view>
            <link-button slot="foot" @tap="$refs.synNcMakOrderFlagDialog.hide()">取消</link-button>
            <link-button slot="foot" @tap="submitActivity">确定</link-button>
        </link-dialog>
        <link-dialog ref="activityListReportDialog" disabledHideOnClickMask :initial="true"
                     borderRadius="32rpx 32rpx 0 0">
            <view slot="head">
                名单提报名额调整
            </view>
            <scroll-view scroll-y="true">
                <view v-for="(item, index) in actListOwnerData" :key="index">
                    <view style="width: 100%;height: 28px;line-height: 28px">
                        <view style="width: 50%;float: left">{{ item.firstName }}</view>
                        <view style="width: 50%;float: left">
                            <link-number-keyboard v-model="item.submitPlaces"
                                                  placeholder="请输入提报名额(人)"></link-number-keyboard>
                        </view>
                    </view>
                </view>
            </scroll-view>
            <link-button slot="foot" @tap="$refs.activityListReportDialog.hide()" autoLoading>取消</link-button>
            <link-button slot="foot" @tap="saveSubmitPlaces " autoLoading>保存</link-button>
            <link-button slot="foot" @tap="saveIssueSubmitPlaces" autoLoading>保存并下发</link-button>
        </link-dialog>
        <!--         申请手动编辑-->
        <link-dialog v-model="openFlag" title="申请手动编辑">
            <view style="padding-left: 6px;">
                该操作将开通费用实际用酒数量编辑功能，且不可逆转，是否继续？
            </view>
            <link-button slot="foot" @tap="cancelActivation">取消</link-button>
            <link-button slot="foot" @tap="confirmActivation">确定</link-button>
        </link-dialog>
    </link-page>
</template>

<script>
import LnkTaps from "../../core/lnk-taps/lnk-taps";
import Audit from "./components/audit";
import BasicInfo from "./components/basic-info";
import Consumers from "./components/consumers";
import CostNew from "./components/cost-new";
import ExecutionFeedback from "./components/execution-feedback";
import ProductionPin from "./components/production-pin";
import Insiders from "./components/insiders";
import InteractiveConfig from "./components/interactive-config";
import ProgressNew from "./components/progress-new";
import Terminal from "./components/terminal";
import ApprovalHistoryPoint from "../../lzlj/approval/components/approval-history-point";
import ApprovalOperator from "../../lzlj/approval/components/approval-operator";
import LnkNoAuth from "../../core/lnk-no-auth/lnk-no-auth";
import Taro from "@tarojs/taro";
import {taro} from "../../../utils/taro";
import ApprovalRecords from "./components/approval-records";
import Participation from "./components/participation";
import {ROW_STATUS} from "../../../utils/constant";

export default {
    name: "market-activity-item-page",
    components: {
        Participation,
        ApprovalRecords,
        LnkNoAuth,
        ApprovalOperator,
        ApprovalHistoryPoint,
        Audit,
        BasicInfo,
        Consumers,
        CostNew,
        ExecutionFeedback,
        Insiders,
        InteractiveConfig,
        ProductionPin,
        ProgressNew,
        Terminal, LnkTaps
    },
    data() {
        const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
        const isSalesAreaManager = ['CityManager', 'SalesRegionManager', 'SalesAreaManager',].includes(userInfo.positionType); //是否是片区经理
        const feedbackPicList = [];
        let code = this.pageParam.pageSource;//页面来源
        let approvalId = "";// 审批id
        let activityItemId = "";//活动ID
        let activityItem = {};//活动对象
        const tapsOptions = [];//需要展示的模块tap 从fieldRows来
        const addBtnFlag = false;//是否需要添加按钮
        let associatedExecutionFlag = false;//是否可以操作关联执行案
        let executionFeedbackBtnFlag = false;//是否可以执行反馈
        let activityAuditFlag = false;//是否可以活动稽核
        let submitFlag = false;//是否可以提交活动
        let enableManualEditing = false; //是否开通手动编辑
        let isRequired = false;  //根据是否有required控制开通手动编辑按钮
        let activityListReportFlag = false;//是否可以创建名单
        if (this.code === 'preview' || this.code === 'view') {
            this.activityItemId = this.pageParam.data.id;//活动对象ID
            this.activityItem = this.pageParam.data;//活动对象
        }
        const auditItem = {
            actId: activityItemId
        };//稽核对象
        const sceneObj = {};
        const approval_from = "";
        let operationFlag = true;//是否可以操作删除数据。approvalId为空时可操作
        let consumersRequire = false;//根据类型配置消费者子对象是否必输 默认不必输
        let interactiveConfigRequire = false;//根据类型配置配置模板子对象是否必输 默认不必输
        return {
            visiblePersonnelId: '',
            openFlag: false,
            // 费用价格是否展示标识
            priceShowFlag: false,
            updateAnalysisData: false,          // 是否刷新活动分析数据
            interactiveConfigRequire,
            operationFlag,
            sceneObj,
            approval_from,
            userInfo,
            isSalesAreaManager,
            auditItem,
            associatedExecutionFlag,
            feedbackPicList,
            authFlag: false,//
            fieldRows: [],//当前费用类型需要展示的模块
            basicInfoFlag: false,//基础信息是否展示
            costNewFlag: false,//费用信息是否展示
            interactiveConfigFlag: false,//互动配置是否展示
            consumersFlag: false,//消费者是否展示
            insidersFlag: false,//内部人员是否展示
            terminalFlag: false,//参与终端/经销商是否展示
            productionPinFlag: false,//活动动销是否展示
            executionFeedbackFlag: false,//执行反馈是否展示
            auditFlag: false,//稽核记录是否展示
            progressNewFlag: false,//报销进度是否展示
            subControlList: [],//活动详情-基础信息-展示子组件
            editSubControlList: [],//活动详情-基础信息-编辑子组件
            approvalId,
            activityItemId,
            submitFlag,
            enableManualEditing,
            isRequired,
            activityListReportFlag,
            activityAuditFlag,
            executionFeedbackBtnFlag,
            addBtnFlag,
            activityItem,
            code,
            currentIndex: 0,
            scrollTop: 0,
            tapsOptions,
            consumersRequire,
            tapsActive: {},
            actListOwnerData: [],//活动对接人数据
            collectId: '', // 陈列采集id
            signInId: '', // 陈列采集所对应的拜访记录的打卡id
            //复检部门
            orgnizationOption: new this.AutoList(this, {
                module: 'action/link/orgnization',
                sortOptions: null,
                param: {
                    filtersRaw: [
                        {"id": "auditDeptFlag", "property": "auditDeptFlag", "value": "Y"},
                        {"id": "orgType", "property": "orgType", "value": "Department"},
                        {"id": "isEffective", "property": "isEffective", "value": "Y"}]
                }
            }),
            //关联执行案使用
            caseOption: new this.AutoList(this, {
                url: {
                    queryByExamplePage: 'action/link/actProg/queryByExamplePage'
                },
                param: {
                    // EXECUTION_STATE 执行案状态 全部生效和部分生效
                    //attr5    执行案有效结束时间是否+5天
                    attr5: 'Y',
                    filtersRaw: [
                        {
                            id: 'executionState',
                            property: 'executionState',
                            value: '[AllEfficient,PartEfficient]',
                            operator: 'in'
                        },
                    ]
                },
                sortOptions: null,
                searchFields: ['executionCode', 'executionName'],
                renderFunc: (h, {data, index}) => {
                    return (
                        <item arrow="false"
                              key={index}
                              data={data}>
                            <view
                                style="width: 100%;background: white;height: 105px;"
                                slot='note'>
                                <view style="width: 90%;float: left;">
                                    <view style="display: flex;margin: auto;">
                                        <view
                                            style="-ms-flex-align: center;-webkit-align-items: center;align-items: center;-ms-flex-pack: justify;-webkit-justify-content: space-between;  justify-content: space-between;height: 80rpx;line-height: 80rpx;padding-left: 32rpx;display: flex;width:100%;">
                                            <view
                                                style="background: #A6B4C7;border-radius: 8rpx;line-height: 40rpx;">
                                                <view
                                                    style="font-size: 28rpx;color: #FFFFFF;letter-spacing: 0;line-height: 40rpx;padding: 2rpx 8rpx;">{data.executionCode}
                                                </view>
                                            </view>
                                        </view>
                                    </view>
                                    <view
                                        style=" -ms-flex-align: center;-webkit-align-items: center;align-items: center;-ms-flex-pack: justify;-webkit-justify-content: space-between;justify-content: space-between;height: 80rpx;line-height: 80rpx;padding-left: 32rpx;display: flex;width: 100%;">
                                        <view
                                            style="font-family: PingFangSC-Semibold;font-size: 32rpx;color: #262626;letter-spacing: 0;line-height: 32rpx;">{data.executionName}
                                        </view>
                                    </view>
                                    <view style="padding-left: 32rpx;">
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 28rpx;color: #8C8C8C;letter-spacing: 0;line-height: 28rpx;float: left;">
                                            {this.priceShowFlag ? `可用余额` + data.availableBalance + `,` : ''}
                                        </view>
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 28rpx;color: #8C8C8C;letter-spacing: 0;text-align: left;line-height: 28rpx;">
                                            {this.priceShowFlag ? `审批金额` + data.approvalAmount : ''}
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </item>
                    )
                },
            }),
        }
    },
    onPageScroll(e) {
        this.scrollTop = e.scrollTop
    },
    async created() {
        this.priceShowFlag = await this.$utils.getPriceDesensitize(this.userInfo);
        this.visiblePersonnelId = await this.$utils.getCfgProperty('marketAct_editing')
    },
    async onShow() {
        const that = this;
        that.sceneObj = await that.$scene.ready();
        that.approval_from = that.sceneObj.query['approval_from'];
        that.code = that.pageParam.pageSource;//页面来源
        if (that.code === 'preview' || that.code === 'view') {
            that.activityItemId = that.pageParam.data.id;//活动对象ID
            that.activityItem = that.pageParam.data;//活动对象
            that.operationFlag = true;
        } else if (!(that.code === 'preview' || that.code === 'view')) {//审批
            if (!(that.approval_from === 'qw')) {
                that.approvalId = that.pageParam.data.id;//审批传过来的审批数据ID
                that.activityItemId = that.pageParam.data.flowObjId;//审批传过来的活动对象ID
            } else if (that.approval_from === 'qw') {
                that.approvalId = that.sceneObj.query['approval_id'];
                if (that.$utils.isNotEmpty(that.sceneObj.query['id'])) {
                    that.activityItemId = that.sceneObj.query['id'];
                    that.code = 'view';
                } else {
                    that.activityItemId = that.sceneObj.query['flowObjId'];
                    that.code = '';
                }
            }
            that.operationFlag = false;
        }
        if (that.code === 'preview') {//活动预览-从其他信息界面而来
            that.$taro.setNavigationBarTitle({title: '活动预览'});
        } else if (that.code === 'view') {//活动查看-活动列表进详情
            that.$taro.setNavigationBarTitle({title: '活动详情'});
        } else {
            that.$taro.setNavigationBarTitle({title: '活动审批'});
        }
        await that.queryActivityItemById();
    },
    mounted() {
        this.$bus.$on("costUpdateRefreshMarketActivityItem", async () => {
            await this.queryActivityItemByIdForCostUpdate();
        });
        this.$bus.$on("updateActivityTemplateRefresh", async () => {
            await this.queryActivityTemplateById();
        })
    },
    methods: {
        updateActivityFun(activityItem) {
            if (activityItem.protocolId && activityItem.protocolId !== this.activityItem.protocolId) {
                this.getDisplayCollect(activityItem.protocolId);
            }
            this.activityItem = {...activityItem};
        },
        /**
         * 获取协议关联的陈列采集纪录
         * <AUTHOR>
         * @date	2023/9/11 17:08
         * @param protocolId 协议id
         */
        async getDisplayCollect(protocolId) {
            // 获取陈列采集记录
            const {rows, success} = await this.$http.post('action/link/visitDisplay/queryByExamplePage', {
                rows: 1,
                sort: 'created',
                order: 'desc',
                filtersRaw: [
                    { id: 'agreementId', property: 'agreementId', value: protocolId },
                    { id: 'created', property: 'created', value: this.pageParam.data.feedbackCommitTime, operator: '<'}
                ]
            });
            if (success && rows && rows.length) {
                this.collectId = rows[0].id;
                const {rows: signRows, success: signSuccess} = await this.$http.post('action/link/signInDetails/queryByExamplePage', {
                    headId: rows[0].visitId,
                    pageFlag: false,
                    filtersRaw: [{id: 'signInType', property: 'signInType', value: 'toShop'}]
                });
                if (signSuccess && signRows && signRows.length) {
                    this.signInId = signRows[0].id;
                }
            }
        },
        /**
         * 重新查询费用申请数据
         * <AUTHOR>
         * @date 2022年2月8日
         * */
        async onBack() {
            await this.$refs['costNew'].queryActivityProdAndCostList();
        },
        /**
         * 活动详情数据查询
         * <AUTHOR>
         * @date 2020-09-14
         * */
        async queryActivityItemByIdForCostUpdate() {
            const data = await this.$http.post('action/link/marketAct/queryById', {
                id: this.activityItemId
            });
            this.activityItem = data.result;
            //活动状态 MC_STATUS ：、已发布、进行中、已结束
            //审批状态 APRO_STATUS ：未提交、、已拒绝、申请审批通过、反馈待审批、反馈驳回
            this.associatedExecutionFlag = this.$utils.isEmpty(this.activityItem.exeCaseId) && (this.activityItem.status === 'Processing' || this.activityItem.status === 'Published' || this.activityItem.status === 'Closed')
                && (this.activityItem.aproStatus === 'New' || this.activityItem.aproStatus === 'Refused' || this.activityItem.aproStatus === 'Approve' || this.activityItem.aproStatus === 'Feedback' || this.activityItem.aproStatus === 'Refeedback');
            // 活动状态 MC_STATUS ：进行中、已发布、已结束  可以执行反馈
            // 审批状态 APRO_STATUS ：申请审批通过、反馈驳回
            this.executionFeedbackBtnFlag = (this.activityItem.status === 'Processing' || this.activityItem.status === 'Published' || this.activityItem.status === 'Closed') && (this.activityItem.aproStatus === 'Approve' || this.activityItem.aproStatus === 'Refeedback');
            // 活动状态 MC_STATUS ：已发布、进行中、已实发、材料补充、已结束
            // 审批状态 APRO_STATUS ：申请审批通过、反馈待审批、反馈驳回、反馈审批通过 活动稽核可以操作
            //"活动稽核按钮，仅职位类型为“稽核人员“&”财务人员“时，可点击。职位类型值列表类型：POSTN_TYPE 财务人员：FinanceStaff；稽核人员：AuditStaff"
            this.activityAuditFlag = (this.activityItem.status === 'Published' || this.activityItem.status === 'Processing' || this.activityItem.status === 'ActualAmount' || this.activityItem.status === 'MaterialSup' || this.activityItem.status === 'Closed')
                && (this.activityItem.aproStatus === 'Approve' || this.activityItem.aproStatus === 'Feedback' || this.activityItem.aproStatus === 'Refeedback' || this.activityItem.aproStatus === 'FeedbackApro')
                && (this.userInfo.positionType === 'FinanceStaff' || this.userInfo.positionType === 'AuditStaff');
            //活动状态为‘新建‘，审批状态为‘未提交’’已拒绝‘,活动提交按钮可点
            this.submitFlag = this.activityItem.status === 'New' && (this.activityItem.aproStatus === 'New' || this.activityItem.aproStatus === 'Refused');
            //活动状态为‘新建‘，审批状态为‘未提交’’已拒绝‘,活动创建名单按钮可点
            this.activityListReportFlag = this.activityItem.status === 'New' && (this.activityItem.aproStatus === 'New' || this.activityItem.aproStatus === 'Refused');
            //活动状态为“新建”、“已作废”和“已实发”，审批状态为‘未提交’’已拒绝‘,不可显示；活动的openScanConfig 以及 giftScanConfig 值来判断：只要存在至少一个Required，则“开通手动编辑”按钮显示；否则隐藏。
            this.enableManualEditing = (this.activityItem.status === 'New' || this.activityItem.status === 'Inactive' || this.activityItem.status === 'ActualAmount');
            this.isRequired = (this.activityItem.openScanConfig === 'Required' || this.activityItem.giftScanConfig === 'Required');
        },
        /**
         * 活动详情数据查询
         * 使用场景：更新了互动配置模板之后重新查询市场活动的模板ID和模板名称字段
         * <AUTHOR>
         * @date 2021-06-08
         * */
        async queryActivityTemplateById() {
            const data = await this.$http.post('action/link/marketAct/queryById', {
                id: this.activityItemId,
                stayFields: 'id,templateId,templateName,actJson'
            });
            this.activityItem = data.result;
        },
        /**
         * 活动详情数据查询
         * 使用场景：列表跳转详情和审批查看审批数据对象
         * <AUTHOR>
         * @date 2020-09-09
         * */
        async queryActivityItemById() {
            this.$store.commit('macTemplate/setMacTemplateId', '');
            const data = await this.$http.post('action/link/marketAct/queryById', {
                id: this.activityItemId
            });
            this.activityItem = data.result;
            if (this.activityItem.protocolId) {
                await this.getDisplayCollect(this.activityItem.protocolId);
            }
            //活动状态 MC_STATUS ：、已发布、进行中、已结束
            //审批状态 APRO_STATUS ：未提交、、已拒绝、申请审批通过、反馈待审批、反馈驳回
            this.associatedExecutionFlag = this.$utils.isEmpty(this.activityItem.exeCaseId) && (this.activityItem.status === 'Processing' || this.activityItem.status === 'Published' || this.activityItem.status === 'Closed')
                && (this.activityItem.aproStatus === 'New' || this.activityItem.aproStatus === 'Refused' || this.activityItem.aproStatus === 'Approve' || this.activityItem.aproStatus === 'Feedback' || this.activityItem.aproStatus === 'Refeedback');
            // 活动状态 MC_STATUS ：进行中、已发布、已结束  可以执行反馈
            // 审批状态 APRO_STATUS ：申请审批通过、反馈驳回
            this.executionFeedbackBtnFlag = (this.activityItem.status === 'Processing' || this.activityItem.status === 'Published' || this.activityItem.status === 'Closed') && (this.activityItem.aproStatus === 'Approve' || this.activityItem.aproStatus === 'Refeedback');
            // 活动状态 MC_STATUS ：已发布、进行中、已实发、材料补充、已结束
            // 审批状态 APRO_STATUS ：申请审批通过、反馈待审批、反馈驳回、反馈审批通过 活动稽核可以操作
            //"活动稽核按钮，仅职位类型为“稽核人员“&”财务人员“时，可点击。职位类型值列表类型：POSTN_TYPE 财务人员：FinanceStaff；稽核人员：AuditStaff"
            this.activityAuditFlag = (this.activityItem.status === 'Published' || this.activityItem.status === 'Processing' || this.activityItem.status === 'ActualAmount' || this.activityItem.status === 'MaterialSup' || this.activityItem.status === 'Closed')
                && (this.activityItem.aproStatus === 'Approve' || this.activityItem.aproStatus === 'Feedback' || this.activityItem.aproStatus === 'Refeedback' || this.activityItem.aproStatus === 'FeedbackApro')
                && (this.userInfo.positionType === 'FinanceStaff' || this.userInfo.positionType === 'AuditStaff');
            //活动状态为‘新建‘，审批状态为‘未提交’’已拒绝‘,活动提交按钮可点
            this.submitFlag = this.activityItem.status === 'New' && (this.activityItem.aproStatus === 'New' || this.activityItem.aproStatus === 'Refused');
            //活动状态为‘新建‘，审批状态为‘未提交’’已拒绝‘,活动创建名单按钮可点
            this.activityListReportFlag = this.activityItem.status === 'New' && (this.activityItem.aproStatus === 'New' || this.activityItem.aproStatus === 'Refused');
            //活动状态为“新建”、“已作废”和“已实发”，审批状态为‘未提交’’已拒绝‘,不可显示；活动的openScanConfig 以及 giftScanConfig 值来判断：只要存在至少一个Required，则“开通手动编辑”按钮显示；否则隐藏。
            this.enableManualEditing = (this.activityItem.status === 'New' || this.activityItem.status === 'Inactive' || this.activityItem.status === 'ActualAmount');
            this.isRequired = (this.activityItem.openScanConfig === 'Required' || this.activityItem.giftScanConfig === 'Required');
            await this.queryActicityTypeModules();
        },
        /**
         * 查询当前费用类型需要展示的组件模块数据
         * */
        async queryActicityTypeModules() {
            if (this.$utils.isEmpty(this.activityItem.costTypeCode)) {
                this.$showError("费用类型信息不完整，请更新活动数据或重新查询");
                return;
            }
            let data;
            if (this.$utils.isNotEmpty(this.activityItem.actIndeSourCode)) {
                data = await this.$utils.getQwMpTemplate('viewActivity', 'ActivityItem', this.activityItem.costTypeCode, this.activityItem.actIndeSourCode);
            } else {
                data = await this.$utils.getQwMpTemplate('viewActivity', 'ActivityItem', this.activityItem.costTypeCode);
            }
            if (!data.success) {
                this.authFlag = true;
                this.$utils.hideLoading();
                return;
            }
            let resultOpt = JSON.parse(data.result);
            this.fieldRows = JSON.parse(resultOpt.conf);
            if (!this.$utils.isEmpty(this.fieldRows)) {
                this.authFlag = false;
            }
            this.tapsOptions = [];
            for (let i = 0; i < this.fieldRows.length; i++) {
                //活动详情-基础信息-展示
                if (this.fieldRows[i].ctrlCode === 'viewActivityBasic') {
                    this.subControlList = this.fieldRows[i].subControlList;
                }
                //活动详情-基础信息-编辑
                if (this.fieldRows[i].ctrlCode === 'editActivityBasic') {
                    this.editSubControlList = this.fieldRows[i].subControlList;
                }
                const comExit = this.fieldRows[i].props.filter((item1) => item1.key === 'componentName');
                //消费者组件是否有配置
                const consumersExit = this.fieldRows[i].props.filter((item1) => item1.val === 'consumers');
                const interactiveConfigExit = this.fieldRows[i].props.filter((item1) => item1.val === 'interactive-config');
                if (this.$utils.isNotEmpty(consumersExit)) {
                    this.consumersRequire = this.fieldRows[i].base.require;
                }
                if (this.$utils.isNotEmpty(interactiveConfigExit)) {
                    this.interactiveConfigRequire = this.fieldRows[i].base.require;
                }
                if (!this.$utils.isEmpty(comExit)) {
                    switch (comExit[0].val) {
                        case 'basic-info': // 基础信息
                            this.basicInfoFlag = true;
                            const basic = {name: "基础信息", seq: "1", val: 'basic-info-v'};
                            this.tapsOptions.push(basic);
                            break;
                        case 'cost-new': // 费用信息
                            this.costNewFlag = true;
                            const cost = {name: "费用申请", seq: "2", val: 'cost-info-v'};
                            this.tapsOptions.push(cost);
                            break;
                        case 'interactive-config': // 互动配置
                            this.interactiveConfigFlag = true;
                            const inter = {name: "互动配置", seq: "3", val: 'interactive-config-v'};
                            this.tapsOptions.push(inter);
                            break;
                        case 'consumers': // 消费者
                            this.consumersFlag = true;
                            const cons = {name: "消费者", seq: "4", val: 'consumers-v'};
                            this.tapsOptions.push(cons);
                            break;
                        case 'insiders': // 内部人员
                            this.insidersFlag = true;
                            const insider = {name: "活动执行人员", seq: "5", val: 'insiders-v'};
                            this.tapsOptions.push(insider);
                            break;
                        case 'terminal': // 参与终端/经销商
                            this.terminalFlag = true;
                            const terminal = {name: "终端|经销商", seq: "6", val: 'terminal-v'};
                            this.tapsOptions.push(terminal);
                            break;
                        case 'production-pin' : //活动动销
                            this.productionPinFlag = true;
                            const prod = {name: "活动动销", seq: "7", val: 'pre-pio-pin-v'};
                            this.tapsOptions.push(prod);
                            break;
                        case 'execution-feedback' : //执行反馈
                            //配置后 非新建、已发布、已作废状态显示
                            if (!(this.activityItem.status === 'New'
                                || this.activityItem.status === 'Published'
                                || this.activityItem.status === 'Inactive')) {
                                this.executionFeedbackFlag = true;
                                const exe = {name: "执行反馈", seq: "8", val: 'execution-feedback-v'};
                                this.tapsOptions.push(exe);
                            }
                            break;
                        case 'audit' : //稽核记录
                            //非新建状态 配置后显示
                            if (this.activityItem.status !== 'New') {
                                this.auditFlag = true;
                                const audit = {name: "稽核记录", seq: "9", val: 'audit-list-v'};
                                this.tapsOptions.push(audit);
                            }
                            break;
                        case 'progress-new' : // 报销进度
                            //配置后 已实发、材料补充、已结束状态显示
                            if (this.activityItem.status === 'ActualAmount'
                                || this.activityItem.status === 'MaterialSup') {
                                this.progressNewFlag = true;
                                const prog = {name: "报销进度", seq: "10", val: 'progress-v'};
                                this.tapsOptions.push(prog);
                            }
                            break;
                        default:
                            break;
                    }
                } else {
                    continue;
                }
            }
            const approval = [{name: "审批历史", seq: "11", val: 'approval-records-v'}, {
                name: "活动分析",
                seq: "12",
                val: 'activity-analysis-v'
            }];
            this.tapsOptions = this.tapsOptions.concat(approval);
            this.tapsActive = this.tapsOptions[0];
        },
        /**
         * 滑动切换swiper页签
         * <AUTHOR>
         * @date 2020-08-10
         * @param e 滑动事件
         */
        swiperChange(e) {
            this.tapsActive = this.tapsOptions[e.detail.current]
        },
        /**
         * taps切换事件
         * <AUTHOR>
         * @date 2020-08-10
         * @param val taps选中对象
         * @param key taps选中对象索引
         */
        switchTab(val, key) {
            this.currentIndex = parseInt(key);
            this.tapsActive = val;
            wx.pageScrollTo({
                selector: `#${val.val}`,
                duration: 500
            })
        },
        /**
         * 查询活动下费用信息
         * 1、申请类
         * */
        async queryActivityProdAndCostList() {
            const data = await this.$http.post('action/link/actMaterial/queryAndGroupData', {
                filtersRaw: [{"id": "actId", "property": "actId", "value": this.activityItem.id, "operator": "="}]
            });
            return !this.$utils.isEmpty(data.rows);
        },
        /**
         * 查询活动下消费者信息
         * */
        async queryActivityConsumer() {
            const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/interCust/queryByAppCustPage', {
                order: 'desc',
                sort: 'matchStatus',
                mcActId: this.activityItem.id,
                rows: 3,
                totalFlag: true,
                filtersRaw: [
                    {id: 'empFlag', property: 'empFlag', value: 'N', operator: '='},
                    {id: 'terminalFlag', property: 'terminalFlag', value: 'N', operator: '='}
                ]
            });
            return !this.$utils.isEmpty(data.rows);
        },
        /**
         * 查询活动下关联的名单头状态=新建、待提报、待审批、审批拒绝的名单头数据 LIST_STATUS
         * */
        async queryActivityListReport() {
            const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/interListNew/queryByExamplePage', {
                mcActId: this.activityItem.id,
                filtersRaw: [
                    {id: 'status', property: 'status', value: '[New,ForSubmitted,Submitted,Refused]', operator: 'IN'}
                ]
            });
            return !this.$utils.isEmpty(data.rows);
        },
        /**
         * 活动提交
         * <AUTHOR>
         * @date 2020-08-10
         * */
        async submit() {
            const restaurantLon = this.editSubControlList.filter((item) => item.values.field === 'restaurantLon');
            const restaurantLat = this.editSubControlList.filter((item) => item.values.field === 'restaurantLat');
            if (!this.$utils.isEmpty(restaurantLon) && !this.$utils.isEmpty(restaurantLat)) {
                if (restaurantLon[0].base.require && restaurantLat[0].base.require) {
                    if (this.$utils.isEmpty(this.activityItem.restaurantLon) || this.$utils.isEmpty(this.activityItem.restaurantLat)) {
                        this.$showError('场地经纬度信息缺失，请重新维护活动供应商地址信息后选择！');
                        return false;
                    }
                }
            }
            if (!this.$utils.isEmpty(restaurantLon)) {
                if (restaurantLon[0].base.require) {
                    if (this.$utils.isEmpty(this.activityItem.restaurantLon)) {
                        this.$showError('场地经度信息缺失，请重新维护活动供应商地址信息后选择！');
                        return false;
                    }
                }
            }
            if (!this.$utils.isEmpty(restaurantLat)) {
                if (restaurantLat[0].base.require) {
                    if (this.$utils.isEmpty(this.activityItem.restaurantLat)) {
                        this.$showError('场地纬度信息缺失，请重新维护活动供应商地址信息后选择！');
                        return false;
                    }
                }
            }
            if (this.$utils.isEmpty(this.activityItem.startTime)) {
                this.$message.warn("请选择活动开始时间");
                return
            }
            if (this.$utils.isEmpty(this.activityItem.endTime)) {
                this.$message.warn("请选择结束时间");
                return
            }
            if (this.$utils.isEmpty(this.activityItem.busScene)) {
                this.$message.warn("请选择活动业务场景");
                return
            }
            const costFlag = await this.queryActivityProdAndCostList();
            if (!costFlag) {
                this.$message.warn("活动提交需维护费用申请数据，请维护费用申请数据！");
                return false;
            }
            if (this.consumersRequire) {
                const consumerDataFlag = await this.queryActivityConsumer();
                if (!consumerDataFlag) {
                    this.$message.warn("活动提交需维护消费者数据，请维护消费者数据！");
                    return false;
                }
            }
            if (this.interactiveConfigRequire) {
                if (this.$utils.isEmpty(this.activityItem.templateId)) {
                    this.$message.warn("活动提交需维护互动配置，请维护互动配置！");
                    return false;
                }
            }
            //2022年6月15日 增加逻辑判断校验
            if (this.$utils.isNotEmpty(this.activityItem.actJson)) {
                let interactiveConfigCheck = false
                let actJSONNode = JSON.parse(this.activityItem.actJson)
                actJSONNode.nodes = actJSONNode.nodes || []
                let interactiveConfigFlag = actJSONNode.nodes.findIndex((item, index) => {
                    return (item.activity !== 'start-node' && item.activity !== 'end-node')
                })
                if (interactiveConfigFlag > -1) {
                    interactiveConfigCheck = actJSONNode.nodes.some((item, index) => {
                        if (this.checkEveType(item)) {
                            return this.$utils.isEmpty(item.backBaseId) && item.activity !== 'start-node' && item.activity !== 'end-node'
                        }
                    })
                }
                if (interactiveConfigCheck) {
                    this.$message.warn("当前互动配置有未生效组件，请检查！");
                    return false;
                }
            }
            //是否有关联的名单头状态listStatus =新建、待提报、待审批、审批拒绝. LIST_STATUS
            // 如果有，则弹窗提示“存在未完成的活动名单，请检查并完成审批或者作废以结束名单提报”
            const activityListReportFlag = await this.queryActivityListReport();
            if (activityListReportFlag) {
                this.$message.warn("存在未完成的活动名单，请检查并完成审批或者作废以结束名单提报");
                return false;
            }
            //查询当前活动是否需要同步到简道云，接口报错时弹出让用户选择
            const data = await this.$http.post('action/link/marketAct/obSynMarketActJdyFlag', {
                orgId: this.activityItem.orgId,
                actId: this.activityItem.id,
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    if (!response.success) {
                        this.$refs.synNcMakOrderFlagDialog.show();
                    }
                }
            });
            //后台返回N时 synNcMakOrderFlag前端固定传N不同步到NC，返回Y时弹窗给用不选择是否同步到NC系统
            if (data.flag === 'N') {
                this.activityItem.synNcMakOrderFlag = 'N';
                await this.submitActivity();
            } else {
                this.$refs.synNcMakOrderFlagDialog.show();
            }
        },
        /**
         * 校验互动配置类型
         * <AUTHOR>
         * @date 2020-08-10
         * */
        checkEveType(item) {
            return item.evetype === 'WheelCamp' || item.evetype === 'Sales' || item.evetype === 'RoundWheel'
                || item.evetype === 'SignIn' || item.evetype === 'Invitation'
                || item.evetype === 'SlotMachine' || item.evetype === 'SmashGoldenEgg'
                || item.evetype === 'BlindBox' || item.evetype === 'IceTrue'
                || item.evetype === 'ExpansionRule' || item.evetype === 'RedPacket'
                || item.evetype === 'Message' || item.evetype === 'Questionnaire' || item.evetype === 'LotteryTicket' || item.evetype === 'TreasureChest';

        },
        /**
         * 是否开通手动编辑
         * <AUTHOR>
         * @date 2023-08-23
         * */
        isOpenFlag() {
            this.openFlag = true;
        },
        /**
         * 确定开通手动编辑
         * <AUTHOR>
         * @date 2023-08-24
         * */
        confirmActivation() {
            this.manualEditing()
            this.openFlag = false;
        },
        async manualEditing() {
            const data = await this.$http.post('action/link/marketAct/enableManualEditing', this.activityItem);
            if (data.success) {
                this.$utils.showAlert('开通成功');
                this.queryActivityItemByIdForCostUpdate();  //重新获取基本数据
            } else {
                this.$showError('开通失败')
            }
        },
        /**
         * 取消开通手动编辑
         * <AUTHOR>
         * @date 2023-08-24
         * */
        cancelActivation() {
            this.openFlag = false;
        },
        /**
         * 一键报备
         * <AUTHOR>
         * @date 2020-08-10
         * */
        reportedKey() {
            this.$nav.push('/pages/lj-market-activity/market-activity/reported-share-page', {
                activityItem: this.activityItem
            })
        },
        /**
         * 活动邀请函
         * <AUTHOR>
         * @date 2020-09-04
         * */
        async activityInvitation() {
            const data = await this.$utils.getQwMpTemplate('ActInvitation');
            if (data.success) {
                let resultOpt = JSON.parse(data.result);
                let invitationInfo = JSON.parse(resultOpt.conf);
                this.$nav.push('/pages/lj-market-activity/market-activity/invitation-info-page', {
                    invitationInfo: invitationInfo,
                    activityItem: this.activityItem
                });
            }
        },
        /**
         * 执行反馈
         * <AUTHOR>
         * @date 2020-08-11
         * */
        executionFeedback() {
            this.$nav.push('/pages/lj-market-activity/work-order/perform-link-page', {
                data: this.activityItem,
                link: 'activity',
                callback: (item) => {
                    this.activityItem = {...item};
                    //活动状态 MC_STATUS ：、已发布、进行中、已结束
                    //审批状态 APRO_STATUS ：未提交、、已拒绝、申请审批通过、反馈待审批、反馈驳回
                    this.associatedExecutionFlag = this.$utils.isEmpty(this.activityItem.exeCaseId) && (this.activityItem.status === 'Processing' || this.activityItem.status === 'Published' || this.activityItem.status === 'Closed')
                        && (this.activityItem.aproStatus === 'New' || this.activityItem.aproStatus === 'Refused' || this.activityItem.aproStatus === 'Approve' || this.activityItem.aproStatus === 'Feedback' || this.activityItem.aproStatus === 'Refeedback');
                    // 活动状态 MC_STATUS ：进行中、已发布、已结束  可以执行反馈
                    // 审批状态 APRO_STATUS ：申请审批通过、反馈驳回
                    if ((this.activityItem.status === 'Processing' || this.activityItem.status === 'Published' || this.activityItem.status === 'Closed') && (this.activityItem.aproStatus === 'Approve' || this.activityItem.aproStatus === 'Refeedback')) {
                        this.executionFeedbackBtnFlag = true;
                    } else {
                        this.executionFeedbackBtnFlag = false;
                    }
                    // 活动状态 MC_STATUS ：已发布、进行中、已实发、材料补充、已结束
                    // 审批状态 APRO_STATUS ：申请审批通过、反馈待审批、反馈驳回、反馈审批通过 活动稽核可以操作
                    //"活动稽核按钮，仅职位类型为“稽核人员“&”财务人员“时，可点击。职位类型值列表类型：POSTN_TYPE 财务人员：FinanceStaff；稽核人员：AuditStaff"
                    if ((this.activityItem.status === 'Published' || this.activityItem.status === 'Processing' || this.activityItem.status === 'ActualAmount' || this.activityItem.status === 'MaterialSup' || this.activityItem.status === 'Closed')
                        && (this.activityItem.aproStatus === 'Approve' || this.activityItem.aproStatus === 'Feedback' || this.activityItem.aproStatus === 'Refeedback' || this.activityItem.aproStatus === 'FeedbackApro')
                        && (this.userInfo.positionType === 'FinanceStaff' || this.userInfo.positionType === 'AuditStaff')
                    ) {
                        this.activityAuditFlag = true;
                    } else {
                        this.activityAuditFlag = false;
                    }
                    //活动状态为‘新建‘，审批状态为‘未提交’’已拒绝‘,活动提交按钮可点
                    if (this.activityItem.status === 'New' && (this.activityItem.aproStatus === 'New' || this.activityItem.aproStatus === 'Refused')) {
                        this.submitFlag = true;
                    } else {
                        this.submitFlag = false;
                    }
                    //活动状态为‘新建‘，审批状态为‘未提交’’已拒绝‘,活动创建名单按钮可点
                    if (this.activityItem.status === 'New' && (this.activityItem.aproStatus === 'New' || this.activityItem.aproStatus === 'Refused')) {
                        this.activityListReportFlag = true;
                    } else {
                        this.activityListReportFlag = false;
                    }
                    //活动状态为“新建”、“已作废”和“已实发”，审批状态为‘未提交’’已拒绝‘,不可显示；活动的openScanConfig 以及 giftScanConfig 值来判断：只要存在至少一个Required，则“开通手动编辑”按钮显示；否则隐藏。
                    this.enableManualEditing = (this.activityItem.status === 'New' || this.activityItem.status === 'Inactive' || this.activityItem.status === 'ActualAmount');
                    this.isRequired = (this.activityItem.openScanConfig === 'Required' || this.activityItem.giftScanConfig === 'Required');
                }
            })
        },
        /**
         * 活动稽核
         * <AUTHOR>
         * @date 2020-08-14
         * */
        activityApprovaAudit() {
            this.$nav.push('/pages/lj-market-activity/work-order/activity-approva-audit-page', {
                data: this.activityItem,
                link: 'check',
                callback: (item) => {
                    this.activityItem = {...item};
                }
            })
        },
        /**
         * 关联执行案
         * <AUTHOR>
         * @date 2020-09-04
         * */
        async associatedExecution() {
            const caseDate = await this.$object(this.caseOption, {
                pageTitle: "请选择执行案",
            });
            const data = await this.$http.post('action/link/marketAct/update', {
                id: this.activityItem.id,
                exeCaseId: caseDate.id,
                noPerformComments: "",
                rowVersion: this.activityItem.rowVersion,
                updateFields: 'id,exeCaseId,noPerformComments,rowVersion',
            });
            this.activityItem = {...data.newRow};
        },
        /**
         * 选择物料是否同步到NC
         * */
        async submitActivity() {
            try {
                this.$utils.showLoading();
                const data = await this.$http.post('action/link/marketAct/commit', {
                    id: this.activityItem.id,
                    synNcMakOrderFlag: this.activityItem.synNcMakOrderFlag
                });
                if (data.success) {
                    this.$refs.synNcMakOrderFlagDialog.hide();
                    this.$message.success("活动提交成功。")
                }
                this.activityItem = data.rows;
                this.submitFlag = this.activityItem.status === 'New' && (this.activityItem.aproStatus === 'New' || this.activityItem.aproStatus === 'Refused');
                //活动状态为‘新建‘，审批状态为‘未提交’’已拒绝‘,活动创建名单按钮可点
                this.activityListReportFlag = this.activityItem.status === 'New' && (this.activityItem.aproStatus === 'New' || this.activityItem.aproStatus === 'Refused');
                this.$bus.$emit('marketActivityListRefresh');

                //市场活动来的提交后返回市场后的列表，执行案详情新建活动提交之后返回执行案列表
                let pages = Taro.getCurrentPages();    //获取当前页面信息栈
                let targetIndex1 = pages.findIndex(function (item) {
                    return item.route === "pages/lj-market-activity/perform-case/perform-case-list-page";
                });
                let targetIndex2 = pages.findIndex(function (item) {
                    return item.route === "pages/lj-market-activity/market-activity/market-activity-list-page";
                });
                if (targetIndex1 === -1 && targetIndex2 === -1) {
                    return this.$nav.backAll()
                }
                const num1 = Number(pages.length - (Number(targetIndex1) + 1));
                const num2 = Number(pages.length - (Number(targetIndex2) + 1));
                if (targetIndex2 === -1) {
                    setTimeout(() => {
                        this.$utils.hideLoading();
                        this.$taro.reLaunch({url: '/pages/echart/lzlj/home/<USER>'});
                        // this.$nav.back(null, num1);
                    }, 1000)
                }
                if (targetIndex1 === -1) {
                    setTimeout(() => {
                        this.$utils.hideLoading();
                        this.$nav.back(null, num2);
                    }, 1000)
                }
            } catch (e) {
                this.$utils.hideLoading();
            }
        },
        /**
         * @createdBy  宋燕荣
         * @date  2021/6/29
         * @methods createdList
         * @para
         * @description 创建名单
         */
        async createdActivityListReport() {
            const param = {
                pageFlag: false,
                filtersRaw: [
                    {id: 'mcActId', property: 'mcActId', value: this.activityItem.id},
                ]
            };
            const data = await this.$http.post('action/link/maInterUser/queryByExamplePage', param);
            data.rows.forEach(item => {
                this.$set(item, 'row_status', ROW_STATUS.UPDATE);
            });
            this.actListOwnerData = data.rows;
            if (this.$utils.isEmpty(this.actListOwnerData)) {
                this.$message.warn('请先选择对接人');
                return false;
            }
            this.$refs.activityListReportDialog.show();
        },
        /**
         * @createdBy  宋燕荣
         * @date  2021/6/29
         * @methods createdList
         * @para
         * @description 名额提报调整-保存按钮
         */
        async saveSubmitPlaces() {
            //2022年4月14日 接口调整到智零
            let activityReport = [];             // 活动提报总人数
            // 计算字段，活动提报名额统计
            this.actListOwnerData.forEach((item) => {
                activityReport.push({
                    id: item.id,
                    mcActId: this.activityItem.id,
                    row_status: ROW_STATUS.UPDATE,
                    submitPlaces: item.submitPlaces ? item.submitPlaces : 0,
                    userPostnId: item.userPostnId
                })
            });
            const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/maInterUser/newSaveSubmitPlaces', activityReport);
            if (data.success) {
                this.$message.success('活动名单保存成功');
                const actdata = await this.$http.post('action/link/marketAct/update', {
                    id: this.activityItem.id,
                    registNum: data.allSubmitPlaces,
                    rowVersion: this.activityItem.rowVersion,
                    updateFields: "id,registNum,rowVersion",
                });
                this.activityItem = {...actdata.newRow};
                this.$dataService.setMarketActivityItem(actdata.newRow);
                this.$set(this.activityItem, 'registNum', data.allSubmitPlaces);
            } else {
                this.$showError('活动名单保存失败');
            }
            this.$refs.activityListReportDialog.hide();
        },
        /**
         * @createdBy  宋燕荣
         * @date  2021/6/29
         * @methods createdList
         * @para
         * @description 名额提报调整-保存并下发
         */
        async saveIssueSubmitPlaces() {
            //2022年4月14日 接口调整到智零
            let activityReport = [];             // 活动提报总人数
            this.actListOwnerData.forEach((item) => {
                activityReport.push({
                    id: item.id,
                    mcActId: this.activityItem.id,
                    row_status: ROW_STATUS.UPDATE,
                    submitPlaces: item.submitPlaces ? item.submitPlaces : 0,
                    userPostnId: item.userPostnId
                })
            });
            const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/maInterUser/newSaveSubmitPlaces', activityReport);
            if (data.success) {
                const submit = await this.$http.post(this.$env.appURL + '/marketactivity/link/marketActivity/newIssuedByList', {id: this.activityItem.id})
                if (submit.success) {
                    this.$message.success('已成功下发活动名单');
                    const actdata = await this.$http.post('action/link/marketAct/update', {
                        id: this.activityItem.id,
                        registNum: data.allSubmitPlaces,
                        rowVersion: this.activityItem.rowVersion,
                        updateFields: "id,registNum,rowVersion",
                    });
                    this.activityItem = {...actdata.newRow};
                    this.$dataService.setMarketActivityItem(actdata.newRow);
                    this.$set(this.activityItem, 'registNum', data.allSubmitPlaces);
                } else {
                    this.$showError('下发活动名单失败');
                }
            } else {
                this.$showError('保存名单失败');
            }
            this.$refs.activityListReportDialog.hide();
        },
    }
}
</script>

<style lang="scss">

.market-activity-item-page {
    width: 100%;
    overflow-x: hidden;

    .btm-v {
        width: 100%;
        height: 112px;
        margin-bottom: 32px;

        .btm-left {
            width: 15%;
            height: 96px;
            float: left;
            border: 2px solid #2F69F8;
            border-radius: 8px;
            line-height: 96px;
            text-align: center;
            color: #2F69F8;
            margin: 16px 0 0 24px;
        }

        .btm-center {
            width: 35%;
            height: 96px;
            float: left;
            margin: 16px 0 0 24px;
            border: 2px solid #2F69F8;
            border-radius: 8px;
            line-height: 96px;

            .center-title {
                font-family: PingFangSC-Regular;
                font-size: 32px;
                color: #2F69F8;
                letter-spacing: 0;
                text-align: center;
                line-height: 96px;
            }
        }

        .btm-right {
            width: 35%;
            height: 96px;
            float: left;
            line-height: 96px;
            margin: 16px 0 0 24px;
            background: #2F69F8;
            box-shadow: 0 8px 24px 0 rgba(47, 105, 248, 0.50);
            border-radius: 8px;

            .right-title {
                font-family: PingFangSC-Regular;
                font-size: 32px;
                color: #FFFFFF;
                letter-spacing: 0;
                text-align: center;
                line-height: 96px;
            }
        }
    }
}
</style>
