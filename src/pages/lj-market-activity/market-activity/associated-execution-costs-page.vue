<template>
    <link-page class="associated-execution-costs-page">
        <view>
            <!--活动上的执行案为空时可以更换执行案。不为空时，这里不允许更换，需要更换的话 去基础信息界面更新执行案编码-->
            <item title="执行案选择" @tap="caseChoose" :arrow="$utils.isEmpty(parentData.exeCaseId)">
                <view>{{$utils.isEmpty(exeCaseId) ? '请选择执行案' : exeCaseCode}}</view>
            </item>
        </view>
        <list v-if="!$utils.isEmpty(exeCaseId)">
            <list-title style="color: #2F69F8;font-size: 14px">执行案明细选择</list-title>
        </list>
        <view v-if="!$utils.isEmpty(costList)">
            <view class="item-data" v-for="(costItem,index) in costList" :key="index">
                <view class="left-view" @tap="selectItem(costItem)">
                    <view class="media-list">
                        <view class="media-top">
                            <view class="left-content">{{costItem.costPaymentWay}}</view>
                            <view class="right-content" style="color: #2F69F8;font-size: 14px">{{costItem.costStatus|
                                lov('FEE_STATUS')}}
                            </view>
                        </view>
                    </view>
                    <view class="content-middle">
                        <view class="content">
                            有效期{{costItem.startTime|date('YYYY-MM-DD HH:mm')}}至{{costItem.endTime|date('YYYY-MM-DD HH:mm')}}
                        </view>
                    </view>
                    <view class="content-middle-line-1">
                        <view>小类名称:{{costItem.costTypeName}}</view>
                    </view>
                    <view class="content-middle-line-1">
                        <view>费用垫付对象:{{costItem.feeReim}}</view>
                    </view>
                    <view class="content-middle-line" v-if="priceShowFlag">
                        <view class="data">可用余额:{{costItem.availableBalance | cny}}</view>
                        <view class="sum">审批金额:{{costItem.approvalAmount | cny}}</view>
                    </view>
                    <view class="content-middle-line" v-if="!priceShowFlag&&costItem.payType==='Money'">
                        <view class="data">可用余额:{{costItem.availableBalance | cny}}</view>
                        <view class="sum">审批金额:{{costItem.approvalAmount | cny}}</view>
                    </view>
                </view>
                <view class="right-view">
                    <view v-if="costItem._checked">
                        <link-icon size="1.8em" style="color:#0076ff;" icon="icon-baocun"/>
                    </view>
                </view>
            </view>
        </view>
        <view v-if="$utils.isEmpty(costList)" class="no-cost-view">
            <view class="no-cost" :style="{'background-image': 'url(' + $imageAssets.noDataImage + ')'}"></view>
            <view class="no-cost-msg" v-if="$utils.isEmpty(parentData.exeCaseId)">请先选择执行案</view>
            <view class="no-cost-msg" v-if="!$utils.isEmpty(parentData.exeCaseId)">当前执行案下没有已生效的明细数据,请前往基础信息更换执行案</view>
        </view>
        <link-dialog ref="feePayMethodTypeDialog">
            <view slot="head">
                提示
            </view>
            <view>
                {{feePayMethodTypeDialogMsg}}
            </view>
            <link-button slot="foot" @tap="$refs.feePayMethodTypeDialog.hide()">确定</link-button>
        </link-dialog>
<!--        <link-dialog ref="feeReimCodeDialog">-->
<!--            <view slot="head">-->
<!--                提示-->
<!--            </view>-->
<!--            <view>-->
<!--                {{feeReimCodeDialogMsg}}-->
<!--            </view>-->
<!--            <link-button slot="foot" @tap="$refs.feeReimCodeDialog.hide()">确定</link-button>-->
<!--        </link-dialog>-->
        <link-dialog ref="costTypeDialog">
            <view slot="head">
                提示
            </view>
            <view>
                {{costTypeDialogMsg}}
            </view>
            <link-button slot="foot" @tap="$refs.costTypeDialog.hide()">确定</link-button>
        </link-dialog>
        <link-dialog ref="feePayCode">
            <view slot="head">
                提示
            </view>
            <view>
                当前兑付方式与填报的兑付方式不一致，是否继续关联？
            </view>
            <link-button slot="foot" @tap="$refs.feePayCode.hide()">取消</link-button>
            <link-button slot="foot" @tap="associated">确定</link-button>
        </link-dialog>
        <link-sticky>
            <link-button block @tap="calibrated">确认关联</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    import Taro from "@tarojs/taro";

    export default {
        name: "associated-execution-costs-page",
        data() {
            const feePayType = this.pageParam.feePayType;//费用兑付方式
            const feePayCode = this.pageParam.feePayCode;//费用兑付方式类型
            const dataList = this.pageParam.dataList;//费用兑付方式下的子对象信息
            const feePayMethodType = this.pageParam.feePayMethodType;//费用兑付方式类型 【产品 现金】
            const parentData = this.pageParam.parentData;/*活动对象信息*/
            const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
            //const feeReimCodeDialogMsg = "";//费用垫付对象不一致时的弹窗信息
            const costTypeDialogMsg = "";//费用小类不一致时的弹窗信息
            const prodAndCostList = this.pageParam.prodAndCostList;//费用信息
            const feePayMethodTypeDialogMsg = "";//兑付方式不一致时的弹窗信息
            return {
                // 费用价格是否展示标识
                priceShowFlag: false,
                prodAndCostList,
                //feeReimCodeDialogMsg,
                feePayMethodTypeDialogMsg,
                costTypeDialogMsg,
                parentData,
                costId: '',//执行案明细ID
                selectedObj: {},
                costList: [],//费用列表
                feePayType,
                feePayCode,
                dataList,
                feePayMethodType,
                exeCaseId: '',//执行案ID
                exeCaseCode: '',//执行案编码
                executivesId: '',//费用垫付对象ID
                province: "",//省
                provinceId: "",//省Id
                city: "",//市
                cityId: "",//市Id
                district: "",//区
                districtId: "",//区Id
                executionState: '',//执行案状态
                //关联执行案使用
                caseOption: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: 'action/link/actProg/queryByExamplePage'
                    },
                    param: {
                        // EXECUTION_STATE 执行案状态 全部生效和部分生效
                        //attr5    执行案有效结束时间是否+5天
                        attr5: 'Y',
                        filtersRaw: [
                            {
                                id: 'executionState',
                                property: 'executionState',
                                value: '[AllEfficient,PartEfficient]',
                                operator: 'in'
                            },
                        ]
                    },
                    sortOptions: null,
                    searchFields: ['executionCode', 'executionName', 'creatorName'],
                    renderFunc: (h, {data, index}) => {
                        return (
                            < item
                        arrow = "false"
                        key = {index}
                        data = {data} >
                            < view
                        style = "width: 100%;background: white;height: 105px;"
                        slot = 'note' >
                            < view
                        style = "width: 90%;float: left;" >
                            < view
                        style = "display: flex;margin: auto;" >
                                            <view
                                                style="-ms-flex-align: center;-webkit-align-items: center;align-items: center;-ms-flex-pack: justify;-webkit-justify-content: space-between;  justify-content: space-between;height: 80rpx;line-height: 80rpx;padding-left: 32rpx;display: flex;width:100%;">
                                                <view
                                                    style="background: #A6B4C7;border-radius: 8rpx;line-height: 40rpx;">
                                                    <view
                                                        style="font-size: 28rpx;color: #FFFFFF;letter-spacing: 0;line-height: 40rpx;padding: 2rpx 8rpx;">{data.executionCode}
                                                    </view>
                                                </view>
                                            </view>
                                        </view>
                                        <view
                                            style=" -ms-flex-align: center;-webkit-align-items: center;align-items: center;-ms-flex-pack: justify;-webkit-justify-content: space-between;justify-content: space-between;height: 80rpx;line-height: 80rpx;padding-left: 32rpx;display: flex;width: 100%;">
                                            <view
                                                style="font-family: PingFangSC-Semibold;font-size: 32rpx;color: #262626;letter-spacing: 0;line-height: 32rpx;">{data.executionName}
                                            </view>
                                        </view>
                                        <view style="padding-left: 32rpx;">
                                            <view
                                                style="font-family: PingFangSC-Regular;font-size: 28rpx;color: #8C8C8C;letter-spacing: 0;line-height: 28rpx;float: left;">
                                                {this.priceShowFlag?`可用余额`+data.availableBalance+`,`:''}
                                            </view>
                                            <view
                                                style="font-family: PingFangSC-Regular;font-size: 28rpx;color: #8C8C8C;letter-spacing: 0;text-align: left;line-height: 28rpx;">
                                                {this.priceShowFlag?`审批金额`+data.approvalAmount:''}
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </item>
                        )
                    },
                }),
            }
        },
        async created() {
            await this.queryMarketActivityItem();
            this.priceShowFlag = await this.$utils.getPriceDesensitize(this.userInfo);
        },
        methods: {
            /**
             * 选择可选执行案
             * <AUTHOR>
             * @date 2020-09-14
             * */
            async caseChoose() {
                if (this.$utils.isEmpty(this.parentData.exeCaseId)) {
                    const list = await this.$object(this.caseOption, {multiple: false});
                    if (!this.$utils.isEmpty(list)) {
                        this.exeCaseCode = list.executionCode;
                        this.exeCaseId = list.id;
                        this.executionState = list.executionState;
                        await this.queryCustList();
                    }
                }
            },
            /*
            * 查询活动信息
            * @auther songyanrong
            * @date 2020-10-27
            * */
            async queryMarketActivityItem() {
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.parentData.id
                });
                this.parentData = {...data.result};
                if (!this.$utils.isEmpty(this.parentData.exeCaseId)) {
                    this.exeCaseId = this.parentData.exeCaseId;
                    this.exeCaseCode = this.parentData.exeCaseCode;
                    await this.queryCustList();
                }
            },
            /**
             * 查询目标执行案下的费用对象
             * */
            async queryCustList() {
                this.$utils.showLoading();
                //查询已生效的明细数据
                let filtersRaw = [
                    {id: 'actProgId', property: 'actProgId', value: this.exeCaseId, operator: '='},
                    {id: 'costStatus', property: 'costStatus', value: 'Efficient', operator: '='}
                ];
                const data = await this.$http.post('action/link/costDetail/queryByExamplePage', {
                    filtersRaw: filtersRaw,
                    oauth: 'MY_ORG',
                    //attr5 开启时间+5天校验
                    attr5: 'Y',
                });
                this.$utils.hideLoading();
                this.costList = data.rows;
            },
            selectItem(input) {
                //响应式字段_checked
                this.$set(input, '_checked', true);
                this.costList.forEach(item => {
                    if (item.id !== input.id) {
                        this.$set(item, '_checked', false);
                    }
                })
            },
            /**
             * 关联校验
             * <AUTHOR>
             * @date 2020-09-14
             * */
            async calibrated() {
                let selectData = this.costList.filter(item => item._checked === true)[0];
                if (this.$utils.isEmpty(selectData)) {
                    this.$message.warn("请先选择费用明细！");
                    return false;
                }
                this.costId = selectData.id;
                this.executivesId = selectData.feeReimId;
                this.province = selectData.province;
                this.provinceId = selectData.provinceId;
                this.city = selectData.city;
                this.cityId = selectData.cityId;
                this.district = selectData.district;
                this.districtId = selectData.districtId;
                //校验兑付方式类型是否一致 20201221 注释掉 20201222放开
                if (selectData.payType !== this.feePayMethodType) {
                    if(this.parentData.releaseFeeFlag !== 'Y'){
                        this.feePayMethodTypeDialogMsg = "当前兑付方式类型与填报的兑付方式类型不一致，请重新选择正确的兑付方式或调整基础信息下的执行案编码！";
                    } else {
                        this.feePayMethodTypeDialogMsg = "当前兑付方式类型与填报的兑付方式类型不一致，请重新选择正确的兑付方式！";
                    }
                    this.$refs.feePayMethodTypeDialog.show();
                } else {
                    //1-判断费用小类是否一致：限制选择的费用信息需和市场活动上的费用小类
                    if (selectData.costTypeCode !== this.parentData.costTypeCode) {
                        this.$showError(`当前兑付方式关联的执行案明细的费用小类与市场活动的费用小类【${this.parentData.costType}】不一致，请确认后重新选择。`);
                        return;
                    }
                    //2--
                    const feeReimDataMoney = this.prodAndCostList.Money;
                    const feeReimDataProduct = this.prodAndCostList.Product;
                    let feeReimData2;
                    let costTypeData;
                    if(!this.$utils.isEmpty(feeReimDataMoney) && !this.$utils.isEmpty(feeReimDataProduct)){
                        feeReimData2 = feeReimDataMoney.filter(item => !this.$utils.isEmpty(item.feeReimCode))[0] || feeReimDataProduct.filter(item => !this.$utils.isEmpty(item.feeReimCode))[0];
                        costTypeData = feeReimDataMoney.filter(item => !this.$utils.isEmpty(item.costTypeCode))[0] || feeReimDataProduct.filter(item => !this.$utils.isEmpty(item.costTypeCode))[0];
                    }else{
                        if(!this.$utils.isEmpty(feeReimDataMoney) && this.$utils.isEmpty(feeReimDataProduct)){
                            feeReimData2 = feeReimDataMoney.filter(item => !this.$utils.isEmpty(item.feeReimCode))[0];
                            costTypeData = feeReimDataMoney.filter(item => !this.$utils.isEmpty(item.costTypeCode))[0];
                        }else{
                            feeReimData2 = feeReimDataProduct.filter(item => !this.$utils.isEmpty(item.feeReimCode))[0];
                            costTypeData = feeReimDataProduct.filter(item => !this.$utils.isEmpty(item.costTypeCode))[0];
                        }
                    }
                    //判断选择的执行案明细数据费用垫付对象是否一致 2020-12-21注释掉费用垫付对象强关联
                    // if (!this.$utils.isEmpty(feeReimData2) && feeReimData2.feeReimCode !== selectData.feeReimCode) {
                    //     this.feeReimCodeDialogMsg = `当前执行案明细的费用垫付对象与已选择的【${feeReimData2.feeReim}】不一致，请重新选择执行案明细数据`;
                    //     this.$refs.feeReimCodeDialog.show();
                    // } else {
                        //2-判断选择的执行案明细数据费用小类是否一致
                        if (!this.$utils.isEmpty(costTypeData) && costTypeData.costTypeCode !== selectData.costTypeCode) {
                            this.costTypeDialogMsg = `当前执行案明细的费用小类与已选择的【${feeReimData2.costTypeName}】不一致，请重新选择执行案明细数据`;
                            this.$refs.costTypeDialog.show();
                        }else{
                            if (selectData.costPaymentWayCode !== this.feePayCode) {
                                this.$refs.feePayCode.show();
                            } else {
                                await this.associated();
                            }
                        }
                    // }
                }
            },
            /**
             * 确定关联执行案费用
             * input : 执行案明细ID
             * <AUTHOR>
             * @date 2020-09-14
             * */
            async associated() {
                // let dataList = [];
                // //更新实际费用目标费用类型下的所有产品信息
                // this.dataList.forEach(obj => {
                //     const item = {
                //         costId: this.costId,//执行案费用ID
                //         id: obj.id,
                //         updateFields: "id,costId"
                //     };
                //     dataList.push(item);
                // });
                // await this.$http.post('action/link/actualFee/batchUpdate', dataList);
                // //更新活动上的执行案ID和费用垫付对象ID
                // const updateActivityItem = {
                //     id: this.parentData.id,
                //     exeCaseId: this.exeCaseId,
                //     executivesId: this.executivesId,
                //     province: this.province,
                //     city: this.city,
                //     district: this.district,
                //     noPerformComments: "",//选了执行案后把无执行案说明清空
                //     rowVersion: this.parentData.rowVersion,
                //     updateFields: "id,exeCaseId,executivesId,province,city,district,noPerformComments,rowVersion"
                // };
                // const data = await this.$http.post('action/link/marketAct/update', updateActivityItem);
                // this.$bus.$emit("initActivityProdAndCostList");
                // this.pageParam.callback(data.newRow);
                // this.$refs.feePayCode.hide();
                // this.$nav.back();
                //song
                let dataList = [];
                //更新实际费用目标费用类型下的所有产品信息
                this.dataList.forEach(obj => {
                    const item = {
                        costId: this.costId,//执行案费用ID
                        id: obj.id,
                        updateFields: "id,costId"
                    };
                    dataList.push(item);
                });
                //更新活动上的执行案ID和费用垫付对象ID
                const updateActivityItem = {
                    id: this.parentData.id,
                    exeCaseId: this.exeCaseId,
                    executivesId: this.executivesId,
                    province: this.province,
                    provinceId: this.provinceId,
                    city: this.city,
                    cityId: this.cityId,
                    district: this.district,
                    districtId: this.districtId,
                    noPerformComments: "",//选了执行案后把无执行案说明清空
                    rowVersion: this.parentData.rowVersion,
                    companyId: this.parentData.companyId,
                    updateFields: "id,exeCaseId,executivesId,province,provinceId,city,cityId,district,districtId,noPerformComments,rowVersion,jiheSecField"
                };
                const dealData = {
                    marketAct:updateActivityItem,
                    actualFeeList:dataList
                }
                const data = await this.$http.post('action/link/actIntegration/updateActAndActualFee', dealData);
                this.$dataService.setMarketActivityItem(data.result);
                this.$bus.$emit("initActivityProdAndCostList");
                this.pageParam.callback(data.result);
                this.$refs.feePayCode.hide();
                this.$nav.back();
            }
        }
    }
</script>

<style lang="scss">
    @import "../../../styles/list-card";
    .associated-execution-costs-page {
        .no-cost-view {

            .no-cost {
                width: 368px;
                height: 368px;
                background-repeat: no-repeat;
                background-size: 100% 100%;
                -moz-background-size: 100% 100%;
                margin: auto;
            }

            .no-cost-msg {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                text-align: center;
                line-height: 38px;
                padding: 50px;
            }
        }

        .item-data {
            width: 100%;
            border-bottom: 2px solid #F2F2F2;
            background: white;
            height: 300px;

            .left-view {
                width: 90%;
                float: left;

                .media-list {
                    @include media-list;

                    .media-top {
                        width: 100%;
                        @include flex-start-center;
                        @include space-between;
                        height: 80px;
                        line-height: 80px;
                        padding-left: 32px;

                        .left-content {
                            font-family: PingFangSC-Semibold;
                            font-size: 32px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 32px;
                            padding-top: 20px;

                        }

                        .right-content {
                            font-family: PingFangSC-Semibold;
                            font-size: 32px;
                            color: #FF5A5A;
                            letter-spacing: 0;
                            text-align: right;
                            line-height: 32px;
                            padding-top: 20px;
                        }

                        .num-view {
                            background: #A6B4C7;
                            border-radius: 8px;
                            line-height: 40px;

                            .num {
                                font-size: 28px;
                                color: #FFFFFF;
                                letter-spacing: 0;
                                line-height: 40px;
                                padding: 2px 8px;
                            }
                        }

                        .status-view {
                            width: 120px;
                            transform: skewX(-10deg);
                            border-radius: 4px;
                            background: #2F69F8;
                            box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                            height: 36px;

                            .status {
                                font-size: 20px;
                                color: #FFFFFF;
                                letter-spacing: 2px;
                                text-align: center;
                                line-height: 36px;
                            }
                        }
                    }
                }

                .content-middle {
                    width: 100%;
                    @include flex-start-center;
                    @include space-between;
                    height: 50px;
                    line-height: 50px;
                    padding-left: 32px;

                    .content {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #000000;
                        letter-spacing: 0;
                        line-height: 28px;
                    }

                    .name {
                        font-family: PingFangSC-Semibold;
                        font-size: 32px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 32px;
                    }

                    .data {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        line-height: 28px;
                    }

                    .sum {
                        font-family: PingFangSC-Semibold;
                        font-size: 32px;
                        color: #FF5A5A;
                        letter-spacing: 0;
                        text-align: right;
                        line-height: 32px;
                    }
                }

                .content-middle-line-1 {
                    font-family: PingFangSC-Regular;
                    padding-left: 32px;
                    font-size: 28px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 45px;
                }

                .content-middle-line {
                    padding-left: 32px;

                    .data {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        line-height: 28px;
                        width: 50%;
                        float: left;
                    }

                    .sum {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        text-align: right;
                        line-height: 28px;
                    }
                }
            }

            .right-view {
                width: 10%;
                float: left;
                height: 210px;
                line-height: 210px;
                text-align: center;
            }
        }
    }
</style>
