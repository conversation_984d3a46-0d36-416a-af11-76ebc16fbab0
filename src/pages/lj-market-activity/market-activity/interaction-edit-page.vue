<template>
    <link-page class="interaction-edit-page">
        <link-form>
            <link-form-item label="互动开始时间" required>
                <link-date v-model="interactionItem.beginTime" view="YMDHm" displayFormat="YYYY-MM-DD HH:mm"
                           valueFormat="YYYY-MM-DD HH:mm"></link-date>
            </link-form-item>
            <link-form-item label="互动结束时间" required>
                <link-date v-model="interactionItem.endTime" view="YMDHm" displayFormat="YYYY-MM-DD HH:mm"
                           valueFormat="YYYY-MM-DD HH:mm"></link-date>
            </link-form-item>
            <link-form-item label="验证方式">
                <link-lov v-model="interactionItem.licenseMode" type="LICENSE_MODE"></link-lov>
            </link-form-item>
            <link-form-item label="地址定位">
                <link-switch v-model="interactionItem.loc"/>
            </link-form-item>
            <link-form-item label="红包" readonly>
                <link-input v-model="interactionItem.couponsName"></link-input>
            </link-form-item>
            <link-form-item label="红包个数" required>
                <link-input type="number" v-model="interactionItem.prizeNo"></link-input>
            </link-form-item>
        </link-form>
        <link-sticky>
            <link-button block @tap="saveInteraction">保存</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    export default {
        name: "interaction-edit-page",
        data() {
            const interactionItem = this.pageParam.item;
            const readonlyFlag = this.pageParam.readonlyFlag;
            return {
                interactionItem,
                readonlyFlag,
            }
        },
        methods: {
            /**
             *  保存数据校验
             *
             *  <AUTHOR>
             *  @date        2020-09-17 11:40
             */
            checkData() {
                if (this.$utils.isEmpty(this.interactionItem.beginTime)) {
                    this.$message.info(`请输入活动开始时间`);
                    return false;
                }
                if (this.$utils.isEmpty(this.interactionItem.endTime)) {
                    this.$message.info(`请输入活动结束时间`);
                    return false;
                }
                if (!/^([1-9]\d*)$/.test(this.interactionItem.prizeNo)) {
                    this.$message.info(`活动红包个数必须为大于零的整数`);
                    return false;
                }
                return true;
            },
            async saveInteraction() {
                if (!this.checkData()) {
                    return;
                }
                await this.$http.post(this.$env.appURL + '/interaction/link/interaction/upsert', this.interactionItem);
                this.pageParam.callback();
                this.$nav.back();
            }
        }
    }
</script>

<style lang="scss">
    .interaction-edit-page {

    }
</style>