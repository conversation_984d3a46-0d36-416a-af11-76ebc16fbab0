<template>
    <link-page class="activity-list-owner-page">
        <view style="background: #fff;">
            <view>
                <link-search-input v-model="searchVal" :placeholder="'主要负责人名称'"/>
            </view>
            <link-auto-list :option="autoList">
                <template slot-scope="{data,index}">
                    <item :key="index" :data="data" :arrow="false" class="select-box" @tap="selectPostn(data)">
                        <view class="select-right">
                            <view class="iconfont icon-yiwanchengbuzhou" v-if="data.checkedFrontFlag === 'Y'"></view>
                            <view class="iconfont icon-circle-outline" v-else></view>
                        </view>
                        <view class="select-left">
                            <view class="postn-name">职位编码：{{data.postnCode}}</view>
                            <view class="postn-name">职位名称：{{data.postnName}}</view>
                            <view class="postn-name">部门：{{data.deptName}}</view>
                            <view class="postn-name">组织：{{data.orgName}}</view>
                            <view class="fst-name">
                                <view class="label">主要负责人：</view>
                                <view class="text">{{data.firstName}}</view>
                            </view>
                        </view>
                    </item>
                </template>
            </link-auto-list>
        </view>
        <link-sticky class="bottom-btn">
            <view class="all-select" @tap="allSelect">
                <view class="iconfont icon-yiwanchengbuzhou" v-if="allSelectFlag"></view>
                <view class="iconfont icon-circle-outline" v-else></view>
                <view class="all-select-text">全选</view>
            </view>
            <link-button class="sure-btn" size="normal" @tap="sureAllocation" :shadow="shadow">确定</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    import Taro from "@tarojs/taro";

    export default {
        name: "activity-list-owner-page",
        data() {
            const activityItem = this.pageParam.activityItem;//活动数据
            let allSelectFlag = false;    //全选开关
            let oldPostnData = [];        //初始对接人数组-后台每个对象会返回checkedFlag，后端在返回的时候是已经区分了哪些已分配，哪些未分配，Y:已分配的
            let oldPrimaryPostnData = [];        //初始主要对接人数组
            let dialogFlag = false;
            let addArr = [];             //新加的数组
            let deleteArr = [];          //删除的数组
            let userInfo = {};
            let shadow = true;
            let nowCheckedData = [];
            let primaryUserId = null;//主要对接人id
            let primaryUserName = null;//主要对接人名称
            let searchVal = '';
            let fuzzyQueryFlag = false;//模糊查询
            let fuzzyQuerySelectData = [];//模糊查询有条件时选择的数据
            const autoList = new this.AutoList(this, {
                url: {
                    queryByExamplePage: 'action/link/mvg/queryPostnForMaUserByPage'
                },
                param: {
                    mvgMapperName: 'maUser',
                    mvgParentId: activityItem.id,
                    oauth: 'ALL',
                    filtersRaw: [{
                        id: 'status',
                        property: 'status',
                        value: 'Y'
                    }]
                },
                sortOptions: null,
                hooks: {
                    afterLoad(data) {
                        fuzzyQuerySelectData = fuzzyQuerySelectData.filter(item => item.checkedFrontFlag === 'Y');//只保留选中的数据
                        if (fuzzyQuerySelectData.length > 0) {
                            fuzzyQuerySelectData.forEach(item1 => {
                                data.rows.map((item2) => {
                                    if (item1.id === item2.id) {
                                        item2.checkedFrontFlag = 'Y';
                                        return {...item2}
                                    } else {
                                        return {...item2}
                                    }
                                });
                            });
                        } else {
                            data.rows.map((item) => {
                                item.checkedFrontFlag = item.checkedFlag;
                                return {...item};
                            });
                        }
                        let selectNum = data.rows.filter(item => item.checkedFlag === 'N');
                        allSelectFlag = selectNum.length === 0;
                        oldPostnData = this.$utils.deepcopy(data.rows);
                        oldPostnData.forEach((x) => {
                            if (x.isDefault === 'Y') {
                                primaryUserId = x.id;
                                primaryUserName = x.firstName;
                            }
                        });
                    }
                }
            });
            return {
                autoList,
                activityItem,
                allSelectFlag,    //全选开关
                oldPostnData,        //初始对接人数组-后台每个对象会返回checkedFlag，后端在返回的时候是已经区分了哪些已分配，哪些未分配，Y:已分配的
                oldPrimaryPostnData,        //初始主要对接人数组
                dialogFlag,
                addArr,              //新加的数组
                deleteArr,           //删除的数组
                userInfo,
                shadow,
                nowCheckedData,
                primaryUserId,
                searchVal,
                fuzzyQueryFlag,//模糊查询
                fuzzyQuerySelectData,//模糊查询有条件时选择的数据
                primaryUserName,
            }
        },
        created() {
            this.userInfo = Taro.getStorageSync('token').result;
        },
        watch: {
            searchVal(newVal, oldVal) {
                if (newVal !== oldVal) {
                    const searchObj = {
                        id: "searchValue",
                        operator: "or like",
                        property: "[firstName]",
                        value: newVal
                    };
                    if (!this.$utils.isEmpty(newVal)) {
                        this.fuzzyQueryFlag = true;
                    } else {
                        this.fuzzyQueryFlag = false;
                    }
                    this.autoList.option.param['filtersRaw'] = this.autoList.option.param['filtersRaw'].filter((item) => item.property !== "[firstName]");
                    this.autoList.option.param['filtersRaw'].push(searchObj);
                    this.autoList.methods.reload()
                }
            },
        },
        methods: {
            /**
             * @createdBy  宋燕荣
             * @date  2021/06/29
             * @methods allSelect
             * @para
             * @description 全选产品
             */
            allSelect() {
                this.allSelectFlag = !this.allSelectFlag;
                const allSelectFlag = this.allSelectFlag ? 'Y' : 'N';
                this.autoList.list.forEach(item => {
                    if (this.allSelectFlag) {
                        item.checkedFrontFlag = allSelectFlag
                    } else {
                        item.checkedFrontFlag = allSelectFlag
                    }
                })
            },
            /**
             * @createdBy  宋燕荣
             * @date  2021/06/29
             * @methods selectPostn
             * @para
             * @description 选择对接人
             */
            selectPostn(val) {
                if (val.checkedFrontFlag === 'Y') {
                    val.checkedFrontFlag = 'N';
                } else {
                    val.checkedFrontFlag = 'Y';
                }
                if (this.fuzzyQueryFlag) {
                    //选择的数据是否存在
                    const dataExit = this.fuzzyQuerySelectData.filter((item1) => item1.id === val.id);
                    if (this.$utils.isNotEmpty(dataExit)) {
                        this.fuzzyQuerySelectData.forEach(item => {
                            if (item.id === val.id) {
                                item.checkedFrontFlag = val.checkedFrontFlag;
                            }
                        })
                    } else {
                        this.fuzzyQuerySelectData.push(val);
                    }
                }
                let selectNum = this.autoList.list.filter(item => item.checkedFrontFlag === 'N');
                this.allSelectFlag = selectNum.length === 0;
            },
            /**
             * @createdBy  宋燕荣
             * @date  2021/06/29
             * @methods sureAllocation
             * @para
             * @description 确认选中对接人
             */
            async sureAllocation() {
                let oldCheckedData = this.autoList.list.filter((x) => x.checkedFlag === 'Y');
                let nowCheckedData = this.autoList.list.filter((x) => x.checkedFrontFlag === 'Y');
                this.nowCheckedData = nowCheckedData;
                // 新加的数据
                this.addArr = [...nowCheckedData].filter((x) => [...oldCheckedData].every((y) => y.id !== x.id));
                this.deleteArr = [...oldCheckedData].filter((x) => [...nowCheckedData].every((y) => y.id !== x.id));
                // if (this.addArr.length === 0 && this.deleteArr.length === 0) {
                //     this.$utils.showAlert('未选择或者取消任何对接人，请重新选择！', {icon: 'none'});
                //     return
                // }
                const selectedData = await this.$http.post('action/link/mvg/queryPostnForMaUserByPage',{
                    mvgMapperName: 'maUser',
                        mvgParentId: this.activityItem.id,
                        oauth: 'ALL',
                        filtersRaw: [{
                        id: 'status',
                        property: 'status',
                        value: 'Y'
                    },{
                            id: 'checkedFlag',
                            property: 'checkedFlag',
                            value: 'Y'
                        }]
                });
                if (this.nowCheckedData.length === 0 && selectedData.rows.length === 0) {
                    this.$utils.showAlert('请勾选对接人', {icon: 'none'});
                    return
                }
                // 默认选中的第一个为主要对接人
                if (this.$utils.isEmpty(this.primaryUserId)) {
                    if(this.nowCheckedData.length !== 0){
                        this.primaryUserId = this.nowCheckedData[0].id;
                        this.primaryUserName = this.nowCheckedData[0].firstName;
                    } else {
                        this.primaryUserId = this.activityItem.actListOwnerId;
                        this.primaryUserName = this.activityItem.actListOwner;
                    }
                }
                await this.confirm();
            },
            /**
             * @createdBy  宋燕荣
             * @date  2021/06/29
             * @methods confirm
             * @para
             * @description 确认分配对接人
             */
            async confirm() {
                if (this.$utils.isEmpty(this.primaryUserId)) {
                    this.$utils.showAlert('未勾选主要对接人，请重新选择', {icon: 'none'});
                    return
                }
                let [needAdd, needDelete, primary] = [[], [], []];
                this.addArr.forEach((item) => {
                    needAdd.push({
                        defField: "isDefault",
                        mvgMapperName: "maUser",
                        mvgSubsetId: item.id,
                        mvgParentId: this.activityItem.id,
                        row_status: "NEW"
                    })
                });
                this.deleteArr.forEach((item) => {
                    needDelete.push({
                        defField: "isDefault",
                        mvgMapperName: "maUser",
                        mvgSubsetId: item.id,
                        mvgParentId: this.activityItem.id,
                        row_status: "DELETE"
                    })
                })
                primary = [{
                    defField: "isDefault",
                    mvgMapperName: "maUser",
                    mvgSubsetId: this.primaryUserId,
                    mvgParentId: this.activityItem.id,
                    row_status: "DEFAULT"
                }];
                let paramArr = needAdd.concat(needDelete, primary);
                const data = await this.$http.post('action/link/mvg/mvgModify', paramArr);
                if (!data.success) {
                    this.$utils.showAlert('分配对接人出错！', {icon: 'none'});
                    return false
                };
                this.pageParam.callback({id:this.primaryUserId,name:this.primaryUserName});
                this.$nav.back()
            },
            /**
             * @createdBy  宋燕荣
             * @date  2020/10/23
             * @methods setPrimary
             * @para
             * @description 设置主要对接人
             */
            setPrimary(val, index) {
                this.primaryUserId = val.id
            }
        }
    }
</script>

<style lang="scss">
    .activity-list-owner-page {
        .terminal-dialog {
            .link-dialog-body {
                display: flex;
                flex-direction: column !important;
                justify-content: normal !important;
                padding: 0;
            }

            .head-tip {
                font-family: PingFangSC-Regular, serif;
                font-size: 32px;
                color: #333333;
                letter-spacing: 0;
                text-align: center;
                line-height: 32px;
                padding: 30px 0px;
                border-bottom: 2px solid #e5e5e5;
                width: 100%;
            }

            .content-top {
                font-family: PingFangSC-Regular, serif;
                font-size: 28px;
                color: #595959;
                letter-spacing: 0;
                line-height: 28px;
                padding: 31px 24px;
                text-align: left;
                width: 100%;
            }

            .content-center {
                width: 100%;
                padding: 0 24px;

                .content-center-row {
                    margin-bottom: 40px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 28px;
                    height: 28px;

                    .left {
                        line-height: 28px;
                        height: 28px;
                        @include flex-start-center;

                        .name {
                            font-family: PingFangSC-Medium, serif;
                            color: #262626;
                            line-height: 1;
                            margin-right: 10px;
                        }

                        .is-primary {
                            font-family: PingFangSC-Regular, serif;
                            color: #2F69F8;
                            line-height: 1;
                        }
                    }

                    .primary-button {
                        line-height: 28px;
                        height: 28px;
                        font-family: PingFangSC-Regular, serif;
                        color: #2F69F8;
                    }
                }
            }

        }

        .select-box {
            @include flex-start-center;
            margin-left: 24px;
            margin-right: 24px;
            border-bottom: 1px solid #F2F2F2;

            .select-left {
                width: 100%;

                .postn-name {
                    width: 100%;
                    font-family: PingFangSC-Medium, serif;
                    font-size: 28px;
                    letter-spacing: 0;
                    line-height: 28px;
                    padding-bottom: 24px;
                }

                .fst-name {
                    width: 100%;
                    font-family: PingFangSC-Regular, serif;
                    font-size: 28px;
                    letter-spacing: 0;
                    line-height: 28px;
                    padding-bottom: 32px;
                    display: flex;

                    .label {
                        color: #8c8c8c;
                        margin-right: 10px;
                    }

                    .text {
                        color: #000000;
                    }
                }
            }

            .select-right {
                margin-right: 24px;

                .iconfont {
                    font-size: 40px;
                    color: #BFBFBF;
                }

                .icon-yiwanchengbuzhou {
                    font-size: 40px;
                    color: #2F69F8;
                }
            }
        }

        .bottom-btn {
            padding-top: 16px;
            padding-bottom: 34px;

            .all-select {
                height: 96px;
                padding-left: 24px;
                @include flex-start-center();
                width: 50%;
                font-size: 28px;
                color: #595959;
                letter-spacing: 0;
                line-height: 28px;

                .iconfont {
                    font-size: 40px;
                    color: #BFBFBF;
                }

                .icon-yiwanchengbuzhou {
                    color: $color-primary;
                }

                .all-select-text {
                    padding-left: 16px;
                }
            }

            .sure-btn {
                width: 340px;
                height: 96px;
                margin-right: 24px;
            }
        }
    }
</style>
