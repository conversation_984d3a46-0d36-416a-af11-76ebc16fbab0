# 市场活动 - 页面配置
```
创建时间：2022/02/09 14:15
创建人：  宋燕荣
```

### 1、配置方式
```
配置入口:小程序模板：统一在PC端
模版控件:统一在PC端菜单【小程序页面模板 / 模板控件】
注意：模版类型必须和模版类型保持一致，这样新建小程序模版时才可以选择到相匹配的目标模版控件。
说明：市场活动模块所有的小程序模版都是模版关联的费用类型为关键，有些模版费用小类的基础上再指定活动类型。活动类型是费用类型的子对象，数据关系可查看【PC端活动管理 / 费用|活动类型菜单展示的基础数据，数据区分所属公司来自于智慧零售系统同步到企微】
模版安全性：可以配置可见组织、可见标签、可见职责、可见用户4种规则。规则之间并集的关系。
```
* 模板类型：LNK_AUTO_TEMPLATE_TYPE

```
1、新建活动 newActivity
2、查看活动 viewActivity
3、活动邀请函 ActInvitation
4、活动业务场景 businessScenario
```
*  模版子类型：TMPL_SUB_BIZ_TYPE
```
1、活动报备 ActReport 父：查看活动
2、执行反馈 ExecutionFeedback 父：查看活动
3、活动详情 ActivityItem 父：查看活动
4、具体的活动业务场景。（场景值比较多此处就不一一列举，可根据以下条件在PC端值列表界面筛选查询，【类型：TMPL_SUB_BIZ_TYPE ，父值列表等于‘活动业务场景’】）
```
![简图](https://xtzh-public-1256376813.cos.ap-chengdu.myqcloud.com/static/images/technical-documentation/automatic-configuration.jpeg)

#### 配置场景
```
A、活动新建可维护的对象（是否必须维护）
B、活动基本信息新建时需要维护的字段（是否必输，是否只读）
C、活动基本信息展示时展示的字段
D、活动基本信息编辑时可编辑的字段（是否必输，是否只读）
E、活动详情需要展示的子对象
F、执行反馈详情需要展示的对象
G、活动报备时需要报备的字段
H、活动分享生成邀请函的显示图片和字段
I、执行反馈阶段根据业务场景配置每个业务场景需要维护的照片组
```
#### 模版配置
##### PC端菜单-模版控件：配置常规控件和组合控件
```
1、常规控件：配置最小元件，eg:字段、年度回顾的每一张图片位等。

2、组合控件：将几个常规控件添加到一个控件中形成一个组合控件。
组合控件说明：市场活动仅仅是【1、新建活动 newActivity 2、查看活动 viewActivity】这俩类把基本信息需要的字段根据不同费用类型和活动类型配置了不同规则适用的组合模版

2-1、以新建活动类型的模版配置规则为例
控件名称：new-活动-基础信息-****(费用类型和其他关键字)
控件类型：组合控件
是否有效：是
控件业务类型：新建活动
控件显示样式：单行文本
控件说明：言简意赅，什么类型的什么模版
控件编码：activity-module
具体模版内容：把需要的字段添加到模版中
```

##### PC菜单-小程序模版：配置最终用户查询的模版。
```
1、以【新建活动】类型的小程序模版配置规则为例
模版名称：new-活动新建-***(费用类型)或加其他关键字
模版类型：新建活动
模版关联的费用类型：选择费用大中小类可多选
模版内容：添加需要的基本信息组合控件+当前费用类型需要维护的子对象

2、【查看活动】同上1

3、【活动名单提报-嘉宾名单】
模版名称：活动名单提报-***(费用类型)或加其他关键字
模版类型：活动名单提报-嘉宾名单
模版子类型：具体某一个活动类型
模版内容：添加需要字段

4、【活动业务场景】
模版名称：场景名称+（某些关键字比如2022财年，可选）
模版类型：活动业务场景
模版子类型：具体某一个业务场景类型值
模版内容：选择当前业务场景需要上传的图片模版

5、【活动邀请函】
模版名称：活动邀请函+（某些关键字可选）
模版类型：活动邀请函
模版内容：从模版控件中选择需要展示的内容字段并维护说明信息。比如活动介绍说明-称呼字段及内容

6、【年度回顾】
模版名称：**板块轮番图-通用模板
模版类型：年度回顾
模版子类型：年度回顾-消费者 或 年度回顾-终端板块 或 年度回顾-基础信息 或 年度回顾-市场活动
模版内容：从模版控件中选择需要展示的内容字段并维护说明信息。比如活动介绍说明-称呼字段及内容
```
### 2、查询方式以及参数
```
配置入口：统一在PC端菜单【小程序页面模板 / 小程序模板维护】
查询规则：第一顺位：从缓存中获取，第二顺位：从后端查询获取。
小程序查询调用方法：getQwMpTemplate
```
```
/**
     * 泸州老窖企微项目--获取企微小程序模板内容
     * 第一顺位：从缓存中获取
     * 第二顺位：从后端查询获取
     * <AUTHOR>
     * @date 6/22/21
     * @param type 模板类型
     * @param subType 模板子类型
     * @param feeType 费用子类型
     * @param actType 活动类型
     * @param cacheFrom 是否需要从缓存中获取，避免有些模板变更比较频繁，每次都需要实时获取
     * @param baseUrl 模板基础查询路径
     * @return {success: true/false, result: 模板内容，message: 错误信息}
     */
    async getQwMpTemplate(type: string,
                          subType: string = '',
                          feeType: string = '',
                          actType: string = '',
                          cacheFrom: boolean = true,
                          baseUrl: string = '/action/link/mpTmpl/get/') {
        // 返回结果
        const result  = {success: false, result: null, message: '模板获取失败'};
        if (!type || type.indexOf('/') !== -1) {
            result.message = '模板类型不正确';
            return result;
        }
        const QW_MP_TMPL_KEY = 'link_qw_mp_tmpl'; // 企微小程序模板缓存对象key值
        // 获取模板缓存
        let tmplObject: Object = Taro.getStorageSync(QW_MP_TMPL_KEY) || {};
        if(type === 'newActivity' || type === 'viewActivity'){
            $aegis.report("获取小程序模版", tmplObject, '缓存模版信息获取', this.userInfo);
        }
        // 1.根据传入的模板参数，生成模板健值
        const moduleKey = type + subType + feeType + actType;
        // 2.根据key值尝试从缓存中获取模板
        const moduleContent = tmplObject[moduleKey] || null;
        if(type === 'newActivity' || type === 'viewActivity'){
            $aegis.report("获取小程序模版", moduleContent, '具体的模版内key');
        }
        // 3.分离模板中的配置内容与时间戳
        let timestamp = '';
        let jsonResult = null;
        if (Boolean(moduleContent) && cacheFrom) {
            const index = moduleContent.indexOf('#');
            if(type === 'newActivity' || type === 'viewActivity'){
                $aegis.report("获取小程序模版", index, '存在时间戳-时间戳的位置');
            }
            // 存在时间戳
            if (index > 0 && index < 30) {
                timestamp = moduleContent.substring(0, index);
                jsonResult = moduleContent.substring(index + 1);
                if(type === 'newActivity' || type === 'viewActivity'){
                    $aegis.report("获取小程序模版",'不属于index > 0 && index < 30','存在时间戳');
                }
            } else {
                // 不存在时间戳，则直接返回缓存内容
                result.success = true;
                result.message = '模板获取成功';
                result.result = moduleContent;
                if(type === 'newActivity' || type === 'viewActivity'){
                    $aegis.report("获取小程序模版", moduleContent, '不存在时间戳，则直接返回缓存内容');
                }
                return result;
            }
        }
        // 4.缓存中不存在或者需要对比最新更新时间戳，则重新从数据库获取
        let paramUrl: Array<string> = [];
        if (subType) {
            paramUrl.push(`subType=${subType}`);
        }
        if (feeType) {
            paramUrl.push(`feeType=${feeType}`);
        }
        if (actType) {
            paramUrl.push(`actType=${actType}`);
        }
        // 拼接时间戳，用于对比是否需要更新模板
        if (this.isNotEmpty(timestamp)) {
            paramUrl.push(`timestamp=${timestamp}`);
        }
        // 拼接查询参数
        baseUrl = paramUrl.length > 0 ? `${baseUrl}${type}?${paramUrl.join('&')}` : `${baseUrl}${type}`;
        try {
            if(type === 'newActivity' || type === 'viewActivity'){
                $aegis.report("获取小程序模版", baseUrl, '请求baseUrl');
            }
            const queryData = await $http.post(baseUrl, {});
            if(type === 'newActivity' || type === 'viewActivity'){
                $aegis.report("获取小程序模版", queryData, '返回queryData');
            }
            // 5.查询成功，返回并缓存结果
            if (queryData.success) {
                if(type === 'newActivity' || type === 'viewActivity'){
                    $aegis.report("获取小程序模版", queryData.result, '返回queryData.result');
                }
                if (queryData.result) {
                    const index = queryData.result.indexOf('#');
                    if (index > 0 && index < 30) {
                        result.result = queryData.result.substring(index + 1);
                    } else {
                        result.result = queryData.result;
                    }
                    if(type === 'newActivity' || type === 'viewActivity'){
                        $aegis.report("获取小程序模版", queryData.result, 'result');
                    }
                    result.success = true;
                    result.message = '模板获取成功';
                    tmplObject[moduleKey] = queryData.result;
                    Taro.setStorageSync(QW_MP_TMPL_KEY, tmplObject);
                    return result;
                } else {
                    result.success = true;
                    result.message = '模板获取成功';
                    result.result = jsonResult;
                    if(type === 'newActivity' || type === 'viewActivity'){
                        $aegis.report("获取小程序模版", result, '返回的result');
                    }
                    return result;
                }
            }
        } catch (e) {
            result.success = false;
            result.message = e.error || e.result;
        }
        // 缓存以及后台都未成功获取，返回失败
        return result;
    },
```

#### 查询配置页面信息
```
 A、活动新建可维护的对象（是否必须维护）
await this.$utils.getQwMpTemplate('newActivity', '', this.costTypeCode, this.activityType);
await this.$utils.getQwMpTemplate('newActivity', '', this.costTypeCode);

 B、查看活动
await this.$utils.getQwMpTemplate('viewActivity', 'ActivityItem', this.activityItem.costTypeCode, this.activityItem.actIndeSourCode);
await this.$utils.getQwMpTemplate('viewActivity', 'ActivityItem', this.activityItem.costTypeCode);

 C、活动邀请函
await this.$utils.getQwMpTemplate('viewActivity', 'ActReport', this.activityData.costTypeCode, this.activityData.actIndeSourCode);
await this.$utils.getQwMpTemplate('viewActivity', 'ActReport', this.activityData.costTypeCode);

 D、执行反馈
await this.$utils.getQwMpTemplate('viewActivity', 'ExecutionFeedback', this.activityItem.costTypeCode, this.activityItem.actIndeSourCode);
await this.$utils.getQwMpTemplate('viewActivity', 'ExecutionFeedback', this.activityItem.costTypeCode);

 E、活动邀请函
await this.$utils.getQwMpTemplate('viewActivity', 'ActReport', this.activityData.costTypeCode, this.activityData.actIndeSourCode);
await this.$utils.getQwMpTemplate('viewActivity', 'ActReport', this.activityData.costTypeCode);

 F、活动业务场景
活动的业务场景字段是个字符串数组，所以查询活动每个业务场景对应的模版时，需要解析。
await this.$utils.getQwMpTemplate('businessScenario', busSceneInfo);
```

### 3、关于模版缓存
```
1、是否缓存：所有模版请求之后数据结果都使用Taro.setStorageSync(KEY, tmplObject)缓存。
2、缓存更新机制：PC端调整模版之后点击清除模版缓存后，用户调用指定模版时会对比本地缓存模版的时间戳和实际模版的时间戳，如果不一致那么重新发起请求获取最新的模版配置内容。
如果一致那么直接Taro.getStorageSync(KEY)获取模版内容。
3、清除缓存方式：小程序-个人中心-清除缓存
```
------ 市场活动-页面配置-内容结束 ------
