# 出库扫码-流程内
```
创建时间：2022/03/01 16:32
创建人：  宋燕荣
```
* 产品二维码
```
菜单位置：PC端 产品管理/产品二维码
关键信息：产品编码、产品名称、盖内码、盖外码、状态、是否包材
关联关系：通过产品编码关联查询产品二维码具体数据
```
* 扫码组件
```
组件名称：tasting-wine-scan-code
使用场景：出库扫码、转赠扫码、开瓶核销扫码
显影控制：
    字段：v-if="showOutboundFlag || giftFlag || showOpenBottleFlag"
    条件：1）界面来源 pageSource
         2）市场活动的openScanConfig、giftScanConfig字段
         3）公司编码
```
#### 接口约定
```
1、返回的 bullet 字段： 区分前端处理的场景
    1）纯提示类型为：box;
        使用场景：仅把后端返回的msg字段内容弹出提示用户，没有其他操作。
    2）包含确认 类型为：confirm;
        使用场景：选匹配物资的情况
    3）跳转 类型为：skipBox;
        使用场景：重复扫码的情况
    4）校验通过类型为：none;
        使用场景：通过可以继续操作
2、返回的 msg 字段：弹出的内容
```
* 扫码代码步骤
```
1、点击费用信息界面的扫码入口按钮弹出可选的扫码类型按钮（出库扫码、入库扫码、转赠核销）
2、点击出库扫码按钮（以出库扫码为例）
    1) 调用后台校验接口扫码前校验
        const data = await this.$http.post('action/link/actualFee/scanCodeVerification', {
              actId: this.activityItem.id,//活动ID
              feeStage: this.scene,//费用阶段 场景-实际费用 actual 、申请费用 apply
        }
    2）通过后来返回的关键性字段 bullet 的值分情况处理
        i、bullet = 'box' 直接弹出返回的msg内容，没有其他操作
        ii、bullet = 'none' 可以打开手机摄像头扫码
3、扫盖码后调用接口校验
    1）const queryParam = {
            mark: mark, //码信息
            actId: this.activityItem.id,//活动ID
            feeStage: this.scene,//费用阶段 场景-实际费用 actual 、申请费用 apply
        };
      const data = await this.$http.post('action/link/codeProductRecord/codeProductRecordVerification',queryParam);
    2）通过后台返回的关键性字段 bullet 的值分情况处理
        i： box。直接弹出提示，弹出内容为msg字段值，提供一个确定按钮。点击确定关掉弹框。
        ii：confirm。弹出内容提示，提供取消和确认俩个按钮。取消：关闭弹框，什么也不做。确定：关闭当前弹框，打开关联费用物资的弹框。
        iii：skipBox。直接跳转到重复扫码的界面，界面展示，并且进入重复扫码界面时立即生成重复扫码的扫码记录。
        iiii：none。
            iiii-1、直接生成扫码记录(从后台返回的对象中拿取核心字段的值)
            iiii-2、根据返回的 isEffective 是否有效字段，判断指定字段更新产品二维码数据的字段（产品名称、产品ID、产品编码、产品二维码状态【Y更新 其他值不更新这个字段】）
```
* 扫码记录各字段赋值

|  字段 |  出库扫码  |  转赠核销  |  开瓶扫码  |
| :--- | :------ | :------ | :------ |
| 扫码状态 | 'NormalDelivery';//正常出库 扫码状态 SCAN_RECORD_STATUS | 'Normalgifted';//正常出库 扫码状态 SCAN_RECORD_STATUS | 'Normalbottled';//正常开瓶 扫码状态 SCAN_RECORD_STATUS |
| 扫码类型 | 'ActProdScan';//活动物资扫码 扫码类型 SCAN_TYPE | 'ActProdScan';//活动物资扫码 扫码类型 SCAN_TYPE | 'ActProdScan';//活动物资扫码 扫码类型 SCAN_TYPE |
| 扫码子类型 | 'OutScan';//出库扫码 扫码子类型 SCAN_SUB_TYPE | 'GiftScan';//转赠扫码 扫码子类型 值列表类型: SCAN_SUB_TYPE | 'OpenScan';//开瓶扫码 扫码子类型 值列表类型: SCAN_SUB_TYPE |
| 匹配状态 | 取决于data['match']。为true，'MatchSuccessfully';//匹配状态:匹配成功 MATCH_STATUS。为false，'MatchFailed';//匹配状态:匹配失败 MATCH_STATUS。为'false1'，'MatchFailedEx';//匹配状态:匹配失败 MATCH_STATUS | 同出库扫码 | 同出库扫码 |
| 是否有效 | data['isEffective'];//直接用后台返回的isEffective字段赋值和match同级。| 同出库扫码 | 同出库扫码 |
| 产品ID | 匹配到一个产品的情况直接用后台返回的data.rows['prodId'];//产品id，匹配多个或者不匹配属于confirm的情景选择的物资，赋值为所选物资的产品ID | 同出库扫码 | 同出库扫码 |
| 产品码ID | rows['id'],//产品码id | 同出库扫码 | 同出库扫码 |
| 产品码 | 取后端返回的rows['qrCodeOut'],//产品码 | 取后端返回的rows['qrCodeOut'],//产品码 | 取后端返回的rows['qrCodeIn'] |
| 产品码类型 | 产品码类型按照盖外码赋值为2 | 产品码类型按照盖外码赋值为2 | 产品码类型按照盖外码赋值为1 |
| 物资行ID | 匹配到一个就用返回的rows['materialLineId']，匹配多个或者不匹配属于confirm的情景选择的物资，赋值为所选数据的materialLineId,//物资行id | 同出库扫码 | 同出库扫码 |
| 是否包材 | 直接用后端返回的rows['packagingMaterial'],//是否包材 | 同出库扫码 | 同出库扫码 |
| 活动ID | 当前活动的ID | 同出库扫码 | 同出库扫码 |
| 扫码来源 | 'StaffSystem' | 'StaffSystem' | 'StaffSystem' |
| 扫码人 | 当前登陆用户的用户名 | 同出库扫码 | 同出库扫码 |
| 扫码人ID | 当前登陆用户的ID | 同出库扫码 | 同出库扫码 |
| 扫码时间 | 当前时间 | 同出库扫码 | 同出库扫码 |
| 省 | 定位省／空 | 同出库扫码 | 同出库扫码 |
| 市 | 定位市／空 | 同出库扫码 | 同出库扫码 |
| 区县 | 定位区县／空 | 同出库扫码 | 同出库扫码 |
| 详细地址 | 定位详细地址／空 | 同出库扫码 | 同出库扫码 |

* 更新产品二维码数据

|  字段 |  出库扫码  |  转赠核销  |  开瓶扫码  |
| :--- | :------ | :------ | :------ |
| 产品ID | 匹配到一个产品的情况直接用后台返回的data.rows['prodId'];//产品id，匹配多个或者不匹配属于confirm的情景选择的物资，赋值为所选物资的产品ID | 同出库扫码 | 同出库扫码 |
| 产品名称 | 匹配到一个产品的情况直接用后台返回的data.rows['prodCode'];//产品id，匹配多个或者不匹配属于confirm的情景选择的物资，赋值为所选物资的产品编码 | 同出库扫码 | 同出库扫码 |
| 产品编码 | 匹配到一个产品的情况直接用后台返回的data.rows['prodName'];//产品id，匹配多个或者不匹配属于confirm的情景选择的物资，赋值为所选物资的产品名称 | 同出库扫码 | 同出库扫码 |
| 产品二维码状态 | "OutOfStock";//出库的场景-创建完扫码记录都更新为"已出库" PJ_USER_TYPE | "Gifted";//创建完赠送扫码记录 更新状态为"已赠送" PJ_USER_TYPE | "Bottled";//创建完赠送扫码记录 更新状态为"已开瓶" PJ_USER_TYPE |
| ID | prodQrCodeId | prodQrCodeId | prodQrCodeId |


> 出库扫码
* showOutboundFlag
```
true：默认
false：
    // 值列表：SCAN_USING_STATUS
    //窖龄 执行反馈阶段不展示出库扫码。"开瓶：Unused&转赠：Unused"不显示 其他都显示
    if((activityItemNew['openScanConfig'] === 'Unused' &&  activityItemNew['giftScanConfig'] === 'Unused') ||
       (this.pageSource === 'executiveFeedback' && userInfo.coreOrganizationTile.brandCompanyCode === '5151')){
         showOutboundFlag = false
     }
```
> 转赠核销扫码
* giftFlag
```
false：默认
true：
        //只在执行反馈阶段满足校验条件 展示转赠扫码
            if(this.pageSource === 'executiveFeedback' &&(
                (activityItemNew['openScanConfig']=== 'Required' && activityItemNew['giftScanConfig'] === 'Unused')||
                (activityItemNew['openScanConfig']=== 'NotRequired' && activityItemNew['giftScanConfig'] === 'Unused')||
                (activityItemNew['openScanConfig']=== 'Unused' && activityItemNew['giftScanConfig'] === 'Unused')
            )){
                giftFlag = false;
            }else if(this.pageSource === 'executiveFeedback'){
                giftFlag = true;
            }
```
> 开瓶扫码
* showOpenBottleFlag
```
false：默认
true：
        // 在执行反馈阶段 当openScanConfig为Required或者NotRequired时 展示开瓶扫码按钮 对应值列表为SCAN_USING_STATUS
            if(this.pageSource === 'executiveFeedback'
                && (activityItemNew['openScanConfig'] === 'Required' || activityItemNew['openScanConfig'] === 'NotRequired')
                && (activityItemNew['giftScanConfig'] === 'Unused' || activityItemNew['giftScanConfig'] === 'Required' || activityItemNew['giftScanConfig'] === 'NotRequired')
            ) {
                showOpenBottleFlag = true
            }
```

------ 市场活动-品鉴酒-出库扫码-内容结束 ------
