<template>
    <link-page>
        <link-form :option="option" ref="form" :value="option.formData"
                   :rules="formRules" hideSaveButton>
            <link-form-item label="费用类型" readonly>
                <link-input v-model="option.formData.costType"></link-input>
            </link-form-item>
            <link-form-item label="兑付方式">
                <link-lov v-model="option.formData.feePayType" type="PAY_TYPE_ALL"></link-lov>
            </link-form-item>
            <link-form-item label="费用垫付对象" readonly>
                <link-input v-model="option.formData.feeReim" @tap="pickConsumers"></link-input>
            </link-form-item>
            <link-form-item label="活动申请金额">
                <link-input v-model="option.formData.applyAmount"></link-input>
            </link-form-item>
        </link-form>
        <link-sticky>
            <link-button block @tap="save">保存</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    import {PreloadImg} from "../../../utils/service/PreloadImg";
    import {ROW_STATUS} from "../../../utils/constant";

    export default {
        name: "market-activity-add-cost-info-page",
        components: {
            PreloadImg
        },
        data(){
            const config = {
                data: {
                    ...this.pageParam.data
                },
            };
            const option = new this.FormOption(this, {
                ...config,
                operator: 'NEW',
            });
            return{
                option,
                formRules: {},
                //消费者选择
                consumerOption: new this.AutoList(this, {
                    module: 'action/link/accnt',
                    sortOptions: null,
                    filterOption: [],
                    searchFields: [],
                    param: {
                        filtersRaw: [
                            {id: 'paramFiltersRaw0', property: 'acctType', value: 'Terminal', operator: '='}
                        ]
                    },
                    hooks: {
                        beforeLoad(option) {
                            delete option.param.order;
                            delete option.param.sort;
                        },
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} title={data.name} data={data} arrow="false">
                                <view
                                    style="padding: 11rpx 15rpx;-webkit-box-sizing: border-box;box-sizing: border-box;display: flex;width: 100%;-webkit-flex-direction: row;-ms-flex-direction: row;flex-direction: row;">
                                    <image style=" height: 47px;width: 47px;margin-right: 10px;"
                                           src={PreloadImg.headImgAccount(data)}/>
                                    <view
                                        style="height: 63px;display: flex;flex: 1;flex-direction: column;justify-content: space-between;align-items: flex-start;overflow: hidden;">
                                        <view style="width: 100%;">
                                            <view
                                                style="font-family: PingFangSC-Semibold;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 22px;width: 25%;float: left;">张春明
                                            </view>
                                            <view
                                                style="font-family: PingFangSC-Regular;font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 22px;">***********
                                            </view>
                                        </view>
                                        <view style="width:100%">
                                            <view
                                                style="font-family: PingFangSC-Regular;;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 22px;width: 35%;float: left;">团购消费者
                                            </view>
                                            <view
                                                style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 22px;">核心VIP
                                            </view>
                                        </view>
                                        <view
                                            style="font-family: PingFangSC-Regular;width: 100%;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px;">
                                            <view
                                                style="font-family: PingFangSC-Regular;;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 22px;width: 60%;float: left;">邓氏烟酒店
                                            </view>
                                            <view style="float: left;color: #8C8C8C;line-height: 22px;">业代</view>
                                            <view
                                                style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 22px;padding-left:5px;float:left">胡心怡
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </item>
                        )
                    }
                }),
            }
        },
        methods:{
            /**
             * 保存费用信息
             * <AUTHOR>
             * @date 2020-08-11
             * */
            async save(){
                this.option.formData.id = null;
                this.option.formData.row_status = ROW_STATUS.NEW;
                const data = await this.$http.post('action/link/costDetail/upsert', this.option.formData);
                this.pageParam.callback(data.newRow);
                this.$nav.back();
            },
            /**
             * 选择消费者
             * <AUTHOR>
             * @date 2020-08-11
             * */
            async pickConsumers(){
                const data = await this.$object(this.consumerOption);
                this.$set(this.option.formData,'feeReimCode',data.id);
                this.$set(this.option.formData,'feeReim',data.acctNameForNull);
            }
        }
    }
</script>

<style lang="scss">

</style>