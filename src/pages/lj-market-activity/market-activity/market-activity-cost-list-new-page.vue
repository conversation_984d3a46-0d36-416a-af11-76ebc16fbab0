<template>
    <link-page class="market-activity-cost-list-new-page">
        <ma-navigation-bar :backVisible="true"
                           :zIndex="zIndex"
                           :backgroundImg="$imageAssets.homeMenuBgImage"
                           :title="navigationBarTitle"
                           :titleColor="navigationBarTitleColor"
                           :navBackgroundColor="navBackgroundColor"
                           :udf="udfBack">
        </ma-navigation-bar>
        <view class="base">
            <item :title="scene === 'apply' ? '费用申请' : '费用实际'" @tap="chooseCost">
                <view v-if="$utils.isEmpty(prodPayList) && $utils.isEmpty(cashPayList)">{{
                        scene === 'apply' ? '请选择费用申请'
                            : '请选择费用实际'
                    }}
                </view>
                <view v-if="!$utils.isEmpty(prodPayList) || !$utils.isEmpty(cashPayList)">{{
                        scene === 'apply' ?
                            '可重新调整费用申请' : '可重新调整费用实际'
                    }}
                </view>
            </item>
        </view>
        <view>
            <!--现金类-->
            <view v-for="cashItem in cashPayList" class="base">
                <cash-new :cashShow="true" @updateCash="updateCashData" :bthShow="true" :parentData="activityItem"
                          :cashItem="cashItem" :pageSource="pageSource" :scene="scene"
                          :prodAndCostList="prodAndCostList"
                          @deleteCaseFeePay="deleteCaseFeePayFun" :operateFlag="operateFlag"></cash-new>
            </view>
            <!--产品类-->
            <view v-for="prodItem in prodPayList" class="base">
                <prod-new :prodShow="true" :prodItem="prodItem" :pageSource="pageSource" @deleteProd="deleteProdFun"
                          @updateProd="updateProdData" :scene="scene" @deleteProdFeePay="deleteProdFeePayFun"
                          :prodAndCostList="prodAndCostList"
                          :bthShow="true" :parentData="activityItem" :operateFlag="operateFlag"></prod-new>
            </view>
            <!--扫码记录-->
            <view v-if="scene === 'actual'">
                <tasting-wine-scan-code-record :actId="activityItem.id" :title="'开瓶扫码记录'" :scene="scene" :type="'OpenScan'" :isexecutivefeedback="isexecutivefeedback" class="base"></tasting-wine-scan-code-record>
                <tasting-wine-scan-code-record :actId="activityItem.id" :title="'赠送扫码记录'" :scene="scene" :type="'GiftScan'" :isexecutivefeedback="isexecutivefeedback" class="base"></tasting-wine-scan-code-record>
            </view>
        </view>
        <link-sticky v-if="operant === 'NEW'">
            <link-button block mode="stroke" @tap="lastStep">上一步</link-button>
            <link-button block @tap="nextStep" :disabled="disabledFlag">下一步</link-button>
        </link-sticky>
        <link-sticky v-if="operant === 'UPDATE'">
            <link-button block @tap="save" :disabled="disabledFlag">保存</link-button>
        </link-sticky>
        <tasting-wine-scan-code :scene="scene" :activityItem="activityItem" :pageSource="pageSource" :parentData="activityItem"></tasting-wine-scan-code>
    </link-page>
</template>

<script>
import {DateService,FilterService,LovService,ComponentUtils} from "link-taro-component";
import CashNew from "./components/cash-new";
import ProdNew from "./components/prod-new";
import {ROW_STATUS} from "../../../utils/constant";
import MaNavigationBar from "../ma-navigation-bar/ma-navigation-bar";
import Taro from "@tarojs/taro";
import TastingWineScanCode from "./components/tasting-wine-scan-code";
import TastingWineScanCodeRecord from "./components/tasting-wine-scan-code-record"

export default {
    name: "market-activity-cost-list-new-page",
    components: {TastingWineScanCode, MaNavigationBar, ProdNew, CashNew, TastingWineScanCodeRecord},
    data() {
        //页面来源 -
        // 1、执行反馈环节 executiveFeedback
        // 2、other 活动的其他信息(ps:这是一个页面)
        // 3、preview 活动预览界面而来
        // 4、view 活动查看界面查看而来
        // 5、审批、小程序消息、执行案 为空
        // 6、activityAudit 活动稽核
        const pageSource = this.pageParam.pageSource;
        const scene = this.pageParam.scene;//区分实际费用 actual 还是申请费用 apply
        let activityItem = {};
        if(!this.$utils.isEmpty(this.$dataService.getMarketActivityItem())){
            activityItem = this.$dataService.getMarketActivityItem()
        } else {
            activityItem = this.pageParam.data;
        }
        const activityId = activityItem.id;//活动ID
        const exeCaseId = activityItem.exeCaseId; //执行案ID
        const pageForm = this.pageParam.pageForm;//页面来源 'case' 执行案 'marketActivity' 市场活动
        const operant = this.pageParam.operant;//操作行为 NEW 新建：执行案新建活动和活动新建时，UPDATE 更新：活动详情-费用信息-编辑按钮
        const cashShow = false;//现金类-底部内容是否展示
        const prodShow = false;//产品类-底部内容是否展示
        const maintenanceModules = this.pageParam.maintenanceModules;//新建活动配置的需要维护的模块-用于判别是否配置了互动模块，控制界面下一步跳转页面
        const prodAndCostList = {
            Money: [],//现金支付类数据
            Product: [],//产品支付类数据
        };
        let executivesId = '';
        if (!this.$utils.isEmpty(activityItem.executivesId)) {
            executivesId = activityItem.executivesId;
        }
        const needUpdateActivity = {
            executivesId: executivesId,
            id: activityId,
            row_status: ROW_STATUS.UPDATE,
            province: activityItem.province,//默认是当前活动的省市区县
            provinceId: activityItem.provinceId,//默认是当前活动的省id
            city: activityItem.city,//默认是当前活动的省市区县
            cityId: activityItem.cityId,//默认是当前活动的省id
            district: activityItem.district,//默认是当前活动的省市区县
            districtId: activityItem.districtId,//默认是当前活动的省id
            rowVersion: activityItem.rowVersion,
            companyId: activityItem.companyId,
            updateFields: "id,executivesId,province,provinceId,city,cityId,district,districtId,rowVersion,jiheSecField"
        };
        //需要更新的活动对象-核心字段：费用垫付对象ID
        /**
         * 控制是否可以操作 cash-new 和 prod-new组件的删除、新建、以及删除兑付方式的操作
         * 只允许在编辑界面删除兑付方式、删除明细 、新增明细。其他界面可以编辑小计 和 编辑某个产品信息
         * */
        const operateFlag = this.pageParam.operateFlag;
        //场景来源服务于自定义导航栏。
        //1、市场活动新建 newMarketActivity 2、执行案新建市场活动 caseNewMarketActivity 3、其他 other
        let sceneSourceForNavigation = "other";//默认other
        if (!this.$utils.isEmpty(this.pageParam.sceneSourceForNavigation)) {
            sceneSourceForNavigation = this.pageParam.sceneSourceForNavigation;
        }
        const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
        return {
            // 费用价格是否展示标识
            priceShowFlag: false,
            userInfo,
            disabledFlag: false,
            navigationBarTitle: '费用信息',
            navigationBarTitleColor: '#ffffff',
            navBackgroundColor: 'transparent',
            zIndex: ComponentUtils.nextIndex(),
            sceneSourceForNavigation,
            operateFlag,
            pageSource,
            scene,
            needUpdateActivity,
            maintenanceModules,
            prodAndCostList,//产品和费用list
            operant,
            activityId,
            activityItem,
            cashShow,
            prodShow,
            exeCaseId,
            pageForm,
            noCaseChooseCostOption: new this.AutoList(this, {
                module: 'action/link/payment',
                sortOptions: null,
                param: {
                    filtersRaw: [
                        //是否有效
                        {
                            id: 'effectiveFlag',
                            property: 'effectiveFlag',
                            value: 'Y',
                            operator: '='
                        },
                    ],
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} arrow="false">
                            <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                            <view
                                style="display: -webkit-box;display: -ms-flexbox;display: flex;padding: 12px 8px 12px 12px;width:100%">
                                <view
                                    style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px;">
                                    {data.payCode}-{data.payName}
                                </view>
                            </view>
                        </item>
                    )
                }
            }),
            haveCaseChooseCostOption: new this.AutoList(this, {
                url: {
                    //查询活动未选中的费用
                    queryByExamplePage: 'action/link/costDetail/queryUnSelectPage'
                },
                param: {
                    //开启时间校验attr5判断有效时间   attr6 +5天判断有效时间   scene那个步骤调用组件
                    attr5: scene === 'apply' ? 'Y' : '',
                    attr6: scene === 'apply' ? '' : 'Y',
                    filtersRaw: [
                        {id: "actId", property: "actId", value: activityId, operator: '='},
                        {id: 'actProgId', property: 'actProgId', value: exeCaseId, operator: '='},
                        {id: 'costStatus', property: 'costStatus', value: 'Efficient', operator: '='}
                    ],
                    oauth: 'MY_ORG',
                },
                sortOptions: null,
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} arrow="false"
                              style="margin: 0px 12px 12px 12px;border-radius: 8px;">
                            <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                            <view style="width: 100%;background: white;height: 155px;">
                                <view style="width: 100%;float: left;">
                                    <view
                                        style="display: -webkit-box;display: -ms-flexbox;display: flex;margin: auto;">
                                        <view style="width: 100%;height: 40px;line-height:40px;padding-left: 16px;">
                                            <view
                                                style="font-family: PingFangSC-Semibold;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 16px;padding-top: 10px;width:70%;float:left">{data.costPaymentWay}
                                            </view>
                                            <view
                                                style="color: #2F69F8;font-size: 14px">{LovService.filter(data.costStatus, 'FEE_STATUS')}</view>
                                        </view>
                                    </view>
                                    <view style="height: 30px;line-height: 30px;padding-left: 16px;width:100%">
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 12px;color: #000000;letter-spacing: 0;line-height: 14px;">
                                            有效期:{DateService.filter(data.startTime, 'YYYY-MM-DD HH:mm')}至{DateService.filter(data.endTime, 'YYYY-MM-DD HH:mm')}
                                        </view>
                                    </view>
                                    <view style="height: 30px;line-height: 30px;padding-left: 16px;width:100%">
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 12px;color: #000000;letter-spacing: 0;line-height: 14px;">
                                            小类名称:{data.costTypeName}
                                        </view>
                                    </view>
                                    <view style="height: 30px;line-height: 30px;padding-left: 16px;width:100%">
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 12px;color: #000000;letter-spacing: 0;line-height: 14px;">
                                            费用垫付对象:{data.feeReim}
                                        </view>
                                    </view>
                                    <view style="padding-left: 16px;">
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 12px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;width: 50%;float: left;">
                                            {this.priceShowFlag ? `可用余额:`+FilterService.cny(data.availableBalance): (data.payType!=='Money'? '' : `可用余额:`+FilterService.cny(data.availableBalance)) }
                                        </view>
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 12px;color: #8C8C8C;letter-spacing: 0;text-align: right;line-height: 14px;width: 50%;float: left;">
                                            {this.priceShowFlag ? `审批金额:`+FilterService.cny(data.approvalAmount): (data.payType!=='Money'? '' : `审批金额:`+FilterService.cny(data.approvalAmount)) }
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </item>
                    )
                }
            }),
            ...(() => {
                const {prodPayList, cashPayList} = this.pageParam.costListData;
                setTimeout(() => {
                    /*返回上一级的时候，删除列表数据*/
                    this.$on('hook:beforeDestroy', () => {
                        this.pageParam.costListData.prodPayList = []
                        this.pageParam.costListData.cashPayList = []
                    })
                    /*列表数据被修改的时候，保存到上一个页面的缓存变量中*/
                    this.$watch(() => this.prodPayList, val => {
                        this.pageParam.costListData.prodPayList = val
                    })
                    this.$watch(() => this.cashPayList, val => {
                        this.pageParam.costListData.cashPayList = val
                    })
                })
                return {
                    prodPayList,
                    cashPayList,
                    isexecutivefeedback: true //判断是否有边框
                }
            })(),
        }
    },
    async created() {
        if (!this.$utils.isEmpty(this.activityId)) {
            await this.queryActivityProdAndCostList();
        }
        this.priceShowFlag = await this.$utils.getPriceDesensitize(this.userInfo);
    },
    onShow(){
        this.activityItem = this.$dataService.getMarketActivityItem();
    },
    watch: {
        prodAndCostList: {
            handler(newVal) {
                this.prodPayList = newVal.Product;
                this.cashPayList = newVal.Money;
            },
            deep: true
        }
    },
    mounted() {
        this.$bus.$on('initActivityProdAndCostList', async () => {
            await this.queryActivityProdAndCostList();
        });
        this.$bus.$on('ProductRrefresh', (data) => {
            this.$set(this.prodAndCostList, 'Product', data);
        });
        this.$bus.$on('ProductDataListRrefreshs', (feePayType, costId, dataList) => {
            this.prodAndCostList.Product.forEach((item) => {
                if (item.feePayType === feePayType && item.costId === costId) {
                    this.$set(item, 'dataList', dataList);
                }
            })
        });
        this.$bus.$on('MoneyRrefresh', (data) => {
            this.$set(this.prodAndCostList, 'Money', data);
        });
        this.$bus.$on('MoneyDataListRrefresh', (feePayType, costId, dataList) => {
            this.prodAndCostList.Money.forEach((item) => {
                if (item.feePayType === feePayType && item.costId === costId) {
                    this.$set(item, 'dataList', dataList);
                }
            })
        });
        //如果使用扫码功能或删除扫码记录，前置保存一下费用数据
        this.$bus.$on("scanBeforeSaveCostOrDeleteScanCode", async () => {
            let returnFlag = true;
            if(this.operant === 'NEW'){
                returnFlag = await this.nextStep('scanBeforeSaveCost');
                this.$bus.$emit('costSaveReturnFlag',returnFlag);
            } else if (this.operant === 'UPDATE'){
                returnFlag = await this.save('scanBeforeSaveCost');
                this.$bus.$emit('costSaveReturnFlag',returnFlag);
            }
        });
        this.$bus.$on('scanCompleteInitCostList', async () => {
            await this.queryActivityProdAndCostList('scanCompleteInitCostList');
        });
    },
    methods: {
        /**
         * 自定义返回函数
         * @songyanrong
         * @date 2020-12-02
         * */
        udfBack() {
            if (this.sceneSourceForNavigation === 'other' || this.sceneSourceForNavigation === 'caseNewMarketActivity') {
                this.$nav.back();
            } else {
                let pages = Taro.getCurrentPages();    //获取当前页面信息栈
                let targetIndex = pages.findIndex(function (item) {
                    return item.route === "pages/lj-market-activity/market-activity/market-activity-list-page";
                });
                if (targetIndex === -1) {
                    return this.$nav.backAll()
                }
                const num = Number(pages.length - (Number(targetIndex) + 1));
                setTimeout(() => {
                    this.$bus.$emit('marketActivityListRefresh');
                    this.$nav.back(null, num);
                }, 1000)
            }
        },
        /**
         * 删除某个产品
         * <AUTHOR>
         * @date 2020-11-06
         * */
        deleteProdFun(feePayType, dataList) {
            for (let i = 0; i < this.prodAndCostList.Product.length; i++) {
                if (this.prodAndCostList.Product[i].feePayType === feePayType) {
                    this.$set(this.prodAndCostList.Product[i], 'dataList', dataList);
                }
            }
            this.$bus.$emit("ProductRrefresh", this.prodAndCostList.Product);
        },
        /**
         * 选择费用申请
         * <AUTHOR>
         * @date 2020-08-19
         * */
        async chooseCost() {
            if (this.$utils.isEmpty(this.exeCaseId)) {
                await this.noCaseChooseCost();
            } else {
                await this.haveCaseChooseCost();
            }
        },
        /**
         * 无执行案时选费用
         * <AUTHOR>
         * @date 2020-08-19
         * */
        async noCaseChooseCost() {
            const that = this;
            const list = await this.$object(this.noCaseChooseCostOption, {multiple: true});
            list.forEach(i => {
                if (i['payType'] === 'Product') {
                    const j = {
                        feeReimId: "",
                        feeReimCode: "",
                        feeReim: "",
                        feePayType: i.payName,
                        feePayCode: i.payCode,
                        costId: "",
                        costCode: "",
                        costTypeCode: "",
                        dataList: [],
                        payType: 'Product'
                    };
                    if (that.$utils.isEmpty(that.prodAndCostList.Product)) {
                        that.prodAndCostList.Product.push(j);
                    } else {
                        const feePayCodeExist = that.prodAndCostList.Product.filter((item) => item.feePayCode === i.payCode);
                        if (this.$utils.isEmpty(feePayCodeExist)) {
                            that.prodAndCostList.Product.push(j);
                        }
                    }
                }
            });
            list.forEach(i => {
                if (i['payType'] === 'Money') {
                    const j = {
                        feeReimId: "",
                        feeReimCode: "",
                        feeReim: "",
                        feePayType: i.payName,
                        feePayCode: i.payCode,
                        costId: "",
                        costCode: "",
                        costTypeCode: "",
                        dataList: [],
                        payType: 'Money'
                    };
                    if (that.$utils.isEmpty(that.prodAndCostList.Money)) {
                        that.prodAndCostList.Money.push(j);
                    } else {
                        const feePayCodeExist = that.prodAndCostList.Money.filter((item) => item.feePayCode === i.payCode);
                        if (this.$utils.isEmpty(feePayCodeExist)) {
                            that.prodAndCostList.Money.push(j);
                        }
                    }
                }
            });
            if (!this.$utils.isEmpty(this.prodAndCostList.Money)) {
                this.$set(this, 'cashShow', true);
            }
            if (!this.$utils.isEmpty(this.prodAndCostList.Product)) {
                this.$set(this, 'prodShow', true);
            }
        },
        /**
         * 有执行案时选费用
         * <AUTHOR>
         * @date 2020-08-19
         * */
        async haveCaseChooseCost() {
            /*
            * 区分来自执行案还是市场活动
            * 1、如果来自执行案详情的新建活动按钮 那么新建的活动信息【费用大中小类】来自费用明细上
            * 2、如果来自市场活动模块新建
            * */
            // 2、市场活动新建
            //2020-10-09 新加：添加费用时当为选择执行案明细时，带出费用垫付对象ID和名称 更新到活动头上
            //2020-10-19 新加：当关联上执行案明细后，则使用执行案明细上的省-市-区县更新活动上的省市区县；
            const list = await this.$object(this.haveCaseChooseCostOption, {
                multiple: true,
                pageTitle: "费用明细",
            });
            list.forEach(i => {
                if (!this.$utils.isEmpty(i.feeReimId)) {
                    this.needUpdateActivity.executivesId = i.feeReimId;//executivesId 费用垫付对象ID
                    this.needUpdateActivity.executivesName = i.feeReim;//费用垫付对象名称-用于活动新建界面展示-不存
                    this.needUpdateActivity.availableBalance = i.availableBalance;//可用余额-用于费用申请即将恶魔展示-不存
                    this.needUpdateActivity.province = i.province;
                    this.needUpdateActivity.provinceId = i.provinceId;
                    this.needUpdateActivity.city = i.city;
                    this.needUpdateActivity.cityId = i.cityId;
                    this.needUpdateActivity.district = i.district;
                    this.needUpdateActivity.districtId = i.districtId;
                }
                if (i['payType'] === 'Product') {
                    const j = {
                        feeReimId: i.feeReimId,
                        feeReimCode: i.feeReimCode,
                        feeReim: i.feeReim,
                        feePayType: i.costPaymentWay,
                        feePayCode: i.costPaymentWayCode,
                        availableBalance: i.availableBalance,
                        costId: i.id,
                        costCode: i.executionDeatilCode,
                        costTypeCode: i.costTypeCode,
                        dataList: [],
                        costTypeName:i.costTypeName,
                        payType: 'Product'
                    };
                    if (this.$utils.isEmpty(this.prodAndCostList.Product)) {
                        this.prodAndCostList.Product.push(j);
                    } else {
                        const feePayCodeExist = this.prodAndCostList.Product.filter((item) => item.costId === i.id);
                        if (this.$utils.isEmpty(feePayCodeExist)) {
                            this.prodAndCostList.Product.push(j);
                        }
                    }
                }
            });
            list.forEach(i => {
                if (!this.$utils.isEmpty(i.feeReimId)) {
                    this.needUpdateActivity.executivesId = i.feeReimId;//executivesId 费用垫付对象
                    this.needUpdateActivity.executivesName = i.feeReim;//费用垫付对象名称-用于活动新建界面展示-不存
                    this.needUpdateActivity.availableBalance = i.availableBalance;//可用余额-用于费用申请即将恶魔展示-不存
                    this.needUpdateActivity.province = i.province;
                    this.needUpdateActivity.provinceId = i.provinceId;
                    this.needUpdateActivity.city = i.city;
                    this.needUpdateActivity.cityId = i.cityId;
                    this.needUpdateActivity.district = i.district;
                    this.needUpdateActivity.districtId = i.districtId;
                }
                if (i['payType'] === 'Money') {
                    const j = {
                        feeReimId: i.feeReimId,
                        feeReimCode: i.feeReimCode,
                        feeReim: i.feeReim,
                        feePayType: i.costPaymentWay,
                        feePayCode: i.costPaymentWayCode,
                        availableBalance: i.availableBalance,
                        costId: i.id,
                        costCode: i.executionDeatilCode,
                        costTypeCode: i.costTypeCode,
                        dataList: [],
                        costTypeName:i.costTypeName,
                        payType: 'Money'
                    };
                    if (this.$utils.isEmpty(this.prodAndCostList.Money)) {
                        this.prodAndCostList.Money.push(j);
                    } else {
                        const feePayCodeExist = this.prodAndCostList.Money.filter((item) => item.costId === i.id);
                        if (this.$utils.isEmpty(feePayCodeExist)) {
                            this.prodAndCostList.Money.push(j);
                        }
                    }

                }
            });
            if (!this.$utils.isEmpty(this.prodAndCostList.Money)) {
                this.$set(this, 'cashShow', true);
            }
            if (!this.$utils.isEmpty(this.prodAndCostList.Product)) {
                this.$set(this, 'prodShow', true);
            }
            if (this.pageForm === 'case') {
                //1、执行案详情新建,如果用户选择的执行案明细上 费用小类不一致 那么 抛错提示‘请选择相同费用小类的明细创建活动！
                if (!this.$utils.isEmpty(this.needUpdateActivity.executivesId)) {
                    this.activityItem.executivesId = this.needUpdateActivity.executivesId
                }
                if (!this.$utils.isEmpty(this.needUpdateActivity.executivesName)) {
                    this.activityItem.executivesName = this.needUpdateActivity.executivesName;
                }
                this.activityItem.province = this.needUpdateActivity.province;
                this.activityItem.provinceId = this.needUpdateActivity.provinceId;
                this.activityItem.city = this.needUpdateActivity.city;
                this.activityItem.cityId = this.needUpdateActivity.cityId;
                this.activityItem.district = this.needUpdateActivity.district;
                this.activityItem.districtId = this.needUpdateActivity.districtId;
                const benchmarkData = list[0];//判别选择的执行案明细的费用小类是否一致
                this.activityItem.costLargeType = benchmarkData.costLargeType;
                this.activityItem.costLargeTypeCode = benchmarkData.costLargeTypeCode;
                this.activityItem.costMiddleType = benchmarkData.costMiddleType;
                this.activityItem.costMiddleTypeCode = benchmarkData.costMiddleTypeCode;
                this.activityItem.costType = benchmarkData.costTypeName;
                this.activityItem.costTypeCode = benchmarkData.costTypeCode;
            }
        },
        /**
         * 更新产品数据
         * <AUTHOR>
         * @date 2020-08-26
         * */
        async updateProdData(feePayType, costId, editDataList) {
            for (let ii = 0; ii < this.prodAndCostList.Product.length; ii++) {
                const i = this.prodAndCostList.Product[ii];
                if (i.feePayType === feePayType && i.costId === costId) {
                    let tempList = [];
                    tempList = this.$utils.deepcopy(i.dataList);
                    for (let j = 0; j < editDataList.length; j++) {
                        const item = editDataList[j];
                        const cashExit = tempList.filter((item1) => item1.prodId === item.prodId)[0];
                        if (this.$utils.isEmpty(cashExit)) {
                            tempList.push(item);
                        } else {
                            //分情况增加产品数量
                            if (this.scene === 'apply') {
                                cashExit.qty++
                            }
                            if (this.scene === 'actual') {
                                //当产品属于品鉴酒产品组，并且不属于 giftScanConfig 和 openScanConfig 都为 Unused 的情况时不累加；
                                const data = await this.$http.post('action/link/actualFee/checkIsWineTastingByProd',
                                    {prodId: item.prodId});
                                let reply = data['reply'];//reply:true = 是品鉴酒 false = 不是品鉴酒
                                //当产品属于品鉴酒产品组，并且不属于 giftScanConfig 和 openScanConfig 都为 Unused 的情况时不累加，否则累加。
                                if(reply && !(this.activityItem['giftScanConfig'] === 'Unused' && this.activityItem['openScanConfig'] === 'Unused')){
                                    cashExit.actualQty = cashExit.actualQty;
                                } else {
                                    cashExit.actualQty++
                                }
                            }
                        }
                    }
                    i.dataList = [...tempList];
                }
            };
        },
        /**
         * 更新现金数据
         * <AUTHOR>
         * @date 2020-08-26
         * */
        updateCashData(feePayType, costId, editDataList) {
            this.prodAndCostList.Money.forEach(i => {
                if (i.feePayType === feePayType && i.costId === costId) {
                    let tempList = [];
                    tempList = this.$utils.deepcopy(i.dataList);
                    for (let i = 0; i < editDataList.length; i++) {
                        const item = editDataList[i];
                        const cashExit = tempList.filter((item1) => item1.prodName === item.prodName);
                        if (this.$utils.isEmpty(cashExit)) {
                            tempList.push(item);
                        }
                    }
                    i.dataList = [...tempList];
                }
            });
        },
        /**
         * 上一步：新建活动时
         * <AUTHOR>
         * @date 2020-08-05
         * */
        lastStep() {
            if (this.pageForm !== 'case') {
                this.$bus.$emit("marketActivity");
            }
            this.$nav.back();
        },
        /**
         * 下一步：新建活动时
         * <AUTHOR>
         * @date 2020-08-05
         * scenarioType:next/back next标志正常流程往下走 back点左上角返回需要保存信息后返回
         * type: "scanBeforeSaveCost" ：扫码之前前置保存的场景，不跳转下一页。
         * */
        async nextStep(type) {
            let baseList = [];//大类的列表
            if (this.$utils.isEmpty(this.cashPayList)) {
                baseList = this.$utils.deepcopy(this.prodPayList);
            } else {
                baseList = this.cashPayList.concat(this.prodPayList);
            }
            if (this.$utils.isEmpty(baseList)) {
                this.$message.warn("请维护费用信息！",{customFlag:true});
                return false;
            }
            //校验兑付方式下需要维护明细
            const emptyDataList = baseList.filter((item) => this.$utils.isEmpty(item.dataList));
            let placeholderDataRequired = "";
            if (!this.$utils.isEmpty(emptyDataList)) {
                for (let i = 0; i < emptyDataList.length; i++) {
                    placeholderDataRequired = placeholderDataRequired + emptyDataList[i].feePayType + ',';
                }
                placeholderDataRequired = placeholderDataRequired + '兑付方式下没有物资明细，请维护数据或删除该兑付方式!'
            }
            if (!this.$utils.isEmpty(placeholderDataRequired)) {
                this.$message.warn(placeholderDataRequired,{customFlag:true});
                return false;
            }
            //2021-09-29 为了后台计算现金类不报错，现金类申请数量和实际数量默认给1
            baseList.forEach((item) => {
                item.dataList.forEach((item2) => {
                    item2.payType = item.payType
                })
            });
            /**
             * //1-判断费用小类是否一致：限制选择的费用信息需和市场活动上的费用小类。
             如果费用明细没关联执行案明细就不校验，如果关联了执行案明细，那么判断活动上的费用小类和活动上的费用小类是否一致，不一致的话提示【XXX兑付方式关联的执行案明细的费用小类与市场活动的费用小类XXX不一致，请确认后重新选择】。
             //2-执行案新建时和市场活动新建时都需要判断费用小类是否一致：判断选择的费用明细的费用小类是否一致。
             如果费用明细有没关联执行案明细的费用信息就不校验，只校验关联了执行案明细的费用明细上的费用小类是不是一致，不一致的话提示【请选择相同费用小类的明细数据】。
             * */
            //1-判断费用小类是否一致：限制选择的费用信息需和市场活动上的费用小类
            let costTypePlaceholder = "";
            if (this.pageForm !== 'case') {
                const costCheckData = baseList.filter(item => !this.$utils.isEmpty(item.costTypeCode) && item.costTypeCode !== this.activityItem.costTypeCode);
                if (!this.$utils.isEmpty(costCheckData)) {
                    for (let i = 0; i < costCheckData.length; i++) {
                        costTypePlaceholder = costTypePlaceholder + costCheckData[i].feePayType + ',';
                    }
                    this.$showError(`${costTypePlaceholder}兑付方式关联的执行案明细的费用小类与市场活动的费用小类【${this.activityItem.costType}】不一致，请确认后重新选择。`,{customFlag:true});
                    return false;
                }
            }
            //2-执行案新建时和市场活动新建时都需要判断费用小类是否一致：判断选择的费用明细的费用小类是否一致
            const benchmarkData = baseList[0];
            const benchmarkData2 = baseList.filter(item => item.costTypeCode !== benchmarkData.costTypeCode);
            if (!this.$utils.isEmpty(benchmarkData2)) {
                this.$showError("请选择相同费用小类的明细数据",{customFlag:true});
                return false;
            }
            //校验兑付方式下的申请总额与可用余额的大小。费用实际后台校验
            let placeholderCalculate = "";
            if (this.scene === 'apply' && !this.$utils.isEmpty(this.exeCaseId)) {
                for (let i = 0; i < baseList.length; i++) {
                    //可用余额
                    const availableBalance = baseList[i].availableBalance;
                    //申请总额
                    let sum = 0;
                    if (!this.$utils.isEmpty(baseList[i].costId)) {
                        baseList[i].dataList.forEach(item => {
                            if (!this.$utils.isEmpty(item.qty)) {
                                sum += this.$utils.numberMul(item.actualTranPrice, item.qty);
                            } else {
                                sum += Number(item.actualTranPrice)
                            }
                        });
                        if (Number(sum) > Number(availableBalance)) {
                            placeholderCalculate = placeholderCalculate + baseList[i].feePayType + ',';
                        }
                    }
                }
                if (!this.$utils.isEmpty(placeholderCalculate)) {
                    placeholderCalculate = placeholderCalculate + '兑付方式下申请总额不能超过可用余额，请检查修改!'
                }
            }
            if (!this.$utils.isEmpty(placeholderCalculate)) {
                this.$message.warn(placeholderCalculate,{customFlag:true});
                return false;
            }
            this.disabledFlag = true;
            let insertCostList = [];//新建时 插入到费用表中的数据列表
            baseList.forEach(item => {
                insertCostList = insertCostList.concat(item.dataList);
            });
            insertCostList = insertCostList.filter((item) => this.$utils.isEmpty(item.id));
            //现金类数量默认为1
            insertCostList.forEach((item) => {
                if(item.payType === 'Money'){
                    item.qty = 1 //现金类数量默认为1
                    item.actualQty = 1 //现金类实际数量默认为1
                }
            });
            if (this.pageForm === 'case') {
                this.disabledFlag = false;
                const id = await this.$newId();
                this.activityItem.id = id;
                insertCostList.forEach((item) => {
                    item.actId = id;
                });
                //如果是执行案-点击新建活动时-维护完费用信息
                this.$nav.push('/pages/lj-market-activity/perform-case/new-activity-page', {
                    data: this.activityItem,
                    insertCostList: insertCostList,
                })
            } else {
                let activityItemCopy = {};
                if(!this.$utils.isEmpty(this.$dataService.getMarketActivityItem())){
                    activityItemCopy = this.$dataService.getMarketActivityItem();
                } else {
                    activityItemCopy = this.activityItem;
                }

                //如果市场活动上的的执行案ID exeCaseId 为空，那么费用对象上的执行案明细ID costId 设为空。
                if(this.$utils.isEmpty(activityItemCopy['exeCaseId'])){
                    insertCostList.forEach((item) => {
                        item.costId = null;
                    })
                }

                // 获取缓存中的活动版本号
                if(!this.$utils.isEmpty(this.$dataService.getMarketActivityItem())){
                    this.needUpdateActivity.rowVersion = this.$dataService.getMarketActivityItem()['rowVersion']
                } else {
                    this.needUpdateActivity.rowVersion = activityItemCopy['rowVersion'];
                }
                this.needUpdateActivity.updateFields = "id,executivesId,province,provinceId,city,cityId,district,districtId,rowVersion,jiheSecField";
                let dealData = {
                    marketAct : this.needUpdateActivity
                };
                if (this.scene === 'apply') {
                    dealData['actMaterialList'] = insertCostList;
                } else if (this.scene === 'actual') {
                    dealData['actualFeeList'] = insertCostList;
                }
                this.$utils.showLoading();
                const data = await this.$http.post('action/link/actIntegration/updateActIdAndCityAfterSelectedFee', dealData,{
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$showError(response.result,{customFlag:true});
                        this.disabledFlag = false;
                        this.$utils.hideLoading();
                    }
                });
                this.$utils.hideLoading();
                this.activityItem = {...data.result};
                this.$dataService.setMarketActivityItem(data.result);
                await this.queryActivityProdAndCostList();
                this.disabledFlag = false;
                if(this.$utils.isEmpty(type) || type !== 'scanBeforeSaveCost'){
                    const interactiveConfig = this.maintenanceModules.filter(item => item.values['clickFun'] === 'addConfigurationInformation');
                    if (!this.$utils.isEmpty(interactiveConfig)) {
                        //如果是市场活动模块-新建活动-维护了-互动信息模块 否则跳转至其他信息界面。
                        this.$nav.push('/pages/lj-market-activity/market-activity/marketing-activity-model/marketing-activity-model-list-page', {
                            data: this.activityItem,
                            sceneSourceForNavigation: this.sceneSourceForNavigation,
                            interactiveConfigRequire: this.pageParam.interactiveConfigRequire
                        });
                    } else {
                        this.$nav.push('/pages/lj-market-activity/market-activity/new-market-activity-other-info-page', {
                            data: this.activityItem,
                            sceneSourceForNavigation: this.sceneSourceForNavigation,
                        })
                    }
                }
            }
        },
        /**
         * 保存
         * <AUTHOR>
         * @date 2020-08-27
         * type: "scanBeforeSaveCost" ：扫码之前前置保存的场景，不跳转下一页。
         * */
        async save(type) {
            let baseList = [];//基础的列表
            if (!this.$utils.isEmpty(this.cashPayList)) {
                baseList = this.cashPayList.concat(this.prodPayList);
            } else {
                baseList = this.$utils.deepcopy(this.prodPayList);
            }
            if (this.$utils.isEmpty(baseList)) {
                this.$message.warn("请维护费用信息！",{customFlag:true});
                return false;
            }
            const emptyDataList = baseList.filter((item) => this.$utils.isEmpty(item.dataList));
            let placeholderDataRequired = "";
            if (!this.$utils.isEmpty(emptyDataList)) {
                for (let i = 0; i < emptyDataList.length; i++) {
                    placeholderDataRequired = placeholderDataRequired + emptyDataList[i].feePayType + ',';
                }
                placeholderDataRequired = placeholderDataRequired + '兑付方式下无物资明细，请维护数据或删除该兑付方式!'
            }
            if (!this.$utils.isEmpty(placeholderDataRequired)) {
                this.$message.warn(placeholderDataRequired,{customFlag:true});
                return false;
            }
            //2021-09-29 为了后台计算现金类不报错，现金类申请数量和实际数量默认给1
            baseList.forEach((item) => {
                item.dataList.forEach((item2) => {
                    item2.payType = item.payType
                })
            });
            /**
             * //1-判断费用小类是否一致：限制选择的费用信息需和市场活动上的费用小类。
             如果费用明细没关联执行案明细就不校验，如果关联了执行案明细，那么判断活动上的费用小类和活动上的费用小类是否一致，不一致的话提示【XXX兑付方式关联的执行案明细的费用小类与市场活动的费用小类XXX不一致，请确认后重新选择】。
             //2-执行案新建时和市场活动新建时都需要判断费用小类是否一致：判断选择的费用明细的费用小类是否一致。
             如果费用明细有没关联执行案明细的费用信息就不校验，只校验关联了执行案明细的费用明细上的费用小类是不是一致，不一致的话提示【请选择相同费用小类的明细数据】。
             * */
            //1-判断费用小类是否一致：限制选择的费用信息需和市场活动上的费用小类
            let costTypePlaceholder = "";
            if (this.pageForm !== 'case') {
                const costCheckData = baseList.filter(item => !this.$utils.isEmpty(item.costTypeCode) && item.costTypeCode !== this.activityItem.costTypeCode);
                if (!this.$utils.isEmpty(costCheckData)) {
                    for (let i = 0; i < costCheckData.length; i++) {
                        costTypePlaceholder = costTypePlaceholder + costCheckData[i].feePayType + ',';
                    }
                    this.$showError(`${costTypePlaceholder}兑付方式关联的执行案明细的费用小类与市场活动的费用小类【${this.activityItem.costType}】不一致，请确认后重新选择。`,{customFlag:true});
                    return false;
                }
            }
            //2-执行案新建时和市场活动新建时都需要判断费用小类是否一致：判断选择的费用明细的费用小类是否一致
            const associatedCsaeItemList = baseList.filter(item => !this.$utils.isEmpty(item.costTypeCode));
            if(!this.$utils.isEmpty(associatedCsaeItemList)){
                const benchmarkData = associatedCsaeItemList[0];
                const benchmarkData2 = associatedCsaeItemList.filter(item => item.costTypeCode !== benchmarkData.costTypeCode);
                if (!this.$utils.isEmpty(benchmarkData2)) {
                    this.$showError("请选择相同费用小类的明细数据",{customFlag:true});
                    return false;
                }
            }

            //校验兑付方式下的申请总额与可用余额的大小。费用实际后台校验
            let placeholderCalculate = "";
            if (this.scene === 'apply' && !this.$utils.isEmpty(this.exeCaseId)) {
                for (let i = 0; i < baseList.length; i++) {
                    //可用余额
                    const availableBalance = baseList[i].availableBalance;
                    //申请总额
                    let sum = 0;
                    if (!this.$utils.isEmpty(baseList[i].costId)) {
                        baseList[i].dataList.forEach(item => {
                            if (!this.$utils.isEmpty(item.qty)) {
                                sum += this.$utils.numberMul(item.actualTranPrice, item.qty);
                            } else {
                                sum += Number(item.actualTranPrice)
                            }
                        });
                        if (Number(sum) > Number(availableBalance)) {
                            placeholderCalculate = placeholderCalculate + baseList[i].feePayType + ',';
                        }
                    }
                }
                if (!this.$utils.isEmpty(placeholderCalculate)) {
                    placeholderCalculate = placeholderCalculate + '兑付方式下申请总额不能超过可用余额，请检查修改!'
                }
            }
            if (!this.$utils.isEmpty(placeholderCalculate)) {
                this.$message.warn(placeholderCalculate,{customFlag:true});
                return false;
            }
            this.disabledFlag = true;
            let insertDataListTemp = [];
            let insertDataList = [];//新建数据数组
            let updateCashListTemp = [];
            let updateCashList = [];//更新的现金数组
            baseList.forEach(item => {
                insertDataListTemp = insertDataListTemp.concat(item.dataList);
            });
            if (!this.$utils.isEmpty(this.cashPayList)) {
                this.cashPayList.forEach(item => {
                    updateCashListTemp = updateCashListTemp.concat(item.dataList);
                });
            }
            insertDataListTemp.forEach(item => {
                if (item.row_status === ROW_STATUS.NEW) {
                    insertDataList.push(item);
                }
            });
            //现金类数量默认为1
            insertDataList.forEach((item) => {
                if(item.payType === 'Money'){
                    item.qty = 1 //现金类数量默认为1
                    item.actualQty = 1 //现金类实际数量默认为1
                }
            });
            if (!this.$utils.isEmpty(updateCashListTemp)) {
                updateCashListTemp.forEach(item => {
                    if (item.row_status !== ROW_STATUS.NEW) {
                        const updateDate = {
                            id: item.id,
                            costId: item.costId,
                            actualTranPrice: item.actualTranPrice,
                            row_status: ROW_STATUS.UPDATE,
                            updateFields: "id,actualTranPrice"
                        };
                        updateCashList.push(updateDate);
                    }
                });
            }

            let activityItemCopy = {};
            if(!this.$utils.isEmpty(this.$dataService.getMarketActivityItem())){
                activityItemCopy = this.$dataService.getMarketActivityItem();
            } else {
                activityItemCopy = this.activityItem;
            }

            // 获取缓存中的活动版本号
            if(!this.$utils.isEmpty(this.$dataService.getMarketActivityItem())){
                this.needUpdateActivity.rowVersion = this.$dataService.getMarketActivityItem()['rowVersion']
            } else {
                this.needUpdateActivity.rowVersion = activityItemCopy['rowVersion'];
            }
            this.needUpdateActivity.updateFields = "id,executivesId,province,provinceId,city,cityId,district,districtId,rowVersion,jiheSecField";
            let dealData = {
                marketAct : this.needUpdateActivity
            };
            //产品类更新某一条时有单独保存的机制。
            let dealCashList = [];
            if(!this.$utils.isEmpty(updateCashList)){
                dealCashList = insertDataList.concat(updateCashList)
            } else {
                dealCashList = insertDataList;
            }
            if (this.scene === 'apply') {
                dealData['actMaterialList'] = dealCashList;
            } else if (this.scene === 'actual') {
                dealData['actualFeeList'] = dealCashList;
            }
            this.$utils.showLoading();
            const data = await this.$http.post('action/link/actIntegration/updateActIdAndCityAfterSelectedFee', dealData,{
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(response.result,{customFlag:true});
                    this.disabledFlag = false;
                    this.$utils.hideLoading();
                }
            });
            this.$utils.hideLoading();
            this.activityItem = {...data.result};
            this.$dataService.setMarketActivityItem(data.result);
            await this.queryActivityProdAndCostList();
            this.disabledFlag = false;
            this.pageParam.callback(this.activityItem);//刷新活动详情界面
            if(this.$utils.isEmpty(type) || type !== 'scanBeforeSaveCost'){
                this.$nav.back();
            }
        },
        /**
         * 查询活动下费用信息
         * 1、申请类
         * 2、实际类
         * */
        async queryActivityProdAndCostList(type) {
            /**
             * 如果过操作场景为：
             * A用户新建市场活动M1保存之后维护费用信息但没保存，跳出小程序数据有缓存的情况下，
             * B用户更新了市场活动M1的费用信息且保存到数据库中。
             * 当A用户返回到小程序，有缓存的情况下，费用信息以数据库中查询出来的为先，给用户A提示框说明费用信息有被编辑过在B编辑的基础上按需更新。
             * @type {string}
             */
                //查询申请费用
            let url = "";
            let params = {filtersRaw: [{id: "actId", property: "actId", value: this.activityItem.id, operator: "="}]};
            if (this.scene === 'apply') {
                url = 'action/link/actMaterial/queryAndGroupData'
            } else if (this.scene === 'actual') {
                url = 'action/link/actualFee/queryAndGroupData/edit';
                if(type === 'scanCompleteInitCostList'){
                    params = {filtersRaw: [{id: "actId", property: "actId", value: this.activityItem.id, operator: "="}],queryType : 'actualQtyUpdate'}
                }
            }
            if (this.pageForm === 'case') {
                url = 'action/link/actMaterial/queryAndGroupData'
            }
            const data = await this.$http.post(url, params);
            if (!this.$utils.isEmpty(data.rows)) {
                this.prodAndCostList = {...data.rows};
                if (!this.$utils.isEmpty(this.prodAndCostList.Money)) {
                    this.$set(this, 'cashShow', true);
                } else {
                    this.prodAndCostList.Money = this.pageParam.costListData.cashPayList || [];
                }
                if (!this.$utils.isEmpty(this.prodAndCostList.Product)) {
                    this.$set(this, 'prodShow', true)
                } else {
                    this.prodAndCostList.Product = this.pageParam.costListData.prodPayList || [];
                }
            } else {
                this.prodAndCostList.Money = this.pageParam.costListData.cashPayList || [];
                this.prodAndCostList.Product = this.pageParam.costListData.prodPayList || [];
            }
        },
        /*
        * 删除申请费用的兑付方式-现金
        * @auther songyanrong
        * @date 2020-10-16
        * */
        async deleteCaseFeePayFun(feePayCode, costId) {
            const caseInfoList = this.cashPayList.filter((item) => item.feePayCode === feePayCode && item.costId === costId);
            //是否有已经保存到数据库的费用信息
            let exists = [];
            if (!this.$utils.isEmpty(caseInfoList)) {
                if (!this.$utils.isEmpty(caseInfoList[0].dataList)) {
                    exists = caseInfoList[0].dataList.filter((item) => !this.$utils.isEmpty(item.id));
                }
                if (!this.$utils.isEmpty(caseInfoList[0].dataList) && !this.$utils.isEmpty(exists)) {
                    const del = {
                        actId: this.activityItem.id,
                        feePayCode: feePayCode,
                        costId: costId
                    };
                    await this.$http.post('action/link/actMaterial/deleteByFeePayCode', del);
                }
            }
            if (!this.$utils.isEmpty(costId)) {
                this.prodAndCostList.Money = this.prodAndCostList.Money.filter((item) => item.costId !== costId);
            } else {
                this.prodAndCostList.Money = this.prodAndCostList.Money.filter((item) => item.feePayCode !== feePayCode);
            }
            this.$bus.$emit("MoneyRrefresh", this.prodAndCostList.Money);
        },
        /*
        * 删除申请费用的兑付方式-产品
        * @auther songyanrong
        * @date 2020-10-16
        * */
        async deleteProdFeePayFun(feePayCode, costId) {
            const prodInfoList = this.prodPayList.filter((item) => item.feePayCode === feePayCode && item.costId === costId);
            //是否有已经保存到数据库的产品信息
            let exists = [];
            if (!this.$utils.isEmpty(prodInfoList)) {
                if (!this.$utils.isEmpty(prodInfoList[0].dataList)) {
                    exists = prodInfoList[0].dataList.filter((item) => !this.$utils.isEmpty(item.id));
                }
                if (!this.$utils.isEmpty(prodInfoList[0].dataList) && !this.$utils.isEmpty(exists)) {
                    const del = {
                        actId: this.activityItem.id,
                        feePayCode: feePayCode,
                        costId: costId,
                    };
                    await this.$http.post('action/link/actMaterial/deleteByFeePayCode', del);
                    this.$bus.$emit("initCodeScanRecordList");//重新查询扫码记录
                }
            }
            if (!this.$utils.isEmpty(costId)) {
                this.prodAndCostList.Product = this.prodAndCostList.Product.filter((item) => item.costId !== costId);
            } else {
                this.prodAndCostList.Product = this.prodAndCostList.Product.filter((item) => item.feePayCode !== feePayCode);
            }
            this.$bus.$emit("ProductRrefresh", this.prodAndCostList.Product);
        },
    }
}
</script>

<style lang="scss">
.market-activity-cost-list-new-page {
    .base {
        border-radius: 24px;
        margin: 24px;
        overflow: hidden;
    }
}
</style>
