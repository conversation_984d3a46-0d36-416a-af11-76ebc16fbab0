<template>
   <link-page>
       <view class="pinjianjiu-scan-code-list-page">
           <link-auto-list :option="autoList">
               <view slot="top" class="head-top">
                   <link-search-input v-model="searchVal" :placeholder="'请输入产品编码/产品名称'"/>
               </view>
               <template slot-scope="{data,index}">
                   <link-swipe-action :key="index">
                       <item :key="index" :data="data" :arrow="false" class="code-record-list" @tap="toDetails(data)">
                           <view slot="note">
                               <view class="list-top">
                                   <view class="code">产品编码： {{data.productCode}}</view>
                                   <view class="del" v-if="deleteScanFlag && pageParam.editFlag" @tap.stop="deleteItem(data)">
                                       <link-icon  icon="icon-close-circle" class="del-icon"></link-icon>
                                   </view>
                               </view>
                               <view class="list-middle">
                                   {{data.productName}}
                               </view>
                               <view class="list-bottom clearfix">
                                   <view class="list-bottom-item">
                                       扫码时间：{{data.scanTime.split(' ')[0]}}
                                   </view>
                                   <view class="list-bottom-item bottom-r">
                                       <text>扫码人： {{data.scanner}}</text>
                                       <view class="icon">
                                           <link-icon icon="icon-right" class="right-icon"></link-icon>
                                       </view>
                                   </view>
                               </view>
                               <view class="qr-code">
                                   <view class="qr-code-item">
                                       <view class="lebal">盖外码:</view>
                                       <view class="val">{{data.qrCodeOut && data.qrCodeOut.split('/').pop().substr(-8)}}</view>
                                   </view>
                                   <view class="qr-code-item"  v-if="data.qrCodeIn && data.scanSubType !== 'GiftScan'">
                                       <view class="lebal">盖内码:</view>
                                       <view class="val">{{data.qrCodeIn.split('/').pop().substr(-8)}}</view>
                                   </view>
                               </view>
                           </view>
                       </item>
                   </link-swipe-action>
               </template>
           </link-auto-list>
       </view>
   </link-page>
</template>

<script>
import {deleteScanCodeCheck} from "./deleteScanCodeCheck";

export default {
    name: "pinjianjiu-scan-code-list-page",
    data() {
        const autoList = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/actScanRecord/queryByExamplePage'
            },
            param: {
                filtersRaw: [
                    {
                        "id": "scanSubType",
                        "property": "scanSubType",
                        "value": this.pageParam.type
                    }
                ],
                onlyCountFlag: false,
                oauth: 'ALL',
                sort: 'created',
                order: 'desc',
                rows: 5,
                actId: this.pageParam.actId,
                scene: this.pageParam.scene
            },
            sortOptions: null
        });

        //Y,有效   N,无效
        const imgList = {
            Y:'effective',
            N:'failure'
        }
        //正常入库、正常开瓶、正常转赠、正常出库
        const normalScanCode = ['Normalstorage', 'Normalbottled', 'Normalgifted', 'NormalDelivery']
        const detailTitle = {
            InScan: '入库扫码详情',
            OutScan: '出库扫码详情',
            OpenScan: '开瓶扫码详情',
            GiftScan: '赠送扫码详情'
        }
        const deleteUrl = {
            InScan: 'action/link/actScanRecord/deleteInStockScanRecord',
            OutScan: 'action/link/actScanRecord/deleteOutStockScanRecord',
            OpenScan: 'action/link/actScanRecord/bottledDeleteRecord',
            GiftScan: 'action/link/actScanRecord/giftDeleteRecord'
        };
        this.$taro.setNavigationBarTitle({title: this.pageParam.title});
        return {
            autoList,
            imgList,
            searchVal: '',
            deleteUrl,                //不同状态的二维码对应不同的删除地址
            normalScanCode,           //正常类型的扫码记录 扫码子类型 值列表类型: SCAN_SUB_TYPE
            detailTitle,               //详情页面title
            deleteScanFlag: false,//是否可以侧滑删除扫码记录
            oneClick: false,          //点击事件是否在执行中，控制删除对应的逻辑只执行一次
        }
    },
    async created() {
        this.deleteScanFlag = await deleteScanCodeCheck.checkFlag(this.pageParam.actId);
    },
    methods: {
        async deleteItem(data) {
            this.$dialog({
                title: '提示',
                content: '确认是否删除该条扫码记录?',
                cancelButton: true,
                onConfirm: () => {
                    if(this.oneClick) return
                    this.oneClick = true
                    this.confirmDelete(data)
                },
            })
        },
        async confirmDelete(data1) {
            const url = this.deleteUrl[data1.scanSubType]
            const params =  {
                id: data1.id,
            }
            try {
                const data = await this.$http.post(url, params);
                if(data.success) {
                    this.$message.success('删除数据成功');
                    //执行反馈阶段=费用实际。删除赠送 、开瓶扫码记录需要更新产品的实际数量
                    if(this.pageParam.scene === 'actual'){
                        if(data1.scanSubType === 'OpenScan' || data1.scanSubType === 'GiftScan'){
                            this.$bus.$emit("scanCompleteInitCostList");
                        }
                    }
                    //重新查询扫码数据
                    this.$bus.$emit("initCodeScanRecordList");
                    this.autoList.methods.reload();
                    return
                }
                this.$showError('删除数据失败');
            }catch (e) {
                this.$showError(`删除数据异常:${e.result}`);
            }finally {
                this.oneClick = false
            }
        },
        toDetails(data) {
            const params =  {
                id: data.id,
                title: this.detailTitle[data.scanSubType]
            }
            this.$nav.push('/pages/lj-market-activity/market-activity/tasting-wine-scan-code-detail-page', params)
        }
    },
    watch: {
        searchVal(newVal, oldVal) {
            if (newVal !== oldVal) {
                const searchObj = {
                    id: "searchValue",
                    operator: "or like",
                    property: "[productCode, productName]",
                    value: newVal
                };
                this.autoList.option.param['filtersRaw'] = this.autoList.option.param['filtersRaw'].filter((item) => item.property !== "[productCode, productName]");
                if(!this.$utils.isEmpty(newVal)){
                    this.autoList.option.param['filtersRaw'].push(searchObj);
                }
                this.autoList.methods.reload()
            }
        }
    }
}
</script>

<style lang="scss">
.pinjianjiu-scan-code-list-page{
    letter-spacing: 0;
    overflow-x: hidden;

    .head-top{
        background-color: rgb(242,242,242);
    }

    .main-title{
        font-family: PingFangSC-Regular;
        font-size: 28px;
        color: #262626;
        padding: 32px 0px;
        text-align: center;
        border-bottom: 2px solid #e5e5e5;
        background-color: white;
    }

    .code-record-list{
        //padding-right: 0px;
        margin: 0 auto;

        .list-top{
            font-size: 28px;
            display: flex;
            justify-content: space-between;
            .code{
                height: 40px;
                margin: 10px 0;
            }
            .del{
                flex: 1;
                display: flex;
                justify-content: flex-end;
                align-items: center;
                .del-icon{
                    font-size: 40px;
                    padding: 10px 0 10px 10px;
                }
            }
        }

        .list-middle{
            font-family: PingFangSC-Medium;
            font-size: 30px;
            color: #262626;
            line-height: 45px;
            margin: 10px 0;
        }

        .list-bottom{
            display: flex;
            padding: 10px 0 20px 0;
            .list-bottom-item{
                flex: 1;
                font-size: 28px;
            }
            .bottom-r {
                display: flex;
                justify-content: space-between;
                .icon{
                    width: 20%;
                    display: flex;
                    justify-content: flex-end;
                    .right-icon {
                        font-size: 35px;
                    }
                }
            }
        }

        .qr-code{
            display: flex;
            font-size: 30px;
            .qr-code-item{
                display: flex;
                margin-right: 40px;
                .lebal{
                    color: black;
                    margin-right: 8px;
                }
                .val {
                    color: #2F69F8;
                }
            }
        }

        .clearfix:after {
            content: "";
            display: block;
            height: 0;
            clear: both;
            visibility: hidden;
        }
    }
}
</style>
