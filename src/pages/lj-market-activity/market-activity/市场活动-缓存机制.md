# 市场活动
```
创建时间：2022/01/10 20:15
创建人：  宋燕荣
模块路径：src/pages/lj-market-activity/market-activity
```
### 缓存机制
```
 A、值列表缓存
 B、活动数据和费用数据缓存
 C、配置模版数据缓存
```

#### 值列表缓存
即查即用，登录不查询所有，当前页面用到哪几个值列表查哪几个，指定查询。使用Vue.observable进行状态管理。

#### 活动数据和费用数据缓存
PageCacheManager

操作new-activity-basic-page和perform-case-cost-list-new-page页面时
如果编辑过程中突然跳出应用会缓存当前页面内容
下次回到首页，会检测当前职位存在缓存数据提示用户是否继续操作
可以选择继续和取消，选择取消则清除指定的缓存数据。

#### 配置模版数据缓存
```
1、是否缓存：所有模版请求之后数据结果都使用Taro.setStorageSync(KEY, tmplObject)缓存。
2、缓存更新机制：PC端调整模版之后点击清除模版缓存后，用户调用指定模版时会对比本地缓存模版的时间戳和实际模版的时间戳，如果不一致那么重新发起请求获取最新的模版配置内容。
如果一致那么直接Taro.getStorageSync(KEY)获取模版内容。
3、清除缓存方式：小程序-个人中心-清除缓存
```
------ 市场活动-缓存机制-内容结束 ------
