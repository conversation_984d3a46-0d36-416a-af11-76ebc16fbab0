<template>
    <link-page class="edit-prod-page">
        <view class="view">
            <view class="item-header">
                <view style="width: 50%;float: left">{{title}}</view>
                <view style="float: left;text-align: right;width: 40%;padding-right: 12px;color: #2F69F8;"
                      @tap="pickProduction">添加
                </view>
            </view>
            <view class="product-list">
                <link-swipe-action v-for="(item,index) in productionPinList" :key="index">
                    <link-swipe-option slot="option"
                                       v-if="!((parentData.status === 'ActualAmount' || parentData.status === 'MaterialSup'))"
                                       @tap="deleteRow(item,index)">删除
                    </link-swipe-option>
                    <item :arrow="false">
                        <view class="product-list-item">
                            <view class="product-list-item__left">
                                {{item.prodCode}}-{{item.prodName}}
                            </view>
                            <view class="product-list-item__center">
                                <view class="row row-1">
                                    {{priceShowFlag?'标价':''}}
                                    <view class="num" v-if="priceShowFlag">{{item.basePrice | numCny}}，</view>
                                    申请
                                    <view class="num">{{item.applyQty}}</view>
                                    <view v-if="item.itemType === 'actual'">
                                        {{item.applyProdUnit|lov('FEE_PROD_UNIT')}}
                                    </view>
                                    <view v-if="item.itemType !== 'actual' || $utils.isEmpty(item.applyProdUnit)">
                                        {{item.prodUnit|lov('FEE_PROD_UNIT')}}
                                    </view>
                                </view>
                                <view class="row row-2">
                                    {{priceShowFlag?'费用核销价格':''}}
                                    <link-icon icon="mp-info-lite" status="info" @tap="showTips()"/>
                                </view>
                                <view  class="row" >
                                    <view class="num" v-if="priceShowFlag">{{item.netPrice|numCny}}，</view>
                                    <view  v-if="item.itemType === 'actual'">实际<view class="num" style="display: inline;">{{item.qty}}</view>
                                    {{item.prodUnit|lov('FEE_PROD_UNIT')}}
                                </view>
                                </view>
                                <view class="row row-3" v-if="priceShowFlag">
                                    <view class="count-price">小计
                                        <view class="num" v-if="item.itemType ===  'apply'">
                                            {{$utils.numberMul(item.netPrice,item.applyQty) |cny}}
                                        </view>
                                        <view class="num" v-if="item.itemType !== 'apply' && (item.itemType === 'actual' || $utils.isEmpty(item.applyProdUnit))">
                                            {{$utils.numberMul(item.netPrice,item.qty)
                                            |cny}}
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view class="product-list-item__right">
                                <link-icon icon="icon-edit" @tap="editProdItem(item)"/>
                            </view>
                        </view>
                    </item>
                </link-swipe-action>
            </view>
        </view>
        <!--调整产品弹框-->
        <link-dialog ref="prodBottom"
                     :noPadding="true"
                     v-model="productDialogFlag"
                     position="bottom"
                     borderRadius="32rpx 32rpx 0 0">
            <view class="model-title">
                <view class="title">编辑</view>
                <view class="iconfont icon-close" @tap="closeDialog"></view>
            </view>
            <link-form>
                <view class="product-name">
                    <view class="name">{{adjustedProductItem.prodName}}</view>
                </view>
                <link-form-item label="单位">
                    <link-lov type="FEE_PROD_UNIT" v-model="adjustedProductItem.prodUnit"></link-lov>
                </link-form-item>
                <link-form-item label="标价" v-if="adjustedProductItem.prodUnit === 'Ping' && priceShowFlag">
                    <link-number-keyboard :precision="2" v-model="adjustedProductItem.saleMiniPrice" readonly></link-number-keyboard>
                </link-form-item>
                <link-form-item label="标价" v-if="adjustedProductItem.prodUnit === 'Xiang' && priceShowFlag">
                    <link-number-keyboard :precision="2" v-model="adjustedProductItem.salePrice" readonly></link-number-keyboard>
                </link-form-item>
                <link-form-item label="费用核销价格" arrow v-if="priceShowFlag">
<!--                    <view style="color: #3b4144" @tap="openProdPinNumberKeyboard">{{adjustedProductItem.netPrice}}</view>-->
                    <link-input v-model=adjustedProductItem.netPrice type="digit"/>
                </link-form-item>
                <link-form-item label="预计数量" v-if="adjustedProductItem.itemType === 'apply'">
                    <link-number v-model="adjustedProductItem.applyQty" :min="1"/>
                </link-form-item>
                <link-form-item label="实际数量" v-if="adjustedProductItem.itemType === 'actual'">
                    <link-number v-model="adjustedProductItem.qty" :min="0"/>
                </link-form-item>
            </link-form>
            <view class="blank"></view>
            <link-sticky class="bottom-btn">
                <link-button class="sure-btn" size="normal" @tap="saveAdjustedProduct">确定</link-button>
            </link-sticky>
        </link-dialog>
        <link-sticky>
            <link-button block @tap="save" :disabled="disabledFlag">保存</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    import {FilterService} from "link-taro-component";
    import {ROW_STATUS} from "../../../utils/constant";
    import Taro from "@tarojs/taro";

    export default {
        name: "edit-prod-page",
        data() {
            const pageSource = this.pageParam.pageSource;//页面来源 - 执行反馈环节 executiveFeedback、其他 other、preview 预览而来 view 查看而来
            const parentId = this.pageParam.parentData.id;
            const parentData = this.pageParam.parentData;
            const parentType = this.pageParam.parentType;
            const title = this.pageParam.title;
            const applyNumFlag = this.pageParam.applyNumFlag;
            const estimatedNumFlag = this.pageParam.estimatedNumFlag;
            const actualNumFlag = this.pageParam.actualNumFlag;
            const orderType = this.pageParam.orderType;//订单类型默认预订单 销售订单 SalesOrder【实际动销】  estimateorder 预订单【预计动销】
            const actualFlag = false;
            const editNetPrice = 0;
            const userInfo = Taro.getStorageSync('token').result;
            return {
                // 费用价格是否展示标识
                priceShowFlag: false,
                userInfo,
                editNetPrice,
                disabledFlag: false,
                actualFlag,
                parentData,
                pageSource,
                productDialogFlag: false,
                adjustedProductItem: {},//调整的产品信息
                estimatedNumFlag,
                applyNumFlag,
                actualNumFlag,
                parentId,
                parentType,
                title,
                productionPinList: [],
                haveProductionPinListFlag: false,//是否存在动销数据
                orderType,
                //产品选择
                productionPinOption: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: 'action/link/product/queryUnSelectByOrgIdPage'
                    },
                    sortOptions: null,
                    searchFields: ['prodName','prodCode'],
                    param: {
                        //source:实际费用添加产品查询（actFee）、申请费用添加查询（applyFee）、动销添加产品（actSale）
                        filtersRaw: [
                            {"id": "attr1", "property": "attr1", "value": parentId},
                            {"id": "source", "property": "source", "value": "actSale"},
                        ],
                        attr3:parentData.salesCityId,
                        attr4: orderType,
                        accntId:parentData.actExecutivesId
                    },
                    hooks: {
                        beforeLoad(option) {
                            delete option.param.order;
                            delete option.param.sort;
                        },
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key = {index} data = {data} arrow = "false" >
                            <link-checkbox val = {data.id} toggleOnClickItem slot = "thumb"/>
                            <view style = "display: flex;flex: 1;flex-direction: column;justify-content: space-between;align-items: flex-start;overflow: hidden;" >
                            <view style = "margin:12px;">
                            <view style = "background: #A6B4C7;border-radius: 4px;line-height: 20px;" >
                            <view style = "font-size: 14px;color: #FFFFFF;letter-spacing: 0;line-height: 20px;padding: 1px 4px;" > {data.prodCode}
                            </view>
                            </view>
                            </view>
                            <view style = "margin-left:12px;width:100%">
                            <view style = "font-family: PingFangSC-Semibold;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 16px;" > {data.prodName}
                            </view>
                            </view>
                            <view
                        style = "margin:12px 0 0 12px;width:100%" >
                            <view
                        style = "font-family: PingFangSC-Regular;font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;width: 50%;float: left;" >

                        {
                         this.priceShowFlag ?`箱价:`+FilterService.numCny(data.salePrice):''
                        }
                    </view>
                        <view
                        style = "font-family: PingFangSC-Regular;font-size: 14px;color: #000000;letter-spacing: 0;text-align: left;line-height: 14px;" >

                        {
                            this.priceShowFlag ?`瓶价:` + FilterService.numCny(data.saleMiniPrice):''
                        }
                    </view>
                        </view>
                        </view>
                        </item>
                    )
                    },
                }),
                actualFiltersRaw: [{"id": "itemType", "property": "itemType", "value": "actual"}],
                applyFiltersRaw: [{"id": "itemType", "property": "itemType", "value": "apply"}],
            }
        },
        async created() {
            await this.queryProductionPinList();
            this.priceShowFlag = await this.$utils.getPriceDesensitize(this.userInfo);
        },
        methods: {
            showTips() {
                this.$message.primary('费用核销价格由业务代表填写，为受益客户需占用的产品单价')
            },
            async openProdPinNumberKeyboard() {
                this.editNetPrice = await this.$numberKeyboard({initValue: this.editNetPrice,precision: 2});
                this.adjustedProductItem.netPrice = this.editNetPrice;
                this.editNetPrice = 0;
            },
            /**
             * 添加产品
             * <AUTHOR>
             * @date 2020-08-12
             * */
            async pickProduction() {
                const list = await this.$object(this.productionPinOption, {
                    pageTitle: "产品",
                    multiple: true,
                });

                let itemType = "";
                if (this.actualFlag) {
                    itemType = "actual"; //实际动销
                } else {
                    itemType = "apply"; //预计动销
                }
                list.forEach((item) => {
                    if (this.$utils.isEmpty(this.productionPinList)) {
                        this.productionPinList.push({
                            prodCode: item.prodCode,
                            prodName: item.prodName,
                            qty: '1',//实际数量
                            applyQty: '1',//预计数量
                            prodUnit: 'Ping',
                            basePrice: FilterService.num(item.saleMiniPrice),//标准单价
                            netPrice: FilterService.num(item.saleMiniPrice),//实际成交价
                            mcActId: this.parentId,
                            prodId: item.id,
                            row_status: "NEW",
                            itemType: itemType,
                            saleMiniPrice: FilterService.num(item.saleMiniPrice),//动销瓶价
                            salePrice: FilterService.num(item.salePrice),//动销箱价,
                            priceListId: item.priceListId,//价目表ID
                            applyProdUnit:'Ping',//实际动销时 展示预计动销单位使用
                        });
                    } else {
                        const prodExistFlag = this.productionPinList.filter(item1 => item1.prodId === item.id)[0]
                        if (this.$utils.isEmpty(prodExistFlag)) {
                            this.productionPinList.push({
                                prodCode: item.prodCode,
                                prodName: item.prodName,
                                qty: '1',//实际数量
                                applyQty: '1',//预计数量
                                prodUnit: 'Ping',
                                basePrice: FilterService.num(item.saleMiniPrice),//标准单价
                                netPrice: FilterService.num(item.saleMiniPrice),//实际成交价
                                mcActId: this.parentId,
                                prodId: item.id,
                                row_status: "NEW",
                                itemType: itemType,
                                saleMiniPrice: FilterService.num(item.saleMiniPrice),//动销瓶价
                                salePrice: FilterService.num(item.salePrice),//动销箱价,
                                priceListId: item.priceListId,//价目表ID
                                applyProdUnit:'Ping',//实际动销时 展示预计动销单位使用
                            });
                        } else {
                            if(this.orderType === 'SalesOrder'){
                                prodExistFlag.qty++;
                            } else {
                                prodExistFlag.applyQty++;
                            }

                        }
                    }
                });
            },
            /**
             * 保存
             * <AUTHOR>
             * @date 2020-08-17
             * */
            async save() {
                if (this.$utils.isEmpty(this.productionPinList)) {
                    this.$message.warn("请选择物资");
                    return false;
                }
                this.$utils.showLoading();
                this.disabledFlag = true;
                let row_status = "";
                if (this.haveProductionPinListFlag) {
                    row_status = ROW_STATUS.UPDATE;
                } else {
                    row_status = ROW_STATUS.NEW;
                }
                const insertData = {
                    "priceListId": this.productionPinList[0].priceListId,
                    "mcActId": this.parentId,
                    "row_status": row_status,
                    saleOrderItemList: this.productionPinList
                };
                await this.$http.post('action/link/saleorderitem/createActSaleProds', insertData,{
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError(response.result);
                        this.disabledFlag = false;
                    }
                });
                this.$utils.hideLoading();
                await this.queryProductionPinList();
                this.pageParam.callback();
                this.$nav.back();
            },
            /**
             * 查询列表
             * <AUTHOR>
             * @date 2020-08-12
             * */
            async queryProductionPinList() {
                this.$utils.showLoading();
                //1、有没有实际订单头 有实际订单头的话就查实际动销的产品数据，没有的话查申请
                //2、执行反馈时传attr1后台区分是否需要copy申请动销数据为实际动销
                const actualSaleOrderFiltersRaw = [{
                    "id": "mcActId",
                    "property": "mcActId",
                    "value": this.parentId
                }, {"id": "orderType", "property": "orderType", "value": "SalesOrder"}];
                let actualSaleOrderData;//实际动销订单头
                actualSaleOrderData = await this.$http.post('action/link/saleorder/queryByExamplePage', {
                    filtersRaw: actualSaleOrderFiltersRaw,
                });
                let actualData;//实际动销数据
                if (!this.$utils.isEmpty(actualSaleOrderData.rows)) {
                    actualData = await this.$http.post('action/link/saleorderitem/queryByExamplePage', {
                        filtersRaw: [{"id": "headId", "property": "headId", "value": actualSaleOrderData.rows[0].id}],
                    });
                    this.productionPinList = actualData.rows;
                    this.actualFlag = true;
                } else {
                    this.actualFlag = false;
                    await this.setDataFun();
                }
                this.$utils.hideLoading();
            },
            async setDataFun() {
                let actualData;//实际动销数据
                if (this.pageSource === 'executiveFeedback') {
                    actualData = await this.$http.post('action/link/saleorderitem/queryActSaleProds', {
                        mcActId: this.parentId,
                        filtersRaw: this.actualFiltersRaw,
                        attr1: 'feedback'
                    });
                    this.actualFlag = true;
                } else {
                    actualData = await this.$http.post('action/link/saleorderitem/queryActSaleProds', {
                        mcActId: this.parentId,
                        filtersRaw: this.actualFiltersRaw,
                    });
                    this.actualFlag = false;
                }
                if (!this.$utils.isEmpty(actualData.rows)) {
                    this.productionPinList = actualData.rows;
                    this.actualFlag = true;
                } else {
                    const applyData = await this.$http.post('action/link/saleorderitem/queryActSaleProds', {
                        mcActId: this.parentId,
                        filtersRaw: this.applyFiltersRaw,
                    });
                    this.productionPinList = applyData.rows;
                    this.actualFlag = false;
                }
                if (!this.$utils.isEmpty(this.productionPinList)) {
                    this.productionPinshow = true;
                }
            },
            /**
             * 长按删除一行
             * <AUTHOR>
             * @date 2020-08-12
             * */
            deleteRow(input, index) {
                if ((this.parentData.status === 'ActualAmount'
                    || this.parentData.status === 'MaterialSup')) {
                    return false;
                }
                const that = this;
                this.$taro.showModal({
                    title: '提示',
                    content: '是否要删除当前行信息',
                    success: async (res) => {
                        if (res.confirm) {
                            if (!that.$utils.isEmpty(input.id) && input.row_status !== ROW_STATUS.NEW) {
                                let url = "action/link/saleorderitem/deleteById";
                                await this.$http.post(url, input);
                            }
                            this.productionPinList.splice(index, 1);
                            this.pageParam.callback();
                        } else if (res.cancel) {
                        }
                    }
                });
            },
            /**
             * 编辑产品信息调整实际价格和数量
             * <AUTHOR>
             * @date 2020-09-02
             * */
            editProdItem(item) {
                if(!this.$utils.isEmpty(item.saleMiniPrice)){
                    item.saleMiniPrice = FilterService.num(item.saleMiniPrice)
                }
                if(!this.$utils.isEmpty(item.salePrice)){
                    item.salePrice = FilterService.num(item.salePrice)
                }
                if(!this.$utils.isEmpty(item.netPrice)){
                    item.netPrice = FilterService.num(item.netPrice)
                }
                this.adjustedProductItem = item;
                this.$refs.prodBottom.show()
            },
            /**
             * 关闭底部弹窗
             * <AUTHOR>
             * @date 2020-09-02
             * @param param
             */
            closeDialog() {
                this.productDialogFlag = !this.productDialogFlag
            },
            /**
             * 保存编辑的产品信息
             * <AUTHOR>
             * @date 2020-09-02
             * */
            async saveAdjustedProduct() {
                if(this.$utils.isEmpty(this.adjustedProductItem.netPrice)){
                    this.$message.warn('实价不能为空，请重新输入');
                    return false;
                }
                const regexp = /^(([1-9][0-9]*)|(([0]\.\d{0,2}|[1-9][0-9]*\.\d{0,2})))$/;
                const regexp2 = /^[0-9]*$/
                const flag = regexp.test(this.adjustedProductItem.netPrice);
                const flag2 = regexp2.test(this.adjustedProductItem.netPrice);
                if(!(flag || flag2)) {
                    this.$message.warn(`实价为纯数字且不能超过2位小数，请检查。`);
                    return false;
                }
                if (this.adjustedProductItem.prodUnit === 'Ping') {
                    this.adjustedProductItem.basePrice = this.adjustedProductItem.saleMiniPrice;
                } else if (this.adjustedProductItem.prodUnit === 'Xiang') {
                    this.adjustedProductItem.basePrice = this.adjustedProductItem.salePrice;
                }
                //如果id不为空时 为更新数据
                if (!this.$utils.isEmpty(this.adjustedProductItem.id)) {
                    const updateDate = {
                        id: this.adjustedProductItem.id,
                        prodUnit: this.adjustedProductItem.prodUnit,
                        netPrice: this.adjustedProductItem.netPrice,
                        applyQty: this.adjustedProductItem.applyQty,
                        qty: this.adjustedProductItem.qty,
                        updateFields: "id,prodUnit,netPrice,applyQty,qty"
                    };
                    await this.$http.post('action/link/saleorderitem/update', updateDate);
                    this.$refs.prodBottom.hide();
                } else {
                    this.$refs.prodBottom.hide();
                }
            },
        }
    }
</script>

<style lang="scss">
    .edit-prod-page {
        .view {
            background: white;

            .item-header {
                height: 88px;
                width: 100%;
                padding-left: 32px;
                font-size: 28px;
                line-height: 88px;
                color: #262626;
                letter-spacing: 0;
                border-bottom: 2px solid #F2F2F2;
            }

            .product-list {
                font-size: 28px;
                color: #262626;
                letter-spacing: 0;
                line-height: 38px;

                .product-list-item {
                    display: flex;
                    align-items: center;
                    padding: 32px 24px;
                    justify-content: flex-start;
                    border-bottom: 2px solid #F2F2F2;

                    &__left {
                        text-align: left;
                        width: 40%;
                        color: #262626;
                    }

                    &__center {
                        width: 50%;
                        display: flex;
                        justify-content: center;
                        flex-direction: column;

                        .row {
                            width: 100%;
                            font-size: 24px;
                            color: #8C8C8C;
                            letter-spacing: 0;
                            line-height: 28px;
                            display: flex;
                            justify-content: flex-end;
                            margin-bottom: 24px;

                            .num {
                                font-size: 28px;
                                color: #000000;
                                letter-spacing: 0;
                                line-height: 28px;
                                padding-left: 4px;
                                padding-right: 4px;
                            }
                        }

                        .row-3 {
                            margin-bottom: 0;

                            .status {
                                line-height: 24px;
                                margin-right: 10px;
                            }

                            .success {
                                color: #2EB3C2;
                            }

                            .fail {
                                color: #FF5A5A;
                            }

                            .count-price {
                                display: flex;

                                .num {
                                    margin-left: 10px;
                                }
                            }
                        }
                    }

                    &__right {
                        text-align: right;
                        width: 10%;
                        color: #2F69F8;
                    }
                }
            }

            .table-v {
                padding-bottom: 40px;
            }

            .table {
                display: table;
                border-collapse: collapse;
                border: 1px solid #ccc;
                width: 100%;
            }

            .table-caption {
                display: table-caption;
                margin: 0;
                padding: 0;
                font-size: 16px;
            }

            .table-column-group {
                display: table-column-group;
            }

            .table-column-16 {
                display: table-column;
                width: 320px;
            }

            .table-column-8 {
                display: table-column;
                width: 160px;
            }

            .table-row-group {
                display: table-row-group;
            }

            .table-row {
                display: table-row;
                height: 80px;
                line-height: 80px

            }

            .table-row-group .table-row:hover, .table-footer-group .table-row:hover {
                background: #f6f6f6;
            }

            .table-cell {
                display: table-cell;
                padding: 0 5px;
                border: 1px solid rgba(47, 105, 248, 0.10);
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #262626;
                letter-spacing: 0;
                line-height: 40px;
                vertical-align: middle;
                text-align: center;
            }

            .table-cell-title {
                display: table-cell;
                padding: 0 5px;
                border: 1px solid rgba(47, 105, 248, 0.10);
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
                text-align: center;
                line-height: 28px;
                vertical-align: middle;
            }

            .table-header-group {
                display: table-header-group;
                background: rgba(47, 105, 248, 0.07);
                font-weight: bold;
            }

            .table-footer-group {
                display: table-footer-group;
            }
        }
    }
</style>
