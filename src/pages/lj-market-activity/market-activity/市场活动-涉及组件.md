# 市场活动-组件
```
创建时间：2022/01/10 20:15
创建人：  宋燕荣
模块路径：src/pages/lj-market-activity/market-activity
```
#### 涉及组件 18个
* 组件路径：都在src/pages/lj-market-activity/market-activity/components文件夹内

#### 组件基础信息如下
|  序号 |  组件名称  |  含义  |  作用  |  功能  |  是否可操作  |  具体操作  |  可否复用  |  是否共用  |  使用场景 scena  |  是否跳转详情页  |  详情页面路径 |  是否查看更多  | 更多界面路径 |
| :--- | :------ | :------ | :------ | :------ | :------ | :------ | :------ | :------ | :------ | :------ |:------ | :------ | :------ |
|1| approval-records     | 审批记录 |   查看展示活动审批记录和操作记录    | 仅查看数据 | 否 | 无 | 可复用 | 否 | 活动详情界面展示审批记录 | 否 | 无 | 否 | 无 |
|2| audit        |   稽核记录   |   查看展示活动的稽核记录   | 仅查看数据 | 否 | 无 | 可复用 | 是 |  见下面使用场景 scena | 是 | pages/lj-market-activity/market-activity/audit-item-page.vue | 否 | 无 |
|3| basic-info | 活动基本信息 |  展示活动基本信息，提供编辑基本信息入口方法  | 展示、提供编辑入口 | 是 | 跳转去编辑界面 | 是 | 是 | 见下面使用场景 scena | 否 | 无 | 否 | 无 |
|4| cash-new | 费用-现金类 | 操作兑付方式-现金类数据 | 新增、更新、删除、查看 | 是 | 新增、更新、删除 | 是 | 是 | 见下面使用场景 scena | 否 | 无 | 否 | 无 |
|5| consumers | 消费者子对象 | 查询展示活动关联的消费者信息 | 选择新增、侧滑删除、查询列表、查看更多、查询详情 | 是 | 新增、删除 | 是 | 是 | 见下面使用场景 scena |  是 | '/pages/lj-consumers/account/account-item-page' | 是 | '/pages/lj-market-activity/market-activity/consumer-list-page' |
|6| cost-new | 费用申请 | 展示、编辑活动申请费用(现金类、产品类) | 新增、更新、删除、展示 | 是 | 新增费用、更新费用（小计、产品的数量和价格）、删除某条费用明细、以费用兑付方式整体删除费用 | 是 | 是 | 见下面使用场景 scena | 否 | 否 | 否 | 无 |
|7| cover-code | 扫码记录 | 展示、删除 | 展示、删除 | 是 | 删除 | 是 | 是 | 见下面使用场景 scena | 否 | 否 | 否 | 否 |
|8| design-interaction-type | 互动配置操作整合组件 | 整合<app-wheel-camp-card>、<app-sales>、<app-sign-in>组件供数据维护展示 | 维护展示互动配置信息 | 是 | 具体查看src/pages/lj-market-activity/market-activity/components/design-interaction-type.vue | 是 | 否 | 见下面使用场景 scena | 否 | 否 | 否 | 无 |
|9| execution-feedback | 执行反馈组件 | 展示执行反馈环节数据 | 展示图片、实际费用、扫码记录、盖内码数据 | 否 | 无 | 是 | 是 | 见下面使用场景 scena | 否 | 否 | 否 | 无 |
|10| insiders | 内部人员 | 查询展示活动关联的内部人员信息 | 选择新增、侧滑删除、展示、查看更多 | 是 | 新增、删除 |  是 | 是 | 见下面使用场景 scena | 否 | 否 | 否 | 无 |
|11| interactive-config | 活动的互动配置展示组件 | 展示当前活动的互动配置 | 纯展示 | 是 | 提供一个编辑入口按钮 |  是 | 是 | 见下面使用场景 scena | 否 | 否 | 否 | 无 |
|12| participation | 活动分析 | 查询展示活动分析数据 | 纯展示 | 否 | 无 | 是 | 否 | 见下面使用场景 scena | 否 | 否 | 否 | 无 |
|13| prod-new | 费用-产品类 | 操作兑付方式-产品类数据 | 新增、更新、删除、查看 | 是 | 新增、更新、删除 | 是 | 是 | 见下面使用场景 scena | 否 | 无 | 否 | 无 |
|14| production-pin | 活动动销 | 展示以及维护更新活动动销数据 | 编辑入口、更新、查询 | 是 | 编辑入口、更新单条 | 是 | 是 | 见下面使用场景 scena | 否 | 无 | 否 | 无 |
|15| progress-new | 报销进度 | 展示报销进度数据 | 仅展示 | 否 | 无 | 可复用 | 是 | 见下面使用场景 scena | 否 | 无 | 否 | 无 |
|16| tasting-wine-scan-code | 流程内-扫码功能组件 | 操作流程内出库、开瓶、转增扫码 | 扫码功能、关联费用功能 | 是 | 扫码操作、关联费用、生成扫码记录、更新产品二维码 | 是 | 否 | 见下面使用场景 scena | 否 | 否 | 否 | 无 |
|17| tasting-wine-scan-code-record | 流程内扫码记录展示 | 展示、删除扫码记录 | 查询展示、删除扫码数据 | 是 | 查看更多入口、删除扫码记录、查看列表、查看详情 | 是 | 是 | 见下面使用场景 scena | 是 | '/pages/lj-market-activity/outbound-code/outbound-record-list-details-page' | 是 | '/pages/lj-market-activity/market-activity/pinjianjiu-scan-code-list-page.vue' |
|18| terminal | 参与终端/经销商 | 查询展示活动关联的终端、经销商数据 | 选择新增、侧滑删除、查询列表、查看更多、查询详情 | 是 | 新增、删除 | 是 | 是 | 见下面使用场景 scena |  否 | 无 | 是 | '/pages/lj-market-activity/market-activity/terminal-list-page' |
#### 各组件参数以及含义

* 1、```approval-records```

| 参数 | 类型 | 默认值 | 含义 | 作用 |
| :------ | :------ | :------ | :------ | :------ |
|parentId|String|无|父ID（市场活动ID)| 筛选数据

* 2、```audit```

|  参数  |  类型  |  默认值  |  含义  |  作用  |
| :------ | :------ | :------ | :------ | :------ |
| parentId    |String| "" | 父ID（市场活动ID） | 筛选数据

*  3、```basic-info```

|  参数  |  类型  |  默认值  |  含义  |  作用  |
| :------ | :------ | :------ | :------ | :------ |
| activityItem |    Object | {}|市场活动数据对象 | 展示的数据对象
|subControlList|Array|无|基础信息配置的展示子组件|提供基础信息展示的字段信息|
|editSubControlList|Array|无|基础信息配置的编辑子组件|提供基础信息编辑时界面展示可供编辑的字段|
|pageSource|String|无|界面来源-区分哪个界面调用的当前组件，配合活动状态和审批状态控制是否可以编辑基础信息按钮权限|1、执行反馈环节 executiveFeedback。2、other 活动的其他信息(ps:这是一个页面)。 3、preview 活动预览界面而来。4、view 活动查看界面查看而来。 5、审批或小程序消息进去 为空。 6、activityAudit 活动稽核。具体控制建议看代码。|

* 4、```cash-new```

|  参数  |  类型  |  默认值  |  含义  |  作用  |
| :------ | :------ | :------ | :------ | :------ |
| cashShow    |Boolean| false | 现金类内容是否展示 | 显影控制
| radiusTopFlag    |Boolean| false | 样式flag | 控制view的border-radius
| parentData    |Object| {} | 市场活动数据对象 | 根据活动的状态和审批状态控制当前组件的按钮权限
|pageSource | String | 'other' | 界面来源-区分哪个界面调用的当前组件，配合活动状态和审批状态控制是否可以编辑信息按钮权限| 页面来源 -1、执行反馈环节 executiveFeedback。2、other 活动的其他信息(ps:这是一个页面)。3、preview 活动预览界面而来。4、view 活动查看界面查看而来。5、审批或小程序消息进去 为空。6、activityAudit 活动稽核
|cashItem | Object | 无 |兑付方式-现金类对象 | 数据展示对象
| messageScene | String | 无 | 场景，只有执行反馈环节传 'costApprovalMsg' 区分费用审批场景 | 参与界面按钮的控制
|scene | String | 无 |场景-实际费用 actual 、申请费用 apply 控制保存编辑时调用的接口|区分费用阶段，区分调用接口
|operateFlag|Boolean|false| 控制是否可以操作 cash-new 和 prod-new组件的删除、新建、以及删除兑付方式的操作|只允许在编辑界面删除兑付方式、删除明细 、新增明细。其他界面可以编辑小计 和 编辑某个产品信息
|provinceOperateFlag|Boolean|true|控制是否可以点击现金类小计 和 产品类的编辑笔|默认可以编辑，仅费用提交按钮的弹出的费用实际信息内容块不能编辑
|prodAndCostList|Object| |费用信息|主要用于费用实际关联执行案明细判断时

* 5、```consumers```

|  参数  |  类型  |  默认值  |  含义  |  作用  |
| :------ | :------ | :------ | :------ | :------ |
| haveMarginFlag    |Boolean| false | 是否为有边距类型 | 控制不同界面引用当前组件的样式调整
|parentId|String|""|父ID（市场活动ID）|作为限制条件查询、处理数据
| pageSource    |String| '' |页面来源 -1、执行反馈环节 executiveFeedback。2、other 活动的其他信息(ps:这是一个页面)。3、preview 活动预览界面而来。4、view 活动查看界面查看而来。5、审批或小程序消息进去 为空。6、activityAudit 活动稽核 | 控制按钮的操作权限
|parentData|Object|{}|父对象-活动对象|参与按钮权限控制以及作为限制条件查询数据
|operationFlag|Boolean|false|是否可以操作删除数据|approvalId为空时可操作

* 6、```cost-new```

|  参数  |  类型  |  默认值  |  含义  |  作用  |
| :------ | :------ | :------ | :------ | :------ |
|parentData|Object|{}|活动对象|1、用户查询活动下申请费用信息。2、将当前活动对象传递给其他引用组件。3、控制某些按钮
|scene|String|无|费用场景区分申请还是实际|实际 actual 、申请 apply、控制保存编辑产品信息时调用的接口
|pageSource|String|''|页面来源 -1、执行反馈环节 executiveFeedback。2、other 活动的其他信息(ps:这是一个页面)。3、preview 活动预览界面而来。4、view 活动查看界面查看而来。5、审批或小程序消息进去 为空。6、activityAudit 活动稽核|1、参与编辑按钮权限控制。2、将当前活动对象传递给其他引用组件。


* 7、```cover-code```

|  参数  |  类型  |  默认值  |  含义  |  作用  |
| :------ | :------ | :------ | :------ | :------ |
| title    |String | "扫码记录"|标题 | 展示
|radiusBottomFlag | Boolean | false|样式flag | 控制view的border-radius
|parentId|String|""|父ID(市场活动ID)|数据筛选
|parentData|Object|{}|父对象(市场活动对象)|通过活动的状态和审批状态控制删除按钮的操作权限
|operationFlag|Boolean|false|是否可以操作删除数据|执行反馈界面可以操作[活动详情点执行反馈按钮过去的界面和执行反馈模块列表进详情的界面]，执行反馈组件不可以

* 8、```design-interaction-type```

|  参数  |  类型  |  默认值  |  含义  |  作用  |
| :------ | :------ | :------ | :------ | :------ |
| arrNodes    |Array| 无 | 互动信息数组 | 供数据展示
|flag | Boolean | 无 | 当前是否无互动活动 | 控制“当前无互动活动,请继续下一步”显影
|activityItem|Object|无|活动对象|传递给当前组件使用的其他组件

* 9、```execution-feedback```

|  参数  |  类型  |  默认值  |  含义  |  作用  |
| :------ | :------ | :------ | :------ | :------ |
| pageSource    |String| '' |页面来源 -1、执行反馈环节 executiveFeedback。2、other 活动的其他信息(ps:这是一个页面)。3、preview 活动预览界面而来。4、view 活动查看界面查看而来。5、审批或小程序消息进去 为空。6、activityAudit 活动稽核 | 传递给当前组件引用的'cash-new'和'prod-new'俩个组件，以及控制当前组件view的显影
|scene | String | 无 | 场景-实际 actual 、申请 apply | 区分费用阶段是申请还是实际，传递给'cash-new'和'prod-new'俩个组件使用
|parentData|Object|{}|活动对象|1、用户查询活动下实际费用信息。2、查询当前活动的业务场景有哪些场景图。3、根据活动ID获取执行反馈基本信息。4、将当前活动对象传递给其他引用组件

* 10、```insiders```

|  参数  |  类型  |  默认值  |  含义  |  作用  |
| :------ | :------ | :------ | :------ | :------ |
| haveMarginFlag    |Boolean | false|是否为有边距类型| 控制不同界面引用当前组件的样式调整
|parentId | String | "" | 父ID（市场活动ID）|作为限制条件供查询使用
|pageSource|String|''|页面来源 -1、执行反馈环节 executiveFeedback。2、other 活动的其他信息(ps:这是一个页面)。3、preview 活动预览界面而来。4、view 活动查看界面查看而来。5、审批或小程序消息进去 为空。6、activityAudit 活动稽核|参与按钮权限控制以及跳转查看全部界面时传递值
|parentData|Object|{}|父对象-活动对象|参与按钮权限控制以及作为限制条件查询数据
|operationFlag | Boolean|false |是否可以操作删除数据|控制侧滑删除按钮的权限

* 11、```interactive-config```

|  参数  |  类型  |  默认值  |  含义  |  作用  |
| :------ | :------ | :------ | :------ | :------ |
| interactiveConfigRequire    |Boolean | false|父ID（市场活动ID） | 作为限制条件供查询
|titleShow | Boolean| false |是否需要展示title | 控制界面
|pageSource|String|''|页面来源 -1、执行反馈环节 executiveFeedback。2、other 活动的其他信息(ps:这是一个页面)。3、preview 活动预览界面而来。4、view 活动查看界面查看而来。5、审批或小程序消息进去 为空。6、activityAudit 活动稽核|参与按钮权限控制以及跳转查看全部界面时传递值
|parentData|Object|{}|父对象-活动对象|参与按钮权限控制以及作为限制条件查询数据

* 12、```participation```

|  参数  |  类型  |  默认值  |  含义  |  作用  |
| :------ | :------ | :------ | :------ | :------ |
| mcActId    |String| '' | 父ID（市场活动ID） | 筛选数据
|updateAnalysisData|Boolean|false|watch字段|是否重新查询数据

* 13、```prod-new```

|  参数  |  类型  |  默认值  |  含义  |  作用  |
| :------ | :------ | :------ | :------ | :------ |
| prodShow    |Boolean| false | 显影控制 | 显影控制
| radiusTopFlag    |Boolean| false | 样式flag | 控制view的border-radius
|scene |String|无|场景|区分费用场景，实际费用 actual 、申请费用 apply 控制保存编辑时调用的接口
|pageSource|String|''|页面来源 -1、执行反馈环节 executiveFeedback。2、other 活动的其他信息(ps:这是一个页面)。3、preview 活动预览界面而来。4、view 活动查看界面查看而来。5、审批或小程序消息进去 为空。6、activityAudit 活动稽核|参与按钮权限控制以及分场景计算不同的字段加和
|parentData|Object|{}|父对象-活动对象|参与按钮权限控制以及作为限制条件查询数据
|prodItem|Object|{dataList: [],feePayType: '',feePayCode: '',costId: '',applyAmount: 0,actualTotal: 0,availableBalance: 0,}|产品对象|当前组件的核心数据对象，用于节目字段展示以及计算字段源数据
|messageScene|String|无|场景|只有执行反馈环节传 'costApprovalMsg' 区分费用审批场景
|operateFlag|Boolean|false|控制是否可以操作 cash-new 和 prod-new组件的删除、新建、以及删除兑付方式的操作|只允许在编辑界面删除兑付方式、删除明细 、新增明细。其他界面可以编辑小计 和 编辑某个产品信息
|provinceOperateFlag|Boolean|true|控制是否可以点击现金类小计 和 产品类的编辑笔|默认可以编辑，仅费用提交按钮的弹出的费用实际信息内容块不能编辑
|prodAndCostList|Object||费用信息（不区分兑付方式）|主要用于费用实际关联执行案明细判断时

* 14、```production-pin```

|  参数  |  类型  |  默认值  |  含义  |  作用  |
| :------ | :------ | :------ | :------ | :------ |
| haveMarginFlag    |Boolean| false | 是否为有边距类型 | 控制不同界面引用当前组件的样式调整
|title|String|预生产动销|标题|提示和展示用
|parentData | Object|{}|父对象-为活动对象|1、控制按钮权限。2、作为限制条件查询数据。3、传递给编辑物资详情界面。
|pageSource|String|''|界面来源-区分哪个界面调用的当前组件，配合活动状态和审批状态控制是否可以编辑基础信息按钮权限|1、执行反馈环节 executiveFeedback。2、other 活动的其他信息(ps:这是一个页面)。 3、preview 活动预览界面而来。4、view 活动查看界面查看而来。 5、审批或小程序消息进去 为空。 6、activityAudit 活动稽核。具体控制建议看代码。|
|messageScene|String|无|场景|场景 只有执行反馈环节传 'costApprovalMsg'

* 15、```progress-new```

|  参数  |  类型  |  默认值  |  含义  |  作用  |
| :------ | :------ | :------ | :------ | :------ |
| parentId    |String| "" | 父ID(市场活动ID) | 作为限制条件用于查询报销单列表

* 16、```tasting-wine-scan-code```

|  参数  |  类型  |  默认值  |  含义  |  作用  |
| :------ | :------ | :------ | :------ | :------ |
| scene    |String| 无 | 场景 | 实际费用 actual 、申请费用 apply 控制保存编辑时调用的接口
|activityItem | Object|{}|父对象-活动对象|作为限制条件查询数据、传递到扫码详情界面、控制按钮显影
|pageSource|String|无|界面来源|1、执行反馈环节 executiveFeedback。2、other 活动的其他信息(ps:这是一个页面)。 3、preview 活动预览界面而来。4、view 活动查看界面查看而来。 5、审批或小程序消息进去 为空。 6、activityAudit 活动稽核。具体控制建议看代码。|

* 17、```tasting-wine-scan-code-record```

|  参数  |  类型  |  默认值  |  含义  |  作用  |
| :------ | :------ | :------ | :------ | :------ |
| actId    |String| "" | 父ID(市场活动ID) | 作为限制条件用于查询扫码数据
|scene|String|无|场景|实际费用 actual 、申请费用 apply
|title|String|'扫码记录'|标题|展示
|type |String | '' |扫码类型|数据区分 OutScan 出库扫码，InScan 入库扫码，OpenScan 开瓶扫码，GiftScan 转赠扫码
|isexecutivefeedback|Boolean|false|是否执行反馈界面使用|控制样式

* 18、```terminal```

|  参数  |  类型  |  默认值  |  含义  |  作用  |
| :------ | :------ | :------ | :------ | :------ |
| haveMarginFlag    |Boolean| false | 是否为有边距类型 |控制样式
| parentId    |String| "" | 父ID(市场活动ID) | 作为限制条件用于查询数据
| pageSource    |String| '' |页面来源 -1、执行反馈环节 executiveFeedback。2、other 活动的其他信息(ps:这是一个页面)。3、preview 活动预览界面而来。4、view 活动查看界面查看而来。5、审批或小程序消息进去 为空。6、activityAudit 活动稽核 | 控制按钮的操作权限
|parentData|Object|{}|父对象-为活动对象|限制数据查询以及根据活动的状态和审判状态控制按钮的操作权限
|operationFlag|Boolean|false|是否可以操作删除数据。approvalId为空时可操作|是否可以操作删除数据。approvalId为空时可操作

#### 使用场景 scena
* 1、 ```approval-records```
```
1、活动详情展示审批历史子对象：src/pages/lj-market-activity/market-activity/market-activity-item-page.vue
```

* 2、 ```audit```
```
1、活动详情展示稽核记录子对象：src/pages/lj-market-activity/market-activity/market-activity-item-page.vue
2、活动稽核详情界面展示稽核记录「专供稽核人员使用」：src/pages/lj-market-activity/work-order/activity-approva-audit-page.vue
```
* 3、 ```basic-info```
```
1、活动详情展示基本信息：src/pages/lj-market-activity/market-activity/market-activity-item-page.vue
2、活动稽核详情界面展示活动基本信息：src/pages/lj-market-activity/work-order/activity-approva-audit-page.vue
3、活动执行反馈界面展示活动基本信息：src/pages/lj-market-activity/work-order/perform-link-page.vue
4、品鉴酒出库扫码详情界面展示关联的活动信息：src/pages/lj-market-activity/outbound-code/outbound-code-item-page.vue
5、品鉴酒入库扫码详情界面展示关联活动信息：src/pages/lj-market-activity/inbound-code/inbound-code-item-page.vue
```
* 4、 ```cash-new```
```
1、费用申请展示数据组件中展示现金类兑付方式数据：src/pages/lj-market-activity/market-activity/components/cost-new.vue
2、执行反馈展示数据组件中展示费用实际的现金类兑付方式数据：src/pages/lj-market-activity/market-activity/components/execution-feedback.vue
3、市场活动费用信息界面中-现金类兑付方式：src/pages/lj-market-activity/market-activity/market-activity-cost-list-new-page.vue
4、执行案活动费用信息界面中-现金类兑付方式：src/pages/lj-market-activity/perform-case/perform-case-cost-list-new-page.vue
5、执行反馈操作界面中-费用实际的现金类兑付方式数据：src/pages/lj-market-activity/work-order/perform-link-page.vue
```
* 5、 ```consumers```
```
1、活动稽核详情界面展示稽核记录「专供稽核人员使用」：src/pages/lj-market-activity/work-order/activity-approva-audit-page.vue
2、活动详情展示基本信息：src/pages/lj-market-activity/market-activity/market-activity-item-page.vue
3、活动其他信息界面：src/pages/lj-market-activity/market-activity/new-market-activity-other-info-page.vue
```
* 6、 ```cost-new```
```
1、活动稽核详情界面展示稽核记录「专供稽核人员使用」：src/pages/lj-market-activity/work-order/activity-approva-audit-page.vue
2、活动详情展示基本信息：src/pages/lj-market-activity/market-activity/market-activity-item-page.vue
```
* 7、```cover-code```
```
1、<execution-feedback>执行反馈信息组件
2、执行反馈详情界面：src/pages/lj-market-activity/work-order/perform-link-page.vue
```
* 8、```design-interaction-type```
```
1、互动配置界面：src/pages/lj-market-activity/market-activity/interactive-configuration-page.vue
```
* 9、```execution-feedback```
```
1、活动稽核详情界面展示稽核记录「专供稽核人员使用」：src/pages/lj-market-activity/work-order/activity-approva-audit-page.vue
2、活动详情展示基本信息：src/pages/lj-market-activity/market-activity/market-activity-item-page.vue
```
* 10、```insiders```
```
1、活动稽核详情界面展示稽核记录「专供稽核人员使用」：src/pages/lj-market-activity/work-order/activity-approva-audit-page.vue
2、活动详情展示基本信息：src/pages/lj-market-activity/market-activity/market-activity-item-page.vue
3、活动其他信息界面：src/pages/lj-market-activity/market-activity/new-market-activity-other-info-page.vue
4、执行反馈详情界面：src/pages/lj-market-activity/work-order/perform-link-page.vue
```
* 11、```interactive-config```
```
1、活动稽核详情界面展示稽核记录「专供稽核人员使用」：src/pages/lj-market-activity/work-order/activity-approva-audit-page.vue
```
* 12、```participation```
```
1、活动详情展示基本信息：src/pages/lj-market-activity/market-activity/market-activity-item-page.vue
```
* 13、```prod-new```
```
1、申请费用组件<cost-new> 使用：src/pages/lj-market-activity/market-activity/components/cost-new.vue
2、执行反馈组件<execution-feedback>使用：src/pages/lj-market-activity/market-activity/components/execution-feedback.vue
3、执行反馈详情界面-费用实际的产品类兑付方式：src/pages/lj-market-activity/work-order/perform-link-page.vue
4、市场活动费用信息界面中-产品类兑付方式：src/pages/lj-market-activity/market-activity/market-activity-cost-list-new-page.vue
5、执行案活动费用信息界面中-产品类兑付方式：src/pages/lj-market-activity/perform-case/perform-case-cost-list-new-page.vue
```
* 14、```production-pin```
```
1、活动稽核详情界面展示稽核记录「专供稽核人员使用」：src/pages/lj-market-activity/work-order/activity-approva-audit-page.vue
2、活动详情展示基本信息：src/pages/lj-market-activity/market-activity/market-activity-item-page.vue
3、活动其他信息界面：src/pages/lj-market-activity/market-activity/new-market-activity-other-info-page.vue
4、执行反馈详情界面：src/pages/lj-market-activity/work-order/perform-link-page.vue
5、已弃用不需要考虑-页面name: "edit-cost-item-page",
```
* 15、```progress-new```
```
1、活动稽核详情界面展示稽核记录「专供稽核人员使用」：src/pages/lj-market-activity/work-order/activity-approva-audit-page.vue
2、活动详情展示基本信息：src/pages/lj-market-activity/market-activity/market-activity-item-page.vue
```
* 16、```tasting-wine-scan-code```
```
1、市场活动费用信息界面：src/pages/lj-market-activity/market-activity/market-activity-cost-list-new-page.vue
```
* 17、```tasting-wine-scan-code-record```
```
1、申请费用组件<cost-new> 使用：src/pages/lj-market-activity/market-activity/components/cost-new.vue
2、执行反馈组件<execution-feedback>使用：src/pages/lj-market-activity/market-activity/components/execution-feedback.vue
3、市场活动费用信息界面：src/pages/lj-market-activity/market-activity/market-activity-cost-list-new-page.vue
4、执行反馈详情界面：src/pages/lj-market-activity/work-order/perform-link-page.vue
```
* 18、```terminal```
```
1、执行反馈详情界面：src/pages/lj-market-activity/work-order/perform-link-page.vue
2、活动详情展示基本信息：src/pages/lj-market-activity/market-activity/market-activity-item-page.vue
3、活动其他信息界面：src/pages/lj-market-activity/market-activity/new-market-activity-other-info-page.vue
4、活动稽核详情界面展示稽核记录「专供稽核人员使用」：src/pages/lj-market-activity/work-order/activity-approva-audit-page.vue
```
------ 市场活动-涉及组件-内容结束 ------
