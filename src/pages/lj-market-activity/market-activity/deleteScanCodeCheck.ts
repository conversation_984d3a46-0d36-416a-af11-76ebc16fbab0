import {$http} from "../../../utils/$http";

export const deleteScanCodeCheck = {
    /**
     * 删除扫码记录时,查询活动信息，状态status MC_STATUS 为“已实发 ActualAmount ”和“已作废 Inactive ”的不允许删除
     * <AUTHOR>
     * @date 2021/12/16
     * @param key 活动ID
     */
    async checkFlag(key) {
        return new Promise(async (resolve) => {
            try {
                const data = await $http.post('action/link/marketAct/queryById', {
                    id: key
                });
                if (data.success) {
                    if(data.result['status'] === 'ActualAmount' || data.result['status'] === 'Inactive' || data.result.aproStatus === 'Feedback'){
                        resolve(false);
                    } else {
                        resolve(true);
                    }
                } else {
                    resolve(false);
                }
            } catch (e) {
                resolve(false);
            }
        });
    },
}
