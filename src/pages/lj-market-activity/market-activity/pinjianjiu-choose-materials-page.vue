<template>
    <link-page class="pinjianjiu-choose-materials-page">
        <link-auto-list :option="autoList" hideCreateButton :searchInputBinding="{props:{placeholder:'产品编码/产品名称'}}">
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="scan-list" @tap.stop="click(data)" >
                    <view class="scan-item">
                        <view class="list-cell">
                            <view style="width: 100%;height: 20px">
                                <view class="code">{{data.prodCode}}</view>
                                <view class="fee-pay-type">{{data.feePayType}}</view>
                            </view>
                            <view class="name">{{data.prodName}}</view>
                            <view class="bottle-info">
                                <view class="font"><text class="font-title">申请</text>{{data.qty}}瓶</view>
                                <view class="font"><text class="font-title">出库</text>{{data.outOfStock}}瓶</view>
                                <view class="font"><text class="font-title">开瓶</text>{{data.bottledSum}}瓶</view>
                                <view class="font"><text class="font-title">赠送</text>{{data.giftedSum}}瓶</view>
                            </view>
                        </view>
                    </view>
                    <view>
                        <link-icon icon="icon-yiwanchengbuzhou" style="color:#2F69F8" slot="icon" v-if="data.clickFlag"/>
                        <link-icon icon="icon-circle-outline" slot="icon" v-else/>
                    </view>
                </item>
            </template>
        </link-auto-list>
        <link-sticky>
            <link-button block @tap="confirm ">确定</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    export default {
        name: "pinjianjiu-choose-materials-page",
        data(){
            let scene = '';// 区分选择物资界面的标题 实际费用 actual 、申请费用 apply
            let activityItem = this.pageParam.activityItem;//活动对象
            let codeData = this.pageParam.codeData;//扫码对象
            let scanScenario = this.pageParam.scanScenario;//扫码类型
            if (!this.$utils.isEmpty(this.pageParam.scene)) {
                scene = this.pageParam.scene;
                if (scene === 'actual') {
                    this.$taro.setNavigationBarTitle({title: '费用实际物资'});
                } else {
                    this.$taro.setNavigationBarTitle({title: '费用申请物资'});
                }
            }
            let prodId = '';
            if(codeData.match){
                prodId=codeData.rows.prodId;
            };
            let queryByExamplePage = 'action/link/actualFee/scanCodeVerification' ;
            let  param ={
                actId: activityItem.id,
                feeStage: scene,
                allDataFlag: 'Y',
                prodId:prodId,
            };
            if(scanScenario !== 'outbound'){
                queryByExamplePage = 'action/link/actualFee/queryFeeListDataPage';
                if(this.pageParam.tequCompany){
                    param={
                        wineTasting : 'Y1',
                        actId: activityItem.id,
                        prodId : prodId
                    }
                }else {
                    param={
                        wineTasting : 'Y2',
                        actId: activityItem.id,
                        prodId : prodId
                    }
                }
            }


            const autoList = new this.AutoList(this, {
                url: {
                    queryByExamplePage: queryByExamplePage
                },
                param: param ,
                sortOptions: null,
                searchFields: ['prodCode', 'prodName'],
                hooks: {
                    afterLoad (data) {
                        if(this.$utils.isNotEmpty(data['rows'])){
                            //是否选中
                            data.rows.forEach(async (item) => {
                                item.clickFlag = false;
                            })
                        }
                    }
                }
            });
            return{
                scene,
                autoList,
                scanScenario,
            }
        },
        methods:{
            click(data){
                this.autoList.list.forEach((item) => {
                    if(item.id === data.id){
                        data.clickFlag = !data.clickFlag;
                    } else {
                        item.clickFlag = false;
                    }
                });
            },
            confirm(){
                let checkedData = this.autoList.list.filter((x) => x.clickFlag);
                if(this.$utils.isEmpty(checkedData)){
                    this.$message.warn('请选择物资');
                    return false;
                }
                this.pageParam.callback(checkedData[0]);
                this.$nav.back();
            }
        }
    }
</script>

<style lang="scss">
    .pinjianjiu-choose-materials-page {
        .scan-item{
            background-color: #FFFFFF;
            position: relative;
            width: 100%;
            display: flex;
            flex-direction: column;

            .list-cell {
                width: 100%;
                position: relative;
                flex-direction: column;
                justify-content: space-between;
                align-items: start;
                .view-margin{
                    margin: 12px 16px;
                }
                .font{
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 28px;
                    width: 25%;
                    float: left;

                    font-title{
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        line-height: 28px;
                    }
                }
                .code{
                    font-family: DIN-Regular;
                    font-size: 28px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    line-height: 40px;
                    background: #A6B4C7;
                    border-radius: 8px;
                    width: 47%;
                    text-align: center;
                    float: left;
                }
                .fee-pay-type{
                    font-family: DIN-Regular;
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 40px;
                    width: 47%;
                    text-align: center;
                    float: right;
                }
                .name{
                    font-family: PingFangSC-Medium;
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 36px;
                    padding-top: 20px;
                }
                .bottle-info{
                    width: 100%;
                    height: 30px;
                    padding-top: 20px;
                    .bottle-row{
                        display: flex;
                        flex-direction: row;
                        view{
                            flex: 1;
                        }
                        .right{
                            text-align: right;
                        }
                    }
                }
            }
        }
    }
</style>
