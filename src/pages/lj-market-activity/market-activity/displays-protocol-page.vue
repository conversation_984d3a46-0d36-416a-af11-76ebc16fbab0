<template>
    <link-page class="displays-protocol-page">
        <link-form ref="form" :value="formData">
            <link-form-item label="协议类型" required>
                <view>{{formData.agrType|lov('AGR_TYPE')}}</view>
            </link-form-item>
            <link-form-item label="陈列活动类型" required v-if="isDisplay && disTypeExcludeLovs">
                <link-lov v-model="formData.displayType" type="EXHIBITION_TYPE" :excludeLovs="disTypeExcludeLovs" @change="changeDisplayType"/>
            </link-form-item>
            <link-form-item label="陈列政策类型" required v-if="isDisplay && disPolicyTypeExcludeLovs && userInfo.coreOrganizationTile">
                <link-lov v-model="formData.displayPolicyType" type="DISPLAY_POLICY_TYPE" :excludeLovs="disPolicyTypeExcludeLovs"
                          parentType="ACTIVITY_COMPANY" :parentVal="userInfo.coreOrganizationTile.l3Id"/>
            </link-form-item>
            <link-form-item label="陈列协议等级" :required="isDisplay" v-if="isDisplay">
                <link-lov v-model="formData.displayLevel" type="DISPLAY_LEVEL"/>
            </link-form-item>
            <link-form-item label="协议名称" required>
                <link-input v-model="formData.agrName"/>
            </link-form-item>
            <link-form-item label="客户名称" required>
                <link-input placeholder="请输入客户名称" readonly
                            @tap="pickAccount" suffixIcon="mp-arrow-right" v-model="formData.accntName"/>
            </link-form-item>
            <link-form-item label="开始时间" required>
                <link-date view="YMD" valueFormat="YYYY-MM-DD" displayFormat="YYYY年MM月DD日"
                           v-model="formData.startTime"/>
            </link-form-item>
            <link-form-item label="结束时间" required>
                <link-date view="YMD" valueFormat="YYYY-MM-DD" displayFormat="YYYY年MM月DD日" v-model="formData.endTime"/>
            </link-form-item>
            <link-form-item label="陈列数量" required :arrow="false" v-if="isDisplay">
                <view class="add-btn" @tap="addDisStandard">添加</view>
            </link-form-item>
            <link-swipe-action v-for="(item, index) in agreeProdList" :key="index">
                <link-swipe-option slot="option" @tap="deleteProdItem(item, index)">删除</link-swipe-option>
                <item class="prod-item" :arrow="false" @tap="editItem(item, index)">
                    <view class="left-wrap">产品小类：{{item.productSmallType | lov('PROD_BUS_S_CLASS')}}</view>
                    <view class="right-wrap">
                        <text class="num">{{item.productNumber}}</text>瓶
                        <link-icon icon="icon-edit" @tap="editItem(item, index)"/>
                    </view>
                </item>
            </link-swipe-action>

            <link-form-item label="备注" vertical>
                <link-textarea v-model="formData.comments"/>
            </link-form-item>
            <view class="display-basics-column" v-if="formData.id && isDisplay">
                <view class="label">
                    <text>*</text>陈列协议照片<link-alert icon="mp-info" status="error" class="inline-tips">协议类照片建议进行竖拍</link-alert>
                </view>
                <lnk-img-watermark :parentId="formData.id"
                                   :moduleType="formData.agrType"
                                   :delFlag="true"
                                   :album="false"
                                   :continueFlag="true"
                                   :pathKeyArray="formData.attachmentList"
                                   :useModuleName="formData.accntName"
                                   moduleName="陈列协议"
                                   :displayCustomizedWatermark="true"
                                   @imgUploadSuccess="imageArrLength"
                                   @imgDeleteSuccess="imageArrLength"
                                   ref="agreementImg"
                                   :newFlag="true"></lnk-img-watermark>
            </view>
            <link-alert icon="mp-info" status="error" v-if="isDisplay">与终端签订了协议的，必须上传已生效的纸质协议照片；未与终端签订协议的，上传陈列照片</link-alert>
            <view class="display-basics-column" v-if="formData.id && isDisplay">
                <view class="label">
                    <text>*</text>陈列照片
                </view>
                <lnk-img-watermark :parentId="formData.id"
                                   moduleType="displayPhoto"
                                   :delFlag="true"
                                   :album="false"
                                   :continueFlag="true"
                                   :pathKeyArray="formData.attachmentList"
                                   :useModuleName="formData.accntName"
                                   moduleName="陈列照片"
                                   :displayCustomizedWatermark="true"
                                   @imgUploadSuccess="disImageArrLength"
                                   @imgDeleteSuccess="disImageArrLength"
                                   ref="agreementImg"
                                   :newFlag="true"></lnk-img-watermark>
            </view>
            <link-form-item label="陈列扫码数量(瓶)" v-if="isDisplay" disabled :required="isDisplay && formData.displayType !== 'Normal'">
                <link-input disabled v-model="formData.displayScan"/>
            </link-form-item>
            <!-- 扫码按钮 -->
            <view class="scan-wrap" v-if="isDisplay">
                <view class="iconfont icon-saoma1" @tap="scanCode"></view>
            </view>
            <!-- 扫码详情 -->
            <display-scan-code :head-id="formData.id"
                               source="agree" ref="scanCode"
                               v-if="formData.id && isDisplay"
                               @updateDisScan="getDisplaySacn"/>
        </link-form>
        <view class="blank"></view>
        <link-sticky v-if="isDisplay">
            <link-button mode="stroke" block @tap="sureAddProduct('save', false)">保存</link-button>
            <link-button block @tap="sureAddProduct('submit')">提交</link-button>
        </link-sticky>
        <link-sticky class="bottom-btn" v-else>
            <link-button class="sure-btn" size="normal" @tap="sureAddProduct('submit')" autoLoading>提交</link-button>
        </link-sticky>

        <!-- 调整陈列数量 -->
        <link-dialog ref="prodBottom"
                     v-model="productDialogFlag"
                     :noPadding="true"
                     position="bottom"
                     borderRadius="32rpx 32rpx 0 0">
            <view class="model-title">
                <view class="title">编辑</view>
                <view class="iconfont icon-close" @tap="productDialogFlag = false"></view>
            </view>
            <link-form>
                <link-form-item label="产品小类">
                    <view>{{editProdItem.productSmallType | lov('PROD_BUS_S_CLASS')}}</view>
                </link-form-item>
                <link-form-item label="陈列数量(瓶)">
                    <link-number v-model="editProdItem.productNumber" :min="0"/>
                </link-form-item>
            </link-form>
            <view class="link-dialog-foot-custom">
                <link-button shadow style="width: 100vw;margin-right: 28px;" @tap="saveItem">确定</link-button>
            </view>
        </link-dialog>
    </link-page>
</template>

<script lang="jsx">
    import Taro from "@tarojs/taro";
    import {LovService} from "link-taro-component";
    import LnkImgWatermark from "../../core/lnk-img-watermark/lnk-img-watermark";
    import DisplayScanCode from '../../terminal/terminal/components/display-scan-code';

    export default {
        name: "displays-protocol-page",
        components: {LnkImgWatermark, DisplayScanCode},
        data() {
            const formData = {
                agrName: null,
                agrTypeName:null,//暂时性字段
                // isEffective: 'Y', // 默认有效
                accntId: null,
                accntName: '',
                startTime: null,
                endTime: null,
                comments: null,
                id: null,
                row_status: 'NEW',
                ...this.pageParam.data
            };
            const parentData = this.pageParam.parentData;
            const agreementOauth = this.$utils.isPostnOauth() === 'MY_POSTN'? 'MULTI_POSTN': this.$utils.isPostnOauth();//查询协议安全性调整
            const saleProdOption = new this.AutoList(this, {
                url: {
                    queryByExamplePage: 'action/link/agreementProduct/queryProductSeriesPage'
                },
                hooks: {
                    beforeLoad(option) {
                        option.param.accountId = this.formData.accntId;
                    },
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} class="select-box" arrow="false">
                            <link-checkbox val={data.id} toggleOnClickItem slot="thumb"></link-checkbox>
                            <view class="select-left">
                                <view class="store-name">{LovService.filter(data.productSmallType, 'PROD_BUS_S_CLASS')}</view>
                            </view>
                        </item>
                    )
                }
            })
            return {
                userInfo: Taro.getStorageSync('token').result, //用户信息
                disTypeExcludeLovs: null, // 陈列活动类型排除值列表
                disPolicyTypeExcludeLovs: null, // 陈列政策类型排除值列表
                formData,
                parentData,//活动信息
                noSubAccountOrgCode : 'noMatchId',
                upLoadImage: [],
                displayImage: [],   // 上传的陈列图片
                agreeProdList: [],  // 协议陈列数量列表
                productDialogFlag: false,  // 编辑陈列数量弹框是否展示
                editProdItem: {},  // 编辑的陈列数量项
                editProdIndex: 0,  // 编辑的陈列数量下标
                saleProdOption,  // 根据所选终端筛选其所有所售产品的产品业务小类
                scannedCode: '', // 扫描的码
                addressInfo: {}, // 地址信息
                coordinate: {}, // 定位信息
                //客户查询 类型门店 状有效
                //2021-08-09改为和终端列表查询的安全性一致.调用数量接口，根本当前用户的职位判断数据安全性
                accountOption: new this.AutoList(this, {
                    // url: {
                    //     queryByExamplePage: 'action/link/accnt/queryAccntByCityOrgPage'
                    // },
                    module : 'action/link/accnt',
                    param: {
                        oauth: agreementOauth,
                        filtersRaw: [
                            //客户大类
                            {
                                id: 'acctType',
                                property: 'acctType',
                                value: '[Terminal,Distributor]',
                                operator: 'IN'
                            },
                            //状态
                            {
                                id: 'acctStatus',
                                property: 'acctStatus',
                                value: 'Y',
                                operator: '='
                            },
                        ],
                        //attr2: parentData.salesCityId
                    },
                    sortOptions: null,
                    searchFields: ['acctName'],
                    hooks: {
                        beforeLoad(option) {
                            delete option.param.order;
                            delete option.param.sort;
                        },
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item arrow="false"
                                  key={index}
                                  data={data}>
                                <view style="width: 100%;padding: 5px 0 0 14px" slot='note'>
                                    <view style="margin-bottom: 5px">
                                        <view
                                            style="font-family: PingFangSC-Semibold;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 16px;float:left">
                                                {data.acctType === 'Terminal' ? data.acctName : data.billTitle}
                                        </view>
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;line-height: 16px;padding-left: 5px;float: left;">
                                                {data.mobilePhone}
                                        </view>
                                    </view>
                                    <view style="color: gray;clear:both;padding-top:10px">
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px">
                                                {data.acctType === 'Terminal' ? data.addrDetailAddr : data.billDetailAddr}
                                        </view>
                                    </view>
                                </view>
                            </item>
                        )
                    }
                }),
            }
        },
        async created() {
            this.disTypeExcludeLovs = await this.getExcludeLovs('EXHIBITION_TYPE');
            this.disPolicyTypeExcludeLovs = await this.getExcludeLovs('DISPLAY_POLICY_TYPE');
            this.formData.id = await this.$newId();
            this.formData.agrType = this.pageParam.data.agrType
            this.$set(this.formData, 'accntName', this.pageParam.data.accntName);
            this.$set(this.formData, 'accntId', this.pageParam.data.id);
            if (this.$utils.isNotEmpty(this.formData.agrType) && this.formData.agrType === 'display') {
                this.formData.displayType = 'Chunlei';
                this.formData.agrStatus = 'Draft'; // 陈列协议新建时默认为草稿
                this.formData.isEffective = 'N'; // 陈列协议新建时默认为否
                this.formData.examineStatus = 'NoneCheck';
            }
            await this.getAddressInfo();
        },
        computed: {
            // 是否是陈列协议
            isDisplay() {
                return this.formData.agrType === 'display';
            }
        },
        watch: {
            'formData.startTime'(val) {
                if (val && this.formData.endTime && (new Date(val) > new Date(this.formData.endTime))) {
                    this.$message['warn']('开始日期不可晚于结束日期');
                    this.$nextTick(() => {
                        this.$data.formData.startTime = ''
                    });
                }
            },
            'formData.endTime'(val) {
                if (val && this.formData.startTime && (new Date(this.formData.startTime) > new Date(val))) {
                    this.$message['warn']('结束日期不可早于开始日期');
                    this.$nextTick(() => {
                        this.$set(this.formData, 'endTime', '')
                    });
                }
            },
        },
        methods: {
            /**
             * 获取被排除的值列表(根据值列表配置的是否应用字段，没有勾选上的则被排除)
             * <AUTHOR>
             * @date	2023/11/2 11:55
             */
            async getExcludeLovs(type) {
                const filtersRaw = [
                    {id: 'type', property: 'type', value: type},
                    {id: 'activeFlag', property: 'activeFlag', value: 'Y'},
                    {id: 'useFlag', property: 'useFlag', value: 'N'}
                ];
                if (type === 'DISPLAY_POLICY_TYPE') filtersRaw.push({id: 'parentVal', property: 'parentVal', value: this.userInfo.coreOrganizationTile.l3Id});
                try {
                    const {success, rows} = await this.$http.post('action/link/basic/queryByExamplePage', {filtersRaw});
                    if (success) {
                        return rows.map((item) => item.val);
                    }
                } catch (e) {
                    return [];
                }
            },
            /**
             * 改变陈列活动类型
             * <AUTHOR>
             * @date	2023/4/27 2:22
             */
            changeDisplayType(val) {
                this.$forceUpdate();
            },
            /**
             * 获取当前地址
             * <AUTHOR>
             * @date	2023/4/26 18:11
             */
            async getAddressInfo() {
                const that = this;
                this.coordinate = await that.$locations.getCurrentCoordinate();
                // 校验用户是否授权地理位置
                if (!this.$utils.isEmpty(this.coordinate.latitude) && !this.$utils.isEmpty(this.coordinate.longitude)) {
                    let address = await that.$locations.reverseTMapGeocoder(this.coordinate.latitude, this.coordinate.longitude, '陈列协议');
                    this.addressInfo = {
                        province: address['originalData'].result.addressComponent['province'],
                        city: address['originalData'].result.addressComponent['city'],
                        district: address['originalData'].result.addressComponent['district'],
                        scanAddress: address['originalData'].result.formatted_address
                    }
                }
            },
            /**
             * 选择产品
             * <AUTHOR>
             * @date	2023/4/26 11:23
             */
            async chooseItem(item) {
                await this.getCodeData({
                    ...this.addressInfo,
                    headId: this.formData.id,
                    mark: this.scannedCode,
                    productId: item.id,
                    source: 'agree'  // 协议
                });
            },
            /**
             * 扫码
             * <AUTHOR>
             * @date	2023/4/25 22:09
             */
            async scanCode() {
                const that = this;
                if (!this.checkedData()) {
                    return;
                }
                await wx.scanCode({
                    onlyFromCamera: true, // 只允许从相机扫码
                    success: async (res) => {
                        if (res.result) {
                            let flag = true;
                            if (that.formData.row_status === 'NEW') {
                                flag = await that.sureAddProduct('save', false);
                            }
                            that.scannedCode = res.result;
                            if (flag) {
                                that.getCodeData({
                                    ...that.addressInfo,
                                    headId: that.formData.id,
                                    mark: res.result,
                                    source: 'agree'  // 协议
                                });
                            }
                        }
                    }
                });
            },
            /**
             * 获取扫码数据
             * <AUTHOR>
             * @date	2023/4/25 22:54
             */
            async getCodeData(param) {
                try {
                    this.$utils.showLoading();
                    const {success, displayScan, rows} = await this.$http.post('action/link/visitDisplay/visitDisplayScanVerification', param)
                    if (success) {
                        this.$set(this.formData, 'displayScan', displayScan);
                        this.$refs.scanCode && this.$refs.scanCode.scanRecordOption.list.unshift(rows);
                        this.$message.success('扫码成功！');
                        setTimeout(() => {
                            this.scanCode()
                        }, 500)
                    }
                } catch (e) {
                    console.log('扫码错误！' );
                } finally {
                    this.$utils.hideLoading();
                }
            },
            /**
             * 获取陈列扫码总数
             * <AUTHOR>
             * @date	2023/4/27 19:23
             */
            async getDisplaySacn() {
                const {success, result} = await this.$http.post('action/link/agreement/queryById', {
                    id: this.formData.id
                });
                if (success) {
                    this.$set(this.formData, 'displayScan', result.displayScan);
                }
            },
            /**
             * 获取陈列图片
             * <AUTHOR>
             * @date	2023/4/21 11:15
             */
            disImageArrLength(param) {
                this.displayImage = param;
            },
            /**
             * 获取陈列采集图片
             * <AUTHOR>
             * @date 2022-3-1
             * @param param 打卡图片长度
             */
            imageArrLength (param) {
                this.upLoadImage = param;
            },
            /**
             * 编辑项
             * <AUTHOR>
             * @date	2023/4/18 17:03
             */
            async editItem(item, index) {
                this.editProdItem = item;
                this.editProdIndex = index;
                this.productDialogFlag = true;
            },
            /**
             * 保存编辑项
             * <AUTHOR>
             * @date	2023/4/19 10:50
             */
            saveItem() {
                this.agreeProdList[this.editProdIndex] = this.editProdItem;
                this.productDialogFlag = false;
            },
            /**
             * 删除项
             * <AUTHOR>
             * @date	2023/4/18 15:24
             */
            deleteProdItem(item, index) {
                this.agreeProdList.splice(index, 1);
            },
            /**
             * 添加陈列数量
             * <AUTHOR>
             * @date	2023/4/18 15:10
             */
            async addDisStandard() {
                if (!this.formData.accntId) {
                    this.$showError('请先输入客户名称！');
                    return;
                }
                const list = await this.$object(this.saleProdOption, {
                    pageTitle: '产品小类',
                    showInDialog: true,
                    multiple: true,
                    // selected: this.agreeProdList.map(item => item.id),
                });
                let agreeProdList = this.$utils.deepcopy(this.agreeProdList);
                list.map(async (newItem) => {
                    // 选中的产品小类里是否包含之前选过的产品小类
                    const index = this.agreeProdList.findIndex((oldItem) => oldItem.productSmallType === newItem.productSmallType);
                    // 没选择过的数据则状态赋值为新建
                    if (index === -1 && newItem) {
                        const id = await this.$newId();
                        agreeProdList.push({
                            id: id,
                            headId: this.formData.id,
                            productLargeType: newItem.productLargeType,
                            productMiddleType: newItem.productMiddleType,
                            productSmallType: newItem.productSmallType,
                            productNumber: newItem.productNumber || '',
                            row_status: 'NEW'
                        });
                    }
                });
                this.agreeProdList = agreeProdList;
            },
            /*
           * 选择客户
           * @auther songyanrong
           * @date 2020-11-21
           * */
            async pickAccount() {
                await this.queryCfgProperty();
                if (this.userInfo.coreOrganizationTile
                    && this.userInfo.coreOrganizationTile.l3Code
                    && this.noSubAccountOrgCode.indexOf(this.userInfo.coreOrganizationTile.l3Code) > -1) {
                    // 若条件未加上，则添加条件
                    const filter = this.accountOption.option.param.filtersRaw.find(item => item.id === 'multiAcctMainFlag');
                    if (!filter) {
                        this.accountOption.option.param.filtersRaw.push({
                            id: 'multiAcctMainFlag',
                            property: 'multiAcctMainFlag',
                            value: 'Y'
                        })
                    }
                }
                this.accountOption.option.param['attr2'] = this.parentData.salesCityId;
                const accountData = await this.$object(this.accountOption, {multiple: false});
                // 清空根据之前客户所选的产品业务小类
                if (this.isDisplay && !this.$utils.isEmpty(this.formData.accntId) && this.formData.accntId !== accountData.id) {
                    this.agreeProdList = [];
                }
                this.$set(this.formData, 'accntName', accountData.acctName);
                this.$set(this.formData, 'accntId', accountData.id);
            },
            /**
             * 检查数据
             * <AUTHOR>
             * @date 2020-09-08
             */
            checkedData(back = false) {
                if(this.$utils.isEmpty(this.formData.agrType)){
                    this.$message['warn']('请输入协议类型');
                    return false
                }
                if (this.isDisplay && this.$utils.isEmpty(this.formData.displayType)) {
                    this.$message['warn']('请选择陈列活动类型');
                    return false
                }
                if (this.isDisplay && this.$utils.isEmpty(this.formData.displayPolicyType)) {
                    this.$message['warn']('请选择陈列政策类型');
                    return false
                }
                if (this.isDisplay && this.$utils.isEmpty(this.formData.displayLevel)) {
                    this.$message['warn']('请选择陈列协议等级');
                    return false
                }
                if (this.$utils.isEmpty(this.formData.agrName)) {
                    this.$message['warn']('请输入协议名称');
                    return false
                }
                if(this.formData.agrType !== 'others'){
                    if (this.$utils.isEmpty(this.formData.accntId)) {
                        this.$message['warn']('请选择客户');
                        return false
                    }
                }
                if (this.$utils.isEmpty(this.formData.startTime)) {
                    this.$message['warn']('请选择开始日期');
                    return false
                }
                if (this.$utils.isEmpty(this.formData.endTime)) {
                    this.$message['warn']('请选择结束日期');
                    return false
                }
                if (new Date(this.formData.startTime) > new Date(this.formData.endTime)) {
                    this.$message['warn']('结束日期不可早于开始日期');
                    return false
                }
                if (this.isDisplay && !this.agreeProdList.length) {
                    this.$message['warn']('请维护陈列数量');
                    return false
                }
                const index = this.agreeProdList.findIndex((item) => !item.productNumber || item.productNumber === 0);
                if (this.isDisplay && index !== -1) {
                    this.$message['warn']('请维护产品业务小类数量');
                    return false
                }
                if(back && this.isDisplay && this.upLoadImage.length === 0) {
                    this.$message['warn'](`请上传陈列协议照片`);
                    return false
                }
                if(back && this.isDisplay && this.displayImage.length === 0) {
                    this.$message['warn'](`请上传陈列照片`);
                    return false
                }
                if (back && this.isDisplay && this.formData.displayType !== 'Normal'
                    && (this.$utils.isEmpty(this.formData.displayScan) || this.formData.displayScan === 0)) {
                    this.$message['warn']('请先进行陈列扫码');
                    return false;
                }
                return true
            },
            /**
             * 保存
             * <AUTHOR>
             * @date 2020-11-26
             */
            async sureAddProduct(flag, back = true) {
                const that = this;
                try {
                    that.$utils.showLoading();
                    if (that.checkedData(back)) {
                        that.formData.agrCategory = 'protocol';
                        if(that.formData.endTime.indexOf('23:59:59') === -1) that.formData.endTime = `${that.formData.endTime} 23:59:59`;
                        if (flag === 'submit') {
                            that.formData.agrStatus = 'normal';
                        }
                        if(flag === 'submit' && that.isDisplay){
                            that.formData.isEffective = 'Y'; //提交时 陈列协议为有效
                        }
                        let formData = this.$utils.deepcopy(that.formData);
                        that.isDisplay && (formData.attr4 = 'accntSubpage');
                        const data = await that.$http.post('action/link/agreement/upsert', formData, {
                            handleFailed: (error) => {
                                that.$utils.hideLoading();
                            }
                        });
                        if (data.success) {
                            that.formData.row_status = 'UPDATE';
                            that.formData = {...that.formData, ...data.newRow};
                            if (that.isDisplay && (back || flag)) {
                                await this.saveAgreeProd();
                            }
                            if (flag === 'save' && that.isDisplay) {
                                that.$message.success('保存成功，但协议提交后才会生效!'); //陈列协议保存时提示
                            } else if (flag === 'save') {
                                that.$message.success('保存成功!'); //非陈列协议保存时提示
                            }
                            if (back) {
                                that.$bus.$emit('newProtocolData', data.newRow);
                                that.$utils.hideLoading();
                                that.$nav.back(null, 2);
                            } else {
                                return true;
                            }
                        }
                    } else {
                        return false;
                    }
                } catch (e) {
                    that.$utils.hideLoading();
                    that.$aegis.report({
                        msg: '新建协议错误捕获',
                        ext1: JSON.stringify(e),
                        trace: 'error'
                    });
                    return false;
                } finally {
                    that.$utils.hideLoading();
                }
            },
            /**
             * 保存陈列数量
             * <AUTHOR>
             * @date	2023/5/8 17:22
             */
            async saveAgreeProd() {
                const {success, result} = await this.$http.post('action/link/agreementProduct/batchUpsert', this.agreeProdList);
                if (success) {
                    this.agreeProdList = result;
                    this.agreeProdList.forEach((item) => item.row_status = 'UPDATE');
                }
            },
            /**
             * 查询系统配置参数
             * 用于控制指定公司组织下用户查询时不查multiAcctMainFlag=N 的数据。
             * 受益客户、活动新建陈列协议客户名称、参与终端/经销商、宴席推荐终端四个字段（子对象）使用
             * */
            async queryCfgProperty(){
                const data = await this.$http.post('action/link/cfgProperty/queryByExamplePage', {
                    filtersRaw: [
                        {
                            id: 'key',
                            property: 'key',
                            value: 'MC_NO_SUB_ACCOUNT_ORG_CODE',
                        }
                    ]
                });
                if (data.success && data.rows && data.rows.length) {
                    this.noSubAccountOrgCode = data.rows[0].value
                } else {
                    this.noSubAccountOrgCode = 'noMatchId'
                }
            }
        }
    }
</script>

<style lang="scss">
    .displays-protocol-page {
        padding-bottom: 150px;
        /*deep*/
        .link-icon {
            font-size: 28px;
        }

        /*deep*/
        .link-input-text-align-left {
            text-align: right;
        }

        /*deep*/
        .link-item {
            padding: 24px;
        }

        .last-time-collect {
            @include flex-center-center();
            height: 76px;
            background: #ffffff;
            text-align: center;
            margin-bottom: 24px;

            .icon-time-circle {
                font-size: 28px;
                color: #8C8C8C;
            }

            .last-collect-text {
                font-size: 28px;
                color: #8C8C8C;
                padding-left: 8px;
            }

            .last-time {
                font-size: 28px;
                color: #000;
                padding-left: 8px;
            }
        }

        .basics-container {
            background: #ffffff;

            .basics-column {
                @include flex();
                @include direction-column();
                border-bottom: 1px solid #F2F2F2;
                background: #ffffff;
                padding-bottom: 24px;

                .label {
                    margin: auto 24px;
                    padding-top: 40px;
                    padding-bottom: 24px;
                    font-family: PingFangSC-Regular, serif;
                    font-size: 28px;
                    color: #595959;
                    letter-spacing: 0;
                    line-height: 28px;

                    text {
                        color: #FF595A;
                        margin-left: -14px;
                    }
                }

                .value {
                    font-family: PingFangSC-Regular, serif;
                    font-size: 28px;
                    color: #BFBFBF;
                    letter-spacing: 0;
                    line-height: 28px;
                    padding-bottom: 40px;
                    margin-left: -24px;

                    .text-area {
                        width: 100%;
                        color: #BFBFBF;
                    }
                }
            }
        }

        .blank {
            height: 24px;
            width: 100%;
        }

        .bottom-btn {
            padding-top: 16px;
            padding-bottom: 34px;

            .sure-btn {
                box-shadow: 0 8px 24px 0 rgba(47, 105, 248, 0.50);
                width: 340px;
                height: 96px;
                margin-right: 24px;
                margin-left: 24px;
            }
        }

        .add-btn {
            font-size: 28px;
            color: #2F69F8;
        }

        .select-box {
            @include flex-start-center;
            border-bottom: 1px solid #F2F2F2;
            .select-left {
                width: 100%;
                padding-left: 24px;
                .prod-num {
                    text {
                        font-family: PingFangSC-Regular,serif;
                        font-size: 28px;
                        color: #FFFFFF;
                        letter-spacing: 0;
                        line-height: 28px;
                        background: #A6B4C7;
                        border-radius: 8px;
                        padding: 6px 12px;
                    }
                    margin-top: 6px;
                    margin-bottom: 20px;
                }
                .store-name {
                    width: 100%;
                    font-family: PingFangSC-Regular,serif;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    font-weight: bold;
                }
                .store-supplier {
                    padding-top: 8px;
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                }
            }
        }

        .prod-item {
            border-bottom: 1px solid #bfbfbf;

            &:last-child {
                border: none;
            }

            .link-item-content {
                justify-content: space-between;
            }

            .left-wrap {
                color: #262626;
            }

            .right-wrap {
                display: flex;

                .num {
                    color: #262626;
                    margin-right: 6px;
                }

                .icon-edit {
                    color: #2F69F8;
                }
            }
        }

        .model-title {
            display: flex;
            margin-left: 24px;

            .title {
                font-size: 32px;
                color: #262626;
                letter-spacing: 0;
                text-align: center;
                line-height: 80px;
                height: 80px;
                width: 85%;
                padding-left: 40px;
            }

            .icon-close {
                color: #BFBFBF;
                font-size: 36px;
                line-height: 80px;
                height: 80px;
            }
        }

        .display-num {
            font-size: 28px;
            display: flex;
            background-color: #ffffff;
            padding: 24px;
            justify-content: space-between;
            color: #595959;

            .scan-num {
                margin-right: 4px;
            }

            .view-btn {
                color: #2F69F8;
            }
        }

        .display-basics-column {
            @include flex();
            @include direction-column();
            border-bottom: 1px solid #F2F2F2;
            background: #ffffff;
            padding-bottom: 24px;

            .label {
                margin: auto 24px;
                padding-top: 40px;
                padding-bottom: 24px;
                font-family: PingFangSC-Regular, serif;
                font-size: 28px;
                color: #595959;
                letter-spacing: 0;
                line-height: 28px;

                text {
                    color: #FF595A;
                    margin-left: -14px;
                }
            }

            .value {
                font-family: PingFangSC-Regular, serif;
                font-size: 28px;
                color: #BFBFBF;
                letter-spacing: 0;
                line-height: 28px;
                padding-bottom: 40px;
                margin-left: -24px;

                .text-area {
                    width: 100%;
                    color: #BFBFBF;
                }
            }
        }

        .scan-wrap {
            background: #fff;
            padding-left: 13px;
            .icon-saoma1 {
                color: #2945E8;
                font-size: 184px;
            }
        }
        .inline-tips {
            display: inline;
            background: none;
            padding: 0;
        }
    }
</style>
