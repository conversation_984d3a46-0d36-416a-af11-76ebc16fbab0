<template>
    <link-page class="audit-item-page">
        <view class="zero-view"></view>
        <view class="audit-item">
            <view>
                <view v-for="item in details" :key="item.label" class="item">
                    <view class="label">{{item.label}}</view>
                    <view class="text" v-if="$utils.isEmpty(item.type)">{{item.text}}</view>
                    <view class="text" v-if="$utils.isNotEmpty(item.type)">{{item.text | lov(item.type)}}</view>
                </view>
            </view>
            <view>
                <text style="font-size: 14px;color: #595959;">附件</text>
                <lnk-img :parentId="auditItem.id"
                         moduleType="MKT"
                         :delFlag="false"
                         :album="false"
                         :newFlag="false">
                </lnk-img>
            </view>
        </view>
    </link-page>
</template>

<script>
    import LnkImg from "../../core/lnk-img-watermark/lnk-img-watermark";
    export default {
        name: "audit-item-page",
        components: {LnkImg},
        data() {
            const auditItem = this.pageParam.data;
            return {
                auditItem,
                details: [],
            }
        },
        created() {
        if (this.pageParam.data.auditType==='FinanceDepartmentAudit') {
            this.details = [
                {label: '稽核时间', text: this.auditItem.created},
                {label: '稽核人', text: this.auditItem.feedbackerName},
                {label: '稽核人工号', text: this.auditItem.feedbackerNum},
                {label: '稽核人部门', text: this.auditItem.auditClass},
                {label: '稽核类型', text: this.auditItem.auditType, type: 'AUDIT_TYPE'},
                {label: '是否转交其他部门', text: this.auditItem.isTraOtherClass},
                {label: '转交部门', text: this.auditItem.traClass},
                {label: '稽核意见', text: this.auditItem.auditOpinion},
                {label: '稽核结论', text: this.auditItem.auditResult, type: 'AUDIT_RESULT'}
            ];
        }else{
            this.details = [
                {label: '稽核时间', text: this.auditItem.created},
                {label: '稽核人', text: this.auditItem.feedbackerName},
                {label: '稽核类型', text: this.auditItem.auditType, type: 'AUDIT_TYPE'},
                {label: '稽核人工号', text: this.auditItem.feedbackerNum},
                {label: '是否实地检核', text: this.auditItem.isFieldAudit, type:'IS_FLAG'},
                {label: '是否正常', text: this.auditItem.isIllegalAct, type:'IS_FLAG'},
                {label: '稽核金额', text: this.auditItem.auditAmount},
                {label: '活动执行标准及执行情况', text: this.auditItem.implementation},
                {label: '异常/违规情况', text: this.auditItem.violations},
                {label: '其他', text: this.auditItem.others},
            ];
        }
        }
    }
</script>

<style lang="scss">
    .audit-item-page {
        .zero-view {
            width: 100%;
            height: 30px;
        }

        .audit-item {
            width: 93%;
            margin: auto;
            border-radius: 16px;
            padding: 20px 0 20px 28px;
            background: white;

            .item {
                line-height: 56px;
                width: 100%;
                display: flex;
                .label {
                    font-size: 28px;
                    color: #595959;
                    letter-spacing: 0;
                    line-height: 56px;
                    float: left;
                    width: 50%;
                }

                .text {
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                    text-align: right;
                    line-height: 56px;
                    float: right;
                    width: 48%;
                    padding-right: 16px;

                }
            }
        }
    }
</style>
