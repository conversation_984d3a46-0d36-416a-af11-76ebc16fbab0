<template>
    <view class="tasting-wine-scan-code-detail-page">
        <view class="scan-code-content">
            <view v-for="(val, key) in infoList" class="block-v" :key="key">
                <view class="title">{{val}}</view>
                <view class="val" :class="{'text-blue': key === 'qrCodeOut' || key === 'qrCodeIn'}">{{recordInfo[key]}}</view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "tasting-wine-scan-code-detail-page",
    data() {
        return {
            infoList: {
                productName: '产品名称',
                productCode: '产品编码',
                qrCodeOut: '盖外码(后8位)',
                qrCodeIn: '盖内码(后8位)',
                descriptionType: '匹配状态',
                scanner: '扫码人',
                scanTime: '扫码时间',
                packagingMaterial: '是否包材',
                province: '省',
                city: '市',
                district: '区',
                scanAddr: '详细地址',
                // isBulkPickup: '是否批量扫码',
                caseOrBoxCode: '实际扫码层级'
            },
            recordInfo: {}        //基础信息
        }
    },
    created() {
        this.init()
        this.$taro.setNavigationBarTitle({title: this.pageParam.title});
    },
    methods: {
        async init() {
            const params = {
                id: this.pageParam.id
            }
            try {
                const data = await this.$http.post('action/link/actScanRecord/queryById', params);
                if (data.success) {
                    this.recordInfo = data.result
                    if (this.recordInfo.descriptionType !== 'MatchFailed') {
                        delete this.infoList.matchFailComment
                    }
                    // 是否批量扫码
                    // if (this.recordInfo.isBulkPickup === 'Y') {
                    //     this.recordInfo.isBulkPickup = '是'
                    // } else {
                    //     this.recordInfo.isBulkPickup = '否'
                    // }
                    // 实际扫码层级
                    const levelToCodeMap = {
                      7: '箱码',
                      5: '盒码',
                      3: '瓶码',
                    };
                    this.recordInfo.caseOrBoxCode = levelToCodeMap[this.recordInfo.sourceCodeLevel] || '盖外码';
                    //取扫码后缀展示
                    this.recordInfo.qrCodeOut = this.recordInfo.qrCodeOut && this.recordInfo.qrCodeOut.split('/').pop().substr(-8)
                    if (this.pageParam.title === '赠送扫码详情') {
                        delete this.infoList.qrCodeIn
                    } else {
                        this.recordInfo.qrCodeIn = this.recordInfo.qrCodeIn && this.recordInfo.qrCodeIn.split('/').pop().substr(-8)
                    }
                    //有备注内容才显示这个字段
                    if(data.result.remark) {
                        this.infoList.remark = '备注'
                    }

                    const p1 = this.$lov.getNameByTypeAndVal('RIGHT_STATUS', this.recordInfo.isEffective);
                    const p2 = this.$lov.getNameByTypeAndVal('SCAN_RECORD_STATUS', this.recordInfo.scanRecordStatus);
                    const p3 = this.$lov.getNameByTypeAndVal('MATCH_STATUS', this.recordInfo.descriptionType);
                    const p4 = this.$lov.getNameByTypeAndVal('PACK_MATERIAL', this.recordInfo.packagingMaterial);
                    [this.recordInfo.isEffective,
                        this.recordInfo.scanRecordStatus,
                        this.recordInfo.descriptionType,
                        this.recordInfo.packagingMaterial] = await Promise.all([p1, p2, p3, p4])
                    return
                }
                this.$showError('获取详情失败');
            } catch (e) {
                this.$showError(`获取详情异常:${e.result}`);
            }
        }
    }
}
</script>

<style lang="scss">
.tasting-wine-scan-code-detail-page{
    padding-top: 24px;
    font-family: PingFangSC-Regular;
    letter-spacing: 0;
    background-color: rgb(242, 242, 242);

    .scan-code-content{
        padding: 24px 0;
        width: 95%;
        margin: 0 auto;
        background-color: white;
        border-radius: 16px;
    }

    .main-title{
        font-size: 28px;
        color: #262626;
        padding: 17px 24px;
        border-bottom: 2px solid #e5e5e5;
    }

    .block-v {
        padding:0 24px;
        font-size: 28px;
        line-height: 60px;
        display: flex;

        .title {
            color: #8C8C8C;
            width: 35%;
            float: left;
        }

        .val {
            color: #262626;
            text-align: right;
            width: 65%;
            float: left;
            text-overflow: ellipsis;
        }

        .text-blue{
            color: #2F69F8;
        }
    }
}
</style>
