<template>
    <link-page class="check-out-the-winners-page">
        <link-auto-list :option="autoList">
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="content-list-item">
                    <view class="check-out-the-winners" slot="note">
                        <view class="data-v"  v-if="scene === 'hongbao'">
                            <view class="top">
                                <view class="name">{{data.nickName}}</view>
                                <view class="tel">{{data.created| date('MM-dd HH:mm')}}</view>
                            </view>
                            <view class="bottom">
                                <view class="icon-v">
                                    <view class="iconfont icon-style icon-jiangpin"></view>
                                </view>
                                <view class="prize-name">现金红包</view>
                                <view class="quantity">{{data.redEnvelope|cny}}</view>
                            </view>
                        </view>
                        <view class="data-v" v-if="scene === 'zhongjiang'">
                            <view class="record-list">
                                <view class="list-cell">
                                    <view class="media-list">
                                        <image class="media-list-logo" :src="data.avatarUrl"/>
                                        <view class="media-list-body">
                                            <view class="list-item">
                                                <view class="left">{{data.appAcctName || data.nickName}}</view>
                                                <view class="right">{{data.noEncrypedTel}}</view>
                                            </view>
                                            <view class="list-item award-item">
                                                <view class="left" style="display: flex;">
                                                    <view class="iconfont icon-style icon-jiangpin"></view>
                                                    {{data.awardName}}
                                                </view>
                                                <view class="right">{{data.created | date('YYYY-MM-DD')}}</view>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
    export default {
        name: "check-out-the-winners-page",
        data() {
            const parentId = this.pageParam.parentId;
            //场景 hongbao zhongjiang
            const scene = this.pageParam.scene;
            const componentFromId = this.pageParam.assemblyId;
            return {
                autoList: new this.AutoList(this, {
                    module: this.$env.appURL + '/interaction/link/actionInfo',
                    searchFields: ['nickName'],
                    param: {
                        filtersRaw: []
                    },
                    sortOptions: null,
                    hooks: {
                        beforeLoad (options) {
                            if (this.scene === 'hongbao') {
                            options.param.sort = 'redEnvelope'
                            options.param.filtersRaw = [
                                ...options.param.filtersRaw,
                                {id: 'interactionId', property: 'interactionId', value: this.parentId, operator: '='},
                                {id: 'type', property: 'type', value: 'Participate', operator: '='}
                            ]
                            } else {
                                if (this.$utils.isEmpty(this.componentFromId)) {
                                    options.param.filtersRaw = [
                                        ...options.param.filtersRaw,
                                        {id: 'interactionId', property: 'interactionId', value: this.parentId, operator: '='},
                                        {id: 'type', property: 'type', value: 'Participate', operator: '='},
                                    ];
                                } else {
                                    options.param.filtersRaw = [
                                        ...options.param.filtersRaw,
                                        {id: 'interactionId', property: 'interactionId', value: this.componentFromId, operator: '='},
                                        {id: 'type', property: 'type', value: 'Participate', operator: '='},
                                        {id: 'componentFromId', property: 'componentFromId', value: this.parentId, operator: '='}
                                    ];
                                }

                            }
                        }
                    }
                }),
                componentFromId,
                parentId,
                scene
            }
        },
        methods: {
        }
    }
</script>

<style lang="scss">
    .check-out-the-winners-page {
        background: white;

        .no-data {
            width: 368px;
            height: 368px;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            -moz-background-size: 100% 100%;
            margin: auto;
            margin-top: 100px;
        }

        .no-data-msg {
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #8C8C8C;
            letter-spacing: 0;
            text-align: center;
            line-height: 38px;
            padding: 50px;
        }

        .check-out-the-winners {
            .data-v {
                height: 128px;
                .record-list {
                    background-color: #FFFFFF;
                    position: relative;
                    width: 100%;
                    display: flex;
                    flex-direction: column;

                    .list-cell {
                        position: relative;
                        display: flex;
                        flex-direction: row;
                        justify-content: space-between;
                        align-items: center;

                        .media-list {
                            padding: 11px 15px;
                            box-sizing: border-box;
                            display: flex;
                            width: 100%;
                            flex-direction: row;
                            justify-content: space-between;

                            .media-list-logo {
                                height: 120px;
                                width: 120px;
                                margin-right: 20px;
                                border-radius: 50%;

                                image {
                                    height: 100%;
                                    width: 100%;
                                }
                            }

                            .media-list-body {
                                display: flex;
                                flex: 1;
                                flex-direction: column;
                                justify-content: space-between;
                                align-items: flex-start;
                                overflow: hidden;
                                .award-item{
                                    font-size: 28px;
                                    color: #262626;
                                }
                                .list-item{
                                    display: flex;
                                    justify-content: space-between;
                                    width: 100%;
                                    height: 60px;
                                    line-height: 60px;
                                    .left{
                                        width: 55%;
                                        overflow: auto;
                                        text-overflow:ellipsis;
                                        white-space: nowrap;
                                        margin-right: 5%;
                                    }
                                    .right{
                                        width: 40%;
                                    }
                                }
                            }
                        }
                    }
                }
                .top {
                    width: 100%;
                    height: 64px;
                    line-height: 64px;

                    .name {
                        font-size: 32px;
                        color: #262626;
                        letter-spacing: 0;
                        float: left;

                    }

                    .tel {
                        font-size: 28px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        padding-left: 15px;
                        float: left;

                    }

                    .date {
                        font-size: 28px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        text-align: right;
                        width: 50%;
                        float: left;

                    }
                }
                .icon-style {
                    font-size: 28px;
                    background-image: linear-gradient(90deg, #2F61F8 0%, #8A2FF8 100%);
                    -webkit-background-clip: text;
                    color: transparent
                }
                .bottom {
                    width: 100%;
                    height: 64px;
                    line-height: 64px;

                    .icon-v {
                        float: left;
                        width: 6%;
                        height: 100%;
                    }

                    .prize-name {
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 0;
                        width: 80%;
                        float: left;
                        height: 100%;
                    }

                    .award-name {
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 0;
                        width: 20%;
                        float: left;
                        height: 100%;
                    }

                    .x-view {
                        font-size: 28px;
                        color: #BFBFBF;
                        letter-spacing: 0;
                        text-align: right;
                        float: left;
                        width: 5%;
                        height: 100%;
                    }

                    .quantity {
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 0;
                        text-align: center;
                        width: 13%;
                        float: left;
                        height: 100%;
                    }

                    .prize-set-name{
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 0;
                        text-align: right;
                        width: 74%;
                        float: left;
                        height: 100%;
                    }
                }
            }

            .line {
                background: #F2F2F2;
                border-radius: 24px;
                margin: 50px 24px 0 24px;
                height: 2px;

            }
        }
    }
</style>
