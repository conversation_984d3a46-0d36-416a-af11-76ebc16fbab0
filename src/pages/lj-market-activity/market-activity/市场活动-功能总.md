# 市场活动
```
创建时间：2022/02/10 20:15
创建人：  宋燕荣
模块路径：src/pages/lj-market-activity/market-activity
```
### 逻辑功能实现
* 说明：下面所有页面实现功能数字序号都对应以下22列。
```
1、新建活动
2、活动作废
3、活动列表展示、按状态快捷查询、筛选、排序、模糊查询
4、提交活动申请
5、一键报备
6、分享海报
7、创建名单
8、名单提报名额调整
9、活动稽核
10、执行反馈
11、提交费用审批
12、清除执行案编码功能及操作权限【活动阶段和场景】
13、执行反馈阶段维护业务场景图片，拍照加水印【时间、地址、一些业务信息】、相册选择
14、新增、删除、编辑、查看费用申请-现金类兑付方式数据
15、新增、编辑、查看费用实际-现金类兑付方式数据
16、新增、删除、编辑、查看费用申请-产品类兑付方式数据
17、新增、编辑、查看费用实际-产品类兑付方式数据
18、维护、切换、清除互动信息
19、选择消费者关联到当前活动、新建消费者
20、选择内部人员关联到当前活动
21、选择参与终端/经销商关联到当前活动
22、新增、更新、删除活动动销数据
```

### 活动列表
功能1、2、3
#### 一 新建活动

俩个新建途径
* 1、市场活动模块列表新建入口
* 2、执行案模块-执行案详情-快捷新建市场活动

#####  1、市场活动模块列表新建入口。
##### 1.1、vue页面路径
* 市场活动的模版都和费用的大中小类项关联，某些还限制活动类型。活动类型是费用小类的子对象，具体关系PC端【活动管理 / 费用|活动类型】菜单可查。

1.1.1 选择费用类型和活动类型
src/pages/lj-market-activity/market-activity/new-market-activity-page.vue

1.1.2 新建活动维护基本信息界面
src/pages/lj-market-activity/market-activity/new-activity-basic-page.vue

1.1.3 维护费用信息
src/pages/lj-market-activity/market-activity/market-activity-cost-list-new-page.vue

#####  2、执行案模块-执行案详情-快捷新建市场活动。
##### 2.1、vue页面路径
* 市场活动的模版都和费用的大中小类项关联，某些还限制活动类型。活动类型是费用小类的子对象，具体关系PC端【活动管理 / 费用|活动类型】菜单可查。

2.1.1 维护费用信息-选择执行案明细带出费用大、中、小类。然后去查询费用|活动类型去查看当前费用小类可选的活动类型以及活动类型是否需要必选。
src/pages/lj-market-activity/perform-case/perform-case-cost-list-new-page.vue

2.1.2 新建活动维护基本信息界面
src/pages/lj-market-activity/perform-case/new-activity-page.vue
##### 2.2、页面包含按钮
```
上一步
作用：返回上一个界面
```
```
下一步
作用：保存市场活动数据且跳转到下一页
说明：市场活动模块新建下一页跳转到费用维护界面，执行案新建活动下一页当前费用小类需要维护的子对象，如果当前费用小类添加了互动配置子对象那么跳转到互动配置界面，否则都跳转到其他信息界面。（跳转到互动配置界面/pages/lj-market-activity/market-activity/interactive-configuration-page或其他信息界面/pages/lj-market-activity/market-activity/new-market-activity-other-info-page）

```
```
保存并下发
作用：创建名单并下发。添加活动对接人，给确定的活动对接人下发活动。
```
##### 2.3 市场活动重要字段赋值逻辑 详情见 市场活动-重要字段赋值逻辑.md

* 数据保存接口
```
- 活动保存：action/link/marketAct/upsert
- 费用保存：action/link/actMaterial/batchInsert
- 对接人名额提报调整数据保存：action/link/actIntegration/saveSubmitPlacesAndUpsert
- 对接人名额调整提报并下发：action/link/actIntegration/saveAndIssuedSubmitPlaces
```
*  数据校验
```
- 获取模版配置上所有的必输字段
- 获取某些非必输但是电话号码类的校验是否有值，有值时需要校验格式是否正常
- 获取活动开始时间和结束时间的先后顺序
- 获取字段的模版编码如果是'link-number-keyboard',由于link-number-keyboard组件用户觉得弹出速度比较慢，后来改成使用link-input,类型是数字类型，安卓机不可以切换其他输入方式输入的只有数字。但是iOS如果安装了其他输入法则界面可以切换输入非数字类型的内容，所以需要加校验。
- 如果配置了会员相关字段，会员等级为必选
```

#### 二 活动作废

* 可以作废活动的场景
```
活动状态 MC_STATUS =新建、已发布、进行中、执行结束，
活动审批状态 APRO_STATUS =未提交、提报待审批、已拒绝、申请审批通过、反馈待审批、反馈驳回、反馈撤回时
```
* 作废活动入口

```市场活动列表数据左滑操作作废```

#### 三 活动列表
* 展示、按状态快捷查询、筛选、排序、模糊查询
```
展示字段：
    活动编码 activityNum
    活动状态 status
    活动名称 activityName
    审批状态 aproStatus
    稽核状态 whetherAudit
    活动时间 startTime
    提报人 executor
    申请金额 【当活动审批状态APRO_STATUS不为（已反馈、反馈驳回、反馈撤回）时且当活动状态为新建、已发布、进行中、已结束时，取活动费用申请表的数据，活动金额=现金申请金额+产品申请金额；】
    实际填报金额 【当活动审批状态APRO_STATUS为已反馈、反馈驳回、反馈撤回时，活动状态不为新建、已发布。取活动费用实际表的数据，活动金额=现金实际填报金额+产品实际填报金额；】
    实发金额 【当活动状态为已实发、材料补充、已作废时，取活动费用实际表的数据，活动金额=现金实发金额+产品实发金额。】
```

* 模糊查询条件
```
活动编码、活动名称、现金申请金额、提报人
[activityNum,activityName,cashApplyAmount,executor]
```

### 活动详情&新建活动流程
* 新建活动流程中子对象的维护和活动详情界面维护类似，合并一起描述.

#### 4、提交活动申请

* 操作按钮权限
```
1&2都满足才可以点击提交

1、活动状态为‘新建‘，审批状态为‘未提交’’已拒绝‘
2、且不属于以下职位类型['CityManager', 'SalesRegionManager', 'SalesAreaManager',].includes(userInfo.positionType); //是否是片区经理
```
* 活动提交前端校验
```
1、活动开始时间、活动结束时间、活动业务场景字段必输
2、配置的消费者对象是否必输 是：校验是否维护了消费者子对象数据 否：跳过
3、配置的互动配置是否必输 是：校验通过 否：提示需要维护费用申请数据
4、是否维护了费用申请数据 是：校验是否维护了消费者子对象数据 否：跳过
5、存在未完成的活动名单，请检查并完成审批或者作废以结束名单提报
- 是否有关联的名单头状态listStatus =新建、待提报、待审批、审批拒绝. LIST_STATUS。如果有，则弹窗提示“存在未完成的活动名单，请检查并完成审批或者作废以结束名单提报”
6、查询当前活动是否需要同步到简道云，接口报错时弹出让用户选择
```
* 接口 action/link/marketAct/commit
* 传参
{id: 活动ID
synNcMakOrderFlag:是否同步NC}

#### 5、一键报备
```
查询当前活动的费用小类和活动类型匹配的活动报备的小程序模版，模版当中包含需要报备的活动字段
提供复制报备文本功能，供用户发送给目标。

报备内容eg: 品鉴会报备
    报备时间2022-02-10 17:35
    报备人及电话:宋燕荣;18347285925
    区域主管姓名及电话:赵冬兰;15681568633
    执行案编号:P2108081163
    销售区县:
    活动编码:SCHD220107968001
    活动名称:测试
    活动类型:品鉴会
    开始时间:2022-01-07 09:47:00
    结束时间:2022-01-08 09:47:00
    子公司/经销商:泸州博成酒类销售有限公司
    受益客户:博大测试

    订餐人:
    订餐人联系电话:
    酒店名称:北京
    酒店地址:北京市北京市东城区测试
    酒店电话:17380449461
    消费者人数:1
    用餐人数:
    用酒规格及数量:

    现金申请金额:0
    产品申请金额:0
    执行人:宋燕荣
    执行人联系电话:18347285925
    购买产品及数量:
    活动消费者:

    备注:报备人确认以上信息真实有效。
```

#### 6、分享海报
* 生成包含标题、欢迎问候语、活动信息、展示图片、结束语
* 功能：可以点击保存为照片，供用户发送给目标。

#### 7、创建名单 & 8、名单提报名额调整
##### 下发名单以及调整提报名额
*  创建名单保存
```
接口 action/link/actIntegration/saveSubmitPlacesAndUpsert
传参
{marketAct: 活动对象
maInterUserList:下发名单列表}
```
* 创建名单保存并下发
```
接口 action/link/actIntegration/saveAndIssuedSubmitPlaces
传参
{marketAct: 活动对象
maInterUserList:下发名单列表}
```
#### 9、活动稽核
* 按钮权限
```
1&2&3都满足才可以点击
1、活动状态 MC_STATUS ：已发布、进行中、已实发、材料补充、已结束
2、审批状态 APRO_STATUS ：申请审批通过、反馈待审批、反馈驳回、反馈审批通过 活动稽核可以操作
3、"活动稽核按钮，仅职位类型为“稽核人员“&”财务人员“时，可点击。职位类型值列表类型：POSTN_TYPE 财务人员：FinanceStaff；稽核人员：AuditStaff"
```
* 作用
```
支持总部市场部门、财务部门、纪检部门对市场活动费用进行抽检
供稽核人员稽核活动信息查看活动基本信息以及子对象数据
稽核人员维护稽核意见、稽核结论、是否转交其他部门复检、复检部门。
```
* 接口-保存稽核数据 action/link/exeFeedback/insert
* 传参
{稽核数据对象}
* 接口-更新活动稽核状态 action/link/marketAct/update
* 传参
{活动对象指定字段更新，更新稽核状态whetherAudit}


#### 11、提交费用审批
* 按钮权限
```
1&2同时满足可以点击
1、活动状态 MC_STATUS ：进行中、已结束
2、审批状态 APRO_STATUS ：申请审批通过、反馈驳回
```
* 提交前校验
```
1、需要检查基础信息必输项是否都填写完毕
2、活动结束时间必须晚于活动开始时间
3、基础信息【执行案编码】以及【无执行案说明】二者不能同时为空
4、电话号码类的字段值格式是否正确
5、配置是否需要维护消费者数据，是 校验是否维护了消费者数据 否 跳过
6、查询配置的图片类型 必须上传的图片类型是否传了图
7、提交费用审批之前，获取费用信息展示，并提示是否继续提交
```
* 接口 action/link/marketAct/marketActApproval
* 传参 {id:活动ID，attr8:照片备注}

#### 12、清除执行案编码功能及操作权限
> 清除执行案编码，将同步清掉该活动下已关联了相关执行案明细的费用申请明细及活动上关联信息(省市区县ID和名称)

* 按钮权限
```
1、非执行反馈环境时编辑基础信息-【执行案编码】可以清除执行案编码
2、!((活动状态为新建且审批状态为新建或已拒绝) || (活动状态为进行中或执行结束或
已发布且审批状态为申请审批通过或反馈驳回或反馈撤回))
```

* 接口 action/link/actIntegration/clearActProdCode
* 参数
```
{
  marketAct:活动对象,
  actMaterial:{
     actId: 活动ID
     }
}
```
* 后端处理
```
删除其下挂的费用申请对象，清掉活动上的省ID、省名称、市ID、市名称、区县ID、区县名称、活动的费用垫付对象ID
```
* 前端处理
```
clearActProdCode接口清除执行案时省市区县id同时清空
校验返回新行省id为空 受益人不为空 满足后取受益人的省市区保存

```

#### 13、执行反馈阶段维护业务场景图片，拍照加水印【时间、地址、一些业务信息】、相册选择
* 业务场景模版配置相关内容请查看市场活动-页面配置.md
* 图片存放位置：腾讯云
* LNK_ATTACHMENT存放数据对象关系
```
核心点：
1、使用水印相机组件 src/pages/core/lnk-img-watermark/lnk-img-watermark.vue
2、attachmentPath 要展示的原图片路径字段/图片cosKey
3、env.cosUploadUrl拼接图片cosKey为完整的图片路径，供展示
```
#### 18、维护、切换、清除互动信息
* 互动配置界面
```
1、界面路径
    src/pages/lj-market-activity/market-activity/interactive-configuration-page.vue
2、功能介绍
    选择符合安全性以及各项条件的营销模版数据。
    选择模版之后将模版ID和模版名称保存到企微市场活动的templateName和templateId上。
    以及将重要的营销模版上的活动互动信息保存到actJson字段上。这几类互动活动【红包或大转盘或微信签到或邀请或砸金蛋或老虎机】的内容展示都是解析的actJson字段。
       - actJson字段值示例：
           {"nodes":[{"secondlabel":"双击查看或编辑","shape":"start-node","size":"56*56","x":541,"y":40,"index":0,"label":"开始","id":"d525db161","type":"node"},{"secondlabel":"双击查看或编辑","shape":"end-node","size":"56*56","x":542,"y":358,"index":1,"label":"结束","id":"bb51b51c2","type":"node"},{"secondlabel":"双击查看或编辑","shape":"plantwheel-node","size":"56*56","x":581,"evetype":"WheelCamp","y":210,"index":2,"label":"企微模板大转盘","id":"dc36f00a","v-7817d102":"","keyval":"248386092125155333","type":"node","appLabel":"测试实物\r（国窖小酒）\r1\n","backBaseId":"248863972256649242"}]}
3、特殊逻辑
    更新模板时判断是否存在已经编辑保存的互动信息，有已经保存编辑过的活动信息 切换营销模板的时候需要提示用户。已存在编辑保存的互动信息，重新选择营销模板会失效已保存的互动信息，是否确认重新选择营销模板？
4、互动活动
    红包或大转盘或微信签到或邀请或砸金蛋或老虎机
5、涉及组件
    互动界面包含组件 <design-interaction-type>
           design-interaction-type包含具体的互动活动组件：
                    <app-wheel-camp-card>
                    <app-sales>
                    <app-sign-in>
```
------ 市场活动-功能总-内容结束 ------
