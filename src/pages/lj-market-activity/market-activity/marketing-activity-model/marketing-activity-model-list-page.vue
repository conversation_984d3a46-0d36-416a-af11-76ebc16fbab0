<template>
  <link-page class="marketing-activity-model-list-page">
      <view class="base">
          <item title="营销模板" :arrow="false">
              <view>
                  {{currentTemplate.activityName || '请下滑勾选营销模板' }}
              </view>
          </item>
      </view>
      <link-auto-list :option="autoList">
          <template slot-scope="{data,index}">
              <link-radio-group v-model="currentTemplateId">
                  <list>
                      <item :key="index" :data="data" :arrow="false" class="marketing-activity-model-list">
                          <view slot="note" class="item-container">
                              <view class="title-line">
                                  <view class="marketing-activity-name">{{data.activityName}}</view>
                                  <link-checkbox :val="data.id" toggleOnClickItem slot="thumb" @tap="chooseTemplate(data)"></link-checkbox>
                              </view>
                              <!--两边圆点-->
                              <view class="dot-left"></view>
                              <view class="dot-right"></view>
                              <view class="model-content">
                                  <view class="model-item" v-for="(item, index) in data.actJson.nodes">
                                      <view v-if="item.evetype && (item.evetype === 'WheelCamp' || item.evetype === 'LotteryTicket'
                            || item.evetype === 'Sales' || item.evetype === 'SignIn' || item.evetype === 'RoundWheel'
                            || item.evetype === 'Invitation' || item.evetype === 'SmashGoldenEgg'
                            || item.evetype === 'SlotMachine' || item.evetype === 'BlindBox'
                            || item.evetype === 'IceTrue' || item.evetype === 'ExpansionRule'
                            || item.evetype === 'Questionnaire' || item.evetype === 'TreasureChest')">互动模板：{{item.tipText}}</view>
                                  </view>
                              </view>
                          </view>
                      </item>
                  </list>
              </link-radio-group>
          </template>
      </link-auto-list>
      <link-sticky>
          <link-button block mode="stroke" @tap="lastStep">上一步</link-button>
          <link-button block @tap="nextStep('next')">下一步</link-button>
      </link-sticky>
      <link-dialog ref="tempUpdateConfirm">
          <view slot="head">
              提示
          </view>
          <view>
              已存在编辑保存的互动信息，重新选择营销模板会失效已保存的互动信息，是否确定重新选择营销模板？
          </view>
          <link-button slot="foot" @tap="cancel">取消</link-button>
          <link-button slot="foot" @tap="associatedUpdateTemp">确定</link-button>
      </link-dialog>
  </link-page>
</template>
<script>
export default {
    name: 'marketing-activity-model-list-page',
    data () {
        const userInfo = this.$taro.getStorageSync('token').result;         // 获取用户信息
        const actIndeSourCode = this.pageParam.data.actIndeSourCode;
        const actIndeSourCodeDeal = `[${actIndeSourCode}]`;//处理的活动类型-格式为"[PinJianHui]"
        const activityItem = this.pageParam.data;
        //1、市场活动新建 newMarketActivity 2、执行案新建市场活动 caseNewMarketActivity 3、其他 other
        let sceneSourceForNavigation = "other";//默认other
        if (!this.$utils.isEmpty(this.pageParam.sceneSourceForNavigation)) {
            sceneSourceForNavigation = this.pageParam.sceneSourceForNavigation;
        }
        return {
            pageFrom: '',
            chooseData: {},
            type: '',
            sceneSourceForNavigation,
            activityItem,
            currentTemplateId: '',
            currentTemplate: {activityName: ''},
            userInfo,
            autoList: new this.AutoList(this, {
                module: this.$env.appURL + '/marketactivity/link/marketActivity',
                url: {
                    queryByExamplePage: this.$env.appURL + '/marketactivity/link/marketActivity/queryByExampleForQwPage'
                },
                sortOptions: null,
                param: {
                    rows: 25,
                    sort: 'lastUpdated',
                    attr3: 'subOrg',
                    attr6: 'range',
                    orgId: userInfo.orgId,
                    type: 'Template',
                    // activeFlg: 'Y',
                    filtersRaw: [
                        {id: 'type', property: 'type', value: 'Template', operator: '='},
                        {id: 'actType', property: 'activityType', value: actIndeSourCodeDeal, operator: 'IN'},
                        {id: 'activeFlg', property: 'activeFlg', value: 'Y', operator: '='}
                    ]
                },
                // request: ({url, param}) => {
                //     param.filtersRaw = JSON.stringify(param.filtersRaw)
                //     const data = this.$httpForm.post(url, param)
                //     return data;
                // },
                hooks: {
                    afterLoad (data) {
                        data.rows.forEach((item)=> {
                            item.actJson = JSON.parse(item.actJson);
                        })
                    }
                }
            }),
            currentClickItemId: ''              //  当前点击的id，用于判断多次点击同一个item
        }
    },
    onShow () {
        this.currentTemplateId = this.$store.getters['macTemplate/getMacTemplateId'] || '';
        this.type = '';
    },
    created() {
        this.currentTemplate.activityName = this.pageParam.templateName || '';
        this.pageFrom = this.pageParam.pageFrom || '';
    },
    methods: {
        /**
         * @desc 取消选择
         * <AUTHOR>
         * @date 2023/5/8 14:17
         **/
        cancel() {
            this.currentTemplateId = this.$store.getters['macTemplate/getMacTemplateId'];
            this.$refs.tempUpdateConfirm.hide();
        },
        /**
         * 确认更新营销模板时
         * 1、更新活动上营销模板信息
         * 2、失效之前营销模板下的活动信息
         * */
        async associatedUpdateTemp() {
            this.$refs.tempUpdateConfirm.hide();
            this.currentTemplate = this.chooseData;
            this.currentTemplateId = this.chooseData.id;
        },
        /**
         * @desc 上一步直接返回
         * <AUTHOR>
         * @date 2023/4/24 14:19
         **/
        lastStep() {
            this.$nav.back();
        },
        /**
         * @desc 选择模板
         * <AUTHOR>
         * @date 2023/4/24 14:21
         * @param  data 选中的模板行
         **/
        chooseTemplate (data) {
            // 多次点击同一个
            if (this.currentClickItemId === data.id) {
                this.currentTemplate = data;
                this.currentTemplateId = data.id;
                return;
            }
            this.currentClickItemId = data.id;
            const tempId = this.$store.getters['macTemplate/getMacTemplateId']
            this.type = 'newTemplate';
            if (this.currentTemplateId === data.id) {
                this.type = 'sameTemplate';
            }
            if (!this.$utils.isEmpty(tempId) && data.id !== tempId) {
                this.chooseData = data;
                this.$refs.tempUpdateConfirm.show();
                return
            }
            this.currentTemplate = data;
            this.currentTemplateId = data.id;
        },
        /**
         * @desc 点击下一步跳转互动活动列表页
         * <AUTHOR>
         * @date 2023/4/24 14:19
         **/
        async nextStep () {
            let operant = '';
            if (this.pageFrom === 'MarketingActivityItem') {
                operant = 'UPDATE';
            }
            if (this.type === 'sameTemplate' || (this.$utils.isEmpty(this.type) && !this.$utils.isEmpty(this.currentTemplateId) && this.currentTemplateId === this.$store.getters['macTemplate/getMacTemplateId'])) {
                this.activityItem['actJson'] = JSON.stringify(this.currentTemplate['actJson']);
                this.$nav.push('/pages/lj-market-activity/market-activity/interactive-configuration-page', {
                    data: this.activityItem,
                    currentTemplate: this.currentTemplate,
                    pageFrom: 'MarketingActivityModel',
                    operant: operant
                });
                return;
            }
            if (this.$utils.isEmpty(this.currentTemplateId)) {
                this.$nav.push('/pages/lj-market-activity/market-activity/new-market-activity-other-info-page', {
                    data: this.activityItem,
                    sceneSourceForNavigation: this.sceneSourceForNavigation
                })
            } else {
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/marketActivity/quickCreateInteractions', {
                    marketTemplateId: this.currentTemplate.id,
                    id: this.activityItem.id
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.currentTemplate = {};
                        this.currentTemplateId = '';
                        this.$showError(`模板初始化失败：${response.result}`);
                    }
                })
                if (data.success) {
                    this.$utils.hideLoading();
                    if (this.pageFrom === 'MarketingActivityItem') {
                        this.$bus.$emit('refreshInteractionItem');
                    }
                    this.activityItem['actJson'] = JSON.stringify(this.currentTemplate['actJson']);
                    this.$store.commit('macTemplate/setMacTemplateId', this.currentTemplateId);
                    this.$nav.push('/pages/lj-market-activity/market-activity/interactive-configuration-page', {
                        data: this.activityItem,
                        currentTemplate: this.currentTemplate,
                        pageFrom: 'MarketingActivityModel',
                        operant: operant
                    });
                }
            }
        }
    }
}
</script>
<style lang="scss">
.marketing-activity-model-list-page{
    .base {
        padding: 24px 24px 0;
        overflow: hidden;
        /*deep*/ .link-item{
        border-radius: 12px;
    }
    }
    .marketing-activity-model-list{
        background: #FFFFFF;
        width: 95%;
        margin: 24px auto auto auto;
        border-radius: 16px;
    }
    .item-container{
        height: 280px;
        width: 100%;
        .title-line{
            display: flex;
            justify-content: space-between;
            height: 60px;
            border-bottom: solid 4px #f2f2f2;
            align-items: baseline;
            width: 100%;
            .marketing-activity-name{
                font-size: 28px;
                color: #333333;
            }
        }
        .dot-left {
            position: absolute;
            left: -10px;
            top: 76px;
            background-color: #f2f2f2;
            width: 24px;
            height: 24px;
            border-radius: 100%
        }

        .dot-right {
            position: absolute;
            right: -10px;
            top: 76px;
            background-color: #f2f2f2;
            width: 24px;
            height: 24px;
            border-radius: 100%
        }
        .model-content {
            height: 150px;
            border-bottom: solid 4px #f2f2f2;
            top: 88px;
            bottom: 88px;
            width: 100%;
            overflow-y: auto;
            font-size: 28px;
            color: #333333;
            padding: 20px 0;
        }
    }
}
</style>
