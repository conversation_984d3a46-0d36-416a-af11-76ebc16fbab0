# 市场活动-字段赋值
```
创建时间：2022/02/15 10:15
创建人：  宋燕荣
```
##### 2.3 市场活动重要字段赋值逻辑
ps：没有特别说明的picklist数据选择都是来自企微本系统
* 1、 省ID、省名称、市ID、市名称、区县ID、区县名称
```
市场活动的地址信息：基本展示之外，稽核人员查询市场活动数据和云空间数据的安全性根据地址信息做安全性
2种地址信息赋值：
    i：选择受益对象(选客户：客户大类为经销商、分销商、终端)时带出省名称、市名称、区县名称。
        根据省、市、区县名称调用接口'/action/link/alladdress/queryIdByName'查询地址的对应ID。
        将省ID、省名称、市ID、市名称、区县ID、区县名称赋值到市场活动对象上
    ii：维护费用信息时带出执行案明细上的地址信息将省ID、省名称、市ID、市名称、区县ID、区县名称赋值到市场活动对象上
```
* 2、 公司ID、销售大区ID、销售大区名称、销售片区ID、销售片区名称、销售城市ID、销售城市名称、销售区县ID、销售区县名称
```
市场活动新建时默认赋值：当前用户coreOrganizationTile对象中的组织层级数据（coreOrganizationTile对象的内容可以找技术经理要组织平铺表层级各字段对应含义截图）
```
* 3、 执行人、执行人电话
```
活动新建时默认当前用户的名称和联系电话
```
* 4、 活动状态、活动阶段、审批状态、稽核状态、目标人群
```
活动新建时活动状态默认新建
活动新建时活动阶段默认活动提报
活动新建时审批状态默认未提交
活动新建时稽核状态默认未稽核
活动新建时目标人群默认'OrdinaryConsumers'普通消费者
活动新建时国家默认中国，值通过值列表获取
```
* 5、 无执行案说明
```
类型：文本字段，手动输入
特殊逻辑：执行案编码和无执行案说明二者必有其一，如果活动创建时不选择执行案编码那么无执行案说明必输。
```
* 6、 子公司/经销商
```
类型：picklist，选对象
数据来源：客户
查询接口：action/link/accnt/querySunCompByCityOrgPage
查询条件：
    i：查询客户表上客户大类【ACCT_TYPE】=【Dealer】经销商
    ii：客户状态为'Y'
    iii：当前市场活动的salesCityId和companyId限制数据范围，查询当前公司某个销售城市的客户经销商数据
字段赋值：
    i、客户的赋值 billTitle 赋值市场活动的 actExecutivesName
    ii、客户ID给市场活动的子公司/经销商ID
```
* 7、 费用垫付对象
```
仅展示
数据来源：活动费用选择执行案明细时从执行案明细带出赋值给市场活动
```
* 8、受益对象
```
类型：picklist，选对象
数据来源：客户
查询接口：action/link/accnt/queryAccntByCityOrgPage
查询条件：
    i：查询客户表上客户大类【ACCT_TYPE】=【Dealer】经销商&【Distributor】 分销商&【Terminal】  终端的数据
    ii：客户状态为'Y'
    iii：当前市场活动的salesCityId和companyId限制数据范围，查询当前公司某个销售城市的客户数据
    iiii：查看系统配置参数，配置情况下查询multiAcctMainFlag为Y的数据
特殊取值：
    i、当客户大类=【Dealer】&【Distributor】时，企微前端查询出来显示在列表的数据及选择后显示在的活动头上的数据，取客户的营业执照名称【billTitle 】显示
    ii、当客户大类=【Terminal】时，企微前端查出来显示在列表的数据及选择后显示在活动头上的数据，取客户的门头店招名称【acctName】；
    iii、根据客户上的省名称、市名称、区县名称调用接口查询地址的ID。将对应的省ID、省名称、市ID、市名称、区县ID、区县名称赋值给市场活动。
字段赋值：
    i、客户名称赋值为受益对象名称（当客户大类=【Dealer】&【Distributor】时，企微前端查询出来显示在列表的数据及选择后显示在的活动头上的数据，取客户的营业执照名称【billTitle 】显示）
    ii、客户ID赋值受益对象ID
    iii、选择受益对象(选客户：客户大类为经销商、分销商、终端)时带出省名称、市名称、区县名称。根据省、市、区县名称调用接口'/action/link/alladdress/queryIdByName'查询地址的对应ID。将省ID、省名称、市ID、市名称、区县ID、区县名称赋值到市场活动对象上
```
* 9、陈列品项
```
类型：值列表字段
特殊处理：市场活动只选择选择产品中类时需要带出对应的产品大类。产品大类和产品中类为级联关系。
```
* 10、协议
```
类型：picklist，选对象
数据来源：合同 /agreement
查询接口：action/link/agreement/queryByExamplePage
查询条件：
    i：filtersRaw: [{id: 'agrType', property: 'agrType', value: '[display,storeSign,Cabinet]', operator: 'IN'},]
    ii：安全性字段 this.$utils.isPostnOauth() === 'MY_POSTN'? 'MULTI_POSTN': this.$utils.isPostnOauth();//查询协议安全性调整
特殊逻辑：可以快捷新建协议，先选择协议类型（display 陈列协议 Cabinet 酒柜协议 signboard 门头协议 others 其他协议）后跳转页面维护数据
字段赋值：带出协议ID protocolId 、协议名称 protocolName 、协议类型 protocolType 、协议开始时间 protocolStartTime 、协议结束时间 protocolEndTime
```
* 11、宴席买家
```
类型：picklist，选对象
数据来源：老窖-智玲的消费者数据
查询接口： module: this.$env.appURL + '/action/link/consumer',
查询条件：
    i：filtersRaw: [
          {id: 'consumerType', property: 'consumerType', value: 'ChannelConsumer', operator: '='},
          {id: 'accntChannel', property: 'accntChannel', value: 'MarketingPlatform', operator: '='},
          {id: 'empFlag', property: 'empFlag', value: 'N', operator: '='}
      ],
    ii：oauth: 'MY_ORG',
字段赋值：宴席买家ID masterId = 消费者ID，宴席买家名称 masterName = acctNameSecret
```
* 12、活动类型
```
类型： 值列表
```
* 13、活动场地
```
类型：picklist，选对象
查询接口：action/link/accnt/queryAccntByCityOrgPage
查询条件：
    i、filtersRaw: [
         //是否品鉴会基地
         {id: 'appreciationFlag', property: 'appreciationFlag', value: 'Y', operator: '='},
         {id: 'acctStatus', property: 'acctStatus', value: 'Y', operator: '='},
     ],
    ii、当活动的 销售片区ID=系统参数配置里的ID时 attr2取活动销售片区的ID 否则取活动销售城市的ID
字段赋值：活动场地id、活动场地编码、场地地址、场地地址、活动场地经度、活动场地纬度、酒店电话
特殊逻辑：可以快捷新建活动场地
    新建默认赋值：
        i、appreciationFlag = 'Y'
        ii、客户大类默认-供应商
        iii、客户中类默认-活动场地 Venue 值列表 ACCNT_CATEGORY
```
* 14、场地地址
```
仅展示、选活动场地带出
```
* 15、供应商
```
类型：picklist，选对象
查询接口：action/link/accnt/queryAccntByCityOrgPage
查询条件：
    param: {
                        filtersRaw: [
                            {
                                "id": "acctType",
                                "property": "acctType",
                                "value": "Supplier"
                            },//状态
                            {
                                id: 'acctStatus',
                                property: 'acctStatus',
                                value: 'Y',
                                operator: '='
                            },],
                        attr2: option.formData.salesCityId,
                        isCompanyCostFlag: 'N',
                        acctType: 'Supplier'
                    }
赋值逻辑：供应商ID = 客户ID 供应商名称 = acctName
```
* 16、宴席推荐终端
```
类型：picklist，选对象
查询接口：action/link/accnt/queryFollowUpTerminal
查询条件：
    i、市场活动的销售城市限制数据选择
        param: {
                          attr1: option.formData.salesCityId,
                          attr3: 'Dealer,Terminal,Distributor'
                      },
    ii、查看系统配置参数，配置情况下查询multiAcctMainFlag为Y的数据
特殊逻辑：
    i、当客户大类=【Dealer】&【Distributor】时，企微前端查询出来显示在列表的数据及选择后显示在的活动头上的数据，取客户的营业执照名称【billTitle 】显示
    ii、当客户大类=【Terminal】时，企微前端查出来显示在列表的数据及选择后显示在活动头上的数据，取客户的门头店招名称【acctName】；
```
* 17、销售城市
```
类型：picklist 选对象
查询接口：module: 'action/link/orgnization',
查询条件：
    param: {
                        filtersRaw: [{
                            id: 'orgType',
                            property: 'orgType',
                            operator: '=',
                            value: 'SalesCity'
                        },{
                            id: 'id',
                            property: 'isEffective',
                            operator: '=',
                            value: 'Y'
                        }],
                        attr2: option.formData.salesRegionId //销售片区id限制
                    },
特殊逻辑：
    i、新建时可以编辑销售城市，活动保存之后就不允许改了
    2、切换销售城市需清掉活动头上的销售区县ID 名称、活动场地id 名称 编码、场地地址、经度、维度、酒店电话、子公司/经销商id 名称、受益对象ID 名称、购买产品id 名称
```
* 18、销售区县
```
类型：picklist 选对象
查询接口：module: 'action/link/orgnization',
查询条件：
        param: {
                    filtersRaw: [{
                        id: 'orgType',
                        property: 'orgType',
                        operator: '=',
                        value: 'SalesArea'
                    },{
                        id: 'id',
                        property: 'isEffective',
                        operator: '=',
                        value: 'Y'
                    }],
                    attr2: option.formData.salesCityId //销售城市id限制
                },
特殊逻辑：
    i、新建时可以编辑销售区县，活动保存之后就不允许改了
```
* 19、购买产品
```
类型：picklist 选对象
查询接口：action/link/product/queryPriceListProdByOrgIdPage
查询条件：
        param: {
                    attr2: option.formData.salesCityId
                },
字段赋值：购买产品ID、购买产品名称
```
* 20、活动目标人群
```
类型：picklist 选对象
特殊逻辑：
    根据公司id查询带出默认的忠诚度计划
        (1) 如获取到信息忠诚度计划信息，则将忠诚度计划赋值到忠诚度计划字段上，同时前端将字段忠诚度计划、会员等级、邀约人字段显示出来，忠诚度计划字段不允许修改；
        (2）如查不到忠诚度计划，弹窗告诉他“提示该公司没有会员体系，无法创建会员活动，目标人群将更新为普通消费者”，只给一个确认按钮，点确认则更新目标人群为普通消费者OrdinaryConsumers；
        (3）反之当用户主动选择普通消费者时，则不需要掉接口也不需要展示后续忠诚度计划、会员等级、邀约人字段给用户填写；
        (4) 其余逻辑请查看name: "new-activity-basic-page"页面的pickTargetPopulationChange()
```
* 21、忠诚度
```
仅展示，活动目标人群带出
```
* 22、会员等级
```
类型：picklist 选对象
特殊逻辑：处理逻辑比较多 具体看pickMemberTier方法 这里不赘述
```
* 23、邀约人
```
类型：picklist 选对象
查询接口：this.$env.appURL + '/loyalty/loyalty/member/queryByExamplePage'
查询条件：
            param: {
                    filtersRaw: [{id: 'empFlag', property: 'empFlag', value: 'Y'}]
                }
字段赋值：邀约人人ID 邀约人名称（选择对象的名称||电话）
```
* 24、对接人
```
类型：mvg 选对象
特殊逻辑：请查看name: "new-activity-basic-page"页面的 pickActName()
```
* 25、提报名额
```
仅展示
```
* 26、执行案编码
```
类型：picklist 选对象
特殊逻辑：
    1、费用实际时 releaseFeeFlag ：是否释放申请费用占用执行案明细的金额。Y的时候 不允许编辑
    2、!((状态=新建 && 审批状态=新建||已拒绝) || (状态=进行中||执行结束||已发布)&&(审批状态=申请审批通过||反馈驳回||反馈撤回))不允许操作。反之可以操作。
    3、其他特殊逻辑请查看name: "new-activity-basic-page"页面的 pickExeCaseCode()
    4、清除执行案编码：
        i、非执行反馈环节
        ii、!((状态=新建 && 审批状态=新建||已拒绝) || (状态=进行中||执行结束||已发布)&&(审批状态=申请审批通过||反馈驳回||反馈撤回))不允许操作。反之可以操作。
        iii、清除执行案编码，将同步清掉该活动下已关联了相关执行案明细的费用申请明细及活动上关联信息
字段赋值：
    1、选择执行案编码：带出执行案编码、执行案id、执行案名称、执行方案描述
    2、清除执行案：后台处理获取接口返回的最新对象使用。
```
* 27、业务场景
```
类型: 值列表 多选
值列表类型：父值列表 LNK_AUTO_TEMPLATE_TYPE ，父值列表类型值'businessScenario'， 值列表 TMPL_SUB_BIZ_TYPE。
特殊逻辑： 1、日常可以在多选的界面勾选或者去掉已选，字段栏也可以点击X清空业务场景字段。
            说明：业务场景关系到市场活动执行反馈阶段的图片上传，不同财年使用的业务场景可能不同。
                 选择业务场景时只可以选择生效且使用的，但历史市场活动数据选择的失效的业务场景值也需要转化出来，清除业务场景时会提示用户"***当前已取消应用，删除后只能选择本财年有效应用的业务场景进行执行反馈，是否确认删除"
```
* 28、剩余其他字段走配置 详情见 src/pages/lj-market-activity/market-activity/new-activity-basic-jsx-component.tsx
说明：市场活动新加字段（非配置）需要额外处理的字段需要在这俩个界面都加。
```
1、src/pages/lj-market-activity/market-activity/new-activity-basic-page.vue
2、src/pages/lj-market-activity/perform-case/new-activity-page.vue
为什么不共用？查看市场活动-Q&A.md
```
------ 市场活动-字段赋值逻辑-内容结束 ------
