<template>
    <link-page class="white-or-black-edit-page">
        <!--顶部-->
        <lnk-taps :taps="tapsOptions" v-model="tapsActive" @switchTab="switchTab" :wordLength="187.5"></lnk-taps>
        <view style="margin-top: 44px;">
            <view class="tips-info">说明：当白名单人数超过奖项数量时，白名单人员先参与抽奖先中奖</view>
            <view class="head-title">奖项明细及白名单</view>
            <view v-for="(item, index) in prizeList" :key="index" class="prize-list" :id="'award' + index">
                <line-title :title="item.awardName + '白名单'"/>
                <view class="prize-item">
                    <view class="name">{{item.name}}</view>
                    <view class="win-qty">{{item.winQty}}</view>
                    <view class="add-list" v-if="editFlag" @tap="addWhiteOrBlackList('whiteList', item)">添加白名单</view>
                </view>
                <view v-if="item['whiteList'].length > 0">
                    <view class="list-item" v-for="(whiteItem, whiteIndex) in item['whiteList']" :key="whiteIndex">
                        <link-swipe-action>
                        <item class="list-item-content" :arrow="false">
                                <view class="media-list">
                                    <view class="media-list-body" style="width: 70%">
                                        <view class="media-list-text-top">{{whiteItem.acctName}}</view>
                                        <view class="media-list-text-bottom">单位：{{whiteItem.company}}</view>
                                    </view>
                                    <view class="media-list-right">{{whiteItem.phone}}</view>
                                </view>
                        </item>
                            <link-swipe-option slot="option" v-if="editFlag" @tap="deleteBlackOrWhiteList(whiteItem,'whiteList', item)">删除</link-swipe-option>
                        </link-swipe-action>
                    </view>
                    <view class="load-more" v-if="item['whiteTotal'] > 10" @tap="loadMore('whiteList', item)">{{ item['whiteTotal'] > item['whiteList'].length ? '加载更多' : '加载完成' }}</view>
                </view>
            </view>
            <view class="head-title"><view>黑名单</view><view class="edit"  v-if="editFlag" @tap="addWhiteOrBlackList('blackList')">添加</view></view>
            <view class="black-list">
                <view class="list-item" v-for="(blackItem, blackIndex) in blackList" :key="blackIndex">
                    <link-swipe-action>
                        <item class="list-item-content" :arrow="false">
                            <view class="media-list">
                                <view class="media-list-body" style="width: 70%">
                                    <view class="media-list-text-top">{{blackItem.acctName}}</view>
                                    <view class="media-list-text-bottom uni-ellipsis">单位：{{blackItem.company}}</view>
                                </view>
                                <view class="media-list-right">{{blackItem.phone}}</view>
                            </view>
                        </item>
                        <link-swipe-option slot="option" v-if="editFlag" @tap="deleteBlackOrWhiteList(blackItem,'blackList')">删除</link-swipe-option>
                    </link-swipe-action>
                </view>
                <view class="load-more" v-if="blackListTotal > 10" @tap="loadMore('blackList')">{{ blackListTotal>blackList.length ? '加载更多' : '加载完成' }}</view>
            </view>
        </view>
    </link-page>
</template>

<script>
import LnkTaps from "../../../core/lnk-taps/lnk-taps";
import LineTitle from "../../../lzlj/components/line-title";
export default {
    name: "white-or-black-edit-page",
    components: {LineTitle, LnkTaps},
    data() {
        const drawingAlgorithm = this.pageParam.data.drawingAlgorithm;
        const interactionId = this.pageParam.data.interactionId;
        const marketActivityId = this.pageParam.data.marketActivityId;
        const marketStatus = this.pageParam.data.marketStatus;
        let editFlag = false
        if ((marketStatus === 'Published' || marketStatus === 'Processing') && drawingAlgorithm !== 'ByAwardNumber') {
            editFlag = true
        }
        return {
            whiteGroupId: '',
            editFlag,
            marketStatus,
            page: 1,
            blackListTotal: 0,
            tapsActive: {},
            tapsOptions: [],
            currentIndex: 0,
            scrollTop: 0,
            marketActivityId,
            interactionId,
            prizeList: [],
            blackList: [],
            whiteListGroup: [],                      // 所有白名单分组
            whiteListGroupId: '',
            blacklistGroupId: '',
            consumerOption: new this.AutoList(this, {
                module: this.$env.appURL + '/interaction/link/activityCrowd',
                url: {
                    queryByExamplePage: this.$env.appURL + '/interaction/link/activityCrowd/queryMarketInfoByExamplePage'
                },
                param: {
                    interactionId: '',
                    filterFlag: 'Y',
                    whiteGroupId: '',
                    filtersRaw: []
                },
                hooks: {
                    beforeLoad (options) {
                        if (options.param.filtersRaw.length === 0) {
                            options.param.filtersRaw = [{id: 'mcActId', property: 'mcActId', value: this.marketActivityId}];
                        } else {
                            options.param.filtersRaw.forEach((item) => {
                                if (item.property === 'mcActId') {
                                    item.value = this.marketActivityId;
                                }
                            })
                        }
                        if (!!this.whiteGroupId) {
                            options.param['whiteGroupId'] = this.whiteGroupId;
                        }
                        if (this.interactionId) {
                            options.param['interactionId'] = this.interactionId;
                        }
                    }
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} arrow="false">
                            <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                            <view
                                style="width:100%">
                                <view
                                    style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px;padding-bottom: 10px;">
                                    {data.acctName}
                                </view>
                                <view
                                    style="display:flex;font-family: PingFangSC-Regular;font-size: 12px;color: #262626;letter-spacing: 0;line-height: 14px;padding-bottom: 10px;">
                                    <view style="color: #8C8C8C;min-width: 60px;">手机号</view>
                                    {data.phone}
                                </view>
                                <view
                                    style="display:flex;font-family: PingFangSC-Regular;font-size: 12px;color: #262626;letter-spacing: 0;line-height: 14px;">
                                    <view style="color: #8C8C8C;min-width: 60px;">负责人</view>
                                    {data.saleman}
                                </view>
                            </view>
                        </item>
                    )
                }
            })
        }
    },
    async created () {
        await this.getBlackOrWhiteListGroupId();
        await this.initPrizeSet();
        await this.initBlackOrWhiteList('blackList');
    },
    methods: {
        /**
         * @desc 加载更多
         * <AUTHOR>
         * @date 2022/6/17 11:50
         **/
        async loadMore (type, item) {
            if (type === 'whiteList') {
                if (item['whiteTotal'] === item['whiteList'].length) return;
                item['whitePage']++;
            } else {
                if (this.blackListTotal === this.blackList.length) return;
                this.page++;
            }
            await this.initBlackOrWhiteList(type, item, 'loadMore')
        },
        /**
         * @desc 切换
         * <AUTHOR>
         * @date 2022/6/17 11:41
         **/
        switchTab(val) {
            this.tapsActive = val;
            wx.pageScrollTo({
                selector: `#${val.val}`,
                duration: 500
            })
        },
        /**
         * @desc 初始化黑白名单数据
         * <AUTHOR>
         * @date 2022/6/15 15:42
         **/
        async initBlackOrWhiteList (type, item, loadType) {
            let groupId = '';
            let page = 1;
            if (type === 'whiteList') {
                groupId = item['groupId'];
                page = item['whitePage']
            } else {
                page = this.page;
                groupId = this.blacklistGroupId;
            }
            const data = await this.$http.post(this.$env.appURL + '/interaction/link/activityCrowd/queryByExamplePage', {
                filtersRaw: [{id: 'groupId', property: 'groupId', value: groupId}, {id: 'mcActId', property: 'mcActId', value: this.marketActivityId}],
                page: page,
                rows: 10,
                pageFlag: true
            });
            if (type === 'whiteList') {
                if (loadType === 'loadMore') {
                    item['whiteList'] = item['whiteList'].concat(data.rows);
                } else {
                    item['whiteList'] = data.rows;
                }
                item['whiteTotal'] = data.total;
            } else {
                if (loadType === 'loadMore') {
                    this.blackList = this.blackList.concat(data.rows);
                } else {
                    this.blackList = data.rows;
                }
                this.blackListTotal = data.total;
            }
        },
        /**
         * @desc 获取黑白名单分组
         * <AUTHOR>
         * @date 2022/6/15 17:54
         **/
        async getBlackOrWhiteListGroupId() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/interaction/link/consumerGroup/queryByExamplePage', {
                    marketActivityId: this.marketActivityId,          // 营销活动id
                    interactionId: this.interactionId                // 互动活动id
                });
                if (data.success) {
                    const whiteListGroup = data.rows.filter((item) => !!item.prizeSetId);
                    const blackList = data.rows.filter((item) => !item.prizeSetId);
                    this.whiteListGroup = whiteListGroup.map((item) => ({
                        val: item.id,
                        name: item.groupName,
                        prizeSetId: item.prizeSetId
                    }));
                    this.blacklistGroupId = blackList[0].id;
                } else {
                    this.$showError('获取分组信息失败:' + data.result);
                }
            } catch (e) {
                this.$showError('获取分组信息出错' );
            }
        },
        /**
         * @desc 删除黑白名单数据
         * <AUTHOR>
         * @date 2022/6/17 10:13
         **/
        async deleteBlackOrWhiteList (row, type, item) {
            let msg = '', url = '', param;
            if (type === 'whiteList') {
                msg = '白名单';
                url = '/interaction/link/activityCrowd/batchDeleteWhiteList';
                param = [{
                    interCustId: row.interCustId,
                    interactionId: this.interactionId,
                    mcActId: this.marketActivityId
                }]
            } else {
                msg = '黑名单';
                url = '/interaction/link/activityCrowd/deleteBySelected';
                param = {
                    interCustId: row.interCustId,
                    interactionId: this.interactionId,
                    groupId: this.blacklistGroupId
                }
            }
            this.$utils.showLoading();
            const data = await this.$http.post(this.$env.appURL + url, param, {
                autoHandleError: false,
                    handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError(`取消${msg}失败:` + response.result);
                }});
            if (data.success) {
                this.$utils.hideLoading();
                this.page = 1;
                if (!this.$utils.isEmpty(item)) {
                    item['whitePage'] = 1;
                }
                await this.initBlackOrWhiteList(type, item);
                this.$message.success(`取消${msg}成功！`);
            }
        },
        /**
         * @desc 添加黑白名单
         * <AUTHOR>
         * @date 2022/6/15 15:21
         **/
        async addWhiteOrBlackList (type, item) {
            const that = this;
            let msg = ''
            let selectedList = []
            if (type === 'whiteList') {
                const groupData = this.whiteListGroup.filter((whiteGroupItem)=>whiteGroupItem.prizeSetId === item.id);
                this.groupId = groupData[0].val;
                msg = '白名单';
                selectedList = item['whiteList']
                this.whiteGroupId = this.groupId;
            } else {
                this.whiteGroupId = '';
                this.groupId = this.blacklistGroupId;
                msg = '黑名单';
                selectedList = this.blackList;
            }
            const list = await this.$object(this.consumerOption, {
                pageTitle: '消费者',
                showInDialog: true,
                multiple: true
            });
            let consumerDataList = [];
            consumerDataList = list.map(i => ({
                interCustId: i.id,
                groupId: this.groupId,
                interactionId: this.interactionId
            }));
            if (type === 'blackList') {
                await that.$taro.showModal({
                    title: '提示',
                    content: '当前选中消费者将不再参与本次活动抽奖！',
                    success: async (res) => {
                        if (res.confirm) {
                            await that.insertWhiteOrBlackList(consumerDataList, msg);
                            await that.initBlackOrWhiteList(type, item);
                        }
                    }
                });
            } else {
                await this.insertWhiteOrBlackList(consumerDataList, msg);
                await this.initBlackOrWhiteList(type, item);
            }
        },
        /**
         * @desc 初始化奖项列表
         * <AUTHOR>
         * @date 2022/6/15 11:06
         **/
        async initPrizeSet() {
            const prizeData = await this.$http.post(this.$env.appURL + '/interaction/link/prizeSet/queryByExamplePage', {
                filtersRaw: [
                    {id: 'interactionId', property: 'interactionId', value: this.interactionId}
                ],
                oauth: 'ALL',
                pageFlag: true,
                rows: 50
            });
            if (prizeData.rows.length > 0) {
                let prizeListData = prizeData.rows.filter(item=> item.awardType !== 'NotWinning')
                prizeListData.forEach((item, index)=> {
                    const whiteGroupData = this.whiteListGroup.filter((whiteItem) => whiteItem.prizeSetId === item.id);
                    this.$set(item, 'groupId', whiteGroupData[0].val);
                    this.$set(item, 'whitePage', 1);
                    this.$set(item, 'whiteList', []);
                    this.initBlackOrWhiteList('whiteList', item);
                    this.tapsOptions.push({name: (item.awardName.length > 7 ? ((item.awardName.substring(0,7) + '...') + '白名单') : (item.awardName + '白名单')), seq: index, val: 'award' + index})
                })
                this.tapsOptions.push({name: '黑名单', val: 'blackList', key: (prizeData.rows.length - 1)});
                this.tapsActive = this.tapsOptions[0]
                this.prizeList = prizeListData;
            }
        },
        /**
         * @desc 添加黑白名单数据
         * <AUTHOR>
         * @date 2022/6/17 10:21
         **/
        async insertWhiteOrBlackList (param, msg) {
            this.$utils.showLoading();
            const data = await this.$http.post(this.$env.appURL + '/interaction/link/activityCrowd/batchInsert', param, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError(`添加${msg}失败:` + response.result);
                }});
            if (data.success) {
                this.$utils.hideLoading();
                this.$message.success(`添加${msg}成功！`);
            }
        },
    }
}
</script>

<style lang="scss">
.white-or-black-edit-page {
    .title, .prize-item, .black-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 28px;
        background-color: white;
        border-bottom: 1px solid #efefef;
        font-size: 28px;
        .add-list{
            color: #2F61F8;
        }
        .name {
            width: 45%;
        }
    }
    .tips-info{
        font-size: 24px;
        width: 96%;
        color: #333333;
        background: wheat;
        padding: 2%;
    }
    .head-title{
        font-weight: bold;
        font-size: 36px;
        padding: 3% 2% 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .edit{
            font-size: 28px;
            color: #2F69F8;
            letter-spacing: 0;
            line-height: 28px;
        }
    }
    .prize-list {
        margin: 20px 0;
    }
    .prize-item {
        margin-top: 20px;
    }
    .black-list {
        margin-top: 20px;
    }
    .list-item {
        background-color: white;
        border-bottom: 1px solid #efefef;
    }
    .list-item-content {
        position: relative;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .media-list {
            padding: 11px 15px;
            box-sizing: border-box;
            display: flex;
            width: 100%;
            flex-direction: row;
            justify-content: space-between;

            .media-list-logo {
                height: 94px;
                width: 94px;
                margin-right: 20px;

                image {
                    height: 100%;
                    width: 100%;
                }
            }

            .media-list-body {
                display: flex;
                flex: 1;
                flex-direction: column;
                justify-content: space-between;
                align-items: flex-start;
                overflow: hidden;

                .media-list-text-top {
                    width: 100%;
                    line-height: 60px;
                    font-size: 30px;
                    color: #262626;
                }

                .media-list-text-bottom {
                    width: 100%;
                    line-height: 40px;
                    font-size: 26px;
                    color: #8f8f94;
                }
            }
        }
    }
    .load-more{
        width: 100%;
        text-align: center;
        color: #6D96FA;
        margin-top: 32px;
        font-size: 24px;
        padding-bottom: 32px;
    }
}
</style>
