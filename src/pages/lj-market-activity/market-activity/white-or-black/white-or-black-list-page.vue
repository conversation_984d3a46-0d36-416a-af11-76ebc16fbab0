<template>
    <link-page class="white-or-black-list-page">
        <view :class="classifyItemList.length > 0 ? 'classify-height' : 'classify'">
            <lnk-taps :taps="classifyList" v-model="classifyListActive" @switchTab="switchTab"></lnk-taps>
            <lnk-taps :taps="classifyItemList" v-model="classifyItemListActive" @switchTab="switchTabItem" :wordLength="187.5" v-if="classifyItemList.length > 0" :marginTop="true"></lnk-taps>
        </view>
        <link-auto-list :option="autoList" hideCreateButton>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="account-list-item">
                    <view class="account-list" slot="note">
                        <view class="list-cell">
                            <view class="media-list">
                                <view class="media-list-body" style="width: 70%">
                                    <view class="media-list-text-top">{{data.acctName}}</view>
                                    <view class="media-list-text-bottom uni-ellipsis">负责人：{{data.phone}}</view>
                                    <view class="media-list-text-bottom uni-ellipsis">单位：{{data.company}}</view>
                                </view>
                                <view class="media-list-right">
                                    {{data.groupName}}
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
        <link-fab-button v-if="marketStatus === 'Published' || marketStatus === 'Processing'" @tap="editWhiteOrBlackItem('new')"></link-fab-button>
    </link-page>
</template>

<script>
import LnkTaps from "../../../core/lnk-taps/lnk-taps";

export default {
    name: "white-or-black-list-page",
    components: {LnkTaps},
    data () {
        const marketActivityId = this.pageParam.data.marketActivityId;
        const marketStatus = this.pageParam.data.marketStatus;
        const interactionId = this.pageParam.data.interactionId;
        return {
            wordLength: 0,
            whiteListGroup: [],
            whiteListGroupId: '',
            interactionId,
            marketActivityId,
            marketStatus,
            groupId: '',
            blacklistGroupId: '',                       // 黑名单分组信息
            autoList:  new this.AutoList(this, {
                module: this.$env.appURL + '/interaction/link/activityCrowd',
                exactSearchFields: [
                    {
                        field: 'acctName',
                        showValue: '姓名',
                        searchOnChange: true,
                        clearOnChange: true,
                        exactSearch: true
                    }, {
                        field: 'phone',
                        showValue: '手机号',
                        searchOnChange: true,
                        clearOnChange: true,
                        exactSearch: true
                    }
                ],
                filterOption: null,
                param: {
                    oauth: 'MY_POSTN_ONLY',
                    sort: 'lastUpdated',
                    filtersRaw: []
                },
                loadOnStart: false,
                sortOptions: null,
                hooks: {
                    async beforeCreateItem(param) {
                        param.marketActivityId = this.marketActivityId;
                        param.interactionId = this.interactionId;
                    },
                    beforeLoad (option) {
                        option.param.filtersRaw = [
                            ...option.param.filtersRaw,
                            {id: 'groupId', property: 'groupId', value: `[${this.groupId}]`, operator: 'IN'},
                            {id: 'mcActId', property: 'mcActId', value: this.marketActivityId}
                        ];
                    }
                }
            }),
            classifyList: [
                {val: 'whiteList', name: '白名单', seq: '1'},
                {val: 'blackList', name: '黑名单', seq: '2'},
            ],
            classifyListActive: {},
            classifyItemListActive: {},
            classifyItemList: []
        }
    },
    async created () {
        this.classifyListActive = this.classifyList[0];
        await this.getBlackOrWhiteListGroupId();
    },
    methods: {
        /**
         * @desc 获取黑白名单分组
         * <AUTHOR>
         * @date 2022/6/15 17:54
         **/
        async getBlackOrWhiteListGroupId() {
            return new Promise(async (rs, rj) => {
                try {
                    const data = await this.$http.post(this.$env.appURL + '/interaction/link/consumerGroup/queryByExamplePage', {
                        marketActivityId: this.marketActivityId,          // 营销活动id
                        interactionId: this.interactionId                // 互动活动id
                    });
                    if (data.success) {
                        const whiteListGroup = data.rows.filter((item) => !!item.prizeSetId);
                        const blackList = data.rows.filter((item) => !item.prizeSetId);
                        this.whiteListGroup = whiteListGroup.map((item,index) => ({
                            val: item.id,
                            name: item.groupName,
                            seq: (index + 1)
                        }));
                        let str = '';
                        this.whiteListGroup.forEach((item) => {
                            str = item.val + ',' + str;
                            if (item.name.length > 10) {
                              item.name = item.name.substring(0, 10) + '…';
                            }
                        });
                        this.whiteListGroupId = str.substring(0, str.length - 1);
                        this.blacklistGroupId = blackList[0].id;
                        this.classifyItemList = this.whiteListGroup;
                        this.groupId = this.whiteListGroupId;
                        this.autoList.methods.reload();
                        rs();
                    } else {
                        this.$nav.error('获取分组信息失败:' + data.result);
                        rj(new Error());
                    }
                } catch (e) {
                    this.$nav.error('获取分组信息出错' );
                    rj(new Error());
                }
            });
        },
        /**
         * @desc 新增黑白名单数据
         * <AUTHOR>
         * @date 2022/6/15 16:04
         **/
        editWhiteOrBlackItem (type) {
            const path = '/pages/lj-market-activity/market-activity/white-or-black/white-or-black-edit-page';
            this.$nav.push(path, {
                data: {interactionId: this.interactionId, marketActivityId: this.marketActivityId, operator: type},
            })

        },
        /**
         * @desc 切换筛选
         * <AUTHOR>
         * @date 2022/6/15 10:04
         **/
        async switchTab (item) {
            this.classifyItemListActive = {};
            this.classifyItemList = [];
            if (item.val === 'blackList') {
                this.groupId = this.blacklistGroupId;
            } else {
                this.groupId = this.whiteListGroupId;
                this.classifyItemList = this.whiteListGroup;
            }
            this.autoList.methods.reload();
        },
        /**
         * @desc 切换白名单奖项数据
         * <AUTHOR>
         * @date 2022/6/16 16:42
         **/
        async switchTabItem (item) {
            this.groupId = item.val;
            this.autoList.methods.reload();
        },
    }
}
</script>

<style lang="scss">
.white-or-black-list-page {
    .classify{
        height: 100px;
    }
    .classify-height {
        height: 200px;
    }
    .list-cell {
        position: relative;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .media-list {
            padding: 11px 15px;
            box-sizing: border-box;
            display: flex;
            width: 100%;
            flex-direction: row;
            justify-content: space-between;
            .status-button{
                .status-button-lov{
                    width: 100%;
                }
                .link-input-content{
                    width: 100%;

                }
                .link-input-inner{
                    color: #fff!important;
                    text-align: center;
                    min-width: 100%;
                    font-size: 20px;
                }
            }

            .media-list-logo {
                height: 94px;
                width: 94px;
                margin-right: 20px;

                image {
                    height: 100%;
                    width: 100%;
                }
            }

            .media-list-body {
                display: flex;
                flex: 1;
                flex-direction: column;
                justify-content: space-between;
                align-items: flex-start;
                overflow: hidden;

                .media-list-text-top {
                    width: 100%;
                    line-height: 36px;
                    font-size: 30px;
                    color: #262626;
                }

                .media-list-text-bottom {
                    width: 100%;
                    line-height: 40px;
                    font-size: 26px;
                    color: #8f8f94;
                }
            }
            .media-list-right{
                display: flex;
                flex-direction: column;
                justify-content: center;
                .phone-call{
                    width: 180px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-top: 45px;
                    padding-left: 20px;
                    .iconfont {
                        font-size: 40px;
                        color: #bcbcbc;
                    }
                }
            }
        }
    }
}

</style>
