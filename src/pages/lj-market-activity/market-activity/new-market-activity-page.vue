<template>
    <link-page class="new-market-activity-page">
        <link-form hideSaveButton>
            <view class="activity-type-view">
                <view class="activity-type" :style="{'background-image': 'url(' + $imageAssets.activityBgImage + ')'}">
                    <view class="type-title" @tap="$refs.pickActivityType.show()">
                        <text>{{costType === null ? '请选择费用类型和活动类型' : `${costLargeType}/${costMiddleType}/${costType}/${activityTypeName}`}}</text>
                        <link-icon icon='mp-desc'/>
                    </view>
                </view>
            </view>
            <view class="no-activity-type-view" v-if="costType === null">
                <view class="no-activity-type"
                      :style="{'background-image': 'url(' + $imageAssets.noDataImage + ')'}"></view>
                <view class="no-activity-type-msg">暂无信息，请先选费用类型和活动类型</view>
            </view>
            <view v-if="costType !== null" class="form-view">
                <view class="module-v">
                    <lnk-no-auth v-if="authFlag"></lnk-no-auth>
                    <view class="module" v-for="(item,index) in fieldRows" :key="index" v-else>
                        <view @tap="clickColumn(item)">
                            <view class="left">
                                <view class="left-icon-v">
                                    <link-icon :icon="item.values.leftIconNew"
                                               style="color:  #2F69F8;font-size: 18px;position: absolute;margin: auto;left: 0;top: 0;right: 0;bottom: 0;"/>
                                </view>
                            </view>
                            <view class="center">{{item.values.title}}</view>
                            <view class="right">
                                <link-icon icon="mp-arrow-right" style="color: #BFBFBF;font-size: 14px"/>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <link-dialog ref="pickActivityType" position="bottom">
                <view class="pick-activity-type">
                    <view style="width: 100%;height: 20px;line-height: 20px">
                        <view style="width: 90%;float: left;text-align: center;">费用类型和活动类型</view>
                        <view @tap="$refs.pickActivityType.hide()">
                            <link-icon icon="mp-close"/>
                        </view>
                    </view>
                    <view class="circle-v">
                        <view v-for="(item,index) in levelList" :key="item.id">
                            <view>
                                <view class="circle">
                                    <view :class="item.solid === true ? 'solid-circle' : 'hollow-circle'"></view>
                                    <view :class="item.solid === true ? 'name' : 'hollow-name'" @tap="reselect(item)">
                                        {{item.costTypeName}}
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view class="prompt" @tap="queryConfigTemplate">{{prompt}}</view>
                    <scroll-view scroll-y="true" class="list-container" v-if="costLargeTypeFlag">
                        <view v-for="item1 in costLargeTypeList" :key="item1.id">
                            <view class="list-item" @tap="selectcostLargeType(item1)">
                                <view class="left-content">
                                    <view class="row-1">
                                        <text>{{item1.costTypeName}}</text>
                                    </view>
                                </view>
                                <view class="right-content">
                                    <view v-if="item1._checked">
                                        <link-icon size="1.8em" style="color:#2F69F8;font-size: 16px" icon="icon-check"/>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </scroll-view>
                    <scroll-view scroll-y="true" class="list-container" v-if="costMiddleTypeFlag">
                        <view v-for="item2 in costMiddleTypeList" :key="item2.id">
                            <view class="list-item" @tap="selectcostMiddleType(item2)">
                                <view class="left-content">
                                    <view class="row-1">
                                        <text>{{item2.costTypeName}}</text>
                                    </view>
                                </view>
                                <view class="right-content">
                                    <view v-if="item2._checked">
                                        <link-icon size="1.8em" style="color:#2F69F8;font-size: 16px" icon="icon-check"/>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </scroll-view>
                    <scroll-view scroll-y="true" class="list-container" v-if="costTypeFlag">
                        <view v-for="item3 in costTypeList" :key="item3.id">
                            <view class="list-item" @tap="selectcostType(item3)">
                                <view class="left-content">
                                    <view class="row-1">
                                        <text>{{item3.costTypeName}}</text>
                                    </view>
                                </view>
                                <view class="right-content">
                                    <view v-if="item3._checked">
                                        <link-icon size="1.8em" style="color:#2F69F8;font-size: 16px" icon="icon-check"/>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </scroll-view>
                    <scroll-view scroll-y="true" class="list-container" v-if="activityTypeFlag">
                        <view v-for="item4 in activityTypeList" :key="item4.id">
                            <view class="list-item" @tap="selectActivityType(item4)">
                                <view class="left-content">
                                    <view class="row-1">
                                        <text>{{item4.actType | lov('MC_TYPE')}}</text>
                                    </view>
                                </view>
                                <view class="right-content">
                                    <view v-if="item4._checked">
                                        <link-icon size="1.8em" style="color:#2F69F8;font-size: 16px" icon="icon-check"/>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </link-dialog>
        </link-form>
    </link-page>
</template>

<script>
    import Taro from "@tarojs/taro";
    import LnkNoAuth from "../../core/lnk-no-auth/lnk-no-auth";

    export default {
        name: "new-market-activity-page",
        components: {LnkNoAuth},
        data() {
            const costLargeTypeList = [];
            const costMiddleTypeList = [];
            const costTypeList = [];
            const costLargeType = null;//大
            const costMiddleType = null;//中
            const costType = null;//小
            const costLargeTypeCode = null;//大
            const costMiddleTypeCode = null;//中
            const costTypeCode = null;//小
            const actIndeSourCode = null;//活动类型的源代码
            const prompt = '提示：请选择费用大类';
            const levelList = [];
            const solid = true;
            const costLargeTypeFlag = true;
            const costMiddleTypeFlag = false;
            const costTypeFlag = false;
            const costTypeData = [];
            const scene = 'apply';//申请费用
            //场景来源服务于自定义导航栏。
            //1、市场活动新建 newMarketActivity 2、执行案新建市场活动 caseNewMarketActivity 3、其他 other
            const sceneSourceForNavigation = "newMarketActivity";
            const activityTypeList = [];
            const activityTypeFlag = false;
            const activityType = null; //活动类型
            const activityTypeName = '';//活动类型名称
            const actTypeRequired = '';//活动类型是否必输，由选择的费用小类带出
            const activityChannel = '';//活动渠道-由选择活动类型带出
            const activityCategory = '';//活动类别-由选择活动类型带出
            const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
            return {
                userInfo,
                sceneSourceForNavigation,
                scene,
                actIndeSourCode,
                authFlag: false,
                costLargeTypeCode,
                costMiddleTypeCode,
                costTypeCode,
                costTypeData,
                costLargeTypeFlag,
                costMiddleTypeFlag,
                costTypeFlag,
                solid,
                levelList,
                prompt,
                costLargeTypeList,
                costTypeList,
                costMiddleTypeList,
                costLargeType,
                costMiddleType,
                costType,
                formRules: {},
                fieldRows: [],//需要维护的模块
                interactiveConfigRequire: false,//互动模板是否必输
                activityTypeList,
                activityTypeFlag,
                activityType,
                activityTypeName,
                actTypeRequired,
                activityChannel,
                activityCategory,
                activityLevel: '' // 活动级别
            }
        },
        async created() {
            await this.queryActivityTypeData()
        },
        methods: {
            /**
             * 查询费用类型
             * <AUTHOR>
             * @date 2020-08-24
             * */
            async queryActivityTypeData() {
                const data = await this.$http.post('action/link/feeType/queryByExamplePage', {
                    pageFlag: false,
                    filtersRaw: [
                        //是否有效
                        {
                            id: 'effectiveFlag',
                            property: 'effectiveFlag',
                            value: 'Y',
                            operator: '='
                        },
                    ],
                });
                this.costTypeData = data.rows;
                this.costTypeData.forEach(i => {
                    if (i['costTypeLevel'] === '1') {
                        this.costLargeTypeList.push(i);
                    }
                });
            },
            /**
             * 根据费用小类去查询需要维护的模块
             * <AUTHOR>
             * @date 2020-08-19
             * */
            async queryNeedMaintainModule() {
                let data;
                if(this.$utils.isNotEmpty(this.activityType)) {
                    data = await this.$utils.getQwMpTemplate('newActivity', '', this.costTypeCode, this.activityType);
                } else {
                    data = await this.$utils.getQwMpTemplate('newActivity', '', this.costTypeCode);
                }
                if (!data.success) {
                    this.authFlag = true;
                    this.fieldRows = [];
                    return;
                }
                let resultOpt = JSON.parse(data.result);
                this.fieldRows = JSON.parse(resultOpt.conf);
                if (!this.$utils.isEmpty(this.fieldRows)) {
                    this.authFlag = false;
                }
                for (let i = 0; i < this.fieldRows.length; i++) {
                    const interactiveConfigExit = this.fieldRows[i].props.filter((item1) => item1.val === 'interactive-config');
                    if(this.$utils.isNotEmpty(interactiveConfigExit)){
                        this.interactiveConfigRequire = this.fieldRows[i].base.require;
                    }
                }
            },
            /**
             * 点击每一行
             * <AUTHOR>
             * @date 2020-08-19
             * */
            clickColumn(item) {
                const input = item.values.clickFun;
                if (input === 'addBasicInfo') {
                    this.addBasicInfo(item);
                }else{
                  this.$message.primary('请维护基础信息');
                }
            },
            async getExcludebusSceneData(){
                const isTeQu = this.userInfo.coreOrganizationTile.brandCompanyCode === '5137'
                const isJiaoLing = this.userInfo.coreOrganizationTile.brandCompanyCode === '5151'
                let param = {
                    filtersRaw: [
                        {id: "type", property: "type", value: 'TMPL_SUB_BIZ_TYPE'},
                        {id: "activeFlag", property: "activeFlag", value: 'Y'},
                        {id: "parentVal", property: "parentVal", value: 'businessScenario'},
                    ]
                }
                // 费用小类为事件营销活动，特曲订餐活动类型为浓香私宴，窖龄订餐活动类型为链路会员时，业务场景只能选择【促销活动用餐】
                if (this.costType === '事件营销活动' && ((isJiaoLing && this.activityTypeName === '链路会员') || (isTeQu  && this.activityTypeName === '浓香私宴'))) {
                    param.filtersRaw.push({id: "val", property: "val", value: "ActivityMeal5", operator: "<>"},)
                } else {
                    param.filtersRaw.push({id: "useFlag", property: "useFlag", value: 'N'},)
                }
                const data = await this.$http.post('action/link/basic/queryByExamplePage', param)
                let baseArray = [];
                if(data.rows){
                    data.rows.forEach((item) => {
                        baseArray.push(item.val)
                    })
                }
                return baseArray;
            },
            /**
             * 跳转去维护基本信息界面
             * <AUTHOR>
             * @date 2020-08-19
             * */
            async addBasicInfo(item) {
                const excludebusSceneData = await this.getExcludebusSceneData();
                const id = await this.$newId();
                const data = this.pageParam.data;
                data.costLargeType = this.costLargeType;//大类
                data.costLargeTypeCode = this.costLargeTypeCode;
                data.costMiddleType = this.costMiddleType;//中类
                data.costMiddleTypeCode = this.costMiddleTypeCode;
                data.costType = this.costType;//小类
                data.costTypeCode = this.costTypeCode;
                data.actIndeSourCode = this.activityType;//活动类型源代码
                data.activityChannel = this.activityChannel;//活动渠道  值列表：ACT_CHANNEL
                data.activityCategory = this.activityCategory;//活动类别 值列表：ACT_CATEGORY
                data.isCompanyCostFlag = 'N';//是否总部活动费用字段的值默认为N
                data.activityLevel = this.activityLevel;// 活动级别
                data.id = id;
                this.$nav.push('/pages/lj-market-activity/market-activity/new-activity-basic-page', {
                    data: data,
                    status: 'New',
                    editSubControlList: item.subControlList,//新建基础信息时-子组件
                    maintenanceModules: this.fieldRows,//配置的需要维护模块
                    sceneSourceForNavigation: this.sceneSourceForNavigation,
                    interactiveConfigRequire: this.interactiveConfigRequire,
                    excludebusSceneData:excludebusSceneData,//业务场景需要排除的值列表值
                })
            },
            /**
             * 跳转去选择费用界面
             * <AUTHOR>
             * @date 2020-08-19
             * */
            addCostInfo() {
                this.$nav.push('/pages/lj-market-activity/market-activity/market-activity-cost-list-new-page', {
                    exeCaseId: null,
                    pageForm: 'marketActivity',
                    operant: 'NEW',
                    scene: this.scene,
                    pageSource: 'view',
                    maintenanceModules: this.fieldRows,//配置的需要维护模块
                    /**
                     * 控制是否可以操作 cash-new 和 prod-new组件的删除、新建、以及删除兑付方式的操作
                     * 只允许在编辑界面删除兑付方式、删除明细 、新增明细。其他界面可以编辑小计 和 编辑某个产品信息
                     * */
                    operateFlag: true,
                    sceneSourceForNavigation: this.sceneSourceForNavigation,
                })
            },
            /**
             * 跳转去选择互动模版界面
             * <AUTHOR>
             * @date 2020-08-19
             * */
            addConfigurationInformation() {
                this.$nav.push('/pages/lj-market-activity/market-activity/interactive-configuration-page',{
                    sceneSourceForNavigation: this.sceneSourceForNavigation,
                    interactiveConfigRequire: this.interactiveConfigRequire
                })
            },
            /**
             * 消费者、内部人员、参与终端/经销商、活动动销，跳转去其他信息下界面
             * <AUTHOR>
             * @date 2020-08-19
             * */
            gotoMarketActivityItem(input) {
                this.$nav.push('/pages/lj-market-activity/market-activity/new-market-activity-other-info-page', {
                    pageFormObj: input,
                    sceneSourceForNavigation: this.sceneSourceForNavigation,
                })
            },
            /**
             *  选择类型
             *
             *  <AUTHOR>
             *  @date        2020-08-04
             */
            selectcostLargeType(item) {
                //响应式字段_checked
                const _that = this;
                this.$set(item, '_checked', true);
                this.$set(item, 'solid', true);
                this.costLargeType = item.costTypeName;
                this.costLargeTypeCode = item.costTypeCode;
                this.deelData(item, 'one');
                this.costLargeTypeList.filter(function (val) {
                    if (val.id !== item.id) {
                        _that.$set(val, '_checked', false);
                    }
                });
                this.costLargeTypeFlag = false;
                this.costMiddleTypeFlag = true;
                //中类
                this.costMiddleTypeList = [];
                this.costTypeData.forEach(i => {
                    if (i['parentCostCode'] === item.costTypeCode) {
                        this.costMiddleTypeList.push(i);
                    }
                });
            },
            selectcostMiddleType(item) {
                //响应式字段_checked
                const _that = this;
                this.$set(item, '_checked', true);
                this.$set(item, 'solid', true);
                this.costMiddleType = item.costTypeName;
                this.costMiddleTypeCode = item.costTypeCode;
                this.costMiddleTypeList.filter(function (val) {
                    if (val.id !== item.id) {
                        _that.$set(val, '_checked', false);
                    }
                });
                this.deelData(item, 'two');
                this.costMiddleTypeFlag = false;
                this.costTypeFlag = true;
                //小类
                this.costTypeList = [];
                this.costTypeData.forEach(i => {
                    if (i['parentCostCode'] === item.costTypeCode) {
                        this.costTypeList.push(i);
                    }
                });
            },
            async selectcostType(item) {
                //响应式字段_checked
                const _that = this;
                this.$set(item, '_checked', true);
                this.$set(item, 'solid', true);
                this.costType = item.costTypeName;
                this.costTypeCode = item.costTypeCode;
                this.actTypeRequired = item.actTypeRequired;
                this.costTypeList.filter(function (val) {
                    if (val.id !== item.id) {
                        _that.$set(val, '_checked', false);
                    }
                });
                this.deelData(item, 'three');
                this.costTypeFlag = false;
                this.activityTypeFlag = true;
                await this.queryActivityType();
            },
            async selectActivityType(item){
                const _that = this;
                this.$set(item, '_checked', true);
                this.$set(item, 'solid', true);
                this.activityType = item.actType;
                this.activityChannel = item.actChannel;
                this.activityCategory = item.actCategory;
                // 防止活动类型切换从有值切换到无值
                this.activityLevel = item.activityLevel ? item.activityLevel : '';
                this.activityTypeName = await this.$lov.getNameByTypeAndVal('MC_TYPE',item.actType);
                this.activityTypeList.filter(function (val) {
                    if (val.id !== item.id) {
                        _that.$set(val, '_checked', false);
                    }
                });
                this.deelData({id:item.id,costTypeName:this.activityTypeName}, 'four');
                this.$refs.pickActivityType.hide();
                //根据费用小类去查询需要维护的模块
                await this.queryNeedMaintainModule();
            },
            async queryConfigTemplate() {
                if (this.prompt === '请选择活动类型或点击查询模版' || this.prompt === '提示：请选择活动类型') {
                    if (this.actTypeRequired === 'Y') {
                        if (this.$utils.isEmpty(this.activityType)) {
                            this.$message.warn('当前费用小类下活动类型为必选项，请选择活动类型。');
                            return false;
                        }
                    } else {
                        this.$refs.pickActivityType.hide();
                        //根据费用小类去查询需要维护的模块
                        await this.queryNeedMaintainModule();
                    }
                }
            },
            /**
             * 根据费用小类编码查询活动类型可选数据
             * */
            async queryActivityType(){
                let params = {};
                if (!this.$utils.isEmpty(this.userInfo.coreOrganizationTile.l3Id)) {
                    params = {
                        sort: "created",
                        order: 'desc',
                        pageFlag: true,
                        attr5:'orgType',
                        isEffective: 'Y',
                        companyId : this.userInfo.coreOrganizationTile.l3Id,
                        filtersRaw: [{
                            id: 'costTypeCode',
                            property: 'costTypeCode',
                            value: this.costTypeCode,
                            operator: '='
                        }],
                    }
                } else {
                    params = {
                        sort: "created",
                        order: 'desc',
                        pageFlag: true,
                        attr5:'orgType',
                        isEffective: 'Y',
                        filtersRaw: [{
                            id: 'costTypeCode',
                            property: 'costTypeCode',
                            value: this.costTypeCode,
                            operator: '='
                        }],
                    }
                }
                const data = await this.$http.post('action/link/actCostTypeMap/queryByExamplePage', params);
                this.activityTypeList = data.rows || [];
            },
            /**
             *  重新选择类型
             *  <AUTHOR>
             *  @date        2020-08-04
             */
            async reselect(item) {
                if(item.costTypeName === '请选择活动类型或点击查询模版'){
                    await this.queryConfigTemplate();
                    return;
                }
                if (item.key === 'one') {
                    this.costMiddleType = null;
                    this.costType = null;
                    this.activityType = null;
                    this.activityTypeName = '';
                    this.costLargeTypeFlag = true;
                    this.costMiddleTypeFlag = false;
                    this.costTypeFlag = false;
                    this.activityTypeFlag = false;
                    this.levelList = this.levelList.filter((ele) => ele.key === 'one');
                    this.costMiddleTypeList.forEach((val) => {
                        this.$set(val, '_checked', false);
                    });
                    this.prompt = '提示：请选择费用大类';
                }
                if (item.key === 'two') {
                    this.costType = null;
                    this.activityType = null;
                    this.activityTypeName = '';
                    this.costLargeTypeFlag = false;
                    this.costMiddleTypeFlag = true;
                    this.costTypeFlag = false;
                    this.activityTypeFlag = false;
                    this.levelList = this.levelList.filter((ele) => ele.key === 'one' || ele.key === 'two');
                    this.costTypeList.forEach((val) => {
                        this.$set(val, '_checked', false);
                    });
                    this.prompt = '提示：请选择费用中类';
                }
                if (item.key === 'three') {
                    this.activityType = null;
                    this.activityTypeName = '';
                    this.costLargeTypeFlag = false;
                    this.costMiddleTypeFlag = false;
                    this.costTypeFlag = true;
                    this.activityTypeFlag = false;
                    this.levelList = this.levelList.filter((ele) => ele.key !== 'four');
                    this.prompt = '提示：请选择费用小类';
                }
                if (item.key === 'four') {
                    this.activityType = null;
                    this.activityTypeName = '';
                    this.costLargeTypeFlag = false;
                    this.costMiddleTypeFlag = false;
                    this.costTypeFlag = false;
                    this.activityTypeFlag = true;
                    if(this.actTypeRequired === 'Y'){
                        this.prompt = '提示：请选择活动类型';
                    }else{
                        this.prompt = '请选择活动类型或点击查询模版';
                    }
                }
            },
            async deelData(item, type) {
                const _that = this;
                const newItem = {costTypeName: item.costTypeName, key: type, solid: true, id: item.id};
                let exist = true;
                if (this.$utils.isEmpty(this.levelList)) {
                    this.levelList.push(newItem);
                } else {
                    const test = this.levelList.filter((ele) => ele.key === type)[0] || {};
                    if (!this.$utils.isEmpty(test)) {
                        _that.$set(test, 'costTypeName', item.costTypeName);
                        _that.$set(test, 'solid', true);
                    } else {
                        this.levelList.push(newItem);
                    }
                }
                if (this.costLargeType !== null && this.costMiddleType === null) {
                    const newItem = {costTypeName: '请选择费用中类', key: 'two', solid: false};
                    this.levelList.push(newItem);
                    this.prompt = '提示：请选择费用中类';
                    this.solid = false;
                }
                if (this.costMiddleType !== null && this.costType === null) {
                    const newItem = {costTypeName: '请选择费用小类', key: 'three', solid: false};
                    this.levelList.push(newItem);
                    this.prompt = '提示：请选择费用小类';
                    this.solid = false;
                }
                if( this.costType !== null && this.activityType === null){
                    let newItem = {};
                    if(this.actTypeRequired === 'Y'){
                        newItem  = {costTypeName: '请选择活动类型', key: 'four', solid: false};
                    }else{
                        newItem = {costTypeName: '请选择活动类型或点击查询模版', key: 'four', solid: false};
                    }
                    this.levelList.push(newItem);
                    if(this.actTypeRequired === 'Y'){
                        this.prompt = '提示：请选择活动类型';
                    }else{
                        this.prompt = '请选择活动类型或点击查询模版';
                    }
                    this.solid = false;
                }
            }
        }
    }
</script>

<style lang="scss">
    .new-market-activity-page {
        background: white !important;

        .activity-type-view {
            margin: 24px;

            .activity-type {
                height: 144px;
                background-repeat: no-repeat;
                background-size: 100% 100%;
                -moz-background-size: 100% 100%;
                margin: 24px;

                .type-title {
                    text-align: center;
                    color: white;
                    font-size: 32px;
                    width: 100%;
                    padding-top: 40px;
                }
            }
        }

        .no-activity-type-view {
            margin-top: 70px;

            .no-activity-type {
                width: 368px;
                height: 368px;
                background-repeat: no-repeat;
                background-size: 100% 100%;
                -moz-background-size: 100% 100%;
                margin: auto;
            }

            .no-activity-type-msg {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                text-align: center;
                line-height: 38px;
                padding-top: 30px;
            }
        }

        .pick-activity-type {
            .solid-circle {
                background: #2F69F8;
                border: 1px solid #2F69F8;
                width: 12px;
                height: 12px;
                border-radius: 30px;
                float: left;
                position: absolute;
                left: 32px;
            }

            .hollow-circle {
                background: #FFFFFF;
                border: 1px solid #2F69F8;
                width: 12px;
                height: 12px;
                border-radius: 30px;
                float: left;
                position: absolute;
                left: 32px;
            }

            .line-border {
                display: block;
                height: 64px;
                width: 100%;
                left: 200px;
                border-left: 5px solid #2F69F8;

                .right-content {
                    margin-left: 50px;
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 64px;
                }
            }

            .prompt {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #BFBFBF;
                letter-spacing: 0;
                line-height: 56px;
            }

            .circle-v {

                .circle {
                    height: 68px;
                    line-height: 68px;

                    .name {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 28px;
                        padding-left: 48px;
                    }

                    .hollow-name {
                        color: #2F69F8;
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        letter-spacing: 0;
                        line-height: 28px;
                        padding-left: 48px;
                    }
                }
            }

            .circle-v :not(:last-child) {
                .circle {
                    border-left: 4px solid #2F69F8;
                }
            }

            .list-container {
                max-height: 40vh;
            }

            .list-item {
                display: flex;
                border-bottom: 1px solid #F2F2F2;
                height: 92px;
                line-height: 92px;

                .left-content {
                    display: inline-block;
                    width: 80%;

                    .row-1 {
                        width: 100%;
                        @include flex-start-center;
                        @include space-between;

                        .status {
                            color: gray;
                            font-size: 12px;
                        }
                    }

                    .row-2 {
                        width: 100%;
                        margin-top: 1vh;

                        .date {
                            color: gray;
                            font-size: 12px;
                        }
                    }
                }

                .right-content {
                    padding-left: 20px;
                    display: inline-block;
                    width: 19%;
                    line-height: 92px;
                    text-align: right;
                }
            }
        }

        .form-view {
            .module-v {
                .module {
                    margin: 24px;
                    background: white;
                    border-radius: 16px;
                    height: 128px;
                    box-shadow: 0 2px 20px 0 rgba(47, 105, 248, 0.12);

                    .left {
                        width: 20%;
                        float: left;
                        height: 128px;
                        position: relative;

                        .left-icon-v {
                            width: 80px;
                            height: 80px;
                            border-radius: 50%;
                            background: rgba(47, 105, 248, 0.10);
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            margin: auto;
                        }
                    }

                    .center {
                        width: 70%;
                        float: left;
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 128px;
                    }

                    .right {
                        line-height: 128px;
                    }
                }
            }
        }
    }

</style>
