<template>
    <link-page class="new-venue-page">
        <link-form :option="option">
            <link-form-item label="场地名称" required>
                <link-input v-model="option.formData.acctName"></link-input>
            </link-form-item>
            <link-form-item label="场地小类" required>
                <link-lov v-model="option.formData.subAcctType" type="SUB_ACCT_TYPE" :multiple="false"
                          parent-type="ACCNT_CATEGORY" :parent-val="'Venue'"></link-lov>
            </link-form-item>
            <link-form-item required label="所在地区">
                <link-address placeholder="请选择所在地区"
                              :province.sync="option.formData.province"
                              :city.sync="option.formData.city"
                              :district.sync="option.formData.district"/>
            </link-form-item>
            <link-form-item label="场地地址" required>
                <view slot="title" @tap="getLocation">
                    <text>场地地址(获取定位)</text>
                    <link-icon style="color: #2F69F8" icon="icon-location" class="link-location"/>
                </view>
                <link-input v-model="option.formData.address" disabled></link-input>
            </link-form-item>
            <link-form-item label="联系人">
                <link-input v-model="option.formData.contactName"></link-input>
            </link-form-item>
            <link-form-item label="联系电话">
                <link-input v-model="option.formData.mobilePhone"></link-input>
            </link-form-item>
        </link-form>
        <link-sticky>
            <link-button block @tap="save" :disabled="disabledFlag">保存</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    import Taro from "@tarojs/taro";
    import {ROW_STATUS} from "../../../utils/constant";

    export default {
        name: "new-venue-page",
        data() {
            const config = {
                ...this.pageParam,//列表传递的数据对象
                model: {},
                data: {
                    ...this.pageParam.data
                }
            };
            const option = new this.FormOption(this, {
                ...config,
                operator: 'NEW',
            });
            /**
             * 活动场地经度
             */
            const restaurantLon = "";
            /**
             * 活动场地纬度
             */
            const restaurantLat = "";
            const marketActivityItem = this.pageParam.marketActivityItem;//活动信息
            return {
                restaurantLon,
                restaurantLat,
                option,
                userInfo: {},
                disabledFlag: false,
                marketActivityItem,
            }
        },
        onLoad() {
            this.$locations.QQClearLocation();
            this.userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
        },
        async onShow(){
            const location = this.$locations.QQGetLocation();
            if(location) {
                location.distance = await this.$locations.getDistance(this.coordinate.latitude, this.coordinate.longitude, location.latitude, location.longitude);
                this.overhangFlag = location.distance * 1000 > this.allowDistance;
                if (this.overhangFlag) {
                    this.overhangDialog();
                } else {
                    let address = await this.$locations.reverseTMapGeocoder(location.latitude, location.longitude, '添加场地');
                    this.option.formData.latitude = address.wxMarkerData[0].latitude;
                    this.option.formData.longitude = address.wxMarkerData[0].longitude;
                    this.option.formData.province = address.originalData.result.addressComponent['province'];
                    this.option.formData.city = address.originalData.result.addressComponent['city'];
                    this.option.formData.district = address.originalData.result.addressComponent['district'];
                    this.$set(this.option.formData, 'address', address.originalData.result['sematic_description']);
                    this.option.formData.latitude = address.originalData['result'].location.lat;
                    this.option.formData.longitude = address.originalData['result'].location.lng;
                }
            }
        },
        methods: {
            async getLocation() {
                const that = this;
                that.coordinate = await that.$locations.getCurrentCoordinate();
                if (!that.$utils.isEmpty(that.coordinate)) {
                    await this.$locations.chooseLocation(that.coordinate.latitude, that.coordinate.longitude);
                } else {
                    let userLocation = await that.$locations.openSetting();
                    if (userLocation['scope.userLocation']) {
                        that.coordinate = await that.$locations.getCurrentCoordinate();
                        that.$store.commit('coordinate/setCoordinate', that.coordinate);
                        await that.initData();
                    }
                }
            },
            /**
             *  校验数据
             *
             *  <AUTHOR>
             *  @date        2020-10-10
             */
            checkData() {
                if (this.$utils.isEmpty(this.option.formData.acctName)) {
                    this.$message.warn('请输入场地名称');
                    return false;
                }
                if (this.$utils.isEmpty(this.option.formData.subAcctType)) {
                    this.$message.warn('请选择场地小类');
                    return false;
                }
                if (this.$utils.isEmpty(this.option.formData.province) || this.$utils.isEmpty(this.option.formData.city) || this.$utils.isEmpty(this.option.formData.district)) {
                    this.$message.warn('请选择所在地区');
                    return false;
                }
                if (this.$utils.isEmpty(this.option.formData.address)) {
                    this.$message.warn('请完善场地地址');
                    return false;
                }
                if (this.$utils.isEmpty(this.option.formData.latitude)||this.$utils.isEmpty(this.option.formData.longitude)) {
                    this.$message.warn('请通过地图定位选择位置');
                    return false;
                }
                return true;
            },
            /**
             * 保存活动场地
             * */
            async save() {
                if (!this.checkData()) {
                    return;
                }
                this.$utils.showLoading();
                this.disabledFlag = true;
                this.option.formData.id = await this.$newId();
                this.option.formData.postnId = this.userInfo.postnId;
                //如果不是前端新建活动的话 场地的组织ID取活动上的组织ID
                if (this.marketActivityItem.row_status !== ROW_STATUS.NEW) {
                    this.option.formData.orgId = this.marketActivityItem.orgId;
                } else {
                    //如果是前端新建活动，那么看销售大区、销售片区、销售城市、销售区县是否赋值，
                    //如果有赋值就用最低层级的组织id作为活动id。
                    //如果没有赋值就用当前登录用户组织id--这个逻辑后台给。
                    let orgId;
                    if (!this.$utils.isEmpty(this.marketActivityItem.companyId)) {
                        orgId = this.marketActivityItem.companyId;
                    }

                    if (!this.$utils.isEmpty(this.marketActivityItem.salesBigAreaId)) {
                        orgId = this.marketActivityItem.salesBigAreaId;
                    }

                    if (!this.$utils.isEmpty(this.marketActivityItem.salesRegionId)) {
                        orgId = this.marketActivityItem.salesRegionId;
                    }

                    if (!this.$utils.isEmpty(this.marketActivityItem.salesCityId)) {
                        orgId = this.marketActivityItem.salesCityId;
                    }

                    if (!this.$utils.isEmpty(this.marketActivityItem.salesDistrictId)) {
                        orgId = this.marketActivityItem.salesDistrictId;
                    }

                    this.option.formData.orgId = orgId;
                }
                this.option.formData.appreciationFlag = "Y";
                const data = await this.$http.post('action/link/accnt/insert', this.option.formData, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        if (response.result === '电话号码重复。') {
                            this.$showError('酒店电话号码重复，当前输入手机号为已有酒店，请确认后输入');
                            this.$set(this.option.formData, 'mobilePhone', '');
                            this.disabledFlag = false;
                        } else {
                            this.$showError(response.result);
                            this.disabledFlag = false;
                        }
                    }
                });
                if (data.success) {
                    this.$message.success('保存成功');
                    this.pageParam.callback(data.newRow);
                    this.$utils.hideLoading();
                    this.$nav.back();
                }
            }
        }
    }
</script>

<style lang="scss">
    .new-venue-page {
        .get-location {
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #2F69F8;
            letter-spacing: 0;
            text-align: right;
            line-height: 28px;
            word-break: keep-all;
        }
    }
</style>
