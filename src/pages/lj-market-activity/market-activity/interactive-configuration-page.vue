<template>
    <link-page class="interactive-configuration-page">
        <ma-navigation-bar :backVisible="true"
                           :zIndex="zIndex"
                           :backgroundImg="$imageAssets.homeMenuBgImage"
                           :title="navigationBarTitle"
                           :titleColor="navigationBarTitleColor"
                           :navBackgroundColor="navBackgroundColor"
                           :udf="udfBack">
        </ma-navigation-bar>
        <view class="base">
            <item title="营销模板" @tap="chooseTemplate" :arrow="false">
                <view v-if="$utils.isEmpty(interactive.template)">请选择营销模板</view>
                <view v-if="!$utils.isEmpty(interactive.template)">{{interactive.template}}
                    <link-icon icon="mp-close" style="width: 25px;height: 25px" v-if="pageFrom !== 'MarketingActivityModel'" @tap="clearTemp($event)"/>
                </view>
            </item>
        </view>
        <view v-if="$utils.isEmpty(arrNodes) && pageFrom !== 'MarketingActivityModel'" class="no-cost-view">
            <view style="width: 100%;height: 35px"></view>
            <view class="no-cost" :style="{'background-image': 'url(' + $imageAssets.noDataImage + ')'}"></view>
            <view class="no-cost-msg">暂无信息，请先选择营销模板</view>
        </view>
        <view style="width: 100%;height: 12px;"></view>
        <view v-if="!$utils.isEmpty(arrNodes)" class="template-info-view">
            <design-interaction-type :arrNodes="arrNodes" :flag="flag"
                                     :activityItem="activityItem"></design-interaction-type>
        </view>
        <link-sticky v-if="operant !== 'UPDATE'">
            <link-button block mode="stroke" @tap="lastStep">上一步</link-button>
            <link-button block @tap="nextStep('next')">下一步</link-button>
        </link-sticky>
        <link-sticky v-if="operant === 'UPDATE'">
            <link-button block @tap="nextStep('save')">保存互动配置</link-button>
        </link-sticky>
        <link-dialog ref="tempUpdateConfirm">
            <view slot="head">
                提示
            </view>
            <view>
                已存在编辑保存的互动信息，重新选择营销模板会失效已保存的互动信息，是否确定重新选择营销模板？
            </view>
            <link-button slot="foot" @tap="$refs.tempUpdateConfirm.hide()">取消</link-button>
            <link-button slot="foot" @tap="associatedUpdateTemp">确定</link-button>
        </link-dialog>
        <link-dialog ref="clearTempConfirm">
            <view slot="head">
                提示
            </view>
            <view>
                清除营销模板会失效已保存的互动信息，是否确定清除营销模板？
            </view>
            <link-button slot="foot" @tap="$refs.clearTempConfirm.hide()">取消</link-button>
            <link-button slot="foot" @tap="clearTempConfirm">确定</link-button>
        </link-dialog>
    </link-page>
</template>

<script>
    import DesignInteractionType from "./components/design-interaction-type";
    import Taro from "@tarojs/taro";
    import MaNavigationBar from "../ma-navigation-bar/ma-navigation-bar";
    import {ComponentUtils} from "link-taro-component";
    import {ROW_STATUS} from "../../../utils/constant";

    export default {
        name: "interactive-configuration-page",
        components: {MaNavigationBar, DesignInteractionType},
        data() {
            const pageFrom = this.pageParam.pageFrom || '';
            const activityItem = this.pageParam.data;
            const interactive = {
                template: activityItem.templateName
            };
            const awardList = [];
            const actIndeSourCode = this.pageParam.data.actIndeSourCode;
            const actIndeSourCodeDeal = `[${actIndeSourCode}]`;//处理的活动类型-格式为"[PinJianHui]"
            let arrNodes = this.pageParam.data.actJson && JSON.parse(this.pageParam.data.actJson).nodes;
            let flag = false;
            if (!this.$utils.isEmpty(arrNodes)) {
                const exist = arrNodes.filter((i) => i.evetype === 'LotteryTicket' || i.evetype === 'Sales' || i.evetype === 'WheelCamp' || i.evetype === 'RoundWheel' || i.evetype === 'SignIn' || i.evetype === 'Invitation' || i.evetype === 'SmashGoldenEgg' || i.evetype === 'SlotMachine' || i.evetype === 'BlindBox' || i.evetype === 'IceTrue' || i.evetype === 'ExpansionRule' || i.evetype === 'Message' || i.evetype === 'WxMessage' || i.evetype === 'Questionnaire' || i.evetype === 'TreasureChest');
                // 标识有红包或大转盘或微信签到或砸金蛋或老虎机或盲盒或冰鉴正宗或抽奖规则拓展
                flag = !this.$utils.isEmpty(exist);
            }
            const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
            //场景来源服务于自定义导航栏。
            //1、市场活动新建 newMarketActivity 2、执行案新建市场活动 caseNewMarketActivity 3、其他 other
            let sceneSourceForNavigation = "other";//默认other
            if (!this.$utils.isEmpty(this.pageParam.sceneSourceForNavigation)) {
                sceneSourceForNavigation = this.pageParam.sceneSourceForNavigation;
            }
            const operant = this.pageParam.operant;//操作行为 空 新建：执行案新建活动和活动新建时，UPDATE 更新：活动详情-互动配置-编辑按钮
            return {
                pageFrom,
                operant,
                navigationBarTitle: '互动配置',
                navigationBarTitleColor: '#ffffff',
                navBackgroundColor: 'transparent',
                zIndex: ComponentUtils.nextIndex(),
                sceneSourceForNavigation,
                actIndeSourCodeDeal,
                tempList: {},//选择的营销模板对象
                userInfo,
                activityItem,
                flag,
                arrNodes,
                interactive,
                awardList,//奖项列表
                chooseTemplateOption: new this.AutoList(this, {
                    module: this.$env.appURL + '/marketactivity/link/marketActivity',
                    sortOptions: null,
                    param: {
                        rows: 25,
                        sort: 'lastUpdated',
                        attr3: 'subOrg',
                        attr6: 'range',
                        orgId: userInfo.orgId,
                        filtersRaw: [
                            {id: 'type', property: 'type', value: 'Template'},
                            {id: 'actType', property: 'activityType', value: actIndeSourCodeDeal, operator: 'IN'},
                            {id: 'activeFlg', property: 'activeFlg', value: 'Y'}
                        ]
                    },
                    request: ({url, param}) => {
                        param.filtersRaw = JSON.stringify(param.filtersRaw)
                        const data = this.$httpForm.post(url, param)
                        return data;
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} title={data.name} data={data} arrow="false">
                                <view
                                    style="display: -webkit-box;display: -ms-flexbox;display: flex;padding: 12px 8px 12px 12px;width:100%">
                                    <view
                                        style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px;">
                                        {data.activityName}
                                    </view>
                                </view>
                            </item>
                        )
                    }
                }),
            }
        },
        async created() {
            await this.queryActivityItemById();
        },
        mounted() {
            this.$bus.$on("interactionComponentUpdateActivity",(data) => {
                this.$set(this, 'activityItem', data);
                this.arrNodes = data.actJson && JSON.parse(data.actJson).nodes;
            });
        },
        methods: {
            /**
             * 自定义返回函数
             * @songyanrong
             * @date 2020-12-02
             * */
            udfBack() {
                if (this.sceneSourceForNavigation === 'other') {
                    this.$nav.back();
                } else if (this.sceneSourceForNavigation === 'newMarketActivity') {
                    let pages = Taro.getCurrentPages();    //获取当前页面信息栈
                    let targetIndex = pages.findIndex(function (item) {
                        return item.route === "pages/lj-market-activity/market-activity/market-activity-list-page";
                    });
                    if (targetIndex === -1) {
                        return this.$nav.backAll()
                    }
                    const num = Number(pages.length - (Number(targetIndex) + 1));
                    setTimeout(() => {
                        this.$bus.$emit('marketActivityListRefresh');
                        this.$nav.back(null, num);
                    }, 1000)
                } else if (this.sceneSourceForNavigation === 'caseNewMarketActivity') {
                    let pages = Taro.getCurrentPages();    //获取当前页面信息栈
                    let targetIndex = pages.findIndex(function (item) {
                        return item.route === "pages/lj-market-activity/perform-case/perform-case-item-page";
                    });
                    if (targetIndex === -1) {
                        return this.$nav.backAll()
                    }
                    const num = Number(pages.length - (Number(targetIndex) + 1));
                    setTimeout(() => {
                        this.$bus.$emit('initActivityList');
                        this.$nav.back(null, num);
                    }, 1000)
                }
            },
            /**
             * 清除营销模版
             * <AUTHOR>
             * @date 2020-12-03
             * */
            clearTemp(e) {
                e.stopPropagation();
                this.$refs.clearTempConfirm.show();
            },
            /**
             * 确定清除营销模版
             * <AUTHOR>
             * @date 2020-12-03
             * */
            async clearTempConfirm() {
                const clearActivityTemplateInfo = {
                    templateName: '',
                    templateId: null,
                    actJson: '',
                    inactiveInteractionFlag : "Y",
                    id: this.activityItem.id,
                    rowVersion: this.activityItem.rowVersion,
                    updateFields: "id,templateName,templateId,actJson,rowVersion"
                };
                //更新活动上的模板ID和模板名称
                const data = await this.$http.post('action/link/marketAct/update', clearActivityTemplateInfo);
                this.activityItem = {...data.newRow};
                this.$dataService.setMarketActivityItem(data.newRow);
                this.$bus.$emit('updateActivityTemplateRefresh');
                this.$set(this.interactive, 'template', '');
                this.$set(this, 'arrNodes', null);
                this.$bus.$emit('refreshInteractionItem');
                this.$refs.clearTempConfirm.hide();
            },
            /**
             * 活动详情数据查询
             * 使用场景：从上一个页面跳转过来查询
             * <AUTHOR>
             * @date 2020-11-03
             * */
            async queryActivityItemById() {
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.activityItem.id
                });
                this.activityItem = {...data.result};
                this.$dataService.setMarketActivityItem(data.result);
                this.$set(this.interactive, 'template', this.activityItem.templateName);
                this.arrNodes = this.activityItem.actJson && JSON.parse(this.activityItem.actJson).nodes;
                if (!this.$utils.isEmpty(this.arrNodes)) {
                    const exist = this.arrNodes.filter((i) => i.evetype === 'LotteryTicket' || i.evetype === 'Sales' || i.evetype === 'WheelCamp' || i.evetype === 'RoundWheel' || i.evetype === 'SignIn' || i.evetype === 'Invitation' || i.evetype === 'SmashGoldenEgg' || i.evetype === 'SlotMachine' || i.evetype === 'BlindBox' || i.evetype === 'IceTrue' || i.evetype === 'ExpansionRule'  || i.evetype === 'Message' || i.evetype === 'WxMessage' || i.evetype === 'Questionnaire' || i.evetype === 'TreasureChest');
                    // 标识有红包或大转盘或微信签到或邀请或砸金蛋或老虎机或盲盒
                    this.flag = !this.$utils.isEmpty(exist);
                }
            },
            /**
             * 上一步
             * <AUTHOR>
             * @date 2020-08-06
             * */
            lastStep() {
                this.$bus.$emit("marketActivity");
                this.$nav.back();
            },
            /**
             * 下一步
             * <AUTHOR>
             * @date 2020-08-06
             * operant :  next 下一步 save 保存
             * */
            async nextStep(operant) {
                // 红包或大转盘或微信签到或邀请或砸金蛋或老虎机或盲盒
                // interactiveConfigRequire 互动配置是否必输
                if(this.pageParam.interactiveConfigRequire && this.$utils.isEmpty(this.interactive.template)){
                    this.$message.warn(`互动配置必输，请选择！`,{customFlag:true});
                    return
                }
                let flag = true;
                if (!this.$utils.isEmpty(this.arrNodes)) {
                    for (let i = 0; i < this.arrNodes.length; i++) {
                        if (this.$utils.isEmpty(this.arrNodes[i].backBaseId) &&
                                (this.arrNodes[i].evetype === 'LotteryTicket' ||
                                this.arrNodes[i].evetype === 'WheelCamp' ||
                                this.arrNodes[i].evetype === 'RoundWheel' ||
                                this.arrNodes[i].evetype === 'Sales' ||
                                this.arrNodes[i].evetype === 'SignIn' ||
                                this.arrNodes[i].evetype === 'Invitation' ||
                                this.arrNodes[i].evetype === 'SmashGoldenEgg' ||
                                this.arrNodes[i].evetype === 'SlotMachine' ||
                                this.arrNodes[i].evetype === 'BlindBox' ||
                                this.arrNodes[i].evetype === 'IceTrue' ||
                                this.arrNodes[i].evetype === 'ExpansionRule'  ||
                                this.arrNodes[i].evetype === 'Message' ||
                                this.arrNodes[i].evetype === 'WxMessage' ||
                                this.arrNodes[i].evetype === 'Questionnaire' ||
                                this.arrNodes[i].evetype === 'TreasureChest')){
                            this.$message.warn(`有互动未编辑保存，请检查`,{customFlag:true});
                            flag = false;
                        }
                    }
                }
                if (flag) {
                    // 更新营销活动
                    let nodesObject = {
                        nodes: this.arrNodes,
                    };
                    this.activityItem.actJson = JSON.stringify(nodesObject);
                    this.activityItem.row_status = ROW_STATUS.UPDATE;
                    const data = await this.$http.post('action/link/marketAct/upsert', this.activityItem, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$utils.hideLoading();
                            this.$showError('保存营销活动失败！' + response.result);
                        }
                    });
                    if (data.success) {
                        this.activityItem = {...data.newRow};
                        this.$dataService.setMarketActivityItem(data.newRow);
                    }
                    if (operant === 'next') {
                        this.$nav.push('/pages/lj-market-activity/market-activity/new-market-activity-other-info-page', {
                            data: this.activityItem,
                            sceneSourceForNavigation: this.sceneSourceForNavigation
                        })
                    } else if (operant === 'save') {
                        this.$nav.back(null,2);
                    }
                    this.$bus.$emit('refreshInteractionItem');
                }
            },
            /**
             * 数量减一
             * <AUTHOR>
             * @date 2020-08-06
             * */
            minusNumber(item) {
                if (item.qty > 1) {
                    item.qty = parseInt(item.qty) - 1;
                }
            },
            /**
             * 数量加一
             * <AUTHOR>
             * @date 2020-08-06
             * */
            plusNumber(item) {
                if (this.$utils.isEmpty(item.qty)) {
                    item.qty = 1;
                } else {
                    item.qty = parseInt(item.qty) + 1;
                }
            },
            /**
             * 选择模版
             * <AUTHOR>
             * @date 2020-08-20
             * */
            async chooseTemplate() {
                if (this.pageFrom === 'MarketingActivityModel') return
                this.tempList = await this.$object(this.chooseTemplateOption, {multiple: false});
                let updateTempFLag = false;//更新模板时 前模板是否存在已经编辑保存的互动信息
                //有已经保存编辑过的活动信息 切换营销模板的时候需要提示用户
                //已存在编辑保存的互动信息，重新选择营销模板会失效已保存的互动信息，是否确认重新选择营销模板？
                // 红包或大转盘或微信签到或邀请或砸金蛋或老虎机
                if (!this.$utils.isEmpty(this.arrNodes)) {
                    for (let i = 0; i < this.arrNodes.length; i++) {
                        if (!!this.arrNodes[i].backBaseId
                            && (this.arrNodes[i].evetype === 'WheelCamp' ||
                                this.arrNodes[i].evetype === 'LotteryTicket' ||
                                this.arrNodes[i].evetype === 'RoundWheel' ||
                                this.arrNodes[i].evetype === 'Sales' ||
                                this.arrNodes[i].evetype === 'SignIn' ||
                                this.arrNodes[i].evetype === 'Invitation' ||
                                this.arrNodes[i].evetype === 'SmashGoldenEgg' ||
                                this.arrNodes[i].evetype === 'SlotMachine' ||
                                this.arrNodes[i].evetype === 'BlindBox' ||
                                this.arrNodes[i].evetype === 'IceTrue' ||
                                this.arrNodes[i].evetype === 'ExpansionRule'  ||
                                this.arrNodes[i].evetype === 'Message' ||
                                this.arrNodes[i].evetype === 'WxMessage' ||
                                this.arrNodes[i].evetype === 'Questionnaire' ||
                                this.arrNodes[i].evetype === 'TreasureChest')) {
                            updateTempFLag = true;
                        }
                    }
                }
                if (updateTempFLag) {
                    this.$refs.tempUpdateConfirm.show()
                } else {
                    //更新活动上模板信息的对象
                    this.$set(this.interactive, 'template', this.tempList.activityName);
                    this.arrNodes = JSON.parse(this.tempList.actJson).nodes;
                    this.arrNodes.forEach(i => {
                        // 标识有红包或大转盘或微信签到或邀请或砸金蛋或老虎机或盲盒
                        this.flag = i.evetype === 'LotteryTicket' || i.evetype === 'Sales' || i.evetype === 'WheelCamp' || i.evetype === 'RoundWheel' || i.evetype === 'SignIn' || i.evetype === 'Invitation' || i.evetype === 'SmashGoldenEgg' || i.evetype === 'SlotMachine' || i.evetype === 'BlindBox' || i.evetype === 'IceTrue' || i.evetype === 'ExpansionRule' || i.evetype === 'Message' || i.evetype === 'WxMessage' || i.evetype === 'Questionnaire'|| i.evetype === 'TreasureChest';
                    });
                    //切换模板时flag为false时，当前模板无互动需要把活动上的actJson字段清空。
                    let updateActJson = this.tempList.actJson;
                    if(!this.flag){
                        updateActJson = '';
                    }
                    const updateActivityTemplateInfo = {
                        templateName: this.tempList.activityName,
                        templateId: this.tempList.id,
                        id: this.activityItem.id,
                        actJson: updateActJson,
                        rowVersion: this.activityItem.rowVersion,
                        updateFields: "id,templateName,templateId,actJson,rowVersion"
                    };
                    //更新活动上的模板ID和模板名称
                    const data = await this.$http.post('action/link/marketAct/update', updateActivityTemplateInfo);
                    this.activityItem = {...data.newRow};
                    this.$dataService.setMarketActivityItem(data.newRow);
                    this.$bus.$emit('updateActivityTemplateRefresh');
                }
            },
            /**
             * 确认更新营销模板时
             * 1、更新活动上营销模板信息
             * 2、失效之前营销模板下的活动信息
             * */
            async associatedUpdateTemp() {
                for (let i = 0; i < this.arrNodes.length; i++) {
                    if (!!this.arrNodes[i].backBaseId
                        && (this.arrNodes[i].evetype === 'LotteryTicket' || this.arrNodes[i].evetype === 'WheelCamp' || this.arrNodes[i].evetype === 'RoundWheel' || this.arrNodes[i].evetype === 'Sales'
                            || this.arrNodes[i].evetype === 'SignIn' ||  this.arrNodes[i].evetype === 'Invitation'
                            || this.arrNodes[i].evetype === 'SmashGoldenEgg' || this.arrNodes[i].evetype === 'SlotMachine'
                            || this.arrNodes[i].evetype === 'BlindBox' || this.arrNodes[i].evetype === 'IceTrue'
                            || this.arrNodes[i].evetype === 'ExpansionRule' || this.arrNodes[i].evetype === 'Questionnaire' || this.arrNodes[i].evetype === 'TreasureChest')) {
                        await this.$http.post(this.$env.appURL + '/interaction/link/interaction/occupyReward', {
                            id: this.arrNodes[i].backBaseId,
                            status: 'Inactive'
                        });
                    } else if (!!this.arrNodes[i].backBaseId && this.arrNodes[i].evetype === 'WxMessage') {
                        await this.$http.post(this.$env.appURL + '/marketactivity/link/wxMsgRecord/deleteById', {
                            id: this.arrNodes[i].backBaseId
                        });
                    } else if (!!this.arrNodes[i].backBaseId && this.arrNodes[i].evetype === 'Message'){
                        await this.$http.post(this.$env.appURL + '/marketactivity/link/messageRecord/deleteById', {
                            id: this.arrNodes[i].backBaseId
                        });
                    }
                }
                this.$refs.tempUpdateConfirm.hide();
                this.$set(this.interactive, 'template', this.tempList.activityName);
                this.arrNodes = JSON.parse(this.tempList.actJson).nodes;
                this.arrNodes.forEach(i => {
                    this.flag = i.evetype === 'LotteryTicket' || i.evetype === 'Sales' || i.evetype === 'WheelCamp' || i.evetype === 'RoundWheel' || i.evetype === 'SignIn' || i.evetype === 'Invitation' || i.evetype === 'SmashGoldenEgg' || i.evetype === 'SlotMachine' || i.evetype === 'BlindBox' || i.evetype === 'IceTrue' || i.evetype === 'ExpansionRule'  || i.evetype === 'Message' || i.evetype === 'WxMessage' || i.evetype === 'Questionnaire' || i.evetype === 'TreasureChest';
                });
                let updateActivityTemplateInfo;
                if(this.flag){
                    updateActivityTemplateInfo = {
                        templateName: this.tempList.activityName,
                        templateId: this.tempList.id,
                        id: this.activityItem.id,
                        rowVersion: this.activityItem.rowVersion,
                        updateFields: "id,templateName,templateId,rowVersion"
                    };//更新活动上模板信息的对象
                } else {
                    //切换模板时flag为false时，当前模板无互动需要把活动上的actJson字段清空。
                    updateActivityTemplateInfo = {
                        templateName: this.tempList.activityName,
                        templateId: this.tempList.id,
                        id: this.activityItem.id,
                        actJson:'',
                        rowVersion: this.activityItem.rowVersion,
                        updateFields: "id,templateName,templateId,actJson,rowVersion"
                    };//更新活动上模板信息的对象
                }
                //更新活动上的模板ID和模板名称
                const actData = await this.$http.post('action/link/marketAct/update', updateActivityTemplateInfo);
                if (actData.success) {
                    this.activityItem = {...actData.newRow};
                    this.$dataService.setMarketActivityItem(actData.newRow);
                    this.$bus.$emit('updateActivityTemplateRefresh');
                } else {
                    this.$showError('更新营销活动失败！' + actData.result,{customFlag:true});
                }

            }
        }
    }
</script>

<style lang="scss">
    .interactive-configuration-page {
        .base {
            padding: 24px 24px 0;
            overflow: hidden;
            /*deep*/ .link-item{
            border-radius: 12px;
        }
        }
        .no-cost-view {

            .no-cost {
                width: 368px;
                height: 368px;
                background-repeat: no-repeat;
                background-size: 100% 100%;
                -moz-background-size: 100% 100%;
                margin: auto;
            }

            .no-cost-msg {
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                text-align: center;
                line-height: 38px;
                padding-top: 30px;
            }
        }

        .template-info-view {
            .template-info-date {
                width: 100%;
                height: 168px;
                background: white;
                border-bottom: 2px solid #F2F2F2;

                .date-line {
                    width: 100%;
                    height: 84px;
                    line-height: 84px;

                    .title {
                        font-size: 28px;
                        color: #595959;
                        letter-spacing: 0;
                        float: left;
                        text-align: left;
                        padding-left: 24px;
                    }

                    .date {
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 0;
                        text-align: right;
                        padding-right: 24px;
                    }
                }
            }

            .template-info {
                background: white;

                .info-title {
                    font-size: 28px;
                    color: #595959;
                    letter-spacing: 0;
                    line-height: 28px;
                    padding: 24px;
                }

                .info-line {
                    width: 100%;
                    height: 92px;

                    .title {
                        padding: 24px;
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 28px;
                        width: 65%;
                        float: left;

                    }

                    .num-view {
                        width: 35%;
                        float: right;

                        .num-v-o {
                            .num-v-s {
                                width: 168px;
                                height: 48px;
                                margin: 14px 24px auto auto;
                                background: #F2F2F2;
                                border-radius: 8px;

                                .minu-num {
                                    width: 30%;
                                    float: left;
                                    text-align: center;
                                }

                                .num {
                                    width: 40%;
                                    font-size: 28px;
                                    color: #262626;
                                    letter-spacing: 0;
                                    text-align: center;
                                    line-height: 28px;
                                    float: left;
                                }

                                .plus-num {
                                    width: 30%;
                                    float: right;
                                    text-align: center;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
</style>
