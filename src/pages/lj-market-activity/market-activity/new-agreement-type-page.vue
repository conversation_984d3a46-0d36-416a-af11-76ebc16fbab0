<template>
    <link-page>
        <link-dialog ref="chooseTypeDialog" verticalFootButton disabledHideOnClickMask>
            <view slot="head">
                操作确认
            </view>
            <view>
                请选择协议类型
            </view>
            <link-button slot="foot" @tap="addAgreement('displayCabinet', 'display')">陈列协议</link-button>
            <link-button slot="foot" @tap="addAgreement('displayCabinet', 'Cabinet')">酒柜协议</link-button>
            <!-- 240122 by lld 取消市场活动新建门头协议 -->
            <!-- <link-button slot="foot" @tap="addAgreement('signboard')">门头协议</link-button> -->
            <link-button slot="foot" @tap="addAgreement('displayCabinet', 'others')">其他协议</link-button>
        </link-dialog>
    </link-page>
</template>

<script>
    export default {
        name: "new-agreement-type-page",
        data() {
            const formData = this.pageParam.data;
            return {
                formData
            }
        },
        mounted() {
            this.$refs.chooseTypeDialog.show();
        },
        methods: {
            onBack() {
                this.$nav.back();
            },
            // AGR_TYPE 协议类型
            //display 陈列协议 Cabinet 酒柜协议 signboard 门头协议 others 其他协议
            async addAgreement(input, agrType = '') {
                let id;
                let accntName = '';
                if (!this.$utils.isEmpty(this.formData['beneficiaryId'])) {
                    id = this.formData['beneficiaryId'];
                    accntName = this.formData.beneficiaryName;
                } else {
                    id = await this.$newId();
                }
                const clientDetails = {
                    id: id,
                    accntName: accntName,
                    agrType
                };
                if (input === 'signboard') {
                    this.$nav.push('/pages/terminal/visit/door-collect-page', {
                        data: clientDetails,
                        parentData: this.formData,
                        pageFrom: 'marketActivity',
                        isEditFlag: true,
                    });
                } else if (agrType === 'display') {
                    this.$nav.push('/pages/terminal2/protocol/protocol-edit-page.vue', {
                        data: {
                            id: clientDetails.id,
                            acctName: clientDetails.accntName,
                        },
                        activityData: this.formData,
                        source: 'activity'
                    });
                } else if (input === 'displayCabinet') {
                    this.$nav.push('/pages/lj-market-activity/market-activity/displays-protocol-page', {
                        data: clientDetails,
                        parentData: this.formData,
                        pageFrom: 'marketActivity',
                        isEditFlag: true,
                    });
                }
                this.$refs.chooseTypeDialog.hide();
            },
        }
    }
</script>

<style scoped>

</style>
