# 市场活动
```
创建时间：2022/01/10 15:00
创建人：  宋燕荣
```
### 模块介绍
```
项目整合销售助手、稽核系统、简道云、微信报备、线下excel五大市场活动管理渠道为一体并打通CRM系统，NC系统两大费用系统，构建执行案申请审批--活动申请提报--活动执行反馈--活动费用确认回传全流程，完成费用和市场活动的双闭环，且辅以一键报备、海报分享、品鉴酒开瓶扫码核销等便捷性操作，解决用户跨多平台多线程提报活动和重复操作的问题
```
### 费用场景

* 市场活动费用场景包括两部分：

1、基于执行案的活动费用提报全流程：执行案同步--活动提报--活动执行反馈--费用确认实发；
2、无执行案的活动费用提报全流程：活动提报—执行案同步--活动执行反馈—费用确认实发；

### 名单提报

* 活动及活动名单提报场景：
```
1、活动创建
2、名单创建
3、名单下发
4、名单提报
5、名单审批
6、活动发布；
步骤123在【市场活动】模块，步骤45在【活动名单提报】模块
```
### 文档内容

```
1、市场活动总体介绍.md
2、市场活动-状态流转.md
3、市场活动-数据存储.md
4、市场活动-涉及组件.md
5、市场活动-缓存机制.md
6、市场活动-页面配置.md
7、市场活动-Q&A.md
8、市场活动-安全性.md
9、市场活动-功能.md
```

###涉及对象 12个
```
A、市场活动基础信息
B、费用申请信息
C、互动配置
D、消费者
E、内部人员
F、参与终端/经销商
G、活动动销
H、执行反馈
I、稽核记录
J、报销进度
K、审批历史
L、参与情况
```

### 涉及组件 18个
* 组件路径：都在src/pages/lj-market-activity/market-activity/components文件夹内

### 逻辑功能实现
```
1、新建活动
2、活动作废
3、活动列表展示、按状态快捷查询、筛选、排序、模糊查询
4、提交活动申请
5、一键报备
6、分享海报
7、创建名单
8、名单提报名额调整
9、活动稽核
10、执行反馈
11、提交费用审批
12、清除执行案编码功能及操作权限【活动阶段和场景】
13、新增、删除、编辑、查看费用申请-现金类兑付方式数据
14、新增、编辑、查看费用实际-现金类兑付方式数据
15、新增、删除、编辑、查看费用申请-产品类兑付方式数据
16、新增、编辑、查看费用实际-产品类兑付方式数据
17、维护、切换、清除互动信息
18、选择消费者关联到当前活动、新建消费者
19、选择内部人员关联到当前活动
20、选择参与终端/经销商关联到当前活动
21、新增、更新、删除活动动销数据
22、执行反馈阶段维护业务场景图片，拍照加水印【时间、地址、一些业务信息】、相册选择
```
------ 市场活动-总体介绍-内容结束 ------
