<template>
    <link-page class="market-activity-list-page">
        <lnk-taps :taps="marketActivityOptions" v-model="marketActivityStatusActive"
                  @switchTab="onTap" v-if="!pageParam.source"></lnk-taps>
        <view class="sort-search-filter-container">
            <view class="sort-search-filter" :style="{'z-index': zIndex,'margin-top': '-1px'}">
                <view>
                    <link-search-input v-model="searchVal" :placeholder="'活动名称/活动编码/执行案编码/现金申请金额/提报人'"/>
                </view>
                <view class="sort-filter-view">
                    <link-sort
                            :options="sortOptions"
                            :value="sortVal[0]"
                            @input="sortChange"/>
                    <view class="search-filter">
                        <link-filter v-model="filterOption"/>
                    </view>
                </view>
            </view>
        </view>
        <link-auto-list :option="autoList" style="margin-top: 70px;">
            <template slot-scope="{data,index}">
                <link-swipe-action>
                    <link-swipe-option slot="option" @tap="invalidMarketAct(data,index)">
                        作废
                    </link-swipe-option>
                    <item :key="index" :data="data" :arrow="false" class="market-activity-list-item"
                          @tap="gotoItem(data)">
                        <market-activity-item :data="data" source="marketAct" :priceShowFlag="priceShowFlag" slot="note"/>
                    </item>
                </link-swipe-action>
            </template>
        </link-auto-list>
        <link-dialog ref="stateDialog" :initial="true">
            <view slot="head">
                提示
            </view>
            <view>
                {{stateDialogMsg}}
            </view>
            <link-button slot="foot" @tap="$refs.stateDialog.hide()">确定</link-button>
        </link-dialog>
        <link-dialog ref="invalidMarketActDialog" :initial="true">
            <view slot="head">
                提示
            </view>
            <view>
                活动作废后不可逆转，是否确定作废?
            </view>
            <link-button slot="foot" @tap="$refs.invalidMarketActDialog.hide()">取消</link-button>
            <link-button slot="foot" @tap="invalidMarketActConfirm">确定</link-button>
        </link-dialog>
    </link-page>
</template>

<script>
    import {ROW_STATUS} from "../../../utils/constant";
    import Taro from "@tarojs/taro";
    import StatusButton from "../../lzlj/components/status-button";
    import LnkTaps from "../../core/lnk-taps/lnk-taps";
    import {ComponentUtils,getFiltersRaw} from "link-taro-component";
    import MarketActivityItem from './components/market-activity-item';

    export default {
        name: "market-activity-list-page",
        components: {LnkTaps, StatusButton, MarketActivityItem},
        data() {
            const menuId = this.pageParam.menuId;
            const accessGroupOauth = this.$utils.getMenuAccessGroup(menuId);
            let param = {
                queryFields:'created,activityNum,status,aproStatusForFilter,activityName,aproStatus,whetherAudit,startTime,endTime,executor,cashApplyAmount,prodApplyAmount,cashFillAmount,prodFillAmount,cashRealAmount,prodRealAmount,id,busScene'
            };
            if (!this.$utils.isEmpty(accessGroupOauth)) {
                param = {
                    oauth: accessGroupOauth,
                    queryFields:'created,activityNum,status,aproStatusForFilter,activityName,aproStatus,whetherAudit,startTime,endTime,executor,exeCaseCode,cashApplyAmount,prodApplyAmount,cashFillAmount,prodFillAmount,cashRealAmount,prodRealAmount,id,busScene'
                }
            }
            if(this.pageParam.source === 'home'){
                let nowDate = new Date();
                let startDate = this.$date.format(nowDate, 'YYYY-MM-DD HH:mm:ss');
                nowDate.setDate(nowDate.getDate()+3)
                let lastDate = this.$date.format(nowDate, 'YYYY-MM-DD HH:mm:ss');
                param.filtersRaw = [
                    {
                        id: 'aproStatus',
                        property: 'aproStatus',
                        value: '[Approve,RefeedWithdraw]',
                        operator: 'in'
                    }, {
                        id: 'status',
                        property: 'status',
                        value: 'Inactive',
                        operator: '<>'
                    }, {
                        id: 'startTime',
                        property: 'startTime',
                        value: lastDate,
                        operator: '<'
                    }, {
                        id: 'startTime',
                        property: 'startTime',
                        value: startDate,
                        operator: '>'
                    }
                ]
            }
            const autoList = new this.AutoList(this, {
                module: '',
                createPath: '/pages/lj-market-activity/market-activity/new-market-activity-page',
                param: param,
                disabled:{
                    creatable: (() => {
                        //片区经理在新建活动提交、执行反馈的提交、活动名单提报的提交页面，均不可以进行提交动作。
                        return !['CityManager', 'SalesRegionManager', 'SalesAreaManager',].includes(Taro.getStorageSync('token').result.positionType) && this.pageParam.source !== 'home'
                    })
                },
                sortField: 'created',
                sortDesc: 'desc',
                loadOnStart: false,
                hooks: {
                    async beforeCreateItem(param) {
                        const countey = await this.$lov.getValByTypeAndName('COUNTRY_ID', '中国');
                        param.data = {
                            row_status: ROW_STATUS.NEW,
                            executor: this.userInfo.firstName,
                            executorPhone: this.userInfo.contactPhone,
                            status: "New",//活动状态默认新建
                            actStage: "ActReport",//活动阶段默认活动提报
                            aproStatus: "New",//审批状态默认未提交
                            whetherAudit: "NoCheck",//稽核状态默认未稽核 -- 2022-5-31市场抽检优化
                            companyId: this.userInfo.coreOrganizationTile.l3Id,//公司id
                            salesBigAreaId: this.userInfo.coreOrganizationTile.l4Id,//大区id
                            salesBigArea: this.userInfo.coreOrganizationTile.l4Name,
                            salesRegionId: this.userInfo.coreOrganizationTile.l5Id,//片区ID
                            salesRegion: this.userInfo.coreOrganizationTile.l5Name,
                            salesCityId: this.userInfo.coreOrganizationTile.l6Id,//城市ID
                            salesCity: this.userInfo.coreOrganizationTile.l6Name,
                            salesDistrictId: this.userInfo.coreOrganizationTile.l7Id,//区县ID
                            salesDistrict: this.userInfo.coreOrganizationTile.l7Name,
                            targetPopulation: 'OrdinaryConsumers',
                            countryName: countey,
                        };
                    }
                },
                sortOptions: null,
            });
            const marketActivityOptions = [
                {name: '全部', seq: '1', val: 'All', property: 'customField',},
                {name: '草稿', seq: '2', val: 'Draft', property: 'customField',},
                {name: '待审批', seq: '3', val: 'Pending', property: 'customField',},
                {name: '即将开始', seq: '4', val: 'AboutToBegin', property: 'customField',},
                {name: '待反馈', seq: '5', val: 'ToFeedback', property: 'customField',},
                {name: '已实发', seq: '6', val: 'ActualAmount', property: 'customField',}
            ];
            const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
            return {
                // 费用价格是否展示标识
                priceShowFlag: false,
                currentObj: {},
                stateDialogMsg: "",
                marketActivityOptions,
                autoList,
                userInfo,
                searchVal: '',
                sortVal: {
                    0: {field: 'created', desc: true, type: 'desc'},
                },
                zIndex: ComponentUtils.nextIndex(),
                marketActivityStatusActive: {},
                sortOptions: [
                    {label: '活动开始日期', field: 'startTime', desc: false},
                    {label: '现金申请金额', field: 'cashApplyAmount', desc: false},
                ],
                filterOption: [
                    {label: '活动状态', field: 'statusForFilter', type: 'lov', lov: 'MC_STATUS', multiple: false},
                    {label: '审批状态', field: 'aproStatusForFilter', type: 'lov', lov: 'APRO_STATUS', multiple: false},
                    {label: '活动开始时间', field: 'startTime', type: 'date'},
                    {label: '活动结束时间', field: 'endTime', type: 'date'},
                ],
                copyActivityParam: null,
            };
        },
        watch: {
            searchVal(newVal, oldVal) {
                if(this.$utils.isNotEmpty(this.autoList.option.param['filtersRaw'])){
                    this.autoList.option.param['filtersRaw'] = [];
                }
                if (newVal !== oldVal) {
                    if (this.$utils.isEmpty(newVal)){
                        this.autoList.methods.reload();
                        return;
                    }
                    const searchObj = {
                        id: "searchValue",
                        operator: "or like",
                        property: "[activityNum,activityName,cashApplyAmount,executor,exeCaseCode]",
                        value: newVal
                    };
                    this.autoList.option.param['filtersRaw'] = this.autoList.option.param['filtersRaw'].filter((item) => item.property !== "[activityNum,activityName,cashApplyAmount,executor]");
                    this.autoList.option.param['filtersRaw'].push(searchObj);
                    this.autoList.methods.reload()
                }
            },
            filterOption() {
                this.filterList(getFiltersRaw(this.filterOption))
            }
        },
        mounted() {
            this.$bus.$on('marketActivityListRefresh', async () => {
                await this.autoList.methods.reload();
                this.$bus.$emit('updateNoteList');
            });
        },
        async created() {
            this.marketActivityStatusActive = this.marketActivityOptions[3];
            //财务人员：FinanceStaff；稽核人员：AuditStaff
            if (!(this.userInfo.positionType === 'FinanceStaff' || this.userInfo.positionType === 'AuditStaff')) {
                this.autoList.option.module = 'export/link/marketAct';
            } else {
                this.autoList.option.url = {
                    queryByExamplePage: 'export/link/marketAct/queryByExamplePage'
                };
            }
            if(!this.pageParam.source){
                this.autoList.option.param['filtersRaw'] = [{
                    id: 'status',
                    property: 'status',
                    value: 'Published'
                }];
            }
            this.copyActivityParam = this.$utils.deepcopy(this.autoList.option.param);
            await this.autoList.methods.reload();
            if (this.$utils.isEmpty(this.pageParam) && (this.pageParam.msg === 'reload')) {
                await this.autoList.methods.reload();
            }
            this.priceShowFlag = await this.$utils.getPriceDesensitize(this.userInfo);
            this.$store.commit('macTemplate/setMacTemplateId', '');
        },
        methods: {
            /**
             * 筛选函数
             * <AUTHOR>
             * @date 2020-11-19
             * @param param
             */
            async filterList(param) {
                const that = this;
                if (this.$utils.isEmpty(param)) {
                    this.copyActivityParam.filtersRaw = this.copyActivityParam.filtersRaw.filter((item) => !(item.property === 'statusForFilter' || item.property === 'aproStatusForFilter'));
                    this.autoList.option.param = this.copyActivityParam;
                    await this.autoList.methods.reload();
                } else {
                    that.autoList.option.param.filtersRaw = that.autoList.option.param.filtersRaw.filter((item) => !(item.property === 'statusForFilter' || item.property === 'aproStatusForFilter'));
                    param.forEach(item => {
                        that.autoList.option.param.filtersRaw.push(item);
                    });
                    await that.autoList.methods.reload();
                }
                this.copyActivityParam = this.$utils.deepcopy(this.autoList.option.param);
            },
            /**
             *  @description: tab页签切换
             *  @author: songyanrong
             *  @date: 2020/11/19 14:16
             */
            /**
             * 活动状态：MC_STATUS
             * 审批状态 APRO_STATUS
             * 全部：保留现有功能；
             * 草稿：活动状态=新建，且活动审批状态=未提交、已拒绝；
             * 待审批：活动状态=新建&进行中&执行结束，且活动审批状态=提报待审批&反馈待审批；
             * 即将开始：活动状态=已发布；
             * 待反馈：活动状态=进行中&执行结束，且活动审批状态=申请审批通过&反馈驳回&反馈撤回；
             * 已实发：活动状态=已实发；
             * 从市场活动菜单点击进去列表，默认定位在「即将开始」的位置；
             * customField
             * */
            async onTap(item) {
                let statusConditions = {};//活动状态条件
                let aproStatusConditions = {};//活动审批状态条件
                if (item.val === 'All') {//全部
                    statusConditions = {};
                    aproStatusConditions = {};
                }
                if (item.val === 'Draft') {//草稿
                    statusConditions = {
                        id: 'status',
                        property: 'status',
                        value: 'New'
                    };
                    aproStatusConditions = {
                        id: 'aproStatus',
                        property: 'aproStatus',
                        value: '[New,Refused]',
                        operator: 'IN'
                    };
                }
                if (item.val === 'Pending') {//待审批
                    statusConditions = {
                        id: 'status',
                        property: 'status',
                        value: '[New,Processing,Closed]',
                        operator: 'IN'
                    };
                    aproStatusConditions = {
                        id: 'aproStatus',
                        property: 'aproStatus',
                        value: '[Submitted,Feedback]',
                        operator: 'IN'
                    };
                }
                if (item.val === 'AboutToBegin') {//即将开始
                    statusConditions = {
                        id: 'status',
                        property: 'status',
                        value: 'Published'
                    };
                    aproStatusConditions = {};
                }
                if (item.val === 'ToFeedback') {//待反馈
                    statusConditions = {
                        id: 'status',
                        property: 'status',
                        value: '[Processing,Closed]',
                        operator: 'IN'
                    };
                    aproStatusConditions = {
                        id: 'aproStatus',
                        property: 'aproStatus',
                        value: '[Approve,Refeedback,RefeedWithdraw]',
                        operator: 'IN'
                    };
                }
                if (item.val === 'ActualAmount') {//已实发
                    statusConditions = {
                        id: 'status',
                        property: 'status',
                        value: 'ActualAmount'
                    };
                    aproStatusConditions = {};
                }
                const that = this;
                const statusExist = that.autoList.option.param.filtersRaw.filter((item) => item.property === 'status');
                const aproStatusExist = that.autoList.option.param.filtersRaw.filter((item) => item.property === 'aproStatus');
                if (!this.$utils.isEmpty(statusExist)) {
                    let index = that.autoList.option.param.filtersRaw.findIndex(val => val.property === 'status');
                    that.autoList.option.param.filtersRaw.splice(index, 1);
                }
                if (!this.$utils.isEmpty(statusConditions)) {
                    that.autoList.option.param.filtersRaw.push(statusConditions);
                }
                if (!this.$utils.isEmpty(aproStatusExist)) {
                    let index = that.autoList.option.param.filtersRaw.findIndex(val => val.property === 'aproStatus');
                    that.autoList.option.param.filtersRaw.splice(index, 1);
                }
                if (!this.$utils.isEmpty(aproStatusConditions)) {
                    that.autoList.option.param.filtersRaw.push(aproStatusConditions);
                }
                this.copyActivityParam = this.$utils.deepcopy(this.autoList.option.param);
                await that.autoList.methods.reload();
            },
            async gotoItem(data) {
                const cacheData = await this.$http.post('export/link/marketAct/queryById', {
                    id: data.id
                });
              if(cacheData.success){
                this.$dataService.setMarketActivityItem(cacheData.result);
                this.$nav.push('/pages/lj-market-activity/market-activity/market-activity-item-page', {
                    data: cacheData.result,
                    pageSource: "view" //标志界面来源 区别于"其他信息"界面的预览活动
                })
              }
            },
            /**
             * 排序函数
             * <AUTHOR>
             * @date 2020-11-18
             * @param val
             */
            sortChange(val) {
                this.autoList.sortField = val.field;
                this.autoList.sortDesc = val.desc;
                this.autoList.methods.reload();
            },
            /**
             * 作废活动
             * <AUTHOR>
             * @date 2020-12-24
             * */
            async invalidMarketAct(item, index) {
                this.currentObj = item;
                // 可以作废场景
                // 活动状态 MC_STATUS =新建、已发布、进行中、执行结束，发布失败, 发布中
                // 活动审批状态 APRO_STATUS =未提交、提报待审批、已拒绝、申请审批通过、反馈待审批、反馈驳回、反馈撤回时
                if ((item.status === 'New' || item.status === 'Published' || item.status === 'Processing' || item.status === 'Closed' || item.status === 'PublishFailed' || item.status === 'Publishing')
                    && (item.aproStatus === 'New' || item.aproStatus === 'Submitted' || item.aproStatus === 'Refused'
                        || item.aproStatus === 'Approve' || item.aproStatus === 'Feedback' || item.aproStatus === 'Refeedback'
                        || item.aproStatus === 'RefeedWithdraw')) {
                    this.$refs.invalidMarketActDialog.show();
                } else {
                    const statusName = await this.$lov.getNameByTypeAndVal('MC_STATUS', item.status);
                    const aproStatusName = await this.$lov.getNameByTypeAndVal('APRO_STATUS', item.aproStatus);
                    this.stateDialogMsg = `状态是${statusName},审批状态是${aproStatusName}的活动不能作废`;
                    this.$refs.stateDialog.show();
                }
            },
          async invalidMarketActConfirm() {
            this.$utils.showLoading();
            await this.$http.post('action/link/marketAct/invalidMarketAct', {id: this.currentObj.id},{
              handleFailed: data => {
                this.$utils.hideLoading();
                this.$showError('作废失败，请稍后重试！' + data.result);
              }
            });
            this.$utils.hideLoading();
            this.$refs.invalidMarketActDialog.hide();
            this.autoList.methods.reload();
          },
        }
    }
</script>

<style lang="scss">
    @import "../../../styles/list-card";
    .market-activity-list-page {
        background-color: #F2F2F2;
        font-family: PingFangSC-Regular;
        .link-input{
            width: 100%;
        }
        .sort-search-filter-container {
            height: 100px;
            margin-bottom: 22px;

            .sort-search-filter {
                position: fixed;
                width: 100%;
                background: #ffffff;
                margin-top: 2px;
                border-top: 1px solid #F2F2F2;

                .sort-filter-view {
                    @include flex-start-center;
                    @include space-between;
                    border-top: 1px solid #f2f2f2;

                    .link-sort {
                        padding: 32px 0 24px 24px;
                        width: 50%;
                    }

                    .search-filter {
                        @include flex-start-center;

                        .icon-sousuo {
                            font-size: 28px;
                            color: #595959;
                            padding: 38px 24px 30px 24px;
                        }

                        .link-filter {
                            padding: 32px 24px 24px 16px;
                        }
                    }
                }
            }
        }

        /*deep*/
        .lnk-tabs-container {
            height: 92px;
        }

        .market-activity-list-item {
            background: #FFFFFF;
            width: 95%;
            margin: 24px auto auto auto;
            border-radius: 16px;
        }
    }
</style>
