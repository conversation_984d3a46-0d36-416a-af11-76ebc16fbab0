<template>
    <view class="progress-new">
        <view class="menu-stair" style="margin-bottom: 12px">
            <view class="line">
                <view class="line-top"></view>
                <view class="line-bottom"></view>
            </view>
            <view class="stair-title">报销进度</view>
        </view>
        <view class="progress-data">
            <view class="item-data"
                  v-for="(item,index) in progressList" :key="index">
                <view class="media-list">
                    <view class="media-top">
                        <view class="num-view">
                            <view class="num">{{item.bxCode}}</view>
                        </view>
                        <view class="status-view">
                            <view class="status">{{item.bxState}}</view>
                        </view>
                    </view>
                </view>
                <view class="content-middle">
                    <view class="name">{{item.bxTerminal}}</view>
                </view>
                <view class="content-middle-line">
                    <view class="data">客户费用单类型:{{item.reimType}}</view>
                    <view class="sum">报销金额:{{item.bxAmount|cny}}</view>
                </view>
                <view class="content-middle-line-2">
                    <view class="data">报销时间:{{item.reimTime}}</view>
                </view>
                <view class="content-middle-line-2">
                    <view class="data">制单日期:{{item.orderDate}}</view>
                </view>
                <view class="content-middle-line-2">
                    <view class="data">审批日期:{{item.apprDate}}</view>
                </view>
                <view class="content-middle-line-2" style="padding-bottom: 12px">
                    <view class="data">关闭日期:{{item.closeDate}}</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    export default {
        name: "progress-new",
        props: {
            //父ID
            parentId: {
                type: String,
                default: "",
            },
            //没用-页面来源 -
            // 1、执行反馈环节 executiveFeedback
            // 2、other 活动的其他信息(ps:这是一个页面)
            // 3、preview 活动预览界面而来
            // 4、view 活动查看界面查看而来
            // 5、审批或小程序消息进去 为空
            // 6、activityAudit 活动稽核
            pageSource: {
                type: String,
                default: ''
            },
        },
        async created() {
            await this.queryProgressList();
        },
        data() {
            return {
                progressList: [],//报销单列表
            }
        },
        methods: {
            /**
             * 查询报销单列表
             * <AUTHOR>
             * @date 2020-08-24
             * */
            async queryProgressList() {
                const data = await this.$http.post('action/link/reimbursement/queryByExamplePage', {
                    sort: "created",
                    order: 'desc',
                    filtersRaw: [{id: 'actHeadId', property: 'actHeadId', value: this.parentId, operator: '='}]
                });
                this.progressList = data.rows || [];
            }
        }
    }
</script>

<style lang="scss">
    @import "../../../../styles/list-card";
    .progress-new {
        .menu-stair {
            width:100%;
            margin-left: 24px;
            padding-top: 24px;
            @include flex-start-center;

            .line {
                .line-top {
                    width: 8px;
                    height: 16px;
                    background: #3FE0E2;
                }

                .line-bottom {
                    width: 8px;
                    height: 16px;
                    background: #2F69F8;
                }
            }

            .stair-title {
                width: 30%;
                margin-left: 16px;
                font-family: PingFangSC-Semibold, serif;
                font-size: 32px;
                color: #262626;
                letter-spacing: 1px;
                line-height: 32px;
            }
        }

        .progress-data {

            .item-data {
                background: white;
                margin: 24px;
                border-radius: 16px;

                .media-list {
                    @include media-list;

                    .media-top {
                        width: 100%;
                        @include flex-start-center;
                        @include space-between;
                        height: 80px;
                        line-height: 80px;

                        .left-content {
                            font-family: PingFangSC-Semibold;
                            font-size: 32px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 32px;
                            padding-top: 20px;

                        }

                        .right-content {
                            font-family: PingFangSC-Semibold;
                            font-size: 32px;
                            color: #FF5A5A;
                            letter-spacing: 0;
                            text-align: right;
                            line-height: 32px;
                            padding-top: 20px;
                        }

                        .num-view {
                            background: #A6B4C7;
                            border-radius: 8px;
                            line-height: 50px;
                            margin-left: 24px;

                            .num {
                                font-size: 28px;
                                color: #FFFFFF;
                                letter-spacing: 0;
                                line-height: 40px;
                                padding: 2px 8px;
                            }
                        }

                        .status-view {
                            width: 120px;
                            transform: skewX(-10deg);
                            border-radius: 4px;
                            background: #2F69F8;
                            box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                            height: 36px;
                            margin-right: 24px;

                            .status {
                                font-size: 20px;
                                color: #FFFFFF;
                                letter-spacing: 2px;
                                text-align: center;
                                line-height: 36px;
                            }
                        }
                    }
                }

                .content-middle {
                    width: 100%;
                    @include flex-start-center;
                    @include space-between;
                    height: 40px;
                    line-height: 40px;
                    margin-left: 24px;

                    .content {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #000000;
                        letter-spacing: 0;
                    }

                    .name {
                        font-family: PingFangSC-Semibold;
                        font-size: 32px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 32px;
                    }
                }

                .content-middle-line {
                    margin-top: 20px;

                    .data {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        line-height: 28px;
                        width: 55%;
                        float: left;
                        margin-left: 24px;
                    }

                    .sum {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #000000;
                        letter-spacing: 0;
                        line-height: 28px;
                        margin-right: 24px;
                    }
                }

                .content-middle-line-2 {
                    margin-top: 20px;

                    .data {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        line-height: 28px;
                        margin-left: 24px;
                    }
                }
            }
        }
    }
</style>
