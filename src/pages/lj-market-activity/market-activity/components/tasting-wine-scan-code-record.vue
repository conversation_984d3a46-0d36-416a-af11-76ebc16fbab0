<template>
    <view class="tasting-wine-scan-code-record" :class="{'isexecutivefeedback': !isexecutivefeedback}" v-if="scanInfoList.length > 0">
        <view class="head-top">
            <view class="main-title">{{title}}</view>
            <view class="more" @tap="seeMore" v-if="scanInfoList.length >= 4">
                <view>查看更多</view>
                <view class="icon">
                    <link-icon icon="icon-right" class="right-icon"></link-icon>
                </view>
            </view>
        </view>
        <view class="code-content">
            <link-swipe-action v-for="(data,index) in scanInfoList.slice(0, 3)" :key="index" >
                <item :key="index" :data="data" :arrow="false" class="code-record-list" @tap="toDetails(data)">
                    <view slot="note">
                        <view class="list-top">
                            <view class="code">产品编码： {{data.productCode}}</view>
                            <view class="del" v-if="deleteScanFlag && editFlag" @tap.stop="deleteItem(data)">
                                <link-icon  icon="icon-close-circle" class="del-icon"></link-icon>
                            </view>
                        </view>
                        <view class="list-middle">
                            {{data.productName}}
                        </view>
                        <view class="list-bottom clearfix">
                            <view class="list-bottom-item">
                                扫码时间：{{data.scanTime.split(' ')[0]}}
                            </view>
                            <view class="list-bottom-item bottom-r">
                                <text>扫码人： {{data.scanner}}</text>
                                <view class="icon">
                                    <link-icon icon="icon-right" class="right-icon"></link-icon>
                                </view>
                            </view>
                        </view>
                        <view class="qr-code">
                            <view class="qr-code-item">
                                <view class="lebal">盖外码：</view>
                                <view class="val">{{data.qrCodeOut && data.qrCodeOut.split('/').pop().substr(-8)}}</view>
                            </view>
<!--                            <view class="qr-code-item">-->
<!--                                <view class="val">{{data.isBulkPickup === 'Y' ? '批量赠送' : ''}}</view>-->
<!--                            </view>-->
                            <view class="qr-code-item"  v-if="data.qrCodeIn && data.scanSubType !== 'GiftScan'">
                                <view class="lebal">盖内码:</view>
                                <view class="val">{{data.qrCodeIn.split('/').pop().substr(-8)}}</view>
                            </view>
                        </view>
                        <view class="list-bottom clearfix">
                            <view class="list-bottom-item">
                                匹配状态：{{data.descriptionType | lov('MATCH_STATUS')}}
                            </view>
                        </view>
                    </view>
                </item>
            </link-swipe-action>
        </view>
    </view>
</template>

<script>
import {deleteScanCodeCheck} from '../deleteScanCodeCheck'
export default {
    name: "tasting-wine-scan-code-record",
    components: {deleteScanCodeCheck},
    props: {
        editFlag:{
            type: Boolean,
            default: true
        },
        actId: {
            type: String,
            default: ''
        },
        //场景-实际费用 actual 、申请费用 apply
        scene: {
            type: String
        },
        title: {
            type: String,
            default: '扫码记录'
        },
        //扫码类型 :OutScan 出库扫码，InScan 入库扫码，OpenScan 开瓶扫码，GiftScan 转赠扫码
        type: {
            type: String,
            default: ''
        },
        isexecutivefeedback: {
            type: Boolean,
            default: false
        },
        //父对象-活动对象
        parentData: {
            type: Object,
            default: function () {
                return {};
            }
        },
    },
    data() {
        //Y,有效   N,无效
        const imgList = {
            Y:'effective',
            N:'failure'
        }
        //正常入库、正常开瓶、正常转赠、正常出库
        const normalScanCode = ['Normalstorage', 'Normalbottled', 'Normalgifted', 'NormalDelivery']
        const detailTitle = {
            InScan: '入库扫码详情',
            OutScan: '出库扫码详情',
            OpenScan: '开瓶扫码详情',
            GiftScan: '赠送扫码详情'
        }
        const deleteUrl = {
            InScan: 'action/link/actScanRecord/deleteInStockScanRecord',
            OutScan: 'action/link/actScanRecord/deleteOutStockScanRecord',
            OpenScan: 'action/link/actScanRecord/bottledDeleteRecord',
            GiftScan: 'action/link/actScanRecord/giftDeleteRecord'
        };
        return {
            imgList,
            scanInfoList: [],
            normalScanCode,      //正常类型的扫码记录 扫码子类型 值列表类型: SCAN_SUB_TYPE
            deleteUrl,           //删除地址
            detailTitle,          //详情页面title
            deleteScanFlag: false,//是否可以侧滑删除扫码记录
            oneClick: false,          //点击事件是否在执行中，控制删除对应的逻辑只执行一次
        }
    },

    mounted() {
        this.$bus.$on('initCodeScanRecordList', async () => {
            await this.init();
        });
    },
    async created() {
        this.init();
        if(this.parentData['status'] === 'ActualAmount' || this.parentData['status'] === 'Inactive' || this.parentData['aproStatus'] === 'Feedback'){
            this.deleteScanFlag = false;
        } else {
            this.deleteScanFlag = true;
        }
        // this.deleteScanFlag = await deleteScanCodeCheck.checkFlag(this.actId);
    },
    methods: {
        seeMore() {
            const params = {
                actId: this.actId,
                scene: this.scene,
                title: this.title,
                type: this.type,
                editFlag: this.editFlag
            }
            this.$nav.push('/pages/lj-market-activity/market-activity/pinjianjiu-scan-code-list-page.vue',params)
        },
        async init() {
            const params = {
                filtersRaw: [
                    {
                        "id": "scanSubType",
                        "property": "scanSubType",
                        "value": this.type
                    }
                ],
                onlyCountFlag: false,
                oauth: 'ALL',
                sort: 'created',
                order: 'desc',
                rows: 4,
                actId: this.actId,
                scene: this.scene
            }
            try {
                const data = await this.$http.post('action/link/actScanRecord/queryByExamplePage', params)
                this.scanInfoList = data.rows
            }catch(e) {
                this.$showError('获取扫码数据失败', e)
            }
        },
        async deleteItem(data) {
            this.$dialog({
                title: '提示',
                initial: true,
                content: '确认是否删除该条扫码记录?',
                cancelButton: true,
                onConfirm: () => {
                    if(this.oneClick) return
                    this.oneClick = true
                    this.confirmDelete(data)
                },
            })
        },
        async confirmDelete(data) {
            const url = this.deleteUrl[data.scanSubType]
            const params =  {
                id: data.id,
            }
            try {
                const data = await this.$http.post(url, params);
                if(data.success) {
                    this.$message.success('删除数据成功');
                    await this.init()
                    this.$bus.$emit("initCodeScanRecordList");
                    //执行反馈阶段=费用实际。赠送 、开瓶后需要更新产品的实际数量
                    if(this.scene === 'actual'){
                        if(this.type === 'OpenScan' || this.type === 'GiftScan'){
                            this.$bus.$emit("scanCompleteInitCostList");
                        }
                    }
                    return
                }
                this.$showError('删除数据失败');
            }catch (e) {
                this.$showError(`删除数据异常:${e.result}`);
            }finally {
                this.oneClick = false
            }
        },
        toDetails(data) {
            const params =  {
                id: data.id,
                title: this.detailTitle[data.scanSubType]
            }
            this.$nav.push('/pages/lj-market-activity/market-activity/tasting-wine-scan-code-detail-page', params)
        }
    },
}
</script>

<style lang="scss">
.tasting-wine-scan-code-record{
    .link-swipe-action {
        width: 100%;
    }
    letter-spacing: 0;
    overflow-x: hidden;
    background-color: white;
    margin-top: 20px;

    .link-item-icon{
        width:0px;
        padding-left: 0px;
    }

    .head-top{
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: PingFangSC-Semibold;
        height: 88px;
        border-bottom: 2px solid #e5e5e5;
        padding: 0px 28px;
        font-size: 28px;

        .main-title{
            color: #262626;
        }

        .more{
            width: 22%;
            color: rgb(145,145,145);
            display: flex;
            .icon {
                flex: 1;
            }
        }
    }
    .code-record-list{
        padding-right: 0px;
        width: 95%;

        .qr-code{
            display: flex;
            font-size: 30px;
            .qr-code-item{
                display: flex;
                margin-right: 40px;
                .lebal{
                    color: black;
                    margin-right: 8px;
                }
                .val {
                    color: #2F69F8;
                }
            }
        }

        .list-top{
            font-size: 28px;
            display: flex;
            justify-content: space-between;
            .code{
                height: 40px;
                margin: 10px 0;
            }
            .del{
                flex: 1;
                display: flex;
                justify-content: flex-end;
                align-items: center;
                .del-icon{
                    font-size: 40px;
                    padding: 10px 0 10px 10px;
                }
            }
        }

        .list-middle{
            font-family: PingFangSC-Medium;
            font-size: 30px;
            color: #262626;
            line-height: 45px;
            margin: 10px 0;
        }

        .list-bottom{
            display: flex;
            padding: 10px 0 20px 0;
            .list-bottom-item{
                flex: 1;
                font-size: 28px;
            }
            .bottom-r {
                display: flex;
                justify-content: space-between;
                .icon{
                    width: 20%;
                    display: flex;
                    justify-content: flex-end;
                    .right-icon {
                        font-size: 35px;
                    }
                }
            }
        }

        .clearfix:after {
            content: "";
            display: block;
            height: 0;
            clear: both;
            visibility: hidden;
        }
    }
    .notexecutivefeedback {
        width: 90%;
    }
}
.isexecutivefeedback{
    .link-swipe-action {
        width: 95vw;
    }
}
</style>
