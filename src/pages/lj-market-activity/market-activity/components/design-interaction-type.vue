<template>
    <view class="design-interaction-type">
        <app-interaction-list :list="arrNodes">
            <template slot-scope="{data,index}">
                <view v-if="['LotteryTicket', 'WheelCamp', 'RoundWheel', 'SmashGoldenEgg', 'SlotMachine', 'BlindBox', 'IceTrue', 'TreasureChest'].includes(data.evetype)"
                      :key="index"
                      :data="data"
                      :arrow="false">
                    <app-wheel-camp-card :item.sync="arrNodes[index]"
                                         type="activity"
                                         :arrNodes="arrNodes"
                                         :evetype="data.evetype"
                                         :activityItem="activityItem"></app-wheel-camp-card>
                </view>
                <view :key="index" :data="data" :arrow="false" v-else-if="data.evetype === 'Sales'">
                    <app-sales :item.sync="arrNodes[index]"
                               :arrNodes="arrNodes"
                               :editFlag="false"
                               :activityItem="activityItem"></app-sales>
                </view>
                <view :key="index" :data="data" :arrow="false" v-else-if="data.evetype === 'SignIn'">
                    <app-sign-in :item.sync="arrNodes[index]"
                                 :arrNodes="arrNodes"
                                 :editFlag="false"
                                 :evetype="data.evetype"
                                 :key="index"
                                 :activityItem="activityItem"/>
                </view>
                <view :key="index" :data="data" :arrow="false" v-else-if="data.evetype === 'Invitation'">
                    <app-sign-in :item.sync="arrNodes[index]"
                                 :arrNodes="arrNodes"
                                 :editFlag="false"
                                 :evetype="data.evetype"
                                 :key="index"
                                 :activityItem="activityItem"/>
                </view>
                <view :key="index" :data="data" :arrow="false" v-else-if="data.evetype === 'ExpansionRule'">
                    <app-expansion-rule :item.sync="arrNodes[index]"
                                        :arrNodes="arrNodes"
                                        :editFlag="false"
                                        :evetype="data.evetype"
                                        :key="index"
                                        :activityItem="activityItem"/>
                </view>
                <view :key="index" :data="data" :arrow="false"
                      v-else-if="data.evetype === 'Message' || data.evetype === 'WxMessage'">
                    <app-message :item.sync="arrNodes[index]"
                                 :arrNodes="arrNodes"
                                 :editFlag="false"
                                 :evetype="data.evetype"
                                 :key="index"
                                 :activityItem="activityItem"/>
                </view>
                <view :key="index" :data="data" :arrow="false" v-else-if="data.evetype === 'Questionnaire'">
                    <app-questionnaire :item.sync="arrNodes[index]"
                                       :arrNodes="arrNodes"
                                       :editFlag="false"
                                       :evetype="data.evetype"
                                       :key="index"
                                       :activityItem="activityItem"/>
                </view>
            </template>
        </app-interaction-list>
        <view v-if="!flag" class="none-box">
            当前无互动活动,请继续下一步
        </view>
    </view>
</template>

<script>
    import AppInteractionList from "../../../lzlj/interaction-component/app-interaction-list";
    import AppWheelCampCard from "../../../lzlj/interaction-component/app-wheel-camp-card";
    import AppSales from "../../../lzlj/interaction-component/app-sales";
    import AppSignIn from "../../../lzlj/interaction-component/app-sign-in";
    import AppExpansionRule from "../../../lzlj/interaction-component/app-expansion-rule";
    import AppMessage from "../../../lzlj/interaction-component/app-message";
    import AppQuestionnaire from "../../../lzlj/interaction-component/app-questionnaire";

    export default {
        name: "design-interaction-type",
        components: {
            AppQuestionnaire,
            AppMessage,
            AppExpansionRule,
            AppSignIn,
            AppSales,
            AppWheelCampCard,
            AppInteractionList
        },
        props: {
            arrNodes: {type: Array},
            flag: {type: Boolean},
            activityItem: {type: Object}
        },
        data() {
            return {}
        },
    }
</script>

<style lang="scss">
    .design-interaction-type {
        .none-box {
            width: 100%;
            text-align: center;
            margin-top: 80px;
            transform: translateY(-50%);
            color: gray;
        }
    }
</style>
