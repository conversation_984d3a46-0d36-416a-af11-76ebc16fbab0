<template>
    <view class="tasting-wine-scan-code-statistic">
        <view class="statistic-item1">{{'本次扫码可赠送数' + nowScanGiftNum + '瓶'}}</view>
        <view class="statistic-item2">{{'本次赠送数量' + count + '瓶'}}</view>
    </view>
</template>

<script>
export default {
    props: {
        list: {},
        nowScanGiftNum: {
            type: Number
        }
    },
    inject: {
        ['@@LINK_CHECKBOX_PROVIDE']: {}
    },
    data() {
        return {}
    },
    computed: {
        count() {
            // const group = this['@@LINK_CHECKBOX_PROVIDE']
            return this.list.reduce((prev, item) => {
                const checkFlag = item.giftedSum > 0
                prev += !checkFlag ? 0 : (item.giftedSum == undefined ? 0 : Number(item.giftedSum))
                return prev
            }, 0)
        },
    }
}
</script>
<style lang="scss">
.tasting-wine-scan-code-statistic {
    display: flex;
    width: 80%;
    @include space-between;
    .statistic-item1 {
        color: #3e68ef;
        font-size: 28px;
    }
    .statistic-item2 {
        color: red;
        font-size: 28px;
    }
}
</style>
