<template>
    <link-page class="market-prod-new">
        <view class="prod-view" :style="radiusTopFlag?'border-radius:8px':'none'" v-if="prodShow">
            <view class="item-header" v-if="!$utils.isEmpty(feePayType)">
                <view v-if="operateFlag">
                    <!--                        控制是否可以操作 cash-new 和 prod-new组件的删除、新建、以及删除兑付方式的操作.可以操作时2行展示，兑付方式编码在第二行，新建和删除的按钮距离放大-->
                    <view style="width: 100%;height: 40px">
                        <view style="height: 44px;font-family: PingFangSC-Semibold;float: left;width: 40%;display: flex;align-items: center;">
                            <view style="white-space: pre-wrap;">{{feePayType}}</view>
                        </view>
                        <view style="float: right;text-align: center;padding-right: 24px;color: #2F69F8;font-size: 14px;line-height: 44px;width: 40px"
                              class="iconfont icon-plus"
                              @tap="addProd"
                              v-if="(otherLinkBtnCtr || executiveFeedbackLinkBtnCtrl) && operateFlag">
                        </view>
                        <view style="float: right;text-align: center;padding-right: 24px;color: #2F69F8;font-size: 14px;line-height: 44px;width: 40px"
                              class="iconfont icon-shanchu" v-if="deleteBtn && operateFlag" @tap="deleteByFeePayCode"></view>
                        <view style="float: right;text-align: center;padding-right: 24px;color: #2F69F8;font-size: 14px;line-height: 44px;width: 40px"
                              class="iconfont icon-saoma" v-if="false" @tap="scanCode"></view>
                    </view>
                    <view style="width: 100%;height: 40px;float: left">
                        <view class="iconfont icon-style icon-guanlian" style="line-height: 44px;"
                              v-show="executiveFeedbackLinkBtnCtrl && pageSource !== 'activityAudit' && !$utils.isEmpty(pageSource)
                      && messageScene !=='costApprovalMsg' && associatedExecutionCostsFlag"
                              @tap="gotoAssociatedExecutionCosts">
                            关联执行案明细
                        </view>
                        <view v-if="!associatedExecutionCostsFlag"
                              style="float: left;color: #2F69F8;font-size: 12px;line-height: 44px;">
                            {{costCode}}
                        </view>
                    </view>
                </view>
                <view v-else>
                    <view style="height: 44px;font-family: PingFangSC-Semibold;float: left;width: 40%;display: flex;align-items: center;">
                        <view style="white-space: pre-wrap;">{{feePayType}}</view>
                    </view>
                    <view class="iconfont icon-style icon-guanlian" style="line-height: 44px;"
                          v-show="executiveFeedbackLinkBtnCtrl && pageSource !== 'activityAudit' && !$utils.isEmpty(pageSource)
                      && messageScene !=='costApprovalMsg' && associatedExecutionCostsFlag"
                          @tap="gotoAssociatedExecutionCosts">
                        关联执行案明细
                    </view>
                    <view v-if="!associatedExecutionCostsFlag"
                          style="padding-left: 10px;float: left;color: #2F69F8;font-size: 12px;line-height: 44px;">
                        {{costCode}}
                    </view>
                    <view style="float: right;text-align: right;padding-right: 24px;color: #2F69F8;font-size: 14px;line-height: 44px;"
                          class="iconfont icon-plus"
                          @tap="addProd"
                          v-if="(otherLinkBtnCtr || executiveFeedbackLinkBtnCtrl) && operateFlag">
                    </view>
                    <view style="float: right;text-align: right;padding-right: 24px;color: #2F69F8;font-size: 14px;line-height: 44px;"
                          class="iconfont icon-shanchu" v-if="deleteBtn && operateFlag" @tap="deleteByFeePayCode"></view>
                    <view style="float: right;text-align: right;padding-right: 24px;color: #2F69F8;font-size: 14px;line-height: 44px;"
                          class="iconfont icon-saoma" v-if="false" @tap="scanCode"></view>
                </view>
            </view>
            <view class="product-list">
                <link-swipe-action v-for="(item,index) in dataList" :key="item.id">
                    <link-swipe-option slot="option" v-if="!$utils.isEmpty(pageSource)  && scene !=='actual' && pageSource !== 'activityAudit' && deleteBtn && operateFlag"
                                       @tap="handleProdDelete(item,index)">删除
                    </link-swipe-option>
                    <item :arrow="false">
                        <view class="product-list-item">
                            <view class="product-list-item__left">
                                {{item.prodCode}}-{{item.prodName}}
                            </view>
                            <view :class="(executiveFeedbackLinkBtnCtrl || otherLinkBtnCtr) === true ? 'product-list-item__center' : 'product-list-item__center_2'">
                                <view class="row row-1">
                                    {{priceShowFlag?'标价':''}}
                                    <view class="num" v-if="priceShowFlag">{{item.price|numCny}}，</view>
                                    申请
                                    <view class="num">{{item.qty}}</view>
                                    <!--费用申请时的产品单位-->
                                    <view v-if="!(actualAmountShowFlag || pageSource === 'executiveFeedback' || activityItemActualFlag)">
                                        {{item.unit|lov('FEE_PROD_UNIT')}}
                                    </view>
                                    <!--费用实际时展示的申请单位-->
                                    <view v-if="actualAmountShowFlag || pageSource === 'executiveFeedback' || activityItemActualFlag">
                                        {{item.applyUnit|lov('FEE_PROD_UNIT')}}
                                    </view>
                                </view>
                                <view class="row row-2" style="flex-direction: column;text-align: right">
                                    <view style="margin-right: 28rpx" v-if="priceShowFlag">
                                    费用核销价格
                                    <link-icon class="actual" @tap="actualTip" icon="mp-info-lite" status="info"/>
                                    </view>
                                    <view style="display: flex;margin-top: 12rpx;">
                                    <view class="num" style="flex: 1" v-if="priceShowFlag">{{item.actualTranPrice|numCny}}，</view>
                                    <!--活动状态[已实发、已结束、材料补充]或者执行反馈时展示实际数量 ,费用实际时展示 其他不展示-->
                                    <view v-if="actualAmountShowFlag || pageSource === 'executiveFeedback' || activityItemActualFlag">
                                        实际
                                        <view class="num" style="display: inline;">{{item.actualQty}}</view>
                                        {{item.unit|lov('FEE_PROD_UNIT')}}
                                    </view>
                                    </view>
                                </view>
                                <view class="row row-3">
                                    <!--只有执行反馈环节展示匹配状态-->
                                    <view class="materialSource">{{item.materialSource | lov('MATERIAL_SOURCE') }}
                                    </view>
                                    <view class="status" :class="status" v-if="pageSource === 'executiveFeedback'">
                                        {{item.matchStatus |
                                        lov('MATCH_STATUS')}}
                                    </view>
                                    <view class="count-price" v-if="priceShowFlag">小计
                                        <view class="num" v-if="scene === 'actual'">
                                            {{$utils.numberMul(item.actualTranPrice,item.actualQty) |cny}}
                                        </view>
                                        <view class="num" v-if="scene === 'apply'">
                                            {{$utils.numberMul(item.actualTranPrice,item.qty)|cny}}
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <!--活动状态是新建或者执行反馈环节可以编辑产品信息-->
                            <view class="product-list-item__right" style="line-height: 168rpx;"
                                  v-if="(executiveFeedbackLinkBtnCtrl || otherLinkBtnCtr) && provinceOperateFlag && !isSalesAreaManager">
                                <link-icon icon="icon-edit" @tap="editProdItem(item)"/>
                            </view>
                        </view>
                    </item>
                </link-swipe-action>
            </view>
            <view class="webstat" v-if="dataList.length>0">
                <view class="webstat-v" v-if="priceShowFlag">
                    <view class="webstat-title" :class="{ red: confirm && actualTotal == 0}" v-if="actualAmountShowFlag">实际总额&nbsp;</view>
                    <view class="webstat-val" v-if="actualAmountShowFlag">
                        <text :class="{ red: confirm && actualTotal == 0 }">{{actualTotal | cny}}</text>，
                    </view>
                    <view class="webstat-title" v-if="!actualAmountShowFlag">可用余额&nbsp;</view>
                    <view class="webstat-val" v-if="!actualAmountShowFlag">{{availableBalance|cny}}，</view>
                    <view class="webstat-title">申请总额&nbsp;</view>
                    <view class="webstat-val" style="padding-right: 12px">{{applyAmount|cny}}</view>
                </view>
                <view class="feeReim-v">
                    {{feeReim}}
                </view>
                <view class="feeReim-v" v-if="this.prodItem.costTypeName">
                    <view>
                        费用小类:  {{Boolean(this.prodItem.costTypeName)? this.prodItem.costTypeName : '' }}
                    </view>
                </view>
                <view class="feeReim-v" v-if="this.prodItem.dataList[0].comment">
                    <view>
                        备注:  {{Boolean(this.prodItem.dataList[0].comment)? this.prodItem.dataList[0].comment : '' }}
                    </view>
                </view>
            </view>
        </view>
        <!--调整产品弹框-->
        <link-dialog ref="prodBottom"
                     :noPadding="true"
                     v-model="productDialogFlag"
                     position="bottom"
                     borderRadius="32rpx 32rpx 0 0">
            <view class="model-title">
                <view class="title">编辑</view>
                <view class="iconfont icon-close" @tap="closeDialog"></view>
            </view>
            <link-form>
                <view class="product-name">
                    <view class="name">{{adjustedProductItem.prodName}}</view>
                </view>
                <link-form-item label="物资来源">
                    <link-lov type="MATERIAL_SOURCE" v-model="adjustedProductItem.materialSource"></link-lov>
                </link-form-item>
                <link-form-item label="单位">
                    <link-lov type="FEE_PROD_UNIT" v-model="adjustedProductItem.unit"></link-lov>
                </link-form-item>
                <link-form-item label="标价" v-if="adjustedProductItem.unit === 'Ping' && priceShowFlag">
                    <link-input v-model="adjustedProductItem.materialPrice" readonly></link-input>
                </link-form-item>
                <link-form-item label="标价" v-if="adjustedProductItem.unit === 'Xiang' && priceShowFlag">
                    <link-input v-model="adjustedProductItem.sellingPrice" readonly></link-input>
                </link-form-item>
                <link-form-item label="费用核销价格" arrow v-if="priceShowFlag">
                    <link-number v-model=adjustedProductItem.actualTranPrice hideButton :precision="2"/>
                </link-form-item>
                <!--可编辑时：执行反馈环节编辑实际数量，其他场景编辑申请数量-->
                <link-form-item label="申请数量" v-if="pageSource !== 'executiveFeedback'">
                    <link-number v-model="adjustedProductItem.qty" :min="1"/>
                </link-form-item>
                <link-form-item label="实际数量" v-if="pageSource === 'executiveFeedback'">
                    <link-number v-model="adjustedProductItem.actualQty" :min="0" v-if="actualEditProdActualQtyFlag"/>
                    <link-input v-model="adjustedProductItem.actualQty" readonly v-else @tap="promptInfo"></link-input>
                 <!--待处理特定情况下品鉴酒的实际瓶不可编辑    -->
                </link-form-item>
            </link-form>
            <view class="blank"></view>
            <link-sticky class="bottom-btn">
                <link-button class="sure-btn" size="normal" @tap="saveAdjustedProduct">确定</link-button>
            </link-sticky>
        </link-dialog>
    </link-page>
</template>

<script>
    import {FilterService} from "link-taro-component";
    import {ROW_STATUS} from "../../../../utils/constant";
    import Taro from "@tarojs/taro";
    export default {
        name: "prod-new",
        props: {
            prodShow: {
                type: Boolean,
                default: false
            },
            //上半部分
            radiusTopFlag: {
                type: Boolean,
                default: false
            },
            //四边-弃用
            radiusAllFlag: {
                type: Boolean,
                default: false
            },
            //下半部分-弃用
            radiusBottomFlag: {
                type: Boolean,
                default: false
            },
            //按钮是否显示-弃用
            bthShow: {
                type: Boolean,
                default: false
            },
            //场景-实际费用 actual 、申请费用 apply 控制保存编辑时调用的接口
            scene: {
                type: String
            },
            //页面来源 -
            // 1、执行反馈环节 executiveFeedback
            // 2、other 活动的其他信息(ps:这是一个页面)
            // 3、preview 活动预览界面而来
            // 4、view 活动查看界面查看而来
            // 5、审批或小程序消息进去 为空
            // 6、activityAudit 活动稽核
            pageSource: {
                type: String,
                default: 'other'
            },
            //父对象-为活动对象
            parentData: {
                type: Object,
                default: function () {
                    return {};
                }
            },
            //产品对象
            prodItem: {
                type: Object,
                default: function () {
                    return {
                        dataList: [],
                        feePayType: '',
                        feePayCode: '',
                        costId: '',
                        applyAmount: 0,
                        actualTotal: 0,
                        availableBalance: 0,
                    };
                }
            },
            //场景 只有执行反馈环节传 'costApprovalMsg' 区分费用审批场景
            messageScene: {
                type: String,
            },
            /**
             * 控制是否可以操作 cash-new 和 prod-new组件的删除、新建、以及删除兑付方式的操作
             * 只允许在编辑界面删除兑付方式、删除明细 、新增明细。其他界面可以编辑小计 和 编辑某个产品信息
             * */
            operateFlag: {
                type: Boolean,
                default: false
            },
            /**
             * 控制是否可以点击现金类小计 和 产品类的编辑笔
             * 默认可以编辑，仅费用提交按钮的弹出的费用实际信息内容块不能编辑
             * */
            provinceOperateFlag: {
                type: Boolean,
                default: true
            },
            //费用信息 - 主要用于费用实际关联执行案明细判断时
            prodAndCostList: {
                type: Object,
                defalt: [],
            },
            // 是否是提交时确认弹框使用
            confirm: {
                type: Boolean,
                default: false
            }
        },
        data() {
            let activityItemActualFlag = false;//是否为活动详情而来 且是实际费用类型
            if (this.scene === 'actual' && this.pageSource === 'view') {
                activityItemActualFlag = true;
            }
            let actualAmountShowFlag = false;//默认实际总额不显示
            let executiveFeedbackLinkBtnCtrl = false;//执行反馈环节 所有按钮的控制（ps:不控制字段的显影）
            let otherLinkBtnCtr = false;//非执行反馈、活动稽核环节 所有按钮的控制
            /**
             * 1、活动状态 [已实发\已结束\材料补充]时 展示实际总额不展示可用余额。
             * 2、执行反馈环节[pageSource === 'executiveFeedback'，'view']展示实际总额,不展示可用余额。
             * 3、scene 实际费用 actual 展示实际总额,不展示可用余额。
             * */
            if ((this.pageSource === 'executiveFeedback' || activityItemActualFlag) ||
                (this.parentData.status === 'ActualAmount'
                    || this.parentData.status === 'MaterialSup') && this.scene === 'actual') {
                actualAmountShowFlag = true;
            }
            /*
            * 执行反馈环节 活动状态 MC_STATUS 为进行中\已结束 且 审批状态为 申请审批通过、反馈驳回
            * 2021-04-19 更新 活动状态=已发布的编辑权限
            * */
            if (this.pageSource === 'executiveFeedback' && (this.parentData.status === 'Processing' || this.parentData.status === 'Closed' || this.parentData.status === 'Published')
                && (this.parentData.aproStatus === 'Approve' || this.parentData.aproStatus === 'Refeedback')) {
                executiveFeedbackLinkBtnCtrl = true;
            }
            /*
            * 非执行反馈、活动稽核环节、预览 活动状态 MC_STATUS 新建 且 审批状态为 未提交、已拒绝 this.pageSource !== 'preview' &&
            * */
            if (this.pageSource !== 'executiveFeedback' && this.pageSource !== 'activityAudit'
                && this.messageScene !== 'costApprovalMsg' && this.parentData.status === 'New'
                && (this.parentData.aproStatus === 'New' || this.parentData.aproStatus === 'Refused')) {
                otherLinkBtnCtr = true;
            }
            const parentId = this.parentData.id;
            let source = "";
            if (this.scene === 'actual') {
                source = 'actFee'
            } else if (this.scene === 'apply') {
                source = 'applyFee'
            }
            let deleteBtn = false;//删除按钮控制逻辑
            //活动状态 MC_STATUS ：新建
            //审批状态 APRO_STATUS ：未提交、已拒绝
            if (this.parentData.status === 'New' && (this.parentData.aproStatus === 'New' || this.parentData.aproStatus === 'Refused')) {
                deleteBtn = true;
            }
            // 活动状态 MC_STATUS “已发布”&“进行中”&"已结束"
            // 活动审批状态 APRO_STATUS 为“提报审批通过”&“反馈驳回””反馈撤回“时
            // 执行反馈时 费用实际时
            let scanCodeBtn = false;//扫码按钮显隐控制逻辑
            if ((this.parentData.status === 'Published' || this.parentData.status === 'Processing' || this.parentData.status === 'Closed') && this.pageSource === 'executiveFeedback' && this.scene === 'actual'
                && (this.parentData.aproStatus === 'Approve' || this.parentData.aproStatus === 'Refeedback' || this.parentData.aproStatus === 'RefeedWithdraw')) {
                scanCodeBtn = true;
            }
            const editActualTranPrice = 0;
            const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
            // 编辑产品信息的弹框中是否可以编辑实际数量。如果是费用实际，产品为品鉴酒，且不属于可编辑的场景
            // 1、"开瓶：unused & 转赠：required" 2、"开瓶：required & 转赠：unused"
            // 3、"开瓶：required & 转赠：required" 4、"开瓶：required & 转赠：NotRequired" 5、"开瓶：NotRequired & 转赠：required"
            const actualEditProdActualQtyFlag = true;
            const cfgPropertyOrg = {};//企业参数配置 品牌公司组织ID 相关信息
            const cfgPropertyActivityType = {};//企业参数配置 活动类型 相关信息
            const currentEditProdObj = {};//当前编辑的产品对象
            const editProdIsPinJianJiuFlag = false;
            return {
                // 费用价格是否展示标识
                priceShowFlag: false,
                cfgPropertyOrg,
                cfgPropertyActivityType,
                currentEditProdObj,
                editProdIsPinJianJiuFlag,
                userInfo,
                editActualTranPrice,
                activityItemActualFlag,
                scanCodeBtn,
                deleteBtn,
                executiveFeedbackLinkBtnCtrl,
                otherLinkBtnCtr,
                parentId,
                actualAmountShowFlag,
                adjustedProductItem: {},//调整的产品信息
                productDialogFlag: false,
                status: 'success',
                backupQty:'',
                actualEditProdActualQtyFlag,
                activityItemReply:{},//执行反馈选择完产品后，实际数量赋值逻辑判断需要
                //产品选择
                productionPinOption: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: 'action/link/product/queryUnSelectByOrgIdPage'
                    },
                    sortOptions: null,
                    searchFields: ['prodName', 'prodCode'],
                    param: {
                        //source:实际费用添加产品查询（actFee）、申请费用添加查询（applyFee）、动销添加产品（actSale）
                        filtersRaw: [
                            {"id": "attr1", "property": "attr1", "value": parentId},
                            {"id": "attr2", "property": "attr2", "value": this.prodItem.feePayCode},
                            {"id": "source", "property": "source", "value": source}],
                        attr3: this.parentData.salesCityId,
                        attr5: this.prodItem.costCode,
                        accntId: this.parentData.actExecutivesId,
                    },
                    hooks: {
                        beforeLoad(option) {
                            delete option.param.order;
                            delete option.param.sort;
                        },
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} arrow="false">
                                <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                                <view
                                    style="display: flex;flex: 1;flex-direction: column;justify-content: space-between;align-items: flex-start;overflow: hidden;">
                                    <view style="margin:12px;">
                                        <view style="background: #A6B4C7;border-radius: 4px;line-height: 20px;">
                                            <view
                                                style="font-size: 14px;color: #FFFFFF;letter-spacing: 0;line-height: 20px;padding: 1px 4px;">{data.prodCode}
                                            </view>
                                        </view>
                                    </view>
                                    <view style="margin-left:12px;">
                                        <view
                                            style="font-family: PingFangSC-Semibold;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 16px;">
                                            {data.prodName}
                                        </view>
                                    </view>
                                    <view style="margin:12px 0 0 12px;width:100%">
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;width: 50%;float: left;">{this.priceShowFlag?`箱价:`+ FilterService.numCny(data.sellingPrice):'' }
                                        </view>
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 14px;color: #000000;letter-spacing: 0;text-align: left;line-height: 14px;">{this.priceShowFlag?`瓶价:`+FilterService.numCny(data.materialPrice): ''}
                                        </view>
                                    </view>
                                </view>
                            </item>
                        )
                    },
                }),
            }
        },
        computed: {
            dataList() {
                return this.prodItem.dataList;//产品类子对象
            },
            feePayType() {
                return this.prodItem.feePayType;//费用兑付方式名称
            },
            feePayCode() {
                return this.prodItem.feePayCode;//费用兑付方式编码
            },
            costCode() {
                return this.prodItem.costCode;//执行案明细编码
            },
            applyAmount() {
                let countApplyAmount = 0;
                if ((this.pageSource === 'executiveFeedback' || this.parentData.status === 'ActualAmount' || this.parentData.status === 'MaterialSup')) {
                    countApplyAmount = this.prodItem.applyAmount;//申请总额
                } else {
                    this.prodItem.dataList.forEach(item => {
                        const actualTranPrice = parseFloat(item.actualTranPrice);
                        const qty = parseFloat(item.qty);
                        const t = this.$utils.numberMul(actualTranPrice, qty);
                        countApplyAmount = this.$utils.numberAdd(countApplyAmount, t);
                    });
                }
                return countApplyAmount;
            },
            actualTotal() {
                let countActualAmount = 0;
                if ((this.pageSource === 'executiveFeedback' || this.parentData.status === 'ActualAmount' || this.parentData.status === 'MaterialSup')) {
                    this.prodItem.dataList.forEach(item => {
                        const actualTranPrice = parseFloat(item.actualTranPrice);
                        const actualQty = parseFloat(item.actualQty);
                        const t = this.$utils.numberMul(actualTranPrice, actualQty);
                        countActualAmount = this.$utils.numberAdd(countActualAmount, t);
                    });
                } else {
                    countActualAmount = this.prodItem.actualTotal;//实际总额
                }
                return countActualAmount;
            },
            availableBalance() {
                return this.prodItem.availableBalance;//可用余额
            },
            costId() {
                return this.prodItem.costId;//费用明细ID
            },
            associatedExecutionCostsFlag() {//是否可以操作关联执行案明细取决于：dataList里的数据对象 costId 字段是否有值
                if (this.$utils.isEmpty(this.prodItem.costId)) {
                    if (this.$utils.isEmpty(this.prodItem.dataList)) {
                        return true;
                    } else if (!this.$utils.isEmpty(this.prodItem.dataList) && this.$utils.isEmpty(this.prodItem.dataList[0].costId)) {
                        return true;
                    }
                } else {
                    return false;
                }
            },
            feeReim() {
                return this.prodItem.feeReim;//费用垫付对象名称
            },
            //片区经理关闭市场活动提交权限
            isSalesAreaManager: function () {
                return ['CityManager', 'SalesRegionManager', 'SalesAreaManager',].includes(Taro.getStorageSync('token').result.positionType)
            }
        },
        async created() {
            if(!this.$utils.isEmpty(this.$dataService.getMarketActivityItem())){
                this.activityItemReply = this.$dataService.getMarketActivityItem()
            } else {
                this.activityItemReply = this.parentData;
            }
            this.priceShowFlag = await this.$utils.getPriceDesensitize(this.userInfo);
        },
        watch:{
            'adjustedProductItem.unit'(newVal){
                this.adjustedProductItem.actualTranPrice = newVal==='Xiang'? FilterService.num(this.adjustedProductItem.sellingPrice):FilterService.num(this.adjustedProductItem.materialPrice);
            }
        },
        methods: {
            actualTip(){
                this.$message.primary('费用核销价格由业务代表填写，为受益客户需占用的产品单价')
            },
             promptInfo(){
                this.$dialog({
                    title: '提示',
                    content: '实际数量不可编辑，请进行开瓶或赠送扫码。',
                    cancelButton: false,
                    onConfirm: () => {}
                })
            },
            async openProdNumberKeyboard() {
                this.editActualTranPrice = await this.$numberKeyboard({initValue: this.editActualTranPrice,precision: 2});
                this.adjustedProductItem.actualTranPrice = this.editActualTranPrice;
                this.editActualTranPrice = 0;
            },
            /**
             * 添加产品
             * <AUTHOR>
             * @date 2020-08-20
             * */
            async addProd() {
                const list = await this.$object(this.productionPinOption, {
                    pageTitle: "产品",
                    multiple: true,
                });
                let dataList = [];
                //执行反馈时实际
                if (this.pageSource === 'executiveFeedback') {
                    for (let i = 0; i < list.length; i++) {
                        let item = list[i];
                        const data = await this.$http.post('action/link/actualFee/checkIsWineTastingByProd',
                            {prodId: item.id});
                        let reply = data['reply'];//reply:true = 是品鉴酒 false = 不是品鉴酒
                        let actualQty = 1;//默认为1。当产品属于品鉴酒产品组，并且不属于 giftScanConfig 和 openScanConfig 都为 Unused 的情况时默认为0；
                        if(reply && !(this.activityItemReply['giftScanConfig'] === 'Unused' && this.activityItemReply['openScanConfig'] === 'Unused')){
                            actualQty = 0;
                        }
                        const prodItem = {
                            prodCode: item.prodCode,
                            prodName: item.prodName,
                            qty: 0,
                            actualQty: actualQty,
                            price: FilterService.num(item.materialPrice),
                            actualTranPrice: item.prodUnit === 'Xiang'? FilterService.num(item.sellingPrice):FilterService.num(item.materialPrice), //单位为箱 展示箱价
                            prodId: item.id,
                            actId: this.parentId,
                            feePayCode: this.feePayCode,
                            feePayType: this.feePayType,
                            costId: this.costId,
                            materialSource: 'SpecialDealer',//默认为特约经销商
                            unit: item.prodUnit==='yuan' ? item.prodUnit : 'Ping',
                            row_status: ROW_STATUS.NEW,
                            materialPrice: FilterService.num(item.materialPrice),//活动瓶价
                            sellingPrice: FilterService.num(item.sellingPrice),//活动箱价
                        }
                        dataList.push(prodItem);
                    }
                } else {
                    dataList = list.map(
                        item => ({
                            prodCode: item.prodCode,
                            prodName: item.prodName,
                            qty: 1,
                            price: FilterService.num(item.materialPrice),
                            actualTranPrice: item.prodUnit === 'Xiang'? FilterService.num(item.sellingPrice):FilterService.num(item.materialPrice), //单位为箱 展示箱价
                            prodId: item.id,
                            actId: this.parentId,
                            feePayCode: this.feePayCode,
                            feePayType: this.feePayType,
                            costId: this.costId,
                            materialSource: 'SpecialDealer',//默认为特约经销商
                            unit: item.prodUnit==='yuan' ? item.prodUnit : 'Ping',
                            row_status: ROW_STATUS.NEW,
                            materialPrice: FilterService.num(item.materialPrice),//活动瓶价
                            sellingPrice: FilterService.num(item.sellingPrice),//活动箱价
                        })
                    );
                }
                this.$emit('updateProd', this.feePayType, this.costId, dataList); //通知父组件改变。
                this.$bus.$emit("costUpdateRefreshMarketActivityItem");
            },
            /**
             * 删除产品
             * <AUTHOR>
             * @date 2020-11-06
             * */
            async handleProdDelete(item, index) {
                //2021-11-09考虑多人操作的场景
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.parentData.id
                });
                //为空代表是新建市场活动场景
                if(!this.$utils.isEmpty(data.result)){
                    if(!(data.result.status === 'New' && (data.result.aproStatus === 'New' || data.result.aproStatus === 'Refused'))){
                        this.$message.warn('活动已被更新，当前活动状态和审批状态不允许删除产品，请返回列表重新查询活动数据。');
                        return ;
                    }
                }
                const that = this;
                if (that.dataList.length === 1) {
                    this.$taro.showModal({
                        title: '提示',
                        content: '当前兑付方式只有一条费用明细，删除费用明细将删除整个兑付方式，是否确认删除？',
                        success: async (res) => {
                            if (res.confirm) {
                                this.$emit('deleteProdFeePay', this.feePayCode, this.costId); //通知父组件改变。
                            } else if (res.cancel) {
                            }
                        }
                    });
                } else {
                    this.$taro.showModal({
                        title: '提示',
                        content: '是否要删除当前行信息',
                        success: async (res) => {
                            if (res.confirm) {
                                if (!that.$utils.isEmpty(item.id) && item.row_status !== ROW_STATUS.NEW) {
                                    let url = "";
                                    if (that.scene === 'actual') {
                                        url = 'action/link/actualFee/deleteById'
                                    } else if (that.scene === 'apply') {
                                        url = 'action/link/actMaterial/deleteById'
                                    }
                                    await this.$http.post(url, {id: item.id});
                                    this.$emit('deleteProd', this.feePayType, that.dataList); //通知父组件改变。
                                    this.$bus.$emit("costUpdateRefreshMarketActivityItem");
                                    this.$bus.$emit("initCodeScanRecordList");//重新查询扫码记录
                                }
                                that.dataList.splice(index, 1);
                            } else if (res.cancel) {
                            }
                        }
                    });
                }
            },
            /**
             * 关闭底部弹窗
             * <AUTHOR>
             * @date 2020-09-02
             * @param param
             */
            closeDialog() {
                this.productDialogFlag = !this.productDialogFlag
            },
            /**
             * 编辑产品信息调整实际价格和数量
             * <AUTHOR>
             * @date 2020-09-02
             * */
            async editProdItem(item) {
                //2021-08-03考虑多人操作的场景
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.parentData.id
                });
                //为空代表是执行案模块新建市场活动场景
                if(!this.$utils.isEmpty(data.result)){
                    // 获取缓存中的活动版本号
                    if(!this.$utils.isEmpty(this.$dataService.getMarketActivityItem())){
                        this.parentData['rowVersion'] = this.$dataService.getMarketActivityItem()['rowVersion']
                    } else {
                        this.parentData['rowVersion'] = data.result['rowVersion'];
                    }
                    /* 执行反馈环节 活动状态 MC_STATUS 为进行中、已结束 且 审批状态为 申请审批通过、反馈驳回
                * 2021-04-19 更新 活动状态=已发布的编辑权限
                    * */
                    if (this.pageSource === 'executiveFeedback' && (data.result.status === 'Processing' || data.result.status === 'Closed' || data.result.status === 'Published')
                        && (data.result.aproStatus === 'Approve' || data.result.aproStatus === 'Refeedback')) {
                        this.executiveFeedbackLinkBtnCtrl = true;
                    } else {
                        this.executiveFeedbackLinkBtnCtrl = false;
                    }
                    /*
                    * 非执行反馈、活动稽核环节、预览 活动状态 MC_STATUS 新建 且 审批状态为 未提交、已拒绝
                    * */
                    if (this.pageSource !== 'executiveFeedback' && this.pageSource !== 'activityAudit'
                        && this.messageScene !== 'costApprovalMsg' && data.result.status === 'New'
                        && (data.result.aproStatus === 'New' || data.result.aproStatus === 'Refused')) {
                        this.otherLinkBtnCtr = true;
                    } else {
                        this.otherLinkBtnCtr = false;
                    }
                    if (!(this.otherLinkBtnCtr || this.executiveFeedbackLinkBtnCtrl)) {
                        this.$message.warn('活动已被更新，当前活动状态和审批状态不允许编辑产品信息，请返回列表重新查询活动数据。');
                        return ;
                    }
                }
                //费用实际编辑产品信息时，需要查询当前产品是否数据品鉴酒产品，若属于品鉴酒再判别是否属于特例开放编辑权限的。若不属于品鉴酒则走原本的逻辑
                if(this.scene === 'actual'){
                    this.currentEditProdObj = item;
                    if(this.$utils.isNotEmpty(item.id)){
                    item = await this.queryActualDataById(item);
                    }
                    await this.checkActualEditProdActualQtyFlag();
                }
                if (!this.$utils.isEmpty(item.materialPrice)) {
                    item.materialPrice = FilterService.num(item.materialPrice)
                }
                if (!this.$utils.isEmpty(item.sellingPrice)) {
                    item.sellingPrice = FilterService.num(item.sellingPrice)
                }
                this.adjustedProductItem = item;
                if (this.scene === 'apply') {
                   this.backupQty =  item.qty;
                }
                this.$refs.prodBottom.show()
            },
            /**
             * 查询费用实际产品的信息
             * 根据实际物资的ID查询数据，避免扫码开瓶后编辑产品信息，产品的实际数量被置空
             * <AUTHOR>
             * @date 2022-01-24
             * @param item 费用实际当前编辑的产品信息对象
             * */
            async queryActualDataById(item){
                const data = await this.$http.post('action/link/actualFee/queryById', {
                    id: item.id,
                });
                return data.result;
            },
            /**
             * 保存编辑的产品信息
             * <AUTHOR>
             * @date 2020-09-02
             * */
            async saveAdjustedProduct() {
                if(this.$utils.isEmpty(this.adjustedProductItem.actualTranPrice)){
                    this.$message.warn('实际价格不能为空，请重新输入');
                    return false;
                }
                const regexp = /^(([1-9][0-9]*)|(([0]\.\d{0,2}|[1-9][0-9]*\.\d{0,2})))$/;
                const regexp2 = /^[0-9]*$/
                const flag = regexp.test(this.adjustedProductItem.actualTranPrice);
                const flag2 = regexp2.test(this.adjustedProductItem.actualTranPrice);
                if(!(flag || flag2)) {
                    this.$message.warn(`费用核销价格为纯数字且不能超过2位小数，请检查。`);
                    return false;
                }
                if (this.adjustedProductItem.unit === 'Ping') {
                    this.adjustedProductItem.price = this.adjustedProductItem.materialPrice;
                } else if (this.adjustedProductItem.unit === 'Xiang') {
                    this.adjustedProductItem.price = this.adjustedProductItem.sellingPrice;
                }
                //如果id不为空时 为更新数据
                if (!this.$utils.isEmpty(this.adjustedProductItem.id)) {
                    /**
                     * 匹配品鉴酒
                     * 费用申请阶段，修改申请瓶数时的校验若当前物资行的产品属于该公司下品鉴酒产品组中的产品，
                     * 则在编辑修改“申请瓶数”时，需添加扫码记录数量的校验，
                     * 如果修改后的“申请瓶数”少于当前物资的“总出库数”，
                     * 则弹窗提示：“编辑后出库瓶数少于申请瓶数，如需减少申请瓶数，请先删除对应的出库扫码记录”，点击【确定】关闭弹窗，不做数量改变
                     * */
                    if (this.scene === 'apply') {
                        const checkReplyData = {
                            id: this.adjustedProductItem.id,
                            prodId: this.adjustedProductItem.prodId,
                            qty: this.adjustedProductItem.qty,
                        };
                        const dataReply = await this.$http.post('action/link/actMaterial/checkActMaterialRevise', checkReplyData);
                        if(!dataReply['reply']){
                            const that = this;
                            this.$dialog({
                                title: '提示',
                                content: '编辑后出库瓶数少于申请瓶数，如需减少申请瓶数，请先删除对应的出库扫码记录。',
                                cancelButton: false,
                                onConfirm: () => {
                                    that.adjustedProductItem.qty = that.backupQty;
                                    if(that.$refs.prodBottom){
                                        that.$refs.prodBottom.hide();
                                    }
                                },
                            })
                            return false;
                        }
                    }

                    let actualQty = 1;//执行反馈环节更新的是实际数量,本身有数量直接用没有的话 默认为1
                    /*this.$aegis.report({
                        msg: '记录编辑确认前实际瓶数类型', // 日志提示
                        ext1: typeof(this.adjustedProductItem.actualQty), // 日志对象
                        trace: 'log' // 日志类型
                    });
                    this.$aegis.report({
                        msg: '记录编辑确认前实际瓶数数据', // 日志提示
                        ext1: JSON.stringify(this.adjustedProductItem), // 日志对象
                        trace: 'log' // 日志类型
                    });*/
                    if (!this.$utils.isEmpty(this.adjustedProductItem.actualQty)) {
                        actualQty = this.adjustedProductItem.actualQty;
                    }
                    let updateDate= {};
                    if(this.actualEditProdActualQtyFlag) {
                         updateDate = {
                            id: this.adjustedProductItem.id,
                            unit: this.adjustedProductItem.unit,
                            actualTranPrice: this.adjustedProductItem.actualTranPrice,
                            materialSource: this.adjustedProductItem.materialSource,
                            qty: this.adjustedProductItem.qty,//除执行反馈环节外其他可以更新产品时更新的是申请数量
                            actualQty: actualQty,
                            row_status: ROW_STATUS.UPDATE,
                            updateFields: "id,unit,actualTranPrice,qty,actualQty,materialSource"
                        };
                    }else{
                         updateDate = {
                            id: this.adjustedProductItem.id,
                            unit: this.adjustedProductItem.unit,
                            actualTranPrice: this.adjustedProductItem.actualTranPrice,
                            materialSource: this.adjustedProductItem.materialSource,
                            qty: this.adjustedProductItem.qty,//除执行反馈环节外其他可以更新产品时更新的是申请数量
                            row_status: ROW_STATUS.UPDATE,
                            updateFields: "id,unit,actualTranPrice,qty,materialSource"
                        };
                    }
                    //考虑多人操作
                    let dealData = {
                        marketAct : {
                            id: this.parentData.id,
                            rowVersion: this.parentData.rowVersion,
                            remark: this.parentData.remark,
                            row_status: ROW_STATUS.UPDATE,
                            updateFields: 'id,remark,rowVersion'
                        }
                    };
                    const feeList = [];
                    feeList.push(updateDate);
                    if (this.scene === 'apply') {
                        dealData['actMaterialList'] = feeList;
                    } else if (this.scene === 'actual') {
                        dealData['actualFeeList'] = feeList;
                    }
                    const data = await this.$http.post('action/link/actIntegration/updateActIdAndCityAfterSelectedFee', dealData);
                    if(data.success){
                        if (this.scene === 'actual') {
                            //actualEditProdActualQtyFlag为 true则允许编辑actualQty  false则不允许编辑actualQty
                            if (this.actualEditProdActualQtyFlag) {
                                this.dataList.forEach((item, index) => {
                                    if (item.id === updateDate.id) {
                                        this.dataList[index].unit = updateDate.unit;
                                        this.dataList[index].actualTranPrice = updateDate.actualTranPrice;
                                        this.dataList[index].materialSource = updateDate.materialSource;
                                        this.dataList[index].qty = updateDate.qty;
                                        this.dataList[index].actualQty = updateDate.actualQty;
                                    }
                                })
                            } else {
                                this.dataList.forEach((item, index) => {
                                    if (item.id === updateDate.id) {
                                        this.dataList[index].unit = updateDate.unit;
                                        this.dataList[index].actualTranPrice = updateDate.actualTranPrice;
                                        this.dataList[index].materialSource = updateDate.materialSource;
                                        this.dataList[index].qty = updateDate.qty;
                                    }
                                })
                            }
                        }
                    }
                    this.$dataService.setMarketActivityItem(data.result);
                    this.$emit('updateProdPayList');
                    this.$bus.$emit("ProductDataListRrefreshs", this.feePayType, this.costId, this.dataList);
                    this.$bus.$emit("costUpdateRefreshMarketActivityItem");
                    this.$refs.prodBottom.hide();
                } else {
                    this.$refs.prodBottom.hide();
                    this.$bus.$emit("ProductDataListRrefreshs", this.feePayType, this.costId, this.dataList);
                    this.$bus.$emit("costUpdateRefreshMarketActivityItem");
                }
            },
            /**
             *  前往操作关联执行案明细
             *  <AUTHOR>
             *  @date 2020-09-14
             * */
            gotoAssociatedExecutionCosts() {
                this.$nav.push('/pages/lj-market-activity/market-activity/associated-execution-costs-page', {
                    feePayType: this.feePayType,
                    feePayCode: this.feePayCode,
                    costId: this.costId,
                    dataList: this.dataList,
                    feePayMethodType: 'Product', //对付方式类型 ： 产品 类
                    parentData: this.parentData,/*活动信息*/
                    prodAndCostList: this.prodAndCostList,//费用信息
                    callback: (item) => {
                        this.$emit('updateMarketAct', item); //通知父组件更新活动信息。
                    }
                })
            },
            /*
            * 根据兑付方式编码删除申请费用
            * <AUTHOR>
            * @date 2020-10-16
            * */
            async deleteByFeePayCode() {
                //2021-08-03考虑多人操作的场景
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.parentData.id
                });
                //为空代表是执行案模块新建市场活动场景
                if(!this.$utils.isEmpty(data.result)){
                    if(!(data.result.status === 'New' && (data.result.aproStatus === 'New' || data.result.aproStatus === 'Refused'))){
                        this.$message.warn('活动已被更新，当前活动状态和审批状态不允许删除兑付方式，请返回列表重新查询活动数据。');
                        return ;
                    }
                }
                this.$taro.showModal({
                    title: '提示',
                    content: '即将删除该支付方式下所有费用明细,请确认',
                    success: async (res) => {
                        if (res.confirm) {
                            this.$emit('deleteProdFeePay', this.feePayCode, this.costId); //通知父组件改变。
                            this.$bus.$emit("costUpdateRefreshMarketActivityItem");
                        } else if (res.cancel) {
                        }
                    }
                })
            },
            async scanCode() {
                const that = this;
                // 只允许从相机扫码
                await wx.scanCode({
                    onlyFromCamera: true,
                    success(res) {
                        that.relateBaseInfoFun(res.result);
                    }
                });
            },
            //扫码之后拿到码信息之后的操作
            async relateBaseInfoFun(mark) {
                const queryParam = {
                    mark: mark,
                    actId: this.parentData.id,
                    scanSource: "StaffSystem",
                    scanType: "ActProdScan",
                };
                await this.$httpForm.post('action/link/qianlima/relateBaseInfo', queryParam, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        if (response.result) {
                            this.$dialog({
                                title: '提示',
                                content: response.result,
                                cancelButton: false,
                                onConfirm: () => {
                                    this.$bus.$emit('queryCoverCodeData');
                                }
                            })
                        }
                    }
                });
                this.$bus.$emit('queryCoverCodeData');
                this.$emit('updateProdPayList'); //通知父组件更新费用实际信息。
            },
            /**
             * 查询当前产品是不是品鉴酒产品
             * <AUTHOR>
             * @date 2021-11-02
             * */
            async checkIsWineTastingByProd(){
                const data = await this.$http.post('action/link/actualFee/checkIsWineTastingByProd',
                    {prodId: this.currentEditProdObj.prodId});
                this.editProdIsPinJianJiuFlag = data['reply'];
            },
            /** 值列表：SCAN_USING_STATUS
             * 检测费用实际时是否可以编辑产品的实际数量字段
             * @auther songyanrong
             * @date 2021-11-02
             * 说明：如果当前是费用实际阶段，产品属于品鉴酒那么1、查找系统参数分辨当前活动是否在本组织属于可以编辑实际数量的情况 可以编辑或不允许编辑
             * 2、产品不属于品鉴酒 那么就走原本的逻辑 可以编辑实际数量
             * */
            async checkActualEditProdActualQtyFlag(){
                //先查询是否属于品鉴酒
                await this.checkIsWineTastingByProd();
                //是品鉴酒 查询是否特殊开放
                //属于可编辑的场景
                // 1、"开瓶：unused & 转赠：unused" 2、"开瓶：unused & 转赠：NotRequired"
                // 3、"开瓶：NotRequired & 转赠：unused" 4、"开瓶：NotRequired & 转赠：NotRequired"
                if(this.editProdIsPinJianJiuFlag){
                    const data = await this.$http.post('action/link/marketAct/queryById', {
                        id: this.parentData.id
                    });
                    let activityItemNew = data.result;
                    if((activityItemNew['openScanConfig'] === 'Unused' &&  activityItemNew['giftScanConfig'] === 'Unused')
                    || (activityItemNew['openScanConfig'] === 'Unused' &&  activityItemNew['giftScanConfig'] === 'NotRequired')
                    || (activityItemNew['openScanConfig'] === 'NotRequired' &&  activityItemNew['giftScanConfig'] === 'Unused')
                    || (activityItemNew['openScanConfig'] === 'NotRequired' &&  activityItemNew['giftScanConfig'] === 'NotRequired')){
                        this.actualEditProdActualQtyFlag = true;
                    } else {
                        this.actualEditProdActualQtyFlag = false;
                    }
                    console.log('this.actualEditProdActualQtyFlag',this.actualEditProdActualQtyFlag)
                } else {
                //不是品鉴酒
                    this.actualEditProdActualQtyFlag = true;
                }
            }
        }
    }
</script>

<style lang="scss">
    .market-prod-new{
        display: flex;

        .actual{
            position: relative;
            font-size: 28px;
        }

        .model-title {
            display: flex;
            margin-left: 24px;

            .title {
                font-family: PingFangSC-Regular, serif;
                font-size: 32px;
                color: #262626;
                letter-spacing: 0;
                text-align: center;
                line-height: 80px;
                height: 80px;
                width: 85%;
                padding-left: 40px;
            }

            .icon-close {
                color: #BFBFBF;
                font-size: 36px;
                line-height: 80px;
                height: 80px;
            }
        }

        .product-name {
            @include flex-start-center;
            @include space-between();
            margin-left: 24px;
            margin-right: 24px;
            margin-top: 24px;

            .name {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #262626;
                letter-spacing: 0;
                line-height: 28px;
            }

            .unit {
                font-size: 28px;
                color: #8C8C8C;
            }
        }

        .blank {
            height: 204px;
            width: 100%;
        }

        .bottom-btn {
            padding-top: 16px;
            padding-bottom: 34px;

            .all-select {
                height: 96px;
                padding-left: 24px;
                @include flex-start-center();
                width: 50%;
                font-size: 28px;
                color: #595959;
                letter-spacing: 0;
                line-height: 28px;

                .iconfont {
                    font-size: 40px;
                    color: #BFBFBF;
                }

                .icon-yiwanchengbuzhou {
                    color: $color-primary;
                }

                .all-select-text {
                    padding-left: 16px;
                }
            }

            .sure-btn {
                width: 340px;
                height: 96px;
                margin-right: 24px;
                margin-left: 24px;
            }
        }

        .prod-view {
            width: 100%;
            background: white;

            .webstat {
                width: 100%;
                height: 92px;
                line-height: 92px;

                .webstat-v {
                    width: 100%;
                    height: 46px;
                    line-height: 46px;
                    margin-top: 10px;
                    padding-left: 24px;

                    .webstat-title {
                        font-family: PingFangSC-Regular;
                        font-size: 24px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        text-align: right;
                        line-height: 46px;
                        float: left;
                    }

                    .webstat-val {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #000000;
                        letter-spacing: 0;
                        text-align: right;
                        line-height: 46px;
                        float: left;
                    }

                    .red {
                        color: red;
                        font-weight: bold;
                    }
                }

                .feeReim-v {
                    font-family: PingFangSC-Regular;
                    width: 94%;
                    text-align: left;
                    font-size: 24px;
                    color: #000000;
                    letter-spacing: 0;
                    float: left;
                    line-height: 46px;
                    //height: 46px;
                    padding-left: 24px;
                }
            }

            .item-header {
                height: 88px;
                width: 100%;
                padding-left: 32px;
                font-size: 28px;
                //line-height: 88px;
                color: #262626;
                letter-spacing: 0;
                border-bottom: 2px solid #F2F2F2;
                display: table;
                .iconfont {
                    font-size: 32px;
                }

                .icon-style {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #2F69F8;
                    letter-spacing: 0;
                    float: left;
                    padding-left: 18px;
                }
            }

            .product-list {
                font-size: 28px;
                color: #262626;
                letter-spacing: 0;
                line-height: 38px;

                .link-item {
                    border-bottom: 2px solid #F2F2F2;
                }

                .link-swipe-action {
                    width: 100% !important;
                }

                .product-list-item {
                    display: flex;
                    align-items: center;
                    padding: 32px 0px;
                    justify-content: flex-start;

                    &__left {
                        text-align: left;
                        color: #262626;
                        width: 35%;
                    }

                    &__center {
                        width: 60%;
                        display: flex;
                        justify-content: center;
                        flex-direction: column;

                        .row {
                            width: 100%;
                            font-size: 24px;
                            color: #8C8C8C;
                            letter-spacing: 0;
                            line-height: 28px;
                            display: flex;
                            justify-content: flex-end;
                            margin-bottom: 24px;

                            .num {
                                font-size: 28px;
                                color: #000000;
                                letter-spacing: 0;
                                line-height: 28px;
                                padding-left: 4px;
                                padding-right: 4px;
                            }
                        }

                        .row-3 {
                            margin-bottom: 0;

                            .materialSource {
                                line-height: 28px;
                                margin-right: 10px;
                                color: #2F69F8;
                            }

                            .status {
                                line-height: 28px;
                                margin-right: 10px;
                            }

                            .MatchSuccessfully .PartialMatch .success {
                                color: #2EB3C2;
                            }

                            .MatchFailed .fail {
                                color: #FF5A5A;
                            }

                            .count-price {
                                display: flex;

                                .num {
                                    margin-left: 10px;
                                }
                            }
                        }
                    }

                    &__center_2 {
                        width: 65%;
                        display: flex;
                        justify-content: center;
                        flex-direction: column;

                        .row {
                            width: 100%;
                            font-size: 24px;
                            color: #8C8C8C;
                            letter-spacing: 0;
                            line-height: 28px;
                            display: flex;
                            justify-content: flex-end;
                            margin-bottom: 24px;

                            .num {
                                font-size: 28px;
                                color: #000000;
                                letter-spacing: 0;
                                line-height: 28px;
                                padding-left: 4px;
                                padding-right: 4px;
                            }
                        }

                        .row-3 {
                            margin-bottom: 0;

                            .materialSource {
                                line-height: 28px;
                                margin-right: 10px;
                                color: #2F69F8;
                            }

                            .status {
                                line-height: 28px;
                                margin-right: 10px;
                            }

                            .success {
                                color: #2EB3C2;
                            }

                            .fail {
                                color: #FF5A5A;
                            }

                            .count-price {
                                display: flex;

                                .num {
                                    margin-left: 10px;
                                }
                            }
                        }
                    }

                    &__right {
                        text-align: right;
                        width: 5%;
                        color: #2F69F8;
                        height: 130px;
                        line-height: 130px;
                    }
                }
            }
        }
    }
</style>
