<template>
    <link-page class="market-activity-audit">
        <!--稽核记录-->
        <view class="audit-list-v">
            <view class="menu-stair" style="margin-bottom: 12px">
                <view class="line">
                    <view class="line-top"></view>
                    <view class="line-bottom"></view>
                </view>
                <view class="stair-title">稽核记录</view>
            </view>
            <view class="audit-list">
                <list>
                    <item v-for="(item,index) in auditList" :key="index">
                        <view slot="note">
                            <view class="audit" @tap="gotoItem(item)">
                                <view class="row">
                                    <view class="outer-ring">
                                        <view class="inner-ring"></view>
                                    </view>
                                    <view class="date">{{item.auditTime|date('YYYY-MM-DD HH:mm')}}</view>
                                    <view class="date">{{item.auditResult|lov('AUDIT_RESULT')}}</view>
                                </view>
                                <view class="row" style="margin-left: 24px;margin-top: 12px">
                                    <view class="title">{{item.auditClass}}</view>
                                    <view class="title" style="margin-top: 12rpx">{{item.auditType|lov('AUDIT_TYPE')}}</view>
                                    <view class="name">{{item.feedbackerName}}</view>
                                </view>
                            </view>
                        </view>
                    </item>
                </list>
            </view>
        </view>
    </link-page>
</template>

<script>
    export default {
        name: "audit",
        props: {
            //父ID
            parentId: {
                type: String,
                default: "",
            },
            //没用--页面来源 -
            // 1、执行反馈环节 executiveFeedback
            // 2、other 活动的其他信息(ps:这是一个页面)
            // 3、preview 活动预览界面而来
            // 4、view 活动查看界面查看而来
            // 5、审批或小程序消息进去 为空
            // 6、activityAudit 活动稽核
            pageSource: {
                type: String,
                default: ''
            },

        },
        data() {
            return {
                auditList: []
            }
        },
        async created() {
            await this.queryAuditData();
        },
        mounted() {
            this.$bus.$on('auditSuccessRefresh', async () => {
                await this.queryAuditData();
            });
        },
        methods: {
            /**
             * 查询稽核记录
             * <AUTHOR>
             * @date 2020-08-24
             * */
            async queryAuditData() {
                const data = await this.$http.post('action/link/exeFeedback/queryByExamplePage', {
                    sort: "created",
                    order: 'desc',
                    filtersRaw: [{id: 'actId', property: 'actId', value: this.parentId, operator: '='}, {
                        id: 'feedbackType',
                        property: 'feedbackType',
                        value: 'CheckFeedback',
                        operator: '='
                    }]
                });
                this.auditList = data.rows || [];
            },
            /**
             * 查看详情
             * <AUTHOR>
             * @date 2020-08-28
             * */
            gotoItem(item) {
                this.$nav.push('/pages/lj-market-activity/market-activity/audit-item-page', {
                    data: item
                })
            }
        }
    }
</script>

<style lang="scss">
    .market-activity-audit {
        clear: both;

        .audit-list-v {
            .menu-stair {
                width: 100%;
                margin-left: 24px;
                padding-top: 24px;
                @include flex-start-center;

                .line {
                    .line-top {
                        width: 8px;
                        height: 16px;
                        background: #3FE0E2;
                    }

                    .line-bottom {
                        width: 8px;
                        height: 16px;
                        background: #2F69F8;
                    }
                }

                .stair-title {
                    width: 30%;
                    margin-left: 16px;
                    font-family: PingFangSC-Semibold, serif;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 1px;
                    line-height: 32px;
                }
            }

            .audit-list {
                margin: 24px;

                .audit {
                    .row {
                        .outer-ring {
                            background: #EFF3FF;
                            width: 32px;
                            height: 32px;
                            border-radius: 50%;
                            position: relative;
                            float: left;

                            .inner-ring {
                                background: #2F69F8;
                                width: 12px;
                                height: 12px;
                                line-height: 32px;
                                border-radius: 50%;
                                position: absolute;
                                top: 10px;
                                left: 10px;
                            }
                        }

                        .date {
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #8C8C8C;
                            letter-spacing: 0;
                            line-height: 28px;
                            padding-left: 56px;

                        }

                        .title {
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 28px;
                            width: 70%;
                            float: left;
                        }

                        .name {
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #8C8C8C;
                            letter-spacing: 0;
                            text-align: right;
                            line-height: 28px;
                            padding-right: 24px;
                        }
                    }
                }
            }
        }
    }
</style>
