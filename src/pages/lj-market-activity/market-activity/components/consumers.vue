<template>
    <link-page>
        <view class="market-activity-consumers">
            <!--消费者无边界类-->
            <view class="consumers-view" v-if="!haveMarginFlag">
                <view class="item-header">
                    <view style="width: 40%;float: left">消费者</view>
                    <view style="float: left;width: 50%;padding-right: 24rpx;color: #2F69F8;display: flex;align-items: center;justify-content: flex-end;text-align:right;"
                          v-if="!$utils.isEmpty(pageSource) && editFlag && pageSource !== 'activityAudit' && !isSalesAreaManager">
                        <view class="item-info" @tap="addPeopleScreen">人群包</view>
                        <view class="item-info" @tap="pickConsumers">添加</view>
                    </view>
                </view>
                <view>
                    <list style="margin: 12px">
                        <link-swipe-action v-for="(item,index) in consumerList.slice(0,3)" :key="`${item.id}_${consumerList.length}`">
                            <link-swipe-option slot="option"
                                               v-if="parentData.status === 'New' && !$utils.isEmpty(pageSource) && editFlag && pageSource !== 'activityAudit' && operationFlag"
                                               @tap="handleConsumerDelete(item,index)">删除
                            </link-swipe-option>
                            <item :arrow="false" @tap="gotoItem(item)" class="consumers-item-wrap">
                                <view class="consumers-item">
                                    <view class="acct-name">{{item.acctName}}</view>
                                    <view class="top-label">
                                        <text class="label-text">{{item.saleman}}跟进</text>
                                    </view>

                                    <view class="tags-wrap">
                                        <view class="tag-item" v-if="item.subAcctType">{{item.subAcctType | lov('ACCT_SUB_TYPE')}}</view>
                                        <view class="tag-item" v-if="item.loyaltyLevel">{{item.loyaltyLevel | lov('ACCT_MEMBER_LEVEL') }}</view>
                                        <view class="tag-item red" v-if="item.impFlag === 'Y'" >重点客户</view>
                                    </view>
                                    <view class="info-item">
                                        <view class="info-label">联系方式</view>
                                        <view class="phone">{{item.phone}}</view>
                                        <view class="status-view" v-if="item.matchStatus !== 'Absent'" @tap.stop="updateInterCust(item,$event)" :style="{background: matchTypeColor[item.matchStatus]}">
                                            <view class="status">{{item.matchStatus | lov('CUST_MATCH_TYPE')}}</view>
                                        </view>
                                    </view>
                                    <view class="info-item">
                                        <view class="info-label">单位</view>
                                        <view class="info-value">{{item.company}}</view>
                                    </view>
                                    <view class="info-item">
                                        <view class="info-label">职务</view>
                                        <view class="info-value">{{item.jobTitle}}</view>
                                    </view>
                                    <view class="info-item">
                                        <view class="info-label">所属客户</view>
                                        <view class="info-value">{{item.belongToStore}}</view>
                                    </view>
                                </view>
                            </item>
                        </link-swipe-action>
                    </list>
                    <list>
                        <view @tap="gotoConsumerList()" v-if="consumerTotal>3" :arrow="false">
                            <view class="more">
                                查看全部({{consumerTotal}})>>
                            </view>
                        </view>
                    </list>
                </view>
            </view>
            <!--消费者 有边界类-->
            <view class="consumers-v" id="consumers-v" v-else-if="haveMarginFlag">
                <view class="menu-stair" style="margin-bottom: 12px">
                    <view class="line">
                        <view class="line-top"></view>
                        <view class="line-bottom"></view>
                    </view>
                    <view class="stair-title">消费者</view>
                    <view class="edit" v-if="!$utils.isEmpty(pageSource) && editFlag && pageSource !== 'activityAudit' && !isSalesAreaManager">
                        <view class="item-info" @tap="addPeopleScreen">人群包</view>
                        <view class="item-info" @tap="pickConsumers">添加</view>
                    </view>
                </view>
                <view>
                    <list style="margin: 12px">
                        <link-swipe-action v-for="(item,index) in consumerList.slice(0,3)" :key="`${item.id}_${consumerList.length}`">
                            <link-swipe-option slot="option"
                                               v-if="parentData.status === 'New' && !$utils.isEmpty(pageSource) && editFlag && pageSource !== 'activityAudit' && operationFlag"
                                               @tap="handleConsumerDelete(item,index)">删除
                            </link-swipe-option>
                            <item :arrow="false" @tap="gotoItem(item)" class="consumers-item-wrap">
                                <view class="consumers-item">
                                    <view class="acct-name">{{item.acctName}}</view>
                                    <view class="top-label">
                                        <text class="label-text">{{item.saleman}}跟进</text>
                                    </view>
                                    <view class="tags-wrap">
                                        <view class="tag-item" v-if="item.subAcctType">{{item.subAcctType | lov('ACCT_SUB_TYPE')}}</view>
                                        <view class="tag-item" v-if="item.loyaltyLevel">{{item.loyaltyLevel | lov('ACCT_MEMBER_LEVEL') }}</view>
                                        <view v-if="item.impFlag === 'Y'" class="tag-item red">重点客户</view>
                                    </view>
                                    <view class="info-item">
                                        <view class="info-label">联系方式</view>
                                        <view class="phone">{{item.phone}}</view>
                                        <view class="status-view" v-if="item.matchStatus !== 'Absent'" @tap.stop="updateInterCust(item,$event)" :style="{background: matchTypeColor[item.matchStatus]}">
                                            <view class="status">{{item.matchStatus | lov('CUST_MATCH_TYPE')}}</view>
                                        </view>
                                    </view>
                                    <view class="info-item">
                                        <view class="info-label">单位</view>
                                        <view class="info-value">{{item.company}}</view>
                                    </view>
                                    <view class="info-item">
                                        <view class="info-label">职务</view>
                                        <view class="info-value">{{item.jobTitle}}</view>
                                    </view>
                                    <view class="info-item">
                                        <view class="info-label">所属客户</view>
                                        <view class="info-value">{{item.belongToStore}}</view>
                                    </view>
                                </view>
                            </item>
                        </link-swipe-action>
                    </list>
                    <list>
                        <view @tap="gotoConsumerList()" v-if="consumerTotal>3" :arrow="false">
                            <view class="more">
                                查看全部({{consumerTotal}})>>
                            </view>
                        </view>
                    </list>
                </view>
            </view>
            <link-dialog ref="custMatchTypeDialog" :initial="true">
                <view slot="head">
                    请选择匹配类型
                </view>
                <link-button style="margin-left: 5px;margin-right: 5px;"
                             v-for="(item,index) in custMatchTypeList" :key="index" @tap="pikeMatchStatus(item)">{{item.name}}</link-button>
            </link-dialog>
        </view>
    </link-page>
</template>

<script lang="jsx">
    import {PreloadImg} from "../../../../utils/service/PreloadImg";
    import {DateService, LovService} from "link-taro-component";
    import {ROW_STATUS} from "../../../../utils/constant";
    import Taro from "@tarojs/taro";

    export default {
        name: "consumers",
        props: {
            //是否为有边距类型
            haveMarginFlag: {
                type: Boolean,
                default: false
            },
            //有边距时是否需要添加按钮-弃用
            addBtnFlag: {
                type: Boolean,
                default: false
            },
            //父ID
            parentId: {
                type: String,
                default: "",
            },
            //页面来源 -
            // 1、执行反馈环节 executiveFeedback
            // 2、other 活动的其他信息(ps:这是一个页面)
            // 3、preview 活动预览界面而来
            // 4、view 活动查看界面查看而来
            // 5、审批或小程序消息进去 为空
            // 6、activityAudit 活动稽核
            pageSource: {
                type: String,
                default: ''
            },
            //父对象-暂时为活动对象
            parentData: {
                type: Object,
                default: {},
            },
            //是否可以操作删除数据。approvalId为空时可操作
            operationFlag: {
                type: Boolean,
                default: false
            },
            updateAnalysisData: { // 是否刷新活动分析数据-没用
                type: Boolean,
                default: false
            }
        },
        computed: {
            isSalesAreaManager: function () {
                return ['CityManager', 'SalesRegionManager', 'SalesAreaManager',].includes(Taro.getStorageSync('token').result.positionType)
            }
        },
        data() {
            let companyId = this.$taro.getStorageSync('token').result.coreOrganizationTile.l3Id;        // 品牌公司代码
            const consumerTotal = 0;//消费者总数
            let editFlag = false;//是否可以编辑基础信息
            //页面来源 executiveFeedback 非执行反馈、执行反馈：活动状态 MC_STATUS : 新建 审批状态 APRO_STATUS : 未提交、已拒绝 的活动能够编辑；
            //页面来源 executiveFeedback 执行反馈：活动状态 MC_STATUS : 进行中、已结束、已发布 审批状态 APRO_STATUS : 申请审批通过、反馈驳回、反馈撤回 的活动能够编辑；
            if ((this.pageSource !== 'executiveFeedback' && this.pageSource !== 'activityAudit' && this.parentData.status === 'New'
                && (this.parentData.aproStatus === 'New' || this.parentData.aproStatus === 'Refused')
            ) || (this.pageSource === 'executiveFeedback' && (this.parentData.status === 'Processing' || this.parentData.status === 'Closed' || this.parentData.status === 'Published')
                && (this.parentData.aproStatus === 'Approve' || this.parentData.aproStatus === 'Refeedback' || this.parentData.aproStatus === 'RefeedWithdraw')
            )) {
                editFlag = true;
            }
            return {
                addFlag: true,     // 是否可以新建消费者
                editFlag,
                userInfo: {},//用户信息
                consumerList: [],
                consumerTotal,
                matchTypeColor: { // 匹配状态显示颜色
                    Interaction: '#32CD32', // 新增-绿色
                    Absent: '#FF0000', // 未到场-红色
                    Success: '#2F69F8' // 匹配成功-蓝色
                },
                //消费者选择
                consumerOption: new this.AutoList(this, {
                    module: this.$env.appURL + '/action/link/consumer',
                    url: {
                        queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/consumerListSend'
                    },
                    sortOptions: null,
                    exactSearchFields: [{
                        field: 'acctName',
                        showValue: '消费者姓名', // 展示名,用于显示的字段=
                        exactSearch: true,
                        searchOnChange: true,
                        clearOnChange: true
                    }, {
                        field: 'mobilePhone1',
                        showValue: '消费者手机号', // 展示名,用于显示的字段=
                        exactSearch: true,
                        searchOnChange: true,
                        clearOnChange: true
                    }],
                    filterOption: [
                        {label: '消费者姓名', field: 'acctName', type: 'text'},
                        {label: '消费者手机号', field: 'mobilePhone1', type: 'text'},
                        {label: '消费者类型', field: 'subAcctType', type: 'lov', lov: 'ACCT_SUB_TYPE', lovOption: {parentType: 'ACTIVITY_COMPANY', parentVal: companyId}
                    }],
                    searchFields: ['acctName', 'mobilePhone1'],
                    param: {
                        rows: 25,
                        filterActivityId: this.parentId,
                        filtersRaw: [
                            {id: 'companyId', property: 'companyId', value: companyId, operator: '='},
                            {id: 'consumerType', property: 'consumerType', value: 'ChannelConsumer', operator: '='},
                            {id: 'accntChannel', property: 'accntChannel', value: 'MarketingPlatform', operator: '='},
                            {id: 'empFlag', property: 'empFlag', value: 'N', operator: '='},
                            {id: 'followFlag', property: 'followFlag', value: 'Y', operator: '='}
                        ],
                        oauth: 'MY_POSTN_ONLY'
                    },
                    hooks: {
                        beforeLoad (option) {
                            for (let i = 0; i < option.param.filtersRaw.length; i++) {
                                if (option.param.filtersRaw[i].property === 'acctName') {
                                    option.param.filtersRaw[i].operator = 'like';
                                }
                            }
                        }
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} arrow={false} style="margin: 12px;border-radius: 8px;position: relative;overflow: hidden;padding: 20px 14px 14px 14px;">
                                <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                                <view style="width: 100%;">
                                    <view style="width: 100%;font-size: 16px;color: #212223;line-height: 24px;font-weight: 600;margin-bottom: 8px;">{data.acctName}</view>
                                    <view style={"background: #2F69F8;color: #fff;transform: skew(30deg, 0);display: flex;border-bottom-left-radius: 7px;;position: absolute;right: -5px;top: 0;"}>
                                        <text style={"font-size: 12px;transform: skew(-30deg, 0);padding: 4px 16px;"}>{data.fstName}跟进</text>
                                    </view>

                                    <view style="display: flex;margin-bottom: 8px;">
                                        <view style="padding:0 5px;font-size: 11px;font-weight: 400;color: #3F66EF;line-height: 18px;background: #F0F5FF;border-radius: 2px;text-align: center; margin-right: 8px;">{LovService.filter(data.subAcctType, 'ACCT_SUB_TYPE')}</view>
                                        <view style="padding:0 5px;font-size: 11px;font-weight: 400;color: #3F66EF;line-height: 18px;background: #F0F5FF;border-radius: 2px;text-align: center; margin-right: 8px;">{ LovService.filter(data.loyaltyLevel, 'ACCT_MEMBER_LEVEL') }</view>
                                        {data.impFlag === 'Y' ? <view style="padding:0 5px;font-size: 11px;font-weight: 400;color: #FF461E;line-height: 18px;background: #FFF1EB;border-radius: 2px;text-align: center; margin-right: 8px;">重点客户</view> : ''}
                                    </view>
                                    <view style="width: 100%;font-size: 14px;display: flex;color: #317DF7;margin-bottom: 4px;line-height: 22px;">
                                        <view style="color: #999999;width: 66px;">联系方式</view>
                                        <view> {data.mobilePhone1}</view>
                                    </view>
                                    <view style="width: 100%;font-size: 14px;display: flex;color: #333333;margin-bottom: 4px;line-height: 22px;">
                                        <view style="color: #999999; width:66px;">单位</view>
                                        <view> {data.company}</view>
                                    </view>
                                    <view style="width: 100%;font-size: 14px;display: flex;color: #333333;margin-bottom: 4px;line-height: 22px;">
                                        <view style="color: #999999;width: 66px;">职务</view>
                                        <view> {data.position}</view>
                                    </view>
                                    <view style="width: 100%;font-size: 14px;display: flex;color: #333333;line-height: 22px;">
                                        <view style="color: #999999;width: 66px;">所属客户</view>
                                        <view> {data.belongToStore}</view>
                                    </view>
                                </view>
                            </item>
                        )
                    },
                    slots: {
                        other: () => (
                            <view>
                                {this.addFlag && <link-fab-button onTap={this.addConsumer}></link-fab-button>}
                            </view>),
                        filterGroup: () => (
                            <link-filter-group>
                                <link-filter-item label="创建时间(升序)" param={{sort: {field: 'created', desc: false}}}/>
                                <link-filter-item label="最近更新(升序)" param={{sort: {field: 'lastUpdated', desc: false}}}/>
                                <view  onTap={this.chooseStoreList}
                                       style={this.isChosen ? 'padding: 8rpx 16rpx;'+
                                           'margin-right: 8rpx;' +
                                           'white-space: nowrap;' +
                                           'display: inline-block;' +
                                           'background: #EDF3FF;' +
                                           'color: #2F69F8;'+
                                           'border-radius: 4rpx;':'padding: 8rpx 16rpx;'+
                                           'margin-right: 8rpx;' +
                                           'white-space: nowrap;' +
                                           'display: inline-block;' +
                                           'background-color: #f2f2f2;' +
                                           'color: #333333;' +
                                           'border-radius: 4rpx;'}
                                       >所属客户</view>
                            </link-filter-group>
                        )
                    }
                }),
                customerOption: new this.AutoList(this, {
                    module: this.$env.appURL + '/action/link/accnt',
                    url: {
                        queryByExamplePage: this.$env.appURL + '/link/interCustTerminal/queryAccntPage'
                    },
                    param: {
                        postnId: '',
                        oauth: 'MY_POSTN_ONLY',
                    },
                    searchFields: ['acctName'],
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} className="select-box" arrow="false">
                                <link-checkbox val={data.accntId} toggleOnClickItem slot="thumb"></link-checkbox>
                                <view slot="title" style="display: flex;">{data.acctName} <view style="margin-left: 1em;background: #EDF3FF;color: #2F69F8;border: 1px solid #EDF3FF;font-size:12px;padding: 3px;border-radius: 3px;">{LovService.filter(data.acctType, 'ACCT_TYPE')}</view></view>
                                <view slot="note">{data.province}{data.city}{data.district}{data.address}</view>
                            </item>)
                    },
                    hooks: {
                        beforeLoad (option) {
                            delete option.param.order;
                            delete option.param.sort;
                            option.param.postnId = this.userInfo.postnId;
                        }
                    }
                }),
                peopleScreenOption: new this.AutoList(this, {
                    module: this.$env.dmpURL + '/link/cdcConScreen',
                    param: {
                        oauth: 'MY_POSTN_ONLY',
                        filtersRaw: [{id: 'status', property: 'status', value: '[finish]', operator: 'IN'}]
                    },
                    searchFields: ['crowdSelection'],
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} arrow={false} style="margin: 12px;border-radius: 8px;position: relative;overflow: hidden;padding: 20px 14px 14px 14px;">
                                <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                                <view style="width: 100%;">
                                    <view style="width: 100%;font-size: 16px;color: #212223;line-height: 24px;font-weight: 600;margin-bottom: 8px; display:flex; justify-content: space-between;">
                                        <view>{data.crowdSelection}</view>
                                        <view style="padding:0 5px;font-size: 12px;font-weight: 400;color: #3F66EF;line-height: 18px;text-align: center; margin-right: 8px;">{LovService.filter(data.status, 'CROWD_SELECTION_STATE')}</view>
                                    </view>
                                    <view style="width: 100%;font-size: 14px;display: flex;color: #333333;margin-bottom: 4px;line-height: 22px;">
                                        <view style="color: #999999;width: 66px;">更新时间</view>
                                        <view> {DateService.filter(data.lastUpdated, 'YYYY-MM-DD HH:mm:ss')}</view>
                                    </view>
                                    <view style="width: 100%;font-size: 14px;display: flex;color: #333333;margin-bottom: 4px;line-height: 22px;">
                                        <view style="color: #999999; width:66px;">创建时间</view>
                                        <view> {DateService.filter(data.created, 'YYYY-MM-DD HH:mm:ss')}</view>
                                    </view>
                                    <view style="width: 100%;font-size: 14px;display: flex;color: #333333;margin-bottom: 4px;line-height: 22px;">
                                        <view style="color: #999999;width: 78px;">人群包描述</view>
                                        <view> {data.description}</view>
                                    </view>
                                </view>
                            </item>)
                    },
                    hooks: {
                    }
                }),
                peopleScreenId: '', // 人群包ID
                peopleScreenConsumerOption: new this.AutoList(this, {
                    module: this.$env.appURL + '/action/link/sendDmp/send',
                    url: {
                        queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/send'
                    },
                    param: {
                        dmpUrl: '/link/cdcConSrcLine/queryByExamplePage',
                        filtersRaw: []
                    },
                    sortOptions: null,
                    exactSearchFields: [{
                        field: 'name',
                        showValue: '消费者姓名', // 展示名,用于显示的字段=
                        exactSearch: true,
                        searchOnChange: true,
                        clearOnChange: true
                    }, {
                        field: 'phoneNumber',
                        showValue: '消费者手机号', // 展示名,用于显示的字段=
                        exactSearch: true,
                        searchOnChange: true,
                        clearOnChange: true
                    }],
                    hooks: {
                        beforeLoad (option) {
                            for (let i = 0; i < option.param.filtersRaw.length; i++) {
                                if (option.param.filtersRaw[i].property === 'name') {
                                    option.param.filtersRaw[i].operator = 'like';
                                }
                            }
                            option.param.filtersRaw = [
                                ...option.param.filtersRaw,
                                {id: 'headId', property: 'headId', value: this.peopleScreenId, operator: '='},
                                {id: 'postnId', property: 'postnId',value: this.userInfo.postnId, operator: '='}
                            ]
                        }
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} arrow={false} style="margin: 12px;border-radius: 8px;position: relative;overflow: hidden;padding: 20px 14px 14px 14px;">
                                <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                                <view style="width: 100%;">
                                    <view style="width: 100%;font-size: 16px;color: #212223;line-height: 24px;font-weight: 600;margin-bottom: 8px;">{data.name}</view>
                                    <view style={"background: #2F69F8;color: #fff;transform: skew(30deg, 0);display: flex;border-bottom-left-radius: 7px;;position: absolute;right: -5px;top: 0;"}>
                                        <text style={"font-size: 12px;transform: skew(-30deg, 0);padding: 4px 16px;"}>{data.fstName}跟进</text>
                                    </view>

                                    <view style="display: flex;margin-bottom: 8px;">
                                        <view style="padding:0 5px;font-size: 11px;font-weight: 400;color: #3F66EF;line-height: 18px;background: #F0F5FF;border-radius: 2px;text-align: center; margin-right: 8px;">{LovService.filter(data.type, 'ACCT_SUB_TYPE')}</view>
                                        <view style="padding:0 5px;font-size: 11px;font-weight: 400;color: #3F66EF;line-height: 18px;background: #F0F5FF;border-radius: 2px;text-align: center; margin-right: 8px;">{ LovService.filter(data.loyaltyLevel, 'ACCT_MEMBER_LEVEL') }</view>
                                    </view>
                                    <view style="width: 100%;font-size: 14px;display: flex;color: #317DF7;margin-bottom: 4px;line-height: 22px;">
                                        <view style="color: #999999;width: 66px;">联系方式</view>
                                        <view> {data.phoneNumber}</view>
                                    </view>
                                    <view style="width: 100%;font-size: 14px;display: flex;color: #333333;margin-bottom: 4px;line-height: 22px;">
                                        <view style="color: #999999; width:66px;">单位</view>
                                        <view> {data.companyName}</view>
                                    </view>
                                    <view style="width: 100%;font-size: 14px;display: flex;color: #333333;margin-bottom: 4px;line-height: 22px;">
                                        <view style="color: #999999;width: 66px;">职务</view>
                                        <view> {data.position}</view>
                                    </view>
                                    <view style="width: 100%;font-size: 14px;display: flex;color: #333333;line-height: 22px;">
                                        <view style="color: #999999;width: 66px;">所属客户</view>
                                        <view> {data.belongToStore}</view>
                                    </view>
                                </view>
                            </item>
                        )
                    }
                }),
                isChosen: false,
                custMatchTypeList:[],
            }
        },
        async created() {
            this.custMatchTypeList = await this.$lov.getLovByType('CUST_MATCH_TYPE');
            this.userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
            await this.queryConsumerList();
            // 不展示消费者添加按钮：不在值列表【ASSIGN_POSITION】中，且值列表状态为有效的，其余的职位类型进入【消费者列表】【市场活动添加消费者，新建按钮】【礼赠添加消费者新建】【动销添加消费者新建】【拜访添加消费者新建】【名单提报添加消费者新建】
            const lovData = await this.$lov.getLovByType('ASSIGN_POSITION');
            const assignList = lovData.map(item => item.val);
            if (!assignList.includes(this.userInfo.positionType)) {
                this.addFlag = false;
            }
        },
        mounted() {
            this.$bus.$on('initConsumers', async () => {
                await this.queryConsumerList();
            })
        },
        methods: {
            /**
             * @desc 选择所属客户数据
             **/
            async chooseStoreList () {
                this.isChosen = !this.isChosen
                if (this.isChosen) {
                    const list = await this.$object(this.customerOption, {
                        pageTitle: '请选择所属客户',
                        showInDialog: true,
                        multiple: false,
                        autoListProps: {searchInputBinding: {props: {placeholder: '搜索所属客户名称'}}}
                    });
                    this.consumerOption.option.param['belongToStoreIdList'] = [list.id];
                    this.consumerOption.methods.reload();
                } else {
                    delete this.consumerOption.option.param['belongToStoreIdList'];
                    this.consumerOption.methods.reload();
                }
            },


            //2021-08-04考虑多人操作的场景
            async operationalControl (){
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.parentData.id
                });
                if ((this.pageSource !== 'executiveFeedback' && this.pageSource !== 'activityAudit' && data.result.status === 'New'
                    && (data.result.aproStatus === 'New' || data.result.aproStatus === 'Refused')
                ) || (this.pageSource === 'executiveFeedback' && (data.result.status === 'Processing' || data.result.status === 'Closed' || data.result.status === 'Published')
                    && (data.result.aproStatus === 'Approve' || data.result.aproStatus === 'Refeedback' || data.result.aproStatus === 'RefeedWithdraw')
                )) {
                    return  true;
                } else {
                    return false;
                }
            },
          /**
           * @createdBy  张丽娟
           * @date  2021/4/8
           * @methods getMasterNamePickOptionOuath
           * @para
           * @description 获取宴席主家安全性
           */
          async getMasterNamePickOptionOuath() {
            const param = {filtersRaw: [{id: 'moduleCode', property: 'moduleCode', operator: 'LIKE', value: 'corpwx_consumer_menu'}]};
            const url = 'action/link/appMenu/queryByExamplePage';
            const {success, rows} = await this.$http.post(url, param);
            if(success){
              if(rows.length > 0){
                const parentId = rows[0].id;
                let securityMode = 'MY_ORG';
                if (this.userInfo.positionType === 'Salesman'
                  || this.userInfo.positionType === 'AccountManager'
                  || this.userInfo.positionType === 'CustServiceSpecialist'
                  || this.userInfo.positionType === 'SalesTeamLeader'
                  || this.userInfo.positionType === 'SalesManager') {
                  securityMode = 'MY_POSTN';
                }
                const param = {
                  parentId,
                  securityMode
                };
                const securityMenuOauth = await this.getSecurityMenuOauth(param);
                this.consumerOption.option.param['oauth'] = securityMenuOauth;
              }
            }else{
              this.$showError('请维护安全性菜单');
            }
          },
          /**
           * @createdBy  张丽娟
           * @date  2021/4/8
           * @methods getSecurityMenuOauth
           * @para
           * @description 获取安全性菜单
           */
          async getSecurityMenuOauth(param) {
            const url = 'action/link/appSecurityMenu/queryByExamplePage';
            const {success, rows} = await this.$http.post(url, param);
            if (!success || !rows || rows.length === 0) {
              return;
            }
            return `ALL${rows[0].id}`;
          },
            /**
             * @desc 跳转至人群包添加消费者数据
             * <AUTHOR>
             * @date 2023/9/11 14:55
             **/
            async addPeopleScreen () {
                const flag = await this.operationalControl();
                if(!flag){
                    this.$message.warn('活动已被更新，当前活动状态和审批状态不允许添加消费者，请返回列表重新查询活动数据。');
                    return ;
                }
                const list = await this.$object(this.peopleScreenOption, {
                    pageTitle: '人群包',
                    multiple: false,
                    showInDialog: true
                });
                this.peopleScreenId = list.id;
                if (this.peopleScreenId) {
                    this.$utils.showLoading();
                    setTimeout(async () => {
                        this.$utils.hideLoading();
                        const item = await this.$object(this.peopleScreenConsumerOption, {
                            pageTitle: '消费者',
                            multiple: true,
                            selected: this.consumerList.map(item => item.id),
                            beforeConfirm: async (data) => {
                                let consumerDataList = [];
                                consumerDataList = data.map(i => ({
                                    'mcActId': this.parentId,
                                    'company': i.companyName,
                                    'jobTitle': i.position,
                                    'salemanId': i.conPostnId,
                                    'accntChannelId': i.consumerId,
                                    'acctName': i.name,
                                    'phone': i.phoneNumber,
                                    'relevanceType': 'New',
                                    'row_status': 'NEW',
                                    'saleman': i.fstName
                                }));
                                // 执行反馈补录消费者，【来源类型】为'反馈补录'，【匹配状态】为'新增嘉宾'
                                if (this.pageSource === 'executiveFeedback') {
                                    consumerDataList.forEach((item, index) => {
                                        consumerDataList[index]['sourceType'] = 'BackTracking'; // 来源类型
                                        consumerDataList[index]['matchStatus'] = 'Interaction'; // 匹配状态
                                    });
                                }
                                await this.$http.post(this.$env.appURL + '/marketactivity/link/interCust/batchUpsertICCrow', consumerDataList);
                                this.$emit('update:updateAnalysisData', true);
                                await this.queryConsumerList();
                            }
                        });
                    }, 1000)
                }
            },
          /**
             * 选择消费者
             * <AUTHOR>
             * @date 2020-08-07
             * */
            async pickConsumers() {
                const flag = await this.operationalControl();
                if(!flag){
                    this.$message.warn('活动已被更新，当前活动状态和审批状态不允许添加消费者，请返回列表重新查询活动数据。');
                    return ;
                }
                const list = await this.$object(this.consumerOption, {
                    pageTitle: '消费者',
                    multiple: true,
                    selected: this.consumerList.map(item => item.id),
                    beforeConfirm: async (data) => {
                        let consumerDataList = [];
                        consumerDataList = data.map(i => ({
                            'mcActId': this.parentId,
                            'openId': i.wxOwnOpenId,
                            'company': i.company,
                            'jobTitle': i.position,
                            'salemanId': i.postnId,
                            'accntChannelId': i.id,
                            'headUrl': i.wxHeadimgurl,
                            'acctName': i.acctName,
                            'phone': i.mobilePhone1,
                            'gender': i.gender,
                            'birthDate': i.birthday,
                            'relevanceType': 'New',
                            'row_status': 'NEW',
                            'birthDateType': i.birthdayType,
                            'empFlag': 'N',
                            'terminalFlag': 'N',
                            'saleman': i.fstName
                        }));
                        // 执行反馈补录消费者，【来源类型】为'反馈补录'，【匹配状态】为'新增嘉宾'
                        if (this.pageSource === 'executiveFeedback') {
                            consumerDataList.forEach((item, index) => {
                                consumerDataList[index]['sourceType'] = 'BackTracking'; // 来源类型
                                consumerDataList[index]['matchStatus'] = 'Interaction'; // 匹配状态
                            });
                        }
                        await this.$http.post(this.$env.appURL + '/marketactivity/link/interCust/batchUpsertICCrow', consumerDataList);
                        this.$emit('update:updateAnalysisData', true);
                        await this.queryConsumerList();
                    }
                });
            },
            /**
             * 新增消费者
             * <AUTHOR>
             * @date 2020-08-07
             * */
            async addConsumer() {
                const id = await this.$newId();
                const accountItem = {
                  id: id,
                  row_status: ROW_STATUS.NEW,
                  consumerDataType: 'ChannelConsumer',
                  dataSource: 'MarketingPlatform',
                  dataType: 'Consumer',
                  accntSourceFrom: 'SalesAssistant',
                  orgId: this.userInfo.orgId,
                  fstName: this.userInfo.firstName,
                  postnId: this.userInfo.postnId,
                  belongToCompanyId: this.userInfo.coreOrganizationTile['l3Id'] || '',
                  type: "ToBeFollowed",
                  birthType: 'Yang',
                  brandPreference: "",
                  hobby: "",
                  terminalFlag : 'N',
                  listOfTags: {
                      accountId: id,
                      list: []
                  }
                };
                let pathFlag = await this.$utils.getCfgProperty('lj_consumers_flag')
                if (['other', 'preview'].includes(this.pageSource)) {
                    if (pathFlag === 'Y') {
                        this.$nav.redirect('/pages/lj-consumers/account/account-item-edit-page.vue', {
                            data: accountItem,
                            marketActivityId: this.parentId,
                            userInfo: this.userInfo,
                            pageFrom: 'Activity',
                            pageSource: this.pageSource,
                            source: ['Banquet', 'YanXiPresent'].includes(this.parentData.activityType) ? 'Yanxi' : 'Activity',
                            callback: (data) => {
                                this.consumerOption.methods.reload();
                                this.$emit('update:updateAnalysisData', true)
                            },
                            updateConsumers: async (data) => {
                                let consumerDataList = [{
                                    'mcActId': this.parentId,
                                    'openId': data.openId,
                                    'company': data.company,
                                    'jobTitle': data.position,
                                    'salemanId': data.postnId,
                                    'accntChannelId': data.id,
                                    'acctName': data.name,
                                    'phone': data.phoneNumber,
                                    'gender': data.gender,
                                    'birthDate': data.birth,
                                    'relevanceType': 'New',
                                    'row_status': 'NEW',
                                    'birthDateType': data.birthType,
                                    'empFlag': 'N',
                                    'terminalFlag': 'N',
                                    'saleman': data.fstName
                                }];
                                // 执行反馈补录消费者，【来源类型】为'反馈补录'，【匹配状态】为'新增嘉宾'
                                if (this.pageSource === 'executiveFeedback') {
                                    consumerDataList.forEach((item, index) => {
                                        consumerDataList[index]['sourceType'] = 'BackTracking'; // 来源类型
                                        consumerDataList[index]['matchStatus'] = 'Interaction'; // 匹配状态
                                    });
                                }
                                await this.$http.post(this.$env.appURL + '/marketactivity/link/interCust/batchUpsertICCrow', consumerDataList);
                                this.$emit('update:updateAnalysisData', true);
                                await this.queryConsumerList();
                                // 新建活动
                                if (this.pageSource === 'other') {
                                    let marketActItem = this.$dataService.getMarketActivityItem();
                                    if (['Banquet', 'YanXiPresent'].includes(marketActItem.activityType)) {
                                        marketActItem.masterId = data.id;
                                        marketActItem.masterName = data.name;
                                        this.$dataService.setMarketActivityItem(marketActItem);
                                    }
                                }
                            },
                            viewUpdateMaster: (data) => {
                                // 消费信息同步到宴席主家字段上
                                this.$bus.$emit('viewUpdateMaster', data);
                            }
                        })
                    } else {
                        this.$nav.redirect('/pages/lj-consumers/account-old/account-item-edit-page.vue', {
                            data: accountItem,
                            marketActivityId: this.parentId,
                            userInfo: this.userInfo,
                            pageFrom: 'Activity',
                            callback: (data) => {
                                this.consumerOption.methods.reload();
                                this.$emit('update:updateAnalysisData', true)
                            },
                        })
                    }
                } else {
                    if (pathFlag === 'Y') {
                        this.$nav.push('/pages/lj-consumers/account/account-item-edit-page.vue', {
                            data: accountItem,
                            marketActivityId: this.parentId,
                            userInfo: this.userInfo,
                            pageFrom: 'Activity',
                            pageSource: this.pageSource,
                            source: ['Banquet', 'YanXiPresent'].includes(this.parentData.activityType) ? 'Yanxi' : 'Activity',
                            callback: (data) => {
                                this.consumerOption.methods.reload();
                                this.$emit('update:updateAnalysisData', true)
                            },
                            updateConsumers: async (data) => {
                                let consumerDataList = [{
                                    'mcActId': this.parentId,
                                    'openId': data.openId,
                                    'company': data.company,
                                    'jobTitle': data.position,
                                    'salemanId': data.postnId,
                                    'accntChannelId': data.id,
                                    'acctName': data.name,
                                    'phone': data.phoneNumber,
                                    'gender': data.gender,
                                    'birthDate': data.birth,
                                    'relevanceType': 'New',
                                    'row_status': 'NEW',
                                    'birthDateType': data.birthType,
                                    'empFlag': 'N',
                                    'terminalFlag': 'N',
                                    'saleman': data.fstName
                                }];
                                // 执行反馈补录消费者，【来源类型】为'反馈补录'，【匹配状态】为'新增嘉宾'
                                if (this.pageSource === 'executiveFeedback') {
                                    consumerDataList.forEach((item, index) => {
                                        consumerDataList[index]['sourceType'] = 'BackTracking'; // 来源类型
                                        consumerDataList[index]['matchStatus'] = 'Interaction'; // 匹配状态
                                    });
                                }
                                await this.$http.post(this.$env.appURL + '/marketactivity/link/interCust/batchUpsertICCrow', consumerDataList);
                                this.$emit('update:updateAnalysisData', true);
                                await this.queryConsumerList();
                                // 新建活动
                                if (this.pageSource === 'other') {
                                    let marketActItem = this.$dataService.getMarketActivityItem();
                                    if (['Banquet', 'YanXiPresent'].includes(marketActItem.activityType)) {
                                        marketActItem.masterId = data.id;
                                        marketActItem.masterName = data.name;
                                        this.$dataService.setMarketActivityItem(marketActItem);
                                    }
                                }
                            },
                            viewUpdateMaster: (data) => {
                                // 消费信息同步到宴席主家字段上
                                this.$bus.$emit('viewUpdateMaster', data);
                            }
                        })
                    } else {
                        this.$nav.push('/pages/lj-consumers/account-old/account-item-edit-page.vue', {
                            data: accountItem,
                            marketActivityId: this.parentId,
                            userInfo: this.userInfo,
                            pageFrom: 'Activity',
                            callback: (data) => {
                                this.consumerOption.methods.reload();
                                this.$emit('update:updateAnalysisData', true)
                            },
                        })
                    }
                }
            },
            /**
             * 查看更多消费者
             * <AUTHOR>
             * @date 2020-08-07
             * */
            gotoConsumerList() {
                this.$nav.push('/pages/lj-market-activity/market-activity/consumer-list-page', {
                    parentId: this.parentId,
                    parentData: this.parentData,
                    operationFlag: this.operationFlag,
                    editFlag: this.editFlag,
                    pageSource: this.pageSource,
                    callback: async () => {
                        await this.queryConsumerList();
                    }
                })
            },
            /**
             * 删除消费者
             * <AUTHOR>
             * @date 2020-08-07
             * */
            async handleConsumerDelete(item, index) {
                const flag = await this.operationalControl();
                if(!flag){
                    this.$message.warn('活动已被更新，当前活动状态和审批状态不允许删除消费者，请返回列表重新查询活动数据。');
                    return ;
                }
                await this.$http.post(this.$env.appURL + '/marketactivity/link/interCust/deleteMactById', item);
                this.$emit('update:updateAnalysisData', true)
                await this.queryConsumerList();
            },
            /**
             * 查询消费者列表
             * <AUTHOR>
             * @date 2020-08-14
             * */
            async queryConsumerList() {
                const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/interCust/queryByAppCustPage', {
                    order: 'desc',
                    sort: 'matchStatus',
                    mcActId: this.parentId,
                    rows: 3,
                    totalFlag: true,
                    filtersRaw: [
                        {id: 'empFlag', property: 'empFlag', value: 'N', operator: '='},
                        {id: 'terminalFlag', property: 'terminalFlag', value: 'N', operator: '='}
                    ]
                });
                this.consumerList = data.rows;
                console.log('consumerList',this.consumerList)
                this.consumerTotal = data.total;
            },
            /**
             * 更新活动人群匹配状态
             * <AUTHOR>
             * @date 2021-01-27
             * */
            async updateInterCust(item,event){
                // 活动状态 MC_STATUS =已发布、进行中、执行结束、已实发，
                // 活动审批状态 APRO_STATUS =申请审批通过、反馈驳回、反馈撤回、反馈审批通过
                // lzljqw-004-283：活动消费者状态不允许调整
              /*  if((this.parentData.status === 'Published' || this.parentData.status === 'Processing' || this.parentData.status === 'Closed' || this.parentData.status === 'ActualAmount')
                    && (this.parentData.aproStatus === 'Approve' || this.parentData.aproStatus === 'Refeedback' || this.parentData.aproStatus === 'RefeedWithdraw' || this.parentData.aproStatus === 'FeedbackApro')){
                    event.stopPropagation();
                    this.currentConsumerData = item;
                    this.$refs.custMatchTypeDialog.show();
                }*/
            },
            /**
             * 选择活动人群匹配状态
             * <AUTHOR>
             * @date 2021-01-27
             * */
            async pikeMatchStatus(item){
                this.currentConsumerData.matchStatus = item.val;
                await this.$http.post(this.$env.appURL + '/marketactivity/link/interCust/update', this.currentConsumerData);
                this.$refs.custMatchTypeDialog.hide();
                await this.queryConsumerList();
            },
            /**
             * 查看消费者详情
             * @auther songyanrong
             * @date 2020-09-03
             * */
           async gotoItem(item) {
                let pathFlag = await this.$utils.getCfgProperty('lj_consumers_flag')
                if(pathFlag==='Y'){
                    this.$refs.custMatchTypeDialog.hide();
                    this.$nav.push('/pages/lj-consumers/account/account-item-page', {
                        data: {id: item.accntChannelId}
                    })
                }else{
                this.$refs.custMatchTypeDialog.hide();
                this.$nav.push('/pages/lj-consumers/account-old/account-item-page', {
                    data: {id: item.accntChannelId}
                })
                }
            },
        }
    }
</script>

<style lang="scss">
    .market-activity-consumers {
        /*deep*/
        .link-swipe-action {
            position: relative;
            overflow-x: hidden;
            width: 100%;
        }
        clear: both;

        .consumers-view {
            background: white;

            .item-header {
                height: 88px;
                width: 100%;
                padding-left: 32px;
                font-size: 28px;
                line-height: 88px;
                color: #262626;
                letter-spacing: 0;
                border-bottom: 2px solid #F2F2F2;
                .item-info{
                    width: 30%;
                }
            }

            .more {
                font-family: PingFangSC-Regular;
                width: 100%;
                text-align: center;
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
                line-height: 76px;
                background-color: #f2f2f2;
            }
        }

        .consumers-v {
            clear: both;
            margin-top: 24px;

            .menu-stair {
                width: 100%;
                @include flex-start-center;
                margin-left: 24px;
                padding-top: 36px;

                .line {
                    .line-top {
                        width: 8px;
                        height: 16px;
                        background: #3FE0E2;
                    }

                    .line-bottom {
                        width: 8px;
                        height: 16px;
                        background: #2F69F8;
                    }
                }

                .stair-title {
                    width: 30%;
                    margin-left: 16px;
                    font-family: PingFangSC-Semibold, serif;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 1px;
                    line-height: 32px;
                }

                .edit {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #2F69F8;
                    letter-spacing: 0;
                    line-height: 28px;
                    text-align: right;
                    width: 58%;
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    .item-info{
                        width: 20%;
                    }
                }
            }

            .more {
                font-family: PingFangSC-Regular;
                width: 100%;
                text-align: center;
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
                line-height: 76px;
                background-color: #f2f2f2;
            }
        }

        .consumers-item-wrap {
            position: relative;
            width: 100%;
            border-bottom: 1px solid #f2f2f2;
            overflow: hidden;
            padding: 40px 28px 28px 28px;

            .consumers-item {
                width: 100%;

                .acct-name {
                    width: 100%;
                    font-size: 32px;
                    color: #212223;
                    line-height: 48px;
                    font-weight: 600;
                    margin-bottom: 16px;
                }

                .top-label {
                    background: #2F69F8;
                    color: white;
                    transform: skew(30deg, 0);
                    display: flex;
                    border-bottom-left-radius: 14px;
                    position: absolute;
                    right: -10px;
                    top: 0;

                    .label-text {
                        font-size: 24px;
                        transform: skew(-30deg, 0);
                        padding: 8px 32px;
                    }
                }

                .status-view {
                    position: absolute;
                    right: 16px;
                    width: 120px;
                    transform: skewX(-10deg);
                    border-radius: 4px;
                    background: #2F69F8;
                    box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                    height: 36px;

                    .status {
                        font-size: 22px;
                        color: #FFFFFF;
                        letter-spacing: 2px;
                        text-align: center;
                        line-height: 32px;
                    }
                }

                .tags-wrap {
                    display: flex;
                    margin-bottom: 16px;

                    .tag-item {
                        padding: 0 10px;
                        font-size: 22px;
                        font-weight: 400;
                        color: #3F66EF;
                        line-height: 36px;
                        background: #F0F5FF;
                        border-radius: 4px;
                        text-align: center;
                        margin-right: 16px;
                    }

                    .red {
                        color: #FF461E;
                        background: #FFF1EB;
                    }
                }

                .info-item {
                    width: 100%;
                    font-size: 28px;
                    display: flex;
                    margin-bottom: 8px;
                    line-height: 44px;
                    position: relative;

                    .info-label {
                        color: #999999;
                        width: 132px;
                    }

                    .info-value {
                        color: #333333;
                    }

                    .phone {
                        color: #317DF7;
                    }
                }
            }
        }
    }
</style>
