<!--
@created<PERSON>y  yangying
@date  2023/06/02
@description 市场活动-活动项
-->
<template>
    <view class="market-activity-item">
        <view class="media-list">
            <view class="media-top">
                <view class="num-view" @longPress="wx.setClipboardData({data: data.activityNum})">
                    <view class="num">{{data.activityNum}}</view>
                </view>
                <view class="icon-bar" v-if="source === 'perform'">
                    <view  style="color: #EA3232; flex: 1">
                        <view v-if="data.executeStatus === 'Overdue'">
                            <link-icon class="icon" icon="icon-chaoqi"/><text>超期</text>
                        </view>
                    </view>
                    <view style="color: #EC974A;flex: 1">
                        <view v-if="data.isVirtualAct === 'virtualAct'">
                            <link-icon class="icon" icon="icon-xuni"/><text>虚拟</text>
                        </view>
                    </view>
                </view>
                <status-button :label="data.status | lov('MC_STATUS')"></status-button>
            </view>
        </view>
        <view class="content-middle">
            <view class="name">{{data.activityName.length > 20 ? data.activityName.substring(0,20) + '...' : data.activityName}}</view>
        </view>
        <view class="content-middle-line">
            <view class="data">
                <view class="title">审批状态</view>
                <view class="val" :class="[source !== 'feedback' ? data.aproStatus : '']">{{data.aproStatus | lov('APRO_STATUS')}}</view>
            </view>
            <view class="sum">
                <view class="title">稽核状态</view>
                <view class="val">{{data.whetherAudit | lov('WHETHER_AUDIT')}}</view>
            </view>
        </view>
        <view class="content-middle-line">
            <view class="data">
                <view class="title">活动时间</view>
                <view class="val">{{data.startTime | date('YYYY-MM-DD')}}</view>
            </view>
            <view class="sum">
                <view class="title">提报人</view>
                <view class="val">{{data.executor}}</view>
            </view>
        </view>
        <view class="content-middle-line" v-if="priceShowFlag">
            <!--当活动审批状态APRO_STATUS不为（已反馈、反馈驳回、反馈撤回）时
            且当活动状态为新建、已发布、进行中、已结束时，取活动费用申请表的数据，活动金额=现金申请金额+产品申请金额；-->
            <view class="sum-2" v-if="!['Feedback', 'Refeedback', 'RefeedWithdraw'].includes(data.aproStatus)
                  && ['New', 'Published', 'Processing', 'Closed'].includes(data.status)">
                <view class="title">申请金额</view>
                <view class="val">{{((data.cashApplyAmount + 0) + (data.prodApplyAmount + 0)) | cny}}</view>
            </view>
            <!--当活动审批状态APRO_STATUS为已反馈、反馈驳回、反馈撤回时，活动状态不为新建、已发布。取活动费用实际表的数据，活动金额=现金实际填报金额+产品实际填报金额；-->
            <view class="sum-2" v-if="['Feedback', 'Refeedback', 'RefeedWithdraw'].includes(data.aproStatus)
                  && !['New', 'Published'].includes(data.status)">
                <view class="title">实际填报金额</view>
                <view class="val">{{((data.cashFillAmount + 0) + (data.prodFillAmount + 0)) | cny}}</view>
            </view>
            <!--当活动状态为已实发、材料补充、已作废时，取活动费用实际表的数据，活动金额=现金实发金额+产品实发金额。-->
            <view class="sum-2" v-if="['ActualAmount', 'MaterialSup', 'Inactive'].includes(data.status)">
                <view class="title">实发金额</view>
                <view class="val">{{((data.cashRealAmount + 0) + (data.prodRealAmount + 0)) | cny}}</view>
            </view>
        </view>
    </view>
</template>

<script>
import StatusButton from '../../../lzlj/components/status-button';
export default {
    name: 'market-activity-item',
    props: {
        data: Object,
        priceShowFlag: Boolean,
        source: String // perform：执行案    feedback：执行反馈    marketAct：市场活动
    },
    components: {StatusButton},
}
</script>

<style lang="scss">
@import "../../../../styles/list-card";

.market-activity-item {

    .media-list {
        @include media-list;

        .media-top {
            width: 100%;
            @include flex-start-center;
            @include space-between;
            height: 80px;
            line-height: 80px;

            .left-content {
                font-family: PingFangSC-Semibold;
                font-size: 32px;
                color: #262626;
                letter-spacing: 0;
                line-height: 32px;
                padding-top: 20px;

            }

            .right-content {
                font-family: PingFangSC-Semibold;
                font-size: 32px;
                color: #FF5A5A;
                letter-spacing: 0;
                text-align: right;
                line-height: 32px;
                padding-top: 20px;
            }

            .num-view {
                background: #A6B4C7;
                border-radius: 8px;
                line-height: 50px;

                .num {
                    font-size: 28px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    line-height: 40px;
                    padding: 2px 8px;
                }
            }

            .icon-bar{
                display:flex;
                width: 25%;
                font-size: 24px;
            }

            .status-view {
                width: 120px;
                transform: skewX(-10deg);
                border-radius: 4px;
                background: #2F69F8;
                box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                height: 36px;

                .status {
                    font-size: 20px;
                    color: #FFFFFF;
                    letter-spacing: 2px;
                    text-align: center;
                    line-height: 36px;
                }
            }
        }
    }

    .content-middle {
        width: 100%;
        @include flex-start-center;
        @include space-between;
        height: 80px;
        line-height: 80px;

        .content {
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #000000;
            letter-spacing: 0;
        }

        .name {
            font-family: PingFangSC-Semibold;
            font-size: 32px;
            color: #262626;
            letter-spacing: 0;
            line-height: 32px;
        }
    }

    .content-middle-line {
        width: 100%;

        .data {
            width: 58%;
            float: left;

            .title {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                line-height: 56px;
                width: 35%;
                float: left;

            }

            .val {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
                line-height: 56px;
            }

            .Submitted, .Feedback{
                color: #2F69F8;
            }

            .Approve, .FeedbackApro{
                color: #2EB3C2;
            }

            .Refused, .Refeedback{
                color: #FF5A5A;
            }

        }

        .sum {
            width: 42%;
            float: left;

            .title {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                line-height: 56px;
                float: left;
                width: 50%;
            }

            .val {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
                line-height: 56px;
                white-space:nowrap;
            }
        }

        .sum-2 {
            width: 58%;
            float: left;

            .title {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                line-height: 56px;
                float: left;
                margin-right: 24px;
            }

            .val {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
                line-height: 56px;
            }
        }
    }
}
</style>
