<template>
    <!--费用申请-->
    <view class="cost-new-v">
        <view class="menu-stair">
            <view class="line">
                <view class="line-top"></view>
                <view class="line-bottom"></view>
            </view>
            <view class="stair-title">费用申请</view>
            <view class="edit" @tap="edit"
                  v-if="editFlag && !$utils.isEmpty(pageSource) && pageSource !== 'activityAudit' && !isSalesAreaManager">编辑
            </view>
        </view>
        <view style="margin: 12px" v-if="!$utils.isEmpty(cashPayList) || !$utils.isEmpty(prodPayList)">
            <!--现金类-->
            <view v-for="cashItem in cashPayList">
                <cash-new :parentData="parentData" :bthShow="editFlag" :cashShow="cashShow" @updateCash="updateCashData"
                          :pageSource="pageSource" :cashItem="cashItem" :scene="scene" :radiusTopFlag="true"
                          @deleteCaseFeePay="deleteCaseFeePayFun"></cash-new>
            </view>
            <view style="width: 100%;height: 12px"></view>
            <!--产品类-->
            <view v-for="prodItem in prodPayList">
                <prod-new :parentData="parentData" :bthShow="editFlag" :prodShow="prodShow" @deleteProd="deleteProdFun"
                          @updateProd="updateProdData" :prodItem="prodItem" @deleteProdFeePay="deleteProdFeePayFun"
                          :scene="scene" :pageSource="pageSource" :radiusTopFlag="true"
                ></prod-new>
            </view>
            <view style="width: 100%;height: 12px"></view>
        </view>
    </view>
</template>

<script>
    import CashNew from "./cash-new";
    import ProdNew from "./prod-new";
    import Taro from "@tarojs/taro";
    import TastingWineScanCodeRecord from "./tasting-wine-scan-code-record"

    export default {
        name: "cost-new",
        components: {ProdNew, CashNew ,TastingWineScanCodeRecord},
        props: {
            //父对象-为活动对象
            parentData: {
                type: Object,
                default: function () {
                    return {};
                }
            },
            //场景-实际 actual 、申请 apply、控制保存编辑产品信息时调用的接口
            scene: {
                type: String
            },
            //页面来源 -
            // 1、执行反馈环节 executiveFeedback
            // 2、other 活动的其他信息(ps:这是一个页面)
            // 3、preview 活动预览界面而来
            // 4、view 活动查看界面查看而来
            // 5、审批或小程序消息进去 为空
            // 6、activityAudit 活动稽核
            pageSource: {
                type: String,
                default: ''
            }
        },
        computed: {
            prodPayList() {//产品支付类数据
                this.costListData.prodPayList = this.prodAndCostList.Product;
                return this.prodAndCostList.Product
            },
            cashPayList() {//现金支付类数据
                this.costListData.cashPayList = this.prodAndCostList.Money;
                return this.prodAndCostList.Money
            },
            isSalesAreaManager: function () {
                return ['CityManager', 'SalesRegionManager', 'SalesAreaManager',].includes(Taro.getStorageSync('token').result.positionType)
            }
        },
        data() {
            const prodAndCostList = {
                Money: [],//现金支付类数据
                Product: [],//产品支付类数据
            };
            const costListData = {
                prodPayList: [],
                cashPayList: [],
            };
            let editFlag = false;//是否可以编辑基础信息
            const cashShow = true;//现金内容是否展示
            const prodShow = true;//产品内容是否展示
            //活动状态 MC_STATUS : 新建
            //审批状态 APRO_STATUS : 未提交、已拒绝 的活动能够编辑；
            if (this.parentData.status === 'New'
                && (this.parentData.aproStatus === 'New' || this.parentData.aproStatus === 'Refused')) {
                editFlag = true;
            }
            return {
                prodAndCostList,
                editFlag,
                cashShow,
                prodShow,
                costListData,
                showRecordList: true  //是否展示扫码记录
            }
        },
        async created() {
            await this.queryActivityProdAndCostList();
            this.hasActualCost()
        },
        mounted() {
            this.$bus.$on('initActivityProdAndCostList', async () => {
                await this.queryActivityProdAndCostList();
            });
            this.$bus.$on('ProductRrefresh', (data) => {
                this.$set(this.prodAndCostList, 'Product', data);
            });
            this.$bus.$on('ProductDataListRrefreshs', (feePayType, costId, dataList) => {
                this.prodAndCostList.Product.forEach((item) => {
                    if (item.feePayType === feePayType && item.costId === costId) {
                        this.$set(item, 'dataList', dataList);
                    }
                })
            });
            this.$bus.$on('MoneyRrefresh', (data) => {
                this.$set(this.prodAndCostList, 'Money', data);
            });
            this.$bus.$on('MoneyDataListRrefresh', (feePayType, costId, dataList) => {
                this.prodAndCostList.Money.forEach((item) => {
                    if (item.feePayType === feePayType && item.costId === costId) {
                        this.$set(item, 'dataList', dataList);
                    }
                })
            });
        },
        methods: {
            async edit() {
                //2021-08-03考虑多人操作的场景
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.parentData.id
                });
                if (data.result.status === 'New'
                    && (data.result.aproStatus === 'New' || data.result.aproStatus === 'Refused')) {
                    this.editFlag = true;
                } else {
                    this.$message.warn('活动已被更新，当前活动状态和审批状态不允许编辑费用申请，请返回列表重新查询活动数据。');
                    return ;
                }
                this.$nav.push('/pages/lj-market-activity/market-activity/market-activity-cost-list-new-page', {
                    costListData: this.costListData,
                    data: this.parentData,
                    activityId: this.parentData.id,
                    exeCaseId: '',
                    pageForm: 'marketActivity',
                    operant: 'UPDATE',
                    scene: this.scene,
                    pageSource: this.pageSource,
                    /**
                     * 控制是否可以操作 cash-new 和 prod-new组件的删除、新建、以及删除兑付方式的操作
                     * 只允许在编辑界面删除兑付方式、删除明细 、新增明细。其他界面可以编辑小计 和 编辑某个产品信息
                     * */
                    operateFlag: true,
                    callback: async () => {
                        await this.queryActivityProdAndCostList();
                    }
                })
                ;
            },
            /**
             * 查询活动下费用信息
             * 1、申请类
             * */
            async queryActivityProdAndCostList() {
                const data = await this.$http.post('action/link/actMaterial/queryAndGroupData', {
                    filtersRaw: [{"id": "actId", "property": "actId", "value": this.parentData.id, "operator": "="}]
                });
                if (!this.$utils.isEmpty(data.rows)) {
                    this.prodAndCostList = {...data.rows};
                    if (!this.$utils.isEmpty(this.prodAndCostList.Money)) {
                        this.cashShow = true;
                    }
                    if (!this.$utils.isEmpty(this.prodAndCostList.Product)) {
                        this.prodShow = true;
                    }
                } else {
                    this.prodAndCostList.Money = [];
                    this.prodAndCostList.Product = [];
                }
            },
            /**
             * 更新现金数据
             * <AUTHOR>
             * @date 2020-08-26
             * */
            async updateCashData(feePayType, costId, editDataList) {
                this.cashPayList.forEach(i => {
                    if (i.feePayType === feePayType && i.costId === costId) {
                        let tempList = [];
                        tempList = this.$utils.deepcopy(i.dataList);
                        for (let i = 0; i < editDataList.length; i++) {
                            const item = editDataList[i];
                            const cashExit = tempList.filter((item1) => item1.prodName === item.prodName);
                            if (this.$utils.isEmpty(cashExit)) {
                                tempList.push(item);
                            }
                        }
                        this.$set(i, 'dataList', tempList)
                    }
                });
                this.$bus.$emit("CashRrefresh", this.cashPayList)
            },
            /**
             * 更新产品数据
             * <AUTHOR>
             * @date 2020-08-26
             * */
            updateProdData(feePayType, costId, editDataList) {
                this.prodPayList.forEach(i => {
                    if (i.feePayType === feePayType && i.costId === costId) {
                        let tempList = [];
                        tempList = this.$utils.deepcopy(i.dataList);
                        for (let i = 0; i < editDataList.length; i++) {
                            const item = editDataList[i];
                            const cashExit = tempList.filter((item1) => item1.prodId === item.prodId)[0];
                            if (this.$utils.isEmpty(cashExit)) {
                                tempList.push(item);
                            }else{
                                //只有费用申请时使用该组件
                                cashExit.qty++;
                            }
                        }
                        this.$set(i, 'dataList', tempList);
                    }
                });
            },
            /**
             * 删除某个产品
             * <AUTHOR>
             * @date 2020-11-06
             * */
            deleteProdFun(feePayType, dataList) {
                for (let i = 0; i < this.prodAndCostList.Product.length; i++) {
                    if (this.prodAndCostList.Product[i].feePayType === feePayType) {
                        this.$set(this.prodAndCostList.Product[i], 'dataList', dataList);
                    }
                }
                this.$bus.$emit("ProductRrefresh", this.prodAndCostList.Product)
            },
            /*
            * 删除申请费用的兑付方式-现金
            * @auther songyanrong
            * @date 2020-10-16
            * */
            async deleteCaseFeePayFun(feePayCode, costId) {
                const caseInfoList = this.cashPayList.filter((item) => item.feePayCode === feePayCode && item.costId === costId);
                //是否有已经保存到数据库的费用信息-存在
                let exists = [];
                if (!this.$utils.isEmpty(caseInfoList)) {
                    if (!this.$utils.isEmpty(caseInfoList[0].dataList)) {
                        exists = caseInfoList[0].dataList.filter((item) => !this.$utils.isEmpty(item.id));
                    }
                    if (!this.$utils.isEmpty(caseInfoList[0].dataList) && !this.$utils.isEmpty(exists)) {
                        const del = {
                            actId: this.parentData.id,
                            feePayCode: feePayCode,
                            costId:costId
                        };
                        await this.$http.post('action/link/actMaterial/deleteByFeePayCode', del);
                    }
                }
                if (!this.$utils.isEmpty(costId)) {
                    this.prodAndCostList.Money = this.prodAndCostList.Money.filter((item) => item.costId !== costId);
                } else {
                    this.prodAndCostList.Money = this.prodAndCostList.Money.filter((item) => item.feePayCode !== feePayCode);
                }
                this.$bus.$emit("MoneyRrefresh", this.prodAndCostList.Money);
            },
            /*
            * 删除申请费用的兑付方式-产品
            * @auther songyanrong
            * @date 2020-10-16
            * */
            async deleteProdFeePayFun(feePayCode, costId) {
                const prodInfoList = this.prodPayList.filter((item) => item.feePayCode === feePayCode && item.costId === costId);
                //是否存在已经保存到数据库的产品信息-存在
                let exists = [];
                if (!this.$utils.isEmpty(prodInfoList)) {
                    if (!this.$utils.isEmpty(prodInfoList[0].dataList)) {
                        exists = prodInfoList[0].dataList.filter((item) => !this.$utils.isEmpty(item.id));
                    }
                    if (!this.$utils.isEmpty(prodInfoList[0].dataList) && !this.$utils.isEmpty(exists)) {
                        const del = {
                            actId: this.parentData.id,
                            feePayCode: feePayCode,
                            costId: costId,
                        };
                        await this.$http.post('action/link/actMaterial/deleteByFeePayCode', del);
                        this.$bus.$emit("initCodeScanRecordList");//重新查询扫码记录
                    }
                }
                if (!this.$utils.isEmpty(costId)) {
                    this.prodAndCostList.Product = this.prodAndCostList.Product.filter((item) => item.costId !== costId);
                } else {
                    this.prodAndCostList.Product = this.prodAndCostList.Product.filter((item) => item.feePayCode !== feePayCode);
                }
                this.$bus.$emit("ProductRrefresh", this.prodAndCostList.Product);
            },
            async hasActualCost() {
                const data = await this.$http.post('action/link/actualFee/queryAndGroupData/detail', {
                    filtersRaw: [{"id": "actId", "property": "actId", "value": this.parentData.id, "operator": "="}]
                });
                this.showRecordList = this.$utils.isEmpty(data.rows)
                console.log(this.showRecordList)
            }
        }
    }
</script>

<style lang="scss">
    .cost-new-v {
        clear: both;

        .menu-stair {
            width: 100%;
            margin-left: 24px;
            padding-top: 24px;
            @include flex-start-center;

            .line {
                .line-top {
                    width: 8px;
                    height: 16px;
                    background: #3FE0E2;
                }

                .line-bottom {
                    width: 8px;
                    height: 16px;
                    background: #2F69F8;
                }
            }

            .stair-title {
                width: 30%;
                margin-left: 16px;
                font-family: PingFangSC-Semibold, serif;
                font-size: 32px;
                color: #262626;
                letter-spacing: 1px;
                line-height: 32px;
            }

            .edit {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
                line-height: 28px;
                text-align: right;
                width: 58%;
            }
        }
    }
</style>

