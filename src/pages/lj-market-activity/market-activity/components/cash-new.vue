<template>
    <view class="market-activity-cash-new">
        <view class="cash-new" :style="radiusTopFlag?'border-radius:8px':'none'" v-if="cashShow">
            <view class="item-header">
                <view v-if="operateFlag">
<!--                        控制是否可以操作 cash-new 和 prod-new组件的删除、新建、以及删除兑付方式的操作.可以操作时2行展示，兑付方式编码在第二行，新建和删除的按钮距离放大-->
                    <view style="width: 100%;height: 40px">
                        <view style="height: 44px;font-family: PingFangSC-Semibold;float: left;width: 38%;display: flex;align-items: center;">
                            <view style="white-space: pre-wrap;">{{feePayType}}</view>
                        </view>
                        <view style="float: right;text-align: center;padding-right: 24px;color: #2F69F8;font-size: 14px;line-height: 44px;width: 40px"
                              class="iconfont icon-plus"
                              @tap="addCash"
                              v-if="(otherLinkBtnCtr || executiveFeedbackLinkBtnCtrl) && operateFlag">
                        </view>
                        <view style="float: right;text-align: center;padding-right: 24px;color: #2F69F8;font-size: 14px;line-height: 44px;width: 40px"
                              class="iconfont icon-shanchu" v-if="deleteBtn && operateFlag" @tap="deleteByFeePayCode"></view>
                    </view>
                    <view style="width: 100%;height: 40px;float: left">
                        <view class="iconfont icon-style icon-guanlian" style="line-height: 44px;"
                              v-show="executiveFeedbackLinkBtnCtrl && pageSource !== 'activityAudit' && !$utils.isEmpty(pageSource)
                      && messageScene !=='costApprovalMsg' && associatedExecutionCostsFlag"
                              @tap="gotoAssociatedExecutionCosts">关联执行案明细
                        </view>
                        <view v-if="!associatedExecutionCostsFlag"
                              style="float: left;color: #2F69F8;font-size: 12px;line-height: 44px;">{{costCode}}
                        </view>
                    </view>
                </view>
                <view v-else>
<!--                    不允许操作时兑付方式类型名称和兑付方式编码一排显示-->
                    <view style="height: 44px;font-family: PingFangSC-Semibold;float: left;width: 38%;display: flex;align-items: center;">
                        <view style="white-space: pre-wrap;">{{feePayType}}</view>
                    </view>
                    <view class="iconfont icon-style icon-guanlian" style="line-height: 44px;"
                          v-show="executiveFeedbackLinkBtnCtrl && pageSource !== 'activityAudit' && !$utils.isEmpty(pageSource)
                      && messageScene !=='costApprovalMsg' && associatedExecutionCostsFlag"
                          @tap="gotoAssociatedExecutionCosts">关联执行案明细
                    </view>
                    <view v-if="!associatedExecutionCostsFlag"
                          style="padding-left: 10px;float: left;color: #2F69F8;font-size: 12px;line-height: 44px;">{{costCode}}
                    </view>
                    <view style="float: right;text-align: right;padding-right: 24px;color: #2F69F8;font-size: 14px;line-height: 44px;"
                          class="iconfont icon-plus"
                          @tap="addCash"
                          v-if="(otherLinkBtnCtr || executiveFeedbackLinkBtnCtrl) && operateFlag">
                    </view>
                    <view style="float: right;text-align: right;padding-right: 24px;color: #2F69F8;font-size: 14px;line-height: 44px;"
                          class="iconfont icon-shanchu" v-if="deleteBtn && operateFlag" @tap="deleteByFeePayCode"></view>
                </view>
            </view>
            <view style="border-bottom: 1px solid #f2f2f2;">
                <link-swipe-action v-for="(item,index) in dataList" :key="item.id" style="width: 100% !important;">
                    <link-swipe-option slot="option" v-if="!$utils.isEmpty(pageSource)  && scene !=='actual' && pageSource !== 'activityAudit' && deleteBtn && operateFlag"
                                       @tap="deleteRow(item,index)">
                        删除
                    </link-swipe-option>
                    <view class="cash-list">
                        <view class="cash-type">{{item.prodName | lov('CASH_FEE')}}</view>
                        <view class="cash-amount-v">
                            <view class="amount-title">小计</view>
                            <!--活动状态是new时、执行反馈环节可编辑-->
                            <view class="amount-v"
                                  v-if="(otherLinkBtnCtr || executiveFeedbackLinkBtnCtrl) && provinceOperateFlag">
                                <link-input readonly
                                            @tap="hideDecimalPoint(item)"
                                            v-model="item.actualTranPrice"
                                ></link-input>
                            </view>
                            <view class="amount" v-if="!((otherLinkBtnCtr || executiveFeedbackLinkBtnCtrl) && provinceOperateFlag)">
                                {{item.actualTranPrice |cny}}
                            </view>
                        </view>
                    </view>
                </link-swipe-action>
            </view>
            <view class="webstat" v-if="dataList.length>0">
                <view class="webstat-v">
                    <view class="webstat-title" :class="{ red: confirm && actualTotal == 0}" v-if="actualAmountShowFlag">实际总额&nbsp;</view>
                    <view class="webstat-val" v-if="actualAmountShowFlag">
                        <text :class="{ red: confirm && actualTotal == 0 }">{{actualTotal | cny}}</text>，
                    </view>
                    <view class="webstat-title" v-if="!actualAmountShowFlag">可用余额&nbsp;</view>
                    <view class="webstat-val" v-if="!actualAmountShowFlag">{{availableBalance|cny}}，</view>
                    <view class="webstat-title">申请总额&nbsp;</view>
                    <view class="webstat-val" style="padding-right: 12px">{{applyAmount|cny}}</view>
                </view>
                <view class="feeReim-v">
                    {{feeReim}}
                </view>
                <view class="feeReim-v" v-if="this.cashItem.costTypeName">
                    <view>
                        费用小类:  {{Boolean(this.cashItem.costTypeName)? this.cashItem.costTypeName : '' }}
                    </view>
                </view>
                <view class="feeReim-v" v-if="this.cashItem.dataList[0].comment">
                    <view>
                        备注:  {{Boolean(this.cashItem.dataList[0].comment)? this.cashItem.dataList[0].comment : '' }}
                    </view>
                </view>
            </view>
        </view>
    </view>

</template>

<script>
    import {ROW_STATUS} from "../../../../utils/constant";
    import Taro from "@tarojs/taro";
    export default {
        name: "cash-new",
        props: {
            cashShow: {
                type: Boolean,
                default: false
            },
            //上半部分
            radiusTopFlag: {
                type: Boolean,
                default: false
            },
            //四边-弃用
            radiusAllFlag: {
                type: Boolean,
                default: false
            },
            //下半部分-弃用
            radiusBottomFlag: {
                type: Boolean,
                default: false
            },
            //按钮是否显示-弃用
            bthShow: {
                type: Boolean,
                default: false
            },
            //父对象-活动对象
            parentData: {
                type: Object,
                default: function () {
                    return {};
                }
            },
            //页面来源 -
            // 1、执行反馈环节 executiveFeedback
            // 2、other 活动的其他信息(ps:这是一个页面)
            // 3、preview 活动预览界面而来
            // 4、view 活动查看界面查看而来
            // 5、审批或小程序消息进去 为空
            // 6、activityAudit 活动稽核
            pageSource: {
                type: String,
                default: 'other'
            },
            //现金对象
            cashItem: {
                type: Object
            },
            //场景 只有执行反馈环节传 'costApprovalMsg' 区分费用审批场景
            messageScene: {
                type: String,
            },
            //场景-实际费用 actual 、申请费用 apply 控制保存编辑时调用的接口
            scene: {
                type: String
            },
            /**
             * 控制是否可以操作 cash-new 和 prod-new组件的删除、新建、以及删除兑付方式的操作
             * 只允许在编辑界面删除兑付方式、删除明细 、新增明细。其他界面可以编辑小计 和 编辑某个产品信息
             * */
            operateFlag: {
                type: Boolean,
                default: false
            },
            /**
             * 控制是否可以点击现金类小计 和 产品类的编辑笔
             * 默认可以编辑，仅费用提交按钮的弹出的费用实际信息内容块不能编辑
             * */
            provinceOperateFlag: {
                type: Boolean,
                default: true
            },
            //费用信息 - 主要用于费用实际关联执行案明细判断时
            prodAndCostList: {
                type: Object,
                defalt: [],
            },
            // 是否是提交时确认弹框使用
            confirm: {
                type: Boolean,
                default: false
            }
        },
        data() {
            const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
            let activityItemActualFlag = false;//是否为活动详情而来 且是实际费用类型
            if (this.scene === 'actual' && this.pageSource === 'view') {
                activityItemActualFlag = true;
            }
            let actualAmountShowFlag = false;//默认实际总额不显示
            let executiveFeedbackLinkBtnCtrl = false;//执行反馈环节 所有按钮的控制
            let otherLinkBtnCtr = false;//非执行反馈、活动稽核环节、 所有按钮的控制
            /**
             * 1、活动状态 [已实发\已结束\材料补充]时 展示实际总额不展示可用余额。
             * 2、执行反馈环节展示实际总额,不展示可用余额。
             * 3、scene 实际费用 actual 展示实际总额,不展示可用余额。
             * */
            if ((this.pageSource === 'executiveFeedback' || activityItemActualFlag) || (
                this.parentData.status === 'ActualAmount'
                || this.parentData.status === 'MaterialSup') && this.scene === 'actual') {
                actualAmountShowFlag = true;
            }
            /*
            * 执行反馈环节 活动状态 MC_STATUS 为进行中、已结束 且 审批状态为 申请审批通过、反馈驳回
            * 2021-04-19 更新 活动状态=已发布的编辑权限
            * */
            if (this.pageSource === 'executiveFeedback' && (this.parentData.status === 'Processing' || this.parentData.status === 'Closed' || this.parentData.status === 'Published')
                && (this.parentData.aproStatus === 'Approve' || this.parentData.aproStatus === 'Refeedback')) {
                executiveFeedbackLinkBtnCtrl = true;
            }
            /*
            * 非执行反馈、活动稽核环节、预览 活动状态 MC_STATUS 新建 且 审批状态为 未提交、已拒绝
            * */
            if (this.pageSource !== 'executiveFeedback' && this.pageSource !== 'activityAudit'
                && this.messageScene !== 'costApprovalMsg' && this.parentData.status === 'New'
                && (this.parentData.aproStatus === 'New' || this.parentData.aproStatus === 'Refused')) {
                otherLinkBtnCtr = true;
            }
            const parentId = this.parentData.id;//父ID
            let deleteBtn = false;//删除按钮控制逻辑
            //活动状态 MC_STATUS ：新建
            //审批状态 APRO_STATUS ：未提交、已拒绝
            if (this.parentData.status === 'New' && (this.parentData.aproStatus === 'New' || this.parentData.aproStatus === 'Refused')) {
                deleteBtn = true;
            }
            const actualTranPriceBackup = "";
            return {
                userInfo,
                activityItemActualFlag,
                actualTranPriceBackup,
                deleteBtn,
                executiveFeedbackLinkBtnCtrl,
                otherLinkBtnCtr,
                parentId,
                actualAmountShowFlag,
                cashFeeOption: new this.AutoList(this, {
                    module: 'action/link/basic',
                    param: {
                        filtersRaw: [{
                            id: 'type',
                            property: 'type',
                            operator: '=',
                            value: 'CASH_FEE'
                        }, {
                            id: 'activeFlag',
                            property: 'activeFlag',
                            operator: '=',
                            value: 'Y'
                        }]
                    },
                    sortOptions: null,
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} arrow="false">
                                <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                                <view
                                    style="display: -webkit-box;display: -ms-flexbox;display: flex;padding: 12px 8px 12px 12px;width:100%">
                                    <view
                                        style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px;">
                                        {data.name}
                                    </view>
                                </view>
                            </item>
                        )
                    }
                }),
            }
        },
        computed: {
            dataList() {
                return this.cashItem.dataList || [];
            },
            feePayCode() {
                return this.cashItem.feePayCode;//费用兑付方式编码
            },
            feePayType() {
                return this.cashItem.feePayType;//费用兑付方式名称
            },
            costCode() {
                return this.cashItem.costCode;//执行案明细编码
            },
            applyAmount() {
                let countApplyAmount = 0;
                if ((this.pageSource === 'executiveFeedback' || this.parentData.status === 'ActualAmount' || this.parentData.status === 'MaterialSup')) {
                    countApplyAmount = this.cashItem.applyAmount;//申请总额
                } else {
                    this.cashItem.dataList.forEach(item => {
                        if(this.$utils.isEmpty(item.actualTranPrice)){
                            countApplyAmount += 0
                        }else{
                            countApplyAmount = this.$utils.numberAdd(item.actualTranPrice,countApplyAmount);
                        }
                    });
                }
                return countApplyAmount;
            },
            actualTotal() {
                let countActualAmount = 0;
                if ((this.pageSource === 'executiveFeedback' || this.parentData.status === 'ActualAmount' || this.parentData.status === 'MaterialSup')) {
                    this.cashItem.dataList.forEach(item => {
                        if(this.$utils.isEmpty(item.actualTranPrice)){
                            countActualAmount += 0;
                        }else{
                            countActualAmount = this.$utils.numberAdd(item.actualTranPrice,countActualAmount);
                        }
                    });
                } else {
                    countActualAmount = this.cashItem.actualTotal;//实际总额
                }
                return countActualAmount;
            },
            availableBalance() {
                return this.cashItem.availableBalance;//可用余额
            },
            costId() {
                return this.cashItem.costId;//费用明细ID
            },
            associatedExecutionCostsFlag() {//是否可以操作关联执行案明细取决于：dataList里的数据对象 costId 字段是否有值
                if (this.$utils.isEmpty(this.cashItem.costId)) {
                    if (this.$utils.isEmpty(this.cashItem.dataList)) {
                        return true;
                    } else if (!this.$utils.isEmpty(this.cashItem.dataList) && this.$utils.isEmpty(this.cashItem.dataList[0].costId)) {
                        return true;
                    }
                } else {
                    return false;
                }
            },
            feeReim() {
                return this.cashItem.feeReim;//费用垫付对象名称
            },
            //片区经理关闭市场活动提交权限
            isSalesAreaManager () {
                return ['CityManager', 'SalesRegionManager', 'SalesAreaManager',].includes(Taro.getStorageSync('token').result.positionType)
            }
        },
        methods: {
            async hideDecimalPoint(item) {
                if(this.isSalesAreaManager) {
                    return
                }
                //2021-08-03考虑多人操作的场景
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.parentId
                });
                //为空代表是执行案模块新建市场活动场景
                if(!this.$utils.isEmpty(data.result)){
                    this.parentData.id = data.result.id;

                    // 获取缓存中的活动版本号
                    if(!this.$utils.isEmpty(this.$dataService.getMarketActivityItem())){
                        this.parentData['rowVersion'] = this.$dataService.getMarketActivityItem()['rowVersion']
                    } else {
                        this.parentData['rowVersion'] = data.result['rowVersion'];
                    }
                    this.parentData.remark = data.result.remark;
                    /* 执行反馈环节 活动状态 MC_STATUS 为进行中、已结束 且 审批状态为 申请审批通过、反馈驳回
                * 2021-04-19 更新 活动状态=已发布的编辑权限
                    * */
                    if (this.pageSource === 'executiveFeedback' && (data.result.status === 'Processing' || data.result.status === 'Closed' || data.result.status === 'Published')
                        && (data.result.aproStatus === 'Approve' || data.result.aproStatus === 'Refeedback')) {
                        this.executiveFeedbackLinkBtnCtrl = true;
                    } else {
                        this.executiveFeedbackLinkBtnCtrl = false;
                    }
                    /*
                    * 非执行反馈、活动稽核环节、预览 活动状态 MC_STATUS 新建 且 审批状态为 未提交、已拒绝
                    * */
                    if (this.pageSource !== 'executiveFeedback' && this.pageSource !== 'activityAudit'
                        && this.messageScene !== 'costApprovalMsg' && data.result.status === 'New'
                        && (data.result.aproStatus === 'New' || data.result.aproStatus === 'Refused')) {
                        this.otherLinkBtnCtr = true;
                    } else {
                        this.otherLinkBtnCtr = false;
                    }
                    if (!(this.otherLinkBtnCtr || this.executiveFeedbackLinkBtnCtrl)) {
                        this.$message.warn('活动已被更新，当前活动状态和审批状态不允许更新小计，请返回列表重新查询活动数据。');
                        return ;
                    }
                }
                this.actualTranPriceBackup = item.actualTranPrice;
                item.actualTranPrice = await this.$numberKeyboard({initValue: 0, precision: 2});
                if(this.$utils.isEmpty(item.actualTranPrice)){
                    this.$message.warn('小计不能为空，请重新输入');
                    return false;
                }
                await this.openNumberKeyboard(item);
            },
            async openNumberKeyboard(item) {
                this.$set(item, 'actualTranPrice', item.actualTranPrice);
                if (!this.$utils.isEmpty(item.id)) {
                    const updateDate = {
                        id: item.id,
                        actualTranPrice: item.actualTranPrice,
                        row_status: ROW_STATUS.UPDATE,
                        updateFields: "id,actualTranPrice"
                    };

                    //考虑多人操作
                    let dealData = {
                        marketAct : {
                            id: this.parentData.id,
                            rowVersion: this.parentData.rowVersion,
                            remark: this.parentData.remark,
                            row_status: ROW_STATUS.UPDATE,
                            updateFields: 'id,rowVersion,remark'
                        }
                    };
                    const feeList = [];
                    feeList.push(updateDate);
                    if (this.scene === 'apply') {
                        dealData['actMaterialList'] = feeList;
                    } else if (this.scene === 'actual') {
                        dealData['actualFeeList'] = feeList;
                    }
                    const data = await this.$http.post('action/link/actIntegration/updateActIdAndCityAfterSelectedFee', dealData,{
                        autoHandleError: false,
                        handleFailed: (response) => {
                            if (!response.success) {
                                this.$set(item, 'actualTranPrice', this.actualTranPriceBackup);
                                this.$showError(response.result)
                            }
                        }
                    });
                    item = data.result;
                    this.$dataService.setMarketActivityItem(data.result);
                    this.$bus.$emit("MoneyDataListRrefresh", this.feePayType, this.costId, this.dataList);
                    this.$bus.$emit("costUpdateRefreshMarketActivityItem");
                }
            },
            /**
             * 添加现金
             * <AUTHOR>
             * @date 2020-08-20
             * */
            async addCash() {
                const list = await this.$object(this.cashFeeOption, {multiple: true});
                const dataList = list.map(
                    item => ({
                        id: "",
                        prodName: item.val,
                        actualTranPrice: 0,
                        costId: this.costId,
                        actId: this.parentId,
                        feePayCode: this.feePayCode,
                        feePayType: this.feePayType,
                        row_status: ROW_STATUS.NEW
                    })
                );
                this.$emit('updateCash', this.feePayType, this.costId, dataList); //通知父组件改变。
                this.$bus.$emit("costUpdateRefreshMarketActivityItem");
            },
            /**
             * 删除一行
             * <AUTHOR>
             * @date 2020-08-20
             * */
            async deleteRow(input, index) {
                //2021-08-03考虑多人操作的场景
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.parentId
                });
                //为空代表是新建市场活动场景
                if(!this.$utils.isEmpty(data.result)){
                    if (!(data.result.status === 'New' && (data.result.aproStatus === 'New' || data.result.aproStatus === 'Refused'))) {
                        this.$message.warn('活动已被更新，当前活动状态和审批状态不允许删除现金明细，请返回列表重新查询活动数据。');
                        return ;
                    }
                }
                const that = this;
                if (that.dataList.length === 1) {
                    this.$taro.showModal({
                        title: '提示',
                        content: '当前兑付方式只有一条费用明细，删除费用明细将删除整个兑付方式，是否确认删除？',
                        success: async (res) => {
                            if (res.confirm) {
                                this.$emit('deleteCaseFeePay', this.feePayCode, this.costId); //通知父组件改变。
                            } else if (res.cancel) {
                            }
                        }
                    });
                } else {
                    this.$taro.showModal({
                        title: '提示',
                        content: '是否要删除当前行信息',
                        success: async (res) => {
                            if (res.confirm) {
                                if (!that.$utils.isEmpty(input.id) && input.row_status !== ROW_STATUS.NEW) {
                                    await that.$http.post('action/link/actMaterial/deleteById', {id: input.id});
                                }
                                that.dataList.splice(index, 1);
                                this.$bus.$emit('MoneyDataListRrefresh', that.feePayType, that.costId, that.dataList); //通知父组件改变。
                                that.$message.success('费用删除成功');
                            } else if (res.cancel) {
                            }
                        }
                    });
                }
                this.$bus.$emit("costUpdateRefreshMarketActivityItem");
                this.$bus.$emit("initActivityProdAndCostList");
            },
            /**
             *  前往操作关联执行案明细
             *  <AUTHOR>
             *  @date 2020-09-14
             * */
            gotoAssociatedExecutionCosts() {
                this.$nav.push('/pages/lj-market-activity/market-activity/associated-execution-costs-page', {
                    feePayType: this.feePayType,
                    feePayCode: this.feePayCode,
                    dataList: this.dataList,
                    feePayMethodType: 'Money', //对付方式类型 ： 现金 类
                    parentData: this.parentData,/*活动信息*/
                    prodAndCostList: this.prodAndCostList,//费用信息
                    callback: (item) => {
                        this.$emit('updateMarketAct', item); //通知父组件更新活动信息。
                    }
                })
            },
            /*
            * 根据兑付方式编码删除申请费用
            * <AUTHOR>
            * @date 2020-10-16
            * */
            async deleteByFeePayCode() {
                //2021-08-03考虑多人操作的场景
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.parentId
                });
                //为空代表是执行案模块新建市场活动场景
                if(!this.$utils.isEmpty(data.result)){
                    if (!(data.result.status === 'New' && (data.result.aproStatus === 'New' || data.result.aproStatus === 'Refused'))) {
                        this.$message.warn('活动已被更新，当前活动状态和审批状态不允许删除兑付方式，请返回列表重新查询活动数据。');
                        return ;
                    }
                }
                this.$taro.showModal({
                    title: '提示',
                    content: '即将删除该支付方式下所有费用明细,请确认',
                    success: async (res) => {
                        if (res.confirm) {
                            this.$emit('deleteCaseFeePay', this.feePayCode, this.costId); //通知父组件改变。
                            this.$bus.$emit("costUpdateRefreshMarketActivityItem");
                        } else if (res.cancel) {
                        }
                    }
                })
            },
        }
    }
</script>

<style lang="scss">
    .market-activity-cash-new {
        display: flex;
        .cash-new {
            width: 100%;
            background: white;

            /*deep*/
            .link-input.link-input-text-align-right {
                text-align: center;
            }

            .item-header {
                height: 88px;
                width: 100%;
                padding-left: 24px;
                font-size: 28px;
                //line-height: 88px;
                color: #262626;
                letter-spacing: 0;
                border-bottom: 2px solid #F2F2F2;
                display: table;
                .icon-style {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #2F69F8;
                    letter-spacing: 0;
                    float: left;
                    padding-left: 18px;
                }
            }

            .link-swipe-action {
                overflow-y: hidden;
            }

            .cash-list {
                display: flex;
                padding: 0 24px;
                justify-content: space-between;

                .cash-type {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 80px;
                    width: 40%;
                    height: 80px;
                }

                .cash-amount-v {
                    width: 60%;
                    height: 80px;
                    line-height: 80px;
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;

                    .amount-title {
                        font-family: PingFangSC-Regular;
                        font-size: 24px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        text-align: right;
                        line-height: 24px;
                        margin-right: 8px;
                    }

                    .amount-v {
                        width: 160px;
                        border: 2px solid #E0E4EC;
                        border-radius: 8px;
                        height: 48px;

                        .link-input {
                            height: 48px;

                            .link-input-prefix-icon {
                                color: #262626 !important;
                                font-size: 20px;
                                margin-top: -2px;
                                margin-left: 8px;
                            }

                            input {
                                text-align: center;
                                /*padding: 0 !important;*/
                                /*margin-left: 0 !important;*/
                                /*text-align: left;*/
                            }
                        }
                    }

                    .amount {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 28px;
                    }
                }
            }

            .webstat {
                width: 100%;
                height: 92px;
                line-height: 92px;

                .webstat-v {
                    width: 100%;
                    height: 46rpx;
                    line-height: 46rpx;
                    margin-top: 10px;
                    padding-left: 24px;

                    .webstat-title {
                        font-family: PingFangSC-Regular;
                        font-size: 24px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        text-align: right;
                        line-height: 46px;
                        float: left;
                    }

                    .webstat-val {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #000000;
                        letter-spacing: 0;
                        text-align: right;
                        line-height: 46px;
                        float: left;
                    }

                    .red {
                        color: red;
                        font-weight: bold;
                    }
                }

                .feeReim-v {
                    font-family: PingFangSC-Regular;
                    width: 94%;
                    //text-align: right;
                    font-size: 24px;
                    color: #000000;
                    letter-spacing: 0;
                    float: left;
                    line-height: 46px;
                    //height: 46px;
                    padding-left: 24px;
                }
            }

        }
    }
</style>

