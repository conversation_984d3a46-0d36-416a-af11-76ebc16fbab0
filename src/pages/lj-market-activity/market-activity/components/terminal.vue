<template>
    <!--参与终端/经销商-->
    <view class="terminal-view" v-if="!haveMarginFlag">
        <view class="item-header">
            <view style="width: 50%;float: left">终端|经销商</view>
            <view style="float: left;text-align: right;width: 40%;padding-right: 12px;color: #2F69F8;"
                  v-if="!$utils.isEmpty(pageSource) && editFlag && pageSource !== 'activityAudit'"
                  @tap="addTerminal">添加
            </view>
        </view>
        <view>
            <list style="margin: 12px">
                <link-swipe-action v-for="(data,index) in terminalList.slice(0,3)" :key="`${data.id}_${terminalList.length}`">
                    <link-swipe-option slot="option" v-if="!$utils.isEmpty(pageSource) && editFlag && pageSource !== 'activityAudit' && operationFlag"
                                       @tap="handleTerminalDelete(data,index)">删除
                    </link-swipe-option>
                    <item :arrow="false">
                        <view class="terminal-list" slot="note">
                            <view class="list-cell">
                                <view class="media-list">
                                    <image class="media-list-logo" :src="data.storeUrl"></image>
                                    <view class="store-content">
                                        <view class="store-content-top" v-if="data.acctType">
                                            <!--【客户一级分类】为“终端Terminal”的时候显示acctName字段-->
                                            <view class="store-title" v-if="data.acctType === 'Terminal'">
                                                {{data.acctName}}
                                            </view>
                                            <!--【客户一级分类】为“分销商Distributor”、经销商 Dealer 时展示billTitle字段-->
                                            <view class="store-title"
                                                  v-if="data.acctType === 'Distributor' || data.acctType === 'Dealer'">
                                                {{data.billTitle}}
                                            </view>
                                            <!--已认证-->
                                            <view class="store-level" v-if="data.acctStage === 'ykf'">
                                                <image :src="$imageAssets.storeStatusVerifiedImage"></image>
                                            </view>
                                            <!--未认证-->
                                            <view class="store-level" v-if="data.acctStage === 'xk'">
                                                <image :src="$imageAssets.storeStatusUnverifiedImage"></image>
                                            </view>
                                            <!--已失效-->
                                            <view class="store-level" v-if="data.acctStage === 'ysx'">
                                                <image :src="$imageAssets.storeStatusInvalidationImage"></image>
                                            </view>
                                            <!--潜客-->
                                            <view class="store-level" v-if="data.acctStage === 'dkf'">
                                                <image :src="$imageAssets.storeStatusPotentialImage"></image>
                                            </view>
                                        </view>
                                        <view class="store-content-middle">
                                            <view class="store-type" v-if="data.acctCategory">{{data.acctCategory |
                                                lov('ACCNT_CATEGORY')}}
                                            </view>
                                            <view class="store-type"
                                                  v-if="data.acctLevel !== undefined || data.capacityLevel !== undefined">
                                                <view style="float: left;padding-right: 3px"
                                                      v-if="data.acctLevel !== undefined">
                                                    {{data.acctLevel |
                                                    lov('ACCT_LEVEL')}}
                                                </view>
                                                <view style="padding-left: 2px;padding-left: 10px;padding-right: 18px;"
                                                      v-if="data.capacityLevel !== undefined"> |
                                                    {{data.capacityLevel |
                                                    lov('CAPACITY_LEVEL')}}
                                                </view>
                                            </view>
                                        </view>
                                        <view class="store-content-representative">
                                            <view class="terminal-type" style="width: 30px">业代</view>
                                            <view class="terminal-name">{{data.trackerNames}}</view>
                                        </view>
                                        <view class="store-content-address">
                                            <view class="store-address">
                                                {{data.addrDetailAddr}}
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </item>
                </link-swipe-action>
            </list>
            <list>
                <view @tap="gotoTerminalListList()" v-if="terminalTotal>3" :arrow="false">
                    <view class="more">
                        查看全部({{terminalTotal}})>>
                    </view>
                </view>
            </list>
        </view>
    </view>
    <!--参与终端/经销商-->
    <view class="terminal-v" id="terminal-v" v-else-if="haveMarginFlag">
        <view class="menu-stair" style="margin-bottom: 12px">
            <view class="line">
                <view class="line-top"></view>
                <view class="line-bottom"></view>
            </view>
            <view class="stair-title">终端|经销商</view>
            <view class="edit" @tap="addTerminal"
                  v-if="!$utils.isEmpty(pageSource) && editFlag && pageSource !== 'activityAudit'">添加
            </view>
        </view>
        <list style="margin: 12px">
            <link-swipe-action v-for="(data,index) in terminalList.slice(0,3)" :key="`${data.id}_${terminalList.length}`" style="width: 100%">
                <link-swipe-option slot="option" v-if="!$utils.isEmpty(pageSource) && editFlag && pageSource !== 'activityAudit' && operationFlag"
                                   @tap="handleTerminalDelete(data,index)">删除
                </link-swipe-option>
                <item :arrow="false">
                    <view class="terminal-list" slot="note">
                        <view class="list-cell">
                            <view class="media-list">
                                <image class="media-list-logo" :src="data.storeUrl"></image>
                                <view class="store-content">
                                    <view class="store-content-top" v-if="data.acctType">
                                        <!--【客户一级分类】为“终端Terminal”的时候显示acctName字段-->
                                        <view class="store-title" v-if="data.acctType === 'Terminal'">
                                            {{data.acctName}}
                                        </view>
                                        <!--【客户一级分类】为“分销商Distributor”、经销商 Dealer 时展示billTitle字段-->
                                        <view class="store-title"
                                              v-if="data.acctType === 'Distributor' || data.acctType === 'Dealer'">
                                            {{data.billTitle}}
                                        </view>
                                        <!--已认证-->
                                        <view class="store-level" v-if="data.acctStage === 'ykf'">
                                            <image :src="$imageAssets.storeStatusVerifiedImage"></image>
                                        </view>
                                        <!--未认证-->
                                        <view class="store-level" v-if="data.acctStage === 'xk'">
                                            <image :src="$imageAssets.storeStatusUnverifiedImage"></image>
                                        </view>
                                        <!--已失效-->
                                        <view class="store-level" v-if="data.acctStage === 'ysx'">
                                            <image :src="$imageAssets.storeStatusInvalidationImage"></image>
                                        </view>
                                        <!--潜客-->
                                        <view class="store-level" v-if="data.acctStage === 'dkf'">
                                            <image :src="$imageAssets.storeStatusPotentialImage"></image>
                                        </view>
                                    </view>
                                    <view class="store-content-middle">
                                        <view class="store-type" v-if="data.acctCategory">{{data.acctCategory |
                                            lov('ACCNT_CATEGORY')}}
                                        </view>
                                        <view class="store-type"
                                              v-if="data.acctLevel !== undefined || data.capacityLevel !== undefined">
                                            <view style="float: left;padding-right: 3px"
                                                  v-if="data.acctLevel !== undefined">
                                                {{data.acctLevel |
                                                lov('ACCT_LEVEL')}}
                                            </view>
                                            <view style="padding-left: 2px;padding-left: 10px;padding-right: 18px;"
                                                  v-if="data.capacityLevel !== undefined"> |
                                                {{data.capacityLevel |
                                                lov('CAPACITY_LEVEL')}}
                                            </view>
                                        </view>
                                    </view>
                                    <view class="store-content-representative">
                                        <view class="terminal-type" style="width: 30px">业代</view>
                                        <view class="terminal-name">{{data.trackerNames}}</view>
                                    </view>
                                    <view class="store-content-address">
                                        <view class="store-address">
                                            {{data.addrDetailAddr}}
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </link-swipe-action>
        </list>
        <list>
            <view @tap="gotoTerminalListList()" v-if="terminalTotal>3" :arrow="false">
                <view class="more">
                    查看全部({{terminalTotal}})>>
                </view>
            </view>
        </list>
    </view>
</template>

<script>
    import {$imageAssets} from "link-taro-component";
    import {LovService} from "link-taro-component";
    import Taro from "@tarojs/taro";


    export default {
        name: "terminal",
        props: {
            //是否为有边距类型
            haveMarginFlag: {
                type: Boolean,
                default: false
            },
            //弃用-有边距时是否需要添加按钮
            addBtnFlag: {
                type: Boolean,
                default: false
            },
            //父ID
            parentId: {
                type: String,
                default: "",
            },
            //页面来源 -
            // 1、执行反馈环节 executiveFeedback
            // 2、other 活动的其他信息(ps:这是一个页面)
            // 3、preview 活动预览界面而来
            // 4、view 活动查看界面查看而来
            // 5、审批或小程序消息进去 为空
            // 6、activityAudit 活动稽核
            pageSource: {
                type: String,
                default: ''
            },
            //父对象-为活动对象
            parentData: {
                type: Object,
                default: {},
            },
            //是否可以操作删除数据。approvalId为空时可操作
            operationFlag:{
                type: Boolean,
                default: false
            }
        },
        async created() {
            this.userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
            await this.queryTerminalList()
        },
        data() {
            const terminalTotal = 0;
            let editFlag = false;//是否可以编辑基础信息
            //页面来源 executiveFeedback 非执行反馈、执行反馈：活动状态 MC_STATUS : 新建 审批状态 APRO_STATUS : 未提交、已拒绝 的活动能够编辑；
            //页面来源 executiveFeedback 执行反馈：活动状态 MC_STATUS : 进行中、已结束、已发布 审批状态 APRO_STATUS : 申请审批通过、反馈驳回、反馈撤回 的活动能够编辑；
            if ((this.pageSource !== 'executiveFeedback' && this.pageSource !== 'activityAudit' && this.parentData.status === 'New'
                && (this.parentData.aproStatus === 'New' || this.parentData.aproStatus === 'Refused')
            ) || (this.pageSource === 'executiveFeedback' && (this.parentData.status === 'Processing' || this.parentData.status === 'Closed' || this.parentData.status === 'Published')
                && (this.parentData.aproStatus === 'Approve' || this.parentData.aproStatus === 'Refeedback' || this.parentData.aproStatus === 'RefeedWithdraw')
            )) {
                editFlag = true;
            }
            return {
                userInfo: {},//用户信息
                editFlag,
                terminalTotal,
                terminalList: [],
                noSubAccountOrgCode : 'noMatchId',
                //参与终端/经销商选择
                // 客户大类【ACCT_TYPE】=【Dealer】&【Distributor】&【Terminal】，且当前登陆人的职位所在城市及下级组织的数据；
                TerminalOption: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: "export/link/accnt/queryUnSelectedAccntPage"
                    },
                    sortOptions: null,
                    filterOption: [
                        {label: '客户大类', field: 'acctType', type: 'lov', lov: 'ACCT_TYPE'},
                    ],
                    searchFields: ['acctName', 'billTitle'],
                    param: {
                        filterActivityId: this.parentId,
                        filtersRaw: [
                            {
                                id: 'acctStatus',
                                value: 'Y',
                                property: 'acctStatus'
                            }
                        ],
                        attr2: this.parentData.salesCityId,
                        attr3: 'Dealer,Terminal,Distributor',
                        companyId: this.parentData.companyId,
                    },
                    queryFields: 'id,acctType,storeUrl,acctStage,acctName,billTitle,created,acctCategory,acctLevel,capacityLevel,trackerNames,addrDetailAddr',
                    hooks: {
                        beforeLoad(option) {
                            delete option.param.order;
                            delete option.param.sort;
                        },
                        afterLoad(data) {
                            data.rows.forEach(async (item) => {
                                if (!this.$utils.isEmpty(item.storePicPreKey)) {
                                    let urlData = await this.$image.getSignedUrl(item.storePicPreKey);
                                    this.$set(item, 'storeUrl', urlData);
                                } else {
                                    this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
                                }
                            })
                        }
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} arrow="false">
                                <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                                <view
                                    style="display: -webkit-box;display: -ms-flexbox;display: flex;padding: 12px 8px 12px 12px;width:100%">
                                    <image
                                        style="box-shadow: 0 2px 15px 0 rgba(0, 44, 152, 0.22);border-radius: 8px;width: 64px;height: 64px;overflow: hidden;"
                                        src={data.storeUrl}></image>
                                    <view style="width: 80%;">
                                        <view
                        style = "display: -webkit-box;display: -ms-flexbox;display: flex;-webkit-box-pack: start;-ms-flex-pack: start;justify-content: flex-start;-webkit-box-align: center;-ms-flex-align: center;align-items: center;-webkit-box-pack: justify;-ms-flex-pack: justify;justify-content: space-between;margin-left: 12px;" >
                            <view
                        style = "font-family: PingFangSC-Semibold, serif;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 16px;" >
                            {data.acctType === 'Terminal' ? data.acctName : data.billTitle} </view>
                            <view
                        style = "margin-right: -3px;width: 60px;height: 22px;" >
                            {LovService.filter(data.acctStage, 'TERMINAL_STATUS')}
                            </view>
                            </view>
                            <view
                        style = "display: -webkit-box;display: -ms-flexbox;display: flex;-webkit-box-pack: start;-ms-flex-pack: start;justify-content: flex-start;-webkit-box-align: center;-ms-flex-align: center;align-items: center;margin-left: 12px;margin-top: 12px;" >
                            {data.acctCategory && <view
                                style = "border: 1px solid #2F69F8;border-radius: 8px;font-size: 10px;padding-left: 9px;padding-right: 9px;line-height: 20px;height: 20px;color: #2F69F8;margin-right: 5px;" > {
                                    LovService.filter(data.acctCategory, 'ACCNT_CATEGORY')
                                } </view>}
                        {
                            (data.acctLevel || data.capacityLevel) && <view
                            style = "border: 1px solid #2F69F8;border-radius: 8px;font-size: 10px;padding-left: 9px;padding-right: 9px;line-height: 20px;height: 20px;color: #2F69F8;margin-right: 5px;" >
                                {
                                    data.acctLevel && <view style = "float: left" >
                                    {LovService.filter(data.acctLevel, 'ACCT_LEVEL')}
                                    </view>}
                            {
                                data.capacityLevel && <view
                                style = "padding-left: 2px;float:left" > |
                                    {LovService.filter(data.capacityLevel, 'CAPACITY_LEVEL')}
                                    </view> }

                                    </view>}
                                    </view>
                                    <view
                                style = "display: -webkit-box;display: -ms-flexbox;display: flex;margin-left: 12px;margin-top: 12px;" >
                                    <view
                                style = "color: #8C8C8C;font-size:12px;width: 40px;" > 业代
                            </view>
                                <view
                                style = "font-family: PingFangSC-Regular,serif;font-size: 12px;color: #000000;letter-spacing: 0;padding-left: 4px;" > {
                                        data.trackerNames
                                    } </view>
                                    </view>
                                    <view
                                style = "margin-left: 12px;margin-top: 10px;font-family: PingFangSC-Regular,serif;font-size: 12px;color: #262626;letter-spacing: 0;line-height: 12px;" >
                                    <view> {data.addrDetailAddr} </view>
                                        </view>
                                    </view>
                                </view>
                            </item>
                        )
                    },
                }),
            }
        },
        methods: {
            //2021-08-04考虑多人操作的场景
            async operationalControl (){
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.parentData.id
                });
                if ((this.pageSource !== 'executiveFeedback' && this.pageSource !== 'activityAudit' && data.result.status === 'New'
                    && (data.result.aproStatus === 'New' || data.result.aproStatus === 'Refused')
                ) || (this.pageSource === 'executiveFeedback' && (data.result.status === 'Processing' || data.result.status === 'Closed' || data.result.status === 'Published')
                    && (data.result.aproStatus === 'Approve' || data.result.aproStatus === 'Refeedback' || data.result.aproStatus === 'RefeedWithdraw')
                )) {
                    return  true;
                } else {
                    return false;
                }
            },
            /**
             * 添加参与终端/经销商
             * <AUTHOR>
             * @date 2020-08-11
             * */
            async addTerminal() {
                const flag = await this.operationalControl();
                if(!flag){
                    this.$message.warn('活动已被更新，当前活动状态和审批状态不允许添加参与终端/经销商，请返回列表重新查询活动数据。');
                    return ;
                }
                await this.queryCfgProperty();
                if (this.userInfo.coreOrganizationTile
                    && this.userInfo.coreOrganizationTile.l3Code
                    && this.noSubAccountOrgCode.indexOf(this.userInfo.coreOrganizationTile.l3Code) > -1) {
                    // 若条件未加上，则添加条件
                    const filter = this.TerminalOption.option.param.filtersRaw.find(item => item.id === 'multiAcctMainFlag');
                    if (!filter) {
                        this.TerminalOption.option.param.filtersRaw.push({
                            id: 'multiAcctMainFlag',
                            property: 'multiAcctMainFlag',
                            value: 'Y'
                        })
                    }
                }
                const list = await this.$object(this.TerminalOption, {
                    pageTitle: '终端|经销商',
                    multiple: true,
                });
                const addTerList = list.map(item => ({
                        acctId: item.id,
                        actId: this.parentId
                    })
                );
                await this.$http.post('action/link/attendTerminal/batchInsert', addTerList);
                await this.queryTerminalList();
            },
            /**
             * 删除参与终端/经销商
             * <AUTHOR>
             * @date 2020-08-11
             * */
            async handleTerminalDelete(item, index) {
                const flag = await this.operationalControl();
                if(!flag){
                    this.$message.warn('活动已被更新，当前活动状态和审批状态不允许删除参与终端/经销商，请返回列表重新查询活动数据。');
                    return ;
                }
                await this.$http.post('action/link/attendTerminal/deleteById', item);
                await this.queryTerminalList();
            }
            ,
            /**
             * 查看更多参与终端/经销商
             * <AUTHOR>
             * @date 2020-08-11
             * */
            gotoTerminalListList() {
                this.$nav.push('/pages/lj-market-activity/market-activity/terminal-list-page', {
                    parentId: this.parentId,
                    operationFlag: this.operationFlag,
                    editFlag: this.editFlag,
                    pageSource: this.pageSource,
                    callback: async () => {
                        await this.queryTerminalList();
                    }
                })
            }
            ,
            /**
             * 查询参与终端/经销商列表
             * <AUTHOR>
             * @date 2020-08-13
             * */
            async queryTerminalList() {
                const data = await this.$http.post('action/link/attendTerminal/queryByExamplePage', {
                    totalFlag: true,
                    rows: 3,
                    sort: "created",
                    order: 'desc',
                    filtersRaw: [{id: 'actId', property: 'actId', value: this.parentId, operator: '='}]
                });
                data.rows.forEach(async (item) => {
                    if (!this.$utils.isEmpty(item.storePicPreKey)) {
                        let urlData = await this.$image.getSignedUrl(item.storePicPreKey);
                        this.$set(item, 'storeUrl', urlData);
                    } else {
                        this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
                    }
                });
                this.terminalList = data.rows || [];
                this.terminalTotal = data.total;
            },
            /**
             * 查询系统配置参数
             * 用于控制指定公司组织下用户查询时不查multiAcctMainFlag=N 的数据。
             * 受益客户、活动新建陈列协议客户名称、参与终端/经销商、宴席推荐终端四个字段（子对象）使用
             * */
            async queryCfgProperty(){
                const data = await this.$http.post('action/link/cfgProperty/queryByExamplePage', {
                    filtersRaw: [
                        {
                            id: 'key',
                            property: 'key',
                            value: 'MC_NO_SUB_ACCOUNT_ORG_CODE',
                        }
                    ]
                });
                if (data.success && data.rows && data.rows.length) {
                    this.noSubAccountOrgCode = data.rows[0].value
                } else {
                    this.noSubAccountOrgCode = 'noMatchId'
                }
            }
        }
    }
</script>

<style lang="scss">
    .terminal-view {
        background: white;
        margin-top: 12px;
        /*deep*/
        .link-swipe-action {
            position: relative;
            overflow-x: hidden;
            width: 100%;
        }
        .item-header {
            height: 88px;
            width: 100%;
            padding-left: 32px;
            font-size: 28px;
            line-height: 88px;
            color: #262626;
            letter-spacing: 0;
            border-bottom: 2px solid #F2F2F2;
        }

        .terminal-list {
            .list-cell {
                .media-list {
                    @include flex;
                    padding: 24px 16px 24px 24px;

                    .media-list-logo {
                        box-shadow: 0 4px 31px 0 rgba(0, 44, 152, 0.22);
                        border-radius: 16px;
                        width: 128px;
                        height: 128px;
                        overflow: hidden;
                    }

                    .store-content {
                        width: 80%;

                        .store-content-top {
                            @include flex-start-center;
                            @include space-between;
                            margin-left: 24px;

                            .store-title {
                                font-family: PingFangSC-Semibold, serif;
                                font-size: 32px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 32px;
                            }

                            .store-level {
                                margin-right: -3px;
                                width: 120px;
                                height: 44px;

                                image {
                                    width: 100%;
                                    height: 100%;
                                }
                            }
                        }

                        .store-content-middle {
                            @include flex-start-center;
                            margin-top: 20px;
                            margin-left: 24px;

                            .store-type {
                                white-space: nowrap;
                                border: 1px solid #2F69F8;
                                border-radius: 8px;
                                font-size: 20px;
                                padding-left: 18px;
                                padding-right: 18px;
                                line-height: 40px;
                                height: 40px;
                                color: #2F69F8;
                                margin-right: 10px;
                            }
                        }

                        .store-content-representative {
                            @include flex;
                            margin-left: 24px;
                            margin-top: 24px;

                            .terminal-type {
                                color: #8C8C8C;
                            }

                            .terminal-name {
                                font-family: PingFangSC-Regular, serif;
                                font-size: 24px;
                                color: #000000;
                                letter-spacing: 0;
                                padding-left: 8px;
                            }
                        }

                        .store-content-address {
                            margin-left: 24px;
                            margin-top: 20px;
                            font-family: PingFangSC-Regular, serif;
                            font-size: 24px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 24px;
                        }
                    }
                }
            }
        }

        .more {
            font-family: PingFangSC-Regular;
            width: 100%;
            text-align: center;
            font-size: 28px;
            color: #2F69F8;
            letter-spacing: 0;
            line-height: 76px;
            background-color: #f2f2f2;
        }
    }

    .terminal-v {
        margin-top: 24px;

        .menu-stair {
            width: 100%;
            @include flex-start-center;
            margin-left: 24px;
            padding-top: 36px;

            .line {
                .line-top {
                    width: 8px;
                    height: 16px;
                    background: #3FE0E2;
                }

                .line-bottom {
                    width: 8px;
                    height: 16px;
                    background: #2F69F8;
                }
            }

            .stair-title {
                width: 30%;
                margin-left: 16px;
                font-family: PingFangSC-Semibold, serif;
                font-size: 32px;
                color: #262626;
                letter-spacing: 1px;
                line-height: 32px;
            }

            .edit {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
                line-height: 28px;
                text-align: right;
                width: 58%;
            }
        }

        .terminal-list {
            .list-cell {
                .media-list {
                    @include flex;
                    padding: 24px 16px 24px 24px;

                    .media-list-logo {
                        box-shadow: 0 4px 31px 0 rgba(0, 44, 152, 0.22);
                        border-radius: 16px;
                        width: 128px;
                        height: 128px;
                        overflow: hidden;
                    }

                    .store-content {
                        width: 80%;

                        .store-content-top {
                            @include flex-start-center;
                            @include space-between;
                            margin-left: 24px;

                            .store-title {
                                font-family: PingFangSC-Semibold, serif;
                                font-size: 32px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 32px;
                            }

                            .store-level {
                                margin-right: -3px;
                                width: 120px;
                                height: 44px;

                                image {
                                    width: 100%;
                                    height: 100%;
                                }
                            }
                        }

                        .store-content-middle {
                            @include flex-start-center;
                            margin-top: 20px;
                            margin-left: 24px;

                            .store-type {
                                white-space: nowrap;
                                border: 1px solid #2F69F8;
                                border-radius: 8px;
                                font-size: 20px;
                                padding-left: 18px;
                                padding-right: 18px;
                                line-height: 40px;
                                height: 40px;
                                color: #2F69F8;
                                margin-right: 10px;
                            }
                        }

                        .store-content-representative {
                            @include flex;
                            margin-left: 24px;
                            margin-top: 24px;

                            .terminal-type {
                                color: #8C8C8C;
                            }

                            .terminal-name {
                                font-family: PingFangSC-Regular, serif;
                                font-size: 24px;
                                color: #000000;
                                letter-spacing: 0;
                                padding-left: 8px;
                            }
                        }

                        .store-content-address {
                            margin-left: 24px;
                            margin-top: 20px;
                            font-family: PingFangSC-Regular, serif;
                            font-size: 24px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 24px;
                        }
                    }
                }
            }
        }

        .more {
            font-family: PingFangSC-Regular;
            width: 100%;
            text-align: center;
            font-size: 28px;
            color: #2F69F8;
            letter-spacing: 0;
            line-height: 76px;
            background-color: #f2f2f2;
        }
    }
</style>
