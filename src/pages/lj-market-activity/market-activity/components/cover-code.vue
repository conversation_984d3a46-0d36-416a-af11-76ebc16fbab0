<template>
    <!--盖内码-->
    <view class="cover-code-view" :style="radiusBottomFlag?'border-radius:0px 0px 8px 8px':'none'">
        <view class="item-header" v-if="codeList.length>0">
            <view style="width: 50%;float: left;padding-left: 16px;">{{title}}</view>
            <view v-if="unfoldFlag">
                <view style="float: left;text-align: right;width: 38%;padding-right: 25px;color: #2F69F8;"
                      @tap="packUp">
                    收起
                    <link-icon icon="icon-up-circle" style="height: 44px"/>
                </view>
            </view>
            <view v-if="!unfoldFlag">
                <view style="float: left;text-align: right;width: 38%;padding-right: 25px;color: #2F69F8;"
                      @tap="unfold">
                    展开
                    <link-icon icon="icon-down-circle" style="height: 44px"/>
                </view>
            </view>
        </view>
        <list v-if="unfoldFlag">
            <link-swipe-action v-for="(item,index) in codeList" :key="`${item.id}_${codeList.length}`">
                <link-swipe-option slot="option" v-if="deleteFlag && operationFlag"
                                   @tap="handleCoverCodeDelete(item,index)">删除
                </link-swipe-option>
                <item :arrow="false">
                    <view slot="note">
                        <view class="code-view-row">
                            <view class="code-view-left">{{item.prodQrCode}}</view>
                            <view class="code-view-right">
                                <view class="code-item">{{item.productCode}}
                                </view>
                                <view class="code-item" :class="item.scanRecordStatus">{{item.scanRecordStatus |
                                    lov('SCAN_RECORD_STATUS')}}
                                </view>
                                <view class="code-item" :class="item.descriptionType">{{item.descriptionType |
                                    lov('MATCH_STATUS')}}
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </link-swipe-action>
        </list>
    </view>
</template>

<script>
    export default {
        name: "cover-code",
        props: {
            //标题
            title: {
                type: String,
                default: "扫码记录"
            },
            //上半部分-弃用
            radiusTopFlag: {
                type: Boolean,
                default: false
            },
            //四边-弃用
            radiusAllFlag: {
                type: Boolean,
                default: false
            },
            //下半部分
            radiusBottomFlag: {
                type: Boolean,
                default: false
            },
            //父ID
            parentId: {
                type: String,
                default: "",
            },
            //父对象-为活动对象
            parentData: {
                type: Object,
                default: {},
            },
            //是否可以操作删除数据。执行反馈界面可以操作[活动详情点执行反馈按钮过去的界面和执行反馈模块列表进详情的界面]，执行反馈组件不可以
            operationFlag: {
                type: Boolean,
                default: false
            }
        },
        data() {
            const unfoldFlag = true;
            let deleteFlag = false;//是否可以删除
            //活动状态 MC_STATUS : 进行中、执行结束 审批状态 APRO_STATUS : 申请审批通过、反馈撤回、反馈驳回 的活动扫码记录能够删除；
            if ((this.parentData.status === 'Processing' || this.parentData.status === 'Closed')
                && (this.parentData.aproStatus === 'Approve' || this.parentData.aproStatus === 'RefeedWithdraw' || this.parentData.aproStatus === 'Refeedback')) {
                deleteFlag = true;
            }
            return {
                unfoldFlag,
                codeList: [],
                deleteFlag,
            }
        },
        async created() {
            await this.queryCoverCodeData();
        },
        mounted() {
            this.$bus.$on('queryCoverCodeData', async () => {
                await this.queryCoverCodeData();
            });
        },
        methods: {
            /**
             * 收起
             * <AUTHOR>
             * @date 2020-08-28
             * */
            packUp() {
                this.unfoldFlag = false;
            },
            /**
             * 展开
             * <AUTHOR>
             * @date 2020-08-28
             * */
            unfold() {
                this.unfoldFlag = true;
            },
            /**
             * 查询扫码记录
             * <AUTHOR>
             * @date 2020-08-28
             * */
            async queryCoverCodeData() {
                const data = await this.$http.post('action/link/actScanRecord/queryByExamplePage', {
                    filtersRaw: [
                        {
                            "id": "scanType",
                            "property": "scanType",
                            "value": "[ActSaleScan,ActProdScan]",
                            "operator": "IN"
                        },
                        {"id": "scanSource", "property": "scanSource", "value": "StaffSystem"},
                        {"id": "headId", "property": "headId", "value": this.parentId}
                    ],
                });
                this.codeList = data.rows || [];
            },
            /**
             * 删除扫码记录
             * <AUTHOR>
             * @date 2021-01-26
             * */
            async handleCoverCodeDelete(item, index) {
                await this.$http.post('action/link/actScanRecord/deleteById', item);
                await this.queryCoverCodeData();
            },
        }
    }
</script>

<style lang="scss">
    .cover-code-view {
        background: white;
        overflow: hidden;
        /*deep*/
        .link-swipe-action {
            position: relative;
            overflow-x: hidden;
            width: 100%;
        }
        .item-header {
            height: 88px;
            width: 100%;
            font-size: 28px;
            line-height: 88px;
            color: #262626;
            letter-spacing: 0;
            border-bottom: 2px solid #F2F2F2;
        }

        .code-view-row {
            width: 100%;
            height: 160px;
            background: white;

            .code-view-left {
                height: 160px;
                width: 70%;
                float: left;
                word-break: break-all;
                padding: 25px;
                line-height: 40px;
            }

            .code-view-right {
                width: 22%;
                height: 160px;
                float: right;
                line-height: 40px;
                text-align: center;

                .Repeate {
                    color: #FF5A5A;
                }

                .MatchFailed {
                    color: #FF5A5A;
                }

                .MatchSuccessfully {
                    color: #2EB3C2;
                }

                .Normal {
                    color: #2EB3C2;
                }

                .Repeate {
                    color: #FF5A5A;
                }
            }
        }
    }
</style>
