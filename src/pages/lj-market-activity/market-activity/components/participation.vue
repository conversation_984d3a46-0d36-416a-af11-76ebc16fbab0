<template>
    <view class="participation" v-if="isCompleted">
        <line-title title="活动分析"/>
        <view class="participation-list">
        <view class="participation-item" v-for="(item,index) in actAnalysis" :key="index">
          <link-icon icon="mp-info-lite" status="info" @tap="showTips(item.tips)"/>
          <view class="participation-data" v-if="item.value === 'attendanceRate' || item.value === 'newMemberRate' || item.value === 'consumerImpRate' || item.value === 'consumerScanRate'">
            <view class="number">{{ item.number || 0}}%</view>
            <view class="name">{{item.name}}</view>
          </view>
          <view class="participation-data" v-else>
            <view class="number">{{(item.number > 0 ||  item.number === 0 ) ? item.number : '--'}}</view>
            <view class="name">{{item.name}}</view>
          </view>
        </view>
        </view>
    </view>
</template>

<script>
import LineTitle from "../../../echart/lzlj/components/line-title";
export default {
    name: "participation",
    components: {LineTitle},
    props: {
        mcActId: {
            type: String,
            default: ''
        },
        updateAnalysisData: {
           type: Boolean,
           default: false
       }
    },
    watch: {
      updateAnalysisData (val) {
        if (val) {
          this.queryActAnalysis()
        }
      }
    },
    data () {
        return {
            isCompleted: false,             // 是否加载完成
            actAnalysis: [// 活动分析对象
                {
                  value: 'subAndInv',
                  number: 0,
                  name: '提报人数',
                  tips: '基础信息部分的消费者人数'
                }, {
                  value: 'inputConsumerNum',
                  number: 0,
                  name: '名单录入数',
                  tips: '录入消费者详细信息的人数'
                }, {
                  value: 'sceneNum',
                  number: 0,
                  name: '名单内扫码人数',
                  tips: '录入名单内扫码人数'
                }, {
                  value: 'interactionScanNum',
                  number: 0,
                  name: '参加扫码活动人数',
                  tips: '参加活动的扫码人数'
                }, {
                  value: 'sceneAddNum',
                  number: 0,
                  name: '名单外扫码人数',
                  tips: '名单外扫码人数'
                }, {
                  value: 'attendanceRate',
                  number: 0,
                  name: '到场匹配率',
                  tips: '录入名单内扫码人数÷名单录入数'
                },{
                  value: 'newMemberNum',
                  number: 0,
                  name: '新增会员数',
                  tips: '本场活动注册的会员数'
                },{
                    value: 'consumerImpRate',
                    number: 0,
                    name: '消费者录入率',
                    tips: '录入消费者人数÷提报人数'
                },{
                    value: 'consumerScanRate',
                    number: 0,
                    name: '消费者扫码率',
                    tips: '扫码人数÷提报人数'
                },{
                    value: 'appendNum',
                    number: 0,
                    name: '补录人数',
                    tips: '活动创建成功后或活动执行完成后录入的消费者人数'
                },{
                    value: 'appendInnerScanNum',
                    number: 0,
                    name: '补录名单内扫码人数',
                    tips: '补录名单中参与扫码的消费者人数'
                }
            ]
        }
    },
    async created() {
        await this.queryActAnalysis();
    },
    methods: {
        /**
         * @desc 展示提示语
         * <AUTHOR>
         * @date 2021/6/4 14:57
         **/
        async showTips (data) {
          this.$message.primary(data)
        },
        /**
         * @desc 查询互动活动分析
         * <AUTHOR>
         * @date 2021/5/17 11:30
         **/
        async queryActAnalysis () {
            const data = await this.$http.post(this.$env.appURL + '/cscAnalysis/link/singleActAnalysis/queryActAnalysis', {mcActId: this.mcActId});
            if (data.success) {
                data.result.appendNum = Number(data.result.appendNum)
                data.result.appendInnerScanNum = Number(data.result.appendInnerScanNum)
                this.actAnalysis.forEach((item) => {
                  item.number = data.result[item.value]



                  if (item.value === 'attendanceRate') { // 到场率
                    if (data.result.crowNum) {
                      item.number = (data.result.arrivalMatchingRate*100).toFixed(2)
                    }
                  } else if (item.value === 'newMemberRate') { // 会员转化率
                    if (data.result.crowNum) {
                      item.number = ((data.result.newMemberNum/data.result.crowNum)*100).toFixed(2)
                    }
                  } else if (item.value === 'consumerImpRate') { // 消费者提报率
                      if (data.result.reportConsumerNum) {
                          item.number = ((data.result.recordConsumerNum / data.result.reportConsumerNum) * 100).toFixed(2)
                      }
                  } else if (item.value === 'consumerScanRate') { // 消费者扫码率
                      if (data.result.subAndInv) {
                          item.number = ((data.result.interactionScanNum / parseInt(data.result.subAndInv, 10)) * 100).toFixed(2)
                      }
                  } else {
                    item.number = data.result[item.value]
                  }
                })
                this.isCompleted = true;
            }
        }
    }
}
</script>

<style lang="scss">
.participation{
    margin: 0 24px;
    .participation-list{
        margin-top: 32px;
        border-radius: 16px;
        padding: 32px;
        display: flex;
        flex-wrap: wrap;
        background: white;
        .participation-item{
            text-align: center;
            line-height: 40px;
            width: 29%;
            height: 120px;
            border-radius: 10px;
            border: 1px solid #1E8BFF;
            margin: 0 20px 20px 0;
            font-size: 20px;
            display: flex;
            flex-direction: column;
            .link-icon{
              margin-left: auto;
              font-size: 36px;
            }
           .participation-data{
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              .number{
                color: #1E8BFF;
              }
          }
        }
    }
}
</style>
