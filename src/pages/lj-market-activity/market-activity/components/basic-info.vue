<template>
    <view class="market-activity-basic-info">
        <!--基础信息-->
        <view class="basic-info-v" id="basic-info-v"
              :style="pageSource === 'advance' ? 'margin-top:0px' : 'margin-top:46px'">
            <view class="menu-stair">
                <view class="line">
                    <view class="line-top"></view>
                    <view class="line-bottom"></view>
                </view>
                <view class="stair-title">基础信息</view>
                <view class="edit" @tap="editBasicInfo" v-if="editFlag && !$utils.isEmpty(pageSource) && !isSalesAreaManager">编辑</view>
            </view>
            <view class="basic-info">
                <view style="width: 100%;height: 8px"></view>
                <view v-for="item in subControlList">
                    <!--
                    配置的金额字段 根据状态控制显影
                    新建 New 已发布 Published 进行中 Processing
                    1、实际动销金额 actualSalesAmount 不显示状态：新建、已发布
                    2、现金审批金额 cashApoAmount不显示状态：新建
                    3、现金实际填报金额 cashFillAmount 不显示状态：新建、已发布
                    4、现金实发金额 cashRealAmount 不显示状态：新建、已发布、进行中、已结束
                    5、产品审批金额 prodApoAmount  不显示状态：新建
                    6、产品实际填报金额 prodFillAmount 不显示状态：新建、已发布
                    7、产品实发金额 prodRealAmount 不显示状态：新建、已发布、进行中、已结束
                    -->
                    <view class="block-v" v-if="item.values.field !== 'activityNum' && item.ctrlCode !== 'view-line' &&
                    (
                        (item.values.field === 'actualSalesAmount' && !(activityItemData.status === 'New' || activityItemData.status === 'Published')&& priceShowFlag)
                        || (item.values.field === 'cashApoAmount' && activityItemData.status !== 'New' && priceShowFlag)
                        || (item.values.field === 'cashFillAmount'&& priceShowFlag && !(activityItemData.status === 'New' || activityItemData.status === 'Published') && priceShowFlag)
                        || (item.values.field === 'cashRealAmount'&& priceShowFlag && !(activityItemData.status === 'New' || activityItemData.status === 'Published' || activityItemData.status === 'Processing' || activityItemData.status === 'Closed'))
                        || (item.values.field === 'cashApplyAmount'&& priceShowFlag)
                        || (item.values.field === 'prodApplyAmount'&& priceShowFlag)
                        || (item.values.field === 'prodApoAmount'&& priceShowFlag && activityItemData.status !== 'New')
                        || (item.values.field === 'prodFillAmount'&& priceShowFlag && !(activityItemData.status === 'New' || activityItemData.status === 'Published') )
                        || (item.values.field === 'prodRealAmount'&& priceShowFlag && !(activityItemData.status === 'New' || activityItemData.status === 'Published' || activityItemData.status === 'Processing' || activityItemData.status === 'Closed'))
                        || !(['actualSalesAmount', 'cashApoAmount', 'cashFillAmount', 'cashApplyAmount', 'prodApplyAmount'].includes(item.values.field)
                        || ['cashRealAmount', 'prodApoAmount', 'prodFillAmount', 'prodRealAmount'].includes(item.values.field))
                        )">
                        <view class="title">{{fieldNameChange(item.values.field, item.base.label)}}</view>
                        <view class="val"
                              v-if="$utils.isEmpty(item.values.lovType) && $utils.isEmpty(item.values.cny) && $utils.isEmpty(item.values.date)">
                            {{activityItemData[item.values.field]}}
                        </view>
                        <view class="val" v-if="!$utils.isEmpty(item.values.lovType)">
                            {{activityItemData[item.values.field]|lov(`${item.values.lovType}`)}}
                        </view>
                        <view class="val" v-if="!$utils.isEmpty(item.values.cny)">{{activityItemData[item.values.field]
                            | cny}}
                        </view>
                        <view class="val" v-if="!$utils.isEmpty(item.values.date)">{{activityItemData[item.values.field]
                            | date(`${item.values.date}`)}}
                        </view>
                    </view>
                    <view v-if="item.values.field === 'activityNum'" class="block-v">
                        <view class="title">活动编码</view>
                        <view class="val" @longPress="copyActCode(activityItemData['activityNum'])">
                            {{activityItemData['activityNum']}}
                        </view>
                    </view>
                    <view v-if="item.values.field === 'actIndeSourCode' || item.values.field === 'activityType'" class="block-v">
                        <view class="title">业务场景</view>
                        <view class="val">
                            {{activityItemData['busScene']| lls('TMPL_SUB_BIZ_TYPE')}}
                        </view>
                    </view>
                    <view class="line" v-if="item.ctrlCode === 'view-line'"></view>
                </view>
                <view style="width: 100%;height: 8px"></view>
            </view>
        </view>
    </view>
</template>

<script>
    import Taro from "@tarojs/taro";
    export default {
        name: "basic-info",
        props: {
            activityItem: {
                type: Object,
                default: function () {
                    return {};
                }
            },
            //基础信息-展示子组件
            subControlList: {
                type: Array,
            },
            //基础信息-编辑子组件
            editSubControlList: {
                type: Array,
            },
            //页面来源 -
            // 1、执行反馈环节 executiveFeedback
            // 2、other 活动的其他信息(ps:这是一个页面)
            // 3、preview 活动预览界面而来
            // 4、view 活动查看界面查看而来
            // 5、审批或小程序消息进去 为空
            // 6、activityAudit 活动稽核
            pageSource: {
                type: String,
            }
        },
        computed: {
            activityItemData: function () {
                return this.activityItem;
            },
            isSalesAreaManager: function () {
                return ['CityManager', 'SalesRegionManager', 'SalesAreaManager',].includes(Taro.getStorageSync('token').result.positionType)
            }
        },
        data() {
            const userInfo = Taro.getStorageSync('token').result;
            let editFlag = false;//是否可以编辑基础信息
            //页面来源 executiveFeedback 非执行反馈、活动稽核：活动状态 MC_STATUS : 新建 审批状态 APRO_STATUS : 未提交、已拒绝 的活动能够编辑；
            //页面来源 executiveFeedback 执行反馈：活动状态 MC_STATUS : 进行中、已结束、已发布 审批状态 APRO_STATUS : 申请审批通过、反馈驳回、反馈撤回 的活动能够编辑；
            if ((this.pageSource !== 'executiveFeedback' && this.pageSource !== 'activityAudit' && this.activityItem.status === 'New'
                && (this.activityItem.aproStatus === 'New' || this.activityItem.aproStatus === 'Refused')
            ) || (this.pageSource === 'executiveFeedback' && (this.activityItem.status === 'Processing' || this.activityItem.status === 'Closed' || this.activityItem.status === 'Published')
                && (this.activityItem.aproStatus === 'Approve' || this.activityItem.aproStatus === 'Refeedback' || this.activityItem.aproStatus === 'RefeedWithdraw')
            )) {
                editFlag = true;
            }
            return {
                // 费用价格是否展示标识
                priceShowFlag: false,
                userInfo,
                editFlag,
                master: {
                    masterId: '',
                    masterName: ''
                }, // 宴席主家
            }
        },
        async created() {
            this.priceShowFlag = await this.$utils.getPriceDesensitize(this.userInfo);
        },
        mounted() {
            // 新建消费者后将不需要审批的消费者直接带到宴席主家上
            this.$bus.$on('viewUpdateMaster', (data) => {
                this.$set(this.master, 'masterId', data.id);
                this.$set(this.master, 'masterName', data.name);
            })
        },
        methods: {
            /**
             * 宴席主家展示字段名称处理
             * <AUTHOR>
             * @date	2025/1/2
             */
            fieldNameChange(field,label) {
                if (field === 'masterName') {
                    return ['1216'].includes(this.userInfo.coreOrganizationTile.brandCompanyCode) ? '关键人' : label;
                } else {
                    return label
                }
            },
            /**
             * 复制活动编码
             *  <AUTHOR>
             *  @date 2022年7月22日
             */
            copyActCode(text) {
                wx.setClipboardData({data: text});
            },
            async getExcludebusSceneData(){
                const isTeQu = this.userInfo.coreOrganizationTile.brandCompanyCode === '5137'
                const isJiaoLing = this.userInfo.coreOrganizationTile.brandCompanyCode === '5151'
                let param = {
                    filtersRaw: [
                        {id: "type", property: "type", value: 'TMPL_SUB_BIZ_TYPE'},
                        {id: "activeFlag", property: "activeFlag", value: 'Y'},
                        {id: "parentVal", property: "parentVal", value: 'businessScenario'},
                    ]
                }
                // 费用小类为事件营销活动，特曲订餐活动类型为浓香私宴，窖龄订餐活动类型为链路会员时，业务场景只能选择【促销活动用餐】
                if (this.activityItem.costType === '事件营销活动' && ((isJiaoLing && this.activityItem.activityType === 'LianLuHuiYuan') || (isTeQu  && this.activityItem.activityType === 'NongXiangSiYan'))) {
                    param.filtersRaw.push({id: "val", property: "val", value: "ActivityMeal5", operator: "<>"},)
                } else {
                    param.filtersRaw.push({id: "useFlag", property: "useFlag", value: 'N'},)
                }
                const data = await this.$http.post('action/link/basic/queryByExamplePage', param)
                let baseArray = [];
                if(data.rows){
                    data.rows.forEach((item) => {
                        baseArray.push(item.val)
                    })
                }
                return baseArray;
            },
            /**
             * 编辑基础信息
             * */
            async editBasicInfo() {
                const excludebusSceneData = await this.getExcludebusSceneData();
                //2021-08-04考虑多人操作的场景
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.activityItemData.id
                });
                if ((this.pageSource !== 'executiveFeedback' && this.pageSource !== 'activityAudit' && data.result.status === 'New'
                    && (data.result.aproStatus === 'New' || data.result.aproStatus === 'Refused')
                ) || (this.pageSource === 'executiveFeedback' && (data.result.status === 'Processing' || data.result.status === 'Closed' || data.result.status === 'Published')
                    && (data.result.aproStatus === 'Approve' || data.result.aproStatus === 'Refeedback' || data.result.aproStatus === 'RefeedWithdraw')
                )) {
                    this.editFlag = true;
                } else {
                    this.$message.warn('活动已被更新，当前活动状态和审批状态不允许编辑基础信息，请返回列表重新查询活动数据。');
                    return ;
                }

                if (!this.$utils.isEmpty(this.activityItemData.prodLargeClass)) {
                    this.activityItemData.prodLargeClass = await this.$lov.getNameByTypeAndVal('PROD_L_CLASS', this.activityItemData.prodLargeClass);
                }
                if (!this.$utils.isEmpty(this.activityItemData.displayItem)) {
                    this.activityItemData.displayItem = await this.$lov.getNameByTypeAndVal('PROD_M_CLASS', this.activityItemData.displayItem);
                }
                this.$nav.push('/pages/lj-market-activity/market-activity/new-activity-basic-page', {
                    data: this.activityItemData,
                    editSubControlList: this.editSubControlList,
                    pageSource: this.pageSource,
                    pageFrom: 'basicInfo',
                    excludebusSceneData:excludebusSceneData,//业务场景需要排除的值列表值
                    master: this.master, // 宴席主家
                    callback: (data) => {
                        this.$emit('updateActivity', data); //通知父组件更新活动信息。
                    }
                })
            }
        }
    }
</script>

<style lang="scss">
    .market-activity-basic-info {
        .basic-info-v {

            .menu-stair {
                width: 100%;
                margin-left: 24px;
                padding-top: 40px;
                @include flex-start-center;

                .line {
                    clear: both;

                    .line-top {
                        width: 8px;
                        height: 16px;
                        background: #3FE0E2;
                    }

                    .line-bottom {
                        width: 8px;
                        height: 16px;
                        background: #2F69F8;
                    }
                }

                .stair-title {
                    width: 30%;
                    margin-left: 16px;
                    font-family: PingFangSC-Semibold, serif;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 1px;
                    line-height: 32px;
                }

                .edit {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #2F69F8;
                    letter-spacing: 0;
                    line-height: 28px;
                    text-align: right;
                    width: 58%;
                }
            }

            .basic-info {
                background: #FFFFFF;
                border-radius: 16px;
                margin: 24px;

                .block-v {
                    padding: 0 24px;
                    display: flex;

                    .title {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        line-height: 60px;
                        width: 35%;
                        float: left;
                    }

                    .val {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 0;
                        text-align: right;
                        line-height: 60px;
                        width: 65%;
                        float: left;
                        text-overflow: ellipsis;
                    }
                }

                .line {
                    margin: 32px 24px 32px 24px;
                    height: 0;
                    border: 2px dashed #DADEE9;
                }
            }
        }
    }
</style>
