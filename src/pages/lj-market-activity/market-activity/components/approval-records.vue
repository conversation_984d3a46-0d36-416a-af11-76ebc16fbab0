<template>
    <link-page class="market-activity-approval">
        <!--审批历史-->
        <view class="approval-list-v">
            <view class="menu-stair" style="margin-bottom: 12px">
                <view class="line">
                    <view class="line-top"></view>
                    <view class="line-bottom"></view>
                </view>
                <view class="stair-title">审批历史</view>
                <view class="edit" v-if="approvalList.length>0">
                    <view v-if="unfoldFlag">
                        <view style="float: right;text-align: right;width: 50%;color: #2F69F8;"
                              @tap="packUp">
                            展开
                            <link-icon icon="icon-up-circle" style="height: 44px"/>
                        </view>
                    </view>
                    <view v-if="!unfoldFlag">
                        <view style="float: right;text-align: right;width: 50%;color: #2F69F8;"
                              @tap="unfold">
                            收起
                            <link-icon icon="icon-down-circle" style="height: 44px"/>
                        </view>
                    </view>
                </view>
            </view>
            <view class="approval-list">
                <list v-if="!unfoldFlag">
                    <item v-for="(item,index) in operatorList" :key="index" :arrow="false">
                        <view slot="note">
                            <view class="approval">
                                <view class="row">
                                    <view class="outer-ring">
                                        <view class="inner-ring"></view>
                                    </view>
                                    <view class="date">{{item.flowStart|date('YYYY-MM-DD HH:mm:ss')}}</view>
                                    <view class="title">{{item.flowStartPsnName}}</view>
                                    <view class="operator">提交</view>
                                    <view class="name" style="color: #2F69F8;">{{item.approvalType|lov('APPROVAL_OBJECT_TYPE')}}</view>
                                </view>
                            </view>
                        </view>
                    </item>
                    <item v-for="(item,index) in approvalList" :key="index" :arrow="false">
                        <view slot="note">
                            <view class="approval">
                                <view class="row">
                                    <view class="outer-ring">
                                        <view class="inner-ring"></view>
                                    </view>
                                    <view class="date">{{item.submmitTime|date('YYYY-MM-DD HH:mm:ss')}}</view>
                                    <view class="title">{{item.submmitUserName}}</view>
                                    <view class="operator">提交</view>
                                    <view class="name" style="color: #2F69F8;">{{item.flowType|lov('APPROVAL_OBJECT_TYPE')}}</view>
                                </view>
                                <view class="row" style="margin-top: 10px">
                                    <view class="outer-ring">
                                        <view class="inner-ring"></view>
                                    </view>
                                    <view class="date">{{item.approvalEnd|date('YYYY-MM-DD HH:mm:ss')}}</view>
                                    <view class="title">{{item.approvalUserName}}</view>
                                    <view class="name">{{item.approvalOpera}}</view>
                                </view>
                                <view class="row" style="margin-left: 24px;margin-top: 12px">
                                    <view>{{item.approvalView}}</view>
                                </view>
                            </view>
                        </view>
                    </item>
                </list>
                <list v-if="unfoldFlag">
                    <item v-for="(item,index) in operatorList" :key="index" :arrow="false">
                        <view slot="note">
                            <view class="approval">
                                <view class="row">
                                    <view class="outer-ring">
                                        <view class="inner-ring"></view>
                                    </view>
                                    <view class="date">{{item.flowStart|date('YYYY-MM-DD HH:mm:ss')}}</view>
                                    <view class="title">{{item.flowStartPsnName}}</view>
                                    <view class="operator">提交</view>
                                    <view class="name" style="color: #2F69F8;">{{item.approvalType|lov('APPROVAL_OBJECT_TYPE')}}</view>
                                </view>
                            </view>
                        </view>
                    </item>
                    <item v-for="(item,index) in approvalList.slice(0,2)" :key="index" :arrow="false">
                        <view slot="note">
                            <view class="approval">
                                <view class="row">
                                    <view class="outer-ring">
                                        <view class="inner-ring"></view>
                                    </view>
                                    <view class="date">{{item.submmitTime|date('YYYY-MM-DD HH:mm:ss')}}</view>
                                    <view class="title">{{item.submmitUserName}}</view>
                                    <view class="operator">提交</view>
                                    <view class="name" style="color: #2F69F8;">{{item.flowType|lov('APPROVAL_OBJECT_TYPE')}}</view>
                                </view>
                                <view class="row" style="margin-top: 10px">
                                    <view class="outer-ring">
                                        <view class="inner-ring"></view>
                                    </view>
                                    <view class="date">{{item.approvalEnd|date('YYYY-MM-DD HH:mm:ss')}}</view>
                                    <view class="title">{{item.approvalUserName}}</view>
                                    <view class="name">{{item.approvalOpera}}</view>
                                </view>
                                <view class="row" style="margin-left: 24px;margin-top: 12px">
                                    <view>{{item.approvalView}}</view>
                                </view>
                            </view>
                        </view>
                    </item>
                </list>
            </view>
        </view>
    </link-page>
</template>

<script>
    export default {
        name: "approval-records",
        props: {
            //父ID
            parentId: {
                type: String,
            },
        },
        data() {
            const unfoldFlag = true;
            return {
                unfoldFlag,
                approvalList: [],//审批记录
                operatorList:[],//操作记录
            }
        },
        async created() {
            await this.queryApprovalData();//查询审批记录
            await this.queryOperatorData();//查询操作数据
        },
        mounted() {
            this.$bus.$on('initActivityApprovalRecords', async () => {
                await this.queryApprovalData();//查询审批记录
                await this.queryOperatorData();//查询操作数据
            });
        },
        methods: {
            /**
             * 收起
             * <AUTHOR>
             * @date 2021-01-06
             * */
            packUp() {
                this.unfoldFlag = false;
            },
            /**
             * 展开
             * <AUTHOR>
             * @date 2021-01-06
             * */
            unfold() {
                this.unfoldFlag = true;
            },
            /**
             * 查询审批历史记录
             * <AUTHOR>
             * @date 2021-01-06
             *
             */
            async queryApprovalData() {
                setTimeout(async () => {
                    const data = await this.$http.post('action/link/flowRecord/queryByExamplePage', {
                        totalFlag: true,
                        sort: "created",
                        order: 'desc',
                        filtersRaw: [{id: 'flowObjId', property: 'flowObjId', value: this.parentId, operator: '='}]
                    });
                    this.approvalList = data.rows || [];
                }, 3000);
            },
            /**
             * 查询操作记录
             * <AUTHOR>
             * @date 2021-01-21
             * */
            async  queryOperatorData(){
                setTimeout(async () => {
                    const data = await this.$http.post('action/link/flow/v2/queryByExamplePage', {
                        totalFlag: true,
                        sort: "created",
                        order: 'desc',
                        filtersRaw: [{id: 'flowObjId', property: 'flowObjId', value: this.parentId, operator: '='},
                            {id:'flowStatus',property: 'flowStatus',value: 'Running',operator: '='}]
                    });
                    this.operatorList = data.rows || [];
                }, 3000);
            }
        }
    }
</script>

<style lang="scss">
    .market-activity-approval {
        clear: both;

        .approval-list-v {
            .menu-stair {
                width: 100%;
                margin-left: 24px;
                padding-top: 24px;
                @include flex-start-center;

                .line {
                    .line-top {
                        width: 8px;
                        height: 16px;
                        background: #3FE0E2;
                    }

                    .line-bottom {
                        width: 8px;
                        height: 16px;
                        background: #2F69F8;
                    }
                }

                .stair-title {
                    width: 30%;
                    margin-left: 16px;
                    font-family: PingFangSC-Semibold, serif;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 1px;
                    line-height: 32px;
                }

                .edit {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #2F69F8;
                    letter-spacing: 0;
                    line-height: 28px;
                    text-align: right;
                    width: 58%;
                }
            }

            .approval-list {
                margin: 24px;

                .approval {
                    .row {
                        .outer-ring {
                            background: #EFF3FF;
                            width: 32px;
                            height: 32px;
                            border-radius: 50%;
                            position: relative;
                            float: left;

                            .inner-ring {
                                background: #2F69F8;
                                width: 12px;
                                height: 12px;
                                line-height: 32px;
                                border-radius: 50%;
                                position: absolute;
                                top: 10px;
                                left: 10px;
                            }
                        }

                        .date {
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #8C8C8C;
                            letter-spacing: 0;
                            line-height: 32px;
                            height: 32px;
                            padding-left: 20px;
                            width: 50%;
                            float: left;
                        }

                        .title {
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 32px;
                            height: 32px;
                            float: left;
                        }

                        .operator{
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 32px;
                            height: 32px;
                            float: left;
                            padding-left: 10px;
                        }

                        .name {
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #262626;
                            letter-spacing: 0;
                            text-align: right;
                            line-height: 32px;
                            height: 32px;
                        }
                    }
                }
            }
        }
    }
</style>
