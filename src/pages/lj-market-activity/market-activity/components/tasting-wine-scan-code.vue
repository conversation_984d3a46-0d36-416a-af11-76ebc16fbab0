<template>
    <view class="tasting-wine-scan-code-component">
        <!-- 可拖动扫码按钮 -->
        <movable-area class="movable-area">
            <movable-view class="movable-btn" direction="all" :x="($device.systemInfo.windowWidth - 70)" :y="($device.systemInfo.windowHeight - 260)">
                <link-fab-button icon="icon-scan" @tap="showScanCodeType" v-if="giftFlag || showOpenBottleFlag"/>
            </movable-view>
        </movable-area>

        <link-dialog ref="scanCodeTypeRef" position="poster">
            <view class="scan-main">
                <view class="scan-title">选择扫码业务</view>
                <view class="scan-type">
                    <view class="scan-item" @tap="scanCode('open')" v-if="showOpenBottleFlag">
                        <link-icon icon="icon-kaipinghexiao" style="color: #d81e06; font-size: 3em"></link-icon>
                        <view class="scan-type-item">开瓶核销</view>
                    </view>
                    <view class="scan-item" @tap="scanCode('gift')" v-if="giftFlag">
                        <link-icon icon="icon-zengsonghexiao" style="color: #ffb939; font-size: 3em"></link-icon>
                        <view class="scan-type-item">赠送核销</view>
                    </view>
                </view>
            </view>
        </link-dialog>
        <link-dialog ref="boxDialog" disabledHideOnClickMask title="扫码异常提示">
            {{ boxInfo }}
            <link-button slot="foot" @tap="$refs.boxDialog.hide()">关闭</link-button>
        </link-dialog>
        <!--开瓶物资弹窗-异常提示-->
        <link-dialog ref="confirmDialog" disabledHideOnClickMask title="扫码异常提示">
            {{ confirmInfoOpen }}
            <link-button slot="foot" @tap="$refs.confirmDialog.hide()" style="color: #ffffff">取消</link-button>
            <link-button slot="foot" @tap="showChooseMaterialsOpenDialog">确定</link-button>
        </link-dialog>
        <!--赠送物资弹窗-异常提示-->
        <link-dialog ref="confirmDialog" disabledHideOnClickMask title="扫码异常提示">
            {{ confirmInfo }}
            <link-button slot="foot" @tap="$refs.confirmDialog.hide()" style="color: #333333">取消</link-button>
            <link-button slot="foot" @tap="showChooseMaterialsDialog">确定</link-button>
        </link-dialog>
        <!--开瓶物资弹窗-->
        <link-dialog ref="chooseMaterialsOpenDialog" disabledHideOnClickMask title="关联物资">
            <view class="choose-materials-view">
                <view class="choose-materials-column">
                    <view class="tips">注： {{ this.confirmInfoOpen }}</view>
                    <view @tap="doChoose('chooseMaterialsOpen')" class="materials-column-content">
                        <view class="choose">
                            <view class="icon-red">*</view>
                            <view class="choose-prod">
                                <view>选择物资</view>
                                <view>
                                    <link-icon icon="icon-right"></link-icon>
                                </view>
                            </view>
                        </view>
                        <view class="prod-content" v-if="associatedCosts.prodCode">
                            <view class="head">
                                <view>产品编码：{{ associatedCosts.prodCode }}</view>
                                <view>{{ associatedCosts.feePayType }}</view>
                            </view>
                            <view class="prod-name">{{ associatedCosts.prodName }}</view>
                        </view>
                    </view>
                    <view class="label">备注</view>
                    <view class="value">
                        <link-textarea class="text-area" :placeholder="'请输入备注'"
                                       mode="textarea"
                                       v-model="associatedCosts.remark" :nativeProps="{maxlength:200}"
                                       placeholder-style="color: #BFBFBF;"></link-textarea>
                    </view>
                </view>
            </view>
            <link-button slot="foot" @tap="$refs.chooseMaterialsOpenDialog.hide()" style="color: #333333">取消</link-button>
            <link-button slot="foot" @tap="generateScanRecords()">确定</link-button>
        </link-dialog>
        <!--赠送物资弹窗-->
        <link-dialog ref="chooseMaterialsDialog" disabledHideOnClickMask title="关联物资">
            <view class="choose-materials-view">
                <view class="choose-materials-column">
                    <view class="tips">注： {{ this.confirmInfo }}</view>
                    <view @tap="doChoose('chooseMaterials')" class="materials-column-content">
                        <view class="choose">
                            <view class="icon-red">*</view>
                            <view class="choose-prod">
                                <view>选择物资</view>
                                <view>
                                    <link-icon icon="icon-right"></link-icon>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view class="label">备注</view>
                    <view class="value">
                        <link-textarea class="text-area" :placeholder="'请输入备注'"
                                       mode="textarea"
                                       v-model="associatedCosts.remark" :nativeProps="{maxlength:200}"
                                       placeholder-style="color: #BFBFBF;"></link-textarea>
                    </view>
                </view>
            </view>
            <link-button slot="foot" @tap="$refs.chooseMaterialsDialog.hide()" style="color: #333333">取消</link-button>
            <link-button slot="foot" @tap="generateScanRecords()">确定</link-button>
        </link-dialog>
    </view>
</template>

<script>
import Taro from "@tarojs/taro";
import {getCurrentCoordinate, reverseTMapGeocoder} from "../../../../utils/locations-tencent";
import TastingWineScanCodeStatistic from './tasting-wine-scan-code-statistic.vue'

export default {
    name: "tasting-wine-scan-code",
    props: {
        //场景-实际费用 actual 、申请费用 apply 控制保存编辑时调用的接口
        scene: {
            type: String
        },
        //父对象-活动对象
        activityItem: {
            type: Object,
            default: function () {
                return {};
            }
        },
        //页面来源 -
        // 1、执行反馈环节 executiveFeedback
        // 2、other 活动的其他信息(ps:这是一个页面)
        // 3、preview 活动预览界面而来
        // 4、view 活动查看界面查看而来
        // 5、审批、小程序消息、执行案 为空
        // 6、activityAudit 活动稽核
        pageSource: {
            type: String
        }
    },
    data() {
        let userInfo = Taro.getStorageSync('token').result;
        let activityItemNew = {};
        if (!this.$utils.isEmpty(this.$dataService.getMarketActivityItem())) {
            activityItemNew = this.$dataService.getMarketActivityItem()
        } else {
            activityItemNew = this.activityItem;
        }
        console.log(activityItemNew['openScanConfig'], activityItemNew['giftScanConfig'])
        return {
            oneFlag: true, //控制confirm确认框重复点击问题
            scanOneFlag: true, //控制扫码组件单次点击
            boxInfo: "后台没有返回校验结果",//纯文本弹框内容
            confirmInfo: "",//
            confirmInfoOpen: "",//开瓶物资弹窗-提示
            associatedCosts: {
                prodCode: "",//产品编码
                prodId: "",//产品ID
                prodName: "", //产品名称
                remark: "",//备注,
                materialLineId: "",//物资ID
                feePayType: "",//兑付方式
            },//关联费用的对象
            codeProductRecordUpdateObj: {
                prodCode: "",//产品编码
                prodId: "",//产品ID
                prodName: "", //产品名称
            },//更新二维码的数据使用对象，流程内，如果无效则不更新码状态，但是要更新码数据上的产品ID、产品编码、产品名称【优先取接口返回的，如果接口没返回再使用选择的(就算是选的情况也优先使用返回的产品信息)】。
            scanRecordsData: {},//扫码记录对象
            codeData: {},//二维码数据行对象
            scanScenario: "",//扫码类型 open 开瓶 gift 转增
            qrCodeStatus: "",//产品二维码状态-扫码记录创建之后更新产品二维码状态时使用。
            showOpenBottleFlag: this.pageSource === 'executiveFeedback' && activityItemNew['openScanConfig'] !== 'Unused',//开瓶扫码是否展示
            giftFlag: this.pageSource === 'executiveFeedback' && activityItemNew['giftScanConfig'] !== 'Unused', //转赠校验条件判断标志
            userInfo,
            coordinate: {}, // 存储地理经纬度信息
            addressData: {},  // 地理位置信息
            addressDataFull: '', //详细地理位置
            addressFlag: false,  //是否开启地理定位
            codeProductRecordUpdateProdFlag: false, //更新的产品二维码对象的产品ID字段，仅仅当返回的产品二维码上的产品ID为内，且是前端选择物资的情况下更新产品二维码状态时也更新产品二维码上的产品ID
            basicOption: {},
            doChoose: this.throttle(1000),
            nowScanGiftNum: 1, //本次扫码可赠送数，有rowsOther取其中rows长度，无rowsOther取1
            countFromChild: 0, // 从子组件获取本次赠送数量
            codeDataList: [], // 关联物资-箱码/盒码-扫码记录
            listProdId: '', // 关联物资-箱码/盒码-扫码记录-产品ID
            listMatch: null, // 关联物资-箱码/盒码-扫码记录-产品匹配
        }
    },
    async mounted() {
        this.$bus.$on('costSaveReturnFlag', (returnFlag) => {
            if (returnFlag !== false) {
                this.$refs.scanCodeTypeRef.show();
            }
        });
    },
    methods: {
        /**
         * 防止出现多个弹框
         * @auther 康丰强
         * @date 2022-5-24
         * */
        throttle(wait) {
            let pre = 0;
            let next = 0
            return function (func) {
                next = +new Date()
                if ((next - pre) > wait) {
                    pre = next;
                    this[func]()
                }
            }
        },
        /**
         * 流程内-品鉴酒融合-扫码
         * @auther songyanrong
         * @date 2021-10-19
         * type 区分扫码场景 : open 开瓶扫码 gift 转增核销
         * */
        async scanCode(type) {
            if (!this.scanOneFlag) return
            this.scanOneFlag = false
            this.scanScenario = type;
            await this.getAddress();
            if (type === 'open') {
                await this.kaiPingFun()
            }
            if (type === 'gift') {
                await this.zhuanZengFun(this.coordinate);
            }
        },
        /**
         * 获取定位地址  百度经纬度逆解析
         * <AUTHOR>
         * @date 2021年11月5日09:19:57
         */
        async getAddress() {
            this.coordinate = await this.$locations.getCurrentCoordinate();
            // 校验用户是否授权地理位置
            if (!this.$utils.isEmpty(this.coordinate.latitude) && !this.$utils.isEmpty(this.coordinate.longitude)) {
                let address = await reverseTMapGeocoder(this.coordinate.latitude, this.coordinate.longitude, '核销扫码定位');
                this.addressData = address['originalData'].result.addressComponent;
                this.addressDataFull = address['originalData'].result.formatted_address;
                this.addressFlag = true;
            }
        },
        /**
         * 流程内-品鉴酒融合-扫码
         * @auther songyanrong
         * @date 2021-10-19
         * */
        showScanCodeType() {
            //市场活动-模块新建及后续费用操作。扫码之前做前置保存，然后扫码校验时走费用数据保存到数据库的场景校验。
            //如果使用扫码功能或删除扫码记录，前置保存一下费用数据
            this.$bus.$emit('scanBeforeSaveCostOrDeleteScanCode');
        },
        /**
         * 开瓶扫码
         * @auther 康丰强
         * @date 2021-10-19
         * bullet = "box";//纯文本
         * bullet = "confirm";//有确认框
         * bullet = "none";//可以调用摄像头获取瓶码数据或者直接生成扫码记录的情况
         * */
        async kaiPingFun() {
            this.coordinate = await this.$locations.getCurrentCoordinate();
            if (this.$utils.isEmpty(this.coordinate.latitude) && this.$utils.isEmpty(this.coordinate.longitude)) {
                this.$dialog({
                    title: '提示',
                    content: '请确认手机地理位置授权是否打开，或者【设置】-【企业微信】位置权限管理是否打开？',
                    cancelButton: false,
                    confirmText: '去开启',
                    onConfirm: async () => {
                        let userLocation = await this.$locations.openSetting();
                        if (userLocation['scope.userLocation']) {
                            this.coordinate = await this.$locations.getCurrentCoordinate();
                        }
                    }
                });
                return
            } else {
                try {
                    this.$utils.showLoading();
                    const data = await this.$http.post('action/link/actualFee/scanCodeVerificationOpen', {
                        actId: this.activityItem.id,
                        wineTasting: 'Y2'
                    }, {
                        handleFailed: data => {
                            this.scanOneFlag = true
                        }
                    });
                    this.scanOneFlag = true
                    if (data.success) {
                        //判断 bullet和msg是否有返回值
                        if (this.$utils.isEmpty(data['bullet']) || this.$utils.isEmpty(data['msg'])) {
                            this.showMsg();
                        } else {
                            //纯提示类型
                            if (data['bullet'] === 'box') {
                                // this.showMsg(data['msg']);
                                this.$showError(data.msg);
                            }
                            //数据检查无问题 开始扫码
                            if (data['bullet'] === 'none') {
                                this.pingJianJiuScanCode('open')
                            }
                        }
                    } else {
                        this.$showError('校验失败，请稍后重试！' + data.detailMessage);
                    }
                } catch (e) {
                    this.$showError('校验失败，请稍后重试！');
                } finally {
                    this.$utils.hideLoading();
                }
            }
        },
        /**
         * @auther 康丰强
         * @date 2021年10月28日
         * 开瓶扫码时后台会强制校验出库扫码必输。
         * */
        async bottleOpeningVerification(mark) {
            try {
                const queryParam = {
                    mark: mark,
                    actId: this.activityItem.id,
                    feature: 'N'
                };
                this.$utils.showLoading();
                const data = await this.$http.post('action/link/codeProductRecord/codeProductRecordVerificationOpen', queryParam);
                if (data.success) {
                    this.$utils.hideLoading();
                    if (this.$utils.isEmpty(data['bullet']) || this.$utils.isEmpty(data['msg'])) {
                        this.showMsg();
                    } else {
                        if (data['bullet'] === 'box') {
                            // this.showMsg(data.msg);
                            this.$showError(data.msg);
                        }
                        if (data['bullet'] === 'confirm') {
                            this.confirmInfoOpen = data.msg;
                            this.codeData = data;
                            this.showChooseMaterialsOpenDialog()
                        }
                        if (data['bullet'] === 'none') {
                            await this.generateScanRecords(data);
                        }
                    }
                } else {
                    this.$showError('校验失败，请稍后重试！' + data.result);
                }
            } catch (e) {
                this.$showError('校验失败，请稍后重试！');
            } finally {
                this.$utils.hideLoading();
            }

        },
        /**
         * 流程内-品鉴酒融合-赠送扫码  扫码前出库数校验
         * @auther 吕志平
         * @date 2021年10月21日20:12:13
         * */
        async zhuanZengFun(coordinate) {
            this.coordinate = coordinate || await this.$locations.getCurrentCoordinate();
            if (this.$utils.isEmpty(this.coordinate.latitude) && this.$utils.isEmpty(this.coordinate.longitude)) {
                this.$dialog({
                    title: '提示',
                    content: '请确认手机地理位置授权是否打开，或者【设置】-【企业微信】位置权限管理是否打开？',
                    cancelButton: false,
                    confirmText: '去开启',
                    onConfirm: async () => {
                        let userLocation = await this.$locations.openSetting();
                        if (userLocation['scope.userLocation']) {
                            this.coordinate = await this.$locations.getCurrentCoordinate();
                        }
                    }
                });
                return
            } else {
                try {
                    this.$utils.showLoading();
                    this.scanOneFlag = true;
                    const data = await this.$http.post('action/link/actualFee/scanCodeVerificationGift', {
                        actId: this.activityItem.id,
                        feeStage: this.scene
                    }, {
                        handleFailed: data => {
                            this.scanOneFlag = true
                        }
                    });
                    this.scanOneFlag = true
                    if (data.success) {
                        this.$utils.hideLoading();
                        if (this.$utils.isEmpty(data['bullet']) || this.$utils.isEmpty(data['msg'])) {
                            this.showMsg();
                        } else {
                            //纯提示类型
                            if (data['bullet'] === 'box') {
                                // this.showMsg(data['msg']);
                                this.$showError(data.msg);
                            }
                            //数据检查无问题 开始扫码
                            if (data['bullet'] === 'none') {
                                this.zhuanZengFunScanCode();
                            }
                        }
                    } else {
                        this.$showError('校验失败，请稍后重试！' + data.detailMessage);
                    }
                } catch (e) {
                    this.$showError('校验失败，请稍后重试！');
                } finally {
                    this.$utils.hideLoading();
                }
            }
        },
        /**
         * 流程内-转赠扫码 扫码
         * @auther 吕志平
         * @date 2021年10月22日11:17:54
         * */
        async zhuanZengFunScanCode() {
            const that = this;
            // 只允许从相机扫码
            await wx.scanCode({
                onlyFromCamera: true,
                success(res) {
                    // 调后台校验接口
                    that.scanCodeAfter(res.result);
                }
            });
        },
        /**
         * 流程内-转赠扫码 扫码结果多种情况校验
         * @auther 吕志平
         * @date 2021年10月22日11:17:54
         * */
        async scanCodeAfter(mark) {
            try {
                const queryParam = {
                    mark: mark,
                    actId: this.activityItem.id,
                    feeStage: this.scene,
                };
                this.$utils.showLoading();
                const data = await this.$http.post('action/link/codeProductRecord/codeProductRecordVerificationGift',
                    queryParam);
                if (data.success) {
                    this.$utils.hideLoading();
                    // 分两种情况，判断是否有rowsOther的情况（箱码/盒码）
                    // 有rowsOther时，遍历每条值判断其bullet的值，若为confirm，则将其保存到codeDataList中，最后再调用showChooseMaterialsDialog
                    if (!!data.rowsOther && data.rowsOther.length > 0) {
                        const rowsOther = data.rowsOther;
                        for (const item of rowsOther) {
                            if (this.$utils.isEmpty(item['bullet']) || this.$utils.isEmpty(item['msg'])) {
                                this.showMsg();
                            } else {
                                if (item['bullet'] === 'box') {
                                    // this.showMsg(data.msg);
                                    this.$showError(item.msg);
                                }
                                //包含确认的
                                if (item['bullet'] === 'confirm') {
                                    // this.confirmInfo = item.msg;
                                    this.codeDataList.push(item);
                                    // this.nowScanGiftNum = rowsOther.length;
                                    // 保存转赠扫码行数据
                                    // this.codeData = item;
                                    // 本次扫码可赠送数
                                    // if (!!item.rowsOther && item.rowsOther.length > 0) {
                                    //     item.rows = item.rowsOther[0].rows
                                    //     this.nowScanGiftNum = item.rowsOther.length;
                                    // }
                                    //多个物资行情况
                                    // this.showChooseMaterialsDialog()
                                }
                                //数据检查无问题 开始扫码
                                if (item['bullet'] === 'none') {
                                    await this.generateScanRecords(item);
                                }
                            }
                        }
                        if(!!this.codeDataList && this.codeDataList.length > 0) {
                            // 等待map遍历结束后执行以下代码
                            this.nowScanGiftNum = rowsOther.length;
                            const isMsg = this.codeDataList.find(item => item['msg']);
                            if (isMsg) {
                                this.confirmInfo = isMsg.msg;
                            }
                            // 当计数器达到 rowsOther.length 时执行 showChooseMaterialsDialog
                            this.showChooseMaterialsDialog();
                        }
                    } else {
                        // 扫盖外码的情况
                        if (this.$utils.isEmpty(data['bullet']) || this.$utils.isEmpty(data['msg'])) {
                            this.showMsg();
                        } else {
                            if (data['bullet'] === 'box') {
                                // this.showMsg(data.msg);
                                this.$showError(data.msg);
                            }
                            //包含确认的
                            if (data['bullet'] === 'confirm') {
                                this.confirmInfo = data.msg;
                                //保存转赠扫码行数据
                                this.codeData = data;
                                // 本次扫码可赠送数
                                this.nowScanGiftNum = 1;
                                //多个物资行情况
                                this.showChooseMaterialsDialog()
                            }
                            //数据检查无问题 开始扫码
                            if (data['bullet'] === 'none') {
                                await this.generateScanRecords(data);
                            }
                        }
                    };
                } else {
                    this.$showError('校验失败，请稍后重试！' + data.result);
                }
            } catch (e) {
                this.$showError('校验失败，请稍后重试！');
            } finally {
                this.$utils.hideLoading();
            }
        },
        /**
         * 流程内-品鉴酒融合 根据产品码校验
         * 扫码之后拿到码信息之后的操作
         * @auther songyanrong
         * @date 2021-10-20
         * */
        async codeProductRecordVerification(mark) {
            try {
                const queryParam = {
                    mark: mark,
                    actId: this.activityItem.id,
                    feeStage: this.scene
                };
                this.$utils.showLoading();
                const data = await this.$http.post('action/link/codeProductRecord/codeProductRecordVerification',
                    queryParam);
                if (data.success) {
                    this.$utils.hideLoading();
                    if (this.$utils.isEmpty(data['bullet']) || this.$utils.isEmpty(data['msg'])) {
                        this.showMsg();
                    } else {
                        if (data['bullet'] === 'box') {
                            // this.showMsg(data.msg);
                            this.$showError(data.msg);
                        }
                        if (data['bullet'] === 'confirm') {
                            this.confirmInfoOpen = data.msg;
                            this.codeData = data;
                            this.showChooseMaterialsOpenDialog()
                        }
                        if (data['bullet'] === 'none') {
                            await this.generateScanRecords(data);
                        }

                    }
                } else {
                    this.$utils.hideLoading();
                    this.$showError('校验失败，请稍后重试！' + data.result);
                }
            } catch (e) {
                this.$showError('校验失败，请稍后重试！');
            } finally {
                this.$utils.hideLoading();
            }
        },
        /**
         * 流程内-品鉴酒融合-校验返回 纯信息弹出提示使用
         * @auther songyanrong
         * @date 2021-10-20
         * */
        showMsg(msg) {
            if (!this.$utils.isEmpty(msg)) {
                this.boxInfo = msg;
            }
            this.$refs.boxDialog.show();
        },
        /**
         * 流程内-品鉴酒融合 扫产品码
         * @auther songyanrong
         * @date 2021-10-20
         * */
        async pingJianJiuScanCode(type) {
            const that = this;
            // 只允许从相机扫码
            await wx.scanCode({
                onlyFromCamera: true,
                success(res) {
                    if (type === 'open') {
                        that.bottleOpeningVerification(res.result)
                        return
                    }
                    // 调后台校验接口
                    that.codeProductRecordVerification(res.result);
                }
            });
        },
        /**
         * 流程内-品鉴酒融合-校验返回 带确认的弹框使用
         * @auther songyanrong
         * @date 2021-10-20
         * */
        showConfirm(input) {
            this.confirmInfo = input.msg;
            this.$refs.confirmDialog.show();
        },
        /**
         * 流程内-品鉴酒融合-校验返回 选择物资的弹框展示-开瓶
         * @auther songyanrong
         * @date 2021-10-20
         * */
        showChooseMaterialsOpenDialog() {
            this.$refs.confirmDialog.hide();
            this.$refs.chooseMaterialsOpenDialog.show();
        },
        /**
         * 流程内-品鉴酒融合-校验返回 选择物资的弹框展示-赠送
         * @auther songyanrong
         * @date 2021-10-20
         * */
        showChooseMaterialsDialog() {
            this.$refs.confirmDialog.hide();
            this.$refs.chooseMaterialsDialog.show();
        },
        /**
         * 流程内-品鉴酒融合-校验返回 点击选物资弹窗-开瓶
         * @auther songyanrong
         * @date 2021-10-20
         * */
        async chooseMaterialsOpen() {
            const param={
                wineTasting : 'N',
                actId: this.activityItem.id,
                prodId : this.codeData.rows.prodId
            }
            if(!this.codeData.match) {
                param.attr3 = 'notMatch'
            }
            this.basicOption = new this.AutoList(this, {
                module: 'action/link/actualFee',
                url: {
                    queryByExamplePage: 'action/link/actualFee/queryFeeListDataPage',
                },
                param: param,
                sortOptions: null,
                searchFields: ['prodCode', 'prodName'],
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} arrow="false" style="width: 100%">
                            <link-checkbox val={data.id} toggleOnClickItem style="padding: 10px"/>
                            <view style="width: 100%; display:flex;align-items: center;">
                                <view style="flex:1">
                                    <view style="width: 100%;height: 20px;display: flex;justify-content: space-between">
                                        <view>{data.prodCode}</view>
                                        <view style="font-size: 28rpx;color: #262626;">{data.feePayType}</view>
                                    </view>
                                    <view style="font-size: 28rpx;color: #262626;line-height: 36px;">{data.prodName}</view>
                                    <view class="bottle-info" style="display: flex;justify-content: space-between">
                                        <view>申请<text style="padding-left: 20rpx; color: blue">{data.qty}瓶</text></view>
                                        <view>开瓶<text style="padding-left: 20rpx; color: blue">{data.bottledSum}瓶</text></view>
                                        <view>赠送<text style="padding-left: 20rpx; color: blue">{data.giftedSum}瓶</text></view>
                                    </view>
                                </view>
                            </view>
                        </item>
                    )
                },
            });
            const list = await this.$object(this.basicOption, {
                showInDialog: true,
                pageTitle: '请选择物资',
                dialogProps: {
                    height: '85vh',
                },
            })
            this.associatedCosts.prodCode = list.prodCode;
            this.associatedCosts.prodId = list.prodId;
            this.associatedCosts.materialLineId = list.id;//物资行ID
            this.associatedCosts.prodName = list.prodName;
            this.associatedCosts.feePayType = list.feePayType;
        },
        /**
         * 流程内-品鉴酒融合-校验返回 点击选物资弹窗-赠送
         * @auther songyanrong
         * @date 2021-10-20
         * */
        async chooseMaterials() {
            // 若存在codeDataList，其prodId、match值都相同
            if(!!this.codeDataList && this.codeDataList.length > 0) {
                const listProdId = this.codeDataList.find(item => item.rows);
                if(!!listProdId) {
                    this.listProdId = listProdId.rows.prodId;
                }
                // match的值为布尔值，用some()方法来查找是否存在一个元素的match属性等于true，如果找到，listMatch将为true，否则，listMatch将为false
                // 可确保listMatch的值在match为false时也为false布尔值
                this.listMatch = this.codeDataList.some(item => item.match === true);
            }
            let prodId = '';
            if (Object.keys(this.codeData).length === 0) {
                // 无codeData
                prodId = this.listProdId;
            } else {
                // 有codeData
                prodId = this.codeData.rows.prodId;
            }
            const param = {
                wineTasting: 'N',
                actId: this.activityItem.id,
                prodId: prodId
            }
            if (Object.keys(this.codeData).length !==0 && !this.codeData.match) {
                param.attr3 = 'notMatch'
            }
            if(this.listMatch === false) {
                param.attr3 = 'notMatch'
            }
            this.basicOption = new this.AutoList(this, {
                module: 'action/link/actualFee',
                url: {
                    queryByExamplePage: 'action/link/actualFee/queryFeeListDataPage',
                },
                param: param,
                sortOptions: null,
                searchFields: ['prodCode', 'prodName'],
                slots: {
                    bottom: () => (
                        <TastingWineScanCodeStatistic list={this.basicOption.list}
                                                      nowScanGiftNum={this.nowScanGiftNum}/>
                    )
                },
                hooks: {
                    afterLoad(data) {
                        data.rows.forEach(item => {
                            this.$set(item, 'giftedSum', 0);
                        })
                    },
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} arrow="false" style="width: 100%">
                            {/* <link-checkbox val={data.id} toggleOnClickItem style="padding: 10px"/> */}
                            <view style="width: 100%; display:flex;align-items: center;">
                                <view style="flex:1">
                                    <view style="width: 100%;height: 20px;display: flex;justify-content: space-between">
                                        <view>{data.prodCode}</view>
                                        <view style="font-size: 28rpx;color: #262626;">{data.feePayType}</view>
                                    </view>
                                    <view
                                        style="font-size: 28rpx;color: #262626;line-height: 36px;">{data.prodName}</view>
                                    <view class="bottle-info"
                                          style="display: flex;justify-content: space-between; align-items: center">
                                        <view style="width: 25%">申请
                                            <text style="padding-left: 20rpx; color: blue">{data.qty}瓶</text>
                                        </view>
                                        <view style="width: 25%">开瓶
                                            <text style="padding-left: 20rpx; color: blue">{data.bottledSum}瓶</text>
                                        </view>
                                        <view style="width: 50%; display: flex; align-items: center">
                                            <text>本次赠送</text>
                                            <link-number type="number" v-model={data.giftedSum}
                                                         style="color: blue; margin-left: 20rpx; width: 64px"/>
                                            <text style="color: blue">瓶</text>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </item>
                    )
                },
            });
            let list = []
            await this.$object(this.basicOption, {
                showInDialog: true,
                pageTitle: '请选择物资',
                selected: ' ',
                dialogProps: {
                    dialogClass: 'link-object-dialog wine-scan-code-basic-option-dialog',
                    height: '85vh',
                },
                beforeConfirm: async (edited) => {
                    let count = this.basicOption.list.reduce((prev, item) => {
                        const checkFlag = item.giftedSum > 0
                        prev += !checkFlag ? 0 : (item.giftedSum == undefined ? 0 : Number(item.giftedSum))
                        return prev
                    }, 0)
                    if (count !== this.nowScanGiftNum) {
                        const msg = '本次扫码数量与赠送数量不一致，请检查！'
                        this.$showError(msg)
                        return Promise.reject(msg)
                    }
                    list = [...this.basicOption.list];
                    // this.$refs.chooseMaterialsDialog.hide()
                    // 使用 filter 方法筛选具有非空 giftedSum 属性的对象
                    const filteredList = list.filter(item => item.giftedSum !== undefined && item.giftedSum !== null && item.giftedSum != 0);
                    if(!!this.codeDataList && this.codeDataList.length > 0) {
                        await this.beforeGenerateOther(filteredList);
                    } else {
                        await this.beforeGenerate(filteredList);
                    }
                    //重新查询扫码数据
                    this.$bus.$emit("initCodeScanRecordList");
                    this.codeDataList = [];
                    if (this.$refs.scanCodeTypeRef) {
                        this.$refs.scanCodeTypeRef.hide();
                    }
                    this.$refs.chooseMaterialsDialog.hide();
                },
            });
        },
        /**
         * 批量插入-关联物资-生成扫码数据（箱码/盒码/有rowsOther的情况
         * <AUTHOR>
         * @date 2023-11-06
         * @desc
         * */
        async beforeGenerateOther(filteredList) {
            let codeDataListIndex = 0; // 初始化为0，表示从第一个 this.codeDataList 对象开始
            for (const item of filteredList) {
                // 获取当前对象的 giftedSum 值
                const giftedSum = item.giftedSum;
                console.log('giftedSum', giftedSum);
                for (let i = 0; i < giftedSum; i++) {
                    if (codeDataListIndex < this.codeDataList.length) {
                        const codeData = this.codeDataList[codeDataListIndex];
                        console.log('beforeGenerateOther -> codeData', codeData);

                        this.associatedCosts.prodCode = item.prodCode;
                        this.associatedCosts.prodId = item.prodId;
                        this.associatedCosts.materialLineId = item.id;
                        this.associatedCosts.prodName = item.prodName;
                        this.associatedCosts.feePayType = item.feePayType;
                        this.associatedCosts.giftedSum = item.giftedSum;

                        // 设置其他属性...
                        let descriptionType = "";
                        let isEffective = "";
                        if (this.scanScenario === 'gift') {
                            this.qrCodeStatus = "Gifted";
                        }

                        if (codeData['match']) {
                            descriptionType = 'MatchSuccessfully';
                        } else {
                            if (!codeData['match']) {
                                descriptionType = 'MatchFailed';
                            }
                            if (typeof codeData['match'] === "string") {
                                if (codeData['match'] === 'false1') {
                                    descriptionType = 'MatchFailedEx';
                                }
                            }
                        }

                        let prodId = this.$utils.isNotEmpty(codeData.rows['prodId']) ? codeData.rows['prodId'] : this.associatedCosts.prodId;
                        this.codeProductRecordUpdateProdFlag = !this.$utils.isNotEmpty(codeData.rows['prodId']);
                        isEffective = codeData['isEffective'];

                        this.codeProductRecordUpdateObj.prodCode = codeData.rows['prodCode'];
                        this.codeProductRecordUpdateObj.prodId = codeData.rows['prodId'];
                        this.codeProductRecordUpdateObj.prodName = codeData.rows['prodName'];

                        this.scanRecordsData = {
                            prodId: prodId,
                            prodQrCodeId: codeData.rows['id'],
                            prodQrCode: codeData.rows['qrCodeOut'],
                            prodQrCodeType: 2,
                            hisStatus: codeData.rows['qrCodeStatus'],
                            verifyType: 'FeeVerify',
                            packagingMaterial: codeData.rows['packagingMaterial'],
                            materialLineId: this.associatedCosts.materialLineId,
                            remark: this.associatedCosts.remark,
                            descriptionType: descriptionType,
                            isEffective: isEffective,
                            scanRecordStatus: 'Normalgifted',
                            scanType: 'ActProdScan',
                            scanSubType: 'GiftScan',
                            scanner: this.userInfo.firstName,
                            scannerId: this.userInfo.id,
                            scannerTime: this.$date.format(new Date(), 'YYYY-MM-DD HH:mm:ss'),
                            actId: this.activityItem.id,
                            scanSource: 'StaffSystem',
                            province: this.addressData.province || '',
                            city: this.addressData.city || '',
                            district: this.addressData.district || '',
                            scanAddr: this.addressDataFull || '',
                            giftedSum: this.associatedCosts.giftedSum,
                            caseOrBoxCode: codeData.rows['caseOrBoxCode'],
                            isBulkPickup: codeData.rows['isBulkPickup'],
                            sourceCodeLevel: codeData.rows['sourceCodeLevel'],
                        };
                        await this.generateMultiScanRecords(this.scanRecordsData);
                        codeDataListIndex++; // 移动到下一个 this.codeDataList 对象
                    }
                }
            }
        },
        /**
         * 批量插入-关联物资-生成扫码数据（盖外/无rowsOther的情况
         * <AUTHOR>
         * @date 2023-11-06
         * @desc
         * */
        async beforeGenerate(filteredList) {
            await Promise.all(filteredList.map(async (item) => {
                this.associatedCosts.prodCode = item.prodCode;
                this.associatedCosts.prodId = item.prodId;
                this.associatedCosts.materialLineId = item.id;//物资行ID
                this.associatedCosts.prodName = item.prodName;
                this.associatedCosts.feePayType = item.feePayType;
                this.associatedCosts.giftedSum = item.giftedSum;
                let descriptionType = "";
                let isEffective = "";
                if (this.scanScenario === 'gift') {
                    this.qrCodeStatus = "Gifted";//创建完赠送扫码记录 更新状态为"已赠送" PJ_USER_TYPE
                }
                if (this.codeData['match']) {
                    descriptionType = 'MatchSuccessfully';//匹配状态:匹配成功 MATCH_STATUS
                } else {
                    if (!this.codeData['match']) {
                        descriptionType = 'MatchFailed';//匹配状态:匹配失败 MATCH_STATUS
                    }
                    if (typeof this.codeData['match'] === "string") {
                        if (this.codeData['match'] === 'false1') {
                            descriptionType = 'MatchFailedEx';//匹配状态:匹配失败 MATCH_STATUS
                        }
                    }
                }
                let prodId = "";
                if (this.$utils.isNotEmpty(this.codeData.rows['prodId'])) {
                    prodId = this.codeData.rows['prodId'];
                    this.codeProductRecordUpdateProdFlag = false;
                } else {
                    prodId = this.associatedCosts.prodId;
                    this.codeProductRecordUpdateProdFlag = true;
                }
                isEffective = this.codeData['isEffective'];//直接用后台返回的isEffective字段赋值和match同级。
                this.codeProductRecordUpdateObj.prodCode = this.codeData.rows['prodCode'];//产品编码
                this.codeProductRecordUpdateObj.prodId = this.codeData.rows['prodId'];//产品id
                this.codeProductRecordUpdateObj.prodName = this.codeData.rows['prodName'];//产品名称
                this.scanRecordsData = {
                    prodId: prodId ,//当前物资行的产品id
                    prodQrCodeId: this.codeData.rows['id'],//盖外码id
                    prodQrCode:  this.codeData.rows['qrCodeOut'],//产品码
                    prodQrCodeType: 2,//产品码类型按照盖外码赋值为2
                    hisStatus: this.codeData.rows['qrCodeStatus'],
                    verifyType: 'FeeVerify',
                    packagingMaterial: this.codeData.rows['packagingMaterial'],//是否包材
                    materialLineId: this.associatedCosts.materialLineId,//物资行id
                    remark: this.associatedCosts.remark,//备注
                    descriptionType: descriptionType,
                    isEffective: isEffective,
                    scanRecordStatus: 'Normalgifted',
                    scanType: 'ActProdScan',
                    scanSubType: 'GiftScan',
                    scanner: this.userInfo.firstName,
                    scannerId: this.userInfo.id,
                    scannerTime: this.$date.format(new Date(), 'YYYY-MM-DD HH:mm:ss'),
                    actId: this.activityItem.id,
                    scanSource: 'StaffSystem',
                    province: this.addressData.province || '',
                    city: this.addressData.city || '',
                    district: this.addressData.district || '',
                    scanAddr: this.addressDataFull || '',
                    giftedSum: this.associatedCosts.giftedSum,
                    caseOrBoxCode: this.codeData.rows['caseOrBoxCode'],
                    isBulkPickup: this.codeData.rows['isBulkPickup'],
                    sourceCodeLevel: this.codeData.rows['sourceCodeLevel'],
                }
                await this.generateMultiScanRecords(this.scanRecordsData);
            }));
        },

        /**
         * 批量插入-关联物资-编辑多条物资本次赠送数insert/update
         * <AUTHOR>
         * @date 2023-11-02
         * @desc
         * */
        async generateMultiScanRecords(scanRecordsData) {
            try {
                if (!this.associatedCosts.materialLineId) {
                    this.$message.warn('请先选择费用物资');
                    this.oneFlag = true
                    return
                }
                await this.$http.post('action/link/actScanRecord/insert', this.scanRecordsData, {
                    handleFailed: data => {
                        this.oneFlag = true
                    }
                });
                const updateProd = {
                    prodId: this.$utils.isNotEmpty(this.codeProductRecordUpdateObj.prodId) ? this.codeProductRecordUpdateObj.prodId : this.associatedCosts.prodId,
                    prodCode: this.$utils.isNotEmpty(this.codeProductRecordUpdateObj.prodCode) ? this.codeProductRecordUpdateObj.prodCode : this.associatedCosts.prodCode,
                    prodName: this.$utils.isNotEmpty(this.codeProductRecordUpdateObj.prodName) ? this.codeProductRecordUpdateObj.prodName : this.associatedCosts.prodName,
                }
                //只要无效就不更新产品二维码状态2000
                if (this.scanRecordsData['isEffective'] !== 'N') {
                    //更新产品二维码状态和产品信息
                    const param = {
                        id: this.scanRecordsData.prodQrCodeId,
                        qrCodeStatus: this.qrCodeStatus,
                        prodId: updateProd.prodId,
                        prodCode: updateProd.prodCode,
                        prodName: updateProd.prodName,
                        scanTime: this.scanRecordsData.scannerTime,
                        scanner: this.userInfo.firstName,
                        updateFields: "id,qrCodeStatus,prodId,prodCode,prodName,scanTime,scanner"
                    };
                    await this.$http.post('action/link/codeProductRecord/update', param, {
                        handleFailed: data => {
                            this.oneFlag = true
                        }
                    });
                } else {
                    //更新码数据上的产品的信息
                    await this.$http.post('action/link/codeProductRecord/update', {
                        id: this.scanRecordsData.prodQrCodeId,
                        prodId: updateProd.prodId,
                        prodCode: updateProd.prodCode,
                        prodName: updateProd.prodName,
                        scanTime: this.scanRecordsData.scannerTime,
                        scanner: this.userInfo.firstName,
                        updateFields: "id,prodId,prodCode,prodName,scanTime,scanner"
                    }, {
                        handleFailed: data => {
                            this.oneFlag = true
                        }
                    });
                }
                this.$message.success('扫码数据创建成功,产品二维码数据已更新。');
                this.associatedCosts = {
                    prodCode: "",
                    prodId: "",
                    prodName: "",
                    remark: "",
                    materialLineId: "",
                }// 清空关联费用的对象
                this.codeProductRecordUpdateObj = {prodId: '', prodCode: '', prodName: ''};
                this.codeData = {};
                this.listMatch = null;
                //执行反馈阶段=费用实际。赠送 、开瓶后需要更新产品的实际数量
                if (this.scene === 'actual') {
                     this.$bus.$emit("scanCompleteInitCostList");
                }
                this.oneFlag = true
            } catch (e) {
                console.log('e', e);
            }
        },
        /**
         * 流程内-品鉴酒融合-校验返回 确定时生成扫码记录
         * @auther songyanrong
         * @date 2021-10-20
         * rows: 只匹配一个直接生成扫码记录的场景，扫码返回的二维码对象
         * 说明：如果rows和type没值就是选择物资的场景。有值的话 是匹配到一条产品需要前端直接生成扫码记录的
         * */
        async generateScanRecords(data) {
            if (!this.oneFlag) return
            this.oneFlag = false
            //对象处理放大可以合为一个，但由于考虑后续写到一个方法里判断会比较多，
            // 所以写了俩个处理对象的方法，一个用于仅一个匹配直接生成扫码数据 另外一个用户选择物资后生成扫码数据。
            if (this.$utils.isNotEmpty(data)) {
                await this.dealOnlyOneMatchScanRecords(data);
                //插入扫码记录
                await this.$http.post('action/link/actScanRecord/insert', this.scanRecordsData, {
                    handleFailed: data => {
                        this.oneFlag = true
                    }
                });
                /*this.$aegis.report({
                    msg: '更新产品二维码状态和产品的信息前扫码信息isEffective状态', // 日志提示
                    ext1: JSON.stringify(this.scanRecordsData['isEffective']), // 日志对象
                    trace: 'log' // 日志类型
                });*/
                //只要无效就不更新产品二维码状态
                try {
                    if (this.scanRecordsData['isEffective'] !== 'N') {
                        //更新产品二维码状态和产品的信息
                        await this.$http.post('action/link/codeProductRecord/update', {
                            id: this.scanRecordsData.prodQrCodeId,
                            qrCodeStatus: this.qrCodeStatus,
                            prodId: this.codeProductRecordUpdateObj.prodId,
                            prodCode: this.codeProductRecordUpdateObj.prodCode,
                            prodName: this.codeProductRecordUpdateObj.prodName,
                            scanTime: this.scanRecordsData.scannerTime,
                            scanner: this.userInfo.firstName,
                            updateFields: "id,qrCodeStatus,prodId,prodCode,prodName,scanTime,scanner"
                        }, {
                            handleFailed: data => {
                                this.oneFlag = true
                            }
                        });
                        /*this.$aegis.report({
                            msg: '更新产品二维码状态',
                            ext1: JSON.stringify(this.qrCodeStatus),
                            trace: 'log'
                        });*/
                    } else {
                        //更新码数据上的产品的信息
                        await this.$http.post('action/link/codeProductRecord/update', {
                            id: this.scanRecordsData.prodQrCodeId,
                            prodId: this.codeProductRecordUpdateObj.prodId,
                            prodCode: this.codeProductRecordUpdateObj.prodCode,
                            prodName: this.codeProductRecordUpdateObj.prodName,
                            scanTime: this.scanRecordsData.scannerTime,
                            scanner: this.userInfo.firstName,
                            updateFields: "id,prodId,prodCode,prodName,scanTime,scanner"
                        }, {
                            handleFailed: data => {
                                this.oneFlag = true
                            }
                        });
                        /*this.$aegis.report({
                            msg: '更新码数据上的产品的信息:产品id',
                            ext1: JSON.stringify(this.codeProductRecordUpdateObj.prodId),
                            trace: 'log'
                        });*/
                    }
                    // 更改逻辑为一次插入多条数据，即不需要再多次调用扫码界面
                    // setTimeout(() => {
                    //     this.scanCode(this.scanScenario)
                    // }, 500)
                } catch (e) {
                    this.$aegis.report({
                        msg: '更新产品码数据报错',
                        ext1: JSON.stringify(e),
                        trace: 'log'
                    });
                }
                this.$message.success('扫码数据创建成功,产品二维码数据已更新。');
                this.codeProductRecordUpdateObj = {prodId: '', prodCode: '', prodName: ''};
                //重新查询扫码数据
                this.$bus.$emit("initCodeScanRecordList");
                //执行反馈阶段=费用实际。赠送 、开瓶后需要更新产品的实际数量
                if (this.scene === 'actual') {
                    if (this.scanScenario === 'gift' || this.scanScenario === 'open') {
                        this.$bus.$emit("scanCompleteInitCostList");
                    }
                }
                if (this.$refs.scanCodeTypeRef) {
                    this.$refs.scanCodeTypeRef.hide();
                }
                this.$refs.chooseMaterialsOpenDialog.hide();
                this.oneFlag = true
            } else {
                if (!this.associatedCosts.materialLineId) {
                    this.$message.warn('请先选择费用物资');
                    this.oneFlag = true
                    return
                }
                await this.dealOnlyOneMatchScanRecords();
                //插入扫码记录
                await this.$http.post('action/link/actScanRecord/insert', this.scanRecordsData, {
                    handleFailed: data => {
                        this.oneFlag = true
                    }
                });
                const updateProd = {
                    prodId: this.$utils.isNotEmpty(this.codeProductRecordUpdateObj.prodId) ? this.codeProductRecordUpdateObj.prodId : this.associatedCosts.prodId,
                    prodCode: this.$utils.isNotEmpty(this.codeProductRecordUpdateObj.prodCode) ? this.codeProductRecordUpdateObj.prodCode : this.associatedCosts.prodCode,
                    prodName: this.$utils.isNotEmpty(this.codeProductRecordUpdateObj.prodName) ? this.codeProductRecordUpdateObj.prodName : this.associatedCosts.prodName,
                }
                //只要无效就不更新产品二维码状态
                if (this.scanRecordsData['isEffective'] !== 'N') {
                    //更新产品二维码状态和产品信息
                    const param = {
                        id: this.scanRecordsData.prodQrCodeId,
                        qrCodeStatus: this.qrCodeStatus,
                        prodId: updateProd.prodId,
                        prodCode: updateProd.prodCode,
                        prodName: updateProd.prodName,
                        scanTime: this.scanRecordsData.scannerTime,
                        scanner: this.userInfo.firstName,
                        updateFields: "id,qrCodeStatus,prodId,prodCode,prodName,scanTime,scanner"
                    };
                    await this.$http.post('action/link/codeProductRecord/update', param, {
                        handleFailed: data => {
                            this.oneFlag = true
                        }
                    });
                } else {
                    //更新码数据上的产品的信息
                    await this.$http.post('action/link/codeProductRecord/update', {
                        id: this.scanRecordsData.prodQrCodeId,
                        prodId: updateProd.prodId,
                        prodCode: updateProd.prodCode,
                        prodName: updateProd.prodName,
                        scanTime: this.scanRecordsData.scannerTime,
                        scanner: this.userInfo.firstName,
                        updateFields: "id,prodId,prodCode,prodName,scanTime,scanner"
                    }, {
                        handleFailed: data => {
                            this.oneFlag = true
                        }
                    });
                }
                this.$message.success('扫码数据创建成功,产品二维码数据已更新。');
                this.associatedCosts = {
                    prodCode: "",
                    prodId: "",
                    prodName: "",
                    remark: "",
                    materialLineId: "",
                }// 清空关联费用的对象
                this.codeProductRecordUpdateObj = {prodId: '', prodCode: '', prodName: ''};
                //重新查询扫码数据
                this.$bus.$emit("initCodeScanRecordList");
                //执行反馈阶段=费用实际。赠送 、开瓶后需要更新产品的实际数量
                if (this.scene === 'actual') {
                    if (this.scanScenario === 'gift' || this.scanScenario === 'open') {
                        this.$bus.$emit("scanCompleteInitCostList");
                    }
                }
                this.oneFlag = true
                this.$refs.chooseMaterialsOpenDialog.hide()
            }
        },
        /**
         * 流程内-品鉴酒融合 处理扫码记录对象
         * @auther songyanrong
         * @date 2021-10-27
         * rows: 只匹配一个直接生成扫码记录的场景，扫码返回的二维码对象
         * type: 扫码场景
         * 说明：如果rows和type没值就是选择物资的场景。有值的话 是匹配到一条产品需要前端直接生成扫码记录的
         * */
        async dealOnlyOneMatchScanRecords(data) {
            let descriptionType = "";
            let isEffective = "";
            let scanRecordStatus = "";
            let scanType = "";
            let scanSubType = "";
            if (this.scanScenario === 'gift') {
                scanRecordStatus = 'Normalgifted';//正常出库 扫码状态 SCAN_RECORD_STATUS
                scanType = 'ActProdScan';//活动物资扫码 扫码类型 SCAN_TYPE
                scanSubType = 'GiftScan';//转赠扫码 扫码子类型 值列表类型: SCAN_SUB_TYPE
                this.qrCodeStatus = "Gifted";//创建完赠送扫码记录 更新状态为"已赠送" PJ_USER_TYPE
            }
            if (this.scanScenario === 'open') {
                scanRecordStatus = 'Normalbottled';//正常开瓶 扫码状态 SCAN_RECORD_STATUS
                scanType = 'ActProdScan';//活动物资扫码 扫码类型 SCAN_TYPE
                scanSubType = 'OpenScan';//开瓶扫码 扫码子类型 值列表类型: SCAN_SUB_TYPE
                this.qrCodeStatus = "Bottled";//创建完赠送扫码记录 更新状态为"已开瓶" PJ_USER_TYPE
            }
            //只一个产品匹配的场景
            if (this.$utils.isNotEmpty(data)) {
                if (data['match']) {
                    descriptionType = 'MatchSuccessfully';//匹配状态:匹配成功 MATCH_STATUS
                } else {
                    if (!data['match']) {
                        descriptionType = 'MatchFailed';//匹配状态:匹配失败 MATCH_STATUS
                    }
                    if (typeof data['match'] === "string") {
                        if (data['match'] === 'false1') {
                            descriptionType = 'MatchFailedEx';//匹配状态:匹配失败 MATCH_STATUS
                        }
                    }
                }
                isEffective = data['isEffective'];//直接用后台返回的isEffective字段赋值和match同级。
                this.codeProductRecordUpdateObj.prodCode = data.rows['prodCode'];//产品编码
                this.codeProductRecordUpdateObj.prodId = data.rows['prodId'];//产品id
                this.codeProductRecordUpdateObj.prodName = data.rows['prodName'];//产品名称
                this.scanRecordsData = {
                    prodId: data.rows['prodId'],//产品id
                    prodQrCodeId: data.rows['id'],//产品码id
                    prodQrCode: this.scanScenario === 'open' ? data.rows['qrCodeIn'] : data.rows['qrCodeOut'],//产品码
                    prodQrCodeType: this.scanScenario === 'open' ? 1 : 2,//开瓶为盖外码1
                    hisStatus: data.rows.qrCodeStatus,
                    verifyType: 'FeeVerify',
                    materialLineId: data.rows['materialLineId'],//物资行id
                    packagingMaterial: data.rows['packagingMaterial'],//是否包材
                    descriptionType: descriptionType,
                    isEffective: isEffective,
                    scanRecordStatus: scanRecordStatus,
                    scanType: scanType,
                    scanSubType: scanSubType,
                    scanner: this.userInfo.firstName,
                    scannerId: this.userInfo.id,
                    scannerTime: this.$date.format(new Date(), 'YYYY-MM-DD HH:mm:ss'),
                    actId: this.activityItem.id,
                    scanSource: 'StaffSystem',
                    province: this.addressData.province || '',
                    city: this.addressData.city || '',
                    district: this.addressData.district || '',
                    scanAddr: this.addressDataFull || '',
                    isBulkPickup: data.rows['isBulkPickup'],
                    sourceCodeLevel: data.rows['sourceCodeLevel']
                }
            } else {
                //需要选择物资的场景
                if (this.codeData['match']) {
                    descriptionType = 'MatchSuccessfully';//匹配状态:匹配成功 MATCH_STATUS
                } else {
                    if (!this.codeData['match']) {
                        descriptionType = 'MatchFailed';//匹配状态:匹配失败 MATCH_STATUS
                    }
                    if (typeof this.codeData['match'] === "string") {
                        if (this.codeData['match'] === 'false1') {
                            descriptionType = 'MatchFailedEx';//匹配状态:匹配失败 MATCH_STATUS
                        }
                    }
                }
                let prodId = "";
                if (this.$utils.isNotEmpty(this.codeData.rows['prodId'])) {
                    prodId = this.codeData.rows['prodId'];
                    this.codeProductRecordUpdateProdFlag = false;
                } else {
                    prodId = this.associatedCosts.prodId;
                    this.codeProductRecordUpdateProdFlag = true;
                }
                isEffective = this.codeData['isEffective'];//直接用后台返回的isEffective字段赋值和match同级。
                this.codeProductRecordUpdateObj.prodCode = this.codeData.rows['prodCode'];//产品编码
                this.codeProductRecordUpdateObj.prodId = this.codeData.rows['prodId'];//产品id
                this.codeProductRecordUpdateObj.prodName = this.codeData.rows['prodName'];//产品名称
                this.scanRecordsData = {
                    prodId: prodId,//产品id
                    prodQrCodeId: this.codeData.rows['id'],//产品码id
                    prodQrCode: this.scanScenario === 'open' ? this.codeData.rows['qrCodeIn'] : this.codeData.rows['qrCodeOut'],//产品码
                    prodQrCodeType: this.scanScenario === 'open' ? 1 : 2,//产品码类型按照盖外码赋值为2
                    hisStatus: this.codeData.rows.qrCodeStatus,
                    verifyType: 'FeeVerify',
                    packagingMaterial: this.codeData.rows['packagingMaterial'],//是否包材
                    materialLineId: this.associatedCosts.materialLineId,//物资行id
                    remark: this.associatedCosts.remark,//备注
                    descriptionType: descriptionType,
                    isEffective: isEffective,
                    scanRecordStatus: scanRecordStatus,
                    scanType: scanType,
                    scanSubType: scanSubType,
                    scanner: this.userInfo.firstName,
                    scannerId: this.userInfo.id,
                    scannerTime: this.$date.format(new Date(), 'YYYY-MM-DD HH:mm:ss'),
                    actId: this.activityItem.id,
                    scanSource: 'StaffSystem',
                    province: this.addressData.province || '',
                    city: this.addressData.city || '',
                    district: this.addressData.district || '',
                    scanAddr: this.addressDataFull || '',
                    isBulkPickup: this.codeData.rows['isBulkPickup'],
                    sourceCodeLevel: this.codeData.rows['sourceCodeLevel']
                }
            }
        },
    }
}
</script>

<style lang="scss">
.tasting-wine-scan-code-component {

    .movable-area {
        pointer-events: none;
        width: 100vw;
        height: calc(100vh - 228px);
        position: fixed;
        top: 96px;

        .movable-btn {
            pointer-events: auto;
            width: 108px;
            height: 108px;

            .link-fab-button {
                right: 0;
                bottom: 0 !important;
            }
        }
    }

    .scan-main {
        background: #FFFFFF;
        border-radius: 12px;
        width: 540px;
        height: 400px;
        margin: auto;

        .scan-title {
            width: 100%;
            height: 100px;
            font-family: PingFangSC-Regular;
            font-size: 32px;
            color: #333333;
            letter-spacing: 0;
            text-align: center;
            line-height: 100px;
        }

        .scan-type {
            width: 100%;
            height: 300px;
            padding-top: 50px;
            display: flex;
            justify-content: space-around;

            .scan-item {
                text-align: center;
                padding-top: 30px;

                .scan-type-item {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                    text-align: center;
                    line-height: 28px;
                    padding-top: 20px;
                }
            }
        }
    }

    .choose-materials-view {
        background: #ffffff;
        border-radius: 16px;
        width: 100%;

        .choose-materials-column {
            @include flex();
            @include direction-column();

            .tips {
                font-size: 24px;
                color: #666666;
                padding-left: 24px;
            }

            .materials-column-content {
                padding: 20px 0;
                border-bottom: 1px solid #eeeeee;

                .choose {
                    display: flex;

                    .icon-red {
                        color: red;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        width: 24px;
                    }

                    .choose-prod {
                        flex: 1;
                        font-size: 30px;
                        display: flex;
                        justify-content: space-between;
                    }
                }

                .prod-content {
                    font-size: 25px;
                    padding-left: 24px;

                    .head {
                        padding: 20px 0;
                        color: #5a5a5a;
                        display: flex;
                        justify-content: space-between;
                    }
                }
            }

            .label {
                padding: 32px 0 24px 24px;
                font-family: PingFangSC-Regular;
                font-size: 30px;
                color: #595959;
                letter-spacing: 0;
                line-height: 28px;
            }

            .value {
                font-family: PingFangSC-Regular, serif;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
                line-height: 28px;
                width: 100%;

                .link-input .link-input-content {
                    width: 100%;
                    text-align: left;
                    padding-left: 24px;
                }
            }
        }
    }
}

.wine-scan-code-basic-option-dialog {
    .link-sticky-bottom {
        bottom: calc(env(safe-area-inset-bottom) + 120px) !important;
        padding: 16px !important;
    }
}
</style>
