<template>
    <link-page class="interactive-config-v">
        <!--互动配置-->
        <view>
          <line-title v-if="titleShow" title="互动配置" :buttonName="(!$utils.isEmpty(pageSource) && editFlag && pageSource !== 'activityAudit' && parentData.source !== 'PC' && !isSalesAreaManager) ? '编辑' : ''" @tap="edit"/>
          <view v-if="!$utils.isEmpty(arrNodes)">
            <view class="interactive-config" v-for="(item,index) in arrNodes" :key="index">
                <view style="width: 100%;height: 12px"></view>
                <view class="activity-type" :style="{'background-image': 'url(' + $imageAssets.activityBgImage + ')'}"
                      v-if="item.evetype === 'LotteryTicket' || item.evetype === 'WheelCamp' || item.evetype === 'RoundWheel' || item.evetype === 'Sales' || item.evetype === 'SignIn' || item.evetype === 'Invitation' || item.evetype === 'SlotMachine' || item.evetype === 'SmashGoldenEgg' || item.evetype === 'BlindBox' || item.evetype === 'IceTrue' || item.evetype === 'ExpansionRule' || item.evetype === 'Message'  || item.evetype === 'WxMessage' || item.evetype === 'Questionnaire' || item.evetype === 'TreasureChest'">
                    <view class="type-title">
                        {{item['text'] || item['label']}}
                    </view>
                    <view class="activity-info">
                        <view class="info-v">
                            <view v-if="!$utils.isEmpty(item.interactionItem) && (item.interactionItem['status'] === 'Active'||item.interactionItem['status'] === 'Closed')">
                                <view v-if="item.interactionItem['interactionFormat'] === 'RedPacket'"
                                      style="width: 100%;min-height: 90px;">
                                    <view class="list-item">
                                        <view class="name">
                                            <view class="left">互动开始时间：</view>
                                            <view class="right">{{item.interactionItem['beginTime']}}</view>
                                        </view>
                                        <view class="name">
                                            <view class="left">互动结束时间：</view>
                                            <view class="right">{{item.interactionItem['endTime']}}</view>
                                        </view>
<!--                                        <view class="name">-->
<!--                                            <view class="left">授权方式：</view>-->
<!--                                            <view class="right">{{item.interactionItem['licenseMode'] |-->
<!--                                                lov('LICENSE_MODE')}}-->
<!--                                            </view>-->
<!--                                        </view>-->
                                        <view class="name" v-if="item.interactionItem['loc']">
                                            <view class="left">是否授权地理位置：</view>
                                            <view class="right" v-if="item.interactionItem['loc'] === 'Y'">{{item.interactionItem['loc'] |
                                                lov('LOCATION_COLLECTION')}}({{item.interactionItem['locationRange']}}m)
                                            </view>
                                            <view class="right" v-else>{{item.interactionItem['loc'] |
                                                lov('LOCATION_COLLECTION')}}
                                            </view>
                                        </view>
                                        <view class="name">
                                            <view class="left">{{item.interactionItem['couponsName']}}</view>
                                            <view class="right">{{item.interactionItem['prizeNo']}}份</view>
                                        </view>
                                    </view>
                                </view>
                                <!--砸金蛋、大转盘、大转盘圆盘、老虎机、签到、报名、盲盒、冰鉴正宗、抽奖规则拓展-->
                                <view v-if="item.interactionItem['interactionFormat'] === 'LotteryTicket' || item.interactionItem['interactionFormat'] === 'WheelCamp' || item.interactionItem['interactionFormat'] === 'RoundWheel' || item.interactionItem['interactionFormat'] === 'SlotMachine' || item.interactionItem['interactionFormat']  === 'SmashGoldenEgg' || item.interactionItem['interactionFormat']  === 'SignIn' || item.interactionItem['interactionFormat']  === 'Invitation' || item.interactionItem['interactionFormat'] === 'BlindBox' || item.interactionItem['interactionFormat'] === 'IceTrue' || item.interactionItem['interactionFormat'] === 'ExpansionRule' || item.interactionItem['interactionFormat'] === 'Questionnaire' || item.interactionItem['interactionFormat'] === 'TreasureChest'"
                                      style="width: 100%;min-height: 45px;overflow-y: auto;">
                                    <view class="list-item">
                                        <view class="name">
                                            <view class="left" style="white-space: pre-line;width: 100%;">互动开始时间：{{item.interactionItem['beginTime']}}</view>
                                        </view>
                                        <view class="name">
                                            <view class="left" style="white-space: pre-line;width: 100%;">互动结束时间：{{item.interactionItem['endTime']}}</view>
                                        </view>
                                        <view class="name">
                                            <view class="left" style="white-space: pre-line;width: 100%;">{{item['appLabel']}}
                                            </view>
                                        </view>
                                    </view>
                                </view>
                                <view class="view-ision">
                                    <view class="left-circle"></view>
                                    <view class="line"></view>
                                    <view class="right-circle"></view>
                                </view>
                                <view class="activity-btn" v-if="pageSource === 'view'">
                                    <!--调查问卷-->
                                    <view class="btn-v"
                                        v-if="(parentData.status === 'Published' || parentData.status === 'Processing' || parentData.status === 'ActualAmount' || parentData.status === 'Closed') && item.interactionItem['interactionFormat'] === 'Questionnaire'"
                                        @tap="checkInteractionRecord(item)">
                                    <view class="title">问卷明细</view>
                                  </view>
                                   <!--红包类-->
                                    <view class="btn-v"
                                          v-if="(parentData.status === 'Published' || parentData.status === 'Processing' || parentData.status === 'ActualAmount' || parentData.status === 'Closed') && item.interactionItem['interactionFormat'] === 'RedPacket'"
                                          @tap="checkOutTheWinners('hongbao',item)">
                                        <view class="title">领取明细</view>
                                    </view>
                                    <!--砸金蛋、大转盘、老虎机、盲盒、冰鉴正宗、抽奖拓展规则-->
                                    <view class="btn-v"
                                          v-if="(parentData.status === 'Published' || parentData.status === 'Processing' || parentData.status === 'ActualAmount' || parentData.status === 'Closed')
                                          && (item.interactionItem['interactionFormat'] === 'LotteryTicket' || item.interactionItem['interactionFormat'] === 'WheelCamp' || item.interactionItem['interactionFormat'] === 'RoundWheel' || item.interactionItem['interactionFormat'] === 'SlotMachine' || item.interactionItem['interactionFormat'] === 'SmashGoldenEgg' || item.interactionItem['interactionFormat'] === 'BlindBox' || item.interactionItem['interactionFormat'] === 'IceTrue' || item.interactionItem['interactionFormat'] === 'ExpansionRule' || item.interactionItem['interactionFormat'] === 'TreasureChest')"
                                          @tap="checkOutTheWinners('zhongjiang',item)">
                                        <view class="title">中奖情况</view>
                                    </view>
                                    <!--签到、报名-->
                                    <view class="btn-v"
                                          v-if="(parentData.status === 'Published' || parentData.status === 'Processing' || parentData.status === 'ActualAmount' || parentData.status === 'Closed')
                                            && (item.interactionItem['interactionFormat'] === 'Invitation' || item.interactionItem['interactionFormat'] === 'SignIn')"
                                          @tap="checkInteractionRecord(item)">
                                      <view class="title">{{ item.interactionItem['interactionFormat'] === 'SignIn' ? '查看签到情况' : '查看邀请情况'}}</view>
                                    </view>

                                    <view class="btn-v"
                                          v-if="(parentData.status === 'Published' || parentData.status === 'Processing' || parentData.status === 'Closed') && (item.interactionItem['interactionFormat'] === 'LotteryTicket' || item.interactionItem['interactionFormat'] === 'WheelCamp' || item.interactionItem['interactionFormat'] === 'RoundWheel' || item.interactionItem['interactionFormat'] === 'RedPacket' || item.interactionItem['interactionFormat'] === 'SignIn' || item.interactionItem['interactionFormat'] === 'Invitation' || item.interactionItem['interactionFormat'] === 'SlotMachine' || item.interactionItem['interactionFormat'] === 'SmashGoldenEgg' || item.interactionItem['interactionFormat'] === 'BlindBox' || item.interactionItem['interactionFormat'] === 'IceTrue' || item.interactionItem['interactionFormat'] === 'ExpansionRule' || item.interactionItem['interactionFormat'] === 'Questionnaire' || item.interactionItem['interactionFormat'] === 'TreasureChest')"
                                          @tap="viewInvitationCode(item.interactionItem,item)"
                                    >
                                        <view class="title">活动码查看</view>
                                    </view>
                                    <view class="btn-v"
                                          v-if="(item.interactionItem['drawingAlgorithm'] === 'DrawByTime' || item.interactionItem['drawingAlgorithm'] === 'ByProbability') && (parentData.status === 'Published' || parentData.status === 'Processing' || parentData.status === 'Closed') && (item.interactionItem['interactionFormat'] === 'LotteryTicket' || item.interactionItem['interactionFormat'] === 'WheelCamp' || item.interactionItem['interactionFormat'] === 'RoundWheel' || item.interactionItem['interactionFormat'] === 'SlotMachine' || item.interactionItem['interactionFormat'] === 'SmashGoldenEgg' || item.interactionItem['interactionFormat'] === 'BlindBox' || item.interactionItem['interactionFormat'] === 'IceTrue' || item.interactionItem['interactionFormat'] === 'TreasureChest')"
                                          @tap="viewBlackOrWhiteList(item.interactionItem, parentData.status)"
                                    >
                                        <view class="title">黑白名单</view>
                                    </view>
                                    <!--调查问卷-->
                                    <view class="btn-v"
                                          v-if="(parentData.status === 'Published' || parentData.status === 'Processing' || parentData.status === 'Closed') && item.interactionItem['interactionFormat'] === 'Questionnaire'"
                                          @tap="setQuestionnaireSendConfig(item.interactionItem, parentData.status)">
                                        <view class="title">发送设置</view>
                                    </view>
                                    <!--绑定个人码-->
                                    <view class="btn-v"
                                          v-if="(parentData.status === 'Published' || parentData.status === 'Processing') && (item.interactionItem['interactionFormat'] === 'LotteryTicket' || item.interactionItem['interactionFormat'] === 'WheelCamp' || item.interactionItem['interactionFormat'] === 'RoundWheel' || item.interactionItem['interactionFormat'] === 'RedPacket' || item.interactionItem['interactionFormat'] === 'SignIn' || item.interactionItem['interactionFormat'] === 'Invitation' || item.interactionItem['interactionFormat'] === 'SlotMachine' || item.interactionItem['interactionFormat'] === 'SmashGoldenEgg' || item.interactionItem['interactionFormat'] === 'BlindBox' || item.interactionItem['interactionFormat'] === 'IceTrue' || item.interactionItem['interactionFormat'] === 'ExpansionRule' || item.interactionItem['interactionFormat'] === 'Questionnaire' || item.interactionItem['interactionFormat'] === 'TreasureChest')"
                                          @tap="bindInteractionCode(item)"
                                    >
                                        <view class="title">{{item.interactionItem.mcActQrHeadId ? '查看个人码' : '绑定个人码'}}</view>
                                    </view>
                                </view>
                            </view>
                            <view v-if="item.messageItem && item.messageItem['msgType'] === 'Message'" style="width: 100%;min-height: 45px;overflow-y: auto;">
                                <view class="list-item">
                                    <view class="name">
                                        <view class="left" style="white-space: pre-line;width: 100%;">{{item['appLabel']}}
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
          </view>
        </view>
        <view>
            <link-dialog ref="imageShare" position="poster" :initial="true">
                <view :style="{'background-image': 'url('+ $imageAssets.adornImg + ')'}" style="width: 90vw;height: 90px;background-repeat: no-repeat;background-size: 100% 100%;
                border-radius: 10px 10px 0 0;background-color: white">
                    <view style="font-weight:500;font-size: 16px;color: #FFFFFF;letter-spacing: 0;text-align: center;line-height: 90px;">
                        {{currentInteractiveInfo.label}}
                    </view>
                </view>
                <view style="width: 100%;height: 248px;text-align: center;background: white">
                    <image style="padding-top: 44px;width: 160px;height: 160px" :src="imgUrl.src"
                           mode="widthFix"></image>
                </view>
                <view style="width: 100%;padding-top: 10px;padding-bottom: 5px;height: 35px;background: white">
                    <view style="position: absolute;border: 1px #DADEE9 dashed;margin-top: 8px;width: 100%;"></view>
                </view>
                <view style="width: 100%;height: 60px;background: white;border-radius: 0 0 10px 10px; justify-content: space-around; display: flex;" v-if="showButton">
                    <link-button mode="stroke" @tap="bindInteractionCode(currentInteractiveInfo)">绑定个人码</link-button>
                    <link-button mode="stroke" @tap="saveShareImg">保存到相册</link-button>
                </view>
                <view style="width: 100%;height: 60px;background: white;border-radius: 0 0 10px 10px; justify-content: center;display: flex;" v-else>
                    <link-button mode="stroke" @tap="saveShareImg">保存到相册</link-button>
                </view>
            </link-dialog>
        </view>
    </link-page>
</template>

<script>
    import {ROW_STATUS} from "../../../../utils/constant";
    import Taro from "@tarojs/taro";
    import {LovService} from "link-taro-component";
    import LineTitle from "../../../lzlj/components/line-title";

    export default {
        name: "interactive-config",
      components: {LineTitle},
      props: {
            //是否需要展示底部btn-弃用
            btnShow: {
                type: Boolean,
                default: false
            },
            interactiveConfigRequire: {
                type: Boolean,
                default: false
            },
            //是否需要展示title
            titleShow: {
                type: Boolean,
                default: false
            },
            //页面来源 -
            // 1、执行反馈环节 executiveFeedback
            // 2、other 活动的其他信息(ps:这是一个页面)
            // 3、preview 活动预览界面而来
            // 4、view 活动查看界面查看而来
            // 5、审批或小程序消息进去 为空
            // 6、activityAudit 活动稽核
            pageSource: {
                type: String,
                default: ''
            },
            //图--弃用
            typeBg: {
                type: String,
                default: ''
            },
            //父对象-活动对象
            parentData: {
                type: Object,
                default: {},
            },
        },
        computed: {
            arrNodes() {//活动的配置信息
                if (!this.$utils.isEmpty(this.arrNodesData)) {
                    return this.arrNodesData
                } else {
                    return [];
                }
            },
            isSalesAreaManager: function () {
                return ['CityManager', 'SalesRegionManager', 'SalesAreaManager',].includes(Taro.getStorageSync('token').result.positionType)
            }
        },
        data() {
            const arrNodesData = [];
            const parentId = this.parentData.id;
            let editFlag = false;//是否可以编辑基础信息
            const imgUrl = {
                src: "",
            };
            //活动状态 MC_STATUS : 新建
            //审批状态 APRO_STATUS : 未提交、已拒绝 的活动能够编辑；
            if (this.parentData.status === 'New' && (this.parentData.aproStatus === 'New' || this.parentData.aproStatus === 'Refused')) {
                editFlag = true;
            }
            const currentInteractiveInfo = {};
            return {
                showButton: false,  // 是否展示绑定活动二维码按钮
                currentInteractiveInfo,
                arrNodesData,
                parentId,//父ID
                editFlag,
                imgUrl,
                activityOption: new this.AutoList(this, { // 选择绑定活动列表
                    module: this.$env.appURL + '/interaction/link/qRHeader',
                    searchFields: ['qrName'],
                    param: {
                        oauth: 'MY_POSTN_ONLY',
                        filtersRaw: [
                            { id: 'qrType', property: 'qrType', operator: 'in', value: '[McAct]' },
                            { id: 'qrForm', property: 'qrForm', operator: '=', value: 'QrCode' },
                            { id: 'status', property: 'status', operator: '=', value: 'Active' }
                        ]
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} arrow="false" style="padding: 14px 10px;">
                                <link-checkbox val={data.id} toggleOnClickItem slot="thumb" style="padding-right: 0;"/>
                                <view style="-webkit-box-sizing: border-box;box-sizing: border-box;display: flex;width: 100%;-webkit-flex-direction: row;-ms-flex-direction: row;flex-direction: row;align-items: center;">
                                    <view style="width: 100%;display: flex;flex: 1;flex-direction: column;justify-content: space-between;align-items: center;overflow: hidden;">
                                        <view style="width: 100%;height: 32px;display:flex;justify-content: space-between; align-items:center;">
                                            <view style="width: auto;background: #A6B4C7;border-radius: 4px;line-height: 25px;font-size: 14px;color: #FFFFFF;letter-spacing: 0;line-height: 20px;padding: 1px 4px;">{data.id}</view>
                                            <view style="background: #2F69F8;box-shadow: 0 1.5px 2px 0 rgba(47, 105, 248, 0.35);min-width: 30px;color: white;letter-spacing: 0;white-space: nowrap;text-align: right;text-decoration: none;height: 9px;transform: skew(-30deg, 0);display: flex;justify-content: center;align-items: center;border-radius: 3px;padding: 5px 8px;margin-right: 5px;">
                                                <view style="font-size: 10px; transform: skew(30deg, 0);">{LovService.filter(data.status, 'QR_CODE_STATUS')}</view>
                                            </view>
                                        </view>
                                        <view style="width: 100%;line-height: 32px;display:flex;justify-content: space-between; align-items:center;">
                                            <view style="font-size: 16px;color: #262626;font-weight: 500;text-overflow: ellipsis;overflow: hidden;width: 80%;white-space:nowrap;">{data.qrName}</view>
                                        </view>
                                        <view style="width: 100%;line-height: 32px;font-size: 12px;display: flex;color: #333333;">
                                            <view style="color: #999999;margin-right: 5px;display: flex;white-space: nowrap;">当前关联互动: </view><view style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;color:#999999;">{data.interactionName || '无'}</view></view>
                                    </view>
                                </view>
                            </item>
                        )
                    }
                })
            }
        },
        async created() {
            await this.initInteractionItem();
        },
        mounted() {
            this.$bus.$on('refreshInteractionItem', async () => {
                await this.refreshInteractionItemFun();
            })
        },
        methods: {
            /**
             * @createdBy 曾宇
             * @date 2022/12/28
             * @methods: setQuestionnaireSendConfig
             * @description: 问卷发送设置
             **/
            setQuestionnaireSendConfig(interaction, marketStatus) {
                this.$nav.push('/pages/lj-market-activity/market-activity/questionnaire-send-list/send-record-list-page', {
                    data: {
                        interactionId: interaction.id,
                        marketActivityId: interaction.marketActivityId,
                        marketStatus: marketStatus
                    }
                })
            },
            /**
             * @desc 查看黑白名单
             * <AUTHOR>
             * @date 2022/6/15 10:14
             **/
            viewBlackOrWhiteList (interaction, marketStatus) {
                this.$nav.push('/pages/lj-market-activity/market-activity/white-or-black/white-or-black-edit-page', {
                    data: {interactionId: interaction.id, marketActivityId: interaction.marketActivityId, marketStatus: marketStatus, drawingAlgorithm: interaction.drawingAlgorithm},
                })
            },
            async refreshInteractionItemFun(){
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.parentData.id
                });
                let actJson = data.result.actJson && JSON.parse(data.result.actJson).nodes;

                if (!this.$utils.isEmpty(actJson)) {
                    const text = this.$utils.deepcopy(actJson);
                    for (const item of text) {
                        if (this.$utils.isNotEmpty(item.backBaseId) && (item.evetype === 'LotteryTicket' || item.evetype === 'WheelCamp'
                            || item.evetype === 'Sales' || item.evetype === 'SignIn' || item.evetype === 'RoundWheel'
                            || item.evetype === 'Invitation' || item.evetype === 'SmashGoldenEgg'
                            || item.evetype === 'SlotMachine' || item.evetype === 'BlindBox'
                            || item.evetype === 'IceTrue' || item.evetype === 'ExpansionRule'
                            || item.evetype === 'Questionnaire' || item.evetype === 'TreasureChest')) {
                            const data = await this.$http.post(this.$env.appURL + '/interaction/link/interaction/queryById', {
                                id: item.backBaseId
                            });
                            let appLabel = '';
                            if (item.evetype !== 'Questionnaire') {
                                const loc = await this.$lov.getNameByTypeAndVal('S_IF', data.result.loc);
                                if (item.evetype === 'SignIn' || item.evetype === 'Invitation') {
                                    if (loc) {
                                        if ( data.result.loc === 'Y' && data.result.locationRange > 0) {
                                            appLabel = `是否授权地理位置：${loc}(${data.result.locationRange}m)\n`;
                                        } else {
                                            appLabel = `是否授权地理位置：${loc}\n`;
                                        }
                                    }
                                    this.$set(item, 'appLabel', appLabel);
                                } else if (item.evetype === 'ExpansionRule') {
                                    appLabel = `中奖人数上限：${data.result.qty}\n 关联互动：${data.result.componentName} \n`;
                                    this.$set(item, 'appLabel', appLabel);
                                } else {
                                    if (loc) {
                                        if (data.result.loc === 'Y' && data.result.locationRange > 0) {
                                            appLabel = `是否授权地理位置：${loc}(${data.result.locationRange}m)\n`;
                                        } else {
                                            appLabel = `是否授权地理位置：${loc}\n`;
                                        }
                                    }
                                    this.$set(item, 'appLabel', appLabel);
                                }
                            } else {
                                if (data.result.componentType=== 'Reward') {
                                    appLabel = `问卷名称：${data.result.surveyName} \n关联互动：${data.result.componentName} \n`;
                                } else {
                                    appLabel = `问卷名称：${data.result.surveyName} \n`;
                                }
                                this.$set(item, 'appLabel', appLabel);
                            }
                            this.$set(item, 'interactionItem', data.result);
                        } else if (item.evetype === 'Message' && item.backBaseId) {
                            const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/messageRecord/queryById', {
                                id: item.backBaseId
                            });
                            if (data.success && data.result) {
                                let appLabel = ''
                                const sendMethod = await this.$lov.getNameByTypeAndVal('MSG_SEND_METH', data.result.sendMethod);
                                if (data.result.sendMethod === 'Manual') {
                                    appLabel = `短信发送方式：${sendMethod}\n`;
                                } else {
                                    appLabel = `短信发送方式：${sendMethod}\n 短信发送时间：${data.result.sendDate} \n`;
                                }
                                this.$set(item, 'appLabel', appLabel);
                                this.$set(item, 'messageItem', data.result);
                            }
                        } else if (item.evetype === 'WxMessage' && item.backBaseId) {
                            const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/wxMsgRecord/queryById', {
                                id: item.backBaseId
                            });
                            if (data.success && data.result) {
                                let appLabel = ''
                                const sendType = await this.$lov.getNameByTypeAndVal('MSG_SEND_METH', data.result.sendType);
                                if (data.result.sendType === 'Manual') {
                                    appLabel = `发送方式：${sendType}\n 授权互动：${data.result.assemblyName}\n 关联互动：${data.result.authoInteraction}\n `;
                                } else {
                                    appLabel = `发送方式：${sendType}\n 发送时间：${data.result.sendTime} \n 授权互动：${data.result.assemblyName}\n 关联互动：${data.result.authoInteraction}\n `;
                                }
                                this.$set(item, 'appLabel', appLabel);
                                this.$set(item, 'messageItem', data.result);
                            }
                        }
                    }
                  this.arrNodesData = text;
                }else{
                    this.arrNodesData = [];
                }
            },
            async edit() {
                //2021-08-04考虑多人操作的场景
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.parentData.id
                });
                if (data.result.status === 'New' && (data.result.aproStatus === 'New' || data.result.aproStatus === 'Refused')) {
                    this.editFlag = true;
                } else {
                    this.$message.warn('活动已被更新，当前活动状态和审批状态不允许编辑互动配置，请返回列表重新查询活动数据。');
                    return ;
                }
                if (data.result.templateId) {
                    this.$store.commit('macTemplate/setMacTemplateId', data.result.templateId);
                }
                this.$nav.push('/pages/lj-market-activity/market-activity/marketing-activity-model/marketing-activity-model-list-page', {
                    data: this.parentData,
                    interactiveConfigRequire: this.interactiveConfigRequire,
                    templateName: data.result.templateName || '',
                    pageFrom: 'MarketingActivityItem'
                });
            },
            /**
             *  @description: 查询互动活动信息
             *  @author: songyanrong
             *  @date: 2020/9/01
             */
            async initInteractionItem() {
                if (this.$utils.isEmpty(this.parentData)) {
                    return;
                }
                let actJson = this.parentData.actJson && JSON.parse(this.parentData.actJson).nodes;
                console.log(actJson, 'actJson')
                if (!this.$utils.isEmpty(actJson)) {
                    const text = this.$utils.deepcopy(actJson);
                    for (const item of text) {
                        if (this.$utils.isNotEmpty(item.backBaseId) && (item.evetype === 'LotteryTicket' || item.evetype === 'WheelCamp'
                            || item.evetype === 'Sales' || item.evetype === 'SignIn' || item.evetype === 'RoundWheel'
                            || item.evetype === 'Invitation' || item.evetype === 'SmashGoldenEgg'
                            || item.evetype === 'SlotMachine' || item.evetype === 'BlindBox'
                            || item.evetype === 'IceTrue' || item.evetype === 'ExpansionRule'
                            || item.evetype === 'Questionnaire' || item.evetype === 'TreasureChest')) {
                            const data = await this.$http.post(this.$env.appURL + '/interaction/link/interaction/queryById', {
                                id: item.backBaseId
                            });
                            let appLabel = '';
                            if (item.evetype !== 'Questionnaire') {
                                const loc = await this.$lov.getNameByTypeAndVal('S_IF', data.result.loc);
                                if (item.evetype === 'SignIn' || item.evetype === 'Invitation') {
                                    if (loc) {
                                        if ( data.result.loc === 'Y' && data.result.locationRange > 0) {
                                            appLabel = `是否授权地理位置：${loc}(${data.result.locationRange}m)\n`;
                                        } else {
                                            appLabel = `是否授权地理位置：${loc}\n`;
                                        }
                                    }
                                    this.$set(item, 'appLabel', appLabel);
                                } else if (item.evetype === 'ExpansionRule') {
                                    appLabel = `中奖人数上限：${data.result.qty}\n 关联互动：${data.result.componentName} \n`;
                                    this.$set(item, 'appLabel', appLabel);
                                } else {
                                    if (loc) {
                                        if (data.result.loc === 'Y' && data.result.locationRange > 0) {
                                            appLabel = `是否授权地理位置：${loc}(${data.result.locationRange}m)\n`;
                                        } else {
                                            appLabel = `是否授权地理位置：${loc}\n`;
                                        }
                                    }
                                    this.$set(item, 'appLabel', appLabel);
                                }
                            } else {
                                if (data.result.componentType=== 'Reward') {
                                    appLabel = `问卷名称：${data.result.surveyName} \n关联互动：${data.result.componentName} \n`;
                                } else {
                                    appLabel = `问卷名称：${data.result.surveyName} \n`;
                                }
                                this.$set(item, 'appLabel', appLabel);
                            }
                            this.$set(item, 'interactionItem', data.result);
                        } else if (item.evetype === 'Message' && item.backBaseId) {
                            const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/messageRecord/queryById', {
                                id: item.backBaseId
                            });
                            if (data.success && data.result) {
                                let appLabel = ''
                                const sendMethod = await this.$lov.getNameByTypeAndVal('MSG_SEND_METH', data.result.sendMethod);
                                if (data.result.sendMethod === 'Manual') {
                                    appLabel = `短信发送方式：${sendMethod}\n`;
                                } else {
                                    appLabel = `短信发送方式：${sendMethod}\n 短信发送时间：${data.result.sendDate} \n`;
                                }
                                this.$set(item, 'appLabel', appLabel);
                                this.$set(item, 'messageItem', data.result);
                            }
                        } else if (item.evetype === 'WxMessage' && item.backBaseId) {
                            const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/wxMsgRecord/queryById', {
                                id: item.backBaseId
                            });
                            if (data.success && data.result) {
                                let appLabel = ''
                                const sendType = await this.$lov.getNameByTypeAndVal('MSG_SEND_METH', data.result.sendType);
                                if (data.result.sendType === 'Manual') {
                                    appLabel = `发送方式：${sendType}\n 授权互动：${data.result.assemblyName}\n 关联互动：${data.result.authoInteraction}\n `;
                                } else {
                                    appLabel = `发送方式：${sendType}\n 发送时间：${data.result.sendTime} \n 授权互动：${data.result.assemblyName}\n 关联互动：${data.result.authoInteraction}\n `;
                                }
                                this.$set(item, 'appLabel', appLabel);
                                this.$set(item, 'messageItem', data.result);
                            }
                        }
                    }
                  this.arrNodesData = text;
                }
            },
            /**
             * 查看中奖情况和查看红包领取情况共用
             * scene：hongbao zhongjiang
             * <AUTHOR>
             * @date 2020-08-18
             * */
            checkOutTheWinners(scene,item) {
                this.$nav.push('/pages/lj-market-activity/market-activity/check-out-the-winners-page', {
                    parentId: item.interactionItem.id,
                    assemblyId: item.interactionItem.assemblyId || '',
                    scene: scene
                })
            },
          /**
           * @desc 查看互动记录
           * <AUTHOR>
           * @date 2021/5/13 15:28
           **/
           checkInteractionRecord (item) {
             this.$nav.push('/pages/lj-market-activity/market-activity/interaction-record/interaction-record-page', {
               data: {id: item.interactionItem.id, surveyId: item.interactionItem.surveyId || '', evetype: item.evetype},
             })
           },
            /**
             * @desc 确认绑定前是否提示
             * <AUTHOR>
             * @date 2023/3/16 14:54
             **/
            async confirmBind (data, interactionItem) {
                if (!this.$utils.isEmpty(data.interactionName)) {
                    this.$dialog({
                        title: '提示',
                        content: `当前二维码已绑定【${data.interactionName}】，请确认是否更换为当前互动`,
                        cancelButton: true,
                        onConfirm: async () => {
                            await this.bindOrUnbindData(data, interactionItem);
                        },
                        onCancel: () => {
                        }
                    })
                } else {
                    await this.bindOrUnbindData(data, interactionItem);
                }
            },
            /**
             * @desc 解除绑定或确认绑定
             * <AUTHOR>
             * @date 2023/3/14 09:53
             **/
            async bindOrUnbindData (data, interactionItem) {
                const updateData = await this.$http.post(this.$env.appURL + '/interaction/link/qRHeader/bindInteraction', {
                    headId: data.id,
                    interactionId: interactionItem.id,
                    bindFlag: 'Binding'
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('绑定失败！' + response.result);
                    }
                });
                if (updateData.success) {
                    this.$message.success('绑定成功');
                    this.$refs.imageShare.hide();
                    this.initInteractionItem();
                }
            },
            /**
             * @desc 绑定个人码
             * <AUTHOR>
             * @date 2023/4/25 17:57
             **/
            async bindInteractionCode (item) {
                this.currentInteractiveInfo = item;
                if (!this.$utils.isEmpty(item.interactionItem.mcActQrHeadId)) {
                    this.$nav.push('/pages/lj-consumers/activity-qrcode/activity-qrcode-list-page', {
                        pageFrom: 'InteractiveConfig',
                        callback: () => {
                            this.initInteractionItem();
                        }
                    });
                    return;
                }
                const list = await this.$object(this.activityOption, {pageTitle: "个人二维码绑定设置"});
                setTimeout(() => {
                    this.confirmBind(list, item.interactionItem)
                }, 800);
            },
            /**
             * 分享活动码
             * <AUTHOR>
             * @date 2020-08-18
             */
            viewInvitationCode(interactionItem, item) {
                this.currentInteractiveInfo = item;
                this.createMiniProgramCode(interactionItem);
            },
            /**
             * 生成分享二维码
             *  <AUTHOR>
             *   @date        2020-07-28
             * */
            async createMiniProgramCode(interactionItem) {
                this.$utils.showLoading();
                let params = {};
                params = {
                    interactionId: interactionItem.id
                };
                const queryData = await this.$http.post(this.$env.appURL + '/interaction/link/qRHeader/viewQrCodesByInteraction', params);
                let imageUrl
                if (queryData.codeUrl) {
                    this.currentInteractiveInfo.label = '个人码';
                    if(queryData.codeUrl.includes('http')){
                        imageUrl = queryData.codeUrl
                    }else{
                        imageUrl = this.$env.appImageURL + queryData.codeUrl
                    }
                    this.imgUrl.src = imageUrl;
                    this.showButton = false;
                } else {
                    this.currentInteractiveInfo.label = '活动码';
                    this.imgUrl.src = queryData.miniCodeUrl;
                    this.showButton = true;
                }
                this.$utils.hideLoading();
                this.$refs.imageShare.show();
            },
            /**
             * 保存分享图
             * <AUTHOR>
             * @date 2020-07-28
             */
            async saveShareImg() {
                this.$utils.showLoading();
                const that = this;
                wx.getImageInfo({
                    src: that.imgUrl.src,
                    success: function (ret) {
                        wx.saveImageToPhotosAlbum({
                            filePath: ret.path,
                            success: function (data) {
                            },
                            fail: function (err) {
                                if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
                                    that.$message.info('打开设置窗口');
                                    wx.openSetting({
                                        success(settingdata) {
                                            if (settingdata.authSetting['scope.writePhotosAlbum']) {
                                                that.$message.info('获取权限成功，请再次点击保存图片到相册。')
                                            } else {
                                                that.$message.info('获取权限失败,无法使用该功能，请授权再使用。');
                                            }
                                        }
                                    })
                                }
                            }
                        })
                    }
                });
                this.$utils.hideLoading();
            },
            /**
             *  编辑互动活动
             *
             *  <AUTHOR>
             *  @date        2020-09-17 11:07
             */
            async gotoEditInteraction(item) {
                // 兼容新建跳详情
                const data = await this.$http.post(this.$env.appURL + '/interaction/link/interaction/queryById', {
                    id: item.id,
                });
                item = data.result;
                item.row_status = ROW_STATUS.UPDATE;
                this.$nav.push('/pages/lj-market-activity/market-activity/interaction-edit-page', {
                    item: item,
                    readonlyFlag: this.readonlyFlag,
                    callback: () => {
                        this.initInteractionItem();
                    }
                });
            },
        }
    }
</script>

<style lang="scss">
    .interactive-config-v {
        clear: both;

        .button-private {
            display: inline;
            padding-left: 0;
            padding-right: 0;
            box-sizing: border-box;
            font-size: 18px;
            text-align: center;
            text-decoration: none;
            line-height: 2.55555556;
            border-radius: 5px;
            -webkit-tap-highlight-color: transparent;
            overflow: hidden;
            color: #000;
        }

        .button-private:after {
            width: 200%;
            height: 200%;
            position: absolute;
            top: 0;
            left: 0;
            border: none;
            -webkit-transform: scale(.5);
            transform: scale(.5);
            -webkit-transform-origin: 0 0;
            transform-origin: 0 0;
            box-sizing: border-box;
        }
        .interactive-config {

            .activity-type {
                background-repeat: no-repeat;
                background-size: 100%;
                -moz-background-size: 100%;
                margin: 0 24px 24px 24px;

                .type-title {
                    line-height: 92px;
                    text-align: center;
                    color: white;
                    font-size: 28px;
                }

                .activity-info {
                    background: white;
                    border-radius: 0 0 16px 16px;

                    .info-v {
                        width: 100%;
                        //min-height: 230px;

                        .list-item {
                            .name {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #262626;
                                letter-spacing: 0;
                                display: flex;

                                .left {
                                    display: inline-block;
                                    width: 50%;
                                    float: left;
                                    padding-left: 30px;
                                    padding-top: 20px;
                                }

                                .right {
                                    display: inline-block;
                                    width: 50%;
                                    float: right;
                                    text-align: right;
                                    padding-right: 30px;
                                    padding-top: 20px;
                                }
                            }

                            .date {
                                font-size: 28px;
                                color: #6B7378;
                                margin-top: 20px;
                            }
                        }

                        .title {
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 76px;
                        }

                        .x-v {
                            font-family: PingFangSC-Regular;
                            width: 10%;
                            float: left;
                            text-align: right;
                            font-size: 28px;
                            color: #BFBFBF;
                            letter-spacing: 0;
                            line-height: 76px;
                        }

                        .num {
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #000000;
                            letter-spacing: 0;
                            text-align: right;
                            line-height: 76px;
                            width: 9%;
                            float: left;
                            padding-right: 24px;

                        }

                        .view-ision {
                            width: 702px;
                            padding-top: 20px;
                            padding-bottom: 10px;
                            height: 70px;

                            .left-circle {
                                position: absolute;
                                width: 30px;
                                height: 30px;
                                background: #F2F2F2;
                                border-radius: 50%;
                                left: 0;
                                margin-left: 15px;
                            }

                            .line {
                                position: absolute;
                                border: 2px #F2F2F2 dashed;
                                margin-top: 15px;
                                width: 97%;
                                float: left;
                            }

                            .right-circle {
                                position: absolute;
                                width: 30px;
                                height: 30px;
                                background: #F2F2F2;
                                border-radius: 50%;
                                right: 0;
                                margin-right: 15px;
                            }
                        }

                        .activity-btn {
                            width: 100%;
                            background: white;
                            display: flex;
                            justify-content: center;
                            .btn-v {
                                border: 2px solid #2F69F8;
                                border-radius: 8px;
                                margin-right: 20px;
                                margin-bottom: 34px;
                                height: 76px;
                                width: 28%;

                                .title {
                                    font-family: PingFangSC-Regular;
                                    font-size: 28px;
                                    color: #2F69F8;
                                    letter-spacing: 0;
                                    text-align: center;
                                    line-height: 76px;
                                }
                            }
                            .btn-v:last-child{
                                margin-right: 0;
                            }
                        }
                    }
                }

            }
        }
      /*deep*/.edit {
                padding-right: 16px;
              }
    }
</style>
