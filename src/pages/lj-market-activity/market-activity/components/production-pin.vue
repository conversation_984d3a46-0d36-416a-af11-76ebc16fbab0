<template>
    <link-page>
        <!--活动物资 使用界面：2、预计生产动销 3、实际生产动销 4、产品支付-->
        <view class="production-pin-view" v-if="!haveMarginFlag">
            <view class="item-header">
                <view style="width: 50%;float: left">{{title}}</view>
                <view style="float: left;text-align: right;width: 40%;padding-right: 12px;color: #2F69F8;"
                      @tap="edit"
                      v-if="editFlag && !$utils.isEmpty(pageSource)">
                    编辑
                </view>
            </view>
            <view class="table-v">
                <view class="product-list">
                    <view class="product-list-item" v-for="(item,index) in productionPinList" :key="index">
                        <view class="product-list-item__left">
                            {{item.prodCode}}-{{item.prodName}}
                        </view>
                        <view class="product-list-item__center">
                            <view class="row row-1">
                                {{priceShowFlag?'标价':''}}
                                <view class="num" v-if="priceShowFlag">{{item.basePrice |numCny}}，</view>
                                申请
                                <view class="num">{{item.applyQty}}</view>
                                <!--实际动销时展示的预计动销单位-->
                                <view v-if="item.itemType === 'actual'">
                                    {{item.applyProdUnit|lov('FEE_PROD_UNIT')}}
                                </view>
                                <!--预计动销时的单位-->
                                <view v-if="item.itemType !== 'actual' || $utils.isEmpty(item.applyProdUnit)">
                                    {{item.prodUnit|lov('FEE_PROD_UNIT')}}
                                </view>
                            </view>
                            <view class="row row-2">
                                {{priceShowFlag?'费用核销价格':''}}
                                <link-icon icon="mp-info-lite" status="info" @tap="showTips()"/>
                            </view>
                            <view class="row">
                                <view class="num" v-if="priceShowFlag">{{item.netPrice|numCny}}，</view>
                                <!--                                实际动销展示实际数量-->
                                <view v-if="item.itemType === 'actual'">实际
                                    <view class="num" style="display: inline;">{{item.qty}}</view>
                                    {{item.prodUnit|lov('FEE_PROD_UNIT')}}
                                </view>
                            </view>
                            <view class="row row-3">
                                <view class="count-price" v-if="priceShowFlag">小计
                                    <view class="num" v-if="item.itemType === 'apply'">{{(item.netPrice *
                                        item.applyQty) |numCny}}
                                    </view>
                                    <view class="num" v-if="item.itemType === 'actual'">{{(item.netPrice *
                                        item.qty) |numCny}}
                                    </view>
                                </view>
                            </view>
                        </view>
                        <view class="product-list-item__right"  style="line-height: 168rpx;"
                              v-if="(pageSource === 'executiveFeedback' || (parentData.status === 'New' && pageSource !=='view'))
                              && !$utils.isEmpty(pageSource)&&messageScene!=='costApprovalMsg' && pageSource !== 'activityAudit'">
                            <link-icon icon="icon-edit"
                                       @tap="editProdItem(item)"/>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="production-pin-v" v-else-if="haveMarginFlag">
            <view class="menu-stair" style="margin-bottom: 12px">
                <view class="line">
                    <view class="line-top"></view>
                    <view class="line-bottom"></view>
                </view>
                <view class="stair-title">{{title}}</view>
                <view class="edit" @tap="edit"
                      v-if="editFlag && !$utils.isEmpty(pageSource)">
                    编辑
                </view>
            </view>
            <view class="table-v" v-if="productionPinList.length>0">
                <view class="product-list">
                    <view class="product-list-item" v-for="(item,index) in productionPinList" :key="index">
                        <view class="product-list-item__left">
                            {{item.prodCode}}-{{item.prodName}}
                        </view>
                        <view class="product-list-item__center">
                            <view class="row row-1">
                                {{priceShowFlag?'标价':''}}
                                <view class="num" v-if="priceShowFlag">{{item.basePrice | numCny}}，</view>
                                申请
                                <view class="num">{{item.applyQty}}</view>
                                <!--实际动销时展示的预计动销单位-->
                                <view v-if="item.itemType === 'actual'">
                                    {{item.applyProdUnit|lov('FEE_PROD_UNIT')}}
                                </view>
                                <!--预计动销时的单位-->
                                <view v-if="item.itemType !== 'actual' || $utils.isEmpty(item.applyProdUnit)">
                                    {{item.prodUnit|lov('FEE_PROD_UNIT')}}
                                </view>
                            </view>
                            <view class="row row-2">
                                {{priceShowFlag?'费用核销价格':''}}
                                <link-icon icon="mp-info-lite" status="info" @tap="showTips()"/>
                            </view>
                            <view class="row">
                                <view class="num" v-if="priceShowFlag">{{item.netPrice|numCny}}，</view>
                                <!--                                实际动销展示实际数量-->
                                <view v-if="item.itemType === 'actual'">实际
                                    <view class="num" style="display: inline;">{{item.qty}}</view>
                                    {{item.prodUnit|lov('FEE_PROD_UNIT')}}
                                </view>
                            </view>
                            <view class="row row-3">
                                <view class="count-price" v-if="priceShowFlag">小计
                                    <view class="num" v-if="item.itemType === 'apply'">
                                        {{$utils.numberMul(item.netPrice,item.applyQty) |numCny}}
                                    </view>
                                    <view class="num" v-if="item.itemType !== 'apply' && (item.itemType === 'actual' || $utils.isEmpty(item.applyProdUnit))">
                                        {{$utils.numberMul(item.netPrice,item.qty) |numCny}}
                                    </view>
                                </view>
                            </view>
                        </view>
                        <view class="product-list-item__right"  style="line-height: 168rpx; margin-top: 24rpx !important;"
                              v-if="(pageSource === 'executiveFeedback' || (parentData.status === 'New' && pageSource !=='view'))
                              && !$utils.isEmpty(pageSource)&&messageScene!=='costApprovalMsg' && pageSource !== 'activityAudit'">
                            <link-icon icon="icon-edit"
                                       @tap="editProdItem(item)"/>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <!--调整产品弹框-->
        <link-dialog ref="prodBottom"
                     :noPadding="true"
                     :initial="true"
                     v-model="productDialogFlag"
                     position="bottom"
                     borderRadius="32rpx 32rpx 0 0">
            <view class="model-title">
                <view class="title">编辑</view>
                <view class="iconfont icon-close" @tap="closeDialog"></view>
            </view>
            <link-form>
                <view class="product-name">
                    <view class="name">{{adjustedProductItem.prodName}}</view>
                </view>
                <link-form-item label="单位">
                    <link-lov type="FEE_PROD_UNIT" v-model="adjustedProductItem.prodUnit"></link-lov>
                </link-form-item>
                <link-form-item label="标价" v-if="adjustedProductItem.prodUnit === 'Ping' && priceShowFlag">
                    <link-number-keyboard :precision="2" v-model="adjustedProductItem.saleMiniPrice"
                                          readonly></link-number-keyboard>
                </link-form-item>
                <link-form-item label="标价" v-if="adjustedProductItem.prodUnit === 'Xiang' && priceShowFlag">
                    <link-number-keyboard :precision="2" v-model="adjustedProductItem.salePrice"
                                          readonly></link-number-keyboard>
                </link-form-item>
                <link-form-item label="费用核销价格" arrow v-if="priceShowFlag">
<!--                    <view style="color: #3b4144" @tap="openProdPinNumberKeyboard">{{adjustedProductItem.netPrice}}</view>-->
                    <link-input v-model=adjustedProductItem.netPrice type="digit"/>
                </link-form-item>
                <link-form-item label="预计数量" v-if="adjustedProductItem.itemType === 'apply'">
                    <link-number v-model="adjustedProductItem.applyQty" :min="1"/>
                </link-form-item>
                <link-form-item label="实际数量" v-if="adjustedProductItem.itemType === 'actual'">
                    <link-number v-model="adjustedProductItem.qty" :min="0"/>
                </link-form-item>
            </link-form>
            <view class="blank"></view>
            <link-sticky class="bottom-btn">
                <link-button class="sure-btn" size="normal" @tap="saveAdjustedProduct">确定</link-button>
            </link-sticky>
        </link-dialog>
    </link-page>
</template>

<script>

    import {ROW_STATUS} from "../../../../utils/constant";
    import {FilterService} from "link-taro-component";
    import Taro from "@tarojs/taro";

    export default {
        name: "production-pin",
        props: {
            //是否为有边距类型
            haveMarginFlag: {
                type: Boolean,
                default: false
            },
            //弃用-有边距时是否需要添加按钮
            addBtnFlag: {
                type: Boolean,
                default: false
            },
            //标题
            title: {
                type: String,
                default: "预生产动销"
            },
            //弃用-title:实际数量 字段为:actualNum 0817改为 actualQty
            actualNumFlag: {
                type: Boolean,
                default: false,
            },
            //弃用-title:预计数量 字段为:estimatedNum 0817改为 qty
            estimatedNumFlag: {
                type: Boolean,
                default: false,
            },
            //弃用-title:申请数量 字段为:estimatedNum 0817改为 qty
            applyNumFlag: {
                type: Boolean,
                default: false,
            },
            //弃用-父对象 act : actId cos : costId
            parentType: {
                type: String,
                default: "",
            },
            //父对象-为活动对象
            parentData: {
                type: Object,
                default: {},
            },
            //页面来源 -
            // 1、执行反馈环节 executiveFeedback
            // 2、other 活动的其他信息(ps:这是一个页面)
            // 3、preview 活动预览界面而来
            // 4、view 活动查看界面查看而来
            // 5、审批或小程序消息进去 为空
            // 6、activityAudit 活动稽核
            pageSource: {
                type: String,
                default: ''
            },
            //场景 只有执行反馈环节传 'costApprovalMsg'
            messageScene: {
                type: String,
            }
        },
        data() {
            const productionPinshow = true;
            let editFlag = false;//是否可以编辑
            //页面来源 executiveFeedback 非执行反馈：活动状态 MC_STATUS : 新建 审批状态 APRO_STATUS : 未提交、已拒绝 的活动能够编辑；
            //页面来源 executiveFeedback 执行反馈：活动状态 MC_STATUS : 进行中\已结束 审批状态 APRO_STATUS : 申请审批通过、反馈驳回 的活动能够编辑；
            if ((this.pageSource !== 'executiveFeedback' && this.pageSource !== 'activityAudit' && this.parentData.status === 'New'
                && (this.parentData.aproStatus === 'New' || this.parentData.aproStatus === 'Refused')
            ) || (this.pageSource === 'executiveFeedback' && (this.parentData.status === 'Processing' || this.parentData.status === 'Closed')
                && (this.parentData.aproStatus === 'Approve' || this.parentData.aproStatus === 'Refeedback')
            )) {
                editFlag = true;
            }
            const parentId = this.parentData.id;
            const editNetPrice = 0;
            const orderType = 'estimateorder';//订单类型默认预订单 销售订单 SalesOrder【实际动销】  estimateorder 预订单【预计动销】
            const userInfo = Taro.getStorageSync('token').result;
            return {
                // 费用价格是否展示标识
                priceShowFlag: false,
                userInfo,
                editNetPrice,
                parentId,
                adjustedProductItem: {},//调整的产品信息
                productDialogFlag: false,
                editFlag,
                productionPinshow,
                //预生产动销、实际生产动销、产品列表数据、产品支付界面
                productionPinList: [],
                actualFiltersRaw: [{"id": "itemType", "property": "itemType", "value": "actual"}],
                applyFiltersRaw: [{"id": "itemType", "property": "itemType", "value": "apply"}],
                orderType,
            }
        },
        async created() {
            await this.queryProductionPinList();
            this.priceShowFlag = await this.$utils.getPriceDesensitize(this.userInfo);
        },
        methods: {
            showTips() {
                this.$message.primary('费用核销价格由业务代表填写，为受益客户需占用的产品单价')
            },

            //2021-08-04考虑多人操作的场景..
            async operationalControl (){
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.parentData.id
                });
                if ((this.pageSource !== 'executiveFeedback' && this.pageSource !== 'activityAudit' && data.result.status === 'New'
                    && (data.result.aproStatus === 'New' || data.result.aproStatus === 'Refused')
                ) || (this.pageSource === 'executiveFeedback' && (data.result.status === 'Processing' || data.result.status === 'Closed')
                    && (data.result.aproStatus === 'Approve' || data.result.aproStatus === 'Refeedback')
                )) {
                    return  true;
                } else {
                    return false;
                }
            },
            async openProdPinNumberKeyboard() {
                this.editNetPrice = await this.$numberKeyboard({initValue: this.editNetPrice,precision: 2});
                this.adjustedProductItem.netPrice = this.editNetPrice;
                this.editNetPrice = 0;
            },
            /**
             * 查询列表
             * <AUTHOR>
             * @date 2020-08-12
             * */
            async queryProductionPinList() {
                this.$utils.showLoading();
                //1、有没有实际订单头 有实际订单头的话就查实际动销的产品数据，没有的话查申请
                //2、执行反馈时传attr1后台区分是否需要copy申请动销数据为实际动销
                const actualSaleOrderFiltersRaw = [{
                    "id": "mcActId",
                    "property": "mcActId",
                    "value": this.parentId
                }, {"id": "orderType", "property": "orderType", "value": "SalesOrder"}];
                let actualSaleOrderData;//实际动销订单头
                actualSaleOrderData = await this.$http.post('action/link/saleorder/queryByExamplePage', {
                    filtersRaw: actualSaleOrderFiltersRaw,
                });
                let actualData;//实际动销数据
                if (!this.$utils.isEmpty(actualSaleOrderData.rows)) {
                    this.orderType = 'SalesOrder';
                    actualData = await this.$http.post('action/link/saleorderitem/queryByExamplePage', {
                        filtersRaw: [{"id": "headId", "property": "headId", "value": actualSaleOrderData.rows[0].id}],
                    });
                    this.productionPinList = actualData.rows;
                } else {
                    await this.setDataFun();
                }
                this.$utils.hideLoading();
            },
            async setDataFun() {
                let actualData;//实际动销数据
                if (this.pageSource === 'executiveFeedback') {
                    actualData = await this.$http.post('action/link/saleorderitem/queryActSaleProds', {
                        mcActId: this.parentId,
                        filtersRaw: this.actualFiltersRaw,
                        attr1: 'feedback'
                    });
                } else {
                    actualData = await this.$http.post('action/link/saleorderitem/queryActSaleProds', {
                        mcActId: this.parentId,
                        filtersRaw: this.actualFiltersRaw,
                    });
                }
                if (!this.$utils.isEmpty(actualData.rows)) {
                    this.orderType = 'SalesOrder';
                    this.productionPinList = actualData.rows;
                } else {
                    const applyData = await this.$http.post('action/link/saleorderitem/queryActSaleProds', {
                        mcActId: this.parentId,
                        filtersRaw: this.applyFiltersRaw,
                    });
                    this.productionPinList = applyData.rows;
                    this.orderType = 'estimateorder';
                }
                if (!this.$utils.isEmpty(this.productionPinList)) {
                    this.productionPinshow = true;
                }
            },
            /**
             * 编辑
             * <AUTHOR>
             * @date 2020-08-17
             * */
            async edit() {
                const flag = await this.operationalControl();
                if(!flag){
                    this.$message.warn(`活动已被更新，当前活动状态和审批状态不允许编辑${this.title}，请返回列表重新查询活动数据。`);
                    return ;
                }
                this.$nav.push('/pages/lj-market-activity/market-activity/edit-prod-page', {
                        parentData: this.parentData,
                        parentType: this.parentType,
                        title: this.title,
                        applyNumFlag: this.applyNumFlag,
                        estimatedNumFlag: this.estimatedNumFlag,
                        actualNumFlag: this.actualNumFlag,
                        pageSource: this.pageSource,
                        orderType: this.orderType,
                        callback: async () => {
                            await this.queryProductionPinList()
                        }
                    },
                );
            },
            /**
             * 关闭底部弹窗
             * <AUTHOR>
             * @date 2020-09-02
             * @param param
             */
            closeDialog() {
                this.productDialogFlag = !this.productDialogFlag
            },
            /**
             * 编辑产品信息调整实际价格和数量
             * <AUTHOR>
             * @date 2020-09-02
             * */
            async editProdItem(item) {
                const flag = await this.operationalControl();
                if(!flag){
                    this.$message.warn(`活动已被更新，当前活动状态和审批状态不允许编辑${this.title}，请返回列表重新查询活动数据。`);
                    return ;
                }
                if (!this.$utils.isEmpty(item.saleMiniPrice)) {
                    item.saleMiniPrice = FilterService.num(item.saleMiniPrice)
                }
                if (!this.$utils.isEmpty(item.salePrice)) {
                    item.salePrice = FilterService.num(item.salePrice)
                }
                if (!this.$utils.isEmpty(item.netPrice)) {
                    item.netPrice = FilterService.num(item.netPrice)
                }
                this.adjustedProductItem = item;
                this.$refs.prodBottom.show()
            },
            /**
             * 保存编辑的产品信息
             * <AUTHOR>
             * @date 2020-09-02
             * */
            async saveAdjustedProduct() {
                if(this.$utils.isEmpty(this.adjustedProductItem.netPrice)){
                    this.$message.warn('费用核销价格不能为空，请重新输入');
                    return false;
                }
                const regexp = /^(([1-9][0-9]*)|(([0]\.\d{0,2}|[1-9][0-9]*\.\d{0,2})))$/;
                const regexp2 = /^[0-9]*$/
                const flag = regexp.test(this.adjustedProductItem.netPrice);
                const flag2 = regexp2.test(this.adjustedProductItem.netPrice);
                if(!(flag || flag2)) {
                    this.$message.warn(`费用核销价格为纯数字且不能超过2位小数，请检查。`);
                    return false;
                }
                const updateDate = {
                    id: this.adjustedProductItem.id,
                    prodUnit: this.adjustedProductItem.prodUnit,
                    netPrice: this.adjustedProductItem.netPrice,
                    applyQty: this.adjustedProductItem.applyQty,
                    qty: this.adjustedProductItem.qty,
                    updateFields: "id,prodUnit,netPrice,applyQty,qty"
                };
                await this.$http.post('action/link/saleorderitem/update', updateDate);
                this.$refs.prodBottom.hide();
            },
        }
    }
</script>

<style lang="scss">
    .model-title {
        display: flex;
        margin-left: 24px;

        .title {
            font-family: PingFangSC-Regular, serif;
            font-size: 32px;
            color: #262626;
            letter-spacing: 0;
            text-align: center;
            line-height: 80px;
            height: 80px;
            width: 90%;
            padding-left: 40px;
        }

        .icon-close {
            color: #BFBFBF;
            font-size: 36px;
            line-height: 80px;
            height: 80px;
        }
    }

    .product-name {
        @include flex-start-center;
        @include space-between();
        margin-left: 24px;
        margin-right: 24px;
        margin-top: 24px;

        .name {
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #262626;
            letter-spacing: 0;
            line-height: 28px;
        }

        .unit {
            font-size: 28px;
            color: #8C8C8C;
        }
    }

    .blank {
        height: 204px;
        width: 100%;
    }

    .bottom-btn {
        padding-top: 16px;
        padding-bottom: 34px;

        .all-select {
            height: 96px;
            padding-left: 24px;
            @include flex-start-center();
            width: 50%;
            font-size: 28px;
            color: #595959;
            letter-spacing: 0;
            line-height: 28px;

            .iconfont {
                font-size: 40px;
                color: #BFBFBF;
            }

            .icon-yiwanchengbuzhou {
                color: $color-primary;
            }

            .all-select-text {
                padding-left: 16px;
            }
        }

        .sure-btn {
            width: 340px;
            height: 96px;
            margin-right: 24px;
            margin-left: 24px;
        }
    }

    .production-pin-view {
        background: white;
        margin: 24px;

        .item-header {
            height: 88px;
            width: 100%;
            padding-left: 32px;
            font-size: 28px;
            line-height: 88px;
            color: #262626;
            letter-spacing: 0;
            border-bottom: 2px solid #F2F2F2;
        }


        .table-v {
            padding-bottom: 40px;

            .product-list {
                font-size: 28px;
                color: #262626;
                letter-spacing: 0;
                line-height: 38px;
                background: white;

                .product-list-item {
                    display: flex;
                    align-items: center;
                    padding: 32px 24px;
                    justify-content: flex-start;
                    border-bottom: 2px solid #F2F2F2;

                    &__left {
                        text-align: left;
                        width: 40%;
                    }

                    &__center {
                        width: 60%;
                        display: flex;
                        justify-content: center;
                        flex-direction: column;

                        .row {
                            width: 100%;
                            font-size: 24px;
                            color: #8C8C8C;
                            letter-spacing: 0;
                            line-height: 28px;
                            display: flex;
                            justify-content: flex-end;
                            margin-bottom: 24px;

                            .num {
                                font-size: 28px;
                                color: #000000;
                                letter-spacing: 0;
                                line-height: 28px;
                                padding-left: 4px;
                                padding-right: 4px;
                            }
                        }

                        .row-3 {
                            margin-bottom: 0;

                            .status {
                                line-height: 24px;
                                margin-right: 10px;
                            }

                            .success {
                                color: #2EB3C2;
                            }

                            .fail {
                                color: #FF5A5A;
                            }

                            .count-price {
                                display: flex;

                                .num {
                                    margin-left: 10px;
                                }
                            }
                        }
                    }

                    &__right {
                        text-align: right;
                        width: 5%;
                        color: #2F69F8;
                        height: 130px;
                        line-height: 130px;
                    }
                }
            }
        }

        .table {
            display: table;
            border-collapse: collapse;
            border: 1px solid #ccc;
            width: 100%;
        }

        .table-caption {
            display: table-caption;
            margin: 0;
            padding: 0;
            font-size: 16px;
        }

        .table-column-group {
            display: table-column-group;
        }

        .table-column-16 {
            display: table-column;
            width: 320px;
        }

        .table-column-8 {
            display: table-column;
            width: 160px;
        }

        .table-row-group {
            display: table-row-group;
        }

        .table-row {
            display: table-row;
            height: 80px;
            line-height: 80px

        }

        .table-row-group .table-row:hover, .table-footer-group .table-row:hover {
            background: #f6f6f6;
        }

        .table-cell {
            display: table-cell;
            padding: 0 5px;
            border: 1px solid rgba(47, 105, 248, 0.10);
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #262626;
            letter-spacing: 0;
            line-height: 40px;
            vertical-align: middle;
            text-align: center;
        }

        .table-cell-title {
            display: table-cell;
            padding: 0 5px;
            border: 1px solid rgba(47, 105, 248, 0.10);
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #2F69F8;
            letter-spacing: 0;
            text-align: center;
            line-height: 28px;
            vertical-align: middle;
        }

        .table-header-group {
            display: table-header-group;
            background: rgba(47, 105, 248, 0.07);
            font-weight: bold;
        }

        .table-footer-group {
            display: table-footer-group;
        }
    }

    .production-pin-v {
        .menu-stair {
            width: 100%;
            margin-left: 24px;
            padding-top: 24px;
            @include flex-start-center;

            .line {
                .line-top {
                    width: 8px;
                    height: 16px;
                    background: #3FE0E2;
                }

                .line-bottom {
                    width: 8px;
                    height: 16px;
                    background: #2F69F8;
                }
            }

            .stair-title {
                width: 30%;
                margin-left: 16px;
                font-family: PingFangSC-Semibold, serif;
                font-size: 32px;
                color: #262626;
                letter-spacing: 1px;
                line-height: 32px;
            }

            .edit {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
                line-height: 28px;
                text-align: right;
                width: 58%;
            }
        }

        .table-v {
            margin: 24px;

            .product-list {
                font-size: 28px;
                color: #262626;
                letter-spacing: 0;
                line-height: 38px;
                background: white;
                border-radius: 16px;

                .product-list-item {
                    display: flex;
                    align-items: center;
                    padding: 32px 24px;
                    justify-content: flex-start;
                    border-bottom: 2px solid #F2F2F2;

                    &__left {
                        width: 40%;
                    }

                    &__center {
                        width:60%;
                        display: flex;
                        justify-content: center;
                        flex-direction: column;

                        .row {
                            width: 100%;
                            font-size: 24px;
                            color: #8C8C8C;
                            letter-spacing: 0;
                            line-height: 28px;
                            display: flex;
                            justify-content: flex-end;
                            margin-bottom: 24px;

                            .num {
                                font-size: 28px;
                                color: #000000;
                                letter-spacing: 0;
                                line-height: 28px;
                                padding-left: 4px;
                                padding-right: 4px;
                            }
                        }

                        .row-3 {
                            margin-bottom: 0;

                            .status {
                                line-height: 24px;
                                margin-right: 10px;
                            }

                            .success {
                                color: #2EB3C2;
                            }

                            .fail {
                                color: #FF5A5A;
                            }

                            .count-price {
                                display: flex;

                                .num {
                                    margin-left: 10px;
                                }
                            }
                        }
                    }

                    &__right {
                        text-align: right;
                        width: 5%;
                        color: #2F69F8;
                        height: 130px;
                        line-height: 130px;
                    }
                }
            }
        }

        .table {
            background: white;
            /*display: table;*/
            /*border-collapse: collapse;*/
            /*border: 1rpx solid #ccc;*/
            width: 100%;
            padding: 40px 0 40px 0;
            border-radius: 16px;

        }

        .table-caption {
            display: table-caption;
            margin: 0;
            padding: 0;
            font-size: 16px;
        }

        .table-column-group {
            display: table-column-group;
        }

        .table-column-16 {
            display: table-column;
            width: 320px;
        }

        .table-column-8 {
            display: table-column;
            width: 160px;
        }

        .table-row-group {
            display: table-row-group;
        }

        .table-row {
            display: table-row;
            height: 80px;
            line-height: 80px

        }

        .table-row-group .table-row:hover, .table-footer-group .table-row:hover {
            background: #f6f6f6;
        }

        .table-cell {
            display: table-cell;
            padding: 0 5px;
            border: 1px solid rgba(47, 105, 248, 0.10);
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #262626;
            letter-spacing: 0;
            line-height: 40px;
            vertical-align: middle;
            text-align: center;
        }

        .table-cell-title {
            display: table-cell;
            padding: 0 5px;
            border: 1px solid rgba(47, 105, 248, 0.10);
            font-family: PingFangSC-Regular;
            font-size: 24px;
            color: white;
            letter-spacing: 0;
            text-align: center;
            line-height: 24px;
            vertical-align: middle;
        }

        .table-header-group {
            display: table-header-group;
            background: #6D96FA;
            font-weight: bold;
        }

        .table-footer-group {
            display: table-footer-group;
        }
    }
</style>
