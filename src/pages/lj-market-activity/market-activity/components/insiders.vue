<template>
    <!--内部人员-->
    <view class="insiders-view" v-if="!haveMarginFlag">
        <view class="item-header">
            <view style="width: 40%;float: left">活动执行人员</view>
            <view style="float: left;text-align: right;width: 50%;padding-right: 24rpx;color: #2F69F8;"
                  v-if="!$utils.isEmpty(pageSource) && editFlag && pageSource !== 'activityAudit' && !isSalesAreaManager"
                  @tap="addInsider">添加
            </view>
        </view>
        <view style="border-radius: 8px">
            <list style="margin: 12px">
                <link-swipe-action v-for="(item,index) in insiderList.slice(0,3)" :key="`${item.id}_${insiderList.length}`">
                    <link-swipe-option slot="option"  v-show="!$utils.isEmpty(pageSource) && editFlag && pageSource !== 'activityAudit' && operationFlag && activityItem.postnId !== item.userPostnId"
                                       @tap="handleInsiderDelete(item,index)">删除
                    </link-swipe-option>
                    <item :arrow="false">
                        <view class="insider-list" slot="note">
                            <view class="list-cell">
                                <view class="media-list">
                                    <view class="media-list-body">
                                        <view class="media-list-text-top">
                                            <view class="name">{{item.userName}}</view>
                                            <view class="tel">{{item.empTel}}</view>
                                        </view>
                                        <view class="media-list-text-bottom uni-ellipsis">
                                            <view class="icon-v">
                                                <link-icon icon="icon-gonghaobumenhezhiwei1" size="0.9em"/>
                                            </view>
                                            <view class="num">{{item.userNo}}</view>
                                            <view class="line">|</view>
                                            <view class="dept">{{item.userOrgName}}</view>
                                        </view>
                                        <view class="media-list-text-bottom uni-ellipsis">
                                            <view class="icon-v" style="color: #fff;">
                                                <link-icon icon="icon-gonghaobumenhezhiwei1" size="0.9em"/>
                                            </view>
                                            <view class="post">{{item.postnName}}</view>
                                            <view class="line" v-if="item.staffType">|</view>
                                            <view class="post">{{item.staffType | lov('STAFF_TYPE')}}</view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </item>
                </link-swipe-action>
            </list>
            <list>
                <view @tap="gotoInsidersList()" v-if="insiderTotal>3" :arrow="false">
                    <view class="more">
                        查看全部({{insiderTotal}})>>
                    </view>
                </view>
            </list>
        </view>
    </view>
    <!--内部人员-->
    <view class="insiders-v" v-else-if="haveMarginFlag">
        <view class="menu-stair" style="margin-bottom: 12px">
            <view class="line">
                <view class="line-top"></view>
                <view class="line-bottom"></view>
            </view>
            <view class="stair-title">活动执行人员</view>
            <view class="edit" @tap="addInsider"
                  v-if="!$utils.isEmpty(pageSource) && editFlag && pageSource !== 'activityAudit' && !isSalesAreaManager">添加
            </view>
        </view>
        <view style="border-radius: 8px">
            <list style="margin: 12px">
                <link-swipe-action v-for="(item,index) in insiderList.slice(0,3)" :key="`${item.id}_${insiderList.length}`">
                    <link-swipe-option slot="option" v-show="!$utils.isEmpty(pageSource) && editFlag && pageSource !== 'activityAudit' && operationFlag && activityItem.postnId !== item.userPostnId"
                                       @tap="handleInsiderDelete(item,index)">删除
                    </link-swipe-option>
                    <item :arrow="false">
                        <view class="insider-list" slot="note">
                            <view class="list-cell">
                                <view class="media-list">
                                    <view class="media-list-body">
                                        <view class="media-list-text-top">
                                            <view class="name">{{item.userName}}</view>
                                            <view class="tel">{{item.empTel}}</view>
                                        </view>
                                        <view class="media-list-text-bottom uni-ellipsis">
                                            <view class="icon-v">
                                                <link-icon icon="icon-gonghaobumenhezhiwei1" size="0.9em"/>
                                            </view>
                                            <view class="num">{{item.userNo}}</view>
                                            <view class="line">|</view>
                                            <view class="dept">{{item.userOrgName}}</view>
                                        </view>
                                        <view class="media-list-text-bottom uni-ellipsis">
                                            <view class="icon-v" style="color: #fff;">
                                                <link-icon icon="icon-gonghaobumenhezhiwei1" size="0.9em"/>
                                            </view>
                                            <view class="post">{{item.postnName}}</view>
                                            <view class="line" v-if="item.staffType">|</view>
                                            <view class="post">{{item.staffType | lov('STAFF_TYPE')}}</view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </item>
                </link-swipe-action>
            </list>
        </view>
        <list>
            <view @tap="gotoInsidersList()" v-if="insiderTotal>3" :arrow="false">
                <view class="more">
                    查看全部({{insiderTotal}})>>
                </view>
            </view>
        </list>
    </view>
</template>

<script>
    import Taro from "@tarojs/taro";

    export default {
        name: "insiders",
        props: {
            //是否为有边距类型
            haveMarginFlag: {
                type: Boolean,
                default: false
            },
            //有边距时是否需要添加按钮-弃用
            addBtnFlag: {
                type: Boolean,
                default: false
            },
            //父ID
            parentId: {
                type: String,
                default: "",
            },
            //页面来源 -
            // 1、执行反馈环节 executiveFeedback
            // 2、other 活动的其他信息(ps:这是一个页面)
            // 3、preview 活动预览界面而来
            // 4、view 活动查看界面查看而来
            // 5、审批或小程序消息进去 为空
            // 6、activityAudit 活动稽核
            pageSource: {
                type: String,
                default: ''
            },
            //父对象-活动对象
            parentData: {
                type: Object,
                default: {},
            },
            //是否可以操作删除数据。approvalId为空时可操作
            operationFlag: {
                type: Boolean,
                default: false
            }
        },
        async created() {
            await this.queryInsiderList();
            this.activityItem = this.parentData;
            // await this.queryActivityItemById();
        },
        computed: {
            isSalesAreaManager: function () {
                return ['CityManager', 'SalesRegionManager', 'SalesAreaManager',].includes(Taro.getStorageSync('token').result.positionType)
            }
        },
        data() {
            const insiderTotal = 0;
            let editFlag = false;//是否可以编辑基础信息
            //页面来源 executiveFeedback 非执行反馈：活动状态 MC_STATUS : 新建 审批状态 APRO_STATUS : 未提交、已拒绝 的活动能够编辑；
            //页面来源 executiveFeedback 执行反馈：活动状态 MC_STATUS : 进行中、已结束、已发布 审批状态 APRO_STATUS : 申请审批通过、反馈驳回、反馈撤回 的活动能够编辑；
            if ((this.pageSource !== 'executiveFeedback' && this.pageSource !== 'activityAudit' && this.parentData.status === 'New'
                && (this.parentData.aproStatus === 'New' || this.parentData.aproStatus === 'Refused')
            ) || (this.pageSource === 'executiveFeedback' && (this.parentData.status === 'Processing' || this.parentData.status === 'Closed' || this.parentData.status === 'Published')
            && (this.parentData.aproStatus === 'Approve' || this.parentData.aproStatus === 'Refeedback' || this.parentData.aproStatus === 'RefeedWithdraw')
            )) {
                editFlag = true;
            }
            const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
            const activityItem = {};
            return {
                activityItem,
                userInfo,
                editFlag,
                insiderTotal,//内部人员数据量
                insiderList: [],//内部人员列表
                //内部人员
                insiderOption: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: 'action/link/position/queryUnSelectPage'
                    },
                    sortOptions: null,
                    filterOption: null,
                    searchFields: ['fstName', 'username', 'deptName', 'text'],
                    param: {
                        filtersRaw: [
                            {
                                "id": "actId",
                                "property": "actId",
                                "value": this.parentId
                            },
                            {"id": "id", "property": "id", "value": userInfo.postnId, "operator": "<>"},
                            {id: "isEffective_0", property: "isEffective", value: "Y"},
                        ],
                        attr2: this.parentData.salesRegionId,
                    },
                    hooks: {
                        afterLoad: (data) => {
                            data.rows.forEach(async(item) => {
                                const staffType = await this.$lov.getNameByTypeAndVal('STAFF_TYPE', item.staffType);
                                item.staffType = staffType || item.staffType || '人员类型异常为空';
                            });
                        }
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} arrow="false">
                                <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                                <view
                                    style="display: flex;flex: 1;flex-direction: column;justify-content: space-between;align-items: flex-start;overflow: hidden;">
                                    <view style="width:100%">
                                        <view
                                            style="font-family: PingFangSC-Semibold;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 22px;float: left;">{data.fstName}
                                        </view>
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 22px;float: left;padding-left:8px">{data.empTel}
                                        </view>
                                    </view>
                                    <view style="width:100%;word-wrap: break-word">
                                        <view style="width:7%;float:left;color: #8C8C8C">
                                            <link-icon icon="icon-gonghaobumenhezhiwei1" size="0.9em"/>
                                        </view>
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 22px;float: left;text-align: center;">{data.username}
                                        </view>
                                        <view
                                            style="width: 2%;float: left;text-align: center;line-height: 22px;padding-left:8px;padding-right:8px">|
                                        </view>
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 22px;float: left;text-align: center;">{data.deptName}
                                        </view>
                                    </view>
                                    <view style="width:100%;word-wrap: break-word">
                                        <view style="width:7%;float:left;color: #fff">
                                            <link-icon icon="icon-gonghaobumenhezhiwei1" size="0.9em"/>
                                        </view>
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 22px;float: left;text-align: center;">{data.text}
                                        </view>
                                        <view
                                            style="width: 2%;float: left;text-align: center;line-height: 22px;padding-left:8px;padding-right:8px">|
                                        </view>
                                        <view
                                            style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 22px;float: left;text-align: center;">{data.staffType}
                                        </view>
                                    </view>
                                </view>
                            </item>
                        )
                    },
                }),
            }
        },
        methods: {
            //2021-08-04考虑多人操作的场景
            async operationalControl (){
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.parentData.id
                });
                if ((this.pageSource !== 'executiveFeedback' && this.pageSource !== 'activityAudit' && data.result.status === 'New'
                    && (data.result.aproStatus === 'New' || data.result.aproStatus === 'Refused')
                ) || (this.pageSource === 'executiveFeedback' && (data.result.status === 'Processing' || data.result.status === 'Closed' || data.result.status === 'Published')
                    && (data.result.aproStatus === 'Approve' || data.result.aproStatus === 'Refeedback' || data.result.aproStatus === 'RefeedWithdraw')
                )) {
                    return  true;
                } else {
                    return false;
                }
            },
            async queryActivityItemById() {
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.parentId,
                });
                this.activityItem = {...data.result};
            },
            /**
             * 添加内部人员
             * <AUTHOR>
             * @date 2020-08-07
             * */
            async addInsider() {
                const flag = await this.operationalControl();
                if(!flag){
                    this.$message.warn('活动已被更新，当前活动状态和审批状态不允许添加活动执行人员，请返回列表重新查询活动数据。');
                    return ;
                }
                const list = await this.$object(this.insiderOption, {
                    pageTitle: '活动执行人员',
                    multiple: true,
                    selected: this.insiderList.map(item => item.id)
                });
                let insertDataList = [];
                insertDataList = list.map(item => ({
                    actId: this.parentId,
                    userId: item.userId,
                    userPostnId: item.postnId,
                    userOrgId: item.orgId,
                }));
                await this.$http.post('action/link/actAccompany/batchInsert', insertDataList);
                await this.queryInsiderList();
            },
            /**
             * 删除内部人员
             * <AUTHOR>
             * @date 2020-08-07
             * */
            async handleInsiderDelete(item, index) {
                const flag = await this.operationalControl();
                if(!flag){
                    this.$message.warn('活动已被更新，当前活动状态和审批状态不允许添加活动执行人员，请返回列表重新查询活动数据。');
                    return ;
                }
                await this.$http.post('action/link/actAccompany/deleteById', item);
                await this.queryInsiderList();
            },
            /**
             * 查看更多内部人员
             * <AUTHOR>
             * @date 2020-08-07
             * */
            gotoInsidersList() {
                this.$nav.push('/pages/lj-market-activity/market-activity/insiders-list-page', {
                    parentId: this.parentId,
                    operationFlag: this.operationFlag,
                    editFlag: this.editFlag,
                    pageSource: this.pageSource,
                    activityItem:this.activityItem,
                    callback: async () => {
                        await this.queryInsiderList();
                    }
                })
            },
            /**
             * 查询内部人员列表
             * <AUTHOR>
             * @date 2020-08-13
             * */
            async queryInsiderList() {
                const data = await this.$http.post('action/link/actAccompany/queryByExamplePage', {
                    rows: 3,
                    sort: "created",
                    order: 'desc',
                    pageFlag: true,
                    filtersRaw: [{id: 'actId', property: 'actId', value: this.parentId, operator: '='}]
                });
                this.insiderList = data.rows || [];
                this.insiderTotal = data.total;
            }
        }
    }
</script>

<style lang="scss">
    .insiders-view {
        background: white;
        /*deep*/
        .link-swipe-action {
            position: relative;
            overflow-x: hidden;
            width: 100%;
        }
        .item-header {
            height: 88px;
            width: 100%;
            padding-left: 32px;
            font-size: 28px;
            line-height: 88px;
            color: #262626;
            letter-spacing: 0;
            border-bottom: 2px solid #F2F2F2;
        }

        .insider-list {
            background-color: #FFFFFF;
            position: relative;
            width: 100%;
            display: flex;
            flex-direction: column;

            .list-cell {
                position: relative;
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                align-items: center;

                .media-list {
                    padding: 11px 15px;
                    box-sizing: border-box;
                    display: flex;
                    width: 100%;
                    flex-direction: row;

                    .media-list-body {
                        display: flex;
                        flex: 1;
                        flex-direction: column;
                        justify-content: space-between;
                        align-items: flex-start;
                        overflow: hidden;

                        .media-list-text-top {
                            width: 100%;

                            .name {
                                font-family: PingFangSC-Semibold;
                                font-size: 32px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 44px;
                                float: left;
                            }

                            .tel {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #8C8C8C;
                                letter-spacing: 0;
                                line-height: 44px;
                                width: 40%;
                                float: left;
                                padding-left: 18px;
                            }

                            .right-v {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 44px;
                                text-align: right;
                            }
                        }

                        .media-list-text-bottom {
                            width: 100%;

                            .icon-v {
                                width: 5%;
                                float: left;
                                line-height: 28px;
                            }

                            .num {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 36px;
                                float: left;
                                text-align: center;
                            }

                            .dept {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 36px;
                                float: left;
                                text-align: center;
                            }

                            .post {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 36px;
                                float: left;
                                text-align: center;
                            }

                            .line {
                                width: 2%;
                                float: left;
                                text-align: center;
                                line-height: 36px;
                                padding-left: 16px;
                                padding-right: 16px;
                            }
                        }
                    }
                }
            }
        }

        .more {
            font-family: PingFangSC-Regular;
            width: 100%;
            text-align: center;
            font-size: 28px;
            color: #2F69F8;
            letter-spacing: 0;
            line-height: 76px;
            background-color: #f2f2f2;
        }
    }

    .insiders-v {
        /*deep*/
        .link-swipe-action {
            position: relative;
            overflow-x: hidden;
            width: 100%;
        }
        .menu-stair {
            width: 100%;
            margin-left: 24px;
            padding-top: 40px;
            @include flex-start-center;

            .line {
                clear: both;

                .line-top {
                    width: 8px;
                    height: 16px;
                    background: #3FE0E2;
                }

                .line-bottom {
                    width: 8px;
                    height: 16px;
                    background: #2F69F8;
                }
            }

            .stair-title {
                width: 30%;
                margin-left: 16px;
                font-family: PingFangSC-Semibold, serif;
                font-size: 32px;
                color: #262626;
                letter-spacing: 1px;
                line-height: 32px;
            }

            .edit {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
                line-height: 28px;
                text-align: right;
                width: 58%;
            }
        }

        .insider-list {
            background-color: #FFFFFF;
            position: relative;
            width: 100%;
            display: flex;
            flex-direction: column;

            .list-cell {
                position: relative;
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                align-items: center;

                .media-list {
                    padding: 11px 15px;
                    box-sizing: border-box;
                    display: flex;
                    width: 100%;
                    flex-direction: row;

                    .media-list-body {
                        display: flex;
                        flex: 1;
                        flex-direction: column;
                        justify-content: space-between;
                        align-items: flex-start;
                        overflow: hidden;

                        .media-list-text-top {
                            width: 100%;

                            .name {
                                font-family: PingFangSC-Semibold;
                                font-size: 32px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 44px;
                                float: left;

                            }

                            .tel {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #8C8C8C;
                                letter-spacing: 0;
                                line-height: 44px;
                                width: 40%;
                                float: left;
                                padding-left: 18px;
                            }

                            .right-v {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 44px;
                                text-align: right;
                            }
                        }

                        .media-list-text-bottom {
                            width: 100%;

                            .icon-v {
                                width: 5%;
                                float: left;
                                line-height: 28px;
                            }

                            .num {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 36px;
                                float: left;
                                text-align: center;
                            }

                            .dept {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 36px;
                                float: left;
                                text-align: center;
                            }

                            .post {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 36px;
                                float: left;
                                text-align: center;
                            }

                            .line {
                                width: 2%;
                                float: left;
                                text-align: center;
                                line-height: 36px;
                                padding-left: 16px;
                                padding-right: 16px;
                            }
                        }
                    }
                }
            }
        }

        .more {
            font-family: PingFangSC-Regular;
            width: 100%;
            text-align: center;
            font-size: 28px;
            color: #2F69F8;
            letter-spacing: 0;
            line-height: 76px;
            background-color: #f2f2f2;
        }
    }
</style>
