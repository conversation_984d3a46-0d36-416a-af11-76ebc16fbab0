<template>
  <!--执行反馈-->
  <view class="execution-feedback-v" v-if="pageSource === 'view' || pageSource === 'activityAudit'" id="execution-feedback-v">
    <view class="menu-stair" style="margin-bottom: 12px">
      <view class="line">
        <view class="line-top"></view>
        <view class="line-bottom"></view>
      </view>
      <view class="stair-title">执行反馈</view>
    </view>
    <view class="execution-feedback">
      <!--图片模板无权限-->
      <lnk-no-auth v-if="imgAuthFlag"></lnk-no-auth>
      <view v-if="!imgAuthFlag">
        <view v-for="(bus,index) in processedPictureList" :key="index">
          <view style="margin-top: 10px;font-family: PingFangSC-Semibold, serif;font-size: 14px;
            color: #262626;letter-spacing: 1px;line-height: 32px;text-align: center;background: #ebf1fb">
            {{bus.title | lov('TMPL_SUB_BIZ_TYPE')}}
          </view>
          <view class="pic-area">
            <view v-for="(item,index2) in bus.picTypeList" :key="index2" class="pic-row">
                <display-link-img v-if="bus.title === 'DisplayData' && parentData.protocolId"
                                  :protocolId="parentData.protocolId"
                                  :collectId="collectId"
                                  :signInId="signInId"
                                  :item="item"
                                  :useModuleName="item.ctrlName"
                                  :moduleType="item.ctrlCode"
                                  :picTypeList="pictureAll"></display-link-img>
                <block v-if="bus.title !== 'DisplayData'">
                    <view class="pic-type">
                        {{item.moduleType | lov('FEEDBACK_PHOTO_TYPE')}}
                    </view>
                    <view v-if="item.showInvolve" class="InvolveBox">
                        <link-checkbox v-model="item.involveFlag" disabled :trueValue="true" :falseValue="false"/>
                        <view class="InvolveItem">
                            是否不涉及
                        </view>
                    </view>
                    <view class="iconfont icon-info-circle" style="float: right;margin-right: 10px;font-size: 14px;color: #8C8C8C;margin-top: 12px;"
                          v-if="$utils.isNotEmpty(item.values.placeholder)||$utils.isNotEmpty(item.base.placeholder)"
                          @tap="showPlaceholder(item.values.placeholder,item.base.placeholder)"></view>
                    <view class="pic-list">
                        <view class="uploaded-pic">
                            <view class="pic">
                                <view class="pic-container">
                                    <template v-if="item.dataList.length">
                                        <view class="pic-item" v-for="(data, index) in item.dataList" :key="index">
                                            <view class="pic-bg">
                                                <image :src="data.imgUrl" @tap="clickImg(pictureAll,data)" class="image"/>
                                                <!--                          <image :src="data.imgUrl" @tap="clickImg(item.dataList,index)" class="image"/>-->
                                            </view>
                                            <view class="data-source">{{data.dataSource | lov('PIC_SOURCE')}}</view>
                                            <view class="data-source" style="font-size: 11px;line-height: 12px">{{data.created | date('YYYY-MM-DD HH:mm:ss')}}</view>
                                        </view>
                                    </template>
                                    <view class="pic-item" v-if="!item.dataList.length">
                                        <view class="pic-bg">
                                            <view class="image" style="background: #f2f2f2;"> <link-icon  class="InvolveIcon" icon="icon-jinyong"/></view>
                                        </view>
                                        <view class="data-source">此项不涉及</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view class="supernatant" v-if="item.dataList.length >= 4">
                        <view class="title">
                            共{{item.dataList.length}}张
                        </view>
                    </view>
                </block>
            </view>
          </view>
        </view>
      </view>
      <view v-if="$utils.isNotEmpty(exeFeedbackInfo.comments)" style="padding: 10px;font-size: 14px;">
        照片备注：<view style="color: #8C8C8C;">{{exeFeedbackInfo.comments}}</view>
      </view>
      <view class="line" v-if="!$utils.isEmpty(cashPayList) || !$utils.isEmpty(prodPayList)"></view>
      <!--费用实际-->
      <view class="actual-cost-view" style="margin-top: 12px;"
            v-if="!$utils.isEmpty(cashPayList) || !$utils.isEmpty(prodPayList)">
        <view class="title">
          费用实际
        </view>
        <!--现金类-->
        <view v-for="(cashItem, index) in cashPayList" :key="index">
          <cash-new :parentData="parentData" :cashShow="cashShow" @updateCash="updateCashData" :bthShow="true"
                    :cashItem="cashItem" :pageSource="pageSource" :scene="scene" :radiusTopFlag="true"></cash-new>
        </view>
        <view style="width: 100%;height: 12px"></view>
        <!--产品类-->
        <view v-for="(prodItem, index) in prodPayList" :key="index">
          <prod-new :prodShow="prodShow" @updateProd="updateProdData"
                    :bthShow="true" :scene="scene" :prodItem="prodItem" :radiusTopFlag="true"
                    :parentData="parentData" :pageSource="pageSource"
          ></prod-new>
        </view>
      </view>
        <!--扫码记录-->
        <view>
            <tasting-wine-scan-code-record :edit-flag="!isSalesAreaManager" :actId="parentData.id" :title="'开瓶扫码记录'" :scene="scene" :type="'OpenScan'"></tasting-wine-scan-code-record>
            <tasting-wine-scan-code-record :edit-flag="!isSalesAreaManager" :actId="parentData.id" :title="'赠送扫码记录'" :scene="scene" :type="'GiftScan'"></tasting-wine-scan-code-record>
        </view>
        <!--盖内码-->
        <cover-code :title="'扫码记录'" :parentId="parentData.id" :radiusBottomFlag="true" :parentData="parentData"
                    :operationFlag="false"></cover-code>
    </view>
  </view>
</template>

<script>
  import CashNew from "./cash-new";
  import ProdNew from "./prod-new";
  import CoverCode from "./cover-code";
  import {ComponentUtils} from "link-taro-component";
  import LnkNoAuth from "../../../core/lnk-no-auth/lnk-no-auth";
  import TastingWineScanCodeRecord from "./tasting-wine-scan-code-record"
  import Taro from "@tarojs/taro";
  import DisplayLinkImg from '../../work-order/components/display-link-img';

  export default {
    name: "execution-feedback",
    components: {LnkNoAuth, ProdNew, CashNew, CoverCode, TastingWineScanCodeRecord, DisplayLinkImg},
    props: {
      //页面来源 -
      // 1、执行反馈环节 executiveFeedback
      // 2、other 活动的其他信息(ps:这是一个页面)
      // 3、preview 活动预览界面而来
      // 4、view 活动查看界面查看而来
      // 5、审批或小程序消息进去 为空
      // 6、activityAudit 活动稽核
      pageSource: {
        type: String,
        default: ''
      },
      //场景-实际 actual 、申请 apply
      scene: {
        type: String
      },
      //父对象-活动对象
      parentData: {
        type: Object,
        default: function () {
          return {};
        },
      },
      collectId: {
        type: String
      },
      signInId: {
        type: String
      },
    },
    data() {
      const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
      const cashShow = true;//现金内容是否展示
      const prodShow = true;//产品内容是否展示
      const isSalesAreaManager = ['CityManager', 'SalesRegionManager', 'SalesAreaManager',].includes(userInfo.positionType); //是否是片区经理
        return {
        isSalesAreaManager,
        imgList: [],//图片列表
        prodPayList: [],//产品支付类数据
        cashPayList: [],//现金支付类数据
        cashShow,
        prodShow,
        imgAuthFlag: false,
        busScenePicList: [],//根据业务场景-查询 需要上传的图片类型
        //糅合业务场景需要上传的图片、已经上传的图片数据为一个新的数据，
        // 展示层级为：[{title:'业务场景名称',picTypeList:[{moduleType:'图片类型',dataList:[类型对应的图片数据]}...]}...]
        processedPictureList: [],
        originalPath: [],            // 原图片url数组列表
        scrollLeft: '',
        pictureAll:[],
        imgTimeFlag:false,  //根据时间控制上传照片类型
        exeFeedbackInfo:{},//执行反馈信息
      }
    },
    async created() {
        let feedback_img_time = await this.$utils.getCfgProperty('feedback_img_time');
        if(Date.parse(new Date(feedback_img_time.replace(/-/g, '/'))) <= Date.parse(new Date(this.parentData.created.replace(/-/g, '/')))){
            this.imgTimeFlag = true;
        }
      await this.queryActivityProdAndCostList();
      await this.queryActivityPicList();
      await this.getExeFeedbackInfo();
      //单独保存照片备注后返回到活动详情页时需要重新查询照片备注信息
      this.$bus.$on('updateExeFeedBackComment', async () => {
        await this.getExeFeedbackInfo();
      });
      //执行反馈费用提交之后需要重新查询执行反馈图片和照片备注
     this.$bus.$on('queryExeFeedBackInfo', async () => {
         await this.queryActivityProdAndCostList();
         await this.queryActivityPicList();
         await this.getExeFeedbackInfo();
     })
    },
      mounted() {
          this.$bus.$on('initActivityProdAndCostList', async () => {
              await this.queryActivityProdAndCostList();
          });
          this.$bus.$on('scanCompleteInitCostList', async () => {
              await this.queryActivityProdAndCostList('scanCompleteInitCostList');
          });
      },
    methods: {
      /**
       * desc 获取执行反馈基本信息
       * <AUTHOR>
       * @date 2021-03-31
       * @params
       */
      async getExeFeedbackInfo() {
        try {
          const data = await this.$http.post('action/link/exeFeedback/queryByExamplePage', {
            oauth: 'All',
            sort: 'lastUpdated',
            order: 'desc',
            filtersRaw: [
              {id: 'actId', property: 'actId', value: this.parentData.id},
              {id: 'feedbackType', property: 'feedbackType', value: 'ExecuteFeedback', operator: '='}
              ]
          });
          if (data.success) {
            if(data.rows.length > 0){
              const obj = data.rows[0];
              obj.commentsBackup = obj.comments = obj.comments || '';
              this.exeFeedbackInfo = obj;
            }
          } else {
            this.$nav.error('获取执行反馈基本信息失败：' + data.result);
          }
        } catch (e) {
          this.$nav.error('获取执行反馈基本信息出错' );
        }
      },
      /**
       * 点击图片预览，直接调用imgService服务
       * <AUTHOR>
       * @date 2020-07-09
       * @param index 当前图片对象所属下标
       */
      clickImg (imgList,data) {
        let index  =  imgList.findIndex((imgList) => imgList.imgUrl  === data.imgUrl);
        this.originalPath = [];
        imgList.forEach(async (item) => {
          this.$set(item, 'originalImgUrl', item.imgUrl);
          this.originalPath.push(item.originalImgUrl);
          if (imgList.length === this.originalPath.length) {
            const inOptions = {
              current: this.originalPath[index],
              urls: this.originalPath
            };
          }
        });
        if (this.originalPath.length !== 0) {
          const inOptions = {
            current: this.originalPath[index],
            urls: this.originalPath
          };
          this.$image.previewImages(inOptions);
        }
      },
      /**
       * 删除某个产品
       * <AUTHOR>
       * @date 2020-11-06
       * */
      deleteProdFun(feePayType, dataList) {
        for (let i = 0; i < this.prodAndCostList.Product.length; i++) {
          if (this.prodAndCostList.Product[i].feePayType === feePayType) {
            this.$set(this.prodAndCostList.Product[i], 'dataList', dataList);
          }
        }
        this.$bus.$emit("deleteProdRrefresh", this.prodAndCostList.Product);
      },
      /**
       * 对查询出的图片数据分组
       * <AUTHOR>
       * @date 2020-09-03
       * */
      groupBy(array, f) {
        const groups = {};
        array.forEach(function (o) {
          const group = JSON.stringify(f(o));
          groups[group] = groups[group] || [];
          groups[group].push(o);
        });
        return Object.keys(groups).map(function (group) {
          return groups[group];
        });
      },
      /**
       * 查看物资明细
       * <AUTHOR>
       * @date 2020-08-13
       * */
      gotoGoodsItemPage() {
        this.$nav.push('/pages/lj-market-activity/market-activity/goods-item-page', {});
      },
      /**
       * 查询活动下费用信息
       * 实际费用信息
       * <AUTHOR>
       * @date 2020-08-26
       * */
      async queryActivityProdAndCostList(type) {
          let url = "action/link/actualFee/queryAndGroupData/detail";
          let params = {filtersRaw: [{"id": "actId", "property": "actId", "value": this.parentData.id, "operator": "="}]};
          if(type === 'scanCompleteInitCostList'){
              url = 'action/link/actualFee/queryAndGroupData/edit';
              params = {filtersRaw: [{id: "actId", property: "actId", value: this.parentData.id, operator: "="}],queryType : 'actualQtyUpdate'}
          }
        const data = await this.$http.post(url, params);
        this.cashPayList = data.rows.Money || [];
        this.prodPayList = data.rows.Product || [];
      },
      /**
       * 查询当前活动下的图片,且处理
       * 1、先查询当前活动基础信息-业务场景 维护的需要上传的图片数据
       * 2、查看当前活动已上传的图片数据
       * <AUTHOR>
       * @date 2020-09-03
       * */
      async queryActivityPicList() {
        this.busScenePicList = [] ;
        this.processedPictureList = [];
        this.pictureAll = [];
        this.imgList = [];
        //1、先查询业务场景配置的图片
        if (this.$utils.isEmpty(this.parentData.busScene)) {
          this.$showError("业务场景未维护，请先维护活动基础信息-业务场景");
          return;
        }
        const text = Array.isArray(this.parentData.busScene);
        let busSceneData;
        if (!text) {
          busSceneData = ComponentUtils.toArray(JSON.parse(this.parentData.busScene));
        } else {
          busSceneData = this.parentData.busScene;
        }
        for (let i = 0; i < busSceneData.length; i++) {
          const busSceneInfo = busSceneData[i];
          const data = await this.$utils.getQwMpTemplate('businessScenario', busSceneInfo);
          if (!data.success) {
              this.imgAuthFlag = true;
              break;
          }
          this.imgAuthFlag = false;
          let resultOpt = JSON.parse(data.result);
          let temp = [];//当前业务场景配置的场景图片数组信息
          temp = JSON.parse(resultOpt.conf);
          const busPicData = {
            title: busSceneInfo,
            picTypeList: temp
          };
          this.busScenePicList.push(busPicData);
        }
          let data = [];
          if (this.$utils.isNotEmpty(this.parentData.id)) {
              data = await this.$http.post('action/link/attachment/queryByExamplePage', {
                  sort: 'created',
                  order: 'desc',
                  headId: this.parentData.id,
                  rows: 2000,
                  queryFields: 'id,uploadType,attachmentPath,moduleType,headId,dataSource,created,smallurl'
              });
          }
        //获取腾讯云上的图片
        const groupedData = data.rows.map(
          item => ({
            uploadType:item.uploadType,
            attachmentPath: item.attachmentPath,
            moduleType: item.moduleType,
            headId: item.headId,
            dataSource:item.dataSource,
            created:item.created,
          })
        );
        groupedData.forEach(async (item) => {
          if(item.uploadType === 'cos'){
            let imgUrl = await this.$image.getSignedUrl(item.attachmentPath);
            this.$set(item, 'imgUrl', imgUrl);
          }else{
            this.$set(item, 'imgUrl', item.attachmentPath);
          }
        });
        //分组图片列表
        const picGroup = this.groupBy(groupedData, function (item) {
          return [item.moduleType];
        });
        picGroup.forEach(i => {
          let imgItem = {
            moduleType: "",
            dataList: [],
            base:{},//存放配置的信息
            values:{},//存放配置的信息
          };
          imgItem.moduleType = i[0].moduleType;
          imgItem.dataList = i;
          this.imgList.push(imgItem);
        });
        if(this.parentData.aproStatus === 'Feedback' || this.parentData.aproStatus === 'FeedbackApro'){
        if(!this.$utils.isEmpty(this.imgList) && !this.$utils.isEmpty(this.busScenePicList)){
          for (let i = 0; i < this.busScenePicList.length; i++) {
            const busSceneItem = this.busScenePicList[i];
            let processedPictureObj = {title: busSceneItem.title, picTypeList: []};//配置的图片
              if (busSceneItem.title === 'DisplayData') {
                // 陈列协议图片资料
                                processedPictureObj.picTypeList = busSceneItem.picTypeList;
              } else {
                  for (let j = 0; j < busSceneItem.picTypeList.length; j++) {
                      const filterArr = this.imgList.filter((item1) => item1.moduleType === busSceneItem.picTypeList[j].ctrlCode);
                      if(this.$utils.isNotEmpty(filterArr)){
                          filterArr[0].base = {
                              ...busSceneItem.picTypeList[j].base
                          };
                          filterArr[0].values = {
                              ...busSceneItem.picTypeList[j].values
                          };
                          this.pictureAll = this.pictureAll.concat(filterArr[0].dataList);
                          processedPictureObj.picTypeList = processedPictureObj.picTypeList.concat(filterArr);
                      }
                }
              }
            this.processedPictureList.push(processedPictureObj);
              console.log('processedPictureList', this.processedPictureList);
          }
          //不涉及处理
            this.busScenePicList.forEach((item,index)=>{
                item.picTypeList.forEach((item1,index1)=>{
                if(this.$utils.isNotEmpty(item1.values)){
                    if(item1.values.involve==='true'){
                        if (this.imgList.findIndex((item2)=>{
                            return item1.ctrlCode === item2.moduleType
                        })=== -1 ){
                            this.processedPictureList[index].picTypeList.push({
                                    base: item1.base,
                                    dataList:[],
                                    moduleType: item1.ctrlCode,
                                    values: item1.values,
                                    showInvolve: true,
                                    involveFlag: true
                            })
                        }else if(this.processedPictureList[index].picTypeList[index1].dataList.length){
                           this.processedPictureList[index].picTypeList[index1].showInvolve=true;
                           this.processedPictureList[index].picTypeList[index1].involveFlag=false;
                            }
                        }
                    }
                })
            })

        }
          let fixedPicObj = {title: '其它确认材料', picTypeList: []};//写定的图片
            let filterFixedArr = [];
            if(this.imgTimeFlag){
                 filterFixedArr = this.imgList.filter((item1) =>
                    item1.moduleType === 'DealerCommitPic'
                    || item1.moduleType === 'PayVoucher'
                    || item1.moduleType === 'BillPic'
                    || item1.moduleType === 'GrantReceipt'
                 );
            }else{
                filterFixedArr = this.imgList.filter((item1) =>
                    item1.moduleType === 'DealerCommitPic'
                    || item1.moduleType === 'PayVoucher'
                    || item1.moduleType === 'BillPic'
                    || item1.moduleType === 'PresentOutPic'
                    || item1.moduleType === 'ProdPayPic');
            }
          if(this.$utils.isNotEmpty(filterFixedArr)){
            filterFixedArr[0].base = {
              placeholder:''
            };
            filterFixedArr[0].values = {
              placeholder:''
            };
            fixedPicObj.picTypeList =  fixedPicObj.picTypeList.concat(filterFixedArr);
            filterFixedArr.forEach((item) => {
              this.pictureAll = this.pictureAll.concat(item.dataList);
            });
          }
          //不涉及改造 前端固定写死三种类型 对于传入的参数进行判断
            fixedPicObj = this.processing(fixedPicObj)
          this.processedPictureList.push(fixedPicObj);
        }
        else{
            if(!this.$utils.isEmpty(this.imgList) && !this.$utils.isEmpty(this.busScenePicList)){
                for (let i = 0; i < this.busScenePicList.length; i++) {
                    const busSceneItem = this.busScenePicList[i];
                    const title = await this.$lov.getNameByTypeAndVal('TMPL_SUB_BIZ_TYPE',busSceneItem.title);
                    let processedPictureObj = {title: title, picTypeList: []};//配置的图片
                    for (let j = 0; j < busSceneItem.picTypeList.length; j++) {
                        const filterArr = this.imgList.filter((item1) => item1.moduleType === busSceneItem.picTypeList[j].ctrlCode);
                        if(this.$utils.isNotEmpty(filterArr)){
                            filterArr[0].base = {
                                ...busSceneItem.picTypeList[j].base
                            };
                            filterArr[0].values = {
                                ...busSceneItem.picTypeList[j].values
                            };
                            this.pictureAll = this.pictureAll.concat(filterArr[0].dataList);
                            processedPictureObj.picTypeList = processedPictureObj.picTypeList.concat(filterArr);
                        }
                    }
                    this.processedPictureList.push(processedPictureObj);
                }
            }
            let fixedPicObj = {title: '其它确认材料', picTypeList: []};//写定的图片
            let filterFixedArr = [];
            if(this.imgTimeFlag){
                filterFixedArr = this.imgList.filter((item1) =>
                    item1.moduleType === 'DealerCommitPic'
                    || item1.moduleType === 'PayVoucher'
                    || item1.moduleType === 'BillPic'
                );
            }else{
                filterFixedArr = this.imgList.filter((item1) =>
                    item1.moduleType === 'DealerCommitPic'
                    || item1.moduleType === 'PayVoucher'
                    || item1.moduleType === 'BillPic'
                    || item1.moduleType === 'PresentOutPic'
                    || item1.moduleType === 'ProdPayPic');
            }
            if(this.$utils.isNotEmpty(filterFixedArr)){
                filterFixedArr[0].base = {
                    placeholder:''
                };
                filterFixedArr[0].values = {
                    placeholder:''
                };
                fixedPicObj.picTypeList =  fixedPicObj.picTypeList.concat(filterFixedArr);
                filterFixedArr.forEach((item) => {
                    this.pictureAll = this.pictureAll.concat(item.dataList);
                });
            }
            this.processedPictureList.push(fixedPicObj);
        }
      },
        /**
         * 不涉及处理前端固定类型展示
         * <AUTHOR>
         * @date 2022年8月23日16:36:37
         * */
        processing(fixedPicObj){
            const BillPicIndex = fixedPicObj.picTypeList.findIndex((item)=>
                item.moduleType === 'BillPic'
            )
            if( BillPicIndex !== -1){
                fixedPicObj.picTypeList[BillPicIndex].showInvolve=true;
                fixedPicObj.picTypeList[BillPicIndex].involveFlag=false;
            }else{
                fixedPicObj.picTypeList.push({
                    base:{
                        placeholder:''
                    },
                    dataList:[],
                    moduleType: 'BillPic',
                    values: {
                        placeholder:''
                    },
                    showInvolve: true,
                    involveFlag: true
                })
            }
            const PayVoucherIndex = fixedPicObj.picTypeList.findIndex((item)=>
                item.moduleType === 'PayVoucher'
            )
            if( PayVoucherIndex !== -1){
                fixedPicObj.picTypeList[PayVoucherIndex].showInvolve=true;
                fixedPicObj.picTypeList[PayVoucherIndex].involveFlag=false;
            }else{
                fixedPicObj.picTypeList.push({
                    base:{
                        placeholder:''
                    },
                    dataList:[],
                    moduleType: 'PayVoucher',
                    values: {
                        placeholder:''
                    },
                    showInvolve: true,
                    involveFlag: true
                })
            }
            if(this.imgTimeFlag){
            const GrantReceiptIndex = fixedPicObj.picTypeList.findIndex((item)=>
                item.moduleType === 'GrantReceipt'
            )
            if( GrantReceiptIndex !== -1){
                fixedPicObj.picTypeList[GrantReceiptIndex].showInvolve=true;
                fixedPicObj.picTypeList[GrantReceiptIndex].involveFlag=false;
            }else{
                fixedPicObj.picTypeList.push({
                    base:{
                        placeholder:''
                    },
                    dataList:[],
                    moduleType: 'GrantReceipt',
                    values: {
                        placeholder:''
                    },
                    showInvolve: true,
                    involveFlag: true
                })
            }}
            return fixedPicObj
        },
      /**
       * 更新现金数据
       * <AUTHOR>
       * @date 2020-08-26
       * */
      async updateCashData(feePayType,costId, item) {
        this.cashPayList.forEach(i => {
          if (i.feePayType === feePayType && i.costId === costId) {
            let tempList = [];
            tempList = this.$utils.deepcopy(i.dataList);
            for (let i = 0; i < item.length; i++) {
              tempList.push(item[i]);
            }
            this.$set(i, 'dataList', tempList)
          }
        });
      },
      /**
       * 更新产品数据
       * <AUTHOR>
       * @date 2020-08-26
       * */
      async updateProdData(feePayType,costId, item) {
        this.prodPayList.forEach(i => {
          if (i.feePayType === feePayType && i.costId === costId) {
            let tempList = [];
            tempList = this.$utils.deepcopy(i.dataList);
            for (let i = 0; i < item.length; i++) {
              tempList.push(item[i]);
            }
            this.$set(i, 'dataList', tempList)
          }
        });
      },
      showPlaceholder(valuesPlaceholder,basePlaceholder) {
        /*
        * 照片上传说明取值顺序
        * a:优先使用小程序模板配置的模板控件上的说明
        * b:如果a情况没有值，则使用模板控件上的说明
        * c:如果a、b情况都没有值 那么照片类型旁边的说明图标就隐藏。
        * */
        let msg = "";
        // 模板控件中的图片上传说明
        if(this.$utils.isNotEmpty(basePlaceholder)){
          msg = basePlaceholder
        }
        // 优先使用小程序模板中的图片上传说明
        if(this.$utils.isNotEmpty(valuesPlaceholder)){
          msg = valuesPlaceholder
        }
        this.$message({message: msg})
      },
    }
  }
</script>

<style lang="scss">
  .execution-feedback-v {
    /*deep*/
    .link-icon {
      width: 1.2em;
      height: 1.2em;
      font-size: 1.2em;
      position: absolute;
      display: inline-flex;
      color: inherit;
      -webkit-align-items: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
    }
    .InvolveBox{
        display: inline-block;
        color: #8C8C8C;
        float: right;
        padding-right: 12px;
        padding-top: 20px;
        .InvolveItem{
            padding-left: 48px;
            font-size: 28px;
            line-height: 48px;
        }
    }
      .InvolveIcon{
          color:#BFBFBF;
          height: 140px;
          font-size: 120px;
      }
    .menu-stair {
      width: 100%;
      margin-left: 24px;
      padding-top: 24px;
      @include flex-start-center;

      .line {
        .line-top {
          width: 8px;
          height: 16px;
          background: #3FE0E2;
        }

        .line-bottom {
          width: 8px;
          height: 16px;
          background: #2F69F8;
        }
      }

      .stair-title {
        width: 30%;
        margin-left: 16px;
        font-family: PingFangSC-Semibold, serif;
        font-size: 32px;
        color: #262626;
        letter-spacing: 1px;
        line-height: 32px;
      }
    }

    .execution-feedback {
      background: #FFFFFF;
      border-radius: 16px;
      margin: 24px;

      .pic-area {
        .pic-row {
          width: 100%;
          height: 315px;

          .pic-scroll{
            white-space: nowrap;
            width: 100%;
            .pic-scroll-v{
              display: inline-block;
              width: 140px;
              height: 150px;
              margin-top: 10px;
              margin-left: 10px;

              .pic-bg {
                display:inline-block;
                width: 100%;
                height: 144px;

                .image {
                  width: 145px;
                  height: 144px;
                  border-radius: 16px;
                }
              }
              .data-source{
                font-size: 24px;
                width: 100%;
                height: 40px;
                text-align: center;
                line-height: 40px;
              }
            }
          }

          .pic-type {
            font-family: PingFangSC-Regular;
            line-height: 40px;
            height: 40px;
            background: #2F69F8;
            border-radius: 4px 0 16px 4px;
            font-size: 24px;
            color: #FFFFFF;
            width: auto;
            display: inline-block !important;
            padding: 0 20px;
            margin-top: 24px;
          }

          .pic-list {
            width: 100%;
            height: 240px;

            .uploaded-pic {
              width: 100%;
              height: 188px;
              background-color: #FFFFFF;
              margin-top: 10px;
              padding-bottom: 10px;

              .pic {
                width: 100%;
                height: 240px;
                margin-top: 6px;

                .scroll-bj{
                  width: 100%;
                  height: 240px;
                  //@include flex;
                  white-space: nowrap;
                  margin-left: 8px;
                  .scroll-container{
                    display:inline-block;
                    width: 100%;
                    height: 100%;
                    //@include flex;
                    margin-left: 8px;

                    .pic-item {
                      display:inline-block;
                      //@include flex;
                      @include direction-column;
                      -webkit-flex-shrink: 0;
                      -ms-flex-negative: 0;
                      flex-shrink: 0;
                      width: 146px;
                      height: 144px;
                      margin-left: 24px;

                      .pic-bg {
                        display:inline-block;
                        width: 100%;
                        height: 144px;

                        .image {
                          width: 145px;
                          height: 144px;
                          border-radius: 16px;
                        }
                      }
                      .data-source{
                        font-size: 24px;
                        width: 100%;
                        height: 40px;
                        text-align: center;
                        line-height: 40px;
                      }

                      .pic-copywriter {
                        margin-top: -28px;
                        position: relative;
                        letter-spacing: 1px;
                        font-size: 14px;
                        color: #FFFFFF;
                        line-height: 25px;
                        text-align: center;
                        white-space: nowrap;
                        background-image: linear-gradient(-180deg, rgba(0, 0, 0, 0.00) 0%, rgba(32, 61, 103, 0.40) 100%);
                      }
                    }

                    .pic-item:last-child {
                      padding-right: 16px;
                    }
                  }
                }

                .pic-container {
                  width: 100%;
                  height: 100%;
                  @include flex;
                  margin-left: 8px;
                  overflow-x: scroll;
                  overflow-y: hidden;

                  .pic-item {
                    @include flex;
                    @include direction-column;
                    -webkit-flex-shrink: 0;
                    -ms-flex-negative: 0;
                    flex-shrink: 0;
                    width: 146px;
                    height: 144px;
                    margin-left: 24px;

                    .pic-bg {
                      width: 100%;
                      height: 144px;

                      .image {
                        width: 145px;
                        height: 144px;
                        border-radius: 16px;
                      }
                    }
                    .data-source{
                      font-size: 24px;
                      width: 100%;
                      height: 40px;
                      text-align: center;
                      line-height: 40px;
                    }

                    .pic-copywriter {
                      margin-top: -28px;
                      position: relative;
                      letter-spacing: 1px;
                      font-size: 14px;
                      color: #FFFFFF;
                      line-height: 25px;
                      text-align: center;
                      white-space: nowrap;
                      background-image: linear-gradient(-180deg, rgba(0, 0, 0, 0.00) 0%, rgba(32, 61, 103, 0.40) 100%);
                    }
                  }

                  .pic-item:last-child {
                    padding-right: 16px;
                  }
                }
              }
            }
          }

          .supernatant {
            width: 97px;
            height: 240px;
            background-image: linear-gradient(269deg, #FFFFFF 67%, rgba(255, 255, 255, 0.60) 100%);
            box-shadow: -4px 0 16px 0 rgba(38, 45, 63, 0.13);
            position: relative;
            top: -240px;
            left: 607px;
            text-align: center;

            .title {
              font-family: PingFangSC-Regular;
              font-size: 24px;
              color: #595959;
              line-height: 240px;
            }
          }
        }
      }

      .line {
        margin: 32px 24px 32px 24px;
        height: 0;
        border: 2px dashed #DADEE9;
      }

      .amount-info {
        .row {
          margin: 24px;

          .title {
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #8C8C8C;
            letter-spacing: 0;
            line-height: 28px;
            width: 30%;
            float: left;
          }

          .value {
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #262626;
            letter-spacing: 0;
            text-align: right;
            line-height: 28px;
          }
        }
      }

      .actual-cost-view {
        .title {
          font-family: PingFangSC-Semibold;
          font-size: 32px;
          color: #262626;
          letter-spacing: 2px;
          width: 100%;
          height: 88px;
          line-height: 88px;
          background: white;
          text-align: center;
        }
      }
    }

  }
</style>

