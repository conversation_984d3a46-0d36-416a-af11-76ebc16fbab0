<template>
    <link-page class="send-record-list-page">
        <lnk-taps :taps="quesOperationOption" v-model="quesOperationActive" @switchTab="onTap"></lnk-taps>
        <view class="blank"></view>
        <link-auto-list :option="questionRecordList" hideCreateButton>
            <template slot-scope="{data,index}">
                <link-swipe-action>
                    <link-swipe-option slot="option" @tap="handleConsumerSendType(data)" v-if="quesOperationActive.val === 'Y' || quesOperationActive.val === 'N'">
                        {{ data.isSend === 'Y' ? '取消发送' : '加入发送' }}</link-swipe-option>
                    <item :key="index" :data="data" class="list-item-content" :arrow="false">
                        <view class="media-list">
                            <view class="media-list-body" style="width: 70%">
                                <view class="media-list-text-top">{{data.acctName}}</view>
                            </view>
                            <view class="media-list-right">{{data.showPhone}}</view>
                        </view>
                    </item>
                </link-swipe-action>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
import LnkTaps from "../../../core/lnk-taps/lnk-taps";
export default {
    name: "send-record-list-page",
    components: {LnkTaps},
    data () {
        const interactionId = this.pageParam.data.interactionId;
        const marketActivityId = this.pageParam.data.marketActivityId;
        const marketStatus = this.pageParam.data.marketStatus;
        const questionRecordList  = new this.AutoList(this, {
            url: {queryByExamplePage: this.$env.appURL + '/interaction/link/activityCrowd/queryByExamplePage'},
            param: {
                filtersRaw: [
                    {"id":"groupName","property":"groupName","value":"全部分组"},
                    {"id":"mcActId","property":"mcActId","value": marketActivityId}
                ]
            },
            exactSearchFields: [
                {
                    field: 'acctName',
                    showValue: '姓名',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: true
                }, {
                    field: 'phone',
                    showValue: '手机号',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: true
                }
            ],
            sortOptions: null
        });
        return {
            quesOperationActive: {name: '全部', seq: '1', val: 'ALL'},
            quesOperationOption: [
                {name: '全部', seq: '1', val: 'ALL'},
                {name: '发送名单', seq: '2', val: 'Y'},
                {name: '取消发送名单', seq: '3', val: 'N'},
            ],
            interactionId,
            marketActivityId,
            marketStatus,
            questionRecordList
        }
    },
    methods: {
        /**
         * @desc 切换tab
         * <AUTHOR>
         * @date 2023/2/8 10:00
         **/
        onTap () {
            try {
                let filterOption = {}
                if (this.quesOperationActive.val === 'ALL') {
                    filterOption = {}
                } else {
                    filterOption = {
                        id: 'isSend',
                        property: 'isSend',
                        value: this.quesOperationActive.val
                    }
                }
                let isSendExit = this.questionRecordList.option.param.filtersRaw.filter((item) => item.property === 'isSend');
                if (!this.$utils.isEmpty(isSendExit)) {
                    let index = this.questionRecordList.option.param.filtersRaw.findIndex(val => val.property === 'isSend');
                    this.questionRecordList.option.param.filtersRaw.splice(index, 1);
                }
                if (!this.$utils.isEmpty(filterOption)) {
                    this.questionRecordList.option.param.filtersRaw.push(filterOption);
                }
                this.questionRecordList.methods.reload();
            } catch (e) {
                console.log(e, '测试')
            }
        },
        /**
         * @createdBy 曾宇
         * @date 2022/12/28
         * @updateBy 马晓娟
         * @update 2023/02/08
         * @methods: handleConsumerAdd
         * @description: 添加发送人员名单
         **/
        async handleConsumerSendType(row) {
            this.$utils.showLoading();
            const param = {
                id: row.id,         // 分组人群id
                isSend: row.isSend === 'Y' ? 'N' : 'Y'
            };
            const data = await this.$http.post(this.$env.appURL + '/interaction/link/activityCrowd/updateIsSend', param, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError('修改失败:' + response.result);
                }});
            if (data.success) {
                this.$utils.hideLoading();
                this.$message.success('修改成功！');
                await this.questionRecordList.methods.reload();
            }
        }
    }
}
</script>

<style lang="scss">
.send-record-list-page {
    .blank {
        width: 100%;
        height: 96px;
        background: #F2F2F2;
    }
    .title-item {
        font-weight: bold;
        font-size: 36px;
        margin-bottom: 16px;
        padding: 2% 2% 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title-btn {
            font-size: 28px;
            color: #2F69F8;
            letter-spacing: 0;
            line-height: 28px;
        }
    }
    .list-item-content {
        height: 60px;
        position: relative;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        margin: 8px 12px;
        border-radius: 8px;

        .media-list {
            padding: 11px 15px;
            box-sizing: border-box;
            display: flex;
            width: 100%;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;

            .media-list-logo {
                height: 94px;
                width: 94px;
                margin-right: 20px;

                image {
                    height: 100%;
                    width: 100%;
                }
            }

            .media-list-body {
                display: flex;
                flex: 1;
                flex-direction: column;
                justify-content: space-between;
                align-items: flex-start;
                overflow: hidden;

                .media-list-text-top {
                    width: 100%;
                    line-height: 60px;
                    font-size: 30px;
                    color: #2b2b2b;
                }

                .media-list-text-bottom {
                    width: 100%;
                    line-height: 40px;
                    font-size: 26px;
                    color: #8f8f94;
                }
            }
        }
    }
    .link-swipe-action {
        .link-swipe-option-container {
            .link-swipe-option-status-error {
                height: 92px;
                border-radius: 4px 0 4px 0;
            }
        }
    }
}
</style>
