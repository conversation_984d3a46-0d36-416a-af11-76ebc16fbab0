<template>
    <link-page class="terminal-list-page">
        <view class="terminal-view">
            <list>
                <link-swipe-action v-for="(data,index) in terminalList" :key="`${data.id}_${terminalList.length}`">
                    <link-swipe-option slot="option" @tap="handleTerminalDelete(data,index)"
                                       v-if="!$utils.isEmpty(pageSource) && editFlag && pageSource !== 'activityAudit' && operationFlag">
                        删除
                    </link-swipe-option>
                    <item :arrow="false">
                        <view class="terminal-list" slot="note">
                            <view class="list-cell">
                                <view class="media-list">
                                    <image class="media-list-logo" :src="data.storePicKey"></image>
                                    <view class="store-content">
                                        <view class="store-content-top" v-if="data.acctType">
                                            <!--【客户一级分类】为“终端Terminal”的时候显示acctName字段-->
                                            <view class="store-title" v-if="data.acctType === 'Terminal'">
                                                {{data.acctName}}
                                            </view>
                                            <!--【客户一级分类】为“分销商Distributor”、经销商 Dealer 时展示billTitle字段-->
                                            <view class="store-title"
                                                  v-if="data.acctType === 'Distributor' || data.acctType === 'Dealer'">
                                                {{data.billTitle}}
                                            </view>
                                            <!--已认证-->
                                            <view class="store-level" v-if="data.acctStage === 'ykf'">
                                                <image :src="$imageAssets.storeStatusVerifiedImage"></image>
                                            </view>
                                            <!--未认证-->
                                            <view class="store-level" v-if="data.acctStage === 'xk'">
                                                <image :src="$imageAssets.storeStatusUnverifiedImage"></image>
                                            </view>
                                            <!--已失效-->
                                            <view class="store-level" v-if="data.acctStage === 'ysx'">
                                                <image :src="$imageAssets.storeStatusInvalidationImage"></image>
                                            </view>
                                            <!--潜客-->
                                            <view class="store-level" v-if="data.acctStage === 'dkf'">
                                                <image :src="$imageAssets.storeStatusPotentialImage"></image>
                                            </view>
                                        </view>
                                        <view class="store-content-middle">
                                            <view class="store-type" v-if="data.acctCategory">{{data.acctCategory |
                                                lov('ACCNT_CATEGORY')}}
                                            </view>
                                            <view class="store-type"
                                                  v-if="data.acctLevel !== undefined || data.capacityLevel !== undefined">
                                                <text style="float: left" v-if="data.acctLevel !== undefined">
                                                    {{data.acctLevel |
                                                    lov('ACCT_LEVEL')}}
                                                </text>
                                                <text style="padding-left: 2px" v-if="data.capacityLevel !== undefined">
                                                    | {{data.capacityLevel |
                                                    lov('CAPACITY_LEVEL')}}
                                                </text>
                                            </view>
                                        </view>
                                        <view class="store-content-representative">
                                            <view class="terminal-type" style="width: 30px">业代</view>
                                            <view class="terminal-name">{{data.trackerNames}}</view>
                                        </view>
                                        <view class="store-content-address">
                                            <view class="store-address">
                                                {{data.addrDetailAddr}}
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </item>
                </link-swipe-action>
            </list>
        </view>
    </link-page>
</template>

<script>
    export default {
        name: "terminal-list-page",
        data() {
            const parentId = this.pageParam.parentId;
            const operationFlag = this.pageParam.operationFlag;
            const editFlag = this.pageParam.editFlag;
            const pageSource = this.pageParam.pageSource;
            return {
                parentId,
                operationFlag,
                editFlag,
                pageSource,
                terminalList: [],
            }
        },
        async created() {
            await this.queryTerminalList()
        },
        methods: {
            //2021-08-04考虑多人操作的场景
            async operationalControl (){
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.parentId
                });
                if ((data.result.status === 'New'
                    && (data.result.aproStatus === 'New' || data.result.aproStatus === 'Refused')
                ) || ((data.result.status === 'Processing' || data.result.status === 'Closed' || data.result.status === 'Published')
                    && (data.result.aproStatus === 'Approve' || data.result.aproStatus === 'Refeedback' || data.result.aproStatus === 'RefeedWithdraw')
                )) {
                    return  true;
                } else {
                    return false;
                }
            },
            /**
             * 查询列表
             * <AUTHOR>
             * @date 2020-08-13
             * */
            async queryTerminalList() {
                const data = await this.$http.post('action/link/attendTerminal/queryByExamplePage', {
                    totalFlag: true,
                    sort: "created",
                    order: 'desc',
                    filtersRaw: [{id: 'actId', property: 'actId', value: this.parentId, operator: '='}]
                });
                data.rows.forEach(async (item) => {
                    if (!this.$utils.isEmpty(item.storePicPreKey)) {
                        let urlData = await this.$image.getSignedUrl(item.storePicPreKey);
                        this.$set(item, 'storeUrl', urlData);
                    } else {
                        this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
                    }
                })
                this.terminalList = data.rows || [];
            },
            /**
             * 删除参与终端/经销商
             * <AUTHOR>
             * @date 2020-08-11
             * */
            async handleTerminalDelete(item, index) {
                const flag = await this.operationalControl();
                if(!flag){
                    this.$message.warn('活动已被更新，当前活动状态和审批状态不允许删除参与终端/经销商，请返回列表重新查询活动数据。');
                    return ;
                }
                await this.$http.post('action/link/attendTerminal/deleteById', item);
                await this.queryTerminalList();
                this.pageParam.callback();
            },
        }
    }
</script>

<style lang="scss">
    .terminal-list-page {
        .terminal-view {
            background: white;
            margin-top: 12px;

            .terminal-list {
                .list-cell {
                    .media-list {
                        @include flex;
                        padding: 24px 16px 24px 24px;

                        .media-list-logo {
                            box-shadow: 0 4px 31px 0 rgba(0, 44, 152, 0.22);
                            border-radius: 16px;
                            width: 128px;
                            height: 128px;
                            overflow: hidden;
                        }

                        .store-content {
                            width: 80%;

                            .store-content-top {
                                @include flex-start-center;
                                @include space-between;
                                margin-left: 24px;

                                .store-title {
                                    font-family: PingFangSC-Semibold, serif;
                                    font-size: 32px;
                                    color: #262626;
                                    letter-spacing: 0;
                                    line-height: 32px;
                                }

                                .store-level {
                                    margin-right: -3px;
                                    width: 120px;
                                    height: 44px;

                                    image {
                                        width: 100%;
                                        height: 100%;
                                    }
                                }
                            }

                            .store-content-middle {
                                @include flex-start-center;
                                margin-top: 20px;
                                margin-left: 24px;

                                .store-type {
                                    white-space: nowrap;
                                    border: 1px solid #2F69F8;
                                    border-radius: 8px;
                                    font-size: 20px;
                                    padding-left: 18px;
                                    padding-right: 18px;
                                    line-height: 40px;
                                    height: 40px;
                                    color: #2F69F8;
                                    margin-right: 10px;
                                }
                            }

                            .store-content-representative {
                                @include flex;
                                margin-left: 24px;
                                margin-top: 24px;

                                .terminal-type {
                                    color: #8C8C8C;
                                }

                                .terminal-name {
                                    font-family: PingFangSC-Regular, serif;
                                    font-size: 24px;
                                    color: #000000;
                                    letter-spacing: 0;
                                    padding-left: 8px;
                                }
                            }

                            .store-content-address {
                                margin-left: 24px;
                                margin-top: 20px;
                                font-family: PingFangSC-Regular, serif;
                                font-size: 24px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 24px;
                            }
                        }
                    }
                }
            }

            .more {
                font-family: PingFangSC-Regular;
                width: 100%;
                text-align: center;
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
                line-height: 76px;
                background-color: #f2f2f2;
            }
        }
    }
</style>
