<template>
    <link-page class="reported-share-page">
        <view class="report">
            <!--模板无权限-->
            <lnk-no-auth v-if="authFlag"></lnk-no-auth>
            <view v-else>
                <view class="report-data">{{actIndeSourCodeName}}报备</view>
                <view class="report-data">报备时间:{{activityItem.reportDate}}</view>
                <view class="report-data">报备人及电话:{{userInfo.firstName}};{{userInfo.contactPhone}}</view>
                <view class="report-data">区域主管姓名及电话:{{activityItem.regionalHead}};{{activityItem.regionalHeadPhone}}
                </view>
                <view v-for="item in fieldRows" class="report-data">
                    <!--除activeConsumers活动消费者以外-->
                    <view v-if="!(item.ctrlCode === 'report-blank' || item.values.field === 'activeConsumers')">
                        <view style="float: left">{{item.base.label}}:</view>
                        <view
                                v-if="$utils.isEmpty(item.values.lovType) && $utils.isEmpty(item.values.cny) && $utils.isEmpty(item.values.date)">
                            {{activityItem[item.values.field]}}
                        </view>
                        <view v-if="!$utils.isEmpty(item.values.lovType)">
                            {{activityItem[item.values.field]|lov(`${item.values.lovType}`)}}
                        </view>
                        <view class="val" v-if="!$utils.isEmpty(item.values.cny)">{{activityItem[item.values.field]
                            | cny}}
                        </view>
                        <view class="val" v-if="!$utils.isEmpty(item.values.date)">{{activityItem[item.values.field]
                            | date(`${item.values.date}`)}}
                        </view>
                    </view>
                    <!--单独展示活动消费者-->
                    <view v-if="item.ctrlCode !== 'report-blank' && item.values.field === 'activeConsumers'">
                        <view style="width: 100%">{{item.base.label}}:</view>
                        <text>{{activeConsumers}}</text>
                    </view>
                </view>
                <!--1、当活动状态 MC_STATUS =新建，且活动审批状态 APRO_STATUS =未提交&提报待审批&已拒绝时
                    2、当活动状态=已发布&进行中&执行结束&已实发，且活动审批状态=申请审批通过&反馈待审批&反馈驳回&反馈撤回&反馈审批通过时
                    3、活动状态=已作废时-->
                <view class="report-data"
                      v-if="activityItem.status === 'New' && (activityItem.aproStatus === 'New' || activityItem.aproStatus === 'Submitted' || activityItem.aproStatus === 'Refused')">
                    备注:报备人确认以上信息真实有效。
                </view>
                <view class="report-data"
                      v-if="(activityItem.status === 'Published' || activityItem.status === 'Processing' || activityItem.status === 'Closed' || activityItem.status === 'ActualAmount')
                      && (activityItem.aproStatus === 'Approve' || activityItem.aproStatus === 'Feedback' || activityItem.aproStatus === 'Refeedback'
                      ||activityItem.aproStatus === 'RefeedWithdraw'||activityItem.aproStatus === 'FeedbackApro')">
                    备注:活动已审批通过，确认以上信息真实有效。
                </view>
                <view class="report-data" v-if="activityItem.status === 'Inactive'">备注:活动已作废。</view>
            </view>
        </view>
        <link-sticky>
            <link-button block @tap="copyText">复制文本</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    import Taro from "@tarojs/taro";
    import LnkNoAuth from "../../core/lnk-no-auth/lnk-no-auth";

    export default {
        name: "reported-share-page",
        components: {LnkNoAuth},
        data() {
            const activityData = this.pageParam.activityItem;
            const userInfo = Taro.getStorageSync('token').result;
            return {
                // 费用价格是否展示标识
                priceShowFlag: false,
                userInfo,
                activityData,
                activityItem: {},
                actIndeSourCodeName: '',
                authFlag: false,
                fieldRows: [],//当前费用类型需要报备的字段
                activeConsumers: '',
            }
        },
        async created() {
            await this.queryShareData();
            this.priceShowFlag = await this.$utils.getPriceDesensitize(this.userInfo);
            await this.queryReportedShareData();
        },
        onLoad() {
            // this.userInfo = Taro.getStorageSync('token').result;         获取用户信息
        },
        methods: {
            change(str){
                var reg = /[;；]/g;
                str=str.replace(reg,"$&\r\n");
                this.activeConsumers = str;
            },
            /**
             * 查询分享需要的活动信息
             * */
            async queryShareData() {
                if(this.$utils.isEmpty(this.activityData.id)){
                    return;
                }
                const data = await this.$http.post('action/link/marketAct/report', {
                    id: this.activityData.id
                });
                this.activityItem = {
                    reportDate: "",
                    exeCaseCode: '',
                    activityName: '',
                    noPerformComments: '',
                    activityType: '',
                    startTime: "",
                    endTime: "",
                    restaurant: "",
                    restaurantPhone: "",
                    restaurantAddr: "",
                    roomNum: "",
                    tableNum: "",
                    dinnersNum: "",
                    wineNameCount: "",
                    executivesName: "",
                    executivesPhone: "",
                    actExecutivesName: "",
                    actExecutivesPhone: "",
                    firstName: "",
                    contactPhone: "",
                    regionalHead: "",
                    regionalHeadPhone: "",
                    cashApplyAmount: "",
                    prodApplyAmount: "",
                    cashFillAmount: "",
                    prodFillAmount: "",
                    orderer: "",
                    ordererPhone: "",
                    actualNum: "",
                    salesBigArea:"",
                    salesRegion:"",
                    salesCity:"",
                    salesDistrict:"",
                    purchasedProdQty:"",
                    ...data.rows
                };
                if(!this.$utils.isEmpty(data.rows['activeConsumers'])){
                    this.change(data.rows['activeConsumers']);
                }
                const reportDate = this.$date.format(new Date(), 'YYYY-MM-DD HH:mm');
                this.$set(this.activityItem, 'reportDate', reportDate);
                this.actIndeSourCodeName = await this.$lov.getNameByTypeAndVal('MC_TYPE', this.activityItem.actIndeSourCode);
            },
            /**
             * 查询分享配置的活动信息
             * */
            async queryReportedShareData() {
                if (this.$utils.isEmpty(this.activityData.costTypeCode)) {
                    this.$showError("费用类型信息不完整，请更新活动数据或重新查询");
                    return;
                }
                this.$utils.showLoading();
                let data;
                if(this.$utils.isNotEmpty(this.activityData.actIndeSourCode)) {
                    data = await this.$utils.getQwMpTemplate('viewActivity', 'ActReport', this.activityData.costTypeCode, this.activityData.actIndeSourCode);
                } else {
                    data = await this.$utils.getQwMpTemplate('viewActivity', 'ActReport', this.activityData.costTypeCode);
                }
                if (!data.success) {
                    this.authFlag = true;
                    this.$utils.hideLoading();
                    return;
                }
                this.$utils.hideLoading();
                this.authFlag = false;
                let resultOpt = JSON.parse(data.result);
                this.fieldRows = JSON.parse(resultOpt.conf);
                if (!this.priceShowFlag){
                    for (let index=0;index<this.fieldRows.length;index++){
                        if (this.$utils.isNotEmpty(this.fieldRows[index].values.cny)){
                            this.fieldRows.splice(index,1)
                            index--;
                        }
                    }
                }
            },
            /**
             * 复制文本
             * <AUTHOR>
             * @date 2020-08-18
             * */
            async copyText() {
                let frontPart = "";//前半部分
                let secondPart = "";//后半部分
                let note = "";//备注
                let string = "";
                this.actIndeSourCodeName = await this.$lov.getNameByTypeAndVal('MC_TYPE', this.activityItem.actIndeSourCode);
                frontPart = `${this.actIndeSourCodeName}报备\n报备时间${this.activityItem.reportDate}\n报备人及电话:${this.userInfo.firstName};${this.userInfo.contactPhone}\n区域主管姓名及电话:${this.activityItem.regionalHead};${this.activityItem.regionalHeadPhone}`;
                if (!this.$utils.isEmpty(this.activityItem.exeCaseCode)) {
                    frontPart = frontPart + `\n执行案编号:${this.activityItem.exeCaseCode}`;
                } else {
                    frontPart = frontPart + `\n无执行案说明:${this.activityItem.noPerformComments}`;
                }
                for (let i = 0; i < this.fieldRows.length; i++) {
                    const item = this.fieldRows[i];
                    if (item.ctrlCode === 'report-blank') {
                        secondPart = secondPart + '\n';
                    } else {
                        if(item.values.field !== 'activeConsumers'){
                            if (!this.$utils.isEmpty(item.values.lovType)) {
                                const fieldName = await this.$lov.getNameByTypeAndVal(item.values.lovType, this.activityItem[item.values.field]);
                                secondPart = secondPart + `\n${item.base.label}:${fieldName}`
                            } else {
                                secondPart = secondPart + `\n${item.base.label}:${this.activityItem[item.values.field]}`
                            }
                        } else {
                            const activeConsumersText = `\n${item.base.label}:\n` + this.activeConsumers;
                            secondPart = secondPart + activeConsumersText;
                        }
                    }
                }
                if (this.activityItem.status === 'New' && (this.activityItem.aproStatus === 'New' || this.activityItem.aproStatus === 'Submitted' || this.activityItem.aproStatus === 'Refused')) {
                    note = `\n备注:报备人确认以上信息真实有效。`
                }
                if ((this.activityItem.status === 'Published' || this.activityItem.status === 'Processing' || this.activityItem.status === 'Closed' || this.activityItem.status === 'ActualAmount')
                    && (this.activityItem.aproStatus === 'Approve' || this.activityItem.aproStatus === 'Feedback' || this.activityItem.aproStatus === 'Refeedback'
                        || this.activityItem.aproStatus === 'RefeedWithdraw' || this.activityItem.aproStatus === 'FeedbackApro')) {
                    note = `\n备注:活动已审批通过，确认以上信息真实有效。`
                }
                if(this.activityItem.status === 'Inactive'){
                    note = `\n备注:活动已作废。`
                }
                string = frontPart + secondPart + note;
                wx.setClipboardData({
                    data: string,
                    success: function () {
                        // 添加下面的代码可以复写复制成功默认提示文本`内容已复制`
                        wx.showToast({
                            title: '复制成功',
                            duration: 3000
                        });
                        wx.getClipboardData({
                            success: function (res) {
                            }
                        })
                    }
                })
            }
        }
    }
</script>

<style lang="scss">
    .reported-share-page {
        background: white;

        .report {
            .report-data {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #595959;
                letter-spacing: 0;
                line-height: 30px;
                padding: 32px 0 0 24px;
                clear: both;
            }
        }
    }
</style>
