<template>
    <link-page class="new-consumer-page">
        <link-form :option="option" :rules="formRules" ref="form" :value="option">
            <link-form-item label="姓名" required field="acctName">
                <link-input v-model="option.formData.acctName"></link-input>
            </link-form-item>
            <link-form-item label="性别" required field="gender">
                <link-lov v-model="option.formData.gender" type="GENDER"></link-lov>
            </link-form-item>
            <link-form-item label="消费者手机号" required field="mobilePhone1">
                <link-input type="number" v-model="option.formData.mobilePhone1" @input="onKeyPhone"
                            placeholder="消费者手机号"/>
            </link-form-item>
            <link-form-item label="单位名称" required field="company">
                <link-input v-model="option.formData.company"></link-input>
            </link-form-item>
            <link-form-item label="职务" required field="position">
                <link-input v-model="option.formData.position"></link-input>
            </link-form-item>
            <link-form-item label="消费者类别" arrow field="position">
                <link-lov type="ACCT_SUB_TYPE" parent-type="ACTIVITY_COMPANY"
                          :parent-val="option.formData.companyId"
                          v-model="option.formData.subAcctType" placeholder="请选择..."/>
            </link-form-item>
            <link-form-item label="消费者级别" arrow field="position">
                <link-lov type="ACCT_CHILD_TYPE" parent-type="ACCT_SUB_TYPE"
                          :parent-val="option.formData.subAcctType"
                          v-model="option.formData.acctLevel" placeholder="请选择..."/>
            </link-form-item>
            <link-form-item label="所属终端" readonly>
                <link-input v-model="option.formData.company"></link-input>
            </link-form-item>
        </link-form>
        <link-dialog ref="oldCustomerPoster">
            <view class="old-cus-dialog-view">
                <view class="old-cus-o">
                    <view class="o-left">
                        <image :src="oldCustomerItem|headImgAccount(oldCustomerItem)"/>
                    </view>
                    <view class="o-right">
                        <view class="name">{{oldCustomerItem.acctName}}</view>
                        <view class="tel">{{oldCustomerItem.mobilePhone1}}</view>
                    </view>
                    <view class="cancel-v">
                        <view @tap="cancel">
                            <link-icon icon="mp-close"/>
                        </view>
                    </view>
                </view>
                <view class="old-cus-t">
                    <view class="t-left">
                        <view class="icon-v">
                            <link-icon icon="icon-leixinghedengji"/>
                        </view>
                        <view class="title">消费者类别</view>
                        <view class="val">{{oldCustomerItem.subAcctType | lov('ACCT_SUB_TYPE')}}</view>
                    </view>
                    <view class="t-left">
                        <view class="icon-v">
                            <link-icon icon="icon-leixinghedengji"/>
                        </view>
                        <view class="title">消费者等级</view>
                        <view class="val">{{oldCustomerItem.acctLevel | lov('ACCT_CHILD_TYPE')}}</view>
                    </view>
                    <view class="t-left">
                        <view class="icon-v">
                            <link-icon icon="icon-jiaose"/>
                        </view>
                        <view class="title">业代</view>
                        <view class="val">{{oldCustomerItem.fstName}}</view>
                    </view>
                    <view class="t-left">
                        <view class="icon-v">
                            <link-icon icon="icon-hexinzhongduan"/>
                        </view>
                        <view class="title">所属终端</view>
                        <view class="val">{{oldCustomerItem.belongToStore}}</view>
                    </view>
                </view>
                <view class="old-cus-s">
                    <view @tap="applicationTransfer" class="pike-v">
                        <view class="title">申请转交并选择此客户</view>
                    </view>
                </view>
                <view class="old-cus-s" style="margin-top: 10px">
                    <view @tap="pike" class="cancel-v">
                        <view class="title">选择此客户</view>
                    </view>
                </view>
            </view>
        </link-dialog>
        <link-sticky>
            <link-button block @tap="saveAccount">保存</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    import {ROW_STATUS} from "../../../utils/constant";
    import Taro from "@tarojs/taro";

    export default {
        name: "new-consumer-page",
        data() {
            const config = {
                model: {},
                data: {
                    ...this.pageParam.item
                }
            };
            const option = new this.FormOption(this, {
                ...config,
                operator: 'NEW',
            });
            const oldCustomerItem = {};
            return {
                oldCustomerItem,
                option,
                formRules: {
                    acctName: this.Validator.required("请输入姓名"),
                    gender: this.Validator.required("请选择性别"),
                    mobilePhone1: this.Validator.phone(),
                    company: this.Validator.required("请输入单位名称"),
                    position: this.Validator.required("请输入职务"),
                    subAcctType: this.Validator.required("请选择消费者类别"),
                    acctLevel: this.Validator.required("请选择消费者级别"),
                }
            }
        },
        methods: {
            onKeyPhone: function (event) {
                if (this.option.formData.row_status === ROW_STATUS.NEW) {
                    if (this.option.formData.mobilePhone1 && this.option.formData.mobilePhone1.length === 11) {
                        // 查询老客户
                        this.getRegularConsumer(this.option.formData.mobilePhone1);
                    }
                }
            },
            /**
             * 根据手机号,查询老客户
             * <AUTHOR>
             * @date 2020-08-07
             * @param mobilePhone 手机号
             */
            async getRegularConsumer(mobilePhone) {
                // 根据手机号,获取客户信息
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/consumerListSend', {
                    filtersRaw: [
                        {id: 'mobilePhone1', property: 'mobilePhone1', value: mobilePhone, operator: '='},
                        {
                            id: 'accntChannel',
                            property: 'accntChannel',
                            value: this.option.formData.accntChannel,
                            operator: '='
                        },
                        {id: 'companyId', property: 'companyId', value: this.option.formData.companyId, operator: '='},
                    ],
                });
                // 如果客户信息大于等于1,则显示老客户窗口,选择老客户
                if (data.rows.length >= 1) {
                    this.oldCustomerItem = data.rows[0];
                    this.$refs.oldCustomerPoster.show()
                }
            },
            /**
             * 取消
             * <AUTHOR>
             * @date 2020-08-07
             */
            cancel() {
                this.$refs.oldCustomerPoster.hide()
            },
            /**
             * 选择
             * <AUTHOR>
             * @date 2020-08-07
             */
            pike() {
                this.$refs.oldCustomerPoster.hide()
            },
            /*
            * 申请转交并选择此客户
            * <AUTHOR>
            * @date 2020-10-27
            * */
            applicationTransfer() {

            },
            /**
             * 保存消费者
             * <AUTHOR>
             * @date 2020-08-25
             * */
            async saveAccount() {
                //await this.$refs.form.validate({loading: true});
                if (!this.checkData()) {
                    return;
                }
                this.userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
                // 若类型为空/“未分配”时则可选择此消费者且更新消费者的类型为待跟进，所属业代为当前登录人。
                if (!this.option.formData.subAcctType || this.option.formData.subAcctType === 'Unassigned') {
                    this.option.formData.subAcctType = 'ToBeFollowed';
                    this.option.formData.postnId = this.userInfo.postnId;
                }
                this.option.formData.yxFlag = 'Y';
                const data = await this.$http.post(this.$env.appURL + '/action/link/consumer/guideAccountUpdate', this.option.formData);
                this.pageParam.callback(data.result);
                this.$nav.back();
            },
            checkData() {
                if (this.$utils.isEmpty(this.option.formData.acctName)) {
                    this.$showError("请输入姓名");
                    return false
                }
                if (this.$utils.isEmpty(this.option.formData.gender)) {
                    this.$showError("请选择性别");
                    return false
                }
                if (this.$utils.isEmpty(this.option.formData.mobilePhone1)) {
                    if (!/^1[3456789]\d{9}$/.test(this.option.formData.mobilePhone1)) {
                        this.$showError("请输入消费者手机号");
                        return false
                    }
                }
                if (this.$utils.isEmpty(this.option.formData.company)) {
                    this.$showError("请输入单位名称");
                    return false
                }
                if (this.$utils.isEmpty(this.option.formData.position)) {
                    this.$showError("请输入职务");
                    return false
                }
                if (this.$utils.isEmpty(this.option.formData.subAcctType)) {
                    this.$showError("请选择消费者类别");
                    return false
                }
                if (this.$utils.isEmpty(this.option.formData.acctLevel)) {
                    this.$showError("请选择消费者级别");
                    return false
                }
                return true;
            }
        }
    }
</script>

<style lang="scss">
    .new-consumer-page {
        .old-cus-dialog-view {
            width: 654px;
            height: 800px;
            border-radius: 16px;
            background-color: white;

            .old-cus-o {
                width: 100%;
                height: 208px;
                border-radius: 8px 8px 100px 100px;
                background-color: #2F69F8;

                .o-left {
                    width: 128px;
                    float: left;
                    border: 12px solid rgba(255, 255, 255, 0.12);
                    border-radius: 100px;
                    height: 128px;
                    margin: 40px 0 40px 48px;

                    image {
                        width: 104px;
                        height: 104px;
                        line-height: 180px;
                    }
                }

                .o-right {
                    float: left;
                    width: 50%;
                    margin: 70px 0 0 30px;

                    .name {
                        font-family: PingFangSC-Semibold;
                        font-size: 32px;
                        color: #FFFFFF;
                        letter-spacing: 0;
                        line-height: 32px;
                    }

                    .tel {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #FFFFFF;
                        letter-spacing: 0;
                        line-height: 28px;
                        padding-top: 20px;
                    }
                }

                .cancel-v {
                    width: 15%;
                    float: right;
                    height: 60px;
                    margin-top: 50px;
                    font-size: 36px;
                    text-align: center;
                    color: white;
                }
            }

            .old-cus-t {
                width: 100%;
                height: 316px;

                .t-left {
                    margin: 28px 48px 24px 48px;
                    height: 60px;

                    .icon-v {
                        width: 10%;
                        float: left;
                        color: #8C8C8C;
                    }

                    .title {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        line-height: 44px;
                        width: 40%;
                        float: left;

                    }

                    .val {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 0;
                        text-align: right;
                        line-height: 44px;
                        width: 50%;
                        float: left;
                    }
                }
            }

            .old-cus-s {
                width: 100%;
                height: 100px;
                margin-top: 30px;


                .cancel-v {
                    width: 90%;
                    height: 74px;
                    float: left;
                    color: white;
                    margin-left: 5%;
                    border: 2px solid #2F69F8;
                    border-radius: 4px;

                    .title {
                        font-family: PingFangSC-Regular;
                        font-size: 32px;
                        color: #2F69F8;
                        letter-spacing: 0;
                        text-align: center;
                        line-height: 74px;
                    }
                }

                .pike-v {
                    width: 90%;
                    height: 74px;
                    float: left;
                    background: #2F69F8;
                    box-shadow: 0 16px 48px 0 rgba(47, 105, 248, 0.50);
                    border-radius: 8px;
                    margin-left: 5%;

                    .title {
                        font-family: PingFangSC-Regular;
                        font-size: 32px;
                        color: #FFFFFF;
                        letter-spacing: 0;
                        text-align: center;
                        line-height: 74px;
                    }
                }
            }
        }

        .link-dialog .link-dialog-content {
            width: 654px !important;

            .link-dialog-body {
                padding: 0px !important;
                -webkit-flex: 1;
                -ms-flex: 1;
                flex: 1;
                font-size: 28px;
                overflow: hidden;
                word-break: break-all;

            }
        }
    }
</style>
