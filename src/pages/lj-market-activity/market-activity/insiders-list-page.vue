<template>
    <link-page class="insiders-list-page">
        <view class="insiders-view">
            <list>
                <link-swipe-action v-for="(item,index) in insiderList" :key="`${item.id}_${insiderList.length}`">
                    <link-swipe-option slot="option" v-if="!$utils.isEmpty(pageSource) && editFlag && pageSource !== 'activityAudit' && activityItem.postnId !== item.userPostnId"
                                       @tap="handleInsiderDelete(item,index)">删除
                    </link-swipe-option>
                    <item :arrow="false">
                        <view class="insider-list" slot="note">
                            <view class="list-cell">
                                <view class="media-list">
                                    <view class="media-list-body">
                                        <view class="media-list-text-top">
                                            <view class="name">{{item.userName}}</view>
                                            <view class="tel">{{item.empTel}}</view>
                                        </view>
                                        <view class="media-list-text-bottom uni-ellipsis">
                                            <view class="icon-v">
                                                <link-icon icon="icon-gonghaobumenhezhiwei1" size="0.9em"/>
                                            </view>
                                            <view class="num">{{item.userNo}}</view>
                                            <view class="line">|</view>
                                            <view class="dept">{{item.userOrgName}}</view>
                                        </view>
                                        <view class="media-list-text-bottom uni-ellipsis">
                                            <view class="icon-v" style="color: #fff;">
                                                <link-icon icon="icon-gonghaobumenhezhiwei1" size="0.9em"/>
                                            </view>
                                            <view class="post">{{item.postnName}}</view>
                                            <view class="line" v-if="item.staffType">|</view>
                                            <view class="post">{{item.staffType | lov('STAFF_TYPE')}}</view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </item>
                </link-swipe-action>
            </list>
        </view>
    </link-page>
</template>

<script>
    export default {
        name: "insiders-list-page",
        data() {
            const parentId = this.pageParam.parentId;
            const operationFlag = this.pageParam.operationFlag;
            const editFlag = this.pageParam.editFlag;
            const pageSource = this.pageParam.pageSource;
            const activityItem = this.pageParam.activityItem;
            return {
                activityItem,
                parentId,
                operationFlag,
                editFlag,
                pageSource,
                insiderList: []
            }
        },
        async created() {
            await this.queryInsiderList();
        },
        methods: {
            //2021-08-04考虑多人操作的场景
            async operationalControl (){
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.parentId
                });
                if ((data.result.status === 'New'
                    && (data.result.aproStatus === 'New' || data.result.aproStatus === 'Refused')
                ) || ((data.result.status === 'Processing' || data.result.status === 'Closed' || data.result.status === 'Published')
                    && (data.result.aproStatus === 'Approve' || data.result.aproStatus === 'Refeedback' || data.result.aproStatus === 'RefeedWithdraw')
                )) {
                    return  true;
                } else {
                    return false;
                }
            },
            /**
             * 查询内部人员列表
             * <AUTHOR>
             * @date 2020-08-13
             * */
            async queryInsiderList() {
                const data = await this.$http.post('action/link/actAccompany/queryByExamplePage', {
                    sort: "created",
                    order: 'desc',
                    filtersRaw: [{id: 'actId', property: 'actId', value: this.parentId, operator: '='}]
                });
                this.insiderList = data.rows || [];
            },
            /**
             * 删除内部人员
             * <AUTHOR>
             * @date 2020-08-07
             * */
            async handleInsiderDelete(item, index) {
                const flag = await this.operationalControl();
                if(!flag){
                    this.$message.warn('活动已被更新，当前活动状态和审批状态不允许添加活动执行人员，请返回列表重新查询活动数据。');
                    return ;
                }
                await this.$http.post('action/link/actAccompany/deleteById', item);
                await this.queryInsiderList();
                this.pageParam.callback();
            },
        }
    }
</script>

<style lang="scss">
    .insiders-list-page {
        .insiders-view {
            background: white;

            .insider-list {
                background-color: #FFFFFF;
                position: relative;
                width: 100%;
                display: flex;
                flex-direction: column;

                .list-cell {
                    position: relative;
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    align-items: center;

                    .media-list {
                        padding: 11px 15px;
                        box-sizing: border-box;
                        display: flex;
                        width: 100%;
                        flex-direction: row;

                        .media-list-body {
                            display: flex;
                            flex: 1;
                            flex-direction: column;
                            justify-content: space-between;
                            align-items: flex-start;
                            overflow: hidden;

                            .media-list-text-top {
                                width: 100%;

                                .name {
                                    font-family: PingFangSC-Semibold;
                                    font-size: 32px;
                                    color: #262626;
                                    letter-spacing: 0;
                                    line-height: 44px;
                                    float: left;

                                }

                                .tel {
                                    font-family: PingFangSC-Regular;
                                    font-size: 28px;
                                    color: #8C8C8C;
                                    letter-spacing: 0;
                                    line-height: 44px;
                                    float: left;
                                }

                                .right-v {
                                    font-family: PingFangSC-Regular;
                                    font-size: 28px;
                                    color: #262626;
                                    letter-spacing: 0;
                                    line-height: 44px;
                                    text-align: right;
                                }
                            }

                            .media-list-text-bottom {
                                width: 100%;

                                .icon-v {
                                    width: 5%;
                                    float: left;
                                    line-height: 28px;
                                }


                                .num {
                                    font-family: PingFangSC-Regular;
                                    font-size: 28px;
                                    color: #262626;
                                    letter-spacing: 0;
                                    line-height: 36px;
                                    float: left;
                                    text-align: center;
                                }

                                .dept {
                                    font-family: PingFangSC-Regular;
                                    font-size: 28px;
                                    color: #262626;
                                    letter-spacing: 0;
                                    line-height: 36px;
                                    float: left;
                                    text-align: center;
                                }

                                .post {
                                    font-family: PingFangSC-Regular;
                                    font-size: 28px;
                                    color: #262626;
                                    letter-spacing: 0;
                                    line-height: 36px;
                                    float: left;
                                    text-align: center;
                                }

                                .line {
                                    width: 2%;
                                    float: left;
                                    text-align: center;
                                    line-height: 36px;
                                    padding-left: 16px;
                                    padding-right: 16px;
                                }
                            }
                        }
                    }
                }
            }

            .more {
                font-family: PingFangSC-Regular;
                width: 100%;
                text-align: center;
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
                line-height: 76px;
                background-color: #f2f2f2;
            }
        }
    }
</style>
