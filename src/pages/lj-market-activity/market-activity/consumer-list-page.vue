<template>
    <link-page class="consumer-list-page">
        <view class="view">
            <link-auto-list :option="autoList">
                <template slot-scope="{data,index}">
                    <link-swipe-action>
                        <link-swipe-option slot="option" @tap="handleConsumerDelete(data,index)"
                                           v-if="parentData.status === 'New' && !$utils.isEmpty(pageSource) && editFlag && pageSource !== 'activityAudit' && operationFlag">
                            删除
                        </link-swipe-option>
                        <item :arrow="false" @tap="gotoItem(data)" class="consumers-item-wrap">
                            <view class="consumers-item">
                                <view class="acct-name">{{data.acctName}}</view>
                                <view class="top-label">
                                    <text class="label-text">{{data.saleman}}跟进</text>
                                </view>

                                <view class="tags-wrap">
                                    <view class="tag-item" v-if="data.subAcctType">{{data.subAcctType | lov('ACCT_SUB_TYPE')}}</view>
                                    <view class="tag-item" v-if="data.loyaltyLevel">{{data.loyaltyLevel | lov('ACCT_MEMBER_LEVEL') }}</view>
                                    <view v-if="data.impFlag === 'Y'" class="tag-item red">重点客户</view>
                                </view>
                                <view class="info-item">
                                    <view class="info-label">联系方式</view>
                                    <view class="phone">{{data.phone}}</view>
                                    <view class="status-view" v-if="data.matchStatus !== 'Absent'" @tap.stop="updateInterCust(data, $event)" :style="{background: matchTypeColor[data.matchStatus]}">
                                        <view class="status">{{data.matchStatus | lov('CUST_MATCH_TYPE')}}</view>
                                    </view>
                                </view>
                                <view class="info-item">
                                    <view class="info-label">单位</view>
                                    <view class="info-value">{{data.company}}</view>
                                </view>
                                <view class="info-item">
                                    <view class="info-label">职务</view>
                                    <view class="info-value">{{data.jobTitle}}</view>
                                </view>
                                <view class="info-item">
                                    <view class="info-label">所属客户</view>
                                    <view class="info-value">{{data.belongToStore}}</view>
                                </view>
                            </view>
                        </item>
                    </link-swipe-action>
                </template>
            </link-auto-list>
        </view>
        <link-dialog ref="custMatchTypeDialog">
            <view slot="head">
                请选择匹配类型
            </view>
            <link-button style="margin-left: 5px;margin-right: 5px;"
                         v-for="(item,index) in custMatchTypeList" :key="index" @tap="pikeMatchStatus(item)">{{item.name}}</link-button>
        </link-dialog>
    </link-page>
</template>

<script>

    export default {
        name: "consumer-list-page",
        data() {
            const parentId = this.pageParam.parentId;
            const operationFlag = this.pageParam.operationFlag;
            const editFlag = this.pageParam.editFlag;
            const pageSource = this.pageParam.pageSource;
            const parentData = this.pageParam.parentData;
            const autoList = new this.AutoList(this, {
                url: {
                    queryByExamplePage: this.$env.appURL + '/marketactivity/link/interCust/queryByAppCustPage'
                },
                searchFields: ['acctName', 'phone'],
                param: {
                    mcActId: parentId,
                    filtersRaw: [
                        {id: 'empFlag', property: 'empFlag', value: 'N', operator: '='},
                        {id: 'terminalFlag', property: 'terminalFlag', value: 'N', operator: '='}
                    ]
                },
                hooks: {
                    beforeLoad (option) {
                        for (let i = 0; i < option.param.filtersRaw.length; i++) {
                            if (option.param.filtersRaw[i].property === 'acctName') {
                                option.param.filtersRaw[i].operator = 'like';
                            }
                        }
                    }
                },
                sortOptions: null,
                exactSearchFields: [{
                    field: 'acctName',
                    showValue: '消费者姓名', // 展示名,用于显示的字段=
                    exactSearch: true,
                    searchOnChange: true,
                    clearOnChange: true
                }, {
                    field: 'phone',
                    showValue: '消费者手机号', // 展示名,用于显示的字段=
                    exactSearch: true,
                    searchOnChange: true,
                    clearOnChange: true
                }],
            });
            return {
                operationFlag,
                editFlag,
                pageSource,
                autoList,
                parentId,
                parentData,
                consumerList: [],
                custMatchTypeList:[],
                currentConsumerData:{},
                matchTypeColor: { // 匹配状态显示颜色
                    Interaction: '#32CD32', // 新增-绿色
                    Absent: '#FF0000', // 未到场-红色
                    Success: '#2F69F8' // 匹配成功-蓝色
                }
            }
        },
        async created() {
            this.custMatchTypeList = await this.$lov.getLovByType('CUST_MATCH_TYPE');
        },
        methods: {
            //2021-08-04考虑多人操作的场景
            async operationalControl (){
                const data = await this.$http.post('action/link/marketAct/queryById', {
                    id: this.parentId
                });
                if ((data.result.status === 'New'
                    && (data.result.aproStatus === 'New' || data.result.aproStatus === 'Refused')
                ) || ((data.result.status === 'Processing' || data.result.status === 'Closed' || data.result.status === 'Published')
                    && (data.result.aproStatus === 'Approve' || data.result.aproStatus === 'Refeedback' || data.result.aproStatus === 'RefeedWithdraw')
                )) {
                    return  true;
                } else {
                    return false;
                }
            },
            /**
             * 删除消费者
             * <AUTHOR>
             * @date 2020-08-07
             * */
            async handleConsumerDelete(item, index) {
                const flag = await this.operationalControl();
                if(!flag){
                    this.$message.warn('活动已被更新，当前活动状态和审批状态不允删除消费者，请返回列表重新查询活动数据。');
                    return ;
                }
                await this.$http.post(this.$env.appURL + '/marketactivity/link/interCust/deleteMactById', item);
                await this.autoList.methods.reload();
                this.pageParam.callback();
            },
            /**
             * 查看消费者详情
             * @auther songyanrong
             * @date 2020-09-03
             * */
            async gotoItem(item) {
                let pathFlag = await this.$utils.getCfgProperty('lj_consumers_flag')
                if(pathFlag==='Y'){
                    this.$refs.custMatchTypeDialog.hide();
                    this.$nav.push('/pages/lj-consumers/account/account-item-page', {
                        data: {id: item.accntChannelId}
                    })
                }else{
                this.$refs.custMatchTypeDialog.hide();
                this.$nav.push('/pages/lj-consumers/account-old/account-item-page', {
                    data: {id: item.accntChannelId}
                })
               }
            },
            /**
             * 更新活动人群匹配状态
             * <AUTHOR>
             * @date 2021-01-27
             * */
            async updateInterCust(item,event){
                // 活动状态 MC_STATUS =已发布、进行中、执行结束、已实发，
                // 活动审批状态 APRO_STATUS =申请审批通过、反馈驳回、反馈撤回、反馈审批通过
                // lzljqw-004-283：活动消费者状态不允许调整
                /*if((this.parentData.status === 'Published' || this.parentData.status === 'Processing' || this.parentData.status === 'Closed' || this.parentData.status === 'ActualAmount')
                && (this.parentData.aproStatus === 'Approve' || this.parentData.aproStatus === 'Refeedback' || this.parentData.aproStatus === 'RefeedWithdraw' || this.parentData.aproStatus === 'FeedbackApro')){
                    event.stopPropagation();
                    this.currentConsumerData = item;
                    this.$refs.custMatchTypeDialog.show();
                }*/
            },
            /**
             * 选择活动人群匹配状态
             * <AUTHOR>
             * @date 2021-01-27
             * */
            async pikeMatchStatus(item){
                this.currentConsumerData.matchStatus = item.val;
                await this.$http.post(this.$env.appURL + '/marketactivity/link/interCust/update', this.currentConsumerData);
                this.$refs.custMatchTypeDialog.hide();
                await this.autoList.methods.reload();
            }
        }
    }
</script>

<style lang="scss">
    .consumer-list-page {
        .view {

            .item-header {
                height: 88px;
                width: 100%;
                padding-left: 32px;
                font-size: 28px;
                line-height: 88px;
                color: #262626;
                letter-spacing: 0;
                border-bottom: 2px solid #F2F2F2;
            }

            .link-auto-list-wrapper {
                padding: 0 20px;

                .link-swipe-action {
                    width: 100%;
                    margin: 24px 0;
                }
            }

            .consumers-item-wrap {
                border-radius: 16px;
                position: relative;
                width: 100%;
                overflow: hidden;
                padding: 40px 28px 28px 28px;

                .consumers-item {
                    width: 100%;

                    .acct-name {
                        width: 100%;
                        font-size: 32px;
                        color: #212223;
                        line-height: 48px;
                        font-weight: 600;
                        margin-bottom: 16px;
                    }

                    .top-label {
                        background: #2F69F8;
                        color: white;
                        transform: skew(30deg, 0);
                        display: flex;
                        border-bottom-left-radius: 14px;
                        position: absolute;
                        right: -10px;
                        top: 0;

                        .label-text {
                            font-size: 24px;
                            transform: skew(-30deg, 0);
                            padding: 8px 32px;
                        }
                    }

                    .status-view {
                        position: absolute;
                        right: 16px;
                        width: 120px;
                        transform: skewX(-10deg);
                        border-radius: 4px;
                        background: #2F69F8;
                        box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                        height: 36px;

                        .status {
                            font-size: 22px;
                            color: #FFFFFF;
                            letter-spacing: 2px;
                            text-align: center;
                            line-height: 32px;
                        }
                    }

                    .tags-wrap {
                        display: flex;
                        margin-bottom: 16px;

                        .tag-item {
                            padding: 0 10px;
                            font-size: 22px;
                            font-weight: 400;
                            color: #3F66EF;
                            line-height: 36px;
                            background: #F0F5FF;
                            border-radius: 4px;
                            text-align: center;
                            margin-right: 16px;
                        }

                        .red {
                            color: #FF461E;
                            background: #FFF1EB;
                        }
                    }

                    .info-item {
                        width: 100%;
                        font-size: 28px;
                        display: flex;
                        margin-bottom: 8px;
                        line-height: 44px;
                        position: relative;

                        .info-label {
                            color: #999999;
                            width: 132px;
                        }

                        .info-value {
                            color: #333333;
                        }

                        .phone {
                            color: #317DF7;
                        }
                    }
                }
            }

            .more {
                font-family: PingFangSC-Regular;
                width: 100%;
                text-align: center;
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
                line-height: 76px;
                background-color: #f2f2f2;
            }
        }
    }
</style>
