<template>
    <view class="inbound-code-cost-goods">
        <view class="main-title">费用申请物资</view>
        <view class="fee-pay">
            <view>{{costGoods.feePayType}}</view>
        </view>
        <view class="content">
            <view class="content-code">
                <text class="code-num">{{costGoods.prodCode}}</text>
            </view>
            <view class="content-name">{{costGoods.prodName}}</view>
            <view class="content-num">
                <view class="num-item">
                    <text class="title">申请</text>
                    <text class="val">{{costGoods.qty}}瓶</text>
                </view>
                <view class="num-item num-item-c">
                    <text class="title">已出库</text>
                    <text class="val">{{ costGoods.outQty }}瓶</text>
                </view>
                <view class="num-item num-item-r">
                    <text class="title">待出库</text>
                    <text class="val">{{costGoods.estOutQty}}瓶</text>
                </view>
            </view>
            <view style="clear: both"></view>
        </view>
    </view>
</template>

<script>
export default {
    name: "cost-goods",
    props: {
        id: {
            type: String,
            default: ''
        },
    },
    data() {
        return {
            costGoods: {},    //物资信息
        }
    },
    created() {
        this.init()
    },
    methods: {
        async init() {
            const params =  {
                id: this.id,
                attr4: 'wineTastingOutStock'
            }
            const data = await this.$http.post('action/link/actMaterial/queryById', params);
            this.$emit('estOutQty', data.result.estOutQty)
            this.costGoods = data.result
        }
    }
}
</script>

<style lang="scss">
.inbound-code-cost-goods{
    background-color: rgb(255,255,255);
    font-family: PingFangSC-Regular;
    letter-spacing: 0;
    .main-title{
        font-size: 32px;
        color: #262626;
        padding: 32px 24px;
        border-bottom: 2px solid #e5e5e5;
    }
    .fee-pay{
        font-size: 28px;
        padding: 48px 0 24px 24px;
    }
    .content{
        padding: 32px 24px;
        .content-code{
            .code-num{
                background-color:  #A6B4C7;
                color: white;
                font-size: 28px;
                padding: 2px 8px;
                margin: 10px 0;
                border-radius: 10px;
            }
        }
        .content-name{
            font-family: PingFangSC-Medium;
            font-weight: bold;
            font-size: 28px;
            color: #262626;
            line-height: 45px;
            margin: 10px 0;
        }
        .content-num{
            .num-item{
                float: left;
                width: 33.3%;
                font-size: 28px;
                line-height: 40px;

                .title {
                    color: #8C8C8C;
                }

                .val {
                    color: #262626;
                    text-align: right;
                }
            }
            .num-item-c{
                text-align: center;
            }
            .num-item-r{
                text-align: right;
            }
        }
    }
}

</style>
