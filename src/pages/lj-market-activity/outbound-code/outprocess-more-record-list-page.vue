<template>
    <link-page>
        <view class="outprocess-more-record-list-page">
            <link-auto-list :option="autoList">
                <view slot="top" class="head-top">
                    <link-search-input v-model="searchVal" :placeholder="'请输入活动编码/产品名称'"/>
                </view>
                <template slot-scope="{data,index}">
                    <link-swipe-action :key="index">
                        <link-swipe-option label="删除" @tap="deleteItem(data)" slot="option" class="list-swipe" v-if="pageParam.isShowDelete && deleteScanFlag"/>
                        <item :key="index" :data="data" :arrow="false" class="code-record-list" @tap="toDetails(data)">
                            <view slot="note">
                                <view class="list-top">
                                    <view class="code">{{data.productCode}}</view>
                                    <image class="iseffective" :src="$imageAssets[imgList[data.isEffective]]"></image>
                                </view>
                                <view class="list-middle">
                                    {{data.productName}}
                                </view>
                                <view class="list-bottom clearfix">
                                    <view :class="['list-bottom-item', 'bottom-l', {issuccess: normalScanCode.includes(data.scanRecordStatus)}]">{{data.scanRecordStatus | lov('SCAN_RECORD_STATUS')}}</view>
                                    <view :class="['list-bottom-item', 'bottom-c', {issuccess: data.descriptionType === 'MatchSuccessfully'}]">{{data.descriptionType | lov('MATCH_STATUS')}}</view>
                                    <view class="list-bottom-item bottom-r">{{data.scanner}}</view>
                                </view>
                                <view class="qr-code">
                                    <view class="qr-code-item">
                                        <view class="lebal">盖外码:</view>
                                        <view class="val">{{data.qrCodeOut && data.qrCodeOut.split('/').pop().substr(-8)}}</view>
                                    </view>
                                    <view class="qr-code-item" v-if="data.qrCodeIn && data.scanSubType !== 'GiftScan'">
                                        <view class="lebal">盖内码:</view>
                                        <view class="val">{{data.qrCodeIn.split('/').pop().substr(-8)}}</view>
                                    </view>
                                </view>
                            </view>
                        </item>
                    </link-swipe-action>
                </template>
            </link-auto-list>
        </view>
    </link-page>
</template>

<script>
import {deleteScanCodeCheck} from "../market-activity/deleteScanCodeCheck";

export default {
    name: "outprocess-more-record-list-page",
    data() {
        const autoList = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/actScanRecord/queryByExamplePage'
            },
            param: {
                filtersRaw: [
                    {
                        "id": "paramFilterRaw0",
                        "property": "materialLineId",
                        "value": this.pageParam.id
                    },
                    {
                        "id": "scanSubType",
                        "property": "scanSubType",
                        "value": this.pageParam.type
                    }
                ],
                onlyCountFlag: false,
                oauth: 'ALL',
                sort: 'created',
                order: 'desc',
                rows: 4
            },
            sortOptions: null
        });

        //Y,有效   N,无效
        const imgList = {
            Y:'effective',
            N:'failure'
        }
        //正常入库、正常开瓶、正常转赠、正常出库
        const normalScanCode = ['Normalstorage', 'Normalbottled', 'Normalgifted', 'NormalDelivery']
        const tipsInfo = {
            InScan: '删除扫码记录，将同步更新物资的出库扫码数量，是否确认删除',
            OutScan: '删除扫码记录，将同步更新物资的出库扫码数量，是否确认删除'
        }
        const detailTitle = {
            InScan: '入库扫码详情',
            OutScan: '出库扫码详情',
        }
        const deleteUrl = {
            InScan: 'action/link/actScanRecord/deleteInStockScanRecord',
            OutScan: 'action/link/actScanRecord/deleteOutStockScanRecord'
        };
        this.$taro.setNavigationBarTitle({title: this.pageParam.title});   //动态设置当前页标题
        return {
            autoList,
            imgList,
            searchVal: '',
            tipsInfo,                 //删除提示
            deleteUrl,                //不同状态的二维码对应不同的删除地址
            normalScanCode,           //正常类型的扫码记录 扫码子类型 值列表类型: SCAN_SUB_TYPE
            detailTitle,               //详情页面title
            deleteScanFlag: false,//是否可以侧滑删除扫码记录
        }
    },
    async created() {
        this.deleteScanFlag = await deleteScanCodeCheck.checkFlag(this.pageParam.actId);
    },
    methods: {
        async deleteItem(data) {
            this.$dialog({
                title: '提示',
                content: this.tipsInfo[data.scanSubType],
                cancelButton: true,
                onConfirm: () => {
                    this.confirmDelete(data)
                },
            })
        },
        async confirmDelete(data1) {
            const url = this.deleteUrl[data1.scanSubType]
            const params =  {
                id: data1.id
            }
            try {
                const data = await this.$http.post(url, params);
                if(data.success) {
                    this.$message.success('删除数据成功');
                    this.autoList.methods.reload();
                    if(this.pageParam.type === 'OutScan') {
                        this.$bus.$emit('outProcessOutListReload')
                    }else if(this.pageParam.type === 'InScan') {
                        this.$bus.$emit('outProcessInListReload')
                    } //更新最开始列表页和详情页得物资信息和扫码记录
                    return
                }
                this.$showError('删除数据失败');
            }catch (e) {
                this.$showError(`删除数据异常:${e.result}`);
            }
        },
        toDetails(data) {
            const params =  {
                id: data.id,
                title: this.detailTitle[data.scanSubType]
            }
            this.$nav.push('/pages/lj-market-activity/outbound-code/outbound-record-list-details-page', params)
        }
    },
    watch: {
        searchVal(newVal, oldVal) {
            if (newVal !== oldVal) {
                const searchObj = {
                    id: "searchValue",
                    operator: "or like",
                    property: "[activityNum, prodCode, prodName]",
                    value: newVal
                };
                this.autoList.option.param['filtersRaw'] = this.autoList.option.param['filtersRaw'].filter((item) => item.property !== "[activityNum, prodCode, prodName]");
                this.autoList.option.param['filtersRaw'].push(searchObj);
                this.autoList.methods.reload()
            }
        }
    }
}
</script>

<style lang="scss">
.outprocess-more-record-list-page{
    letter-spacing: 0;
    overflow-x: hidden;

    .head-top{
        background-color: rgb(242,242,242);
    }

    .code-record-list{
        margin: 0 auto;

        .list-top{
            font-size: 28px;
            display: flex;
            justify-content: space-between;

            .code{
                background-color:  #A6B4C7;
                color: white;
                height: 40px;
                padding: 2px 8px;
                margin: 10px 0;
                border-radius: 10px;
            }
            .iseffective{
                width: 92px;
                height: 48px;
            }

        }

        .list-middle{
            font-family: PingFangSC-Medium;
            font-size: 28px;
            color: #262626;
            line-height: 45px;
            margin: 10px 0;
        }

        .list-bottom{
            padding: 10px 0 20px 0;

            .list-bottom-item{
                font-size: 28px;
                float: left;
                width: 33.3%;
            }

            .bottom-l{
                color: #FF5A5A ;
            }

            .bottom-c{
                text-align: center;
                color: #FF5A5A ;
            }

            .bottom-r{
                text-align: right;
            }

            .issuccess{
                color: #2EB3C2;
            }
        }

        .qr-code{
            display: flex;
            justify-content: space-between;
            font-size: 28px;
            .qr-code-item{
                display: flex;
                .lebal{
                    margin-right: 8px;
                }
                .val {
                    color: #262626;
                }
            }
        }

        .clearfix:after {
            content: "";
            display: block;
            height: 0;
            clear: both;
            visibility: hidden;
        }
    }
}
</style>

