<template>
  <link-page class="inbound-code-item">
      <basic-info class="list-item" :id="this.pageParam.actId"></basic-info>
      <cost-goods class="list-item" ref="costGoods" :id="this.pageParam.id"  @estOutQty="showSaveBtn"></cost-goods>
      <code-record class="list-item" ref="codeRecord" :id="this.pageParam.id" :actId="this.pageParam.actId" :isShowDelete="pageParam.codeStatusActive.seq == '1'" @change="this.reloadInfo"></code-record>
      <link-sticky>
          <view class="link-fab-group-sticky">
              <link-button class="submit" @tap="save" v-if="pageParam.codeStatusActive.seq == '1'">保存</link-button>
              <link-button @tap="scanCode" v-if="isShowSaveBtn && pageParam.codeStatusActive.seq == '1'">点击扫码</link-button>
          </view>
      </link-sticky>
  </link-page>
</template>

<script>
import BasicInfo from "./components/basic-info"
import CostGoods from "./components/cost-goods"
import CodeRecord from "./components/code-record"
export default {
    name: "inbound-code-item",
    components: {BasicInfo, CostGoods, CodeRecord},
    data() {
        const confirmCodeScanningUrl = {
            4004: 'action/link/codeScan/mountQrWithoutProduct',          //千里码查不到产品及包材信息
            4005: 'action/link/codeScan/mountQrNotInMaterial',           //该产品不存在于费用申请物之中
            4006: 'action/link/codeScan/mountQrNotInMaterial',           //该产品不存在于费用申请物之中
            4008: 'action/link/codeScan/mountQrNotInGroupWhenMultiCode', //产品编码多值且均不在产品组范围
            4009: 'action/link/codeScan/mountQrAreNotEqualToMaterial'    //该产品不存在于费用申请物之中后续处理
        }
        return {
            oneFlag: true,
            val: {0: ''},             //备注字段
            confirmCodeScanningUrl,   //扫码不生效后续处理接口地址
            isShowSaveBtn: true,
            coordinate: {},          //经纬度信息
            addressInfo: {}           //扫码地址信息
        }
    },
    methods: {
        /**
         * desc 扫码动作
         * <AUTHOR>
         * @date 2021/10/12
         */
        async scanCode() {
            const that = this;
            // 只允许从相机扫码
            await wx.scanCode({
                onlyFromCamera: true,
                success(res) {
                    that.runScan(res.result)
                }
            });
        },
        /**
         * desc 获得扫码动作的数据，然后校验、保存数据
         * <AUTHOR>
         * @date 2021/10/19
         * @params info 扫码物品信息
         */
        async runScan(info) {
            this.$utils.showLoading();
            await this.getAddressInfo()
            // 真实扫码
            const params = {
                queryMark: info,
                materiallineId: this.pageParam.id,
                ...this.addressInfo
            }
            try {
                const data = await this.$http.post('action/link/codeScan/doOutStockScan', params)
                if (data.code === 4000) {
                    this.$message.success('扫码成功')
                    this.reloadInfo()  //更新对应的信息
                    return
                }
                if(data.code === 4003) {
                    this.repeatScanning(data.message)
                    this.$refs['codeRecord'].init();
                    return
                }
                this.showTips(data, info)
            } catch (e) {
                this.$showError('扫码异常', e)
            } finally {
                this.$utils.hideLoading();
            }
        },
        /**
         * desc
         * <AUTHOR>
         * @date 2021/10/19
         * @params data 初次扫码返回的数据
         * @params info 扫码物品信息
         */
        showTips(data, info) {
            this.$dialog({
                title: '提示',
                content: data.message,
                cancelButton: true,
                onConfirm: () => {
                    //4002 已开瓶、赠送
                    if(data.code === 4002) {
                        return
                    }
                    this.showRemarks(data, info)
                },
            })
        },
        /**
         * desc 展示备注弹框
         * <AUTHOR>
         * @date 2021/10/18
         * @params data 初次扫码返回的数据
         * @params info 扫码物品信息
         */
        showRemarks(data, info) {
            this.val[0] = ''
            this.$dialog({
                title: '备注',
                content: (h) => {
                    return (
                        <view style = "width:100%">
                            <link-textarea v-model={this.val[0]}/>
                        </view>
                    )
                },
                cancelButton: true,
                onConfirm: () => {
                    this.confirmCodeScanning(info, data)
                },
                onCancel: () => {
                    //返回至上一个弹窗
                    this.showTips(data, info)
                }
            })
        },
        /**
         * desc 接口正常响应但是扫码不成功的后续处理
         * <AUTHOR>
         * @date 2021/10/18
         * @params data 初次扫码返回的数据
         * @params info 扫码物品信息
         */
        async confirmCodeScanning(info, data) {
            if(!this.oneFlag) return
            this.oneFlag = false
            await this.getAddressInfo()
            //根据返回的状态码，确定不同的扫码情况，走不同的接口
            const url = this.confirmCodeScanningUrl[data.code]
            let params = {
                queryMark: info,
                materiallineId: this.pageParam.id,
                remark: this.val[0],
                ...this.addressInfo
            }
            if ([4008, 4009].includes(data.code)) {
                const addParam = {
                    randomId: data.randomId,
                    randomCode: data.randomCode,
                }
                params = {...params, ...addParam}
            }

            try {
                const data = await this.$http.post(url, params)
                if (data.code === 4000) {
                    this.$message.success('扫码成功')
                    this.reloadInfo()  //更新对应的信息
                    return
                }
                this.$showError('扫码失败')
            } catch (e) {
                this.$showError('扫码异常', e)
            } finally {
                this.oneFlag = true
            }
        },
        /**
         * desc 重复扫码
         * @auther 康丰强
         * @date 2021-10-25
         * */
        repeatScanning(msg){
            this.$nav.push('pages/lj-market-activity/inbound-code/repeat-scanning-list-page',{
                msg: msg,
                title: '扫码出库'
            })
        },
        /**
         * desc 获取当前地址
         * @auther 康丰强
         * @date 2021-11-5
         * */
        async getAddressInfo() {
            const that = this;
            this.coordinate = await that.$locations.getCurrentCoordinate();
            // 校验用户是否授权地理位置
            if (!this.$utils.isEmpty(this.coordinate.latitude) && !this.$utils.isEmpty(this.coordinate.longitude)) {
                let address = await that.$locations.reverseTMapGeocoder(this.coordinate.latitude, this.coordinate.longitude, '扫码定位');
                this.addressInfo = {
                    province: address['originalData'].result.addressComponent['province'],
                    city: address['originalData'].result.addressComponent['city'],
                    district: address['originalData'].result.addressComponent['district'],
                    scanAddr: address['originalData'].result.formatted_address
                }
                return true
            }
            this.$showError('定位失败')
            return false
        },
        save() {
            this.$nav.back()
        },
        reloadInfo() {
            this.$refs['costGoods'].init();     //更新物资行
            this.$refs['codeRecord'].init();    //更新扫码记录
            this.$bus.$emit("reloadOutList");      //更新一开始的列表页(列表页上存有物资信息)
        },
        showSaveBtn(data) {
            if(data > 0) {
                this.isShowSaveBtn = true
            } else {
                this.isShowSaveBtn = false
            }
        }
    },
    mounted() {
        this.$bus.$on("outProcessOutListReload",async () => {
            await this.reloadInfo()
        })
    }
}
</script>

<style lang="scss">
  .inbound-code-item{
      padding: 24px 0;

      .list-item{
          width: 95%;
          border-radius: 16px;
          margin: 0 auto 24px;
      }

      .link-fab-group-sticky{
          .submit{
              background-color: white;
              color: #2F69F8;
              border: 1px solid #2F69F8;
          }
      }
  }
</style>
