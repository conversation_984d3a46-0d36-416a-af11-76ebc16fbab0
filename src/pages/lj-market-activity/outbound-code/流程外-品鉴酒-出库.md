# 流程外-品鉴酒-出库

------
### 初始文档
```
创建时间：2022/01/12 14:11
创建人：  康丰强
模块路径：src/pages/lj-market-activity/outbound-code
```
### 业务背景
> 针对品鉴酒扫码融合，对企业微信端出库列表和出库扫码功能设计
### 安全性
> * 职位安全性

### 涉及组件
* 组件构成:
```
outbound-code
  ├─ components
  │  ├─ basic-info                    //outbound-code-item-page组件内引用,展示活动基础信息
  │  ├─ code-record                   //outbound-code-item-page组件内引用,展示费用申请物资
  │  └─ cost-goods                    //outbound-code-item-page组件内引用,展示出库扫码记录
  └─ outbound-code-item-page          //outbound-code-list-page组件内引用,展示出库扫码详情
  └─ outbound-code-list-page          //展示出库扫列表
  └─ outbound-code-list-details-page  //扫码记录详情组件（与流程外入库通用）
  └─ outprocess-more-record-list-page //查看更多组件（与流程外入库通用, 流程内也是用的这个组件）
  ```

* 组件路径:都在src/pages/lj-market-activity/outbound-code文件夹内

### 逻辑功能实现
```
1、查看出库列表
2、搜索框
3、查看具体物资行对应的出库扫码情况
4、查看出库扫码记录详情
5、查看更多（出库扫码记录）
6、点击扫码
7、保存
```

### 逻辑功能说明
```
1、出库扫码列表顶部筛选块为待扫码、已完成，进入页面默认定位在“待扫码”筛选块，
待扫码筛选条件如下：
    A、按照（1）中的安全性找到登陆人当前职位可以查看到的活动数据，记为a；
    B、取a中活动状态=新建、已发布、进行中、执行结束，且活动审批状态=提报待审批、申请审批通过、反馈撤回、反馈驳回的活动数据，记为b；
    C、取b的活动id，在活动费用申请物资表中，找到与此活动id关联的费用申请物资行，取产品id，记为c；
    D、取a活动数据上的归属 公司，在产品组头表上，找到公司id=这些活动的companyid，且产品组类型=品鉴酒扫码的产品组头，记为d，产品组下的产品行记为d1；
    E、以c数据产品id，匹配d1数据的产品id，若c-产品id=d1-产品id，则取c的产品id数据作为扫码记录列表的备用数据e；
    F、e的数据中，【待扫码瓶数】>0的数据，确定为“待扫码”筛选块的展示数据，按照活动id+物资行的产品id作为唯一性条件，展示待扫码记录的列表；
已完成筛选条件如下：
    A、按照（1）中的安全性找到登陆人当前职位可以查看到的活动数据，记为a；
    B、取a中活动状态=新建、已发布、进行中、执行结束、已实发，且活动审批状态=提报待审批、申请审批通过、反馈撤回、反馈驳回、反馈待审批、反馈审批通过的活动数据，记为b；
    C、取b的活动id，在活动费用申请物资表中，找到与此活动id关联的费用申请物资行，取产品id，记为c；
    D、取a活动数据上的归属 公司，在产品组头表上，找到公司id=这些活动的companyid，且产品组类型=品鉴酒扫码的产品组头，记为d，产品组下的产品行记为d1；（产品组相关逻辑找李智美或者李志臻）
    E、以c数据产品id，匹配d1数据的产品id，若c-产品id=d1-产品id，则取c的产品id数据作为扫码记录列表的备用数据e；
    F、e的数据中，【待扫码瓶数】=0的数据，确定为“已完成”筛选块的展示数据，按照活动id+物资行的产品id作为唯一性条件，展示已完成记录的列表
2、支持按照活动编码、产品编码、产品名称进行模糊查询
3、根据活动id和物资行id分别查询对应的信息
4、根据扫码行id查询对应的信息
5、当出库扫码记录大于3条展示查看更多按钮
6、当待扫码瓶>0，该按钮显示，当待扫码瓶数=0时，该按钮不显示
7、当待出库瓶数>0时显示，当待出库瓶数=0时不显示，点击按钮，返回至出库扫码列表页面；


```
