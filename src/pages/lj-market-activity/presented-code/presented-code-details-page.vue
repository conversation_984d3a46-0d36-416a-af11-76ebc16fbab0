<template>
    <view class="basic-info">
        <view class="card-content">
            <view class="card-title">转赠扫码记录</view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">产品编码</view>
                    <view class="value">{{this.detailsData.prodNum}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label-address">产品名称</view>
                    <view class="value value-address">{{this.detailsData.prodName}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label-address">盖外码(后8位)</view>
                    <view class="value value-address">{{this.detailsData.qrCodeOut && this.detailsData.qrCodeOut.split('/').pop().substr(-8)}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="this.detailsData.qrCodeIn">
                <view class="card-rows-content">
                    <view class="label-address">盖内码(后8位)</view>
                    <view class="value value-address">{{this.detailsData.qrCodeIn.split('/').pop().substr(-8)}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">扫码状态</view>
                    <view class="value">{{this.detailsData.scanRecordStatus | lov('SCAN_RECORD_STATUS')}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">匹配状态</view>
                    <view class="value">{{this.detailsData.descriptionType | lov('MATCH_STATUS')}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">是否有效</view>
                    <view class="value">{{this.detailsData.isEffective ==='Y' ? '有效': '无效'}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">扫码人</view>
                    <view class="value">{{this.detailsData.scanner}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">扫码时间</view>
                    <view class="value">{{this.detailsData.scanTime}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">是否取自包材</view>
                    <view class="value">{{this.detailsData.packagingMaterial === '1' ? '取自包材' : '不取自包材'}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label-address">匹配失败原因</view>
                    <view class="value value-address">{{this.detailsData.matchFailComment}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">备注</view>
                    <view class="value value-address">{{this.detailsData.remark}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">省</view>
                    <view class="value">{{this.detailsData.province}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">市</view>
                    <view class="value">{{this.detailsData.city}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">区</view>
                    <view class="value">{{this.detailsData.district}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">详细地址</view>
                    <view class="value">{{this.detailsData.scanAddr}}</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "presented-code-details-page.vue",
    data(){
        return{
            detailsData:{}
        }
    },
    async created() {
        await  this.getScanByID();
    },
    methods:{
        async getScanByID(){
            const data = await this.$http.post('action/link/actScanRecord/queryTypeCodeScanById',
                {
                    id: this.pageParam.data.id,
                });
            this.detailsData = data.rows;
        }
    }
}
</script>

<style lang="scss">
.basic-info {
    padding-top: 24px;
    padding-bottom: 68px;
    .card-content {
        background: #ffffff;
        border-radius: 16px;
        width: 702px;
        padding-top: 8px;
        padding-bottom: 40px;
        margin: 0px auto 24px;
        .card-title {
            @include flex-start-center;
            @include space-between;
            border-bottom: 2px solid #F2F2F2;
            font-size: 28px;
            padding: 17px 24px;
            .sync-info-title {
                color: #262626;
            }
        }
        .card-rows {
            font-family: PingFangSC-Regular,serif;
            font-size: 28px;
            letter-spacing: 0;
            line-height: 28px;
            padding: 32px 24px 0 24px;
            .card-rows-content {
                @include flex();
                @include space-between();
                .label {
                    color: #8C8C8C;
                }
                .value {
                    color: #262626;
                }
                .label-address {
                    color: #8C8C8C;
                    width: 34%;
                }
                .value-address {
                    text-align: right;
                    //width: 80%;
                    line-height: 40px;
                    color: #262626;
                    word-break: break-all;
                }
            }
        }
        .line {
            margin-top: -8px;
        }
    }
}
</style>
