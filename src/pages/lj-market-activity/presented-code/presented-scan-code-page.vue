<template>
    <link-page>

        <view class="basic-info">

            <item title="活动ID" @tap="multipleUsage">
                {{this.$utils.isEmpty(selectActId)? '请先选择活动' : selectActId}}
            </item>

             <view v-if="showFlag">
                <view class="card-content" v-for="item in actData">
                    <view class="card-title">活动信息基础</view>
                    <view class="card-rows" >
                        <view class="card-rows-content">
                            <view class="label">活动编码</view>
                            <view class="value">{{item.activityNum}}</view>
                        </view>
                    </view>
                    <view class="card-rows" >
                        <view class="card-rows-content">
                            <view class="label">活动名称</view>
                            <view class="value-address">{{item.activityName}}</view>
                        </view>
                    </view>
                    <view class="card-rows" >
                        <view class="card-rows-content">
                            <view class="label">费用小类</view>
                            <view class="value">{{item.feeType}}</view>
                        </view>
                    </view>
                    <view class="card-rows" >
                        <view class="card-rows-content">
                            <view class="label">活动类型</view>
                            <view class="value">{{item.activityType | lov('MC_TYPE')}}</view>
                        </view>
                    </view>
                    <view class="card-rows" >
                        <view class="card-rows-content">
                            <view class="label">销售大区</view>
                            <view class="value">{{item.salesRegion}}</view>
                        </view>
                    </view>
                    <view class="card-rows" >
                        <view class="card-rows-content">
                            <view class="label">销售片区</view>
                            <view class="value">{{item.salesArea}}</view>
                        </view>
                    </view>
                    <view class="card-rows" >
                        <view class="card-rows-content">
                            <view class="label">销售城市</view>
                            <view class="value">{{item.salesCity}}</view>
                        </view>
                    </view>
                    <view class="card-rows" >
                        <view class="card-rows-content">
                            <view class="label">执行案编码</view>
                            <view class="value">{{item.excCaseNum}}</view>
                        </view>
                    </view>
                    <view class="card-rows" >
                        <view class="card-rows-content">
                            <view class="label-address">无执行案说明</view>
                            <view class="value-address">{{item.noExcComment}}</view>
                        </view>
                    </view>
                    <view class="card-rows" >
                        <view class="card-rows-content">
                            <view class="label-address">子公司/经销商</view>
                            <view class="value-address">{{item.dealer}}</view>
                        </view>
                    </view>
                    <view class="card-rows" >
                        <view class="card-rows-content">
                            <view class="label">受益用户</view>
                            <view class="value">{{item.benefitAcct}}</view>
                        </view>
                    </view>
                    <view class="card-rows" >
                        <view class="card-rows-content">
                            <view class="label">执行人</view>
                            <view class="value">{{item.executor}}</view>
                        </view>
                    </view>
                </view>

                <view class="card-content">
                    <view class="card-title">费用实际物资</view>
                    <view class="card-rows" v-for="item in actData[0].actualFeeList">
                        <view class="card-fee">{{item.feePayName}}</view>
                        <view v-for="citem in item.productFee" class="feepay">
                            <view class="media-list">
                                <view class="num-view">
                                    <view class="num">{{citem.prodCode}}</view>
                                </view>
                            </view>
                            <view class="info view-margin">{{citem.prodName}}</view>
                            <view class="bottle-info">
                                <view class="bottle-row view-margin">
                                    <view class="font">
                                        <text class="textTitle">申请</text>
                                        <text class="textVal" style="text-align: right">{{citem.qty}}瓶</text>
                                    </view>
                                    <view class="font center">
                                        <text class="textTitle">总出库</text>
                                        <text class="textVal">{{citem.outQty}}瓶</text>
                                    </view>
                                    <view class="font right">
                                        <text class="textTitle">开瓶</text>
                                        <text class="textVal">{{citem.openQty}}瓶</text>
                                    </view>
                                </view>
                                <view class="bottle-row view-margin">
                                    <view class="font">
                                        <text class="textTitle">赠送</text>
                                        <text class="textVal" style="text-align: right">{{citem.giftQty}}瓶</text>
                                    </view>
                                    <view class="font center">
                                        <text class="textTitle">已入库</text>
                                        <text class="textVal">{{citem.inQty}}瓶</text>
                                    </view>
                                    <view class="font right">
                                        <text class="textTitle">待入库</text>
                                        <text class="textVal">{{citem.estInQty}}瓶</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="card-content">
                    <view class="card-title" ><text class="flex8">转赠扫码记录</text>
                        <view @tap="gotoScanList" v-if="this.autoList.list.length > 3">
                            <text class="flex1 more">查看更多</text>
                            <link-icon icon="icon-right" class="flex1"/>
                        </view>
                    </view>
                    <link-auto-list :option="autoList" :customRefresh=true >
                        <template slot-scope="{data,index}">
                            <link-swipe-action :key="index">
                            <link-swipe-option label="删除" @tap="deleteItem(data)" slot="option" v-if="deleteScanFlag"/>
                                <item v-if="index<=2" class="code-record-list" style="border-top: 2px #F2F2F2 solid;" @tap="gotoDetails(data)" :key="index" :data="data" :arrow="false">
                                    <view slot="note">
                                        <view class="list-top">
                                            <view class="code">{{data.prodNum}}</view>
                                            <image class="iseffective" :src="data.isEffective==='Y' ? $imageAssets.effective : $imageAssets.failure "></image>
                                        </view>
                                        <view class="list-middle">
                                            {{data.prodName}}
                                        </view>
                                        <view class="list-bottom clearfix">
                                            <view :class="['list-bottom-item', 'bottom-l', {issuccess: data.scanRecordStatus === 'Normalgifted'}]">{{data.scanRecordStatus | lov('SCAN_RECORD_STATUS')}}</view>
                                            <view :class="['list-bottom-item', 'bottom-c', {issuccess: data.descriptionType === 'MatchSuccessfully'}]">{{data.descriptionType | lov('MATCH_STATUS')}}</view>
                                            <view class="list-bottom-item bottom-r">{{data.scanner}}</view>
                                        </view>
                                    </view>
                                </item>
                            </link-swipe-action>
                        </template>
                    </link-auto-list>

                </view>

                <link-sticky >
                        <link-button mode="stroke" block @tap="save">保存</link-button>
                        <link-button block @tap="zhuanZengFun()" >扫码</link-button>
                </link-sticky>
             </view>

        </view>

        <link-dialog ref="boxDialog" disabledHideOnClickMask title="扫码异常提示">
            {{boxInfo}}
            <link-button slot="foot" @tap="$refs.boxDialog.hide()">关闭</link-button>
        </link-dialog>
        <link-dialog ref="confirmDialog" disabledHideOnClickMask title="扫码异常提示">
            {{confirmInfo}}
            <link-button slot="foot" @tap="$refs.confirmDialog.hide()" style="color: #333333">取消</link-button>
            <link-button slot="foot" @tap="showChooseMaterialsDialog">确定</link-button>
        </link-dialog>
        <link-dialog ref="chooseMaterialsDialog" disabledHideOnClickMask title="关联费用">
            <view class="choose-materials-view">
                <view class="choose-materials-column">
                    <view class="label">{{'费用实际物资'}}</view>
                    <view class="value" style="background:  #F7F7F7" @tap="chooseMaterials()">
                        <link-input v-model="associatedCosts.prodCode" inputReadonly
                                    :placeholder="'请选择需关联的费用实际物资'"
                                    style="background: #F7F7F7;border-radius: 4px;width: 90%;float: left;">
                        </link-input>
                        <view class="ent-wrap">
                            <view class="iconfont icon-right"
                                  style="float: left;line-height: 30px;font-size: 12px;width: 10%"></view>
                        </view>
                    </view>
                    <view class="label">备注</view>
                    <view class="value">
                        <link-textarea class="text-area" :placeholder="'请输入备注'"
                                       v-model="associatedCosts.remark" :nativeProps="{maxlength:200}"
                                       placeholder-style="color: #BFBFBF;"></link-textarea>
                    </view>
                </view>
            </view>
            <link-button slot="foot" @tap="$refs.chooseMaterialsDialog.hide()" style="color: #333333">取消</link-button>
            <link-button slot="foot" @tap="generateScanRecords">确定</link-button>
        </link-dialog>

    </link-page>
</template>

<script>
import {$utils} from "../../../utils/$utils";
import Taro from "@tarojs/taro";
import {getCurrentCoordinate, reverseTMapGeocoder} from "../../../utils/locations-tencent";
import {deleteScanCodeCheck} from "../market-activity/deleteScanCodeCheck";

export default {
    name: "presented-scan-code-page",
    data(){
        //用来判断是否为特曲公司以及阶段
        let tequCompany = false;
        let userInfo = Taro.getStorageSync('token').result;
        //特曲公司   品鉴酒与其他公司一样.
        // if(userInfo.coreOrganizationTile.brandCompanyCode === '5137'){
        //     tequCompany = true;
        // }
        return{
            oneFlag: true,
            scene:'actual',
            autoList: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: 'action/link/actScanRecord/queryGiftScanRecordByActPage'
                    },
                    param:this.scanListParam

             }),
            actData:[] , //费用实际物资 和活动信息存储对象
            showFlag:false,  //显示控制
            selectActId:'',  //选中活动id
            scanListParam: {  //扫码记录查询参数
                rows: 5,
                actId :  this.selectActId,
                scanSubType : 'GiftScan',
                scanType : 'ActProdScan'
            },
            boxInfo: "后台没有返回校验结果",//纯文本弹框内容
            confirmInfo: "",//
            associatedCosts: {
                prodCode: "",//产品编码
                prodId: "",//产品ID
                remark: "",//备注,
                materialLineId: "",//物资ID
            },//关联费用的对象
            scanRecordsData: {},//扫码记录对象
            codeData: {},//二维码数据行对象
            scanScenario: "zhuanzeng" ,
            qrCodeStatus: "",//产品二维码状态-扫码记录创建之后更新产品二维码状态时使用。
            userInfo,
            tequCompany, //特曲公司判断 true为是特曲公司  反之不是
            coordinate:{}, // 存储地理经纬度信息
            addressData:{},  // 地理位置信息
            addressDataFull:'', //详细地理位置
            addressFlag:false,  //
            deleteScanFlag: true,//是否可以侧滑删除扫码记录
        }
    },
    async created() {
        // this.deleteScanFlag = await deleteScanCodeCheck.checkFlag(this.actData[0].id);
    },
    methods:{

        /**
         * 获取定位地址  百度经纬度逆解析
         * <AUTHOR>
         * @date 2021年11月5日09:19:57
         */
        async getAddress() {
            this.coordinate = await this.$locations.getCurrentCoordinate();
            // 校验用户是否授权地理位置
            if (!this.$utils.isEmpty(this.coordinate.latitude) && !this.$utils.isEmpty(this.coordinate.longitude)) {
                let address = await reverseTMapGeocoder(this.coordinate.latitude, this.coordinate.longitude, '转赠扫码记录');
                this.addressData = address['originalData'].result.addressComponent;
                this.addressDataFull = address['originalData'].result.formatted_address;
                this.addressFlag = true;
            }
        },
        /**
         * 删除扫码记录部分
         * @auther 吕志平
         * @date 2021年11月1日16:08:28
         * */
        async deleteItem(data) {
            this.$dialog({
                title: '提示',
                content: '是否确认删除扫码记录',
                cancelButton: true,
                onConfirm: () => {
                    this.confirmDelete(data)
                },
            })
        },
        async confirmDelete(data) {
            const params =  {
                id: data.id,
                feature: this.tequCompany ? 'Y':''
            }
            try {
                const data = await this.$http.post('action/link/actScanRecord/giftDeleteRecord', params);
                if(data.success) {
                    const datac = await this.$http.post('action/link/actualFee/queryAndGroupData/edit', {filtersRaw: [{id: "actId", property: "actId", value: this.actData[0].id, operator: "="}],queryType : 'actualQtyUpdate'});
                    this.$message.success('删除数据成功');
                    await  this.reload()
                    this.$emit('change', true)
                    return
                }
                this.$showError('删除数据失败');
            }catch (e) {
                this.$showError(`删除数据异常:${e.result}`);
            }
        },
        /**
         * 删除后刷新部分数据
         * @auther 吕志平
         * @date 2021年11月1日16:08:28
         * */
       async reload() {
            this.showFlag = false;
           this.autoList.methods.reload()
           this.actData=[];
           const data = await this.$http.post('action/link/actScanRecord/queryActGiftScanByActId',
               {
                   //特曲公司校验
                   attr4:this.tequCompany? 'Y' : '',
                   id:this.selectActId
               });
           this.actData.push(data.rows);
            this.showFlag = true;
        },

        /**
         * 监听选择活动的返回事件 存在异步 不能放在callback里
         * @auther 吕志平
         * @date 2021年11月1日16:08:28
         * */
        async onBack(param){
            if(this.$utils.isEmpty(param) && !this.$utils.isEmpty(this.selectActId)){
                this.actData = [];
                this.autoList.option.param={
                    rows: 5,
                    actId :  this.selectActId,
                    scanSubType : 'GiftScan',
                    scanType : 'ActProdScan'
                };
                //加载遮罩
                this.showFlag = false;
                this.$utils.showLoading();
                const normalData = await this.$http.post('action/link/actScanRecord/queryActGiftScanByActId',
                    {
                        //特曲公司校验
                        attr4:this.tequCompany? 'Y' : '',
                        id:this.selectActId
                    });
                this.actData.push(normalData.rows);
                this.autoList.methods.reload();
                //加载完毕 显示内容
                this.$utils.hideLoading();
                if(!this.$utils.isEmpty(normalData.rows)){
                    this.showFlag = true;
                }
                return;
            }
            if (this.$utils.isEmpty(param)){
                return;
            }
            if(!this.$utils.isEmpty(param.data)){
                    this.actData = [];
                    this.selectActId = param.data;
                    this.autoList.option.param={
                        rows: 5,
                        actId :  param.data,
                        scanSubType : 'GiftScan',
                        scanType : 'ActProdScan'
            };
            //加载遮罩
            this.$utils.showLoading();
                this.showFlag = false;
            const data = await this.$http.post('action/link/actScanRecord/queryActGiftScanByActId',
                {
                    //特曲公司校验
                    attr4:this.tequCompany? 'Y' : '',
                   id:param.data
                });
            this.actData.push(data.rows);
            //加载完毕 显示内容
            this.$utils.hideLoading();
            if(!this.$utils.isEmpty(param)){
                this.showFlag = true;
            }
            }
        },
        multipleUsage() {
            this.$nav.push('/pages/lj-market-activity/presented-code/presented-act-scan-page')
        },

        save(){
            this.$nav.back();
        },
        /**
         * 流程内-品鉴酒融合-赠送扫码  扫码前出库数校验
         * @auther 吕志平
         * @date 2021年10月21日20:12:13
         * */
        async zhuanZengFun() {
                await this.getAddress();
            if(this.tequCompany){
                this.zhuanZengFunScanCode();
                return;
            }
            try {
                this.$utils.showLoading();
                const data = await this.$http.post('action/link/actualFee/scanCodeVerificationGift', {
                    actId: this.actData[0].id,
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    //判断 bullet和msg是否有返回值
                    if (this.$utils.isEmpty(data['bullet']) || this.$utils.isEmpty(data['msg'])) {
                        this.showMsg();
                    } else {
                        //纯提示类型
                        if (data['bullet'] === 'box') {
                            // this.showMsg(data['msg']);
                            this.$showError(data.msg);
                        }
                        //数据检查无问题 开始扫码
                        if (data['bullet'] === 'none') {
                            this.zhuanZengFunScanCode();
                        }
                    }
                } else {
                    this.$utils.hideLoading();
                    this.$showError('校验失败，请稍后重试！' + data.detailMessage);
                }
            } catch (e) {
                this.$showError(e+'校验失败，请稍后重试！');
            } finally {
                this.$utils.hideLoading();
            }
        },
        /**
         * 流程内-转赠扫码 扫码
         * @auther 吕志平
         * @date 2021年10月22日11:17:54
         * */
        async zhuanZengFunScanCode() {
            const that = this;
            // 只允许从相机扫码
            await wx.scanCode({
                onlyFromCamera: true,
                success(res) {
                    // 调后台校验接口
                    that.scanCodeAfter(res.result);
                }
            });
        },
        /**
         * 流程内-转赠扫码 扫码结果多种情况校验
         * @auther 吕志平
         * @date 2021年10月22日11:17:54
         * */
        async scanCodeAfter(mark) {
            try {
                const queryParam = {
                    mark: mark,
                    actId: this.actData[0].id,
                    feature : this.tequCompany ? 'Y' : ''  //是否为特曲公司 Y是  不传就不是
                };
                this.$utils.showLoading();
                const data = await this.$http.post('action/link/codeProductRecord/codeProductRecordVerificationGift',
                    queryParam);
                if (data.success) {
                    this.$utils.hideLoading();
                    if (this.$utils.isEmpty(data['bullet']) || this.$utils.isEmpty(data['msg'])) {
                        this.showMsg();
                    } else {
                        if (data['bullet'] === 'box') {
                            // this.showMsg(data.msg);
                            this.$showError(data.msg);
                        }
                        //包含确认的
                        if (data['bullet'] === 'confirm') {
                            //保存转赠扫码行数据
                            this.codeData = data;
                            //多个物资行情况
                            this.showConfirm(data);
                        }
                        //数据检查无问题 开始扫码
                        if (data['bullet'] === 'none') {
                            await this.generateScanRecords(data);
                        }
                        //跳转类型
                        if (data['bullet'] === 'skipBox') {
                            this.sweepRepetitionCode(data['msg'], data.rows);
                        }
                    }
                } else {
                    this.$utils.hideLoading();
                    this.$showError('校验失败，请稍后重试！' + data.result);
                }
            } catch (e) {
                this.$showError(e+'校验失败，请稍后重试！');
            } finally {
                this.$utils.hideLoading();
            }
        },
        /**
         * 流程内-品鉴酒融合-校验返回 带确认的弹框使用
         * @auther songyanrong
         * @date 2021-10-20
         * */
        showConfirm(input) {
            this.confirmInfo = input.msg;
            this.$refs.confirmDialog.show();
        },
        /**
         * 流程内-品鉴酒融合-校验返回 纯信息弹出提示使用
         * @auther songyanrong
         * @date 2021-10-20
         * */
        showMsg(msg) {
            if (!this.$utils.isEmpty(msg)) {
                this.boxInfo = msg;
            }
            this.$refs.boxDialog.show();
        },
        /**
         * 流程内-品鉴酒融合-校验返回 重复扫码
         * @auther songyanrong
         * @date 2021-10-20
         * msg: 重复扫码的弹出信息
         * rows：重复扫码需要生成扫码记录的对象
         * type: 区分扫码场景
         * */
        sweepRepetitionCode(msg, rows) {
            this.$nav.push('pages/lj-market-activity/market-activity/sweep-repetition-code-page.vue', {
                msg: msg,
                data: rows,
                activityItem: this.actData[0],
                addressData: this.addressData,  // 地理位置信息
                addressDataFull:this.addressDataFull, //详细地理位置
                addressFlag: this.addressFlag,
                type: this.scanScenario
            })
        },
        /**
         * 流程内-品鉴酒融合-校验返回 选择物资的弹框展示
         * @auther songyanrong
         * @date 2021-10-20
         * */
        showChooseMaterialsDialog() {
            this.$refs.confirmDialog.hide();
            this.$refs.chooseMaterialsDialog.show();
        },
        /**
         * 流程内-品鉴酒融合-校验返回 点击选物资弹窗
         * @auther songyanrong
         * @date 2021-10-20
         * */
        chooseMaterials() {
            this.$nav.push('pages/lj-market-activity/market-activity/pinjianjiu-choose-materials-page.vue', {
                scene: this.scene,
                activityItem: this.actData[0],
                scanScenario: this.scanScenario,//扫码类型
                tequCompany : this.tequCompany,  //特曲公司判断 true为是特曲公司  反之不是
                codeData : this.codeData,
                callback: (data) => {
                    this.associatedCosts.prodCode = data.prodCode;
                    this.associatedCosts.prodId = data.prodId;
                    this.associatedCosts.materialLineId = data.id;//物资行ID
                },
            })
        },
        /**
         * 流程内-品鉴酒融合-校验返回 确定时生成扫码记录
         * @auther songyanrong
         * @date 2021-10-20
         * rows: 只匹配一个直接生成扫码记录的场景，扫码返回的二维码对象
         * 说明：如果rows和type没值就是选择物资的场景。有值的话 是匹配到一条产品需要前端直接生成扫码记录的
         * */
        async generateScanRecords(data) {
            if(!this.oneFlag) return
            this.oneFlag = false
            let test = data;
            //对象处理放大可以合为一个，但由于考虑后续写到一个方法里判断会比较多，
            // 所以写了俩个处理对象的方法，一个用于仅一个匹配直接生成扫码数据 另外一个用户选择物资后生成扫码数据。
            if (this.$utils.isNotEmpty(test.rows)) {
                await this.dealOnlyOneMatchScanRecords(test);
                //插入扫码记录
                await this.$http.post('action/link/actScanRecord/insert', this.scanRecordsData);
                //更新产品二维码状态
                await this.$http.post('action/link/codeProductRecord/update', {
                    id: this.scanRecordsData.prodQrCodeId,
                    qrCodeStatus: this.qrCodeStatus,
                    updateFields: "id,qrCodeStatus"
                });
                if(this.$refs.scanCodeTypeRef){
                    this.$refs.scanCodeTypeRef.hide();
                }
                this.$refs.chooseMaterialsDialog.hide();
                this.$utils.showLoading();
                const actdata = await this.$http.post('action/link/actScanRecord/queryActGiftScanByActId',
                    {
                        id:this.actData[0].id
                    });
                if(!this.$utils.isEmpty(actdata.rows)){
                    this.actData=[];
                    this.actData.push(actdata.rows);
                };
                this.autoList.methods.reload();
                const data = await this.$http.post('action/link/actualFee/queryAndGroupData/edit', {filtersRaw: [{id: "actId", property: "actId", value: this.actData[0].id, operator: "="}],queryType : 'actualQtyUpdate'});
                this.$utils.hideLoading();
                this.$message.success('扫码成功');
                this.oneFlag = true
            } else {
                if (!this.associatedCosts.materialLineId) {
                    this.$message.warn('请先选择费用物资');
                    this.oneFlag = true
                    return
                }
                await this.dealOnlyOneMatchScanRecords();
                //插入扫码记录
                await this.$http.post('action/link/actScanRecord/insert', this.scanRecordsData);
                //更新产品二维码状态
                await this.$http.post('action/link/codeProductRecord/update', {
                    id: this.scanRecordsData.prodQrCodeId,
                    qrCodeStatus: this.qrCodeStatus,
                    updateFields: "id,qrCodeStatus"
                });
                if(this.$refs.scanCodeTypeRef){
                    this.$refs.scanCodeTypeRef.hide();
                }
                this.$refs.chooseMaterialsDialog.hide();
                this.$utils.showLoading();
                const actdata = await this.$http.post('action/link/actScanRecord/queryActGiftScanByActId',
                    {
                        id:this.actData[0].id
                    });
                if(!this.$utils.isEmpty(actdata.rows)){
                    this.actData=[];
                    this.actData.push(actdata.rows);
                };
                this.autoList.methods.reload();
                this.$utils.hideLoading();
                const data = await this.$http.post('action/link/actualFee/queryAndGroupData/edit', {filtersRaw: [{id: "actId", property: "actId", value: this.actData[0].id, operator: "="}],queryType : 'actualQtyUpdate'});
                this.$message.success('扫码成功');
                this.oneFlag = true
            }
        },
        /**
         * 流程内-品鉴酒融合 处理扫码记录对象
         * @auther songyanrong
         * @date 2021-10-27
         * rows: 只匹配一个直接生成扫码记录的场景，扫码返回的二维码对象
         * type: 扫码场景
         * 说明：如果rows和type没值就是选择物资的场景。有值的话 是匹配到一条产品需要前端直接生成扫码记录的
         * */
        async dealOnlyOneMatchScanRecords(data) {
            let descriptionType = "";
            let isEffective = "";
            let scanRecordStatus = "";
            let scanType = "";
            let scanSubType = "";
            if (this.scanScenario === 'zhuanzeng') {
                scanRecordStatus = 'Normalgifted';//正常出库 扫码状态 SCAN_RECORD_STATUS
                scanType = 'ActProdScan';//活动物资扫码 扫码类型 SCAN_TYPE
                scanSubType = 'GiftScan';//转赠扫码 扫码子类型 值列表类型: SCAN_SUB_TYPE
                this.qrCodeStatus = "Gifted";//创建完赠送扫码记录 更新状态为"已赠送" PJ_USER_TYPE
            }
            //只一个产品匹配的场景
            if (this.$utils.isNotEmpty(data)) {
                if (data['match']) {
                    descriptionType = 'MatchSuccessfully';//匹配状态:匹配成功 MATCH_STATUS
                    isEffective = "Y";//有效
                } else {
                    descriptionType = 'MatchFailed';//匹配状态:匹配失败 MATCH_STATUS
                    isEffective = "N";//无效
                }
                if(this.addressFlag) {
                    this.scanRecordsData = {
                        prodId: data.rows['prodId'],//产品id
                        prodQrCodeId: data.rows['id'],//产品码id
                        prodQrCode: data.rows['qrCodeOut'],//产品码
                        prodQrCodeType: 2,//产品码类型按照盖外码赋值为2
                        materialLineId: data.rows['materialLineId'],//物资行id
                        packagingMaterial: data.rows['packagingMaterial'],//是否包材
                        descriptionType: descriptionType,
                        isEffective: isEffective,
                        scanRecordStatus: scanRecordStatus,
                        scanType: scanType,
                        scanSubType: scanSubType,
                        scanner: this.userInfo.firstName,
                        province: this.addressData.province,
                        city:this.addressData.city,
                        district:this.addressData.district,
                        //districtCode:this.addressData.adcode,
                        scanAddr:this.addressDataFull,
                        actId: this.selectActId,
                        scanSource: 'StaffSystem'
                    }
                }else {
                    this.scanRecordsData = {
                        prodId: data.rows['prodId'],//产品id
                        prodQrCodeId: data.rows['id'],//产品码id
                        prodQrCode: data.rows['qrCodeOut'],//产品码
                        prodQrCodeType: 2,//产品码类型按照盖外码赋值为2
                        materialLineId: data.rows['materialLineId'],//物资行id
                        packagingMaterial: data.rows['packagingMaterial'],//是否包材
                        descriptionType: descriptionType,
                        isEffective: isEffective,
                        scanRecordStatus: scanRecordStatus,
                        scanType: scanType,
                        scanSubType: scanSubType,
                        scanner: this.userInfo.firstName,
                        actId: this.selectActId,
                        scanSource: 'StaffSystem'
                    }
                }
            } else {
                //需要选择物资的场景
                if (this.codeData['match']) {
                    descriptionType = 'MatchSuccessfully';//匹配状态:匹配成功 MATCH_STATUS
                    isEffective = "Y";//有效
                } else {
                    descriptionType = 'MatchFailed';//匹配状态:匹配失败 MATCH_STATUS
                    isEffective = "Y";//无效
                }
                let prodId = "";
                if (this.$utils.isNotEmpty(this.codeData.rows['prodId'])) {
                    prodId = this.codeData.rows['prodId'];
                } else {
                    prodId = this.associatedCosts.prodId;
                }
                if (this.addressFlag){
                    this.scanRecordsData = {
                        actId: this.selectActId,
                        prodId: prodId,//产品id
                        prodQrCodeId: this.codeData.rows['id'],//产品码id
                        prodQrCode: this.codeData.rows['qrCodeOut'],//产品码
                        prodQrCodeType: 2,//产品码类型按照盖外码赋值为2
                        packagingMaterial: this.codeData.rows['packagingMaterial'],//是否包材
                        materialLineId: this.associatedCosts.materialLineId,//物资行id
                        remark: this.associatedCosts.remark,//备注
                        descriptionType: descriptionType,
                        isEffective: isEffective,
                        scanRecordStatus: scanRecordStatus,
                        scanType: scanType,
                        scanSubType: scanSubType,
                        scanner:this.userInfo.firstName,
                        province: this.addressData.province,
                        city:this.addressData.city,
                        district:this.addressData.district,
                        //districtCode:this.addressData.adcode,
                        scanAddr:this.addressDataFull,
                        scanSource: 'StaffSystem'
                    }
                }else {
                    this.scanRecordsData = {
                        actId: this.selectActId,
                        prodId: prodId,//产品id
                        prodQrCodeId: this.codeData.rows['id'],//产品码id
                        prodQrCode: this.codeData.rows['qrCodeOut'],//产品码
                        prodQrCodeType: 2,//产品码类型按照盖外码赋值为2
                        packagingMaterial: this.codeData.rows['packagingMaterial'],//是否包材
                        materialLineId: this.associatedCosts.materialLineId,//物资行id
                        remark: this.associatedCosts.remark,//备注
                        descriptionType: descriptionType,
                        isEffective: isEffective,
                        scanRecordStatus: scanRecordStatus,
                        scanType: scanType,
                        scanSubType: scanSubType,
                        scanner:this.userInfo.firstName,
                        scanSource: 'StaffSystem'
                    }
                }
            }
        },
        /**
         * @auther 吕志平
         * @date 2021年11月1日16:59:39
         * 说明：跳转到扫码记录查看更多 参数活动id
         * */
        gotoScanList(){
            this.$nav.push('/pages/lj-market-activity/presented-code/presented-code-itemlist-page', {data: this.actData[0]})
        },
        /**
         * @auther 吕志平
         * @date 2021年11月1日16:59:39
         * 说明：跳转到扫码详情界面 参数: 选中当行的扫码记录
         * */
        gotoDetails(data) {
            this.$nav.push('/pages/lj-market-activity/presented-code/presented-code-details-page', {data: data})
        },
    }
}
</script>

<style lang="scss">
.link-auto-list-no-more{
    display: none !important;
}
.choose-materials-view {
    background: #ffffff;
    border-radius: 16px;
    width: 100%;

    .choose-materials-column {
        @include flex();
        @include direction-column();

        .label {
            padding: 32px 0 24px 24px;
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #595959;
            letter-spacing: 0;
            line-height: 28px;
        }

        .value {
            font-family: PingFangSC-Regular, serif;
            font-size: 28px;
            color: #000000;
            letter-spacing: 0;
            line-height: 28px;
            margin: 0 12px 0 12px;

            .text-area {
                width: 100%;
                color: #595959;
                border-radius: 16px;
            }

            textarea {
                height: 200px;
            }

            .ent-wrap {
                color: #BFBFBF;
                white-space: nowrap;
                display: inline-block;
                height: 60px;
                line-height: 60px;
            }
        }
    }
}
.basic-info {
    padding-bottom: 130px;
    .link-sticky{
        z-index: 3 !important;
    }
    .link-icon{
        color: #BFBFBF;
        font-size: 32px;
        align-items: end;
    }
    .card-content {
        background: #ffffff;
        border-radius: 16px;
        width: 95%;
        padding-top: 8px;
        padding-bottom: 40px;
        margin: 24px auto 24px;
        .feepay{
            margin: 20px auto;
        }
        .card-fee{
            @include flex-start-center;
            @include space-between;
            font-size: 28px;
            padding: 17px 0px;
            .sync-info-title {
                color: #262626;
            }
        }
        .card-title {
            @include flex-start-center;
            @include space-between;
            border-bottom: 2px solid #F2F2F2;
            font-size: 28px;
            padding: 17px 24px;
            .sync-info-title {
                color: #262626;
            }
        }
        .card-rows {
            font-family: PingFangSC-Regular,serif;
            font-size: 28px;
            letter-spacing: 0;
            line-height: 28px;
            padding: 32px 24px 0 24px;
            .card-rows-content {
                @include flex();
                @include space-between();
                .label {
                    color: #8C8C8C;
                }
                .value {
                    color: #262626;
                }
                .label-address  {
                    color: #8C8C8C;
                    width: 34%;
                }
                .value-address {
                    text-align: right;
                    //width: 80%;
                    line-height: 40px;
                    color: #262626;
                    word-break: break-all;
                }
            }
        }
        .code-record-list{
            padding-right: 0px;
            width: 95%;
            .list-top{
                font-size: 28px;
                display: flex;
                justify-content: space-between;

                .code{
                    background-color:  #A6B4C7;
                    color: white;
                    height: 48px;
                    padding: 7px 19px;
                    margin: 10px 0;
                    border-radius: 10px;
                }
                .iseffective{
                    width: 92px;
                    height: 48px;
                }

            }

            .list-middle{
                font-family: PingFangSC-Medium;
                font-size: 32px;
                color: #262626;
                line-height: 45px;
                font-weight: 600;
                margin: 10px 0;
            }

            .list-bottom{
                padding: 10px 0 20px 0;

                .list-bottom-item{
                    font-size: 28px;
                    float: left;
                    width: 33.3%;
                }

                .bottom-l{
                    color: #FF5A5A ;
                }

                .bottom-c{
                    text-align: center;
                    color: #FF5A5A ;
                }

                .bottom-r{
                    text-align: right;
                }

                .issuccess{
                    color: #2EB3C2;
                }
            }

            .clearfix:after {
                content: "";
                display: block;
                height: 0;
                clear: both;
                visibility: hidden;
            }
        }

        .line {
            margin-top: -8px;
        }
        .media-list{
            display: flex;
            position: relative;
            .num-view {
                background: #A6B4C7;
                border-radius: 8px;
                margin-bottom: 6px;
                .num {
                    font-size: 28px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    line-height: 40px;
                    padding: 2px 8px;
                }
            }
        }

        .view-margin{
            margin: 20px auto;
        }
        .font{
            color: #000000;
            font-size: 28px;
            .textTitle{
                color:#8C8C8C;
                font-size: 28px;
            }
            .textVal{
                display: inline-block;
                width: 3rem;
            }
        }
        .info{
            color: #000000;
            font-size: 28px;
            font-weight: bold;
        }
        .bottle-info{
            width: 100%;
            .bottle-row{
                display: flex;
                flex-direction: row;
                view{
                    flex: 1;
                }
                .center{
                    text-align: center;
                }
                .right{
                    text-align: right;
                }
            }
        }
        .flex{
            display: flex;
        };
        .flex8{
            flex:8;
        }
        .flex1{
            flex:1;
        }
        .succeed{
            color: #2EB3C2;
        }
        .fail{
            color:#FF5A5A ;
        }
        .more{
            color: #C7C3C0;
            word-break: keep-all;
            font-size: 0.8em;
        }
        .state{
            position: absolute;
            top: 4px;
            right: 0px;
            line-height: 40px;
            width: 92px;
            height: 40px;
            image{
                width: 100%;
                height: 100%;
            }
        }
    }

}

</style>
