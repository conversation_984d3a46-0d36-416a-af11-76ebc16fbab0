<template>
    <link-page class="card-content">
        <!--        <link-auto-list :option="autoList" hideCreateButton :searchInputBinding="{props:{placeholder:'活动编码/产品编码/产品名称'}}">-->
        <link-auto-list :option="autoList" hideCreateButton :searchInputBinding="{props:{placeholder:'扫码人/产品编码/产品名称'}}">
            <link-filter-group slot="filterGroup">
                <link-filter-item label="有效" :param="{filter:{property:'isEffective',value:'Y',operator:'LIKE'}}"/>
            </link-filter-group>
            <template slot-scope="{data,index}">
                <link-swipe-action :key="index">
                    <link-swipe-option label="删除" @tap="deleteItem(data)" v-if="deleteScanFlag" slot="option"/>
                <item :key="index" :data="data" :arrow="false" class="scan-list"   >
                    <view class="card-rows" style="border-top: 2px #F2F2F2 solid;" @tap="gotoDetails(data)" slot="note">
                        <view class="media-list">
                            <view class="num-view">
                                <view class="num">{{data.prodNum}}</view>
                            </view>
                            <view class="state"><image :src="data.isEffective==='Y' ? $imageAssets.effective : $imageAssets.failure "></image></view>
                        </view>

                        <view class="flex  view-margin">
                            <view class="info flex8" >{{data.prodName}}</view>
                            <link-icon icon="icon-right" class="flex1"/>
                        </view>

                        <view class="bottle-info">
                            <view class="bottle-row view-margin">
                                <view class="font succeed">{{data.scanRecordStatus | lov('SCAN_RECORD_STATUS')}}</view>
                                <view class="font center succeed">{{data.descriptionType | lov('MATCH_STATUS')}}</view>
                                <view class="font right">{{data.scanner}}</view>
                            </view>
                        </view>

                        <view class="qr-code">
                            <view class="qr-code-item">
                                <view class="lebal">盖外码:</view>
                                <view class="val">{{data.qrCodeOut && data.qrCodeOut.split('/').pop().substr(-8)}}</view>
                            </view>
                            <view class="qr-code-item" v-if="data.qrCodeIn">
                                <view class="lebal">盖内码:</view>
                                <view class="val">{{data.qrCodeIn.split('/').pop().substr(-8)}}</view>
                            </view>
                        </view>
                    </view>
                </item>
                </link-swipe-action>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
import {deleteScanCodeCheck} from "../market-activity/deleteScanCodeCheck";

export default {
    name: "presented-code-itemlist-page.vue",
    data(){
        const autoList = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/actScanRecord/queryTypeCodeScanByActNumPage'
            },
            param: {
                materialLineId : this.pageParam.data.materialLineId,
                activityNum: this.pageParam.data.activityNum,
                scanType:"ActProdScan",
                scanSubType:"GiftScan",
                filtersRaw: []
            },
            searchFields: ['scanner', 'prodNum', 'prodName'],
            filterOption:[
                {label: '扫码状态', field: 'scanRecordStatus', type: 'lov',lov: 'SCAN_RECORD_STATUS'},
                {label: '匹配状态', field: 'descriptionType', type: 'lov',lov: 'MATCH_STATUS'}
            ]
        });
        return{
            autoList,
            deleteScanFlag:false
        }

    },
    async created() {
        this.deleteScanFlag = await deleteScanCodeCheck.checkFlag(this.pageParam.data.id);
    },
    methods:{
        async deleteItem(data) {
            this.$dialog({
                title: '提示',
                content: '是否确认删除扫码记录',
                cancelButton: true,
                onConfirm: () => {
                    this.confirmDelete(data)
                },
            })
        },
        async confirmDelete(data) {
            const params =  {
                id: data.id,
            }
            try {
                const data = await this.$http.post('action/link/actScanRecord/giftDeleteRecord', params);
                if(data.success) {
                    this.$message.success('删除数据成功');
                    this.autoList.methods.reload();
                    this.$emit('change', true)
                    return
                }
                this.$showError('删除数据失败');
            }catch (e) {
                this.$showError(`删除数据异常:${e}`);
            }
        },
        gotoDetails(data) {
            this.$nav.push('/pages/lj-market-activity/presented-code/presented-code-details-page', {data: data})
        },
    }

}
</script>

<style lang="scss">

.link-item-icon{
    width: 0 !important;
}
.link-icon{
    color: #BFBFBF;
    font-size: 32px;
    align-items: end;
}
.card-content {
    background: #ffffff;
    border-radius: 16px;
    padding-top: 8px;
    padding-bottom: 40px;
    margin: 0px auto 24px;
    .card-title {
        @include flex-start-center;
        @include space-between;
        border-bottom: 2px solid #F2F2F2;
        font-size: 28px;
        padding: 17px 24px;
        .sync-info-title {
            color: #262626;
        }
    }
    .card-rows {
        font-family: PingFangSC-Regular,serif;
        font-size: 28px;
        letter-spacing: 0;
        line-height: 28px;
        padding: 32px 24px 0 24px;
        .card-rows-content {
            @include flex();
            @include space-between();
            .label {
                color: #8C8C8C;
            }
            .value {
                color: #262626;
            }
            .label-address  {
                color: #8C8C8C;
                width: 34%;
            }
            .value-address {
                text-align: right;
                //width: 80%;
                line-height: 40px;
                color: #262626;
                word-break: break-all;
            }
        }
    }
    .line {
        margin-top: -8px;
    }
    .media-list{
        display: flex;
        position: relative;
        .num-view {
            background: #A6B4C7;
            border-radius: 8px;
            margin-bottom: 6px;
            .num {
                font-size: 28px;
                color: #FFFFFF;
                letter-spacing: 0;
                line-height: 40px;
                padding: 2px 8px;
            }
        }
    }

    .view-margin{
        margin: 20px auto;
    }
    .font{
        color: #000000;
        font-size: 28px;
        text{
            color:#8C8C8C;
            font-size: 28px;
            margin-right: 1em;
        }
    }
    .info{
        color: #000000;
        font-size: 28px;
        font-weight: bold;
    }
    .bottle-info{
        width: 100%;
        .bottle-row{
            display: flex;
            flex-direction: row;
            view{
                flex: 1;
            }
            .center{
                text-align: center;
            }
            .right{
                text-align: right;
            }
        }
    }
    .qr-code{
        display: flex;
        justify-content: space-between;
        font-size: 28px;
        .qr-code-item{
            display: flex;
            .lebal{
                color:#8C8C8C;
                margin-right: 8px;
            }
            .val {
                color: #262626;
            }
        }
    }
    .flex{
        display: flex;
    };
    .flex8{
        flex:8;
    }
    .flex1{
        flex:1;
    }
    .succeed{
        color: #2EB3C2;
    }
    .fail{
        color:#FF5A5A ;
    }
    .more{
        color: #C7C3C0;
        word-break: keep-all;
        font-size: 0.8em;
    }
    .state{
        position: absolute;
        top: 4px;
        right: 0px;
        line-height: 40px;
        width: 92px;
        height: 40px;
        image{
            width: 100%;
            height: 100%;
        }
    }
}

</style>
