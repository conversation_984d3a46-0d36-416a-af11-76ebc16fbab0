# 品鉴酒-转赠扫码


------
* 初始文档
```
创建时间：2022年1月12日
创建人：  吕志平
```
* 模块介绍
> 本菜单针对品鉴酒扫码融合,对企业微信端在费用申请流程中的赠送扫码功能设计.
> * 展示拥有转赠扫码记录的活动信息与扫码记录提供查询和筛选功能
> * 可以选择 : 活动的状态=已发布、进行中、执行结束；审批状态=申请审批通过、反馈待审批、反馈撤回、反馈驳回；活动中所有品鉴酒物资行的（总开瓶数 + 赠出瓶数 + 已入库数）需小于 总申请数量。的活动进行转赠扫码操作.
> * 支持扫码记录左滑删除


* 涉及对象
> * 市场活动-本系统
> * 扫码记录-本系统
> * 产品行(物资行)-本系统


* 是否共用
> * 共用模块1
    在市场活动状态为executiveFeedback(执行反馈)阶段时,如若还有剩余的出库产品时可以进行赠送操作.


* 数据存储
> * 数据来源 本系统数据库
> * 存储方式 本系统数据库
> * 是否同步 否

* 缓存机制
> * 是否缓存 否

* 安全性
> * 根据登录用户当前职位的职位类型定义，包含一下三种情况：
> * A、当前登录人职位类型=业务代表Salesman、业务主管SalesSupervisor、团购经理GroupBuyManager、客户经理AccountManager、客服经理CustServiceManager、VIP经理VipManager、客服专员CustServiceSpecialist、客服主管CustServiceSupervisor、小组组长SalesTeamLeader、渠道经理SalesChannelManger时，用当前登录人职位，与活动-内部人员表里面的数据匹配，查询该职位及下级职位的活动数据关联的费用实际行数据展示；
> * B、当前登录人职位类型=会战指挥长BattleCommander、品牌销管部部长BPSalesManager、总部内勤HeadquartersStuff、大区内勤RInternalStaff、渠道管理部部长CMDeptDirector、渠道主管ChannelSupervisor、会员管理部经理MemberManager、品牌联络部主管BLRegionManager、品牌推广部主管BPRegionManager、品牌推广部部长BPDeptManager、销售公司总经理SalesGeneralManager、品牌公司总经理BrandManager、片区内勤InternalStaff、系统管理员SysAdmin、城市经理SalesManager，SalesCityManager、片区经理SalesAreaManager、大区经理SalesRegionManager、战区经理、股份公司总经理GeneralManager、城市内勤CInternalStaff、品牌公司信息管理员BrandSysAdmin、片区信息专员RegionSysAdmin时，按照当前登录人职位的组织，与活动头上的orgid匹配查看组织及下级组织活动数据关联的费用实际行数据展示；
> * C、当前登录人职位类型=稽核人员AuditStaff、财务人员FinanceStaff、纪检人员InspectionStaff时，按照当前登录人职位的稽核人员安全性与活动头上的归属公司和省-市-区县匹配，查看对应品牌公司和省市区县的活动数据关联费用实际行数据展示；


* 状态流转
> 触发流程 : 当选择活动进行扫码操作时下列三种状态字段会根据条件变更
#### 一 scanRecordStatus ，扫码状态 值列表(SCAN_RECORD_STATUS)
> * 1.产品码状态=“已出库”且扫码状态=正常出库
    2.存在是否有效字段值为“有效”且产品码id匹配的扫码记录.
    3.行物资的（总开瓶数 + 赠出瓶数 + 已入库数）小于 总出库瓶数
    满足上述条件给扫码记录的扫码状态(scanRecordStatus)赋值为“正常赠送”(Normalgifted)
> * 1.当产品码状态=已赠送时，且存在该产品码的扫码状态=正常赠送的扫码，
    2.存在是否有效字段值为“有效”记录时,扫码状态(scanRecordStatus)赋值为“重复赠送”(Repeatgifted)
#### 二 isEffective ，是否有效
> * 1.产品码状态=“已出库”且扫码状态=正常出库
    2.存在是否有效字段值为“有效”且产品码id匹配的扫码记录.
    3.匹配的行物资的（总开瓶数 + 赠出瓶数 + 已入库数）小于 总出库瓶数
    满足上述条件给扫码记录的是否有效字段(isEffective)赋值为“有效”(Y)
> * 1.产品码状态=“已出库”且扫码状态=正常出库
    2.存在是否有效字段值为“有效”且产品码id匹配的扫码记录.
    3.产品码不匹配的行物资的产品码时,且选中的行物资信息满足（总开瓶数 + 赠出瓶数 + 已入库数）小于 总出库瓶数
    4.判断产品二维码中的产品是否属于当前操作人员职位对应的组织所属品牌公司中产品分组类型为品鉴酒的产品，是则是否有效字段(isEffective)赋值为“有效（Y）”，否则赋值为“无效（N）”；
#### 三 descriptionType，扫码记录的匹配状态  值列表(MATCH_STATUS)
> * 1.产品码状态=“已出库”且扫码状态=正常出库
    2.存在是否有效字段值为“有效”且产品码id匹配的扫码记录.
    3.匹配的行物资的（总开瓶数 + 赠出瓶数 + 已入库数）小于 总出库瓶数
    满足上述条件给扫码记录的匹配状态字段(descriptionType)赋值为“匹配成功”(MatchSuccessfully)
> * 1.产品码状态=“已出库”且扫码状态=正常出库
    2.存在是否有效字段值为“有效”且产品码id匹配的扫码记录.
    3.产品码不匹配的行物资的产品码时,且选中的行物资信息满足（总开瓶数 + 赠出瓶数 + 已入库数）小于 总出库瓶数
    满足上述条件给扫码记录的匹配状态字段(descriptionType)赋值为“匹配失败”(MatchFailed)
* 状态流转
> 触发流程 : 当选择扫码记录时删除时,会根据当前扫码记录的是否有效字段和扫码状态字段对产品码状态进行回退
> * 1.是否有效字段=“有效”且扫码状态="正常出库"且当前删除记录的扫码状态字段='正常赠送'时
    当完成扫码记录删除，要去根据扫码记录上的扫码产品id，关联产品二维码，若删除的数据为有效数据，则更新二维码状态为“已出库”；更新赠出瓶数-1（首先更新费用实际表的赠出瓶数字段，然后再更新到费用申请表的赠出瓶数字段）；同时将执行反馈中的产品支付的实际瓶数-1；若删除的数据为无效数据，则不做数据的变动。



* 涉及组件
> * link-auto-list
> * AutoList
> * link-dialog
> * link-swipe-action
> * link-sticky


## 模块实现
###涉及页面
#### 一 扫码记录活动查看
#####  1、vue页面路径
> * 1、页面完整路径
    src/pages/lj-market-activity/presented-code/presented-code-page.vue
##### 2、页面实现功能
> * (1).扫码记录列表根据活动数据查看扫码记录数据
> * (2).查询框：支持按照活动编码、产品编码、产品名称进行模糊查询

#### 二 扫码记录活动详情查看
#####  1、vue页面路径
> * 1、页面完整路径
    src/pages/lj-market-activity/presented-code/presented-code-item-page.vue
##### 2、页面实现功能
> * (1).展示活动信息,物资行信息,扫码记录信息.
> * (2).点击扫码记录信息跳转到扫码记录详情页面展示信息.也可以点击[查看更多]对扫码记录进行搜索.
> * (3).左滑删除扫码记录并进行状态更新.

#### 三 扫码记录详情查看
#####  1、vue页面路径
> * 1、页面完整路径
    src/pages/lj-market-activity/presented-code/presented-code-details-page.vue
##### 2、页面实现功能
> * (1).展示扫码记录详细信息.

#### 四 扫码记录查看更多
#####  1、vue页面路径
> * 1、页面完整路径
    src/pages/lj-market-activity/presented-code/presented-code-itemlist-page.vue
##### 2、页面实现功能
> * (1).可以查询当前活动上的所有的赠送扫码记录.支持扫码人,产品名,产品码模糊查询.有效字段筛选

#### 四 扫码转赠
#####  1、vue页面路径
> * 1、页面完整路径
    src/pages/lj-market-activity/presented-code/presented-scan-code-page.vue
##### 2、页面实现功能
> * (1).可以选择执行反馈阶段的活动进行赠送操作
    (2).支持左滑删除操作

## 配置页面
> * 无


------ 到此开瓶扫码记录模块内容结束 ------

