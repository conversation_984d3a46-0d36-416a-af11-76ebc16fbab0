<template>
    <link-page class="presented-code-page">
        <link-auto-list :option="autoList" hideCreateButton :searchInputBinding="{props:{placeholder:'活动编码/产品编码/产品名称'}}">

            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="scan-list" @tap.stop="gotoItem(data)" >
                    <view class="scan-item" slot="note">
                        <view class="list-cell">
                            <view class="font view-margin"><text>活动编码</text>{{data.activityNum}}</view>
                            <view class="info view-margin" >{{data.materialProdNum}} | {{data.feePayName}}</view>
                            <view class="info  view-margin">{{data.materialProdName}}</view>
                            <view class="bottle-info">
                                <view class="bottle-row view-margin">
                                    <view class="font"><text class="textTitle">出库</text><text class="textVal" style="text-align: right">{{data.outQty}}瓶</text></view>
                                    <view class="font right"><text class="textTitle">开瓶</text><text class="textVal" >{{data.openQty}}瓶</text></view>
                                </view>
                                <view class="bottle-row view-margin">
                                    <view class="font"><text class="textTitle">赠送</text><text class="textVal" style="text-align: right">{{data.giftQty}}瓶</text></view>
                                    <view class="font right"><text class="textTitle">已入库</text><text class="textVal" >{{data.inQty}}瓶</text></view>
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
        <link-fab-button :bottom="120" icon="icon-scan" @tap="scanCode"/>
    </link-page>
</template>

<script>

import Taro from "@tarojs/taro";

export default {

    name: "presented-code-page",
    data(){
        const autoList = new this.AutoList(this, {
            //
            url: {
                queryByExamplePage: 'action/link/actScanRecord/queryTypeCodeScanPage'
            },
            param: {
                //attr2开启筛选条件
                attr2:'Gift',
                scanSubType : 'GiftScan',
                filtersRaw: []
            },
            sortOptions: null,
            searchFields: ['activityNum', 'materialProdNum', 'materialProdName'],
        });
        return{
            autoList,
        }
    },
    created() {

    },
    methods:{
       async onBack(){
          await  this.autoList.methods.reload();
        },
        /**
         *  @description: 扫码功能及逻辑处理
         *  @author: 吕志平
         *  @date: 2021年10月25日14:42:19
         */
        async scanCode(){
          await  this.$nav.push('/pages/lj-market-activity/presented-code/presented-scan-code-page',{data:''})
        },

        /**
         *  @description: 跳转详情
         *  @author: 吕志平  窖龄酒成都金牛区业务代表具有稽核人员安全性
         *  @date: 2021年9月15日16:55:55
         */
        gotoItem(data) {
            this.$nav.push('/pages/lj-market-activity/presented-code/presented-code-item-page', {data: data})
        },
        /**
         * 1.走职位安全性
         * 2.走组织安全性
         * 3.稽查人员 走稽查人员安全性
         * <AUTHOR>
         * @date 2021年9月15日17:20:35
         */
        isOauth() {
            let positionType = Taro.getStorageSync('token').result.positionType;
            let arr = [
                'SalesSupervisor',         // 业务主管
                'Salesman',                // 业务代表
                'GroupBuyManager',         // 团购经理
                'AccountManager',          // 客户经理
                'CustServiceManager',      // 客服经理
                'VipManager',              // VIP经理
                'CustServiceSpecialist',   // 客服专员
                'CustServiceSupervisor',   // 客服主管
                'SalesTeamLeader',         // 小组组长
                'SalesChannelManger'       // 渠道经理
            ];
            let flag = arr.includes(positionType);
            return flag ? 'MY_POSTN' : 'MY_ORG';
        },
    }

}
</script>

<style lang="scss">
.presented-code-page{
    padding: 0;
    margin: 0;
    /*deep*/
    .link-auto-list .link-auto-list-top-bar {
        border-bottom: none !important;
    }
    /*deep*/
    .link-search-input{
        padding: 26px;
    }


    .scan-list{
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
        .scan-item{
            background-color: #FFFFFF;
            position: relative;
            width: 100%;
            display: flex;
            flex-direction: column;

            .list-cell {
                width: 100%;
                position: relative;
                flex-direction: column;
                justify-content: space-between;
                align-items: start;
                .view-margin{
                    margin: 12px 16px;
                }
                .font{
                    color: #000000;
                    font-size: 28px;
                    .textTitle{
                        color:#8C8C8C;
                        font-size: 28px;
                    }
                    .textVal{
                        display: inline-block;
                        width: 3rem;
                    }
                }
                .info{
                    color: #000000;
                    font-size: 28px;
                    font-weight: bold;
                }
                .bottle-info{
                    width: 100%;
                    .bottle-row{
                        display: flex;
                        flex-direction: row;
                        view{
                            flex: 1;
                        }
                        .right{
                            text-align: right;
                        }
                    }
                }
            }
        }
    }
}
</style>
