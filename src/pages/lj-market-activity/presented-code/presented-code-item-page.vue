<template>
    <link-page>
    <view class="basic-info">
        <view v-if="showFlag">
        <view class="card-content"  v-for="item in actData">
            <view class="card-title">活动信息基础</view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">活动编码</view>
                    <view class="value">{{item.activityNum}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">活动名称</view>
                    <view class="value-address">{{item.activityName}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">费用小类</view>
                    <!--                 <view class="val">{{oldCustomerItem.subAcctType | lov('ACCT_SUB_TYPE')}}</view>  -->
                    <view class="value">{{item.feeType}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">活动类型</view>
                    <view class="value">{{item.activityType | lov('MC_TYPE')}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">销售大区</view>
                    <view class="value">{{item.salesRegion}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">销售片区</view>
                    <view class="value">{{item.salesArea}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">销售城市</view>
                    <view class="value">{{item.salesCity}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">执行案编码</view>
                    <view class="value">{{item.excCaseNum}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label-address">无执行案说明</view>
                    <view class="value-address">{{item.noExcComment}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label-address">子公司/经销商</view>
                    <view class="value-address">{{item.dealer}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">受益用户</view>
                    <view class="value">{{item.benefitAcct}}</view>
                </view>
            </view>
            <view class="card-rows" >
                <view class="card-rows-content">
                    <view class="label">执行人</view>
                    <view class="value">{{item.executor}}</view>
                </view>
            </view>
        </view>


        <view class="card-content" v-for="item in actData">
            <view class="card-title">费用实际物资</view>
            <view class="card-rows" >
                <view class="card-fee">{{item.feePayName}}</view>
                <view class="media-list">
                    <view class="num-view">
                        <view class="num">{{item.materialProdNum}}</view>
                    </view>
                </view>
                <view class="info view-margin">{{item.materialProdName}}</view>
                <view class="bottle-info">
                    <view class="bottle-row view-margin">
                        <view class="font">
                            <text class="textTitle">申请</text>
                            <text class="textVal" style="text-align: right">{{item.applyQty}}瓶</text>
                        </view>
                        <view class="font center">
                            <text class="textTitle">总出库</text>
                            <text class="textVal">{{item.outQty}}瓶</text>
                        </view>
                        <view class="font right">
                            <text class="textTitle">开瓶</text>
                            <text class="textVal">{{item.openQty}}瓶</text>
                        </view>
                    </view>
                    <view class="bottle-row view-margin">
                        <view class="font">
                            <text class="textTitle">赠送</text>
                            <text class="textVal" style="text-align: right">{{item.giftQty}}瓶</text>
                        </view>
                        <view class="font center">
                            <text class="textTitle">已入库</text>
                            <text class="textVal">{{item.inQty}}瓶</text>
                        </view>
                        <view class="font right">
                            <text class="textTitle">待入库</text>
                            <text class="textVal">{{item.estInQty}}瓶</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <view class="card-content">
            <view class="card-title" ><text class="flex8">转赠扫码记录</text>
                <view @tap="gotoScanList" v-if="this.autoList.list.length > 3">
                    <text class="flex1 more">查看更多</text>
                    <link-icon icon="icon-right" class="flex1"/>
                </view>
            </view>

            <link-auto-list :option="autoList">
                <template slot-scope="{data,index}">
                    <link-swipe-action :key="index">
                        <link-swipe-option label="删除" @tap="deleteItem(data)" v-if="deleteScanFlag" slot="option"/>
                        <item v-if="index<=2" class="code-record-list" style="border-top: 2px #F2F2F2 solid;" @tap="gotoDetails(data)" :key="index" :data="data" :arrow="false">
                            <view slot="note">
                                <view class="list-top">
                                    <view class="code">{{data.prodNum}}</view>
                                    <image class="iseffective" :src="data.isEffective==='Y' ? $imageAssets.effective : $imageAssets.failure "></image>
                                </view>
                                <view class="list-middle">
                                    {{data.prodName}}
                                </view>
                                <view class="list-bottom clearfix">
                                    <view :class="['list-bottom-item', 'bottom-l', {issuccess: data.scanRecordStatus === 'Normalgifted'}]">{{data.scanRecordStatus | lov('SCAN_RECORD_STATUS')}}</view>
                                    <view :class="['list-bottom-item', 'bottom-c', {issuccess: data.descriptionType === 'MatchSuccessfully'}]">{{data.descriptionType | lov('MATCH_STATUS')}}</view>
                                    <view class="list-bottom-item bottom-r">{{data.scanner}}</view>
                                </view>
                                <view class="qr-code">
                                    <view class="qr-code-item">
                                        <view class="lebal">盖外码:</view>
                                        <view class="val">{{data.qrCodeOut && data.qrCodeOut.split('/').pop().substr(-8)}}</view>
                                    </view>
                                    <view class="qr-code-item" v-if="data.qrCodeIn">
                                        <view class="lebal">盖内码:</view>
                                        <view class="val">{{data.qrCodeIn.split('/').pop().substr(-8)}}</view>
                                    </view>
                                </view>
                            </view>
                        </item>
                    </link-swipe-action>
                </template>
            </link-auto-list>

        </view>


        <link-dialog ref="errDialog" disabledHideOnClickMask title="异常提示">
             查询该活动下数据为空点击确认返回
            <link-button slot="foot" @tap="back()">确定</link-button>
        </link-dialog>

       </view>
    </view>
    </link-page>
</template>

<script>
import Taro from "@tarojs/taro";
import {deleteScanCodeCheck} from "../market-activity/deleteScanCodeCheck";

export default {
    name: "presented-code-item",
    data(){
        //用来判断是否为特曲公司以及阶段
        let tequCompany = false;
        let userInfo = Taro.getStorageSync('token').result;
        //特曲公司 且为执行反馈阶段
        if(userInfo.coreOrganizationTile.brandCompanyCode === '5137'){
            tequCompany = true;
        }
        return{
            autoList: new this.AutoList(this, {
                url: {
                    queryByExamplePage: 'action/link/actScanRecord/queryTypeCodeScanByActNumPage'
                },
                param:{
                    rows: 5,
                    attr4: tequCompany ? 'Y' : '',
                    materialLineId : this.pageParam.data.materialLineId,
                    activityNum: this.pageParam.data.activityNum,
                    scanType:"ActProdScan",
                    scanSubType:"GiftScan",

                }

            }),
            actData:[],
            tequCompany,
            showFlag:false,
            deleteScanFlag: false,//是否可以侧滑删除扫码记录
        }
    },
    async created() {
        await this.getScanByID();
        this.deleteScanFlag = await deleteScanCodeCheck.checkFlag(this.pageParam.data.id);
    },
    methods:{
        async deleteItem(data) {
            this.$dialog({
                title: '提示',
                content: '是否确认删除扫码记录',
                cancelButton: true,
                onConfirm: () => {
                    this.confirmDelete(data)
                },
            })
        },
        async confirmDelete(data) {
            const params =  {
                id: data.id,
                feature: this.tequCompany ? 'Y':''
            }
            try {
                const data = await this.$http.post('action/link/actScanRecord/giftDeleteRecord', params);
                if(data.success) {
                    this.$message.success('删除数据成功');
                    await this.reload()
                    this.$emit('change', true)
                    return
                }
                this.$showError('删除数据失败');
            }catch (e) {
                this.$showError(`删除数据异常:${e.result}`);
            }
        },

        async reload() {
            this.autoList.methods.reload();
            this.actData=[];
            const data = await this.$http.post('action/link/actScanRecord/queryTypeCodeScanByActNumPage',
                {
                    //判断特曲公司
                    attr4:this.tequCompany ? 'Y' : '',
                    materialLineId : this.pageParam.data.materialLineId,
                    activityNum: this.pageParam.data.activityNum,
                    scanType:"ActProdScan",
                    scanSubType:"GiftScan",
                    rows: 5
                });
            if(this.$utils.isEmpty(data.rows)){
                this.$nav.back();
                return;
            }
            this.actData[0] = data.rows[0];
        },
        back(){
            this.$nav.back();
        },
        async  gotoScanList(){
          await  this.$nav.push('/pages/lj-market-activity/presented-code/presented-code-itemlist-page', {data: this.pageParam.data})
        },

        gotoDetails(data) {
            this.$nav.push('/pages/lj-market-activity/presented-code/presented-code-details-page', {data: data})
        },
        async onBack(param){
           await this.reload()
        },
        async getScanByID(){
            const data = await this.$http.post('action/link/actScanRecord/queryTypeCodeScanByActNumPage',
                {
                    //判断特曲公司
                    attr4:this.tequCompany ? 'Y' : '',
                    materialLineId : this.pageParam.data.materialLineId,
                    activityNum: this.pageParam.data.activityNum,
                    scanType:"ActProdScan",
                    scanSubType:"GiftScan",
                    rows: 5
                });
            if(this.$utils.isEmpty(data.rows)){
                this.$refs.errDialog.show();
            }
            this.actData[0] = data.rows[0];
            this.showFlag=true;
        }
    }
}
</script>

<style lang="scss">
.link-auto-list-no-more{
    display: none !important;
}
.basic-info {
    padding-top: 24px;
    padding-bottom: 68px;

    .link-icon{
        color: #BFBFBF;
        font-size: 32px;
        align-items: end;
    }
    .card-content {
        background: #ffffff;
        border-radius: 16px;
        width: 95%;
        padding-top: 8px;
        padding-bottom: 40px;
        margin: 0px auto 24px;
        .card-title {
            @include flex-start-center;
            @include space-between;
            border-bottom: 2px solid #F2F2F2;
            font-size: 28px;
            padding: 17px 24px;
            .sync-info-title {
                color: #262626;
            }
        }
        .card-fee{
            @include flex-start-center;
            @include space-between;
            font-size: 28px;
            padding: 17px 0px;
            .sync-info-title {
                color: #262626;
            }
        }
        .card-rows {
            font-family: PingFangSC-Regular,serif;
            font-size: 28px;
            letter-spacing: 0;
            line-height: 28px;
            padding: 32px 24px 0 24px;
            .card-rows-content {
                @include flex();
                @include space-between();
                .label {
                    color: #8C8C8C;
                }
                .value {
                    color: #262626;
                }
                .label-address  {
                    color: #8C8C8C;
                    width: 34%;
                }
                .value-address {
                    text-align: right;
                    //width: 80%;
                    line-height: 40px;
                    color: #262626;
                    word-break: break-all;
                }
            }
        }
        .code-record-list{
            padding-right: 0px;
            width: 95%;
            .list-top{
                font-size: 28px;
                display: flex;
                justify-content: space-between;

                .code{
                    background-color:  #A6B4C7;
                    color: white;
                    height: 48px;
                    padding: 7px 19px;
                    margin: 10px 0;
                    border-radius: 10px;
                }
                .iseffective{
                    width: 92px;
                    height: 48px;
                }

            }

            .list-middle{
                font-family: PingFangSC-Medium;
                font-size: 32px;
                color: #262626;
                line-height: 45px;
                font-weight: 600;
                margin: 10px 0;
            }

            .list-bottom{
                padding: 10px 0 20px 0;

                .list-bottom-item{
                    font-size: 28px;
                    float: left;
                    width: 33.3%;
                }

                .bottom-l{
                    color: #FF5A5A ;
                }

                .bottom-c{
                    text-align: center;
                    color: #FF5A5A ;
                }

                .bottom-r{
                    text-align: right;
                }

                .issuccess{
                    color: #2EB3C2;
                }
            }

            .qr-code{
                display: flex;
                justify-content: space-between;
                font-size: 28px;
                .qr-code-item{
                    display: flex;
                    .lebal{
                        color:#8C8C8C;
                        margin-right: 8px;
                    }
                    .val {
                        color: #262626;
                    }
                }
            }

            .clearfix:after {
                content: "";
                display: block;
                height: 0;
                clear: both;
                visibility: hidden;
            }
        }
        .line {
            margin-top: -8px;
        }
        .media-list{
            display: flex;
            position: relative;
            .num-view {
                background: #A6B4C7;
                border-radius: 8px;
                margin-bottom: 6px;
                .num {
                    font-size: 28px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    line-height: 40px;
                    padding: 2px 8px;
                }
            }
        }

        .view-margin{
            margin: 20px auto;
        }
        .font{
            color: #000000;
            font-size: 28px;
            .textTitle{
                color:#8C8C8C;
                font-size: 28px;
            }
            .textVal{
                display: inline-block;
                width: 3rem;
            }
        }
        .info{
            color: #000000;
            font-size: 28px;
            font-weight: bold;
        }
        .bottle-info{
            width: 100%;
            .bottle-row{
                display: flex;
                flex-direction: row;
                view{
                    flex: 1;
                }
                .center{
                    text-align: center;
                }
                .right{
                    text-align: right;
                }
            }
        }
        .flex{
            display: flex;
        };
        .flex8{
            flex:8;
        }
        .flex1{
            flex:1;
        }
        .succeed{
            color: #2EB3C2;
        }
        .fail{
            color:#FF5A5A ;
        }
        .more{
            color: #C7C3C0;
            word-break: keep-all;
            font-size: 0.8em;
        }
        .state{
            position: absolute;
            top: 4px;
            right: 0px;
            line-height: 40px;
            width: 92px;
            height: 40px;
            image{
                width: 100%;
                height: 100%;
            }
        }
    }
}

</style>

