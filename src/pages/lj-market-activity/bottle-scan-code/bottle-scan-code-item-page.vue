<template>
 <view class="basic-info" v-if="showFlag">
     <view class="card-content">
         <view class="card-title">活动信息基础</view>
         <view class="card-rows" >
             <view class="card-rows-content">
                 <view class="label">活动编码</view>
                 <view class="value">{{this.scanData[0].activityNum}}</view>
             </view>
         </view>
         <view class="card-rows" >
             <view class="card-rows-content">
                 <view class="label">活动名称</view>
                 <view class="value-address">{{this.scanData[0].activityName}}</view>
             </view>
         </view>
         <view class="card-rows" >
             <view class="card-rows-content">
                 <view class="label">费用小类</view>
<!--                 <view class="val">{{oldCustomerItem.subAcctType | lov('ACCT_SUB_TYPE')}}</view>  -->
                 <view class="value">{{this.scanData[0].feeType}}</view>
             </view>
         </view>
         <view class="card-rows" >
             <view class="card-rows-content">
                 <view class="label">活动类型</view>
                 <view class="value">{{this.scanData[0].activityType | lov('MC_TYPE')}}</view>
             </view>
         </view>
         <view class="card-rows" >
             <view class="card-rows-content">
                 <view class="label">销售大区</view>
                 <view class="value">{{this.scanData[0].salesRegion}}</view>
             </view>
         </view>
         <view class="card-rows" >
             <view class="card-rows-content">
                 <view class="label">销售片区</view>
                 <view class="value">{{this.scanData[0].salesArea}}</view>
             </view>
         </view>
         <view class="card-rows" >
             <view class="card-rows-content">
                 <view class="label">销售城市</view>
                 <view class="value">{{this.scanData[0].salesCity}}</view>
             </view>
         </view>
         <view class="card-rows" >
             <view class="card-rows-content">
                 <view class="label">执行案编码</view>
                 <view class="value">{{this.scanData[0].excCaseNum}}</view>
             </view>
         </view>
         <view class="card-rows" >
             <view class="card-rows-content">
                 <view class="label-address">无执行案说明</view>
                 <view class="value-address">{{this.scanData[0].noExcComment}}</view>
             </view>
         </view>
         <view class="card-rows" >
             <view class="card-rows-content">
                 <view class="label-address">子公司/经销商</view>
                 <view class="value-address">{{this.scanData[0].dealer}}</view>
             </view>
         </view>
         <view class="card-rows" >
             <view class="card-rows-content">
                 <view class="label">受益用户</view>
                 <view class="value">{{this.scanData[0].benefitAcct}}</view>
             </view>
         </view>
         <view class="card-rows" >
             <view class="card-rows-content">
                 <view class="label">执行人</view>
                 <view class="value">{{this.scanData[0].executor}}</view>
             </view>
         </view>
     </view>
     <view class="card-content">
         <view class="card-title">费用实际物资</view>
         <view class="card-rows" >
             <view class="card-fee">{{this.scanData[0].feePayName}}</view>
             <view class="media-list">
                 <view class="num-view">
                     <view class="num">{{this.scanData[0].materialProdNum}}</view>
                 </view>
             </view>
<!--             | lov('CASH_FEE')-->
             <view class="info view-margin">{{this.scanData[0].materialProdName  }}</view>
             <view class="bottle-info">
                 <view class="bottle-row view-margin">
                     <view class="font">
                         <text class="textTitle">申请</text>
                         <text class="textVal" style="text-align: right">{{this.scanData[0].applyQty}}瓶</text>
                     </view>
                     <view class="font center">
                         <text class="textTitle">总出库</text>
                         <text class="textVal">{{this.scanData[0].outQty}}瓶</text>
                     </view>
                     <view class="font right">
                         <text class="textTitle">开瓶</text>
                         <text class="textVal">{{this.scanData[0].openQty}}瓶</text>
                     </view>
                 </view>
                 <view class="bottle-row view-margin">
                     <view class="font">
                         <text class="textTitle">赠送</text>
                         <text class="textVal" style="text-align: right">{{this.scanData[0].giftQty}}瓶</text>
                     </view>
                     <view class="font center">
                         <text class="textTitle">已入库</text>
                         <text class="textVal">{{this.scanData[0].inQty}}瓶</text>
                     </view>
                     <view class="font right">
                         <text class="textTitle">待入库</text>
                         <text class="textVal">{{this.scanData[0].estInQty}}瓶</text>
                     </view>
                 </view>
             </view>
         </view>
     </view>

     <view class="card-content">
         <view class="card-title" ><text class="flex8">开瓶扫码记录</text>
             <view @tap="gotoScanList">
                 <text class="flex1 more">查看更多</text>
                 <link-icon icon="icon-right" class="flex1"/>
             </view>
         </view>
         <view class="card-rows" style="border-top: 2px #F2F2F2 solid;" @tap="gotoDetails(item)" v-for="item in scanData">
             <view class="media-list">
                 <view class="num-view">
                     <view class="num">{{item.prodNum}}</view>
                 </view>
                 <view class="state"><image :src="item.isEffective==='Y' ? $imageAssets.effective : $imageAssets.failure "></image></view>
             </view>
             <view class="flex  view-margin">
                 <view class="info flex8" >{{item.prodName}}</view>
                 <link-icon icon="icon-right" class="flex1"/>
             </view>
             <view class="bottle-info">
                 <view class="bottle-row view-margin">
                     <view class="font succeed">{{item.scanRecordStatus | lov('SCAN_RECORD_STATUS')}}</view>
                     <view class="font center succeed">{{item.descriptionType | lov('MATCH_STATUS')}}</view>
                     <view class="font right">{{item.scanner}}</view>
                 </view>
             </view>
             <view class="qr-code">
                 <view class="qr-code-item">
                     <view class="lebal">盖外码:</view>
                     <view class="val">{{item.qrCodeOut && item.qrCodeOut.split('/').pop().substr(-8)}}</view>
                 </view>
                 <view class="qr-code-item" v-if="item.qrCodeIn">
                     <view class="lebal">盖内码:</view>
                     <view class="val">{{item.qrCodeIn.split('/').pop().substr(-8)}}</view>
                 </view>
             </view>
         </view>
     </view>
 </view>
</template>

<script>
import Taro from "@tarojs/taro";

export default {
    name: "bottle-scan-code-item-page.vue",
    data(){
        //用来判断是否为特曲公司以及阶段
        let tequCompany = false;
        let userInfo = Taro.getStorageSync('token').result;
        //特曲公司 且为执行反馈阶段
        if(userInfo.coreOrganizationTile.brandCompanyCode === '5137'){
            tequCompany = true;
        }
        const autoList = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/actScanRecord/queryTypeCodeScanByActNumPage'
            },
            param: {
                activityNum: this.pageParam.data.activityNum,
                scanType:"ActProdScan",
                scanSubType:"OpenScan",
                filtersRaw: []

            }
        });
        return{
            autoList,
            showFlag:false,
            scanData:[],
            tequCompany
        }
    },
    async created() {
        await this.getScanByID();
    },
    methods:{
        gotoScanList(){
            this.$nav.push('/pages/lj-market-activity/bottle-scan-code/bottle-scan-code-itemlist-page', {data: this.pageParam.data})
        },

        gotoDetails(data) {
            this.$nav.push('/pages/lj-market-activity/bottle-scan-code/bottle-scan-code-details-page', {data: data})
        },

        async getScanByID(){
            const data = await this.$http.post('action/link/actScanRecord/queryTypeCodeScanByActNumPage',
                {
                            //是否特曲
                            attr4:this.tequCompany ? 'Y' : '',
                            materialLineId : this.pageParam.data.materialLineId,
                            activityNum: this.pageParam.data.activityNum,
                            scanType:"ActProdScan",
                            scanSubType:"OpenScan",
                            rows: 5
                        });
            this.scanData = data.rows;
            this.showFlag=true;
        }
    }
}
</script>

<style lang="scss">
.basic-info {
    padding-top: 24px;
    padding-bottom: 68px;

    .link-icon{
       color: #BFBFBF;
       font-size: 32px;
        align-items: end;
    }
    .card-content {
        background: #ffffff;
        border-radius: 16px;
        width: 702px;
        padding-top: 8px;
        padding-bottom: 40px;
        margin: 0px auto 24px;
        .card-title {
            @include flex-start-center;
            @include space-between;
            border-bottom: 2px solid #F2F2F2;
            font-size: 28px;
            padding: 17px 24px;
            .sync-info-title {
                color: #262626;
            }
        }
        .card-fee{
            @include flex-start-center;
            @include space-between;
            font-size: 28px;
            padding: 17px 0px;
            .sync-info-title {
                color: #262626;
            }
        }
        .card-rows {
            font-family: PingFangSC-Regular,serif;
            font-size: 28px;
            letter-spacing: 0;
            line-height: 28px;
            padding: 32px 24px 0 24px;
            .card-rows-content {
                @include flex();
                @include space-between();
                .label {
                    color: #8C8C8C;
                }
                .value {
                    color: #262626;
                }
                .label-address  {
                    color: #8C8C8C;
                    width: 34%;
                }
                .value-address {
                    text-align: right;
                    //width: 80%;
                    line-height: 40px;
                    color: #262626;
                    word-break: break-all;
                }
            }
        }
        .line {
            margin-top: -8px;
        }
        .media-list{
            display: flex;
            position: relative;
            .num-view {
                background: #A6B4C7;
                border-radius: 8px;
                margin-bottom: 6px;
                .num {
                    font-size: 28px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    line-height: 40px;
                    padding: 2px 8px;
                }
            }
        }

        .view-margin{
            margin: 20px auto;
        }
        .font{
            color: #000000;
            font-size: 28px;
            .textTitle{
                color:#8C8C8C;
                font-size: 28px;
            }
            .textVal{
                display: inline-block;
                width: 3rem;
            }
        }
        .info{
            color: #000000;
            font-size: 28px;
            font-weight: bold;
        }
        .bottle-info{
            width: 100%;
            .bottle-row{
                display: flex;
                flex-direction: row;
                view{
                    flex: 1;
                }
                .center{
                    text-align: center;
                }
                .right{
                    text-align: right;
                }
            }
        }
        .qr-code{
            display: flex;
            justify-content: space-between;
            font-size: 28px;
            .qr-code-item{
                display: flex;
                .lebal{
                    color:#8C8C8C;
                    margin-right: 8px;
                }
                .val {
                    color: #262626;
                }
            }
        }
        .flex{
            display: flex;
        };
        .flex8{
            flex:8;
        }
        .flex1{
            flex:1;
        }
        .succeed{
          color: #2EB3C2;
         }
        .fail{
            color:#FF5A5A ;
        }
        .more{
            color: #C7C3C0;
            word-break: keep-all;
            font-size: 0.8em;
        }
        .state{
            position: absolute;
            top: 4px;
            right: 0px;
            line-height: 40px;
            width: 92px;
            height: 40px;
            image{
                width: 100%;
                height: 100%;
            }
        }
    }
}

</style>
