# 品鉴酒-开瓶记录列表


------
* 初始文档
```
创建时间：2022年1月11日
创建人：  吕志平
```
* 模块介绍
>  开瓶扫码列表根据职位类型按照活动数据查看安全性客制化多种安全性展示数据，即根据登录用户当前职位的职位类型定义可以看到的开瓶扫码数据列表，包含一下三种情况：
> * A、当前登录人职位类型=业务代表Salesman、业务主管SalesSupervisor、团购经理GroupBuyManager、客户经理AccountManager、客服经理CustServiceManager、VIP经理VipManager、客服专员CustServiceSpecialist、客服主管CustServiceSupervisor、小组组长SalesTeamLeader、渠道经理SalesChannelManger时，用当前登录人职位，与活动-内部人员表里面的数据匹配，查询该职位及下级职位的活动数据关联的费用实际行数据展示；
> * B、当前登录人职位类型=会战指挥长BattleCommander、品牌销管部部长BPSalesManager、总部内勤HeadquartersStuff、大区内勤RInternalStaff、渠道管理部部长CMDeptDirector、渠道主管ChannelSupervisor、会员管理部经理MemberManager、品牌联络部主管BLRegionManager、品牌推广部主管BPRegionManager、品牌推广部部长BPDeptManager、销售公司总经理SalesGeneralManager、品牌公司总经理BrandManager、片区内勤InternalStaff、系统管理员SysAdmin、城市经理SalesManager，SalesCityManager、片区经理SalesAreaManager、大区经理SalesRegionManager、战区经理、股份公司总经理GeneralManager、城市内勤CInternalStaff、品牌公司信息管理员BrandSysAdmin、片区信息专员RegionSysAdmin时，按照当前登录人职位的组织，与活动头上的orgid匹配查看组织及下级组织活动数据关联的费用实际行数据展示；
> * C、当前登录人职位类型=稽核人员AuditStaff、财务人员FinanceStaff、纪检人员InspectionStaff时，按照当前登录人职位的稽核人员安全性与活动头上的归属公司和省-市-区县匹配，查看对应品牌公司和省市区县的活动数据关联费用实际行数据展示；

* 涉及对象
> * 市场活动-本系统
> * 扫码记录-本系统


* 是否共用
> 否


* 数据存储
> * 数据来源 本系统数据库
> * 存储方式 本系统数据库
> * 是否同步 否

* 缓存机制
> * 是否缓存 否

* 安全性
> * 根据职位类型决定 按照职位还是按照组织id(后端处理) 稽核人员走稽核人员安全性

* 状态流转
> * 无



* 涉及组件
> * link-auto-list
> * AutoList


## 模块实现
###涉及页面
#### 一 扫码记录活动查看
#####  1、vue页面路径
> * 1、页面完整路径
    src/pages/lj-market-activity/bottle-scan-code/bottle-scan-code-page.vue
##### 2、页面实现功能
> * (1).开瓶扫码记录列表根据活动数据查看扫码记录数据
> * (2).查询框：支持按照活动编码、产品编码、产品名称进行模糊查询

#### 二 扫码记录活动详情查看
#####  1、vue页面路径
> * 1、页面完整路径
    src/pages/lj-market-activity/bottle-scan-code/bottle-scan-code-item-page.vue
##### 2、页面实现功能
> * (1).展示活动信息,物资行信息,扫码记录信息.
> * (2).点击扫码记录信息跳转到扫码记录详情页面展示信息.也可以点击[查看更多]对扫码记录进行搜索.

#### 三 扫码记录详情查看
#####  1、vue页面路径
> * 1、页面完整路径
    src/pages/lj-market-activity/bottle-scan-code/bottle-scan-code-details-page.vue
##### 2、页面实现功能
> * (1).展示扫码记录详细信息.

#### 四 扫码记录查看更多
#####  1、vue页面路径
> * 1、页面完整路径
    src/pages/lj-market-activity/bottle-scan-code/bottle-scan-code-itemlist-page.vue
##### 2、页面实现功能
> * (1).可以查询当前活动上的所有开瓶扫码记录.支持扫码人,产品名,产品码模糊查询.有效字段筛选

## 配置页面
> * 无


------ 到此开瓶扫码记录模块内容结束 ------

