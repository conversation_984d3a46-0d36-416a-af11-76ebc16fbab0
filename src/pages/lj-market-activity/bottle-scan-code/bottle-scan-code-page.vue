<template>
    <link-page class="bottle-scan-code-page">
        <link-auto-list :option="autoList" hideCreateButton :searchInputBinding="{props:{placeholder:'活动编码/产品编码/产品名称'}}">
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="scan-list" @tap.stop="gotoItem(data)" >
                    <view class="scan-item" slot="note">
                        <view class="list-cell">
                            <view class="font view-margin"><text class="textTitle">活动编码</text>{{data.activityNum}}</view>
                            <view class="info view-margin" >{{data.materialProdNum}} | {{data.feePayName}}</view>
                            <view class="info  view-margin">{{data.materialProdName}}</view>
                            <view class="bottle-info">
                                  <view class="bottle-row view-margin">
                                      <view class="font"><text class="textTitle">出库</text><text class="textVal" style="text-align: right">{{data.outQty}}瓶</text></view>
                                      <view class="font right"><text class="textTitle">开瓶</text><text class="textVal">{{data.openQty}}瓶</text></view>
                                  </view>
                                  <view class="bottle-row view-margin">
                                      <view class="font"><text class="textTitle">赠送</text><text class="textVal" style="text-align: right">{{data.giftQty}}瓶</text></view>
                                      <view class="font right"><text class="textTitle">已入库</text><text class="textVal">{{data.inQty}}瓶</text></view>
                                  </view>
                            </view>
                        </view>
                    </view>

                </item>
            </template>



        </link-auto-list>
    </link-page>
</template>

<script>
import Taro from "@tarojs/taro";
export default {

    name: "bottle-scan-code-page",
    data(){
        const autoList = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/actScanRecord/queryTypeCodeScanPage'
            },
            param: {
                //attr2开启筛选条件
                attr2:'Open',
                scanSubType : 'OpenScan',
                filtersRaw: []
            },
            sortOptions: null,
            searchFields: ['activityNum', 'materialProdNum', 'materialProdName'],
        });
        return{
            autoList,
        }
    },
    created() {

    },
    methods:{
        /**
         *  @description: 跳转详情
         *  @author: 吕志平
         *  @date: 2021年9月15日16:55:55
         */
        gotoItem(data) {
            this.$nav.push('/pages/lj-market-activity/bottle-scan-code/bottle-scan-code-item-page', {data: data})
        },
    }
}
</script>

<style lang="scss">
.bottle-scan-code-page{
    padding: 0;
    margin: 0;
    /*deep*/
    .link-auto-list .link-auto-list-top-bar {
        border-bottom: none !important;
    }
    /*deep*/
    .link-search-input{
        padding: 26px;
    }


    .scan-list{
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
        .scan-item{
            background-color: #FFFFFF;
            position: relative;
            width: 100%;
            display: flex;
            flex-direction: column;

            .list-cell {
                width: 100%;
                position: relative;
                flex-direction: column;
                justify-content: space-between;
                align-items: start;
                .view-margin{
                    margin: 12px 16px;
                }
                .font{
                    color: #000000;
                    font-size: 28px;
                    .textTitle{
                        color:#8C8C8C;
                        font-size: 28px;
                    }
                    .textVal{
                        display: inline-block;
                        width: 3rem;
                    }
                }
                .info{
                    color: #000000;
                    font-size: 28px;
                    font-weight: bold;
                }
                .bottle-info{
                    width: 100%;
                    .bottle-row{
                        display: flex;
                        flex-direction: row;
                        view{
                            flex: 1;
                        }
                        .right{
                            text-align: right;
                        }
                    }
                }
            }
        }
    }
}
</style>
