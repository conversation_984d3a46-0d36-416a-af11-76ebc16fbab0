<template>
    <link-page class="inbound-code-list-page">
        <link-auto-list :option="autoList" :arrow="false">
            <view slot="top" class="type">
                <lnk-taps :taps="codeOptions" v-model="codeStatusActive" @switchTab="onTap"></lnk-taps>
                <link-search-input v-model="searchVal" :placeholder="'请输入活动编码/产品编码/产品名称'"/>
            </view>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="inbound-code-list-item" @tap="toItem(data)">
                    <view slot="note">
                        <view class="content-top">
                            <text>活动编码</text>
                            <text class="top-code">{{data.activityNum}}</text>
                        </view>
                        <view class="content-middle">
                            <text class="text-bold">{{data.prodCode}}</text>
                            <text class="content-middle-center"> | </text>
                            <text class="text-bold">{{data.feePayType}}</text>
                            <view class="text-bold productName">{{data.prodName}}</view>
                        </view>
                        <view class="content-bottom">
                            <view class="content-bottom-item">
                                <view>
                                    <text class="label">出库:</text>
                                    <text class="text">{{data.outQty}}</text>
                                </view>
                                <view>
                                    <text class="label">开瓶:</text>
                                    <text class="text">{{data.openQty}}</text>
                                </view>
                            </view>
                            <view class="content-bottom-item">
                                <view>
                                    <text class="label">赠送:</text>
                                    <text class="text">{{data.giftQty}}</text>
                                </view>
                                <view>
                                    <text class="label">已入库:</text>
                                    <text class="text">{{data.inQty}}</text>
                                </view>
                            </view>
                            <view class="text-red">
                                <text>待入库</text>
                                <text>{{data.estInQty}}</text>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
import Taro from "@tarojs/taro";
import LnkTaps from "../../core/lnk-taps/lnk-taps";
export default {
    name: "inbound-code-list-page",
    components: {LnkTaps},
    data() {
        const codeOptions = [
            {name: '待扫码', seq: '1', val: 'code1'},
            {name: '已完成', seq: '2', val: 'code2'}
        ]
        const autoList = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/actMaterial/queryInStockScanPage'
            },
            param: {
                filtersRaw: [],
                page: 1,
                pageFlag: true,
                onlyCountFlag: false,
                oauth: 'ALL',
                rows: 5,
                sort: 'created',
                order: 'desc'
            },
            sortOptions: null
        });
        return {
            searchVal: '',               //搜索关键词
            codeOptions,                 //顶部tab列表
            codeStatusActive: null,      //当前选中的tab
            autoList,
        }
    },
    methods: {
        onTap() {
            this.autoList.option.param.attr1 = this.codeStatusActive && this.codeStatusActive.name === "已完成" ? "scanned" : ""
            this.autoList.methods.reload()
        },
        toItem(data) {
            const params = {
                actId: data.actId,
                id: data.id,
                codeStatusActive: this.codeStatusActive
            }
            this.$nav.push('/pages/lj-market-activity/inbound-code/inbound-code-item-page', params)
        }
    },
    created() {
        this.codeStatusActive = this.codeOptions[0]
        this.$bus.$on("reloadInList",async () => {
            await this.autoList.methods.reload();
        })
    },
    watch: {
        searchVal(newVal, oldVal) {
            if (newVal !== oldVal) {
                const searchObj = {
                    id: "searchValue",
                    operator: "or like",
                    property: "[activityNum, prodCode, prodName]",
                    value: newVal
                };
                this.autoList.option.param['filtersRaw'] = this.autoList.option.param['filtersRaw'].filter((item) => item.property !== "[activityNum, prodCode, prodName]");
                this.autoList.option.param['filtersRaw'].push(searchObj);
                this.autoList.methods.reload()
            }
        }
    }
}
</script>

<style lang="scss">
.inbound-code-list-page {
    background-color: #F2F2F2;
    .link-search-input {
        padding-top: 120px !important;
    }

    .type {
        color: #8C8C8C;
        font-size: 28px;

        .lnk-tabs {
            width: 100%;
            font-size: 28px;
            text-align: center;
            margin-right: 24px;
        }
    }

    .inbound-code-list-item {
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;

        .content-top{
            width: 100%;
            font-size: 28px;
            line-height: 48px;

            .top-code{
                font-family: DIN-Regular;
                color: #000000;
                letter-spacing: 0;
            }
            .top-code-text{
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
                line-height: 28px;
            }
        }

        .content-middle{
            font-family: PingFangSC-Semibold;
            font-weight: bold;
            font-size: 28px;
            color: #262626;
            letter-spacing: 0;
            line-height: 35px;
            margin: 10px 0;

            .text-bold {
                font-family: PingFangSC-Medium;
                color: #000000;
                letter-spacing: 0;
            }

            .content-middle-center{
                margin: 0 10px;
            }

            .productName{
                margin: 10px 0;
            }
        }

        .content-bottom-item{
            display: flex;
            justify-content: space-between;

            .label {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                line-height: 56px;
                margin-right: 5px;
            }

            .text {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
                line-height: 56px;
            }
        }

        .text-red{
            color: #FF5A5A;
            font-size: 28px;
            line-height: 48px;
        }
    }
}
</style>
