<template>
    <view class="inbound-code-record">
        <view class="head-top">
            <view class="main-title">入库扫码记录</view>
            <view class="more" v-if="scanInfoList.length === 4" @tap="seeMore">查看更多</view>
        </view>
        <view class="code-content">
            <link-swipe-action v-for="(data,index) in scanInfoList.slice(0, 3)" :key="index">
                <link-swipe-option label="删除" @tap="deleteItem(data)" slot="option" class="list-swipe" v-if="isShowDelete && deleteScanFlag" />
                <item :key="index" :data="data" :arrow="false" class="code-record-list" @tap="toDetails(data)">
                    <view slot="note">
                        <view class="list-top">
                            <view class="code">{{data.productCode}}</view>
                            <image class="iseffective" :src="$imageAssets[imgList[data.isEffective]]"></image>
                        </view>
                        <view class="list-middle">
                            {{data.productName}}
                        </view>
                        <view class="list-bottom clearfix">
                            <view :class="['list-bottom-item', 'bottom-l', {issuccess: data.scanRecordStatus === 'Normalstorage'}]">{{data.scanRecordStatus | lov('SCAN_RECORD_STATUS')}}</view>
                            <view :class="['list-bottom-item', 'bottom-c', {issuccess: data.descriptionType === 'MatchSuccessfully'}]">{{data.descriptionType | lov('MATCH_STATUS')}}</view>
                            <view class="list-bottom-item bottom-r">{{data.scanner}}</view>
                        </view>
                        <view class="qr-code">
                            <view class="qr-code-item">
                                <view class="lebal">盖外码:</view>
                                <view class="val">{{data.qrCodeOut && data.qrCodeOut.split('/').pop().substr(-8)}}</view>
                            </view>
                            <view class="qr-code-item" v-if="data.qrCodeIn">
                                <view class="lebal">盖内码:</view>
                                <view class="val">{{data.qrCodeIn.split('/').pop().substr(-8)}}</view>
                            </view>
                        </view>
                    </view>
                </item>
            </link-swipe-action>
        </view>
    </view>
</template>

<script>
import {deleteScanCodeCheck} from "../../market-activity/deleteScanCodeCheck";

export default {
    name: "code-record",
    props: {
        id: {
            type: String,
            default: ''
        },
        actId: {
            type: String,
            default: ''
        },
        isShowDelete: {
            type: Boolean,
            default: true
        }
    },
    data() {
        //Y,有效   N,无效
        const imgList = {
            Y:'effective',
            N:'failure'
        }
        return {
            imgList,
            scanInfoList: [],
            deleteScanFlag: false,//是否可以侧滑删除扫码记录
        }
    },
    async created() {
        this.init();
        this.deleteScanFlag = await deleteScanCodeCheck.checkFlag(this.actId);
    },
    methods: {
        async init() {
            const params = {
                filtersRaw: [
                    {
                        "id": "paramFilterRaw0",
                        "property": "materialLineId",
                        "value": this.id
                    },
                    {
                        "property":"scanSubType",
                        "value":"InScan",
                        "id":"paramFiltersRaw1"
                    }
                ],
                onlyCountFlag: false,
                oauth: 'ALL',
                sort: 'created',
                order: 'desc',
                rows: 4
            };
            try {
                const data = await this.$http.post('action/link/actScanRecord/queryByExamplePage', params)
                this.scanInfoList = data.rows
            }catch(e) {
                this.$showError('获取扫码数据失败', e)
            }
        },
        async deleteItem(data) {
            this.$dialog({
                title: '提示',
                content: '删除扫码记录，将同步更新物资的入库扫码数量，是否确认删除',
                cancelButton: true,
                onConfirm: () => {
                    this.confirmDelete(data)
                },
            })
        },
        async confirmDelete(data) {
            const params =  {
                id: data.id
            }
            try {
                const data = await this.$http.post('action/link/actScanRecord/deleteInStockScanRecord', params);
                if(data.success) {
                    this.$message.success('删除数据成功');
                    this.$emit('change')
                    return
                }
                this.$showError('删除数据失败');
            }catch (e) {
                this.$showError(`删除数据异常:${e.result}`);
            }
        },
        toDetails(data) {
            const params =  {
                id: data.id,
                title: '入库扫码详情'
            }
            this.$nav.push('/pages/lj-market-activity/outbound-code/outbound-record-list-details-page', params)
        },
        seeMore() {
            const params = {
                type: 'InScan',
                id: this.id,
                actId: this.actId,
                title: '入库扫码列表',
                isShowDelete: this.isShowDelete
            }
            this.$nav.push('/pages/lj-market-activity/outbound-code/outprocess-more-record-list-page', params)
        }
    },
}
</script>

<style lang="scss">
.inbound-code-record{
    letter-spacing: 0;
    overflow-x: hidden;
    background-color: white;
    margin-top: 20px;

    .link-item-icon{
        width:0px;
        padding-left: 0px;
    }

    .link-swipe-action {
        width: 95vw;
    }

    .head-top{
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: PingFangSC-Semibold;
        height: 88px;
        border-bottom: 2px solid #e5e5e5;
        padding: 0px 28px;
        font-size: 28px;
        background-color: white;

        .main-title{
            color: #262626;
            font-size: 32px;
        }

        .more{
            color: rgb(47,105,248)
        }
    }
    .code-record-list{
        .list-top{
            font-size: 28px;
            display: flex;
            justify-content: space-between;

            .code{
                background-color:  #A6B4C7;
                color: white;
                height: 40px;
                padding: 2px 8px;
                margin: 10px 0;
                border-radius: 10px;
            }

            .iseffective{
                width: 92px;
                height: 48px;
                display: flex;
                justify-content: center;
                align-items: center;
            }

        }

        .list-middle{
            font-weight: bold;
            font-family: PingFangSC-Medium;
            font-size: 28px;
            color: #262626;
            line-height: 45px;
            margin: 10px 0;
        }

        .list-bottom{
            display: flex;
            padding: 10px 0 20px 0;

            .list-bottom-item{
                flex: 1;
                font-size: 28px;
            }

            .bottom-l{
                color: #FF5A5A ;
            }

            .bottom-c{
                text-align: center;
                color: #FF5A5A ;
            }

            .bottom-r{
                text-align: right;
            }

            .issuccess{
                color: #2EB3C2;
            }
        }

        .qr-code{
            display: flex;
            justify-content: space-between;
            font-size: 28px;
            .qr-code-item{
                display: flex;
                .lebal{
                    margin-right: 8px;
                }
                .val {
                    color: black;
                }
            }
        }

        .clearfix:after {
            content: "";
            display: block;
            height: 0;
            clear: both;
            visibility: hidden;
        }
    }
}
</style>
