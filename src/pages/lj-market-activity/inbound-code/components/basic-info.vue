<template>
    <view class="inbound-code-basic-info">
        <view class="main-title">活动基础信息</view>
        <view>
            <view v-for="(val, key, i) in infoList" class="block-v" :key="key">
                <view class="title">{{val}}</view>
                <view class="val">{{basicInfo[key]}}</view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "basic-info",
    props: {
        id: {
            type: String,
            default: ''
        },
    },
    data() {
        const infoList = {
            activityNum: '活动编码',
            activityName: '活动名称',
            costType: '费用小类',
            activityType: '活动类型',
            salesBigArea: '销售大区',
            salesRegion: '销售片区',
            salesCity: '销售城市',
            exeCaseCode: '执行案编码',
            noPerformComments: '无执行案说明',
            actExecutivesName: '子公司/经销商',
            beneficiaryName: '受益客户',
            executor: '执行人'
        }
        return {
            infoList,            //页面字段
            basicInfo: {}        //基础信息
        }
    },
    created() {
        this.init()
    },
    methods: {
        async init() {
            const params =  {
                id: this.id
            }
            try {
                const data = await this.$http.post('action/link/marketAct/queryById', params);
                data.result.activityType = await this.$lov.getNameByTypeAndVal('TMPL_SUB_BIZ_TYPE',  data.result.activityType);
                this.basicInfo = data.result
            }catch (e) {
                this.$showError('获取入库扫码基础信息异常', e)
            }
        }
    }
}
</script>

<style lang="scss">
.inbound-code-basic-info{
    background-color: rgb(255,255,255);
    font-family: PingFangSC-Regular;
    letter-spacing: 0;

    .main-title{
        font-size: 32px;
        color: #262626;
        padding: 32px 24px;
        border-bottom: 2px solid #e5e5e5;
    }

    .block-v {
        padding: 0 24px 0 24px;
        font-size: 28px;
        line-height: 60px;
        display: flex;

        .title {
            color: #8C8C8C;
            width: 35%;
            float: left;
        }

        .val {
            color: #262626;
            text-align: right;
            width: 65%;
            float: left;
            text-overflow: ellipsis;
        }
    }

}
</style>
