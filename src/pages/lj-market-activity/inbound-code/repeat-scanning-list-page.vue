<template>
    <link-page class="repeat-scanning-list-page">
        <view class="sweep-repetition-code-img">
            <image :src="$imageAssets.sweepRepetitionCode"></image>
        </view>
        <view class="sweep-repetition-code-title">
            <view>重复扫码!</view>
        </view>
        <view class="sweep-repetition-code-msg">
            <view class="msg">{{pageParam.msg}}</view>
        </view>
        <view class="sweep-repetition-code-end">
        </view>
    </link-page>
</template>

<script>
export default {
    name: "repeat-scanning-list-page",
    data() {
        return{

        }
    },
    created() {
        this.$taro.setNavigationBarTitle({title: this.pageParam.title});
    }
}
</script>

<style lang="scss">
.repeat-scanning-list-page {
    background: white;

    .sweep-repetition-code-img {
        width: 100%;

        image {
            width: 100%;
        }
    }

    .sweep-repetition-code-title {
        font-family: PingFangSC-Medium;
        font-size: 36px;
        color: #262626;
        letter-spacing: 0;
        text-align: center;
        line-height: 36px;
    }

    .sweep-repetition-code-msg {
        font-family: PingFangSC-Regular;
        font-size: 28px;
        color: #262626;
        letter-spacing: 0;
        text-align: center;
        line-height: 100px;
    }

    .sweep-repetition-code-end {
        font-family: PingFangSC-Regular;
        font-size: 28px;
        color: #262626;
        letter-spacing: 0;
        text-align: center;
    }
}
</style>