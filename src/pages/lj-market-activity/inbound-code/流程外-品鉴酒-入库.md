# 流程外-品鉴酒-入库

------
### 初始文档
```
创建时间：2022/01/12 15:45
创建人：  康丰强
模块路径：src/pages/lj-market-activity/inbound-code
```
### 业务背景
> 针对品鉴酒扫码融合，对企业微信端出库列表和入库扫码功能设计
### 安全性
> * 职位安全性

### 涉及组件
* 组件构成:
```
outbound-code
  ├─ components
  │  ├─ basic-info                    //inbound-code-item-page组件内引用,展示活动基础信息
  │  ├─ code-record                   //inbound-code-item-page组件内引用,展示费用申请物资
  │  └─ cost-goods                    //inbound-code-item-page组件内引用,展示出库扫码记录
  └─ inbound-code-item-page           //inbound-code-list-page组件内引用,展示出库扫码详情
  └─ inbound-code-list-page           //展示出库扫列表
  └─ repeat-scanning-list-page        //重复扫码组件（与流程外出库通用）
  ```

* 组件路径:都在src/pages/lj-market-activity/inbound-code文件夹内

### 逻辑功能实现
```
1、查看入库列表
2、搜索框
3、查看具体物资行对应的出库扫码情况
4、查看出库扫码记录详情
5、查看更多（入库扫码记录）
6、点击扫码
7、保存
```

### 逻辑功能说明
```
1、入库扫码列表顶部筛选块为待扫码、已完成，进入页面默认定位在“待扫码”筛选块，
待扫码筛选条件如下：
    A：按照1）中的安全性找到当前登陆人当前职位可以看到的活动数据，记录为a；
    B：取a中活动状态等于已发布、进行中、执行结束、已实发，且活动审批状态为申请审批通过、反馈撤回、反馈驳回、反馈待审批、反馈审批通过的活动数据，记为b；
    C：取b的活动id，在活动费用申请物资表中，找到与当前活动id关联的费用申请物资行，取产品id记录为c； D：取a活动数据上的归属公司，在产品组表头上，找到公司id与该活动的公司id一致的数据，并且在产品组列表中，产品组类型为品鉴酒的产品组，记作d，产品组下的产品行记为d1；
    E：以c数据的产品id，匹配的d1数据的产品id，若产品id相同，取c的产品id作为待入库列表的数据e；
    F：若数据e中，【待入库瓶数】大于0的记录，确认为“待入库”页签的展示数据，按照活动id，申请使用物资行的产品id作为唯一性条件，展示待入库扫码记录的列表；
已完成筛选条件如下：
    A：按照1）中的安全性找到当前登陆人当前职位可以看到的活动数据，记录为a；
    B：取a中活动状态等于发布、进行中、执行结束、已实发，且活动审批状态为申请审批通过、反馈撤回、反馈驳回、反馈待审批、反馈审批通过的活动数据，记为b；
    C：取b的活动id，在活动费用申请物资表中，找到与当前活动id关联的费用申请物资行，取产品id记录为c；
    D：取a活动数据上的归属公司，在产品组表头上，找到公司id与该活动的公司id一致的数据，并且在产品组列表中，产品组类型为品鉴酒的产品，记作d，产品足下的产品行记为d1；
    E：以c数据的产品id，匹配的d1数据的产品id，若产品id相同，取c的产品id作为待入库列表的数据e；
    F：若数据e中，【待入库瓶数】等于0，已入库大于等于1的记录，确认为“已完成”页签的展示数据，按照活动id，申请使用物资行的产品id作为唯一性条件，展示已完成扫码记录的列表；
2、支持按照活动编码、产品编码、产品名称进行模糊查询
3、根据活动id和物资行id分别查询对应的信息
4、根据扫码行id查询对应的信息
5、当出库扫码记录大于3条展示查看更多按钮
6、当待扫码瓶>0，该按钮显示，当待扫码瓶数=0时，该按钮不显示
7、当待出库瓶数>0时显示，当待出库瓶数=0时不显示，点击按钮，返回至出库扫码列表页面；


```
