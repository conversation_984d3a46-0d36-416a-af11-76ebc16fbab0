<template>
    <link-page class="perform-case-list-page">
        <lnk-taps :taps="actProgOptions" v-model="actProgStatusActive" @switchTab="onTap" v-if="!pageParam.source"></lnk-taps>
        <link-auto-list :class="{'home-search':pageParam.source==='home'}" :option="autoList" hideCreateButton
        :searchInputBinding="{props:{placeholder:'执行案名称/执行案编码'}}">
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="perform-case-list-item">
                    <view slot="note">
                        <view class="media-list">
                            <view class="media-top">
                                <view class="num-view">
                                    <view class="num">{{data.executionCode}}</view>
                                </view>
                                <status-button :type="data.executionState">{{data.executionState | lov('EXECUTION_STATE')}}</status-button>
                            </view>
                        </view>
                        <view class="content-middle">
                            <view class="name">{{data.executionName}}</view>
                        </view>
                        <view class="content-middle-line" v-if="!$utils.isEmpty(data.approvalAmount) && priceShowFlag">
                            <view class="data">
                                <view class="title">审批金额</view>
                                <view class="val">{{data.approvalAmount| cny}}</view>
                            </view>
                        </view>
                        <view class="content-middle-line" v-if="!$utils.isEmpty(data.creatorName)">
                            <view class="data">
                                <view class="title">制单人</view>
                                <view class="val">{{data.creatorName}}</view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
    import Taro from "@tarojs/taro";
    import StatusButton from "../../lzlj/components/status-button";
    import LnkTaps from "../../core/lnk-taps/lnk-taps";

    export default {
        name: "perform-case-list-page",
        components: {LnkTaps, StatusButton},
        data() {
            /*
            * 一：执行案查询逻辑
            * 执行案模块 查询的是组织范围内 默认查询全部生效的执行案但是可以切换 查询全部 全部生效 部分生效 待终止 终止状态的数据。
            * 其他需要选择执行案的地方 是查询组织范围内且全部有效+部分有效的执行案
            * 二：执行案明细查询逻辑
            * 展示状态为已生效的数据
            * 2021-03-01更新：默认查询全部的数据
            * edit by 谭少奇 2023/09/18搜索加入字段、加入筛选条件 移除搜索变量
            * */
            const userInfo = Taro.getStorageSync('token').result;
            const menuId = this.pageParam.menuId;
            const accessGroupOauth = this.$utils.getMenuAccessGroup(menuId);
            let nowDate = new Date();
            let startDate = this.$date.format(nowDate, 'YYYY-MM-DD HH:mm:ss');
            nowDate.setDate(nowDate.getDate()+3)
            let lastDate = this.$date.format(nowDate, 'YYYY-MM-DD HH:mm:ss');
            let param = {
                // attr4: "mpExecutionPage",ss
                //2021-03-01更新：默认查询全部的数据
                filtersRaw: this.pageParam.source === 'home'?[
                    {id: 'actDeadLine', property: 'actDeadLine', value: lastDate, operator: '<'},
                    {id: 'actDeadLine', property: 'actDeadLine', value: startDate, operator: '>'}
                ]:[],
                queryFields: "executionCode,id,executionState,executionName,approvalAmount,creatorName",

            };
            if (!this.$utils.isEmpty(accessGroupOauth)) {
                param.oauth = accessGroupOauth;
            }
            const autoList = new this.AutoList(this, {
                url: {
                    queryByExamplePage: 'export/link/actProg/queryByExamplePage'
                },
                itemPath: '/pages/lj-market-activity/perform-case/perform-case-item-page',
                searchFields: ['executionCode', 'executionName', 'creatorName'],
                param: param,
                sortOptions: null,
                // edit by 谭少奇 新增筛选条件执行案提报时间
                filterOption: [{
                		label: '执行案提报时间',
                		field: 'createdTime',
                		type: 'date',
                	}
                ]
            });
            const actProgOptions = [
                {name: '全部', seq: '1', val: ''},
                {name: '全部生效', seq: '2', val: 'AllEfficient'},
                {name: '部分生效', seq: '3', val: 'PartEfficient'},
                {name: '待终止', seq: '4', val: 'Terminated'},
                {name: '终止', seq: '5', val: 'Closed'}
            ];
            return {
                // 费用价格是否展示标识
                priceShowFlag: false,
                userInfo,
                autoList,
                actProgOptions,
                actProgStatusActive: {},
            };
        },
        async created() {
            this.actProgStatusActive = this.actProgOptions[0];
            this.priceShowFlag = await this.$utils.getPriceDesensitize(this.userInfo);
        },
        methods: {
            /**
             *  @description: tab页签切换
             *  @author: 马晓娟
             *  @date: 2020/9/17 14:16
             */
            onTap(item) {
                if (!this.$utils.isEmpty(item.val)) {
                  let executionState = {
                    id: 'executionState',
                    property: 'executionState',
                    value: item.val
                  };
                  const executionStateExist = this.autoList.option.param.filtersRaw.filter((item) => item.property === 'executionState');
                  if (!this.$utils.isEmpty(executionStateExist)) {
                    let index = this.autoList.option.param['filtersRaw'].findIndex(val => val.property === 'executionState');
                    this.autoList.option.param['filtersRaw'][index]['value'] = item.val ;
                  }else{
                    this.autoList.option.param.filtersRaw.push(executionState);
                  }
                } else {
                    this.autoList.option.param['filtersRaw'] = this.autoList.option.param['filtersRaw'].filter((item) => item.property !== 'executionState');
                }
                this.autoList.methods.reload();
            },
        }
    }
</script>

<style lang="scss">
    @import "../../../styles/list-card";
    .perform-case-list-page {
        .lnk-tabs-container {
        	height: 92px;
        }
        background-color: #F2F2F2;
        font-family: PingFangSC-Regular;

        .link-auto-list .link-auto-list-top-bar {
            border-bottom: none !important;
        }

        .home-search .link-auto-list-top-bar {
            border-bottom: none !important;
            display: none;
        }

        .link-search-input {
            padding-top: 0px !important;
        }

        .have-padding{
            padding-top: 24px !important;
        }

        .type {
            //@include flex-end-center;
            color: #8C8C8C;
            font-size: 28px;
            //padding: 10px 20px 10px 10px;

            .lnk-tabs {
                width: 100%;
                font-size: 28px;
                text-align: center;
                margin-right: 24px;
                //padding: 6px 24px;
            }
        }

        .perform-case-list-item {
            background: #FFFFFF;
            margin: 24px;
            border-radius: 16px;
        }

        .media-list {
            @include media-list;

            .media-top {
                width: 100%;
                @include flex-start-center;
                @include space-between;

                .num-view {
                    background: #A6B4C7;
                    border-radius: 8px;
                    line-height: 40px;

                    .num {
                        font-size: 28px;
                        color: #FFFFFF;
                        letter-spacing: 0;
                        line-height: 40px;
                        padding: 2px 8px;
                    }
                }
            }
        }

        .content-middle {
            width: 100%;
            @include flex-start-center;
            @include space-between;
            margin: 40px 0 8px 0;

            .name {
                font-family: PingFangSC-Semibold;
                font-size: 32px;
                color: #262626;
                letter-spacing: 0;
                line-height: 32px;
            }
        }

        .content-middle-line {
            width: 100%;

            .data {
                width: 100%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    width: 20%;
                    float: left;

                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                }
            }
        }
    }
</style>
