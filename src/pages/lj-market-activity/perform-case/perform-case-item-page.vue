<template>
    <link-page class="perform-case-item">
        <link-form :option="formOption" hideEditButton hideSaveButton>
            <view class="zero-view"></view>
            <view style="width: 94%;margin-left: 3%">
                <view class="case-item">
                    <view class="item">
                        <view class="label">CRM/NC执行案编码</view>
                        <view class="text">{{formOption.formData.executionCode}}</view>
                    </view>
                    <view class="item">
                        <view class="label">执行案名称</view>
                        <view class="text">{{formOption.formData.executionName}}</view>
                    </view>
                    <view class="item">
                        <view class="label">状态</view>
                        <view class="text">{{formOption.formData.executionState | lov('EXECUTION_STATE')}}</view>
                    </view>
                    <view class="item" v-if="priceShowFlag">
                        <view class="label">申请金额</view>
                        <view class="text">{{formOption.formData.applyAmount | cny}}</view>
                    </view>
                    <view class="item" v-if="priceShowFlag">
                        <view class="label">审批金额</view>
                        <view class="text">{{formOption.formData.approvalAmount | cny}}</view>
                    </view>
                    <view class="item">
                        <view class="label">活动截止时间</view>
                        <view class="text">{{formOption.formData.actDeadLine | date('YYYY-MM-DD HH:mm:ss')}}</view>
                    </view>
                    <view class="item">
                        <view class="label">创建人</view>
                        <view class="text">{{formOption.formData.creatorName}}</view>
                    </view>
                    <view class="item">
                        <view class="label">创建人工号</view>
                        <view class="text">{{formOption.formData.creator}}</view>
                    </view>
                    <view class="item">
                        <view class="label">来源渠道</view>
                        <view class="text">{{formOption.formData.progSource}}</view>
                    </view>
                    <view class="item">
                        <view class="label">执行案描述</view>
                        <view class="text">{{formOption.formData.progComment}}</view>
                    </view>
                </view>
            </view>
            <view class="zero-view"></view>
            <view class="cost-list">
                <view class="item-header">
                    <text>费用信息</text>
                </view>
                <view class="item-data"
                      v-for="(costItem,index) in costList" :key="index" @tap="gotoCostItem(costItem)">
                    <view class="left-view">
                        <view class="media-list">
                            <view class="media-top">
                                <view class="left-content">{{costItem.costPaymentWay}}</view>
                                <view class="right-content" style="color: #2F69F8;font-size: 14px">
                                    {{costItem.costStatus|
                                    lov('FEE_STATUS')}}
                                </view>
                            </view>
                        </view>
                        <view class="content-middle">
                            <view class="content">
                                <view class="title">有效期</view>
                                <view class="val">{{costItem.startTime|date('YYYY-MM-DD')}}至{{costItem.endTime|date('YYYY-MM-DD')}}</view>
                            </view>
                        </view>
                        <view class="content-middle-line-1">
                            <view class="data">
                                <view class="title">费用垫付对象</view>
                                <view class="val">{{costItem.feeReim}}</view>
                            </view>
                        </view>
                        <view class="content-middle-line" v-if="priceShowFlag">
                            <view class="data">
                                <view class="title">可用余额</view>
                                <view class="val">{{costItem.availableBalance | cny}}</view>
                            </view>
                            <view class="sum">
                                <view class="title">审批金额</view>
                                <view class="val">{{costItem.approvalAmount | cny}}</view>
                            </view>
                        </view>
                    </view>
                    <view class="right-view">
                        <link-icon icon="mp-arrow-right" style="color: #BFBFBF;font-size: 14px"/>
                    </view>
                </view>
            </view>
            <view class="zero-view"></view>
            <view class="activity-list">
                <view class="item-header">
                    <view style="width: 50%;float: left">活动信息</view>
                    <view style="float: left;text-align: right;width: 35%;padding-right: 24px;color: #2F69F8;"
                          v-if="activityTotal>2"
                          @tap="queryActivityList">查看全部
                    </view>
                </view>
                <view class="item-data"
                      v-for="(activityItem,index) in activityList" :key="index" @tap="gotoMarketActivityItem(activityItem)">
                    <market-activity-item source="perform" :data="activityItem" :priceShowFlag="priceShowFlag" class="left-view"/>
                    <view class="right-view">
                        <link-icon icon="mp-arrow-right" style="color: #BFBFBF;font-size: 14px"/>
                    </view>
                </view>
            </view>
<!--    执行案-稽核记录       -->
            <view class="activity-list" v-if="closedShow">
                <view class="item-header">
                    <view style="width: 50%;float: left">稽核记录</view>
                    <view style="float: left;text-align: right;width: 35%;padding-right: 24px;color: #2F69F8;"
                          v-if="auditMore"
                          @tap="queryAuditActivityList">查看全部
                    </view>
                </view>

                <view class="item-data" v-for="(data,index) in auditList" :key="index" >
                        <view class="left-view" @tap="gotoAuditActivityItem(data)">
                            <view class="media-list">
                                <view class="media-top">
                                    <view class="num-view" style="background-color: #FFFFFF">
                                        <view class="num" style="color: #333333">{{data.activityNum}}</view>
                                    </view>
                                    <view class="abnormal-view" v-if="!data['isIllegalAct']">
                                        <view class="abnormal">不正常</view>
                                    </view>
                                </view>
                            </view>
                            <view class="content-middle">
                                <view class="name">{{data.activityName}}</view>
                            </view>
                            <view class="content-middle-line">
                                <view class="data" style="width: 100%">
                                    <view class="title">实际活动时间</view>
                                    <view class="val" style="text-align: right;">{{data.actRealTime|date('YYYY-MM-DD')}}---{{data.actRealEndTime|date('YYYY-MM-DD')}}</view>
                                </view>
                            </view>
                            <view class="content-middle-line">
                                <view class="data" style="width: 100%">
                                    <view class="title">是否实地检核</view>
                                    <view class="val" style="text-align: right;">{{data.isFieldAudit|lov('IS_FLAG')}}</view>
                                </view>
                            </view>
                            <view class="content-middle-line" style="display: flex;">
                                <view class="sum-2" style="flex: 1">
                                    <view class="title" style="margin-right: 10px;">申请金额 </view>
                                    <view class="val" style="color: #2F69F8"> {{data.activityAmount}}</view>
                                </view>
                                <view class="sum-2"  style="flex: 1">
                                    <view class="title" style="margin-right: 10px;">稽核金额 </view>
                                    <view class="val"  style="color: #2F69F8" > {{data.auditAmount}}</view>
                                </view>
                            </view>
                        </view>
                        <view class="right-view">
                            <link-icon icon="mp-arrow-right" style="color: #BFBFBF;font-size: 14px"/>
                        </view>
                </view>

            </view>
        </link-form>

        <link-sticky>
            <link-button block @tap="newActivities" v-if="!isSalesAreaManager&&showFlag&&createButton">新建活动</link-button>
            <link-button block :style="closedButton?'display:flex':'display:none'"  @tap="Closure">结案</link-button>
        </link-sticky>
        <view v-if="closedFlag" style="height: 60px;"></view>
        <view v-if="!isSalesAreaManager&&showFlag&&!closedFlag" style="height: 60px;"></view>
    </link-page>
</template>

<script>
    import {ROW_STATUS} from "../../../utils/constant";
    import Taro from "@tarojs/taro";
    import StatusButton from "../../lzlj/components/status-button";
    import MarketActivityItem from '../market-activity/components/market-activity-item';

    export default {
        name: "perform-case-item-page",
        components: {StatusButton, MarketActivityItem},
        data() {
            const config = {
                data: {
                    ...this.pageParam.data
                },
            };
            const formOption = new this.FormOption(this, {
                ...config,
                url: {
                    queryById: 'action/link/actProg/queryById'
                },
                operator: 'READ',
            });
            const performCaseItem = {
                ...config.data
            };
            const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
            const costList = [];
            const activityList = [];
            const activityTotal = 0;
            return {
                // 费用价格是否展示标识
                priceShowFlag: false,
                positionFlag:false,
                createButton:false,
                closedButton:false,
                closedShow:false,
                closedFlag:false,
                activityTotal,
                costList,
                activityList,
                auditList:[],
                auditMore:false,
                performCaseItem,
                formOption,
                userInfo,
                showFlag:false //是否显示提交按钮
            }
        },
        computed: {
            isSalesAreaManager: function () {
                return ['CityManager', 'SalesRegionManager', 'SalesAreaManager',].includes(Taro.getStorageSync('token').result.positionType)
            }
        },
        async onLoad() {
            // 查询费用
           await this.initCustList();
            // 查询活动信息
           await this.initActivityList();
            //查询结案按钮是否显示
           await this.getUserPositionDept();
            // 查询稽核信息
            if(this.closedShow){
           await this.initAuditList();
            }
        },
        mounted() {
            this.$bus.$on('initActivityList', async () => {
                await this.initActivityList();
            });
        },
        async created() {
            this.priceShowFlag = await this.$utils.getPriceDesensitize(this.userInfo);
            this.positionFlag = ['FinanceStaff', 'AuditStaff', 'InspectionStaff'].includes(this.userInfo.positionType);
        },
        methods: {
            /**
             * 结案按钮
             * <AUTHOR>
             * @date 2022年5月20日
             * */
            Closure(){
                this.$dialog({
                    title: '确认提示',
                    content: '结案状态不可逆，是否结案？',
                    confirmButton: true,
                    cancelButton: true,
                    initial: true,
                    onConfirm: async () => {
                        const data = await this.$http.post('action/link/actProg/update', {
                            id: this.formOption.formData.id,
                            isClosed: 'Closed',
                            updateFields: 'isClosed'
                        });
                        if(data.success&&this.$utils.isNotEmpty(data.newRow)){
                            this.$message.success('执行案已结案');
                            this.closedButton = false;
                        }else{
                            this.$nav.error('执行案结案失败');
                        }
                    }
                });
            },
            /**
             * 结案按钮显示
             * <AUTHOR>
             * @date 2022年5月20日
             * */
            async getUserPositionDept(){
                try{
                    const data = await this.$http.post('action/link/position/queryByExamplePage', {id: this.userInfo.postnId});
                    if(data.success&&this.$utils.isNotEmpty(data.rows)){
                        if((data.rows[0].deptCode==='BM100536'||data.rows[0].deptCode==='L202206090001') && this.formOption.formData['isClosed'] !== 'Closed' ){
                            this.closedFlag = true;
                            this.closedShow = true;
                            this.createButton = false;
                        }else if (data.rows[0].deptCode==='BM100536'||data.rows[0].deptCode==='L202206090001'){
                            this.closedShow = true;
                            this.createButton = false;
                        }
                        else{
                            this.createButton = true;
                        }
                    } else{
                        this.closedFlag = false;
                        this.closedShow = false;
                        this.createButton = true;
                    }
                } catch(e){
                    this.closedFlag = false;
                    this.closedShow = false;
                    this.createButton = true;
                }
            },

            async initCustList() {
                const data = await this.$http.post('action/link/costDetail/queryByExamplePage', {
                    filtersRaw: [
                        {id: 'actProgId', property: 'actProgId', value: this.performCaseItem.id, operator: '='},
                    ],
                    oauth: 'MY_ORG',
                });
                this.costList = data.rows;
                this.costList.forEach((item)=>{
                    if(!item.passMark && item.costStatus!=='Terminated' && item.costStatus!=='TerminatProcessed'){
                        this.showFlag = true;
                    }
                })
            },
            async initActivityList() {
                const data = await this.$http.post('action/link/marketAct/queryByExamplePage', {
                    totalFlag: true,
                    rows: 2,
                    sort: "created",
                    order: 'desc',
                    filtersRaw: [
                        {id: 'exeCaseId', property: 'exeCaseId', value: this.performCaseItem.id, operator: '='},
                    ]
                });
                this.activityList = data.rows;
                this.activityTotal = data.total;
            },
            /**
             * 获取稽核信息
             * <AUTHOR>
             * @date 2022年5月20日
             * */
           async initAuditList(){
                const data = await this.$http.post('action/link/exeFeedback/queryActProgAuditPage', {
                    totalFlag: true,
                    rows: 5,
                    sort: "created",
                    order: 'desc',
                    exeCaseId: this.performCaseItem.id
                });
                if(data.rows.length !== 0 && this.closedFlag){
                    this.closedButton = true;
                }
                if(data.rows.length>2){
                    this.auditList.push(data.rows[0])
                    this.auditList.push(data.rows[1])
                    this.auditMore = true;
                }else{
                    this.auditList = data.rows;
                    this.auditMore = false;
                }

            },
            /**
             * 新建活动
             * <AUTHOR>
             * @date 2020-08-03
             * */
            async newActivities() {
                const id = await this.$newId();
                const countey = await this.$lov.getValByTypeAndName('COUNTRY_ID','中国');
                //为了做费用的缓存，copy一个界面单独给执行案新建场景使用。
                this.$nav.push('/pages/lj-market-activity/perform-case/perform-case-cost-list-new-page', {
                    pageForm: 'case',
                    operant: 'NEW',//控制下一个界面按钮
                    scene: 'apply',//区分申请费用还是实际费用
                    /**
                     * 控制是否可以操作 cash-new 和 prod-new组件的删除、新建、以及删除兑付方式的操作
                     * 只允许在编辑界面删除兑付方式、删除明细 、新增明细。其他界面可以编辑小计 和 编辑某个产品信息
                     * */
                    operateFlag: true,
                    data: {
                        id: id,
                        row_status: ROW_STATUS.NEW,
                        executor: this.userInfo.firstName,
                        executorPhone: this.userInfo.contactPhone,
                        status: "New",//活动状态默认新建
                        actStage: "ActReport",//活动阶段默认活动提报
                        aproStatus: "New",//审批状态默认New
                        whetherAudit: "NoCheck",//稽核状态默认未稽核
                        exeCaseId: this.performCaseItem.id,//执行案ID
                        exeCaseCode: this.performCaseItem.executionCode,//执行案编码
                        executionName: this.performCaseItem.executionName,//执行案名称
                        progComment:this.performCaseItem.progComment,//备注
                        companyId: this.userInfo.coreOrganizationTile.l3Id,//公司id
                        salesBigAreaId: this.userInfo.coreOrganizationTile.l4Id,//大区id
                        salesBigArea: this.userInfo.coreOrganizationTile.l4Name,
                        salesRegionId: this.userInfo.coreOrganizationTile.l5Id,//片区ID
                        salesRegion: this.userInfo.coreOrganizationTile.l5Name,
                        salesCityId: this.userInfo.coreOrganizationTile.l6Id,//城市ID
                        salesCity: this.userInfo.coreOrganizationTile.l6Name,
                        salesDistrictId: this.userInfo.coreOrganizationTile.l7Id,//区县ID
                        salesDistrict: this.userInfo.coreOrganizationTile.l7Name,
                        targetPopulation: 'OrdinaryConsumers',
                        countryName: countey,
                        isCompanyCostFlag : 'N',//是否总部活动费用字段的值默认为N
                    }
                });
            },
            /**
             * 费用详情
             * <AUTHOR>
             * @date 2020-08-03
             * */
            gotoCostItem(input) {
                this.$nav.push('/pages/lj-market-activity/perform-case/cost-item-page', {
                    data: input,
                    pageSource: 'case',
                    callback: async () => {
                        //执行案详情时查询
                        const data = await this.$http.post('action/link/actProg/queryById', {
                            id: this.performCaseItem.id
                        });
                        this.formOption.formData = {...data.result};
                      this.initCustList();
                    }
                })
            },
            /**
             * 查看所有活动
             * <AUTHOR> */
            queryActivityList() {
                this.$nav.push('/pages/lj-market-activity/perform-case/activity-list-page', {
                    exeCaseId: this.performCaseItem.id
                })
            },
            /**
             * 查看所有执行案下所属活动的稽核
             * <AUTHOR> */
            queryAuditActivityList() {
                this.$nav.push('/pages/lj-market-activity/perform-case/performed-act-audit-page', {
                    exeCaseId: this.performCaseItem.id
                })
            },
            /**
             * 调转到活动详情
             * <AUTHOR>
             * @date 2020-10-09
             * */
            async gotoMarketActivityItem(input) {
                const cacheData = await this.$http.post('action/link/marketAct/queryById', {
                    id: input.id
                });
                if(cacheData.success){
                this.$dataService.setMarketActivityItem(cacheData.result);
                this.$nav.push('/pages/lj-market-activity/market-activity/market-activity-item-page', {
                    data: cacheData.result,
                    pageSource: "view" //标志界面来源 区别于"其他信息"界面的预览活动
                })
                }
            },
            /**
             * 调转到稽核记录
             * <AUTHOR>
             * */
            async gotoAuditActivityItem(data) {
                this.$nav.push('/pages/lj-market-activity/work-order/activity-approva-audit-page', {
                    data: {
                        id:data.actId,
                        startTime: data.actRealTime,
                        endTime: data.actRealEndTime
                    },
                    link: 'check' //标志界面来源 区别于"其他信息"界面的预览活动
                })
            }
        }
    }
</script>

<style lang="scss">
    @import "../../../styles/list-card";
    .perform-case-item {
        .iconBar{
            display:flex;
            width: 25%;
        }
        .abnormal-view {
            background: #EA2F2F;
            border-radius: 8px;
            line-height: 40px;

            .abnormal {
                font-size: 28px;
                color: #FFFFFF;
                letter-spacing: 0;
                line-height: 40px;
                padding: 2px 8px;
            }
        }

        background-color: #F2F2F2;
        width: 100%;
        overflow-x: hidden;

        .zero-view {
            width: 100%;
            height: 28px;
        }

        .case-item {
            margin: auto;
            border-radius: 16px;
            padding: 20px 28px 20px 28px;
            background: white;

            .item {
                line-height: 56px;
                width: 100%;
                display: flex;

                .label {
                    color: #595959;
                    font-size: 28px;
                    display: flex;
                    align-items: center;
                    box-sizing: border-box;
                    width: 300px;
                }

                .text {
                    color: #262626;
                    width: 420px;
                    font-size: 28px;
                    text-align: right;
                }
            }
        }

        .cost-list {
            background: white;
            width: 93%;
            margin: auto;
            border-radius: 16px;
            padding-bottom: 24px;

            .item-header {
                height: 88px;
                width: 100%;
                padding-left: 32px;
                font-size: 28px;
                line-height: 88px;
                color: #262626;
                letter-spacing: 0;
                border-bottom: 2px solid #F2F2F2;
            }

            .item-data {
                width: 100%;
                border-bottom: 2px solid #F2F2F2;
                display: flex;

                .left-view {
                    width: 90%;

                    .media-list {
                        @include media-list;

                        .media-top {
                            width: 100%;
                            @include flex-start-center;
                            @include space-between;
                            height: 80px;
                            line-height: 80px;
                            padding-left: 32px;

                            .left-content {
                                font-family: PingFangSC-Semibold;
                                font-size: 32px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 32px;
                                padding-top: 20px;

                            }

                            .right-content {
                                font-family: PingFangSC-Semibold;
                                font-size: 32px;
                                color: #FF5A5A;
                                letter-spacing: 0;
                                text-align: right;
                                line-height: 32px;
                                padding-top: 20px;
                            }

                            .num-view {
                                background: #A6B4C7;
                                border-radius: 8px;
                                line-height: 40px;

                                .num {
                                    font-size: 28px;
                                    color: #FFFFFF;
                                    letter-spacing: 0;
                                    line-height: 40px;
                                    padding: 2px 8px;
                                }
                            }
                        }
                    }

                    .content-middle {
                        width: 100%;
                        @include flex-start-center;
                        @include space-between;
                        height: 50px;
                        line-height: 50px;
                        padding-left: 32px;

                        .content {
                            width: 100%;
                            float: left;

                            .title {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #8C8C8C;
                                letter-spacing: 0;
                                line-height: 56px;
                                width: 15%;
                                float: left;
                            }

                            .val {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #000000;
                                letter-spacing: 0;
                                line-height: 56px;
                                width: 75%;
                                float: left;
                                padding-left: 5px;
                            }
                        }

                        .name {
                            font-family: PingFangSC-Semibold;
                            font-size: 32px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 32px;
                        }

                        .data {
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #8C8C8C;
                            letter-spacing: 0;
                            line-height: 28px;
                        }

                        .sum {
                            font-family: PingFangSC-Semibold;
                            font-size: 32px;
                            color: #FF5A5A;
                            letter-spacing: 0;
                            text-align: right;
                            line-height: 32px;
                        }
                    }

                    .content-middle-line-1 {
                        width: 100%;
                        padding-left: 32px;

                        .data {
                            width: 100%;
                            float: left;

                            .title {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #8C8C8C;
                                letter-spacing: 0;
                                line-height: 56px;
                                width: 32%;
                                float: left;
                            }

                            .val {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #000000;
                                letter-spacing: 0;
                                line-height: 56px;
                                width: 66%;
                                float: left;
                                padding-left: 5px;
                            }
                        }
                    }

                    .content-middle-line {
                        width: 100%;
                        padding-left: 32px;

                        .data {
                            width: 50%;
                            float: left;

                            .title {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #8C8C8C;
                                letter-spacing: 0;
                                line-height: 56px;
                                width: 40%;
                                float: left;

                            }

                            .val {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #000000;
                                letter-spacing: 0;
                                line-height: 56px;
                                width: 58%;
                                float: left;
                                padding-left: 5px;
                            }
                        }

                        .sum {
                            width: 50%;
                            float: left;

                            .title {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #8C8C8C;
                                letter-spacing: 0;
                                line-height: 56px;
                                width: 40%;
                                float: left;

                            }

                            .val {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #000000;
                                letter-spacing: 0;
                                line-height: 56px;
                                width: 58%;
                                float: left;
                                padding-left: 5px;
                            }
                        }

                        .sum-2 {
                            width: 100%;
                            float: left;

                            .title {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #8C8C8C;
                                letter-spacing: 0;
                                line-height: 56px;
                                float: left;
                                margin-right: 5px;
                            }

                            .val {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #000000;
                                letter-spacing: 0;
                                line-height: 56px;
                            }
                        }
                    }
                }

                .right-view {
                    width: 10%;
                    float: left;
                    height: 210px;
                    line-height: 210px;
                    text-align: center;
                }
            }

            .item-data:last-child {
                border-bottom: none;
            }
        }

        .activity-list {
            background: white;
            margin: 0 24px 24px 24px;
            border-radius: 16px;
            padding-bottom: 24px;

            .item-header {
                height: 88px;
                width: 100%;
                padding-left: 32px;
                font-size: 28px;
                line-height: 88px;
                color: #262626;
                letter-spacing: 0;
                border-bottom: 2px solid #F2F2F2;
            }

            .item-data {
                width: 100%;
                border-bottom: 2px solid #F2F2F2;
                background: white;
                display: flex;

                .left-view {
                    width: 90%;

                    .media-list {
                        @include media-list;

                        .media-top {
                            width: 100%;
                            @include flex-start-center;
                            @include space-between;
                            height: 80px;
                            line-height: 80px;
                            padding-left: 32px;

                            .left-content {
                                font-family: PingFangSC-Semibold;
                                font-size: 32px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 32px;
                                padding-top: 20px;

                            }

                            .right-content {
                                font-family: PingFangSC-Semibold;
                                font-size: 32px;
                                color: #FF5A5A;
                                letter-spacing: 0;
                                text-align: right;
                                line-height: 32px;
                                padding-top: 20px;
                            }

                            .num-view {
                                background: #A6B4C7;
                                border-radius: 8px;
                                line-height: 40px;

                                .num {
                                    font-size: 28px;
                                    color: #FFFFFF;
                                    letter-spacing: 0;
                                    line-height: 40px;
                                    padding: 2px 8px;
                                }
                            }

                            .status-view {
                                width: 120px;
                                transform: skewX(-10deg);
                                border-radius: 4px;
                                background: #2F69F8;
                                box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                                height: 36px;

                                .status {
                                    font-size: 20px;
                                    color: #FFFFFF;
                                    letter-spacing: 2px;
                                    text-align: center;
                                    line-height: 36px;
                                }
                            }
                        }
                    }

                    .content-middle {
                        width: 100%;
                        @include flex-start-center;
                        @include space-between;
                        height: 80px;
                        line-height: 80px;
                        padding-left: 32px;

                        .content {
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #000000;
                            letter-spacing: 0;
                            line-height: 28px;
                        }

                        .name {
                            font-family: PingFangSC-Semibold;
                            font-size: 32px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 32px;
                        }
                    }

                    .content-middle-line {
                        width: 100%;
                        padding-left: 32px;

                        .data {
                            width: 55%;
                            float: left;

                            .title {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #8C8C8C;
                                letter-spacing: 0;
                                line-height: 56px;
                                width: 40%;
                                float: left;

                            }

                            .val {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #000000;
                                letter-spacing: 0;
                                line-height: 56px;
                                width: 58%;
                                float: left;
                                padding-left: 5px;
                            }

                            .Submitted, .Feedback{
                                color: #2F69F8;
                            }

                            .Approve, .FeedbackApro{
                                color: #2EB3C2;
                            }

                            .Refused, .Refeedback{
                                color: #FF5A5A;
                            }
                        }

                        .sum {
                            width: 45%;
                            float: left;

                            .title {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #8C8C8C;
                                letter-spacing: 0;
                                line-height: 56px;
                                width: 50%;
                                float: left;

                            }

                            .val {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #000000;
                                letter-spacing: 0;
                                line-height: 56px;
                                width: 48%;
                                float: left;
                                padding-left: 0px;
                                white-space:nowrap;
                            }
                        }

                        .sum-2 {
                            width: 100%;
                            float: left;

                            .title {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #8C8C8C;
                                letter-spacing: 0;
                                line-height: 56px;
                                float: left;
                                margin-right: 5px;
                            }

                            .val {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #000000;
                                letter-spacing: 0;
                                line-height: 56px;
                            }
                        }
                    }
                }

                .right-view {
                    width: 10%;
                    float: left;
                    height: 200px;
                    line-height: 200px;
                    text-align: center;
                }
            }

            .item-data:last-child {
                border-bottom: none;
            }
        }
    }
</style>
