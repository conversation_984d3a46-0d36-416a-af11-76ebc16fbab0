<template>
    <link-page class="perform-case-cost-list-new-page">
        <ma-navigation-bar :backVisible="true"
                           :zIndex="zIndex"
                           :backgroundImg="$imageAssets.homeMenuBgImage"
                           :title="navigationBarTitle"
                           :titleColor="navigationBarTitleColor"
                           :navBackgroundColor="navBackgroundColor"
                           :udf="udfBack">
        </ma-navigation-bar>
        <view>
            <item :title="scene === 'apply' ? '费用申请' : '费用实际'" @tap="chooseCost">
                <view v-if="$utils.isEmpty(prodPayList) && $utils.isEmpty(cashPayList)">{{
                    scene === 'apply' ? '请选择费用申请'
                    : '请选择费用实际'
                    }}
                </view>
                <view v-if="!$utils.isEmpty(prodPayList) || !$utils.isEmpty(cashPayList)">{{
                    scene === 'apply' ?
                    '可重新调整费用申请' : '可重新调整费用实际'
                    }}
                </view>
            </item>
        </view>
        <view>
            <view style="width: 100%;height: 12px"></view>
            <!--现金类-->
            <view v-for="(cashItem,index1) in cashPayList">
                <cash-new :cashShow="true" @updateCash="updateCashData" :bthShow="true" :parentData="activityItem"
                          :cashItem="cashItem" :pageSource="pageSource" :scene="scene"
                          :prodAndCostList="prodAndCostList"
                          @deleteCaseFeePay="deleteCaseFeePayFun" :operateFlag="operateFlag"></cash-new>
            </view>
            <view style="width: 100%;height: 12px"></view>
            <!--产品类-->
            <view v-for="(prodItem,index2) in prodPayList">
                <prod-new :prodShow="true" :prodItem="prodItem" :pageSource="pageSource" @deleteProd="deleteProdFun"
                          @updateProd="updateProdData" :scene="scene" @deleteProdFeePay="deleteProdFeePayFun"
                          :prodAndCostList="prodAndCostList"
                          :bthShow="true" :parentData="activityItem" :operateFlag="operateFlag"></prod-new>
            </view>
            <view style="width: 100%;height: 12px"></view>
        </view>
        <link-sticky>
            <link-button block mode="stroke" @tap="lastStep">上一步</link-button>
            <link-button block @tap="nextStep">下一步</link-button>
        </link-sticky>
        <link-dialog ref="dialog" disabledHideOnClickMask verticalFootButton>
            <view slot="head">
                活动类型
            </view>
            <view style="width: 100%;text-align: center;">
                <view style="width: 100%;height: 20px;line-height: 20px" @tap="showPickActivityDialog">
                    <view style="width: 90%;float: left;text-align: center;">
                        <view v-if="!$utils.isEmpty(activityItem.actIndeSourCode)">{{activityItem.actIndeSourCode | lov('MC_TYPE')}}</view>
                        <view v-if="$utils.isEmpty(activityItem.actIndeSourCode)" style="color: #999999">请选择活动类型</view>
                    </view>
                    <view style="width: 10%;float: right;">
                        <link-icon icon="mp-arrow-right"/>
                    </view>
                </view>
            </view>
            <link-button slot="foot" @tap="createNewActivity">新建活动</link-button>
            <link-button slot="foot" @tap="newDirectActivity">不选类型直接创建工作单</link-button>
            <link-button slot="foot" @tap="cancel">取消</link-button>
        </link-dialog>
        <link-dialog ref="activityPickDialog" position="bottom" disabledHideOnClickMask>
            <view style="width: 100%;height: 20px;line-height: 20px">
                <view style="width: 90%;float: left;text-align: center;">活动类型选择</view>
                <view style="width: 10%;height: 20px;float: right" @tap="$refs.activityPickDialog.hide()">
                    <link-icon icon="mp-close"/>
                </view>
            </view>
            <scroll-view scroll-y="true" class="list-container" style="height: 600rpx">
                <view v-for="(item1,index) in activityTypeList" :key="item1.id">
                    <view class="list-item" @tap="selectCostActivityType(item1)">
                        <view class="left-content">
                            <view class="row-1">
                                <text>{{item1.actTypeName}}</text>
                            </view>
                        </view>
                        <view class="right-content">
                            <view v-if="item1._checked">
                                <link-icon size="1.8em" style="color:#2F69F8;font-size: 16px" icon="icon-check"/>
                            </view>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </link-dialog>
    </link-page>
</template>

<script>
    import {DateService,LovService,FilterService,ComponentUtils} from "link-taro-component";
    import CashNew from "../market-activity/components/cash-new";
    import ProdNew from "../market-activity/components/prod-new";
    import {ROW_STATUS} from "../../../utils/constant";
    import MaNavigationBar from "../ma-navigation-bar/ma-navigation-bar";
    import Taro from "@tarojs/taro";
    import {PageCacheManager} from "../../../utils/PageCacheManager";

    export default {
        name: "perform-case-cost-list-new-page",
        components: {MaNavigationBar, ProdNew, CashNew},
        data() {
            //页面来源 -
            // 1、执行反馈环节 executiveFeedback
            // 2、other 活动的其他信息(ps:这是一个页面)
            // 3、preview 活动预览界面而来
            // 4、view 活动查看界面查看而来
            // 5、审批、小程序消息、执行案 为空
            // 6、activityAudit 活动稽核
            const pageSource = this.pageParam.pageSource;
            const scene = this.pageParam.scene;//区分实际费用 actual 还是申请费用 apply 【执行案新建活动-固定为apply】
            const activityItem = this.pageParam.data;
            const activityId = this.pageParam.data.id;//活动ID,先选择费用后维护市场活动的情况，这个ID仅用于匹配后台查询活动已选费用接口结构，最终新建活动的ID和费用信息保存时的父ID是在点下一步即时生成的。
            const exeCaseId = this.pageParam.data.exeCaseId; //执行案ID
            const pageForm = this.pageParam.pageForm;//页面来源 'case' 执行案 'marketActivity' 市场活动 【执行案新建活动-固定为case】
            const cashShow = false;//现金类-底部内容是否展示
            const prodShow = false;//产品类-底部内容是否展示
            const maintenanceModules = this.pageParam.maintenanceModules;//新建活动配置的需要维护的模块-用于判别是否配置了互动模块，控制界面下一步跳转页面
            const prodAndCostList = {
                Money: [],//现金支付类数据
                Product: [],//产品支付类数据
            };
            let executivesId = '';
            if (!this.$utils.isEmpty(this.pageParam.data.executivesId)) {
                executivesId = this.pageParam.data.executivesId;
            }
            //省市区县默认为空，执行案模块新建活动时，操作当前费用界面时还没有保存活动信息。
            const needUpdateActivity = {
                executivesId: executivesId,
                id: activityId,
                row_status: ROW_STATUS.UPDATE,
                province: "",
                provinceId: "",
                city: "",
                cityId: "",
                district: "",
                districtId: "",
                        updateFields: "id,executivesId,province,provinceId,city,cityId,district,districtId,jiheSecField"
            };
            //需要更新的活动对象-核心字段：费用垫付对象ID
            /**
             * 控制是否可以操作 cash-new 和 prod-new组件的删除、新建、以及删除兑付方式的操作
             * 只允许在编辑界面删除兑付方式、删除明细 、新增明细。其他界面可以编辑小计 和 编辑某个产品信息
             * */
            const operateFlag = this.pageParam.operateFlag;
            //场景来源服务于自定义导航栏。
            //1、市场活动新建 newMarketActivity 2、执行案新建市场活动 caseNewMarketActivity 3、其他 other
            let sceneSourceForNavigation = "other";//默认other
            if (!this.$utils.isEmpty(this.pageParam.sceneSourceForNavigation)) {
                sceneSourceForNavigation = this.pageParam.sceneSourceForNavigation;
            }

            /*检测应用切入后台缓存当前页面数据*/
            const cacheData = PageCacheManager.getInitialData({
                ctx: this,
                path: 'lj-market-activity/perform-case/perform-case-cost-list-new-page.vue',
                title: '执行案-费用信息',
                initialData: {
                    prodAndCostList,
                    nextPageCacheData: {formData: null},
                },
            })
            if (cacheData.prodAndCostList !== prodAndCostList) {
                setTimeout(() => {
                    this.prodAndCostList = cacheData.prodAndCostList
                }, 1000)
            }
            const activityTypeList = [];
            const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
            return {
                // 费用价格是否展示标识
                priceShowFlag: false,
                userInfo,
                activityTypeList,
                nextPageCacheData: cacheData.nextPageCacheData,
                navigationBarTitle: '费用信息',
                navigationBarTitleColor: '#ffffff',
                navBackgroundColor: 'transparent',
                zIndex: ComponentUtils.nextIndex(),
                sceneSourceForNavigation,
                operateFlag,
                pageSource,
                scene,
                needUpdateActivity,
                maintenanceModules,
                prodAndCostList,//产品和费用list
                activityId,
                activityItem,
                cashShow,
                prodShow,
                exeCaseId,
                pageForm,
                prodPayList: [],
                cashPayList: [],
                insertCostList: [],//新建时 插入到费用表中的数据列表
                actTypeRequired: '',// 活动类型是否必输
                index: 0,
                noCaseChooseCostOption: new this.AutoList(this, {
                    module: 'action/link/payment',
                    sortOptions: null,
                    param: {
                        filtersRaw: [
                            //是否有效
                            {
                                id: 'effectiveFlag',
                                property: 'effectiveFlag',
                                value: 'Y',
                                operator: '='
                            },
                        ],
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} arrow="false">
                            <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                            <view
                        style="display: -webkit-box;display: -ms-flexbox;display: flex;padding: 12px 8px 12px 12px;width:100%">
                            <view
                        style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px;">
                            {data.payCode}-{data.payName}
                            </view>
                            </view>
                            </item>
                    )
                    }
                }),
                haveCaseChooseCostOption: new this.AutoList(this, {
                    url: {
                        //查询活动未选中的费用
                        queryByExamplePage: 'action/link/costDetail/queryUnSelectPage'
                    },
                    param: {
                        //attr5开启时间校验
                        attr5: 'Y',
                        filtersRaw: [
                            {id: "actId", property: "actId", value: activityId, operator: '='},
                            {id: 'actProgId', property: 'actProgId', value: exeCaseId, operator: '='},
                            {id: 'costStatus', property: 'costStatus', value: 'Efficient', operator: '='}
                        ],
                        oauth: 'MY_ORG',
                    },
                    sortOptions: null,
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} arrow="false"
                        style="margin: 0px 12px 12px 12px;border-radius: 8px;">
                            <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                            <view style="width: 100%;background: white;height: 155px;">
                            <view style="width: 100%;float: left;">
                            <view
                        style="display: -webkit-box;display: -ms-flexbox;display: flex;margin: auto;">
                            <view style="width: 100%;height: 40px;line-height:40px;padding-left: 16px;">
                            <view
                        style="font-family: PingFangSC-Semibold;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 16px;padding-top: 10px;width:70%;float:left">{data.costPaymentWay}
                            </view>
                            <view
                        style="color: #2F69F8;font-size: 14px">{LovService.filter(data.costStatus, 'FEE_STATUS')}</view>
                            </view>
                            </view>
                            <view style="height: 30px;line-height: 30px;padding-left: 16px;width:100%">
                            <view
                        style="font-family: PingFangSC-Regular;font-size: 12px;color: #000000;letter-spacing: 0;line-height: 14px;">
                            有效期:{DateService.filter(data.startTime, 'YYYY-MM-DD HH:mm')}至{DateService.filter(data.endTime, 'YYYY-MM-DD HH:mm')}
                    </view>
                        </view>
                        <view style="height: 30px;line-height: 30px;padding-left: 16px;width:100%">
                            <view
                        style="font-family: PingFangSC-Regular;font-size: 12px;color: #000000;letter-spacing: 0;line-height: 14px;">
                            小类名称:{data.costTypeName}
                    </view>
                        </view>
                        <view style="height: 30px;line-height: 30px;padding-left: 16px;width:100%">
                            <view
                        style="font-family: PingFangSC-Regular;font-size: 12px;color: #000000;letter-spacing: 0;line-height: 14px;">
                            费用垫付对象:{data.feeReim}
                    </view>
                        </view>
                        <view style="padding-left: 16px;">
                            <view
                        style="font-family: PingFangSC-Regular;font-size: 12px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;width: 50%;float: left;">
                            {this.priceShowFlag ? `可用余额:`+FilterService.cny(data.availableBalance): (data.payType!=='Money'? '' : `可用余额:`+FilterService.cny(data.availableBalance)) }
                            </view>
                        <view
                        style="font-family: PingFangSC-Regular;font-size: 12px;color: #8C8C8C;letter-spacing: 0;text-align: right;line-height: 14px;width: 50%;float: left;">
                            {this.priceShowFlag ? `审批金额:`+FilterService.cny(data.approvalAmount): (data.payType!=='Money'? '' : `审批金额:`+FilterService.cny(data.approvalAmount)) }
                    </view>
                        </view>
                        </view>
                        </view>
                        </item>
                    )
                    }
                }),
            }
        },
        watch: {
            prodAndCostList: {
                handler(newVal) {
                    this.prodPayList = newVal.Product;
                    this.cashPayList = newVal.Money;
                },
                deep: true
            }
        },
         async created() {
             this.priceShowFlag = await this.$utils.getPriceDesensitize(this.userInfo);
        },
        mounted() {
            this.$bus.$on('ProductRrefresh', (data) => {
                this.$set(this.prodAndCostList, 'Product', data);
            });
            this.$bus.$on('ProductDataListRrefreshs', (feePayType, costId, dataList) => {
                this.prodAndCostList.Product.forEach((item) => {
                    if (item.feePayType === feePayType && item.costId === costId) {
                        this.$set(item, 'dataList', dataList);
                    }
                })
            });
            this.$bus.$on('MoneyRrefresh', (data) => {
                this.$set(this.prodAndCostList, 'Money', data);
            });
            this.$bus.$on('MoneyDataListRrefresh', (feePayType, costId, dataList) => {
                this.prodAndCostList.Money.forEach((item) => {
                    if (item.feePayType === feePayType && item.costId === costId) {
                        this.$set(item, 'dataList', dataList);
                    }
                })
            });
        },
        methods: {
            /**
             * 自定义返回函数
             * @songyanrong
             * @date 2020-12-02
             * */
            udfBack() {
                if (this.sceneSourceForNavigation === 'other' || this.sceneSourceForNavigation === 'caseNewMarketActivity') {
                    this.$nav.back();
                } else {
                    let pages = Taro.getCurrentPages();    //获取当前页面信息栈
                    let targetIndex = pages.findIndex(function (item) {
                        return item.route === "pages/lj-perform-case/perform-case/perform-case-list-page";
                    });
                    if (targetIndex === -1) {
                        return this.$nav.backAll()
                    }
                    const num = Number(pages.length - (Number(targetIndex) + 1));
                    setTimeout(() => {
                        this.$bus.$emit('marketActivityListRefresh');
                        this.$nav.back(null, num);
                    }, 1000)
                }
            },
            /**
             * 删除某个产品
             * <AUTHOR>
             * @date 2020-11-06
             * */
            deleteProdFun(feePayType, dataList) {
                for (let i = 0; i < this.prodAndCostList.Product.length; i++) {
                    if (this.prodAndCostList.Product[i].feePayType === feePayType) {
                        this.$set(this.prodAndCostList.Product[i], 'dataList', dataList);
                    }
                }
                this.$bus.$emit("ProductRrefresh", this.prodAndCostList.Product);
            },
            /**
             * 选择费用申请
             * <AUTHOR>
             * @date 2020-08-19
             * */
            async chooseCost() {
                await this.haveCaseChooseCost();
            },
            /**
             * 有执行案时选费用
             * <AUTHOR>
             * @date 2020-08-19
             * */
            async haveCaseChooseCost() {
                /*
                * 区分来自执行案还是市场活动
                * 1、如果来自执行案详情的新建活动按钮 那么新建的活动信息【费用大中小类】来自费用明细上
                * 2、如果来自市场活动模块新建
                * */
                // 2、市场活动新建
                //2020-10-09 新加：添加费用时当为选择执行案明细时，带出费用垫付对象ID和名称 更新到活动头上
                //2020-10-19 新加：当关联上执行案明细后，则使用执行案明细上的省-市-区县更新活动上的省市区县；
                const list = await this.$object(this.haveCaseChooseCostOption, {
                    multiple: true,
                    pageTitle: "费用明细",
                });
                list.forEach(i => {
                    if (!this.$utils.isEmpty(i.feeReimId)) {
                        this.needUpdateActivity.executivesId = i.feeReimId;//executivesId 费用垫付对象ID
                        this.needUpdateActivity.executivesName = i.feeReim;//费用垫付对象名称-用于活动新建界面展示-不存
                        this.needUpdateActivity.availableBalance = i.availableBalance;//可用余额-用于费用申请即将恶魔展示-不存
                        this.needUpdateActivity.province = i.province;
                        this.needUpdateActivity.provinceId = i.provinceId;
                        this.needUpdateActivity.city = i.city;
                        this.needUpdateActivity.cityId = i.cityId;
                        this.needUpdateActivity.district = i.district;
                        this.needUpdateActivity.districtId = i.districtId;;
                    }
                    if (i['payType'] === 'Product') {
                        const j = {
                            feeReimId: i.feeReimId,
                            feeReimCode: i.feeReimCode,
                            feeReim: i.feeReim,
                            feePayType: i.costPaymentWay,
                            feePayCode: i.costPaymentWayCode,
                            availableBalance: i.availableBalance,
                            costId: i.id,
                            costCode: i.executionDeatilCode,
                            costTypeCode: i.costTypeCode,
                            dataList: [],
                            costTypeName:i.costTypeName,
                            payType: 'Product'
                        };
                        if (this.$utils.isEmpty(this.prodAndCostList.Product)) {
                            this.prodAndCostList.Product.push(j);
                        } else {
                            const feePayCodeExist = this.prodAndCostList.Product.filter((item) => item.costId === i.id);
                            if (this.$utils.isEmpty(feePayCodeExist)) {
                                this.prodAndCostList.Product.push(j);
                            }
                        }
                    }
                });
                list.forEach(i => {
                    if (!this.$utils.isEmpty(i.feeReimId)) {
                        this.needUpdateActivity.executivesId = i.feeReimId;//executivesId 费用垫付对象
                        this.needUpdateActivity.executivesName = i.feeReim;//费用垫付对象名称-用于活动新建界面展示-不存
                        this.needUpdateActivity.availableBalance = i.availableBalance;//可用余额-用于费用申请即将恶魔展示-不存
                        this.needUpdateActivity.province = i.province;
                        this.needUpdateActivity.provinceId = i.provinceId;
                        this.needUpdateActivity.city = i.city;
                        this.needUpdateActivity.cityId = i.cityId;
                        this.needUpdateActivity.district = i.district;
                        this.needUpdateActivity.districtId = i.districtId;
                    }
                    if (i['payType'] === 'Money') {
                        const j = {
                            feeReimId: i.feeReimId,
                            feeReimCode: i.feeReimCode,
                            feeReim: i.feeReim,
                            feePayType: i.costPaymentWay,
                            feePayCode: i.costPaymentWayCode,
                            availableBalance: i.availableBalance,
                            costId: i.id,
                            costCode: i.executionDeatilCode,
                            costTypeCode: i.costTypeCode,
                            dataList: [],
                            costTypeName:i.costTypeName,
                            payType: 'Money'
                        };
                        if (this.$utils.isEmpty(this.prodAndCostList.Money)) {
                            this.prodAndCostList.Money.push(j);
                        } else {
                            const feePayCodeExist = this.prodAndCostList.Money.filter((item) => item.costId === i.id);
                            if (this.$utils.isEmpty(feePayCodeExist)) {
                                this.prodAndCostList.Money.push(j);
                            }
                        }

                    }
                });
                if (!this.$utils.isEmpty(this.prodAndCostList.Money)) {
                    this.$set(this, 'cashShow', true);
                }
                if (!this.$utils.isEmpty(this.prodAndCostList.Product)) {
                    this.$set(this, 'prodShow', true);
                }
                if (this.pageForm === 'case') {
                    //1、执行案详情新建,如果用户选择的执行案明细上 费用小类不一致 那么 抛错提示‘请选择相同费用小类的明细创建活动！
                    if (!this.$utils.isEmpty(this.needUpdateActivity.executivesId)) {
                        this.activityItem.executivesId = this.needUpdateActivity.executivesId
                    }
                    if (!this.$utils.isEmpty(this.needUpdateActivity.executivesName)) {
                        this.activityItem.executivesName = this.needUpdateActivity.executivesName;
                    }
                    this.activityItem.province = this.needUpdateActivity.province;
                    this.activityItem.provinceId = this.needUpdateActivity.provinceId;
                    this.activityItem.city = this.needUpdateActivity.city;
                    this.activityItem.cityId = this.needUpdateActivity.cityId;
                    this.activityItem.district = this.needUpdateActivity.district;
                    this.activityItem.districtId = this.needUpdateActivity.districtId;
                    const benchmarkData = list[0];//判别选择的执行案明细的费用小类是否一致
                    this.activityItem.costLargeType = benchmarkData.costLargeType;
                    this.activityItem.costLargeTypeCode = benchmarkData.costLargeTypeCode;
                    this.activityItem.costMiddleType = benchmarkData.costMiddleType;
                    this.activityItem.costMiddleTypeCode = benchmarkData.costMiddleTypeCode;
                    this.activityItem.costType = benchmarkData.costTypeName;
                    this.activityItem.costTypeCode = benchmarkData.costTypeCode;
                    //前端增加控制并打印日志查询问题
                    if (this.$utils.isEmpty(this.activityItem.provinceId)){
                        this.$aegis.report({
                            msg: '出现了,从执行案入口进入创建活动,省市区id为空的情况',
                            ext1: JSON.stringify(list),
                            trace: 'log'
                        });
                        const addressData = await  this.$http.post('action/link/alladdress/queryIdByName', {
                            stateName: this.activityItem.province,
                            cityName: this.activityItem.city,
                            countyName: this.activityItem.district
                        });
                        if(addressData.success){
                            this.$set(this.activityItem, 'provinceId', addressData.stateId);
                            this.$set(this.activityItem, 'cityId', addressData.cityId);
                            this.$set(this.activityItem, 'districtId', addressData.countyId);
                        }
                    }
                }
            },
            /**
             * 更新产品数据
             * <AUTHOR>
             * @date 2020-08-26
             * */
            async updateProdData(feePayType, costId, editDataList) {
                this.prodAndCostList.Product.forEach(i => {
                    if (i.feePayType === feePayType && i.costId === costId) {
                        let tempList = [];
                        tempList = this.$utils.deepcopy(i.dataList);
                        for (let i = 0; i < editDataList.length; i++) {
                            const item = editDataList[i];
                            const cashExit = tempList.filter((item1) => item1.prodId === item.prodId)[0];
                            if (this.$utils.isEmpty(cashExit)) {
                                tempList.push(item);
                            } else {
                                //分情况增加产品数量
                                if (this.scene === 'apply') {
                                    cashExit.qty++
                                }
                                if (this.scene === 'actual') {
                                    cashExit.actualQty++
                                }
                            }
                        }
                        i.dataList = [...tempList];
                    }
                });
            },
            /**
             * 更新现金数据
             * <AUTHOR>
             * @date 2020-08-26
             * */
            updateCashData(feePayType, costId, editDataList) {
                this.prodAndCostList.Money.forEach(i => {
                    if (i.feePayType === feePayType && i.costId === costId) {
                        let tempList = [];
                        tempList = this.$utils.deepcopy(i.dataList);
                        for (let i = 0; i < editDataList.length; i++) {
                            const item = editDataList[i];
                            const cashExit = tempList.filter((item1) => item1.prodName === item.prodName);
                            if (this.$utils.isEmpty(cashExit)) {
                                tempList.push(item);
                            }
                        }
                        i.dataList = [...tempList];
                    }
                });
            },
            /**
             * 上一步：新建活动时
             * <AUTHOR>
             * @date 2020-08-05
             * */
            lastStep() {
                if (this.pageForm !== 'case') {
                    this.$bus.$emit("marketActivity");
                }
                this.$nav.back();
            },
            /**
             * 下一步：新建活动时
             * <AUTHOR>
             * @date 2020-08-05
             * scenarioType:next/back next标志正常流程往下走 back点左上角返回需要保存信息后返回
             * */
            async nextStep() {
                try {
                    this.$utils.showLoading();
                    this.insertCostList = [];
                    let baseList = [];//大类的列表
                    if (this.$utils.isEmpty(this.cashPayList)) {
                        baseList = this.$utils.deepcopy(this.prodPayList);
                    } else {
                        baseList = this.cashPayList.concat(this.prodPayList);
                    }
                    if (this.$utils.isEmpty(baseList)) {
                        this.$utils.hideLoading();
                        this.$message.warn("请维护费用信息！",{customFlag:true});
                        return false;
                    }
                    //校验兑付方式下需要维护明细
                    const emptyDataList = baseList.filter((item) => this.$utils.isEmpty(item.dataList));
                    let placeholderDataRequired = "";
                    if (!this.$utils.isEmpty(emptyDataList)) {
                        for (let i = 0; i < emptyDataList.length; i++) {
                            placeholderDataRequired = placeholderDataRequired + emptyDataList[i].feePayType + ',';
                        }
                        placeholderDataRequired = placeholderDataRequired + '兑付方式下没有物资明细，请维护数据或删除该兑付方式!'
                    }
                    if (!this.$utils.isEmpty(placeholderDataRequired)) {
                        this.$utils.hideLoading();
                        this.$message.warn(placeholderDataRequired,{customFlag:true});
                        return false;
                    }
                    //2021-09-29 为了后台计算现金类不报错，现金类申请数量和实际数量默认给1
                    baseList.forEach((item) => {
                        item.dataList.forEach((item2) => {
                            item2.payType = item.payType
                        })
                    });
                    //1-判断费用小类是否一致：限制选择的费用信息需和市场活动上的费用小类
                    let costTypePlaceholder = "";
                    if (this.pageForm !== 'case') {
                        const costCheckData = baseList.filter(item => !this.$utils.isEmpty(item.costTypeCode) && item.costTypeCode !== this.activityItem.costTypeCode);
                        if (!this.$utils.isEmpty(costCheckData)) {
                            for (let i = 0; i < costCheckData.length; i++) {
                                costTypePlaceholder = costTypePlaceholder + costCheckData[i].feePayType + ',';
                            }
                            this.$utils.hideLoading();
                            this.$showError(`${costTypePlaceholder}兑付方式关联的执行案明细的费用小类与市场活动的费用小类【${this.activityItem.costType}】不一致，请确认后重新选择。`,{customFlag:true});
                            return;
                        }
                    }
                    //2-执行案新建时和市场活动新建时都需要判断费用小类是否一致：判断选择的费用明细的费用小类是否一致
                    const benchmarkData = baseList[0];
                    const benchmarkData2 = baseList.filter(item => item.costTypeCode !== benchmarkData.costTypeCode);
                    if (!this.$utils.isEmpty(benchmarkData2)) {
                        this.$utils.hideLoading();
                        this.$showError("请选择相同费用小类的明细数据",{customFlag:true});
                        return;
                    }
                    //2021-05-19查询费用小类上设置的活动类型是否必输。查询可选的活动类型数据。
                    await this.queryActTypeRequiredByCostTypeCode();
                    await this.queryActivityType();
                    //校验兑付方式下的申请总额与可用余额的大小。费用实际后台校验
                    let placeholderCalculate = "";
                    if (this.scene === 'apply' && !this.$utils.isEmpty(this.exeCaseId)) {
                        for (let i = 0; i < baseList.length; i++) {
                            //可用余额
                            const availableBalance = baseList[i].availableBalance;
                            //申请总额
                            let sum = 0;
                            if (!this.$utils.isEmpty(baseList[i].costId)) {
                                baseList[i].dataList.forEach(item => {
                                    if (!this.$utils.isEmpty(item.qty)) {
                                        sum += this.$utils.numberMul(item.actualTranPrice, item.qty);
                                    } else {
                                        sum += Number(item.actualTranPrice)
                                    }
                                });
                                if (Number(sum) > Number(availableBalance)) {
                                    placeholderCalculate = placeholderCalculate + baseList[i].feePayType + ',';
                                }
                            }
                        }
                        if (!this.$utils.isEmpty(placeholderCalculate)) {
                            placeholderCalculate = placeholderCalculate + '兑付方式下申请总额不能超过可用余额，请检查修改!'
                        }
                    }
                    if (!this.$utils.isEmpty(placeholderCalculate)) {
                        this.$utils.hideLoading();
                        this.$message.warn(placeholderCalculate,{customFlag:true});
                        return false;
                    }
                    baseList.forEach(item => {
                        this.insertCostList = this.insertCostList.concat(item.dataList);
                    });
                    this.insertCostList = this.insertCostList.filter((item) => this.$utils.isEmpty(item.id));
                    //现金类数量默认为1
                    this.insertCostList.forEach((item) => {
                        if(item.payType === 'Money'){
                            item.qty = 1; //现金类数量默认为1
                            item.actualQty = 1 //现金类实际数量默认为1
                        }
                    });
                    if (this.$utils.isEmpty(this.activityTypeList)) {
                        this.$utils.hideLoading();
                        this.createNewActivity();
                    } else {
                        this.$utils.hideLoading();
                        this.$refs.dialog.show();
                    }
                } catch (e) {
                    this.$utils.hideLoading();
                } finally {
                    //this.$utils.hideLoading();
                }
            },
            async getExcludeBusSceneData(){
                const data = await this.$http.post('action/link/basic/queryByExamplePage', {
                    filtersRaw: [
                        {"id": "type", "property": "type", "value": 'TMPL_SUB_BIZ_TYPE'},
                        {"id": "activeFlag", "property": "activeFlag", "value": 'Y'},
                        {"id": "useFlag", "property": "useFlag", "value": 'N'},
                        {"id": "parentVal", "property": "parentVal", "value": 'businessScenario'},
                    ]
                })
                let baseArray = [];
                if(data.rows){
                    data.rows.forEach((item) => {
                        baseArray.push(item.val)
                    })
                }
                return baseArray;
            },
            /**
             * 新建活动
             * */
            async createNewActivity(){
                const excludeBusSceneData = await this.getExcludeBusSceneData();
                //存在来回上一步下一步的操作所以这个位置new 新的活动ID
                const id = await this.$newId();//即时生成的活动ID
                this.activityItem.id = id;
                this.insertCostList.forEach((item) => {
                    item.actId = this.activityItem.id;
                });
                if(this.actTypeRequired === 'Y'){
                    if (this.$utils.isEmpty(this.activityItem.actIndeSourCode)) {
                        if(this.$utils.isEmpty(this.activityTypeList)){
                            this.$message.warn('当前费用类型要求活动类型必输，请联系管理员维护活动类型',{customFlag:true});
                        } else {
                            this.$message.warn('当前费用类型要求活动类型必输，请选择活动类型',{customFlag:true});
                        }
                        return false;
                    }
                } else {
                    if(this.$utils.isEmpty(this.activityItem.actIndeSourCode)){
                        this.activityItem.actIndeSourCode = '';
                    }
                }
                //如果是执行案-点击新建活动时-维护完费用信息
                this.$nav.push('/pages/lj-market-activity/perform-case/new-activity-page', {
                    data: this.activityItem,
                    insertCostList: this.insertCostList,
                    pageCacheData: this.nextPageCacheData,
                    excludeBusSceneData:excludeBusSceneData,//业务场景需要排除的值列表值
                })
                this.$refs.dialog.hide();
            },
            /**
             * 不选择类型直接新建
             * */
            async newDirectActivity(){
                const excludeBusSceneData = await this.getExcludeBusSceneData();
                //存在来回上一步下一步的操作所以这个位置new 新的活动ID
                const id = await this.$newId();//即时生成的活动ID
                this.activityItem.id = id;
                this.insertCostList.forEach((item) => {
                    item.actId = this.activityItem.id;
                });
                if(this.actTypeRequired === 'Y'){
                    if (this.$utils.isEmpty(this.activityItem.actIndeSourCode)) {
                        if(this.$utils.isEmpty(this.activityTypeList)){
                            this.$message.warn('当前费用类型要求活动类型必输，请联系管理员维护活动类型',{customFlag:true});
                        } else {
                            this.$message.warn('当前费用类型要求活动类型必输，请选择活动类型',{customFlag:true});
                        }
                        return false;
                    }
                } else {
                    if(this.$utils.isEmpty(this.activityItem.actIndeSourCode)){
                        this.activityItem.actIndeSourCode = '';
                    }
                }
                //如果是执行案-点击新建活动时-维护完费用信息
                this.$nav.push('/pages/lj-market-activity/perform-case/new-activity-page', {
                    data: this.activityItem,
                    insertCostList: this.insertCostList,
                    pageCacheData: this.nextPageCacheData,
                    excludeBusSceneData:excludeBusSceneData,//业务场景需要排除的值列表值
                })
                this.$refs.dialog.hide();
            },
            /**
             * 根据费用小类编码查询活动类型是否必输
             * */
            async queryActTypeRequiredByCostTypeCode(){
                try{
                    11
                const data = await this.$http.post('action/link/feeType/queryByExamplePage', {
                    pageFlag: false,
                    filtersRaw: [
                        //是否有效
                        {
                            id: 'effectiveFlag',
                            property: 'effectiveFlag',
                            value: 'Y',
                            operator: '='
                        },
                        {
                            id: 'costTypeLevel',
                            property: 'costTypeLevel',
                            value: '3',
                            operator: '='
                        },
                        {
                            id: 'costTypeCode',
                            property: 'costTypeCode',
                            value: this.activityItem.costTypeCode,
                            operator: '='
                        }
                    ],
                });
                if(this.$utils.isNotEmpty(data.rows)){
                    this.actTypeRequired = data.rows[0].actTypeRequired;
                } else {
                    this.actTypeRequired = '';
                }
                }catch (e) {
                    this.$message.warn('活动类型是否必输查询异常，请联系管理员',e,{customFlag:true});
                }
            },
            /**
             * 根据费用小类编码查询活动类型可选数据
             * */
            async queryActivityType(){
                this.activityTypeList = [];
                let params = {};
                if (!this.$utils.isEmpty(this.userInfo.coreOrganizationTile.l3Id)) {
                    params = {
                        sort: "created",
                        order: 'desc',
                        pageFlag: true,
                        attr5:'orgType',
                        companyId : this.userInfo.coreOrganizationTile.l3Id,
                        filtersRaw: [{
                            id: 'costTypeCode',
                            property: 'costTypeCode',
                            value: this.activityItem.costTypeCode,
                            operator: '='
                        }],
                    }
                } else {
                    params = {
                        sort: "created",
                        order: 'desc',
                        pageFlag: true,
                        attr5:'orgType',
                        filtersRaw: [{
                            id: 'costTypeCode',
                            property: 'costTypeCode',
                            value: this.activityItem.costTypeCode,
                            operator: '='
                        }],
                    }
                }
                const data = await this.$http.post('action/link/actCostTypeMap/queryByExamplePage', params);
                if(data.rows){
                    this.activityTypeList = data.rows;
                    //如果当前费用小类下只有一个活动类型那么做默认否则不做
                    if(this.activityTypeList.length === 1){
                        this.activityItem.actIndeSourCode = this.activityTypeList[0].actType;
                        this.activityTypeList[0]._checked = true;
                    } else if(this.activityTypeList.length > 1){
                        if(!this.$utils.isEmpty(this.activityItem.actIndeSourCode)){
                            this.activityTypeList.forEach((item) => {
                                if(item.actType === this.activityItem.actIndeSourCode){
                                    item._checked = true;
                                }
                            })
                        }
                    }
                }
            },
            /*
            * 删除申请费用的兑付方式-现金
            * @auther songyanrong
            * @date 2020-10-16
            * */
            async deleteCaseFeePayFun(feePayCode, costId) {
                const caseInfoList = this.cashPayList.filter((item) => item.feePayCode === feePayCode && item.costId === costId);
                //是否有已经保存到数据库的费用信息
                let exists = [];
                if (!this.$utils.isEmpty(caseInfoList)) {
                    if (!this.$utils.isEmpty(caseInfoList[0].dataList)) {
                        exists = caseInfoList[0].dataList.filter((item) => !this.$utils.isEmpty(item.id));
                    }
                    if (!this.$utils.isEmpty(caseInfoList[0].dataList) && !this.$utils.isEmpty(exists)) {
                        const del = {
                            actId: this.activityItem.id,
                            feePayCode: feePayCode,
                            costId: costId
                        };
                        await this.$http.post('action/link/actMaterial/deleteByFeePayCode', del);
                    }
                }
                if (!this.$utils.isEmpty(costId)) {
                    this.prodAndCostList.Money = this.prodAndCostList.Money.filter((item) => item.costId !== costId);
                } else {
                    this.prodAndCostList.Money = this.prodAndCostList.Money.filter((item) => item.feePayCode !== feePayCode);
                }
                this.$bus.$emit("MoneyRrefresh", this.prodAndCostList.Money);
            },
            /*
            * 删除申请费用的兑付方式-产品
            * @auther songyanrong
            * @date 2020-10-16
            * */
            async deleteProdFeePayFun(feePayCode, costId) {
                const prodInfoList = this.prodPayList.filter((item) => item.feePayCode === feePayCode && item.costId === costId);
                //是否有已经保存到数据库的产品信息
                let exists = [];
                if (!this.$utils.isEmpty(prodInfoList)) {
                    if (!this.$utils.isEmpty(prodInfoList[0].dataList)) {
                        exists = prodInfoList[0].dataList.filter((item) => !this.$utils.isEmpty(item.id));
                    }
                    if (!this.$utils.isEmpty(prodInfoList[0].dataList) && !this.$utils.isEmpty(exists)) {
                        const del = {
                            actId: this.activityItem.id,
                            feePayCode: feePayCode,
                            costId: costId,
                        };
                        await this.$http.post('action/link/actMaterial/deleteByFeePayCode', del);
                    }
                }
                if (!this.$utils.isEmpty(costId)) {
                    this.prodAndCostList.Product = this.prodAndCostList.Product.filter((item) => item.costId !== costId);
                } else {
                    this.prodAndCostList.Product = this.prodAndCostList.Product.filter((item) => item.feePayCode !== feePayCode);
                }
                this.$bus.$emit("ProductRrefresh", this.prodAndCostList.Product);
            },
            selectActivityType(item){
            },
            selectCostActivityType(input){
                //响应式字段_checked
                const _that = this;
                this.$set(input, '_checked', true);
                this.activityItem.actIndeSourCode = input.actType;
                this.activityTypeList.filter(function (val) {
                    if (val.actType !== input.actType) {
                        _that.$set(val, '_checked', false);
                    }
                });
                this.$refs.activityPickDialog.hide();
            },
            async showPickActivityDialog(){
                await this.queryActivityType();
                this.$refs.activityPickDialog.show();
            },
            cancel(){
                this.activityItem.actIndeSourCode = '';
                this.$refs.dialog.hide();
            }
        }
    }
</script>

<style lang="scss">
    .perform-case-cost-list-new-page {
        .list-item {
            display: flex;
            border-bottom: 1px solid #F2F2F2;
            height: 92px;
            line-height: 92px;

            .left-content {
                display: inline-block;
                width: 80%;

                .row-1 {
                    width: 100%;
                    @include flex-start-center;
                    @include space-between;
                }
            }

            .right-content {
                padding-left: 20px;
                display: inline-block;
                width: 19%;
                line-height: 92px;
                text-align: right;
            }
        }
    }
</style>
