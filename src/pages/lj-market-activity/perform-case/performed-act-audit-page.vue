<template>
    <link-page class="performed-act-audit-page">
        <link-auto-list :option="autoList" hideCreateButton :searchInputBinding="{props:{placeholder:'活动编码/活动名称'}}">
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="activity-list-item" @tap="gotoItem(data)">
                    <view slot="note">
                        <view class="media-list">
                            <view class="media-top">
                                <view class="num-view" style="background-color: #FFFFFF">
                                    <view class="num" style="color: #333333">{{data.activityNum}}</view>
                                </view>
                                <view class="abnormal-view" v-if="!data['isIllegalAct']">
                                    <view class="abnormal">不正常</view>
                                </view>
                            </view>
                        </view>
                        <view class="content-middle">
                            <view class="name">{{data.activityName}}</view>
                        </view>
                        <view class="content-middle-line">
                            <view class="data" style="width: 100%">
                                <view class="title">实际活动时间</view>
                                <view class="val" style="text-align: right;">{{data.actRealTime|date('YYYY-MM-DD')}}---{{data.actRealEndTime|date('YYYY-MM-DD')}}</view>
                            </view>
                        </view>
                        <view class="content-middle-line">
                            <view class="data" style="width: 100%">
                                <view class="title">是否实地检核</view>
                                <view class="val" style="text-align: right;">{{data.isFieldAudit|lov('IS_FLAG')}}</view>
                            </view>
                        </view>
                        <view class="content-middle-line" style="display: flex;">
                            <view class="sum-2" style="flex: 1">
                                <view class="title" style="margin-right: 10px;">申请金额 </view>
                                <view class="val" style="color: #2F69F8"> {{data.activityAmount}}</view>
                            </view>
                            <view class="sum-2"  style="flex: 1">
                                <view class="title" style="margin-right: 10px;">稽核金额 </view>
                                <view class="val"  style="color: #2F69F8" > {{data.auditAmount}}</view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
import StatusButton from "../../lzlj/components/status-button";
export default {
    name: "performed-act-audit-page",
    components: {StatusButton},
    data() {
        const exeCaseId = this.pageParam.exeCaseId;
        const autoList = new this.AutoList(this, {
            module: '',
            url: {
                queryByExamplePage: 'action/link/exeFeedback/queryActProgAuditPage'
            },
            searchFields: ['activityNum', 'activityName',],
            param: {
                exeCaseId:exeCaseId
            },
            sortOptions: null,
        });
        return {
            autoList
        };
    },
    methods: {
        async gotoItem(data) {
            this.$nav.push('/pages/lj-market-activity/work-order/activity-approva-audit-page', {
                data: {
                    id:data.actId,
                    startTime: data.actRealTime,
                    endTime: data.actRealEndTime
                },
                link: 'check' //标志界面来源
            })
        }
    }
}
</script>

<style lang="scss">
@import "../../../styles/list-card";
.performed-act-audit-page {
    .iconBar{
        display:flex;
        width: 25%;
    }
    .abnormal-view {
        background: #EA2F2F;
        border-radius: 8px;
        line-height: 40px;

        .abnormal {
            font-size: 28px;
            color: #FFFFFF;
            letter-spacing: 0;
            line-height: 40px;
            padding: 2px 8px;
        }
    }
    background-color: #F2F2F2;
    font-family: PingFangSC-Regular;

    .activity-list-item {
        background: #FFFFFF;
        width: 95%;
        margin: 24px auto auto auto;
        border-radius: 16px;

        .media-list {
            @include media-list;

            .media-top {
                width: 100%;
                @include flex-start-center;
                @include space-between;
                height: 80px;
                line-height: 80px;

                .left-content {
                    font-family: PingFangSC-Semibold;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 32px;
                    padding-top: 20px;

                }

                .right-content {
                    font-family: PingFangSC-Semibold;
                    font-size: 32px;
                    color: #FF5A5A;
                    letter-spacing: 0;
                    text-align: right;
                    line-height: 32px;
                    padding-top: 20px;
                }

                .num-view {
                    background: #A6B4C7;
                    border-radius: 8px;
                    line-height: 50px;

                    .num {
                        font-size: 28px;
                        color: #FFFFFF;
                        letter-spacing: 0;
                        line-height: 40px;
                        padding: 2px 8px;
                    }
                }

                .status-view {
                    width: 120px;
                    transform: skewX(-10deg);
                    border-radius: 4px;
                    background: #2F69F8;
                    box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                    height: 36px;

                    .status {
                        font-size: 20px;
                        color: #FFFFFF;
                        letter-spacing: 2px;
                        text-align: center;
                        line-height: 36px;
                    }
                }
            }
        }

        .content-middle {
            width: 100%;
            @include flex-start-center;
            @include space-between;
            height: 80px;
            line-height: 80px;

            .content {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
            }

            .name {
                font-family: PingFangSC-Semibold;
                font-size: 32px;
                color: #262626;
                letter-spacing: 0;
                line-height: 32px;
            }
        }

        .content-middle-line {
            width: 100%;

            .data {
                width: 60%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    width: 32%;
                    float: left;

                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                }

                .Submitted, .Feedback{
                    color: #2F69F8;
                }

                .Approve, .FeedbackApro{
                    color: #2EB3C2;
                }

                .Refused, .Refeedback{
                    color: #FF5A5A;
                }
            }

            .sum {
                width: 40%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    float: left;
                    margin-right: 5px;
                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                }
            }

            .sum-2 {
                width: 100%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    float: left;
                    margin-right: 5px;
                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                }
            }
        }
    }
}
</style>
