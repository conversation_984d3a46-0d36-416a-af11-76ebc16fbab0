<template>
    <link-page class="new-activity-cost-page">
        <view>
            <view style="width: 100%;height: 12px"></view>
            <view class="cost-list">
                <view class="item-data"
                      v-for="(costItem,index) in costList" :key="index" @tap="editCostItem">
                    <view class="left-view">
                        <view class="media-list">
                            <view class="media-top">
                                <view class="left-content">{{costItem.feePayType}}</view>
                            </view>
                        </view>
                        <view class="content-middle">
                            <view class="content"> 有效期{{costItem.startTime|date('YYYY-MM-DD')}}至{{costItem.endTime|date('YYYY-MM-DD')}}</view>
                        </view>
                        <view class="content-middle-line">
                            <view class="data">可用余额 {{costItem.availableBalance | cny}}</view>
                            <view class="sum">申请金额(¥)<input type="number" class="sum-input" v-model="costItem.applyAmount"
                                                            @tap.stop="stop($event)"/></view>
                        </view>
                    </view>
                    <view class="right-view">
                        <link-icon icon="mp-arrow-right" style="color: #BFBFBF;font-size: 14px"/>
                    </view>
                </view>
            </view>
        </view>
        <link-sticky>
            <link-button block mode="stroke" @tap="lastStep">上一步</link-button>
            <link-button block @tap="nextStep">下一步</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    export default {
        name: "new-activity-cost-page",
        data() {
            const progId = this.pageParam.progId;
            const costList = [];
            return {
                progId,
                costList,
            }
        },
        onLoad() {
            // 查询费用
            this.initCustList();
        },
        methods: {
            async initCustList() {
                const data = await this.$http.post('action/link/costDetail/queryByExamplePage', {
                    filtersRaw: [
                        {id: 'actProgId', property: 'actProgId', value: this.progId, operator: '='},
                    ],
                    oauth: 'MY_ORG',
                });
                this.costList = data.rows;
            },
            /**
             * 上一步
             * <AUTHOR>
             * @date 2020-08-04
             * */
            lastStep() {
                this.$nav.back();
            },
            /**
             * 下一步
             * <AUTHOR>
             * @date 2020-08-04
             * */
            nextStep() {
                this.$nav.push('/pages/lj-market-activity/perform-case/new-activity-page')
            },
            /**
             * 编辑费用信息
             * <AUTHOR>
             * @date 2020-08-04
             * */
            editCostItem() {
                this.$nav.push('/pages/lj-market-activity/perform-case/edit-cost-item-page');
            },
            stop(e) {
                e.stopPropagation();
            }
        }
    }
</script>

<style lang="scss">
    @import "../../../styles/list-card";
    .new-activity-cost-page {
        .cost-list {
            border-radius: 16px;

            .item-header {
                height: 88px;
                width: 100%;
                padding-left: 32px;
                font-size: 28px;
                line-height: 88px;
                color: #262626;
                letter-spacing: 0;
                border-bottom: 2px solid #F2F2F2;
            }

            .item-data {
                width: 93%;
                border-bottom: 2px solid #F2F2F2;
                background: white;
                height: 220px;
                border-radius: 16px;
                margin: auto auto 24px auto;

                .left-view {
                    width: 90%;
                    float: left;

                    .media-list {
                        @include media-list;

                        .media-top {
                            width: 100%;
                            @include flex-start-center;
                            @include space-between;
                            height: 80px;
                            line-height: 80px;
                            padding-left: 32px;

                            .left-content {
                                font-family: PingFangSC-Semibold;
                                font-size: 32px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 32px;
                                padding-top: 20px;

                            }

                            .right-content {
                                font-family: PingFangSC-Semibold;
                                font-size: 32px;
                                color: #FF5A5A;
                                letter-spacing: 0;
                                text-align: right;
                                line-height: 32px;
                                padding-top: 20px;
                            }

                            .num-view {
                                background: #A6B4C7;
                                border-radius: 8px;
                                line-height: 40px;

                                .num {
                                    font-size: 28px;
                                    color: #FFFFFF;
                                    letter-spacing: 0;
                                    line-height: 40px;
                                    padding: 2px 8px;
                                }
                            }

                            .status-view {
                                width: 120px;
                                transform: skewX(-10deg);
                                border-radius: 4px;
                                background: #2F69F8;
                                box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                                height: 36px;

                                .status {
                                    font-size: 20px;
                                    color: #FFFFFF;
                                    letter-spacing: 2px;
                                    text-align: center;
                                    line-height: 36px;
                                }
                            }
                        }
                    }

                    .content-middle {
                        width: 100%;
                        @include flex-start-center;
                        @include space-between;
                        height: 80px;
                        line-height: 80px;
                        padding-left: 32px;

                        .content {
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #000000;
                            letter-spacing: 0;
                            line-height: 28px;
                        }

                        .name {
                            font-family: PingFangSC-Semibold;
                            font-size: 32px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 32px;
                        }

                        .data {
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #8C8C8C;
                            letter-spacing: 0;
                            line-height: 28px;
                        }

                        .sum {
                            font-family: PingFangSC-Semibold;
                            font-size: 32px;
                            color: #FF5A5A;
                            letter-spacing: 0;
                            text-align: right;
                            line-height: 32px;
                        }
                    }

                    .content-middle-line {
                        padding-left: 32px;

                        .data {
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #8C8C8C;
                            letter-spacing: 0;
                            line-height: 28px;
                            width: 45%;
                            float: left;
                        }

                        .sum {
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #8C8C8C;
                            letter-spacing: 0;
                            text-align: right;
                            line-height: 28px;
                            float: right;

                            .sum-input {
                                width: 160px;
                                float: right;
                                text-align: center;
                                line-height: 28px;
                                background: #F2F2F2;
                                border-radius: 8px;
                                height: 28px;
                                margin: -8px 0 0 8px;

                            }
                        }
                    }
                }

                .right-view {
                    width: 10%;
                    float: left;
                    height: 100px;
                    line-height: 100px;
                    text-align: center;
                }
            }
        }
    }
</style>
