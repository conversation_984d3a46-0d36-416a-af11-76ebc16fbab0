<template>
    <link-page class="cost-item-page">
        <approval-history-point :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)"></approval-history-point>
        <view style="width: 100%;height: 670px;">
            <view class="zero-view"></view>
            <view class="cost-item">
                <view v-for="item in details" :key="item.label" class="item">
                    <view class="label">{{item.label}}</view>
                    <view class="text">{{item.text}}</view>
                </view>
            </view>
        </view>
        <link-sticky>
            <approval-operator :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)"></approval-operator>
            <link-button @tap="termination" :status="'error'" block
                         v-if="pageSource === 'case' && (costItem.costStatus === 'Efficient')">终止费用
            </link-button>
        </link-sticky>
        <view class="end-comment-dialog">
            <link-dialog ref="endCommentDialog" disabledHideOnClickMask title="请填写终止说明">
                <view>
                    <link-textarea v-model="endComment" placeholder="请输入终止说明"
                                   :nativeProps="{maxlength:200}"></link-textarea>
                </view>
                <view>
                    <link-button @tap="terminationFun" block>提交</link-button>
                </view>
            </link-dialog>
        </view>
    </link-page>
</template>

<script>
    import Taro from "@tarojs/taro";
    import ApprovalHistoryPoint from "../../lzlj/approval/components/approval-history-point";
    import ApprovalOperator from "../../lzlj/approval/components/approval-operator";
    import {FilterService} from "link-taro-component";

    export default {
        name: "cost-item-page",
        components: {ApprovalOperator, ApprovalHistoryPoint},
        data() {
            const pageSource = this.pageParam.pageSource;
            let approvalId = "";// 审批id
            let costItemId = "";//费用ID
            //const terminationFlag = false;
            const userInfo = {};
            const endComment = "";
            const costItem = {};//费用信息对象
            const sceneObj = {};
            const approval_from = "";
            return {
                sceneObj,
                approval_from,
                costItemId,
                pageSource,//页面来源 case 或者审批
                approvalId,
                endComment,
                userInfo,
                //terminationFlag,
                details: [],
                costItem,
                // 费用价格是否展示标识
                priceShowFlag: false,
            }
        },
        async created() {
            this.userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
            // if (this.userInfo.positionType === 'SalesSupervisor') {
            //     this.terminationFlag = true;//业务主管直接终止
            // }
            // ;
            this.priceShowFlag = await this.$utils.getPriceDesensitize(this.userInfo);
        },
        async onShow() {
            const that = this;
            that.sceneObj = await that.$scene.ready();//that.$store.getters['scene/getScene'];//消息场景对象
            that.approval_from = that.sceneObj.query['approval_from'];
            if (that.pageSource === 'case') {
                that.costItemId = that.pageParam.data.id;//执行案费用对象的数据ID
            } else if (that.pageSource !== 'case') {
                if (that.approval_from !== 'qw') {
                    that.approvalId = that.pageParam.data.id;//审批传过来的审批数据ID
                    that.costItemId = that.pageParam.data.flowObjId;//审批传过来的费用对象ID
                } else if (that.approval_from === 'qw') { //从小程序审批消息而来
                    that.approvalId = that.sceneObj.query['approval_id'];
                    that.costItemId = that.sceneObj.query['flowObjId'];
                }
            }
            await that.queryCostItemById();
        },
        methods: {
            async queryCostItemById() {
                //执行案费用进详情时查询
                const data = await this.$http.post('action/link/costDetail/queryById', {
                    id: this.costItemId
                });
                this.costItem = data.result;
                if(this.priceShowFlag){
                this.details = [
                    {label: '执行案明细编码', text: this.costItem.executionDeatilCode},
                    {label: '承担部门', text: this.costItem.actOrg},
                    {
                        label: '费用类型',
                        text: await this.costItem.costTypeName
                    },
                    {label: '兑付方式', text: this.costItem.costPaymentWay},
                    {label: '费用垫付对象', text: this.costItem.feeReim},
                    {
                        label: '费用状态',
                        text: await this.$lov.getNameByTypeAndVal('FEE_STATUS', this.costItem.costStatus)
                    },
                    {label: '开始时间', text: this.costItem.startTime},
                    {label: '结束时间', text: this.costItem.endTime},
                    {label: '省', text: this.costItem.province},
                    {label: '市', text: this.costItem.city},
                    {label: '区县', text: this.costItem.district},
                    {label: '产品类型', text: this.costItem.prodType},
                    {label: '占用金额', text: FilterService.cny(this.costItem.occupiedAmount)},
                    {label: '申请金额', text: FilterService.cny(this.costItem.applyAmount)},
                    {label: '审批金额', text: FilterService.cny(this.costItem.approvalAmount)},
                    {label: '活动实发金额', text: FilterService.cny(this.costItem.actualAmount)},
                    {label: '可用余额', text: FilterService.cny(this.costItem.availableBalance)},
                    {label: '备注', text: this.costItem.comment},
                ];
                }else{
                    this.details = [
                        {label: '执行案明细编码', text: this.costItem.executionDeatilCode},
                        {label: '承担部门', text: this.costItem.actOrg},
                        {
                            label: '费用类型',
                            text: await this.costItem.costTypeName
                        },
                        {label: '兑付方式', text: this.costItem.costPaymentWay},
                        {label: '费用垫付对象', text: this.costItem.feeReim},
                        {
                            label: '费用状态',
                            text: await this.$lov.getNameByTypeAndVal('FEE_STATUS', this.costItem.costStatus)
                        },
                        {label: '开始时间', text: this.costItem.startTime},
                        {label: '结束时间', text: this.costItem.endTime},
                        {label: '省', text: this.costItem.province},
                        {label: '市', text: this.costItem.city},
                        {label: '区县', text: this.costItem.district},
                        {label: '产品类型', text: this.costItem.prodType},
                        {label: '备注', text: this.costItem.comment},
                    ];
                }
            },
            /**
             * 终止费用
             * <AUTHOR>
             * @date 2020-08-03
             * */
            termination() {
                this.$dialog({
                    title: '提示',
                    content: (h) => {
                        return [
                            <view style="width:100%">{'终止费用会导致此笔费用不能再使用，如需再使用则需要在费用系统中重新提报执行案，确定要终止吗？'}</view>
                        ]
                    },
                    cancelButton: true,
                    onConfirm: async () => {
                        this.$refs.endCommentDialog.show();
                    },
                    onCancel: () => {
                    }
                })
            },
            /**
             * 终止费用
             * <AUTHOR>
             * @date 2020-08-03
             * */
            async terminationFun() {
                let id = "";
                if (!this.$utils.isEmpty(this.costItem.id)) {
                    id = this.costItem.id;
                } else {
                    id = this.costItemId;
                }
                let url = "";
                // if (this.terminationFlag) {
                //     url = "action/link/costDetail/costEnd"
                // } else {
                //     url = "action/link/costDetail/costEndApply";
                // }
                //直接调后台终止方法，后台有查询那些职位不走审批直接提交
                url = "action/link/costDetail/costEndApply";
                this.$refs.endCommentDialog.hide();
                await this.$httpForm.post(url, {
                    id: id,
                    endComment: this.endComment
                });
              this.pageParam.callback();
              this.$nav.back();
            }
        }
    }
</script>

<style lang="scss">
    .cost-item-page {
        .end-comment-dialog .link-dialog.link-dialog-content-flex-center .link-dialog-content > view:not(:first-child) {
            display: block;
            -webkit-align-items: center;
            -ms-flex-align: center;
            align-items: center;
            -webkit-justify-content: center;
            -ms-flex-pack: center;
            justify-content: center;
        }

        background-color: #F2F2F2;

        .zero-view {
            width: 100%;
            height: 30px;
        }

        .cost-item {
            width: 93%;
            margin: auto;
            border-radius: 16px;
            padding: 20px 24px 20px 24px;
            background: white;
            height: auto;

            .item {
                width: 100%;
                display: flex;

                .label {
                    height: 56px;
                    display: inline-block;
                    font-size: 28px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    float: left;
                    width: 35%;
                }

                .text {
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                    text-align: right;
                    width: 65%;
                }
            }
        }

        .btn {
            background: #FF5A5A;
            box-shadow: 0 16px 48px 0 rgba(255, 90, 90, 0.50);
            border-radius: 8px;
            width: 90%;
            color: white;
        }
    }
</style>
