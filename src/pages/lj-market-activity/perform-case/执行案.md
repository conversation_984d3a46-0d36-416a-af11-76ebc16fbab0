# 执行案
------
### 初始文档
```
创建时间：2022/01/10 20:15
创建人：  宋燕荣
```
* 功能涵盖：
```
1、查询展示NC外部系统同步过来的执行案数据以及执行案明细数据
2、展示选择当前执行案的市场活动列表
3、提供快捷新建市场活动入口
4、可以手动终止执行案明细
```

#### 执行案列表
* 数据查询安全性
```
执行案模块:查询的是组织范围内.默认查询全部生效的执行案但是可以切换,查询全部、全部生效、部分生效、待终止
终止状态的数据。
其他需要选择执行案的地方：是查询组织范围内且全部有效+部分有效的执行案。
```
* 模糊查询条件
```
['executionCode', 'executionName', 'creatorName']
执行案编码、执行案名称、制单人
```

#### 执行案详情
* 功能点
```
1、展示执行案信息
2、展示执行案明细信息 可查看明细详情 规则条件内可以终止费用
3、展示市场活动列表信息 可查询明细共用市场活动模块的详情界面 规则条件内可以操作
```
* 终止费用
```
已生效的执行案明细可以操作终止
终止提示：终止费用会导致此笔费用不能再使用，如需再使用则需要在费用系统中重新提报执行案，确定要终止吗
终止时：直接调后台终止方法，后台有查询那些职位不走审批直接提交
    - 接口 action/link/costDetail/costEndApply
    - 参数 {id: 执行案明细ID,endComment: 终止说明}
```
* 快捷新建活动
```
1、先选择执行案下的明细带出省市区县和对付方式类型，进而维护费用信息。
2、将费用信息携带到第二个界面维护活动的基础信息，基础信息保存之后再保存费用对象（此时是申请费用）
    注意：这个场景新建的市场活动执行案编码是固定的不可调整，市场活动新加字段（非配置）需要在这个场景中也加上。
```
------ 市场活动-执行案-内容结束 ------
