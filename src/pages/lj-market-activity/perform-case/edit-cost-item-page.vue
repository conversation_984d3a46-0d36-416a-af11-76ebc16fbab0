<template>
    <link-page class="edit-cost-item-page">
        <view class="zero-view"></view>
        <view class="case-item">
            <view v-for="item in details" :key="item.label" class="item">
                <view class="label">{{item.label}}</view>
                <view class="text">{{item.text}}</view>
            </view>
        </view>
        <!--产品信息 需传是否有边框 title 展示数量字段 实际 actualNumFlag or预计 estimatedNumFlag or 申请applyNumFlag -->
        <production-pin :haveMarginFlag="false" :title="'产品信息'" :applyNumFlag="true"></production-pin>
    </link-page>
</template>

<script>
    import ProductionPin from "../market-activity/components/production-pin";
    export default {
        name: "edit-cost-item-page",
        components: {ProductionPin},
        data() {
            return {
                details: [],
            }
        },
        async created() {
            this.details = [
                {label: 'CRM/NC执行案编码', text: '2100292882828'},
                {label: '有效期', text: '2020-05-18'},
                {label: '有效期', text: '2020-05-18'},
                {label: '报销截止日期', text: '2020-05-18'},
                {label: '费用类型', text: '2020年度'},
                {label: '兑付方式', text: '2020-05-18'},
                {label: '费用垫付对象', text: '2020-05-18'},
                {label: '活动申请金额', text: '2020-05-18'},
                {label: '可用余额', text: '2020年度'},
            ]
        },
        methods: {}
    }
</script>

<style lang="scss">
    .edit-cost-item-page {
        background-color: #F2F2F2;

        .zero-view {
            width: 100%;
            height: 30px;
        }

        .case-item {
            width: 93%;
            margin: auto;
            border-radius: 16px;
            padding: 20rpx 28rpx 20rpx 28rpx;
            background: white;
            height: 550px;

            .item {
                line-height: 56px;
                width: 100%;

                .label {
                    height: 56px;
                    display: inline-block;
                    font-size: 28px;
                    color: #595959;
                    letter-spacing: 0;
                    line-height: 56px;
                    float: left;
                    width: 43%;
                }

                .text {
                    height: 56px;
                    display: inline-block;
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                    text-align: right;
                    line-height: 56px;
                    float: right;
                    width: 57%;
                    padding-right: 16px;

                }
            }
        }
    }
</style>