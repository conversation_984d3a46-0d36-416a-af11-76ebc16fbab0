<template>
    <link-page class="activity-list-page">
        <link-auto-list :option="autoList" hideCreateButton>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="activity-list-item" @tap="gotoItem(data)">
                    <market-activity-item source="perform" :data="data" :priceShowFlag="priceShowFlag" slot="note"/>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
    import MarketActivityItem from '../market-activity/components/market-activity-item';
    import StatusButton from "../../lzlj/components/status-button";
    import Taro from "@tarojs/taro";
    export default {
        name: "activity-list-page",
        components: {StatusButton, MarketActivityItem},
        data() {
            const userInfo = Taro.getStorageSync('token').result;
            const exeCaseId = this.pageParam.exeCaseId;
            const autoList = new this.AutoList(this, {
                module: 'action/link/marketAct',
                searchFields: ['activityNum', 'activityName', 'cashApplyAmount', 'executor'],
                param: {
                    filtersRaw: [
                        {id: 'exeCaseId', property: 'exeCaseId', value: exeCaseId, operator: '='},
                    ]
                },
                sortOptions: null,
            });
            return {
                // 费用价格是否展示标识
                priceShowFlag: false,
                userInfo,
                autoList
            };
        },
        async created() {
            this.priceShowFlag = await this.$utils.getPriceDesensitize(this.userInfo);
        },
        methods: {
            async gotoItem(data) {
                const cacheData = await this.$http.post('action/link/marketAct/queryById', {
                    id: data.id
                });
                this.$dataService.setMarketActivityItem(cacheData.result);
                this.$nav.push('/pages/lj-market-activity/market-activity/market-activity-item-page', {
                    data: data,
                    pageSource: "view" //标志界面来源 区别于"其他信息"界面的预览活动
                })
            }
        }
    }
</script>

<style lang="scss">
    @import "../../../styles/list-card";
    .activity-list-page {
        .iconBar{
            display:flex;
            width: 25%;
        }

        background-color: #F2F2F2;
        font-family: PingFangSC-Regular;

        .activity-list-item {
            background: #FFFFFF;
            width: 95%;
            margin: 24px auto auto auto;
            border-radius: 16px;
        }
    }
</style>
