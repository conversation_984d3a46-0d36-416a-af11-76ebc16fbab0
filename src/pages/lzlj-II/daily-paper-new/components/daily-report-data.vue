<template>
    <view class="daily-report-data">
        <view v-for="(pItem, pIndex) in postnDailyReportList" :key="pIndex">
            <view class="daily-postn">
                {{pItem.postnName}}
            </view>
            <view class="daily-data-type">
                今日达成数据
            </view>
            <!--                    终端板块-->
            <view  v-for="(dItem, dIndex) in dailyTemp">
            <view v-if="pItem.terminalDailyReport">
                <!--拜访内容-->
                <view class="daily-line-box quota-item" v-if="dItem.name=='visitData' && pItem.terminalDailyReport.visitData">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'终端拜访情况：本日完成拜访'+ pItem.terminalDailyReport.visitData.total+'次，拜访终端'+pItem.terminalDailyReport.visitData.acctNum+'家'"></line-title>
                        <view class="right-icon-operate"
                              :style="{color: controlAndRemarksObj[pItem.postnId+'_unfoldVisit'] ? '#2F69F8': '#262626'}"
                              @tap="switchState(pItem.postnId+'_unfoldVisit')">
                            <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_unfoldVisit']"
                                       icon="icon-shouqi"/>
                            <link-icon v-else icon="icon-zhankai"/>
                        </view>
                    </view>
                    <view class="visit-box"
                          v-for="(vItem, vIndex) in pItem.terminalDailyReport.visitData.list" :key="vIndex"
                          v-show="controlAndRemarksObj[pItem.postnId+'_unfoldVisit']">
                        <time-during :start-time="vItem.visitTime"
                                     :end-time="vItem.visitEndTime"></time-during>
                        <line-title :hiddenLeft="false" :title="vItem.accntName"></line-title>
                        <address-tip :address="vItem.fullAddress"></address-tip>
                    </view>
                </view>
                <!--配额申请-->
                <view class="daily-line-box quota-item" v-if="dItem.name=='quotaData' && pItem.terminalDailyReport.quotaData">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="pItem.terminalDailyReport.quotaData.title"></line-title>
                        <view class="right-icon-operate"
                              :style="{color: controlAndRemarksObj[pItem.postnId+'_unfoldQuota'] ? '#2F69F8': '#262626'}"
                              @tap="switchState(pItem.postnId+'_unfoldQuota')">
                            <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_unfoldQuota']"
                                       icon="icon-shouqi"/>
                            <link-icon v-else icon="icon-zhankai"/>
                        </view>
                    </view>
                    <view class="visit-box"
                          v-for="(qItem, qIndex) in pItem.terminalDailyReport.quotaData.list" :key="qIndex"
                          v-show="controlAndRemarksObj[pItem.postnId+'_unfoldQuota']">
                        <line-title :hiddenLeft="false" :title="qItem.joinAccntName"></line-title>
                        <item :title="qItem.materialName" v-if="qItem.type==='Increase'" rightWidth="45px"
                              :content="'+'+qItem.sumNum+'件'" :arrow="false"/>
                        <item :title="qItem.materialName" v-if="qItem.type==='Decrease'" rightWidth="45px"
                              :content="'-'+qItem.sumNum+'件'" :arrow="false"/>
                    </view>
                </view>
                <!--终端下单-->
                <view class="daily-line-box quota-item" v-if="dItem.name=='orderData' && pItem.terminalDailyReport.orderData">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'终端下单：共为'+pItem.terminalDailyReport.orderData.acctNum+'家终端下单'
                                                +pItem.terminalDailyReport.orderData.total+'件产品'"></line-title>
                        <view class="right-icon-operate"
                              :style="{color: controlAndRemarksObj[pItem.postnId+'_unfoldOrder'] ? '#2F69F8': '#262626'}"
                              @tap="switchState(pItem.postnId+'_unfoldOrder')">
                            <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_unfoldOrder']"
                                       icon="icon-shouqi"/>
                            <link-icon v-else icon="icon-zhankai"/>
                        </view>
                    </view>
                    <view class="visit-box"
                          v-for="(sItem, sIndex) in pItem.terminalDailyReport.orderData.list" :key="sIndex"
                          v-show="controlAndRemarksObj[pItem.postnId+'_unfoldOrder']">
                        <line-title :hiddenLeft="false" :title="sItem.acctName"></line-title>
                        <item v-for="(proItem, proIndex) in sItem.prodList" :key="proIndex"
                              :title="proItem.prodName" rightWidth="45px" :content="proItem.prodNum+'件'"
                              :arrow="false"/>
                    </view>
                </view>

                <!--终端新开情况-->
                <view class="daily-line-box quota-item" v-if="dItem.name=='newTerminalData' && pItem.terminalDailyReport.newTerminalData">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'终端新开情况：本日新开'+(pItem.terminalDailyReport.newTerminalData.total || 0)+'家终端'"></line-title>
                        <view class="right-icon-operate"
                              :style="{color: controlAndRemarksObj[pItem.postnId+'_unfoldNewTerminal'] ? '#2F69F8': '#262626'}"
                              @tap="switchState(pItem.postnId+'_unfoldNewTerminal')">
                            <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_unfoldNewTerminal']"
                                       icon="icon-shouqi"/>
                            <link-icon v-else icon="icon-zhankai"/>
                        </view>
                    </view>
                    <view class="visit-box"
                          v-for="(sItem, sIndex) in pItem.terminalDailyReport.newTerminalData.list" :key="sIndex"
                          v-show="controlAndRemarksObj[pItem.postnId+'_unfoldNewTerminal']">
                        <line-title :hiddenLeft="false" :title="sItem.accntName"></line-title>
                        <item :title="'认证通过时间: ' + sItem.authenticatedTime" rightWidth="45px" :arrow="false"/>
                    </view>
                </view>
                <!--终端进货情况-->
                <view class="daily-line-box quota-item" v-if="dItem.name=='terminalPurchaseData' && pItem.terminalDailyReport.terminalPurchaseData">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'终端进货情况：共'+(pItem.terminalDailyReport.terminalPurchaseData.accntNum||0)+'家终端扫码入库'
                                                +(pItem.terminalDailyReport.terminalPurchaseData.total||0)+'件产品'"></line-title>
                        <view class="right-icon-operate"
                              :style="{color: controlAndRemarksObj[pItem.postnId+'_unfoldTerminalPurchase'] ? '#2F69F8': '#262626'}"
                              @tap="switchState(pItem.postnId+'_unfoldTerminalPurchase')">
                            <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_unfoldTerminalPurchase']"
                                       icon="icon-shouqi"/>
                            <link-icon v-else icon="icon-zhankai"/>
                        </view>
                    </view>
                    <view class="visit-box"
                          v-for="(sItem, sIndex) in pItem.terminalDailyReport.terminalPurchaseData.list" :key="sIndex"
                          v-show="controlAndRemarksObj[pItem.postnId+'_unfoldTerminalPurchase']">
                        <line-title :hiddenLeft="false" :title="sItem.accntName"></line-title>
                        <item :title="'进货数量: '+sItem.purchaseQty+'件'" rightWidth="45px" :arrow="false"/>
                    </view>
                </view>
            </view>
            <!--                    活动板块-->
            <view v-if="pItem.actDailyReport">
                <!--<view class="daily-data-type">-->
                <!--    市场活动-->
                <!--</view>-->
                <!--  活动执行情况-->
                <view class="daily-line-box quota-item"
                      v-if="dItem.name=='marketActHappenedTodayData' && pItem.actDailyReport.marketActHappenedTodayData">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'活动执行情况：今日新申请'+(pItem.actDailyReport.marketActHappenedTodayData.applyNum || 0)+'场活动,实发'+(pItem.actDailyReport.marketActActualSalaryData.actualNum || 0)+'场活动'"></line-title>
                        <view class="right-icon-operate"
                              :style="{color: controlAndRemarksObj[pItem.postnId+'_unfoldHappenedToday'] ? '#2F69F8': '#262626'}"
                              @tap="switchState(pItem.postnId+'_unfoldHappenedToday')">
                            <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_unfoldHappenedToday']"
                                       icon="icon-shouqi"/>
                            <link-icon v-else
                                       icon="icon-zhankai"/>
                        </view>
                    </view>

                    <view class="visit-box"
                          v-show="controlAndRemarksObj[pItem.postnId+'_unfoldHappenedToday']"
                          v-for="(ahItem, ahIndex) in pItem.actDailyReport.marketActHappenedTodayData.actList"
                          :key="ahIndex">
                        <view class="market-activity-list-item">
                            <view class="media-list">
                                <view class="media-top">
                                    <view class="num-view">
                                        <view class="num">{{ahItem.actNum}}</view>
                                    </view>
                                </view>
                            </view>
                            <view class="content-middle-line">
                                <view style="width: 100%">
                                    <view class="data" style="width: 75%;float: left">
                                        <view class="name">{{ahItem.costType}}·{{ahItem.actType |
                                            lov('MC_TYPE')}}
                                        </view>
                                        <view class="title" :class="ahItem.status">{{ahItem.status |
                                            lov('MC_STATUS')}}·{{ahItem.approveStatus | lov('APRO_STATUS')}}
                                        </view>
                                        <view class="val" v-if="!ahItem.realAmount">审批金额:{{ahItem.poAmount |
                                            cny}}
                                        </view>
                                        <view class="val" v-else>实发金额:{{ahItem.realAmount | cny}}</view>
                                    </view>
                                    <view style="width: 25%;float: right">
                                        <view
                                            :style="{'background-image': 'url(' + $imageAssets.dailyStamp + ')'}"
                                            style="width: 60px;height: 60px;background-repeat: no-repeat;background-size: 100% 100%;
                border-radius: 10px 10px 0 0;background-color: white;text-align: center">
                                            <view
                                                style="font-size: 10px;line-height: 62px;transform: rotate(-15deg);color: #FF5A5A">
                                                今日新增
                                            </view>
                                        </view>
                                    </view>
                                </view>
                                <view class="sum">
                                    <view class="title">{{ahItem.startTime |date('YYYY-MM-DD HH:mm')}}&nbsp;~&nbsp;{{ahItem.endTime
                                        |date('YYYY-MM-DD HH:mm')}}
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>

                <!-- 活动金额情况 -->
                <view class="daily-line-box quota-item"
                      v-if="dItem.name=='marketActActualSalaryData' && pItem.actDailyReport.marketActActualSalaryData">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'活动金额情况：今日活动审批'+(pItem.actDailyReport.marketActHappenedTodayData.approveAmount || 0)+'元,实发'+(pItem.actDailyReport.marketActActualSalaryData.realAmount || 0)+'元'"></line-title>
                        <view class="right-icon-operate"
                              :style="{color: controlAndRemarksObj[pItem.postnId+'_unfoldActualSalary'] ? '#2F69F8': '#262626'}"
                              @tap="switchState(pItem.postnId+'_unfoldActualSalary')">
                            <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_unfoldActualSalary']"
                                       icon="icon-shouqi"/>
                            <link-icon v-else
                                       icon="icon-zhankai"/>
                        </view>
                    </view>

                    <view class="visit-box"
                          v-show="controlAndRemarksObj[pItem.postnId+'_unfoldActualSalary']"
                          v-for="(asItem, asIndex) in pItem.actDailyReport.marketActActualSalaryData.actList"
                          :key="asIndex">
                        <view class="market-activity-list-item">
                            <view class="media-list">
                                <view class="media-top">
                                    <view class="num-view">
                                        <view class="num">{{asItem.actNum}}</view>
                                    </view>
                                </view>
                            </view>
                            <view class="content-middle-line">
                                <view style="width: 100%">
                                    <view class="data" style="width: 75%;float: left">
                                        <view class="name">{{asItem.costType}}·{{asItem.actType |
                                            lov('MC_TYPE')}}
                                        </view>
                                        <view class="title">{{asItem.status |
                                            lov('MC_STATUS')}}·{{asItem.approveStatus | lov('APRO_STATUS')}}
                                        </view>
                                        <view class="val">实发金额:{{asItem.realAmount | cny}}</view>
                                    </view>
                                    <view style="width: 25%;float: right">
                                        <view
                                            :style="{'background-image': 'url(' + $imageAssets.dailyStamp + ')'}"
                                            style="width: 60px;height: 60px;background-repeat: no-repeat;background-size: 100% 100%;
                border-radius: 10px 10px 0 0;background-color: white;text-align: center">
                                            <view
                                                style="font-size: 10px;line-height: 62px;transform: rotate(-15deg);color: #FF5A5A">
                                                今日新增
                                            </view>
                                        </view>
                                    </view>
                                </view>
                                <view class="sum">
                                    <view class="title">{{asItem.startTime |date('YYYY-MM-DD HH:mm')}} ~ {{asItem.endTime |date('YYYY-MM-DD HH:mm')}}
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>

                <!--                            待执行-->
                <view class="daily-line-box quota-item"
                      v-if="dItem.name=='marketActToPerformData' && pItem.actDailyReport.marketActToPerformData">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'待执行：共'+pItem.actDailyReport.marketActToPerformData.toPerformActNum+'场,申请金额' +pItem.actDailyReport.marketActToPerformData.applyAmount"></line-title>
                        <view class="right-icon-operate"
                              :style="{color: controlAndRemarksObj[pItem.postnId+'_unfoldToPerform'] ? '#2F69F8': '#262626'}"
                              @tap="switchState(pItem.postnId+'_unfoldToPerform')">
                            <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_unfoldToPerform']"
                                       icon="icon-shouqi"/>
                            <link-icon v-else
                                       icon="icon-zhankai"/>
                        </view>
                    </view>

                    <view class="visit-box" v-show="controlAndRemarksObj[pItem.postnId+'_unfoldToPerform']"
                          v-for="(apItem, apIndex) in pItem.actDailyReport.marketActToPerformData.actList"
                          :key="apIndex">
                        <view class="market-activity-list-item">
                            <view class="media-list">
                                <view class="media-top">
                                    <view class="num-view">
                                        <view class="num">{{apItem.actNum}}</view>
                                    </view>
                                </view>
                            </view>
                            <view class="content-middle-line">
                                <view style="width: 100%">
                                    <view class="data" style="width: 75%;float: left">
                                        <view class="name">{{apItem.costType}}·{{apItem.actType |
                                            lov('MC_TYPE')}}
                                        </view>
                                        <view class="title">{{apItem.status |
                                            lov('MC_STATUS')}}·{{apItem.approveStatus | lov('APRO_STATUS')}}
                                        </view>
                                        <view class="val">申请金额:{{apItem.applyAmount | cny}}</view>
                                    </view>
                                    <view style="width: 25%;float: right">
                                        <view
                                            :style="{'background-image': 'url(' + $imageAssets.dailyStamp + ')'}"
                                            style="width: 60px;height: 60px;background-repeat: no-repeat;background-size: 100% 100%;
                border-radius: 10px 10px 0 0;background-color: white;text-align: center">
                                            <view
                                                style="font-size: 10px;line-height: 62px;transform: rotate(-15deg);color: #FF5A5A">
                                                今日新增
                                            </view>
                                        </view>
                                    </view>
                                </view>
                                <view class="sum">
                                    <view class="title">{{apItem.startTime |date('YYYY-MM-DD HH:mm')}}&nbsp;~&nbsp;{{apItem.endTime
                                        |date('YYYY-MM-DD HH:mm')}}
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>

                <!--                            尚未实发-->
                <view class="daily-line-box quota-item"
                      v-if="dItem.name=='marketActNotActualSalaryData' && pItem.actDailyReport.marketActNotActualSalaryData">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'尚未实发：共'+pItem.actDailyReport.marketActNotActualSalaryData.notActualActNum+'场,审批金额' +pItem.actDailyReport.marketActNotActualSalaryData.approveAmount"></line-title>
                        <view class="right-icon-operate"
                              :style="{color: controlAndRemarksObj[pItem.postnId+'_unfoldNotActualSalary'] ? '#2F69F8': '#262626'}"
                              @tap="switchState(pItem.postnId+'_unfoldNotActualSalary')">
                            <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_unfoldNotActualSalary']"
                                       icon="icon-shouqi"/>
                            <link-icon v-else
                                       icon="icon-zhankai"/>
                        </view>
                    </view>

                    <view class="visit-box"
                          v-show="controlAndRemarksObj[pItem.postnId+'_unfoldNotActualSalary']"
                          v-for="(anaItem, anaIndex) in pItem.actDailyReport.marketActNotActualSalaryData.actList"
                          :key="anaIndex">
                        <view class="market-activity-list-item">
                            <view class="media-list">
                                <view class="media-top">
                                    <view class="num-view">
                                        <view class="num">{{anaItem.actNum}}</view>
                                    </view>
                                </view>
                            </view>
                            <view class="content-middle">
                                <view class="name">{{anaItem.costType}}·{{anaItem.actType |
                                    lov('MC_TYPE')}}
                                </view>
                            </view>
                            <view class="content-middle-line">
                                <view class="data">
                                    <view class="title">{{anaItem.status |
                                        lov('MC_STATUS')}}·{{anaItem.approveStatus | lov('APRO_STATUS')}}
                                    </view>
                                    <view class="val">审批金额:{{anaItem.poAmount | cny}}</view>
                                </view>
                                <view class="sum">
                                    <view class="title">{{anaItem.startTime |date('YYYY-MM-DD HH:mm')}}&nbsp;~&nbsp;{{anaItem.endTime
                                        |date('YYYY-MM-DD HH:mm')}}
                                    </view>
                                </view>
                                <view class="sum">
                                    <view class="title">{{anaItem.feedbackComment}}</view>
                                    <view class="title">{{anaItem.approvalComment}}</view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <!-- 消费者板块 -->
            <view v-if="pItem.consumerDailyReport">
                <view class="consumer-title" v-if="pItem.consumerDailyReport.consumerList&& dItem.name=='consumerList'">以下数据统计以系统内生效且审批通过的数据为依据</view>
                <!--消费者数量情况-->
                <view class="daily-line-box quota-item" v-if="pItem.consumerDailyReport.consumerList&& dItem.name=='consumerList'">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="`消费者数量情况：本日新增已认证${pItem.consumerDailyReport.summary.consumerTotal}人`"></line-title>
                        <view class="right-icon-operate"
                              :style="{color: controlAndRemarksObj[pItem.postnId+'_consumerList'] ? '#2F69F8': '#262626'}"
                              @tap="switchState(pItem.postnId+'_consumerList')">
                            <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_consumerList']"
                                       icon="icon-shouqi"/>
                            <link-icon v-else icon="icon-zhankai"/>
                        </view>
                    </view>
                    <view class="visit-box"
                          v-for="(anaItem, qIndex) in pItem.consumerDailyReport.consumerList" :key="qIndex+'consumerList'"
                          v-show="controlAndRemarksObj[pItem.postnId+'_consumerList']">
                          <view class="buy-list-item">
                              <view class="buy-list-item-title wd30" v-if="(qIndex+1) === 1">
                                  <view>姓名</view>
                                  <view>K序列等级</view>
                                  <view>V序列等级</view>
                              </view>
                              <view class="buy-list-item-content wd30">
                                  <view>{{anaItem.name}}</view>
                                  <view>{{anaItem.typeName}}</view>
                                  <view>{{anaItem.loyaltyLevelName}}</view>
                              </view>
                          </view>
                    </view>
                </view>

                <!--消费者建设情况-->
                <view class="daily-line-box quota-item" v-if="(pItem.consumerDailyReport.actList ||
                pItem.consumerDailyReport.giftList || pItem.consumerDailyReport.visitList) && dItem.name=='devList'">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="`消费者建设情况：本日跟进消费者场景码触达${pItem.consumerDailyReport.summary.codeTouchTotal}人、
                                    礼赠触达${pItem.consumerDailyReport.summary.giftTouchTotal}人、
                                    拜访触达${pItem.consumerDailyReport.summary.visitTouchTotal}人`"></line-title>
                        <view class="right-icon-operate"
                              :style="{color: controlAndRemarksObj[pItem.postnId+'_devList'] ? '#2F69F8': '#262626'}"
                              @tap="switchState(pItem.postnId+'_devList')">
                            <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_devList']"
                                       icon="icon-shouqi"/>
                            <link-icon v-else icon="icon-zhankai"/>
                        </view>
                    </view>
                    <view class="visit-box"
                          v-for="(anaItem, qIndex) in pItem.consumerDailyReport.actList" :key="qIndex+'actList'"
                          v-show="controlAndRemarksObj[pItem.postnId+'_devList']">
                          <view class="buy-list-item">
                              <view class="buy-list-item-title wd48" v-if="(qIndex+1) === 1">
                                  <view>活动类型</view>
                                  <view>触达人数</view>
                              </view>
                              <view class="buy-list-item-content wd48">
                                  <view>{{anaItem.activityType | lov('MC_TYPE')}}</view>
                                  <view>{{anaItem.total}}人</view>
                              </view>
                          </view>
                    </view>
                    <view class="visit-box"
                          v-for="(anaItem, qIndex) in pItem.consumerDailyReport.giftList" :key="qIndex+'giftList'"
                          v-show="controlAndRemarksObj[pItem.postnId+'_devList']">
                          <view class="buy-list-item">
                              <view class="buy-list-item-title wd48" v-if="(qIndex+1) === 1">
                                  <view>礼赠类型</view>
                                  <view>触达人数</view>
                              </view>
                              <view class="buy-list-item-content wd48">
                                  <view>{{anaItem.preType | lov('CSM_GIFT_TYPE')}}</view>
                                  <view>{{anaItem.total}}人</view>
                              </view>
                          </view>
                    </view>
                    <view class="visit-box"
                          v-for="(anaItem, qIndex) in pItem.consumerDailyReport.visitList" :key="qIndex+'visitList'"
                          v-show="controlAndRemarksObj[pItem.postnId+'_devList']">
                          <view class="buy-list-item">
                              <view class="buy-list-item-title wd48" v-if="(qIndex+1) === 1">
                                  <view>拜访方式</view>
                                  <view>触达人数</view>
                              </view>
                              <view class="buy-list-item-content wd48">
                                  <view>{{anaItem.visitType | lov('CSM_VISIT_WAY')}}</view>
                                  <view>{{anaItem.total}}人</view>
                              </view>
                          </view>
                    </view>
                </view>
                <!--消费者转换情况-->
                <view class="daily-line-box quota-item" v-if="(pItem.consumerDailyReport.orderList ||
                pItem.consumerDailyReport.inviteList) && dItem.name=='transList'">
                        <view class="daily-line-title">
                            <line-title :hiddenLeft="true"
                                        :title="`消费者转化情况：本日新增动销订单金额${pItem.consumerDailyReport.summary.orderAmount}元、
                                        转介绍消费者数量${pItem.consumerDailyReport.summary.inviteConTotal}人`"></line-title>
                            <view class="right-icon-operate"
                                  :style="{color: controlAndRemarksObj[pItem.postnId+'_transList'] ? '#2F69F8': '#262626'}"
                                  @tap="switchState(pItem.postnId+'_transList')">
                                <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_transList']"
                                           icon="icon-shouqi"/>
                                <link-icon v-else icon="icon-zhankai"/>
                            </view>
                        </view>
                        <view class="visit-box"
                              v-for="(anaItem, qIndex) in pItem.consumerDailyReport.orderList" :key="qIndex+'orderList'"
                              v-show="controlAndRemarksObj[pItem.postnId+'_transList']">
                              <view class="buy-list-item">
                                  <view class="buy-list-item-title wd24" v-if="(qIndex+1) === 1">
                                      <view>姓名</view>
                                      <view>品项</view>
                                      <view>动销金额</view>
                                      <view>动销瓶数</view>
                                  </view>
                                  <view class="buy-list-item-content wd24">
                                      <view>{{anaItem.acctName}}</view>
                                      <view>{{anaItem.prodTypeName}}</view>
                                      <view>{{anaItem.orderAmount}}元</view>
                                      <view>{{anaItem.qty}}瓶</view>
                                  </view>
                              </view>
                        </view>
                        <view class="visit-box"
                              v-for="(anaItem, qIndex) in pItem.consumerDailyReport.inviteList" :key="qIndex+'inviteList'"
                              v-show="controlAndRemarksObj[pItem.postnId+'_transList']">
                              <view class="buy-list-item">
                                  <view class="buy-list-item-title wd30" v-if="(qIndex+1) === 1">
                                      <view>转介绍消费者姓名</view>
                                      <view>K序列等级</view>
                                      <view>V序列等级</view>
                                  </view>
                                  <view class="buy-list-item-content wd30">
                                      <view>{{anaItem.name}}</view>
                                      <view>{{anaItem.typeName}}</view>
                                      <view>{{anaItem.loyaltyLevelName}}</view>
                                  </view>
                              </view>
                        </view>
                    </view>
                </view>
            </view>
            </view>
        </view>
    </view>
</template>

<script>
import LineTitle from "../../../echart/lzlj/components/line-title";
import timeDuring from "../../../terminal/components/time-during";
import addressTip from "../../../terminal/components/address-tip";
/**
 * @file
 * <AUTHOR>
 * @date 2023/4/27
 */
export default {
    name: 'daily-report-data',

    components: {LineTitle,timeDuring, addressTip},

    mixins: [],

    props: {
        postnDailyReportList: {
            type: Array
        },
        controlAndRemarksObj: {
            type: Object
        },
        dailyTemp: {
            type: Array
        }
    },

    data() {
        return {}
    },
    computed: {},

    watch: {},

    created() {

    },

    mounted() {
    },

    destroyed() {
    },

    methods: {
        switchState(type) {
            this.$emit('switchState',type)
        }
    }

}
</script>

<style lang="scss">
.daily-report-data {
    .wd30{
        view{
            width:30%;
        }
    }
    .wd24{
        view{
            width:24%;
        }
    }
    .wd48{
       view{
           width:48%;
       }
    }
    .consumer-title {
        margin: 30px 20px 2px 20px;
        color:red;
        font-size: 28px;
    }
 .buy-list-item {
        background: #FFFFFF;
        width: 95%;
        font-size: 28px;
        border-radius: 16px;
        line-height: 40px;
        text-align: left;
        margin-top: 30px;
        .buy-list-item-title{
            display: flex;
            justify-content: space-between;
            font-weight: bold;
        }
       .buy-list-item-content{
            display: flex;
            justify-content: space-between;
            view{
                overflow-x: scroll;
                overflow-y: hidden;
                white-space: nowrap;
			}
        }
    }

}
</style>
