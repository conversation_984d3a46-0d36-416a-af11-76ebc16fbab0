<template>
    <link-auto-list :option="autoList" class="daily-list" :searchInputBinding="{ props: { placeholder: '' } }"
        :hideCreateButton="hideCreateButton">
        <template slot="top">
            <lnk-taps :taps="tabs" v-model="activeTab" @switchTab="changeTab"></lnk-taps>
        </template>
        <template slot="other">
            <link-fab-button @tap="onClickItem" v-if="isShowAdd" />
        </template>
        <view slot="searchRight" class="search-container">
            <link-dropdown-group placement="left" align="end">
                <view class="only-today" :style="scopeStyle">
                    {{ dataScopeActive.name }}
                </view>
                <view slot="dropdown">
                    <link-dropdown-menu label="仅看本人" @tap="chooseScopt('MY')" />
                    <link-dropdown-menu label="查看下级" @tap="chooseScopt('DAILY_REPORT_MY_POSTN')" />
                </view>
            </link-dropdown-group>
            <view v-if="dailyType === 'DAY'" class="only-today" @tap="viewTodayData" :style="dateStyle">仅看今日</view>
            <link-filter v-model="filterOption" @change="handleChange" />
        </view>
        <template slot-scope="{data,index}">
            <view :key="index" :data="data" class="daily-list-rows" @tap="gotoItem(data)">
                <view slot="note" class="daily-item">
                    <view class="daily-rows">
                        <view class="daily-date"><label class="dot-top"></label>{{ $utils.dateFormat(data.dailyDate) }}
                        </view>
                        <view class="daily-person">提报人<label class="daily-person-name">{{ data.createdName }}</label>
                        </view>
                    </view>
                </view>
            </view>
        </template>
    </link-auto-list>
</template>

<script>
import { ROW_STATUS } from "../../../../utils/constant";
import LnkTaps from "../../../core/lnk-taps/lnk-taps";
import { getFiltersRaw } from "link-taro-component";
import Taro from "@tarojs/taro";
export default {
    name: "daily-list",
    data() {
        const autoList = new this.AutoList(this, {
            module: 'export/link/terminal/daily',
            url: {
                queryByExamplePage: 'export/link/dailyReport/queryDailyReportPage'
            },
            loadOnStart: false,
            sortField: 'dailyDate',
            param: {
                order: 'desc',
                oauth: Taro.getStorageSync('token').result.positionType === 'Salesman' ? 'MY' : 'DAILY_REPORT_MY_POSTN',
                filtersRaw: []
            },
            hooks: {
                beforeLoad(option) {
                    option.param.dailyType = this.dailyType;
                },
            },
            sortOptions: null,
            searchFields: ['dailyFinished', 'dailyUnfinished', 'other', 'createdName'],
        });
        return {
            autoList,
            hideCreateButton: true,                 // 列表携带的新建按钮是否显示
            dailyTemp: [],                          // 日报模板
            todayFlag: false,                         // 是否高亮‘仅看今日'按钮
            filterOption: [
                { label: '日期', field: 'dailyDate', type: 'date' },
            ],
            dateStyle: 'background: #EDF3FF;color: #2F69F8;border: 1px solid #EDF3FF;', // 标签样式
            scopeStyle: 'background: #EDF3FF;color: #2F69F8;border: 1px solid #EDF3FF;', // 标签样式
            dataScope: [
                { id: '1', val: 'MY', name: '仅看本人' },
                { id: '1', val: 'DAILY_REPORT_MY_POSTN', name: '查看下级' }
            ],//数据范围切换
            dataScopeActive: {},//默认值
            ifHasDailyReport: false,//是否存在今日的日报数据

            activeTab: {},
            tabs: [
                { name: '日报', seq: '1', val: 'DAY', url: '/export/link/dailyReport/queryDailyReportPage' },
                { name: '周报', seq: '2', val: 'WEEK', url: '/export/link/dailyReport/queryDailyReportPage' },
                { name: '月报', seq: '3', val: 'MONTH', url: '/export/link/dailyReport/queryDailyReportPage' },
            ],
            dailyType: 'DAY'
        }
    },
    components: {
        LnkTaps
    },
    async created() {
        this.dataScopeActive = Taro.getStorageSync('token').result.positionType == 'Salesman' ? this.dataScope[0] : this.dataScope[1];
        this.viewTodayData();//默认查询今日。本人的
        this.activeTab = this.tabs[0]
        let sceneObj = await this.$scene.ready();
        if (sceneObj.query['activeTab'] === 'MONTH') {
            this.activeTab = this.tabs[2]
            this.dailyType = 'MONTH'
        }
        if (sceneObj.query['activeTab'] === 'WEEK') {
            this.activeTab = this.tabs[1]
            this.dailyType = 'WEEK'
        }
    },
    mounted() {
        this.$bus.$on('dailyHeaderRefresh', async () => {
            // this.viewTodayData();
            if (this.dailyType !== 'DAY') {
                const filtersRaw = this.autoList.option.param['filtersRaw'].filter((item) => item.property !== "dailyDate");
                this.autoList.option.param['filtersRaw'] = filtersRaw;
            }
            this.autoList.methods.reload();
        });
        this.setNavbarTitle()
    },
    computed: {
        isShowAdd() {
            const userInfo = this.$taro.getStorageSync('token').result
            if (userInfo.coreOrganizationTile.brandCompanyCode === '1612' && userInfo.positionType === 'SalesAreaManager') return true
            return this.$utils.isPostnOauth() === 'MY_POSTN'
        },
    },
    methods: {
        async handleChange(val) {
            const filterArr = getFiltersRaw(val);
            console.log(filterArr)
            this.autoList.option.param.filtersRaw = filterArr;
            await this.autoList.methods.reload();
        },
        switchPost() {
            this.$refs.dropdown.hide();
        },
        /**
         * 获取日报模板
         * <AUTHOR>
         * @date 2020-11-10
         */
        async getTmlType() {
            this.dailyTemp = [];
            const templateTypeMap = {
                DAY: 'Daily',
                WEEK: 'Weekly',
                MONTH: 'Monthly'
            }
            const type = templateTypeMap[this.dailyType]
            const data = await this.$utils.getQwMpTemplate(type);
            if (data.success) {
                let result = JSON.parse(data.result);
                let tempArr = JSON.parse(result.conf);
                tempArr.forEach((item) => {
                    if (item.values.field) {
                        let objectData = {
                            ctrlCode: item.ctrlCode,
                            name: item.values.field,
                            display: true,
                            label: item.base.label,
                            required: item.base.require,
                            type: item.values.type,
                            lovType: item.values.lovType
                        };
                        this.dailyTemp.push(objectData);
                    }
                })
            }
        },
        /**
         * 新建日报
         * <AUTHOR>
         * @date 2021-08-26
         */
        async onClickItem() {
            let content = ''
            if (this.dailyType === 'DAY') {
                content = '您今日已经提报过日报，是否需要重新提报？'
            } else if (this.dailyType === 'WEEK') {
                // if (!this.checkEnableCreateWeekReport()) {
                //     this.$message('当前不是提交周报的时间，请在周六18:00至周日18:00之间提交！')
                //     return
                // }
                content = '您本周已经提报过周报，是否需要重新提报？'
            } else {
                // if (!this.checkEnableCreateMonthReport()) {
                //     this.$message("当前不是提交月报的时间，请在每月的最后3天提交！")
                //     return
                // }
                content = '您本月已经提报过月报，是否需要重新提报？'
            }

            let that = this;
            const data = await this.$http.post('action/link/dailyReport/newCheck', {
                dailyType: this.dailyType
            });
            this.ifHasDailyReport = data['ifHasDailyReport'];
            const dailyReportHeaderId = data['dailyReportHeaderId'];

            // 是否有今日数据，提示新建
            if (!this.ifHasDailyReport) {
                await this.newGotoPage(ROW_STATUS.NEW, dailyReportHeaderId)
            } else {
                this.$dialog({
                    title: '提示',
                    content,
                    cancelButton: true,
                    onConfirm: () => {
                        that.newGotoPage(ROW_STATUS.UPDATE, dailyReportHeaderId)
                    },
                    onCancel: () => {
                    }
                })
            }
        },
        /**
         * 去新建
         * <AUTHOR>
         * @date 2021-08-26
         * @param type : ROW_STATUS.NEW 标志为新建没有当日历史日报 Update新建时存在当日历史日报
         * @param dailyReportHeaderId : 头ID，更新时使用，需要查询出已经上传的附件
         */
        async newGotoPage(type, dailyReportHeaderId) {
            await this.getTmlType(); //获取日报模板配置的字段
            this.$nav.push('/pages/lzlj-II/daily-paper-new/daily-item-new-page', {
                dailyTemp: this.dailyTemp,
                dailyType: this.dailyType,
                newScene: type,
                dailyReportHeaderId: dailyReportHeaderId,
            });
        },
        /**
         * 只看今日
         * <AUTHOR>
         * @date 2021-08-26
         */
        viewTodayData() {
            this.todayFlag = !this.todayFlag;
            this.todayFlag ? this.dateStyle = 'background: #EDF3FF;color: #2F69F8;border: 1px solid #EDF3FF;'
                : this.dateStyle = 'border: 1px solid #E0E4EC;color: #8C8C8C;';
            this.todayItem()
        },
        /**
         * 切换职位
         * <AUTHOR>
         * @date 2021-08-26
         */
        switchPostnScope(placement, align, e) {
            this.$dropdown({
                target: e,
                placement, align,
                render: () => {
                    return (
                        <view>
                            <link-dropdown-menu label="仅看本人" onTap={() => this.chooseScopt('MY')} />
                            <link-dropdown-menu label="查看下级" onTap={() => this.chooseScopt('DAILY_REPORT_MY_POSTN')} />
                        </view>
                    )
                },
            })
        },
        /**
         * 切换职位
         * <AUTHOR>
         * @date 2021-08-26
         */
        chooseScopt(type) {
            this.dataScopeActive = this.dataScope.filter((item) => item.val === type)[0];
            //this.autoList.option.param.oauth = type
            // if (type === 'DAILY_REPORT_MY_POSTN') {
            //     this.autoList.option.param.oauth = type
            // } else {
            //     delete this.autoList.option.param.oauth
            // }
            if (this.dailyType === 'WEEK' || this.dailyType === 'MONTH') {
                this.autoList.methods.reload({
                    page: 1,
                    filtersRaw: [],
                    oauth: type
                });
            } else {
                this.autoList.option.param.oauth = type
                this.autoList.methods.reload();
            }
        },
        /**
         * 今日数据筛选条件
         * <AUTHOR>
         * @date 2021-08-26
         */
        todayItem() {
            if (!this.todayFlag) {
                this.autoList.option.param['filtersRaw'] = this.autoList.option.param['filtersRaw'].filter((item) => item.property !== "dailyDate");
                this.autoList.methods.reload();
            } else {
                if (this.dailyType === 'DAY') {
                    this.autoList.option.param.filtersRaw = [
                        {
                            id: 'dailyDate',
                            property: 'dailyDate',
                            value: this.$date.format(new Date(Date.now()), 'YYYY-MM-DD')
                        }
                    ];
                }
                this.autoList.methods.reload();
            }
        },
        /**
         * 跳转详情
         * <AUTHOR>
         * @date 2021-08-26
         */
        async gotoItem(data) {
            await this.getTmlType(); //获取日报模板配置的字段
            this.$nav.push('/pages/lzlj-II/daily-paper-new/daily-item-page', {
                dailyTemp: this.dailyTemp,
                dailyType: this.dailyType,
                data: data
            });
        },
        changeTab(val, key) {
            this.activeTab = val;
            this.dailyType = this.activeTab.val
            this.todayFlag = false;
            this.dateStyle = 'border: 1px solid #E0E4EC;color: #8C8C8C;';
            this.setNavbarTitle()
            this.dataScopeActive = Taro.getStorageSync('token').result.positionType == 'Salesman' ? this.dataScope[0] : this.dataScope[1]
            this.autoList.methods.reload({
                page: 1,
                filtersRaw: [],
                oauth: Taro.getStorageSync('token').result.positionType == 'Salesman' ? 'MY' : 'DAILY_REPORT_MY_POSTN'

            });
        },
        setNavbarTitle() {
            let title = ''
            if (this.dailyType === 'DAY') {
                title = '日报列表'
            } else if (this.dailyType === 'WEEK') {
                title = '周报列表'
            } else if (this.dailyType === 'MONTH') {
                title = '月报列表'
            }
            this.$taro.setNavigationBarTitle({ title })
        },
        checkEnableCreateWeekReport() {
            // 周报填写时间为周六18:00至周日18:00 ；
            const now = new Date();
            const dayOfWeek = now.getDay();
            if (dayOfWeek === 6) { // 如果今天是周六
                const currentHour = now.getHours();
                if (currentHour >= 18) {
                    return true; // 如果当前时间已经是18:00或之后，则在周六18:00至周日18:00之间
                }
            } else if (dayOfWeek === 0) { // 如果今天是周日
                const currentHour = now.getHours();
                if (currentHour < 18) {
                    return true; // 如果当前时间还在18:00之前，则在周六18:00至周日18:00之间
                }
            }
            return false; // 其他情况都不在周六18:00至周日18:00之间
        },
        checkEnableCreateMonthReport() {
            // 月报填写时间为每月最后3天。
            const today = new Date();
            const year = today.getFullYear();
            const month = today.getMonth();
            const lastDateOfMonth = new Date(year, month + 1, 0);
            const lastThreeDaysOfMonth = new Date(year, month, lastDateOfMonth.getDate() - 2);
            return today >= lastThreeDaysOfMonth;
        },
    }
}
</script>

<style lang="scss">
    .daily-lst-page {
        .daily-list {
            margin-top: 100px;
            .daily-rows {
                border-bottom: 1px solid #f2f2f2;
            }
        }
    }
</style>
