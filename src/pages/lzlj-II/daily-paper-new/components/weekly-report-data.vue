<template>
    <view class="weekly-report-data">
        <view v-for="(pItem, pIndex) in postnDailyReportList" :key="pIndex">
            <view class="daily-postn">
                {{ pItem.postnName }}
            </view>

            <view class="daily-data-type">
                {{dailyType === 'WEEK' ? '本周数据汇总' : '本月数据汇总'}}
            </view>
            <view  v-for="(dItem, dIndex) in dailyTemp">
            <!-- 终端板块-->
            <view v-if="pItem.terminalDailyReport">

                <!--拜访内容-->
                <view class="daily-line-box quota-item" v-if="dItem.name=='visitData' && pItem.terminalDailyReport.visitData">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'终端拜访情况：拜访终端'+ (pItem.terminalDailyReport.visitData.total || 0)+'家'"></line-title>
                        <view class="right-icon-operate"
                              :style="{color: controlAndRemarksObj[pItem.postnId+'_unfoldVisit'] ? '#2F69F8': '#262626'}"
                              @tap="switchState(pItem.postnId+'_unfoldVisit')">
                            <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_unfoldVisit']"
                                       icon="icon-shouqi"/>
                            <link-icon v-else icon="icon-zhankai"/>
                        </view>
                    </view>
                    <view class="visit-box"
                          v-for="(vItem, vIndex) in pItem.terminalDailyReport.visitData.list" :key="vIndex"
                          v-show="controlAndRemarksObj[pItem.postnId+'_unfoldVisit']">
                        <time-during :start-time="vItem.visitTime"
                                     :end-time="vItem.visitEndTime"></time-during>
                        <line-title :hiddenLeft="false" :title="vItem.accntName"></line-title>
                        <address-tip :address="vItem.fullAddress"></address-tip>
                    </view>
                </view>
                <!--配额申请-->
                <view class="daily-line-box quota-item" v-if="dItem.name=='quotaData' && pItem.terminalDailyReport.quotaData">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="pItem.terminalDailyReport.quotaData.title"></line-title>
                        <view class="right-icon-operate"
                              :style="{color: controlAndRemarksObj[pItem.postnId+'_unfoldQuota'] ? '#2F69F8': '#262626'}"
                              @tap="switchState(pItem.postnId+'_unfoldQuota')">
                            <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_unfoldQuota']"
                                       icon="icon-shouqi"/>
                            <link-icon v-else icon="icon-zhankai"/>
                        </view>
                    </view>
                    <view class="visit-box"
                          v-for="(qItem, qIndex) in pItem.terminalDailyReport.quotaData.list" :key="qIndex"
                          v-show="controlAndRemarksObj[pItem.postnId+'_unfoldQuota']">
                        <line-title :hiddenLeft="false" :title="qItem.joinAccntName"></line-title>
                        <item :title="qItem.materialName" v-if="qItem.type==='Increase'" rightWidth="45px"
                              :content="'+'+qItem.sumNum+'件'" :arrow="false"/>
                        <item :title="qItem.materialName" v-if="qItem.type==='Decrease'" rightWidth="45px"
                              :content="'-'+qItem.sumNum+'件'" :arrow="false"/>
                    </view>
                </view>
                <!--终端下单-->
                <view class="daily-line-box quota-item" v-if="dItem.name=='orderData' && pItem.terminalDailyReport.orderData">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'终端下单：下单终端'+(pItem.terminalDailyReport.orderData.total || 0)+'家'"></line-title>
                        <view class="right-icon-operate"
                              :style="{color: controlAndRemarksObj[pItem.postnId+'_unfoldOrder'] ? '#2F69F8': '#262626'}"
                              @tap="switchState(pItem.postnId+'_unfoldOrder')">
                            <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_unfoldOrder']"
                                       icon="icon-shouqi"/>
                            <link-icon v-else icon="icon-zhankai"/>
                        </view>
                    </view>
                    <view class="visit-box"
                          v-for="(sItem, sIndex) in pItem.terminalDailyReport.orderData.list" :key="sIndex"
                          v-show="controlAndRemarksObj[pItem.postnId+'_unfoldOrder']">
                        <line-title :hiddenLeft="false" :title="sItem.acctName"></line-title>
                        <item v-for="(proItem, proIndex) in sItem.prodList" :key="proIndex"
                              :title="proItem.prodName" rightWidth="45px" :content="proItem.prodNum+'件'"
                              :arrow="false"/>
                    </view>
                </view>
                <!--终端新开情况-->
                <view class="daily-line-box quota-item" v-if="dItem.name=='newTerminalData' && pItem.terminalDailyReport.newTerminalData">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'终端新开情况：新开终端'+(pItem.terminalDailyReport.newTerminalData.total || 0)+'家'"></line-title>
                        <view class="right-icon-operate"
                              :style="{color: controlAndRemarksObj[pItem.postnId+'_unfoldNewTerminal'] ? '#2F69F8': '#262626'}"
                              @tap="switchState(pItem.postnId+'_unfoldNewTerminal')">
                            <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_unfoldNewTerminal']"
                                       icon="icon-shouqi"/>
                            <link-icon v-else icon="icon-zhankai"/>
                        </view>
                    </view>
                    <view class="visit-box"
                          v-for="(sItem, sIndex) in pItem.terminalDailyReport.newTerminalData.list" :key="sIndex"
                          v-show="controlAndRemarksObj[pItem.postnId+'_unfoldNewTerminal']">
                        <line-title :hiddenLeft="false" :title="sItem.accntName"></line-title>
                        <item :title="'认证通过时间: ' + sItem.authenticatedTime" rightWidth="45px" :arrow="false"/>
                    </view>
                </view>
                <!--终端进货情况-->
                <view class="daily-line-box quota-item" v-if="dItem.name=='terminalPurchaseData' && pItem.terminalDailyReport.terminalPurchaseData">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'终端进货情况：共'+(pItem.terminalDailyReport.terminalPurchaseData.accntNum||0)+'家终端扫码入库'
                                                +(pItem.terminalDailyReport.terminalPurchaseData.total||0)+'件产品'"></line-title>
                        <view class="right-icon-operate"
                              :style="{color: controlAndRemarksObj[pItem.postnId+'_unfoldTerminalPurchase'] ? '#2F69F8': '#262626'}"
                              @tap="switchState(pItem.postnId+'_unfoldTerminalPurchase')">
                            <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_unfoldTerminalPurchase']"
                                       icon="icon-shouqi"/>
                            <link-icon v-else icon="icon-zhankai"/>
                        </view>
                    </view>
                    <view class="visit-box"
                          v-for="(sItem, sIndex) in pItem.terminalDailyReport.terminalPurchaseData.list" :key="sIndex"
                          v-show="controlAndRemarksObj[pItem.postnId+'_unfoldTerminalPurchase']">
                        <line-title :hiddenLeft="false" :title="sItem.accntName"></line-title>
                        <item :title="'进货数量: '+sItem.purchaseQty+'件'" rightWidth="45px" :arrow="false"/>
                    </view>
                </view>
                <!--终端客户等级达成情况-->
                <view class="daily-line-box quota-item" v-if="dItem.name=='terminalLevelAchievement' && pItem.terminalDailyReport.terminalLevelAchievement">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="`终端客户等级达成情况: 达成${(pItem.terminalDailyReport.terminalLevelAchievement.total || 0)}家`"></line-title>
                        <view class="right-icon-operate"
                              :style="{color: controlAndRemarksObj[pItem.postnId+'_unfoldLevelAchievement'] ? '#2F69F8': '#262626'}"
                              @tap="switchState(pItem.postnId+'_unfoldLevelAchievement')">
                            <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_unfoldLevelAchievement']"
                                       icon="icon-shouqi"/>
                            <link-icon v-else icon="icon-zhankai"/>
                        </view>
                    </view>
                    <view class="visit-box"
                          v-for="(sItem, sIndex) in pItem.terminalDailyReport.terminalLevelAchievement.list" :key="sIndex"
                          v-show="controlAndRemarksObj[pItem.postnId+'_unfoldLevelAchievement']">
                        <line-title :hiddenLeft="false" :title="`客户达成等级：${sItem.achievementLevel}`"></line-title>
                        <item :title="'终端编码: '+' '+sItem.acctCode" rightWidth="45px" :arrow="false"/>
                        <item :title="'终端名称: '+' '+sItem.acctName" rightWidth="45px" :arrow="false"/>
                    </view>
                </view>

            </view>
            <!-- 消费者板块 -->
			<view v-if="pItem.consumerDailyReport">
			    <view class="consumer-title" v-if="pItem.consumerDailyReport.summary&& dItem.name=='consumerList'">以下数据统计以系统内生效且审批通过的数据为依据</view>
			    <!--消费者数量情况-->
			    <view class="daily-line-box quota-item" v-if="pItem.consumerDailyReport.summary&& dItem.name=='consumerList'">
			        <view class="daily-line-title">
			            <line-title :hiddenLeft="true"
			                        :title="`消费者数量情况：本${dailyType === 'WEEK' ? '周' : '月'}新增已认证${pItem.consumerDailyReport.summary.consumerTotal}人`"></line-title>
			            <view class="right-icon-operate"
			                  :style="{color: controlAndRemarksObj[pItem.postnId+'_consumerList'] ? '#2F69F8': '#262626'}"
			                  @tap="switchState(pItem.postnId+'_consumerList')">
			                <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_consumerList']"
			                           icon="icon-shouqi"/>
			                <link-icon v-else icon="icon-zhankai"/>
			            </view>
			        </view>
			    </view>

			    <!--消费者建设情况-->
			    <view class="daily-line-box quota-item" v-if="pItem.consumerDailyReport.summary && dItem.name=='devList'">
			        <view class="daily-line-title">
			            <line-title :hiddenLeft="true"
			                        :title="`消费者建设情况：本${dailyType === 'WEEK' ? '周' : '月'}跟进消费者场景码触达${pItem.consumerDailyReport.summary.codeTouchTotal}人、
			                        礼赠触达${pItem.consumerDailyReport.summary.giftTouchTotal}人、
			                        拜访触达${pItem.consumerDailyReport.summary.visitTouchTotal}人`"></line-title>
			            <view class="right-icon-operate"
			                  :style="{color: controlAndRemarksObj[pItem.postnId+'_devList'] ? '#2F69F8': '#262626'}"
			                  @tap="switchState(pItem.postnId+'_devList')">
			                <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_devList']"
			                           icon="icon-shouqi"/>
			                <link-icon v-else icon="icon-zhankai"/>
			            </view>
			        </view>
			    </view>
			    <!--消费者转换情况-->
			    <view class="daily-line-box quota-item" v-if="pItem.consumerDailyReport.summary && dItem.name=='transList'">
			            <view class="daily-line-title">
			                <line-title :hiddenLeft="true"
			                            :title="`消费者转化情况：本${dailyType === 'WEEK' ? '周' : '月'}新增动销订单金额${pItem.consumerDailyReport.summary.orderAmount}元、
			                            转介绍消费者数量${pItem.consumerDailyReport.summary.inviteConTotal}人`"></line-title>
			                <view class="right-icon-operate"
			                      :style="{color: controlAndRemarksObj[pItem.postnId+'_transList'] ? '#2F69F8': '#262626'}"
			                      @tap="switchState(pItem.postnId+'_transList')">
			                    <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_transList']"
			                               icon="icon-shouqi"/>
			                    <link-icon v-else icon="icon-zhankai"/>
			                </view>
			            </view>
			        </view>
			    </view>
            <!-- 活动板块-->
            <view v-if="pItem.actDailyReport">
                <!--<view class="daily-data-type">-->
                <!--    市场活动-->
                <!--</view>-->
                <!-- 活动执行情况-->
                <view class="daily-line-box quota-item"
                      v-if="dItem.name=='marketActHappenedTodayData' && pItem.actDailyReport.marketActHappenedTodayData">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'活动执行情况：'+(dailyType === 'WEEK' ? '本周' : '本月')+'新申请'+(pItem.actDailyReport.marketActHappenedTodayData.applyNum || 0)+'场活动,实发'+(pItem.actDailyReport.marketActActualSalaryData.actualNum || 0)+'场活动'"></line-title>
                        <view class="right-icon-operate"
                              :style="{color: controlAndRemarksObj[pItem.postnId+'_unfoldHappenedToday'] ? '#2F69F8': '#262626'}"
                              @tap="switchState(pItem.postnId+'_unfoldHappenedToday')">
                            <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_unfoldHappenedToday']"
                                       icon="icon-shouqi"/>
                            <link-icon v-else
                                       icon="icon-zhankai"/>
                        </view>
                    </view>
                    <view class="visit-box"
                          v-show="controlAndRemarksObj[pItem.postnId+'_unfoldHappenedToday']"
                          v-for="(ahItem, ahIndex) in pItem.actDailyReport.marketActHappenedTodayData.actList"
                          :key="ahIndex">
                        <view class="market-activity-list-item">
                            <view class="media-list">
                                <view class="media-top">
                                    <view class="num-view">
                                        <view class="num">{{ahItem.actNum}}</view>
                                    </view>
                                </view>
                            </view>
                            <view class="content-middle-line">
                                <view style="width: 100%">
                                    <view class="data" style="width: 75%;float: left">
                                        <view class="name">{{ahItem.costType}}·{{ahItem.actType |
                                            lov('MC_TYPE')}}
                                        </view>
                                        <view class="title" :class="ahItem.status">{{ahItem.status |
                                            lov('MC_STATUS')}}·{{ahItem.approveStatus | lov('APRO_STATUS')}}
                                        </view>
                                        <view class="val" v-if="!ahItem.realAmount">审批金额:{{ahItem.poAmount |
                                            cny}}
                                        </view>
                                        <view class="val" v-else>实发金额:{{ahItem.realAmount | cny}}</view>
                                    </view>
                                    <view style="width: 25%;float: right">
                                        <view
                                            :style="{'background-image': 'url(' + $imageAssets.dailyStamp + ')'}"
                                            style="width: 60px;height: 60px;background-repeat: no-repeat;background-size: 100% 100%;
                border-radius: 10px 10px 0 0;background-color: white;text-align: center">
                                            <view
                                                style="font-size: 10px;line-height: 62px;transform: rotate(-15deg);color: #FF5A5A">
                                                今日新增
                                            </view>
                                        </view>
                                    </view>
                                </view>
                                <view class="sum">
                                    <view class="title">{{ahItem.startTime |date('YYYY-MM-DD HH:mm')}}&nbsp;~&nbsp;{{ahItem.endTime
                                        |date('YYYY-MM-DD HH:mm')}}
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>

                </view>

                <!-- 活动金额情况-->
                <view class="daily-line-box quota-item"
                      v-if="dItem.name=='marketActActualSalaryData' && pItem.actDailyReport.marketActActualSalaryData">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'活动金额情况：'+(dailyType === 'WEEK' ? '本周' : '本月')+'活动审批'+(pItem.actDailyReport.marketActHappenedTodayData.approveAmount || 0)+'元,实发'+(pItem.actDailyReport.marketActActualSalaryData.realAmount || 0)+'元'"></line-title>
                        <view class="right-icon-operate"
                              :style="{color: controlAndRemarksObj[pItem.postnId+'_unfoldActualSalary'] ? '#2F69F8': '#262626'}"
                              @tap="switchState(pItem.postnId+'_unfoldActualSalary')">
                            <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_unfoldActualSalary']"
                                       icon="icon-shouqi"/>
                            <link-icon v-else
                                       icon="icon-zhankai"/>
                        </view>
                    </view>
                    <view class="visit-box"
                          v-show="controlAndRemarksObj[pItem.postnId+'_unfoldActualSalary']"
                          v-for="(asItem, asIndex) in pItem.actDailyReport.marketActActualSalaryData.actList"
                          :key="asIndex">
                        <view class="market-activity-list-item">
                            <view class="media-list">
                                <view class="media-top">
                                    <view class="num-view">
                                        <view class="num">{{asItem.actNum}}</view>
                                    </view>
                                </view>
                            </view>
                            <view class="content-middle-line">
                                <view style="width: 100%">
                                    <view class="data" style="width: 75%;float: left">
                                        <view class="name">{{asItem.costType}}·{{asItem.actType |
                                            lov('MC_TYPE')}}
                                        </view>
                                        <view class="title">{{asItem.status |
                                            lov('MC_STATUS')}}·{{asItem.approveStatus | lov('APRO_STATUS')}}
                                        </view>
                                        <view class="val">实发金额:{{asItem.realAmount | cny}}</view>
                                    </view>
                                    <view style="width: 25%;float: right">
                                        <view
                                            :style="{'background-image': 'url(' + $imageAssets.dailyStamp + ')'}"
                                            style="width: 60px;height: 60px;background-repeat: no-repeat;background-size: 100% 100%;
                border-radius: 10px 10px 0 0;background-color: white;text-align: center">
                                            <view
                                                style="font-size: 10px;line-height: 62px;transform: rotate(-15deg);color: #FF5A5A">
                                                今日新增
                                            </view>
                                        </view>
                                    </view>
                                </view>
                                <view class="sum">
                                    <view class="title">{{asItem.startTime |date('YYYY-MM-DD HH:mm')}}&nbsp;~&nbsp;{{asItem.endTime |date('YYYY-MM-DD HH:mm')}}
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            </view>
            <view style=" display: flex;justify-content: space-between; margin-top: 12px;align-items: flex-end;">
            <view class="daily-data-type">
                <view>任务指标达成情况</view>
            </view>
            <view class="right-icon-operate"
                  :style="{color: controlAndRemarksObj[pItem.postnId+'_unfoldTask'] ? '#2F69F8': '#262626'}"
                  @tap="switchState(pItem.postnId+'_unfoldTask')">
                <link-icon v-if="!controlAndRemarksObj[pItem.postnId+'_unfoldTask']"
                           icon="icon-shouqi"/>
                <link-icon v-else icon="icon-zhankai"/>
            </view>
            </view>
            <view>
                <!--拜访-->
                <view class="daily-line-box quota-item" v-show="controlAndRemarksObj[pItem.postnId+'_unfoldTask']">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'终端拜访情况：计划拜访终端'+ (pItem.terminalVisit || 0)+'家'+ ' '+'达成'+(pItem.terminalVisitAchieveValue || 0)+'家'"></line-title>
                    </view>
                </view>
                <!--终端新开-->
                <view class="daily-line-box quota-item" v-show="controlAndRemarksObj[pItem.postnId+'_unfoldTask']">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'终端新开情况：计划新开终端'+ (pItem.terminalReg || 0)+'家'+ ' '+'实际新开'+(pItem.terminalRegAchieveValue || 0)+'家'"></line-title>
                    </view>
                </view>
                <!--活动场次-->
                <view class="daily-line-box quota-item" v-show="controlAndRemarksObj[pItem.postnId+'_unfoldTask']">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'活动执行情况：计划实发活动'+ (pItem.actNum || 0)+'场'+ ' '+'达成'+(pItem.actNumAchieveValue || 0)+'场'"></line-title>
                    </view>
                </view>
                <!--终端客户达成等级-->
                <view class="daily-line-box quota-item" v-show="controlAndRemarksObj[pItem.postnId+'_unfoldTask']">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'终端客户等级达成情况:'"
                                    :line1="'计划达成：1A'+ (pItem.achieveLevel1A || 0)+'家 '+'2A'+(pItem.achieveLevel2A || 0)+'家 '
                                    +'3A'+(pItem.achieveLevel3A || 0)+'家 '+'4A'+(pItem.achieveLevel4A || 0)+'家 '+'5A'+(pItem.achieveLevel5A || 0)+'家'"
                                    :line2="'实际达成：1A'+(pItem.achieveLevel1AAchieveValue || 0)+'家 '+'2A'+(pItem.achieveLevel2AAchieveValue || 0)+'家 '+
                        '3A'+(pItem.achieveLevel3AAchieveValue || 0)+'家 '+'4A'+(pItem.achieveLevel4AAchieveValue || 0)+'家 '+'5A'+(pItem.achieveLevel5AAchieveValue || 0)+'家 '">
                        </line-title>
                    </view>

                </view>
                <!--进货-->
                <view class="daily-line-box quota-item" v-show="controlAndRemarksObj[pItem.postnId+'_unfoldTask']">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'终端进货情况：计划扫码入库'+ (pItem.actAmount || 0)+'件产品'+ ' '+'实际达成'+(pItem.actAmountAchieveValue|| 0)+'件'"></line-title>
                    </view>
                </view>
                <!--活动金额-->
                <view class="daily-line-box quota-item" v-show="controlAndRemarksObj[pItem.postnId+'_unfoldTask']">
                    <view class="daily-line-title">
                        <line-title :hiddenLeft="true"
                                    :title="'活动金额情况：计划实发金额'+ (pItem.actAmount || 0)+'元'+ ' '+'实际实发金额'+(pItem.actAmountAchieveValue|| 0)+'元'"></line-title>
                    </view>
                </view>
            </view>




        </view>
    </view>
</template>

<script>

import LineTitle from "../../../echart/lzlj/components/line-title";
import timeDuring from "../../../terminal/components/time-during";
import addressTip from "../../../terminal/components/address-tip";
/**
 * 周报和月报用同一套内容
 * @file
 * <AUTHOR>
 * @date 2023/4/27
 */
export default {
    name: 'weekly-report-data',

    components: {LineTitle,timeDuring, addressTip},

    mixins: [],

    props: {
        postnDailyReportList: {
            type: Array
        },
        controlAndRemarksObj: {
            type: Object
        },
        dailyType: {
            type: String
        },
        dailyTemp: {
            type: Array
        }
    },

    data() {
        return {

        }
    },

    computed: {
    },

    watch: {},

    created() {
        console.log(this.dailyTemp)
    },

    mounted() {
    },

    destroyed() {
    },

    methods: {
        switchState(type) {
            this.$emit('switchState', type)
        }
    }

}
</script>

<style lang="scss">
.weekly-report-data {
    .consumer-title {
        margin: 30px 20px 2px 20px;
        color:red;
        font-size: 28px;
    }
}
</style>
