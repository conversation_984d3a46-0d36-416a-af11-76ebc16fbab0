<template>
    <view class="linked-tab-content">
        <view :class="['tab-container', { 'tab-hidden': !showTabs }]" :style="{ top: `${stickyHeight}px` }">
            <view class="data-title-row">
                <view class="messenger-icon">
                    <link-icon icon="icon-jinridongxiao" />
                </view>
                今日达成数据
            </view>
            <scroll-view :scroll-x="true" class="tab-scroll" :scroll-left="scrollLeft">
                <view class="tab-list">
                    <view v-for="(item, index) in tabs" :key="index"
                        :class="['tab-item', { active: currentTabIndex === index }]" @tap="handleTabClick(index)">
                        {{ item.title }}
                    </view>
                </view>
            </scroll-view>
        </view>

        <view class="content-container">
            <view v-for="(mdl, index) in modules" :key="index" :id="`module-${index}`" class="module-item">
                <daily-report-data-module :title="mdl.title" :config="mdl.config" :formData="formData"
                    :readonly="readonly" :moduleDataKey="mdl.dataKey" @form-change="$emit('form-change', $event)"
                    :statsData="statsData" @resize="reCalculate" :queryParams="queryParams" />
            </view>
        </view>
    </view>
</template>

<script>
import Taro from '@tarojs/taro'
import DailyReportDataModule from './daily-report-data-module'
import { ComponentUtils } from 'link-taro-component'

export default {
    name: 'linked-tab-content',
    components: {
        DailyReportDataModule
    },
    props: {
        modules: {
            type: Array,
            default: () => []
        },
        formData: {
            type: Object,
            default: () => ({})
        },
        readonly: {
            type: Boolean,
            default: false
        },
        statsData: {
            type: Object,
            default: () => ({})
        },
        queryParams: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            currentTabIndex: 0,
            showTabs: true,
            scrollLeft: 0,
            modulePositions: [],
            observerStarted: false,
            pageScrollTop: 0,
            isScrolling: false,
            headerHeight: 152,
            stickyHeight: 70,
        }
    },
    computed: {
        tabs() {
            return this.modules.map(({ title }) => ({ title }))
        }
    },
    mounted() {
        setTimeout(() => {
            this.calculateModulePositions()
            this.initScrollListener()
        }, 200)
    },

    beforeDestroy() {
        Taro.eventCenter.off('onPageScroll')
    },

    methods: {

        // 验证必填表单项
        validate() {
            const requiredFields = []
            this.modules.forEach(module => {
                const forms = module.config.filter(item => item.type === 'form')
                forms.forEach(form => {
                    form.formItems.forEach(item => {
                        const { required, flagField } = item
                        if (required && (!flagField || this.formData[flagField] === 'Y')) {
                            requiredFields.push({
                                field: item.field,
                                label: item.label,
                                moduleName: module.title
                            })
                        }
                    })
                })
            })
            for (const field of requiredFields) {
                if (!this.formData[field.field]) {
                    throw new Error(`请填写${field.moduleName}的${field.label}`)
                }
            }
        },
        // 初始化页面滚动监听器
        initScrollListener() {
            if (this.observerStarted) return

            Taro.eventCenter.on('onPageScroll', (e) => {
                const { scrollTop } = e
                this.pageScrollTop = scrollTop
                if (this.isScrolling) return
                this.handleScroll(scrollTop)
                this.updateActiveTabByScroll(scrollTop)
            })

            this.observerStarted = true
        },
        // 处理页面滚动，控制标签栏显示/隐藏
        handleScroll(scrollTop) {
            const lastModulePosition = this.modulePositions[this.modulePositions.length - 1]
            if (lastModulePosition) {
                const bottomPosition = lastModulePosition.top + lastModulePosition.height - this.stickyHeight
                this.showTabs = scrollTop < bottomPosition
            }
        },
        // 计算每个模块的位置信息
        calculateModulePositions() {
            const pageScrollTop = this.pageScrollTop || 0

            const query = Taro.createSelectorQuery()
            this.modules.forEach((_, index) => {
                query.select(`#module-${index}`).boundingClientRect()
            })

            query.exec(rects => {
                this.modulePositions = rects
                    .filter(rect => rect)
                    .map(rect => ({
                        ...rect,
                        top: rect.top + pageScrollTop,
                    }))
            })
        },
        // 更新激活标签并触发滚动
        updateActiveTabByScroll(scrollTop) {
            const actualScrollTop = scrollTop + this.headerHeight

            for (let i = 0; i < this.modulePositions.length; i++) {
                const currentModule = this.modulePositions[i]
                const nextModule = this.modulePositions[i + 1]

                if (!currentModule) continue

                if (!nextModule) {
                    if (actualScrollTop >= currentModule.top) {
                        this.updateActiveTab(i)
                        break
                    }
                } else if (actualScrollTop >= currentModule.top && actualScrollTop < nextModule.top) {
                    this.updateActiveTab(i)
                    break
                }
            }
        },
        // 更新激活标签并触发滚动到视图
        updateActiveTab(index) {
            if (this.currentTabIndex === index) return
            this.currentTabIndex = index

            this.scrollTabIntoView(index)
        },
        // 将选中的标签滚动到可视区域
        scrollTabIntoView(index) {
            const query = Taro.createSelectorQuery()
            query.select('.tab-scroll')
                .boundingClientRect()
                .selectAll('.tab-item')
                .boundingClientRect()
                .exec(res => {
                    if (!res[1] || !res[1][index] || !res[0]) return

                    const container = res[0]
                    const tabs = res[1]
                    const targetTab = tabs[index]

                    // 检查目标tab是否在可视区域内
                    const isVisible = targetTab.left >= container.left &&
                        targetTab.right <= container.right

                    // 只有当tab不在可视区域内才滚动
                    if (!isVisible) {
                        let scrollLeft = 0
                        for (let i = 0; i < index; i++) {
                            scrollLeft += tabs[i].width
                        }
                        this.scrollLeft = scrollLeft
                    }
                })
        },
        // 标签点击滚动到对应模块
        handleTabClick(index) {
            if (!this.modulePositions[index]) return
            this.isScrolling = true

            const targetScrollTop = this.modulePositions[index].top - this.headerHeight

            Taro.pageScrollTo({
                scrollTop: targetScrollTop,
                duration: 300
            })

            setTimeout(() => {
                this.isScrolling = false
            }, 350)

            this.currentTabIndex = index
        },
        // 重新计算位置
        reCalculate: ComponentUtils.debounce(function() {
            this.calculateModulePositions()
        }, 300)
    }
}
</script>

<style lang="scss">
.linked-tab-content {
    .data-title-row {
        padding: 0 24rpx;
        margin-bottom: 24px;
        font-size: 28px;
        @include flex;
        align-items: center;

        .messenger-icon {
            width: 44px;
            height: 44px;
            @include flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(45deg, #00B2FF 0%, #006AFF 100%);
            border-radius: 10px;
            position: relative;
            margin-right: 10px;

            .icon-jinridongxiao {
                color: white;
            }
        }
    }

    .tab-container {
        position: sticky;
        left: 0;
        right: 0;
        z-index: 100;
        background: #f2f2f2;
        padding: 14px 0;
        transition: transform 0.3s ease;
        height: 82PX;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        &.tab-hidden {
            transform: translateY(-100%);
        }
    }

    .tab-scroll {
        white-space: nowrap;
        width: 100%;
    }

    .tab-list {
        display: inline-flex;
        padding: 0 24px;
    }

    .tab-item {
        position: relative;
        padding: 8px 24px;
        font-size: 28px;
        color: #666;
        border: 2px solid #DDDDDD;
        border-radius: 32px;
        margin-right: 16px;

        &:last-child {
            margin-right: 0;
        }

        &.active {
            color: #3F66EF;
            font-weight: 500;
            border-color: #3F66EF;

            &::after {
                display: none;
            }
        }
    }

    .module-item {
        margin-bottom: 16px;
    }
}
</style>
