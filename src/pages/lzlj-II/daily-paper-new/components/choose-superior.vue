<template>
    <view class="choose-superior">
        <link-dialog ref="empPostnBottom" position="bottom" height="85vh" class="dialog-bottom" noPadding
            v-model="showDialog" @hide="closeDialog">
            <view class="model-title">
                <view class="iconfont icon-close" @tap="closeDialog"></view>
                <view class="title">请选择上级</view>
            </view>

            <view class="dialog-content" style="height: calc(100% - 44px)">
                <template v-if="defaultSupData.id">
                    <view @tap="tempSupInfo(defaultSupData)" class="default-superior">
                        <link-radio-group v-model="tempSupId">
                            <item :arrow="false" :key="defaultSupData.id" title="默认上级" :data="defaultSupData">
                                <link-checkbox :val="defaultSupData.id" slot="thumb" toggleOnClickItem
                                    @tap="tempSupInfo(defaultSupData)" />
                                <view class="superior-name">{{ defaultSupData.firstName }}</view>
                            </item>
                        </link-radio-group>
                    </view>
                    <view class="divider"></view>
                </template>

                <scroll-view :scroll-y="true" :style="{ 'height': calcScrollViewHeight }" :scroll-top="scrollTop"
                    @scrolltolower="handleScrollToLower">
                    <link-auto-list :option="supList" hideCreateButton :scrollContent="false"
                        :searchInputBinding="{ props: { placeholder: '姓名/工号/职位' } }">
                        <template slot-scope="{data,index}">
                            <view @tap="tempSupInfo(data)">
                                <link-radio-group v-model="tempSupId">
                                    <item :arrow="false" :key="data.id" :title="data.firstName" :data="data">
                                        <link-checkbox :val="data.id" slot="thumb" toggleOnClickItem
                                            @tap="tempSupInfo(data)" />
                                        <view class="checkbox-right">
                                            <view style="color: #1a1a1d">{{ data.username }}</view>
                                            <view style="color: #1a1a1d">{{ data.postnName }}</view>
                                        </view>
                                    </item>
                                </link-radio-group>
                            </view>
                        </template>
                    </link-auto-list>
                </scroll-view>
                <view class="link-dialog-foot-custom">
                    <link-button shadow @tap="submitDialog" label="确定" style="width:100vw" />
                </view>
            </view>
        </link-dialog>
    </view>
</template>

<script>
export default {
    name: 'choose-superior',
    props: {
        show: Boolean,
        defaultSupData: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        const supList = new this.AutoList(this, {
            request: this.fetchSuperiors,
            param: {},
            pageSize: 20,
            loadOnStart: false,
            searchFields: ['firstName', 'username', 'postnName'],
        });
        return {
            showDialog: false,
            tempSupId: null,
            supList,
            selectData: {},
            searchKeyword: '',
            scrollTop: 0,
            isLoading: false
        }
    },
    computed: {
        calcScrollViewHeight() {
            return this.defaultSupData?.id
                ? 'calc(100% - 150px)'
                : 'calc(100% - 95px)'
        }
    },
    watch: {
        show: {
            immediate: true,
            handler(val) {
                this.showDialog = val
                if (val) {
                    this.tempSupInfo(this.defaultSupData)
                    this.supList.methods.reload()
                }
            }
        },
        searchKeyword(newVal, oldVal) {
            if (newVal !== oldVal) {
                this.scrollTop = 1
                this.$nextTick(() => {
                    this.scrollTop = 0
                })
                this.resetSupData()
            }
        }
    },
    methods: {
        handleScrollToLower() {
            if (!this.isLoading && !this.supList.isNoMoreData) {
                this.isLoading = true
                this.loadMore()
            }
        },
        loadMore() {
            // 手动触发加载更多
            if (this.supList && this.supList.handler && this.supList.handler.reachBottom) {
                this.supList.handler.reachBottom()
                    .finally(() => {
                        this.isLoading = false
                    })
            } else {
                this.isLoading = false
            }
        },
        async fetchSuperiors({ param }) {
            try {
                const search = param.filtersRaw?.find(item => item.id.includes('searchValue'))
                const searchKeyword = search?.value || ''
                const hasChange = searchKeyword !== this.searchKeyword
                this.searchKeyword = searchKeyword

                const page = !hasChange && param.page ? param.page : 1
                const pageSize = param.pageSize || 20
                this.supList.isNoMoreData = false

                const { result, total } = await this.$http.post('action/link/newDailyReport/querySuperiors', {
                    attr1: searchKeyword,
                    page,
                    pageFlag: true,
                    totalFlag: true,
                    rows: pageSize
                })

                // 如果存在搜索值但没有查到数据，则调用接口再次查询
                if (searchKeyword && (!result || result.length === 0)) {
                    return await this.searchFromOrg(searchKeyword, page, pageSize)
                }

                return {
                    rows: result || [],
                    page,
                    total: total || 0
                }
            } catch (error) {
                console.error('获取上级列表失败', error)
                return { rows: [], page: param.page || 1, total: 0 }
            }
        },
        async searchFromOrg(searchValue, page, pageSize = 20) {
            try {
                const { result, total } = await this.$http.post('action/link/newDailyReport/queryOrgUser', {
                    attr1: searchValue,
                    page,
                    pageFlag: true,
                    totalFlag: true,
                    rows: pageSize
                })

                return {
                    rows: result || [],
                    page: page,
                    total: total || 0
                }
            } catch (error) {
                console.error('搜索失败', error)
                return { rows: [], page: page, total: 0 }
            }
        },
        resetSupData() {
            const defaultId = this.defaultSupData.id
            if (defaultId && this.selectData.id === defaultId) return
            this.selectData = {}
            this.tempSupId = null
        },
        tempSupInfo(data) {
            if(!data.id) return
            this.selectData = data
            this.tempSupId = data.id
        },
        submitDialog() {
            if (!this.selectData?.id) {
                this.$message.error('请选择上级')
                return
            }

            this.$emit('choose', this.selectData)
            this.$emit('update:show', false)
        },
        closeDialog() {
            this.$emit('update:show', false)
        }
    },
}
</script>

<style lang="scss">
.choose-superior {
    .link-dialog-foot-custom {
        width: auto !important;
    }

    .link-dialog-body {
        position: relative;
    }

    .link-auto-list .link-auto-list-top-bar {
        border: none;
    }

    .link-item .link-item-body-right {
        margin: 0 24px;
        flex: 3 !important;
        align-items: baseline !important;
    }

    .link-radio-group {
        .link-item {
            padding: 24px;

            .link-item-icon {
                display: none;
            }
        }

        .link-item-active {
            background-color: #f6f6f6;
        }
    }

    .list-item {
        flex: 1;
    }

    .link-radio-group .link-item:active,
    .link-item-active {
        background-color: #f6f6f6;
    }

    .dialog-bottom {
        .dialog-content {
            padding: 0 20px;
            position: relative;
        }

        .model-title {
            .title {
                font-size: 32px;
                color: #262626;
                letter-spacing: 0;
                text-align: center;
                line-height: 96px;
                height: 96px;
                width: 90%;
                padding-left: 0 !important;
                margin-right: 80px;
                margin-left: 10vw;
            }

            .icon-close {
                color: #BFBFBF;
                font-size: 48px;
                line-height: 96px;
                height: 96px;
                margin-right: 30px;
                float: right;
            }
        }
    }

    .default-superior {
        .link-radio-group {
            .link-item {
                padding: 0 24px;
            }
        }
    }

    .superior-name {
        font-size: 30px;
        color: #1a1a1d;
        font-weight: 500;
    }

    .divider {
        height: 1px;
        background-color: #f2f2f2;
        margin: 8px 0;
    }

    .error-message {
        color: #ff4d4f;
        font-size: 24px;
        margin-top: 10px;
    }

    .error-message-bottom {
        color: #ff4d4f;
        font-size: 24px;
        margin-bottom: 10px;
        text-align: center;
    }
}
</style>
