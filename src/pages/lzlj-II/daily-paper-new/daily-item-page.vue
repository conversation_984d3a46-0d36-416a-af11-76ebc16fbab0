<template>
    <link-page class="daily-item-page">
        <view class="daily-list-rows">
            <view class="daily-item">
                <view class="daily-rows">
                    <view class="daily-date"><label class="dot-top"></label>{{$utils.dateFormat(dailyHeaderData.dailyDate)}}</view>
                    <view class="daily-person">提报人<label
                        class="daily-person-name">{{dailyHeaderData.createdName}}</label>
                    </view>
                </view>
                <view class="daily-row-2">
                    <template v-if="dailyType === 'DAY'">
                        <daily-report-data
                            :postn-daily-report-list="previewData"
                            :control-and-remarks-obj="controlAndRemarksObj"
                            :daily-temp="dailyTemp"
                            @switchState="switchState" />
                    </template>
                    <template v-else>
                        <!--周报和月报共用一套-->
                        <weekly-report-data
                            :postn-daily-report-list="previewData"
                            :control-and-remarks-obj="controlAndRemarksObj"
                            :daily-type="dailyType"
                            :daily-temp="dailyTemp"
                            @switchState="switchState"/>
                    </template>
                </view>
            </view>
        </view>
        <link-form class="list-box-form" :value="dailyHeaderData">
            <!--模板数据-->
            <link-form-item v-for="(dItem, dIndex) in dailyTemp"
                            :key="dIndex"
                            v-if="dItem.type=='form'"
                            :label="dItem.label"
                            :required="dItem.required"
                            :vertical="dItem.ctrlCode !== 'link-lov'">
                <link-lov v-if="dItem.ctrlCode === 'link-lov'" :type="dItem.lovType" v-model="dailyHeaderData[dItem.name]" readonly/>
                <link-textarea v-else :placeholder="'请输入'+dItem.label" v-model="dailyHeaderData[dItem.name]" readonly/>
            </link-form-item>
            <view class="door-head"><text class="asterisk"></text>领导点评反馈</view>
            <view class="scan-list" v-for="(data,index) in comments">
                <view class="scan-item">
                <view class="list-cell">
                    <view class="info">
                        <textarea class="info-textarea" v-model="data.comment"  maxlength="500" disabled></textarea>
                    </view>
                    <view class="bottle-info">
                        <view class="bottle-row">
                            <view class="font"><text class="textTitlec">点评时间:</text><text class="textVal" >{{data.created | date('YYYY-MM-DD')}}</text></view>
                            <view class="font"><text class="textTitlec">点评人:</text><text class="textVal" >{{data.userName}}</text></view>
                        </view>
                    </view>
                </view>
                </view>
            </view>
            <view class="door-headTitle">
                <text class="asterisk"></text>
                附件
            </view>
            <view class="img-box">
                <lnk-img :parentId="dailyHeaderData.id"
                         :moduleType="moduleType"
                         :delFlag="false"
                         :newFlag="false"
                ></lnk-img>
            </view>
        </link-form>

        <link-dialog ref="commentDialog" width="">
            <link-form>
                <link-form-item label="点评日期" >
                    <link-date v-model="commentForm.created" disabled/>
                </link-form-item>
                <link-form-item label="点评人姓名" >
                    <link-input v-model="commentForm.userName" disabled/>
                </link-form-item>
                <link-form-item label="点评内容" vertical>
                    <link-textarea v-model="commentForm.comment" :nativeProps="{maxlength: 500}" useNative/>
                </link-form-item>
            </link-form>
            <link-button slot="foot" @tap="insertComment" style="background-color:#2f69f8!important;color: #F2F3F6;">确定</link-button>
        </link-dialog>

        <link-sticky>
            <link-button block size="large" @tap="copy" autoLoading :shadow="true" v-if="!showBtn">复制</link-button>
            <link-button block size="large" @tap="dialogShow" autoLoading :shadow="true" v-if="showBtn">点评</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    import LineTitle from "../../lzlj/components/line-title";
    import timeDuring from '../../terminal/components/time-during';
    import addressTip from '../../terminal/components/address-tip';
    import LnkImg from "../../core/lnk-img/lnk-img";
    import DailyReportData from "./components/daily-report-data";
    import WeeklyReportData from "./components/weekly-report-data";
    import Taro from "@tarojs/taro";
    import {DateService} from "link-taro-component";
    export default {
        name: "daily-item-page",
        components: {DailyReportData, WeeklyReportData, LnkImg, LineTitle, timeDuring, addressTip},
        data() {
            const dailyHeaderData = this.pageParam.data;//日报头对象
            const dailyTemp = this.pageParam.dailyTemp;//日报头配置的模板字段
            const previewData = [];//日报详细信息
            const controlAndRemarksObj = {};
            const dailyType = this.pageParam.dailyType || 'DAY'
            return {
                showBtn:false,
                insertFlag:true,
                commentForm: {},
                controlAndRemarksObj,
                previewData,
                moduleType: 'daily',              // 日报图片类型
                dailyHeaderData,//日报头信息
                dailyTemp,
                dailyType,
                comments:[],
            }
        },
        async created() {
            console.log('this.pageParam.dailyTemp',this.pageParam.dailyTemp)
            await this.queryDailyReportDetail();
            await this.getComments();
            this.buttonShow();
        },
        mounted() {
            let title = ''
            if (this.dailyType === 'DAY') {
                title = '日报详情'
            } else if (this.dailyType === 'WEEK') {
                title = '周报详情'
            } else if (this.dailyType === 'MONTH') {
                title = '月报详情'
            }
            this.$taro.setNavigationBarTitle({title})
        },
        methods: {
            buttonShow(){
                const positionTypeList=['Salesman','CustServiceSpecialist','SalesTeamLeader']
                if(this.dailyHeaderData.createdBy==Taro.getStorageSync('token').result.id||positionTypeList.includes(Taro.getStorageSync('token').result.positionType)){
                    this.showBtn=false
                }else{
                    this.showBtn=true
                }
            },
            async  dialogShow(){
                this.commentForm={
                    userName: Taro.getStorageSync('token').result.firstName,
                    created: DateService.format(new Date(), 'YYYY-MM-DD'),
                    createdBy: Taro.getStorageSync('token').result.userId
                }
                const params={filtersRaw: [{id: 'createdBy', property: 'createdBy', value:  Taro.getStorageSync('token').result.id}, {id: 'headerId', property: 'headerId', value: this.pageParam.data.id}]}
                const data= await this.$http.post('action/link/dailyComments/queryByExamplePage', params)
              if(data.rows.length>0){
                  this.commentForm=data.rows[0];
                  this.insertFlag = false;
              }else{
                  this.commentForm.comment=null;
                  this.insertFlag = true;
              }
               this.$refs.commentDialog.show()
            },
            async insertComment(){
                const url = this.insertFlag ? 'action/link/dailyComments/insert' : 'action/link/dailyComments/update';
                if(this.commentForm.comment==null || this.commentForm.comment.length<=0){
                    this.$message.warn('请输入你的评价');
                }else{
                    this.commentForm.headerId = this.pageParam.data.id;
                    const data = await this.$http.post(url, this.commentForm);
                    if(data.success){
                        this.$message.success('点评成功！');
                        this.$refs.commentDialog.hide();
                        await this.getComments();
                    }else{
                        this.$message.error('点评失败！');
                        this.$refs.commentDialog.hide();
                    }
                }

            },
            /**
             * 切换展开状态
             * <AUTHOR>
             * @date 2021-08-30
             * */
            switchState(type) {
                this.controlAndRemarksObj[type] ? this.$set(this.controlAndRemarksObj, type, false) : this.$set(this.controlAndRemarksObj, type, true);
            },
            /**
             * 日报详细信息
             * <AUTHOR>
             * @date 2021-08-30
             */
            async queryDailyReportDetail() {
                const that = this;
                this.$utils.showLoading("查询日报数据...");
                const data = await this.$http.post('action/link/dailyReport/queryDailyReportDetail', this.dailyHeaderData, {
                    handleFailed: (error) => {
                        that.$utils.hideLoading();
                    }
                });

                this.$utils.hideLoading();
                data.result.forEach((item) => {
                    if(!this.$utils.isEmpty(item.terminalDailyReport)){
                        item.terminalDailyReport = JSON.parse(item.terminalDailyReport);
                        if (item.terminalDailyReport.quotaData) {
                            if(this.$utils.isEmpty(item.terminalDailyReport.quotaData['title'])){
                                if (item.terminalDailyReport.quotaData.quotaDeNum!==0 && item.terminalDailyReport.quotaData.quotaInTotal !== 0) {
                                    let title = `配额申请：共为${item.terminalDailyReport.quotaData.acctInNum}家终端申请${item.terminalDailyReport.quotaData.quotaInTotal}件产品, 共为${item.terminalDailyReport.quotaData.acctDeNum}家终端调减${item.terminalDailyReport.quotaData.quotaDeNum}件产品`
                                    this.$set(item.terminalDailyReport.quotaData, 'title', title)
                                } else {
                                    let title = `配额申请：共为${item.terminalDailyReport.quotaData.quotaDeNum !== 0?item.terminalDailyReport.quotaData.acctDeNum: item.terminalDailyReport.quotaData.acctInNum}家终端${item.terminalDailyReport.quotaData.quotaDeNum !== 0?'调减'+item.terminalDailyReport.quotaData.quotaDeNum:'申请'+item.terminalDailyReport.quotaData.quotaInTotal}件产品`
                                    this.$set(item.terminalDailyReport.quotaData, 'title', title)
                                }
                            }
                        }
                    }
                    if(!this.$utils.isEmpty(item.actDailyReport)){
                        item.actDailyReport = JSON.parse(item.actDailyReport)
                    }
                    if(!this.$utils.isEmpty(item.consumerDailyReport)){
                        item.consumerDailyReport = JSON.parse(item.consumerDailyReport)
                    }
                });
                this.previewData = data.result;
            },
            getDailyCopyContent() {
                let data = ''
                this.previewData.forEach((item) => {
                    let content = `职位： ${item.postnName} \r\n`
                    const terminalDailyReport = item.terminalDailyReport
                    if (!!terminalDailyReport) {
                        const {
                            visitData,
                            quotaData,
                            orderData,
                            newTerminalData,
                            terminalPurchaseData,
                        } = terminalDailyReport
                        if (!!visitData) {
                            content += '终端拜访情况：本日完成拜访'+ visitData.total+'次，拜访终端'+visitData.acctNum+'家'
                            content += `\r\n`
                        }
                        if (!!quotaData) {
                            content += quotaData.title
                            content += `\r\n`
                        }
                        if (!!orderData) {
                            content += '终端下单：共为'+orderData.acctNum+'家终端下单' + orderData.total+'件产品'
                            content += `\r\n`
                        }
                        if (!!newTerminalData) {
                            content += '终端新开情况：本日新开'+(newTerminalData.acctNum || 0)+'家终端'
                            content += `\r\n`
                        }
                        if (!!terminalPurchaseData) {
                            content += '终端进货情况：为'+(terminalPurchaseData.accntNum||0)+'家终端下单' + (terminalPurchaseData.total||0)+'件产品'
                            content += `\r\n`
                        }
                    }
                    const actDailyReport = item.actDailyReport
                    if (!!actDailyReport) {
                        const {
                            marketActHappenedTodayData,
                            marketActActualSalaryData,
                            marketActToPerformData,
                            marketActNotActualSalaryData
                        } = actDailyReport
                        if (!!marketActHappenedTodayData) {
                            content += '活动执行情况：今日新申请'+(marketActHappenedTodayData.applyNum || 0)+'场活动,实发'+(marketActActualSalaryData.actualNum || 0)+'场活动'
                            content += `\r\n`
                        }
                        if (!!marketActActualSalaryData) {
                            content += '活动金额情况：今日活动审批'+(marketActHappenedTodayData.approveAmount || 0)+'元,实发'+(marketActActualSalaryData.realAmount || 0)+'元'
                            content += `\r\n`
                        }
                        if (!!marketActToPerformData) {
                            content += '待执行：共'+marketActToPerformData.toPerformActNum+'场,申请金额' +marketActToPerformData.applyAmount
                            content += `\r\n`
                        }
                        if (!!marketActNotActualSalaryData) {
                            content += '尚未实发：共'+marketActNotActualSalaryData.notActualActNum+'场,审批金额' +marketActNotActualSalaryData.approveAmount
                            content += `\r\n`
                        }
                    }
                    data += '\r\n'
                    data += content
                })
                data += '\r\n'
                this.dailyTemp.forEach((template)=>{
                    let content = ''
                    const {label,name } = template
                    if (this.dailyHeaderData[name]) {
                        content += `${label}： ${this.dailyHeaderData[name]}`
                        content += '\r\n'
                    }
                    data += content
                })
                return data
            },
            getWeeklyCopyContent() {
                let data = ''
                // 复制指标内容
                this.previewData.forEach((item) => {
                    console.log('item', item);
                    let content = `职位： ${item.postnName} \r\n`
                    const terminalDailyReport = item.terminalDailyReport
                    if (!!terminalDailyReport) {
                        const {
                            visitData,
                            quotaData,
                            orderData,
                            newTerminalData,
                            terminalPurchaseData,
                            terminalLevelAchievement
                        } = terminalDailyReport
                        if (!!visitData) {
                            content += '终端拜访情况：拜访终端'+ (visitData.total || 0)+'家'
                            content += `\r\n`
                        }
                        if (!!quotaData) {
                            content += quotaData.title
                            content += `\r\n`
                        }
                        if (!!orderData) {
                            content += '终端下单：下单终端'+(orderData.total || 0)+'家'
                            content += `\r\n`
                        }
                        if (!!newTerminalData) {
                            content += '终端新开情况：新开终端'+(newTerminalData.acctNum || 0)+'家'
                            content += `\r\n`
                        }
                        if (!!terminalPurchaseData) {
                            content += '终端进货情况：跟进终端进货'+(terminalPurchaseData.total||0)+'件'
                            content += `\r\n`
                        }
                        if (!!terminalLevelAchievement) {
                            content += `终端客户等级达成情况: 达成${(terminalLevelAchievement.total || 0)}家`
                            content += `\r\n`
                        }
                    }
                    const actDailyReport = item.actDailyReport
                    if (!!actDailyReport) {
                        const {
                            marketActHappenedTodayData,
                            marketActActualSalaryData,
                        } = actDailyReport
                        if (!!marketActHappenedTodayData) {
                            content += '活动执行情况：今日新申请'+(marketActHappenedTodayData.applyNum || 0)+'场活动,实发'+(marketActActualSalaryData.actualNum || 0)+'场活动'
                            content += `\r\n`
                        }
                        if (!!marketActActualSalaryData) {
                            content += '活动金额情况：今日活动审批'+(marketActHappenedTodayData.approveAmount || 0)+'元,实发'+(marketActActualSalaryData.realAmount || 0)+'元'
                            content += `\r\n`
                        }
                    }
                    data += content
                    data += '\r\n'
                })
                // 复制表单内容
                this.dailyTemp.forEach((template)=>{
                    let content = ''
                    const {label,name } = template
                    if (this.dailyHeaderData[name]) {
                        content += `${label}： ${this.dailyHeaderData[name]}`
                        content += '\r\n'
                    }
                    data += content
                })
                return data
            },
            copy(){
                if (this.dailyType === 'DAY') {
                     this.copyDailyDeport()
                } else {
                    this.copyWeeklyDeport()
                }
            },
            copyDailyDeport() {
                const that = this
                let data = this.getDailyCopyContent()
                this.$taro.setClipboardData({
                    data,
                    success(){
                        that.$message.success('已复制到剪贴板!')
                    }
                })
            },
            copyWeeklyDeport() {
                const that = this
                let data = this.getWeeklyCopyContent()
                this.$taro.setClipboardData({
                    data,
                    success(){
                        that.$message.success('已复制到剪贴板!')
                    }
                })
            },
            /**
             * 获取领导点评
             * <AUTHOR>
             * @date 2023-05-17
             * */
            async  getComments(){
                const data = await this.$http.post('action/link/dailyComments/queryByExamplePage',{
                    filtersRaw: [{id:'headerId',property:'headerId',value:this.pageParam.data.id},]
                });
                this.comments=data.rows
                console.log(data.result)
            }
        }
    }
</script>

<style lang="scss">
    @import "../../../styles/list-card";
    .daily-item-page {
        .daily-list-rows {
            background: #FFFFFF;
            border-radius: 16px;
            /*width: 702px;*/
            margin: auto;
            padding: 0;
            /*deep*/
            .link-item .link-item-icon {
                width: 0;
            }

            .daily-item {
                padding: 24px;

                .daily-rows {
                    width: 100%;
                    @include flex;
                    @include space-between;

                    .daily-date {
                        @include flex;
                        font-size: 32px;
                        color: #262626;
                        font-weight: bold;

                        .dot-top {
                            width: 12px;
                            height: 12px;
                            box-shadow: 0 0 12px 12px #c8dded;
                            background-color: #2F69F8;
                            border-radius: 50%;
                            margin: 14px 20px;
                        }
                    }

                }

                .daily-row-2 {
                    font-family: PingFangSC-Regular;

                    .daily-postn {
                        margin-top: 20px;
                        font-family: PingFangSC-Semibold, serif;
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 2px;
                        line-height: 64px;
                        text-align: center;
                        background: #ebf1fb
                    }

                    .daily-data-type {
                        line-height: 40px;
                        height: 40px;
                        background: #2F69F8;
                        border-radius: 4px 0 16px 4px;
                        font-size: 24px;
                        color: #FFFFFF;
                        width: auto;
                        display: inline-block !important;
                        padding: 0 20px;
                        margin-top: 24px;
                    }

                    .daily-line-box {
                        background-color: #ffffff;

                        .visit-box {
                            margin: 0 24px;
                            border-bottom: 1px solid #f2f2f2;

                            .link-item .link-item-body-left .link-item-title, .link-item .link-item-body-left .link-item-content {
                                color: #595959 !important;
                                min-height: inherit;
                                padding: 0;
                            }

                            .market-activity-list-item {
                                background: #FFFFFF;
                                width: 95%;
                                font-size: 28px;
                                border-radius: 16px;

                                .media-list {
                                    @include media-list;

                                    .media-top {
                                        width: 100%;
                                        @include flex-start-center;
                                        @include space-between;
                                        height: 80px;
                                        line-height: 80px;

                                        .num-view {
                                            background: #A6B4C7;
                                            border-radius: 8px;
                                            line-height: 50px;

                                            .num {
                                                font-size: 28px;
                                                color: #FFFFFF;
                                                letter-spacing: 0;
                                                line-height: 40px;
                                                padding: 2px 8px;
                                            }
                                        }
                                    }
                                }

                                .Inactive {
                                    color: #FF5A5A;
                                }
                            }
                        }

                        /*deep*/
                        .line-title {
                            margin-left: 0 !important;
                        }

                        /*deep*/
                        .link-item {
                            min-height: inherit;
                            padding: 32px 0;
                            margin: 0;
                            color: #595959 !important;
                        }

                        .daily-line-title {
                            display: flex;
                            justify-content: space-between;
                            align-items: flex-end;

                            .right-icon-operate {
                                margin-left: 30px;
                            }

                            .terminal-line-title {
                                margin-left: 0 !important;
                            }
                        }
                    }

                    .quota-item {
                        .terminal-line-title {
                            margin: 32px auto 0 !important;
                        }

                        .link-item {
                            margin: 24px auto 32px !important;
                            padding: 0 !important;
                            position: static;
                        }
                    }
                }
            }
        }
        .list-box-form {
            /*deep*/
            .link-item {
                padding: 32px 24px;
            }

            /*deep*/
            .link-textarea-content {
                color: #8C8C8C;
            }
        }

        .img-box {
            background-color: #ffffff;
            padding-bottom: 20px;
        }

        /*deep*/
        .custom-camera {
            padding-bottom: 20px;
        }

        .door-head {
            font-size: 28px;
            color: #333333;
            padding-left: 40px;
            padding-top: 20px;
            background: #ffffff;

            .asterisk {
                color: #FF5A5A;
                margin-left: -14px;
                padding-right: 2px;

            }
        }
        .door-headTitle {
            font-size: 28px;
            color: #333333;
            padding-left: 40px;
            padding-top: 20px;
            background: #ffffff;
            border-top: 1px solid   #d8d8d8;
            margin-top: 12px;
            .asterisk {
                color: #FF5A5A;
                margin-left: -14px;
                padding-right: 2px;

            }
        }
        .scan-list{
            height: 100%;
            width: 100%;
            background: #FFFFFF;
            .scan-item{
                border-radius: 16px;
                background-color: #FFFFFF;
                width: 100%;
                display: flex;
                flex-direction: column;
                margin-bottom: 12px;
                .list-cell {
                    margin: 24px 24px 24px 6px;
                    width: 97%;
                    .font{
                        color: #000000;
                        font-size: 28px;
                        margin-left: 12px;
                        .textTitle{
                            color:#999999;
                            font-size: 24px;
                        }
                        .textTitlec{
                            color:#666666;
                            font-size: 24px;
                        }
                        .textVal{
                            display: inline-block;
                            margin-left: 20px;
                            color: #333333;
                            font-size: 24px;
                        }
                    }
                    .info{
                        color: #333333;
                        line-height: 40px;
                        font-size: 28px;
                        font-weight: bold;
                        background-color: #fff;
                        padding: 12px;
                        box-sizing: border-box;
                        border: solid 1px #d8d8d8;
                        margin: 0 12px;
                        .info-textarea{
                            width: 100%;
                            color: #999;
                            font-size: 28px;
                        }
                    }
                    .bottle-info{
                        width: 100%;
                        .bottle-row{
                            display: flex;
                            flex-direction: row;
                            margin-left: 24px;
                            padding-bottom: 12px;
                            margin-top: 12px;
                            view{
                                flex: 1;
                            }
                            .right{
                                text-align: right;
                            }
                        }
                    }
                }
            }
        }
    }
</style>
