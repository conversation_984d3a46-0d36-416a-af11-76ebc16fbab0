[{"title": "每日有效拜访", "dataKey": "dailyReportTerData", "config": [{"type": "stats", "items": [{"field": "plannedStores", "unit": "家", "label": "当日规划拜访家数"}, {"field": "plannedTimes", "unit": "次", "label": "当日规划拜访次数"}, {"field": "effectiveStores", "unit": "家", "label": "当日有效终端拜访"}, {"field": "effectiveTimes", "unit": "次", "label": "当日完成拜访次数"}], "itemsPreRow": 2}, {"type": "form", "formItems": [{"field": "plannedRouteFlag", "label": "是否按规划路线拜访", "lovType": "YES_OR_NO", "disabled": true, "placeholder": "请选择", "dataKey": "dailyReportTerData"}, {"field": "dailyVisitExcFlag", "label": "是否异常", "required": true, "clearField": "dailyVisitExcReason", "lovType": "YES_OR_NO", "placeholder": "请选择"}, {"field": "dailyVisitExcReason", "label": "情况说明", "flagField": "dailyVisitExcFlag", "required": true, "type": "textarea", "maxLength": 2000, "placeholder": "请填写情况说明"}]}]}, {"title": "星火终端有效拜访", "dataKey": "dailyReportTerData", "config": [{"type": "stat-group", "items": [{"title": "本周", "bgColor": "#FFF7F5", "titleColor": "#EC7700", "items": [{"field": "starfirePlannedStoresWeek", "unit": "家", "label": "需拜访"}, {"field": "starfireEffectiveStoresWeek", "unit": "家", "label": "有效拜访"}, {"field": "starfireVisitRateWeek", "unit": "%", "label": "拜访达成率"}]}, {"title": "当日", "bgColor": "#F5F7FF", "titleColor": "#425CC7", "items": [{"field": "starfireEffectiveStores", "unit": "家", "label": "有效拜访"}, {"field": "starfireCompletedTimes", "unit": "次", "label": "有效拜访"}]}]}, {"type": "form", "formItems": [{"field": "starfireExcFlag", "label": "是否异常", "required": true, "lovType": "YES_OR_NO", "clearField": "starfireExcReason", "placeholder": "请选择"}, {"field": "starfireExcReason", "label": "情况说明", "flagField": "starfireExcFlag", "required": true, "type": "textarea", "maxLength": 2000, "placeholder": "请填写情况说明"}]}]}, {"title": "配额执行", "dataKey": "dailyReportTerData", "config": [{"type": "stat-group", "items": [{"title": "当季度", "bgColor": "#F4FBFB", "titleColor": "#10B7B7FF", "contentClass": "flex-start", "items": [{"field": "executedSeason3852", "unit": "件", "label": "38、52度经典装配额累计执行"}, {"field": "rateSeason3852", "unit": "%", "label": "配额达成率"}, {"field": "netInboundSeason4346", "unit": "件", "label": "43、46度经典装净入库"}, {"field": "targetSeason3852", "unit": "件", "label": "38、52经典装配额目标"}]}, {"title": "当日", "bgColor": "#2532FB0A", "titleColor": "#425CC7FF", "contentClass": "flex-start", "items": [{"field": "executed3852", "unit": "件", "label": "38、52度经典装配额执行"}, {"field": "netInbound4346", "unit": "件", "label": "43、46度经典装净入库"}, {"field": "orders3852", "unit": "件", "label": "38、52度经典装预订单下单"}, {"field": "orders4346", "unit": "件", "label": "43、46度经典装预订单下单"}]}]}, {"type": "form", "formItems": [{"field": "quotaExcFlag", "label": "是否异常", "required": true, "lovType": "YES_OR_NO", "clearField": "quotaExcReason", "placeholder": "请选择"}, {"field": "quotaExcReason", "label": "情况说明", "flagField": "quotaExcFlag", "required": true, "type": "textarea", "maxLength": 2000, "placeholder": "请填写情况说明"}]}]}, {"title": "终端健康度", "dataKey": "dailyReportTerData", "config": [{"type": "stat-group", "items": [{"bgColor": "#F8F8F8", "items": [{"field": "coveredTerminals", "unit": "家", "label": "负责覆盖终端", "tips": "当前财年净入库数大于0的跟进终端"}]}]}, {"type": "stats", "items": [{"field": "activeTerminals", "unit": "家", "label": "活跃终端", "tips": "1.当前财年净入库数大于2且扫码时间不在同一天的跟进终端\n2.当前财年净入库数大于10的跟进终端"}, {"field": "activePercentage", "unit": "%", "label": "活跃终端占比"}, {"field": "cityActivePercentage", "unit": "%", "label": "城市活跃终端占比"}, {"field": "openedTerminals", "unit": "家", "label": "开瓶终端", "tips": "当前财年是存在开瓶扫码数的跟进终端"}, {"field": "openedPercentage", "unit": "%", "label": "开瓶终端占比"}, {"field": "cityOpenedPercentage", "unit": "%", "label": "城市开瓶终端占比"}]}, {"type": "form", "formItems": [{"field": "termHealthExcFlag", "label": "是否异常", "required": true, "lovType": "YES_OR_NO", "clearField": "termHealthExcReason", "placeholder": "请选择"}, {"field": "termHealthExcReason", "label": "情况说明", "flagField": "termHealthExcFlag", "required": true, "type": "textarea", "maxLength": 2000, "placeholder": "请填写情况说明"}]}]}, {"title": "排期人数达成率", "dataKey": "consumerScheduleOpenStat", "config": [{"type": "stats", "items": [{"field": "echoScheduleQty", "unit": "人", "label": "应纳入排期"}, {"field": "alreadyScheduleUnconvertQty", "unit": "人", "label": "已纳入排期的未转化消费者"}, {"field": "scheduleActionAchieveRate", "unit": "%", "label": "排期动作完成率"}, {"field": "schedulePersonAchieveRate", "unit": "%", "label": "排期人数达成率"}]}, {"type": "form", "formItems": [{"field": "achvRateExcFlag", "label": "是否异常", "required": true, "lovType": "YES_OR_NO", "clearField": "achvRateExcReason", "placeholder": "请选择"}, {"field": "achvRateExcReason", "label": "情况说明", "flagField": "achvRateExcFlag", "required": true, "type": "textarea", "maxLength": 2000, "placeholder": "请填写情况说明"}]}]}, {"title": "消费者开瓶", "dataKey": "consumerScheduleOpenStat", "config": [{"type": "stat-group", "items": [{"title": "本周", "bgColor": "#FFF7F5", "titleColor": "#EC7700", "items": [{"field": "totalThisWkOpenBotQty", "unit": "瓶", "label": "累计消费者开瓶"}, {"field": "lastCompareWkOpenRate", "unit": "%", "label": "环比上周增长"}, {"field": "lastCompareWkCityRate", "unit": "%", "label": "城市开瓶增长率"}]}, {"title": "当日", "bgColor": "#F5F7FF", "titleColor": "#425CC7", "items": [{"field": "todayOpenBotQty", "unit": "瓶", "label": "消费者开瓶"}, {"field": "sevenDaysOpenGrowthRate", "unit": "%", "label": "较7日前增长"}, {"field": "sevenDaysCityOpenGrowthRate", "unit": "%", "label": "城市开瓶增长率"}]}]}, {"type": "form", "formItems": [{"field": "scanExcFlag", "label": "是否异常", "required": true, "lovType": "YES_OR_NO", "clearField": "scanExcReason", "placeholder": "请选择"}, {"field": "scanExcReason", "label": "情况说明", "flagField": "scanExcFlag", "required": true, "type": "textarea", "maxLength": 2000, "placeholder": "请填写情况说明"}]}]}, {"title": "消费者开瓶抽奖健康度", "config": [{"type": "table", "dataKey": "consumerScheduleOpenStatItems", "columns": [{"title": "品项", "key": "itemName", "width": 95}, {"title": "綜合开瓶抽奖率", "key": "overallOpenBotRate", "unit": "%", "width": 105}, {"title": "综合开箱抽奖率", "key": "overallOpenBinRate", "unit": "%", "width": 105}, {"title": "综合城市开甁抽奖率", "key": "overallCityOpenBotRate", "unit": "%", "width": 130}, {"title": "综合城市开箱抽奖率", "key": "overallCityOpenBinRate", "unit": "%", "width": 130}, {"title": "年度开甁抽奖率", "key": "yearOpenBotRate", "unit": "%", "width": 105}, {"title": "年度开箱抽奖率", "key": "yearOpenBinRate", "unit": "%", "width": 105}, {"title": "年度城市开甁抽奖率", "key": "yearCityOpenBotRate", "unit": "%", "width": 130}, {"title": "年度城市开箱抽奖率", "key": "yearCityOpenBinRate", "unit": "%", "width": 130}, {"title": "当日异地扫码率", "key": "todayOffsiteScanRate", "unit": "%", "width": 100}, {"title": "当日城市异地扫码率", "key": "todayCityOffsiteScanRate", "unit": "%", "width": 130}, {"title": "季度异地扫码率", "key": "quarOffsiteScanRate", "unit": "%", "width": 100}, {"title": "季度城市异地扫码率", "key": "quarCityOffsiteScanRate", "unit": "%", "width": 130}], "data": []}, {"type": "form", "formItems": [{"field": "scanHealthExcFlag", "label": "是否异常", "clearField": "scanHealthExcReason", "required": true, "lovType": "YES_OR_NO", "placeholder": "请选择"}, {"field": "scanHealthExcReason", "label": "情况说明", "flagField": "scanHealthExcFlag", "required": true, "type": "textarea", "maxLength": 2000, "placeholder": "请填写情况说明"}]}, {"title": "区域重点项目指标", "type": "table", "showActions": true, "showAdd": true, "showDelete": true, "dataKey": "regionProjects", "columns": [{"title": "指标", "key": "projIndex", "width": 65, "type": "input"}, {"title": "目标", "key": "target", "width": 65, "inputType": "number", "type": "input"}, {"title": "当日达成", "key": "achieved", "inputType": "number", "width": 65, "type": "input"}, {"title": "达成率", "key": "achvRate", "width": 65, "type": "computedRate", "unit": "%"}], "data": []}]}]