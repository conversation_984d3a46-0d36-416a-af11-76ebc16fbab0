[{"title": "每日有效拜访情况", "dataKey": "dailyReportTerData", "config": [{"type": "stats", "items": [{"field": "plannedStores", "unit": "家", "label": "当日规划拜访家数"}, {"field": "effectiveStores", "unit": "家", "label": "当日有效终端拜访"}, {"field": "effectiveTimes", "unit": "次", "label": "当日完成拜访次数"}]}, {"type": "form", "formItems": [{"field": "plannedRouteFlag", "label": "是否按规划路线拜访", "lovType": "YES_OR_NO", "disabled": true, "placeholder": "请选择", "dataKey": "dailyReportTerData"}]}]}, {"title": "终端净入库件数", "dataKey": "dailyReportTerData", "config": [{"type": "stat-group", "items": [{"title": "当日", "bgColor": "#F5F7FF", "titleColor": "#425CC7", "contentClass": "flex-2", "items": [{"field": "oldStartQuNetInbound", "unit": "件", "label": "老头曲达成"}, {"field": "boutiqueStartQuNetInbound", "unit": "件", "label": "精品头曲达成"}, {"field": "sixYearStartQuNetInbound", "unit": "件", "label": "六年窖头曲达成"}, {"field": "blackCoverNetInbound", "unit": "件", "label": "黑盖达成"}]}]}]}, {"title": "终端数量及质量", "dataKey": "qualityQuantityList", "config": [{"type": "table", "hideSerial": true, "columns": [{"title": "品项", "key": "prodBusinessMName", "width": 75}, {"title": "覆盖终端", "key": "coveredTerminals", "unit": "家", "width": 80}, {"title": "激活终端", "key": "openedTerminals", "unit": "家", "width": 80}, {"title": "激活终端占比", "key": "openedPercentage", "unit": "%", "width": 90}, {"title": "流通堡垒终端", "key": "circulationTerminals", "unit": "家", "width": 90}, {"title": "流通堡垒终端占比", "key": "circulationPercentage", "unit": "%", "width": 120}, {"title": "餐饮堡垒终端", "key": "cateringTerminals", "unit": "家", "width": 90}, {"title": "餐饮堡垒终端占比", "key": "cateringPercentage", "unit": "%", "width": 120}]}]}, {"title": "消费者开瓶数量", "dataKey": "dachengConsumerOpenStatList", "config": [{"type": "stat-group", "items": [{"title": "当日-老头曲", "bgColor": "#FFF7F5", "titleColor": "#EC7700", "filter": {"field": "prodBusinessMClass", "value": "B00080001"}, "items": [{"field": "todayOpenBotQty", "unit": "瓶", "label": "消费者开瓶"}, {"field": "todayVerifyQty", "unit": "瓶", "label": "核销酒"}, {"field": "todayOffsiteOpenBotQty", "unit": "瓶", "label": "异地开瓶"}]}, {"title": "本周-老头曲", "bgColor": "#FFEEDD", "titleColor": "#BF6E0B", "contentClass": "flex-2", "filter": {"field": "prodBusinessMClass", "value": "B00080001"}, "items": [{"field": "totalThisWkOpenBotQty", "unit": "瓶", "label": "累计消费者开瓶"}, {"field": "lastCompareWkOpenRate", "unit": "%", "label": "环比上周增长"}]}, {"title": "当日-精品头曲", "bgColor": "#E8F0FE", "titleColor": "#2E5AC7", "filter": {"field": "prodBusinessMClass", "value": "B00080002"}, "items": [{"field": "todayOpenBotQty", "unit": "瓶", "label": "消费者开瓶"}, {"field": "todayVerifyQty", "unit": "瓶", "label": "核销酒"}, {"field": "todayOffsiteOpenBotQty", "unit": "瓶", "label": "异地开瓶"}]}, {"title": "本周-精品头曲", "bgColor": "#DDE5FC", "titleColor": "#2A2E9C", "contentClass": "flex-2", "filter": {"field": "prodBusinessMClass", "value": "B00080002"}, "items": [{"field": "totalThisWkOpenBotQty", "unit": "瓶", "label": "累计消费者开瓶"}, {"field": "lastCompareWkOpenRate", "unit": "%", "label": "环比上周增长"}]}, {"title": "当日-六年窖头曲", "bgColor": "#E8F7EA", "titleColor": "#38A745", "filter": {"field": "prodBusinessMClass", "value": "B00080004"}, "items": [{"field": "todayOpenBotQty", "unit": "瓶", "label": "消费者开瓶"}, {"field": "todayVerifyQty", "unit": "瓶", "label": "核销酒"}, {"field": "todayOffsiteOpenBotQty", "unit": "瓶", "label": "异地开瓶"}]}, {"title": "本周-六年窖头曲", "bgColor": "#DCF3DE", "titleColor": "#2E9432", "contentClass": "flex-2", "filter": {"field": "prodBusinessMClass", "value": "B00080004"}, "items": [{"field": "totalThisWkOpenBotQty", "unit": "瓶", "label": "累计消费者开瓶"}, {"field": "lastCompareWkOpenRate", "unit": "%", "label": "环比上周增长"}]}, {"title": "当日-黑盖", "bgColor": "#E4FFFF", "titleColor": "#10B7B7FF", "filter": {"field": "prodBusinessMClass", "value": "B00070001"}, "items": [{"field": "todayOpenBotQty", "unit": "瓶", "label": "消费者开瓶"}, {"field": "todayVerifyQty", "unit": "瓶", "label": "核销酒"}, {"field": "todayOffsiteOpenBotQty", "unit": "瓶", "label": "异地开瓶"}]}, {"title": "本周-黑盖", "bgColor": "#CFFFFF", "titleColor": "#0A7A7A", "contentClass": "flex-2", "filter": {"field": "prodBusinessMClass", "value": "B00070001"}, "items": [{"field": "totalThisWkOpenBotQty", "unit": "瓶", "label": "累计消费者开瓶"}, {"field": "lastCompareWkOpenRate", "unit": "%", "label": "环比上周增长"}]}]}]}, {"title": "消费者数量及质量", "dataKey": "dachengConsumerOpenStatList", "config": [{"type": "table", "hideSerial": true, "columns": [{"title": "品项", "key": "prodBusinessMName", "width": 75}, {"title": "消费者累计人数", "key": "yearCsmReachQty", "unit": "人", "width": 100}, {"title": "消费者新增人数", "key": "todayCsmReachQty", "unit": "人", "width": 100}, {"title": "KOC累计人数", "key": "yearCsmReachKocQty", "unit": "人", "width": 90}, {"title": "KOC新增人数", "key": "todayCsmReachKocQty", "unit": "人", "width": 90}]}]}, {"title": "宴席场次", "dataKey": "banquetBanquetList", "config": [{"type": "table", "hideSerial": true, "columns": [{"title": "品项", "key": "prodBusinessMName", "width": 100}, {"title": "当日宴席达成", "key": "current<PERSON><PERSON>Reached", "unit": "场", "width": 120}, {"title": "累计宴席达成", "key": "cumulativeReached", "unit": "场", "width": 120}]}]}]