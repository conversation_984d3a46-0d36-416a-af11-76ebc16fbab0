# 日报
------
#### 文档变更记录
|序号|版本|作者|日期|备注|
|----|----|----|----|----|
|1|1.0|王彬馨|2022-06-28|创建文档|
#### 功能修改历史
|序号|作者|日期|备注|
|----|----|----|----|
#### 业务背景
展示选择员工/选择日期当天的各个板块内容， 展示核心逻辑处理在后端，前端仅展示。维护日报部分可编辑字段信息
#### 技术总体设计
1.整合终端版块、活动版块、消费者版块日报模板
2.统一日报填写入口-纳入基础板块菜单
3.日报填写以填报人维度，不区分职位、板块——存储按创建人、日报日期、职位维度
#### 技术详细设计
1. 日报列表
   1. 默认“近看今日”、“仅看本人”，点击“仅看本人”下拉可选【仅看本人、查看下级】
   2. 搜索框在原有逻辑基础上，增加提报人搜索
   3. 列表内容按日期排列
   4. 点击某列进入对应的日报详情界面
   5. 日报新建权限-职位类型（对应字段描述在字段说明板块）
   6. 新建校验创建人当日是否存在日报内容
2. 创建日报
- 调用接口：action/link/dailyReport/preview生成预览数据
   日报新建逻辑：
   1. 按现有终端板块逻辑按创建人及其职位（有新建权限的职位类型）获取终端板块、活动版块、消费者版块内容，具体逻辑由各版块提供
   2. 自动生成的日报内容默认折叠
   3. 提交日报保存逻辑：按表结构分别存储日报头表、日报行表，按创建人ID+职位ID+日报日期为唯一性，分别存储对应的日报自动生成内容至对应字段（终端、活动、消费者分字段存储）
- 备注：生成日报不选职位、不选板块，按职位获取对应板块内容排列展示，多个职位依次按此逻辑排列生成展示
3. 日报详情
   1. 显示日报列表选择的记录内容
   2. 仅查看本人日报，按列表选择的提报人ID、日报时间获取日报内容按demo示例排列显示，默认折叠，其他显示内容取其中一条记录。
   3. 查看下级日报，按列表选择的提报人ID+日报日期+职位ID（下级职位ID）获取所有“日报生成内容”按demo示例排列显示
   4. 默认折叠
   5. 终端板块、活动版块、消费者版块(还没做)内容展示
#### 复杂逻辑、字段说明
- 日报新建权限职位类型
  - 'SalesSupervisor',         // 业务主管
  - 'Salesman',                // 业务代表
  - 'GroupBuyManager',         // 团购经理
  - 'AccountManager',          // 客户经理
  - 'RegionalManager',         // 区域经理
  - 'CustServiceManager',      // 客服经理
  - 'VipManager',              // VIP经理
  - 'CustServiceSpecialist',   // 客服专员
  - 'CustServiceSupervisor',   // 客服主管
  - 'BattleCommander',         // 会战指挥长
  - 'SalesTeamLeader',         // 小组组长
  - 'CityManager',             // 城市经理
  - 'SalesChannelManger'       // 渠道经理

------ 市场活动-日报-内容结束 ------
