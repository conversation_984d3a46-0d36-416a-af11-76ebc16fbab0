<template>
    <link-page class="daily-lst-page">
        <new-daily-list v-if="isNewDaily" />
        <daily-list v-else />
    </link-page>
</template>

<script>
    import DailyList from './components/daily-list';
    import NewDailyList from './components/new-daily-list';
    export default {
        name: "daily-list-page",
        components: {
            DailyList,
            NewDailyList
        },
        data() {
            return {
                isNewDaily: false
            }
        },
        mounted() {
            this.queryNewDailyCfg()
        },
        methods: {
            async queryNewDailyCfg() {
                try {
                    const userInfo = this.$taro.getStorageSync('token').result;

                    const { result: companyCode } = await this.$http.get('action/link/user/queryMainPostnCompanyCode', { userId: userInfo.id })
                    if (!companyCode) return

                    const config = await this.$utils.getCfgProperty('NEW_DAILY_COMPANY_CONFIG')
                    const configObj = JSON.parse(config);
                    this.isNewDaily = Object.values(configObj).some(codes => 
                        codes.split(',').map(code => code.trim())
                            .includes(companyCode)
                    );
                } catch(e) {
                    this.$message.error('企业参数:NEW_DAILY_COMPANY_CONFIG配置有误')
                }
            },
        }
    }
</script>

<style lang="scss">
    .daily-lst-page {
        .link-dropdown-service.link-dropdown-service-show {
            z-index: 9999;
            .link-dropdown-service-arrow {
                opacity: 0;
            }
        }
        .search-container {
            @include flex-start-center;
            padding-left: 24px;
            color: #8C8C8C;
            font-size: 28px;
            text-align: center;

            .only-today {
                border-radius: 26px;
                width: 144px;
                height: 52px;
                line-height: 52px;
                font-size: 28px;
                text-align: center;
                margin-right: 10px;
            }
        }

        .daily-list-rows {
            background: #FFFFFF;
            border-radius: 16px;
            width: 702px;
            margin: 24px auto auto auto;
            padding: 0;
            /*deep*/
            .link-item .link-item-icon {
                width: 0;
            }

            .daily-item {
                width: 100%;

                .daily-rows {
                    @include flex;
                    @include space-between;
                    padding: 24px;

                    .daily-date {
                        @include flex;
                        font-size: 32px;
                        color: #262626;
                        font-weight: bold;

                        .dot-top {
                            width: 12px;
                            height: 12px;
                            box-shadow: 0 0 12px 12px #c8dded;
                            background-color: #2F69F8;
                            border-radius: 50%;
                            margin: 14px 20px;
                        }
                    }

                    .daily-person {
                        font-size: 28px;
                        color: #8C8C8C;

                        .daily-person-name {
                            color: #262626;
                            padding-left: 10px;
                        }
                    }
                }
            }
        }
    }
</style>
