<template>
    <link-page class="daily-more-table-list">
        <view class="new-box" :style="{ top: scrollTop + 'rpx' }" v-if="!scrolls">
            <view class="list-head">
                <view class="list-titles" :style="{ width: getWidth(item.width) + 'rpx', background: '#DADBE2' }"
                    v-for="(item, index) in columns" :key="index + 'title'">{{ item.title }}</view>
            </view>
        </view>
        <view class="new-box" :style="{ top: scrollTop + 'rpx' }" v-else>
            <view class="list-head">
                <view class="list-titles" :style="{ width: getWidth(item.width) + 'rpx', background: '#DADBE2' }"
                    v-for="(item, index) in columns" :key="index + 'title'">{{ item.title }}</view>
            </view>
        </view>
        <view class="other-content">
            <scroll-view :scroll-y="true" enable-passive scroll-with-animation='true' class="list-fixed"
                @scrolltolower='toLow'>
                <link-auto-list :option="option">
                    <template #default="{ data, index }">
                        <view :key="index" :data="data" class="more-item">
                            <view class="new-item" :style="{ background: index % 2 === 0 ? 'white' : '#F0F1F3' }">
                                <view class="item-cell" v-for="(item, idx) in columns" :key="idx + 'cell'"
                                    :style="{ width: getWidth(item.width) + 'rpx', color: item.color ? item.color : '#212223', background: index % 2 === 0 ? 'white' : '#F0F1F3' }">
                                    <text v-if="item.lovType" class="cell-text">{{ data[item.key] | lov(item.lovType) }}</text>
                                    <text v-else class="cell-text">
                                        {{ filterVal(data[item.key], item) }}
                                    </text>
                                </view>
                            </view>
                        </view>
                    </template>
                </link-auto-list>
            </scroll-view>
        </view>
        <link-dialog v-model="tipOpen" :enableScroll="false">
            <text>{{ tipContent }}</text>
        </link-dialog>
    </link-page>
</template>

<script>
definePageConfig({
    navigationBarTitleText: '',
});
import { ComponentUtils } from 'link-taro-component';
export default {
    name: 'daily-more-table-list-page',
    data() {
        const { url, columns, queryParams } = this.pageParam || {};

        const option = new this.AutoList(this, {
            url: {
                queryByExamplePage: url
            },
            param: {
                ...queryParams,
                pageFlag: true,
                totalFlag: true
            },
            pageSize: 20,
            loadOnStart: true,
            hooks: {
                beforeLoad({param}) {
                    delete param.sort
                    delete param.order
                    delete param.filtersRaw
                }
            }
        });

        return {
            option,
            columns,
            scrollTop: 0,
            scrolls: false,
            tipOpen: false,
            tipContent: '',
            queryParams: {}
        };
    },
    onPageScroll(e) {
        this.scrolls = true;
        this.changePosition(e.scrollTop);
    },
    created() {
        if (this.pageParam.pageTitle) {
            this.$taro.setNavigationBarTitle({ title: this.pageParam.pageTitle })
        }
    },
    methods: {
        filterVal(val, { emptyVal, unit }) {
            const final =  val != undefined && (!emptyVal || !emptyVal.includes(val))
            return final ? `${val}${unit || ''}` : ""
        },

        getWidth(width) {
            return width ? width * 2 : 160;
        },

        changePosition: ComponentUtils.debounce(function (top) {
            this.scrolls = false;
            this.scrollTop = top;
        }, 500),

        toLow() {
            if (this.pageParam.url) {
                this.option.methods.loadMore()
            }
        }
    }
}
</script>

<style lang='scss'>
.daily-more-table-list {
    font-size: 28px;
    width: 100%;
    min-height: 100vh;
    max-height: 100vh;
    overflow-x: scroll;
    position: relative;

    .link-auto-list-no-more {
        width: 100vw;
    }

    .list-fixed {
        height: calc(100vh - 80px);
    }

    .new-box {
        position: absolute;
        top: 0;
        left: 0;
        height: 80px;
        z-index: 10;
    }

    .other-content {
        position: absolute;
        top: 80px;
        left: 0;
        height: calc(100vh - 80px);
    }

    .list-head {
        background: #DADBE2;
        display: flex;
        align-items: center;
        min-width: 100vw;

        .list-titles {
            flex-shrink: 0;
            width: 200px;
            padding: 20px;
        }
    }

    .more-item {
        .new-item {
            display: flex;
            align-items: center;

            .item-cell {
                flex-shrink: 0;
                width: 200px;
                height: 30px;
                padding: 20px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;

                .cell-text {
                    width: 100%;
                    display: inline-block;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }
    }

    .more-table-data-tip-content {
        text-align: left;
        max-width: 600px;

        .tip-title {
            font-size: 32px;
            font-weight: 500;
            margin-bottom: 16px;
            color: #333;
        }

        text {
            display: block;
            font-size: 28px;
            line-height: 1.5;
            margin-bottom: 8px;
            word-break: break-all;
            white-space: pre-wrap;
        }
    }
}
</style>
