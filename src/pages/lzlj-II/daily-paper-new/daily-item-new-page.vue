<template>
    <link-page class="daily-item-new-page">
        <view class="daily-list-rows">
            <view class="daily-item">
                <view class="daily-rows">
                    <view class="daily-date"><label class="dot-top"></label>{{$utils.dateFormat(previewData.dailyReportHeader.dailyDate)}}
                    </view>
                    <view class="daily-person">提报人<label class="daily-person-name">{{previewData.dailyReportHeader.createdName}}</label>
                    </view>
                </view>
                <view class="daily-row-2">
                    <template v-if="dailyType === 'DAY'">
                        <daily-report-data
                            :postn-daily-report-list="previewData.postnDailyReportList"
                            :control-and-remarks-obj="controlAndRemarksObj"
                            :daily-temp="dailyTemp"
                            @switchState="switchState" />
                    </template>
                    <template v-else>
                        <!--周报和月报共用一套-->
                        <weekly-report-data
                            :postn-daily-report-list="previewData.postnDailyReportList"
                            :control-and-remarks-obj="controlAndRemarksObj"
                            :daily-type="dailyType"
                            :daily-temp="dailyTemp"
                            @switchState="switchState"/>
                    </template>
                </view>
            </view>
        </view>
        <link-form ref="form" :rules="formRules" :value="previewData.dailyReportHeader" class="list-box-form">
            <!--模板数据-->
            <link-form-item v-for="(dItem, dIndex) in dailyTemp"
                            :key="dIndex"
                            v-if="dItem.type=='form'"
                            :label="dItem.label"
                            :required="dItem.required"
                            :field="dItem.name"
                            :vertical="dItem.ctrlCode !== 'link-lov'">
                <link-lov v-if="dItem.ctrlCode === 'link-lov'" :type="dItem.lovType" v-model="previewData.dailyReportHeader[dItem.name]"/>
                <view v-else class="textarea-big">
                        <link-textarea :placeholder="'请输入'+dItem.label" :height="200" :nativeProps="{maxlength: 3000}" mode="textarea" v-model="previewData.dailyReportHeader[dItem.name]"/>
                        <view v-if="!previewData.dailyReportHeader[dItem.name]" class="textarea-length">0/2000</view>
                        <view v-else :class="previewData.dailyReportHeader[dItem.name].length < 2001 ? 'textarea-length' : 'textarea-over-length'" v-model="previewData.dailyReportHeader[dItem.name].length" >{{previewData.dailyReportHeader[dItem.name].length+'/2000'}}</view>
                </view>
            </link-form-item>
            <view class="door-head">
                <text class="asterisk"></text>
                附件
            </view>
            <view class="img-box">
                <lnk-img :parentId="controlAndRemarksObj.id"
                         :moduleType="moduleType"
                         :delFlag="true"
                         :newFlag="true"
                ></lnk-img>
            </view>
        </link-form>

        <link-sticky>
            <link-button block size="large" @tap="submit" autoLoading :shadow="true">{{buttonLabel}}</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    import LineTitle from "../../lzlj/components/line-title";
    import timeDuring from '../../terminal/components/time-during';
    import addressTip from '../../terminal/components/address-tip';
    import LnkImg from "../../core/lnk-img/lnk-img";
    import {ROW_STATUS} from "../../../utils/constant";
    import WeeklyReportData from "./components/weekly-report-data";
    import DailyReportData from "./components/daily-report-data";

    export default {
        name: "daily-item-new-page",
        components: {DailyReportData, WeeklyReportData, LnkImg, LineTitle, timeDuring, addressTip},
        data() {
            const dailyTemp = this.pageParam.dailyTemp;//日报头配置的模板字段
            const controlAndRemarksObj = {
                id: "",
            };
            const previewData = {
                dailyReportHeader: {},//预览的日报头数据
                postnDailyReportList: [],//预览的职位日报数据
            };
            const newScene = this.pageParam.newScene;//新建场景
            const dailyReportHeaderId = this.pageParam.dailyReportHeaderId;//头ID
            const dailyType = this.pageParam.dailyType || 'DAY'
            console.log('dailyTemp', dailyTemp);
            return {
                newScene,
                controlAndRemarksObj,
                moduleType: 'daily',              // 日报图片类型
                dailyTemp,
                previewData,//日报数据
                dailyReportHeaderId,
                formRules: {},
                dailyType
            }
        },
        computed: {
          buttonLabel(){
              let label = '提交'
              if (this.dailyType === 'DAY') {
                  label = '提交今日日报'
              } else if (this.dailyType === 'WEEK') {
                  label = '提交周报'
              } else if (this.dailyType === 'MONTH') {
                  label = '提交月报'
              }
              return label
          }
        },
        async created() {
            console.log(this.dailyTemp)
            this.dailyTemp.forEach(item => {
                if(item.type=='form'){
                    this.formRules[item.name] = this.Validator.len({max: 2000, maxMsg: '不能超过2000个字'})
                }
            })
            if (this.newScene === ROW_STATUS.NEW) {
                this.controlAndRemarksObj.id = await this.$newId();
            } else {
                this.controlAndRemarksObj.id = this.dailyReportHeaderId;
            }
            await this.queryPreviewData();
        },
        mounted() {
            let title = ''
            if (this.dailyType === 'DAY') {
                title = '编辑日报'
            } else if (this.dailyType === 'WEEK') {
                title = '编辑周报'
            } else if (this.dailyType === 'MONTH') {
                title = '编辑月报'
            }
            this.$taro.setNavigationBarTitle({title})
        },
        methods: {
            /**
             * 获取当前时间、判断在八点前or后
             * <AUTHOR>
             * @date 2022-11-02
             * */
            async getTime() {
                let nowTimeStamp = await this.$utils.getServerTime(); // 获取当前时间
                let nowDate = new Date(nowTimeStamp);
                let preDate = new Date(nowTimeStamp - 24*60*60*1000);     // 将前一天的时间戳转换为标准时间
                if(nowDate.getHours() < 8) {
                    this.previewData.dailyReportHeader.dailyDate = `${preDate.getFullYear()}-${preDate.getMonth() + 1}-${preDate.getDate()}`;
                }
            },

            /**
             * 生成预览数据
             * <AUTHOR>
             * @date 2021-08-27
             * */
            async queryPreviewData() {
                const that = this;
                this.$utils.showLoading("预览数据...");
                const data = await this.$http.post('action/link/dailyReport/preview', {
                    dailyType: this.dailyType
                }, {
                    handleFailed: (error) => {
                        that.$utils.hideLoading();
                    }
                });
                this.$utils.hideLoading();
                this.previewData = data.result;
                await this.getTime();
            },
            /**
             * 切换展开状态
             * <AUTHOR>
             * @date 2021-08-30
             * */
            switchState(type) {
                this.controlAndRemarksObj[type] ? this.$set(this.controlAndRemarksObj, type, false) : this.$set(this.controlAndRemarksObj, type, true);
            },
            /**
             * 提交日报
             * <AUTHOR>
             * @date 2021-08-30
             * */
            async submit() {
                await this.$refs.form.validate();
                this.previewData.dailyReportHeader['id'] = this.controlAndRemarksObj.id;
                const data = await this.$http.post('action/link/dailyReport/commit', this.previewData);
                if(data.success) {
                    this.$message.success('保存成功!');
                } else {
                    return false;
                }
                this.$bus.$emit('dailyHeaderRefresh');
                this.$bus.$emit('updateNoteList');
                this.$nav.back();
            },
        }
    }
</script>

<style lang="scss">
    @import "../../../styles/list-card";
    .daily-item-new-page {
        .daily-list-rows {
            background: #FFFFFF;
            border-radius: 16px;
            /*width: 702px;*/
            margin: auto;
            padding: 0;
            /*deep*/
            .link-item .link-item-icon {
                width: 0;
            }

            .daily-item {
                padding: 24px;

                .daily-rows {
                    width: 100%;
                    @include flex;
                    @include space-between;

                    .daily-date {
                        @include flex;
                        font-size: 32px;
                        color: #262626;
                        font-weight: bold;

                        .dot-top {
                            width: 12px;
                            height: 12px;
                            box-shadow: 0 0 12px 12px #c8dded;
                            background-color: #2F69F8;
                            border-radius: 50%;
                            margin: 14px 20px;
                        }
                    }

                }

                .daily-row-2 {
                    font-family: PingFangSC-Regular;

                    .daily-postn {
                        margin-top: 20px;
                        font-family: PingFangSC-Semibold, serif;
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 2px;
                        line-height: 64px;
                        text-align: center;
                        background: #ebf1fb
                    }

                    .daily-data-type {
                        line-height: 40px;
                        height: 40px;
                        background: #2F69F8;
                        border-radius: 4px 0 16px 4px;
                        font-size: 24px;
                        color: #FFFFFF;
                        width: auto;
                        display: inline-block !important;
                        padding: 0 20px;
                        margin-top: 24px;
                    }

                    .daily-line-box {
                        background-color: #ffffff;

                        .visit-box {
                            margin: 0 24px;
                            border-bottom: 1px solid #f2f2f2;

                            .link-item .link-item-body-left .link-item-title, .link-item .link-item-body-left .link-item-content {
                                color: #595959 !important;
                                min-height: inherit;
                                padding: 0;
                            }

                            .market-activity-list-item {
                                background: #FFFFFF;
                                width: 95%;
                                font-size: 28px;
                                border-radius: 16px;

                                .media-list {
                                    @include media-list;

                                    .media-top {
                                        width: 100%;
                                        @include flex-start-center;
                                        @include space-between;
                                        height: 80px;
                                        line-height: 80px;

                                        .num-view {
                                            background: #A6B4C7;
                                            border-radius: 8px;
                                            line-height: 50px;

                                            .num {
                                                font-size: 28px;
                                                color: #FFFFFF;
                                                letter-spacing: 0;
                                                line-height: 40px;
                                                padding: 2px 8px;
                                            }
                                        }
                                    }
                                }

                                .Inactive {
                                    color: #FF5A5A;
                                }
                            }
                        }

                        /*deep*/
                        .line-title {
                            margin-left: 0 !important;
                        }

                        /*deep*/
                        .link-item {
                            min-height: inherit;
                            padding: 32px 0;
                            margin: 0;
                            color: #595959 !important;
                        }

                        .daily-line-title {
                            display: flex;
                            justify-content: space-between;
                            align-items: flex-end;

                            .right-icon-operate {
                                margin-left: 30px;
                            }

                            .terminal-line-title {
                                margin-left: 0 !important;
                            }
                        }
                    }

                    .quota-item {
                        .terminal-line-title {
                            margin: 32px auto 0 !important;
                        }

                        .link-item {
                            margin: 24px auto 32px !important;
                            padding: 0 !important;
                            position: static;
                        }
                    }
                }
            }
        }

        .list-box-form {
            /*deep*/
            .link-item {
                padding: 32px 24px;
            }

            /*deep*/
            .link-textarea-content {
                font-size: 28px;
                padding: 24px 24px 54px 24px;
            }

            .textarea-big {
                position: relative;
                .textarea-length {
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    padding: 0px 40px 16px 0px;
                    color: #8C8C8C;
                    font-size: 28px;
                }
                .textarea-over-length {
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    padding: 0px 40px 16px 0px;
                    color: #ff5a5a;
                    font-size: 28px;
                }
            }
        }

        .img-box {
            background-color: #ffffff;
            padding-bottom: 20px;
        }

        /*deep*/
        .custom-camera {
            padding-bottom: 20px;
        }

        .door-head {
            font-size: 28px;
            color: #333333;
            padding-left: 40px;
            padding-top: 20px;
            background: #ffffff;

            .asterisk {
                color: #FF5A5A;
                margin-left: -14px;
                padding-right: 2px;

            }
        }
    }
</style>
