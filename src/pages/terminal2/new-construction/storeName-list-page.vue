<template>
    <link-page class="es-terminal-list-page">
        <link-auto-list :option="chainStoreData" 
                        :searchInputBinding="{props:{placeholder:'搜索'}}">

            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="terminal-list-item"
                      @tap="goBack(data)">
                    <view class="terminal-list" slot="note">
                        <view class="list-cell">
                            <view class="media-list">
                                <image class="media-list-logo" :src="data.storeUrl" @tap.stop="previewStoreUrl(data)" lazy-load="true"></image>
                                <view class="store-content">
                                    <view class="store-content-top" v-if="data.acctType">
                                        <!--【客户一级分类】为“终端Terminal”的时候显示storeSigns字段-->
                                        <view class="store-title" v-if="data.acctType === 'Terminal'">{{data.acctName}}</view>
                                        <!--【客户一级分类】为“分销商Distributor”时展示billTitle字段-->
                                        <view class="store-title" v-if="data.acctType === 'Distributor'">{{data.acctName || data.billTitle}}</view>
                                        <!--已认证-->
                                        <view class="store-level" v-if="data.acctStage === 'ykf'"><image :src="$imageAssets.storeStatusVerifiedImage"></image></view>
                                        <!--未认证-->
                                        <view class="store-level" v-if="data.acctStage === 'xk'"><image :src="$imageAssets.storeStatusUnverifiedImage"></image></view>
                                        <!--已失效-->
                                        <view class="store-level" v-if="data.acctStage === 'ysx'"><image :src="$imageAssets.storeStatusInvalidationImage"></image></view>
                                        <!--潜客-->
                                        <view class="store-level" v-if="data.acctStage === 'dkf' && !isYangShengOrYouXuan"><image :src="$imageAssets.storeStatusPotentialImage"></image></view>
                                    </view>
                                    <view class="store-content-middle">
                                        <view class="left" >
                                            <!--  四色标签 -->
                                            <color-tag :value="data.fourColorLabel" v-if="data.fourColorLabel" />
                                            <view class="store-type" v-if="data.financingFlag">贷 | {{data.financingFlag | lov('YR_FINANCING_FLAG')}}</view>
                                            <view class="store-type" v-if="typeList.includes(data.acctType)">{{data.acctType | lov('ACCT_TYPE')}}</view>
                                            <view class="store-type" v-if="categoryLst.includes(data.acctCategory)">{{data.acctCategory | lov('ACCNT_CATEGORY')}}</view>
                                            <view class="store-type" v-if="sublist.includes(data.subAcctType)">{{data.subAcctType | lov('SUB_ACCT_TYPE')}}</view>
                                            <!-- 战略零售商标签 -->
                                            <view class="store-type" v-if="data.strategicFlag && data.strategicFlag === 'Y'">{{data.strategicFlag | lov('STRATEGIC_TAG')}}</view>
                                            <view class="store-type" v-if="levelList.includes(data.acctLevel) || caplist.includes(data.capacityLevel)">
                                                <text v-if="levelList.includes(data.acctLevel)">{{data.acctLevel | lov('ACCT_LEVEL')}}</text>
                                                <text v-if="levelList.includes(data.acctLevel) && caplist.includes(data.capacityLevel)"> | </text>
                                                <text v-if="caplist.includes(data.capacityLevel)">{{data.capacityLevel | lov('CAPACITY_LEVEL')}}</text>
                                            </view>
                                            <view class="store-type" v-if="data.judgmentFlag === 'Y'">品鉴</view>
                                            <view class="store-type" v-if="data.trafficHighland === 'Y'">流量高地</view>
                                            <view class="store-type" v-if="data.doorSigns !== undefined">{{data.doorSigns | lov('DOOR_SIGNS')}}</view>
                                            <view class="store-type" v-if="data.terminalDigitization">
                                                <text>{{data.terminalDigitization | lov('TERMINAL_DIGITIZATION')}}</text>
                                            </view>
                                            <!-- 兴冀公司合作状态字段显示逻辑加入 -->
                                            <view class="store-type" v-if="data.accntPartner && (data.salesmanAreaId === '521074619762290688' || userInfo.coreOrganizationTile.l3Id === '411091729387553190')">
												<text>{{data.accntPartner | lov('ACCT_STATE')}}</text>
                                            </view>
                                            <view class="store-type" v-if="data.isSpringAct === 'Y'">春雷行动</view>
                                            <block v-if="data.displayPolicyType">
                                                <view class="store-type"  v-for="(item, index) in data.displayPolicyType.split(',')" :key="index">{{item | lov('DISPLAY_POLICY_TYPE')}}</view>
                                            </block>
                                            <view class="store-type" v-if="data.codeMark">{{data.codeMark}}</view>
                                            <view class="store-type" v-if="data.censusLabels">{{data.censusLabels | lov('CENSUS_STATUS')}}</view>
                                        </view>
                                    </view>
                                  
                                    <view class="store-content-choose">
                                        <view class="store-content-representative">
                                            <view class="terminal-type">编码</view>
                                            <view class="terminal-name">{{data.acctCode}}</view>
                                        </view>
                                    </view>
                                    <view class="store-content-representative" v-if="(pageParam.source === 'advanceOrder' || pageParam.source === 'quotaTerminal'|| pageParam.source === 'quickEntryVisitTerminal') && !$utils.isEmpty(data.billTitle)">
                                        <view class="terminal-type" v-if="data.multiAcctMainFlag === 'Y'">主户头</view>
                                        <view class="terminal-type" v-if="data.multiAcctMainFlag === 'N'">子户头</view>
                                        <view class="terminal-name">{{data.billTitle}}</view>
                                    </view>
                                    <view class="store-content-representative">
                                        <view class="terminal-type">业代</view>
                                        <view class="terminal-name">{{data.formattedSalesManList}}</view>
                                    </view>
                                    <view class="store-content-address">
                                        <view class="store-address">{{data.province}}{{data.city}}{{data.district}}{{data.address}}</view>
                                    </view>
                                </view>
                            </view>
                           
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
       
    </link-page>
</template>

<script>
import lnkTaps from '../../core/lnk-taps/lnk-taps'
import ColorTag from '@/pages/terminal2/components/ColorTag.vue';

export default {
    name: "es-terminal-list-page",
    components: {
        lnkTaps,
        ColorTag
    },
    data () {
		    const userInfo = this.$taro.getStorageSync('token').result
        const isYangShengOrYouXuan = ['1612'].includes(userInfo.coreOrganizationTile.brandCompanyCode);
        const chainStoreData = new this.AutoList(this, {                       // 品牌
            module: 'action/link/accnt',
            url: {
                queryByExamplePage :'export/link/accnt/queryChainStoreList',
            },
            param: {
                filtersRaw:[],
                // filterOperator:"(chainHeadStoreFlag0 or chainHeadStoreFlag1) and acctStatus and acctType and acctCategory",
                mdmCompanyCode: Number(this.pageParam.mdmCompanyCode),
                acctType: 'Terminal',
                oauth: 'ALL'
            },
            // queryFields: 'id,acctName,chainHeadStoreFlag,acctType,mdmCompanyCode,acctStatus,',
            queryFields: 'id,acctName,chainHeadStoreFlag,acctStatus,acctType,censusLabels,isExclusiveShop,fourColorLabel,financingFlag,acctName,billTitle,'
                + 'codeMark,acctStage,acctCategory,subAcctType,acctLevel,capacityLevel,salesmanBrandCom,joinFlag,'
                + 'acctCode,multiAcctMainFlag,salesManListString,province,city,district,townName,address,storePicKey,'
                + 'storePicPreKey,tagList,judgmentFlag,doorSigns,terminalDigitization,accntPartner,orgId,editApprovalStatus,'
                + 'isSpringAct,displayPolicyType,salesmanAreaId,strategicFlag,mdmCompanyCode,trafficHighland,fstName,salesmanBrandComCode,'
                + 'starfireFlag',
            searchFields: ['acctName', 'createdByName', 'acctCode', 'province', 'city', 'district', 'townName', 'address'],
            hooks: {
                beforeLoad(option) {
                  option.param.sort = '';
                  option.param.order = '';
                  option.param.filtersRaw.push(
                      {id:'chainHeadStoreFlag0', property:"chainHeadStoreFlag", value:"Y", operator:"="},
                      {id:'chainHeadStoreFlag1', property:"chainHeadStoreFlag", value:"", operator:"IS NULL"},
                      {id:"acctStatus", property:"acctStatus", value:"Y", operator:"="},
                      {id:"acctStage", property:"acctStage", value:"ykf", operator:"="},
                      {id:"id", property:"id", value: this.pageParam.id, operator:"<>"},
                      {id:"acctType", property:"acctType", value:"Terminal", operator:"="},
                      {id:"acctCategory", property:"acctCategory", value: this.pageParam.acctCategory, operator:"="}
                  )
                  option.param.filterOperator="(chainHeadStoreFlag0 or chainHeadStoreFlag1) and acctStatus and acctType and acctCategory and id and acctStage"
                  let arr = option.param.filtersRaw
                  for(let i=0;i<arr.length;i++){
                      if(arr[i].id.includes('searchValue')){
                          arr[i].id='searchValue0'
                          option.param.filterOperator="(chainHeadStoreFlag0 or chainHeadStoreFlag1) and acctStatus and acctType and acctCategory and searchValue0 and id and acctStage"
                      }
                  }
                },
                async afterLoad (data) {
                    data.rows.map(async (item) => {
                        const { fstName, salesManListString } = item;
                        let formattedSalesManList = salesManListString || '';
                        if (fstName) {
                            const filteredSalesManList = formattedSalesManList
                                .replaceAll(fstName + ',', '') // 移除以姓名+逗号开头的部分
                                .replaceAll(',' + fstName, '') // 移除以逗号+姓名结尾的部分
                                .replaceAll(fstName, ''); // 移除仅包含姓名的部分
                            formattedSalesManList = `(${fstName})${filteredSalesManList}`;
                        }
                        this.$set(item, 'formattedSalesManList', formattedSalesManList);

                        if (!this.$utils.isEmpty(item.storePicPreKey) && item.acctType === 'Terminal') {
                            let urlData = this.$image.getSignedUrl(item.storePicPreKey);
                            this.$set(item, 'storeUrl', urlData);
                        } else {
                            this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
                        }
                    })

                }
            }
        });
       
      
       
        return {
            typeList: [],
            categoryLst: [],
            sublist: [],
            levelList: [],
            caplist:[],
            hideCreateButton: true, // 是否隐藏新建按钮
            chainStoreData,
            userInfo, // 用户信息
            isYangShengOrYouXuan,
        }
    },
    async created() {
        this.chainStoreData.methods.reload();
        this.$bus.$on('terminalListRefresh', async () => {
            await this.chainStoreData.methods.reload();
        });
        this.getTypeArray();
    },
    
    methods: {
        async getTypeArray() {
            const list = await this.$lov.getLovByTypeArray(['ACCT_TYPE', 'ACCNT_CATEGORY', 'SUB_ACCT_TYPE','ACCT_LEVEL','CAPACITY_LEVEL']);
            list[0].forEach(item => {
                this.typeList.push(item.val)
            });
            list[1].forEach(item => {
                this.categoryLst.push(item.val)
            });
            list[2].forEach(item => {
                this.sublist.push(item.val)
            });
            list[3].forEach(item => {
                this.levelList.push(item.val)
            });
            list[4].forEach(item => {
                this.caplist.push(item.val)
            });
        },
      
        /**
         * 门头照片预览
         * <AUTHOR>
         * @date 2025-05-09
         * @param param
         */
        async previewStoreUrl(param) {
            const compressSuffix = '/suoluetu';
            const defaultSuffix = 'default';
            if (this.$utils.isEmpty(param.storeUrl) || param.storeUrl.indexOf(defaultSuffix) !== -1) {
                return;
            }
            const inOptions = {
                current: param.storeUrl.replaceAll(compressSuffix, ''),
                urls: [param.storeUrl]
            };
            this.$image.previewImages(inOptions);
        },
        /**
         * 返回上一页
         * <AUTHOR>
         * @date 2025-05-09
         * @param data 返回携带终端数据
         */
        goBack (data) {
            let param = {
              chainHeadStoreName:data.acctName,
              chainHeadStoreId:data.id
            };
            this.$bus.$emit('basicsListRefresh',param);
            this.$nav.back()
        },
       
       
    }
}
</script>

<style lang="scss">

.es-terminal-list-page {

    .terminal-count {
        padding: 8px 16px;
        margin-right: 8px;
        white-space: nowrap;
        display: inline-block;
        background-color: #f2f2f2;
        color: #333333;
        border-radius: 4px;
    }
    .search-container {
        padding-left: 12px;
        color: #8C8C8C;
        font-size: 28px;
        text-align: center;
    }
    .terminal-list-item {
        background: #FFFFFF;
        width: 702px;
        margin: 24px auto auto auto;
        border-radius: 16px;
    }
    /*deep*/.link-item {
                padding: 0;
            }
    /*deep*/.link-item-icon {
                width: 0;
                padding-left: 0;
            }
    /*deep*/.link-dropdown-content {
                padding: 24px;
            }
    .terminal-list {
        .list-cell {
            .media-list {
                @include flex;
                padding: 24px 16px 24px 24px;
                .media-list-logo {
                    /*box-shadow: 0 4px 31px 0 rgba(0,44,152,0.22);*/
                    border-radius: 16px;
                    width: 128px;
                    height: 128px;
                    overflow: hidden;
                }
                .store-content {
                    width: 80%;
                    .store-content-top {
                        @include flex-start-center;
                        @include space-between;
                        margin-left: 24px;
                        .store-title {
                            font-family: PingFangSC-Semibold,serif;
                            font-size: 32px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 36px;
                            width: 77%;
                            height: 36px;
                            overflow: hidden;
                        }
                        .store-level {
                            margin-right: -3px;
                            width: 120px;
                            height: 44px;
                            image {
                                width: 100%;
                                height: 100%;
                            }
                        }
                    }

                    .store-content-middle {
                        display: flex;
                        justify-content: space-between;
                        padding-left: 32px;
                        .left ,.right{
                            @include flex-start-center;
                            flex-wrap: wrap;
                            margin-top: 10px;
                            .store-type {
                                white-space: nowrap;
                                border: 2px solid #2F69F8;
                                border-radius: 8px;
                                font-size: 20px;
                                padding-left: 18px;
                                padding-right: 18px;
                                line-height: 40px;
                                height: 40px;
                                color: #2F69F8;
                                margin-right: 10px;
                                margin-top: 10px;
                            }
                        }
                        .item-tag {
                            width: 58px;
                            height: 40px;
                            line-height: 36px;
                            text-align: center;
                            color: #ffffff;
                            background: #2F69F8;
                            box-shadow: 0 3px 4px 0 rgba(47,105,248,0.35);
                            border-radius: 8px;
                            padding-left: 27px;
                            padding-right: 27px;
                            font-size: 20px;
                            margin-right: 8px;
                            margin-top: 10px;
                        }
                    }
                    .store-content-representative {
                        @include flex;
                        align-items: center;
                        margin-left: 24px;
                        margin-top: 20px;
                        width: calc(100% - 24px);
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        .terminal-type {
                            color: #8C8C8C;
                            min-width: 50px;

                        }
                        .terminal-name {
                            font-size: 24px;
                            color: #000000;
                            letter-spacing: 0;
                            padding-left: 8px;
                            width: calc(100% - 50px);
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                        }
                    }
                    .store-content-choose {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }
                    .store-content-address {
                        margin-left: 24px;
                        margin-top: 18px;
                        font-family: PingFangSC-Regular,serif;
                        font-size: 24px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 32px;
                    }
                }
            }
        }
        .content-bottom {
            display: flex;
            justify-content: space-between;
            padding: 0 24px 10px;
            .view-port {
                position: relative;
                width: 100%;
                overflow: hidden;
                .label {
                    width: 90%;
                    display: flex;
                    flex-wrap: wrap;
                    position: relative;
                    .label-item {
                        padding: 4px 10px;
                        color: #2F69F8;
                        background-color: rgb(233,242,255);
                        margin: 6px 8px;
                        border-radius: 10px;
                        height: 34px;
                    }
                }
                .label::before {
                    position: absolute;
                    right: -12%;
                    bottom: 2px;
                    background-color: white;
                    content: '';
                    z-index: 2;
                    width: 12%;
                    height: 52px;
                }
                .iconZhankai {
                    width: 10%;
                    position: absolute;
                    top: 0;
                    right: 0;
                    height: 100%;
                    text-align: right;
                }
            }
        }
    }
}
</style>
