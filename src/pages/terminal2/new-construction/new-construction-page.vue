<template>
    <link-page class="new-construction-page">
        <lnk-taps :taps="tapsOptions" v-model="tapsActive" :onlyTabChange="true" @switchTab="switchTab" v-if="pageParam.editFlag !== 'edit'"></lnk-taps>
        <view class="content-container" :style="{'padding-top': pageParam.editFlag !== 'edit' ? '112rpx' : '0'}">
            <view class="content-item" v-if="tapsActive.seq === '1'">
                <new-client-basics :formOption="basicOption"
                                   :listOauth="pageParam.listOauth"
                                   :claimFlag="claimFlag"
                                   ref="clientBasics"
                                   :key="key"
                                   :editFlag="pageParam.editFlag"
                                   :broadCompanyCode="broadCompanyCode"
                                   :storeHeadLength="storeHeadLength"
                                   @addressModify="addressModify"
                                   @getLocation="getLocation"
                                   @checkType="checkCustomerType"
                                   :coordinate="coordinate"
                                   :isShiJiaZhuang="isShiJiaZhuang"
                                   :isGuoJiao="isGuoJiao"
                                   :isJiaoLing="isJiaoLing"
                                   :isChuanDong="isChuanDong"
                                   :isXinLingShou="isXinLingShou"
                                   :isNewShiJiaZhuang="isNewShiJiaZhuang"
                                   :isXingXiangDian="isXingXiangDian"
                                   :isChuanDongOrXingJi="isChuanDongOrXingJi"
                                   :isDaHongYong="isDaHongYong"
                                   :isHuaiJiu="isHuaiJiu"
								   :sellProductArr="sellProductArr"
                                   @storeHeadArr="storeHeadArr"></new-client-basics>
            </view>
            <view class="content-item" v-if="tapsActive.seq === '2'">
                <item title="认证" class="authentication" :arrow="false" v-if="attestationShowFlag">
                    <view @tap="showLog" v-if="authenticationFlag=='N'">
                        <link-switch v-model="authenticationFlag" :disabled="!!switchFlag"/>
                    </view>
                    <view v-if="authenticationFlag=='Y'">
                        <link-switch v-model="authenticationFlag"/>
                    </view>
                </item>
                <financial-info :financialArr.sync="financialArr"
                                @switchChange="switchChange"
                                @deleteChildAccount="deleteChildAccount"
                                @financialPhoto="financialPhoto"
                                @imgUploadSuccess='newImgUploadSuccess'
                                @idCardFront="idCardFront"
                                @idCardBack="idCardBack"
                                @creditNoChange="creditNoChange"
                                @ocrResult="ocrResult"
                                :licenseArr="financialPhotoArr"
                                :idCardFrontArr="idCardFrontArr"
                                :idCardBackArr="idCardBackArr"
                                :authentication="authenticationFlag"
                                :addAccountFlag="addAccountFlag"
                                @addAccounts="addAccounts"
                                :isXinJiuYeOrRongCheng="isXinJiuYeOrRongCheng"
                                :isOnlyTeQu="isOnlyTeQu"
                                :isDaHongYongJiao="isDaHongYongJiao"
                                :isNewHuaiJiu="isNewHuaiJiu"
                                :isShiJiaZhuang="isShiJiaZhuang"
                                :isChuanDong="isChuanDong"
                                :orcEntranceFlag="orcEntranceFlag"
                                :orcIdCardFlag="orcIdCardFlag"
                                ref="financialInfo"></financial-info>
            </view>
            <view class="content-item" v-if="tapsActive.seq === '3'">
                <sale-product :sellProductArr=sellProductArr
                              :newTerminal="true"
                              :formOption="basicOption"
                              @deleteIndex="deleteIndex"
                              @addSellProduct="addSellProduct"
                              @changeStatus="changeStatus"
                ></sale-product>
            </view>
            <view class="content-item" v-if="tapsActive.seq === '4'">
                <contacts-info :contactsArr="contactsArr"
                               :acctId="accntId"
                               :formOption="basicOption"
                               :claimFlag="claimFlag"></contacts-info>
            </view>
            <!-- 收货地址 -->
            <view class="content-item" v-show="tapsActive.seq === '5'">
                <address-info :basicOption="basicOption" @getLocation='getLocation' ref="addrInfo"></address-info>
            </view>
        </view>
        <link-sticky class="bottom-sticky">
            <view class="sticky">
                <link-button block size="large" @tap="submit" :shadow="shadow" autoLoading>提交</link-button>
            </view>
        </link-sticky>
        <!-- <approve-upload-dialog :accountImgArray="accountImgArray"  :uploadDialog="uploadDialog"  @imgUploadSuccess="imgUploadSuccess" @gotoTerminalList="gotoTerminalList" /> -->
        <link-dialog ref="locationFailSelectAddress" class="location-select-address" position="poster">
            <view class="address-t">
                <view style="width: 100%;height: 30px;line-height: 30px">
                    <view style="width: 100%;text-align: center;">当前5G网络不稳定企微生态获取定位失败,请手动选择就近地址</view>
                </view>
                <view class="address-v">
                    <view>
                        <item title="所在地区">
                            <link-address placeholder="请选择所在地区"
                                          :province.sync="selectAddressObj.province"
                                          :city.sync="selectAddressObj.city"
                                          :district.sync="selectAddressObj.district"/>
                        </item>
                    </view>
                    <view class="t-left">
                        <view class="title">详细地址</view>
                        <view class="val">
                            <link-input type="text" v-model="selectAddressObj.addr" placeholder="请填写或选择单位名称"/>
                        </view>
                        <view class="ent-wrap" @tap="searchAddress()">
                            <view class="iconfont icon-sousuo"
                                  style="float: left;line-height: 30px;font-size: 12px;width: 10%"></view>
                            <view style="float: right;font-size: 12px;width: 80%;margin-left: 5px;">智能联想地址</view>
                        </view>
                    </view>
                </view>
                <scroll-view scroll-y="true" class="list-container">
                    <view v-for="(item1,index) in suggestionAddressData" :key="index">
                        <view class="list-item" @tap="selectAddress(item1)">
                            <view class="left-content">
                                <view class="row-1">
                                    <view class="name">{{item1.title}}</view>
                                    <view class="address">{{item1.address}}</view>
                                </view>
                            </view>
                            <view class="right-content">
                                <view v-if="item1._checked">
                                    <link-icon size="1.8em" style="color:#2F69F8;font-size: 16px" icon="icon-check"/>
                                </view>
                            </view>
                        </view>
                    </view>
                </scroll-view>
                <link-button slot="foot" @tap="confirmAddress" block>确定</link-button>
            </view>
        </link-dialog>
    </link-page>
</template>

<script lang="jsx">
    import lnkTaps from '../../core/lnk-taps/lnk-taps'
    import newClientBasics from './components/new-client-basics'
    import saleProduct from './components/sale-product'
    import financialInfo from './components/financial-info'
    import contactsInfo from './components/contacts-info'
    import addressInfo from './components/address-info'
    import lnkNoAuth from "../../core/lnk-no-auth/lnk-no-auth"
    import LnkImg from '../../core/lnk-img/lnk-img';
    import ApproveUploadDialog from './components/approve-upload-dialog';
    import {PageCacheManager} from "../../../utils/PageCacheManager";
    import Taro from "@tarojs/taro";
    import {env} from "../../../../env";
    import {ComponentUtils} from 'link-taro-component';

    const waitTime = 5;
    export default {
        name: "new-construction-page",
        data () {
            // 5G时定位出现问题后，选择省市区县智能联想地址选择获取经纬度
            const selectAddressObj = {
                province:'',
                city:'',
                district:'',
                addr: '',
            };
            const contactsOption = {
                contactsName: '',                                                      // 姓名
                contactsSex: '',                                                       // 性别
                mobilePhone: '',                                                       // 联系电话
                fixedPhone: '',                                                        // 备用电话
                birthdayType: '',                                                      // 生日类型
                birthYear: '',                                                         // 生日年份
                birthday: '',                                                          // 生日日期
                contactsType: '',                                                      // 类型
                winePromoterFlag: 'N',                                                 // 是否为荐酒员
                mainFlag: 'Y',                                                         // 是否是主要联系人
                comments: null,                                                        // 备注
                isEffective: 'Y',                                                      // 状态
                belongTo: '',                                                          // 属于模块
                postnId: this.$taro.getStorageSync('token').result.postnId,            // 职位id
                orgId: this.$taro.getStorageSync('token').result.orgId,                // 组织id
                unfoldFlag: true                                                       // 折叠参数
            };
            const financialOption = {
                invoiceCategory: 'company',                                            //客户性质
                credentType: 'ValueAddedTax',                                          // 开票类型
                billTitle: null,                                                       // 营业执照名称
                creditNo: null,                                                        // 统一社会信用代码
                accountBankName: null,                                                 // 开户行名称
                mainBillAccntId: null,                                                 // 银行账户
                billPhone: null,                                                       // 注册电话
                billAddr: null,                                                        // 公司注册详细地址
                taxpayerNumber: null,                                                  // 联行号
                receiveBillContact: null,                                              // 收票联系人
                receiveBillPhone: null,                                                // 收票联系方式
                fixedName: null,                                                       // 电票接收人姓名
                receiveBillEmail: null,                                                // 电票接收邮箱
                ticketingPhone: null,                                                  // 电票接收电话
                receiveBillProvince: null,                                             // 收票人邮寄地区-省份
                receiveBillCity: null,                                                 // 收票人邮寄地区-城市
                receiveBillDistrict: null,                                             // 收票人邮寄地区-区县
                receiveBillAddr: null,                                                 // 收票详细地址
                multiAcctFlag: 'N',                                                    // 是否一户多开
                multiAcctMainFlag : 'Y',                                               // 是否为主户头
                unfoldFlag: true  //折叠参数
            };
            const basicOption = {
                assessOrgName: null,
                organizationName: null,
                assessOrgCode: null,
                acctType: null,                                                                                            // 客户一级分类
                acctCategory: null,                                                                                        // 客户二级分类
                subAcctType: null,                                                                                         // 客户三级分类
                acctName: '',                                                                                              // 门头名称
                simName: null,                                                                                             // 客户简称
                province: null,                                                                                            // 省
                city: null,                                                                                                // 市
                district: null,                                                                                            // 区
                address: null,                                                                                             // 详细地址
                storePicPreKey: null,                                                                                      // 门头照片
                chainStoreFlag: 'N',                                                                                       // 是否为连锁模式
                isExclusiveShop: null,                                                                                       //是否泸州老窖官方形象店
                isShareHolderShop: null,                                                                                    //是否泸州老窖股东店
                channelMemberFlag: 'N',                                                                                       // 是否渠道会员
                chainHeadStoreFlag: 'N',                                                                                   // 是否为连锁总店
                id: this.pageParam.editFlag === 'edit' ?  this.pageParam.accntId : '',                                     // id
                chainHeadStoreId: null,                                                                                    // 请选择连锁总店id
                chainHeadStoreName: null,                                                                                  // 连锁总店名称
                joinFlag: 'Y',                                                                                             // 是否已合作
                acctLevel: null,                                                                                           // 容量级别
                judgmentFlag: 'N',                                                                                         // 是否为品鉴基地
                appreciationFlag: 'N',                                                                                     // 是否为活动场地
                capacityLevel: null,                                                                                       // 容量级别
                imageRoomNum: null,                                                                                        // 形象包间数
                roomTotalNum: null,                                                                                        // 包间总数
                hallTotalNum: null,                                                                                        // 大厅总数
                xAttr50: null,                                                                                             // 店区位
                area: null,                                                                                                // 店面积
                comments: null,                                                                                            // 备注
                postnId: this.$taro.getStorageSync('token').result.postnId,                                                // 职位id
                orgId: this.$taro.getStorageSync('token').result.orgId,                                                    // 组织id
                acctStage: null,                                                                                           // 客户阶段
                creditChangedFlag: 'N',                                                                                    // 税号/身份证号是否改变，用以判断是否走唯一性校验，父客户等逻辑
                chainChangedFlag: 'N',                                                                                     // 校验当前终端是否从连锁模式变为普通终端，重走唯一性校验，用于获取不同的校验返回信息
                saleCateList: [],                                                                                          // 用于校验[是否子公司供货]和实际所售产品数据是否匹配
                acctStatus: 'Y',                                                                                           // 状态
                multiAcctFlag: 'N',                                                                                        // 是否一户多开
                multiAcctMainFlag : 'Y',                                                                                   // 是否为主户头
                storePicId : null,                                                                                         // 门头照片的id
                auditStatus : 'new',                                                                                       // 审批状态
                longitude: '',                                                                                             // 经度
                latitude: '',                                                                                              // 纬度
                accuracy: '',                                                                                              // 精度
                mdmCompanyCode: this.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode,           // 品牌公司代码
                kaSystemName: '',                                                                                          // 系统名称
                xAttr51: '',                                                                                               // 所属系统ID
                xAttr71: '',                                                                                               // 店号
                companyId: this.$taro.getStorageSync('token').result.coreOrganizationTile['l3Id'],                         // 公司ID
                credentType: '',    // credentType
                creditNo: '',   // 统一社会信用代码
            };
            const userInfo = this.$taro.getStorageSync('token').result;
            const initialData = {
                // 按钮等待时间
                buttonTime: 0,
                key: false,
                claimFlag: this.pageParam.claimFlag ? true : false, //认领终端标志
                contactsOption,                                                         // 联系人对象
                contactsArr:[],                                                         // 联系人数组
                sellProductArr:[],                                                      // 所售产品数组
                financialArr:[],                                                        // 财务信息数组
                basicOption,                                                            // 基础信息
                financialOption,                                                        // 财务信息
                storeHeadLength: 0,                                                     // 门头照片长度
                financialPhotoArr: [],                                                  // 营业执照照片
                idCardBackArr: [],                                                      // 身份证反面照片
                idCardFrontArr: [],                                                     // 身份证正面照片
                accntId: null,                                                          // 客户id
                authenticationFlag: 'N',                                                // 财务认证标志
                shadow: true,                                                           // 按钮阴影
                addAccountFlag: false,                                                  // 添加户头标志
                childAccountFlag: false,                                                // 子户头模块标志
                tapsOptions: [
                    {defaultValue: "N", id: "222323332756804060", name: "基础信息", seq: "1", type: "INTERACTION_TYPE", val: "Vote"},
                    {defaultValue: "N", id: "222323332756804060", name: "所售产品", seq: "3", type: "INTERACTION_TYPE", val: "Vote"},
                    {defaultValue: "N", id: "222323332756804060", name: "联系人", seq: "4", type: "INTERACTION_TYPE", val: "Vote"},
                    {defaultValue: "N", id: "222323332756804060", name: "财务信息", seq: "2", type: "INTERACTION_TYPE", val: "Vote"},
                    {defaultValue: "N", id: "222323332756804060", name: "收货地址", seq: "5", type: "INTERACTION_TYPE", val: "Vote"}
                ],             // 顶部状态栏
                marginTop: true,                // 发票类型状态栏距离顶部距离标志
                attestationShowFlag: true,      // 认证switch控制显隐参数
                tapsActive: {},                 // 顶部状态栏选中对象
                scrollTop: 0,                   // 页面距离顶部距离
                coordinate: null,               // 经纬度
                basicInfoEdit: {},              // 编辑信息的原始数据
                overhangFlag: false,            // 超距标志
                allowDistance: 0,               // 终端新建允许范围
                textareaInputFlag: false,
                chooseData: {},
                saleCateList: [],
                allSaleCateList: [],
                uploadDialog: false,              // 附件上传附件弹框
                accountImgArray: {},                  //附件图片
                newImgArray:{},//新附件图片
                insertAccountData: [],
                initFlowAttachment: [],                 //初始化审批附件
                parContactId:'',//与子户头的联系人信息一致的主户头联系人id
                contactList: [],//终端维护的联系人
                userInfo,  // 获取用户信息,
                broadCompanyCode: '', // 博大公司编码集
                selectAddressObj, // 5G时定位出现问题后，选择省市区县智能联想地址选择获取经纬度
                suggestionAddressData: [],//根据输入智能联想地址列表
                locationFunData:{},//用于存放调用定位时的标识
                openSettingNum: 1,//授权次数 默认为1 如果没授权的情况下 5G网络 定位问题 第一次先授权只要授过权次数加1 当次数>1 还是拿不到定位就给默认信息吧....
                isYangShengOrYouXuan: ['1612'].includes(userInfo.coreOrganizationTile.brandCompanyCode),
                unLimitFlag: false, //不做距离校验
                isJiaoLing: (this.pageParam.editFlag && this.pageParam.editFlag === 'edit') ? this.pageParam.data.mdmCompanyCode==='5151' : userInfo.coreOrganizationTile.brandCompanyCode === '5151',
                isXinJiuYeOrRongCheng: ['5600','1210','1204','5910'].includes(userInfo.coreOrganizationTile.brandCompanyCode), //国窖、新酒业、怀旧、蓉城
                isOnlyTeQu: ['5137'].includes(userInfo.coreOrganizationTile.brandCompanyCode), //特曲
                isDaHongYongJiao: ['5161','5903','5902','5151','5153'].includes(userInfo.coreOrganizationTile.brandCompanyCode), //大成、鸿庐、永粮、窖龄、新酒业
                isXinLingShou: (this.pageParam.editFlag && this.pageParam.editFlag === 'edit') ? this.pageParam.data.mdmCompanyCode==='1222' : userInfo.coreOrganizationTile.brandCompanyCode === '1222', //新零售
                isChuanDongOrXingJi: userInfo.coreOrganizationTile.l5Id === '521074619762290688' || userInfo.coreOrganizationTile.brandCompanyCode === '1612', //根据终端orgId判断 或者当前登录用户-川东或新冀
                isNewShiJiaZhuang: userInfo.coreOrganizationTile.l5Id === '521067754441617408', //根据orgId判断 或者当前登录用户
                isDaHongYong: (this.pageParam.editFlag && this.pageParam.editFlag === 'edit') ? ['5161','5903','5902'].includes(this.pageParam.data.mdmCompanyCode): ['5161','5903','5902'].includes(userInfo.coreOrganizationTile.brandCompanyCode), //大成、鸿庐、永粮
                isHuaiJiu: (this.pageParam.editFlag && this.pageParam.editFlag === 'edit') ? ['5910','5909'].includes(this.pageParam.data.mdmCompanyCode): ['5910','5909'].includes(userInfo.coreOrganizationTile.brandCompanyCode), //怀旧蓉城、乾昇
                isXingXiangDian: userInfo.coreOrganizationTile.l4Id === '328927981139325498', //形象店
                isNewHuaiJiu: ['1204','5910'].includes(userInfo.coreOrganizationTile.brandCompanyCode), //怀旧、蓉城
                isGuoJiao: userInfo.coreOrganizationTile.brandCompanyCode === '5600',
                isShiJiaZhuang: userInfo.coreOrganizationTile.l5Id === '521067754441617408',
                orcEntranceFlag: 'N', // 是否调用OCR营业执照识别接口
                orcIdCardFlag: 'N', // 是否调用OCR身份证识别接口
                initAcctCategory: '', //记录初始值
                changeFlag: false, //判断是否需要校验距离
                isChuanDong: false,
                // showFortTerminal: false // 是否星火终端显隐控制
            };
            return PageCacheManager.getInitialData({
                ctx: this,
                path: 'terminal2/new-construction/new-construction-page.vue',
                title: '终端客户-基础信息',
                initialData,
                initializer: () => {this.initPage()}
            })
        },
        components: {
            lnkTaps,
            newClientBasics,
            saleProduct,
            financialInfo,
            contactsInfo,
            lnkNoAuth,
            LnkImg,
            ApproveUploadDialog,
            addressInfo
        },
        onPageScroll(e) {
            this.scrollTop = e.scrollTop
        },
        mounted() {
            this.$bus.$on('basicsListRefresh', data => {
                if(data&&data.chainHeadStoreId&&data.chainHeadStoreName&&this.basicOption) {
                    this.$set(this.basicOption, 'chainHeadStoreId', data.chainHeadStoreId)
                    this.$set(this.basicOption, 'chainHeadStoreName', data.chainHeadStoreName)
                }
            })
            if (this.pageParam.editFlag === 'edit') {
                this.$taro.setNavigationBarTitle({title: `编辑${this.pageParam.title}`});
            }
        },
        async onShow(){
            const location = this.$locations.QQGetLocation();
            if(location && this.getLocationFlag){
                this.getLocationFlag = false;
                if (this.$utils.isNotEmpty(location)){
                    // @edit by 谭少奇 2023/10/12 收货地址赋值
                    if(this.locationFunData.isDeliverFlag === 'newArea'){
                        const {address, city, district, latitude, longitude, name, province} = location
                        Object.assign( this.$refs['addrInfo'].addressList[this.locationFunData.index], {
                            city,
                            district,
                            province,
                            addr: name,
                            latitude,
                            longitude
                        })
						//获取乡镇/街道信息
						let allAddress = await this.$locations.reverseTMapGeocoder(latitude, longitude, '终端分销商新建');
                        const townName = allAddress['originalData'].result.addressComponent.townName
						this.$refs['addrInfo'].addressList[this.locationFunData.index].town = allAddress['originalData'].result.addressComponent.town;
						this.$refs['addrInfo'].addressList[this.locationFunData.index].townName = townName;
                        // 区域展示处理
                        if(province && this.$utils.isEmpty(townName)){
                            this.$refs['addrInfo'].addressList[this.locationFunData.index].regionInfo = `${province}/${city}/${district}`;
                        } else if(province){
                            this.$refs['addrInfo'].addressList[this.locationFunData.index].regionInfo = `${province}/${city}/${district}/${townName}`
                        }
                        return
                    }
                    if(this.locationFunData.isDeliverFlag !== 'deliverAddr'){
                        this.basicOption.longitude = location.longitude;
                        this.basicOption.latitude = location.latitude;
                    }
                    let distance = await this.$locations.getDistance(Number(this.coordinate.latitude), Number(this.coordinate.longitude), Number(location.latitude), Number(location.longitude));
                    let resData = {res: location, flag: 'success', distance: distance};
                    let acctType = await this.$lov.getValByTypeAndName('ACCT_TYPE', this.basicOption.acctType);
                    let acctCategory = await this.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', this.basicOption.acctCategory, 'ACCT_TYPE', acctType);
                    this.chooseData = resData;
                    if(this.locationFunData.isDeliverFlag !== 'deliverAddr')  {
                        this.overhangFlag = resData.distance * 1000 > this.allowDistance;
                    }
                    if (!this.unLimitFlag && this.overhangFlag && (acctCategory !== 'qdlx-2' && acctCategory !== 'qdlx-4' && acctType==='Terminal') && this.locationFunData.isDeliverFlag !== 'deliverAddr') {
                        this.overhangDialog();
                    } else {
                        let address = await this.$locations.reverseTMapGeocoder(resData.res.latitude, resData.res.longitude, '终端分销商新建');
                        if(this.locationFunData.isDeliverFlag === 'deliverAddr'){
                            this.$set(this.basicOption, 'deliveryProvince', address['originalData'].result.addressComponent.province)
                            this.$set(this.basicOption, 'deliveryCity', address['originalData'].result.addressComponent.city)
                            this.$set(this.basicOption, 'deliveryDistrict', address['originalData'].result.addressComponent.district)
                            this.$set(this.basicOption, 'deliveryTown', address['originalData'].result.addressComponent.town)
                            this.$set(this.basicOption, 'deliveryTownName', address['originalData'].result.addressComponent.townName)
                            this.$set(this.basicOption, 'deliveryLatitude', resData.res.latitude);
                            this.$set(this.basicOption, 'deliveryLongitude', resData.res.longitude);
                            const chooseAddress = resData.res.address.replace(this.basicOption.province, '').replace(this.basicOption.city, '').replace(this.basicOption.district, '').replace(this.basicOption.townName, '')
                            this.$set(this.basicOption, 'deliveryDetailAddr', chooseAddress + resData.res.name)
                            return;
                        }
                        try {
                            this.$utils.showLoading()
                            const data = await this.$http.post('/action/link/alladdress/queryEffectiveByTownCode',{addrCode:address['originalData'].result.addressComponent.town})
                            if(data.success) {
                                if(data.adcodeFlag){
                                    this.$set(this.basicOption, 'adcode', data.adcode)
                                    this.$set(this.basicOption, 'province', data.province)
                                    this.$set(this.basicOption, 'provinceCode', data.provinceCode)
                                    this.$set(this.basicOption, 'city', data.city)
                                    this.$set(this.basicOption, 'cityCode', data.cityCode)
                                    this.$set(this.basicOption, 'district', data.district)
                                    this.$set(this.basicOption, 'districtCode', data.districtCode)
                                    this.$set(this.basicOption, 'town', data.townCode)
                                    this.$set(this.basicOption, 'townName', data.townName)
                                    const chooseAddress = resData.res.address.replace(this.basicOption.province, '').replace(this.basicOption.city, '').replace(this.basicOption.district, '').replace(this.basicOption.townName, '')
                                    this.$set(this.basicOption, 'address', chooseAddress + resData.res.name)
                                    this.$utils.hideLoading();
                                } else {
                                    this.$set(this.basicOption, 'adcode', '')
                                    this.$set(this.basicOption, 'town', address['originalData'].result.addressComponent.town)
                                    this.$set(this.basicOption, 'townName', address['originalData'].result.addressComponent.townName)
                                    this.$set(this.basicOption, 'province', address['originalData'].result.addressComponent.province)
                                    this.$set(this.basicOption, 'provinceCode', this.basicOption.town.slice(0, 2) + '0000')
                                    this.$set(this.basicOption, 'city', address['originalData'].result.addressComponent.city)
                                    this.$set(this.basicOption, 'cityCode', address.result.ad_info.city_code.slice(3))
                                    this.$set(this.basicOption, 'district', address['originalData'].result.addressComponent.district)
                                    this.$set(this.basicOption, 'districtCode', this.basicOption.town.slice(0, 6))
                                    const chooseAddress = resData.res.address.replace(this.basicOption.province, '').replace(this.basicOption.city, '').replace(this.basicOption.district, '')
                                    this.$set(this.basicOption, 'address', chooseAddress + resData.res.name)
                                    this.$utils.hideLoading();
                                }
                            }
                        }catch (e) {
                            this.$utils.hideLoading();
                        }
                    }
                }
            }
        },
        async created() {
            //认领终端跳转
            if(this.claimFlag && this.pageParam.basicDatail) {
                const basicOption= this.$utils.deepcopy(this.pageParam.basicDatail);
                let userInfo = Taro.getStorageSync('token').result;
                if(!this.$utils.isEmpty(this.pageParam.basicDatail.contactsList) && this.pageParam.basicDatail.contactsList.length >0) { //联系人
                    this.contactsArr = this.$utils.deepcopy(this.pageParam.basicDatail.contactsList);
                    this.contactsArr[0].unfoldFlag = true;
                }
                if(!this.$utils.isEmpty(this.pageParam.basicDatail.children) && this.pageParam.basicDatail.children.length >0){ //财务
                    this.addAccountFlag = true; //一户多开标志
                    const children=this.$utils.deepcopy(this.pageParam.basicDatail.children);
                    children.forEach(item =>{
                        for (let it in item){
                            if(it === undefined){
                                it = ''
                            }
                        }
                        //财务信息子户头附件上传
                        if(!this.$utils.isEmpty(item.attachmentList) && item.attachmentList.length >0 ){
                            item.attachmentList.forEach((obj)=>{
                                const that = this;
                                const url = this.getRandFileName(basicOption.id,obj.attachmentPath,userInfo);
                                const copySource = env.cosUploadUrl.replace(/^https?:\/\//, '') + obj.attachmentPath;
                                let param ={
                                    headId: item.id,                                // 模块id
                                    attachmentPath: url,                   // 原图key
                                    smallurl: url + '/suoluetu',                            // 缩略图key
                                    moduleType: obj.moduleType,                        // 所属模块
                                    dataSource: obj.dataSource,                                  // 照片来源
                                    longitude: obj.longitude ? obj.longitude : '',           //如果有使用了地址信息，则将经纬和地址信息保存下来
                                    latitude: obj.latitude ? obj.latitude : '',
                                    address: obj.address ? obj.address : '',
                                }
                                that.$image.copyCos(url,copySource,param);
                            })
                        }
                    })
                    this.financialArr = children; //财务信息子户头
                }
                this.authenticationFlag = 'Y'; //开关打开
                this.accntId = basicOption.id;
                //主户头复制
                const financial = {
                    id: basicOption.id? basicOption.id: '',
                    invoiceCategory: basicOption.invoiceCategory? basicOption.invoiceCategory: '', //客户性质
                    credentType: basicOption.credentType ? basicOption.credentType: '', //类型
                    billTitle: basicOption.billTitle ? basicOption.billTitle:'',//营业执照名称
                    creditNo: basicOption.creditNo ? basicOption.creditNo:'', //身份证
                    accountBankName: basicOption.accountBankName ? basicOption.accountBankName :'',
                    mainBillAccntId: basicOption.mainBillAccntId ? basicOption.mainBillAccntId :'', //银行账户
                    billPhone: basicOption.billPhone ? basicOption.billPhone :'',
                    billAddr: basicOption.billAddr ? basicOption.billAddr : '',
                    registrationDate: basicOption.registrationDate ? basicOption.registrationDate : '',
                    period: basicOption.period ? basicOption.period : '', //经营期限
                    person: basicOption.person ? basicOption.person : '', //法人姓名
                    business: basicOption.business ? basicOption.business : '', //经营业务
                    capital: basicOption.capital ? basicOption.capital:'',
                    taxpayerNumber: basicOption.taxpayerNumber ? basicOption.taxpayerNumber : '',
                    receiveBillContact: basicOption.receiveBillContact ? basicOption.receiveBillContact : '',
                    receiveBillPhone: basicOption.receiveBillPhone ? basicOption.receiveBillPhone : '',
                    receiveBillProvince: basicOption.receiveBillProvince ? basicOption.receiveBillProvince : '',
                    receiveBillCity: basicOption.receiveBillCity ? basicOption.receiveBillCity : '',
                    receiveBillDistrict: basicOption.receiveBillDistrict ? basicOption.receiveBillDistrict : '',
                    receiveBillAddr: basicOption.receiveBillAddr ? basicOption.receiveBillAddr : '',
                    fixedName: basicOption.fixedName ? basicOption.fixedName: '',
                    receiveBillEmail: basicOption.receiveBillEmail ? basicOption.receiveBillEmail: '',
                    ticketingPhone: basicOption.ticketingPhone ? basicOption.ticketingPhone : '',
                    multiAcctFlag: this.pageParam.basicDatail.children && this.pageParam.basicDatail.children.length>0  ? 'Y':'N',           // 是否一户多开
                    multiAcctMainFlag : 'Y',                                               // 是否为主户头
                    unfoldFlag: true
                }
                this.financialArr.unshift(financial); //主户头插入
                const acctTypeName = await this.$lov.getNameByTypeAndVal('ACCT_TYPE', this.pageParam.basicDatail.acctType);
                const acctCategoryName = await this.$lov.getNameByTypeNameParentTypeParentVal('ACCNT_CATEGORY', this.pageParam.basicDatail.acctCategory, 'ACCT_TYPE', this.pageParam.basicDatail.acctType)
                if(!this.$utils.isEmpty(this.pageParam.basicDatail.subAcctType)){
                    const subAcctTypeName = await this.$lov.getNameByTypeNameParentTypeParentVal('SUB_ACCT_TYPE', this.pageParam.basicDatail.subAcctType, 'ACCNT_CATEGORY', this.pageParam.basicDatail.acctCategory);
                    this.$set(basicOption,'subAcctType',subAcctTypeName);
                }
                this.$set(basicOption,'acctType',acctTypeName);
                this.$set(basicOption,'acctCategory',acctCategoryName);
                this.$set(basicOption,'channelMemberFlag','N'); //是否精英会员默认为N
                this.basicOption = basicOption;
                //复制图片
                if(basicOption.attachmentList.length >0){
                    for (const item of basicOption.attachmentList) {
                        const url = this.getRandFileName(basicOption.id,item.attachmentPath,userInfo);
                        const copySource = env.cosUploadUrl.replace(/^https?:\/\//, '') + item.attachmentPath;
                        let param ={
                            headId: basicOption.id,                                // 模块id
                            attachmentPath: url,                   // 原图key
                            smallurl: url + '/suoluetu',                            // 缩略图key
                            moduleType: item.moduleType,                        // 所属模块
                            dataSource: item.dataSource,                                  // 照片来源
                            longitude: item.longitude ? item.longitude : '',           //如果有使用了地址信息，则将经纬和地址信息保存下来
                            latitude: item.latitude ? item.latitude : '',
                            address: item.address ? item.address : '',
                        }
                        if(param.moduleType === 'agrSign'){
                            //门头照片
                            await this.$image.copyCos(url,copySource,param);
                        }else{
                            this.$image.copyCos(url,copySource,param);
                        }
                    }
                    this.key = true;
                }
            }
            //参数配置获取地址距离不校验-定制酒公司1209，新零售公司1222
            const getSapCompany = await this.$utils.getCfgProperty('terminal_remove_limit');
            const sapCompany = getSapCompany.split(',');
            this.unLimitFlag = (this.pageParam.editFlag && this.pageParam.editFlag === 'edit') ? sapCompany.includes(this.pageParam.data.mdmCompanyCode) : sapCompany.includes(this.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode);
            if(this.pageParam.editFlag && this.pageParam.editFlag === 'edit'){ //如果是编辑-重新判断//川东 新冀、石家庄
                await this.getOrg();
            }
            const chuanDongOrgId = await this.$utils.getCfgProperty('East_Sichuan_Tequ_Area_ID') || '521074619762290688'
			// @mark edit by 谭少奇 2023/07/24 10:18 兴冀公司判断由组织ID变更为SAP编码
			this.isChuanDong = this.userInfo.coreOrganizationTile.l5Id === chuanDongOrgId || this.userInfo.coreOrganizationTile.brandCompanyCode === '1612'
            if(this.isChuanDongOrXingJi && !this.basicOption.accntPartner) {
                this.basicOption.accntPartner = 'non-cooperation'
            }
            // 获取是否博大公司标识编码
            this.broadCompanyCode = await this.queryCfgProperty('getPurchaseSumForOrder');
            this.orcEntranceFlag = await this.$utils.getCfgProperty('OCR_ENTRANCE_FLAG');
            this.orcIdCardFlag = await this.$utils.getCfgProperty('OCR_IDCARD_FLAG');
            // 博大公司【合作状态】默认值为未合作
            if (this.broadCompanyCode.indexOf(this.userInfo.coreOrganizationTile.brandCompanyCode) !== -1) {
                this.$set(this.basicOption, 'joinFlag', 'N');
            }
            Promise.all([
                this.$lov.getLovByType('ACCT_TYPE'),
                this.$lov.getLovByType('ACCNT_CATEGORY'),
                this.$lov.getLovByType('SUB_ACCT_TYPE'),
            ]).then(([ACCT_TYPE, ACCNT_CATEGORY, SUB_ACCT_TYPE]) => {
                this.acctCascadeData = {
                    ACCT_TYPE: ACCT_TYPE.filter(item => item.val === 'Terminal' || item.val === 'Distributor'),
                    ACCNT_CATEGORY,
                    SUB_ACCT_TYPE
                };
            });
        },
        computed: {
            switchFlag() {
                let message = ''
                let {contactInfoFlag, msg} = this.doCheckContact()
                if(contactInfoFlag) message = msg
                if (this.sellProductArr.filter(item => item.status === 'Y').length === 0 && this.pageParam.sellProductFlag) message = '您尚未维护该终端的有效所售产品，请维护有效所售产品'
                return message
            }
        },
        watch:{
            async 'basicOption.acctCategory'(newVal) {
                let acctType = await this.$lov.getValByTypeAndName('ACCT_TYPE', this.basicOption.acctType);
                if (newVal && acctType==='Terminal') {
                    let newAcctCategoryVal = await this.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', newVal, 'ACCT_TYPE', acctType);
                    let oldAcctCategoryVal = await this.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', this.initAcctCategory, 'ACCT_TYPE', acctType);
                    if (['qdlx-2', 'qdlx-4'].includes(newAcctCategoryVal) || newAcctCategoryVal == oldAcctCategoryVal || (!['qdlx-2', 'qdlx-4'].includes(newAcctCategoryVal) && !['qdlx-2', 'qdlx-4'].includes(oldAcctCategoryVal))) {
                        this.changeFlag = false
                    } else {
                        this.changeFlag = true
                    }
                }
            },
            // 2024/07/29 @邓佳柳 切换客户大类，业务需求 基础填写的信息都重置
            async 'basicOption.acctType'(newVal, oldVal) {
                if (oldVal && newVal !== oldVal) {
                    let { acctType, acctCategory, subAcctType,id} = this.basicOption
                    this.basicOption = {
                        id,acctType, acctCategory, subAcctType,                                                                             // 客户三级分类
                        acctName: '',                                                                                              // 门头名称
                        simName: '',                                                                                             // 客户简称
                        province: null,                                                                                            // 省
                        city: null,                                                                                                // 市
                        district: null,                                                                                            // 区
                        address: null,                                                                                             // 详细地址
                        storePicPreKey: null,                                                                                      // 门头照片
                        chainStoreFlag: 'N',                                                                                       // 是否为连锁模式
                        isExclusiveShop: null,                                                                                       //是否泸州老窖官方形象店
                        isShareHolderShop: null,                                                                                    //是否泸州老窖股东店
                        channelMemberFlag: 'N',                                                                                       // 是否渠道会员
                        chainHeadStoreFlag: 'N',                                                                                   // id
                        chainHeadStoreId: null,                                                                                    // 请选择连锁总店id
                        chainHeadStoreName: null,                                                                                  // 连锁总店名称
                        joinFlag: 'Y',                                                                                             // 是否已合作
                        acctLevel: null,                                                                                           // 容量级别
                        judgmentFlag: 'N',                                                                                         // 是否为品鉴基地
                        appreciationFlag: 'N',                                                                                     // 是否为活动场地
                        capacityLevel: null,                                                                                       // 容量级别
                        imageRoomNum: null,                                                                                        // 形象包间数
                        roomTotalNum: null,                                                                                        // 包间总数
                        hallTotalNum: null,                                                                                        // 大厅总数
                        businessStartTime:null,
                        businessEndTime:null,  
                        storePhone:null,
                        customerUnitPrice:null, 
                        cuisineCategory:'',
                        isIntangibleCuisine:'N', 
                        isScenicRestaurant:'N',
                        waiterCount:null, 
                        isAtmosphereStore:'N',
                        isCoreStore:'N', 
                        xAttr50: null,                                                                                             // 店区位
                        area: null,                                                                                                // 店面积
                        comments: null,                                                                                            // 备注
                        postnId: this.$taro.getStorageSync('token').result.postnId,                                                // 职位id
                        orgId: this.$taro.getStorageSync('token').result.orgId,                                                    // 组织id
                        acctStage: null,                                                                                           // 客户阶段
                        creditChangedFlag: 'N',                                                                                    // 税号/身份证号是否改变，用以判断是否走唯一性校验，父客户等逻辑
                        chainChangedFlag: 'N',                                                                                     // 校验当前终端是否从连锁模式变为普通终端，重走唯一性校验，用于获取不同的校验返回信息
                        saleCateList: [],                                                                                          // 用于校验[是否子公司供货]和实际所售产品数据是否匹配
                        acctStatus: 'Y',                                                                                           // 状态
                        multiAcctFlag: 'N',                                                                                        // 是否一户多开
                        multiAcctMainFlag : 'Y',                                                                                   // 是否为主户头
                        storePicId : null,                                                                                         // 门头照片的id
                        auditStatus : 'new',                                                                                       // 审批状态
                        longitude: '',                                                                                             // 经度
                        latitude: '',                                                                                              // 纬度
                        accuracy: '',                                                                                              // 精度
                        mdmCompanyCode: this.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode,           // 品牌公司代码
                        kaSystemName: '',                                                                                          // 系统名称
                        xAttr51: '',                                                                                               // 所属系统ID
                        xAttr71: '',                                                                                               // 店号
                        companyId: this.$taro.getStorageSync('token').result.coreOrganizationTile['l3Id'],                         // 公司ID
                        credentType: '',    // credentType
                        creditNo: '',   // 统一社会信用代码
                    }
                }
                // 当客户大类为终端且登录人职位为企业参数配置品牌公司时显示是否星火终端（目前仅限国窖公司："5600"）
                // const brandCompanyCodeArr = await this.$utils.getCfgProperty('SPARK_TERMINAL');
                // this.showFortTerminal = brandCompanyCodeArr.includes(this.userInfo.coreOrganizationTile.brandCompanyCode);
                // const acctType = await this.$lov.getValByTypeAndName('ACCT_TYPE', this.basicOption.acctType);
                // if (this.showFortTerminal) {
                //     if (acctType === 'Terminal') {
                //         this.$set(this.basicOption, 'starfireFlag', 'N');
                //     } else {
                //         delete this.basicOption.starfireFlag;
                //     }
                // }
            },
            // 是否股东门店/是否经销商门店都为空时 所售产品是否自营置空
            'basicOption.isShareHolderShop'(newVal, old) {
                if (newVal === 'N' && this.basicOption.ownStores === 'N') {
                    this.sellProductArr.forEach(item => {
                        item.selfSupport = '';
                    });
                }
            },
            'basicOption.ownStores'(newVal, old) {
                if (newVal === 'N' && this.basicOption.isShareHolderShop === 'N') {
                    this.sellProductArr.forEach(item => {
                        item.selfSupport = '';
                    });
                }
            }
        },
        methods: {
            /**
             * 生成KEY
             * <AUTHOR>
             * @date 2024-1-15
             */
            getRandFileName(id,filePath,userInfo) {
                let extIndex = filePath.lastIndexOf('.');
                let extName = extIndex === -1 ? '' : filePath.substr(extIndex);
                return `${id}-${this.$utils.uuid()}-${parseInt(Math.random() * 10000)}-${userInfo.id}-${this.$date.format(new Date(Date.now()), 'HH:mm:ss')}-${this.$date.format(new Date(Date.now()), 'YYYY.MM.DD')}${extName}`;
            },
            /**
             * 获取组织
             * <AUTHOR>
             * @date 2023-10-30
             */
            async getOrg(){
                try {
                    const param = {
                        filtersRaw:[{id: 'id', property: 'id', value: this.pageParam.data.orgId}],
                        orgLevel: 'Y',
                        pageFlag: true,
                        rows: 1
                    }
                    const data = await this.$http.post('action/link/orgnization/queryByExamplePage', param);
                    if (data.success) {
                        if(data.rows){
                            //川东、石家庄、形象店
                            this.isNewShiJiaZhuang = data.rows[0].orgTile.l5Id && data.rows[0].orgTile.l5Id === '521067754441617408';
                            this.isXingXiangDian = data.rows[0].orgTile.l4Id && data.rows[0].orgTile.l4Id === '328927981139325498';
                            this.isChuanDongOrXingJi = data.rows[0].orgTile.l5Id && data.rows[0].orgTile.l5Id === '521074619762290688' || this.pageParam.data.mdmCompanyCode === '1612'
                        }
                    } else {
                        this.$showError('获取组织失败：' + data.result);
                    }
                } catch (error) {
                    this.$showError('获取组织失败：' + error);
                }
            },
            /**
             * 营业执照/身份证识别赋值
             * <AUTHOR>
             * @date	2023/7/31 14:01
             */
            ocrResult(param, index) {
                const info = {
                    ...this.financialArr[index],
                    ...param
                };
                this.$set(this.financialArr, index, info);
            },
            showLog() {
                this.switchFlag && this.$dialog({
                    title: '提示',
                    content: h => (
                        <view style="padding: 36rpx">{this.switchFlag}</view>
                    ),
                    cancelButton: false,
                    confirmText: '确认',
                    onConfirm:() => {},
                    onCancel: () => {}
                });
            },
            checkProduct() {
                if (this.sellProductArr.filter(item => item.status === 'Y').length === 0 && this.pageParam.sellProductFlag) {
                    this.$dialog({
                        title: '提示',
                        content: h => (
                            <view style="padding: 36rpx">您尚未维护该终端的有效所售产品，请维护有效所售产品</view>
                        ),
                        cancelButton: false,
                        confirmText: '确认',
                        onConfirm:() => {},
                        onCancel: () => {}
                    });
                    return false
                }
                return true
            },
            doCheckContact() {
                let contactInfoFlag = false;//是否需要弹框校验联系人列表有数据，或维护的联系人数据是否完整。
                let msg = "您尚未维护该终端的联系人，请维护联系人";
                if (this.contactList.length === 0 && this.contactsArr.length === 0) {
                    contactInfoFlag = true;
                } else if (this.contactList.length === 1 && this.contactList[0].isEffective !== 'Y'){
                    contactInfoFlag = true;
                    msg = "您尚未维护该终端有效的联系人，请维护联系人";
                } else {
                    //lzlj-002-3346校验手机号
                    contactInfoFlag = false;
                    const reg = /^[1][3456789][0-9]{9}$/;
                    const c = this.contactList.filter((item) => !reg.test(item.mobilePhone) && item.isEffective === 'Y')
                    const d = this.contactsArr.filter((item) => !reg.test(item.mobilePhone) && item.isEffective === 'Y')
                    if(!this.$utils.isEmpty(c) || !this.$utils.isEmpty(d)) {
                        contactInfoFlag = true;
                        msg = "联系人号码格式不正确！"
                    }
                    const a = this.contactList.filter((item1) => item1.isEffective === 'Y' && (this.$utils.isEmpty(item1.contactsName) || this.$utils.isEmpty(item1.mobilePhone) || this.$utils.isEmpty(item1.contactsType)));
                    const b = this.contactsArr.filter((item1) => item1.isEffective === 'Y' && (this.$utils.isEmpty(item1.contactsName) || this.$utils.isEmpty(item1.mobilePhone) || this.$utils.isEmpty(item1.contactsType)));
                    if(!this.$utils.isEmpty(a) || !this.$utils.isEmpty(b)){
                        contactInfoFlag = true;
                        msg = "您维护的联系人数据，姓名、联系电话、角色职务未完善，请完善。"
                    }
                }
                return {contactInfoFlag, msg}
            },
            checkContactInfo() {
                const {contactInfoFlag, msg} = this.doCheckContact()
                if (contactInfoFlag) {
                    this.$dialog({
                        title: '提示',
                        content: h => (
                            <view style="padding: 36rpx">{msg}</view>
                        ),
                        cancelButton: false,
                        confirmText: '确认',
                        onConfirm:() => {},
                        onCancel: () => {}
                    });
                    return false
                }
                return true
            },
            /**
             * 校验主子户头的营业执照照片是否上传(实时查询)
             * 以前的上传照片，是根据添加照片时，会将子组件imgList传给父组件，由于引用类型，所以删除等操作可以同步，所以可校验,但存在主子户头和缓存情况下不可行
             * 不直接去读取营业执照对应组件的imgList是由于可能会切换到其他页面，由于使用v-if子组件数据不会保留，所以只能将数据传给父组件
             * 改为实时查询是因为考虑到如果用户重新填写信息，将从缓存读取数据，数据之间无法关联，删除等存在问题，头id变化的那一次查询也无回调，所以改为实时查询
             * <AUTHOR>
             * @date 2022-7-4
             */
            async checkBusinessImg(item, type) {
                if (!item.id) {
                    return false;
                }
                const params = {
                    headId: item.id,
                    moduleType: type,
                    uploadType: 'cos'
                }
                try {
                    const {rows} = await this.$http.post('action/link/attachment/queryByExamplePage', params)
                    return rows.length === 0
                } catch (e) {
                    console.log(e)
                }
            },
            /**
             * 查询终端维护的联系人
             * <AUTHOR>
             * @date 2021-06-22
             */
            async queryContactsList () {
                // 联系人
                const data = await this.getContactsList()
                this.contactList = data.rows;
            },
            /**
             * 获取联系人信息
             * <AUTHOR>
             * @date 2023-4-13
             */
            async getContactsList() {
                const data = await this.$http.post('action/link/contacts/listByAcctId', {
                    pageFlag: true,
                    onlyCountFlag: false,
                    filtersRaw: [],
                    oauth: 'ALL',
                    sort: 'id',
                    order: 'desc',
                    attr1: this.basicOption.id,
                });
                return data
            },
            /**
             * lzljqw-004-258联系人电话号码校验
             * <AUTHOR>
             * @date 2023-4-13
             */
            async checkContactsList(contactList, contactsArr) {
                const userInfo = this.$taro.getStorageSync('token').result
                if(userInfo.coreOrganizationTile.l4Id === '328927981139325498') return true
                //大成浓香、永粮、鸿泸仅校验客户大类为终端的店老板联系电话
                //特曲终端分销商店老板
                if(
                    ['分销商', 'Distributor'].includes(this.basicOption.acctType)
                    && (
                        userInfo.coreOrganizationTile.brandCompanyCode === '5161'
                        || userInfo.coreOrganizationTile.brandCompanyCode === '5151'
                        || userInfo.coreOrganizationTile.brandCompanyCode === '5902'
                        || userInfo.coreOrganizationTile.brandCompanyCode === '5903'
                        || userInfo.coreOrganizationTile.brandCompanyCode ==='5153'
                    )
                    || ['5137', '1216'].includes(userInfo.coreOrganizationTile.brandCompanyCode)
                ) return true
                if(!contactList) contactList = this.contactList
                if(!contactsArr) contactsArr = this.contactsArr
                const allContactList = [...contactList, ...contactsArr]
                for (let i = 0;i < allContactList.length; i++) {
                    if(allContactList[i].contactsType === 'ShopOwner') {
                        const data = await this.checkShopOwnerInfo(allContactList[i]);
                        if(data.length > 0) {
                            const positionList = await this.checkPosition(data[0].id, userInfo.coreOrganizationTile.l3Id)
                            //国窖形象店大区不在校验范围内
                            const flag1 = userInfo.coreOrganizationTile.brandCompanyCode !== '5600' || await this.checkPositionOrg(positionList)
                            if(positionList.length > 0 && flag1) {
                                const type = await this.$lov.getNameByTypeAndVal('STAFF_TYPE', data[0].staffType);
                                this.$showError(`${type}【${data[0].firstName}】的联系电话不能注册为${this.basicOption.acctType}店老板!`);
                                return false
                            }
                        }
                    }
                }
                return true
            },
            /**
             * 查询职位信息
             * <AUTHOR>
             * @date 2023-4-13
             */
            async checkPosition(userId, companyId) {
                const params = {
                    mvgMapperName: 'userPostn',
                    mvgParentId: userId,
                    mvgAttr7: 'onlyIsEffective',
                    oauth: 'ALL',
                    filtersRaw: [
                        {id: 'companyId', property: 'companyId', value: companyId}
                    ]
                }
                const {rows} = await this.$http.post('action/link/mvg/queryRightListPage', params)
                return rows
            },
            /**
             * 查询职位所属组织是否属于形象店大区及其下级组织
             * <AUTHOR>
             * @param positionList 需要校验的职位列表
             * @param num 并发查询数量
             * @date 2023-5-29
             */
            checkPositionOrg(positionList, num = 4) {
                if(positionList.length === 0) return false
                let count = 0
                let status = 'pending'
                return new Promise((res) => {
                    const run = (row) => {
                        if(!row) return
                        const param = {
                            filtersRaw: [{id: 'id', property: 'id', value: row.orgId, operator: '='}],
                            orgLevel: 'Y'
                        }
                        this.$http.post('action/link/orgnization/queryByExamplePage', param).then(onFulfilled, onRejected)
                    }
                    const onFulfilled = (data) => {
                        const rows = data.rows
                        const l = positionList.length
                        count++
                        //lzljqw-004-465 2023-6-1新增
                        if(rows && rows[0] && rows[0].orgTile.l4Id === '328927981139325498') {
                            status = 'fulfilled'
                            res(false)
                        } else if(status === 'pending'){
                            run(positionList[count + num - 1])
                        }
                        if(count === l && status === 'pending') {
                            res(true)
                        }
                    }
                    const onRejected = () => {
                        status = 'rejected'
                        res(true)
                    }
                    for (let i = 0; i < num; i++) {
                        run(positionList[i])
                    }
                })
            },
            /**
             * 查询人员
             * <AUTHOR>
             * @date 2023-4-13
             */
            async checkShopOwnerInfo(info) {
                if(!info.mobilePhone) return [];
                const params = {
                    filtersRaw: [
                        {id: 'staffType', property: 'staffType', value: '[Internal, Service, Dealer]', operator: 'in'},
                        {id: 'contactPhone', property: 'contactPhone', value: info.mobilePhone},
                        {id: 'status', property: 'status', value: 'NORMAL'}
                    ]
                };
                const {rows} = await this.$http.post('action/link/user/queryUsersByType/VendorEmployee', params);
                return rows;
            },
            async initPage() {
                this.pageParam.editFlag !== 'edit' ? this.tapsActive = this.tapsOptions[0] : this.tapsActive = this.tapsOptions[this.pageParam.activeNum];
                this.marginTop = this.pageParam.editFlag !== 'edit';
                // 终端详情财务认证跳转过来，直接开启认证
                if (!this.$utils.isEmpty(this.pageParam.attestation)) {
                    this.authenticationFlag = 'Y';
                    this.attestationShowFlag = false
                }
                await this.initData();
                if (this.pageParam.editFlag === 'edit') {
                    this.basicInfoEdit = this.pageParam.data;
                    this.basicOption.id =  this.pageParam.accntId;
                    if (this.pageParam.attestation === 'attestation' && this.pageParam.financialAttachment.length !== 0) {
                        this.financialPhotoArr = this.pageParam.financialAttachment.filter(item => item.moduleType === 'billTitlePhoto');
                        this.idCardFrontArr = this.pageParam.financialAttachment.filter(item => item.moduleType === 'IDCardFront');
                        this.idCardBackArr = this.pageParam.financialAttachment.filter(item => item.moduleType === 'IDCardBack');
                    }
                    this.queryByIdTerminalData();
                    this.fetchInitAttachmentArray(this.pageParam.accntId)
                } else if(!this.claimFlag){
                    this.getTerminalDefault();
                }
                if (this.pageParam.attestation === 'attestation') {
                    this.basicOption.acctType = await this.$lov.getValByTypeAndName('ACCT_TYPE', this.basicOption.acctType);
                    this.basicOption.acctCategory = await this.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', this.basicOption.acctCategory, 'ACCT_TYPE', this.basicOption.acctType);
                    this.basicOption.subAcctType = await this.$lov.getValByTypeNameParentTypeParentVal('SUB_ACCT_TYPE', this.basicOption.subAcctType, 'ACCNT_CATEGORY', this.basicOption.acctCategory);
                    this.querySaleProduct();
                }
                this.allowDistance = await this.getAllowDistance();
            },
            /**
             * 查询所售产品
             * <AUTHOR>
             * @date 2020-11-13
             */
            querySaleProduct () {
                this.$http.post('action/link/saleCategory/queryByExamplePage', {
                    pageFlag: true,
                    onlyCountFlag: false,
                    oauth: 'ALL',
                    sort: 'created',
                    order: 'desc',
                    accntId: this.pageParam.accntId
                }, {
                    handleFailed: (error) => {
                    }
                }).then(data => {
                    this.saleCateList = data.rows;
                })
            },
            /**
             * 获取新建终端允许距离
             * <AUTHOR>
             * @date 2020-09-02
             */
            getAllowDistance () {
                return new Promise(resolve => {
                    this.$http.post('action/link/cfgProperty/queryByExamplePage', {
                        key: 'createTerminalLimitDistance'
                    }).then(data => {
                        if (data.success) {
                            this.$taro.setStorageSync('allowDistance', Number(data.rows[0].value));
                            resolve(Number(data.rows[0].value));
                        }
                    });
                })
            },
            /**
             * 监听详细地址修改
             * <AUTHOR>
             * @date 2020-10-23
             * @param val 修改之后的详细地址
             */
            async addressModify({val, flag}) {
                const that = this;
                let dealAddressData = that.basicOption.province + that.basicOption.city + that.basicOption.district + that.basicOption.townName + val;
                //腾讯地图详细地址获取经纬度替换百度的方法
                let latLng = await that.$locations.reverseTMapGgeocodingLaLon(dealAddressData);
                if (latLng.flag === 'success') {
                    that.textareaInputFlag = true;
                    let distance = await that.$locations.getDistance(that.coordinate.latitude, that.coordinate.longitude, latLng.res.result.location.lat, latLng.res.result.location.lng);
                    let acctType = await this.$lov.getValByTypeAndName('ACCT_TYPE', this.basicOption.acctType);
                    let acctCategory = await this.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', this.basicOption.acctCategory, 'ACCT_TYPE', acctType);
                    if(flag) that.overhangFlag = distance * 1000 > that.allowDistance
                    if (!this.unLimitFlag && that.overhangFlag && (acctCategory !== 'qdlx-2' && acctCategory !== 'qdlx-4' && acctType==='Terminal') && flag) {
                        that.overhangDialog()
                    } else {
                        that.basicOption.longitude = latLng.res.result.location.lng;
                        that.basicOption.latitude = latLng.res.result.location.lat;
                    }
                } else {
                    that.$message.warn('输入地址有误，请检查详细地址');
                }
            },
            /**
             * 选择位置的结果
             * <AUTHOR>
             * @date 2020-10-22
             * @param latitude
             * @param longitude
             */
            chooseLocation (latitude, longitude) {
                const that = this;
                return new Promise (resolve => {
                    that.$taro.chooseLocation({
                        latitude: Number(latitude),
                        longitude: Number(longitude),
                        async success(res) {
                            that.basicOption.longitude = res.longitude;
                            that.basicOption.latitude = res.latitude;
                            let distance = await that.$locations.getDistance(Number(latitude), Number(longitude), Number(res.latitude), Number(res.longitude));
                            let data = {res: res, flag: 'success', distance: distance};
                            resolve(data)
                        },
                        fail (res) {
                            let data = {res: res, flag: 'fail'};
                            resolve(data)
                        }
                    })
                })
            },
            /**
              * 定位选择
              * <AUTHOR>
              * @date 2020-09-24
            */
            async getLocation(data={}) {
                const that = this;
                that.locationFunData = data;
                let acctType = await this.$lov.getValByTypeAndName('ACCT_TYPE', this.basicOption.acctType);
                let acctCategory = await this.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', this.basicOption.acctCategory, 'ACCT_TYPE', acctType);
                const addressInfo = await that.$locations.getCurrentCoordinate()
                if(!addressInfo.errMsg) that.coordinate = addressInfo
                // 匹配5G 某些情况下 定位获取不到的问题
                // 手机系统没开定位getLocation:fail auth deny getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF
                // 手机系统定位开了 但是企业微信没有权限 getLocation:fail system permission denied  getLocation:fail:system permission denied
                if(that.coordinate.errMsg === 'getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF'
                    || that.coordinate.errMsg === 'getLocation:fail system permission denied'
                    || that.coordinate.errMsg === 'getLocation:fail:system permission denied'){
                    let net = "";
                    await Taro.getNetworkType({
                        success (res) {
                            net = res.networkType
                        }
                    });
                    if(net === '5g' && this.openSettingNum > 1){
                        that.$refs.locationFailSelectAddress.show();
                    } else {
                        if (that.$utils.isEmpty(this.coordinate.latitude) && that.$utils.isEmpty(this.coordinate.longitude)) {
                            this.$dialog({
                                title: '提示',
                                content: '请确认手机地理位置授权是否打开，或者【设置】-【企业微信】位置权限管理是否打开？',
                                cancelButton: false,
                                confirmText: '去开启',
                                onConfirm: async () => {
                                    let userLocation = await this.$locations.openSetting();
                                    if (userLocation['scope.userLocation']) {
                                        that.coordinate = await that.$locations.getCurrentCoordinate();
                                    }
                                }
                            });
                            this.openSettingNum ++;
                            return
                        }
                    }
                }
                if (!that.$utils.isEmpty(that.coordinate)) {
                    await this.$locations.chooseLocation(that.coordinate.latitude, that.coordinate.longitude);
                    this.getLocationFlag = true;
                    return;
                    //这里结束
                } else {
                    let userLocation = await that.$locations.openSetting();
                    if (userLocation['scope.userLocation']) {
                        that.coordinate = await that.$locations.getCurrentCoordinate();
                        that.$store.commit('coordinate/setCoordinate', that.coordinate);
                        await that.initData();
                    }
                }
            },
            /**
              * 超距弹窗
              * <AUTHOR>
              * @date 2020-10-23
              * @param param
            */
            overhangDialog () {
                const that = this;
                that.$dialog({
                    title: '提示',
                    content: '当前门店地址和您定位位置的距离超过' + that.allowDistance + '米，请检查！',
                    cancelButton: true,
                    onConfirm: () => {
                        setTimeout(async function () {
                            await that.$locations.chooseLocation(that.coordinate.latitude, that.coordinate.longitude);
                            that.getLocationFlag = true;
                            return;
                        }, 20);
                    },
                    onCancel: () => {
                        // 输入框超距时用户点击取消，详细地址赋值原先未超距的详细地址
                        if (that.textareaInputFlag) {
                            if (this.pageParam.editFlag === 'edit') {
                                return
                            }
                            let pCD = `${that.basicOption.province}${that.basicOption.city}${that.basicOption.district}${that.basicOption.townName}`;
                            that.basicOption.address = that.chooseData.res.address.substring(pCD.length, that.chooseData.res.address.length) + that.chooseData.res.name;
                        }
                    }
                })
            },
            /**
             * 身份证反面面照片
             * <AUTHOR>
             * @date 2020-09-16
             * @param arr
             */
            idCardBack (arr) {
                this.idCardBackArr = arr
            },
            /**
              * 身份证正面照片
              * <AUTHOR>
              * @date 2020-09-16
              * @param arr
            */
            idCardFront (arr) {
                this.idCardFrontArr = arr
            },
            /**
              * 营业执照或者身份证照片
              * <AUTHOR>
              * @date 2020-09-16
              * @param arr 上传成功图片数组
            */
            financialPhoto (arr) {
                this.financialPhotoArr = arr
            },
            /**
              * 门头照片长度
              * <AUTHOR>
              * @date 2020-09-15
              * @param param
            */
            storeHeadArr (param) {
                this.storeHeadLength = param.length;
                if (param.length !== 0) {
                    this.basicOption.storePicId = param[0].id;
                }else if(param.length === 0){
                    this.basicOption.storePicId = '';
                }
            },
            /**
             * 监控页面返回参数
             * <AUTHOR>
             * @date 2020-08-31
             * @param param
             */
            onBack (param) {
                if (this.$utils.isEmpty(param)) {
                    return
                }
                if (!this.$utils.isEmpty(param.headNameData)) {
                    this.$refs.clientBasics.onBlurStoreName();
                    this.basicOption.acctName = param.headNameData.name;    // 获取门头名称
                    this.basicOption.province = param.headNameData.province;
                    this.basicOption.city = param.headNameData.city;
                    this.basicOption.district = param.headNameData.district;
                    this.basicOption.address = param.headNameData.address;
                    this.basicOption.longitude = param.headNameData.longitude;
                    this.basicOption.latitude = param.headNameData.latitude;
                }
                if (param.source === 'distinguishDetailsPage') {
                    const info = {
                        ...this.financialArr[param.index],
                        billTitle: param.billTitle,
                        creditNo: param.creditNo,
                        registrationDate: param.registrationDate,
                        period: param.period,
                        person: param.person,
                        business: param.business,
                        capital: param.capital,
                        billAddr: param.address
                    }
                    this.$set(this.financialArr, param.index, info)
                }
                if(param.delFlag) {
                    this.$refs.financialInfo.delPhoto(param.info, param.index)
                }
            },
            /**
             * 校验客户分类数据是否异常
             * <AUTHOR>
             * @date 2022-8-1
             */
            checkCustomerType() {
                if(this.basicOption.acctType === '分销商' && this.basicOption.acctCategory && !this.basicOption.subAcctType) return true
                const p1 = this.acctCascadeData.ACCNT_CATEGORY.some((item) => item.name === this.basicOption.acctCategory && item.parentName === this.basicOption.acctType)
                const p2 = this.acctCascadeData.SUB_ACCT_TYPE.some((item) => item.name === this.basicOption.subAcctType && item.parentName === this.basicOption.acctCategory)
                if(p1 && p2) return true
                this.$showError('客户分类数据异常，请重新选择!');
                return false
            },
            checkShiJiangZhuangData() {
                if (this.$utils.isEmpty(this.basicOption.acctSort)) {
                    this.$message['warn']('请选择客户类型');
                    return false
                }
                if (this.$utils.isEmpty(this.basicOption.acctNature)) {
                    this.$message['warn']('请选择客户性质');
                    return false
                }
                return true
            },
            /**
             * 校验基础数据
             * <AUTHOR>
             * @date 2020-09-15
             */
            async checkData() {
                const that = this;
                // 汉字及大小写字母正则
                const reg = /^[A-Za-z0-9\u4e00-\u9fa5]+$/;
                // 将值列表显示值转为独立源代码
                let acctType = await that.$lov.getValByTypeAndName('ACCT_TYPE', that.basicOption.acctType);
                let acctCategoryVal = await that.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', that.basicOption.acctCategory, 'ACCT_TYPE', acctType);
                let subAcctTypeVal = await that.$lov.getValByTypeNameParentTypeParentVal('SUB_ACCT_TYPE', that.basicOption.subAcctType, 'ACCNT_CATEGORY', acctCategoryVal);
                if (((this.pageParam.identifying && !this.pageParam.sellProductFlag) || this.sellProductArr.filter(item => item.status === 'Y').length !== 0) && this.isChuanDongOrXingJi && this.basicOption.accntPartner !== 'cooperation') {
                    this.$dialog({
                        title: '提示',
                        content: h => (
                            <view style="padding: 36rpx">未合作客户不可添加有效所售产品</view>
                        ),
                        cancelButton: false,
                        confirmText: '确认',
                        onConfirm:() => {},
                        onCancel: () => {}
                    });
                    return false
                }
                if (acctType === 'Terminal' && (that.$utils.isEmpty(that.basicOption.acctType) || that.$utils.isEmpty(that.basicOption.acctCategory) || that.$utils.isEmpty(that.basicOption.subAcctType))) {
                    that.$message['warn']('请选择客户分类');
                    return false
                }
                if (acctType !== 'Terminal' && that.$utils.isEmpty(that.basicOption.acctType)) {
                    that.$message['warn']('请选择客户分类');
                    return false
                }
                if(!this.checkCustomerType()) {
                    return false
                }
                if ((acctCategoryVal === 'qdlx-2' || acctType === 'Terminal' && this.isXinLingShou) && that.$utils.isEmpty(that.basicOption.kaSystemName)) {
                    that.$message['warn']('请选择系统名称');
                    return false
                }
                if ((subAcctTypeVal !== 'LianSuoBianLi' && acctCategoryVal === 'qdlx-2') && that.$utils.isEmpty(that.basicOption.xAttr71)) {
                    that.$message['warn']('请选择店号');
                    return false
                }
                if (acctType === 'Terminal' && that.$utils.isEmpty(that.basicOption.acctName)) {
                    that.$message['warn']('请输入门头名称');
                    return false
                }
                if (acctType === 'Distributor' && that.$utils.isEmpty(that.basicOption.acctName)) {
                    that.$message['warn']('请输入分销商名称');
                    return false
                }
                if (acctCategoryVal !== 'GroupBuy' && that.$utils.isEmpty(that.basicOption.province) && that.$utils.isEmpty(that.basicOption.city) && that.$utils.isEmpty(that.basicOption.district)) {
                    that.$message['warn']('请选择所在地区');
                    return false
                }
                if(this.isNewShiJiaZhuang) {
                    this.checkShiJiangZhuangData();
                }
                if (acctCategoryVal !== 'GroupBuy' && that.$utils.isEmpty(that.basicOption.address)) {
                    that.$message['warn']('请输入门店地址详细地址');
                    return false
                }
                if (acctType === 'Terminal' && that.storeHeadLength === 0 && that.$utils.isEmpty(that.pageParam.editFlag)) {
                    that.$message['warn']('请上传门头照片');
                    return false
                }
                const brandCompanyCode = this.userInfo.coreOrganizationTile.brandCompanyCode;
                const res = await this.$utils.getCfgProperty('IS_CHECK_ORGANIZATION');
                const isBrandCompanyCode = res && res.length && res.includes(brandCompanyCode);
                const checkRes = res.includes(this.basicOption.mdmCompanyCode) && (!this.userInfo.coreOrganizationTile.l3Code) || isBrandCompanyCode;
                if(acctType === 'Terminal' && !this.isXinLingShou && this.$utils.isEmpty(this.basicOption.isExclusiveShop)){
                    that.$message['warn']('请选择是否泸州老窖官方形象店');
                    return false
                }
                if(acctType === 'Terminal' && !this.isXinLingShou && that.$utils.isEmpty(that.basicOption.isShareHolderShop )){
                    that.$message['warn']('请选择是否专营公司股东门店');
                    return false
                }
                // if(acctType === 'Terminal' && this.showFortTerminal && that.$utils.isEmpty(that.basicOption.starfireFlag )){
                //     that.$message['warn']('请选择是否星火终端');
                //     return false
                // }
                if(acctType === 'Terminal' && that.$utils.isEmpty(that.basicOption.doorSigns)){
                    that.$message['warn']('请选择门头店招');
                    return false
                }
                if(acctType === 'Terminal'&& that.basicOption.doorSigns==='JingPin' && that.$utils.isEmpty(that.basicOption.competitiveGoodsType)){
                    that.$message['warn']('请选择竞品类型');
                    return false
                }
                if (acctType === 'Terminal' && that.$utils.isEmpty(that.basicOption.chainStoreFlag)) {
                    //终端  必填
                    that.$message['warn']('请选择是否为连锁模式');
                    return false
                }
                if (acctCategoryVal === 'GroupBuy' && this.isHuaiJiu && that.$utils.isEmpty(that.basicOption.resourceBack)) {
                    that.$message['warn']('请选择资源背景');
                    return false
                }
                if (acctCategoryVal === 'GroupBuy' && this.isHuaiJiu && that.$utils.isEmpty(that.basicOption.belongingCircle)) {
                    that.$message['warn']('请选择所属圈层');
                    return false
                }
                if(this.isJiaoLing) {
                    if(acctType === 'Terminal' && that.$utils.isEmpty(that.basicOption.channelMemberFlag)) {
                        that.$message['warn']('请选择是否为精英会员');
                        return false
                    }
                } else {
                    if(that.$utils.isNotEmpty(that.basicOption.channelMemberFlag)) {
                        delete that.basicOption.channelMemberFlag
                    }
                }
                if((acctType === 'Terminal' || acctCategoryVal === 'Distributor' && this.isXinLingShou) && that.$utils.isEmpty(that.basicOption.ownStores)) {
                    that.$message['warn']('请选择是否经销商自有门店');
                    return false
                }
                if (that.basicOption.chainStoreFlag === 'Y' && that.basicOption.chainHeadStoreFlag === 'N' && that.$utils.isEmpty(that.basicOption.chainHeadStoreName)) {
                    that.$message['warn']('请选择连锁总店名称');
                    return false
                }
                if (acctType === 'Terminal' && that.$utils.isEmpty(that.basicOption.acctLevel)) {
                    if(!this.isChuanDongOrXingJi || that.basicOption.accntPartner === 'cooperation') {
                        that.$message['warn']('请选择客户规划等级');
                        return false
                    }
                }
                if (acctType === 'Terminal' && that.$utils.isEmpty(that.basicOption.capacityLevel)) {
                    if(!this.isChuanDongOrXingJi || that.basicOption.accntPartner === 'cooperation') {
                        that.$message['warn']('请选择容量级别');
                        return false
                    }
                }
                if(this.isChuanDongOrXingJi) {
                    if(!that.basicOption.initialCooperationTime) {
                        const nowTime = await this.$utils.getServerTime();
                        that.basicOption.initialCooperationTime = that.$date.format(new Date(nowTime), 'YYYY-MM-DD HH:mm:ss')
                        that.basicOption.presentCooperationTime = that.$date.format(new Date(nowTime), 'YYYY-MM-DD HH:mm:ss')
                    } else {
                        if(that.basicOption.accntPartner !== 'cooperation') {
                            that.basicOption.presentCooperationTime = that.$date.format(new Date(await this.$utils.getServerTime()), 'YYYY-MM-DD HH:mm:ss')
                        }
                    }
                }
                if (that.basicOption.judgmentFlag === 'Y' && that.$utils.isEmpty(that.basicOption.imageRoomNum)) {
                    that.$message['warn']('请输入形象包间数');
                    return false
                }
                if (acctCategoryVal === 'qdlx-4' && that.$utils.isEmpty(that.basicOption.roomTotalNum)) {
                    that.$message['warn']('请输入包间总数');
                    return false
                }
                if (acctCategoryVal === 'qdlx-4' && that.$utils.isEmpty(that.basicOption.hallTotalNum)) {
                    that.$message['warn']('请输入大厅桌数');
                    return false
                }
                if ((acctCategoryVal === 'qdlx-4') && that.$utils.isEmpty(that.basicOption.businessStartTime)) {
                    that.$message['warn']('请选择营业开始时间');
                    return false
                }
                if ((acctCategoryVal === 'qdlx-4') && that.$utils.isEmpty(that.basicOption.businessEndTime)) {
                    that.$message['warn']('请选择营业结束时间');
                    return false
                }
            
                if ((acctCategoryVal === 'qdlx-4')) {
                const phone = that.basicOption.storePhone;
                
                if (that.$utils.isEmpty(phone)) {
                    that.$message['warn']('请输入订餐号码/门店电话');
                    return false
                }
                const reg = /^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/;
                if (!reg.test(phone)) {
                    that.$message['warn']('订餐号码/门店电话输入号码不正确');
                    return false
                }
                }
                if ((acctCategoryVal === 'qdlx-4') && that.$utils.isEmpty(that.basicOption.customerUnitPrice)) {
                    that.$message['warn']('请输入客单价');
                    return false
                }
                if ((acctCategoryVal === 'qdlx-4') && that.$utils.isEmpty(that.basicOption.cuisineCategory)) {
                    that.$message['warn']('请选择菜系');
                    return false
                }
                if ((acctCategoryVal === 'qdlx-4') && that.$utils.isEmpty(that.basicOption.isIntangibleCuisine)) {
                    that.$message['warn']('请选择是否有非遗菜');
                    return false
                }
                if ((acctCategoryVal === 'qdlx-4') && that.$utils.isEmpty(that.basicOption.isScenicRestaurant)) {
                    that.$message['warn']('请选择是否为景区餐饮店');
                    return false
                }
                if ((acctCategoryVal === 'qdlx-4') && that.$utils.isEmpty(that.basicOption.waiterCount)) {
                    that.$message['warn']('请输入服务员数量');
                    return false
                }
                if ((acctCategoryVal === 'qdlx-4') && that.$utils.isEmpty(that.basicOption.isAtmosphereStore)) {
                    that.$message['warn']('请选择是否为氛围物料店');
                    return false
                }
                if ((acctCategoryVal === 'qdlx-4') && that.$utils.isEmpty(that.basicOption.isCoreStore)) {
                    that.$message['warn']('请选择是否餐酒项目核心门店');
                    return false
                }
                if (acctType === 'Terminal' && that.$utils.isEmpty(that.basicOption.xAttr50)) {
                    that.$message['warn']('请选择店区位');
                    return false
                }
                if (acctType === 'Terminal' && that.$utils.isEmpty(that.basicOption.area)) {
                    that.$message['warn']('请选择店面积');
                    return false
                }
                return true
            },
            /**
             * 检查编辑基础信息是否更新
             * <AUTHOR>
             * @date 2020-09-23
             */
            checkEditData: async function () {
                const that = this;
                // 将值列表显示值转为独立源代码
                let acctType = await that.$lov.getValByTypeAndName('ACCT_TYPE', that.basicOption.acctType);
                let acctCategory = await that.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', that.basicOption.acctCategory, 'ACCT_TYPE', acctType);
                let subAcctType = await that.$lov.getValByTypeNameParentTypeParentVal('SUB_ACCT_TYPE', that.basicOption.subAcctType, 'ACCNT_CATEGORY', acctCategory);
                const fields = ['storePicId', 'simName', 'acctName', 'province', 'city', 'district', 'address', 'chainStoreFlag', 'chainHeadStoreName',
                                'acctLevel', 'capacityLevel', 'appreciationFlag', 'imageRoomNum', 'roomTotalNum', 'hallTotalNum', 'xAttr50', 'area',
                                'kaSystemName', 'xAttr51', 'xAttr71', 'storePicId',]
                const status = this.compareFields(that.basicOption, that.basicInfoEdit, fields)
                const {acctType: acctType1, acctCategory: acctCategory1, subAcctType: subAcctType1} = that.basicInfoEdit
                return !(acctType === acctType1 && acctCategory === acctCategory1 && subAcctType === subAcctType1 && status);
            },
            /**
             * 字段对比优化处理
             * <AUTHOR>
             * @date 2024-09-09
             * @param {String} obj1对比数据源1
             * @param {String} obj2对比数据源2
             * @param {Array} fields对比字段
             */
            compareFields(obj1, obj2, fields) {
                for (const field of fields) {
                    if (obj1[field] !== obj2[field]) {
                        return false;
                    }
                }
                return true;
            },
            /**
             * 检查联系人数据
             * <AUTHOR>
             * @date 2020-10-21
             */
            checkedContactData () {
                const that = this;
                let flag = true;
                for (let i = 0; i < that.contactsArr.length; i++) {
                    let item = that.contactsArr[i];
                    if (that.$utils.isEmpty(item.contactsName) && that.$utils.isEmpty(item.contactsSex)&&
                        that.$utils.isEmpty(item.mobilePhone) && that.$utils.isEmpty(item.fixedPhone) &&
                        that.$utils.isEmpty(item.birthdayType) && that.$utils.isEmpty(item.birthYear) &&
                        that.$utils.isEmpty(item.birthday) && that.$utils.isEmpty(item.contactsType) &&
                        that.$utils.isEmpty(item.comments)) {
                        return flag
                    } else {
                        return false
                    }
                }
            },
            /**
             * 检查所有联系人数据
             * <AUTHOR>
             * @date 2023-05-12
             */
            checkAllContactData () {
                for (let i = 0; i < this.contactsArr.length; i++) {
                    let item = this.contactsArr[i];
                    if (this.$utils.isEmpty(item.contactsName) && this.$utils.isEmpty(item.contactsSex)&&
                        this.$utils.isEmpty(item.mobilePhone) && this.$utils.isEmpty(item.fixedPhone) &&
                        this.$utils.isEmpty(item.birthdayType) && this.$utils.isEmpty(item.birthYear) &&
                        this.$utils.isEmpty(item.birthday) && this.$utils.isEmpty(item.contactsType) &&
                        this.$utils.isEmpty(item.comments)) {
                    } else {
                        return i + 1
                    }
                }
            },
            /**
              * 校验财务信息
              * <AUTHOR>
              * @date 2020-09-15
            */
            async checkedFinancial () {
                const that = this;
                let flag = true;
                for(let i = 0; i < that.financialArr.length; i++) {
                    let item = that.financialArr[i];
                    if (that.$utils.isEmpty(item.invoiceCategory)) {
                        that.$message['warn']('请选择客户类型');
                        flag = false;
                        break
                    }
                    if (that.$utils.isEmpty(item.credentType)) {
                        that.$message['warn']('请选择发票类型');
                        flag = false;
                        break
                    }
                    if (that.$utils.isEmpty(item.billTitle)) {
                        item.invoiceCategory === 'indivisual' ? that.$message['warn']('请输入姓名'): that.$message['warn']('请输入营业执照名称');
                        flag = false;
                        break
                    }
                    if (that.$utils.isEmpty(item.creditNo) || that.$utils.isEmpty(item.creditNo.replace(/\s*/g,""))) {
                        item.invoiceCategory === 'indivisual' ? that.$message['warn']('请输入身份证号'): that.$message['warn']('请输入统一社会信用代码');
                        flag = false;
                        break
                    }
                  if (that.$utils.isNotEmpty(item.creditNo)) {
                    if(item.invoiceCategory !== 'indivisual'){
                      let reg = /^[A-HJ-NPQRTUWXY0-9]{15}$|^[A-HJ-NPQRTUWXY0-9]{17}$|^[A-HJ-NPQRTUWXY0-9]{18}$|^[A-HJ-NPQRTUWXY0-9]{20}$/g;
                      let test = reg.test(item.creditNo);
                      if(!test){
                        that.$message['warn']('请检查统一社会信用代码');
                        flag = false;
                        break
                      }
                    }
                  }
                    if ((item.credentType === 'ValueAddedTax' || item.credentType === 'ElectronicSpecialInvoices' || item.credentType === 'AllElectricTickets') && that.$utils.isEmpty(item.accountBankName)) {
                        that.$message['warn']('请输入开户行名称');
                        flag = false;
                        break
                    }
                    if ((item.credentType === 'ValueAddedTax' || item.credentType === 'ElectronicSpecialInvoices' || item.credentType === 'AllElectricTickets') && that.$utils.isEmpty(item.mainBillAccntId)) {
                        that.$message['warn']('请输入银行账户');
                        flag = false;
                        break
                    }
                    if ((item.credentType === 'ValueAddedTax' || item.credentType === 'ElectronicSpecialInvoices' || item.credentType === 'AllElectricTickets') && that.$utils.isEmpty(item.billPhone)) {
                        that.$message['warn']('请输入注册电话');
                        flag = false;
                        break
                    }
                    if ((item.credentType === 'ValueAddedTax' || item.credentType === 'ElectronicSpecialInvoices' || item.credentType === 'AllElectricTickets') &&  that.$utils.isEmpty(item.billAddr)) {
                        that.$message['warn']('请输入公司注册地址');
                        flag = false;
                        break
                    }
                    if ((this.isXinJiuYeOrRongCheng || this.isDaHongYongJiao && (item.credentType !== 'ElectronicInvoices' || item.invoiceCategory === 'company') || ['PlainInvoice','ValueAddedTax'].includes(item.credentType)) && that.$utils.isEmpty(item.receiveBillContact)) {
                        that.$message['warn']('请输入收票人姓名');
                        flag = false;
                        break
                    }
                    if ((this.isXinJiuYeOrRongCheng || this.isDaHongYongJiao && (item.credentType !== 'ElectronicInvoices' || item.invoiceCategory === 'company') || ['PlainInvoice','ValueAddedTax'].includes(item.credentType)) && that.$utils.isEmpty(item.receiveBillPhone)) {
                        that.$message['warn']('请输入收票人联系电话');
                        flag = false;
                        break
                    }
                    if (!that.$utils.isEmpty(item.receiveBillPhone) &&  !/^1\d{10}$/.test(item.receiveBillPhone)) {
                        that.$message['warn']('收票人联系电话格式不正确');
                        flag = false;
                        break
                    }
                    if (['PlainInvoice','ValueAddedTax'].includes(item.credentType) && (that.$utils.isEmpty(item.receiveBillProvince) || that.$utils.isEmpty(item.receiveBillCity) || that.$utils.isEmpty(item.receiveBillDistrict))) {
                        that.$message['warn']('请输入收票人邮寄地区');
                        flag = false;
                        break
                    }
                    if (['PlainInvoice','ValueAddedTax'].includes(item.credentType) && that.$utils.isEmpty(item.receiveBillAddr)) {
                        that.$message['warn']('请输入收票人邮寄地址');
                        flag = false;
                        break
                    }
                    if (!that.$utils.isEmpty(item.receiveBillEmail) &&  !/^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/.test(item.receiveBillEmail)) {
                        that.$message['warn']('电票接收邮箱格式不正确');
                        flag = false;
                        break
                    }
                    if (
                        (item.credentType === 'ElectronicInvoices' || item.credentType === 'ElectronicSpecialInvoices' ||
                        item.credentType === 'AllElectricTickets' || item.credentType === 'AllElectricInvoices' || item.credentType === 'AllElectricInvoicesPersional') &&
                        that.$utils.isEmpty(item.receiveBillEmail) &&
                        that.$utils.isEmpty(item.ticketingPhone)
                    ) {
                        that.$message['warn']('电票接收邮箱、电票接收电话至少填写一项');
                        flag = false;
                        break
                    }
                    if (item.invoiceCategory === 'company' && await this.checkBusinessImg(item, 'billTitlePhoto')) {
                        that.$message['warn']('请上传营业执照照片');
                        flag = false;
                        break
                    }
                    if (item.invoiceCategory === 'indivisual' && (that.idCardBackArr.length === 0 && !this.claimFlag || this.claimFlag && await this.checkBusinessImg(item, 'IDCardBack'))) { //认领终端获取附件信息
                        that.$message['warn']('请上传身份证人像面照片');
                        flag = false;
                        break
                    }
                    if (item.invoiceCategory === 'indivisual' && (that.idCardFrontArr.length === 0 && !this.claimFlag || this.claimFlag && await this.checkBusinessImg(item, 'IDCardFront'))) {
                        that.$message['warn']('请上传身份证国徽面照片');
                        flag = false;
                        break
                    }
                    if(this.isShiJiaZhuang) {
                        if(await this.checkBusinessImg(item, 'customerInformation')) {
                            that.$message['warn']('请上传客户资料申请表扫描件');
                            flag = false;
                            break
                        }
                        if (item.invoiceCategory === 'company') {
                            let msg = ''
                            const [res1, res2, res3] = await Promise.all([
                                this.checkBusinessImg(item, 'copyBusinessLicense'),
                                this.checkBusinessImg(item, 'openingPermit'),
                                this.checkBusinessImg(item, 'invoicingData')
                            ])
                            if(res3) msg = '请上传开票资料'
                            if(res2) msg = '请上传开户许可证'
                            if(res1) msg = '请上传营业执照复印件'
                            if(msg) {
                                that.$message['warn'](msg);
                                flag = false;
                                break
                            }
                        }
                        if (item.invoiceCategory === 'indivisual') {
                            let msg = ''
                            const [res1, res2] = await Promise.all([
                                this.checkBusinessImg(item, 'copyBankCard')
                            ])
                            if(res2) msg = '请上传个人身份证正反面复印件'
                            if(res1) msg = '请上传个人银行卡复印件'
                            if(msg) {
                                that.$message['warn'](msg);
                                flag = false;
                                break
                            }
                        }
                    }
                    item.creditNo = item.creditNo.replace(/\s*/g,"");
                }
                return flag
            },
            /**
             * 校验是否超距
             * <AUTHOR>
             * @date 2022-12-5
             */
            async checkOverhang() {
                const that = this;
                //判断超距
                if(!this.basicOption.latitude || !this.basicOption.longitude){
                    this.$message['warn']('请重新定位门店地址');
                    return false
                }
                //定位的超距
                if(this.overhangFlag){
                    this.overhangDialog();
                    return false
                }
                let overhangFlag = false;
                if(this.changeFlag){
                    let distance = await this.$locations.getDistance(this.coordinate.latitude, this.coordinate.longitude, this.basicOption.latitude, this.basicOption.longitude);
                    overhangFlag = distance * 1000 > this.allowDistance
                    if(overhangFlag){
                        that.overhangDialog();
                        return false
                    }
                }
                return true
            },
            /**
             * 所售产品信息校验 lzljqw-004-852
             * <AUTHOR>
             * @date 2023-7-11
             */
            async checkProdInfo(data) {
                try {
                    const checkData = this.$utils.deepcopy(data);
                    checkData.forEach(item => delete item.rowId)
                    const result = await this.$http.post('action/link/saleCategory/checkSupplierUniqueForTerminal', checkData, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            return response
                        }
                    })
                    return result
                } catch (e) {
                    const info = {
                        result: `所售产品校验出错`,
                        success: false,
                        ...e
                    }
                    return info
                }
            },
            /**
             * 营业执照验真
             * <AUTHOR>
             * @date	2023/6/12 19:43
             */
            async checkInvoice(financial) {
                try {
                    const {success, licenseReal, result} = await this.$http.post('/action/link/aiOcr/getBaseinfoAiResult', {
                        creditNo: financial.creditNo, //税号
                        name: financial.billTitle //营业执照名称
                    });
                    if (success) {
                        return {licenseReal, licRealRes: result};
                    } else {
                        return null;
                    }
                } catch (e) {

                }
            },
            /**
             * 营业执照校验弹框
             * <AUTHOR>
             * @date   2024/6/6 14:53
             * @param data 弹窗数据：billTitle(营业执照名称)、creditCode(税号)
             * @param originCreditNo 识别的税号
             */
            showInfoDialog(data, originCreditNo) {
                const that = this;
                this.buttonTime = waitTime;
                let timer = setInterval(() => {
                    that.buttonTime--;
                    if (that.buttonTime <= 0) {
                        that.buttonTime = 0;
                        clearTimeout(timer);
                    }
                }, 1000);
                const dfd = ComponentUtils.dfd();
                this.$dialog({
                    title: '提示',
                    content: () => {
                        return (
                            <view style="width:100%;font-size: 26rpx;padding: 24rpx;">
                                <view>纳税人识别号：{data.creditCode}</view>
                                <view style="margin-top: 10rpx;">营业执照名称：{data.biliTitle}</view>
                                <view style="margin-top: 10rpx;">经营者姓名：{data.legalPerson}</view>
                                <view style="font-size: 24rpx;margin-top: 10rpx;">
                                    {data.creditCode
                                        ? data.regMate === 'Yes'
                                            ? originCreditNo === data.creditCode
                                                ? '根据工商注册号获取到的纳税人名称与填写的名称不一致，请点击复制按钮，进行修改！'
                                                : '根据工商注册号获取到的纳税人识别号与填写的纳税人识别号不一致，请点击按钮复制，进行修改!'
                                            : '根据纳税人识别号获取到的营业执照名称不一致，请点击按钮复制，进行修改!'
                                        : '根据当前税号未查询到相关营业执照信息，请核对税号填写是否准确！'
                                    }
                                </view>
                            </view>
                        )
                    },
                    slots: {
                        foot: (h, renderParam) => {
                            return (
                                <block>
                                    <link-button label={`${data.creditCode ? '取消并复制' : '确认'}${this.buttonTime ? '(' + this.buttonTime + ')' : ''}`}
                                                 style="flex: 1"
                                                 disabled={Boolean(this.buttonTime)}
                                                 onTap={() => {renderParam.close(); dfd.reject()}}/>
                                </block>
                            );
                        }
                        /*
                        <link-button label={`确认并提交${this.buttonTime ? '(' + this.buttonTime + ')' : ''}`}
                                                 style="flex: 1"
                                                 disabled={Boolean(this.buttonTime)}
                                                 onTap={() => {dfd.resolve(); renderParam.close()}}/>
                        */
                    }
                });
                return dfd.promise;
            },
            // 5600 校验是否自营必填
            async checkIsAutarky(basicOption) {
                const isShowCompanyCode = await this.$utils.getCfgProperty('IS_AUTARKY');
                let isShowWhether = false
                if ((basicOption.isShareHolderShop === 'Y' || basicOption.ownStores === 'Y') && basicOption.acctType === 'Terminal' && isShowCompanyCode.indexOf(basicOption.mdmCompanyCode) > -1) {
                    isShowWhether = true;
                }
                let isTips = false
                if (!isShowWhether) return isTips
                this.sellProductArr.forEach(item => {
                    if (!item.selfSupport) isTips = true
                })
                return isTips
            },
            /**
             * 提交
             * <AUTHOR>
             * @date 2020-09-10
             */
            async submit() {
                //解决维护所售产品之后维护的财务信息中提审后没有产品为问题
                if(!this.$utils.isEmpty(this.financialArr) && !this.$utils.isEmpty(this.allSaleCateList)){
                    let res = [];
                    res = this.financialArr.filter(n => !this.allSaleCateList.map(v => v.accntId).includes(n.id));
                    if(!this.$utils.isEmpty(res)){
                        res.forEach((item) => {
                            const accntId = item.id;
                            this.sellProductArr.forEach((deepItem)=>{
                                let opt = {
                                    prodId: deepItem.prodId,
                                    prodCode: deepItem.prodCode,
                                    prodName: deepItem.prodName,
                                    prodSeries: deepItem.prodSeries,
                                    prodSecSeries: deepItem.prodSecSeries,
                                    prodThirdSeries: deepItem.prodThirdSeries,
                                    status: deepItem.status || 'Y',
                                    accntId: accntId,
                                    row_status: 'NEW',
                                    supplierManageMode: deepItem.supplierManageMode,
                                    supplierName: deepItem.supplierName,
                                    supplierId: deepItem.supplierId,
                                };
                                if (deepItem.selfSupport) {
                                    opt.selfSupport = deepItem.selfSupport;
                                };
                                this.allSaleCateList.push(opt);
                            })
                        })
                    }
                    const financialIds = this.financialArr.map(item => item.id)
                    this.allSaleCateList = this.allSaleCateList.filter(item => financialIds.includes(item.accntId))
                }
                const that = this;
                let accntType = await this.$lov.getValByTypeAndName('ACCT_TYPE', this.basicOption.acctType);
                let acctCategory = await this.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', this.basicOption.acctCategory, 'ACCT_TYPE', accntType);
                if(!this.unLimitFlag && acctCategory !== 'qdlx-2' && acctCategory !== 'qdlx-4' && accntType==='Terminal'){
                    if(!await this.checkOverhang()) return
                }
                if (!['5161', '5902', '5903'].includes(this.basicOption.mdmCompanyCode)
                    || acctCategory !== 'qdlx-4' || accntType !== 'Terminal') {
                    this.basicOption.trafficHighland = null;
                }
                // 基础信息编辑时，如果数据未进行修改直接返回
                if (that.pageParam.editModule === 'basicInfo') {
                    let checkEditData = await that.checkEditData();
                    if (!checkEditData) {
                        let param = {refreshFlag: false};
                        that.$nav.back(param);
                        return
                    }
                }
                //lzlj-002-4089
                if(this.isNewShiJiaZhuang) { //this.isShiJiaZhuang
                    this.basicOption.accessAcctId = this.basicOption.accessAcctId || this.basicOption.id
                    if(!this.checkShiJiangZhuangData()) return
                }
                let checkFlag = await that.checkData();
                if (!checkFlag) {
                    return
                }
                // 新建-分销商终端时地址校验
                if(acctCategory === 'Distributor' && (this.$utils.isEmpty(this.pageParam.editFlag) || this.pageParam.editFlag !== 'edit')){
                    const checkFlag = that.$refs['addrInfo'].checkData()
                    if(!checkFlag) return
                }
                // 新建-终端时填写了收货地址其中一项就需要进行收货地址必填校验
                let isCheckAddress = false
                that.$refs['addrInfo'].addressList.forEach(item => {
                    if (item.consignee || item.regionInfo || item.mobilePhone || item.addr) {
                        isCheckAddress = true
                    }
                })
                if (isCheckAddress) {
                    const checkPass = that.$refs['addrInfo'].checkData()
                    if(!checkPass) return
                }


                if(!this.isYangShengOrYouXuan) {
                    const prodInfo = await this.checkProdInfo(this.allSaleCateList)
                    if(!prodInfo.success) {
                        this.$dialog({
                            title: '提示',
                            content: h => (
                                <view style="padding: 36rpx">{prodInfo.result}</view>
                            ),
                            cancelButton: false,
                            confirmText: '确认',
                            onConfirm:() => {},
                            onCancel: () => {}
                        });
                        return
                    }
                }
                let filterContacts = this.contactsArr.filter((item) => item.contactsType === 'ShopOwner');
                if (filterContacts.length > 1) {
                    this.$showError('存在多个店老板联系人信息，请修改后重新提交！');
                    return
                }
                //lzljqw-004-272联系人信息提交校验
                const failContactIndex = that.checkAllContactData()
                if(failContactIndex > 0) {
                    const arrIndex = failContactIndex - 1
                    const mobilePhone = this.contactsArr[failContactIndex - 1].mobilePhone
                    const reg = /^[1][3456789][0-9]{9}$/;
                    if (this.$utils.isEmpty(this.contactsArr[arrIndex].contactsName)) {
                        this.$message.warn(`请维护联系人${failContactIndex}的姓名`);
                        return
                    }
                    if (this.$utils.isEmpty(mobilePhone)) {
                        this.$message.warn(`请维护联系人${failContactIndex}的联系电话`);
                        return
                    }
                    if(!reg.test(mobilePhone)) {
                        this.$message.warn(`联系人${failContactIndex}号码格式不正确`);
                        return
                    }
                    if (this.$utils.isEmpty(this.contactsArr[arrIndex].contactsType)) {
                        this.$message.warn(`请维护联系人${failContactIndex}的角色职务`);
                        return
                    }
                }
                let basicInfo = that.$utils.deepcopy(that.basicOption);
                // 将值列表显示值转为独立源代码
                let acctType = await that.$lov.getValByTypeAndName('ACCT_TYPE', basicInfo.acctType);
                let acctCategoryVal = await that.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', basicInfo.acctCategory, 'ACCT_TYPE', acctType);
                let subAcctType = await that.$lov.getValByTypeNameParentTypeParentVal('SUB_ACCT_TYPE', basicInfo.subAcctType, 'ACCNT_CATEGORY', acctCategoryVal);
                basicInfo.acctType = acctType;
                basicInfo.acctCategory = acctCategoryVal;
                basicInfo.subAcctType = subAcctType;
                if (that.sellProductArr.length !== 0) {
                    that.sellProductArr.forEach(item => {
                        if (!that.$utils.isEmpty(item.rowId)) {
                            delete item.rowId
                        }
                    });
                }
                if(this.$utils.isEmpty(basicInfo.deliveryProvince) || this.$utils.isEmpty(basicInfo.deliveryTownName)) {
                    basicInfo.deliveryProvince = basicInfo.province
                    basicInfo.deliveryCity = basicInfo.city
                    basicInfo.deliveryDistrict = basicInfo.district
                    basicInfo.deliveryTown = basicInfo.town
                    basicInfo.deliveryTownName = basicInfo.townName
                    basicInfo.deliveryDetailAddr = basicInfo.address
                    basicInfo.deliveryLongitude = basicInfo.longitude
                    basicInfo.deliveryLatitude = basicInfo.latitude
                }
                // 5600 校验是否自营字段必填
                const isCheck = await this.checkIsAutarky(basicInfo);
                if (isCheck) {
                    this.$message['warn']('请维护所售产品是否自营');
                    return
                }
                //如果是缓存的情况，当缓存是一户多开的情景时，回到界面后financialOption里的还是默认的N，可以走如下的逻辑更新financialOption.multiAcctFlag
                if(!!this.pageParam.__fromCache){
                    if(!this.$utils.isEmpty(that.financialArr)){
                        that.financialArr.forEach(item => {
                            if (item.id === this.financialOption.id) {
                                this.financialOption.multiAcctFlag = item.multiAcctFlag;
                            }
                        })
                    }
                }
                //经纬度不允许为空字符串，影响es同步
                if(!basicInfo.latitude) delete basicInfo.latitude
                if(!basicInfo.longitude) delete basicInfo.longitude
                // 仅提交基础信息 + 终端详情跳转
                if (checkFlag && that.authenticationFlag === 'N' && that.$utils.isEmpty(that.pageParam.attestation)) {
                    const checkContactsFlag = await this.checkContactsList();
                    if (!checkContactsFlag) {
                        return
                    }
                    that.$utils.showLoading();
                    if (that.sellProductArr.length !== 0) {
                        basicInfo.saleCateList = that.sellProductArr;
                    }
                    // 新建 or 编辑
                    if (!that.$utils.isEmpty(that.pageParam.editModule)) {
                        basicInfo['row_status'] = 'UPDATE';
                        basicInfo.creditChangedFlag = that.$utils.isEmpty(basicInfo.creditNo) ? 'N' : 'Y';
                    } else {
                        basicInfo['row_status'] = 'NEW';
                        // 新建状态，客户阶段默认赋值为未认证
                        basicInfo.acctStage = 'xk';
                    }
                    !that.$utils.isEmpty(that.pageParam.editModule)
                        ? basicInfo['row_status'] = 'UPDATE'
                        : basicInfo['row_status'] = 'NEW';
                    // 是否连锁模式【chainStoreFlag】字段由"Y"变"N"时，设为"Y"，否则为"N"；
                    if (!that.$utils.isEmpty(that.pageParam.editModule) && that.basicInfoEdit.chainStoreFlag === 'Y' &&  that.basicOption.chainStoreFlag === 'N') {
                        basicInfo.chainChangedFlag = 'Y'
                    }
                    let insertArr = [];
                    if(that.claimFlag) { //认领传参为对象
                        //删掉财务信息
                        const objList=['invoiceCategory','credentType','billTitle','creditNo','accountBankName','mainBillAccntId','billPhone',
                            'billAddr','registrationDate','period','person','business','capital','taxpayerNumber','receiveBillContact','receiveBillPhone',
                            'receiveBillProvince','receiveBillCity','receiveBillDistrict','receiveBillAddr','fixedName','receiveBillEmail','ticketingPhone']
                        objList.map((item) =>{
                            delete basicInfo[item]
                        })
                        basicInfo.multiAcctFlag = 'N';           // 是否一户多开
                        that.insertBasicInfo(basicInfo);
                    }else{
                        insertArr.push(basicInfo);
                        that.insertBasicInfo(insertArr);
                    }
                }
                // 基础信息和财务信息
                if (!this.claimFlag && checkFlag && that.authenticationFlag === 'Y' && that.financialOption.multiAcctFlag === 'N' && await this.checkedFinancial()) {
                    await this.dealBasicFinancial(basicInfo);
                }
                if (this.claimFlag && checkFlag && that.authenticationFlag === 'Y' && that.financialArr[0].multiAcctFlag === 'N' && await this.checkedFinancial()) {
                    await this.dealBasicFinancial(basicInfo);
                }
                // 一户多开基础信息和财务信息
                if (!this.claimFlag && checkFlag && that.authenticationFlag === 'Y' && that.financialOption.multiAcctFlag === 'Y' && await this.checkedFinancial()) {
                    await this.dealMutiBasicFinancial(basicInfo)
                }
                if (this.claimFlag && checkFlag && that.authenticationFlag === 'Y' && that.financialArr[0].multiAcctFlag === 'Y' && await this.checkedFinancial()) {
                    await this.dealMutiBasicFinancial(basicInfo)
                }
            },
            // 基础信息和财务信息
            async dealBasicFinancial(basicInfo) {
                const that = this;
                const checkProductFlag = this.checkProduct()
                if (!checkProductFlag) return
                // 终端联系人校验
                await this.queryContactsList();
                that.$utils.showLoading();
                const checkContactsFlag1 = this.checkContactInfo()
                if (!checkContactsFlag1) {
                    that.$utils.hideLoading();
                    return
                }
                const checkContactsFlag2 = await this.checkContactsList();
                if (!checkContactsFlag2) {
                    that.$utils.hideLoading();
                    return
                }
                // 客户性质为公司时才调用营业执照验真接口
                if (that.financialArr[0].invoiceCategory === 'company') {
                    // 营业执照验真
                    const {licenseReal, licRealRes} = await this.checkInvoice(that.financialArr[0]);
                    that.financialArr[0].licenseReal = licenseReal;
                    if (that.financialArr[0].licenseReal === 'No') {
                        this.$utils.hideLoading();
                        try {
                            await this.showInfoDialog(licRealRes, that.financialArr[0].creditNo);
                            this.$utils.showLoading();
                        } catch (e) {
                            // 根据工商注册号进行查询，复制税号
                            if (licRealRes.regMate === 'Yes' && that.financialArr[0].creditNo !== licRealRes.creditCode) {
                                wx.setClipboardData({data: licRealRes.creditCode});
                            } else if (licRealRes.biliTitle) {
                                // 复制营业执照名称
                                wx.setClipboardData({data: licRealRes.biliTitle});
                            }
                            return;
                        }
                        this.$utils.showLoading();
                    }
                }
                if (that.sellProductArr.length !== 0) {
                    basicInfo.saleCateList = that.sellProductArr;
                }
                let insertArr = [];
                delete basicInfo.id;                                   // 删除基础信息id
                delete basicInfo.multiAcctFlag;                        // 删除基础信息中是否一户多开的字段
                basicInfo.creditChangedFlag = 'Y';                     // 存在税号值更改为Y
                // 新建状态，客户阶段默认赋值为未认证
                basicInfo.acctStage = 'xk';
                (!this.$utils.isEmpty(this.pageParam.attestation) || basicInfo.row_status === 'UPDATE')
                    ? basicInfo.row_status = 'UPDATE'
                    : basicInfo.row_status = 'NEW';
                let insertBasicData = Object.assign({
                    ...basicInfo,
                    ...that.financialArr[0]
                });
                //认领终端新建，传对象
                if(that.claimFlag){
                    that.insertBasicInfo(insertBasicData);
                } else { //新建传数组
                    insertArr.push(insertBasicData);
                    that.insertBasicInfo(insertArr);
                }
            },
            // 一户多开基础信息和财务信息
            async dealMutiBasicFinancial(basicInfo) {
                const that = this;
                //lzljqw-004-780 只有当存在子户头的时候才更改一户多开标识
                if(that.financialArr.length <= 1) {
                    that.$message['warn']('一户多开模式必须添加子户头信息；非一户多开模式，请取消勾选一户多开；');
                    return
                }
                const checkProductFlag = this.checkProduct()
                if (!checkProductFlag) return
                // 终端联系人校验
                await this.queryContactsList();
                that.$utils.showLoading();
                const checkContactsFlag1 = this.checkContactInfo()
                if (!checkContactsFlag1) {
                    that.$utils.hideLoading();
                    return
                }
                const checkContactsFlag2 = await this.checkContactsList();
                if (!checkContactsFlag2) {
                    that.$utils.hideLoading();
                    return
                }
                let insertArr = [];
                delete basicInfo.id;                                   // 删除基础信息id
                delete basicInfo.multiAcctFlag;                        // 删除基础信息中是否一户多开的字段
                basicInfo.creditChangedFlag = 'Y';                     // 存在税号只更改为Y
                // 新建状态，客户阶段默认赋值为未认证
                basicInfo.acctStage = 'xk';
                let basicMation = Object.assign({
                    ...basicInfo,
                    ...that.financialArr[0]
                });
                let children = [];
                let insertProductFlag = false;
                for await (let item of that.financialArr) {
                    // 客户性质为公司时才调用营业执照验真接口
                    if (item.invoiceCategory === 'company') {
                        // 营业执照验真
                        const {licenseReal, licRealRes} = await this.checkInvoice(item);
                        item.licenseReal = licenseReal;
                        // 纳税人识别号与营业执照名称不对应弹窗
                        if (item.licenseReal === 'No') {
                            this.$utils.hideLoading();
                            try {
                                await this.showInfoDialog(licRealRes, item.creditNo);
                            } catch (e) {
                                // 根据工商注册号进行查询，复制税号
                                if (licRealRes.regMate === 'Yes' && item.creditNo !== licRealRes.creditCode) {
                                    wx.setClipboardData({data: licRealRes.creditCode});
                                } else if (licRealRes.biliTitle) {
                                    // 复制营业执照名称
                                    wx.setClipboardData({data: licRealRes.biliTitle});
                                }
                                return;
                            }
                            this.$utils.showLoading();
                        }
                    }
                    if (item.multiAcctMainFlag === 'N') {
                        basicInfo.row_status = 'NEW';
                    } else {
                        (!this.$utils.isEmpty(this.pageParam.attestation) || basicInfo.row_status === 'UPDATE')
                            ? basicInfo.row_status = 'UPDATE'
                            : basicInfo.row_status = 'NEW';
                    }
                    let insertBasicData = Object.assign({
                        ...basicInfo,
                        ...item
                    });
                    if (item.multiAcctMainFlag === 'N') delete insertBasicData.trafficHighland;
                    that.saleCateList.forEach(val => {
                        delete val.id;
                        val.accntId = item.id;
                        val.row_status = 'NEW';
                    });
                    if (this.pageParam.attestation === 'attestation' && item.multiAcctMainFlag !== 'Y') {
                        insertBasicData.saleCateList = that.saleCateList;
                        insertProductFlag = true;
                    }
                    if (that.sellProductArr.length !== 0) {
                        insertBasicData.saleCateList = that.allSaleCateList.filter(item => item.accntId === insertBasicData.id);
                    }
                    insertArr.push(insertBasicData)
                    if(that.claimFlag && item.multiAcctMainFlag !== 'Y') { //认领终端复制-子户头数据
                        children.push(item);
                    }
                }
                if(this.claimFlag) {
                    basicMation.row_status = 'NEW';
                    basicMation.children = children;
                    if (that.sellProductArr.length !== 0) {
                        basicMation.saleCateList = that.sellProductArr;
                    }
                    await this.insertBasicInfo(basicMation);
                }else{
                    await this.insertBasicInfo(insertArr);
                }
                // 认证财务信息且是一户多开插入所售产品
                if (that.pageParam.attestation === 'attestation' && insertProductFlag && !this.claimFlag) { //认领-新建不审批
                    that.insertSellProduct(that.saleCateList);
                }
            },
            removeOrganizationName(data) {
                if (data && typeof data === 'object' && data.hasOwnProperty('organizationName')) {
                    delete data.organizationName;
                }
            },
            /**
             * 终端新建
             * <AUTHOR>
             * @date 2020-09-15
             */
            async insertBasicInfo(insertData) {
                if(this.claimFlag) {
                    this.claimTerminalAdd(insertData)
                }else {
                    this.terminalAdd(insertData)
                }
            },
            /**
             * 认领终端-新建
             * <AUTHOR>
             * @date 2024-09-10
            */
            async claimTerminalAdd(insertData) {
                const that = this
                if(insertData.multiAcctFlag==='N' && this.$utils.isNotEmpty(insertData.children)) { //非一户多开的时候删除子户头数据
                    delete insertData.children;
                }
                if(this.$utils.isNotEmpty(this.contactsArr) && this.contactsArr.length > 0){
                    insertData.contactsList = this.contactsArr;
                    this.contactsArr.forEach((item) => {
                        item.row_status = 'NEW'; //联系人状态为新建
                    })
                }
                // 收货地址-提交处理
                let isCheckAddress = false
                that.$refs['addrInfo'].addressList.forEach(item => {
                    if (item.consignee || item.regionInfo || item.mobilePhone || item.addr) {
                        isCheckAddress = true
                    }
                })
                if (isCheckAddress) {
                    const newAddressList = that.$refs['addrInfo'].addressList;
                    newAddressList.map(good => {
                        good.acctId = insertData.id;
                        good.isEffective = 'Y';
                        delete good.regionInfo;
                        delete good.unfoldFlag;
                    })
                    insertData.addrList = newAddressList;
                }
                try {
                    const data = await this.$http.post('action/link/accnt/claimAccount', insertData,{
                        handleFailed: (e) => {
                            that.$aegis.report({
                                msg: '提交终端信息出错',
                                ext1: JSON.stringify(e),
                                trace: 'log'
                            });
                            that.$utils.hideLoading();
                        }
                    })
                    if (data.success) {
                        let param = {refreshFlag: true};
                        this.$nav.back(param);
                        this.$bus.$emit('terminalListRefresh');
                    }
                } catch(err) {
                    console.log('err:>>>>>>>>>', err);
                }
            },
            /**
             * 创建终端-新建
             * <AUTHOR>
             * @date 2024-09-10
            */
            async terminalAdd(insertData) {
                const that = this
                this.insertAccountData = []
                this.accountImgArray = {}
                if (insertData && insertData.length) {
                    insertData.forEach((item) => {
                        this.removeOrganizationName(item);
                    })
                }
                const data = await that.$http.post('action/link/accnt/batchUpsert', insertData,
                                {
                                    handleFailed: (e) => {
                                        that.$aegis.report({
                                            msg: '提交终端信息出错',
                                            ext1: JSON.stringify(e),
                                            trace: 'log'
                                        });
                                        that.$utils.hideLoading();
                                    }
                                }
                            );
                if (data.success) {
                    this.basicOption = {
                        ...this.basicOption,
                        row_status: 'UPDATE'
                    }
                    //当为养生酒或者优先时，满足以下条件，可直接创建对应审批记录
                    //①新建时
                    //②编辑时,且审批状态为不通过或未审核
                    if(this.isYangShengOrYouXuan && (this.pageParam.editFlag !== 'edit' || (this.pageParam.editFlag === 'edit' && (that.basicOption.auditStatus === 'failed' || that.basicOption.auditStatus === 'new')))) {
                        that.uploadDialog = true
                    }
                    // 点提交按钮，基础信息，财务信息，所有产品都有值且校验通过后，上传审批附件弹框显示
                    this.insertAccountData = insertData
                    insertData.forEach((item)=>{
                        if(item.id === this.accntId){
                            // 新建时构造主户头，编辑只涉及一个户头
                            that.accountImgArray[item.id] = {
                                attachmentObj: this.initFlowAttachment,
                                accountId: item.id,
                                attachmentTitle: item.billTitle
                            }
                        }else{
                            // 新建时，构造子户头
                            that.accountImgArray[item.id] = {
                                attachmentObj: [],
                                accountId: item.id,
                                attachmentTitle: item.billTitle
                            }
                        }
                    })
                    //lzljqw-004-1151从终端详情进入编辑页面不可发起审批，养生酒、优选除外
                    if ((that.checkedContactData() && that.sellProductArr.length === 0) || this.pageParam.editModule === 'basicInfo') {
                        if(!this.uploadDialog) {
                            let param = {refreshFlag: true};
                            this.$nav.back(param);
                            if (this.pageParam.editFlag === 'edit' && this.pageParam.editModule === 'basicInfo') {
                                this.$bus.$emit('terminalListRefresh');
                            }
                        }
                        that.$utils.hideLoading();
                    } else {
                        await that.insertData(insertData);
                    }
                    // 收货地址-提交保存
                    let isCheckAddress = false
                    that.$refs['addrInfo'].addressList.forEach(item => {
                        if (item.consignee || item.regionInfo || item.mobilePhone || item.addr) {
                            isCheckAddress = true
                        }
                    })
                    if (isCheckAddress) {
                        const newAddress = that.$refs['addrInfo'].addressList
                        newAddress.map(async item => {
                            item.acctId = insertData[0].id
                            delete item.regionInfo
                            delete item.unfoldFlag;
                            that.$http.post('action/link/acctaddress/upsert', {...item})
                        })
                    }
                }
            },
            /**
             * 插入联系人和所售产品
             * <AUTHOR>
             * @date 2020-09-18
             * @param insertData 财务信息和基础信息
             */
            async insertData(insertData) {
                const that = this;
                Promise.all([
                    that.insertSellProduct(this.allSaleCateList),
                    that.insertContacts()]).then(() => {
                    // 只有新建时，才弹框
                    let editAuthenticationFlag = false;
                    // 从基础信息编辑进入， credentType|| billTitle|| creditNo 有值 等同于财务认证标志-authenticationFlag 为Y
                    if((this.pageParam.editFlag === 'edit') && this.$utils.isNotEmpty(this.basicInfoEdit.credentType)
                        ||this.$utils.isNotEmpty(this.basicInfoEdit.billTitle)||this.$utils.isNotEmpty(this.basicInfoEdit.creditNo)){
                        editAuthenticationFlag = true;
                    }
                    if((this.authenticationFlag === 'Y'|| editAuthenticationFlag) && (that.basicOption.auditStatus === 'failed'
                        || that.basicOption.auditStatus === 'new' || that.$utils.isEmpty(that.pageParam.editModule))){
                        if(!this.isYangShengOrYouXuan) {
                            that.uploadDialog = true
                        }else {
                            //养生酒和优选编辑信息之后都是直接返回，触发审批流程再insertBasicInfo新建终端后判段
                            if(!this.uploadDialog) {
                                let param = {refreshFlag: true};
                                this.$nav.back(param);
                                if (this.pageParam.editFlag === 'edit' && this.pageParam.editModule === 'basicInfo') {
                                    this.$bus.$emit('terminalListRefresh');
                                }
                            }
                        }
                    }else{
                       if(!this.uploadDialog) {
                           let param = {refreshFlag: true};
                           this.$nav.back(param);
                           if (this.pageParam.editFlag === 'edit' && this.pageParam.editModule === 'basicInfo') {
                               this.$bus.$emit('terminalListRefresh');
                           }
                       }
                    }
                    if(that.uploadDialog){
                        that.gotoTerminalList()
                    }
                    that.$utils.hideLoading();
                })
            },
            /**
              * 插入审批
              * <AUTHOR>
              * @date 2020-09-18
              * @param insertData
            */
            insertApprove (insertData) {
              let data = insertData
              data.forEach((item,index)=>{
				  let arr = []
				  if(this.newImgArray[item.id]){
					  arr= this.newImgArray[item.id].attachmentObj
				  }

                let attachmentIds = arr.map((item)=>{return{
                  attachmentId: item.id
                }})
                item.flowAttachmentList = attachmentIds
              })
                return new Promise((res, rej) => {
                    this.$http.post('action/link/accnt/batchSubmitOpenAccount', data,
                        {
                            handleFailed: (error)=> {
                                this.$utils.hideLoading();
                                rej(error);
                            }
                        }).then(data => {
                        if (data.success) {
                            res(data.success)
                        }
                    })
                });
            },
            async dealUpsertContactList(contactCopyList,multiAcctMainYList,multiAcctMainNList){
                let upsertContactList = [];
                for (const itemC of contactCopyList) {
                    const processContacts = async (acctList, useExistingId = false) => {
                        for (const acct of acctList) {
                            let newId;
                            //2021-04-22前端生成联系人ID，并且把主户头联系人ID给子户头的parContactId字段，主户头联系人parContactId值为id
                            // 用于后续更新主户头的主要联系人时后台处理更新子户头的主要联系人
                            if (!useExistingId) {
                                newId = await this.$newId();
                            } else {
                                newId = itemC.id; // 或者使用 itemC.id 作为 parContactId
                            }
                            let con = { ...itemC };
                            delete con.acctId;
                            con.acctId = acct.id;
                            con.id = useExistingId ? itemC.id : newId;
                            con.parContactId = useExistingId ? (this.parContactId ?? newId) : itemC.id; // useExistingId 为 false，则设置 parContactId 为新生成的 ID，否则为 itemC.id
                            upsertContactList.push(con);
                        }
                    };
                    //onlyAddSubAccount代表从终端详情，点击前往认证进来，由于进来前做了联系人校验，所以主户头不需要添加联系人信息
                    if (!this.$utils.isEmpty(multiAcctMainYList) && !this.pageParam.onlyAddSubAccount) {
                        await processContacts(multiAcctMainYList, true); // 使用 itemC.id 作为 ID 和 parContactId
                    }
                    if (!this.$utils.isEmpty(multiAcctMainNList)) {
                        await processContacts(multiAcctMainNList, false); // 生成新 ID
                    }
                }
                return upsertContactList;
            },
            /**
              * 插入联系人
              * <AUTHOR>
              * @date 2020-09-16
              * @param param
            */
            insertContacts () {
                const that = this;
                return  new Promise(async (res, rej) => {
                    //lzlj-002-3490将2份联系人信息合并去重
                    let contacts = [...this.contactsArr, ...this.contactList];
                    let arr = [];
                    for (let item of contacts) {
                        const flag1 = this.contactsArr.every(item1 =>
                            item.id ? item1.id !== item.id : item1.mobilePhone !== item.mobilePhone
                        );
                        const flag2 = this.contactList.every(item2 =>
                            item.id ? item2.id !== item.id : item2.mobilePhone !== item.mobilePhone
                        );
                        if (flag2 && !flag1) {
                            arr.push({...item, row_status: 'NEW'});
                        }
                    }
                    if (arr.length !== 0) {
                        let acctType = await that.$lov.getValByTypeAndName('ACCT_TYPE', this.basicOption.acctType)
                        let contactList = that.$utils.deepcopy(arr);
                        contactList.forEach((item, index) => {
                            // 判断联系人中整个对象都为空的情况
                            that.$set(item, 'belongTo', acctType);
                            let condition = that.$utils.isEmpty(item.contactsName) && that.$utils.isEmpty(item.contactsSex) && that.$utils.isEmpty(item.mobilePhone) && that.$utils.isEmpty(item.fixedPhone)
                                && that.$utils.isEmpty(item.birthdayType) && that.$utils.isEmpty(item.birthYear) && that.$utils.isEmpty(item.birthday) && that.$utils.isEmpty(item.comments);
                            if (condition) {
                                contactList.splice(index, 1)
                            } else {
                                // item['row_status'] = 'NEW';
                                delete item.unfoldFlag
                            }
                        });
                        if (contactList.length !== 0) {
                          // 处理联系人给子户头一份
                          let upsertContactList = [];
                          let contactCopyList  = this.$utils.deepcopy(contactList);
                          let multiAcctMainYList = this.financialArr.filter((item) => item.multiAcctMainFlag === 'Y');
                          let multiAcctMainNList = this.financialArr.filter((item) => item.multiAcctMainFlag !== 'Y');
                          upsertContactList = await this.dealUpsertContactList(contactCopyList,multiAcctMainYList,multiAcctMainNList);
                          setTimeout(() => {
                            that.$http.post('action/link/contacts/batchUpsert', upsertContactList, {
                              handleFailed: (error)=> {
                                that.$utils.hideLoading();
                                rej(error)
                              }
                            }).then(data => {
                              if (data.success) {res()}
                            })
                          }, 1000);

                        } else {res()}
                    } else {res()}
                });
            },
            /**
              * 插入所售产品
              * <AUTHOR>
              * @date 2020-09-16
              * @param array
            */
            insertSellProduct (array) {
                return new Promise((res, rej) => {
                    if (array.length !== 0) {
                        this.$http.post('action/link/saleCategory/batchUpsert', array, {
                            handleFailed: (error)=> {
                                this.$utils.hideLoading();
                                rej(error);
                            }
                        }).then(data => {
                            if (data.success) {res()}
                        })
                    } else {
                        res()
                    }
                });
            },
            /**
             * 更改状态是修改的sellProductArr上的status，提交时候用的数据是allSaleCateList，不改变以前的逻辑下只能在更改的时候去更新对应的数据
             * <AUTHOR>
             * @date 2023-8-8
             */
            changeStatus(arr) {
                this.addSellProduct(arr)
            },
            /**
             * 所售产品添加成功
             * <AUTHOR>
             * @date 2020-09-14
             * @param array
             */
            addSellProduct (array) {
                this.sellProductArr = array.map(item => {
                    // 所售产品赋值
                    let obj = {
                        prodId: item.prodId,
                        prodCode: item.prodCode,
                        prodName: item.prodName,
                        prodSeries: item.prodSeries,
                        prodSecSeries: item.prodSecSeries,
                        prodThirdSeries: item.prodThirdSeries,
                        status: item.status || 'Y',
                        accntId: this.accntId,
                        row_status: 'NEW',
                        rowId: item.id,
                        supplierManageMode: item.supplierManageMode,
                        supplierAcctType: item.supplierAcctType || item.acctType,
                        supplierName: item.supplierName,
                        supplierId: item.supplierId,
                        id:item.id
                    };
                    if (item.selfSupport) {
                        obj.selfSupport = item.selfSupport
                    };
                    return obj
                });
                this.allSaleCateList = [];
                array.forEach(item => {
                    this.financialArr.forEach(val => {
                        const deepItem = JSON.parse(JSON.stringify(item));
                        let opt = {
                            prodId: deepItem.prodId,
                            prodCode: deepItem.prodCode,
                            prodName: deepItem.prodName,
                            prodSeries: deepItem.prodSeries,
                            prodSecSeries: deepItem.prodSecSeries,
                            prodThirdSeries: deepItem.prodThirdSeries,
                            status: deepItem.status || 'Y',
                            accntId: val.id,
                            row_status: 'NEW',
                            supplierManageMode: deepItem.supplierManageMode,
                            supplierName: deepItem.supplierName,
                            supplierId: deepItem.supplierId,
                        };
                        if (deepItem.selfSupport) {
                            opt.selfSupport = deepItem.selfSupport
                        };
                        this.allSaleCateList.push(opt);
                    })
                });
                // 当前用户所属品牌公司为博大公司，根据所售产品判断【合作状态】
                if (this.broadCompanyCode.indexOf(this.userInfo.coreOrganizationTile.brandCompanyCode) !== -1) {
                    const effectiveProd = this.sellProductArr.filter((item) => item.status === 'Y');
                    if (effectiveProd.length) {
                        this.$set(this.basicOption, 'joinFlag', 'Y');
                    } else {
                        this.$set(this.basicOption, 'joinFlag', 'N');
                    }
                }
            },
            /**
             * 删除选择
             * <AUTHOR>
             * @date 2020-11-17
             */
            deleteIndex (index) {
                this.sellProductArr.splice(index, 1);
                this.allSaleCateList.splice(index, 1);
                // 当前用户所属品牌公司为博大公司，根据所售产品判断【合作状态】
                if (this.broadCompanyCode.indexOf(this.userInfo.coreOrganizationTile.brandCompanyCode) !== -1) {
                    const effectiveProd = this.sellProductArr.filter((item) => item.status === 'Y');
                    if (effectiveProd.length) {
                        this.$set(this.basicOption, 'joinFlag', 'Y');
                    } else {
                        this.$set(this.basicOption, 'joinFlag', 'N');
                    }
                }
            },
            /**
             * 是否一户多开switch开关
             * <AUTHOR>
             * @date 2020-09-11
             * @param val
             */
            switchChange (val) {
                this.financialOption.multiAcctFlag = val;
                switch (val) {
                    case 'Y':
                        this.addAccountFlag = true;
                        this.addAccounts();
                        break;
                    case 'N':
                        let arr = this.financialArr.filter(item => item.multiAcctMainFlag === 'Y');
                        this.financialArr = arr;
                        this.addAccountFlag = false;
                        break
                }
            },
            /**
             * 添加子户头
             * <AUTHOR>
             * @date 2020-09-11
             */ async addAccounts() {
                let options = {
                    invoiceCategory: 'company',     // 开票类型
                    credentType: 'ValueAddedTax',   // 开票类型
                    billTitle: null,                // 营业执照名称
                    creditNo: null,                 // 统一社会信用代码
                    accountBankName: null,          // 开户行名称
                    mainBillAccntId: null,          // 银行账户
                    billPhone: null,                // 注册电话
                    id: await this.$newId(),        // id
                    billAddr: null,                 // 公司注册详细地址
                    taxpayerNumber: null,           // 联行号
                    multiAcctFlag: 'Y',             // 是否一户多开
                    multiAcctMainFlag: 'N',         // 是否为主户头
                    multiAcctMainId: this.accntId,  // 主户头id
                    receiveBillContact: '',         //收票人姓名
                    receiveBillPhone: '',           //收票人联系方式
                    receiveBillProvince: '',        //收票人邮寄地区
                    receiveBillAddr: '',            //收票人邮寄地址
                    receiveBillEmail: '',           //电票接收邮箱
                    ticketingPhone: '' ,            //电票接收电话
                    unfoldFlag: true             // 折叠参数
                };
                this.financialArr.push(options);
            },
            /**
              * 删除子户头
              * <AUTHOR>
              * @date 2020-09-11
              * @param val 索引
            */
            deleteChildAccount (val) {
                this.financialArr.splice(val, 1);
            },
          /**
           * @createdBy  张丽娟
           * @date  2021/4/6
           * @methods creditNoChange
           * @para
           * @description 修改
           */
            creditNoChange(obj){
              this.financialArr[obj.index].creditNo = obj.val
              this.financialArr = this.$utils.deepcopy(this.financialArr)
            },
            /**
              * 获取基础信息和财务信息默认值
              * <AUTHOR>
              * @date 2020-09-10
            */
            async getTerminalDefault () {
                const that = this;
                const contactId = await this.$newId();
                that.$http.post('action/link/accnt/preDefaultValue', {
                    acctType: 'Terminal'
                }).then(data => {
                    if (data.success) {
                      let opt = {
                        id: data.result.id
                      };
                      that.accntId = data.result.id;
                        that.basicOption = { ...that.basicOption, ...opt };
                      that.financialOption = {...that.financialOption, ...{id: data.result.id}};
                      that.financialArr.push(that.financialOption);
                      that.contactsOption = {...that.contactsOption, ...{acctId: data.result.id}, id: contactId};
                      that.contactsArr.push(that.contactsOption);
                      that.accountImgArray[that.accntId] = {
                        attachmentObj: [],
                        accountId: that.accntId,
                      }
                    }
                })
            },
            /**
             * 数据初始化
             * <AUTHOR>
             * @date 2020-09-06
             */
            async initData() {
                this.coordinate =  await this.$locations.getCurrentCoordinate();
            },
            /**
              * 编辑进入获取数据
              * <AUTHOR>
              * @date 2020-09-17
              * @param param
            */
            queryByIdTerminalData () {
                if (this.$utils.isEmpty(this.pageParam.accntId)) {
                    return;
                }
                const that = this;
                that.$http.post('action/link/accnt/queryById', {
                    id: that.pageParam.accntId
                }).then(async data => {
                    if (data.success) {
                        //将invoiceCategory设置成响应式
                        if (that.$utils.isEmpty(data.result.invoiceCategory)) {
                            that.$set(data.result, 'invoiceCategory', 'company');
                        }
                        // 是否为一户多开字段undefined时赋值默认值
                        if (that.$utils.isEmpty(data.result.multiAcctFlag)) {
                            that.$set(data.result, 'multiAcctFlag', 'N');
                        }
                        // 删掉附件数据
                        if (!that.$utils.isEmpty(data.result.attachmentList)) {
                            delete data.result.attachmentList
                        }
                        // 所售产品数组为null或者undefined时赋值，用于校验[是否子公司供货]和实际所售产品数据是否匹配
                        if (that.$utils.isEmpty(data.result.saleCateList)) {
                            data.result.saleCateList = []
                        }
                        // 删除空数组
                        if (!that.$utils.isEmpty(data.result.addrList) || data.result.addrList.length === 0) {
                            delete data.result.addrList;
                        }
                        // 判断主户头，添加收票人信息字段
                        if (data.result.multiAcctMainFlag === 'Y' && !that.$utils.isEmpty(that.pageParam.attestation)) {
                            data.result.receiveBillContact = null;                                              // 收票联系人
                            data.result.receiveBillPhone = null;                                                // 收票联系方式
                            data.result.receiveBillEmail = null;                                                // 收票人邮箱
                            data.result.ticketingPhone = null;                                                  // 电票联系电话
                            data.result.receiveBillProvince = null;                                             // 收票人邮寄地区-省份
                            data.result.receiveBillCity = null;                                                 // 收票人邮寄地区-城市
                            data.result.receiveBillDistrict = null;                                             // 收票人邮寄地区-区县
                            data.result.receiveBillAddr = null;                                                 // 收票详细地址
                        }
                        // 审批状态 如果没有则添加状态
                        if (that.$utils.isEmpty(data.result.auditStatus)) {
                            data.result.auditStatus = 'new';
                        }
                        if (that.$utils.isEmpty(data.result.chainChangedFlag)) {
                            data.result.chainChangedFlag = 'N';
                        }
                        data.result.unfoldFlag = true;
                        that.financialOption = that.$utils.deepcopy(data.result);
                        that.financialOption.invoiceCategory = that.financialOption.invoiceCategory || 'company';
                        that.financialArr.push(that.financialOption);
                        that.accntId = data.result.id;
                        data.result.acctType = await that.$lov.getNameByTypeAndVal('ACCT_TYPE', data.result.acctType);
                        data.result.acctCategory = await that.$lov.getNameByTypeAndVal('ACCNT_CATEGORY', data.result.acctCategory);
                        data.result.subAcctType = await that.$lov.getNameByTypeAndVal('SUB_ACCT_TYPE', data.result.subAcctType);
                        data.result.mdmCompanyCode = that.$utils.isEmpty(data.result.mdmCompanyCode) ? this.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode : data.result.mdmCompanyCode;
                        that.basicOption = data.result;
                        that.initAcctCategory = that.basicOption.acctCategory; //获取编辑时的中类
                        if( data.result.mdmCompanyCode==='1612' ){
                            that.isChuanDong = true
                        }
                    }
                })
            },
            /**
              * taps切换事件
              * <AUTHOR>
              * @date 2020-08-05
              * @param val taps选中对象
              * @param key taps选中对象索引
            */
            switchTab (val, key) {
                this.tapsActive = val;
                if (this.scrollTop > 100) {
                    wx.pageScrollTo({
                        scrollTop: 0
                    })
                }
            },
            /**
             * @createdBy  谭少奇
             * @date  2023/09/04
             * @para
             * @description 上传附件成功后
             */
            newImgUploadSuccess(arr,accountId) {
              this.newImgArray[accountId] = {
                attachmentObj: arr,
                accountId: accountId,
              }
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/9/25
             * @para
             * @description 上传图片成功后
             */
            imgUploadSuccess(arr,accountId) {
              this.accountImgArray[accountId] = {
                attachmentObj: arr,
                accountId: accountId,
              }
            },
            /**
             * @createdBy  张丽娟
             * @date  2021/3/2
             * @para
             * @description  附件上传完成，返回到列表界面
             */
            async gotoTerminalList(){
              this.$utils.showLoading();
              await this.insertApprove(this.insertAccountData)
              this.$utils.hideLoading()
              let param = {refreshFlag: true};
                this.$nav.back(param);
                if (this.pageParam.editFlag === 'edit' && this.pageParam.editModule === 'basicInfo') {
                    this.$bus.$emit('terminalListRefresh');
                }
            },
          /**
           * @createdBy  张丽娟
           * @date  2021/3/5
           * @para
           * @description 获取初始化开户审批流附件
           */
          async fetchInitAttachmentArray (accntId) {
            let filtersRaw = [
              {id: "flowObjId", property: "flowObjId", value: accntId, operator: "="},
              {id: "approvalFlowType", property: "approvalFlowType", value: 'OpenAccount', operator: "="},
              ]
            const data = await this.$http.post('action/link/flowAttachment/v2/queryByExamplePage', {filtersRaw: filtersRaw});
            if (data.success) {
              data.rows.forEach((item)=>{
                item.smallurl = item.smallUrl
                item.id = item.attachmentId
              })
              this.initFlowAttachment = data.rows
            }else{
              this.$utils.showAlert('获取附件失败！', {icon: 'none'});
            }
          },
         /**
          * 根据key值获取参数配置
          * <AUTHOR>
          * @date 8/11/21
          * @param key 参数配置健值
         */
          async queryCfgProperty(key) {
            const data = await this.$http.post('action/link/cfgProperty/queryByExamplePage', {
              filtersRaw: [
                {id: 'key', property: 'key', value: key}
              ]
            });
            if (data.success && data.rows && data.rows.length) {
              return data.rows[0].value;
            } else {
              return 'noMatch';
            }
          },
            /**
             *  @description: 根据选择的省市区和输入的详细地址联想
             *  @author: syr
             *  @date: 2021/09/17
             */
            async searchAddress(){
                if(this.$utils.isEmpty(this.selectAddressObj.addr)){
                    this.$showError("请输入详细地址");
                    return false;
                }
                const address = this.selectAddressObj.province + this.selectAddressObj.city + this.selectAddressObj.district + this.selectAddressObj.addr;
                const data = await this.$locations.getTMapSuggestion(address);
                this.suggestionAddressData = [...data.data];
            },
            /**
             *  @description: 选择某一个联想地址
             *  @author: syr
             *  @date: 2021/09/18
             */
            selectAddress(item){
                const that = this;
                that.$set(item, '_checked', true);
                that.suggestionAddressData.filter(function (val) {
                    if (val.address !== item.address) {
                        that.$set(val, '_checked', false);
                    }
                });
            },
            /**
             *  @description: 确认某一个联想地址
             *  @author: 宋燕荣
             *  @date: 2021/09/18
             */
            async confirmAddress(){
                const that = this;
                const address = this.suggestionAddressData.filter((item1) => item1._checked === true);
                if(this.$utils.isEmpty(address)){
                    this.$message.info("请选择一个地址");
                    return false;
                }
                that.basicOption.province = address[0].province;
                that.basicOption.city = address[0].city;
                that.basicOption.district = address[0].district;
                that.basicOption.address = address[0].title;
                that.basicOption.fifthGLocationFailStatus = 'Y';
                that.$refs.locationFailSelectAddress.hide();
            }
        }
    }
</script>

<style lang="scss">
    .new-construction-page {
        /*deep*/ .link-search-input-no-padding-bottom {
                     padding-bottom: 24px!important;
                     border-bottom: 1px solid #F2F2F2;
                }
        background: $bg-color-lite;
        .content-container{
            padding-top: 24px;
            background: $bg-color-lite;
        }
        .agreement-upload-content{
            display: flex;
            justify-content: center;
            .lnk-img{
                width: 95%;
                max-width: 432px
            }
            .lnk-img-item{
                width: 45%!important;
                max-width: 206px;
                &:nth-child(odd){
                    margin-right: 20px;
                    margin-left: 0px;
                }
                &:nth-child(even){
                    margin-right: 0px;
                    margin-left: 0px;
                }
            }
        }
        .select-box {
            @include flex-start-center;
            border-bottom: 1px solid #F2F2F2;
            .select-left {
                width: 100%;
                padding-left: 24px;
                .prod-num {
                    text {
                        font-family: PingFangSC-Regular,serif;
                        font-size: 28px;
                        color: #FFFFFF;
                        letter-spacing: 0;
                        line-height: 28px;
                        background: #A6B4C7;
                        border-radius: 8px;
                        padding: 6px 12px;
                    }
                    margin-top: 6px;
                    margin-bottom: 20px;
                }
                .store-name {
                    width: 100%;
                    font-family: PingFangSC-Regular,serif;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    font-weight: bold;
                }
                .store-supplier {
                    padding-top: 8px;
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                }
            }
        }
        .bottom-sticky {
            .sticky {
                width: 100%;
                /*deep*/.link-button {
                            width: 94%;
                            height: 96px;
                            margin-right: 24px;
                            margin-left: 24px;
                        }
            }
        }
        .link-dialog .link-dialog-content {
            .link-dialog-body {
                padding: 0px !important;
                -webkit-flex: 1;
                -ms-flex: 1;
                flex: 1;
                font-size: 28px;
                overflow: hidden;
                word-break: break-all;

            }
        }
        .location-select-address {
            width: 654px !important;
            .link-dialog-content .link-dialog-body {
                padding: 0 !important;
                -webkit-flex: 1;
                -ms-flex: 1;
                flex: 1;
                font-size: 28px;
                overflow: hidden;
                word-break: break-all;
            }
        }
        .address-t {
            width: 654px;
            height: 900px;
            border-radius: 16px;
            background-color: white;
            .list-container {
                height: 560px;
                .list-item {
                    display: flex;
                    border-bottom: 1px solid #F2F2F2;

                    .left-content {
                        display: inline-block;
                        width: 75%;

                        .row-1 {

                            .name{
                                font-family: PingFangSC-Semibold,serif;
                                font-size: 28px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 32px;
                                margin-left: 24px;
                                margin-top: 20px;
                            }
                            .address{
                                margin-left: 24px;
                                margin-top: 10px;
                                font-family: PingFangSC-Regular,serif;
                                font-size: 24px;
                                color: #8C8C8C;
                                letter-spacing: 0;
                                line-height: 32px;
                                margin-bottom: 10px;
                            }
                        }

                        .row-2 {
                            width: 100%;
                            margin-top: 1vh;

                            .date {
                                color: gray;
                                font-size: 12px;
                            }
                        }
                    }

                    .right-content {
                        padding-left: 20px;
                        display: inline-block;
                        width: 15%;
                        line-height: 92px;
                        text-align: right;
                    }
                }
            }

            .address-v {
                width: 100%;

                .t-left {
                    padding: 20px;
                    height: 60px;

                    .title {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #333333;
                        letter-spacing: 0;
                        line-height: 44px;
                        width: 25%;
                        float: left;

                    }
                    .val {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 0;
                        text-align: right;
                        line-height: 44px;
                        width: 50%;
                        float: left;
                    }
                    .ent-wrap{
                        color: $main-color;
                        white-space: nowrap;
                        display: inline-block;
                        height: 60px;
                        line-height: 60px;
                        width: 20%;
                        float: left;
                        text-align: center;
                    }
                }
            }
        }
    }
</style>
