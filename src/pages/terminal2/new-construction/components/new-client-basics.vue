<template>
    <link-page class="new-client-basics" ref="basicsPage">
        <link-form ref="form" :value="formOption" :rules="formRules">
            <link-form-item label="客户分类" required >
                <view slot="title" @tap="showExplain = !showExplain">
                    <text>客户分类</text>
                    <link-icon icon="mp-info-lite" status="info"/>
                </view>
                <link-address
                        :disabled="!acctCascadeData"
                        :province.sync="formOption.acctType"
                        :city.sync="formOption.acctCategory"
                        :district.sync="formOption.subAcctType"
                        :custom="customOption"/>
            </link-form-item>
            <link-form-item label="客户类型" key="acctSort" v-if="isNewShiJiaZhuang" required>
                <link-lov type="ACCT_SORT" v-model="formOption.acctSort"/>
            </link-form-item>
            <link-form-item label="客户性质" key="acctNature" v-if="isNewShiJiaZhuang" required>
                <link-lov type="ACCT_NATURE" v-model="formOption.acctNature"/>
            </link-form-item>
            <link-form-item label="系统名称" v-if="acctCategoryVal === 'qdlx-2' || acctTypeVal === 'Terminal'&& isXinLingShou" required arrow>
                <link-object pageTitle="选择系统名称" :value="formOption.kaSystemName" :row="formOption" :option="superOption"
                             :map="{kaSystemName:'superName',xAttr51:'id'}">
                    <template v-slot="{data}">
                        <item :title="data.superName" :desc="data.val" :key="data.id" :data="data"/>
                    </template>
                </link-object>
            </link-form-item>
            <link-form-item label="店号" v-if="acctCategoryVal === 'qdlx-2' || acctTypeVal === 'Terminal'&& isXinLingShou" field="xAttr71" :required="subAcctTypeVal !== 'LianSuoBianLi' && acctCategoryVal === 'qdlx-2'">
                <link-input placeholder="请输入店号" v-model="formOption.xAttr71"/>
            </link-form-item>
            <!-- 240328迭代取消格式校验 新增字符限制-->
            <link-form-item required field="acctName" v-if="acctTypeVal === 'Terminal'">
                <view slot="title">
                    <text>门头名称</text>
                </view>
                <link-input placeholder="请输入门头名称" v-model="formOption.acctName" :inputReadonly="inputReadOnlyAcct"/>
            </link-form-item>

            <!-- 240328迭代取消格式校验 新增字符限制-->
            <link-form-item required v-if="acctTypeVal === 'Distributor'" field="acctName">
                <view slot="title">
                    <text v-if="acctCategoryVal === 'GroupBuy'">团购客户名称</text>
                    <text v-else>分销商名称</text>
                </view>
                <link-input :placeholder="acctCategoryVal === 'Distributor'? '请输入分销商名称': '请输入团购客户名称'" v-model="formOption.acctName" :inputReadonly="inputReadOnlyAcct"/>
            </link-form-item>
            <!-- 240328迭代取消格式校验 新增字符限制-->
            <link-form-item label="客户简称" field="simName" :itemProps="{rightWidth:'500rpx'}">
                <link-input placeholder="请输入客户简称" v-model="formOption.simName" :inputReadonly="inputReadOnlySimName"/>
            </link-form-item>
            <link-form-item label="考核客户" field="accessAcctId" v-if="isNewShiJiaZhuang">
                <link-object :option="assessmentAccnt"
                             pageTitle="请选择考核客户"
                             :row="targetAccessAcct"
                             :value="targetAccessAcct.accessAcctName"
                             :map="{accessAcctId: 'id', accessAcctName: 'acctName'}">
                    <template v-slot="{data}">
                        <item :title="data.acctName" :key="data.id" :data="data"/>
                    </template>
                </link-object>
            </link-form-item>
            <link-form-item label="客户喷码" key="accntLogisticsCode" v-if="isGuoJiao">
                <link-input placeholder="请输入客户喷码" v-model="formOption.accntLogisticsCode"/>
            </link-form-item>
            <link-form-item :required="acctCategoryVal !== 'GroupBuy'">
                <view slot="title"  @tap="getLocation('storeAddr')">
                    <text>门店地址</text>
                    <link-icon icon="icon-location" class="link-location"/>
                </view>
                <view class="address-placeholder" v-if="$utils.isEmpty(formOption.province) && $utils.isEmpty(formOption.city) && $utils.isEmpty(formOption.district)" @tap="getLocation('storeAddr')">
                    请选择门店地址
                </view>
                <view class="address-color" @tap="getLocation('storeAddr')" v-else>
                    <text v-if="!$utils.isEmpty(formOption.province)">{{formOption.province}}</text><text v-if="!$utils.isEmpty(formOption.city)">/{{formOption.city}}</text><text v-if="!$utils.isEmpty(formOption.district)">/{{formOption.district}}</text><text v-if="!$utils.isEmpty(formOption.townName)">/{{formOption.townName}}</text>
                </view>
            </link-form-item>
            <link-form-item label="详细地址"
                            v-if="$utils.isNotEmpty(formOption.province)"
                            vertical style="border-bottom: 1px solid rgb(247,247,247);" :required="acctCategoryVal !== 'GroupBuy'">
                <link-textarea :disabled="claimFlag" @done="addressDetail('address')" v-model="formOption.address" :nativeProps="{maxlength: 300}"/>
            </link-form-item>
            <view class="door-head" v-if="acctTypeVal === 'Terminal'"><text class="asterisk">*</text>门头照片</view>
            <view class="door-head" v-if="acctTypeVal !== 'Terminal'"><text></text>门头照片</view>
            <lnk-img-watermark :parentId="formOption.id"
                             v-if="$utils.isNotEmpty(formOption.id)"
                             :moduleType="moduleType"
                             :moduleName="moduleName"
                             @notGoCamera="notGoCamera"
                             :continueFlag="false"
                             :delFlag="true"
                             :album="false"
                             :useModuleName="formOption.acctName"
                             @imgUploadSuccess="imageArrLength"
                             @imgDeleteSuccess="deleteImageArrLength"
                             :maxCount="1"
                             :newFlag="storeHeadLength === 0">
            </lnk-img-watermark>

            <!-- 考核组织 -->
            <link-form-item  label="考核组织" field="assessOrgName" disabled v-if="acctTypeVal === 'Terminal' && showOrg">
                <text>{{formOption.assessOrgName}}</text>
            </link-form-item>
            <link-form-item label="是否为连锁模式" v-if="acctTypeVal === 'Terminal'" required>
                <link-switch v-model="formOption.chainStoreFlag" @change='changeSwitch'/>
            </link-form-item>
            <link-form-item label="是否泸州老窖官方形象店" required
                            key="isExclusiveShop"
                            v-if="acctTypeVal === 'Terminal' && !isXinLingShou">
                <link-lov type="IS_FLAG" v-model="formOption.isExclusiveShop"/>
            </link-form-item>
            <link-form-item label="是否专营公司股东门店" required
                            key="isShareHolderShop"
                            v-if="acctTypeVal === 'Terminal' && !isXinLingShou">
                <link-lov type="IS_FLAG" v-model="formOption.isShareHolderShop"/>
            </link-form-item>
            <!-- <link-form-item label="是否星火终端" required
                            key="starfireFlag"
                            v-if="formOption.starfireFlag && acctTypeVal === 'Terminal'">
                <link-lov type="IS_FLAG" v-model="formOption.starfireFlag"/>
            </link-form-item> -->
            <link-form-item label="是否精英荟会员" v-if="acctTypeVal === 'Terminal' && isJiaoLing" required>
                <link-switch v-model="formOption.channelMemberFlag"/>
            </link-form-item>
            <link-form-item label="是否为连锁总店" v-if="formOption.chainStoreFlag === 'Y' && acctTypeVal === 'Terminal'" required>
                <link-switch  v-model="formOption.chainHeadStoreFlag"/>
            </link-form-item>
            <link-form-item 
              label="连锁总店名称" 
              v-if="formOption.chainStoreFlag === 'Y' && formOption.chainHeadStoreFlag === 'N' && acctTypeVal === 'Terminal'"  
              required
              field="chainHeadStoreName"
            >
                <link-input inputReadonly @tap="goToStoreList(formOption)" suffixIcon="mp-arrow-right" v-model="formOption.chainHeadStoreName"/>
            </link-form-item>
            <link-form-item label="是否合作客户" v-if="isChuanDongOrXingJi" key="accntPartner" :disabled="partnerDisabled">
                <link-lov key="accntPartner1" v-if="formOption.accntPartner !== 'stopCooperation'" type="ACCT_STATE" v-model="formOption.accntPartner" :excludeLovs="['stopCooperation']" />
                <link-lov key="accntPartner2" v-else type="ACCT_STATE" v-model="formOption.accntPartner"/>
            </link-form-item>
            <link-form-item label="客户规划等级" :required="requiredFlag" :disabled="disabledFlag"
                            key="acctLevel"
                            v-show="acctTypeVal === 'Terminal'">
                <link-lov type="ACCT_LEVEL" :parentVal="formOption.mdmCompanyCode" parentType="BRAND_COM_NAME" v-model="formOption.acctLevel"/>
            </link-form-item>
            <link-form-item label="容量级别" v-show="acctTypeVal === 'Terminal'" key="capacityLevel" :required="requiredFlag" :disabled="disabledFlag">
                <link-lov type="CAPACITY_LEVEL" v-model="formOption.capacityLevel"/>
            </link-form-item>
            <link-form-item label="是否为品鉴基地" v-if="acctCategoryVal === 'qdlx-4'" :disabled="true" required>
                <!-- <link-switch :disabled="true" v-model="formOption.judgmentFlag" @change="judgmentFlagChangeHandler"/> -->
                <link-lov type="IS_FLAG" v-model="formOption.judgmentFlag"/>
            </link-form-item>
            <link-form-item label="是否为活动场地" v-if="acctCategoryVal === 'qdlx-4'" required>
                <link-switch v-model="formOption.appreciationFlag"/>
            </link-form-item>
            <link-form-item label="合作状态" v-show="broadCompanyCode.indexOf(userInfo.coreOrganizationTile.brandCompanyCode) !== -1" key="joinFlag">
                <link-lov type="JOIN_FLAG" v-model="formOption.joinFlag" readonly/>
            </link-form-item>
            <link-form-item label="形象包间数" required v-if="formOption.judgmentFlag === 'Y' && acctCategoryVal === 'qdlx-4'">
                <link-number-keyboard placeholder="请输入形象包间数" v-model="formOption.imageRoomNum"/>
            </link-form-item>
           
            <link-form-item label="营业开始时间" required v-if="acctCategoryVal === 'qdlx-4'">
                <link-date view="Hm" v-model="formOption.businessStartTime" max="23:59" min="00:00" displayFormat="HH时mm分" valueFormat="HH:mm"/>
            </link-form-item>
            <link-form-item label="营业结束时间" required v-if="acctCategoryVal === 'qdlx-4'">
                <link-date view="Hm" v-model="formOption.businessEndTime" max="23:59" min="00:00" displayFormat="HH时mm分" valueFormat="HH:mm"/>
            </link-form-item>
            <link-form-item label="订餐号码/门店电话" required v-if="acctCategoryVal === 'qdlx-4'">
                <link-number-keyboard placeholder="请输入电话号码" v-model="formOption.storePhone" />
            </link-form-item>
            <link-form-item label="客单价" required v-if="acctCategoryVal === 'qdlx-4'">
                <link-number-keyboard placeholder="请输入客单价" v-model="formOption.customerUnitPrice" :min="1" :max="999999999" />
            </link-form-item>
            <link-form-item label="菜系" required v-if="acctCategoryVal === 'qdlx-4'">
                <link-lov type="CUISINE_TYPE" v-model="formOption.cuisineCategory"/>
            </link-form-item>
            <link-form-item label="是否有非遗菜" required
                            key="isIntangibleCuisine"
                            v-if="acctCategoryVal === 'qdlx-4'">
                <link-lov type="IS_FLAG" v-model="formOption.isIntangibleCuisine"/>
            </link-form-item>
            <link-form-item label="是否为景区餐饮店" required
                            key="isScenicRestaurant"
                            v-if="acctCategoryVal === 'qdlx-4'">
                <link-lov type="IS_FLAG" v-model="formOption.isScenicRestaurant"/>
            </link-form-item>
            <link-form-item label="服务员数量" required v-if="acctCategoryVal === 'qdlx-4'">
                <link-number-keyboard placeholder="请输入服务员数量" v-model="formOption.waiterCount" :min="1" :max="999999999" />
            </link-form-item>
            <link-form-item label="是否为氛围物料店" key="isAtmosphereStore" required v-if="acctCategoryVal === 'qdlx-4'">
                <link-lov type="IS_FLAG" v-model="formOption.isAtmosphereStore"/>
            </link-form-item>
            <link-form-item label="是否餐酒项目核心门店" key="isCoreStore" :disabled="userInfo.positionType!='CityMannager'" required v-if="acctCategoryVal === 'qdlx-4'">
                <link-lov type="IS_FLAG" v-model="formOption.isCoreStore"/>
            </link-form-item>
            <link-form-item label="包间总数" required v-if="acctCategoryVal === 'qdlx-4'">
                <link-number-keyboard placeholder="请输入包间总数" v-model="formOption.roomTotalNum" :min="1" :max="999999"/>
            </link-form-item>
            <link-form-item label="大厅桌数" required v-if="acctCategoryVal === 'qdlx-4'">
                <link-number-keyboard placeholder="请输入大厅桌数" v-model="formOption.hallTotalNum" :min="1" :max="999999" />
            </link-form-item>
            <link-form-item label="店区位" required  v-show="acctTypeVal === 'Terminal'" key="xAttr50">
                <link-lov type="STORE_POSITION_TYPE" v-model="formOption.xAttr50"/>
            </link-form-item>
            <link-form-item label="店面积" required  v-show="acctTypeVal === 'Terminal'" key="area">
                <link-lov type="STORE_AREA" v-model="formOption.area"/>
            </link-form-item>
            <link-form-item label="门头店招" key="doorSigns" v-if="acctTypeVal === 'Terminal'" required>
                <link-lov type="DOOR_SIGNS" v-model="formOption.doorSigns" :excludeLovs="excludeLovDoors"/>
            </link-form-item>
            <link-form-item label="竞品类型" key="competitiveGoodsType" v-if="acctTypeVal === 'Terminal' && formOption.doorSigns==='JingPin'" required>
                <link-lov type="COMPETITIVE_GOODS_TYPE" v-model="formOption.competitiveGoodsType"/>
            </link-form-item>
            <link-form-item label="门头店招备注" key="doorSingsRemark" v-if="acctTypeVal === 'Terminal' && formOption.doorSigns === 'Other'">
                <link-input v-model="formOption.doorSingsRemark"/>
            </link-form-item>
            <link-form-item label="是否经销商自有门店" key="ownStores" required v-if="acctTypeVal === 'Terminal' || acctCategoryVal === 'Distributor' && isXinLingShou">
                <link-lov type="OWN_STORES" v-model="formOption.ownStores"/>
            </link-form-item>
            <link-form-item label="资源背景" key="resourceBack" v-if="acctCategoryVal !== 'Distributor'" :required="acctCategoryVal === 'GroupBuy' && isHuaiJiu">
                <link-input :nativeProps="{maxlength: 5}" placeholder="请输入资源背景" v-model="formOption.resourceBack"/>
            </link-form-item>
            <link-form-item label="所属圈层" key="belongingCircle" v-if="acctCategoryVal !== 'Distributor'" :required="acctCategoryVal === 'GroupBuy' && isHuaiJiu">
                <link-input :nativeProps="{maxlength: 5}" placeholder="请输入所属圈层" v-model="formOption.belongingCircle"/>
            </link-form-item>
            <link-form-item label="是否流量高地" key="trafficHighland"
                            v-if="acctTypeVal === 'Terminal' && acctCategoryVal === 'qdlx-4' && showHignlandFlag">
                <link-lov type="IS_FLAG" v-model="formOption.trafficHighland" />
            </link-form-item>
            <link-form-item label="备注" vertical>
                <link-textarea v-model="formOption.comments" :nativeProps="{maxlength: 300}" style="padding-bottom: 24px"/>
            </link-form-item>
        </link-form>
        <view class="terminal-dialog" v-model="showExplain">
            <link-dialog ref="dialog" width="80vw" v-model="showExplain">
                <view class="head-tip">
                    提示
                </view>
                <scroll-view class="content-center" :scroll-y="true">
                    <view class="word">
                        终端是指具有固定经营场所，从公司自营子公司或授权经销商（特约经销商）处进货，是泸州老窖产品与消费者面对面的展示场所，是泸州老窖产品到达消费者完成实质性消费的最终端口。
                    </view>
                    <view class="word">
                        1.社区便利店：位于小区周边小型便利商店、烟酒副食店。
                    </view>
                    <view class="word">
                        2.异业零售：类似于绿雪芽、大益茶、物业等异业零售类客户产生合作终端。
                    </view>
                    <view class="word">
                        3.封闭渠道：景区/车站/银行等产生合作终端。
                    </view>
                    <view class="word">
                        4.夜场渠道：酒吧/夜店/KTV等(供给鸡尾酒原酒等) 产生合作终端。
                    </view>
                    <view class="word">
                        5.大卖场：全国连锁型卖场（含国际型卖场），如：大润发、麦德龙、沃尔玛等。
                    </view>
                    <view class="word">
                        6.连锁标超：区域性连锁超市，如：红旗连锁等。
                    </view>
                    <view class="word">
                        7.连锁便利：连锁便利店有别于社区便利店，如：罗森、711等。
                    </view>
                    <view class="word">
                        8.特通专卖连锁：特通渠道开设的线下连锁型门店，如：烟草20支等。
                    </view>
                    <view class="word">
                        9.酒类专卖连锁：统一采购、统一结算及配送的线上线下联动新型零售专卖店，如:酒仙名酒城、酒便利、1919、京东酒世界等。
                    </view>
                </scroll-view>
                <link-button slot="foot" @tap="$refs.dialog.hide()">确定</link-button>
            </link-dialog>
        </view>
        <link-dialog ref="positionBottom"
                     :noPadding="true"
                     v-model="dialogFlag"
                     position="bottom"
                     borderRadius="32rpx 32rpx 0 0">
            <scroll-view scroll-y="true" :style="{'height': selectBoxHeight + 'px','margin-bottom': 73+ 'px'}">
                <view class="terminal-list-cell" v-for="(data, index) in allTerminalList" :key="index" @tap="ontapCheckBox(data)">
                    <view class="media-list">
                        <view class="left" >
                            <link-radio-group v-model="selectTerminalId">
                                <item :arrow="false">
                                    <link-checkbox :val=data.id slot="thumb" toggleOnClickItem @tap="ontapCheckBox(data)"/>
                                </item>
                            </link-radio-group>
                        </view>
                        <image class="media-list-logo" :src="data.storeUrl" @tap.stop="previewStoreUrl(data)"  lazy-load="true"></image>
                        <view class="store-content">
                            <view class="store-content-top" v-if="data.acctType">
                                <!--【客户一级分类】为“终端Terminal”的时候显示storeSigns字段-->
                                <view class="store-title" v-if="data.acctType === 'Terminal'">{{data.acctName}}</view>
                                <!--【客户一级分类】为“分销商Distributor”时展示billTitle字段-->
                                <view class="store-title" v-if="data.acctType === 'Distributor'">{{data.billTitle}}
                                </view>
                            </view>
                            <view class="store-content-representative">
                                <view class="terminal-type">编码</view>
                                <view class="terminal-name">{{data.acctCode}}</view>
                            </view>
                            <!--                                  pageParam.source === 'advanceOrder' &&-->
                            <view class="store-content-representative"
                                  v-if="!$utils.isEmpty(data.creditNo)">
                                <view class="terminal-type">税号</view>
                                <view class="terminal-name">{{data.creditNo}}</view>
                            </view>
                            <view class="store-content-representative">
                                <view class="terminal-type">业代</view>
                                <view class="terminal-name">{{data.salesManListString}}</view>
                            </view>
                            <view class="store-content-address">
                                <view class="distance">{{data.distance+ '米'}}</view>
                                <view class="gap-line"></view>
                                <view class="store-address">
                                    {{data.province}}{{data.city}}{{data.district}}{{data.address}}
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </scroll-view>
            <link-sticky class="bottom-btn">
                <link-button class="sure-btn" size="normal" mode="stroke" @tap="dialogFlag = false">取消</link-button>
                <link-button class="sure-btn" size="normal" mode="fill" @tap="confirmTeminal" :shadow="shadow">选择此终端</link-button>
            </link-sticky>
        </link-dialog>
        <link-dialog ref="dialog">
            <view slot="head">
                提示
            </view>
            <view>
                请确认该终端是您当前要新建的终端
            </view>
            <link-button slot="foot" @tap="$refs.dialog.hide()">取消</link-button>
            <link-button slot="foot" @tap="gotoTerminalDetail('提交')">确定</link-button>
        </link-dialog>
        <view class="blank"></view>
    </link-page>
</template>

<script>
    import {ComponentUtils} from "link-taro-component";
    import LnkImgWatermark from '../../../core/lnk-img-watermark/lnk-img-watermark';
    import Taro from "@tarojs/taro";
    export default {
        name: "new-client-basics",
        data () {
            const assessmentAccnt = new this.AutoList(this, {
                module: 'action/link/accnt',
                sortOptions: null,
                title: '请选择考核客户',
                param: {
                    oauth: 'MULTI_POSTN',
                    sort: 'created',
                    order: 'desc',
                    brandComOrgType: 'BranchCompany',
                    filtersRaw: [
                        {"id":"acctType","property":"acctType","value":"Terminal","operator":"="},
                        {"id":"dataSource","property":"dataSource","value":"WeChatWork","operator":"="},
                        // {"id":"brandComOrgType","property":"brandComOrgType","value":"BranchCompany","operator":"="},
                        {"id":"multiAcctMainFlag","property":"multiAcctMainFlag","value":"Y","operator":"="},
                        {"id":"acctStatus","property":"acctStatus","value":"Y","operator":"="},
                        {"property":"fstName","operator":"LIKE","value":Taro.getStorageSync('token').result.firstName,"id":"singleFiltersRaw0"}
                    ],
                },
                searchFields:['acctName']
            });
            const superOption = new this.AutoList(this, {                       // 品牌
                module: 'action/link/superSystem',
                param: {
                    filtersRaw: [{"id": "isEffective", "property": "isEffective", "value": "Y"}],
                },
                sortOptions: null,
                searchFields: ['superName', 'id'],
                filterOption: null,
            });
            const chainStoreData = new this.AutoList(this, {                       // 品牌
                module: 'action/link/accnt',
                url: {
                    queryByExamplePage :'export/link/accnt/queryChainStoreList',
                },
                param: {
                    filtersRaw: [],
                    mdmCompanyCode: Number(this.formOption.mdmCompanyCode),
                    acctType: 'Terminal',
                    oauth: 'ALL'
                },
                queryFields: 'id,acctName,chainHeadStoreFlag,acctType,mdmCompanyCode,acctStatus',
                searchFields:['acctName'],
                hooks: {
                    beforeLoad(option) {
                        option.param.sort = '';
                        option.param.order = '';
                        option.param.filtersRaw.push(
                            {id:'chainHeadStoreFlag0', property:"chainHeadStoreFlag", value:"Y", operator:"="},
                            {id:'chainHeadStoreFlag1', property:"chainHeadStoreFlag", value:"", operator:"IS NULL"},
                            {id:"acctStatus", property:"acctStatus", value:"Y", operator:"="},
                            {id:"id", property:"id", value: this.formOption.id, operator:"<>"},
                            {id:"acctType", property:"acctType", value:"Terminal", operator:"="},
                            {id:"acctCategory", property:"acctCategory", value: this.acctCategoryVal, operator:"="}
                        )
                        option.param.filterOperator="(chainHeadStoreFlag0 or chainHeadStoreFlag1) and acctStatus and acctType and acctCategory and id"
                        let arr = option.param.filtersRaw
                        for(let i=0;i<arr.length;i++){
                            if(arr[i].id.includes('searchValue')){
                                arr[i].id='searchValue0'
                                option.param.filterOperator="(chainHeadStoreFlag0 or chainHeadStoreFlag1) and acctStatus and acctType and acctCategory and searchValue0 and id"
                            }
                        }
                    }
                }
            });
            Promise.all([
                this.$lov.getLovByType('ACCT_TYPE'),
                this.$lov.getLovByType('ACCNT_CATEGORY'),
                this.$lov.getLovByType('SUB_ACCT_TYPE'),
            ]).then(([ACCT_TYPE, ACCNT_CATEGORY, SUB_ACCT_TYPE]) => {
                this.acctCascadeData = {
                    ACCT_TYPE: ACCT_TYPE.filter(item => item.val === 'Terminal' || item.val === 'Distributor'),
                    ACCNT_CATEGORY,
                    SUB_ACCT_TYPE
                };
            });
            return {
                inputReadOnlyAcct: false, // 门头分销商团购字符输入限制标志
                inputReadOnlySimName: false, // 简称字符输入限制标志
                showExplain: false,
                imagShop:'', //形象店
                excludeLovDoors: ['Luzhoulaojiao1573','Luzhoulaojiao1573','Touqu','Jiaoling','Tequ','Touqu60ver'],
                prodPartComCode: '',
                daChengComCode: ['5161', '5902', '5903'], // "大成浓香"、"永粮"、"鸿泸"
                showHignlandFlag: false,
                targetAccessAcct: {
                    accessAcctName: '',
                    accessAcctId: ''
                },
                assessmentAccnt,
                superOption,
                delFlag: true,                                                                                      // 删除标志
                moduleType: 'agrSign',                                                                              // 模块类型
                moduleName: '门头照片',                                                                              // 模块名称
                continueFlag: false,                                                                                 // 继续拍照按钮
                acctName: '',                                                                                       // 客户一级分类菜单选中的值
                categoryName: '',                                                                                   // 客户二级分类菜单选中的值
                acctTypeVal: '',                                                                                    // 客户二级分类菜单选中的独立源代码
                acctCategoryVal: '',                                                                                // 客户二级分类菜单选中的独立源代码
                subAcctTypeVal: '',  
                defaultStoreName: '', 
                acctCascadeData: null,                                                                              // 客户分类级联选择数据
                chainStoreData,
                formRules: {
                    // 自定义异步校验
                    acctName: this.asyncValidator,
                },
                userInfo: {},
                dialogFlag: false,
                allTerminalList: [],//终端列表数组
                selectBoxHeight: 0,
                selectTerminalId: null,//选中终端id
                selectedTerminalObj:{},//选中终端对象
                shadow: true,
                showOrg: true // 是否显示考核组织
            }
        },
        components: {LnkImgWatermark},
        props: {
            claimFlag:{
                type: Boolean,
                default: false
            },
            listOauth: {
                type: String,
                default: ''
            },
            isJiaoLing: {
                type: Boolean,
                default: false
            },
            isGuoJiao: {
                type: Boolean,
                default: false
            },
            isChuanDong: {
                type: Boolean,
                default: false
            },
            isXinLingShou: {
                type: Boolean,
                default: false
            },
            isChuanDongOrXingJi: {
                type: Boolean,
                default: false
            },
            isNewShiJiaZhuang: {
                type: Boolean,
                default: false
            },
            isXingXiangDian: {
                type: Boolean,
                default: false
            },
            isHuaiJiu: {
                type: Boolean,
                default: false
            },
            formOption: {
                type: Object,
                default: {},
                required: true
            },
            storeHeadLength: {
                type: Number
            },
			// 产品列表
			sellProductArr:{
				type: Array,
				default: () => [],
			},
            coordinate:{
                type: Object,
                default: {},
            },
            editFlag:{
                type: String,
                default: '',
            },
            broadCompanyCode: { // 博大公司编码集
                type: String,
                default: '',
            }
        },
        computed: {
            partnerDisabled() {
                const arr = ['underReview', 'approved', 'underReview2', 'approved2', 'failed2']
                return arr.includes(this.formOption.auditStatus) || this.formOption.accntPartner === 'stopCooperation'
            },
            requiredFlag() {
                if(!this.isChuanDongOrXingJi) return true
                if(this.formOption.accntPartner === 'cooperation') return true
                return false
            },
            disabledFlag() {
                if(!this.isChuanDongOrXingJi) return false
                if(this.formOption.accntPartner === 'stopCooperation') return true
                return false
            },
            allcustomerType() {
                return this.formOption.acctType + this.formOption.acctCategory + this.formOption.subAcctType
            },
        },
        watch: {
            'formOption.accessAcctName' (val) {
                if(val) {
                    this.targetAccessAcct.accessAcctName = this.formOption.accessAcctName
                    this.targetAccessAcct.accessAcctId = this.formOption.accessAcctId
                }
            },
            'formOption.acctName' (val) {
                if (val.length >= 50) {
                    this.inputReadOnlyAcct = true
                } else {
                    this.inputReadOnlyAcct = false
                }
            },
            'formOption.simName' (val) {
                if (val.length >= 100) {
                    this.inputReadOnlySimName = true
                } else {
                    this.inputReadOnlySimName = false
                }
            },
            'targetAccessAcct.accessAcctId' (val) {
                this.formOption.accessAcctId = val
            },
            allcustomerType () {
                this.$emit('checkType')
            },
            /**
              * 门头照长度
              * <AUTHOR>
              * @date 2020-09-17
              * @param val 门头照长度参数
            */
            storeHeadLength (val) {
                if (val) {
                    this.storeHeadLength = val;
                }
            },
            /**
              * 监控formOption
              * <AUTHOR>
              * @date 2020-09-14
              * @param val
            */
            'formOption.mdmCompanyCode' (val) {
                console.log('change', this.formOption);
                if (val) {
                    this.chainStoreData.option.param.mdmCompanyCode = val;
                    this.chainStoreData.methods.reload();
                }
            },
            /**
              * 监控客户一级分类获取值
              * <AUTHOR>
              * @date 2020-09-11
              * @param val
            */
            async acctName(val) {
                // 根据客户一级分类显示值获取独立源代码
                this.acctTypeVal = await this.$lov.getValByTypeAndName('ACCT_TYPE', val);
            },
            /**
              * 监控客户二级分类获取值
              * <AUTHOR>
              * @date 2020-09-11
              * @param val
            */
            async categoryName(val) {
                // 根据客户二级级分类显示值获取独立源代码
                const acctTypeVal = await this.$lov.getValByTypeAndName('ACCT_TYPE', this.formOption.acctType);
                if(!this.acctTypeVal)
                    this.acctCategoryVal = await this.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', val, 'ACCT_TYPE', acctTypeVal);
                else
                    this.acctCategoryVal = await this.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', val, 'ACCT_TYPE', this.acctTypeVal);
            },
            /**
             * 监控客户小类的值,修改【是否为品鉴基地】的值
             * <AUTHOR>
             * @date 2020-11-07
             * @param val
             */
            async 'formOption.subAcctType'(val) {
                this.subAcctTypeVal = await this.$lov.getValByTypeNameParentTypeParentVal('SUB_ACCT_TYPE', val, 'ACCNT_CATEGORY', this.acctCategoryVal);
            },
            /**
             * 监听【客户中类】值，修改通过它控制的字段的值
             * <AUTHOR>
             * @date 6/21/21
             * @param val 变更后新值
             */
            'formOption.acctCategory'(val) {
                // 非KA类型
                if (val !== 'qdlx-2' && val !== '商超' && !(this.acctTypeVal === 'Terminal'&& this.isXinLingShou)) {
                    // 清空KA店号
                    this.formOption.xAttr71 = '';
                    // 门店所属供货渠道
                    this.formOption.xAttr74 = '';
                }
                // 非餐饮类型
                if (val !== 'qdlx-4' && val !== '餐饮') {
                    // 清空 是否为品鉴基地
                    this.formOption.judgmentFlag = 'N';
                    // 清空 是否为活动场地
                    this.formOption.appreciationFlag = 'N';
                    // 清空 形象包间数
                    this.formOption.imageRoomNum = '';
                    // 清空 包间总数
                    this.formOption.roomTotalNum = '';
                    // 清空 大厅总数
                    this.formOption.hallTotalNum = '';
                }
            }
        },
        async created () {
            this.userInfo = Taro.getStorageSync('token').result;
            this.showHignlandFlag = this.daChengComCode.includes(this.formOption.mdmCompanyCode)
            const res = await this.$utils.getCfgProperty('IS_CHECK_ORGANIZATION');
            // 所属公司品牌编码
            const brandCompanyCode = this.userInfo && this.userInfo.coreOrganizationTile.brandCompanyCode;
            // 当前登录用户是否属于公司品牌编码
            const isBrandCompanyCode = res && res.length && res.includes(brandCompanyCode);

            // 考核组织展示条件
            this.showOrg = res.includes(this.formOption.mdmCompanyCode) && (!this.userInfo.coreOrganizationTile.l3Code) || isBrandCompanyCode;
            const chuanDongOrgId = await this.$utils.getCfgProperty('East_Sichuan_Tequ_Area_ID') || '521074619762290688'
            if(this.formOption.salesmanAreaId == chuanDongOrgId){
                this.$emit('changeStatusVal',true)
            }
            this.prodPartComCode = await this.$utils.getCfgProperty('PROD_PART_BRANCH_COM');
            this.targetAccessAcct.accessAcctName = this.formOption.accessAcctName;
            this.targetAccessAcct.accessAcctId = this.formOption.accessAcctId;
            this.defaultStoreName= this.formOption.chainHeadStoreName;
            if(this.userInfo.positionType!='CityMannager'){
                this.formOption.isCoreStore = this.formOption.isCoreStore||'N';
            }
        },
        methods: {
            /**
              * 连锁总店初始赋值逻辑调整
              * <AUTHOR>
              * @date 2025-05-07
              * @param param
            */
            changeSwitch(val){
                if(val === 'Y' && !this.formOption.chainHeadStoreFlag){
                    this.$set(this.formOption, 'chainHeadStoreFlag', 'N')
                }
            },
            /**
              * 分销商门头名称
              * <AUTHOR>
              * @date 2020-11-09
              * @param param
              * @date 240314 by lld 0328迭代取消分销商名称格式校验
            */
            // distributorValidator (param) {
            //     let reg = /^[A-Za-z0-9\u4e00-\u9fa5]+$/;
            //     let test = reg.test(param);
            //     if (!test) {
            //         return '请检查分销商名称输入格式是否正确';
            //     }
            // },
            /**
              * 分销商门头名称
              * <AUTHOR>
              * @date 2020-11-09
              * @param param
              * @date 240314 by lld 0328迭代取消门头名称格式校验
            */
            // acctNameValidator (param) {
            //     let reg = /^[A-Za-z0-9\u4e00-\u9fa5]+$/;
            //     let test = reg.test(param);
            //     if (!test) {
            //         return this.acctCategoryVal === 'qdlx-2' ? '请检查终端名称输入格式是否正确' : '请检查门头名称输入格式是否正确';
            //     }
            // },
            /**
             * 门头简称校验
             * <AUTHOR>
             * @date 2020-11-09
             * @param param
             * @date 240314 by lld 0328迭代取消客户简称格式校验
             */
            // simNameValidator (param) {
            //     let reg = /^[A-Za-z0-9\u4e00-\u9fa5]+$/;
            //     let test = reg.test(param);
            //     if (!test) {
            //         return '请检查客户简称输入格式是否正确';
            //     }
            // },
            /**
              * 详细地址广播事件
              * <AUTHOR>
              * @date 2020-11-03
            */
            addressDetail (addr) {
                if(addr === 'address' && this.acctCategoryVal !== 'GroupBuy'){
                    if (this.$utils.isEmpty(this.formOption.address.trim())) {
                        this.$message['warn']('门店地址详细地址不能为空');
                        return;
                    }
                    this.$emit('addressModify', {val: this.formOption.address, flag: true})
                }
                if(addr === 'deliveryDetailAddr'){
                    if (this.$utils.isEmpty(this.formOption.deliveryDetailAddr.trim())) {
                        this.$message['warn']('收货地址详细地址不能为空');
                        return;
                    }
                    this.$emit('addressModify', {val: this.formOption.deliveryDetailAddr, flag: false})
                }
            },
           
            // async changeSwitch(val){
            //     const data = await this.$http.post('action/link/accnt/validateChainHeadStoreUnbind',{id:this.formOption.id},{
            //         autoHandleError: false,
            //         handleFailed: (response) => {
            //             this.$dialog({
            //                 title: '提示',
            //                 content: response.result,
            //                 cancelButton: false,
            //             })
            //         }
            //     });
              
            // },
           
            /**
              * 获取定位
              * <AUTHOR>
              * @date 2020-09-24
              * @param param
            */
            getLocation (isDeliverFlag) {
                if(this.claimFlag){ //终端编辑不可以编辑定位
                    return;
                }
                this.$emit('getLocation', {isDeliverFlag: isDeliverFlag})
            },
            /**
              * 跳转连锁店选择列表
              * <AUTHOR>
              * @date 2025-0506
              * @param value
            */
            goToStoreList(data){
                this.$nav.push('/pages/terminal2/new-construction/storeName-list-page', {...data,acctCategory:this.acctCategoryVal})
            },
            /**
              * 校验门头名称
              * <AUTHOR>
              * @date 2020-09-14
              * @param value
            */
            async asyncValidator(value) {
                if (value === '' || value.trim() === '') {
                    return '不能为空！'
                } else {
                    if(this.editFlag === 'edit')return;
                    this.onBlurStoreName()
                }
            },
            /**
             * 相机拍照不能跳转的提示函数
             * <AUTHOR>
             * @date 2020-09-01
             */
            notGoCamera () {
                if (!this.distanceSelect) {
                    this.$message['warn']('当前位置不在有效打卡范围内，请确认此异常');
                }
            },
            /**
             * 获取打卡图片长度
             * <AUTHOR>
             * @date 2020-08-31
             * @param param 打卡图片长度
             */
            imageArrLength (param) {
                this.$emit('storeHeadArr', param);
            },
            deleteImageArrLength(param){
                this.$emit('storeHeadArr', param);
            },
            /**
              * 客户类型三级联动
              * <AUTHOR>
              * @date 2020-09-23
              * @param tag
              * @param parentValue
            */
            customOption(tag, parentValue) {
                if (!this.acctCascadeData) {
                    return []
                }
                switch (tag) {
                    case 'p':
                        if (this.acctTypeVal === 'Terminal' && this.editFlag === 'edit') {
                           let acctType = this.acctCascadeData.ACCT_TYPE.filter(item => item.val === 'Terminal');
                            return acctType.map(lov => lov.name)
                        }
                        if (this.acctTypeVal === 'Distributor' && this.editFlag === 'edit') {
                            let acctType = this.acctCascadeData.ACCT_TYPE.filter(item => item.val === 'Distributor');
                            return acctType.map(lov => lov.name)
                        }
                        return this.acctCascadeData.ACCT_TYPE.map(lov => lov.name);
                    case 'c':
                        this.acctName = parentValue;
                        const cArr = this.acctCascadeData.ACCNT_CATEGORY.filter(item => item.parentName === parentValue).map(lov => lov.name);
                        if(this.isXingXiangDian) return cArr
                        //新零售
                        if(this.isXinLingShou) return cArr.filter(item => !['流通','商超','餐饮','异业','形象店'].includes(item))
                        return cArr.filter(item => item !== '形象店')
                    case 'd':
                        this.categoryName = parentValue;
                        //新零售特通
                        const cArrL = this.acctCascadeData.SUB_ACCT_TYPE.filter(item => item.parentName === parentValue).map(lov => lov.name);
                        if(this.isXinLingShou) return cArrL.filter(item => !['封闭渠道','夜场渠道'].includes(item))
                        return this.acctCascadeData.SUB_ACCT_TYPE.filter(item => item.parentName === parentValue).map(lov => lov.name);
                }
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/10/21
             * @description 门店名称校验
             */
             onBlurStoreName: ComponentUtils.debounce(async function() {
                const acctCategory = await this.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', this.formOption.acctCategory, 'ACCT_TYPE', 'Terminal');
                if(acctCategory === 'qdlx-2'){
                    return
                }
                this.formOption.acctName = this.formOption.acctName.trim();
                const name = this.formOption.acctName;
                this.userInfo = Taro.getStorageSync('token').result;
                if(this.$utils.isNotEmpty(name) && this.$utils.isNotEmpty(this.coordinate.latitude) && this.$utils.isNotEmpty(this.coordinate.longitude) && this.$utils.isNotEmpty(this.userInfo.coreOrganizationTile.brandCompanyCode)){
                    const isPass = await this.checkData(name);
                    if(!isPass){
                        this.dialogFlag = true;
                    }
                }
            },1000),
            /**
             * @createdBy  张丽娟
             * @date  2020/10/21
             * @description 校验数据
             */
            async checkData(acctName){
               /*1、触发条件【门头名称】与【经度】【纬度】均有值
                2、查询判断是否存在
                    ①【品牌公司代码】与当前用户的一致的
                    ②【组织片区】salesmanArea与当前用户一致的
                    ③【门头名称】相同的（模糊查询）
                      ④与当前经纬度相比≤200米的
                    ⑤【是否为主户头】为“Y”的客户列表
                若不存在：则通过校验
                若存在：显示查询出的终端列表*/
                const param = {
                    acctName: acctName, // 门头名称
                    mdmCompanyCode: this.userInfo.coreOrganizationTile.brandCompanyCode, // 登录用户品牌公司代码
                    latitude: this.coordinate.latitude, // 定位纬度
                    longitude: this.coordinate.longitude, // 定位经度
                }
                const data = await this.$http.post('action/link/accnt/queryStoreSignLikeList',param);
                if (!data.success) {
                    this.$utils.showAlert('查询配置表出错！', {icon: 'none'});
                    return true
                }
                if(data.rows.length > 0){
                    data.rows.length < 2 ? this.selectBoxHeight = 144  * data.rows.length + 57: this.selectBoxHeight = 144  * 2 + 57
                    this.allTerminalList = data.rows;
                    this.allTerminalList.forEach(async (item) => {
                        let firstNameString = '';
                        if(item.salesManList && item.salesManList.length > 0){
                            item.salesManList.forEach((zitem,zindex)=>{
                                if(zindex === item.salesManList.length - 1){
                                    firstNameString += zitem.firstName
                                }else{
                                    firstNameString += zitem.firstName + ';'
                                }
                            })
                            this.$set(item, 'firstNameString', firstNameString);
                        }
                        if (!this.$utils.isEmpty(item.storePicPreKey)) {
                            let urlData = this.$image.getSignedUrl(item.storePicPreKey);
                            this.$set(item, 'storeUrl', urlData);
                        } else {
                            this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
                        }
                    })
                    return false
                }
                return true
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/18
             * @description 确认终端
             */
            confirmTeminal(){
                if(this.$utils.isEmpty(this.selectedTerminalObj.id)){
                    this.$utils.showAlert('请先选择终端！', {icon: 'none'});
                    return
                }
                this.$refs.dialog.show()
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/10/22
             * @description 跳转到终端详情
             */
            async gotoTerminalDetail(){
                const param = [
                    {
                        "mvgSubsetId": this.userInfo.postnId, // 职位id
                        "mvgParentId": this.selectedTerminalObj.id, // 客户id
                        "row_status": "NEW"
                    },
                    ]
                //校验 大成浓香"、"永粮"、"鸿泸
                if (this.prodPartComCode.indexOf(this.selectedTerminalObj.salesmanBrandComCode) > -1) {
                    const rep = await this.$http.post('/action/link/accntProdPart/queryUserProdPartPage',{
                        acctId:this.selectedTerminalObj.id,
                        postnIds:[this.userInfo.postnId],
                        keepOriFlag: 'Y'
                    });
                    if(!rep.success){
                        this.$showError(rep.result);
                        return
                    }else{
                        const param={
                            mvgList:[{defField: 'isDefault',mvgMapperName: 'accntPostn', mvgParentId: this.selectedTerminalObj.id, mvgSubsetId: this.userInfo.postnId, row_status: 'NEW'}],
                            postnIds:[this.userInfo.postnId],
                            prodPartList:[{acctId:this.selectedTerminalObj.id, postnId:this.userInfo.postnId, prodPartCode:rep.rows[0].prodPartCode, userId:rep.rows[0].userId, mainFlag: 'N'}],
                            source: 'MP'
                        }
                        const rsp = await this.$http.post('action/link/accntProdPart/operatePostnAndProdPart',param);
                        if(!rsp.success){
                            this.$showError(rep.result);
                            return
                        }
                    }
                }else{
                    const data = await this.$http.post('action/link/accnt/batchOperateAcctPostn',param);
                    if (!data.success) {
                        this.$utils.showAlert('添加职位出错！', {icon: 'none'});
                        return true
                    }
                }
                this.$nav.redirect('/pages/terminal/terminal/client-details-page',{data: this.selectedTerminalObj, listOauth: this.listOauth})
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/10/22
             * @description 图片预览
             */
            async previewStoreUrl(param) {
                if (!this.$utils.isEmpty(param.storePicKey)) {
                    let imgUrl = this.$image.getSignedUrl(param.storePicKey);
                    const inOptions = {
                        current: imgUrl,
                        urls: [imgUrl]
                    };
                    this.$image.previewImages(inOptions)
                } else {
                    console.log(param.storeUrl)
                    const inOptions = {
                        current: param.storeUrl,
                        urls: [param.storeUrl]
                    };
                    this.$image.previewImages(inOptions)
                }
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/10/22
             * @description 选中终端后
             */
            ontapCheckBox(data){
                this.selectTerminalId = data.id;
                this.selectedTerminalObj = data;
            },
            /**
             * 【是否为品鉴基地】值发生改变主动处理
             * <AUTHOR>
             * @date 6/10/21
             * @param val 新值
             */
            judgmentFlagChangeHandler(val) {
                this.imagShop = val
                // 存在【是否为活动场地】， 则跟随【是否为品鉴基地】赋值
                if (Boolean(this.formOption.appreciationFlag)) {
                    this.$set(this.formOption, 'appreciationFlag', val);
                }
            },
        }
    }
</script>

<style lang="scss">
    .new-client-basics {
        .terminal-dialog{
            .link-dialog-body{
                display: flex;
                flex-direction: column!important;
                justify-content: normal!important;
                padding: 0;
                align-items: flex-start !important;
            }
            .head-tip {
                font-family: PingFangSC-Regular, serif;
                font-size: 32px;
                color: #333333;
                letter-spacing: 0;
                text-align: center;
                line-height: 32px;
                padding: 30px 0px;
                border-bottom: 2px solid #e5e5e5;
                width: 100%;
            }
            .content-top{
                font-family: PingFangSC-Regular,serif;
                font-size: 28px;
                color: #595959;
                letter-spacing: 0;
                line-height: 28px;
                padding: 31px 24px;
                text-align: left;
            }
            .content-center {
                width: 100%;
                max-height: 50vh;
                overflow: scroll;
                .word{
                    font-family: PingFangSC-Regular,serif;
                    font-size: 28px;
                    color: #595959;
                    letter-spacing: 0;
                    text-align: left;
                    padding: 25px 24px;
                    line-height: 40px;
                }
            }
        }
        .select-box {
            display: flex;
            justify-content: space-between;
        }
        .model-title {
            display: flex;
            margin-left: 24px;
            .title {
                font-family: PingFangSC-Regular,serif;
                font-size: 32px;
                color: #262626;
                letter-spacing: 0;
                text-align: center;
                line-height: 96px;
                height: 96px;
                width: 90%;
            }
            .icon-close {
                color: #BFBFBF;
                font-size: 48px;
                line-height: 96px;
                height: 96px;
            }
        }
        .terminal-list-cell {
            position: relative;
            .left{
                display: flex;
                justify-items: center;
            }
            .link-radio-group {
                display: flex;
                .link-item {
                    padding: 24px 24px 24px 0;

                    .link-item-thumb {
                        padding-right: 0;
                        .link-icon{
                            font-size: 50px;
                        }
                    }

                    .link-item-icon {
                        display: none;
                    }
                }

                .link-item-active {
                    background-color: #fff;
                }
            }
            &:after {
                position: absolute;
                bottom: 0;
                left: 24px;
                right: 0;
                height: 2px;
                background-color: $ibc;
                transform: scaleY(0.5);
                content: '';
            }
            .media-list {
                @include flex;
                padding: 32px 32px 32px 24px;
                .media-list-logo {
                    /*box-shadow: 0 4px 31px 0 rgba(0,44,152,0.22);*/
                    border-radius: 16px;
                    width: 128px;
                    height: 128px;
                    overflow: hidden;
                }
                .store-content {
                    flex: 1;
                    .store-content-top {
                        @include flex-start-center;
                        @include space-between;
                        margin-left: 24px;
                        .store-title {
                            font-family: PingFangSC-Semibold,serif;
                            font-size: 32px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 32px;
                        }
                        .store-level {
                            margin-right: -3px;
                            width: 120px;
                            height: 44px;
                            image {
                                width: 100%;
                                height: 100%;
                            }
                        }
                    }
                    .store-content-middle {
                        @include flex-start-center;
                        margin-top: 24px;
                        margin-left: 24px;
                        .store-type {
                            white-space: nowrap;
                            border: 1px solid #2F69F8;
                            border-radius: 8px;
                            font-size: 20px;
                            padding-left: 18px;
                            padding-right: 18px;
                            line-height: 40px;
                            height: 40px;
                            color: #2F69F8;
                            margin-right: 10px;
                        }
                    }
                    .store-content-representative {
                        @include flex;
                        margin-left: 24px;
                        margin-top: 24px;
                        line-height: 24px;
                        .terminal-type {
                            color: #8C8C8C;
                            width: 62px;
                        }
                        .terminal-name {
                            font-family: PingFangSC-Regular,serif;
                            font-size: 24px;
                            color: #000000;
                            letter-spacing: 0;
                            padding-left: 8px;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            flex: 1;
                            width: 0;
                        }
                    }
                    .store-content-address {
                        display: flex;
                        margin-left: 24px;
                        margin-top: 24px;
                        font-family: PingFangSC-Regular,serif;
                        font-size: 24px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 30px;
                        .store-address{
                            flex: 1;
                        }
                        .gap-line{
                            width: 2px;
                            height: 24px;
                            margin: 0 16px;
                            background: #eee;
                        }
                    }
                }
            }
        }
        .bottom-btn{
            .sure-btn {
                width: 340px;
                height: 96px;
                margin-right: 24px;
            }
        }
        background: #F2F2F2;
        /*deep*/.list-title {
                    padding: 12px;
                }
        /*deep*/.link-icon {
                    font-size: 28px;
                }
        /*deep*/.link-input-text-align-left {
                    text-align: right;
                }
        /*deep*/.link-item {
                    padding: 24px;
                }
        /*deep*/.icon-location {
                    font-size: 32px;
                    color: #2F69F8;
                }
        /*deep*/.link-textarea-dialog {
                    margin-top: 70px!important;
                }
        /*deep*/.link-textarea-content {
                    border: 1px solid #ffffff;
                    font-size: 28px;
                    padding: 0 0 24px 0;
                }
        .address-color {
            color: #333333;
            width: 100%;
            text-align: right;
        }
        .address-placeholder {
            color: #E0E0E0;
            font-size: 28px;
            width: 100%;
            text-align: right;
        }
        .icon-shop {
            font-size: 32px;
            color: #2F69F8;
        }
        .door-head {
            font-size: 28px;
            color: #333333;
            padding-left: 24px;
            padding-top: 20px;
            background: #ffffff;
            .asterisk {
                color: #FF5A5A;
                margin-left: -14px;
                padding-right: 2px;

            }
        }
        .blank {
            height: 204px;
            width: 100%;
        }

        .deliver-address{
            display: flex;
            align-items: center;

            .icon-close-circle{
                padding:0 8px 0 12px;
                font-size: 30px;
            }
        }
    }
    .orgCardStyle {
        background-color: red !important;
        /*deep*/
        .link-item-icon {
            display: none;
        }
    }
</style>
<style lang="scss" scoped>
.orgCardStyle {
    background-color: green !important;
    /*deep*/
    .link-item-icon {
        display: none;
    }
}
</style>
