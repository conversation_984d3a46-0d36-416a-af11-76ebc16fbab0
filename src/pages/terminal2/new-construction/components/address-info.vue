<template>
    <link-page class="address-info">
        <view class="address-container" v-for="(item, index) in addressList" :key="index">
            <link-form ref="form" :value="item" :rules="formRules" >
                <link-form-item v-if="addressList.length > 1">
                    <view slot="title" class="title-container">
                        <view class="contacts-text" @tap="unfoldShrink(index)">
                            收货地址 {{index+1}}
                            <link-icon v-if="item.unfoldFlag" icon="icon-shouqi"/>
                            <link-icon v-else icon="icon-zhankai"/>
                        </view>
                        <view class="delete-btn" @tap="deleteAddress(index)">删除</view>
                    </view>
                </link-form-item>
                <view v-if="item.unfoldFlag">
                    <link-form-item label="收货人" required>
                        <link-input placeholder="请填写收货人姓名" v-model="item.consignee"/>
                    </link-form-item>
                    <link-form-item label="联系方式" field="mobilePhone" required>
                        <link-input placeholder="请输入手机号码" v-model="item.mobilePhone"/>
                    </link-form-item>
                    <!-- edit by 谭少奇 调整地址选择为地图选点 -->
                    <link-form-item required @tap="getLocation('newArea', index)">
                        <view slot="title">
                            <text>所在地区</text>
                            <link-icon icon="icon-location" class="link-location"/>
                        </view>
                        <link-input placeholder="请选择所在地区" v-model="item.regionInfo"/>
                    </link-form-item>
                    <link-form-item label="详细地址" v-show="item.addr" vertical  required>
                        <link-textarea placeholder="请填写详细地址" padding-start padding-end v-model="item.addr" mode='textarea'></link-textarea>
                    </link-form-item>
                    <!-- <link-form-item label="是否有效" required>
                        <link-switch v-model="item.isEffective"/>
                    </link-form-item> -->
                </view>

            </link-form>
        </view>
        <view class="add-address" @tap="addAddress">
            <text class="iconfont icon-plus"></text>
            <text class="text">添加收货地址</text>
        </view>
    </link-page>
</template>

<script>
    export default {
        name: "address-info",
        data () {
            return {
                count: 0,
                shadow: true,
                formRules: {
                    // 电话号码校验
                    mobilePhone: this.Validator.phone()
                },
                addressList: [
                    {
                        consignee: '',                 // 收货人
                        mobilePhone: '',               // 收货人电话
                        addrType: 'ShipAddr',          // 地址类型——收货地址
                        addr: '',                      // 详细地址
                        regionInfo: '',                // 区域地址
                        province: '',                  // 省
                        city: '',                      // 市
                        district: '',                  // 区
                        row_status: 'NEW',             // 状态
                        acctId: 'NEW',                 // 客户id
                        townName:'',                   //乡镇
                        town:'',
                        unfoldFlag: true               // 新建终端时地址填写默认展开
                    },
                ]
            }
        },
        props:{
            basicOption: {
                type: Object
            }
        },
        watch: {
            async 'basicOption.acctCategory'(val) {
                if (val) {
                    this.basicOption.acctCategory = val
                }
                if (this.count === 0) { //地址赋值
                    if(!this.$utils.isEmpty(this.basicOption.addrList) && this.basicOption.addrList.length>0){
                        this.addressList = this.$utils.deepcopy(this.basicOption.addrList); //地址
                    }
                    this.addressList.forEach(item => {
                        if (!item.townName) item.townName = ''
                        let {province,city,district,townName} = item
                        this.$set(item, 'unfoldFlag', true)
                        if(province && city && district && townName){
                            this.$set(item,'regionInfo',`${province}/${city}/${district}/${townName}`)
                        }
                    })

                    this.count += 1;
                }
            },
        },
        async created () {
            this.$locations.QQClearLocation();  //清空地理位置

        },
        methods: {
            /**
             * @desc 选择收货地址
             * <AUTHOR>
             * @date 2023-10-08
             */
            async getLocation(isDeliverFlag, index) {
                this.$emit('getLocation', {isDeliverFlag, index})
            },
            /**
             * @desc 收货地址填写校验
             * <AUTHOR>
             * @date 2024-09-09
             * @param item 收货地址信息 index 索引
             */
            checkData () {
                let isCheck = true
                this.addressList.forEach((item, index) => {
                    if (this.$utils.isEmpty(item.consignee)) {
                        this.$message['warn'](`收货地址${index+1}请填写收货人姓名`);
                        isCheck = false
                        return
                    }
                    if (this.$utils.isEmpty(item.mobilePhone)) {
                        this.$message['warn'](`收货地址${index+1}请输入收货人手机号码`);
                        isCheck = false
                        return
                    }
                    if (this.$utils.isEmpty(item.province)) {
                        this.$message['warn'](`收货地址${index+1}请选择收货人所在地区`);
                        isCheck = false
                        return
                    }
                    if (this.$utils.isEmpty(item.addr)) {
                        this.$message['warn'](`收货地址${index+1}请输入收货人详细地址`);
                        isCheck = false
                        return
                    }
                })
                return isCheck
            },
            /**
             * @desc 点击新建收货地址按钮
             * <AUTHOR>
             * @date 2024-09-09
             */
            addAddress() {
                // 添加新地址时其它地址处于收起状态
                this.addressList.forEach(item => {
                    item.unfoldFlag = false
                })
                this.addressList.push({
                    consignee: '',
                    mobilePhone: '',
                    addrType: 'ShipAddr',
                    addr: '',
                    regionInfo: '',
                    province: '',
                    city: '',
                    district: '',
                    row_status: 'NEW',
                    acctId: 'NEW',
					townName:'',
					town:'',
                    unfoldFlag: true
                })
            },
            /**
              * 删除元素
              * <AUTHOR>
              * @date 2024-09-09
              * @param index 删除元素索引
            */
            deleteAddress(index){
                this.addressList.splice(index, 1);
                if (this.addressList.length === 1) {
                    this.addressList[index - 1] && (this.addressList[index - 1].unfoldFlag = true)
                }
            },
            /**
              * 展开隐藏
              * <AUTHOR>
              * @date 2024-09-09
              * @param key 需要展开元素索引
            */
            unfoldShrink (key) {
                this.addressList.forEach((item, index) => {
                    if (key === index) {
                        item.unfoldFlag = !item.unfoldFlag
                    } else {
                        item.unfoldFlag = false
                    }
                })
            }
        },
    }
</script>

<style lang="scss">
    .address-info {
        .link-location{
            color: #1F74FF;
        }
        .assignment-button{
            width: 36%;
            height: 70px;
            float: right;
            margin: 20px 20px 0 0;
        }
        .bottom-sticky {
            /*deep*/.link-button {
                width: 94%;
                height: 96px;
                margin-right: 24px;
                margin-left: 24px;
                font-size: 30px;
           }
        }
        .add-address {
            border: 2px dashed #2F69F8;
            border-radius: 8px;
            margin: auto;
            height: 96px;
            width: 702px;
            text-align: center;
            .text {
                font-family: PingFangSC-Regular,serif;
                font-size: 32px;
                color: #2F69F8;
                letter-spacing: 0;
                line-height: 96px;
            }
            .icon-plus {
                font-size: 32px;
                line-height: 96px;
                color: #2F69F8;
            }
        }
        .title-container {
            display: flex;
            .contacts-text {
                width: 50%;
            }
            .delete-btn {
                width: 50%;
                text-align: right;
                font-family: PingFangSC-Regular,serif;
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
            }
        }
        .address-container {
            margin-bottom: 20px;
        }
    }
</style>
