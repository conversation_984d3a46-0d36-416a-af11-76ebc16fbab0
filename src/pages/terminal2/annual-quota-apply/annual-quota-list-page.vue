<!--
 * @Description: 配额明细列表
 * @Author: 陈友杰
 * @Date: 2024-12-23
-->
<template>
    <link-page class="order-list-page">
        <link-auto-list :option="dataOption" class="order-list" hideCreateButton>
            <template slot-scope="{data,index}">
                <link-card>
                    <view  @tap="goOrderItem(data)">
                        <view class="top-all">
                            <view class="top-container">
                                <view class="code">
                                    <text>{{data.quotaCode}}</text>
                                </view>
                                <view class="order-tag">
                                    <status-button :type="data.approvalStatus.toLowerCase()" :label="data.approvalStatus | lov('ANNUAL_QUOTA_STATUS')"></status-button>
                                </view>
                            </view>
                        </view>
                        <view class="order-title" >
                            <view class="order-time">
                                <text class="label" >终端名称: </text>
                                <text class="value" >{{data.acctName}}</text>
                            </view>
                        </view>
                        <view class="time-buyer">
                            <view class="order-time">
                                <text class="label" >终端编码: </text>
                                <text class="value" >{{data.acctCode}}</text>
                            </view>
                        </view>
                        
                        <view class="order-warehouse" v-if="pageParam.data.isjiaoling || pageParam.data.isdacheng" >
                            <text class="label">配额产品类别: </text>
                            <text class="value">{{data.prodPartCode | lov('PROD_BUS_M_CLASS')}}</text>
                        </view>
                        <view class="order-warehouse" v-else-if="pageParam.data.isGuojiao || pageParam.data.isTequ || pageParam.data.isHuaijiu " >
                            <text class="label">所属品项: </text>
                            <text class="value">{{data.prodPartCode | lov('PROD_BUS_S_CLASS')}}</text>
                        </view>
                        <view class="order-bottom">
                            <view class="order-num">
                                <text class="label">申请类型: </text>
                                <text class="value" >{{data.quotaType | lov('QUOTA_APPLY_TYPE')}}</text>
                            </view>
                            <view class="order-num">
                                <text class="label">{{pageParam.data.isTequ || pageParam.data.isHuaijiu ? '所属年月': '所属财年:'}} </text>
                                <text class="value">{{pageParam.data.isTequ || pageParam.data.isHuaijiu ? data.month : data.fiscalYear}}</text>
                            </view>
                        </view>
                        <view class="order-bottom">
                            <view class="order-num">
                                <text class="label">{{adjustType}}: </text>
                                <text class="value">{{data.quotaQuantity}}</text>
                            </view>
                        </view>
                    </view>
                </link-card>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
    definePageConfig({
        navigationBarTitleText: '配额申请明细'
    })
    import statusButton from '@/pages/lzlj/components/status-button.vue';
    export default {
        name: "annual-quota-list-page",
        data () {
            const url = this.pageParam.url || ''
            const param = this.pageParam.param || {}
            const hooks = this.pageParam.hooks || {}
            const dataOption = new this.AutoList(this, {
                module: 'export/link/basic',
                url: {
                    queryByExamplePage: url
                },
                sortField:this.pageParam.sortField,
                param,
                hooks,
            })
            return {
                dataOption,
                adjustType: '',     //展示调整配额或计划外配额
            }
        },
        components: {
            statusButton
        },
        async created () {
            // 动态展示字段设置
            let val=this.pageParam.data
            this.adjustType = val.label
            // 标题设置
            this.$taro.setNavigationBarTitle({title: this.pageParam.pageTitle});
        },
        computed: {
        },
        methods: {
           
            /**
              * 跳转配额详情
              * <AUTHOR>
              * @date 2024.12.23
              * @param data
            */
            goOrderItem (data) {
                this.pageParam.cellClick && (this.pageParam.cellClick({
                    ...data
                }));      
            },
            /**
             * @description: 页面回退列表刷新
             * @author: 邓佳柳
             * @Date: 2025-02-19
             */            
             onBack(){
                this.dataOption.methods.reload()
             }
        }
    }
</script>

<style lang="scss">
    
    .order-list-page {
        .sort-search-filter-container {
            height: 100px;
            margin-bottom: 22px;
            .sort-search-filter {
                position: fixed;
                width: 100%;
                @include flex-start-center;
                @include space-between;
                background: #ffffff;
                margin-top: 1px;
                /*margin-bottom: 24px;*/
                border-top: 1px solid #F2F2F2;
                .link-sort {
                    padding: 32px 0 24px 24px;
                    width: 50%;
                }
                .search-filter {
                    @include flex-start-center;
                    .icon-sousuo {
                        font-size: 28px;
                        color: #595959;
                        padding: 38px 24px 30px 24px;
                    }
                    .link-filter {
                        padding: 32px 24px 24px 16px;
                    }
                }
            }
        }
        /*deep*/.lnk-tabs-container {
                    height: 92px;
                }
        .order-list {
            .link-card {
                /*deep*/.link-card-content {
                      padding: 40px 24px;
                }
                margin-bottom: 24px;
                .identify {
                    position: absolute;
                    width: 12px;
                    height: 58px;
                    background: #2F69F8;
                    border-radius: 0 0 0 100px;
                    right: 0;
                    top: 0;
                }
                .top-all {
                    .top-container {
                        position: relative;
                        @include flex-start-center;
                        @include space-between;
                        .code {
                            background: #A6B4C7;
                            border-radius: 8px;
                            font-size: 28px;
                            color: #FFFFFF;
                            padding: 6px 12px;
                            .sales-ticket {
                                font-size: 24px;
                            }
                        }
                        .order-tag {
                            display: flex;
                            .item-tag {
                                min-width: 80px;
                                height: 36px;
                                line-height: 36px;
                                text-align: center;
                                color: #ffffff;
                                background: #2F69F8;
                                box-shadow: 0 3px 4px 0 rgba(47,105,248,0.35);
                                border-radius: 8px;
                                padding-left: 27px;
                                padding-right: 27px;
                                font-size: 20px;
                                margin-right: 8px;
                                transform: skewX(-30deg);
                                flex: 1;
                                .tag-content {
                                    transform: skewX(30deg);
                                }
                            }
                            .orange-color {
                                background-color: rgb(247,150,70)
                            }
                        }
                    }
                }
                .time-buyer {
                    margin-top: 12px;
                    @include flex-start-center;
                    @include space-between;
                    .order-time {
                        font-size: 28px;
                        .label{
                            color: #8C8C8C;
                        }
                        .value {
                            color: #000000;
                        }
                    }
                }
                .order-title {
                    margin-top: 12px;
                    font-size: 32px;
                    color: #262626;
                }
                .order-warehouse {
                    font-size: 28px;
                    padding-top: 12px;
                    .label {
                        color: #8C8C8C;
                    }
                    .value {
                        color: #000000;
                    }
                }
                .order-bottom {
                    margin-top: 12px;
                    @include flex-start-center;
                    @include space-between;
                    .order-num {
                        font-size: 28px;
                        .label {
                            color: #8C8C8C;
                        }
                        .value {
                            color: #000000;
                            .unit {
                                font-size: 28px;
                                color: #8C8C8C;
                            }
                        }
                    }
                }
            }
        }
    }
</style>
