<!--
 * @Description:  年度配额申请（窖龄）
 * @Author: 邓佳柳
 * @Date: 2025-3-19
-->
<template>
	<link-page class="annual-quota-jiaoling-page">
		<!-- 审批记录 -->
		<approval-history-point :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)" />
		<!-- 年度配额申请表单组件-窖龄 -->
		<view class="row-content">
			<quota-form-info
				v-if="formData.acctId"
				:formData="formData"
				@validate="validateBeforeSubmit"
				:message="message"
				:readonly="!isEditFlag"
				:brandCompanyCode="5151" />
		</view>
		<!-- 窖龄年度配额明细列表 -->
		<jiaoling-year-quota-list
			v-if="(formData.quotaType === 'PlannedQuotaApplication' && formData.approvalStatus === 'Approved' && $utils.isEmpty(approvalId))
				|| ['QuotaAdjustApplication','UnplannedQuotaApplication'].includes(formData.quotaType)"
			:id="formData.acctId"
			:fiscalYear="formData.fiscalYear"
			:prodPartCode="formData.prodPartCode"
			:flagParam="{
					isEditFlag: isEditFlag,
					isCheckList:(!!pageParam && pageParam.isCheckList) ||($utils.isNotEmpty(approvalId) ? 'N' : '')}">
			<template slot-scope="{ data }">
				<view class="title">年度配额明细</view>
				<view class="flex-between" v-if="formData.quotaType == 'PlannedQuotaApplication'">
					<view>年度配额总额：{{ data.yearlyTotal }}</view>
					<view>年度配额余额：{{ data.yearlyRemaining }}</view>
				</view>
			</template>
		</jiaoling-year-quota-list>
		<!-- 协议信息 -->
		<view class="row-content">
			<view :class="['row-title', 'line']">协议信息</view>
			<view v-if="formData.id">
				<lnk-img-watermark
					:parentId="formData.id"
					ref="imgList"
					:delFlag="$utils.isEmpty(approvalId)&& formData.approvalStatus !== 'Submitted'"
					:historyDelFlag="false"
					moduleType="agrAgreement"
					moduleName="协议信息"
					@imgUploadSuccess="imgUploadSuccess"
					@imgDeleteSuccess="imgDeleteSuccess"
					:newFlag="$utils.isEmpty(approvalId)&& formData.approvalStatus !== 'Submitted'" />
			</view>
		</view>
		<!-- 申请/编辑 -->
		<link-sticky class="bottom-btn" v-if="isEditFlag">
			<link-button class="sure-btn" @tap="submit" autoLoading :throttleTimer="10">提交</link-button>
		</link-sticky>
		<!-- 查看详情 -->
		<link-sticky class="bottom-btn" v-if="
                !isEditFlag &&
                $utils.isEmpty(approvalId) &&
                formData.approvalStatus === 'Approved' &&
                formData.quotaType === 'PlannedQuotaApplication'">
			<link-button class="sure-btn" v-if="isShowAdjustBtn" mode="stroke" @tap="editContent('edit')" autoLoading :throttleTimer="10">配额调整</link-button>
			<link-button class="sure-btn" size="normal" @tap="editContent('out')" autoLoading :throttleTimer="10">计划外配额</link-button>
		</link-sticky>
		<!-- 审批操作 -->
		<link-sticky v-if="$utils.isNotEmpty(approvalId)">
			<approval-operator :approvalId="approvalId"></approval-operator>
		</link-sticky>
	</link-page>
</template>
<script>
	import LnkImgWatermark from '@/pages/core/lnk-img-watermark/lnk-img-watermark';
	import ApprovalHistoryPoint from '@/pages/lzlj/approval/components/approval-history-point';
	import ApprovalOperator from '@/pages/lzlj/approval/components/approval-operator';
	import quotaDetailCard from './components/quota-detail-card.vue';
	import JiaolingYearQuotaList from '@/pages/terminal/terminal/components/year-quota/jiaoling-year-quota-list.vue';
	import QuotaFormInfo from './components/quota-form-info.vue';
	definePageConfig({
		navigationBarTitleText: '年度配额申请'
	});
	export default {
		name: 'annual-quota-jiaoling-page',
		components: {
			LnkImgWatermark,
			ApprovalHistoryPoint,
			ApprovalOperator,
			quotaDetailCard,
			JiaolingYearQuotaList,
			QuotaFormInfo
		},
		data() {
			return {
				formData: {
					acctName: '',
					quotaType: '',
					fiscalYear: 2025,
					quotaQuantity: ''
				}, //配额申请表单对象

				isEditFlag: true, //表单编辑状态
				approvalId: undefined, //审批id
				id: '', //当前记录id
				isShowAdjustBtn: true, //判断是否显示配额调整按钮
				basicId: '',
				message: ''
			};
		},
		watch: {},
		computed: {},
		/**
		 * @description: 窖龄配额申请/编辑/详情/审批详情
		 * @author: 邓佳柳
		 * @Date: 2025-03-20
		 * @param type new|edit|out 配额申请类型
		 * @param operate NEW|UPDATE|空  新建|编辑|查看
		 * @param pageSource approval 审批详情
		 * @param data 页面传递数据
		 */
		async created() {
			console.log('pageParam', this.pageParam);
			await this.init(this.pageParam);

		},
		mounted() {},
		methods: {
			/**
			* @description: 页面初始化
			* @author: 邓佳柳
			* @Date: 2025-03-26
			* @param {*} param
			*/
			async init(param = {}) {
				let {data, type, source, operate} = param;
				this.isEditFlag = this.$utils.isNotEmpty(operate);
				// 配额类型映射
				const typeMap = {
					new: 'PlannedQuotaApplication',
					out: 'UnplannedQuotaApplication',
					edit: 'QuotaAdjustApplication'
				};
				// 场景类型
				if (operate === 'NEW') {
					//新建 申请记录id自动生成
					this.id = await this.$newId();
					let acctId = type === 'new' ? data.id : data.acctId;
					let {acctCode, acctName, fiscalYear = 2025} = data;
					let quotaType = typeMap[type];
					let prodPartCode = type === 'new' ? '' : data.prodPartCode
					this.formData = {id: this.id, acctId, acctCode, acctName, quotaType, fiscalYear, prodPartCode,quotaQuantity:''};
				} else if (source === 'approval') {
					// 审批 查看详情
					this.id = data.flowObjId;
					this.approvalId = data.id;
					this.formData = await this.getApplyInfo();
				} else if(this.$utils.isNotEmpty(data)){
					// 编辑|查看
					this.formData = data || {};
					console.log('-------------',this.formData)
					// 配额计划详情调整配额权限控制
					if(this.formData.quotaType==='PlannedQuotaApplication' && this.formData.approvalStatus==='Approved'){
						let type = await this.checkAccntStandard()
						if(type=== 'standard1'){
							this.isShowAdjustBtn = true;
						}else{
							// 标准2客户:关闭核心客户的调整配额功能，限制仅管理员可调整
							const positionCode = await this.$utils.getCfgProperty('QUOTA_ADJUST_APPLY_CONFIG');
							let userInfo = this.$taro.getStorageSync('token').result;
							this.isShowAdjustBtn= JSON.parse(positionCode).applyPostWhiteList.indexOf(userInfo.positionType)>-1;
						}
					}
				}else{
					// 消息场景
					let sceneObj = await this.$scene.ready()
					console.log('sceneObj',sceneObj);
					
					if (sceneObj.query['approval_from'] === 'qw') {
						this.approvalId = sceneObj.query['approval_id'];
						this.id = sceneObj.query['flowObjId'];
						this.formData = await this.getApplyInfo();
					}
				}
				

				if (this.formData.quotaType === 'QuotaAdjustApplication') {
					this.message = '如若调减，请填写负数!注:调减配额不可低于年度配额余额!';
				}
				// 计划外配额与调整配额 需要配额计划id 和 配额计划明细
				if(this.formData.quotaType!=='PlannedQuotaApplication' && this.isEditFlag){
					let rows = await this.getBasicInfo()
					this.basicId = rows[0].id
				}
				// 设置页面标题
				let title = (await this.$lov.getNameByTypeAndVal('JL_QUOTA_APPLY_TYPE', this.formData.quotaType)) || '年度配额申请';
				let scene = this.$utils.isNotEmpty(this.approvalId) ? '审批':'' ;
				this.$taro.setNavigationBarTitle({title: `${title}${scene}`});
			},
			imgUploadSuccess(imgList) {
				this.imgList = imgList;
			},
			imgDeleteSuccess(imgList) {
				this.imgList = imgList;
			},
			/**
			 * @description: 提交
			 * @author: 邓佳柳
			 * @Date: 2025-03-20
			 */
			async submit() {
				let {id, acctId, quotaType, prodPartCode, fiscalYear, quotaQuantity} = this.formData;
				console.log(id, acctId, quotaType, prodPartCode, fiscalYear, quotaQuantity,this.formData)

				if (quotaType == 'PlannedQuotaApplication' && this.$utils.isEmpty(prodPartCode)) {
					this.$message.error('请选择配额产品类别');
					return;
				}

				if (this.$utils.isEmpty(quotaQuantity)) {
					this.$message.error('配额数量不能为空');
					return;
				}

				if(quotaType == 'QuotaAdjustApplication' && !/^[-]?[1-9]\d*$/.test(quotaQuantity)){
                    this.$message.error('提示：正数整数调增，负数整数调减，请输入正确的数字！')
                    return
                }

				const urlMap = {
					PlannedQuotaApplication: '/action/link/jiaolingQuotaDetail/basicSubmit',
					QuotaAdjustApplication: `/action/link/jiaolingQuotaDetail/adjustedSubmit?basicId=${this.basicId}`,
					UnplannedQuotaApplication: `/action/link/jiaolingQuotaDetail/unplannedSubmit?basicId=${this.basicId}`
				};
				try {
					let url = urlMap[quotaType];
					let params = {id, acctId, quotaType, prodPartCode, fiscalYear, quotaQuantity};
					let {success} = await this.$http.post(url, params);
					if (success) {
						this.$nav.back({jiaolingQuota: true});
					}
				} catch (e) {}
			},
			/**
			 * @description: 获取申请记录
			 * @author: 邓佳柳
			 * @Date: 2025-03-20
			 */
			async getApplyInfo() {
				return new Promise(async (resolve, reject) => {
					try {
						let url = 'action/link/annualQuotaDetails/queryById';
						let params = {
							id: this.id
						};
						let {result} = await this.$http.post(url, params);
						resolve(result);
					} catch (e) {
						reject(e);
					}
				});
			},
			/**
			 * @description: 新建调整配额|计划外配额
			 * @author: 邓佳柳
			 * @Date: 2025-03-19
			 * @param {*} type
			 */
			editContent(type) {
				this.$nav.push('/pages/terminal2/annual-quota-apply/annual-quota-jiaoling-page', {
					data: {...this.formData},
					operate: 'NEW',
					type
				});
			},
			/**
			 * @description: 校验客户标准
			 * @author: 邓佳柳
			 * @Date: 2025-03-25
			 */
			async checkAccntStandard() {
				return new Promise(async (resolve, reject) => {
					try {
						let url = '/action/link/jiaolingQuotaDetail/checkAccntStandard';
						let params = {
							acctId: this.formData.acctId,
							fiscalYear: this.formData.fiscalYear,
							prodPartCode: this.formData.prodPartCode
						};
						let {success,type} = await this.$http.post(url, params);
						// 标准1-standard1
						// 标准2-standard2
						if (success) {
							resolve(type)
						}
					} catch (error) {
						console.error(error);
					}
				});
			},
			/**
			 * @description: 获取配额计划
			 * @author: 邓佳柳
			 * @Date: 2025-03-26
			 */
			 async getBasicInfo(){
                return new Promise(async(resolve) =>{
                    try{
                        let url ='/action/link/annualQuotaDetails/queryByExamplePage'
                        let params = {
                            filtersRaw: [
                                {id:'acctId',property:'acctId',value: this.formData.acctId, operator:'='},
                                {id:'prodPartCode',property:'prodPartCode',value: this.formData.prodPartCode, operator:'='},
                                {id:'fiscalYear',property:'fiscalYear',value: this.formData.fiscalYear, operator:'='},
                                {id:'quotaType',property:'quotaType',value: 'PlannedQuotaApplication', operator:'='},
                            ]
                        }
                        let {rows,success} = await this.$http.post(url,params)
                        if(success){
                            resolve(rows)
                        }else{
                            resolve([])
                        }
                    }catch (e){
                        console.log('获取配额计划失败',e)
                    }
                })

            },
			/**
			 * @description: 判断当前客户是否存在所选【财年】+【品项】的配额计划明细，如存在则弹窗提醒：当前【XX客户编码客户名称】已存在【财年】【品项】配额计划，是否跳转至已有配额计划页面？按钮：取消/跳转
			 * 不存在则按照现有逻辑继续往下校验是否为标准1客户；
			 * @author: 邓佳柳
			 * @Date: 2025-01-13
			 */
			async validateBeforeSubmit() {
				let list = await this.getBasicInfo();
				if (list.length > 0) {
					let prodPartName = await this.$lov.getNameByTypeAndVal('PROD_BUS_M_CLASS', list[0].prodPartCode);
					this.$dialog({
						title: '提示',
						cancelButton: true,
						confirmText: '跳转',
						content: `当前【${list[0].acctCode}】【${list[0].acctName}】已存在【${list[0].fiscalYear}】【${prodPartName}】配额计划，是否跳转至已有配额计划页面？`,
						onConfirm: () => {
							let operate = '';
							if (['Rejected', 'Revoke'].includes(list[0].approvalStatus)) {
								operate = 'UPDATE';
							}
							// 跳转详情
							this.$nav.redirect('/pages/terminal2/annual-quota-apply/annual-quota-jiaoling-page', {
								data: list[0],
								operate
							});
							this.init(Object.assign({}, list[0]));
						},
						onCancel: async () => {
							// 校验标准1客户,清空品项
							if ((await this.checkAccntStandard())==='standard1') {
								this.formData.prodPartCode = '';
							}else{
								// 弹框显示错误 标准2客户退出
								this.$dialog({
									title: '提示',
									content: '核心客户不允许发起年度配额申请',
									onConfirm: () => {
										this.$nav.back();
									},
								})
							}
						}
					});
				} else {
					if((await this.checkAccntStandard()) === 'standard2'){
						// 弹框显示错误 标准2客户退出
						this.$dialog({
							title: '提示',
							content: '核心客户不允许发起年度配额申请',
							onConfirm: () => {
								this.$nav.back();
							},
						})
					}
				}
			}
		}
};
</script>
<style lang="scss">
	.annual-quota-jiaoling-page {
		padding-bottom: 400px;
		padding-top: 10px;
		.row-content {
			font-size: 28px;
			background: #ffffff;
			padding: 20px;
			margin: 20px;
			border-radius: 20px;
		}
		.bottom-btn {
			padding-top: 16px;
			.sure-btn {
				border-radius: 60px;
				margin-right: 24px;
				margin-left: 24px;
			}
		}
		.link-auto-list-no-more {
			display: none !important;
		}
		.flex-between {
			display: flex;
			align-items: center;
			justify-content: space-between;
		}
	}
</style>
