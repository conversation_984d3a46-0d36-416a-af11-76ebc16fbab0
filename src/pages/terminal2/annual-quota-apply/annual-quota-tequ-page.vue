<!--
 * @Description:  年度配额申请（特曲）
 * @Author: 邓佳柳
 * @Date: 2025-02-10
-->
<template>
	<link-page class="annual-quota-tequ-page">
		<!-- 审批记录 -->
		<approval-history-point :approvalId="approvalId" v-if="$utils.isNotEmpty(approvalId)" />
		<!-- 年度配额申请表单组件-特曲 -->
		<view class="row-content" v-if="$utils.isNotEmpty(type)">
			<quota-form-info
				:formData="formData"
				:readonly="!isEditFlag"
				:brandCompanyCode="5137"
				:message="message" 
                @getOrgQuotaTotal='getOrgQuotaTotal'/>
		</view>
		<!-- 特曲年度配额明细列表 -->
		<tequ-year-quota-list
			v-if="formData.acctId && formData.prodPartCode && flag && type !=='orgAuthorized'"
			:id="formData.acctId"
			:fiscalYear="formData.fiscalYear"
			:month="formData.month"
			:prodPartCode="formData.prodPartCode"
			:flagParam="{
				isEditFlag: isEditFlag,
				isCheckList: !!pageParam && pageParam.isCheckList || ($utils.isNotEmpty(approvalId)?'N':''),
				detail:pageParam.detail
			}">
			<view class="row-title line" v-if="pageParam.detail!=='Y'">年度配额明细</view>
		</tequ-year-quota-list>

		<!-- 备注 -->
		<view class="row-content" v-if="$utils.isNotEmpty(type) && formData.quotaType !== 'QuotaAdjustApplication'">
			<view :class="['row-title', 'line']">备注</view>
			<view class="textarea-big">
				<link-textarea class="text-area" :placeholder="'请输入备注'"
					mode="textarea"
					v-model="formData.comments"
					:nativeProps="{maxlength:maxlength}"
					:disabled="!remarkEditable"
					style="padding: 0px"
					placeholder-style="color: #BFBFBF;"></link-textarea>
			</view>
		</view>
		<!-- 协议信息 -->
		<view class="row-content" v-if="formData.acctId && type ==='authorized' && formData.quotaType !== 'QuotaAdjustApplication'">
			<view :class="['row-title', 'line']">协议信息</view>
			<view v-if="formData.id">
				<lnk-img-watermark
					:parentId="formData.id"
					ref="imgList"
					:delFlag="$utils.isEmpty(approvalId) && formData.approvalStatus !== 'Submitted'"
					:historyDelFlag="false"
					moduleType="agrAgreement"
					moduleName="协议信息"
					@imgUploadSuccess="imgUploadSuccess"
					@imgDeleteSuccess="imgDeleteSuccess"
					:newFlag="$utils.isEmpty(approvalId) &&  formData.approvalStatus !== 'Submitted'" />
			</view>
		</view>

		<!-- 授权配额余额 -->
		<view class="row-content" v-if="type==='orgAuthorized'&& $utils.isNotEmpty(approvalId)">
			<view class="info-item">
				<view class="info-label">授权配额余额</view>
				<view class="info-value">{{formData.parentQuotaBalanceQty || 0}}</view>
			</view>
			<view class="info-item">
				<view class="info-label">批准授权配额数量</view>
				<view class="info-value"><link-input placeholder="请填写批准授权配额数量" :disabled="formData.approvalStatus !== 'Submitted'" v-model="formData.appointQuantity" @input="onAppQuaChange" /></view>
			</view>
			<view class="tips">{{ tips }}</view>
		</view>

		<!-- 申请/编辑 -->
		<link-sticky class="bottom-btn" v-if="isEditFlag">
			<link-button class="sure-btn" @tap="submit" autoLoading :throttleTimer="10">提交</link-button>
		</link-sticky>
		<!-- 审批操作 -->
		<link-sticky v-if="$utils.isNotEmpty(approvalId)">
			<approval-operator :approvalId="approvalId" :formData="formData"></approval-operator>
		</link-sticky>

		<link-dialog ref="prodBottom" position="bottom" height="85vh" class="dialog-bottom" noPadding v-model="show">
			<view class="model-title">
				<view class="iconfont icon-close" @tap="dialogHide"></view>
				<view class="title">请选择下一节点审批人</view>
			</view>
			<view class="dialog-content" style="height: calc(100% - 44px);">
				<scroll-view scroll-y="true" :style="{height: 'calc(100% - 75px)'}">
					<link-radio-group v-model="formData.approverId">
						<link-auto-list :option="approverList" hideCreateButton :scrollContent="true" :searchInputBinding="{props:{placeholder:'员工姓名/工号'}}">
							<template slot-scope="{data, index}">
								<view slot="note">
									<item :key="index" :data="data" :arrow="false" class='select-box'>
										<link-checkbox :val=data.userId toggleOnClickItem slot='thumb' />
										<view class='select-item' slot='note'>
											<view class='select-item-left'>{{data.fstName}}</view>
											<view>{{data.userName}}</view>
										</view>
									</item>
								</view>
							</template>
						</link-auto-list>
					</link-radio-group>
				</scroll-view>
				<view class="link-dialog-foot-custom">
					<link-button shadow @tap="cfmApprover" label="确定" style="width: 90vw" />
				</view>
			</view>
		</link-dialog>
	</link-page>
</template>
<script>
	import LnkImgWatermark from '@/pages/core/lnk-img-watermark/lnk-img-watermark';
	import ApprovalHistoryPoint from '@/pages/lzlj/approval/components/approval-history-point';
	import ApprovalOperator from '@/pages/lzlj/approval/components/approval-operator';
	import quotaDetailCard from './components/quota-detail-card.vue';
	import QuotaFormInfo from './components/quota-form-info.vue';
	import TequYearQuotaList from '@/pages/terminal/terminal/components/year-quota/tequ-year-quota-list.vue';
	import {DateService} from 'link-taro-component';

	definePageConfig({
		navigationBarTitleText: ''
	});
	const formAuth={
		id:'', //申请id（必填）
		acctId:'', //终端id（必填）
		quotaType:'', //申请类型（必填）
		fiscalYear:'', //财年（必填）
		quotaQuantity:'', //申请数量（必填）
		prodPartCode:'', //品项（必填）
		month:'', //申请年月（必填）
		approverId:'', //指定审批人id
		comments:''
	}
	const fromOrg = {
		id:'',//申请id
		applyOrgCode:'',//发起申请组织code
		fiscalYear:'',//财年（必填）
		quotaQuantity:'',//申请数量（必填）
		prodPartCode:'',//品项（必填）
		month:'',//申请年月（必填）
		approverId:undefined,//指定审批人id
		orgType:'',//发起申请组织类型 特曲-组织类型-区县 SalesArea
					// 特曲-组织类型-城市 SalesCity
					// 特曲-组织类型-片区 SalesRegion
					// 特曲-组织类型-公司 BranchCompany
		comments:''
	}
	export default {
		name: 'annual-quota-tequ-page',
		components: {
			LnkImgWatermark,
			ApprovalHistoryPoint,
			ApprovalOperator,
			quotaDetailCard,
			QuotaFormInfo,
			TequYearQuotaList
		},
		data() {
		const approverList = new this.AutoList(this, {
			url: {queryByExamplePage: 'action/link/orgnization/queryParentByOrgCode'},
			param: {
			},
			searchFields: ['contactsName', 'approvalPhone'],
			sortOptions: null,
			renderFunc: (h, {data, index}) => {
				return (
					<item key={index} data={data} class='select-box' arrow='false'>
						<link-checkbox val={data.id} toggleOnClickItem slot='thumb' />
						<view class='select-item' slot='note'>
							<view class='select-item-left'>{data.fstName}</view>
							<view>{data.userName}</view>
						</view>
					</item>
				);
			},
			hooks:{
                beforeLoad(option) {

					if(this.type === 'authorized'){
						option.param.orgCode = this.pageParam.data.orgCode|| this.formData.salesAreaCode //终端所属组织编码
						option.param.parentType = 'SalesArea' //SalesArea
					}else{
                        option.param.orgCode = this.formData.applyOrgCode //当前组织编码:发起申请组织code
                        let orgType = this.formData.orgType //发起申请组织类型
                        let parentType = {
                            SalesArea: 'SalesCity',
                            SalesCity: 'SalesRegion',
                            SalesRegion: 'BranchCompany', //指定审批人
                        }
						option.param.parentType = parentType[orgType]//上级组织类型
					}
				},
				afterLoad(data){
					data.total = data.rows.length
				}
			}
			});
			return {
				formData: {
					acctId:'',
					acctName: '',
					quotaType: '',
					fiscalYear: 2025,
					quotaQuantity: ''
				}, //配额申请表单对象

				isEditFlag: false, //表单编辑状态
				approvalId: undefined, //审批id
				id: '', //当前记录id
				type: '', // authorized|orgAuthorized
				maxlength: 300,
				approverList,
				show: false, //弹框显示
				flag:true,
				message:'',
				tips:'', //批准授权配额数量提示
			};
		},
		watch: {},
		computed: {
			remarkEditable(){
				return this.type === 'authorized' && this.isEditFlag || this.type === 'orgAuthorized' && this.$utils.isEmpty(this.approvalId) && this.isEditFlag
			}
		},
		/**
		 * @description: 特曲配额申请/编辑/详情/审批详情
		 * @author: 邓佳柳
		 * @Date: 2025-02-17
		 * @param type authorized|orgAuthorized  类型
		 * @param operate NEW|UPDATE|空  新建|编辑|查看
		 * @param pageSource approval 审批详情
		 * @param data 页面传递数据
		 */
		async created() {
			// 页面传递数据
			let {data, type, operate, from = ''} = this.pageParam || {};
			this.isEditFlag = this.$utils.isNotEmpty(operate);

			const approvalTypeToType = {
				AuthorizedQuotaApply:'authorized',
				OrgAuthorizedQuotaApply:'orgAuthorized'
			}
			// 新建
			if (operate === 'NEW') {
				this.id = await this.$newId();
				this.type = type
				let year = new Date().getFullYear();
				let month = new Date().getMonth() + 1;
				let fiscalYear = month > 10 ? year + 1 : year;
				month = DateService.format(new Date(), 'YYYYMM');
				let {acctId, acctName, acctCode, prodPartCode, quotaType} = data;
				if(this.$utils.isEmpty(prodPartCode)){
					this.flag = false //无品项申请不展示明细
				}
				// 授权配额申请
				if (type === 'authorized') {
					this.formData = {id: this.id, acctId, acctName, acctCode, prodPartCode, quotaType, fiscalYear, month, quotaQuantity: ''};
					// 校验组织类型
					if(!(await this.checkAcctOrgType())) return
				} else {
					// 组织授权配额申请
                    let { firstName } = this.$taro.getStorageSync('token').result;
                    let{ applyOrgCode , orgType } = data //当前组织编码与类型
					this.formData = {
						id: this.id,
						firstName,
						prodPartCode,
						fiscalYear,
						month,
						orgType,
						applyOrgCode,
                        quotaQuantity: '', //申请数量
                        from
                    };
                    if(from !=='list' ){
                      await this.getOrgQuotaTotal(applyOrgCode)
                    }
					// 上级组织与类型
					const levelMap = {
						SalesArea:{ParentPostnType:'城市经理',level:'区县'},
						SalesCity:{ParentPostnType:'片区经理',level:'城市'},
						SalesRegion:{ParentPostnType:'品牌公司管理员',level:'片区'},
					}
					this.message = `即将向上级【${levelMap[orgType].ParentPostnType}】针对【组织${levelMap[orgType].level}】发起批量授权配额申请，
					请按照【${levelMap[orgType].level}维度】填写申请授权配额总量!`
				}
				console.log('新建',this.type)
				this.getDefaultApprover()
			} else if (this.pageParam.source === 'approval') {
				this.type = approvalTypeToType[data.approvalType]
				this.approvalId = data.id;
				this.id = data.flowObjId;
				this.formData = await this.getApplyInfo();
				console.log('审批详情',this.type)

			} else if(this.$utils.isNotEmpty(this.pageParam)){
				if (this.pageParam.detail === 'Y') {
					this.type = ''; //查看更多明细
					this.formData = data
				} else if(this.pageParam.data) {
					// 授权配额与组织授权配额明细
                    this.type = ['AuthorizedQuota', 'QuotaAdjustApplication', 'PlannedQuotaApplication'].includes(data.quotaType) ? 'authorized' : 'orgAuthorized';
					this.id = data.id
					this.formData = await this.getApplyInfo();
                }
				console.log('查看详情',this.pageParam)
			}else{
				// 消息场景
				let sceneObj = await this.$scene.ready()
				console.log('sceneObj',sceneObj);
				
				if (sceneObj.query['approval_from'] === 'qw') {
					this.approvalId = sceneObj.query['approval_id'];
					this.id = sceneObj.query['flowObjId'];
					this.type = approvalTypeToType[sceneObj.query['approval_type']]
					this.formData = await this.getApplyInfo();
				}
			}
			
			// 处理标题
			const mapLov = {
				AuthorizedQuota: '终端授权配额'
			};
			let title = this.pageParam.detail ? '计划内配额' : mapLov[this.formData.quotaType] || '组织授权配额';
			let scene = this.isEditFlag ? '申请' : this.$utils.isNotEmpty(this.approvalId) ? '审批' : '详情';
			this.$taro.setNavigationBarTitle({title: `${title}${scene}`});
		},
		mounted() {},
		methods: {
			imgUploadSuccess(imgList) {
				this.imgList = imgList;
			},
			imgDeleteSuccess(imgList) {
				this.imgList = imgList;
			},
			/**
			 * @description: 提交
			 * @author: 邓佳柳
			 * @Date: 2025-02-13
			 */
			async submit() {

				if(this.type == 'authorized' ){
					let flag = await this.checkAcctOrgType()
					if(!flag) return
				}
				if (this.type == 'authorized' && this.$utils.isEmpty(this.formData.prodPartCode)) {
					this.$message.error('请选择配额产品类别');
					return;
				}
				if (this.type == 'authorized' && this.$utils.isEmpty(this.formData.quotaQuantity)) {
					this.$message.error('配额数量不能为空');
					return;
				}
				// 当前层级为片区时指定公司层级审批人
				if(this.formData.orgType === 'SalesRegion'){
					this.applyHanlder()
				}else if(this.$utils.isEmpty(this.formData.approverId)){
					// 选择审批人
					this.show = true
				}else{
					this.applyHanlder()
				}
			},
			async applyHanlder(){
				try {
					this.$utils.showLoading('提交中...');

					let url = `action/link/annualQuotaApplication/authorizedSubmit`;
					let param = this.type === 'authorized' ? this.$utils.deepcopy(formAuth) : this.$utils.deepcopy(fromOrg)
					if(this.type === 'orgAuthorized'){
						url = 'action/link/orgQuotaApply/orgAuthSubmit'
					}
					Object.keys(param).forEach(key=>{
						param[key] = this.formData[key]
					})
                    if(this.pageParam.from === 'list'){
                        param.attr1 = 'Direct'
                    }
					let {success} = await this.$http.post(url, param);
					this.$utils.hideLoading()
					if (success) {
						this.$nav.back({quota: true});
					}

				} catch (e) {
					this.$utils.hideLoading()
				}
			},
			/**
			 * @description: 获取申请记录
			 * @author: 邓佳柳
			 * @Date: 2025-02-12
			 */
			async getApplyInfo() {
				return new Promise(async (resolve, reject) => {
					try {
						// 授权配额查询与组织授权配额查询
						const urlList = {
							authorized:'action/link/annualQuotaDetails/queryById',
							orgAuthorized:'/action/link/orgQuotaApply/queryById'
						}
						let url = urlList[this.type];
						let params = {
							id: this.id
						};
                        let { result } = await this.$http.post(url, params);
                        if (this.type === 'orgAuthorized') {
                            let { applyQuotaBalanceQty=0, applyQuotaTotal=0,applyFstName } = result
							console.log(applyQuotaBalanceQty, applyQuotaTotal, applyFstName)
                            Object.assign(result,{firstName:applyFstName, authQuotaTotal:applyQuotaTotal,monthAuthQuotaBalanceQty:applyQuotaBalanceQty})
                        }
						resolve(result);
					} catch (e) {
						console.log(e);
					}
				});
			},
			dialogHide() {
				this.show = false;
			},
			async cfmApprover(){
				this.show = false;
				if(this.$utils.isEmpty(this.formData.approverId)){
					this.$message.error('请选择审批人');
					return;
				}
				this.applyHanlder()
            },
            /**
             * @description: 查指定区域授权配额余额与总额
             * @author: 邓佳柳
             * @Date: 2025-04-15
             * @param {*} areaCode
             */			
            async getOrgQuotaTotal(areaCode) {
                try {
                    let url = '/action/link/quotaAreaAuth/queryByExamplePage'
                    let param = {
                        prodPartCode:this.formData.prodPartCode,
                        authMonth:this.formData.month,
                        areaCode:areaCode,
                        fiscalYear:this.formData.fiscalYear
                    }
                    let { success, rows } = await this.$http.post(url, param)
                    if(success && rows.length){
                        const {authQuotaTotal=0, monthAuthQuotaBalanceQty=0} = rows[0]
                        this.$set(this.formData,'authQuotaTotal',authQuotaTotal)
                        this.$set(this.formData,'monthAuthQuotaBalanceQty',monthAuthQuotaBalanceQty)
                    }
                } catch (e) {}

            },
			/**
			 * @description: 查询组织类型
			 * @author: 邓佳柳
			 * @Date: 2025-04-16
			 */		
			checkAcctOrgType(){
				return new Promise((resolve,reject)=>{
					try{

					let url = 'action/link/annualQuotaApplication/checkAcctOrgType'
					let params ={
						id:this.formData.acctId
					}
					this.$http.post(url,params).then(res=>{
						resolve(res.success)
                	})
					}catch(e){
						resolve(false)
					}
				})
				
			},
			/**
			 * @description: 获取上级默认审批人
			 * @author: 邓佳柳
			 * @Date: 2025-04-23
			 */			
			async getDefaultApprover(){
				// 片区上级为品牌公司不需要指定
				if(this.formData.orgType === 'SalesRegion') return 
				try{
				let url = 'action/link/orgnization/queryParentByOrgCode'
				let param ={}
				if(this.type === 'authorized'){
					param.orgCode = this.pageParam.data.orgCode //终端所属组织编码
					param.parentType = 'SalesArea' //SalesArea
				}else{
					param.orgCode = this.formData.applyOrgCode //当前组织编码:发起申请组织code
					let orgType = this.formData.orgType //发起申请组织类型
					let parentType = {
						SalesArea: 'SalesCity',
						SalesCity: 'SalesRegion',
						SalesRegion: 'BranchCompany', //指定审批人
					}
					param.parentType = parentType[orgType]//上级组织类型
					}
					let res = await this.$http.post(url,param)
					if(res.rows.length === 1){
						this.formData.approverId = res.rows[0].userId 
					}
					console.log(param,res)
				}catch(e){
					console.log(e)
				}
			},
			/**
			 * @description: 批准授权配额输入校验
			 * @author: 邓佳柳
			 * @Date: 2025-04-29
			 */	
			 onAppQuaChange(val){
				let { quotaQuantity } = this.formData
				let reg =  /^[1-9]\d*$/
				if(this.$utils.isEmpty(val)){
					this.tips = ''
				}else if(!(reg.test(val))){
					this.tips = '请输入正整数'
				}else if(Number(val) > Number(quotaQuantity)){
					this.tips = '“批准授权配额数量”不能大于“申请授权配额数量”'
				}else{
					this.tips = ''
				}
				
			 }		


		}
	};
</script>
<style lang="scss">
	.annual-quota-tequ-page {
		padding-bottom: 400px;
		padding-top: 10px;
		.row-content {
			font-size: 28px;
			background: #ffffff;
			padding: 20px;
			margin: 20px;
			border-radius: 20px;
			.tips {
				font-size: 22px;
				color: red;
				background: #fff;
				line-height: 24rpx;
				margin-bottom: 20px;
				text-align: right;
			}
		}
		.bottom-btn {
			padding-top: 16px;
			.sure-btn {
				border-radius: 60px;
				margin-right: 24px;
				margin-left: 24px;
			}
		}
		.link-auto-list-no-more {
			display: none !important;
		}
		.dialog-bottom .link-dialog-foot-custom {
			width: auto;
		}
		.model-title {
			.title {
				font-family: PingFangSC-Regular, serif;
				font-size: 32px;
				color: #262626;
				letter-spacing: 0;
				text-align: center;
				line-height: 96px;
				height: 96px;
				width: 90%;
				padding-left: 0 !important;
				margin-right: 80px;
				margin-left: 10vw;
			}
			.icon-close {
				color: #bfbfbf;
				font-size: 48px;
				line-height: 96px;
				height: 96px;
				margin-right: 30px;
				float: right;
			}
		}
		.select-box {
			border-bottom: 1px solid #f2f2f2;
			padding: 0 20px;
			.select-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				color: #bfbfbf;
				font-size: 28px;
				line-height: 28px;
				.select-item-left {
					color: #262626;
				}
			}
		}
		.info-item {
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			font-size: 28px;
			line-height: 48px;
    		margin: 24px 0;
			color: #333333;
		}
	}
</style>
