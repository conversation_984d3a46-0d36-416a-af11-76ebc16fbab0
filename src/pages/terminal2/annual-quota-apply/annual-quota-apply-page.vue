<!--
 年度配额申请（国窖）
 <AUTHOR>
 @date	2024/12/09
-->
<template>
     <link-page class="annual-quota-apply-page" ref="page">
         <view class="row-content">
            <quota-form-info :formData="formData" @validate="validateBeforeSubmit" :message="message" :brandCompanyCode="5600"/>
         </view>
         <!-- 计划外与配额调整申请类型 展示年度配额明细 -->
         <view class="row-content" v-if="type==='edit' || type==='out'">
            <view class="row-title is-required line">年度配额明细</view>
            <quota-detail-card title="计划内配额明细" type="in" :data="details"/>
            <quota-detail-card title="计划外配额明细" type="out" :data="details"/>
         </view>

         <view class="row-content">
             <view :class="['row-title',{'is-required':formData.quotaType==='BaisicQuotaApplication'},'line']">协议信息</view>
             <view>
                <!-- parentId 配额申请记录id -->
                 <lnk-img-watermark :parentId="formData.id"
                    ref="imgList"
                    :delFlag="true"
                    moduleType="agrAgreement"
                    moduleName="年度配额协议照片"
                    @imgUploadSuccess="imgUploadSuccess"
                    @imgDeleteSuccess="imgDeleteSuccess"
                    :newFlag="true"/>
             </view>
         </view>
         <link-sticky class="bottom-btn">
             <link-button class="sure-btn"  @tap="submit" autoLoading :throttleTimer="10">提交</link-button>
         </link-sticky>
     </link-page>
</template>

<script>
    definePageConfig({
        navigationBarTitleText: '年度配额申请'
    })
    import LnkImgWatermark from '../../core/lnk-img-watermark/lnk-img-watermark';
    import QuotaFormInfo from './components/quota-form-info.vue';
    import quotaDetailCard from './components/quota-detail-card.vue';

    export default {
        name: 'annual-quota-apply-page',
        components: {LnkImgWatermark,QuotaFormInfo, quotaDetailCard},
        data(){
            return {
                formData: { // 配额表单对象
                    id: '',
                    acctCode:'',
                    acctName:'',
                    prodPartCode:'',
                    fiscalYear:2025,
                },
                type:this.pageParam.type, //申请类型，必传 ：new , edit , out
                imgList:[],//附件图片
                details:{},//配额详情
                basicId:undefined, //基础配额ID
				message:'',//提示消息通过接口校验
                limit:'N',//数量限制
            }
        },
        methods: {
            /**
             * @description: 提交按钮-根据申请类型提交不同的配额申请
             * @author: 邓佳柳
             * @Date: 2024-12-12
             */
            submit(){
                if(!this.checkWaterImg()) return
                switch(this.formData.quotaType){
                    case 'BaisicQuotaApplication':
                        this.baseQuotaApplication();
                        break;
                    case 'QuotaAdjustApplication':
                        this.quotaAdjustApplication();
                        break;
                    case 'UnplannedQuotaApplication':
                        this.unplanedQuotaApplication()
                        break;
                    default:
                        console.log('申请类型未知')
                        break;
                }

            },
            imgUploadSuccess(imgList){
                this.imgList = imgList
            },
            imgDeleteSuccess(imgList){
                this.imgList = imgList
            },
            /**
             * @description: 基础配额申请
             * @author: 邓佳柳
             * @param {*} id 默认生成
             * @param {*} acctId  终端id
             * @param {*} fiscalYear 财年
             * @param {*} prodPartCode  品项(业务小类编码)
             * @param {*} quotaQuantity  配额数量
             * @Date: 2024-12-12
             */
            async baseQuotaApplication(){
                //  表单数据检查
                let {id,acctId,fiscalYear, prodPartCode,quotaQuantity} = this.formData
                // 所属财年：必填
                if(!fiscalYear){
                    this.$message.error('请选择所属财年')
                    return
                }
                // 所属品项：必填
                if(!prodPartCode){
                    this.$message.error('请选择所属品项')
                    return
                }

                // 【协议信息】：附件，必填；
                if(this.$utils.isEmpty(this.imgList)){
                    this.$message.error('请上传协议信息')
                    return
                }
                let url = '/action/link/annualQuotaDetails/basicSubmit'
                let params = {
                    id,acctId,fiscalYear,prodPartCode,quotaQuantity
                }
                try{
                    let {rows,success} = await this.$http.post(url,params)
                    if(success){
                        this.$nav.back({quota:true});
                    }

                }catch(e){

                }
            },
            /**
             * @description: 调整配额申请
             * @author: 邓佳柳
             * @param {*} id 默认生成
             * @param {*} quotaQuantity 配额数量
             * @Date: 2024-12-12
             */
            async quotaAdjustApplication(){
                // 年度配额余额
                // let {planBalance} = this.details
                // 调整配额
                let {id,quotaQuantity,acctName,prodPartCode,fiscalYear} = this.formData
                // 调整配额：正数整数调增，负数整数调减，绝对值＞0；
                // 填写负数整数时，限制绝对值小于计划内配额余额:
                // 【标准1】客户填写正数整数调整配额，需要满足0＜正数整数调整配额+基础配额≤100
                if(!/^[-]?[1-9]\d*$/.test(quotaQuantity)){
                    this.$message.error('提示：正数整数调增，负数整数调减，请输入正确的数字！')
                    return
                }

                // if(quotaQuantity<0&&Math.abs(quotaQuantity)> Math.abs(planBalance)){
                //     this.$message.error(`当前【${acctName}】【${prodPartCode}】【${fiscalYear}】年度配额余额【${planBalance}】，不允许调减数量超过年度配额余额！`)
                //     return
                // }
                let url = `/action/link/annualQuotaDetails/quotaAdjustApply?basicId=${this.basicId}`
                let params = { id, quotaQuantity:Number(quotaQuantity) }
                try{
                    let {rows,success} = await this.$http.post(url,params)
                    if(success){
                        this.$nav.back({quota:true});
                    }

                }catch(e){

                }

            },
            /**
             * @description: 计划外配额申请
             * @author: 邓佳柳
             * @param {*} id 默认生成
             * @param {*} acctId  终端id
             * @param {*} fiscalYear 财年
             * @param {*} prodPartCode  品项(业务小类编码)
             * @param {*} quotaQuantity  配额数量
             * @param {*} quotaType  配额类型
             * @Date: 2024-12-12
             */
            async unplanedQuotaApplication(){
                let {id,acctId,fiscalYear,prodPartCode,quotaType,quotaQuantity} = this.formData

                // 计划外配额没有数量限制，但为正数整数；
                if(!/^[1-9]\d*$/.test(quotaQuantity)){
                    this.$message.error('请填写正整数')
                    return
                }
                let url = `/action/link/annualQuotaDetails/unplannedSubmit?basicQuotaDetailId=${this.basicId}`
                let params = {
                    id,acctId,fiscalYear,prodPartCode,quotaQuantity,quotaType
                }
                try{
                    let {rows,success} = await this.$http.post(url,params)
                    if(success){
                        this.$nav.back({quota:true});
                    }

                }catch(e){

                }
            },
            /**
             * 校验水印相机是否上传完毕
             */
             checkWaterImg() {
                let check = this.$refs['page'].utils.checkImages();
                if(!check) this.$showError('图片上传中!!')
                return check
            },
            /** 
             * @description:获取配额详情
             * @author: 邓佳柳
             * @Date: 2024-12-18
             */
             async getQuotaDetails(){
                let url ='/action/link/annualQuotaDetails/queryQuotaDetailsByAcctId'
                let params = {
                    acctId:this.formData.acctId,
                    fiscalYear:this.formData.fiscalYear,
                    prodPartCode:this.formData.prodPartCode
                }
                let {rows} = await this.$http.post(url,params)
                this.details = rows[0] || {}
            },
            /**
             * @description: 获取基础配额
             * @author: 邓佳柳
             * @Date: 2025-01-03
             */
            async getBasicInfo(){
                return new Promise(async(resolve) =>{
                    try{
                        let url ='/action/link/annualQuotaDetails/queryByExamplePage'
                        let params = {
                            filtersRaw: [
                                {id:'acctId',property:'acctId',value: this.formData.acctId, operator:'='},
                                {id:'prodPartCode',property:'prodPartCode',value: this.formData.prodPartCode, operator:'='},
                                {id:'fiscalYear',property:'fiscalYear',value: this.formData.fiscalYear, operator:'='},
                                {id:'quotaType',property:'quotaType',value: 'BaisicQuotaApplication', operator:'='},
                            ]
                        }
                        let {rows,success} = await this.$http.post(url,params)
                        if(success){
                            resolve(rows)
                        }else{
                            resolve([])
                        }
                    }catch (e){
                        console.log('获取基础配额失败',e)
                    }
                })
                
            },
            /**
             * @description: 判断当前客户是否存在所选【财年】+【品项】的基础配额明细，如存在则弹窗提醒：当前【XX客户编码客户名称】已存在【财年】【品项】基础配额，是否跳转至已有基础配额页面？按钮：取消/跳转
             * 不存在则按照现有逻辑继续往下校验是否为标准1客户；
             * @author: 邓佳柳
             * @Date: 2025-01-13
             */ 
            async validateBeforeSubmit(){
                let list = await this.getBasicInfo()
                if(list.length >0){
                    let prodPartName = await this.$lov.getNameByTypeAndVal('PROD_BUS_S_CLASS', list[0].prodPartCode)
                    this.$dialog({
                        title: '提示',
                        cancelButton: true,
                        confirmText:'跳转',
                        content: `当前【${list[0].acctCode}】【${list[0].acctName}】已存在【${list[0].fiscalYear}】【${prodPartName}】基础配额，是否跳转至已有基础配额页面？`,
                        onConfirm: () => {
                            // 跳转详情
                            this.$nav.redirect('/pages/terminal2/annual-quota-apply/annual-quota-detail-page', {
                                data: list[0]
                            });
                            
                        },
                        onCancel: () => {
                            // 校验标准客户
                            this.checkAccntStandard()
                        }
                    })
                }else{
                    this.checkAccntStandard()
                }

            },
            /**
             * @description: 校验客户标准
             * @author: 邓佳柳
             * @Date: 2024-12-25 19:25:03
             */
            async checkAccntStandard() {
                try {

                    let url = '/action/link/annualQuotaDetails/checkAccntStandard'
                    let params = {
                        acctId:this.formData.acctId,
                        fiscalYear:this.formData.fiscalYear,
                        prodPartCode:this.formData.prodPartCode
                    }
                    let { success, type } = await this.$http.post(url, params, {
                    // 禁用自动处理错误逻辑，默认请求出错，会自动弹框显示错误，以及记录错误日志
                    autoHandleError: false,
                    handleFailed: (response) => {
                        console.log('手动处理错误', response)
                        // 弹框显示错误 标准2客户退出
                        this.$dialog({
                            title: '提示',
                            // 有的接口返回的错误提示不是 response.message，而是 response.result，这里要写准确了
                            content: response.message || response.result,
                            onConfirm: () => {
                                 this.$nav.back();
                            },
                        })
                    }
                    })
                    // 标准1-新开 :new
                    // 标准1-非核心 :nonCore
                    // 20241231 企业参数控制
                    if(success && this.limit!=='N'){
                        let message = `${type==='new'?'新开终端' : '非核心终端'}计划内配额上限${this.limit}件，超出申请计划外配额。`
                        if(this.formData.quotaType === 'UnplannedQuotaApplication'){
                            this.message = message
                        }
                    }

                } catch (error) {
                    console.error(error)
                }

            },
            /**
             * @description: 国窖年度基础配额申请最大件数限制。（整数，大于0，为N时不限制）
             * @author: 邓佳柳
             * @Date: 2024-12-31
             */
            async getLimit(){
                this.limit = await this.$utils.getCfgProperty('BASIS_QUOTA_MAX_QUANTITY');
                console.log(this.limit)
            }

        },
        /**
         * @description: 进入配额申请页面
         * @url /pages/terminal2/annual-quota-apply/annual-quota-apply-page
         * @param data 当前数据或终端数据
         * @param type new|edit|out 配额申请类型
         * @param isEditFlag true编辑|false新建
         * @Date: 2024-12-16
         */
        async created(){
            this.getLimit()
            // 页面传递数据
            let { type, data, isEditFlag} = this.pageParam
            // 配额申请基础数据
            let {acctCode,acctName,fiscalYear,prodPartCode} = data || {}
            this.$set(this.formData, 'acctCode',  acctCode);
            this.$set(this.formData, 'acctName',  acctName);
            if(['edit','out'].includes(type) || isEditFlag){
                this.$set(this.formData, 'fiscalYear',  fiscalYear);
                this.$set(this.formData, 'prodPartCode',  prodPartCode);
            }

            // 配额申请类型
            switch (type) {
                case 'new':
                    this.$taro.setNavigationBarTitle({title: '基础配额申请'});
                    this.$set(this.formData, 'quotaType', 'BaisicQuotaApplication');
                    break;
                case 'edit':
                    this.$taro.setNavigationBarTitle({title: '配额调整申请'});
                    this.$set(this.formData, 'quotaType', 'QuotaAdjustApplication');
                    this.message = '如若调减，请填写负数!注:调减配额不可低于年度配额余额!'
                    break;
                case 'out':
                    this.$taro.setNavigationBarTitle({title: '计划外配额申请'});
                    this.$set(this.formData, 'quotaType', 'UnplannedQuotaApplication');
                    break;
                default:
                    break;
            }

            // 编辑状态
            if(isEditFlag){
                // 客户id
                this.$set(this.formData, 'acctId',  data.acctId);
                // 记录id
                this.$set(this.formData, 'id',  data.id);
                this.$set(this.formData, 'quotaType', data.quotaType);
                this.$set(this.formData, 'quotaCode', data.quotaCode);
                this.$set(this.formData, 'quotaQuantity', data.quotaQuantity);

            }else{
                // 客户id 新建基础配额从终端
                let acctId = type ==='new' ? data.id : data.acctId
                this.$set(this.formData, 'acctId',  acctId);
                // 配额申请：新建需要生成申请记录id
                let newId = await this.$newId();
                this.$set(this.formData, 'id',  newId);
            }
            // 计划外配额与调整配额 需要基础配额id 和 基础配额明细
            if(this.formData.quotaType!=='BaisicQuotaApplication'){
                this.getQuotaDetails()

                let rows = await this.getBasicInfo()
                this.basicId = rows[0].id
            }
            console.table([
                { key: 'PAGE', val: this.pageParam },
                { key: '表单', val: this.formData },
                { key: 'basicId', val: this.basicId },
                { key: '编辑状态', val: isEditFlag },
            ]);

        }
    }
</script>

<style lang="scss">
.annual-quota-apply-page{
    padding-top: 10px;
    .blank {
        height: 24px;
        width: 100%;
    }
    .block {
        width: 100%;
        height: auto;
        background: #ffffff;
    }
    .row-content {
        font-size: 28px;
        background: #FFFFFF;
        padding: 20px;
        margin: 20px;
        border-radius: 20px;
        .link-form-item{
            .link-item{
                padding:10px 24px;
            }
        }
        .row-title {
            color: #262626;
            font-weight: bold;
        }
        .line{
            padding-bottom: 14px;
            border-bottom: 5px solid #f2f2f2;
        }
        .is-required{
            padding-left: 18px;
           &:before {
               content: '*';
               color: #FF5A5A;
               font-size: 32px;
               text-align: center;
               width: 24px;
               position: absolute;
               left: 34px;
           }
        }
    }
    .bottom-btn {
        padding-top: 16px;
        .sure-btn {
            box-shadow: 0 8px 24px 0 rgba(47, 105, 248, 0.50);
            width: 340px;
            height: 96px;
            margin-right: 24px;
            margin-left: 24px;
        }
    }
}
</style>
