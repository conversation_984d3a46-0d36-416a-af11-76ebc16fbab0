<!--
 * @Description: 配额明细卡片
 * @Author: 邓佳柳
 * @Date: 2024-12-18
-->
<template>
	<view class="plan-box" :style="{background: type === 'out' ? '#fdf4f5' : type === 'in' ? '#eff9ff': bgColor}">
		<slot><view class="plan-title">{{ title }}</view></slot>
		<view class="content-wrap">
			<view class="content-item" v-for="(item, index) in field" :key="index" @tap.stop="handler(item)">
				<view class="line"></view>
				<view class="num-item">
					<view :class="['val',{'textColor':item.url}]">{{ data[item.key] || item.default || 0 }}{{item.unit}}
						<link-icon v-if="item.tips" class="tips" icon="mp-info-lite" status="info" @tap.stop="showTips(item)"/>
					</view>
					<view class="label">{{ item.label }}</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		name: '',
		props: {
			showField: { //  定制展示项
                type: Array,
            },
			title: { // 标题
                type: String,
            },
			data: { //数据 必传
                type: Object,
                required: true,
            },
			type: {//默认展示类型 in|计划内 out|计划外
                type: String,
            },
            bgColor: { //背景色
                type: String,
                default:'#ffffff'
            }
		},
		computed: {
			field() {
                if(this.type === 'in'){
                    return this.inPlainDefaultFiled
                }else if(this.type === 'out'){
                    return this.outPlainDefaltFiled
                }else{
                    return this.showField.filter( i =>{
                        return !i.isShowFiled || i.isShowFiled && this.data[i.key]
                    })
                }
			},
		},
		data() {
			return {
				inPlainDefaultFiled: [
					{label: '计划内总额', key: 'planTotalQuantity'},
					{label: '计划内余额', key: 'planBalance'},
					{label: '基础配额', key: 'basicQuantity'},
					{label: '调整配额', key: 'adjustAppQuantity',url:'/pages/new-board/more-table-list-page.vue'},
					{label: '未执行扣减', key: 'unplanDeduction'},
					{label: '计划内下单', key: 'planOrder'},
					{label: '计划内占用', key: 'planOccupy'},
					{label: '计划内已执行', key: 'planExecute'}
				],
				outPlainDefaltFiled: [
					{label: '计划外配额', key: 'unplannedQuantity',url:'/pages/new-board/more-table-list-page.vue'},
					{label: '计划外余额', key: 'unplanBalance'},
					{label: '计划外下单', key: 'unplanOrder'},
					{label: '计划外占用', key: 'unplanOccupy'},
					{label: '计划外已执行', key: 'unplanExecute'}
				]
			};
		},
        methods:{
            handler(item){
                if(item.url){
                    this.$emit('click',item)
                }
                
            },
			showTips(item){
				this.$emit('show',item)
			}
        }
	};
</script>
<style lang="scss">
	.plan-box {
		border-radius: 20px;
		padding: 20px;
		margin: 20px 0;
		.content-wrap {
			display: flex;
			flex-wrap: wrap;
			margin-bottom: 24px;
			line-height: 60px;
			.content-item {
				margin-top: 24px;
				width: 33%;
				@include flex-start-center;

				.num-item {
					width: 100%;
					text-align: center;
                    font-size: 26rpx;
					height: 100%;
                    .textColor{
                        color: #2F69F8;
                    }
                    .label{
                        color: #666;
                    }
					.val{
						position: relative;
						.tips{
							position: absolute;
							right: 0;
							top: -30px;
							padding: 30px;
						}
					}
					
				}

				.line {
					width: 1px;
					height: 61px;
					margin: 6px 0;
					background-color: #c7c7c7;
				}

				&:nth-child(3n + 1) {
					.line {
						display: none;
					}
				}
			}
		}
	}
	.plan-title {
		color: #2f69f8;
	}
</style>
