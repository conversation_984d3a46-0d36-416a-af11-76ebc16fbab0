<!--
 * @Description: 年度配额申请表单组件-特曲
 * @Author: 邓佳柳
 * @Date: 2025-02-10
-->
<template>
	<view class="quota-form-tequ">
		<link-form ref="form" :value="formData">
			<link-form-item label="终端名称">
				<view class="info-val">{{ formData.acctName }}</view>
			</link-form-item>
			<link-form-item label="申请类型" required>
                <link-lov v-if="typeEditable" type="QUOTA_APPLY_TYPE" v-model="formData.quotaType" :excludeLovs="excludeLovs"/>
				<view class="info-val" v-else>{{ formData.quotaType | lov('QUOTA_APPLY_TYPE') }}</view>
			</link-form-item>
			<link-form-item label="所属财年" required>
				<view class="info-val">{{ formData.fiscalYear }}</view>
			</link-form-item>
			<link-form-item :label="quotaName" required>
				<link-input readonly v-model="formData.quotaQuantity" v-if="readonly" />
				<block v-else>
					<link-number v-model="formData.quotaQuantity" :min="1" />
				</block>
			</link-form-item>
            <link-form-item label="配额类型" v-if="formData.areaType && readonly">
				<view class="info-val">{{ formData.areaType | lov('TEQU_QUOTA_AREA_TYPE') }}</view>
			</link-form-item>
		</link-form>
	</view>
</template>
<script>
	export default {
		name: '',
		components: {},
		props: {
			formData: {
				type: Object, //表单收集对象
				require: false,
				default: () => {
					return {
						acctName: '', //终端名称
						quotaType: '', //申请类型
						fiscalYear: '', //所属财年
						quotaQuantity: '' //配额数量
					};
				}
			},
			readonly: {
				type: Boolean, //查看状态
				require: false,
				default: false
			}
		},
		data() {
			return {
				excludeLovs: ["BaisicQuotaApplication", "QuotaAdjustApplication"], //申请类型
				typeEditable: false, //选择配额申请类型
			};
		},
		computed: {
			quotaName() {
				let {quotaType} = this.formData;
				if (quotaType === 'AuthorizedQuota') {
					return '授权配额';
				} else if (quotaType === 'UnplannedQuotaApplication') {
					return '计划外配额';
				} else {
					return '配额申请';
				}
			},
		},
		async created() {
            let lovs = await this.$lov.getLovByType('QUOTA_APPLY_TYPE')
            this.excludeLovs = lovs.filter(item => ['UnplannedQuotaApplication','AuthorizedQuota'].indexOf(item.val) === -1 ).map(v=>v.val)
            this.typeEditable = this.$utils.isEmpty(this.formData.quotaType) && !this.readonly
		},
	};
</script>
<style lang="scss">
	.quota-form-tequ {
		.info-val {
			color: #3b4144;
		}
	}
</style>
