<!--
 * @Description: 年度配额申请表单组件
 * 5600 国窖
 * 5151 窖龄
 * 5137 特曲
 * 5161 大成
 * @Author: 邓佳柳
 * @Date: 2024-12-10
-->
<template>
	<view class="quota-form-info">
		<link-form ref="form" :value="formData">
			<link-form-item label="申请编码" v-if="formData.hasOwnProperty('applyCode')">
				<link-input v-model="formData.applyCode" readonly />
			</link-form-item>
			<link-form-item label="申请人" v-if="formData.hasOwnProperty('firstName')">
				<link-input v-model="formData.firstName" readonly />
			</link-form-item>
			<!-- 查看状态下才会有配额申请编码 -->
			<link-form-item label="配额申请编码" v-if="formData.quotaCode">
				<link-input v-model="formData.quotaCode" readonly />
			</link-form-item>
			<link-form-item label="终端编码" v-if="formData.hasOwnProperty('acctCode')">
				<link-input v-model="formData.acctCode" readonly />
			</link-form-item>
			<link-form-item label="终端名称" v-if="formData.hasOwnProperty('acctName')">
				<view class="info-val">{{ formData.acctName }}</view>
			</link-form-item>
			<!-- 默认不可编辑 必传-->
			<link-form-item label="申请类型" required v-if="[5151,5600,5137,5910].includes(brandCompanyCode) && formData.hasOwnProperty('quotaType')">
				<view class="info-val" v-if="brandCompanyCode=='5151'">
					{{ formData.quotaType | lov("JL_QUOTA_APPLY_TYPE") }}
				</view>
				<view class="info-val" v-else>
					{{ formData.quotaType | lov("QUOTA_APPLY_TYPE") }}
				</view>
			</link-form-item>
			<link-form-item label="所属财年" :required="[5151,5600].includes(brandCompanyCode)" v-if="[5151,5600,5161].includes(brandCompanyCode)">
				<link-input v-model="formData.fiscalYear" v-if="!yearEditable || readonly" readonly />
				<picker @change="yearChange" :value="yearIndex" range-key="name" :range="yearList" v-else>
					<link-input type="text" v-model="formData.fiscalYear" :value="yearList[yearIndex]" :readonly="true" suffixIcon="mp-arrow-right" />
				</picker>
			</link-form-item>
			<!-- 配额产品类别/选择品项-->
			<link-form-item :label="prodLabel" :required="[5151,5600,5137,5910].includes(brandCompanyCode)">
				<view @tap="addProd">
					<text class="info-val" v-if="formData.prodPartCode" @tap='choseOwnProd'>
						<text v-if="[5151,5161].includes(brandCompanyCode)">{{ formData.prodPartCode | lov('PROD_BUS_M_CLASS') }}</text>
						<text v-else>{{ formData.prodPartCode | lov('PROD_BUS_S_CLASS') }}</text>
					</text>
					<view v-else class="link-input-placeholder" @tap='choseOwnProd'>请选择品项</view>
				</view>
				<view class="link-item-icon" v-if="prodEditable || canShooseProd"><link-icon icon="mp-arrow-right" /></view>
			</link-form-item>
			<link-form-item label="实时库存" v-if="brandCompanyCode ===5161 && formData.quotaType == 'AdjustStockApply'">
				<view class="info-val">{{ formData.stockQty }}</view>
			</link-form-item>
			<link-form-item label="所属波段" v-if="brandCompanyCode ===5161">
				<view class="info-val">{{ formData.bandCode | lov('QUOTA_BAND_CODE')}}</view>
			</link-form-item>
			<link-form-item :label="['QuotaAdjustApplication', 'PlannedQuotaApplication'].includes(formData.quotaType) && brandCompanyCode==5137 ? '所属财年' : '所属年月'" v-if="[5137,5910].includes(brandCompanyCode)" required>
				<view class="info-val">{{ ['QuotaAdjustApplication', 'PlannedQuotaApplication'].includes(formData.quotaType) && brandCompanyCode==5137 ? formData.fiscalYear : formData.month }}</view>
			</link-form-item>
            <link-form-item label="申请日期" v-if="[5137].includes(brandCompanyCode) && ['QuotaAdjustApplication', 'PlannedQuotaApplication'].includes(formData.quotaType)">
            	<view class="info-val">{{ formData.created }}</view>
            </link-form-item>
            <link-form-item label="组织类型" v-if="formData.orgType && brandCompanyCode ===5137">
				<view class="info-val">{{ formData.orgType | lov('ORG_TYPE') }}</view>
			</link-form-item>
			<link-form-item label="当前组织累计授权配额" v-if="formData.hasOwnProperty('authQuotaTotal') && brandCompanyCode ===5137">
				<view class="info-val">{{ formData.authQuotaTotal }}</view>
			</link-form-item>
			<link-form-item label="授权配额余额" v-if="formData.hasOwnProperty('monthAuthQuotaBalanceQty') &&  brandCompanyCode ===5137">
				<view class="info-val">{{ formData.monthAuthQuotaBalanceQty }}</view>
			</link-form-item>
			<!-- 配额数量 -->
			<link-form-item :label="quotaName" required>
				<link-input readonly v-model="formData.quotaQuantity" v-if="readonly" />
				<block v-else>
					<!-- 小数/负数  -->
					<link-input v-if="['QuotaAdjustApplication'].includes(formData.quotaType)&&$utils.isEmpty(formData.accntStand)" v-model="formData.quotaQuantity" @input="handlerInput"/>
					<input class="ipt" v-else-if="['AdjustStockApply'].includes(formData.quotaType)" v-model="formData.quotaQuantity" @input="handlerInput"/>
					<!-- 正整数-->
					<link-number v-else v-model="formData.quotaQuantity" :min="1" />
				</block>
			</link-form-item>
			<view class="tips" v-if="message && !readonly">{{ message }}</view>
			<!-- 提交成功才会有审批状态 -->
			<link-form-item label="配额状态" v-if="formData.approvalStatus">
				<view class="info-val">{{ formData.approvalStatus | lov('ANNUAL_QUOTA_STATUS') }}</view>
			</link-form-item>
            <link-form-item label="批准配额数量" v-if="formData.appointQuantity">
            	<view class="info-val">{{ formData.appointQuantity}}</view>
            </link-form-item>
		</link-form>

		<!-- 国窖选择产品业务小类、窖龄选择产品业务中类 -->
		<link-dialog ref="prodBottom" position="bottom" height="85vh" class="dialog-bottom" noPadding v-model="show">
			<view class="model-title">
				<view class="iconfont icon-close" @tap="dialogHide"></view>
				<view class="title">产品</view>
			</view>
			<view class="dialog-content" style="height: calc(100% - 44px);">
				<scroll-view scroll-y="true" :style="{height: 'calc(100% - 75px)'}">
					<link-radio-group v-model="checkedProd">
						<link-auto-list :option="brandCompanyCode=='5151' ? productMOption : productOption" hideCreateButton :scrollContent="true" :searchInputBinding="{props:{placeholder:'产品编码与名称'}}">
							<template slot-scope="{data, index}">
								<view slot="note">
									<item :key="index" :data="data" :arrow="false" style="padding: 0 15px">
										<template v-if="brandCompanyCode=='5151'">
											<link-checkbox :val="data.prodBusMClass" slot="thumb" toggleOnClickItem />
											<view class="select-left">
												<view class="store-name">{{ data.prodBusMClass | lov('PROD_BUS_M_CLASS') }}</view>
											</view>
										</template>
										<template v-else>
											<link-checkbox :val="data.prodBusSClass" slot="thumb" toggleOnClickItem />
											<view class="select-left">
												<view class="store-name">{{ data.prodBusSClass | lov('PROD_BUS_S_CLASS') }}</view>
											</view>
										</template>
									</item>
								</view>
							</template>
						</link-auto-list>
					</link-radio-group>
				</scroll-view>
				<view class="link-dialog-foot-custom">
					<link-button shadow @tap="choseProd" label="确定" style="width: 90vw" />
				</view>
			</view>
		</link-dialog>
	</view>
</template>
<script>
	export default {
		name: 'quota-form-info',
		props: {
			formData: {
				type: Object, //表单收集对象
				require: false,
				default: () => {
					return {
						quotaCode: '', //配额申请编码 编辑状态不传
						approvalStatus: '', //审批状态 编辑状态不传
						acctId: '', //终端id 必传
						acctCode: '', //终端编码
						acctName: '', //终端名称
						quotaType: '', //申请类型 必传
						fiscalYear: '', //所属财年
						prodPartCode: '', //所属品项
						quotaQuantity: '' //配额数量
					};
				}
			},
			readonly: {
				type: Boolean, //查看状态
				require: false,
				default: false
			},
			message: String, //提示消息通过接口校验
			brandCompanyCode: {
				type: Number,
				require: false
			}
		},
		data() {
			const productOption = new this.AutoList(this, {
				module: 'action/link/saleCategory',
				url: {
					queryByExamplePage: '/action/link/saleCategory/queryProdBusSClass'
				},
				sortOptions: null,
				param: {
					accntId: this.formData.acctId //终端id 必传
				},
				searchFields: ['prodBusSClass', 'prodBusSName'],
				filterOption: null,
				hooks: {
					async beforeLoad({param}) {
						if (param.filtersRaw.length > 0) {
							// 默认搜索
							param.prodBusSName = param.filtersRaw[0].value;
						}
						delete param.sort;
						delete param.order;
					}
				}
			});
            const prodOption = new this.AutoList(this, {
                    module: '/link/quotaProdlnfo',
                    url: {
                    	queryByExamplePage: '/action/link/quotaProdInfo/queryProdPartCode'
                    },
                    param: {companyCode:'5137'},
                    hooks: {
                    	afterLoad(data) {
                    		console.log(data, 33333333)
                            data.rows.forEach(async (i)=>{
                                i.prodPartName = await this.$lov.getNameByTypeAndVal('PROD_BUS_S_CLASS',i.prodPartCode)
                            })
                    	}
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} title={data.prodPartCode} data={data}>{data.prodPartName}</item>
                        )
                    }
                })
			// 窖龄产品中类
			const productMOption = new this.AutoList(this, {
				url: {
					queryByExamplePage: '/action/link/saleCategory/queryProdBusMClass'
				},
				sortOptions: null,
				param: {
					accntId: this.formData.acctId //终端id 必传
				},
				searchFields: ['prodBusMClass', 'prodBusMName'],
				filterOption: null,
				hooks: {
					async beforeLoad({param}) {
						if (param.filtersRaw.length > 0) {
							// 默认搜索
							param.prodBusMName = param.filtersRaw[0].value;
						}
						delete param.sort;
						delete param.order;
					}
				}
			});
			return {
                prodOption,//品项
				yearList: [], //选择财年选项
				yearIndex: 0, //默认选中当前财年索引
				productOption, // 产品弹窗选择配置参数
				show: false, //产品弹框显示
				checkedProd: null, //选中项
				productMOption,//窖龄产品中类
				canShooseProd:false,//选择品项
			};
		},
		computed: {
			// 不同配额类型对应label标签
            quotaName() {
                let { quotaType, orgType } = this.formData || {}
				let nameMap = {
					'BaisicQuotaApplication': '年度基础配额',
					'QuotaAdjustApplication': '调整配额',
					'UnplannedQuotaApplication': '计划外配额',
					'PlannedQuotaApplication': '配额计划',
					'AuthorizedQuota':'申请授权配额数量', //授权配额
					'ExceedQuotaApply':'调整进货配额',
					'AdjustStockApply':'库存调整数量'
				};
				return quotaType ? nameMap[quotaType] : orgType ? '申请授权配额数量': '申请数量';
			},
			// 财年 普通配额可以选择财年，计划外与调整配额默认为普通配额所属财年
			yearEditable() {
				let {quotaType,approvalStatus} = this.formData;
				return ['BaisicQuotaApplication', 'PlannedQuotaApplication'].includes(quotaType)&&this.$utils.isEmpty(approvalStatus) ? true : false;
			},
			// 品项 普通配额可以选择品项，计划外与调整配额默认为普通配额申请对应品项
			prodEditable() {
				let {quotaType,approvalStatus} = this.formData || {};
				return ['BaisicQuotaApplication', 'PlannedQuotaApplication'].includes(quotaType)&&this.$utils.isEmpty(approvalStatus) && !this.readonly;
			},
			// 配额产品label
			prodLabel() {
				return [5151,5161,5137,5910].includes(this.brandCompanyCode) ? '配额产品类别' : '所属品项';
			}
		},
		watch: {
			formData: {
				handler(v, o) {
					if (this.$utils.isNotEmpty(v) && this.$utils.isEmpty(o)) {
						// 财年选项是否初始化
						if (this.yearList.length === 0) this.calcMinYear();
						// 获取财年对应的索引
						let index = this.yearList.findIndex((item) => item.val == this.formData.fiscalYear);
						this.yearIndex = index < 0 ? 0 : index;
						// 财年为空的情况初始化
						if (index < 0) this.$set(this.formData, 'fiscalYear', this.yearList[this.yearIndex].val);
					}
				},
				deep: true,
				immediate: true
			}
		},
		created() {
			this.calcMinYear();
			let {quotaType,prodPartCode} = this.formData || {};
			this.canShooseProd = ['AuthorizedQuota'].includes(quotaType) && this.$utils.isEmpty(prodPartCode) && !this.readonly
		},
		mounted() {},
		methods: {
            /**
             * @description: 配额列表进入手动选择品项
             * @author: 谭少奇
             * @Date: 2025-04-17
             */
            async choseOwnProd(){
                if(this.formData.from !== 'list') return
                const data = await this.$object(this.prodOption)
                this.formData.prodPartCode = data.prodPartCode
                this.$emit('getOrgQuotaTotal',this.formData.applyOrgCode)
            },
			/**
			 * @description: 计算最小财年 （一个财年为11.1日-10.31日）
			 * @author: 邓佳柳
			 * @Date: 2024-12-10
			 */
			calcMinYear() {
				let year = new Date().getFullYear();
				let month = new Date().getMonth() + 1;
				let curFiscalYear = month > 10 ? year + 1 : year;
				this.yearList = [];
				for (let i = 0; i < 10; i++) {
					this.yearList.push({name: `${curFiscalYear + i}年`, val: curFiscalYear + i});
				}
			},
			/**
			 * @description: 选择财年
			 * @author: 邓佳柳
			 * @Date: 2024-12-11
			 * @param {*} e
			 */
			yearChange(e) {
				this.yearIndex = Number(e.detail.value);
				this.$set(this.formData, 'fiscalYear', this.yearList[this.yearIndex].val);
				if (this.$utils.isNotEmpty(this.formData.prodPartCode)) {
					this.$emit('validate');
				}
			},
			/**
			 * @description: 选择品项
			 * @author: 邓佳柳
			 * @Date: 2024-12-10
			 */
			async addProd() {
				if (this.prodEditable || this.canShooseProd){
					this.show = true;
				}
			},
			dialogHide() {
				this.show = false;
			},
			choseProd() {
				console.log('choseProd', this.checkedProd);
				this.show = false;
				if (this.$utils.isNotEmpty(this.checkedProd)) {
					this.formData.prodPartCode = this.checkedProd;
					this.$emit('validate');
				}
			},
			handlerInput(e){
				this.$emit('input',e)
			},
		}
	};
</script>
<style lang="scss">
	.quota-form-info {
		.row-title-right {
			text-align: right;
			color: #2f69f8;
			padding: 20px;
			height: 36px;
		}
		.perform-case-list-item {
			background: #ffffff;
			border-radius: 16px;
		}
		.media-list {
			.num-view {
				background: #a6b4c7;
				border-radius: 8px;
				line-height: 40px;
				display: inline-block;
				.num {
					font-size: 28px;
					color: #ffffff;
					letter-spacing: 0;
					line-height: 40px;
					padding: 2px 8px;
				}
			}
		}
		.dialog-bottom .link-dialog-foot-custom {
			width: auto;
		}
		.model-title {
			.title {
				font-family: PingFangSC-Regular, serif;
				font-size: 32px;
				color: #262626;
				letter-spacing: 0;
				text-align: center;
				line-height: 96px;
				height: 96px;
				width: 90%;
				padding-left: 0 !important;
				margin-right: 80px;
				margin-left: 10vw;
			}
			.icon-close {
				color: #bfbfbf;
				font-size: 48px;
				line-height: 96px;
				height: 96px;
				margin-right: 30px;
				float: right;
			}
		}
		.select-left {
			width: 100%;
			padding-left: 10px;
			.store-name {
				width: 100%;
				font-family: PingFangSC-Regular, serif;
				font-size: 28px;
				color: #262626;
				letter-spacing: 0;
				line-height: 28px;
			}
		}
		.tips {
			font-size: 24px;
			color: red;
			background: #fff;
			line-height: 24rpx;
			padding: 20px 12px;
			text-align: right;
		}
		.info-val {
			color: #3b4144;
		}
		.ipt{
			text-align: right;
		}
	}
</style>
