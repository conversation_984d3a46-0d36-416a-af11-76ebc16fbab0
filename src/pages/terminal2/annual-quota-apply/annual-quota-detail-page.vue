<!--
 年度配额申请详情（国窖）
 <AUTHOR>
 @date	2024/12/09
-->
<template>
     <link-page class="annual-quota-detail-page">
         <!-- 审批记录 -->
         <approval-history-point :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)"></approval-history-point>

         <!-- 配额申请 只读-->
        <quota-form-info :formData="annualQuota" :readonly="true" :brandCompanyCode="5600"/>

         <view class="row-content" v-if="isShowDetails">
             <view class="row-title">年度配额明细</view>
             <quota-detail-card title="计划内配额明细" type="in" :data="details" @click="toDetail"/>
             <quota-detail-card title="计划外配额明细" type="out" :data="details" @click="toDetail"/>
         </view>
         <view class="row-content">
             <view :class="['row-title',{'is-required':annualQuota.quotaType==='BaisicQuotaApplication'},'line']">协议信息</view>
             <view v-if="annualQuota.id">
                 <lnk-img-watermark :parentId="annualQuota.id"
                    ref="imgList"
                    :delFlag="$utils.isEmpty(approvalId) && annualQuota.approvalStatus !== 'Submitted'"
                    :historyDelFlag = "false"
                    moduleType="agrAgreement"
                    moduleName="年度配额协议照片"
                    @imgUploadSuccess="imgUploadSuccess"
                    :newFlag="$utils.isEmpty(approvalId) &&  annualQuota.approvalStatus !== 'Submitted'"/>
             </view>
         </view>
         <link-sticky class="bottom-btn" v-if='showBtn'>
             <link-button class="sure-btn" v-if="isShowAdjustBtn" mode="stroke" @tap="editContent('edit')" autoLoading :throttleTimer="10">配额调整</link-button>
             <link-button class="sure-btn" size="normal" @tap="editContent('out')" autoLoading :throttleTimer="10">计划外配额</link-button>
         </link-sticky>

         <!-- 审批操作 -->
         <link-sticky v-if="$utils.isNotEmpty(approvalId)">
            <approval-operator :approvalId="approvalId"></approval-operator>
        </link-sticky>
     </link-page>
</template>

<script>
    definePageConfig({
        navigationBarTitleText: '年度配额申请'
    })
    import LnkImgWatermark from '../../core/lnk-img-watermark/lnk-img-watermark';
    import ApprovalHistoryPoint from "@/pages/lzlj/approval/components/approval-history-point";
    import ApprovalOperator from "@/pages/lzlj/approval/components/approval-operator";
    import QuotaFormInfo from './components/quota-form-info.vue';
    import quotaDetailCard from './components/quota-detail-card.vue';
    export default {
        name: 'annual-quota-detail-page',
        components: {LnkImgWatermark, ApprovalHistoryPoint, ApprovalOperator,QuotaFormInfo, quotaDetailCard},
        data(){
            const userInfo = this.$taro.getStorageSync('token').result;
            return {
                userInfo,
                annualQuota: {},
                approvalId: undefined,//审批id
                id:undefined,//申请记录id
                isShowAdjustBtn: false,   //判断是否显示配额调整按钮 
                details:{},//配额详情
            }
        },
        computed:{
            showBtn(){
                return this.annualQuota.quotaType === 'BaisicQuotaApplication' && this.annualQuota.approvalStatus === 'Approved' && this.$utils.isEmpty(this.approvalId)
            },
            isShowAgreement(){
                return this.annualQuota.approvalStatus === 'Approved' && this.annualQuota.quotaType==='BaisicQuotaApplication'
            },
            isShowDetails(){
                return this.annualQuota.quotaType === 'BaisicQuotaApplication' && this.annualQuota.approvalStatus === 'Approved' &&  this.$utils.isEmpty(this.approvalId) || ['QuotaAdjustApplication','UnplannedQuotaApplication'].includes(this.annualQuota.quotaType)
            }
        },
        methods:{
            toDetail(data){
                if(['edit','out'].includes(this.pageParam.type)){
                    return
                }
                let headTag=[
                    {label: '配额申请编码', code: 'quotaCode', width: 240,color:'#2F69F8'},
                    {label: '终端名称', code: 'acctName'},
                    {label: '所属品项', code: 'prodPartCode',lovType:"PROD_BUS_S_CLASS"},
                    {label: '申请类型', code: 'quotaType',lovType:'QUOTA_APPLY_TYPE' },
                    {label: '所属财年', code: 'fiscalYear'},
                    {label: data.label, code: 'quotaQuantity'},
                    {label: '配额状态', code: 'approvalStatus', lovType:'ANNUAL_QUOTA_STATUS' }                   
                ]
                this.$nav.push('/pages/terminal2/annual-quota-apply/annual-quota-list-page.vue',{
                    url: '/action/link/annualQuotaDetails/queryByExamplePage',
                    param:{
                        filtersRaw: [
                            // {id:'acctCode',property:'acctCode',value: this.annualQuota.acctCode, operator:'='},
                            {id:'acctCode',property:'acctCode',value: this.annualQuota.acctCode, operator:'='},
                            {id:'prodPartCode',property:'prodPartCode',value: this.annualQuota.prodPartCode, operator:'='},
                            {id:'fiscalYear',property:'fiscalYear',value: this.annualQuota.fiscalYear, operator:'='},
                            {id:'quotaType',property:'quotaType',value: data.key==='unplannedQuantity' ? 'UnplannedQuotaApplication' : 'QuotaAdjustApplication', operator:'='},
                        ]   
                    },
                    data:{...data,isGuojiao:true},
                    headTag,
                    pageTitle: data.key==='unplannedQuantity' ? '计划外配额明细' : '调整配额申请明细',
                    cellClick: (data2) => {
                        if(['Rejected', 'Revoke'].includes(data2.approvalStatus)){
                            this.$nav.push('/pages/terminal2/annual-quota-apply/annual-quota-apply-page', {
                                data: {...data2},   
                                isEditFlag: true,
                                type:  data.key==='unplannedQuantity' ? 'out' : 'edit',
                            });
                        }else{
                            this.$nav.push('/pages/terminal2/annual-quota-apply/annual-quota-detail-page', {
                                data: {...data2},   
                                isEditFlag: false,
                                type:  data.key==='unplannedQuantity' ? 'out' : 'edit',
                            });
                        }
                    }
                });
            },
           
            toOutDetail(){
                console.log(data)
            },
            /**
             * 跳转年度配额申请新建页面
             * <AUTHOR>
             * @date 2014/12/10
             */
            editContent(type){
                this.type=type
                console.log('5455544554')
                this.$nav.push('/pages/terminal2/annual-quota-apply/annual-quota-apply-page', {
                    data: {...this.annualQuota},
                    isEditFlag: false,
                    type
                });
            },
            outContent(){

            },
            imgUploadSuccess(){

            },
            /** 获取配额明细
             * @description:
             * @author: 邓佳柳
             * @Date: 2024-12-18
             */
            async getQuotaDetails(){
                let url ='/action/link/annualQuotaDetails/queryQuotaDetailsByAcctId'
                let params = {
                    acctId:this.annualQuota.acctId,
                    fiscalYear:this.annualQuota.fiscalYear,
                    prodPartCode:this.annualQuota.prodPartCode
                }
                let {rows} = await this.$http.post(url,params)
                this.details = rows[0] || {}
            },
            /** 
             * @description: 1.根据id查询记录--2.获取明细/获取调整配额控制权限
             * @author: 邓佳柳
             * @Date: 2024-12-18
             */  
            async getQuotaApplyInfo(){
                try{
                    let url ='/action/link/annualQuotaDetails/queryById'
                    let params = {
                        id:this.id,
                    }
                    let {result} = await this.$http.post(url,params)
                    this.annualQuota = result
                    // 获取明细
                    this.getQuotaDetails()
                    // 获取调整配额控制权限
                    this.getAdjustBtnOauth()
                }catch (e){
                    console.log(e)
                }
            },
            /**
             * @description: 获取申请记录，处理标题
             * @author: 邓佳柳
             * @param {*} approvalId  审批id
             * @param {*} flowObjId  申请记录id
             * @Date: 2024-12-20
             */ 
            async initApprovePageInfo(approvalId,flowObjId){
                this.approvalId = approvalId
                this.id = flowObjId
                await this.getQuotaApplyInfo()
                // 处理标题
                if(this.annualQuota.quotaType==='BaisicQuotaApplication'){
                    this.$taro.setNavigationBarTitle({title: '基础配额申请审批'});
                }else if(this.annualQuota.quotaType==='QuotaAdjustApplication'){
                    this.$taro.setNavigationBarTitle({title: '配额调整申请审批'});
                }else if(this.annualQuota.quotaType==='UnplannedQuotaApplication'){
                    this.$taro.setNavigationBarTitle({title: '计划外配额申请审批'});
                }
            }, 
            /**
             * @description: 调整配额控制权限
             * @author: 邓佳柳
             * @Date: 2025-01-08
             */ 
            async getAdjustBtnOauth(){
                try {
                    let url = '/action/link/annualQuotaDetails/checkAccntStandard'
                    let params = {
                        acctId:this.annualQuota.acctId,
                        fiscalYear:this.annualQuota.fiscalYear,
                        prodPartCode:this.annualQuota.prodPartCode
                    }
                    let { success } = await this.$http.post(url, params, {
                    // 禁用自动处理错误逻辑，默认请求出错，会自动弹框显示错误，以及记录错误日志
                    autoHandleError: false,
                    handleFailed: async (response) => {
                        console.log('手动处理错误', response)
                        // 标准2客户:关闭核心客户的调整配额功能，限制仅管理员可调整
                        const positionCode = await this.$utils.getCfgProperty('QUOTA_ADJUST_APPLY_CONFIG');
                        this.isShowAdjustBtn= JSON.parse(positionCode).applyPostWhiteList.indexOf(this.userInfo.positionType)>-1;                  
                    }
                    })
                    // 标准1-新开 :new
                    // 标准1-非核心 :nonCore
                    // 新开客户和非核心客户（标准1），都直接可见调整配额功能，发起申请，不限制职位类型；
                    if(success){
                        this.isShowAdjustBtn = true
                    }
                    } catch (error) {
                        console.error(error)
                    }
            }                   
            },

        async created(){
            // 页面来源
            const pageSource = this.pageParam.source;
            // 审批来源
            if (pageSource === 'approval') {
                // 审批传过来的 审批数据ID 配额申请对象ID
                let { id:approvalId, flowObjId } = this.pageParam.data
                this.initApprovePageInfo(approvalId,flowObjId)
            } else if(this.pageParam.data){
                // 查看详情
                this.annualQuota = this.pageParam.data
                this.getQuotaDetails()
                this.getAdjustBtnOauth()
            }else{
                // 消息场景对象
                let sceneObj = await this.$scene.ready();
                // 从小程序审批消息而来 审批数据ID 配额申请对象ID
                let approvalId = sceneObj.query['approval_id'];
                let flowObjId = sceneObj.query['flowObjId'];
                this.initApprovePageInfo(approvalId,flowObjId)
            }
            if(this.pageParam.type==='out'){
                this.$taro.setNavigationBarTitle({title: '计划外配额申请'});
            }else if(this.pageParam.type==='edit'){
                this.$taro.setNavigationBarTitle({title: '年度调整配额申请'});
            }
        }
    }
</script>

<style lang="scss">
.annual-quota-detail-page{
    padding-bottom: 400px;
    .head-info {
        padding-bottom: 4px;
        padding-top: 4px;
        background-color: #ffffff;
        width: 94%;
        border-radius: 16px;
        box-shadow: 0 2px 36px 0 rgba(135,144,168,0.20);
        margin: 24px auto auto auto;
        overflow: hidden;
        .info-row {
            padding: 16px 24px;
            @include flex-start-center;
            @include space-between;
            font-size: 28px;
            .label {
                color: #8C8C8C;
            }
            .value {
                color: #262626;
            }
        }
    }
    .blank {
        height: 24px;
        width: 100%;
    }
    .block {
        width: 100%;
        height: auto;
        background: #ffffff;
    }
    .row-content {
        font-size: 28px;
        background: #FFFFFF;
        padding: 20px;
        margin: 20px;
        border-radius: 20px;
        .plan-title{
            color: #2F69F8;
            padding: 10px 20px;
            margin-top: 20px;
            margin-left: 10px;
        }
        .row-title {
            color: #262626;
            font-weight: bold;
        }
        .line{
            padding-bottom: 14px;
            border-bottom: 5px solid #f2f2f2;
        }
        .is-required{
            padding-left: 18px;
           &:before {
               content: '*';
               color: #FF5A5A;
               font-size: 32px;
               text-align: center;
               width: 24px;
               position: absolute;
               left: 34px;
           }
        }
    }
    .bottom-btn {
        padding-top: 16px;
        .sure-btn {
            border-radius: 60px;
            margin-right: 24px;
            margin-left: 24px;
        }
    }
}
</style>
