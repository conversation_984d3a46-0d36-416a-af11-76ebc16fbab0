<!--
 * @Description:  年度配额申请（怀旧1204/蓉城怀旧5910）
 * @Author: 邓佳柳
 * @Date: 2025-05-12
-->
<template>
	<link-page class="annual-quota-huaijiu-page">
		<!-- 审批记录 -->
		<approval-history-point :approvalId="approvalId" v-if="$utils.isNotEmpty(approvalId)" />
		<!-- 年度配额申请表单组件-怀旧 -->
		<view class="row-content">
			<quota-form-info
                v-if="formData.acctId"
				:formData="formData"
				:readonly="!isEditFlag"
				:brandCompanyCode="5910"
                @validate="validateBeforeSubmit"
				:message="message" />
		</view>
		<!-- 怀旧年度配额明细列表 -->
		<huaijiu-year-quota-list
			v-if="formData.acctId && formData.prodPartCode"
			:id="formData.acctId"
			:fiscalYear="formData.fiscalYear"
			:month="formData.month"
			:prodPartCode="formData.prodPartCode"
			:flagParam="{
				isEditFlag: isEditFlag,
				isCheckList: !!pageParam && pageParam.isCheckList || ($utils.isNotEmpty(approvalId)?'N':''),
			}">
			<view class="row-title line" v-if="pageParam.detail!=='Y'">年度配额明细</view>
		</huaijiu-year-quota-list>

		<!-- 备注 -->
		<view class="row-content">
			<view :class="['row-title', 'line']">备注</view>
			<view class="textarea-big">
				<link-textarea class="text-area" :placeholder="'请输入备注'"
					mode="textarea"
					v-model="formData.comments"
					:nativeProps="{maxlength:maxlength}"
					:disabled="!isEditFlag"
					style="padding: 0px"
					placeholder-style="color: #BFBFBF;"></link-textarea>
			</view>
		</view>
		<!-- 协议信息 -->
		<view class="row-content" v-if="formData.id">
			<view :class="['row-title', 'line']">协议信息</view>
			<view v-if="formData.id">
				<lnk-img-watermark
					:parentId="formData.id"
					ref="imgList"
					:delFlag="$utils.isEmpty(approvalId) && formData.approvalStatus !== 'Submitted'"
					:historyDelFlag="false"
					moduleType="agrAgreement"
					moduleName="协议信息"
					@imgUploadSuccess="imgUploadSuccess"
					@imgDeleteSuccess="imgDeleteSuccess"
					:newFlag="$utils.isEmpty(approvalId) &&  formData.approvalStatus !== 'Submitted'" />
			</view>
		</view>

		<!-- 申请/编辑 -->
		<link-sticky class="bottom-btn" v-if="isEditFlag">
			<link-button class="sure-btn" @tap="submit" autoLoading :throttleTimer="10">提交</link-button>
		</link-sticky>
		<!-- 审批操作 -->
		<link-sticky v-if="$utils.isNotEmpty(approvalId)">
			<approval-operator :approvalId="approvalId" :formData="formData"></approval-operator>
		</link-sticky>

		<link-dialog ref="prodBottom" position="bottom" height="85vh" class="dialog-bottom" noPadding v-model="show">
			<view class="model-title">
				<view class="iconfont icon-close" @tap="dialogHide"></view>
				<view class="title">请选择下一节点审批人</view>
			</view>
			<view class="dialog-content" style="height: calc(100% - 44px);">
				<scroll-view scroll-y="true" :style="{height: 'calc(100% - 75px)'}">
					<link-radio-group v-model="formData.approverId">
						<link-auto-list :option="approverList" hideCreateButton :scrollContent="true" :searchInputBinding="{props:{placeholder:'员工姓名/工号'}}">
							<template slot-scope="{data, index}">
								<view slot="note">
									<item :key="index" :data="data" :arrow="false" class='select-box'>
										<link-checkbox :val=data.userId toggleOnClickItem slot='thumb' />
										<view class='select-item' slot='note'>
											<view class='select-item-left'>{{data.fstName}}</view>
											<view>{{data.userName}}</view>
										</view>
									</item>
								</view>
							</template>
						</link-auto-list>
					</link-radio-group>
				</scroll-view>
				<view class="link-dialog-foot-custom">
					<link-button shadow @tap="cfmApprover" label="确定" style="width: 90vw" />
				</view>
			</view>
		</link-dialog>
	</link-page>
</template>
<script>
	import LnkImgWatermark from '@/pages/core/lnk-img-watermark/lnk-img-watermark';
	import ApprovalHistoryPoint from '@/pages/lzlj/approval/components/approval-history-point';
	import ApprovalOperator from '@/pages/lzlj/approval/components/approval-operator';
	import quotaDetailCard from './components/quota-detail-card.vue';
	import QuotaFormInfo from './components/quota-form-info.vue';
	import HuaijiuYearQuotaList from '@/pages/terminal/terminal/components/year-quota/huaijiu-year-quota-list.vue';
	import {DateService} from 'link-taro-component';

	definePageConfig({
		navigationBarTitleText: ''
	});
	const form={
		id:'', //申请id（必填）
		acctId:'', //终端id（必填）
		quotaType:'', //申请类型（必填）
		fiscalYear:'', //财年（必填）
		quotaQuantity:'', //申请数量（必填）
		prodPartCode:'', //品项（必填）
		month:'', //申请年月（必填）
		approverId:'', //指定审批人id
		comments:''
	}
	export default {
		name: 'annual-quota-huaijiu-page',
		components: {
			LnkImgWatermark,
			ApprovalHistoryPoint,
			ApprovalOperator,
			quotaDetailCard,
			QuotaFormInfo,
			HuaijiuYearQuotaList
		},
		data() {
		const approverList = new this.AutoList(this, {
			url: {queryByExamplePage: 'action/link/orgnization/querySalesCityByAcctId'},
			param: {
			},
			searchFields: ['fstName', 'userName'],
			sortOptions: null,
			renderFunc: (h, {data, index}) => {
				return (
					<item key={index} data={data} class='select-box' arrow='false'>
						<link-checkbox val={data.id} toggleOnClickItem slot='thumb' />
						<view class='select-item' slot='note'>
							<view class='select-item-left'>{data.fstName}</view>
							<view>{data.userName}</view>
						</view>
					</item>
				);
			},
			hooks:{
                beforeLoad(option) {
                    option.param.acctId = this.formData.acctId
				}
			}
			});
			return {
				formData: {
					acctId:'',
					acctName: '',
					quotaType: '',
					fiscalYear: 2025,
					quotaQuantity: ''
				}, //配额申请表单对象

				isEditFlag: false, //表单编辑状态
				approvalId: undefined, //审批id
				id: '', //当前记录id
				type: '', // authorized|orgAuthorized
				maxlength: 300,
				approverList,
				show: false, //弹框显示
				flag:true,
				message:'',
				tips:'', //批准授权配额数量提示
			};
		},
		/**
		 * @description: 怀旧配额申请/编辑/详情/审批详情
		 * @author: 邓佳柳
		 * @Date: 2025-05-12
		 * @param operate NEW|UPDATE|空  新建|编辑|查看
		 * @param pageSource approval 审批详情
		 * @param data 页面传递数据
		 */
		async created() {
			// 页面传递数据
			let {data, operate, } = this.pageParam || {};
			this.isEditFlag = this.$utils.isNotEmpty(operate);

			// 新建
			if (operate === 'NEW') {
				this.id = await this.$newId();
				let year = new Date().getFullYear();
				let month = new Date().getMonth() + 1;
				let fiscalYear = month > 10 ? year + 1 : year;
				month = DateService.format(new Date(), 'YYYYMM');
				let {acctId, acctName, acctCode, prodPartCode, quotaType} = data;
                this.formData = {id: this.id, acctId, acctName, acctCode, prodPartCode, quotaType, fiscalYear, month, quotaQuantity: ''};
                // 校验组织类型
                
				if(this.formData.quotaType=='AuthorizedQuota'){
                    // 当前终端所属组织为【组织类型】，无法确定终端组织城市，请联系内勤调整终端所属组织到对应的销售城市！
                    // 无法查询到终端行政区划城市时，报错提醒：当前终端无【城市】，请重新定位所在地址，获取终端城市后再发起授权配额申请流程；
                    // 根据终端所属组织ID ，关联组织表查询I6-name，职位组织=当前销售城市ID，且职位类型=城市经理的所有状态为有效的职位ID
                    this.getDefaultApprover()
                }
                if(this.formData.quotaType == 'QuotaAdjustApplication'){
                    // 标准2客户可填写负数调减,标准1仅可填写正数调增
                    if(data.acctStandard === 'standard1'){
                        this.$set(this.formData,'accntStand','standard1')
                    }else{
                        this.message = '如若调减，请填写负数!注:调减配额不可低于月度配额余额!';
                    }
                    
                }
                console.log('新建',this.formData,data);

			} else if (this.pageParam.source === 'approval') {
				this.approvalId = data.id;
				this.id = data.flowObjId;
				this.formData = await this.getApplyInfo();
				console.log('审批详情',this.formData)

			} else if(this.$utils.isNotEmpty(this.pageParam.data)){
					this.id = data.id
					this.formData = await this.getApplyInfo();
			}else{
				// 消息场景
				let sceneObj = await this.$scene.ready()
				if (sceneObj.query['approval_from'] === 'qw') {
					this.approvalId = sceneObj.query['approval_id'];
					this.id = sceneObj.query['flowObjId'];
					this.formData = await this.getApplyInfo();
				}
			}
			
			// 处理标题
			const mapLov = {
				AuthorizedQuota: '终端授权配额',
                QuotaAdjustApplication:'年度配额调整'
			};
			let title = mapLov[this.formData.quotaType] || '配额';
			let scene = this.isEditFlag ? '申请' : this.$utils.isNotEmpty(this.approvalId) ? '审批' : '详情';
			this.$taro.setNavigationBarTitle({title: `${title}${scene}`});
		},
		mounted() {},
		methods: {
			imgUploadSuccess(imgList) {
				this.imgList = imgList;
			},
			imgDeleteSuccess(imgList) {
				this.imgList = imgList;
			},
			/**
			 * @description: 提交
			 * @author: 邓佳柳
			 * @Date: 2025-05-12
			 */
			async submit() {
                let{ quotaType, prodPartCode,quotaQuantity,accntStand,approverId} = this.formData
				if (this.$utils.isEmpty(prodPartCode)) {
					this.$message.error('请选择配额产品类别');
					return;
				}
				if (this.$utils.isEmpty(quotaQuantity)) {
					this.$message.error('配额数量不能为空');
					return;
				}
                let reg = /^[-]?[1-9]\d*$/
                if(this.$utils.isEmpty(accntStand) && !(reg.test(Number(quotaQuantity)))){
                    this.$message.error('调整配额请填写整数')
                    return
                }
				// 当前层级为片区时指定公司层级审批人
				if(quotaType == 'AuthorizedQuota' && this.$utils.isEmpty(approverId)){
					// 选择审批人
					this.show = true
				}else{
					this.applyHanlder()
				}
			},
			async applyHanlder(){
				try {
					this.$utils.showLoading('提交中...');

					let urls = {
                       AuthorizedQuota: `action/link/annualQuotaApplication/huaijiuAuthQuotaApply`,
                       QuotaAdjustApplication:'action/link/annualQuotaApplication/huaijiuAdjustQuotaApply'
                    };
					
					let {success} = await this.$http.post(urls[this.formData.quotaType], this.formData,{
                    // 禁用自动处理错误逻辑，默认请求出错，会自动弹框显示错误，以及记录错误日志
                    autoHandleError: false,
                    handleFailed: (response) => {
                        console.log('手动处理错误', response)
                        // 弹框显示错误 标准2客户退出
                        this.$dialog({
                            title: '提示',
                            content: response.message || response.result,
                            cancelButton: (response.message || response.result).indexOf('是否发起调整配额申请')>-1,
                            confirmText:'确认',
                            onConfirm: () => {
                                if((response.message || response.result).indexOf('是否发起调整配额申请')>-1){
										let prodPartCode =this.pageParam.data.prodPartCode
										 if(this.$utils.isEmpty(prodPartCode)){
											prodPartCode = this.formData.prodPartCode
										}
                                        this.$nav.redirect('/pages/terminal2/annual-quota-apply/annual-quota-huaijiu-page', {
                                        data: {
                                            ...this.pageParam.data,
                                            quotaType: 'QuotaAdjustApplication',
											prodPartCode,
                                        },
                                        operate: 'NEW'
                                    })
                                }
                                
                            },
                            onCancel: () => {
                                this.$nav.back();
                            }
                        })
                    }})
					this.$utils.hideLoading()
					if (success) {
						this.$nav.back({quota: true});
					}
                    

				} catch (e) {
					this.$utils.hideLoading()
				}
			},
			/**
			 * @description: 获取申请记录
			 * @author: 邓佳柳
			 * @Date: 2025-05-12
			 */
			async getApplyInfo() {
				return new Promise(async (resolve, reject) => {
					try {
						let url = 'action/link/annualQuotaDetails/queryById';
						let params = {
							id: this.id
						};
                        let { result } = await this.$http.post(url, params);
						resolve(result);
					} catch (e) {
						console.log(e);
					}
				});
			},
			dialogHide() {
				this.show = false;
			},
			async cfmApprover(){
				this.show = false;
				if(this.$utils.isEmpty(this.formData.approverId)){
					this.$message.error('请选择审批人');
					return;
				}
				this.applyHanlder()
            },
			/**
			 * @description: 查询客户标准
			 * @author: 邓佳柳
			 * @Date: 2025-05-12
			 */		
			checkAccntStandard(){
				return new Promise(async (resolve,reject)=>{
					try{

					let url = 'action/link/annualQuotaApplication/huaijiuStandardQuery'
					let params ={
                        acctId: this.formData.acctId,
                        fiscalYear: this.formData.fiscalYear,
                        prodPartCode: this.formData.prodPartCode
					}
					let {success,standard,type} = await this.$http.post(url, params);
						/**standard	standard2/standard1	标准1/标准2
                         * type	NEW/CORE/NO_CORE	核心/非核心/新开 */
						if (success) {
							resolve(standard)
						}
					}catch(e){
						resolve(false)
					}
				})
				
			},
            async validateBeforeSubmit(){
                // 当客户属于标准2客户（核心终端）时，不可发起授权配额申请；
                if (this.formData.quotaType =='AuthorizedQuota' && (await this.checkAccntStandard())==='standard2') {
                    // 弹框显示错误 标准2客户退出
                    this.$dialog({
                        title: '提示',
                        content: '核心客户不允许发起授权配额申请',
                        onConfirm: () => {
                            this.$nav.back();
                        },
                    })
                }
            },
			/**
			 * @description: 获取上级默认审批人
			 * @author: 邓佳柳
			 * @Date: 2025-05-12
			 */			
			async getDefaultApprover(){
				try{
				let url = '/action/link/orgnization/querySalesCityByAcctId'
				let param ={
                    acctId : this.formData.acctId
                }
				
                let res = await this.$http.post(url,param)
                if(res.rows.length === 1){
                    this.formData.approverId = res.rows[0].userId 
                }

				}catch(e){
					console.log(e)
				}
			},		


		}
	};
</script>
<style lang="scss">
	.annual-quota-huaijiu-page {
		padding-bottom: 400px;
		padding-top: 10px;
		.row-content {
			font-size: 28px;
			background: #ffffff;
			padding: 20px;
			margin: 20px;
			border-radius: 20px;
			.tips {
				font-size: 22px;
				color: red;
				background: #fff;
				line-height: 24rpx;
				margin-bottom: 20px;
				text-align: right;
			}
		}
		.bottom-btn {
			padding-top: 16px;
			.sure-btn {
				border-radius: 60px;
				margin-right: 24px;
				margin-left: 24px;
			}
		}
		.link-auto-list-no-more {
			display: none !important;
		}
		.dialog-bottom .link-dialog-foot-custom {
			width: auto;
		}
		.model-title {
			.title {
				font-family: PingFangSC-Regular, serif;
				font-size: 32px;
				color: #262626;
				letter-spacing: 0;
				text-align: center;
				line-height: 96px;
				height: 96px;
				width: 90%;
				padding-left: 0 !important;
				margin-right: 80px;
				margin-left: 10vw;
			}
			.icon-close {
				color: #bfbfbf;
				font-size: 48px;
				line-height: 96px;
				height: 96px;
				margin-right: 30px;
				float: right;
			}
		}
		.select-box {
			border-bottom: 1px solid #f2f2f2;
			padding: 0 20px;
			.select-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				color: #bfbfbf;
				font-size: 28px;
				line-height: 28px;
				.select-item-left {
					color: #262626;
				}
			}
		}
		.info-item {
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			font-size: 28px;
			line-height: 48px;
    		margin: 24px 0;
			color: #333333;
		}
	}
</style>
