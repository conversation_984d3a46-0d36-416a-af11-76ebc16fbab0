<template>
    <link-page class="return-goods-outbound-list-page">
        <lnk-taps
            :taps="approvalTapsOption"
            v-model="approvalTapsActive"
            @switchTab="onTab"
        ></lnk-taps>
        <link-auto-list
            :option="approvalListOption"
            :searchInputBinding="{
                props: { placeholder: '请输入出库客户编码/名称' },
            }"
            hideCreateButton
        >
            <view slot="filterGroup" class="top-filter">
                <scroll-view scroll-x="true" class="top-filter-content">
                    <view class="top-filter-info">
                        <view 
                            v-for="(item, index) in timeFilterOption" 
                            :class="{'time-list-item': true, 'timeChecked': item.val === checkedTimer}"
                            :key="index"
                            @tap="selectTime(item)">
                            <view class="time-item">{{item.name}}</view>
                        </view>
                    </view>
                </scroll-view>
            </view>
            <template slot-scope="{ data, index }">
                <item :key="index" :data="data" :arrow="false" class="approval-content-item" @tap="changeDetails(data)">
                    <view class="approval-content-info">
                        <view class="approval-info-row" v-for="(item, index) in approvalInfo" :key="index">
                            <view class="label">{{item.label}}</view>
                            <view class="value">{{data[item.key]}}</view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
        <!--悬浮按钮-->
        <link-fab-group v-model="showFabButtonFlag" v-if="isShowAddBtn">
            <link-fab-item icon="icon-file-text" label="经销商退货出库" @tap-icon="()=>onTapFabItem('Dealer')"/>
            <link-fab-item icon="icon-file-text" label="分销商退货出库" @tap-icon="()=>onTapFabItem('Distributor')"/>
            <link-fab-item icon="icon-file-text" label="终端退货出库" @tap-icon="()=>onTapFabItem('Terminal')"/>
        </link-fab-group>
    </link-page>
</template>
<script>
import lnkTaps from '../../core/lnk-taps/lnk-taps';
import { approvalTapsOption, approvalInfo, pageRouterParams, timeFilterOption } from './constants/index';
import PAGES_PATH from '../../../constants/pagesPath'
import API from '../../../constants/apiName'
export default {
    name: 'return-goods-outbound-list-page',
    components: { lnkTaps },
    data() {
        let oauth = ''
        if (this.$utils.isPostnOauth() === 'MY_ORG') oauth = 'MULTI_ORG'
        else oauth = 'MULTI_POSTN'
        // 列表请求数据设置
        const approvalListOption = new this.AutoList(this, {
            url: {
                queryByExamplePage: API.RETURN_GOODS_LIST,
            },
            sortField: 'created',
            searchFields: ['sourceCode', 'sourceName'],
            param: {
                filtersRaw: [
                    {id: 'flowStatus', property: 'flowStatus', value: 'Running', operator: '='},
                    {id: 'created', property: 'created', value: this.$utils.getRecentlyMonth(1).startDate, operator: '>='},
                    {id: 'created', property: 'created', value: this.$utils.getRecentlyMonth(1).endDate, operator: '<='},
                ],
                oauth,
            },
            sortOptions: null
        });
        return {
            timeFilterOption,
            approvalTapsOption,
            approvalTapsActive: approvalTapsOption[0],
            approvalListOption,
            approvalInfo,
            userInfo: this.$taro.getStorageSync('token').result, // 登录用户信息
            showFabButtonFlag: false,
            checkedTimer: 'LastOneMonth',
            isShowAddBtn: false
        };
    },
    async created() {
        // 登录职位所属品牌公司为配置的这几个时才能正常新建
        let companyCodeList = await this.$utils.getCfgProperty('SHOW_RETURN_GOODS_OUTBOUND_ADD');
        let positionData = await this.$utils.getCfgProperty('SHOW_RETURN_GOODES_OUTBOUND_ZW');
        if (companyCodeList.indexOf(this.userInfo.coreOrganizationTile.brandCompanyCode !== -1) && positionData.indexOf(this.userInfo.positionType) !== -1) this.isShowAddBtn = true;
        this.$taro.removeStorageSync('SCAN_CODE_RECORD');
    },
    methods: {
        /**
         * 顶部菜单切换
         * <AUTHOR>
         * @date 2024-08-15
         * @param {Object} active 选中菜单
         */
        async onTab(active) {
            this.approvalTapsActive = active;
            this.approvalListOption.option.param.filtersRaw.forEach(item => {
                if (item.property === 'flowStatus') item.value = active.val
            })
            await this.approvalListOption.methods.reload();
        },
        /**
         * 监听页面返回函数
         * <AUTHOR>
         * @date 2024-08-15
         * @param {Object} param 返回携带参数
         */
        onBack (param) {
            this.approvalListOption.methods.reload()
        },
        /**
         * 选择新建退货出库类型
         * <AUTHOR>
         * @date 2024-08-16
         * @param {string} key 跳转页面类型
         */
        onTapFabItem(key) {
            const that = this
            switch(key) {
                case 'Distributor':
                    that.$nav.push(PAGES_PATH.TERMINAL_LIST_PAGE, pageRouterParams[key])
                    break;
                case 'Dealer':
                    that.$nav.push(PAGES_PATH.DEALER_LIST, pageRouterParams[key])
                    break;
                case 'Terminal':
                    that.$nav.push(PAGES_PATH.TERMINAL_LIST_PAGE, pageRouterParams[key])
                    break;
                default:
                    break;           
            }
        },
        /**
         * @desc 选择时间筛选范围
         * <AUTHOR>
         * @date 2024-08-16
         * @param item 当前选中时间范围
         */
        selectTime (item) {
            if (item.val === this.checkedTimer) return;
            this.checkedTimer = item.val;
            let timeObj = {
                LastOneMonth: this.$utils.getRecentlyMonth(1),
                LastThreeMonth: this.$utils.getRecentlyMonth(3),
                LastHalfYear: this.$utils.getRecentlyMonth(6),
                LastYear: this.$utils.getRecentlyMonth(12)
            }
            this.approvalListOption.option.param.filtersRaw.forEach(el => {
                if (el.property === 'created' && el.operator === '>=') el.value = timeObj[item.val].startDate;
                if (el.property === 'created' && el.operator === '<=') el.value = timeObj[item.val].endDate;
            });
            this.approvalListOption.methods.reload();
        },
        /**
         * @desc 跳转审批详情页
         * <AUTHOR>
         * @date 2024-08-19
         */
        changeDetails(res) {
            // TODO 待调整
            this.$nav.push(PAGES_PATH.OUTBOUND_SCAN_CODE_DETAILS, {
                scanType: '8',
                id: res.id,
                created: res.createDate,
                acctName: res.sourceName,
                scanUserName: res.applyUserName
            })
        }
    },
};
</script>

<style lang="scss">
.return-goods-outbound-list-page {
    background: #f2f2f2;
    @include flex;
    @include direction-column;
    .link-auto-list {
        .link-sticky.link-sticky-top{
            top:92px !important;
        }
        .link-auto-list-wrapper{
            margin-top: 92px !important;
        }
        .top-filter{
            flex: 1;
            overflow-x: hidden;
            .top-filter-content{
                width: 100%;
                .top-filter-info{
                    display: flex;
                    white-space: nowrap;
                    font-size: 24px;
                    padding: 8px 24px;
                    align-items: center;
                    flex-wrap: nowrap;
                    .time-list-item{
                        padding: 8px 16px;
                        margin-right: 20px;
                        white-space: nowrap;
                        display: inline-block;
                        background-color: #f2f2f2;
                        color: #333333;
                        border-radius: 4px;
                    }
                    .timeChecked{
                        background-color: rgba(47, 105, 248, 0.1) !important;
                        color: #2f69f8 !important;
                    }
                }
            }
        }
        .approval-content-item {
            background-color: #f2f2f2;
            margin-top: 20px;
        }
        .link-item-content {
            .approval-content-info {
                width: 100%;
                padding: 30px;
                background: #fff;
                border-radius: 10px;
                margin-top: 20px;
                .approval-info-row {
                    line-height: 50px;
                    font-size: 28px;
                    @include flex;
                    .label {
                        width: 260px;
                    }
                    .value {
                        color: #333;
                    }
                }
            }
        }
    }
    .approval-content-item {
        padding: 0 30px;
    }
}
</style>
