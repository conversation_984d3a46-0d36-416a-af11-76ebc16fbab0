<!--
终端-融资预订单详情
<AUTHOR>
@date 2023-09-11
-->
<template>
    <link-page class="finance-order-detail-page">
        <!--菜单顶部-->
        <view class="menu-top" :style="menuTopStyle">
            <view class="page-title">
                <view class="iconfont icon-left" :style="{'padding-top':statusBarHeight + 'px'}" @tap="backOrderList"></view>
                <view class="page-title-text" :style="{'padding-top':statusBarHeight + 'px'}">{{pageParam.pageTitle}}</view>
            </view>
            <view class="order-status-image">
                <image :src="$imageAssets.shippedImage"></image>
            </view>
            <view class="order-status">{{orderHead.orderStatus | lov('EST_ORDER_STATUS')}}</view>
            <view class="acct-name">{{orderHead.acctName}}</view>
        </view>
        <view class="terminal-detail">
            <view class="terminal-address">
                <view class="address-line">
                    <view v-for="item in 19" :class="item % 4 === 1 ? '.blue-line' : (item % 2 === 0 ? '.white-line' : 'red-line')"></view>
                </view>
                <view class="content">
                    <view class="address-icon">
                        <image :src="$imageAssets.addressIconImage"></image>
                    </view>
                    <view class="address-content">
                        <view class="name-phone">
                            <text class="name">{{orderHead.customConsignee}}</text>
                            <text class="phone">{{orderHead.customMobilePhone}}</text>
                        </view>
                        <view class="address-detail">{{orderHead.addr}}</view>
                    </view>
                </view>
            </view>
        </view>
        <view class="order-head-info">
            <view class="order-info-row" v-if="orderHead.acctCode">
                <view class="label">客户编码</view>
                <view class="value">{{orderHead.acctCode}}</view>
            </view>
            <view class="order-info-row" v-if="orderHead.billTitle">
                <view class="label">营业执照名称</view>
                <view class="value">{{orderHead.billTitle}}</view>
            </view>
            <view class="order-info-row" v-if="orderHead.orderChildType">
                <view class="label">类型</view>
                <view class="value">{{orderHead.orderChildType | lov('ORDER_CHILD_TYPE')}}</view>
            </view>
            <view class="order-info-row" v-if="orderHead.orderNo">
                <view class="label">订单编号</view>
                <view class="value">{{orderHead.orderNo}}</view>
            </view>
            <view class="order-info-row" v-if="orderHead.orderDate">
                <view class="label">下单时间</view>
                <view class="value">{{orderHead.orderDate | date('YYYY-MM-DD HH:mm:ss')}}</view>
            </view>
            <view class="order-info-row" v-if="orderHead.completeTime">
                <view class="label">订单完成/关闭时间</view>
                <view class="value">{{orderHead.completeTime | date('YYYY-MM-DD HH:mm:ss')}}</view>
            </view>
            <view class="order-info-row" v-if="orderHead.creatorName">
                <view class="label">制单人</view>
                <view class="value">{{orderHead.creatorName}}</view>
            </view>
            <view class="order-info-row" v-if="orderHead.expectDate">
                <view class="label">计划配送时间</view>
                <view class="value">{{orderHead.expectDate}}</view>
            </view>
            <view class="order-info-row" v-if="orderHead.orderTotalQty">
                <view class="label">订单总数</view>
                <view class="value">
                    <text>{{orderHead.pcSum}}件{{orderHead.botSum}}瓶</text>
                </view>
            </view>
            <view class="order-info-row" v-if="orderHead.receivableAmount">
                <view class="label">订单金额</view>
                <view class="value">{{orderHead.receivableAmount}}</view>
            </view>
            <view class="order-info-row" v-if="orderHead.primaryPayType">
                <view class="label">付款方式</view>
                <view class="value">{{orderHead.primaryPayType | lov('ADVANCE_ORDER')}}</view>
            </view>
            <view class="order-info-row" v-if="orderHead.comments">
                <view class="label">备注</view>
                <view class="value">{{orderHead.comments}}</view>
            </view>
        </view>
        <view class="order-head-info">
            <view class="order-info-row" style="display: block">
                <view style="padding-bottom: 20rpx">附件信息</view>
                <view>
                    <lnk-img-watermark :parentId="pageParam.orderId"
                                       moduleType="advanceOrder"
                                       :delFlag="false"
                                       :album="false"
                                       :newFlag="false">
                    </lnk-img-watermark>
                </view>
            </view>
        </view>
        <view class="product-container" v-if="orderRow.length !== 0">
            <view class="title">
              <view class="product-title">产品信息</view>
              <view class="right-icon-operate" :style="{color: productZhankaiSwitch ? '#2F69F8': '#262626'}" @tap="productZhankaiSwitch ? productZhankaiSwitch = false: productZhankaiSwitch = true">
                <link-icon v-if="!productZhankaiSwitch" icon="icon-shouqi" />
                <link-icon v-else icon="icon-zhankai" />
              </view>
            </view>
            <view class="product-content" v-for="(item, index) in orderRow" :key="index" v-show="productZhankaiSwitch">
                <view class="item-top">
                    <view class="code">{{item.prodCode}}</view>
                    <view class="complimentary" v-if="(item.giftFlag === '是' || item.giftFlag === 'Y') && pageParam.orderType === 'SalesOrder'">赠品</view>
                </view>
                <view class="item-middle">
                    <view class="item-middle-name">{{item.prodName}}</view>
                </view>
                <view class="item-bottom">
                    <view class="item-line-box">
                        <view class="quota">
                            订单数量 <text>{{Number(item.prodNum)}}</text>
                            <text class="unit">{{item.prodUnit | lov('PROD_UNIT')}}</text>
                        </view>
                        <view class="quota" v-if="item.netPrice && pageParam.orderType === 'SalesOrder'">，实际小计 <text>￥{{Number(item.basePrice)}}</text></view>
                    </view>
                    <view class="item-line-box">
                        <view class="quota"> 扫码数量 <text>{{Number(item.scanNum)}}</text> {{item.prodUnit | lov('PROD_UNIT')}} </view>
                    </view>
                    <view class="item-line-box">
                        <view class="quota">供应商名称 <text>{{item.supplierName}}</text></view>
                    </view>
                    <view class="quota-type" v-if="item.isQuotaFlag">
                        <view class="quota-title">配额类型</view>
                        <!-- 计划内配额 -->
                        <view class="quote-item" :class="item.plannedProdNum ? 'light-blue' : ''">
                            <view class="check-row">
                                <view>计划内配额</view>
                                <view v-if="item.plannedProdNum">{{item.plannedProdNum}}{{item.prodUnit | lov('PROD_UNIT')}} </view>
                            </view>
                            <view class="moneyInfo" v-if="item.plannedProdNum">
                                <view class="moneyNum">计划内价格（元/瓶）</view>
                                <text class="text-content">{{item.finalCost}}</text>
                            </view>
                        </view>
                        <!-- 计划外配额 -->
                        <view class="quote-item" :class="item.unplannedProdNum ? 'light-blue' : ''"  v-if="!(isTeQu || isHuaiJiu)">
                            <view class="check-row">
                                <view>计划外配额</view>
                                <view v-if="item.unplannedProdNum">{{item.unplannedProdNum}}{{item.prodUnit | lov('PROD_UNIT')}} </view>
                            </view>
                            <view class="moneyInfo" v-if="item.unplannedProdNum">
                                <view class="moneyNum">计划外价格(元/瓶)</view>
                                <text class="text-content">{{item.planPrice}}</text>
                            </view>
                        </view>          
                    </view>
                    <view class="item-line-box" v-else>
                        <view class="quota">结算价(元/瓶) <text>{{item.finalCost}}</text></view>
                    </view>
                </view>
            </view>
        </view>
        <!--   扫码明细-->
        <view class="menu-code-record-list" v-if="showCodeRecordModule">
          <view class="menu-code-title">
            <view class="menu-code-record-name">扫码明细</view>
            <view class="right-icon-operate" :style="{color: codeZhankaiSwitch ? '#2F69F8': '#262626'}" @tap="codeZhankaiSwitch ? codeZhankaiSwitch = false: codeZhankaiSwitch = true">
              <link-icon v-if="!codeZhankaiSwitch" icon="icon-shouqi" />
              <link-icon v-else icon="icon-zhankai" />
            </view>
          </view>
          <link-auto-list :option="autoList" hideCreateButton v-show="codeZhankaiSwitch">
            <template slot-scope="{data,index}">
              <item :key="index" :data="data" :arrow="false" style="padding: 0">
                <view slot="note" class="list-item" >
                  <view class="list-item-row1">
                    <view class="left">
                      <view class="lable-name">扫码时间</view>
                      <view class="text">{{ data.scanTime}}</view>
                    </view>
                  </view>
                  <view class="list-item-row1">
                    <view class="left">
                      <view class="lable-name">物流码</view>
                      <view class="text"> {{ data.prodQrCode}} </view>
                    </view>
                  </view>
                  <view class="list-item-row2" v-show="data.productName">
                    <view class="left">
                      <view class="text"> {{data.productName}}{{data.prodUnit | lov('PROD_UNIT')}} </view>
                    </view>
                  </view>
                  <view class="list-item-row3">
                    <view class="left">
                      <view class="status" :class="{'status-normal': data.descriptionType === 'MatchSuccessfully','status-abnormal':data.descriptionType === 'MatchFailed'}">
                        {{ data.matchFailComment }}
                      </view>
                    </view>
                  </view>
                  <view class="gap-line"></view>
                </view>
              </item>
            </template>
          </link-auto-list>
        </view>
        <link-sticky class="bottom-sticky">
            <link-button block v-if="showCloseButton"  @tap="closeOrder">关闭订单</link-button>
            <!-- <link-button block @tap="completeOrder" :shadow="shadow">订单完成</link-button> -->
        </link-sticky>
    </link-page>
</template>

<script>
import LnkImgWatermark from "../../core/lnk-img-watermark/lnk-img-watermark";
    export default {
        name: "finance-order-detail-page",
        data () {
            let userInfo = this.$taro.getStorageSync('token').result;
            let companyFlag = false;
            return {
                // 是否可显示配额类型
                isQuotaFlag: false,
                // 可计入配额产品
                quotaProd: [],
                orderImgFlag: false,
                showCloseButton: false,
                isGuoJiao: userInfo.coreOrganizationTile.brandCompanyCode === '5600',
                isTeQu: userInfo.coreOrganizationTile.brandCompanyCode === '5137',
                isJiaoLing: userInfo.coreOrganizationTile.brandCompanyCode === '5151',
                isHuaiJiu: ['1204', '5910'].includes(userInfo.coreOrganizationTile.brandCompanyCode), //是否怀旧、蓉城
                userInfo,  //用户信息
                companyFlag, // 公司判断标识
                cfgPropertyValue:[], //配置值
                codeZhankaiSwitch: true, //扫码展开开关
                productZhankaiSwitch: true,//产品展开开关
                showCodeRecordModule: true, //是否展示扫码模块，如果没有扫码数据，则隐藏整个扫码模块
                orderRow: [],
                orderHead: {},
                menuTopStyle: '',
                menuHeight: '',
                statusBarHeight: this.$device.systemInfo.statusBarHeight + 14,
                shadow: true,
                positionType: this.$taro.getStorageSync('token').result.positionType,
                postnOauth: false,
                autoList : new this.AutoList(this, {
                  url: {
                    queryByExamplePage: 'action/link/codeScanRecord/queryFieldsByExamplePage'
                  },
                  stayFields: "id,prodQrCode,scanTime,productName,matchFailComment,descriptionType",
                  searchFields: null,
                  filterOption: null,                                                                  // 筛选参数
                  param: {
                    filtersRaw: [
                      {id: 'orderId', property: 'orderId', value: this.pageParam.orderId, operator: '='},
                      {id: 'headTransactionType', property: 'headTransactionType', value: 'Purchasein', operator: '='},
                    ]
                  },
                  hooks:{
                    async beforeGotoItem(param) {
                    },
                    afterLoad (data) {
                      this.showCodeRecordModule = data.rows.length >0 ? true: false
                    }
                  },
                  sortOptions: null,
                }),
            }
        },
        async created () {
            await this.queryCfgProperty(); //获取配置公司
            await this.getProdQuota();
            this.queryOrderItem();
            this.menuHeight = this.$device.isIphoneX ? '215px' : '196px';
            this.menuTopStyle = `background-image: url('${this.$imageAssets.homeMenuBgImage}');height: ${this.menuHeight};top:${this.$device.isIphoneX ? '-95px' : '-85px'};`;
        },
        components: {
            LnkImgWatermark,
        },
        methods: {
            /**
             * 配额产品列表获取
             * <AUTHOR> 
             * @date 2025/2/18
             */
            async getProdQuota(){
                const params = {
                    filtersRaw:[
                         {id: "companyCode", property: "companyCode", value: this.userInfo.coreOrganizationTile.brandCompanyCode},
                         {id: "isEffective", property: "isEffective", value: "Y"},
                    ]
                };
                const url = 'action/link/quotaProdInfo/queryByExamplePage'  
                try{
                    const {success, rows} = await this.$http.post(url, params);
                    if (success) {
                        this.quotaProd = rows.map(i=>{
                            return i.productCode
                        });
                    }
                }catch(e){
                    
                }
            },
            closeOrder(){
                this.$dialog({
                    title: '提示',
                    content: '是否确认关闭？',
                    cancelButton: true,
                    onConfirm: () => {
                        this.closeSubmit()
                    },
                    onCancel: () => {}
                })
            },
            async closeSubmit(id){
                const url = '/action/link/saleorder/financeClose'
                const param = {
                    id: this.orderHead.id,
                }
                const data = await this.$http.post(url,param)
                if(data.success){
                    let param = {orderListRefresh: true};
                    this.$nav.back(param);
                }
                
            },
            completeOrder(){
                console.log('完成订单相关')
            },
            imgInitSuccess(imgList) {
                this.orderImgFlag = imgList.length === 0
            },
            /**
             * 返回上一级页面
             * <AUTHOR>
             * @date 2023/08/30
             * @param param
             */
            onBack () {
                this.queryOrderItem();
            },
            /**
             * 获得配置公司
             * <AUTHOR>
             * @date 2023/08/30
             * @param param
             */
            async queryCfgProperty() {
                const data = await this.$http.post('action/link/cfgProperty/queryByExamplePage', {
                    filtersRaw: [{id: 'key', property: 'key', value: 'CalculateOrder', operator: '='}]
                });
                if (!this.$utils.isEmpty(data.rows)){
                    this.cfgPropertyValue = data.rows[0].value.split(',');
                    this.companyFlag = this.cfgPropertyValue.includes(this.userInfo.coreOrganizationTile.brandCompanyCode)
                }
            },
            /**
              * 更新订单状态
              * <AUTHOR>
              * @date 2023/08/30
              * @param flag 关闭/完成订单标志
            */
            updateOrderHead (flag) {
                flag === 'complete' ? this.orderHead.orderStatus = 'AllDelivered' : this.orderHead.orderStatus = 'Cancelled';
                this.$utils.showLoading();
                this.$http.post('action/link/saleorder/updateOrder', this.orderHead, {
                    handleFailed: (error) => {
                        this.$utils.hideLoading();
                    }
                }).then(() => {
                    let param = {orderListRefresh: true};
                    this.$nav.back(param);
                })
            },
            /**
              * 订单详情
              * <AUTHOR>
              * @date 2023/08/30
            */
            queryOrderItem () {
                const that = this;
                const params = {
                    id: that.pageParam.orderId
                }
                let url = '';
                that.pageParam.orderType === 'NcOutStockOrder' ? url = 'action/link/ordeInoutHead/queryById' : url = 'action/link/saleorder/queryById';
                if(that.pageParam.orderType !== 'NcOutStockOrder') {
                    params.attr4 = 'Y'
                }
                that.$http.post(url, params).then(data => {
                    if (data.success) {
                        that.orderHead = data.result;
                        that.isQuotaFlag = that.orderHead.acctType === 'Terminal' && (that.isGuoJiao || that.isTeQu || that.isJiaoLing|| that.isHuaiJiu);
                        that.postnOauth = data.result.postnId === this.$taro.getStorageSync('token').result.postnId;
                        that.showCloseButton = ['FinancePreApproveSuccess'].includes(that.orderHead.orderStatus) && that.positionType !== 'Salesman';
                        that.queryOrderRows(data.result.id);
                    }
                })
            },
            /**
              * 订单行
              * <AUTHOR>
              * @date 2023/08/30
              * @param id
            */
            queryOrderRows (id) {
                const that = this;
                let url = '';
                that.pageParam.orderType === 'NcOutStockOrder' ? url = 'action/link/ordeInoutItem/queryByExamplePage' : url = 'action/link/saleorderitem/queryByExamplePage';
                that.$http.post(url, {
                    filtersRaw:[{id:'headId',property:'headId',value: id,operator:'='}],
                    sort: 'created',
                    order: 'desc',
                }).then(async data => {
                    if (data.success) {
                        if (that.isQuotaFlag) {
                            const prodList = data.rows;
                            prodList.forEach(item => {
                                that.$set(item, 'isQuotaFlag', that.isQuotaFlag && that.quotaProd.indexOf(item.prodCode) > -1);
                                if (typeof(item.plannedProdNum) === 'string') {
                                    item.plannedProdNum = Number(item.plannedProdNum);
                                }
                                if (typeof(item.unplannedProdNum) === 'string') {
                                    item.unplannedProdNum = Number(item.unplannedProdNum);
                                }
                            });
                            that.orderRow = prodList;
                        } else {
                            that.orderRow = data.rows
                        }
                    }
                })
            },
             /**
             * 获取计划内外配额
             * <AUTHOR>
             * @date 2024/12/17 11:54
             * @param prodRows 当前选中的产品数据
             */
             getPlanQuota(prodRows) {
                return new Promise(async (resolve) => {
                    const list = prodRows.map(item => ({
                        prodId: item.prodId,
                        accntId: item.accntId || item.acctId
                    }));
                    const url =`action/link/saleCategory/${this.isTeQu ? 'queryTeQuQuotaAmt' :this.isJiaoLing ? 'queryJiaoLingQuotaAmt' : 'queryQuotaAmt'}`
                    const {success, rows} = await this.$http.post(url, list);
                    if (success) {
                        prodRows.forEach(item => {
                            const i = rows.find((t) => t.prodId === item.prodId);
                            if (i) {
                                item.plannedQuotaTotalAmt = i.plannedQuotaTotalAmt;
                                item.unplannedQuotaTotalAmt = i.unplannedQuotaTotalAmt;
                            }  
                        });
                        resolve(prodRows);
                    }
                });
            },
            /**
              * 返回列表
              * <AUTHOR>
             * @date 2023/08/30
              * @param param
            */
            backOrderList () {
                this.$nav.back()
            },
        }
    }
</script>

<style lang="scss">
    .finance-order-detail-page {
        padding-bottom: 150px;
        .lnk-img-watermark-item {
            margin-left: 10px;
        }
        .lnk-img .lnk-img-item{
            margin-left: 15px;
            width: 145rpx !important;
            height: 145rpx!important;
        }
        .lnk-img-watermark-box .lnk-img-watermark{
            margin: 0 !important;
            width: 100% !important;
        }
        .lnk-img-watermark-box .lnk-img-watermark-item {
            margin-top: 32rpx;
            width: 23%;
        }
        .menu-top {
            width: 750px;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            position: sticky;
            z-index: 1;
            .page-title {
                width: 100%;
                text-align: center;
                font-family: PingFangSC-Semibold, serif;
                font-size: 34px;
                color: #FFFFFF;
                letter-spacing: 0;
                line-height: 34px;
                @include flex-start-center;
                .icon-left {
                    font-size: 38px;
                    width: 10%;
                    padding-bottom: 20px;
                }
                .page-title-text {
                    text-align: center;
                    width: 80%;
                    padding-bottom: 20px;
                }
            }
            .order-status-image {
                position: absolute;
                margin-top: 60px;
                margin-right: 64px;
                right: 0;
                width: 222px;
                height: 140px;
                image {
                    width: 100%;
                    height: 100%;
                }
            }
            .order-status {
                font-size: 32px;
                padding-left: 64px;
                padding-top: 50px;
                color: #FFFFFF;
            }
            .acct-name {
                font-size: 28px;
                color: #FFFFFF;
                padding-top: 24px;
                padding-left: 64px;
            }

        }
        .terminal-detail {
            padding-top: 24px;
            background-image: linear-gradient(180deg, #FFFFFF 0%, rgba(255,255,255,0.00) 100%);
            .terminal-address {
                background: #fff;
                width: 94%;
                border-radius: 16px;
                box-shadow: 0 2px 36px 0 rgba(135,144,168,0.20);
                margin: auto;
                .address-line {
                    @include flex;
                    overflow: hidden;
                    .blue-line {
                        background: #2F69F8;
                        width: 40px;
                        height: 8px;
                        transform: skewX(-45deg);
                    }
                    .white-line {
                        background: #FFFFFF;
                        width: 40px;
                        height: 8px;
                        transform: skewX(-45deg);
                    }
                    .red-line {
                        background: #FF5A5A;
                        width: 40px;
                        height: 8px;
                        transform: skewX(-45deg);
                    }
                }
                .content {
                    @include flex-start-center;
                    @include space-between;
                    padding: 40px 24px;
                    .address-icon {
                        width: 80px;
                        height: 80px;
                        image {
                            width: 100%;
                            height: 100%;
                        }
                    }
                    .address-content {
                        font-size: 28px;
                        color: #595959;
                        width: 83%;
                        .name-phone {
                            padding-left: 16px;
                            .name {
                                font-family: PingFangSC-Semibold,serif;
                                font-size: 32px;
                                color: #262626;
                            }
                            .phone {
                                font-size: 28px;
                                color: #8C8C8C;
                            }
                        }
                        .address-detail {
                            padding-left: 16px;
                            padding-top: 18px;
                            font-size: 28px;
                            color: #262626;
                        }
                    }
                }
            }
        }
        .order-head-info {
            padding-bottom: 4px;
            padding-top: 4px;
            background-color: #ffffff;
            width: 94%;
            border-radius: 16px;
            box-shadow: 0 2px 36px 0 rgba(135,144,168,0.20);
            margin: 24px auto auto auto;
            overflow: hidden;
            .order-info-row {
                padding: 16px 24px;
                @include flex-start-center;
                @include space-between;
                font-size: 28px;
                .label {
                    color: #8C8C8C;
                }
                .value {
                    color: #262626;
                }
            }
        }
        .sync-info-container {
            width: 94%;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 2px 36px 0 rgba(135,144,168,0.20);
            margin: 24px auto 24px auto;
            overflow: hidden;
            padding: 24px 24px 0 24px;
            .title {
                @include flex-start-center;
                @include space-between;
                border-bottom: 2px solid #F2F2F2;
                font-size: 28px;
                padding-bottom: 24px;
                .sync-info-title {
                    color: #262626;
                }
            }
            .sync-info-content {
                .sync-info-row {
                    padding: 16px 0;
                    @include flex-start-center;
                    @include space-between;
                    font-size: 28px;
                    .label {
                        color: #8C8C8C;
                        line-height: 40px;
                    }
                    .value {
                        color: #262626;
                        text-align: right;
                        width: 80%;
                        line-height: 40px;
                        word-break: break-all;
                    }
                }
            }
        }
        .product-container {
            width: 94%;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 2px 36px 0 rgba(135,144,168,0.20);
            margin: 24px auto 24px auto;
            overflow: hidden;
            .title {
                @include flex-start-center;
                @include space-between;
                border-bottom: 2px solid #F2F2F2;
                font-size: 28px;
                padding: 32px 24px;
                .product-title {
                    color: #262626;
                }
              .right-icon-operate{
                padding: 0 25px 34px 0;
              }
            }
            .product-content {
                border-bottom: 2px solid #F2F2F2;
                padding: 32px 24px 0 24px;
                .item-top {
                    padding-top: 24px;
                    @include flex-start-center;
                    @include space-between;
                    .code {
                        padding: 6px 12px;
                        border-radius: 8px;
                        background: #A6B4C7;
                        font-size: 28px;
                        color: #FFFFFF;
                    }
                    .complimentary {
                        background-image: linear-gradient(180deg, #FF8560 0%, #FF4D2D 100%);
                        border-radius: 8px;
                        width: 92px;
                        height: 40px;
                        line-height: 40px;
                        font-size: 28px;
                        color: #FFFFFF;
                        text-align: center;
                    }
                }
                .item-middle {
                    padding-top: 24px;
                    @include flex-start-center;
                    @include space-between;
                    .item-middle-name {
                        font-family: PingFangSC-Semibold,serif;
                        font-size: 32px;
                        color: #262626;
                    }
                }
                .item-bottom {
                    font-size: 28px;
                    padding-bottom: 24px;
                    color: #8C8C8C;
                    .item-line-box{
                        padding-top: 24px;
                        @include flex-start-center;
                        .quota {
                            text {
                                color: #000000;
                            }
                            .unit {
                                font-size: 28px;
                                color: #8C8C8C;
                            }
                        }
                    }

                }
            }
        }
        .quota-type {
            font-size: 28px;
            color: #000;
            padding-top: 20px;

            .quota-title {
                margin-top: 10px;
            }

            .quote-item {
                margin: 20px 0;
                border-radius: 12px;

                .check-row {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 24px;
                }
            }

            .moneyInfo {
                display: flex;
                align-items: center;
                padding-top: 10px; 
                padding-bottom: 10px;

                .moneyNum {
                    font-size: 28px;
                    flex:2;
                    color:#8C8C8C;
                }
            }

            .light-blue {
                background: #F5FBFE;
                padding: 14px;
                margin: 20px -12px;
            }
        }
        .bottom-sticky {
            width: 100%;
            /*deep*/.link-button {
            width: 94%;
            height: 96px;
            margin-right: 24px;
            margin-left: 24px;
            font-size: 30px;
        }
        }
        .menu-code-record-list{
          width: 94%;
          background: #ffffff;
          border-radius: 16px;
          -webkit-box-shadow: 0 2rpx 36rpx 0 rgba(135, 144, 168, 0.2);
          box-shadow: 0 2rpx 36rpx 0 rgba(135, 144, 168, 0.2);
          margin: 24rpx auto 24rpx auto;
          overflow: hidden;
          .menu-code-title{
            border-bottom: 1px solid #f2f2f2;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            .menu-code-record-name{
              height: 100px;
              font-family: PingFangSC-Regular;
              font-size: 28px;
              color: #262626;
              line-height: 100px;
              background: #fff;
              border-radius: 20px 20px 0 0;
            }
            .right-icon-operate{
              padding-left: 0 25px 34px 0;
              height: 100px;
              line-height: 100px;
            }
          }

        .list-item {
          padding:28px 0 0 24px;
        }
        .list-item-row1{
          margin-bottom: 20px;
          font-family: PingFangSC-Regular;
          .left{
            display: flex;
            .lable-name{
              min-width: 90px;
              color: #8c8c8c;
              margin-right: 10px;
              font-size: 28px;
            }
            .text{
              color: #000;
              font-size: 28px;
            }
          }
        }
        .list-item-row2{
          font-family: PingFangSC-Regular;
          margin-bottom: 20px;
          .left{
            display: flex;
            .lable-name{
              min-width: 90px;
              margin-right: 10px;
              color: #000;
              font-size: 28px;
            }
            .text{
              color: #000;
              font-size: 28px;
            }
          }
        }
        .list-item-row3{
          color: #000;
          font-size: 28px;
          .status{
            color: #000;
          }
          .status-normal{
            color: #2EB3C2;
          }
          .status-abnormal{
            color: #FF5A5A;

          }

        }
        .gap-line{
          width: 100%;
          height: 2px;
          border-top: 2px solid #f2f2f2;
          margin-top: 28px;
        }
      }

    }
</style>
