<!--
终端-融资预订单审批
<AUTHOR>
@date 2023-09-11
-->
<template>
    <link-page class="finance-order-apprrove-page" ref="advanceOrderPage">
        <approval-history-point :approvalId="approvalId"  v-if="!$utils.isEmpty(approvalId)"></approval-history-point>
        <view class="menu-stair" >
            <view class="line">
                <view class="line-top"></view>
                <view class="line-bottom"></view>
            </view>
            <view class="stair-title">订单信息</view>
        </view>
        <!--菜单顶部-->
        <view class="menu-top" :style="menuTopStyle">
            <view class="order-status-image">
                <image :src="$imageAssets.shippedImage"></image>
            </view>
            <view class="order-status">{{orderHeadData.orderStatus | lov('EST_ORDER_STATUS')}}</view>
            <view class="acct-name">{{orderHeadData.acctName || orderHeadData.shipAcctName}}</view>
        </view>
        <view class="terminal-detail">
            <view class="terminal-address">
                <view class="address-line">
                    <view v-for="item in 19" :class="item % 4 === 1 ? '.blue-line' : (item % 2 === 0 ? '.white-line' : 'red-line')"></view>
                </view>
                <view class="content">
                    <view class="address-icon">
                        <image :src="$imageAssets.addressIconImage"></image>
                    </view>
                    <view class="address-content">
                        <view class="name-phone">
                            <text class="name">{{orderHeadData.customConsignee}}</text>
                            <text class="phone">{{orderHeadData.customMobilePhone}}</text>
                        </view>
                        <view class="address-detail">{{orderHeadData.province}}{{orderHeadData.city}}{{orderHeadData.district}}{{orderHeadData.addr}}</view>
                    </view>
                </view>
            </view>
        </view>
        <view class="order-head-info">
            <view class="order-info-row" v-if="orderHeadData.acctCode">
                <view class="label">客户编码</view>
                <view class="value">{{orderHeadData.acctCode}}</view>
            </view>
            <view class="order-info-row" v-if="orderHeadData.acctCategory">
                <view class="label">客户分类</view>
                <view class="value">{{orderHeadData.acctCategory | lov('ACCNT_CATEGORY')}}</view>
            </view>
            <view class="order-info-row" v-if="orderHeadData.acctLevel">
                <view class="label">客户规划等级</view>
                <view class="value">{{orderHeadData.acctLevel | lov('ACCT_LEVEL')}}</view>
            </view>
            <view class="order-info-row" v-if="orderHeadData.billTitle">
                <view class="label">营业执照名称</view>
                <view class="value">{{orderHeadData.billTitle}}</view>
            </view>
            <view class="order-info-row" v-if="orderHeadData.orderChildType">
                <view class="label">类型</view>
                <view class="value">{{orderHeadData.orderChildType | lov('ORDER_CHILD_TYPE')}}</view>
            </view>
            <view class="order-info-row" v-if="orderHeadData.orderNo">
                <view class="label">订单编号</view>
                <view class="value">{{orderHeadData.orderNo}}</view>
            </view>
            <view class="order-info-row" v-if="orderHeadData.orderDate">
                <view class="label">下单时间</view>
                <view class="value">{{orderHeadData.orderDate | date('YYYY-MM-DD HH:mm:ss')}}</view>
            </view>
            <view class="order-info-row" v-if="orderHeadData.creatorName">
                <view class="label">制单人</view>
                <view class="value">{{orderHeadData.creatorName}}</view>
            </view>
            <view class="order-info-row" v-if="orderHeadData.expectDate">
                <view class="label">计划配送时间</view>
                <view class="value">{{orderHeadData.expectDate}}</view>
            </view>
            <view class="order-info-row" v-if="orderHeadData.orderTotalQty">
                <view class="label">订单总数</view>
                <view class="value">
                    <text>{{orderHeadData.pcSum}}件{{orderHeadData.botSum}}瓶</text>
                </view>
            </view>
            <view class="order-info-row" v-if="orderHeadData.comments">
                <view class="label">备注</view>
                <view class="value">{{orderHeadData.comments}}</view>
            </view>
        </view>
        <view class="product">
            <view class="title">
                <view class="product-title">产品信息</view>
            </view>
            <view class="product-content" v-for="(item, index) in productList" :key="index">
                <view class="item-top">
                    <view class="code">{{item.prodCode}}</view>
                </view>
                <view class="item-middle">
                    <view class="item-middle-name">{{item.prodName}}</view>
                </view>
                <view class="item-supplier" v-if="item.supplierName"><text>供货商</text> {{item.supplierName}}</view>
                <view class="item-bottom"
                      :style="{'padding-bottom': (item.prodNum > item.quotaBalanceAmt && mdmCompanyCode === '5600' && terminalData.acctType === 'Terminal') ? '0' : '20px'}">
                    <view class="quota">订单数量 <text>{{Number(item.prodNum)}}</text> <text class="unit" v-if="item.prodNum">{{item.prodUnit | lov('PROD_UNIT')}}</text></view>
                </view>
                <view class="quota-type" v-if="item.isQuotaFlag">
                    <view class="quota-title">配额类型</view>
                    <!-- 计划内配额 -->
                    <view class="quote-item" :class="item.plannedProdNum ? 'light-blue' : ''">
                        <view class="check-row">
                            <view>计划内配额</view>
                            <view v-if="item.plannedProdNum">{{item.plannedProdNum}}{{item.prodUnit | lov('PROD_UNIT')}}</view>
                        </view>
                        <view class="moneyInfo" v-if="item.plannedProdNum">
                            <view class="moneyNum">计划内价格（元/瓶）</view>
                            <text class="text-content">{{item.finalCost}}</text>
                        </view>
                    </view>
                    <!-- 计划外配额 -->
                    <view class="quote-item" :class="item.unplannedProdNum ? 'light-blue' : ''"  v-if="!(isTeQu || isHuaiJiu)">
                        <view class="check-row">
                            <view>计划外配额</view>
                            <view v-if="item.unplannedProdNum">{{item.unplannedProdNum}}{{item.prodUnit | lov('PROD_UNIT')}}</view>
                        </view>
                        <view class="moneyInfo" v-if="item.unplannedProdNum">
                            <view class="moneyNum">计划外价格(元/瓶)</view>
                            <text class="text-content">{{item.planPrice}}</text>
                        </view>
                    </view>          
                </view>
                <view class="moneyInfo" v-else>
                    <view class="moneyNum">结算价(元/瓶)</view>
                    <text  style="flex: 1;font-size:28rpx;text-align: right;">{{item.finalCost}}</text>
                </view>
            </view>
        </view>

        <!--  付款信息 -->
        <view class="product" v-if="showFlag">
            <view class="title">付款信息</view>
            <view class="product-content">
                <view class="moneyInfo">
                    <view  class="moneyNum" >订单金额</view>
                    <text  style="flex: 1;font-size:28rpx;text-align: right">{{orderHeadData.receivableAmount}}</text>
                </view>
                <view class="moneyTitle">
                    <view  style="font-size:28rpx;color:#8C8C8C;">附件信息</view>
                    <lnk-img-watermark :parentId="orderHeadData.id"
                           moduleType="advanceOrder"
                           :drawWatermarkCancleFlag="false"
                           multiWatermark
                           v-if="orderHeadData.id"
                           :delFlag="false"
                           :album="false"
                           :newFlag="false">
                    </lnk-img-watermark>
                </view>
            </view>
        </view>
        <view class="approval-operator"  v-if="!$utils.isEmpty(approvalId)">
            <approval-operator :approvalId="approvalId"></approval-operator>
        </view>
    </link-page>
</template>

<script>
import approvalHistoryPoint from '../../lzlj/approval/components/approval-history-point'
import ApprovalOperator from '../../lzlj/approval/components/approval-operator'
import LnkImgWatermark from "../../core/lnk-img-watermark/lnk-img-watermark";
export default {
    name: "finance-order-apprrove-page",
    data () {
        let userInfo = this.$taro.getStorageSync('token').result;
        return {
            // 是否可显示配额类型
            isQuotaFlag: false,
            isTeQu: userInfo.coreOrganizationTile.brandCompanyCode === '5137', //是否特曲
            isJiaoLing: userInfo.coreOrganizationTile.brandCompanyCode === '5151', //是否窖龄
            isHuaiJiu: ['1204', '5910'].includes(userInfo.coreOrganizationTile.brandCompanyCode), //是否怀旧，蓉城
            // 可计入配额产品
            quotaProd: [],
            oldStr: '',
            showFlag:false,//控制显示
            companyFlag:false, //用来判断国窖公司
            amountReceivable:0, //国窖使用  应收金额
            realityReceivable:0, //国窖使用  实收金额
            menuTopStyle:`background-image: url('${this.$imageAssets.homeMenuBgImage}');`,
            basicOption: {},
            selectBoxHeight: 0,
            shadow: true,
            approvalFlag: false,
            productList: [],
            terminalData: {},
            addressData: {},
            allProductList: [],
            formOption: {
                id: this.pageParam.orderId ? this.pageParam.orderId :'',
                expectDate: this.$date.format(new Date(), 'YYYY-MM-DD'),
                comments: ''
            },
            orderHeadDetail: {},
            orderHeadData: {},
            approvalId: '',
            mdmCompanyCode: '',
            orderProductId: [],
            positionType: this.$taro.getStorageSync('token').result.positionType,
            submitBtnOnceFlag: true,
            orderHeadId: '',
            primaryPayType:'Credit',
            ADVANCE_ORDER:[ {text:'现金',val:'Cash'},
                            {text:'转账',val:'Transfer'},
                            {text:'赊销',val:'Credit'}],
            formOptionID:this.pageParam.orderId ? this.pageParam.orderId :'',
        }
    },
    watch:{
        productList:{
            handler(newVal,oldval) {
                this.realityReceivable = 0;
                newVal.map((item,index)=>{
                    this.realityReceivable+=Number(item.paidAmount);
                });
            },
            deep: true // 表示开启深度监听
        }
    },
    components: {
        approvalHistoryPoint,
        ApprovalOperator,
        LnkImgWatermark
    },
    async created() {
        await this.queryCfgProperty();
        await this.getProdQuota();
        const sceneObj = this.$store.getters['scene/getScene'];     // 消息场景对象
        if (this.pageParam.source === 'approval') {
            this.approvalFlag = true;
            this.approvalId = this.pageParam.data.id;
          await  this.queryOrderDetail(this.pageParam.data.flowObjId);
          await  this.$taro.setNavigationBarTitle({title: `融资预订单审批`});
        }
        if (sceneObj.query['approval_from'] === 'qw') {
            await this.queryOrderDetail(sceneObj.query['flowObjId']);
            this.approvalFlag = true;
            this.approvalId = sceneObj.query['approval_id'];
            await   this.$taro.setNavigationBarTitle({title: `融资预订单审批`});
        }
        this.mdmCompanyCode = this.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode;
        this.showFlag=true;
   },
    methods: {
        /**
         * 配额产品列表获取
         * <AUTHOR> 
         * @date 2025/2/18
         */
        async getProdQuota(){
            const params = {
                filtersRaw:[
                     {id: "companyCode", property: "companyCode", value: this.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode},
                     {id: "isEffective", property: "isEffective", value: "Y"},
                ]
            };
            const url = 'action/link/quotaProdInfo/queryByExamplePage'  
            try{
                const {success, rows} = await this.$http.post(url, params);
                if (success) {
                    this.quotaProd = rows.map(i=>{
                        return i.productCode
                    });
                }
            }catch(e){
                
            }
        },
        /**
         * 获得配置公司
         * <AUTHOR>
         * @date 2023/09/04
         * @param param
         */
        async queryCfgProperty() {
            const data = await this.$http.post('action/link/cfgProperty/queryByExamplePage', {
                filtersRaw: [{id: 'key', property: 'key', value: 'CalculateOrder', operator: '='}]
            });
            if (!this.$utils.isEmpty(data.rows)){
                this.cfgPropertyValue = data.rows[0].value.split(',');
                this.companyFlag = this.cfgPropertyValue.includes(this.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode)
            }
        },
        /**
         * 请求订单头数据
         * <AUTHOR>
         * @date 2023/09/04
         * @param id
         */
        async queryOrderDetail(id) {
            const that = this;
            const data = await this.$http.post('action/link/saleorder/queryById', {
                id: id,
                attr4: 'Y'
            }, {
                handleFailed(error) {
                    console.log('请求订单数据错误',error)
                }
            });
            if (data.success) {
                that.orderHeadData = data.result;
                this.isQuotaFlag = this.orderHeadData.acctType === 'Terminal' && ['5137', '5151', '5600', '1204', '5910'].includes(this.orderHeadData.orgBrandComCode);
                if(this.companyFlag) {
                    that.formOptionID = data.result.id;
                    that.formOption.id = data.result.id;
                    that.formOption.payComment=data.result.payComment;
                    that.amountReceivable = Number(data.result.receivableAmount); //国窖使用  应收金额
                    that.realityReceivable =  Number(data.result.actualAmount); //国窖使用  实收金额
                    that.primaryPayType = data.result.primaryPayType;
                }
                if (that.pageParam.source === 'orderList') {
                    that.formOption = data.result;
                    that.addressData.consignee = data.result.customConsignee;
                    that.addressData.mobilePhone = data.result.customMobilePhone;
                }
            }
            await that.queryOrderRows(id);
        },
        /**
         * 订单行数据
         * <AUTHOR>
         * @date 2023/09/04
         * @param id
         */
        async queryOrderRows(id) {
            const that = this;
            that.$http.post('action/link/saleorderitem/queryByExamplePage',{
                filtersRaw:[{id:'headId',property:'headId',value: id,operator:'='}],
                sort: 'created',
                order: 'desc',
                terminal5600Flag: that.terminalData.acctType === 'Terminal' && that.terminalData.mdmCompanyCode === '5600' ? 'Y' : 'N'
            }).then(async data => {
                if (data.success) {
                    that.orderHeadId = data.rows[0].headId;
                    let prodList = data.rows;
                    prodList.forEach(item => {
                        that.orderProductId.push(item.prodId);
                        if (typeof(item.prodNum) === 'string') {
                            item.prodNum = Number(item.prodNum)
                        }
                        that.$set(item, 'isQuotaFlag', that.isQuotaFlag && that.quotaProd.indexOf(item.prodCode) > -1);
                        if (typeof(item.plannedProdNum) === 'string') {
                            item.plannedProdNum = Number(item.plannedProdNum);
                        }
                        if (typeof(item.unplannedProdNum) === 'string') {
                            item.unplannedProdNum = Number(item.unplannedProdNum);
                        }
                        item.row_status = 'UPDATE';
                    });
                    that.productList = prodList;
                }
            })
        },
        /**
         * 获取计划内外配额
         * <AUTHOR>
         * @date 2024/12/17 11:54
         * @param prodRows 当前选中的产品数据
         */
        getPlanQuota(prodRows) {
            return new Promise(async (resolve) => {
                const list = prodRows.map(item => ({
                    prodId: item.prodId,
                    accntId: item.accntId || item.acctId
                }));
                const url =`action/link/saleCategory/${this.isTeQu ? 'queryTeQuQuotaAmt' : this.isJiaoLing ? 'queryJiaoLingQuotaAmt' : 'queryQuotaAmt'}`
                const {success, rows} = await this.$http.post(url, list);
                if (success) {
                    prodRows.forEach(item => {
                        const i = rows.find((t) => t.prodId === item.prodId);
                        if (i) {
                            item.plannedQuotaTotalAmt = i.plannedQuotaTotalAmt;
                            item.unplannedQuotaTotalAmt = i.unplannedQuotaTotalAmt;
                        }  
                    });
                    resolve(prodRows);
                }
            });
        },
    }
}
</script>

<style lang="scss">
.finance-order-apprrove-page {
    padding-bottom: 40px;
    .lnk-img-watermark-item {
        margin-left: 16px;
    }
    .moneyTitle{
        padding-top: 10px; padding-bottom: 10px;
        .lnk-img .lnk-img-item{
            margin-left: 15px;
            width: 145rpx !important;
            height: 145rpx!important;
        }
        .lnk-img-watermark-box .lnk-img-watermark{
            margin: 0 !important;
            width: 100% !important;
        }
    }
    .moneyInfo{
        display: flex;
        padding-top: 10px; padding-bottom: 10px;
        .moneyNum{
            font-size: 28px;
            flex:2;
            color:#8C8C8C;
        }
        input{
            border: solid 1px #8C8C8C;
        }
    }
    .menu-stair {
        margin-left: 24px;
        padding-top: 24px;
        @include flex-start-center;
        .line {
            .line-top {
                width: 8px;
                height: 16px;
                background: #3FE0E2;
            }

            .line-bottom {
                width: 8px;
                height: 16px;
                background: #2F69F8;
            }
        }

        .stair-title {
            margin-left: 16px;
            font-family: PingFangSC-Semibold, serif;
            font-size: 32px;
            color: #262626;
            letter-spacing: 1px;
            line-height: 32px;
        }
    }
    .menu-top {
        margin-top: 24px;
        margin-bottom: 24px;
        width: 750px;
        height: 220px;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        position: sticky;
        z-index: 1;
        .order-status-image {
            position: absolute;
            margin-top: 40px;
            margin-right: 50px;
            right: 0;
            width: 222px;
            height: 140px;
            image {
                width: 100%;
                height: 100%;
            }
        }
        .order-status {
            font-size: 32px;
            padding-left: 50px;
            padding-top: 50px;
            color: #FFFFFF;
        }
        .acct-name {
            font-size: 28px;
            color: #FFFFFF;
            padding-top: 24px;
            padding-left: 50px;
        }

    }
    .terminal-detail {
        background-image: linear-gradient(180deg, #FFFFFF 0%, rgba(255,255,255,0.00) 100%);
        .terminal-name {
            font-family: PingFangSC-Semibold,serif;
            padding-top: 40px;
            padding-bottom: 40px;
            width: 100%;
            text-align: center;
            font-size: 32px;
            color: #262626;
            .icon-right {
                color: #BFBFBF;
                font-size: 32px;
            }
        }
        .terminal-address {
            background: #fff;
            width: 94%;
            border-radius: 16px;
            box-shadow: 0 2px 36px 0 rgba(135,144,168,0.20);
            margin: auto;
            .address-line {
                @include flex;
                overflow: hidden;
                .blue-line {
                    background: #2F69F8;
                    width: 40px;
                    height: 8px;
                    transform: skewX(-45deg);
                }
                .white-line {
                    background: #FFFFFF;
                    width: 40px;
                    height: 8px;
                    transform: skewX(-45deg);
                }
                .red-line {
                    background: #FF5A5A;
                    width: 40px;
                    height: 8px;
                    transform: skewX(-45deg);
                }
            }
            .content {
                @include flex-start-center;
                @include space-between;
                padding: 40px 24px;
                .address-icon {
                    width: 80px;
                    height: 80px;
                    image {
                        width: 100%;
                        height: 100%;
                    }
                }
                .address-content {
                    font-size: 28px;
                    color: #595959;
                    width: 83%;
                    .name-phone {
                        padding-left: 16px;
                        .name {
                            font-family: PingFangSC-Semibold,serif;
                            font-size: 32px;
                            color: #262626;
                        }
                        .phone {
                            font-size: 28px;
                            color: #8C8C8C;
                        }
                    }
                    .address-detail {
                        padding-left: 16px;
                        padding-top: 18px;
                        font-size: 28px;
                        color: #262626;
                    }
                }
                .select-address {
                    font-size: 28px;
                    color: #BFBFBF;
                    text-align: right;
                    width: 83%;
                }
                .iconfont {
                    color:#BFBFBF;
                    font-size: 32px;
                }
            }
        }
    }
    .order-head-info {
        padding-bottom: 4px;
        padding-top: 4px;
        background-color: #ffffff;
        width: 94%;
        border-radius: 16px;
        box-shadow: 0 2px 36px 0 rgba(135,144,168,0.20);
        margin: 24px auto auto auto;
        overflow: hidden;
        .order-info-row {
            padding: 16px 24px;
            @include flex-start-center;
            @include space-between;
            font-size: 28px;
            .label {
                color: #8C8C8C;
            }
            .value {
                color: #262626;
            }
        }
        .order-info-col {
            padding: 16px 24px;
            font-size: 28px;
            .label {
                color: #8C8C8C;
            }
            .value {
                min-height: 120px;
                border: 1px solid #D8D8D8;
                border-radius: 8px;
                padding-top: 12px;
                padding-left: 12px;
                margin-top: 24px;
                color: #262626;
            }
        }
    }
    .logistics {
        /*deep*/.link-icon {
        font-size: 28px;
    }
        /*deep*/.link-input-text-align-left {
                    text-align: right;
                }
        /*deep*/.link-item {
                    padding: 24px;
                }
        width: 94%;
        border-radius: 16px;
        box-shadow: 0 2px 36px 0 rgba(135,144,168,0.20);
        margin: 24px auto auto auto;
        overflow: hidden;
    }
    .product {
        width: 94%;
        background: #ffffff;
        border-radius: 16px;
        box-shadow: 0 2px 36px 0 rgba(135,144,168,0.20);
        margin: 24px auto 24px auto;
        overflow: hidden;
        .title {
            @include flex-start-center;
            @include space-between;
            border-bottom: 2px solid #F2F2F2;
            font-size: 28px;
            padding: 32px 24px;
            .product-title {
                color: #262626;
            }
            .add {
                color: #2F69F8;
            }
        }
        .product-content {
            border-bottom: 2px solid #F2F2F2;
            padding: 32px 24px 0 24px;
            .item-top {
                padding-top: 24px;
                @include flex-start-center;
                @include space-between;
                .code {
                    padding: 6px 12px;
                    border-radius: 8px;
                    background: #A6B4C7;
                    font-size: 28px;
                    color: #FFFFFF;
                }
                .icon-close {
                    color: #BFBFBF;
                    font-size: 32px;
                }
            }
            .item-middle {
                padding-top: 24px;
                @include flex-start-center;
                @include space-between;
                .item-middle-name {
                    font-family: PingFangSC-Semibold,serif;
                    font-size: 32px;
                    color: #262626;
                }
                .unit {
                    font-size: 28px;
                    color: #8C8C8C;
                }
            }
            .item-supplier {
                font-size: 28px;
                padding-top: 24px;
                text {
                    color: #8C8C8C;
                }
            }
            .item-bottom {
                padding-top: 24px;
                font-size: 28px;
                color: #8C8C8C;
                @include flex-end-center;
                @include space-between;
                .quota {
                    text {
                        color: #000000;
                    }
                    .unit {
                        font-size: 28px;
                        color: #8C8C8C;
                    }
                }
                .stock{
                    @include flex-start-center;
                    .stock-num {
                        padding-right: 16px;
                    }
                    .stock-input {
                        background: #F2F2F2;
                        border-radius: 8px;
                        @include flex-start-center;
                        .minus-add {
                            font-size: 32px;
                            width: 44px;
                            text-align: center;
                            height: 48px;
                            color: #BFBFBF;
                        }
                        input {
                            text-align: center;
                            width: 80px;
                            color: #262626;
                            height: 48px;
                        }
                    }
                }
                .prod-unit-radio {
                    display: flex;
                }
            }
            .item-bottom-between {
                padding-top: 24px;
                font-size: 28px;
                color: #8C8C8C;
                @include flex-end-center;
                .quota {
                    text {
                        color: #000000;
                    }
                    .unit {
                        font-size: 28px;
                        color: #8C8C8C;
                    }
                }
                .stock{
                    @include flex-start-center;
                    .stock-num {
                        padding-right: 16px;
                    }
                    .stock-input {
                        background: #F2F2F2;
                        border-radius: 8px;
                        @include flex-start-center;
                        .minus-add {
                            font-size: 32px;
                            width: 44px;
                            text-align: center;
                            height: 48px;
                            color: #BFBFBF;
                        }
                        input {
                            text-align: center;
                            width: 80px;
                            color: #262626;
                            height: 48px;
                        }
                    }
                }
            }

            .warn {
                padding-top: 16px;
                padding-bottom: 40px;
                width: 100%;
                text-align: right;
                font-size: 24px;
                color: #FF5A5A;
                .icon-jinggao {
                    font-size: 28px;
                }
            }
        }
    }
    .quota-type {
        font-size: 28px;
        color: #000;
        padding-top: 20px;

        .quota-title {
            margin-top: 10px;
        }

        .quote-item {
            margin: 20px 0;
            border-radius: 12px;

            .check-row {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 24px;
            }
        }

        .moneyInfo {
            display: flex;
            align-items: center;
            padding-top: 10px; 
            padding-bottom: 10px;

            .moneyNum {
                font-size: 28px;
                flex:2;
                color:#8C8C8C;
            }
        }

        .light-blue {
            background: #F5FBFE;
            padding: 14px;
            margin: 20px -12px;
        }
    }
    .model-title {
        display: flex;
        margin-left: 24px;
        .title {
            font-family: PingFangSC-Regular,serif;
            font-size: 32px;
            color: #262626;
            letter-spacing: 0;
            text-align: center;
            line-height: 80px;
            height: 80px;
            width: 90%;
        }
        .icon-close {
            color: #BFBFBF;
            font-size: 32px;
            line-height: 80px;
            height: 80px;
        }
    }
    .model-title {
        display: flex;
        margin-left: 24px;
        .title {
            font-family: PingFangSC-Regular,serif;
            font-size: 32px;
            color: #262626;
            letter-spacing: 0;
            text-align: center;
            line-height: 80px;
            height: 80px;
            width: 90%;
        }
        .icon-close {
            color: #BFBFBF;
            font-size: 32px;
            line-height: 80px;
            height: 80px;
        }
    }
    .select-box {
        @include flex-start-center;
        border-bottom: 1px solid #F2F2F2;
        /*deep*/.link-item { padding: 20px; }
        /*deep*/.mp-round { color: #BFBFBF; }
        .select-left {
            width: 100%;
            padding-left: 24px;
            .prod-num {
                display: flex;
                justify-content: space-between;
                text {
                    font-family: PingFangSC-Regular,serif;
                    font-size: 28px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    line-height: 28px;
                    background: #A6B4C7;
                    border-radius: 8px;
                    padding: 6px 12px;
                }
                margin-top: 8px;
                margin-bottom: 20px;

                .orange {
                    color: #FF9900;
                    background-color: white;
                }
                .blue {
                    color: #2963F2;
                    background-color: white;
                }
            }
            .store-name {
                width: 100%;
                font-family: PingFangSC-Regular,serif;
                font-size: 28px;
                color: #262626;
                letter-spacing: 0;
                line-height: 28px;
                .credit-no {
                    color: #BFBFBF;
                }
            }
            .supplier {
                padding-top: 12px;
                font-size: 28px;
                color: #262626;
                text {
                    color: #8C8C8C;
                }
            }
        }
        .select-right {
            margin-right: 24px;
            .iconfont {
                font-size: 40px;
                color: #BFBFBF;
            }
            .icon-yiwanchengbuzhou {
                font-size: 40px;
                color: #2F69F8;
            }
        }
    }
    .blank {
        width: 100%;
        height: 150px;
        background: #f2f2f2;
    }
    .bottom-btn {
        padding-top: 16px;
        padding-bottom: 34px;
        .all-select {
            height: 96px;
            padding-left: 24px;
            @include flex-start-center();
            width: 50%;
            font-size: 28px;
            color: #595959;
            letter-spacing: 0;
            line-height: 28px;
            .iconfont {
                font-size: 40px;
                color: #BFBFBF;
            }
            .icon-yiwanchengbuzhou {
                color: $color-primary;
            }
            .all-select-text {
                padding-left: 16px;
            }
        }
        .sure-btn {
            width: 340px;
            height: 96px;
            margin-right: 24px;
        }
    }
    .bottom-sticky {
        width: 100%;
        /*deep*/.link-button {
        width: 94%;
        height: 96px;
        margin-right: 24px;
        margin-left: 24px;
        font-size: 30px;
    }
    }
    .approval-operator {
        background-color: #ffffff;
    }
}
</style>
