<template>
    <view class="terminal-item">
        <image class="terminal-logo" :src="itemData.storePicPreKey ? $image.getSignedUrl(itemData.storePicPreKey) : $imageAssets.terminalDefaultImage" lazy-load="true"></image>
        <view class="item-right">
            <view class="terminal-name">{{itemData.acctCode}}</view>
            <view class="item-right-common">{{itemData.acctName}}</view>
            <view class="item-right-common">{{itemData.salesmanBrandCom}}</view>
            <view class="item-right-common">{{itemData.addrDetailAddr}}</view>
        </view>
    </view>
</template>
<script>
export default {
    name: 'terminal-item',
    props: {
        itemData: {
            type: Object,
            default(){
                return {}
            }
        }
    }
}
</script>
<style lang="scss">
 .terminal-item {
    display: flex;
    .terminal-logo {
        box-shadow: 0 4px 31px 0 rgba(0,44,152,0.22);
        border-radius: 16px;
        width: 128px;
        height: 128px;
        overflow: hidden;
        margin-right: 28px;
    }
    .item-right {
        .terminal-name {
            font-family: PingFangSC-Semibold,serif;
            font-size: 32px;
            color: #262626;
            letter-spacing: 0;
            line-height: 32px;
            margin-bottom: 10px;
        }
    }
    .item-right-common {
        margin-top: 10px;
        font-size: 24px;
        color: #262626;
        max-width: 500px;
    }
 }
</style>