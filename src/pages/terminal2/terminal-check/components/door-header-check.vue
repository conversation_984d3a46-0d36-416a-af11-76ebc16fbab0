<!--
@created<PERSON><PERSON>
@date  2025/04/25
@description 终端检核-门头店招
-->
<template>
    <view class="door-header-check">
        <line-title title="门头店招"></line-title>
        <view class="term pd-bottom">
            <view class="small-title">建设效果</view>
            <view class="door-img-list">
                    <lnk-img-watermark :parentId="terminalData.id"
                                       moduleType="agrSign"
                                       :delFlag="false"
                                       :album="false"
                                       :newFlag="false">
                    </lnk-img-watermark>
            </view>
            <view class="check-item">
                <view class="seq">1.</view>
                <view class="check-detail">
                    <link-form-item label="是否我品门头" class="flag" :disabled="!isEdit">
                        <link-radio-group v-model="formData.myFront" @change="changeMyFront">
                            <item :arrow="false">
                                <link-checkbox toggleOnClickItem val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false">
                                <link-checkbox toggleOnClickItem val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                    <link-form-item label="竞品门头名称" class="flag" v-if="formData.myFront === 'N'" :disabled="!isEdit">
                        <link-lov v-model="formData.competing" type="TERMINAL_CHECK_DOOR_HEADER"></link-lov>
                    </link-form-item>
                    <view class="image-title" v-if="formData.myFront === 'N'">竞品拍照</view>
                    <lnk-img-watermark
                        v-if="formData.id"
                        v-show="formData.myFront === 'N'"
                        ref="doorHeaderShot"
                        :parentId="formData.id"
                        moduleType="doorHeaderShot"
                        :delFlag="isEdit"
                        :newFlag="isEdit"
                        :album="true"
                        :displayCustomizedWatermark="true"
                        :useModuleName="'终端检查'"
                        @imgUploadSuccess="doorHeaderShotArr"
                        @imgDeleteSuccess="doorHeaderShotArr"
                        moduleName="竞品门头截图">
                    </lnk-img-watermark>
                </view>
            </view>
            <view class="check-item">
                <view class="seq">2.</view>
                <view class="check-detail">
                    <link-form-item label="门头与档案照片是否一致" class="flag" :disabled="!isEdit">
                        <link-radio-group v-model="formData.frontPhoto" @change="changeFlag">
                            <item :arrow="false">
                                <link-checkbox toggleOnClickItem val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false">
                                <link-checkbox toggleOnClickItem val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                    <view class="image-title" v-if="formData.frontPhoto === 'N'">门头拍照</view>
                    <lnk-img-watermark
                        v-if="formData.id"
                        v-show="formData.frontPhoto === 'N'"
                        ref="doorHeaderfiles"
                        :parentId="formData.id"
                        moduleType="doorHeaderfiles"
                        :delFlag="isEdit"
                        :newFlag="isEdit"
                        :album="true"
                        :displayCustomizedWatermark="true"
                        :useModuleName="'终端检查'"
                        @imgUploadSuccess="doorHeaderfilesArr"
                        @imgDeleteSuccess="doorHeaderfilesArr"
                        moduleName="门头拍照">
                    </lnk-img-watermark>
                </view>
            </view>
            <view class="check-item check-item-main" v-if="formData.myFront === 'Y'">
                <view class="num">3.</view>
                <view class="check-detail">
                    <link-form-item label="门头大小" class="flag" :disabled="!isEdit">
                        <link-lov v-model="formData.frontSize" type="DOOR_SIZE"></link-lov>
                    </link-form-item>
                </view>
            </view>
            <view class="check-item check-item-main" v-if="formData.myFront === 'Y'">
                <view class="num">4.</view>
                <view class="check-detail">
                    <link-form-item label="门头位置" class="flag" :disabled="!isEdit">
                        <link-lov v-model="formData.frontPrsition" type="DOOR_PROTOCOL"></link-lov>
                    </link-form-item>
                </view>
            </view>
            <view class="check-item check-item-main" v-if="formData.myFront === 'Y'">
                <view class="num">5.</view>
                <view class="check-detail">
                    <link-form-item label="门头视线" class="flag" :disabled="!isEdit">
                        <link-lov v-model="formData.frontSight" type="DOOR_SIGHT"></link-lov>
                    </link-form-item>
                </view>
            </view>
        </view>
    </view>
</template>
<script>
import LineTitle from './line-title.vue'
import LnkImgWatermark from '../../../core/lnk-img-watermark/lnk-img-watermark';
export default {
    name: 'door-header-check',
    props: {
        formData: Object,
        isEdit: Boolean,
        terminalData: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    components:{LineTitle, LnkImgWatermark},
    computed: {
        // 门头照片
        doorHeaderImg() {
            return this.terminalData.storePicPreKey ? this.$image.getSignedUrl(this.terminalData.storePicPreKey) : this.$imageAssets.terminalDefaultImage;
        }
    },
    data() {
        return {
            doorHeaderShotUploadImg: [],  // 门头截图
            doorHeaderfilesUploadImg: [], // 门头与档案截图
            isMyFront: '',
            isFrontPhoto: ''
        }
    },
    methods: {
        changeMyFront(val) {
            if (val === this.isMyFront) return;
            this.isMyFront = val;
            if (val === 'Y' && this.doorHeaderShotUploadImg.length) {
                this.$refs.doorHeaderShot.delALLTypeImg()
            }
        },
        /**
         * 改变标志
         * <AUTHOR>
         * @date	2025/04/25
         */
        changeFlag(val) {
            if (val === this.isFrontPhoto) return;
            this.isFrontPhoto = val;
            if (val === 'Y' && this.doorHeaderfilesUploadImg.length) {
                this.$refs.doorHeaderfiles.delALLTypeImg()
            }
        },
        /**
         * 门头截图
         * <AUTHOR>
         * @date	2025/04/24
        */
        doorHeaderShotArr(param) {
            this.doorHeaderShotUploadImg = param;
            this.$emit('imgList', {
                listName: 'doorHeaderImg',
                list: param
            });
        },
        /**
         * 门头与档案截图
         * <AUTHOR>
         * @date	2025/04/24
        */
        doorHeaderfilesArr(param) {
            this.doorHeaderfilesUploadImg = param;
            this.$emit('imgList', {
                listName: 'doorHeaderFilesImg',
                list: param
            });
        }
    }
}
</script>
<style lang="scss">
.door-header-check {
    .door-img-list {
        display: flex;
        justify-content: center;
    }
    .term {
        .lnk-img-watermark-item {
            &:nth-child(4n+1) {
                margin-right: 10px !important;
            }
        }
        
    }
    .image-title {
        display: flex;
        font-size: 28px;
        padding: 24px 24px 24px 0;
    }
    .pd-bottom {
        padding-bottom: 40px !important;
    }
}
</style>