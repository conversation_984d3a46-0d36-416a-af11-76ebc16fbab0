<!--
@created<PERSON><PERSON>
@date  2025/04/24
@description 终端检查-数字化政策运用
-->
<template>
    <view class="number-policy-utilize-check">
        <line-title title="数字化政策应用"></line-title>
        <view class="term">
            <view class="small-title">终端会员</view>
            <view class="check-item">
                <view class="seq">1.</view>
                <view class="check-detail">
                    <link-form-item label="是否了解终端会员中心" class="flag" :disabled="!isEdit">
                        <link-radio-group v-model="formData.terminalClear">
                            <item :arrow="false" @tap.stop="changeFlag('terminalClear', 'Y')">
                                <link-checkbox val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false" @tap.stop="changeFlag('terminalClear', 'N')">
                                <link-checkbox val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                </view>
            </view>
            <view class="check-item">
                <view class="seq">2.</view>
                <view class="check-detail">
                    <link-form-item label="是否了解通过消费者开瓶获得会员积分，并在商城里兑换商品" class="flag" :disabled="!isEdit">
                        <link-radio-group v-model="formData.terminalRedemption">
                            <item :arrow="false" @tap.stop="changeFlag('terminalRedemption', 'Y')">
                                <link-checkbox val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false" @tap.stop="changeFlag('terminalRedemption', 'N')">
                                <link-checkbox val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                </view>
            </view>
        </view>

        <view class="term">
            <view class="small-title">再来一瓶</view>
            <view class="check-item">
                <view class="seq">1.</view>
                <view class="check-detail">
                    <link-form-item label="是否知晓再来一瓶活动政策" class="flag" :disabled="!isEdit">
                        <link-radio-group v-model="formData.knowActivity">
                            <item :arrow="false" @tap.stop="changeFlag('knowActivity', 'Y')">
                                <link-checkbox val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false" @tap.stop="changeFlag('knowActivity', 'N')">
                                <link-checkbox val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                </view>
            </view>
            <view class="check-item">
                <view class="seq">2.</view>
                <view class="check-detail">
                    <link-form-item label="是否知晓再来一瓶终端服务费政策" class="flag" :disabled="!isEdit">
                        <link-radio-group v-model="formData.oneMorePolicy">
                            <item :arrow="false" @tap.stop="changeFlag('oneMorePolicy', 'Y')">
                                <link-checkbox val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false" @tap.stop="changeFlag('oneMorePolicy', 'N')">
                                <link-checkbox val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                </view>
            </view>
            <view class="check-item">
                <view class="seq">3.</view>
                <view class="check-detail">
                    <link-form-item label="是否知晓再来一瓶服务费申请兑付流程" class="flag" :disabled="!isEdit">
                        <link-radio-group v-model="formData.knowRedemption">
                            <item :arrow="false" @tap.stop="changeFlag('knowRedemption', 'Y')">
                                <link-checkbox val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false" @tap.stop="changeFlag('knowRedemption', 'N')">
                                <link-checkbox val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                </view>
            </view>
            <view class="check-item">
                <view class="seq">4.</view>
                <view class="check-detail">
                    <link-form-item label="再来一瓶服务费用是否兑付及时" class="flag" :disabled="!isEdit">
                        <link-radio-group v-model="formData.redemptionOnTime">
                            <item :arrow="false" @tap.stop="changeFlag('redemptionOnTime', 'Y')">
                                <link-checkbox val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false" @tap.stop="changeFlag('redemptionOnTime', 'N')">
                                <link-checkbox val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                </view>
            </view>
            <view class="check-item">
                <view class="seq">5.</view>
                <view class="check-detail">
                    <link-form-item label="是否兑付过服务费" class="flag" :disabled="!isEdit">
                        <link-radio-group v-model="formData.redemptionServiceFee">
                            <item :arrow="false" @tap.stop="changeFlag('redemptionServiceFee', 'Y')">
                                <link-checkbox val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false" @tap.stop="changeFlag('redemptionServiceFee', 'N')">
                                <link-checkbox val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                </view>
            </view>
        </view>
        
        <view class="term">
            <view class="small-title">数字化费用</view>
            <view class="check-item">
                <view class="seq">1.</view>
                <view class="check-detail">
                    <link-form-item label="店老板是否知晓窖友汇提现功能" class="flag" :disabled="!isEdit">
                        <link-radio-group v-model="formData.knowDrawCash">
                            <item :arrow="false" @tap.stop="changeFlag('knowDrawCash', 'Y')">
                                <link-checkbox val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false" @tap.stop="changeFlag('knowDrawCash', 'N')">
                                <link-checkbox val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                </view>
            </view>
            <view class="check-item">
                <view class="seq">2.</view>
                <view class="check-detail">
                    <link-form-item label="是否知晓开瓶返利，扫码抢盘等政策活动" class="flag" :disabled="!isEdit">
                        <link-radio-group v-model="formData.rebateScan">
                            <item :arrow="false" @tap.stop="changeFlag('rebateScan', 'Y')">
                                <link-checkbox val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false" @tap.stop="changeFlag('rebateScan', 'N')">
                                <link-checkbox val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                </view>
            </view>
        </view>

        <view class="term">
            <view class="small-title">易融平台应用</view>
            <view class="check-item">
                <view class="seq">1.</view>
                <view class="check-detail">
                    <link-form-item label="采购泸州老窖产品时是否需要融资助力" class="flag" :disabled="!isEdit">
                        <link-radio-group v-model="formData.cashFlowDifficulty">
                            <item :arrow="false" @tap.stop="changeFlag('cashFlowDifficulty', 'Y')">
                                <link-checkbox val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false" @tap.stop="changeFlag('cashFlowDifficulty', 'N')">
                                <link-checkbox val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                </view>
            </view>
            <view class="check-item">
                <view class="seq">2.</view>
                <view class="check-detail">
                    <link-form-item label="是否知晓泸州老窖公司易融超市" class="flag" :disabled="!isEdit">
                        <link-radio-group v-model="formData.knowRyPlatform">
                            <item :arrow="false" @tap.stop="changeFlag('knowRyPlatform', 'Y')">
                                <link-checkbox val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false" @tap.stop="changeFlag('knowRyPlatform', 'N')">
                                <link-checkbox val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                </view>
            </view>
        </view>

        <view class="term">
            <view class="small-title">其他问题调研</view>
            <view class="check-item">
                <view class="seq">1.</view>
                <view class="check-detail">
                    <link-form-item label="是否有竞品开瓶酒销售" class="flag" :disabled="!isEdit">
                        <link-radio-group v-model="formData.competingOnsale">
                            <item :arrow="false" @tap.stop="changeFlag('competingOnsale', 'Y')">
                                <link-checkbox val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false" @tap.stop="changeFlag('competingOnsale', 'N')">
                                <link-checkbox val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                </view>
            </view>
            <view class="check-item pd-bottom">
                <view class="seq">2.</view>
                <view class="check-detail">
                    <link-form-item label="其他问题反馈" class="flag" :disabled="!isEdit">
                        <link-radio-group v-model="formData.advice">
                            <item :arrow="false" @tap.stop="changeFlag('advice', 'Y')">
                                <link-checkbox val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false" @tap.stop="changeFlag('advice', 'N')">
                                <link-checkbox val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                    <link-form-item label="问题反馈" v-if="formData.advice === 'Y'" class="advice-textarea"></link-form-item>
                    <view class="textarea-container" v-if="formData.advice === 'Y'">
                        <link-textarea :disabled="!isEdit" :placeholder="'请输入问题反馈，字数长度上限为500'" :height="140" mode="textarea" v-model="formData.remark" :nativeProps="{maxlength: 500}"></link-textarea>
                        <view class="textarea-num">
                            {{`${formData.remark ? formData.remark.length : 0}/500`}}
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script lang="jsx">
import LnkImgWatermark from '../../../core/lnk-img-watermark/lnk-img-watermark';
import LineTitle from './line-title.vue'

export default {
    name: 'number-policy-utilize-check',
    props: {
        formData: Object,
        isEdit: Boolean
    },
    components: {LnkImgWatermark, LineTitle},
    data() {
        return {}
    },
    methods: {
        /**
         * 改变标志
         * <AUTHOR>
         * @date	2023/5/18 11:08
         */
        changeFlag(label, val) {
            if (!this.isEdit) {
                return;
            }
            this.$set(this.formData, label, val);
        }
    }
}
</script>

<style lang="scss">
.number-policy-utilize-check {
    .textarea-container {
        position: relative;
        flex: 1;
        margin-top: 20px;
        .link-textarea {
            .link-textarea-content {
                border-radius: 8px;
            }
        }
        .textarea-num {
            position: absolute;
            right: 34px;
            bottom: 4px;
            font-size: 24px;
        }
    }
    .pd-bottom {
        padding-bottom: 40px !important;
    }
}
</style>
