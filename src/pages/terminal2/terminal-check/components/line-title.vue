<template>
    <view class="line-title">
        <view class="line">
            <view class="line-top"></view>
            <view class="line-bottom"></view>
        </view>
        <view class="stair-title">{{title}}</view>
    </view>
</template>
<script>
export default {
    name: 'line-title',
    props: {
        title: String
    }
}
</script>
<style lang="scss">
.line-title {
    width: 100%;
    margin-left: 24px;
    padding: 32px 0;
    @include flex-start-center;

    .line {
        clear: both;

        .line-top {
            width: 8px;
            height: 16px;
            background: #3FE0E2;
        }

        .line-bottom {
            width: 8px;
            height: 16px;
            background: #2F69F8;
        }
    }

    .stair-title {
        margin-left: 16px;
        font-family: PingFangSC-Semibold, serif;
        font-size: 32px;
        color: #262626;
        letter-spacing: 1px;
        line-height: 32px;
    }
}   
</style>