<!--
@created<PERSON><PERSON>
@date  2025/04/25
@description 终端检核-产品展陈
-->
<template>
    <view class="product-check">
        <line-title title="产品展陈"></line-title>
        <view class="term">
            <view class="check-item">
                <view class="check-detail">
                    <link-form-item label="是否存在我品展陈" class="flag" :disabled="!isEdit">
                        <link-radio-group v-model="formData.myDisplay" @change="changeMyDisplay">
                            <item :arrow="false">
                                <link-checkbox toggleOnClickItem val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false">
                                <link-checkbox toggleOnClickItem val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                </view>
            </view>
        </view>
        <view class="term" style="padding-bottom: 5px;" v-if="formData.myDisplay === 'Y'">
            <view class="display-content-title">
                <view class="label">
                    陈列品项及数量
                </view>
                <view class="add" v-if="isEdit" @tap="openDisplayList">
                    +添加
                </view>
            </view>
            <view class="display-content" v-if="formData.itemList && formData.itemList.length">
                <view class="display-content-warp" v-for="(val, index) in formData.itemList" :key="`display_${index}`">
                    <view class="display-common">
                        <view class="display-lable">
                            品项名称
                        </view>
                        <view class="display-val" v-if="productLovType">
                            {{val.item | lov(productLovType)}}
                        </view>                   
                    </view>
                    <view class="display-common">
                        <view class="display-lable">
                            实际陈列数量
                        </view>
                        <view class="display-val">
                            <link-number
                                class="container-input"
                                :precision="0"
                                hideButton
                                :disabled="!isEdit"
                                :nativeAttrs="{
                                    placeholder: '请输入数量'
                                }"
                                v-model="val.num">
                            </link-number>
                        </view>                   
                    </view>
                </view>
            </view>
        </view>
        <view class="term" v-if="formData.myDisplay === 'Y'">
            <view class="small-title">陈列位置</view>
            <view class="check-item check-item-main">
                <view class="num">1.</view>
                <view class="check-detail">
                    <link-form-item label="陈列货架位置" class="flag" :disabled="!isEdit">
                        <link-lov v-model="formData.displayPosition" type="DISPLAY_POSITION"></link-lov>
                    </link-form-item>
                </view>
            </view>
            <view class="check-item check-item-main">
                <view class="num">2.</view>
                <view class="check-detail check-item-main">
                    <link-form-item label="陈列视线位置" class="flag" :disabled="!isEdit">
                        <link-lov v-model="formData.sightPosition" type="SIGHT_PLACE"></link-lov>
                    </link-form-item>
                </view>
            </view>
            <view class="check-item">
                <view class="seq">3.</view>
                <view class="check-detail">
                    <link-form-item label="产品是否集中陈列" class="flag" :disabled="!isEdit">
                        <link-radio-group v-model="formData.centralizedDisplay">
                            <item :arrow="false" @tap.stop="changeFlag('centralizedDisplay', 'Y')">
                                <link-checkbox val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false" @tap.stop="changeFlag('centralizedDisplay', 'N')">
                                <link-checkbox val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                </view>
            </view>
            <view class="check-item check-item-main">
                <view class="num">4.</view>
                <view class="check-detail">
                    <link-form-item label="氛围/物料营造" class="flag" :disabled="!isEdit" >
                        <link-lov v-model="formData.terminalVibe" :multiple="true" type="TERMINAL_VIBE" class="atmos-wrap"></link-lov>
                    </link-form-item>
                </view>
            </view>
        </view>
        <view class="term pd-bottom" v-if="formData.myDisplay === 'Y'">
            <view class="image-title">产品陈列照片</view>
            <lnk-img-watermark
                v-if="formData.id"
                ref="displayNum"
                :parentId="formData.id"
                moduleType="displayNum"
                :delFlag="isEdit"
                :newFlag="isEdit"
                :album="true"
                :displayCustomizedWatermark="true"
                :useModuleName="'终端检查'"
                @imgUploadSuccess="displayNumArr"
                @imgDeleteSuccess="displayNumArr"
                moduleName="产品陈列照片">
            </lnk-img-watermark>
        </view>
    </view>
</template>

<script lang="jsx">
import LnkImgWatermark from '../../../core/lnk-img-watermark/lnk-img-watermark';
import LineTitle from './line-title.vue'

export default {
    name: 'product-check',
    props: {
        formData: Object,
        terminalData: {
            type: Object,
            default() {
                return {}
            }
        },
        isEdit: Boolean
    },
    components: {LnkImgWatermark, LineTitle},
    data() {
        return {
            selectedProduct: [], // 选中品项内容
            uploadImgList: [], // 上传的图片
            isMyDisplay: ''
        }
    },
    computed:{
        productLovType() {
            let lovKey = ''
            // 国窖陈列品项值列表
            if (this.terminalData.salesmanBrandComCode === '5600') lovKey = 'GUO_JIAO_CHECK_PRODUCT';
            // 特曲陈列品项值列表
            if (this.terminalData.salesmanBrandComCode === '5137') lovKey = 'TE_QU_CHECK_PRODUCT';
            // 蓉城怀旧、怀旧陈列品项值列表
            if (['5910','1204'].includes(this.terminalData.salesmanBrandComCode)) lovKey = 'HUAI_JIU_CHECK_PRODUCT';
            // 大成浓香、永粮、鸿泸陈列品项值列表
            if (['5161','5902','5903'].includes(this.terminalData.salesmanBrandComCode)) lovKey = 'DA_CHENG_CHECK_PRODUCT';
            // 窖龄、酒聚酒陈列品项值列表
            if (['5151', '5153'].includes(this.terminalData.salesmanBrandComCode)) lovKey = 'JIAO_LING_CHECK_PRODUCT';
            return lovKey
        }
    },
    methods: {
        changeMyDisplay(val) {
            if (val === this.isMyDisplay) return;
            this.isMyDisplay = val;
            if (val === 'N' && this.uploadImgList.length) {
                this.$refs.displayNum.delALLTypeImg()
            }
        },
        /**
         * 改变标志
         * <AUTHOR>
         * @date	2025/04/25
         */
        changeFlag(label, val) {
            if (!this.isEdit) return;
            this.$set(this.formData, label, val);
        },
        /**
         * 打开品项列表
         * <AUTHOR>
         * @date	2025/04/25
         */
        async openDisplayList() {
            this.$nav.push('/pages/terminal2/terminal-check/product-select-page', {brandComCode: this.terminalData.salesmanBrandComCode})
        },
        /**
         * 陈列品项及数量拍照
         * <AUTHOR>
         * @date	2025/04/25
         */
        displayNumArr(param) {
            this.uploadImgList = param;
            this.$emit('imgList', {
                listName: 'displayNumImg',
                list: param
            });
        }
    }
}
</script>

<style lang="scss">
.product-check {
    .display-content-title {
        display: flex;
        align-items: center;
        height: 80px;
        justify-content: space-between;
        font-size: 28px;
        .add {
            color: #2F69F8;
        }
    }
    .image-title {
        display: flex;
        font-size: 28px;
        padding: 24px 24px 24px 0;
    }
    .display-content {
        .display-content-warp {
            background-color: #f8f8f8;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 28px;
            padding: 32px 32px 0 32px;
        }
        .display-common {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 80px;
            .container-input {
                padding-left: 10px;
                flex: 1;
                background: #f8f8f8;
                border-radius: 8px;
                height: auto !important;
                width: auto !important;

                input {
                    text-align: left;
                    display: inline-block;
                    height: 60px;
                    line-height: 60px;
                    width: 120px;
                }
            }
        }
    }
    .atmos-wrap {
        .link-input-content {
            white-space: normal;

            .link-input-inner {
                height: auto;
            }
        }
    }
    .pd-bottom {
        padding-bottom: 40px !important;
    }
}
</style>
