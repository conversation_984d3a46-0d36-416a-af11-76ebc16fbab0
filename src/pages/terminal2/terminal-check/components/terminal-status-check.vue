<!--
@created<PERSON><PERSON>
@date  2025/04/24
@description 终端检核-终端状态
-->
<template>
    <view class="terminal-status-check">
        <line-title title="终端状态"></line-title>
        <view class="term">
            <view class="small-title">异常情况</view>
            <view class="check-item">
                <view class="seq">1.</view>
                <view class="check-detail">
                    <link-form-item label="终端关门" class="flag" :disabled="!isEdit">
                        <link-radio-group v-model="formData.close">
                            <item :arrow="false" @tap.stop="changeFlag('close', 'Y')">
                                <link-checkbox val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false" @tap.stop="changeFlag('close', 'N')">
                                <link-checkbox val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                </view>
            </view>
            <view class="check-item">
                <view class="seq">2.</view>
                <view class="check-detail">
                    <link-form-item label="位置找不到" :disabled="!isEdit">
                        <link-radio-group v-model="formData.notFound">
                            <item :arrow="false" @tap.stop="changeFlag('notFound', 'Y')">
                                <link-checkbox val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false" @tap.stop="changeFlag('notFound', 'N')">
                                <link-checkbox val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                </view>
                
            </view>
        </view>
    </view>
</template>
<script>
import LineTitle from './line-title.vue'
export default {
    name: 'terminal-status-check',
    props: {
        formData: Object,
        isEdit: Boolean
    },
    components:{LineTitle},
    methods: {
        /**
         * 改变标志
         * <AUTHOR>
         * @date	2025/04/24
         */
        changeFlag(label, val) {
            if (!this.isEdit) {
                return;
            }
            this.$set(this.formData, label, val);
        }      
    },
}
</script>
<style lang="scss">
.terminal-status-check {

}    
</style>