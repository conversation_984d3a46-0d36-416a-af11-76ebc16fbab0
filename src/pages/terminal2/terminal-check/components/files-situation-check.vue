<!--
@created<PERSON><PERSON>
@date  2025/04/24
@description 终端检核-终端状态
-->
<template>
    <view class="files-situation-check">
        <line-title title="档案情况"></line-title>
        <view class="term">
            <view class="small-title">终端信息</view>
            <view class="preview-details">
                <view class="preview-content">
                    <text class="preview-content-label">
                        老板联系电话：
                    </text>
                    <text class="preview-content-val">
                        {{this.terminalData.mobilePhone || '暂无'}}
                    </text>
                </view>
            </view>
            <view class="check-item">
                <view class="seq">1.</view>
                <view class="check-detail">
                    <link-form-item label="地址是否准确" class="flag" :disabled="!isEdit">
                        <link-radio-group v-model="formData.addressCorrect">
                            <item :arrow="false" @tap.stop="changeFlag('addressCorrect', 'Y')">
                                <link-checkbox val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false" @tap.stop="changeFlag('addressCorrect', 'N')">
                                <link-checkbox val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                    <view class="addr-wrap" @tap="getLocation" v-if="formData.addressCorrect === 'N'">
                        <view class="iconfont icon-dizhi1" v-if="isEdit"></view>
                        <view class="address-label" v-else>
                            终端地址：
                        </view>
                        <view v-if="!formData.address" class="placeholder">请选择地址</view>
                        <view v-else>{{formData.address}}</view>
                    </view>
                </view>
            </view>
            <view class="check-item">
                <view class="seq">2.</view>
                <view class="check-detail">
                    <link-form-item label="老板联系电话是否正确" :disabled="!isEdit">
                        <link-radio-group v-model="formData.phoneCorrect">
                            <item :arrow="false" @tap.stop="changeFlag('phoneCorrect', 'Y')">
                                <link-checkbox val="Y"/>
                                <text class="check-text">是</text>
                            </item>
                            <item :arrow="false" @tap.stop="changeFlag('phoneCorrect', 'N')">
                                <link-checkbox val="N"/>
                                <text class="check-text">否</text>
                            </item>
                        </link-radio-group>
                    </link-form-item>
                    <link-form-item label="老板联系电话" field="phone" v-if="formData.phoneCorrect === 'N'" :disabled="!isEdit">
                        <link-input v-model="formData.phone" placeholder="请输入老板联系电话"/>
                    </link-form-item>
                </view>
            </view>
        </view>
    </view>
</template>
<script>
import LineTitle from './line-title.vue'
export default {
    name: 'files-situation-check',
    props: {
        formData: Object,
        isEdit: Boolean,
        terminalData: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    components:{LineTitle},
    methods: {
        /**
         * 选择地址
         * <AUTHOR>
         * @date	2025/04/24
         */
        async getLocation() {
            if (!this.isEdit) return;
            this.coordinate = await this.$locations.getCurrentCoordinate();
            if (this.$utils.isEmpty(this.coordinate.latitude) && this.$utils.isEmpty(this.coordinate.longitude)) {
                this.$dialog({
                    title: '提示',
                    content: '请确认手机地理位置授权是否打开，或者【设置】-【企业微信】位置权限管理是否打开？',
                    cancelButton: false,
                    confirmText: '去开启',
                    onConfirm: async () => {
                        let userLocation = await this.$locations.openSetting();
                        if (userLocation['scope.userLocation']) {
                            this.coordinate = await this.$locations.getCurrentCoordinate();
                        }
                    }
                });
                return;
            }
            await this.$locations.chooseLocation(this.coordinate.latitude, this.coordinate.longitude);
        },
        /**
         * 选择位置结果
         * <AUTHOR>
         * @date	2025/04/24
         */
        chooseLocation (latitude, longitude) {
            const that = this;
            return new Promise (resolve => {
                that.$taro.chooseLocation({
                    latitude: Number(latitude),
                    longitude: Number(longitude),
                    async success(res) {
                        let data = {res: res, flag: 'success'};
                        resolve(data)
                    },
                    fail (res) {
                        let data = {res: res, flag: 'fail'};
                        resolve(data)
                    }
                })
            })
        },
        /**
         * 改变标志
         * <AUTHOR>
         * @date	2025/04/24
         */
        changeFlag(label, val) {
            if (!this.isEdit) {
                return;
            }
            this.$set(this.formData, label, val);
        }
    },
}
</script>
<style lang="scss">
.files-situation-check {
    .addr-wrap {
        display: flex;
        align-items: center;
        padding: 0 24px 24px 0;
        font-size: 28px;
        .address-label {
            width: 300px;
        }

        .iconfont {
            font-size: 70px;
            color: #2F69F8;
            margin-right: 24px;
        }

        .placeholder {
            color: #ccc;
        }
    }
}    
</style>