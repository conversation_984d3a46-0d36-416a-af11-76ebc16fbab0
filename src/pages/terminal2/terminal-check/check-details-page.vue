<!--
@created<PERSON><PERSON>
@date  2025/04/25
@description 终端检查-检查详情页
-->
<template>
    <link-page class="check-details-page check-terminal-common-style">
        <view class="check-info-card">
            <view class="card-item" v-for="(item, index) in terMinalInfoData" :key="`card_${index}`">
                <text class="label">
                    {{item.label}}
                </text>
                <text class="val">
                    {{formData[item.field]}}
                </text>
            </view>
        </view>
        <link-form :value="formData" ref="form" hideSaveButton>
            <!-- 终端状态 -->
            <terminal-status-check :form-data="formData" :is-edit="false"></terminal-status-check>
            <!-- 档案情况 -->
            <files-situation-check v-if="formData.close === 'N' && formData.notFound === 'N'" :form-data="formData" :terminal-data="pageParam.terminalData" :is-edit="false"></files-situation-check>
            <!-- 门头店招 -->
            <door-header-check v-if="formData.close === 'N'  && formData.notFound === 'N'" :form-data="formData" :terminal-data="pageParam.terminalData" :is-edit="false"></door-header-check>
            <!-- 产品展陈 -->
            <product-check v-if="formData.close === 'N' && formData.notFound === 'N'" :form-data="formData" :terminal-data="pageParam.terminalData" :is-edit="false"></product-check>
            <!-- 数字化政策运用 -->
            <number-policy-utilize-check v-if="formData.close === 'N' && formData.notFound === 'N' && formData.myDisplay === 'Y'" :form-data="formData" :is-edit="false"></number-policy-utilize-check>
        </link-form>
    </link-page>
</template>
<script>
import LnkTaps from '../../core/lnk-taps/lnk-taps';
import LineTitle from './components/line-title.vue'
import TerminalStatusCheck from './components/terminal-status-check.vue'
import FilesSituationCheck from './components/files-situation-check.vue'
import DoorHeaderCheck from './components/door-header-check.vue'
import ProductCheck from './components/product-check.vue'
import NumberPolicyUtilizeCheck from './components/number-policy-utilize-check.vue'
definePageConfig({
    navigationBarTitleText: '检查详情'
});
export default {
    name: 'check-details-page',
    components: {
        LnkTaps, 
        LineTitle,
        TerminalStatusCheck,
        FilesSituationCheck,
        DoorHeaderCheck,
        ProductCheck,
        NumberPolicyUtilizeCheck
    },
    data() {
        return {
            formData:{},
            terMinalInfoData: [
                {
                    label: '终端编码',
                    field: 'acctCode'
                },
                {
                    label: '终端名称',
                    field: 'acctName'
                },
                {
                    label: '批次号',
                    field: 'no'
                },
                {
                    label: '检查时间',
                    field: 'created'
                },
                {
                    label: '检查人',
                    field: 'name'
                }
            ], // 终端检查基础信息
            nextNum: 1, // 当前在哪一步
        }
    },
    created() {
        this.getCheckDetailsData();
    },
    methods: {
        /**
         * 终端检查详情信息
         * <AUTHOR>
         * @date	2025/04/24
         */
        async getCheckDetailsData() {
            const res = await this.$http.post('action/link/inspect/queryById', {
                id: this.pageParam.data.id
            })
            if (res.success) {
                this.formData = res.result;
            } else {
                this.$message.error('请求终端检查详情信息失败');
                
            }
        }
    },
}
</script>
<style lang="scss">
@import "./css/common.scss";
.check-details-page {
    .check-info-card {
        font-size: 28px;
        color: #262626;
        padding: 40px;
        background-color: #fff;
        .card-item {
            justify-content: space-between;
            display: flex;
            height: 60px;
        }
    }
}
</style>