<template>
    <link-page class="terminal-check-map-distribute-page">
        <!--搜索-->
        <link-search-input
            class="search-input"
            v-model="searchText"
            @change="searchStoreName"
            placeholder="终端名称"
        >
            <view class="search-container" @tap="goList">
                <link-icon class="icon" icon="icon-unorderedlist" />
                <view>终端列表</view>
            </view>
        </link-search-input>
        <view class="filter-wrap">
            <view class="filter-content">
                <view class="address-select">
                    <link-address
                        placeholder="当前地区"
                        class="address-content"
                        :province.sync="selectProvince"
                        :city.sync="selectCity"
                        :district.sync="selectDistrict"
                        @update:district="changeDistrict"
                        :custom="addressCustomOption"
                    />
                    <link-icon class="address-icon" icon="mp-desc" />
                </view>
                <link-filter v-model="filterOption" @change="handleChange" />
            </view>
        </view>
        <!-- 地图气泡 -->
        <view class="map-callout" :style="{ top: mapCalloutTop }">
            <view>必检{{ checkTeminalNum }}家</view>
            <view>非必检{{ unMustCheckTotal }}家</view>
        </view>
        <!-- 地图 -->
        <map
            id="map"
            :longitude="longitude"
            :latitude="latitude"
            :scale="scale"
            :show-location="true"
            :show-compass="true"
            :show-scale="true"
            class="map-content"
            :setting="setting"
            :style="{ height: mapHeight + 'px' }"
            :markers="markers"
            @tap="mapTap"
            @markertap="markerTap"
            @regionchange="regionchange"
            @updated="mapUpdated"
        >
            <cover-view
                :style="{
                    'margin-top': mapHeight - 60 + 'px',
                    display: mapHeight === 0 ? 'none' : 'block',
                }"
            >
                <cover-view class="location-aim-container">
                    <cover-view class="location-aim" @tap="backCurrentPosition">
                        <cover-image
                            class="aim-image"
                            :src="$imageAssets.locationAimImage"
                        ></cover-image>
                    </cover-view>
                </cover-view>
            </cover-view>
        </map>
        <!--终端详情-->
        <view class="store-details" v-if="storeDetailsFlag && !previewStatus">
            <view class="icon-back" @tap="backListMap">
                <view class="iconfont icon-left"></view>
            </view>
            <view class="store-item">
                <image
                    class="media-list-logo"
                    :src="storeOptions.storeUrl"
                ></image>
                <view class="store-content">
                    <view class="store-content-company">{{storeOptions.acctCode}}</view>
                    <view class="store-content-company">{{storeOptions.acctName}}</view>
                    <view class="store-content-company">{{storeOptions.salesmanBrandCom}}</view>
                    <view class="store-content-address">{{ storeOptions.distance }}米 | {{storeOptions.addrDetailAddr}}</view>
                    <view class="store-content-company">检查状态-{{storeOptions.checkStatus}}</view>
                </view>
            </view>
            <view class="button">
                <link-button
                    class="button-item"
                    label="路线"
                    icon="icon-luxian"
                    size="large"
                    @tap="navGpsLine"
                />
                <link-button
                    class="button-item"
                    label="终端检查"
                    mode="fill"
                    size="large"
                    @tap="goTerminalCheck(storeOptions)"
                />
            </view>
        </view>
    </link-page>
</template>

<script lang="jsx">
import { getFiltersRaw } from "link-taro-component";

definePageConfig({
    navigationBarTitleText: '终端分布'
});

export default {
    name: 'terminal-check-map-distribute-page',
    data() {
        const filterOption = [
            {
                label: "品牌公司",
                field: "salesmanBrandComCode",
                type: "select",
                multiple: true,
                data: [
                    { name: "国窖", val: "5600" },
                    { name: "特曲", val: "5137" },
                    { name: "大成浓香", val: "5161,5902,5903"}, //大成浓香、永粮、鸿泸
                    { name: "怀旧", val: "5910,1204"}, //蓉城怀旧、怀旧
                    { name: "窖龄酒", val: "5151,5153" } // 窖龄、酒聚酒
                ],
            },
            {
                label: "位置区域",
                field: "distance",
                type: "select",
                multiple: false,
                data: [
                    { name: "500m内", val: "500" },
                    { name: "1Km内", val: "1000" },
                    { name: "2Km内", val: "2000" },
                    { name: "5Km内", val: "5000" },
                    { name: "10Km内", val: "10000" },
                    { name: "20Km内", val: "20000" },
                    { name: "50Km内", val: "50000" },
                    { name: "无限制", val: "" },
                ],
                value: "",
            },
            {
                label: "检查状态",
                field: "acctCheckStatus",
                type: "select",
                multiple: false,
                data: [
                    { name: "全部", val: "all" },
                    { name: "必检-未检", val: "mustUncheck" },
                    { name: "非必检-未检", val: "normalUncheck" },
                    { name: "已检查", val: "checked" }
                ],
                value: "all",
            },
            {
                label: "客户达成等级",
                field: "cumulativeLevel",
                type: "select",
                multiple: true,
                data: [
                    { name: "无", val: "empty"},
                    { name: "B以下", val: "B以下"},
                    { name: "B", val: "B"},
                    { name: "A以下", val: "A以下"},
                    { name: "A", val: "A"},
                    { name: "1A", val: "1A"},
                    { name: "2A", val: "2A"},
                    { name: "3A", val: "3A"},
                    { name: "4A", val: "4A"},
                    { name: "5A", val: "5A"},
                    { name: "5A+", val: "5A+"}
                ],
            }
        ];
        return {
            filterOption,
            filterContentHeight: 0, // 筛选行的高度
            searchText: "", // 顶部搜索框文本
            acctCheckStatusFlag: true, // 默认筛选重点检查终端
            confirmShowAllFlag: false, // 确定展示全市区终端标识，每次进入只询问一次
            allAddress: [], // 所有地址
            CITYWIDE: "全市区", // 全市区字符串常量
            previewStatus: true, // 预览状态
            setting: {
                enable3D: false,
                enableTraffic: false,
            },
            oauth: 'safe_check', // 安全性
            pageNum: 1, // 当前终端地图列表页
            pageLimit: 20, // 一页数据的条数
            nextPageFlag: true, // 是否还有下一页
            longitude: 0, // 经度
            latitude: 0, // 纬度
            mapHeight: 0, // 地图高度
            scale: 12, // 缩放级别
            storeListTotalHeight: 0, // 终端列表滚动容器高度
            searchInputHeight: 0, // 搜索框高度
            addressData: null, // 地址信息对象
            addressDataFull: '', // 完整地址字符串信息
            storeList: [], // 终端列表
            markers: [], // 地图标记数组
            temMarker: [], // 暂存地图标记
            storeOptions: {}, // 当前选中的终端
            selectIndex: null, // 当前选中的mark标记
            storeDetailsFlag: false, // 当前是否展示终端详情
            countMapHeightTimer: null, // 计算终端列表容器media-list的view元素高度计时器
            storeDetailsTimer: null, // 计算终端详情容器store-details的view元素高度计时器
            selectProvince: '', // 选择的省份
            selectCity: '', // 选择的城市
            selectDistrict: '', // 选择的区县
            districtCode: '', // 区县编码
            previewToDetail: false, // 标记是否从预览到详情
            customFilter: {
                salesmanBrandComCodeList: [],
                cumulativeSalesLevelList: [],
                distance: '',
                acctCheckStatus: 'mustUncheck'
            }, // 自定义筛选条件
            copyLongitude: 0, // 备份-经度
            copyLatitude: 0, // 备份-纬度
            matchTypeColor: { // 有效性状态显示颜色
                Y: '#2F68F7', // 有效-蓝色
                N: '#FF0000' // 无效-红色
            },
            teminalArrList: [], // 查询到的所有终端数据
            checkTeminalNum: 0,
            unMustCheckTotal: 0, // 非必检终端家数
            isMarkerTap: false // 是否点击markertap事件
        };
    },
    computed: {
        mapCalloutTop() {
            return `${
                this.filterContentHeight + this.searchInputHeight + 15
            }px`;
        }
    },
    async created() {
        this.resetFilterOption();
        try {
            this.$utils.showLoading("加载中");
            // 获取搜索框高度
            this.getSearchInput();
            // 获取筛选框高度
            this.getFilterContent();
            // 获取地址信息
            await this.getAddress();
            this.allAddress = await this.$address.getData();
            // 请求终端分布数据--根据省市区获取
            await this.getAreaTerminal();
        } finally {
            this.$utils.hideLoading();
        }
    },
    destroyed() {
        clearTimeout(this.countMapHeightTimer);
        clearTimeout(this.storeDetailsTimer);
    },
    methods: {
        /**
         * 地图加载完毕定位到中心
         * * <AUTHOR>
         * @date 2023-09-26
         */
        mapUpdated(){
            if(this.isUpdated){
                return
            }
            this.isUpdated = true
            const mapObj = wx.createMapContext('map',this)
            mapObj.moveToLocation();
        },
        /**
         * 返回地图模式
         * <AUTHOR>
         * @date	2023/5/30 16:23
         */
        onBack(param) {
            if (param === 'reload') this.getAreaTerminal();
        },
        /**
         * 筛选后的change事件
         * @Author:付常涛
         * @Date: 2023/05/26 02:55:35
         */
        handleChange(val) {
            let data = getFiltersRaw(val);
            let keyAll = [];
            data.forEach((item) => {
                if (item.property === "distance") {
                    item.operator = "<=";
                    this.customFilter.distance = item.value
                }
                if (item.property === "salesmanBrandComCode") {
                    item.value = item.value.split("[").join("");
                    item.value = item.value.split("]").join("");
                    this.customFilter.salesmanBrandComCodeList = item.value.split(',');
                }
                if (item.property === 'acctCheckStatus') this.customFilter.acctCheckStatus = item.value
                if (item.property === 'cumulativeLevel') {
                    item.value = item.value.split("[").join("");
                    item.value = item.value.split("]").join("");
                    this.customFilter.cumulativeSalesLevelList = item.value.split(',');
                }
                keyAll.push(item.property);
            });
            if (!keyAll.includes('cumulativeLevel')) this.customFilter.cumulativeSalesLevelList = [];
            if (!keyAll.includes('salesmanBrandComCode')) this.customFilter.salesmanBrandComCodeList = [];
            if (!keyAll.includes('distance')) this.customFilter.distance = '';
            if (!keyAll.includes('acctCheckStatus')) this.customFilter.acctCheckStatus = '';
            setTimeout(() => {
                this.getAreaTerminal();
            }, 450);
        },
        /**
         * 重置筛选查询条件为默认
         * @Author:付常涛
         * @Date: 2023/05/26 02:59:00
         */
        resetFilterOption(){
            this.filterOption[1].value = "";
            this.filterOption[2].value = "mustUncheck"
        },
        /**
         * 点击地图点重新加载数据
         * @Author:zengqiang
         * @Date: 2025/05/07 
         */
        async mapTap(e) {
            try {
                if (this.isMarkerTap) return;
                this.searchText = '';
                this.previewStatus = true;
                this.storeDetailsFlag = false;
                this.markers = []; // 地图标记数组
                this.temMarker = []; // 暂存地图标记
                this.pageNum = 1;
                this.nextPageFlag = true;
                const lat = e.detail.latitude;
                const lng = e.detail.longitude;
                const res = await this.$locations.reverseTMapGeocoder(lat, lng, '终端检查');
                this.moveLocation(
                    lng,
                    lat
                );
                this.longitude = lng;
                this.latitude = lat;
                this.selectProvince = res.result.address_component.province;
                this.selectCity = res.result.address_component.city;
                if (res.result.address_component.district) {
                    this.selectDistrict = res.result.address_component.district;
                    this.districtCode = res.result.ad_info.adcode;
                }
                this.getAreaTerminal();
            } catch(err){
                console.log('点击地图重新加载数据失败', + err);
            }
        },
        /**
         * 选择省市区后移动地图中心点
         * @Author:付常涛
         * @Date: 2023/05/16 16:51:57
         */
        async changeDistrict(val) {
            // 切换省市区后，清除手动筛选接口。恢复默认
            // this.resetFilterOption();
            // 清空search搜索文本
            this.searchText = "";
            // 隐藏列表
            this.previewStatus = true;
            // 如果当前展开了详情，则关闭终端详情
            this.storeDetailsFlag = false;
            this.storeList = []; // 终端列表
            this.markers = []; // 地图标记数组
            this.temMarker = []; // 暂存地图标记
            this.pageNum = 1;
            this.nextPageFlag = true;
            // 根据所选省市区，将地图位移至相应经纬度
            let address = this.selectProvince + this.selectCity;
            if (this.selectDistrict !== this.CITYWIDE) {
                address += this.selectDistrict;
            }
            const res = await this.$locations.reverseTMapGgeocodingLaLon(
                address
            );
            this.moveLocation(
                res.res.result.location.lng,
                res.res.result.location.lat
            );
            this.longitude = res.res.result.location.lng;
            this.latitude = res.res.result.location.lat;
            if (this.selectDistrict !== this.CITYWIDE) this.districtCode = res.res.result.ad_info.adcode;
            else this.districtCode = ''
            // 请求终端分布数据--根据省市区获取
            this.getAreaTerminal();
        },
        /**
         * 地址组件自定义地址数据源
         * <AUTHOR>
         * @date 2023-05-20
         */
        addressCustomOption(tag, parentValue) {
            // 区县前置'全市区'选项
            if (tag === "d") {
                for (let p = 0; p < this.allAddress.length; p++) {
                    const city = this.allAddress[p].children;
                    for (let c = 0; c < city.length; c++) {
                        if (city[c].name === parentValue) {
                            let dis = [];
                            const allD = { name: this.CITYWIDE };
                            dis.push(allD);
                            dis = dis.concat(city[c].children);
                            return dis.map((d) => {
                                return d.name;
                            });
                        }
                    }
                }
            }
        },
        async getMapCenterLocation() {
            let mpCtx = wx.createMapContext("map",this);
            const res = await mpCtx.getCenterLocation();
            // console.log("当前地图的中心点经纬度", res);
            return res;
        },
        // 移动地图
        moveLocation(longitude, latitude) {
            let mpCtx = wx.createMapContext("map",this);
            mpCtx.moveToLocation({
                latitude,
                longitude,
            });
        },
        /**
         * 回到当前位置
         * @Author:付常涛
         * @Date: 2023/05/16 12:00:32
         */
        async backCurrentPosition() {
            // 回到当前位置后，清除手动筛选接口。恢复默认
            this.resetFilterOption();
            // 清空search搜索文本
            this.searchText = "";
            await this.getAddress();
            this.scale = 12;
            this.moveLocation(this.longitude, this.latitude);
            // 隐藏列表
            this.previewStatus = true;
            this.storeList = []; // 终端列表
            this.markers = []; // 地图标记数组
            this.temMarker = []; // 暂存地图标记
            this.pageNum = 1;
            this.nextPageFlag = true;
            // 重新请求当前定位附近终端
            this.getAreaTerminal();
        },
        /**
         * 按照终端名称进行搜索
         * @Author:付常涛
         * @Date: 2023/05/15 16:35:33
         */
        searchStoreName(val) {
            // 当前预览状态去搜索
            this.getAreaTerminal(val);
        },
        
        /**
         * 获取区域内的终端
         * @Author:zengqiang
         * @Date: 2025/04/26
         */
        async getAreaTerminal() {
            try {
                if (!this.searchText) this.$utils.showLoading("请求中");
                const param = this.paramsHandle();
                const res = await this.$http.post(
                    "action/link/inspectorsRange/queryListByDistance",
                    param
                );
                if (res.success) {
                    if (res.rows.length === 0) {
                        this.markers = [];
                        this.storeList = [];
                        this.$message.primary("当前区域无终端");
                    }
                    this.checkTeminalNum = res.total || 0;
                    this.unMustCheckTotal = res.unMustCheckTotal || 0;
                    this.teminalArrList = res.rows;
                    // 给终端加上递增唯一数字序列，用于地图标点id
                    let orderStartId = 123456;
                    res.rows.forEach((item, index) => {
                        res.rows[index].markId = orderStartId;
                        orderStartId += 5;
                    });
                    // 地图markers数据
                    // [acctCheckStatus=checked已检查] -绿色;[必检终端-待检查acctCheckStatus=mustUncheck] -红色;普通待检查acctCheckStatus=normalUncheck] -灰色
                    let markers = res.rows.map((item) => ({
                        iconPath: item.checkFlag ? "/static/images/map-icon/not-core.png" : (item.mustCheckFlag ? "/static/images/map-icon/core.png" : "/static/images/map-icon/no-check.png"),
                        id: item.markId,
                        zIndex: item.checkFlag ? 9 : item.mustCheckFlag ? 999 : 99,
                        latitude: Number(item.latitude),
                        longitude: Number(item.longitude),
                        width: 28,
                        height: 34
                    }));
                    this.markers = markers;
                    this.storeList = res.rows.map((item) => {
                        return {
                            id: item.id,
                            markId: item.markId,
                            acctCheckStatus: item.checkFlag ? 'checked' : item.mustCheckFlag ? 'mustUncheck' : 'normalUncheck',
                            longitude: item.longitude,
                            latitude: item.latitude,
                            distance: item.distance,
                        };
                    });
                    this.countMapHeightTimer = setTimeout(() => {
                        this.countMapHeight();
                    }, 200);
                }
            } catch (e){
                console.log(e);
            } finally {
                if (!this.searchText) this.$utils.hideLoading();
            }
        },
        /**
         * 请求参数处理
         * @Author:zengqiang
         * @Date: 2025/04/26
         */
        paramsHandle() {
            let obj = {
                longitude: this.longitude,
                latitude: this.latitude,
                province: this.selectProvince,
                city: this.selectCity,
                salesmanBrandComCodeList: this.customFilter.salesmanBrandComCodeList,
                cumulativeSalesLevelList: this.customFilter.cumulativeSalesLevelList
            };
            if (this.selectDistrict !== this.CITYWIDE) {
                obj.districtCode = this.districtCode;
                obj.district = this.selectDistrict;
            };
            if (this.customFilter.distance) obj.distance = this.customFilter.distance;
            // 必检-未检请求参数赋值
            if (this.customFilter.acctCheckStatus === 'mustUncheck') {
                obj.checkFlag = false;
                obj.mustCheckFlag = true;
            };
            // 非必检-未检请求参数赋值
            if (this.customFilter.acctCheckStatus === 'normalUncheck') {
                obj.checkFlag = false;
                obj.mustCheckFlag = false;
            };
            // 已检查请求参数赋值
            if (this.customFilter.acctCheckStatus === 'checked') obj.checkFlag = true;
            if (this.searchText) obj.acctName = this.searchText;
            return obj
        },
        /**
         * 去终端分布地图展示页
         * @Author:付常涛
         * @Date: 2023/05/15 16:57:37
         */
        goList() {
            this.$nav.push('pages/terminal2/terminal-check/terminal-check-map-list-page.vue');
        },
        /**
         * 获取搜索框高度
         * @Author:付常涛
         * @Date: 2023/05/15 17:13:42
         */
        getSearchInput() {
            const that = this;
            setTimeout(function () {
                const query = wx.createSelectorQuery();
                query
                    .select(".search-input")
                    .boundingClientRect((ret) => {
                        that.searchInputHeight = ret.height;
                    })
                    .exec();
            }, 200);
        },
        /**
         * 获取筛选行的高度(地址+筛选)
         * @Author:付常涛
         * @Date: 2023/05/25 22:27:54
         */
        getFilterContent() {
            const that = this;
            setTimeout(function () {
                const query = wx.createSelectorQuery();
                query
                    .select(".filter-wrap")
                    .boundingClientRect((ret) => {
                        that.filterContentHeight = ret.height;
                    })
                    .exec();
            }, 200);
        },
        /**
         * 获取地址
         * @Author:付常涛
         * @Date: 2023/05/15 17:16:31
         */
        async getAddress() {
            let coordinate = await this.$locations.getCurrentCoordinate();
            if (
                !this.$utils.isEmpty(coordinate.latitude) &&
                !this.$utils.isEmpty(coordinate.longitude)
            ) {
                this.longitude = Number(coordinate.longitude);
                this.latitude = Number(coordinate.latitude);
                let address = await this.$locations.reverseTMapGeocoder(
                    this.latitude,
                    this.longitude,
                    '选择终端列表'
                );
                this.addressData = address["originalData"].result;
                this.addressDataFull =
                    address["originalData"].result.formatted_address;
                this.selectProvince =
                    this.addressData.addressComponent.province;
                this.selectCity = this.addressData.addressComponent.city;
                //默认给当前所在区
                this.selectDistrict = this.addressData.addressComponent.district;
                this.districtCode = this.addressData.addressComponent.adcode;
            }
        },
        /**
         * 计算地图高度
         * @Author:付常涛
         * @Date: 2023/05/15 20:05:36
         */
        countMapHeight() {
            if (this.searchInputHeight === 0) this.getSearchInput();
            this.mapHeight = this.$device.systemInfo.safeArea.height - 70 - this.searchInputHeight - this.filterContentHeight;
        },
        /**
         * 点击mark标记
         * @Author:付常涛
         * @Date: 2023/05/15 20:19:41
         */
        async markerTap(e) {
            this.isMarkerTap = true;
            console.log('点击marker标点');
            if (this.previewStatus) {
                // 预览状态点击mark到展示详情
                this.previewStatus = false;
                this.previewToDetail = true; // 标记是从预览到详情
            }
            try {
                // 获取终端详情
                let opt = this.storeList.find((item) => item.markId === e.markerId);
                this.$utils.showLoading("加载中");
                this.storeOptions = this.teminalArrList.find(el => el.id === opt.id);
                // 检查状态展示数据处理
                if (this.storeOptions.checkFlag) {
                    this.$set(this.storeOptions, "checkStatus", '已检查');
                } else {
                    if (this.storeOptions.mustCheckFlag) this.$set(this.storeOptions, "checkStatus", '必检未检查');
                    else this.$set(this.storeOptions, "checkStatus", '非必检未检查');
                }
                // 转换门头图片
                if (!this.$utils.isEmpty(this.storeOptions.storePicPreKey)) {
                    let urlData = await this.$image.getSignedUrl(
                        this.storeOptions.storePicPreKey
                    );
                    this.$set(this.storeOptions, "storeUrl", urlData);
                } else {
                    this.$set(
                        this.storeOptions,
                        "storeUrl",
                        this.$imageAssets.terminalDefaultImage
                    );
                }
                this.storeDetailsFlag = true;
                const cb = this.$utils.hideLoading;
                console.log('cb', 111111111111111111);
                this.getStoreDetailsHeight(cb);
                this.selectIndex = this.markers.findIndex((item) => {
                    return item.id === e.markerId;
                });
                this.changeMarker(this.selectIndex);
                // 备份
                this.copyLongitude = this.longitude;
                this.copyLatitude = this.latitude;
                this.longitude = Number(opt.longitude);
                this.latitude = Number(opt.latitude);
            } catch(err) {
                console.log('点击mark标记数据处理失败:' + err);
            } finally {
                // 1秒后改变状态防止触发地图点击事件
                setTimeout(()=>{
                    this.isMarkerTap = false;
                }, 1000);
            }
        },
        /**
         * 根据终端详情块高度调整地图高度
         * @Author:付常涛
         * @Date: 2023/05/15 20:34:45
         */
        getStoreDetailsHeight(cb) {
            const that = this;
            if (this.storeDetailsFlag) {
                this.storeDetailsTimer = setTimeout(function () {
                    const query = wx.createSelectorQuery();
                    query
                        .select(".store-details")
                        .boundingClientRect((ret) => {
                            that.mapHeight =
                                that.$device.systemInfo.windowHeight -
                                ret.height -
                                that.searchInputHeight -
                                that.filterContentHeight -
                                that.$device.systemInfo.statusBarHeight * 2;
                            cb && that.$nextTick(() => {
                                cb.call(that)
                            })
                        })
                        .exec();
                }, 200);
            }
            else{
                cb && that.$nextTick(() => {
                    cb.call(that)
                })
            }
        },
        /**
         * 改变选中的mark的样式
         * @Author:付常涛
         * @Date: 2023/05/15 20:39:44
         */
        changeMarker(key) {
            const newMarkers = this.markers.map((item, index) => {
                if (index !== key) {
                    item.width = 28;
                    item.height = 34;
                } else {
                    item.width = 42;
                    item.height = 51;
                }
                return item;
            });
            this.markers = newMarkers;
        },
        /**
         * 视野发生变化(缩放、拖动)
         * @Author:付常涛
         * @Date: 2023/05/15 20:43:43
         */
        regionchange(e) {
            // console.log("视野变化", e);
        },
        /**
         * 查看终端详情
         * @Author:付常涛
         * @Date: 2023/05/16 09:37:19
         */
        goStoreDetails(data, index) {
            // 将预览状态到详情flag标记为false
            this.previewToDetail = false;
            this.storeDetailsFlag = true;
            this.storeOptions = data;
            this.getStoreDetailsHeight();
            this.longitude = Number(data.longitude);
            this.latitude = Number(data.latitude);
            this.temMarker = this.$utils.deepcopy(this.markers);
            this.markers[index].width = 42;
            this.markers[index].height = 51;
            this.markers = [this.markers[index]];
            this.selectIndex = index;
        },
        /**
         * 返回终端地图列表
         * @Author:付常涛
         * @Date: 2023/05/16 09:44:16
         */
        async backListMap() {
            const that = this;
            this.storeDetailsFlag = false;
            if (this.previewToDetail) {
                // 从预览状态点击mark到展示详情,返回到预览状态
                this.previewStatus = true;
            } else {
                // 只有从列表到详情才拷贝了temMarker
                if (this.temMarker.length > 0) {
                    this.markers = this.temMarker;
                }
            }
            this.markers[this.selectIndex].width = 28;
            this.markers[this.selectIndex].height = 34;
            // 返回列表前-恢复经纬度
            // this.longitude = this.copyLongitude;
            // this.latitude = this.copyLatitude;
            this.countMapHeightTimer = setTimeout(function () {
                that.countMapHeight();
            }, 200);
            // await this.getAddress();
        },
        /**
         * 路线导航
         * @Author:zengqiang
         * @Date: 2025/04/26
         */
        navGpsLine() {
            const that = this;
            wx.openLocation({
                latitude: Number(that.storeOptions.latitude),
                longitude: Number(that.storeOptions.longitude),
                scale: 16,
                name: that.storeOptions.acctName,
                address: `${that.storeOptions.province}${that.storeOptions.city}${that.storeOptions.district}${that.storeOptions.address}`,
            });
        },
        /**
         * 终端检查
         * @Author:zengqiang
         * @Date: 2025/04/26
         */
        async goTerminalCheck(data) {
            this.$nav.push('/pages/terminal2/terminal-check/check-info-list-page.vue', {data});
        }
    },
};
</script>

<style lang="scss">
@import "../../../styles/list-card";
.terminal-check-map-distribute-page {
    background-color: #ffffff;
    padding-bottom: 0 !important;
    .search-container {
        padding-left: 24px;
        color: #8c8c8c;
        font-size: 28px;
        text-align: center;
        view {
            font-size: 24px;
        }
    }
    .map-callout {
        position: absolute;
        z-index: 10000;
        left: 10px;
        top: 264px;
        border-radius: 20px;
        background: rgba(0, 0, 0, 0.7);
        line-height: 52px;
        text-align: center;
        color: #fff;
        font-size: 24px;
        padding: 0 20px;
        .map-callout-switch {
            display: inline-block;
            color: rgb(65, 110, 239);
            font-weight: bold;
            padding-left: 20px;
            .link-icon {
                position: relative;
                top: -2px;
            }
        }
    }
    .filter-wrap {
        .agree-type {
            margin-left: 24px;
        }
    }
    .filter-content {
        display: flex;
        align-items: center;
        padding: 10px 24px;
        justify-content: space-between;
        .link-filter {
            display: inline-block;
        }
        .address-select {
            max-width: 550px;
            display: inline-block;
            position: relative;
            padding-right: 24px;
            .link-input-content {
                overflow: hidden;
                .link-input-inner {
                    overflow: scroll;
                }
            }
            .address-content {
                // width: 170px;
            }
            .address-icon {
                position: absolute;
                right: -6px;
                top: 50%;
                transform: translateY(-50%);
            }
        }
    }
    .map-content {
        width: 100%;
        .location-aim-container {
            @include flex-end-end;
            margin-right: 24px;
            margin-top: 16px;
            .location-aim {
                width: 72px;
                height: 72px;
                border-radius: 50%;
                background: #ffffff;
                box-shadow: 0 5px 9px 0 rgba(11, 33, 85, 0.14);
                text-align: center;
                .aim-image {
                    margin: 11px auto;
                    width: 50px;
                    height: 50px;
                }
            }
        }
    }
    .collapse-content {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px;
        background-color: #ebeef5;
        .collapse-icon {
            font-size: 52px;
            font-weight: bold;
            transition: transform 0.3s;
        }
        .active {
            transform: rotate(180deg);
        }
    }
    .store-list-container {
        background: #ffffff;
        border-radius: 32px 32px 0 0;
        transition: all 0.4s;
        .media-list {
            @include flex;
            border-bottom: 1px solid #f2f2f2;
            .media-list-logo {
                /*box-shadow: 0 4px 31px 0 rgba(0,44,152,0.22);*/
                border-radius: 16px;
                width: 128px;
                height: 128px;
                overflow: hidden;
                margin: 32px 0 32px 24px;
            }
            .store-content {
                width: 80%;
                .store-content-top {
                    @include flex-start-center;
                    @include space-between;
                    margin-left: 24px;
                    margin-top: 56px;
                    .store-title {
                        font-family: PingFangSC-Semibold, serif;
                        font-size: 32px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 32px;
                    }
                    .store-level {
                        margin-right: 24px;
                        width: 120px;
                        height: 44px;
                        image {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }
                .store-content-address {
                    margin-left: 24px;
                    margin-top: 20px;
                    font-family: PingFangSC-Regular, serif;
                    font-size: 24px;
                    color: #8c8c8c;
                    letter-spacing: 0;
                    line-height: 32px;
                }
            }
        }
    }
    .store-details {
        background: #ffffff;
        .icon-back {
            @include flex-start-center;
            width: 100%;
            padding-left: 24px;
            padding-bottom: 24px;
            padding-top: 24px;
            .icon-left {
                color: #ffffff;
                font-size: 36px;
                background: rgba(0, 0, 0, 0.4);
                width: 50px;
                height: 50px;
                line-height: 50px;
                border-radius: 50%;
                text-align: center;
            }
        }
        .store-item {
            background: #ffffff;
            border-radius: 32px 32px 0 0;
            padding-bottom: 40px;
            padding-left: 24px;
            @include media-list();
            /*deep*/
            .store-type-detail {
                white-space: nowrap;
                border: 1px solid #2f69f8;
                border-radius: 8px;
                font-size: 20px;
                padding-left: 18px;
                padding-right: 18px;
                line-height: 40px;
                height: 40px;
                color: #2f69f8;
                margin-right: 10px;
            }
            .store-content-address {
                color: #8c8c8c !important;
                line-height: 32px !important;
            }
            .store-content-company {
                font-family: PingFangSC-Semibold, serif;
                font-size: 26px;
                color: #262626;
                margin-left: 24px;
                margin-top: 20px;
            }
        }
        .button {
            padding-bottom: 68px;
            padding-left: 24px;
            padding-right: 24px;
            @include flex-start-center;
            @include space-between;
            .button-item {
                width: 250px;
                height: 72px;
            }
        }
    }
}
</style>
