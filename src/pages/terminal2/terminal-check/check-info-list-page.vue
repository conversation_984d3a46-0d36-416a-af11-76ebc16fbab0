<!--
@created<PERSON><PERSON>
@date  2025/04/23
@description 检查明细列表页
-->
<template>
    <link-page class="check-info-list-page">
        <link-auto-list 
            :option="checkInfoOption" 
            :hideCreateButton="true">
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="info-rows" @tap="goDetail(data)">
                    <view class="check-info-card" slot="note">
                        <view class="card-item">
                            <text class="label">
                                终端名称：
                            </text>
                            <text class="val">
                                {{data.acctName}}
                            </text>
                        </view>
                        <view class="card-item">
                            <text class="label">
                                检查批次：
                            </text>
                            <text class="val">
                                {{data.no}}
                            </text>
                        </view>
                        <view class="card-item">
                            <text class="label">
                                检查时间：
                            </text>
                            <text class="val">
                                {{data.created}}
                            </text>
                        </view>
                        <view class="card-item">
                            <text class="label">
                                检查人：
                            </text>
                            <text class="val">
                                {{data.name}}
                            </text>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>    
        <link-sticky>
            <link-button block size="large" :shadow="true" @tap="changeDetails">终端详情</link-button>
            <link-button block size="large" @tap="createCheck" :shadow="true">新建检查</link-button>
        </link-sticky>
    </link-page>
</template>
<script>
definePageConfig({
    navigationBarTitleText: '终端检查详情'
});
export default {
    name: 'check-info-list-page',
    data() {
        const checkInfoOption = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/inspect/queryByExamplePage'
            },
            param:{
                oauth: 'ALL',
                order: 'desc',
                attr1: 'nowNo',
                filtersRaw: [
                    {id: 'acctCode', property: 'acctCode', value: this.pageParam.data.acctCode},
                ]
            },
            hooks: {
                beforeLoad (data) {
                    data.param.sort = 'created';
                }
            }
        })
        return {
            checkInfoOption
        }
    },
    methods: {
        /**
         * 跳转终端详情页
         * <AUTHOR>
         * @date	2025/04/23
         */ 
        changeDetails() {
            this.$nav.push("/pages/terminal/terminal/client-details-page", {
                data: this.pageParam.data
            });
        },
        /**
         * 新建终端检查检查
         * <AUTHOR>
         * @date	2025/04/23
         */
        createCheck() {
            this.$nav.push('/pages/terminal2/terminal-check/create-check-page.vue', {data: this.pageParam.data});
        },
        /**
         * 查看检查详情信息
         * <AUTHOR>
         * @date	2025/04/23
         */ 
        goDetail(data) {
            this.$nav.push('/pages/terminal2/terminal-check/check-details-page.vue', {
                data,
                terminalData: this.pageParam.data
            });
        },
        onBack(msg) {
            if (msg === 'refresh') this.checkInfoOption.methods.reload();
        }
        
    },
}
</script>
<style lang="scss">
.check-info-list-page {
    .link-auto-list-wrapper {
        margin: 0 32px 0 32px;
        padding-top: 32px;
        .info-rows {
            background: #FFFFFF;
            border-radius: 24px;
            padding: 36px 24px;
            margin-bottom: 24px;
            .check-info-card {
                font-size: 28px;
                color: #262626;
            }
            .card-item {
                margin-bottom: 10px;
            }
        }
    }
}    
</style>