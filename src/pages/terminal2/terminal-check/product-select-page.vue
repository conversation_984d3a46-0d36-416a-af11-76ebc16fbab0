<template>
    <link-page class="product-select-page">
        <link-checkbox-group v-model="chooseIds">
            <view class="product-item" v-for="(item, index) in productList" :key="`product_${index}`">
                <item :arrow="false" :key="item.id">
                    <link-checkbox :val="item.id" slot="thumb" toggleOnClickItem @tap="selectProd(item)"/>
                </item>
                <view class="product-name">{{item.name}}</view>
            </view>
        </link-checkbox-group>
        <link-sticky>
            <link-checkbox-group v-model="allSelectFlag" class="select-all">
                <item :arrow="false">
                    <link-checkbox val="all" slot="thumb" toggleOnClickItem @tap="selectAll"/>
                    <text slot="title">全选</text>
                </item>
            </link-checkbox-group>
            <link-button block autoLoading size="large" @tap="submitCash">确认</link-button>
        </link-sticky>
    </link-page>
</template>
<script>
definePageConfig({
    navigationBarTitleText: '请选择品项'
});
export default {
    name: 'product-select-page',
    data() {
        return {
            chooseIds: [],
            productList: [],
            allSelectFlag: []
        }
    },
    async created() {
        let lovKey = '';
        // 国窖陈列品项值列表
        if (this.pageParam.brandComCode === '5600') lovKey = 'GUO_JIAO_CHECK_PRODUCT';
        // 特曲陈列品项值列表
        if (this.pageParam.brandComCode === '5137') lovKey = 'TE_QU_CHECK_PRODUCT';
        // 蓉城怀旧、怀旧陈列品项值列表
        if (['5910','1204'].includes(this.pageParam.brandComCode)) lovKey = 'HUAI_JIU_CHECK_PRODUCT';
        // 大成浓香、永粮、鸿泸陈列品项值列表
        if (['5161','5902','5903'].includes(this.pageParam.brandComCode)) lovKey = 'DA_CHENG_CHECK_PRODUCT';
        // 窖龄、酒聚酒陈列品项值列表
        if (['5151', '5153'].includes(this.pageParam.brandComCode)) lovKey = 'JIAO_LING_CHECK_PRODUCT';
        this.productList = await this.$lov.getLovByType(lovKey);
    },
    methods: {
        /**
         * 确认选择品项
         * <AUTHOR>
         * @date	2025/04/24
         */
        submitCash() {
            if (!this.chooseIds.length) return;
            const data = [];
            this.productList.forEach((el) => {
                if (this.chooseIds.includes(el.id)) {
                    data.push({
                        item: el.val,
                        num: ''
                    })
                }
            });
            this.$nav.back({data, pageType: 'selectProduct'});
        },
        /**
         * 全选
         * <AUTHOR>
         * @date	2025/04/24
         */
        selectAll() {
            this.$nextTick(() => {
                if (this.allSelectFlag.length) {
                    this.chooseIds = this.productList.map(item => item.id);
                } else {
                    this.chooseIds = [];
                }
            });
        },
        /**
         * 单选
         * <AUTHOR>
         * @date	2025/04/24
         */
        selectProd() {
            this.$nextTick(() => {
                this.allSelectFlag = this.chooseIds.length === this.productList.length ? ['all'] : [];
            });
        }
    },
}
</script>
<style lang="scss">
    .product-select-page {
        background-color: #fff;
        padding: 20px 30px 30px 30px;
        .product-item {
            height: 80px;
            display: flex;
            align-items: center;
            font-size: 28px;
            border-bottom: 1px solid #f8f8f8;
            &:last-child {
                border-bottom: none;
            }
            .link-item {
                padding: 0;
                min-height: 80px;
            }
        }
        .product-name {
            margin-left: 20px;
        }
    }
</style>