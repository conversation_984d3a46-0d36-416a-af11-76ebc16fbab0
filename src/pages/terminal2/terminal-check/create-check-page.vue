<!--
@created<PERSON><PERSON>
@date  2025/04/25
@description 终端检查
-->
<template>
    <link-page class="create-check-page check-terminal-common-style">
        <link-form :value="formData" ref="form" :rules="formRules" hideSaveButton>
            <!-- 终端状态 -->
            <terminal-status-check v-if="nextNum === 1" :form-data="formData" :terminal-data="pageParam.data" :is-edit="true"></terminal-status-check>
            <!-- 档案情况 -->
            <files-situation-check v-if="nextNum === 2" :form-data="formData" :terminal-data="pageParam.data" :is-edit="true"></files-situation-check>
            <!-- 门头店招 -->
            <door-header-check 
                v-if="nextNum === 3"
                @imgList="imgList"
                :form-data="formData" 
                :terminal-data="pageParam.data" 
                :is-edit="true">
            </door-header-check>
            <!-- 产品展陈 -->
            <product-check v-if="nextNum === 4" @imgList="imgList" :form-data="formData" :terminal-data="pageParam.data" :is-edit="true"></product-check>
            <!-- 数字化政策运用 -->
            <number-policy-utilize-check v-if="nextNum === 5" :form-data="formData" :terminal-data="pageParam.data" :is-edit="true"></number-policy-utilize-check>
        </link-form>

        <!-- 第一步 -->
        <link-sticky v-if="nextNum === 1">
            <link-button @tap="submit" block v-if="formData.close === 'Y' || formData.notFound === 'Y'">提交</link-button>
            <link-button @tap="nextStep" v-else block autoLoading>下一步</link-button>
        </link-sticky>
        <link-sticky v-if="nextNum === 4">
            <link-button @tap="nextNum--" block mode="stroke" v-if="!isSubmit">上一步</link-button>
            <link-button @tap="submit" block v-if="isSubmit" autoLoading>提交</link-button>
            <link-button @tap="nextStep" block v-if="!isSubmit">下一步</link-button>
        </link-sticky>
        <!-- 剩余几步 -->
        <link-sticky v-if="![1, 4].includes(nextNum)">
            <link-button @tap="nextNum--" block mode="stroke">上一步</link-button>
            <link-button @tap="submit" block v-if="nextNum === 5" autoLoading>提交</link-button>
            <link-button @tap="nextStep" block v-if="nextNum !== 5">下一步</link-button>
        </link-sticky>
    </link-page>
</template>
<script>
import TerminalStatusCheck from './components/terminal-status-check.vue'
import FilesSituationCheck from './components/files-situation-check.vue'
import DoorHeaderCheck from './components/door-header-check.vue'
import ProductCheck from './components/product-check.vue'
import NumberPolicyUtilizeCheck from './components/number-policy-utilize-check.vue'
import {reverseTMapGeocoder} from "../../../utils/locations-tencent";
definePageConfig({
    navigationBarTitleText: '新建终端检查'
});
export default {
    name: 'create-check-page',
    components: {
        TerminalStatusCheck,
        FilesSituationCheck,
        DoorHeaderCheck,
        ProductCheck,
        NumberPolicyUtilizeCheck
    },
    data() {
        return {
            formData: {
                id: '',
                itemList: [],
                acctCode: this.pageParam.data.acctCode
            },
            formRules: {
                phone: this.Validator.phone()
            },
            doorHeaderImgList: [], // 竞品门头图片
            doorHeaderFilesImgList: [], // 门头与档案图片
            displayNumImgList: [], // 陈列品项及数量拍照
            nextNum: 1 // 当前在哪一步
        }
    },
    computed: {
        isSubmit() {
            return this.nextNum === 4 && this.formData.myDisplay === 'N';
        }
    },
    async onShow(){
        if (this.formData.addressCorrect !== 'N') return;
        // 处理选中地址
        const location = this.$locations.QQGetLocation();
        if (location) {
            let addressInfo =  await reverseTMapGeocoder(location.latitude, location.longitude, '新建终端检查');
            let address = {
                latitude: addressInfo.wxMarkerData[0].latitude,
                longitude: addressInfo.wxMarkerData[0].longitude,
                address: ''
            }
            this.$set(address, 'address', location.address + location.name);
            this.formData = Object.assign({}, this.formData, address);
        }
        
    },
    async created() {
        const id = await this.$newId();
        this.formData.id = id;
    },
    watch: {
        'formData.addressCorrect'(newData, oldData) {
            if (newData === 'Y') {
                delete this.formData.address;
                delete this.formData.latitude;
                delete this.formData.longitude;
            }
        },
        'formData.phoneCorrect'(newData, oldData) {
            if (newData === 'Y') {
                delete this.formData.phone;
            }
        },
        'formData.myFront'(newData, oldData) {
            if (newData === 'Y') {
                delete this.formData.competing;
            } else {
                delete this.formData.frontSize;
                delete this.formData.frontPrsition;
                delete this.formData.frontSight;
            }
        },
        'formData.phoneCorrect'(newData, oldData) {
            if (newData === 'Y') {
                delete this.formData.phone;
            }
        },
        'formData.myDisplay'(newData, oldData) {
            if (newData === 'N') {
                // 是否存在我品展陈为否时清除下一步已选内容防止提交数据问题
                delete this.formData.itemList;
                delete this.formData.displayPosition;
                delete this.formData.sightPosition;
                delete this.formData.centralizedDisplay;
                delete this.formData.terminalVibe;
                // 数字化政策应用相关内容置空
                delete this.formData.terminalClear;
                delete this.formData.terminalRedemption;
                delete this.formData.knowActivity;
                delete this.formData.oneMorePolicy;
                delete this.formData.knowRedemption;
                delete this.formData.redemptionOnTime;
                delete this.formData.redemptionServiceFee;
                delete this.formData.knowDrawCash;
                delete this.formData.rebateScan;
                delete this.formData.cashFlowDifficulty;
                delete this.formData.knowRyPlatform;
                delete this.formData.competingOnsale;
                delete this.formData.advice;
                delete this.formData.remark;
            }
        },
        'formData.advice'(newData, oldData) {
            if (newData === 'Y') {
                delete this.formData.remark;
            }
        },
    },
    methods: {
        /**
         * 下一步
         * <AUTHOR>
         * @date	2025/04/24
         */
        async nextStep() {
            // 进入到下一步之前需校验当前步骤必填字段
            if (this.nextNum === 1 && !this.checkTerminalStatus()) return;
            if (this.nextNum === 2 && !this.checkFilesSituation()) return;
            if (this.nextNum === 3 && !this.checkDoorHeader()) return;
            if (this.nextNum === 4 && !this.checkProduct()) return;
            this.nextNum++;
        },
        /**
         * 提交检查
         * <AUTHOR>
         * @date	2025/04/24
         */
        async submit() {
            try {
                let params = {};
                if (this.formData.close === 'N' && this.formData.notFound === 'N') {
                    params = this.formData;
                } else {
                    // 第一步提交时只需提交这些参数
                    params.close = this.formData.close;
                    params.notFound = this.formData.notFound;
                    params.id = this.formData.id;
                    params.acctCode = this.formData.acctCode;
                }
                // 在第一步点击提交是只校验第一步内容
                if (this.nextNum === 1 && !this.checkTerminalStatus()) return;
                // 在第四步提交时只需要校验第四步内容
                if (this.nextNum === 4 && !this.checkProduct()) return;
                // 在最后一步提交时只校验当前步骤内容
                if (this.nextNum === 5 && !this.checkNumberPolicy()) return;
                const res = await this.$http.post('action/link/inspect/insert', params);
                if (res.success) {
                    this.$message.success('终端检查提交成功！');
                    this.$nav.back('refresh');
                } else {
                    this.$message.error('终端检查提交失败！');
                }
            } catch(err) {
                console.log('提交检查出错:' + err)
            }
        },
        /**
         * 提交检查
         * <AUTHOR>
         * @date	2025/04/24
         */
        checkTerminalStatus() {
            if (this.$utils.isEmpty(this.formData.close)) {
                this.$message.error('请检查终端关门');
                return false;
            };
            if (this.$utils.isEmpty(this.formData.notFound)) {
                this.$message.error('请检查位置找不到');
                return false;
            };
            return true
        },
        checkFilesSituation() {
            if (this.$utils.isEmpty(this.formData.addressCorrect)) {
                this.$message.error('请检查地址是否准确');
                return false;
            };
            if (this.formData.addressCorrect === 'N' && this.$utils.isEmpty(this.formData.address)) {
                this.$message.error('请选择地址');
                return false;
            };
            if (this.$utils.isEmpty(this.formData.phoneCorrect)) {
                this.$message.error('请检查老板联系电话是否正确');
                return false;
            };
            // 手机号格式校验
            const reg = /^[1][3456789][0-9]{9}$/;
            if (this.formData.phoneCorrect === 'N' && !reg.test(this.formData.phone)) {
                this.$message.error('老板联系电话格式错误');
                return false;
            };
            return true;
        },
        checkDoorHeader() {
            if (this.$utils.isEmpty(this.formData.myFront)) {
                this.$message.error('请检查是否我品门头');
                return false;
            };
            if (this.formData.myFront === 'N' && this.$utils.isEmpty(this.formData.competing)) {
                this.$message.error('请选择竞品门头名称');
                return false;
            };
            if (this.formData.myFront === 'N' && this.$utils.isEmpty(this.doorHeaderImgList)) {
                this.$message.error('请上传竞品门头照片');
                return false;
            }
            if (this.$utils.isEmpty(this.formData.frontPhoto)) {
                this.$message.error('请检查门头与档案照片是否一致');
                return false;
            };
            if (this.formData.frontPhoto === 'N' && this.$utils.isEmpty(this.doorHeaderFilesImgList)) {
                this.$message.error('请上传门头与档案照片');
                return false;
            }
            if (this.formData.myFront === 'Y' && this.$utils.isEmpty(this.formData.frontSize)) {
                this.$message.error('请选择门头大小');
                return false;
            };
            if (this.formData.myFront === 'Y' && this.$utils.isEmpty(this.formData.frontPrsition)) {
                this.$message.error('请选择门头位置');
                return false;
            };
            if (this.formData.myFront === 'Y' && this.$utils.isEmpty(this.formData.frontSight)) {
                this.$message.error('请选择门头视线');
                return false;
            };
            return true;
        },
        checkProduct() {
            if (this.$utils.isEmpty(this.formData.myDisplay)) {
                this.$message.error('请检查是否存在我品展陈');
                return false;
            }
            if (this.formData.myDisplay === 'N') return true
            if (this.$utils.isEmpty(this.formData.itemList)) {
                this.$message.error('请添加陈列品项及数量');
                return false;
            } else {
                let msg = true;
                this.formData.itemList.forEach((el) => {
                    if (this.$utils.isEmpty(el.num)) return msg = false 
                    return
                });
                if (!msg) {
                    this.$message.error('请输入实际陈列数量');
                    return false;
                }
            }
            
            if (this.$utils.isEmpty(this.formData.displayPosition)) {
                this.$message.error('请选择陈列货架位置');
                return false;
            };
            if (this.$utils.isEmpty(this.formData.sightPosition)) {
                this.$message.error('请选择陈列视线位置');
                return false;
            };
            if (this.$utils.isEmpty(this.formData.centralizedDisplay)) {
                this.$message.error('请选择产品是否集中陈列');
                return false;
            };
            if (this.$utils.isEmpty(this.formData.terminalVibe)) {
                this.$message.error('请选择氛围/物料营造');
                return false;
            }
            if (this.$utils.isEmpty(this.displayNumImgList)) {
                this.$message.error('请上传产品陈列照片');
                return false;
            }
            return true
        },
        checkNumberPolicy() {
            if (this.$utils.isEmpty(this.formData.terminalClear)) {
                this.$message.error('请检查是否了解终端会员中心');
                return false;
            };
            if (this.$utils.isEmpty(this.formData.terminalRedemption)) {
                this.$message.error('请检查是否了解通过消费者开瓶获得会员积分，并在商城里兑换商品');
                return false;
            };
            if (this.$utils.isEmpty(this.formData.knowActivity)) {
                this.$message.error('请检查是否知晓再来一瓶活动政策');
                return false;
            };
            if (this.$utils.isEmpty(this.formData.oneMorePolicy)) {
                this.$message.error('请检查是否知晓再来一瓶终端服务费政策');
                return false;
            }
            if (this.$utils.isEmpty(this.formData.knowRedemption)) {
                this.$message.error('请检查是否知晓再来一瓶服务费申请兑付流程');
                return false;
            };
            if (this.$utils.isEmpty(this.formData.redemptionOnTime)) {
                this.$message.error('请检查再来一瓶服务费用是否兑付及时');
                return false;
            };
            if (this.$utils.isEmpty(this.formData.redemptionServiceFee)) {
                this.$message.error('请检查是否兑付过服务费');
                return false;
            };

            if (this.$utils.isEmpty(this.formData.knowDrawCash)) {
                this.$message.error('请检查店老板是否知晓窖友汇提现功能');
                return false;
            };
            if (this.$utils.isEmpty(this.formData.rebateScan)) {
                this.$message.error('请检查是否知晓开瓶返利，扫码抢盘等政策活动');
                return false;
            };

            if (this.$utils.isEmpty(this.formData.cashFlowDifficulty)) {
                this.$message.error('请检查采购泸州老窖产品时是否需要融资助力');
                return false;
            };
            if (this.$utils.isEmpty(this.formData.knowRyPlatform)) {
                this.$message.error('请检查是否知晓泸州老窖公司易融超市');
                return false;
            };
            if (this.$utils.isEmpty(this.formData.competingOnsale)) {
                this.$message.error('请检查是否有竞品开瓶酒销售');
                return false;
            };
            if (this.$utils.isEmpty(this.formData.advice)) {
                this.$message.error('请检查其他问题反馈');
                return false;
            };
            if (this.formData.advice === 'Y' && this.$utils.isEmpty(this.formData.remark)) {
                this.$message.error('请填写问题反馈');
                return false;
            };
            return true;
        },
        onBack(param) {
            if (param && param.pageType === 'selectProduct') this.$set(this.formData, 'itemList', param.data);
        },
        /**
         * 上传图片
         * <AUTHOR>
         * @date	2025/04/24
         */
        imgList(data) {
            if (data.listName === 'doorHeaderImg') this.doorHeaderImgList = data.list;
            if (data.listName === 'doorHeaderFilesImg') this.doorHeaderFilesImgList = data.list;
            if (data.listName === 'displayNumImg') this.displayNumImgList = data.list;
        }
    },
}
</script>
<style lang="scss">
@import "./css/common.scss";
</style>