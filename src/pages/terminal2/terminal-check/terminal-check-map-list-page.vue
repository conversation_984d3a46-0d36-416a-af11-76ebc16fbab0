<!--
@created<PERSON><PERSON>
@date  2025/04/25
@description 终端检查-地图列表页
-->
<template>
    <link-page class="terminal-check-map-list-page">
        <!--搜索-->
        <link-search-input
            class="search-input"
            v-model="searchText"
            @change="searchStoreName"
            placeholder="终端名称"
        >
            <view class="search-container" @tap="goDistribution">
                <link-icon class="icon" icon="icon-dizhi" />
                <view>终端分布</view>
            </view>
        </link-search-input>
        <view class="filter-wrap">
            <view class="filter-content">
                <view class="address-select">
                    <link-address
                        placeholder="当前地区"
                        class="address-content"
                        :province.sync="selectProvince"
                        :city.sync="selectCity"
                        :district.sync="selectDistrict"
                        @update:district="changeDistrict"
                        :custom="addressCustomOption"
                    />
                    <link-icon class="address-icon" icon="mp-desc" />
                </view>
            </view>
        </view>

        <!-- 地图气泡 -->
        <view class="map-callout" :style="{ top: mapCalloutTop }">
            <view>必检{{ checkTeminalNum }}家</view>
            <view>非必检{{ unMustCheckTotal }}家</view>
        </view>
        <!-- 地图 -->
        <map
            id="map"
            :longitude="longitude"
            :latitude="latitude"
            :scale="scale"
            :show-location="true"
            :show-compass="true"
            :show-scale="true"
            class="map-content"
            :setting="setting"
            :style="{ height: mapHeight + 'px' }"
            :markers="markers"
            @markertap="markerTap"
            @regionchange="regionchange"
        >
            <cover-view
                :style="{
                    'margin-top': mapHeight - 60 + 'px',
                    display: mapHeight === 0 ? 'none' : 'block',
                }"
            >
                <cover-view
                    class="location-aim-container"
                    @tap="backCurrentPosition"
                >
                    <cover-view class="location-aim">
                        <cover-image
                            class="aim-image"
                            :src="$imageAssets.locationAimImage"
                        ></cover-image>
                    </cover-view>
                </cover-view>
            </cover-view>
        </map>
        <!--折叠终端列表-->
        <view
            v-if="!storeDetailsFlag"
            class="collapse-content"
            @tap="collapseList"
        >
            <link-icon
                class="collapse-icon"
                :class="{ active: collapseActive }"
                icon="mp-arrow-top"
            />
        </view>
        <!--终端列表-->
        <scroll-view
            class="store-list-container"
            scroll-y="true"
            id="list-store"
            v-if="!storeDetailsFlag"
            @scrolltolower="scrollToLower"
            :style="{ height: storeListTotalHeight + 'px' }"
        >
            <view v-for="(data, index) in storeList" :key="`mapTerminal_${index}`">
                <view class="media-list" @tap="goStoreDetails(data, index)">
                    <image
                        class="media-list-logo"
                        :src="data.storePicPreKey ? $image.getSignedUrl(data.storePicPreKey) : $imageAssets.terminalDefaultImage"
                        lazy-load="true"
                    ></image>
                    <view class="store-content">
                        <!-- <view class="store-content-company">{{data.acctCode}}</view> -->
                        <view class="store-content-company">{{data.acctName}}</view>
                        <!-- <view class="store-content-company">{{data.salesmanBrandCom}}</view> -->
                        <view class="store-content-address">{{ data.distance }}米 | {{data.addrDetailAddr}}</view>
                        <view class="store-content-company">检查状态-{{checkStatusHandle(data.checkFlag, data.mustCheckFlag)}}</view>
                    </view>
                </view>
            </view>
        </scroll-view>
        <!--终端详情-->
        <view class="store-details" v-if="storeDetailsFlag">
            <view class="icon-back" @tap="backListMap">
                <view class="iconfont icon-left"></view>
            </view>
            <view class="store-item">
                <image
                    class="media-list-logo"
                    :src="storeOptions.storeUrl"
                ></image>
                <view class="store-content">
                    <view class="store-content-company">{{storeOptions.acctCode}}</view>
                    <view class="store-content-company">{{storeOptions.acctName}}</view>
                    <view class="store-content-company">{{storeOptions.companyName}}</view>
                    <view class="store-content-address">{{storeOptions.distance}}米 | {{storeOptions.addrDetailAddr}}</view>
                    <view class="store-content-company">检查状态-{{storeOptions.checkStatus}}</view>
                </view>
            </view>
            <view class="button">
                <link-button
                    class="button-item"
                    label="路线"
                    icon="icon-luxian"
                    size="normal"
                    @tap="navGpsLine"
                />
                <link-button
                    class="button-item"
                    label="终端检查"
                    mode="fill"
                    size="normal"
                    @tap="goTerminalCheck(storeOptions)"
                />
            </view>
        </view>
    </link-page>
</template>

<script lang="jsx">
definePageConfig({
    navigationBarTitleText: '终端地图列表'
});

export default {
    name: "terminal-check-map-list-page",
    data() {
        return {
            searchText: '', // 顶部搜索框文本
            setting: {
                enable3D: false,
                enableTraffic: false,
            },
            pageNum: 1, // 当前终端地图列表页
            pageLimit: 200, // 一页数据的条数
            nextPageFlag: true, // 是否还有下一页
            longitude: 0, // 经度
            latitude: 0, // 纬度
            mapHeight: 0, // 地图高度
            scale: 17, // 缩放级别17
            storeListTotalHeight: 0, // 终端列表滚动容器高度
            searchInputHeight: 0, // 搜索框高度
            filterContentHeight: 0, // 筛选行的高度
            addressData: null, // 地址信息对象
            addressDataFull: "", // 完整地址字符串信息
            storeList: [], // 终端列表
            storeMapList: [], // 终端列表
            markers: [], // 地图标记数组
            temMarker: [], // 暂存地图标记
            storeOptions: {}, // 当前选中的终端
            selectIndex: null, // 当前选中的mark标记
            storeDetailsFlag: false, // 当前是否展示终端详情
            countMapHeightTimer: null, // 计算终端列表容器media-list的view元素高度计时器
            storeDetailsTimer: null, // 计算终端详情容器store-details的view元素高度计时器
            selectProvince: "", // 选择的省份
            selectCity: "", // 选择的城市
            selectDistrict: "", // 选择的区县
            districtCode: '', // 区县编码
            collapseContentHeight: 0, // 搜索框高度
            collapseActive: false, // 折叠按钮是否已展开
            mapHeightCopy: 0, // 地图高度拷贝-用于折叠功能
            storeListTotalHeightCopy: 0, // 终端列表滚动容器高度-用于折叠功能
            CITYWIDE: "全市区", // 全市区字符串常量
            checkTeminalNum: 0,
            unMustCheckTotal: 0, // 非必检终端家数
            allAddress: [], // 所有地址
        };
    },
    computed: {
        mapCalloutTop() {
            return `${
                this.filterContentHeight + this.searchInputHeight + 15
            }px`;
        }
    },
    async created() {
        try {
            this.$utils.showLoading('加载中');
            // 获取搜索框高度
            this.getSearchInput();
            // 获取筛选框高度
            this.getFilterContent();
            // 获取折叠按钮的高度
            this.getCollapseHeight();
            // 获取地址信息
            await this.getAddress();
            this.allAddress = await this.$address.getData();
            // 请求终端列表数据
            await this.getAccntList();
            // 请求终端区域数据
            // await this.getAreaTerminal();
        } finally {
            this.$utils.hideLoading();
        }
    },
    destroyed() {
        clearTimeout(this.countMapHeightTimer);
        clearTimeout(this.storeDetailsTimer);
    },
    methods: {
        /**
         * 地址组件自定义地址数据源
         * <AUTHOR>
         * @date 2025-05-06
         */
        addressCustomOption(tag, parentValue) {
            // 区县前置'全市区'选项
            if (tag === "d") {
                for (let p = 0; p < this.allAddress.length; p++) {
                    const city = this.allAddress[p].children;
                    for (let c = 0; c < city.length; c++) {
                        if (city[c].name === parentValue) {
                            let dis = [];
                            const allD = { name: this.CITYWIDE };
                            dis.push(allD);
                            dis = dis.concat(city[c].children);
                            return dis.map((d) => {
                                return d.name;
                            });
                        }
                    }
                }
            }
        },
        /**
         * 复制终端联系人电话
         * * <AUTHOR>
         * @date 2023-09-12
         * @param text
         */
        copyActCode(text) {
            if(text){
                wx.setClipboardData({data: text});
            }
        },
        /**
         * 返回地图模式
         * <AUTHOR>
         * @date	2023/5/30 16:23
         */
        onBack(param) {
            if (param === 'reload') {
                this.getAccntList();
                // this.getAreaTerminal();
            }
        },
        /**
         * 点击折叠按钮
         * @Author:付常涛
         * @Date: 2023/05/18 14:59:51
         */
        collapseList() {
            if (this.collapseActive) {
                // 已展开，就折叠
                this.collapseActive = false;
                this.mapHeight = this.mapHeightCopy;
                this.storeListTotalHeight = this.storeListTotalHeightCopy;
            } else {
                // 展开
                this.collapseActive = true;
                this.mapHeightCopy = this.mapHeight;
                this.storeListTotalHeightCopy = this.storeListTotalHeight;
                this.storeListTotalHeight =
                    this.storeListTotalHeight + this.mapHeight;
                this.mapHeight = 0;
            }
        },
        /**
         * 选择省市区后移动地图中心点
         * @Author:付常涛
         * @Date: 2023/05/16 16:51:57
         */
        async changeDistrict(val) {
            // 清空search搜索文本
            this.searchText = '';
            // 根据所选省市区，将地图位移至相应经纬度
            let address = this.selectProvince + this.selectCity;
            if (this.selectDistrict !== this.CITYWIDE) {
                address += this.selectDistrict;
            }
            // 根据所选省市区，将地图位移至
            const res = await this.$locations.reverseTMapGgeocodingLaLon(
                address
            );
            this.moveLocation(
                res.res.result.location.lng,
                res.res.result.location.lat
            );
            // 再通过地图中心点查询数据
            this.pageNum = 1;
            this.nextPageFlag = true;
            this.markers = [];
            this.storeList = [];
            this.longitude = res.res.result.location.lng;
            this.latitude = res.res.result.location.lat;
            if (this.selectDistrict !== this.CITYWIDE) this.districtCode = res.res.result.ad_info.adcode;
            // 如果当前展开了详情，则关闭终端详情
            this.storeDetailsFlag = false;
            this.getAccntList();
            // this.getAreaTerminal();
        },
        // 移动地图
        moveLocation(longitude, latitude) {
            let mpCtx = wx.createMapContext("map");
            mpCtx.moveToLocation({
                latitude,
                longitude,
            });
        },
        /**
         * 回到当前位置
         * @Author:付常涛
         * @Date: 2023/05/16 12:00:32
         */
        async backCurrentPosition() {
            // 清空search搜索文本
            this.searchText = '';
            await this.getAddress();
            this.scale = 14;
            this.moveLocation(this.longitude, this.latitude);
            // 重新请求当前定位附近终端
            this.pageNum = 1;
            this.nextPageFlag = true;
            this.storeList = [];
            this.markers = [];
            this.getAccntList();
            // this.getAreaTerminal();
        },
        /**
         * 按照终端名称进行搜索
         * @Author:付常涛
         * @Date: 2023/05/15 16:35:33
         */
        searchStoreName(val) {
            this.pageNum = 1;
            this.nextPageFlag = true;
            this.storeDetailsFlag = false;
            this.getAccntList(val);
            // this.getAreaTerminal(val);
        },
        /**
         * 获取终端列表
         * @Author:付常涛
         * @Date: 2023/05/15 16:43:40
         */
        async getAccntList() {
            let that = this;
            if (!this.nextPageFlag) return;
            try {
                if(!this.searchText) this.$utils.showLoading('请求中');
                const param = this.paramsHandle();
                const data = await this.$http.post(
                    "action/link/inspectorsRange/queryListByDistance",
                    param
                );
                if (data.success) {
                    // 如无数据，怎直接计算地图高度
                    if (data.rows.length === 0) {
                        this.markers = [];
                        this.storeList = [];
                        this.storeMapList = [];
                        this.$message.primary("当前区域无终端");
                        return
                    }
                    this.checkTeminalNum = data.total || 0;
                    this.unMustCheckTotal = data.unMustCheckTotal || 0;
                    // 终端列表数据
                    if (data.total > this.pageLimit * this.pageNum) {
                        this.pageNum++;
                    } else {
                        this.nextPageFlag = false;
                    }
                    
                    // 处理等forEach中异步全部执行完毕再 走下一步操作
                    if (this.pageNum === 1) {
                        this.storeList = data.rows;
                    } else {
                        this.storeList = this.storeList.concat(
                            data.rows
                        );
                    }
                    // 给终端加上递增唯一数字序列，用于地图标点id
                    let orderStartId = 123456;
                    data.rows.forEach((item, index) => {
                        data.rows[index].markId = orderStartId;
                        orderStartId += 5;
                    });
                    // 地图markers数据
                    // [acctCheckStatus=checked已检查] -绿色;[必检终端-待检查acctCheckStatus=mustUncheck] -红色;普通待检查acctCheckStatus=normalUncheck] -灰色
                    let markers = data.rows.map((item) => ({
                            iconPath: item.checkFlag ? "/static/images/map-icon/not-core.png" : (item.mustCheckFlag ? "/static/images/map-icon/core.png" : "/static/images/map-icon/no-check.png"),
                            id: item.markId,
                            zIndex:
                                item.checkFlag
                                    ? 9
                                    : item.mustCheckFlag
                                    ? 199
                                    : 99,
                            latitude: Number(item.latitude),
                            longitude: Number(item.longitude),
                            width: 28,
                            height: 34,
                        }));
                    this.markers = markers;
                    this.storeMapList = data.rows.map((item) => {
                        return {
                            id: item.id,
                            markId: item.markId,
                            acctCheckStatus: item.checkFlag ? 'checked' : item.mustCheckFlag ? 'mustUncheck' : 'normalUncheck',
                            longitude: item.longitude,
                            latitude: item.latitude,
                            distance: item.distance,
                        };
                    });
                    this.countMapHeightTimer = setTimeout(
                        function () {
                            that.countMapHeight();
                        },
                        200
                    );
                }
            } finally {
                if(!this.searchText) this.$utils.hideLoading();
            }

        },
        /**
         * 请求参数处理
         * @Author:zengqiang
         * @Date: 2025/04/26
         */
         paramsHandle() {
            let obj = {
                longitude: this.longitude,
                latitude: this.latitude,
                province: this.selectProvince,
                city: this.selectCity,
                distance: '5000'
            };
            if (this.selectDistrict !== this.CITYWIDE) {
                obj.districtCode = this.districtCode;
                obj.district = this.selectDistrict;
            }
            if (this.searchText) obj.acctName = this.searchText;
            return obj
        },
        /**
         * 去终端分布地图展示页
         * @Author:付常涛
         * @Date: 2023/05/15 16:57:37
         */
        goDistribution() {
            this.$nav.back();
        },
        /**
         * 获取搜索框高度
         * @Author:付常涛
         * @Date: 2023/05/15 17:13:42
         */
        getSearchInput() {
            const that = this;
            setTimeout(function () {
                const query = wx.createSelectorQuery();
                query
                    .select(".search-input")
                    .boundingClientRect((ret) => {
                        that.searchInputHeight = ret.height;
                    })
                    .exec();
            }, 200);
        },
        /**
         * 获取筛选行的高度(地址+筛选)
         * @Author:付常涛
         * @Date: 2023/05/25 22:27:54
         */
        getFilterContent() {
            const that = this;
            setTimeout(function () {
                const query = wx.createSelectorQuery();
                query
                    .select(".filter-wrap")
                    .boundingClientRect((ret) => {
                        that.filterContentHeight = ret.height;
                    })
                    .exec();
            }, 200);
        },
        /**
         * 获取地址
         * @Author:付常涛
         * @Date: 2023/05/15 17:16:31
         */
        async getAddress() {
            let coordinate = await this.$locations.getCurrentCoordinate();
            if (
                !this.$utils.isEmpty(coordinate.latitude) &&
                !this.$utils.isEmpty(coordinate.longitude)
            ) {
                this.longitude = Number(coordinate.longitude);
                this.latitude = Number(coordinate.latitude);
                let address = await this.$locations.reverseTMapGeocoder(
                    this.latitude,
                    this.longitude,
                    '陈列检查'
                );
                this.addressData = address["originalData"].result;
                this.addressDataFull = address["originalData"].result.formatted_address;
                this.selectProvince = this.addressData.addressComponent.province;
                this.selectCity = this.addressData.addressComponent.city;
                this.selectDistrict = this.addressData.addressComponent.district;
                this.districtCode = this.addressData.addressComponent.adcode;    
            }
        },
        /**
         * 获取展开终端列表icon的高度
         * @Author:付常涛
         * @Date: 2023/05/18 14:38:32
         */
        getCollapseHeight() {
            const that = this;
            setTimeout(function () {
                const query = wx.createSelectorQuery();
                query
                    .select(".collapse-content")
                    .boundingClientRect((ret) => {
                        console.log("获取展开终端列表icon", ret.height);
                        that.collapseContentHeight = ret.height;
                    })
                    .exec();
            }, 200);
        },
        /**
         * 计算地图高度
         * @Author:付常涛
         * @Date: 2023/05/15 20:05:36
         */
        countMapHeight() {
            const that = this;
            if (this.searchInputHeight === 0) this.getSearchInput();
            const query = wx.createSelectorQuery();
            query
                .select(".media-list")
                .boundingClientRect((ret) => {
                    if (!ret) {
                        ret = { height: 0 };
                    }
                    if (that.storeList.length < 4) {
                        that.mapHeight =
                            that.$device.systemInfo.safeArea.height -
                            ret.height * that.storeList.length -
                            70 -
                            that.searchInputHeight -
                            that.filterContentHeight -
                            that.collapseContentHeight;
                        that.storeListTotalHeight =
                            ret.height * that.storeList.length;
                    } else if (that.$device.systemInfo.windowHeight < 680) {
                        that.mapHeight =
                            that.$device.systemInfo.safeArea.height -
                            ret.height * 3 -
                            70 -
                            that.searchInputHeight -
                            that.filterContentHeight -
                            that.collapseContentHeight;
                        that.storeListTotalHeight = ret.height * 3;
                    } else {
                        that.mapHeight =
                            that.$device.systemInfo.safeArea.height -
                            ret.height * 3 -
                            70 -
                            that.searchInputHeight -
                            that.filterContentHeight -
                            that.collapseContentHeight;
                        that.storeListTotalHeight = ret.height * 3;
                    }
                })
                .exec();
        },
        /**
         * 点击mark标记
         * @Author:付常涛
         * @Date: 2023/05/15 20:19:41
         */
        async markerTap(e) {
            try {
                // 获取终端详情
                let opt = this.storeMapList.find(
                    (item) => item.markId === e.markerId
                );
                this.storeOptions = this.storeList.find(el => el.id === opt.id);
                // 检查状态展示数据处理
                if (this.storeOptions.checkFlag) {
                    this.$set(this.storeOptions, "checkStatus", '已检查');
                } else {
                    if (this.storeOptions.mustCheckFlag) this.$set(this.storeOptions, "checkStatus", '必检未检查');
                    else this.$set(this.storeOptions, "checkStatus", '非必检未检查');
                }
                // 转换门头图片
                if (!this.$utils.isEmpty(this.storeOptions.storePicPreKey)) {
                    let urlData = await this.$image.getSignedUrl(
                        this.storeOptions.storePicPreKey
                    );
                    this.$set(this.storeOptions, "storeUrl", urlData);
                } else {
                    this.$set(
                        this.storeOptions,
                        "storeUrl",
                        this.$imageAssets.terminalDefaultImage
                    );
                }
                this.storeDetailsFlag = true;
                this.getStoreDetailsHeight();
                this.selectIndex = this.markers.findIndex(
                    (item) => { return item.id === e.markerId; }
                );
                this.changeMarker(this.selectIndex);
                this.longitude = Number(opt.longitude);
                this.latitude = Number(opt.latitude);
            } catch(err){
                console.log('点击mark标记数据处理失败:' + err);
            }
            
        },
        /**
         * 根据终端详情块高度调整地图高度
         * @Author:付常涛
         * @Date: 2023/05/15 20:34:45
         */
        getStoreDetailsHeight() {
            const that = this;
            // edit by 谭少奇 2023/09/20 新加入终端详情字段地图高度重新计算
            let heights = that.storeOptions.mobilePhone ? 56 : 40
            if (this.storeDetailsFlag) {
                this.storeDetailsTimer = setTimeout(function () {
                    const query = wx.createSelectorQuery();
                    query
                        .select(".store-details")
                        .boundingClientRect((ret) => {
                            that.mapHeight =
                                that.$device.systemInfo.windowHeight -
                                ret.height -
                                heights -
                                that.searchInputHeight -
                                that.$device.systemInfo.statusBarHeight * 2;
                        })
                        .exec();
                }, 200);
            }
        },
        /**
         * 改变选中的mark的样式
         * @Author:付常涛
         * @Date: 2023/05/15 20:39:44
         */
        changeMarker(key) {
            const newMarkers = this.markers.map((item, index) => {
                if (index !== key) {
                    item.width = 28;
                    item.height = 34;
                } else {
                    item.width = 42;
                    item.height = 51;
                }
                return item;
            })
            this.markers = newMarkers;
        },
        /**
         * 视野发生变化(缩放、拖动)
         * @Author:付常涛
         * @Date: 2023/05/15 20:43:43
         */
        regionchange(e) {
            // console.log("视野变化", e);
        },
        /**
         * 触底函数
         * @Author:付常涛
         * @Date: 2023/05/16 09:36:09
         */
        scrollToLower(e) {
            this.getAccntList();
        },
        /**
         * 查看终端详情
         * @Author:付常涛
         * @Date: 2023/05/16 09:37:19
         */
        async goStoreDetails(data, index) {
            this.storeOptions = this.storeList.find(el => el.id === data.id);
            // 检查状态展示数据处理
            if (this.storeOptions.checkFlag) {
                this.$set(this.storeOptions, "checkStatus", '已检查');
            } else {
                if (this.storeOptions.mustCheckFlag) this.$set(this.storeOptions, "checkStatus", '必检未检查');
                else this.$set(this.storeOptions, "checkStatus", '非必检未检查');
            }
            // 转换门头图片
            if (!this.$utils.isEmpty(this.storeOptions.storePicPreKey)) {
                let urlData = await this.$image.getSignedUrl(
                    this.storeOptions.storePicPreKey
                );
                this.$set(this.storeOptions, "storeUrl", urlData);
            } else {
                this.$set(
                    this.storeOptions,
                    "storeUrl",
                    this.$imageAssets.terminalDefaultImage
                );
            }
            this.storeDetailsFlag = true;
            this.getStoreDetailsHeight();
            this.longitude = Number(this.storeOptions.longitude);
            this.latitude = Number(this.storeOptions.latitude);
            this.temMarker = this.$utils.deepcopy(this.markers);
            let opt = this.storeMapList.find(
                (item) => data.id === item.id
            );
            index = this.temMarker.findIndex(
                (item) => { return item.id === opt.markId; }
            );
            this.markers[index].width = 42;
            this.markers[index].height = 51;
            this.markers = [this.markers[index]];
            this.selectIndex = index;
        },
        /**
         * 返回终端地图列表
         * @Author:付常涛
         * @Date: 2023/05/16 09:44:16
         */
        async backListMap() {
            const that = this;
            this.storeDetailsFlag = false;
            // 直接地图点击mark不会备份temMarker
            if (this.temMarker.length > 0) {
                this.markers = this.temMarker;
            }
            this.markers[this.selectIndex].width = 28;
            this.markers[this.selectIndex].height = 34;
            this.countMapHeightTimer = setTimeout(function () {
                that.countMapHeight();
            }, 200);
        },
        /**
         * 路线导航
         * @Author:付常涛
         * @Date: 2023/05/16 09:47:05
         */
        navGpsLine() {
            const that = this;
            wx.openLocation({
                latitude: Number(that.storeOptions.latitude),
                longitude: Number(that.storeOptions.longitude),
                scale: 16,
                name: that.storeOptions.acctName,
                address: `${that.storeOptions.province}${that.storeOptions.city}${that.storeOptions.district}${that.storeOptions.address}`,
            });
        },
        /**
         * 协议检查
         * @Author:zengqiang
         * @Date: 2025/04/26
         */
        async goTerminalCheck(data) {
            this.$nav.push('/pages/terminal2/terminal-check/check-info-list-page.vue', {data});
        },
        /**
         * 协议检查
         * @Author:zengqiang
         * @Date: 2025/04/26
         */
        checkStatusHandle(checkFlag, mustCheckFlag) {
            let checkStatus = ''
            // 检查状态展示数据处理
            if (checkFlag) {
                checkStatus = '已检查'
            } else {
                if (mustCheckFlag) checkStatus = '必检未检查';
                else checkStatus = '非必检未检查';
            }
            return checkStatus;
        }
    },
};
</script>

<style lang="scss">
@import "../../../styles/list-card";
.terminal-check-map-list-page {
    background-color: #ffffff;
    padding-bottom: 0!important;
    .link-search-input {
        padding: 10px;
    }
    .search-container {
        padding-left: 24px;
        color: #8c8c8c;
        font-size: 28px;
        text-align: center;
        view {
            font-size: 24px;
        }
    }
    .filter-wrap {
        .agree-type {
            margin-left: 24px;
        }
    }
    .filter-content {
        display: flex;
        align-items: center;
        padding: 10px 24px;
        justify-content: space-between;
        .link-filter {
            display: inline-block;
        }
        .address-select {
            max-width: 550px;
            display: inline-block;
            position: relative;
            padding-right: 24px;
            .link-input-content {
                overflow: hidden;
                .link-input-inner {
                    overflow: scroll;
                }
            }
            .address-icon {
                position: absolute;
                right: -6px;
                top: 50%;
                transform: translateY(-50%);
            }
        }
    }
    .map-callout {
        position: absolute;
        z-index: 10000;
        left: 10px;
        top: 264px;
        border-radius: 20px;
        background: rgba(0, 0, 0, 0.7);
        line-height: 52px;
        text-align: center;
        color: #fff;
        font-size: 24px;
        padding: 0 20px;
    }
    .map-content {
        width: 100%;
        .location-aim-container {
            @include flex-end-end;
            margin-right: 24px;
            margin-top: 16px;
            .location-aim {
                width: 72px;
                height: 72px;
                border-radius: 50%;
                background: #ffffff;
                box-shadow: 0 5px 9px 0 rgba(11, 33, 85, 0.14);
                text-align: center;
                .aim-image {
                    margin: 11px auto;
                    width: 50px;
                    height: 50px;
                }
            }
        }
    }
    .collapse-content {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 10rpx;
        background-color: #ebeef5;
        .collapse-icon {
            font-size: 36rpx;
            font-weight: bold;
            transition: transform 0.3s;
        }
        .active {
            transform: rotate(180deg);
        }
    }
    .store-list-container {
        background: #ffffff;
        border-radius: 32px 32px 0 0;
        transition: all 0.4s;
        .media-list {
            @include flex;
            border-bottom: 1px solid #f2f2f2;
            .media-list-logo {
                /*box-shadow: 0 4px 31px 0 rgba(0,44,152,0.22);*/
                border-radius: 16px;
                width: 128px;
                height: 128px;
                overflow: hidden;
                margin: 32px 0 32px 24px;
            }
            .store-content {
                width: 75%;
                .store-content-top {
                    @include flex-start-center;
                    @include space-between;
                    margin-left: 24px;
                    margin-top: 56px;
                    .store-title {
                        font-family: PingFangSC-Semibold, serif;
                        font-size: 32px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 32px;
                    }
                    .store-level {
                        margin-right: 24px;
                        width: 120px;
                        height: 44px;
                        image {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }
                .store-content-address {
                    margin-left: 24px;
                    margin-top: 20px;
                    font-family: PingFangSC-Regular, serif;
                    font-size: 24px;
                    color: #8c8c8c;
                    letter-spacing: 0;
                    line-height: 32px;
                }
                .store-content-company {
                    font-family: PingFangSC-Semibold, serif;
                    font-size: 26px;
                    color: #262626;
                    margin-left: 24px;
                    margin-top: 20px;
                }
            }
        }
    }
    .store-details {
        background: #ffffff;
        .icon-back {
            @include flex-start-center;
            width: 100%;
            padding-left: 24px;
            padding-bottom: 24px;
            padding-top: 24px;
            .icon-left {
                color: #ffffff;
                font-size: 36px;
                background: rgba(0, 0, 0, 0.4);
                width: 50px;
                height: 50px;
                line-height: 50px;
                border-radius: 50%;
                text-align: center;
            }
        }
        .store-item {
            background: #ffffff;
            border-radius: 32px 32px 0 0;
            padding-bottom: 40px;
            padding-left: 24px;
            @include media-list();
            /*deep*/
            .store-type-detail {
                white-space: nowrap;
                border: 1px solid #2f69f8;
                border-radius: 8px;
                font-size: 20px;
                padding-left: 18px;
                padding-right: 18px;
                line-height: 40px;
                height: 40px;
                color: #2f69f8;
                margin-right: 10px;
            }
            .store-content-address {
                color: #8c8c8c !important;
                line-height: 32px !important;
            }
            .store-content-company {
                font-family: PingFangSC-Semibold, serif;
                font-size: 26px;
                color: #262626;
                margin-left: 24px;
                margin-top: 20px;
            }
        }
        .button {
            padding-bottom: 68px;
            padding-left: 24px;
            padding-right: 24px;
            @include flex-start-center;
            @include space-between;
            .button-item {
                width: 250px;
                height: 72px;
            }
        }
    }
}
</style>
