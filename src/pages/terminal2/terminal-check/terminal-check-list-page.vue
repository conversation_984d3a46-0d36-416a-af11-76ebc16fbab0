<!--
@created<PERSON><PERSON>
@date  2025/04/23
@description 终端检查列表
-->
<template>
    <link-page class="terminal-check-list-page">
        <lnk-taps :taps="tapOption" v-model="tapActive" @switchTab="onTap" padding></lnk-taps>
        <link-auto-list 
            :option="terminalOptions" 
            :hideCreateButton="true"
            :searchInputBinding="{props:{placeholder:'终端名称/客户编码'}}">
            <view slot="searchRight" class="search-container" @tap="goMapList">
                <link-icon icon="icon-ditu"/>
                <view>地图</view>
            </view>
            <template slot-scope="{data,index}">
                <view class="terminal-check-list">
                    <item :key="index" :data="data" :arrow="false" class="terminal-check-list-rows" @tap="goDetail(data)">
                        <terminal-item :item-data="data" slot="note"></terminal-item>
                    </item>
                </view>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
import TerminalItem from './components/terminal-item.vue';
import LnkTaps from '../../core/lnk-taps/lnk-taps';

definePageConfig({
    navigationBarTitleText: '终端检查列表'
});

export default {
    name: 'terminal-check-list-page',
    components: {
        LnkTaps,
        TerminalItem
    },
    data() {
        // 检查终端列表
        const terminalOptions = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/inspectorsRange/queryCheckPage'
            },
            searchFields: ['acctName', 'acctCode'],
            param: () => {
                // 检查类型筛选
                return {
                    oauth: 'ALL',
                    queryFields: 'id,acctId,acctCode,acctName,addrDetailAddr,cumulativeSalesLevel,salesmanBrandCom,storePicPreKey,salesmanBrandComCode,mobilePhone',
                }
            },
            hooks: {
                beforeLoad(data) {
                    if (this.tapActive.seq === 1) {
                        data.param.checkFlag = false;
                    } else if (this.tapActive.seq === 2) {
                        data.param.checkFlag = true;
                    } else {
                        delete data.param.checkFlag;
                    }
                    delete data.param.sort;
                    delete data.param.order;
                }
            }
        });
        const tapOption = [
            {name: '待检查', text: '待检查', seq: 1, val: 'djc'},
            {name: '已检查', text: '已检查', seq: 2, val: 'yjc'},
            {name: '全部', text: '全部', seq: 3, val: 'all'},
        ]
        return {
            terminalOptions,
            // tab数据
            tapOption,
            // 当前选中tab
            tapActive: tapOption[0]
        }
    },
    methods: {
        handleFilter() {
            this.terminalOptions.methods.reload();
        },
        onBack() {
            this.terminalOptions.methods.reload();
        },
        /**
         * 切换tab页
         * <AUTHOR>
         * @date	2025/04/23
         */
        onTap() {
            this.terminalOptions.methods.reload();
        },
        /**
         * 跳转地图模式
         * <AUTHOR>
         * @date	2025/04/23
         */
        goMapList() {
            this.$nav.push('pages/terminal2/terminal-check/terminal-check-map-distribute-page.vue');
        },
        /**
         * 跳转检查明细页面
         * <AUTHOR>
         * @date	2025/04/23
         */
        goDetail(data) {
            this.$nav.push('pages/terminal2/terminal-check/check-info-list-page.vue', {data})
        }
    }
}
</script>

<style lang="scss">
.terminal-check-list-page {
    .link-sticky-top {
        top: 93px !important;
    }
    .lnk-tabs-container {
        .lnk-tabs {
            .lnk-tabs-item {
                flex: 1;
            }
        }
    }  
    .link-auto-list-wrapper {
        margin: 124px 32px 0 32px;
    }

    .link-search-input {
        padding: 16px;
    }

    .search-container {
        padding-left: 12px;
        color: #8C8C8C;
        font-size: 28px;
        text-align: center;
    }
    .terminal-check-list-rows {
        background: #FFFFFF;
        border-radius: 24px;
        padding: 36px 24px;
        margin-bottom: 24px;
    }
}
</style>
