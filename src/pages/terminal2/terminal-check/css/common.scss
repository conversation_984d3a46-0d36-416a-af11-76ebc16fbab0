.check-terminal-common-style {
    .term {
        background-color: #fff;
        padding: 0 40px;
    }
    .small-title {
        font-size: 28px;
        line-height: 28px;
        padding: 20px 0;
        color: #262626;
        letter-spacing: 1px;
    }
    .check-item {
        display: flex;
        background: #fff;
        min-height: 24px;

        .lnk-img-watermark {
            margin: 0;
        }

        .link-radio-group {
            display: flex;
        }

        .check-text {
            margin: 0 24px 0 12px;
        }

        .seq {
            padding: 58px 0 0 24px;
            font-size: 28px;
        }

        .check-detail {
            flex: 1;
            .custom-form-item-layout {
                .link-item-body-left {
                    flex: none!important;
                    padding-right: 46px;
                }
            }
            .link-item {
                padding: 28px 0 28px 10px;
                &:after {
                    background-color: #fff;
                }
            }

            .addr-wrap {
                display: flex;
                align-items: center;
                padding: 0 24px 24px 0;
                font-size: 28px;

                .iconfont {
                    font-size: 70px;
                    color: #2F69F8;
                    margin-right: 24px;
                }

                .placeholder {
                    color: #ccc;
                }
            }
        }
    }
    .check-item-main {
        align-items: center;
        .num {
            font-size: 28px;
            padding-left: 24px;
            padding-top: 4px;
        }
    }
    .preview-details {
        padding-left: 24px;
        font-size: 28px;
        color: #333;
        .preview-content {
            display: flex;
            height: 60px;
            align-items: center;
        }
    }
}