/**
 * 高频点字段配置
 * @description 定义不同采集点类型对应的字段映射关系
 */
import basicFieldsConfig from "./basic-fields.json";

/**
 * 获取指定采集点类型的字段配置
 * @param {string} type 采集点类型
 * @returns {Array} 字段配置数组
 */
export function getFieldsByType(type) {
    const specificFields = basicFieldsConfig.typeSpecificFields[type] || [];
    return [...basicFieldsConfig.commonFields, ...specificFields];
}

export default {
    getFieldsByType,
    basicFieldsConfig,
};
