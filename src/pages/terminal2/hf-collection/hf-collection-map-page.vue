<template>
    <link-page class="hf-collection-map-page">
        <!--搜索-->
        <link-search-input
            class="search-input"
            @change="searchPointName"
            placeholder="高频点名称/省市区（县）/详细地址"
        >
            <view class="search-container" @tap="backList">
                <link-icon class="icon" icon="icon-unorderedlist" />
                <view>列表</view>
            </view>
        </link-search-input>

        <!--地图-->
        <map
            id="map"
            :longitude="longitude"
            :latitude="latitude"
            :scale="scale"
            show-location="true"
            show-compass="true"
            show-scale="true"
            class="map-content"
            :style="{ height: mapHeight + 'px' }"
            :setting="setting"
            :markers="markers"
            @markertap="markerTap"
        >
            <cover-view :style="{ 'margin-top': mapHeight - 60 + 'px' }">
                <cover-view
                    class="location-aim-container"
                    @tap="backCurrentPosition"
                >
                    <cover-view class="location-aim">
                        <cover-image
                            class="aim-image"
                            :src="$imageAssets.locationAimImage"
                        ></cover-image>
                    </cover-view>
                </cover-view>
            </cover-view>
        </map>

        <!--高频点列表-->
        <scroll-view
            class="point-list-container"
            scroll-y="true"
            id="list-point"
            v-show="!pointDetailsFlag"
            @scrolltolower="scrollToLower"
            :style="{ height: pointListTotalHeight + 'px' }"
        >
            <view v-for="(data, index) in pointList" :key="index">
                <view
                    class="media-list"
                    :data="data"
                    @tap="goPointDetails(data, index)"
                >
                    <image
                        class="media-list-logo"
                        :src="$imageAssets.terminalDefaultImage"
                        lazy-load="true"
                    ></image>
                    <view class="point-content">
                        <view class="point-content-top">
                            <view class="point-title">{{
                                data.pointName
                            }}</view>
                        </view>
                        <view class="point-content-middle">
                            <view class="point-type" v-if="data.pointType">{{
                                data.pointType
                                    | lov("HIGH_FREQUENCY_POINT_TYPE")
                            }}</view>
                            <link-icon
                                icon="icon-dizhi1"
                                class="location-icon"
                                :style="{
                                    color: getCollectionPointColor(
                                        data.pointType
                                    ),
                                }"
                            />
                        </view>
                        <view
                            class="point-content-representative"
                            v-if="data.visitor"
                        >
                            <text class="label">走访人：</text
                            >{{ data.visitor }}
                        </view>
                        <view
                            class="point-content-representative"
                            v-if="data.accntCode"
                        >
                            <text class="label">终端编码：</text
                            >{{ data.accntCode }}
                        </view>
                        <view class="point-content-address">
                            <view class="point-address"
                                ><text>{{ data.distance }}米 | </text
                                >{{ formatAddress(data) }}</view
                            >
                        </view>
                    </view>
                </view>
            </view>
        </scroll-view>

        <!--高频点详情-->
        <view class="point-details" v-show="pointDetailsFlag">
            <view class="icon-back" @tap="backListMap">
                <view class="iconfont icon-left"></view>
            </view>
            <view class="point-item">
                <image
                    class="media-list-logo"
                    :src="$imageAssets.terminalDefaultImage"
                ></image>
                <view class="point-content">
                    <view class="point-content-top">
                        <view class="point-title">{{
                            pointOptions.pointName
                        }}</view>
                    </view>
                    <view class="point-content-middle">
                        <view
                            class="point-type"
                            v-if="pointOptions.pointType"
                            >{{
                                pointOptions.pointType
                                    | lov("HIGH_FREQUENCY_POINT_TYPE")
                            }}</view
                        >
                        <link-icon
                            icon="icon-dizhi1"
                            class="location-icon"
                            :style="{
                                color: getCollectionPointColor(
                                    pointOptions.pointType
                                ),
                            }"
                        />
                    </view>
                    <view
                        class="point-content-representative"
                        v-if="pointOptions.visitor"
                    >
                        <text class="label">走访人：</text
                        >{{ pointOptions.visitor }}
                    </view>
                    <view
                        class="point-content-representative"
                        v-if="pointOptions.accntCode"
                    >
                        <text class="label">终端编码：</text
                        >{{ pointOptions.accntCode }}
                    </view>
                    <view class="point-content-address">
                        <view class="point-address"
                            ><text>{{ pointOptions.distance }}米 | </text
                            >{{ formatAddress(pointOptions) }}</view
                        >
                    </view>
                </view>
            </view>
            <view class="button">
                <link-button
                    class="button-item"
                    label="路线"
                    icon="icon-luxian"
                    mode="stroke"
                    size="normal"
                    @tap="navGpsLine"
                />
                <link-button
                    class="button-item"
                    label="查看详情"
                    mode="fill"
                    size="normal"
                    @tap="goPointDetailPage(pointOptions)"
                />
            </view>
        </view>
    </link-page>
</template>

<script>
import { getCollectionPointColor } from "./data/collection-point-config";

export default {
    name: "hf-collection-map-page",
    data() {
        return {
            pageNum: 1,
            pageLimit: 20,
            selectIndex: null,
            scale: 17,
            longitude: 0,
            latitude: 0,
            mapHeight: 0,
            nextPageFlag: true,
            searchInputHeight: 0,
            pointListTotalHeight: 0,
            addressData: {},
            pointDetailsFlag: false,
            setting: {
                enable3D: false,
                enableTraffic: false,
            },
            loading: false,
            pointOptions: {},
            markers: [],
            temMarker: [],
            searchVal: "",
            pointList: [], // 高频点列表
            countMapHeightTimer: null, // 计算列表容器高度计时器
            pointDetailsTimer: null, // 计算详情容器高度计时器
        };
    },
    async created() {
        this.getSearchInput();
        await this.getAddress();
        await this.getPointList();
    },
    mounted() {
        // 监听刷新事件
        this.$bus.$on("hfCollectionListRefresh", () => {
            this.refreshMapData();
        });
    },
    onUnload() {
        // 清理事件
        this.$bus.$off("hfCollectionListRefresh");
    },
    destroyed() {
        clearTimeout(this.countMapHeightTimer);
        clearTimeout(this.pointDetailsTimer);
    },
    methods: {
        /**
         * 刷新地图数据
         * @description 用于编辑页面提交成功后刷新地图数据
         */
        async refreshMapData() {
            if (
                this.pointDetailsFlag &&
                this.pointOptions &&
                this.pointOptions.id
            ) {
                await this.refreshSelectedPointData(this.pointOptions.id);
            }
        },

        /**
         * 刷新选中高频点的数据
         * @param {string} pointId 高频点ID
         */
        async refreshSelectedPointData(pointId) {
            const response = await this.$http.post(
                "action/link/highFrequencyPoint/queryByExamplePage",
                {
                    filtersRaw: [
                        {
                            id: "id",
                            property: "id",
                            value: pointId,
                            operator: "=",
                        },
                    ],
                    rows: 1,
                    page: 1,
                    longitude: this.longitude,
                    latitude: this.latitude,
                    oauth: "MY_POSTN",
                }
            );

            if (response.success && response.rows && response.rows.length > 0) {
                const updatedPoint = response.rows[0];

                const pointIndex = this.pointList.findIndex(
                    (point) => point.id === pointId
                );
                if (pointIndex !== -1) {
                    this.$set(this.pointList, pointIndex, updatedPoint);
                }

                this.pointOptions = updatedPoint;

                this.updateSelectedMarker(updatedPoint);
            }
        },

        /**
         * 更新选中标记的显示
         */
        updateSelectedMarker(pointData) {
            // 更新备份的标记数据
            if (
                this.temMarker &&
                this.selectIndex !== null &&
                this.temMarker[this.selectIndex]
            ) {
                this.temMarker[this.selectIndex] = {
                    ...this.temMarker[this.selectIndex],
                    callout: {
                        ...this.temMarker[this.selectIndex].callout,
                        bgColor: this.getCollectionPointColor(
                            pointData.pointType
                        ),
                    },
                    label: {
                        ...this.temMarker[this.selectIndex].label,
                        content: pointData.pointName,
                        anchorX:
                            -(pointData.pointName
                                ? pointData.pointName.length * 12
                                : 0) / 2,
                    },
                };
            }

            // 更新当前显示的选中标记
            const selectedMarker = {
                id: Number(pointData.id),
                latitude: Number(pointData.latitude),
                longitude: Number(pointData.longitude),
                width: 0,
                height: 0,
                callout: {
                    content: "",
                    display: "ALWAYS",
                    bgColor: this.getCollectionPointColor(pointData.pointType),
                    color: "#ffffff",
                    fontSize: 16,
                    borderRadius: 80,
                    padding: 18,
                    textAlign: "center",
                },
                label: {
                    content: pointData.pointName,
                    color: "#262626",
                    fontSize: 16,
                    padding: 0,
                    display: "ALWAYS",
                    textAlign: "center",
                    anchorX:
                        -(pointData.pointName
                            ? pointData.pointName.length * 12
                            : 0) / 2,
                },
            };

            this.markers = [selectedMarker];

            this.longitude = Number(pointData.longitude);
            this.latitude = Number(pointData.latitude);
        },

        /**
         * 格式化地址显示
         */
        formatAddress(data) {
            if (!data) return "";

            const addressParts = [
                data.province,
                data.city,
                data.district,
                data.street,
                data.detailAddress,
            ].filter(Boolean);

            return addressParts.join("");
        },

        /**
         * 按照高频点名称搜索
         */
        searchPointName(val) {
            this.pointDetailsFlag = false;
            this.pageNum = 1;
            this.nextPageFlag = true;
            this.searchVal = val;
            this.getPointList();
        },

        /**
         * 获取高频点列表和对应的地图点位
         */
        async getPointList() {
            if (!this.nextPageFlag || this.loading) return;
            this.loading = true;

            let filtersRaw = [];

            // 添加地理位置过滤
            if (this.addressData.addressComponent) {
                filtersRaw.push(
                    {
                        id: "province",
                        property: "province",
                        value: this.addressData.addressComponent.province,
                    },
                    {
                        id: "city",
                        property: "city",
                        value: this.addressData.addressComponent.city,
                    },
                    {
                        id: "district",
                        property: "district",
                        value: this.addressData.addressComponent.district,
                    }
                );
            }

            // 添加搜索条件
            if (this.searchVal) {
                filtersRaw.push({
                    id: "searchValue",
                    property: "[pointName,accntCode,province,city,district,detailAddress]",
                    value: this.searchVal,
                    operator: "or like",
                });
            }

            let data = await this.$http.post(
                "action/link/highFrequencyPoint/queryByExamplePage",
                {
                    filtersRaw,
                    rows: this.pageLimit,
                    page: this.pageNum,
                    longitude: this.longitude,
                    latitude: this.latitude,
                    oauth: "MY_ORG",
                }
            );

            if (!data.success) {
                return;
            }

            if (data.total > this.pageLimit * this.pageNum) {
                this.pageNum++;
            } else {
                this.nextPageFlag = false;
            }
            this.loading = false;

            if (this.pageNum === 1) {
                this.pointList = [];
                this.markers = [];
            }

            this.pointList = this.pointList.concat(data.rows);

            let markers = data.rows.map((item) => ({
                id: Number(item.id),
                latitude: Number(item.latitude),
                longitude: Number(item.longitude),
                width: 0,
                height: 0,
                callout: {
                    content: "",
                    display: "ALWAYS",
                    bgColor: this.getCollectionPointColor(item.pointType),
                    color: "#ffffff",
                    fontSize: 16,
                    borderRadius: 80,
                    padding: 12,
                    textAlign: "center",
                },
                label: {
                    content: item.pointName,
                    color: "#262626",
                    fontSize: 12,
                    padding: 0,
                    display: "ALWAYS",
                    textAlign: "center",
                    anchorX:
                        -(item.pointName ? item.pointName.length * 12 : 0) / 2,
                },
            }));

            this.markers = this.markers.concat(markers);

            this.countMapHeightTimer = setTimeout(() => {
                this.countMapHeight();
            }, 200);
        },

        /**
         * 计算高度
         */
        countMapHeight() {
            if (this.searchInputHeight === 0) this.getSearchInput();
            const query = wx.createSelectorQuery();
            query
                .select(".media-list")
                .boundingClientRect((ret) => {
                    if (this.pointList.length < 4) {
                        this.mapHeight =
                            this.$device.systemInfo.safeArea.height -
                            ret.height * this.pointList.length -
                            70 -
                            this.searchInputHeight;
                        this.pointListTotalHeight =
                            ret.height * this.pointList.length;
                    } else {
                        this.mapHeight =
                            this.$device.systemInfo.safeArea.height -
                            ret.height * 3 -
                            70 -
                            this.searchInputHeight;
                        this.pointListTotalHeight = ret.height * 3;
                    }
                })
                .exec();
        },

        /**
         * 详情返回列表
         */
        async backListMap() {
            this.pointDetailsFlag = false;
            this.markers = this.temMarker;

            // 恢复原始标记样式
            if (this.selectIndex !== null && this.markers[this.selectIndex]) {
                const marker = this.markers[this.selectIndex];
                marker.callout.padding = 12;
                marker.label.fontSize = 12;
                marker.label.padding = 4;
                marker.label.anchorX = -(marker.label.content.length * 12) / 2;
                marker.label.color = "#262626";
            }

            this.countMapHeightTimer = setTimeout(() => {
                this.countMapHeight();
            }, 200);
            await this.getAddress();
        },

        /**
         * 获取高频点详情块高度
         */
        getPointDetailsHeight() {
            if (this.pointDetailsFlag) {
                this.pointDetailsTimer = setTimeout(() => {
                    const query = wx.createSelectorQuery();
                    query
                        .select(".point-details")
                        .boundingClientRect((ret) => {
                            this.mapHeight =
                                this.$device.systemInfo.windowHeight -
                                ret.height -
                                this.searchInputHeight -
                                this.$device.systemInfo.statusBarHeight * 2;
                        })
                        .exec();
                }, 200);
            }
        },

        /**
         * 获取搜索框高度
         */
        getSearchInput() {
            setTimeout(() => {
                const query = wx.createSelectorQuery();
                query
                    .select(".search-input")
                    .boundingClientRect((ret) => {
                        this.searchInputHeight = ret.height;
                    })
                    .exec();
            }, 200);
        },

        /**
         * 高频点详情
         */
        goPointDetails(data, index) {
            this.pointDetailsFlag = true;
            this.pointOptions = data;
            this.getPointDetailsHeight();
            this.longitude = Number(data.longitude);
            this.latitude = Number(data.latitude);
            this.temMarker = this.$utils.deepcopy(this.markers);

            // 创建选中状态的单个标记
            const selectedMarker = {
                ...this.markers[index],
                callout: {
                    ...this.markers[index].callout,
                    padding: 18,
                    bgColor: this.getCollectionPointColor(data.pointType),
                },
                label: {
                    ...this.markers[index].label,
                    fontSize: 16,
                    padding: 0,
                    anchorX:
                        -(data.pointName ? data.pointName.length * 12 : 0) / 2,
                },
            };

            this.markers = [selectedMarker];
            this.selectIndex = index;
        },

        /**
         * 导航
         */
        navGpsLine() {
            wx.openLocation({
                latitude: Number(this.pointOptions.latitude),
                longitude: Number(this.pointOptions.longitude),
                scale: 16,
                name: this.pointOptions.pointName,
                address: `${this.pointOptions.province}${this.pointOptions.city}${this.pointOptions.district}${this.pointOptions.street}`,
            });
        },

        /**
         * 查看详情页面
         */
        goPointDetailPage(data) {
            this.$nav.push(
                "/pages/terminal2/hf-collection/hf-collection-detail-page",
                {
                    data,
                }
            );
        },

        /**
         * 回到当前定位位置
         */
        async backCurrentPosition() {
            await this.getAddress();
            this.scale = 17;
        },

        /**
         * 触底函数
         */
        scrollToLower(e) {
            this.getPointList();
        },

        /**
         * 返回列表
         */
        backList() {
            this.$nav.back();
        },

        /**
         * 获取当前位置的地址信息
         */
        async getAddress() {
            let coordinate = await this.$locations.getCurrentCoordinate();
            if (
                !this.$utils.isEmpty(coordinate.latitude) &&
                !this.$utils.isEmpty(coordinate.longitude)
            ) {
                this.longitude = Number(coordinate.longitude);
                this.latitude = Number(coordinate.latitude);
                let address = await this.$locations.reverseTMapGeocoder(
                    this.latitude,
                    this.longitude,
                    "高频点地图页面"
                );
                this.addressData = address.originalData.result;
            }
        },

        async changeMarker(key) {
            this.markers.forEach((item, index) => {
                const pointData = this.pointList.find(
                    (point) => Number(point.id) === item.id
                );
                const isSelected = index === key;

                // 更新 callout 样式 - 作为彩色圆点
                this.$set(item.callout, "padding", isSelected ? 16 : 12);
                this.$set(
                    item.callout,
                    "bgColor",
                    this.getCollectionPointColor(pointData?.pointType)
                );

                // 更新 label 样式 - 作为名称标签
                this.$set(item.label, "fontSize", isSelected ? 14 : 12);
                this.$set(item.label, "padding", isSelected ? 6 : 4);
                this.$set(item.label, "anchorY", isSelected ? -50 : -40);

                if (isSelected) {
                    this.$set(item.label, "color", "#ffffff");
                } else {
                    this.$set(item.label, "color", "#262626");
                }
            });
        },

        async markerTap(e) {
            let opt = this.pointList.filter(
                (item) => Number(item.id) === e.markerId
            );
            this.pointOptions = opt[0];
            this.pointDetailsFlag = true;
            this.getPointDetailsHeight();
            this.selectIndex = this.markers.findIndex(
                (item) => Number(item.id) === e.markerId
            );
            await this.changeMarker(this.selectIndex);
            this.longitude = Number(opt[0].longitude);
            this.latitude = Number(opt[0].latitude);
        },

        /**
         * 获取采集点类型对应的颜色
         */
        getCollectionPointColor,
    },
};
</script>

<style lang="scss">
@import "../../../styles/list-card.scss";
.hf-collection-map-page {
    background-color: #ffffff;
    .search-container {
        padding-left: 24px;
        color: #8c8c8c;
        font-size: 28px;
        text-align: center;
    }
    .map-content {
        width: 100%;
        .location-aim-container {
            @include flex-end-end;
            margin-right: 24px;
            margin-top: 16px;
            .location-aim {
                width: 72px;
                height: 72px;
                border-radius: 50%;
                background: #ffffff;
                box-shadow: 0 5px 9px 0 rgba(11, 33, 85, 0.14);
                text-align: center;
                .aim-image {
                    margin: 11px auto;
                    width: 50px;
                    height: 50px;
                }
            }
        }
    }
    .point-list-container {
        background: #ffffff;
        border-radius: 32px 32px 0 0;
        .media-list {
            @include flex;
            border-bottom: 1px solid #f2f2f2;
            .media-list-logo {
                border-radius: 16px;
                width: 128px;
                height: 128px;
                overflow: hidden;
                margin: 32px 0 32px 24px;
            }
            .point-content {
                width: 80%;
                padding-bottom: 15px;
                .point-content-top {
                    @include flex-start-center;
                    @include space-between;
                    margin-left: 24px;
                    margin-top: 32px;
                    .point-title {
                        font-family: PingFangSC-Semibold, serif;
                        font-size: 32px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 32px;
                    }
                }
                .point-content-middle {
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    padding-left: 24px;
                    margin-top: 10px;

                    .point-type {
                        white-space: nowrap;
                        border: 2px solid #2f69f8;
                        border-radius: 8px;
                        font-size: 20px;
                        padding: 8px 18px;
                        line-height: 24px;
                        color: #2f69f8;
                        margin-right: 20px;
                    }

                    .location-icon {
                        font-size: 32px;
                    }
                }
                .point-content-representative {
                    margin-left: 24px;
                    margin-top: 16px;
                    font-family: PingFangSC-Regular, serif;
                    font-size: 24px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 32px;
                    .label {
                        color: #8c8c8c;
                    }
                }
                .point-content-address {
                    margin: 20px 24px 0;
                    font-family: PingFangSC-Regular, serif;
                    font-size: 24px;
                    color: #8c8c8c;
                    letter-spacing: 0;
                    line-height: 32px;
                }
            }
        }
    }
    .point-details {
        background: #ffffff;
        .icon-back {
            @include flex-start-center;
            width: 100%;
            padding-left: 24px;
            padding-bottom: 24px;
            padding-top: 24px;
            .icon-left {
                color: #ffffff;
                font-size: 36px;
                background: rgba(0, 0, 0, 0.4);
                width: 50px;
                height: 50px;
                line-height: 50px;
                border-radius: 50%;
                text-align: center;
            }
        }
        .point-item {
            background: #ffffff;
            border-radius: 32px 32px 0 0;
            padding-bottom: 40px;
            padding-left: 24px;
            @include media-list();
            .media-list-logo {
                margin: 0 32px;
                max-width: 128px;
                min-width: 128px;
            }
            .point-content {
                display: flex;
                flex-direction: column;
                gap: 10px;
            }

            .point-content-address {
                margin-right: 32px;
            }

            .point-content-address {
                color: #8c8c8c !important;
                line-height: 32px !important;
            }
            .point-content-middle {
                display: flex;
                align-items: center;
                .point-type {
                    white-space: nowrap;
                    border: 2px solid #2f69f8;
                    border-radius: 8px;
                    font-size: 20px;
                    padding: 8px 18px;
                    line-height: 24px;
                    color: #2f69f8;
                    margin-right: 20px;
                }
            }
            .point-content-representative,
            .point-content-address {
                font-size: 24px;
            }
        }
        .button {
            padding-bottom: 68px;
            padding-left: 24px;
            padding-right: 24px;
            @include flex-start-center;
            @include space-between;
            .button-item {
                width: 218px;
                height: 72px;
            }
        }
    }
}
</style>
