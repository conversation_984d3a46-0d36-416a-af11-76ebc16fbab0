<template>
    <link-page class="hf-collection-list-page">
        <link-auto-list
            :option="highFrequencyListOption"
            :hideCreateButton="true"
            :searchInputBinding="{
                props: { placeholder: '高频点名称/省市区（县）/详细地址' },
            }"
        >
            <view slot="searchRight" class="search-container" @tap="goMapList">
                <link-icon icon="icon-ditu" />
                <view>地图</view>
            </view>
            <link-filter-group slot="filterGroup">
                <link-filter-item
                    label="创建时间"
                    :param="{ sort: { field: 'created', desc: true } }"
                />
                <link-filter-item
                    label="最新更新"
                    :param="{ sort: { field: 'lastUpdated', desc: true } }"
                />
            </link-filter-group>

            <template slot="other">
                <link-fab-button @tap="goCreateHighFrequency" />
            </template>
            <template slot-scope="{ data, index }">
                <item
                    :key="index"
                    :data="data"
                    :arrow="false"
                    class="hf-list-item"
                    @tap="goToItem(data)"
                >
                    <view class="hf-list" slot="note">
                        <view class="list-cell">
                            <view class="media-list">
                                <image
                                    class="media-list-logo"
                                    :src="$imageAssets.terminalDefaultImage"
                                    lazy-load="true"
                                ></image>
                                <view class="hf-content">
                                    <view class="hf-content-top">
                                        <view class="hf-title">{{
                                            data.pointName
                                        }}</view>
                                    </view>

                                    <view class="hf-content-middle">
                                        <view
                                            class="hf-type"
                                            v-if="data.pointType"
                                        >
                                            {{
                                                data.pointType
                                                    | lov(
                                                        "HIGH_FREQUENCY_POINT_TYPE"
                                                    ) || "--"
                                            }}
                                        </view>
                                        <link-icon
                                            icon="icon-dizhi1"
                                            class="location-icon"
                                            :style="{
                                                color: getCollectionPointColor(
                                                    data.pointType
                                                ),
                                            }"
                                        />
                                    </view>

                                    <view class="hf-content-representative">
                                        <view class="terminal-type"
                                            >走访人</view
                                        >
                                        <view class="terminal-name">
                                            {{ data.visitor }}
                                        </view>
                                    </view>

                                    <view
                                        class="hf-content-representative"
                                        v-if="data.accntCode"
                                    >
                                        <view class="terminal-type"
                                            >终端编码</view
                                        >
                                        <view class="terminal-name">
                                            {{ data.accntCode }}
                                        </view>
                                    </view>

                                    <view class="hf-content-address">
                                        <view class="hf-address">
                                            {{ formatAddress(data) }}
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
import { getCollectionPointColor } from "./data/collection-point-config";

export default {
    name: "hf-collection-list-page",
    data() {
        const highFrequencyListOption = new this.AutoList(this, {
            url: {
                queryByExamplePage:
                    "action/link/highFrequencyPoint/queryByExamplePage",
            },
            loadOnStart: true,
            sortField: "created",
            sortOptions: null,
            searchFields: [
                "pointName",
                "accntCode",
                "province",
                "city",
                "district",
                "detailAddress",
            ],
            filterOption: [
                {
                    label: "高频点类型",
                    field: "pointType",
                    type: "lov",
                    lov: "HIGH_FREQUENCY_POINT_TYPE",
                },
            ],
            param: {
                oauth: "MY_POSTN",
            },
        });

        return {
            hideCreateButton: true,
            searchRightMap: true,
            highFrequencyListOption,
            userInfo: {},
            features: [],
        };
    },
    mounted() {
        // 监听刷新事件
        this.$bus.$on("hfCollectionListRefresh", () => {
            this.highFrequencyListOption.methods.reload();
        });
    },
    onUnload() {
        // 清理事件
        this.$bus.$off("hfCollectionListRefresh");
    },

    methods: {
        /**
         * 跳转创建高频点界面
         */
        goCreateHighFrequency() {
            this.$nav.push(
                "/pages/terminal2/hf-collection/hf-collection-edit-page",
                {
                    mode: "create",
                }
            );
        },

        /**
         * 查看详情
         */
        async goToItem(data) {
            this.$nav.push(
                "/pages/terminal2/hf-collection/hf-collection-detail-page",
                {
                    data,
                }
            );
        },
        /**
         * 切换地图模式
         */
        goMapList() {
            this.$nav.push(
                "/pages/terminal2/hf-collection/hf-collection-map-page",
                {
                    source: "highFrequencyList",
                }
            );
        },
        /**
         * 格式化地址显示
         */
        formatAddress(data) {
            if (!data) return "";

            const addressParts = [
                data.province,
                data.city,
                data.district,
                data.street,
                data.detailAddress,
            ].filter(Boolean);

            return addressParts.join("");
        },

        /**
         * 获取采集点类型对应的颜色
         */
        getCollectionPointColor,
    },
};
</script>

<style lang="scss">
.hf-collection-list-page {
    .search-container {
        padding-left: 12px;
        color: #8c8c8c;
        font-size: 28px;
        text-align: center;
    }

    .hf-list-item {
        background: #ffffff;
        width: 702px;
        margin: 0 auto 24px auto;
        border-radius: 16px;
    }
    /*deep*/
    .link-item {
        padding: 0;
    }
    /*deep*/
    .link-item-icon {
        width: 0;
        padding-left: 0;
    }
    /*deep*/
    .link-dropdown-content {
        padding: 24px;
    }
    .hf-list {
        .list-cell {
            .media-list {
                @include flex;
                padding: 24px 16px 24px 24px;
                .media-list-logo {
                    border-radius: 16px;
                    width: 128px;
                    height: 128px;
                    overflow: hidden;
                }
                .hf-content {
                    width: 70%;
                    .hf-content-top {
                        @include flex-start-center;
                        @include space-between;
                        margin-left: 24px;
                        .hf-title {
                            font-family: PingFangSC-Semibold, serif;
                            font-size: 32px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 36px;
                            width: 77%;
                            height: 36px;
                            overflow: hidden;
                        }
                        .hf-level {
                            margin-right: -3px;
                            width: 120px;
                            height: 44px;
                            .hf-badge {
                                background: linear-gradient(
                                    135deg,
                                    #ff6b6b,
                                    #ff8e53
                                );
                                color: white;
                                padding: 4px 12px;
                                border-radius: 12px;
                                font-size: 20px;
                                font-weight: bold;
                                text-align: center;
                            }
                        }
                    }

                    .hf-content-middle {
                        display: flex;
                        justify-content: flex-start;
                        align-items: center;
                        padding-left: 24px;
                        margin-top: 10px;

                        .hf-type {
                            white-space: nowrap;
                            border: 2px solid #2f69f8;
                            border-radius: 8px;
                            font-size: 20px;
                            padding: 8px 18px;
                            line-height: 24px;
                            color: #2f69f8;
                            margin-right: 12px;
                        }

                        .location-icon {
                            font-size: 32px;
                        }
                    }
                    .hf-content-representative {
                        @include flex;
                        margin-left: 24px;
                        margin-top: 20px;
                        width: calc(100% - 24px);
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        .terminal-type {
                            color: #8c8c8c;
                        }
                        .terminal-name {
                            font-family: PingFangSC-Regular, serif;
                            font-size: 24px;
                            color: #000000;
                            letter-spacing: 0;
                            padding-left: 8px;
                            width: calc(100% - 50px);
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                        }
                    }
                    .hf-content-address {
                        margin-left: 24px;
                        margin-top: 18px;
                        font-family: PingFangSC-Regular, serif;
                        font-size: 24px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 32px;
                    }
                }
                .edit-button {
                    width: 80px;
                    height: 80px;
                    background: #2f69f8;
                    border-radius: 40px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 16px;
                    .edit-icon {
                        font-size: 32px;
                        color: #ffffff;
                    }
                }
            }
        }
    }
}
</style>
