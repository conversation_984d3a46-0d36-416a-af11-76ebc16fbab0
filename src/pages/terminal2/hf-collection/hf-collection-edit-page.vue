<!--
 * @Description: 高频点编辑/新增页面
-->

<template>
    <link-page class="hf-edit-page">
        <!-- Tab导航栏 -->
        <lnk-taps
            :taps="tapsOptions"
            v-model="tapsActive"
            :onlyTabChange="true"
            @switchTab="switchTab"
        ></lnk-taps>

        <view
            class="content-container"
        >
            <!-- 基础信息Tab -->
            <view class="content-item" v-show="tapsActive.seq === '1'">
                <hf-edit-basic-info
                    :formData="formData"
                    ref="basicInfo"
                    :formOption="formOption"
                    @resetFormData="resetBasicInfo"
                />
            </view>

            <!-- 联系人Tab -->
            <view class="content-item" v-show="tapsActive.seq === '2'">
                <hf-edit-contacts
                    :contactsArr="contactsArr"
                    :headId="headId"
                    :formOption="formOption"
                    ref="contactsInfo"
                />
            </view>
        </view>

        <!-- 提交按钮 -->
        <link-sticky class="bottom-sticky">
            <view class="sticky">
                <link-button block size="large" @tap="submit" autoLoading>
                    提交
                </link-button>
            </view>
        </link-sticky>

        <view class="blank"></view>
    </link-page>
</template>

<script>
import lnkTaps from "../../core/lnk-taps/lnk-taps";
import hfEditContacts from "./components/hf-edit-contacts";
import hfEditBasicInfo from "./components/hf-edit-basic-info";
import basicFieldsConfig from "./data/basic-fields.json";
import contactsFieldsConfig from "./data/contacts-fields.json";

export default {
    name: "hf-edit-page",
    components: {
        lnkTaps,
        hfEditContacts,
        hfEditBasicInfo,
    },
    data() {
        const tapsOptions = [
            { name: "基础信息", seq: "1", val: "Basic" },
            { name: "联系人", seq: "2", val: "Contact" },
        ];

        return {
            isEditMode: false,
            itemId: null,

            // 表单数据
            formData: {},
            formOption: {},

            // Tab配置
            tapsOptions,
            tapsActive: tapsOptions[0],

            // 联系人数据
            contactsArr: [],

            // 状态控制
            isSubmitting: false,

            // ID字段
            headId: null,
        };
    },
    mounted() {
        this.initPage();
    },
    async onShow() {
        // 处理返回的位置选择结果
        const location = this.$locations.QQGetLocation();

        if (this.$utils.isNotEmpty(location)) {
            this.$locations.QQClearLocation();

            // 直接调用basic-info组件的方法设置位置数据
            if (this.$refs.basicInfo) {
                this.$refs.basicInfo.setLocationData(location);
            }
        }
    },
    methods: {
        /**
         * 初始化页面数据
         * @description 根据路由参数设置页面状态
         */
        async initPage() {
            const pageParam = this.pageParam;

            if (
                pageParam &&
                pageParam.editFlag === "edit" &&
                pageParam.data
            ) {
                this.isEditMode = true;
                this.headId = pageParam.data.id;
                await this.loadEditData(pageParam.data);
                this.$taro.setNavigationBarTitle({title: '高频点编辑'});
            } else {
                this.isEditMode = false;
                await this.initNewData();
                this.$taro.setNavigationBarTitle({title: '新增高频点'});
            }
        },

        /**
         * 初始化Tab状态
         */
        initTabs() {
            this.tapsActive = this.tapsOptions[0];
        },

        /**
         * 初始化新项目数据
         */
        async initNewData() {
            this.headId = await this.$newId();

            const userInfo = this.$taro.getStorageSync("token").result;
            this.formData = {
                pointType: "",
                visitor: userInfo.firstName || "",
                created: this.$utils.dateFormat(
                    new Date(),
                    "YYYY-MM-DD HH:mm:ss"
                ),
                id: this.headId,
            };

            // 初始化联系人
            const contactId = await this.$newId();
            const defaultContact = {
                id: contactId,
                headId: this.headId,
                unfoldFlag: true,
                row_status: "NEW",
            };

            // 根据字段配置初始化默认值
            contactsFieldsConfig.forEach((field) => {
                defaultContact[field.key] = field.defaultValue || "";
            });

            // 设置为主要联系人
            defaultContact.mainFlag = "Y";

            this.contactsArr = [defaultContact];
        },

        /**
         * 加载编辑数据
         */
        async loadEditData(pageData) {
            try {
                this.$utils.showLoading();

                const { province, city, district, street } = pageData;
                const address = [province, city, district, street].filter(Boolean).join('');
                // 加载基础信息
                this.formData = {
                    ...pageData,
                    address,
                    headId: this.headId,
                };

                // 加载联系人信息
                await this.loadContactsData();
            } catch (error) {
                console.error("加载编辑数据失败:", error);
                this.$message.error("加载数据失败");
            } finally {
                this.$utils.hideLoading();
            }
        },

        /**
         * 切换Tab
         */
        switchTab(val) {
            this.tapsActive = val;
        },

        /**
         * 验证基础信息表单
         * @returns {Promise<boolean>} 验证结果
         */
        async validateBasicForm() {
            try {
                return await this.$refs.basicInfo.validate();
            } catch (error) {
                console.error("基础信息表单验证失败:", error);
                return false;
            }
        },

        /**
         * 验证联系人数据
         */
        validateContacts() {
            return this.$refs.contactsInfo.validate();
        },

        /**
         * 检查数据完整性
         * @returns {Promise<boolean>} 检查结果
         */
        async checkData() {
            // 验证基础信息
            const basicValid = await this.validateBasicForm();
            if (!basicValid) {
                this.tapsActive = this.tapsOptions[0]; // 切换到基础信息tab
                return false;
            }

            // // 验证联系人信息
            const contactsValid = this.validateContacts();
            if (!contactsValid) {
                this.tapsActive = this.tapsOptions[1]; // 切换到联系人tab
                return false;
            }

            return true;
        },

        /**
         * 提交表单
         */
        async submit() {
            if (this.isSubmitting) return;

            // 数据完整性检查
            const isValid = await this.checkData();
            if (!isValid) {
                return;
            }

            this.isSubmitting = true;
            this.$utils.showLoading();

            try {
                // 保存联系人数据
                await this.saveContacts();

                const submitData = {
                    ...this.formData,
                    row_status: this.isEditMode ? "UPDATE" : "NEW",
                    address: void 0
                };

                // 保存基本信息
                const response = await this.$http.post(
                    "action/link/highFrequencyPoint/upsert",
                    submitData
                );

                if (response.success) {
                    this.$message.success(
                        this.isEditMode ? "更新成功" : "创建成功"
                    );

                    this.$bus.$emit("hfCollectionListRefresh");

                    // 如果是编辑模式且有新数据，传递给详情页面
                    if (this.isEditMode && response.newRow) {
                        this.$nav.back({
                            refreshFlag: true,
                            newDetailData: response.newRow
                        });
                    } else {
                        this.$nav.back();
                    }
                } else {
                    throw new Error(response.result || "操作失败");
                }
            } catch (error) {
                this.$message.error("操作失败，请重试");
                console.error("提交错误:", error);
            } finally {
                this.isSubmitting = false;
                this.$utils.hideLoading();
            }
        },

        /**
         * 加载联系人数据
         */
        async loadContactsData() {
            try {
                const contactsData = await this.getContactsList();

                if (
                    contactsData.success &&
                    contactsData.rows &&
                    contactsData.rows.length > 0
                ) {
                    this.contactsArr = contactsData.rows.map((contact) => ({
                        ...contact,
                        unfoldFlag: false,
                        row_status: "UPDATE",
                    }));

                    // 默认展开第一个联系人
                    if (this.contactsArr.length > 0) {
                        this.contactsArr[0].unfoldFlag = true;
                    }
                } else {
                    // 创建默认联系人
                    const defaultContact = {
                        headId: this.headId,
                        unfoldFlag: true,
                        row_status: "NEW",
                    };

                    contactsFieldsConfig.forEach((field) => {
                        defaultContact[field.key] = field.defaultValue || "";
                    });

                    // 设置为主要联系人
                    defaultContact.mainFlag = "Y";

                    this.contactsArr = [defaultContact];
                }
            } catch (error) {
                console.error("加载联系人数据失败:", error);
            }
        },

        /**
         * 查询联系人列表
         */
        async getContactsList() {
            if (!this.headId) {
                return { success: false, rows: [] };
            }

            const data = await this.$http.post(
                "action/link/highFrequencyPointContacts/queryByExamplePage",
                {
                    filtersRaw: [
                        {
                            property: "headId",
                            operator: "=",
                            value: this.headId,
                            id: "singleFiltersRaw_0_auto",
                        },
                    ]
                }
            );
            return data;
        },

        /**
         * 保存联系人数据
         */
        async saveContacts() {
            if (!this.contactsArr || this.contactsArr.length === 0) {
                return;
            }

            const upsertContactList = [];

            for (const contact of this.contactsArr) {
                const hasContent = contact.contactsName || contact.mobilePhone;
                if (!hasContent) continue;

                const contactData = {
                    ...contact,
                    headId: this.headId,
                    unfoldFlag: void 0
                };

                upsertContactList.push(contactData);
            }

            if (upsertContactList.length > 0) {
                await this.$http.post(
                    "action/link/highFrequencyPointContacts/batchUpsert",
                    upsertContactList
                );
            }
        },

        /**
         * 重置表单数据（采集点类型变化时调用）
         */
        resetBasicInfo() {
            const basicFields = basicFieldsConfig.commonFields;
            const preservedData = {};
            basicFields.forEach((field) => {
                preservedData[field.key] = this.formData[field.key];
            });

            this.formData = {
                ...preservedData,
                id: this.formData.id,
                pointType: this.formData.pointType,
                province: this.formData.province,
                city: this.formData.city,
                district: this.formData.district,
            };
        }
    },
};
</script>

<style lang="scss">
.hf-edit-page {
    background: #f5f5f5;
    min-height: 100vh;

    // 内容容器
    .content-container {
        padding-top: 112px;
        .content-item {
            background: #ffffff;
            margin-bottom: 24px;
        }
    }

    // 底部按钮区域
    .bottom-sticky {
        .sticky {
            width: 100%;

            .link-button {
                height: 88px;
                font-size: 32px;
                font-weight: 500;
                border-radius: 6px;
            }
        }
    }

    // 底部空白区域
    .blank {
        height: 200px;
        width: 100%;
    }
}
</style>
