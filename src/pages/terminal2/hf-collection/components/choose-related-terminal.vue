<template>
    <view class="choose-related-terminal">
        <link-dialog
            ref="terminalDialog"
            position="bottom"
            height="85vh"
            class="dialog-bottom"
            noPadding
            :value="true"
            @hide="closeDialog"
        >
            <view class="model-title">
                <view class="iconfont icon-close" @tap="closeDialog"></view>
                <view class="title">请选择关联终端</view>
            </view>

            <view class="dialog-content" style="height: calc(100% - 44px)">
                <view
                    v-if="selectedTerminal"
                    class="selected-terminal-container"
                >
                    <view class="selected-terminal-header">
                        <view class="selected-title">已选择</view>
                        <view class="reset-btn" @tap="resetSelection"
                            >重置</view
                        >
                    </view>
                    <view class="selected-terminal-info">
                        <view class="selected-terminal">
                            {{ selectedTerminal.acctCode }}
                        </view>
                    </view>
                </view>

                <scroll-view
                    :scroll-y="true"
                    :style="{
                        height: selectedTerminal
                            ? 'calc(100% - 130px)'
                            : 'calc(100% - 95px)',
                    }"
                    :scroll-top="scrollTop"
                    @scrolltolower="handleScrollToLower"
                >
                    <link-radio-group v-model="selectedTerminalId">
                        <link-auto-list
                            :option="terminalList"
                            hideCreateButton
                            :scrollContent="false"
                            :searchInputBinding="{
                                props: { placeholder: '终端名称/终端编码' },
                            }"
                        >
                            <template slot-scope="{ data, index }">
                                <item
                                    :arrow="false"
                                    :key="data.id"
                                    :title="data.acctName"
                                    :data="data"
                                    @tap.stop="selectTerminal(data)"
                                >
                                    <link-radio
                                        :val="data.id"
                                        slot="thumb"
                                        toggleOnClickItem
                                    />
                                    <view class="radio-right">
                                        <view style="color: #1a1a1d">
                                            {{ data.acctCode }}
                                        </view>
                                        <view class="acct-type">
                                            {{
                                                data.acctType | lov("ACCT_TYPE")
                                            }}
                                        </view>
                                    </view>
                                </item>
                            </template>
                        </link-auto-list>
                    </link-radio-group>
                </scroll-view>
                <view class="link-dialog-foot-custom">
                    <link-button shadow @tap="submitDialog" label="确定" />
                </view>
            </view>
        </link-dialog>
    </view>
</template>

<script>
export default {
    name: "choose-related-terminal",
    data() {
        const terminalList = new this.AutoList(this, {
            request: async (config) => {
                return await this.$httpForm.post(
                    "action/link/es/accnt/queryByExamplePage",
                    config.param
                );
            },
            param: {
                totalFlag: true,
                pageFlag: true,
                oauth: "MULTI_POSTN",
            },
            pageSize: 20,
            loadOnStart: true,
            hooks: {
                afterLoad(res) {
                    res.rows = res.result || res.rows || [];
                },
            },
            searchFields: ["acctName", "acctCode"],
        });
        return {
            selectedTerminalId: "",
            selectedTerminal: null,
            terminalList,
            scrollTop: 0,
        };
    },
    methods: {
        handleScrollToLower() {
            if (!this.terminalList.isNoMoreData) {
                this.terminalList.handler?.reachBottom();
            }
        },
        selectTerminal(data) {
            if (!data || !data.id) return;

            this.selectedTerminalId = data.id;
            this.selectedTerminal = data;
        },
        resetSelection() {
            // 重置已选择的终端
            this.selectedTerminalId = "";
            this.selectedTerminal = null;
        },
        submitDialog() {
            this.$emit("choose", this.selectedTerminal);
            this.$emit("close");
        },
        closeDialog() {
            this.$emit("close");
        },
    },
};
</script>

<style lang="scss">
.choose-related-terminal {
    .link-dialog-foot-custom {
        width: auto !important;
        .link-button {
            width: 100vw;
        }
    }

    .link-dialog-body {
        position: relative;
    }

    .link-auto-list .link-auto-list-top-bar {
        border: none;
    }

    .link-item .link-item-body-right {
        margin: 0 24px;
        flex: 3 !important;
        align-items: baseline !important;
    }

    .link-radio-group {
        .link-item {
            padding: 24px;

            .link-item-icon {
                display: none;
            }
        }

        .link-item-active {
            background-color: #f6f6f6;
        }
    }

    .list-item {
        flex: 1;
    }

    .link-radio-group .link-item:active,
    .link-item-active {
        background-color: #f6f6f6;
    }

    .dialog-bottom {
        .dialog-content {
            padding: 0 20px;
            position: relative;
        }

        .model-title {
            .title {
                font-size: 32px;
                color: #262626;
                letter-spacing: 0;
                text-align: center;
                line-height: 96px;
                height: 96px;
                width: 90%;
                padding-left: 0 !important;
                margin-right: 80px;
                margin-left: 10vw;
            }

            .icon-close {
                color: #bfbfbf;
                font-size: 48px;
                line-height: 96px;
                height: 96px;
                margin-right: 30px;
                float: right;
            }
        }
    }

    .radio-right {
        font-size: 30px;
        color: #1a1a1d;
    }

    .divider {
        height: 1px;
        background-color: #f2f2f2;
        margin: 8px 0;
    }

    .selected-terminal-container {
        padding: 10px 0;
        border-bottom: 1px solid #f2f2f2;
    }

    .selected-terminal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        padding: 0 5px;
    }

    .selected-title {
        font-size: 28px;
        color: #666;
    }

    .reset-btn {
        font-size: 28px;
        color: #3f66ef;
        padding: 4px 10px;
    }

    .selected-terminal-info {
        padding: 5px 5px;
    }

    .selected-terminal {
        font-size: 26px;
        background-color: #f6f6f6;
        padding: 8px 20px;
        border-radius: 30px;
        color: #262626;
        display: inline-block;
    }

    .acct-type {
        color: #666;
        font-size: 26px;
    }
}
</style>
