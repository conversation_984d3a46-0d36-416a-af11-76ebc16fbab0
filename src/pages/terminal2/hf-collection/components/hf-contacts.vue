<!--
 * @Description: 高频点联系人展示组件
-->

<template>
    <view class="hf-contacts">
        <link-auto-list :option="contactsOptions" hideCreateButton>
            <template slot-scope="{ data, index }">
                <item
                    :key="index"
                    :data="data"
                    :arrow="false"
                    class="consumer-rows"
                >
                    <view slot="note" class="container">
                        <view class="item-container">
                            <view class="item-image">
                                <image
                                    :src="$imageAssets.femaleImage"
                                    v-if="data.contactsSex === 'FEMALE'"
                                ></image>
                                <image
                                    :src="$imageAssets.maleImage"
                                    v-else
                                ></image>
                            </view>
                            <view class="item-right">
                                <view class="item-top">
                                    <view class="item-left">
                                        <view
                                            class="name"
                                            :class="{
                                                'change-msg':
                                                    data.showMsg &&
                                                    data.showMsg[
                                                        'contactsName'
                                                    ],
                                            }"
                                        >
                                            <text v-if="data.mainFlag === 'Y'">
                                                [主要]
                                            </text>
                                            {{ data.contactsName }}
                                        </view>
                                        <view
                                            class="phone approval-phone"
                                            v-if="
                                                data.approvalPhone
                                            "
                                        >
                                            {{ data.approvalPhone }}
                                        </view>
                                        <view
                                            class="phone"
                                            :class="{
                                                'change-msg':
                                                    data.showMsg &&
                                                    data.showMsg[
                                                        'mobilePhone'
                                                    ] &&
                                                    data.changeType ===
                                                        'update',
                                                'new-msg':
                                                    data.showMsg &&
                                                    data.showMsg[
                                                        'mobilePhone'
                                                    ] &&
                                                    data.changeType === 'new',
                                            }"
                                            v-else
                                        >
                                            {{ data.mobilePhone }}
                                        </view>
                                    </view>
                                </view>
                                <view class="item-middle">
                                    <view
                                        class="label"
                                        v-if="
                                            data.birthdayType &&
                                            data.birthYear &&
                                            data.birthday
                                        "
                                    >
                                        生日
                                    </view>
                                    <view class="value">
                                        {{
                                            data.birthdayType
                                                | lov("BIRTHDAY_TYPE")
                                        }}&nbsp;
                                    </view>
                                    <view
                                        class="value"
                                        :class="{
                                            'change-msg':
                                                data.showMsg &&
                                                (data.showMsg['birthdayType'] ||
                                                    data.showMsg['birthYear'] ||
                                                    data.showMsg['birthday']),
                                        }"
                                    >
                                        {{ data.birthYear }}
                                        <text
                                            v-if="
                                                data.birthYear && data.birthday
                                            "
                                        >
                                            -
                                        </text>
                                        {{ data.birthday }}
                                    </view>
                                </view>
                                <view class="comments" v-if="data.comments">
                                    <text class="label">备注</text>
                                    <text class="value">{{
                                        data.comments
                                    }}</text>
                                </view>
                            </view>
                        </view>
                        <view class="button-container">
                            <view
                                class="button"
                                @tap="callPhone(data.mobilePhone)"
                            >
                                拨打电话
                            </view>
                            <view class="button" @tap="editAdd(data, 'edit')">
                                编辑
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </view>
</template>

<script>
export default {
    name: "hf-contacts",
    props: {
        detailData: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        // 联系人
        const contactsOptions = new this.AutoList(this, {
            url: {
                queryByExamplePage:
                    "action/link/highFrequencyPointContacts/queryByExamplePage",
            },
            param: {
                filtersRaw: [
                    {
                        property: "headId",
                        operator: "=",
                        value: this.detailData.id,
                        id: "headId",
                    },
                ],
            },
            sortOptions: null,
            filterBar: {},
            hooks: {
              beforeLoad({param}) {
                delete param.order
                delete param.sort
                delete param.page
                delete param.rows
              }
            }
        });
        return {
            contactsOptions,
        };
    },
    methods: {
        /**
         * 编辑/添加联系人
         */
        editAdd(data, flag) {
            this.$nav.push(
                "/pages/terminal2/hf-collection/hf-collection-edit-page",
                {
                    contactData: data,
                    flag: flag,
                    data: this.detailData,
                    headId: this.detailData.id,
                    editFlag: "edit",
                }
            );
        },
        /**
         * 拨打电话
         */
        callPhone(phone) {
            this.$taro.makePhoneCall({
                phoneNumber: phone,
            });
        },
        refreshContacts() {
            this.contactsOptions.methods.reload();
        },
    },
};
</script>

<style lang="scss">
/*deep*/
.link-sticky-top:before {
    box-shadow: none !important;
}

.hf-contacts {
    .change-msg::after {
        content: "(变更)";
        color: red;
    }

    .new-msg::after {
        content: "(新增)";
        color: red;
    }

    /*deep*/
    .link-item-icon {
        width: 0;
        padding-left: 0;
    }

    .edit-text {
        @include flex-end-center;
        width: 100%;
        padding-bottom: 32px;
        padding-top: 32px;
        text-align: right;
        padding-right: 24px;
        font-family: PingFangSC-Regular, serif;
        font-size: 28px;
        color: #2f69f8;
        letter-spacing: 0;
        line-height: 28px;

        .add {
            padding-right: 24px;
        }
    }

    .consumer-rows {
        background: #ffffff;
        border-radius: 32px;
        padding: 40px 24px;
        width: 702px;
        margin: auto auto 24px auto;

        .container {
            .item-container {
                width: 100%;
                @include flex-center-start;
                padding-bottom: 24px;

                .item-image {
                    width: 80px;
                    height: 80px;
                    border-radius: 50%;

                    .effect {
                        text-align: center;
                        color: #ffffff;
                        position: absolute;
                        background: rgba(0, 0, 0, 0.5);
                        width: 80px;
                        height: 80px;
                        line-height: 80px;
                        border-radius: 50%;
                    }

                    image {
                        width: 100%;
                        height: 100%;
                    }
                }

                .item-right {
                    width: 88%;
                    padding-left: 12px;

                    .item-top {
                        @include flex-center-start;
                        @include space-between;
                        margin-bottom: 24px;

                        .item-left {
                            @include flex-start-center;

                            .name {
                                font-size: 32px;
                                color: #262626;

                                text {
                                    color: #2f69f8;
                                    font-size: 28px;
                                    padding-right: 8px;
                                }
                            }

                            .phone {
                                font-size: 28px;
                                color: #8c8c8c;
                                padding-left: 16px;
                            }

                            .label {
                                color: #8c8c8c;
                                font-size: 28px;
                                padding-right: 16px;
                            }

                            .value {
                                font-size: 28px;
                                color: #262626;
                            }

                            .approval-phone::after {
                                content: "(变更)";
                                color: red;
                            }
                        }

                        .item-tag {
                            padding-left: 14px;
                            padding-right: 14px;
                            height: 36px;
                            line-height: 36px;
                            text-align: center;
                            color: #ffffff;
                            box-shadow: 0 3px 4px 0 rgba(47, 105, 248, 0.35);
                            background-image: linear-gradient(
                                180deg,
                                #ff8560 0%,
                                #ff4d2d 100%
                            );
                            border-bottom-right-radius: 100px;
                            border-top-left-radius: 90px;
                            border-top-right-radius: 100px;
                            font-size: 20px;
                            margin-right: 8px;
                        }

                        .recep-tag {
                            padding: 0 14px;
                            height: 36px;
                            line-height: 36px;
                            color: #ffffff;
                            box-shadow: 0 3px 4px 0 rgba(47, 105, 248, 0.35);
                            background-image: linear-gradient(
                                180deg,
                                #b6e3fd 0%,
                                #2faef8 100%
                            );
                            border-radius: 100px;
                        }
                    }

                    .item-middle {
                        @include flex-start-center;

                        .label {
                            color: #8c8c8c;
                            font-size: 28px;
                            padding-right: 16px;
                        }

                        .value {
                            font-size: 28px;
                            color: #262626;
                        }

                        .line {
                            width: 2px;
                            height: 28px;
                            margin-left: 16px;
                            margin-right: 16px;
                            background: #eeeeee;
                        }
                    }

                    .comments {
                        padding-top: 24px;

                        .label {
                            color: #8c8c8c;
                            font-size: 28px;
                            padding-right: 10px;
                        }

                        .value {
                            font-size: 28px;
                            color: #262626;
                        }
                    }
                }
            }

            .button-container {
                padding-top: 24px;
                @include flex-end-center;
                border-top: 1px solid #f2f2f2;

                .button {
                    width: 158px;
                    height: 60px;
                    line-height: 60px;
                    border: 1px solid #2f69f8;
                    font-size: 28px;
                    color: #2f69f8;
                    text-align: center;
                    margin-left: 2%;
                    border-radius: 8px;
                }
            }
        }
    }
}
</style>
