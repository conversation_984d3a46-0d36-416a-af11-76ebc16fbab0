<!--
 * @Description: 高频点基础信息展示组件
-->

<template>
    <view class="hf-basic-info">
        <view class="card-content">
            <!-- 动态字段展示 -->
            <view
                class="card-rows"
                v-for="field in currentFields"
                :key="field.key"
            >
                <view class="card-rows-content">
                    <view class="card-rows-label">
                        {{ field.label == "地址" ? "高频点地址" : field.label }}
                    </view>
                    <view class="value" :class="getValueClass(field)">
                        {{ formatFieldValue(field, details[field.key]) }}
                    </view>
                </view>
            </view>

            <!-- 附件字段 -->
            <view class="card-rows" v-if="details.pointType">
                <view class="card-rows-content">
                    <view class="card-rows-label"> 附件 </view>
                    <view class="value attachment-display">
                        <lnk-img
                            v-if="details.id"
                            :parentId="details.id"
                            moduleType="HIGH_FREQUENCY_COLLECTION"
                            :delFlag="false"
                            :newFlag="false"
                            :showOnly="true"
                        />
                    </view>
                </view>
            </view>

        </view>
    </view>
</template>

<script>
import { getFieldsByType } from "../data/field-config";
import LnkImg from "../../../core/lnk-img/lnk-img";

export default {
    name: "hf-basic-info",
    components: {
        LnkImg,
    },
    props: {
        details: {
            type: Object,
            default: {},
        },
    },
    computed: {
        /**
         * 获取当前高频点类型对应的字段配置
         * @returns {Array} 字段配置数组
         */
        currentFields() {
            if (!this.details.pointType) {
                return [];
            }
            const noShowFields = ["pointName", "pointType", "detailAddress"];
            return getFieldsByType(this.details.pointType).filter(
                (field) => !noShowFields.includes(field.key)
            );
        },
    },
    methods: {
        /**
         * 格式化字段值显示
         * @param {Object} field 字段配置
         * @param {*} value 字段值
         * @returns {string} 格式化后的值
         */
        formatFieldValue(field, value) {
            if (field.key !== "address" && this.$utils.isEmpty(value)) {
                return "-";
            }

            switch (field.type) {
                case "datetime":
                    return this.$utils.dateFormat(value, "YYYY-MM-DD HH:mm:ss");
                case "date":
                    return this.$utils.dateFormat(value, "YYYY-MM-DD");
                case "lov":
                    return this.$filter.lov(value, field.lovType);
                case "address": {
                    const { province, city, district, street, detailAddress } =
                        this.details;
                    const address = [
                        province,
                        city,
                        district,
                        street,
                        detailAddress,
                    ]
                        .filter(Boolean)
                        .join("");
                    return address;
                }
                case "attachment":
                    // 附件字段显示附件数量或状态
                    if (Array.isArray(value) && value.length > 0) {
                        return `${value.length}个附件`;
                    }
                    return "暂无附件";
                default:
                    return value;
            }
        },

        /**
         * 获取值的样式类
         */
        getValueClass(field) {
            return {
                "value-address": field.type === "textarea",
            };
        },
    },
};
</script>

<style lang="scss">
.hf-basic-info {
    .card-content {
        background: #ffffff;
        border-radius: 16px;
        margin-bottom: 24px;
        margin-left: auto;
        margin-right: auto;
        width: 702px;
        padding-top: 8px;
        padding-bottom: 40px;

        .card-title {
            @include flex-start-center;
            @include space-between;
            border-bottom: 2px solid #f2f2f2;
            font-size: 28px;
            padding: 17px 24px;
            .sync-info-title {
                color: #262626;
            }
        }

        .card-rows {
            font-family: PingFangSC-Regular, serif;
            font-size: 28px;
            letter-spacing: 0;
            line-height: 28px;
            padding: 32px 24px 0 24px;

            .card-rows-content {
                @include flex();
                @include space-between();

                .card-rows-label {
                    color: #8c8c8c;
                    white-space: nowrap;
                    margin-right: 16px;
                }

                .value {
                    color: #262626;
                    text-align: right;
                }

                .label-address {
                    color: #8c8c8c;
                    line-height: 40px;
                }

                .value-address {
                    text-align: right;
                    width: 80%;
                    line-height: 40px;
                    color: #262626;
                    word-break: break-all;
                }

                .red {
                    color: red;
                }

                .attachment-display {
                    max-width: 80%;

                    .no-attachment {
                        color: #8c8c8c;
                        font-size: 24px;
                    }
                }
            }
        }

        .line {
            margin-top: -8px;
        }
    }
}
</style>
