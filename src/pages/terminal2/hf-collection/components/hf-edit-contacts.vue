<!--
 * @Description: 高频点联系人信息组件
-->

<template>
    <link-page class="hf-edit-contacts">
        <view
            class="contacts-container"
            v-for="(item, index) in contactsArr"
            :key="index"
        >
            <link-form ref="form" :value="item" :rules="formRules">
                <link-form-item v-if="contactsArr.length > 1">
                    <view slot="title" class="title-container">
                        <view class="contacts-text" @tap="unfoldShrink(index)">
                            联系人 {{ index + 1 }}
                            <link-icon
                                v-if="item.unfoldFlag"
                                icon="icon-shouqi"
                            />
                            <link-icon v-else icon="icon-zhankai" />
                        </view>
                        <view class="delete-btn" @tap="deleteContacts(index)"
                            >删除</view
                        >
                    </view>
                </link-form-item>

                <view v-if="item.unfoldFlag" class="contacts-content">
                    <!-- 动态渲染联系人字段 -->
                    <view v-for="field in contactsFields" :key="field.key">
                        <link-form-item
                            :label="field.label"
                            :required="field.required"
                            :field="field.key"
                            :vertical="field.vertical"
                            @tap="
                                field.special === 'mainContact'
                                    ? setMainContact(item)
                                    : null
                            "
                        >
                            <!-- 输入框字段 -->
                            <link-input
                                v-if="field.type === 'input'"
                                :placeholder="field.placeholder"
                                v-model="item[field.key]"
                            />

                            <!-- 值列表字段 -->
                            <link-lov
                                v-if="field.type === 'lov'"
                                :type="field.lovType"
                                v-model="item[field.key]"
                            />

                            <!-- 日期字段 -->
                            <link-date
                                v-if="field.type === 'date'"
                                :view="field.view"
                                :valueFormat="field.valueFormat"
                                :displayFormat="field.displayFormat"
                                :max="field.maxYear ? maxYear : null"
                                v-model="item[field.key]"
                            />

                            <!-- 开关字段 -->
                            <link-switch
                                v-if="field.type === 'switch'"
                                v-model="item[field.key]"
                            />

                            <!-- 文本域字段 -->
                            <link-textarea
                                v-if="field.type === 'textarea'"
                                v-model="item[field.key]"
                                :placeholder="field.placeholder"
                                :style="field.style"
                            />
                        </link-form-item>
                    </view>
                </view>
            </link-form>
        </view>

        <view class="add-contacts" @tap="addContacts">
            <text class="iconfont icon-plus"></text>
            <text class="text">添加联系人</text>
        </view>

        <view class="blank"></view>
    </link-page>
</template>

<script>
import contactsFieldsConfig from "../data/contacts-fields.json";

export default {
    name: "hf-edit-contacts",
    data() {
        return {
            maxYear: this.$date.format(new Date(), "YYYY"),
            contactsFields: contactsFieldsConfig,
            formRules: {},
        };
    },
    props: {
        contactsArr: {
            type: Array,
            default: () => [],
            required: true,
        },
        formOption: {
            type: Object,
            default: () => ({}),
        },
        headId: {
            type: String,
            default: "",
        },
    },
    created() {
        this.initContactsUnfoldFlag();
        this.generateFormRules();
    },
    methods: {
        /**
         * 初始化联系人展开状态
         */
        initContactsUnfoldFlag() {
            if (this.contactsArr && this.contactsArr.length > 0) {
                this.contactsArr.forEach((contact, index) => {
                    if (contact.unfoldFlag === undefined) {
                        this.$set(contact, "unfoldFlag", index === 0);
                    }
                });
            }
        },
        /**
         * 生成表单验证规则
         */
        generateFormRules() {
            const rules = {};

            this.contactsFields.forEach((field) => {
                if (field.rules) {
                    const fieldRules = [];

                    switch (field.rules) {
                        case "require":
                            fieldRules.push(
                                this.Validator.required(`请输入${field.label}`)
                            );
                            break;
                        case "phone":
                            fieldRules.push(
                                this.Validator.phone(
                                    `请输入正确的${field.label}格式`
                                )
                            );
                            break;
                        case "email":
                            fieldRules.push(
                                this.Validator.email(
                                    `请输入正确的${field.label}格式`
                                )
                            );
                            break;
                        default:
                            break;
                    }

                    if (fieldRules.length > 0) {
                        rules[field.key] = fieldRules;
                    }
                }
            });

            this.formRules = rules;
        },

        /**
         * 设置主要联系人
         */
        setMainContact(selectedContact) {
            if (selectedContact.mainFlag === "Y") {
                this.contactsArr
                    .filter((contact) => contact.id !== selectedContact.id)
                    .forEach((contact) => {
                        contact.mainFlag = "N";
                    });
            }
        },

        /**
         * 展开/收起联系人信息
         */
        unfoldShrink(index) {
            this.contactsArr.forEach((item, idx) => {
                if (index === idx) {
                    item.unfoldFlag = !item.unfoldFlag;
                    this.$set(this.contactsArr, idx, item);
                } else {
                    item.unfoldFlag = false;
                    this.$set(this.contactsArr, idx, item);
                }
            });
        },

        /**
         * 删除联系人
         * @description 删除指定索引的联系人
         * @param {number} index 要删除的联系人索引
         */
        deleteContacts(index) {
            if (this.contactsArr.length <= 1) {
                this.$message.warn("至少需要保留一个联系人");
                return;
            }

            this.contactsArr.splice(index, 1);

            // 如果删除后只剩一个联系人，自动展开
            if (this.contactsArr.length === 1) {
                this.contactsArr[0].unfoldFlag = true;
            }

            // 如果删除的是主要联系人，将第一个联系人设为主要
            const hasMainContact = this.contactsArr.some(
                (contact) => contact.mainFlag === "Y"
            );
            if (!hasMainContact && this.contactsArr.length > 0) {
                this.contactsArr[0].mainFlag = "Y";
            }
        },

        /**
         * 添加联系人
         */
        async addContacts() {
            const newContact = {
                row_status: "NEW",
                headId: this.headId,
                unfoldFlag: true,
            };

            // 根据字段配置初始化联系人数据
            this.contactsFields.forEach((field) => {
                newContact[field.key] = field.defaultValue || "";
            });

            // 收起其他联系人
            this.contactsArr.forEach((item) => {
                item.unfoldFlag = false;
            });

            // 如果没有主要联系人，则设置为主要
            const hasMainContact = this.contactsArr.some(
                (contact) => contact.mainFlag === "Y"
            );
            if (!hasMainContact) {
                newContact.mainFlag = "Y";
            }

            this.contactsArr.push(newContact);
        },

        /**
         * 验证联系人数据
         * @description 验证所有联系人的必填字段并在内部显示错误提示
         * @returns {boolean} 验证结果
         */
        validate() {
            for (let i = 0; i < this.contactsArr.length; i++) {
                const contact = this.contactsArr[i];

                // 检查必填字段
                for (const field of this.contactsFields) {
                    if (field.required) {
                        if (!contact[field.key] || !contact[field.key].trim()) {
                            this.$message.warn(
                                `请完善联系人${i + 1}-${field.label}`
                            );
                            return false;
                        }
                    }

                    // 检查字段格式
                    if (
                        field.rules &&
                        contact[field.key] &&
                        contact[field.key].trim()
                    ) {
                        if (field.rules === "phone") {
                            const phonePattern = /^1[3-9]\d{9}$/;
                            if (!phonePattern.test(contact[field.key])) {
                                this.$message.warn(
                                    `请完善联系人${i + 1}-${field.label}格式`
                                );
                                return false;
                            }
                        } else if (field.rules === "email") {
                            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                            if (!emailPattern.test(contact[field.key])) {
                                this.$message.warn(
                                    `请完善联系人${i + 1}-${field.label}格式`
                                );
                                return false;
                            }
                        }
                    }
                }
            }

            return true;
        },
    },
};
</script>

<style lang="scss">
.hf-edit-contacts {
    background: #f5f5f5;

    .contacts-container {
        .title-container {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .contacts-text {
                display: flex;
                align-items: center;

                .link-icon {
                    margin-left: 16px;
                    font-size: 24px;
                    color: #8c8c8c;
                }
            }

            .delete-btn {
                color: #ff4d4f;
                font-size: 28px;
                padding: 8px 16px;
                border-radius: 8px;

                &:active {
                    background: #fff1f0;
                }
            }
        }
    }

    .add-contacts {
        border: 2px dashed #2f69f8;
        border-radius: 8px;
        margin: auto;
        height: 96px;
        width: 702px;
        text-align: center;
        .text {
            font-family: PingFangSC-Regular, serif;
            font-size: 32px;
            color: #2f69f8;
            letter-spacing: 0;
            line-height: 96px;
        }
        .icon-plus {
            font-size: 32px;
            line-height: 96px;
            color: #2f69f8;
        }
    }

    .blank {
        height: 100px;
        width: 100%;
    }
}
</style>
