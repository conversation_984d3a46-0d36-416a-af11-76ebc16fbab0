<!--
 * @Description: 定位选择组件
-->

<template>
    <view class="location-selector">
        <!-- 定位失败时的地址选择弹窗 -->
        <link-dialog
            ref="locationFailSelectAddress"
            class="location-select-address"
            position="poster"
        >
            <view class="address-dialog">
                <view class="dialog-header">
                    <view class="dialog-title"
                        >自动获取定位失败,请手动选择就近地址</view
                    >
                </view>

                <view class="address-form">
                    <!-- 省市区选择 -->
                    <view class="region-selector">
                        <item title="所在地区">
                            <link-address
                                placeholder="请选择所在地区"
                                :province.sync="selectAddressObj.province"
                                :city.sync="selectAddressObj.city"
                                :district.sync="selectAddressObj.district"
                            />
                        </item>
                    </view>

                    <!-- 详细地址输入 -->
                    <view class="detail-address">
                        <view class="address-label">详细地址</view>
                        <view class="address-input">
                            <link-input
                                type="text"
                                v-model="selectAddressObj.addr"
                                placeholder="请填写或选择单位名称"
                            />
                        </view>
                        <view class="search-btn" @tap="searchAddress">
                            <view
                                class="iconfont icon-sousuo search-icon"
                            ></view>
                            <view class="search-text">智能联想地址</view>
                        </view>
                    </view>
                </view>

                <!-- 地址联想列表 -->
                <scroll-view scroll-y="true" class="suggestion-list">
                    <view
                        v-for="(item, index) in suggestionAddressData"
                        :key="index"
                    >
                        <view
                            class="suggestion-item"
                            @tap="selectSuggestionAddress(item)"
                        >
                            <view class="suggestion-content">
                                <view class="suggestion-info">
                                    <view class="suggestion-name">{{
                                        item.title
                                    }}</view>
                                    <view class="suggestion-address">{{
                                        item.address
                                    }}</view>
                                </view>
                            </view>
                            <view class="suggestion-check">
                                <view v-if="item._checked">
                                    <link-icon
                                        size="1.8em"
                                        style="color: #2f69f8; font-size: 16px"
                                        icon="icon-check"
                                    />
                                </view>
                            </view>
                        </view>
                    </view>
                </scroll-view>

                <view class="dialog-footer">
                    <link-button @tap="confirmAddress" block>确定</link-button>
                </view>
            </view>
        </link-dialog>
    </view>
</template>

<script>
import { getCurrentCoordinate } from "@/utils/locations-tencent";
import Taro from "@tarojs/taro";

export default {
    name: "location-selector",
    data() {
        return {
            // 定位相关状态
            locationLoading: false,
            coordinate: {},
            openSettingNum: 0,

            // 定位失败时的地址选择对象
            selectAddressObj: {
                province: "",
                city: "",
                district: "",
                addr: "",
            },

            // 地址联想数据
            suggestionAddressData: [],
        };
    },
    methods: {
        /**
         * 获取位置信息
         */
        async getLocation() {
            try {
                this.locationLoading = true;

                // 获取当前坐标
                const addressInfo = await getCurrentCoordinate();

                if (!addressInfo.errMsg) {
                    this.coordinate = addressInfo;
                }

                // 处理各种定位失败的情况
                if (
                    this.coordinate.errMsg ===
                        "getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF" ||
                    this.coordinate.errMsg ===
                        "getLocation:fail system permission denied" ||
                    this.coordinate.errMsg ===
                        "getLocation:fail:system permission denied"
                ) {
                    // 网络类型
                    let net = "";
                    await Taro.getNetworkType({
                        success: (res) => {
                            net = res.networkType;
                        },
                    });

                    // 5G网络特殊处理
                    if (net === "5g" && this.openSettingNum > 1) {
                        this.showLocationFailDialog();
                        return;
                    }

                    if (this.coordinate.latitude && this.coordinate.longitude) {
                        this.showPermissionDialog();
                        this.openSettingNum++;
                        return;
                    }
                }

                if (this.coordinate.latitude && this.coordinate.longitude) {
                    this.triggerLocationSelect(
                        this.coordinate.latitude,
                        this.coordinate.longitude
                    );
                    return;
                }
                if (addressInfo?.latitude && addressInfo?.longitude) {
                    this.triggerLocationSelect(
                        addressInfo.latitude,
                        addressInfo.longitude
                    );
                    return;
                }
                // 定位失败，引导用户手动开启权限或重试
                this.showLocationFailedDialog();
            } catch (error) {
                console.error("定位过程出错:", error);
                this.handleLocationError(error);
            } finally {
                this.locationLoading = false;
            }
        },

        /**
         * 触发位置选择
         */
        async triggerLocationSelect(latitude, longitude) {
            try {
                this.$locations.QQClearLocation();

                await this.$locations.chooseLocation(latitude, longitude);
            } catch (error) {
                console.error("触发位置选择失败:", error);
                this.$message.error({ title: "打开位置选择页面失败" });
            }
        },

        /**
         * 显示权限对话框
         */
        showPermissionDialog() {
            this.$dialog({
                title: "提示",
                content:
                    "请确认手机地理位置授权是否打开，或者【设置】-【企业微信】位置权限管理是否打开？",
                cancelButton: false,
                confirmText: "去开启",
                onConfirm: async () => {
                    Taro.openSetting({
                        success: async (res) => {
                            if (res.authSetting["scope.userLocation"]) {
                                // 重新获取位置
                                this.coordinate = await getCurrentCoordinate();
                                if (
                                    !this.coordinate.errMsg &&
                                    this.coordinate.latitude &&
                                    this.coordinate.longitude
                                ) {
                                    this.triggerLocationSelect(
                                        this.coordinate.latitude,
                                        this.coordinate.longitude
                                    );
                                }
                            }
                        },
                        fail: () => {
                            this.$message.error({
                                title: "打开授权设置失败，请重试",
                            });
                        },
                    });
                },
            });
        },

        /**
         * 显示定位失败对话框
         */
        showLocationFailedDialog() {
            this.$dialog({
                title: "定位失败",
                content: "无法获取当前位置，请检查位置权限设置",
                cancelText: "手动输入",
                confirmText: "重新定位",
                onCancel: () => {
                    this.$message.warn({ title: "请手动输入地址信息" });
                },
                onConfirm: async () => {
                    // 重新尝试定位
                    await this.getLocation();
                },
            });
        },

        /**
         * 显示5G网络定位失败弹窗
         */
        showLocationFailDialog() {
            this.resetSelectAddressObj();
            this.$refs.locationFailSelectAddress.show();
        },

        /**
         * 处理定位错误
         * @param {Error} error 错误对象
         */
        handleLocationError(error) {
            let errorMessage = "定位失败";
            if (error.message?.includes("permission")) {
                errorMessage = "位置权限被拒绝，请在设置中开启位置权限";
            } else if (error.message?.includes("network")) {
                errorMessage = "网络连接异常，请检查网络设置";
            } else {
                errorMessage = "定位服务暂时不可用，请稍后重试或手动输入地址";
            }

            this.$message.error({ title: errorMessage });
        },

        /**
         * 重置地址选择对象
         * @description 清空5G网络失败时的地址选择数据
         */
        resetSelectAddressObj() {
            this.selectAddressObj = {
                province: "",
                city: "",
                district: "",
                addr: "",
            };
            this.suggestionAddressData = [];
        },

        /**
         * 搜索地址联想
         * @description 根据选择的省市区和输入的详细地址进行联想搜索
         */
        async searchAddress() {
            if (this.selectAddressObj.addr) {
                this.$message.warn({ title: "请输入详细地址" });
                return false;
            }

            try {
                const address =
                    this.selectAddressObj.province +
                    this.selectAddressObj.city +
                    this.selectAddressObj.district +
                    this.selectAddressObj.addr;
                const data = await this.$locations.getTMapSuggestion(address);
                this.suggestionAddressData = [...data.data];
            } catch (error) {
                console.error("地址搜索失败:", error);
                this.$message.error({ title: "地址搜索失败，请重试" });
            }
        },

        /**
         * 选择联想地址
         * @description 选择某一个联想地址项
         * @param {Object} item 选中的地址项
         */
        selectSuggestionAddress(item) {
            this.$set(item, "_checked", true);
            this.suggestionAddressData.forEach((address) => {
                if (address.address !== item.address) {
                    this.$set(address, "_checked", false);
                }
            });
        },

        /**
         * 确认地址选择
         * @description 确认选择的联想地址并关闭弹窗
         */
        async confirmAddress() {
            const selectedAddress = this.suggestionAddressData.find(
                (item) => item._checked === true
            );

            if (this.$utils.isEmpty(selectedAddress)) {
                this.$message.warn({ title: "请选择一个地址" });
                return false;
            }

            try {
                const newAddressData = {};

                newAddressData.province = selectedAddress.province;
                newAddressData.city = selectedAddress.city;
                newAddressData.district = selectedAddress.district;
                newAddressData.street = "";

                let detailAddress = selectedAddress.title || "";
                [
                    selectedAddress.province,
                    selectedAddress.city,
                    selectedAddress.district,
                ]
                    .filter(Boolean)
                    .forEach((region) => {
                        detailAddress = detailAddress.replace(region, "");
                    });

                newAddressData.detailAddress = detailAddress.trim();

                const addressParts = [
                    selectedAddress.province,
                    selectedAddress.city,
                    selectedAddress.district,
                ].filter((part) => part?.trim());
                newAddressData.address = addressParts.join("/");

                if (selectedAddress.location) {
                    newAddressData.latitude =
                        selectedAddress.location.lat.toString();
                    newAddressData.longitude =
                        selectedAddress.location.lng.toString();
                }

                this.$emit("change", newAddressData);

                this.hideLocationFailDialog();
            } catch (error) {
                console.error("确认地址失败:", error);
                this.$message.error({ title: "确认地址失败，请重试" });
            }
        },

        /**
         * 隐藏定位失败选择弹窗
         */
        hideLocationFailDialog() {
            this.$refs.locationFailSelectAddress.hide();
        },

        /**
         * 手动触发定位（外部调用）
         */
        triggerLocation() {
            return this.getLocation();
        },
    },
};
</script>

<style lang="scss">
.location-selector {
    // 5G网络定位失败弹窗样式
    .location-select-address {
        .link-dialog-content .link-dialog-body {
            padding: 0 !important;
            flex: 1;
            font-size: 28px;
            overflow: hidden;
            word-break: break-all;
        }

        .address-dialog {
            height: 900px;
            border-radius: 16px;
            background-color: white;
            position: relative;
            display: flex;
            flex-direction: column;

            .dialog-header {
                padding: 20px;
                text-align: center;
                flex-shrink: 0;

                .dialog-title {
                    color: #333;
                    line-height: 1.4;
                }
            }

            .address-form {
                padding: 0 20px;
                flex-shrink: 0;

                .region-selector {
                    margin-bottom: 20px;
                }

                .detail-address {
                    padding: 0 14px;
                    .address-label {
                        color: #333;
                        margin-bottom: 8px;
                    }

                    .address-input {
                        margin-bottom: 12px;
                    }

                    .search-btn {
                        display: flex;
                        align-items: center;
                        padding: 8px 12px;
                        background-color: #f5f5f5;
                        border-radius: 4px;

                        .search-icon {
                            color: #666;
                            margin-right: 8px;
                        }

                        .search-text {
                            color: #666;
                        }

                        &:active {
                            background-color: #e9ecef;
                        }
                    }
                }
            }

            .suggestion-list {
                flex: 1;
                margin-top: 10px;
                margin-bottom: 88px;

                .suggestion-item {
                    display: flex;
                    align-items: center;
                    padding: 20px;
                    border-bottom: 1px solid #f2f2f2;

                    .suggestion-content {
                        flex: 1;

                        .suggestion-info {
                            .suggestion-name {
                                color: #262626;
                                font-weight: 600;
                                line-height: 1.4;
                                margin-bottom: 8px;
                            }

                            .suggestion-address {
                                color: #8c8c8c;
                                line-height: 1.3;
                            }
                        }
                    }

                    .suggestion-check {
                        width: 40px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }

                    &:active {
                        background-color: #f8f9fa;
                    }
                }
            }

            .dialog-footer {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                padding: 20px;
                background-color: white;
                border-top: 1px solid #f2f2f2;
                margin: 0;
                padding: 24px 32px;

                .link-button {
                    width: 100%;
                    margin: 0;
                }
            }
        }
    }
}
</style>
