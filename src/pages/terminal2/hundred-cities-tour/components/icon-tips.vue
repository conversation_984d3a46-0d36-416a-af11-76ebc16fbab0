<template>
    <view class="icon-tips">
        <link-icon icon="mp-info-lite" size="24" @tap="showTips(tips)"/>
        <link-dialog v-model="tipOpen" :enableScroll="false">
            <view class="daily-report-data-tip-content">
                <text v-for="(line, index) in tipLines" :key="index">{{ line }}</text>
            </view>
        </link-dialog>
    </view>
</template>

<script>
export default {
    name: "icon-tips",
    props: {
        tips: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            tipOpen: false,
            tipLines: undefined
        }
    },
    methods: {
        showTips(content) {
            this.tips = content;
            this.tipLines = content.split('\n');
            this.tipOpen = true;
        },
    }
}
</script>

<style lang="scss">
.icon-tips {
    display: inline-block;
    margin-left: 8px;

    .daily-report-data-tip-content {
        padding: 12px;
        text-align: left;

        text {
            display: block;
            font-size: 28px;
            line-height: 1.5;
            margin-bottom: 8px;
        }
    }
}
</style>
