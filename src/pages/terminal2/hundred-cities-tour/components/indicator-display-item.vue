<template>
    <view class="indicator-display-item">
        <view class="indicator-info-content-item" v-for="(indicator, index) in indicatorArray" :key="index">
            <view class="indicator-info-content-item-title">
                <text class="indicator-info-content-item-title-text">{{ indicator.title }}</text>
            </view>
            <view class="indicator-info-content-wrap">
                <view class="indicator-info-content-item-content" :class="{'substandard': item.substandard}"
                      v-for="(item, index) in indicator.content"
                      :key="index">
                    <view class="indicator-info-content-item-content-value">{{ item.value }}</view>
                    <view class="indicator-info-content-item-content-label">{{ item.label }}</view>
                </view>
            </view>
        </view>

    </view>
</template>

<script>
export default {
    name: "indicator-display-item",
    data() {
        return {
            hasSubstandard: false
        }
    },
    props: {
        indicatorArray: {
            type: Array,
            default: () => []
        }
    },
    created() {
        this.indicatorArray.forEach(indicator => {
            indicator.content.forEach(item => {
                this.$set(item, 'substandard', this.checkStandard(item))
            })
        })
    },
    methods: {
        /**
         * @description 是否不达标标红
         * <AUTHOR>
         * @date 2025/5/13
         */
        checkStandard(item) {
            let result = false
            const openBottleStandard = ['k-', 'k', '2k', '3k', '4k', '5k', '6k', '7k', '8k', '9k', '10k'];
            //开瓶达级（特曲小于2K，其余小于3K）标红
            if (['jlMemLevel', 'dchgMemLevel', 'dctqMemLevel', 'gjMemLevel', 'hjMemLevel'].includes(item.key)) {
                result = openBottleStandard.findIndex(i => i === item.value) < 3
            } else if (['tqMemLevel'].includes(item.key)) {
                result = openBottleStandard.findIndex(i => i === item.value) < 2
                //配额执行（国窖、怀旧、头曲小于60件、特曲窖龄小于42件、黑盖小于9件）
            } else if (['dcTqTerCginPcQty', 'hjTerCginPcQty', 'gjTerCginPcQty'].includes(item.key)) {
                result = Number(item.value || 0) < 60
            } else if (['jlTerCginPcQty', 'tqTerCginPcQty'].includes(item.key)) {
                result = Number(item.value || 0) < 42
            } else if (['dcHgTerCginPcQty'].includes(item.key)) {
                result = Number(item.value || 0) < 9
                // 销售额（泸系小于5W标红；终端总体小于20W）
            } else if (['lxTerSaleQty'].includes(item.key)) {
                result = Number(item.value || 0) < 50000
            } else if (['terSaleQty'].includes(item.key)) {
                result = Number(item.value || 0) < 200000
            }
            if (result) {
                this.$emit('hasSubstandard')
            }
            return result
        }
    }
}
</script>

<style lang="scss">
.indicator-display-item {
    .indicator-info-content-item {

        .indicator-info-content-item-title {
            color: #02A7F0;
            padding: 20px;
        }

        .indicator-info-content-wrap {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            flex-wrap: wrap;

            .indicator-info-content-item-content {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 10px;
                box-sizing: border-box;
                width: calc(100% / 3);
                position: relative;

                .indicator-info-content-item-content-label {
                    color: #797979;
                    font-size: 24px;
                }

                .indicator-info-content-item-content-value {
                    font-size: 26px;
                    font-weight: 600;
                    margin-bottom: 10px;
                }
            }

            .indicator-info-content-item-content:not(:nth-child(3n)):not(:last-child)::after {
                content: '';
                position: absolute;
                top: 15%; /* 使分割线从顶部 15% 开始 */
                bottom: 15%; /* 使分割线到底部 15% 结束，这样分割线高度就是 70% */
                right: 0;
                width: 1px;
                background-color: #ccc;
            }

            .substandard {
                color: #ff0000 !important;
            }
        }

    }
}
</style>
