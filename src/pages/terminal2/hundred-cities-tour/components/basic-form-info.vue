<template>
    <view class="basic-form-info">
        <!--        客户基础信息-->
        <view class="basic-form-info-title">基础信息</view>
        <link-form class="form-wrap" ref="form" :value="formData">
            <link-form-item label="客户编码">
                <link-input v-model="formData.acctCode" readonly/>
            </link-form-item>
            <link-form-item label="客户名称">
                <link-input v-model="formData.acctName" readonly/>
            </link-form-item>
            <link-form-item label="纳税人识别号">
                <link-input v-model="formData.creditNo" readonly/>
            </link-form-item>
            <link-form-item label="省/市/区">
                <view class="value">{{ formData.provinceName }}{{ formData.cityName }}{{ formData.districtName }}</view>
            </link-form-item>
            <link-form-item label="详细地址">
                <link-input v-model="formData.detailAddr" readonly/>
            </link-form-item>
        </link-form>
        <!--        综合指标-->
        <view class="basic-form-info-title">综合指标<icon-tips tips="不达标的指标标红"/></view>
        <link-form class="form-wrap" ref="form" :value="formData">
            <link-form-item label="四色指标">
                <view :class="{'substandard': fourColorStandard}" class="value">{{formData.fourColorLabel}}</view>
            </link-form-item>
            <link-form-item label="产融协作是否注册">
                <view :class="{'substandard': financeTerStandard}" class="value">{{formData.financeTerStatus}}</view>
            </link-form-item>
            <link-form-item label="融资标签">
                <view :class="{'substandard': rzStandard}" class="value">{{formData.yrFinanceFlag}}</view>
            </link-form-item>
            <link-form-item label="是否经销商自有门店">
                <link-input :class="{'substandard': ownStandard}" v-model="formData.ownStores" readonly/>
            </link-form-item>
            <link-form-item label="品牌融合目标">
                <link-input v-model="formData.targetNum" readonly/>
            </link-form-item>
            <link-form-item label="品牌融合数量">
                <link-input :class="{'substandard': targetStandard}" v-model="formData.brandTerMergeQty" readonly/>
            </link-form-item>
        </link-form>
    </view>
</template>

<script>
import IconTips from "./icon-tips";
export default {
    name: "basic-form-info",
    components: {IconTips},
    props: {
        formData: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    computed:{
        rzStandard(){
            return this.formData.yrFinanceFlag === '否'
        },
        financeTerStandard(){
            return this.formData.financeTerStatus === '否'
        },
        fourColorStandard(){
            return this.formData.fourColorLabel === '黑' || this.formData.fourColorLabel === '黄'
        },
        targetStandard(){
            return this.formData.brandTerMergeQty < this.formData.targetNum;
        },
        ownStandard(){
            return this.formData.ownStores === '是';
        },
        isSubstandard(){
            return this.rzStandard || this.financeTerStandard || this.fourColorStandard || this.targetStandard || this.ownStandard
        }
    },
    data() {
        return {}
    }
}
</script>

<style lang="scss">
.basic-form-info {
    .basic-form-info-title {
        padding: 20px;
    }

    .form-wrap {
        border-radius: 20px;
        overflow: hidden;
        .value{
            color: #3B4144;
        }
    }
    .substandard{
        color: #FF0000 !important;
    }

}
</style>
