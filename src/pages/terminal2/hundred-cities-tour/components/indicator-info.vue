<template>
    <view class="indicator-info">
        <view class="indicator-info-content-title">
            <view class="indicator-info-content-title-text">
                <text>合作品牌指标</text>
                <icon-tips tips="不达标的指标标红"/>
            </view>
        </view>
        <view class="indicator-info-content">
            <indicator-display-item @hasSubstandard="isSubstandard = true" v-if="initComplete" :indicator-array="indicatorInfo"/>
        </view>
    </view>
</template>

<script>
import IndicatorDisplayItem from "./indicator-display-item";
import IconTips from "./icon-tips";

export default {
    name: "indicator-info",
    props: {
        indicatorData: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    components: {IconTips, IndicatorDisplayItem},
    data() {
        return {
            indicatorInfo: [
                {
                    title: '开瓶达级',
                    content: [{
                        label: '国窖',
                        key: 'gjMemLevel'
                    }, {
                        label: '特曲',
                        key: 'tqMemLevel'
                    }, {
                        label: '怀旧',
                        key: 'hjMemLevel'
                    }, {
                        label: '头曲',
                        key: 'dctqMemLevel'
                    }, {
                        label: '黑盖',
                        key: 'dchgMemLevel'
                    }, {
                        label: '窖龄',
                        key: 'jlMemLevel'
                    }]
                },
                {
                    title: '配额执行（终端净入库件数）',
                    content: [{
                        label: '国窖',
                        key: 'gjTerCginPcQty'
                    }, {
                        label: '特曲',
                        key: 'tqTerCginPcQty'
                    }, {
                        label: '怀旧',
                        key: 'hjTerCginPcQty'
                    }, {
                        label: '头曲',
                        key: 'dcTqTerCginPcQty'
                    }, {
                        label: '黑盖',
                        key: 'dcHgTerCginPcQty'
                    }, {
                        label: '窖龄',
                        key: 'jlTerCginPcQty'
                    }]
                },
                {
                    title: '销售额',
                    content: [{
                        label: '终端销售额（元）',
                        key: 'terSaleQty'
                    }, {
                        label: '泸系销售额（元）',
                        key: 'lxTerSaleQty'
                    }]
                }
            ],
            initComplete: false,
            isSubstandard: false
        }
    },
    created() {
        this.initData()
    },
    methods:{
        initData(){
            this.indicatorInfo.forEach(item => {
                item.content.forEach(item2 => {
                    item2.value = this.indicatorData[item2.key]
                })
            })
            this.initComplete = true
        }
    }
}
</script>

<style lang="scss">
.indicator-info {
    margin: 20px;

    .indicator-info-content-title {
        padding: 20px;
    }

    .indicator-info-content {
        background: #ffffff;
        border-radius: 20px;


    }
}
</style>
