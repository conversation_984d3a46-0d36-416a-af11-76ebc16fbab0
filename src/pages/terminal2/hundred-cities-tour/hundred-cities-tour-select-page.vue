<template>
    <link-page class="hundred-cities-tour-select-page">
        <!--        终端基本信息-->
        <view class="row-content">
            <basic-form-info ref="basic-form" :form-data="acctTravelInfo"/>
        </view>
        <!--        合作品牌指标-->
        <indicator-info ref="indicator-info" v-if="acctTravelInfo.id" :indicator-data="acctTravelInfo"/>
        <!--        评选结果-->
        <view class="standard-result" v-if="isSelectStander">
            <indicator-display-item :indicator-array="standardResult"/>
        </view>
        <!--特殊情况备注-->
        <view class="comments" :class="{'approval-comments': showCommentsMargin}">
            <view class="comments-title">特殊情况备注</view>
            <view class="comments-wrap">
                <link-textarea v-model="comments" :readonly="pageStatus !== 'edit'"></link-textarea>
            </view>
        </view>
        <!--        选择评选标准弹框-->
        <link-dialog ref="standardDialog" position="bottom" :initial="true" v-model="showStandardDialog">
            <view class="standard">
                <view class="standard-title">申请结果</view>
                <view class="standard-content">
                    <view class="standard-content-item">
                        <view class="standard-content-item-title">合作品牌</view>
                        <link-radio-group class="standard-item" v-model="selectStandard.brand">
                            <list>
                                <item v-for="(item,index) in evaluationStandards.brand" :key="index" :arrow="false">
                                    <link-checkbox :val="item.val" toggleOnClickItem slot="thumb"/>
                                    <text slot="title">{{ item.label }}</text>
                                </item>
                            </list>
                        </link-radio-group>
                    </view>
                    <view class="standard-content-item">
                        <view class="standard-content-item-title">旅游标准及名额</view>
                        <link-radio-group v-model="selectStandard.standard">
                            <list>
                                <item v-for="(item,index) in evaluationStandards.standards" :key="index" :arrow="false">
                                    <link-checkbox :val="item.val" toggleOnClickItem slot="thumb"/>
                                    <text slot="title">{{ item.label - 2000 }}元(价值{{ item.label }}元)</text>
                                    <link-number-keyboard v-if="selectStandard.standard === item.val" :max="100000" :precision="0"
                                                          v-model="selectStandard.person"
                                                          placeholder="请填写对应人数"></link-number-keyboard>
                                </item>
                            </list>
                        </link-radio-group>
                    </view>
                </view>
                <link-button class="confirm-btn" @tap="confirmSelect" autoLoading :throttleTimer="10">确认</link-button>
            </view>
        </link-dialog>
        <link-sticky class="bottom-btn" v-if="pageStatus === 'edit'">
            <link-button class="sure-btn" v-if="!isSelectStander" @tap="selectStander" autoLoading :throttleTimer="10">
                选择评选标准
            </link-button>
            <link-button class="sure-btn" v-else @tap="submit" autoLoading :throttleTimer="10">提交</link-button>
        </link-sticky>
        <!-- 审批操作 -->
        <link-sticky class="bottom-btn" v-if="pageStatus === 'approval' && $utils.isNotEmpty(approvalId)">
            <approval-operator :approvalId="approvalId"></approval-operator>
        </link-sticky>
    </link-page>

</template>

<script>
import BasicFormInfo from "./components/basic-form-info";
import IndicatorInfo from "./components/indicator-info";
import IndicatorDisplayItem from "./components/indicator-display-item";
import ApprovalOperator from "../../lzlj/approval/components/approval-operator";

export default {
    name: "hundred-cities-tour-select-page",
    components: {ApprovalOperator, IndicatorDisplayItem, IndicatorInfo, BasicFormInfo},
    data() {
        return {
            acctTravelInfo: {}, //终端百城旅游信息
            comments: '', //备注
            selectStandard: {
                brand: '',
                standard: '',
                person: ''
            },
            standardResult: [{
                title: '申请结果',
                content: []
            }],
            evaluationStandards: {
                brand: [],
                standards: []
            },
            isSelectStander: false,
            showStandardDialog: false,
            pageStatus: 'edit',
            approvalId: undefined,
            approvalStatus: undefined,
            mode: ''
        }
    },
    computed:{
        showCommentsMargin(){
            return this.pageStatus==='approval' && (['Running','PreApprove'].includes(this.approvalStatus) || this.mode === 'sceneQw')
        }
    },
    async created() {
        let sceneObj = await this.$scene.ready();
        let code = this.pageParam.source;//页面来源
        this.pageStatus = this.pageParam.pageStatus || 'approval'
        if (this.pageStatus === 'edit') {
            await this.$taro.setNavigationBarTitle({title: '百城旅游评选'});
            this.queryLovByType()
            this.queryTravelByAcctId()
        } else {
            await this.$taro.setNavigationBarTitle({title: '百城旅游申请审批'});

            if (sceneObj.query['approval_from'] === 'qw') {
                const flowObjId = sceneObj.query['flowObjId'];
                this.approvalId = sceneObj.query['approval_id'];
                this.mode = 'sceneQw'
                await this.queryAcctTravelInfo(flowObjId)
            }else {
                this.acctTravelInfo = JSON.parse(this.pageParam.data.flowObjDetail);
                this.approvalId = this.pageParam.data.id;
                this.approvalStatus = this.pageParam.data.flowStatus;
            }
            this.standardResult[0].content = [
                {
                    label: '合作品牌',
                    value: await this.$lov.getNameByTypeAndVal('COOPERATIVE_BRAND', this.acctTravelInfo.partnerBrand)
                },
                {
                    label: '旅游标准',
                    value: await this.$lov.getNameByTypeAndVal('TRAVEL_REWARD', this.acctTravelInfo.tourStandards)
                },
                {
                    label: '名额',
                    value: this.acctTravelInfo.quota
                }
            ]
            this.isSelectStander = true
            this.comments = this.acctTravelInfo.specialRemark
        }
    },
    methods: {
        /**
         * @description 查询相关值列表
         * <AUTHOR>
         * @date 2025/5/13
         */
        async queryLovByType() {
            const [TRAVEL_REWARD, COOPERATIVE_BRAND] = await this.$lov.getLovByTypeArray(['TRAVEL_REWARD', 'COOPERATIVE_BRAND'])
            const brand = COOPERATIVE_BRAND.map(item => {
                return {
                    label: item.name,
                    val: item.val
                }
            })
            const standards = TRAVEL_REWARD.map(item => {
                return {
                    label: item.name,
                    val: item.val
                }
            })
            this.evaluationStandards = {
                brand,
                standards
            }
        },
        /**
         * @description 根据id查询旅游终端详情
         * <AUTHOR>
         * @date 2025/5/15
         */
        async queryAcctTravelInfo(flowObjId){
            const data = await this.$http.post('action/link/travelReward/queryById', {
                id: flowObjId
            })
            if (data.success && data.result) {
                this.acctTravelInfo = data.result
            }
        },
        /**
         * @description 根据客户id获取旅游终端详情
         * <AUTHOR>
         * @date 2025/5/13
         */
        async queryTravelByAcctId() {
            const data = await this.$http.post('action/link/travelReward/queryByAcctId', {
                acctId: this.pageParam.acctData.id
            })
            if (data.success && data.result) {
                this.acctTravelInfo = data.result
            }
        },
        /**
         * @description 校验是否必填
         * <AUTHOR>
         * @date 2025/5/13
         */
        check() {
            console.log(this.$refs["basic-form"].isSubstandard , this.$refs["indicator-info"].isSubstandard)
            if ((this.$refs["basic-form"].isSubstandard || this.$refs["indicator-info"].isSubstandard) && !this.comments) {
                this.$message.error('当综合指标、对应标准指标数据、终端总体销售额存在标红时，特殊情况备注为必填');
                return false;
            }
            return true
        },
        /**
         * @description 提交
         * <AUTHOR>
         * @date 2025/5/13
         */
        async submit() {
            if(!this.check()) return
            const data = await this.$http.post('/action/link/travelReward/submit', {
                id: this.acctTravelInfo.id,
                acctId: this.acctTravelInfo.acctId,
                partnerBrand: this.selectStandard.brand,
                tourStandards: this.selectStandard.standard,
                quota: this.selectStandard.person,
                specialRemark: this.comments,
            })
            if (data.success && data.result) {
                this.$message.success('提交成功');
                this.$nav.back()
            }
        },
        selectStander() {
            this.showStandardDialog = true
        },
        async confirmSelect() {
            if (!this.selectStandard.brand) {
                this.$message.error('请选择合作品牌');
                return;
            }
            if (!this.selectStandard.standard) {
                this.$message.error('请选择旅游标准');
                return;
            }
            if (!this.selectStandard.person) {
                this.$message.error('请填写对应人数');
                return;
            }
            this.standardResult[0].content = [
                {
                    label: '合作品牌',
                    value: await this.$lov.getNameByTypeAndVal('COOPERATIVE_BRAND', this.selectStandard.brand)
                },
                {
                    label: '旅游标准',
                    value: await this.$lov.getNameByTypeAndVal('TRAVEL_REWARD', this.selectStandard.standard)
                },
                {
                    label: '名额',
                    value: this.selectStandard.person
                }
            ]
            this.isSelectStander = true
            this.showStandardDialog = false;
        }
    }
}
</script>

<style lang="scss">
.hundred-cities-tour-select-page {
    font-size: 28px;

    .row-content {
        margin: 0 20px;
        border-radius: 20px;

        .link-form-item {
            .link-item {
                padding: 10px 24px;
            }
        }

        .row-title {
            color: #262626;
            font-weight: bold;
        }

        .line {
            padding-bottom: 14px;
            border-bottom: 5px solid #f2f2f2;
        }

        .is-required {
            padding-left: 18px;

            &:before {
                content: '*';
                color: #FF5A5A;
                font-size: 32px;
                text-align: center;
                width: 24px;
                position: absolute;
                left: 34px;
            }
        }
    }

    .comments {
        margin-bottom: 20px;

        .comments-title {
            padding: 20px;
            font-size: 28px;
        }

        .comments-wrap {
            margin: 0 20px 20px 20px;
            background: #ffffff;
            border-radius: 20px;

            .link-textarea {
                padding: 0 !important;
            }
        }
    }

    .approval-comments{
        margin-bottom: 450px;
    }

    .standard {
        .standard-title {
            font-size: 30px;
            width: 100%;
            text-align: center;
        }

        .confirm-btn {
            width: 100%;
        }
    }

    .standard-content-item {
        .link-input {
            padding: 0 !important;

            .link-input-inner {
                padding: 0 !important;
            }
        }
    }


    .standard-result {
        margin: 20px;
        background: #ffffff;
        border-radius: 20px;
        padding-bottom: 20px;
    }

}
</style>
