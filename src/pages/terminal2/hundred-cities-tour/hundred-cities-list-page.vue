<template>
    <link-page class="hundred-cities-list-page">
        <link-auto-list :option="autoOption" hideCreateButton :searchInputBinding="{props:{placeholder:'审批名称/申请人'}}">
            <view slot="searchRight" class="search-container">
                <link-filter v-model="filterOption" @change="handleChange"/>
            </view>
            <link-filter-group slot="filterGroup">
                <link-filter-item label="申请时间(升序)" :param="{sort:{field:'created',desc:false}}"/>
            </link-filter-group>
            <template slot-scope="{data,index}">
                <view class="item-container" @tap="gotoDetail(data)">
                    <view class="row-1">
                        <view class="row-1__left">
                            <view class="name">{{data.flowName}}</view>
                        </view>
                        <view class="row-1__right">
                            <status-button :type="data.flowStatus.toLowerCase()" :label="data.flowStatus| lov('FLOW_STATUS')"></status-button>
                        </view>
                    </view>
                    <view class="list-item">
                        <view class="label">客户名称</view>
                        <view class="data">{{data.actNum}}</view>
                    </view>
                    <view class="list-item">
                        <view class="label">客户编码</view>
                        <view class="data">{{data.actNum}}</view>
                    </view>
                    <view class="list-item">
                        <view class="label">申请人</view>
                        <view class="data">{{data.actNum}}</view>
                    </view>
                    <view class="list-item">
                        <view class="label">当前审批人</view>
                        <view class="data">{{data.actNum}}</view>
                    </view>
                    <view class="list-item">
                        <view class="label">申请时间</view>
                        <view class="data">{{data.actNum}}</view>
                    </view>
                </view>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
import StatusButton from "../../lzlj/components/status-button";
import {getFiltersRaw} from "link-taro-component";
export default {
    name: "hundred-cities-list-page",
    components: {StatusButton},
    data(){
        return{
            autoOption: new this.AutoList(this, {
            url: {
                queryByExamplePage: '/export/link/flow/v2/queryMyFlowePage'
            },
            // sortField: 'lastUpdated',
            // sortDesc: 'desc',
            param: {
                filtersRaw: [
                ]
            },
            searchFields: [],
            sortOptions: null,
            hooks: {
            }
        }),
            filterOption: [{label: '审批状态', field: 'flowStatus', type: 'lov', lov: 'FLOW_STATUS'}]
        }
    },
    methods:{
        /**
         *  @description: 筛选数据
         *  @author: 马晓娟
         *  @date: 2020/10/15 20:57
         */
        async handleChange(val) {
            this.autoOption.option.param.filtersRaw = getFiltersRaw(val);
            await this.autoOption.methods.reload();
        },
        gotoDetail(data){
            this.$nav.push('pages/terminal2/hundred-cities-tour/hundred-cities-tour-select-page.vue', {
                acctData: data
            });
        }
    }
}
</script>

<style lang="scss">
.hundred-cities-list-page {
    .item-container {
        font-size: 24px;
        letter-spacing: 0;
        text-align: center;
        line-height: 24px;
        color: #262626;
        background: #ffffff;
        margin: 20px;
        padding: 20px;
        border-radius: 8px;

        .row-1 {
            display: flex;
            justify-content: space-between;

            &__left {
                width: 70%;
                text-align: left;

                .name {
                    font-size: 32px;
                    letter-spacing: 0;
                    line-height: 32px;
                    font-weight: bold;
                    margin-bottom: 24px;
                }
            }

            &__right {
                width: 30%;
                display: flex;
                justify-content: flex-end;
            }
        }

        .list-item {
            display: flex;
            padding: 14px 0;
            align-items: center;
            font-size: 28px;
            color: #262626;
            letter-spacing: 0;
            line-height: 28px;
            text-align: left;
            &__left{
                display: flex;
            }
            .label{
                color: #8C8C8C;
                width: auto;
                margin-right: 18px;
            }
            .data{
                color: #262626;
            }
        }
        .list-item-other{
            display: flex;
            justify-content: space-between;
        }
    }
}
</style>
