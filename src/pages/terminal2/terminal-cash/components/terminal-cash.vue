<template>
    <view class="terminal-cash">
        <!-- 积分类型 -->
        <lnk-taps v-model="tapActive"
                  v-if="tapOption.length"
                  :padding="tapOption.length > 4"
                  :taps="tapOption"
                  @switchTab="onTap">
        </lnk-taps>
        <view v-if="tapActive.val">
            <view class="tap-item">
                <view>
                    {{tapActive.name}}<text v-if="isWriteOff">总</text>余额
                </view>
                <view>{{(Number(tapActive.value) || 0).toFixed(2)}}</view>
            </view>

            <block v-if="isWriteOff">
                <view class="tap-item">
                    <view>核销</view>
                    <view>{{tapActive.alreadyValue || 0}}瓶</view>
                </view>
                <view class="tap-item" v-if="tapActive.pointTypeId !== 'LBNWriteoff'">
                    <view>手续费</view>
                    <view>{{tapActive.premium || 0}}瓶</view>
                </view>
            </block>

           <!--  <block v-else>
                <view class="tap-item">
                    <view>可兑付</view>
                    <view>{{Number(tapActive.remainValue || 0).toFixed(2)}}</view>
                </view>
                <view class="tap-item">
                    <view>暂未开放兑付</view>
                    <view>{{Number(Number(tapActive.value || 0) - Number(tapActive.remainValue || 0)).toFixed(2)}}</view>
                </view>
            </block> -->
        </view>
        <view class="btn-wrap">
            <link-button v-if="tapActive.operateStatus" @tap="goCash">
                {{canCash ? '兑付' : ''}}
            </link-button>
        </view>
    </view>

</template>

<script>
import LnkTaps from '../../../core/lnk-taps/lnk-taps.vue';

export default {
    name: 'terminal-cash',
    components: {LnkTaps},
    props: {
        acctData: Object
    },
    data() {
        return {
            tapActive: {},
            tapOption: [],
            tapActiveNum: 0,
            // 头曲积分是否允许兑付
            tqWriteOffFlag: false,
        }
    },
    computed: {
        isWriteOff() {
            return this.tapActive.purpose === 'writeoff';
        },
        canCash() {
            return ['deduction', 'prize', 'writeoff', 'voucher', 'coupon'].includes(this.tapActive.purpose);
        }
    },
    async created() {
        await this.queryTaps();
        const tqWriteOffFlag = await this.$utils.getCfgProperty('QDHY_WRITE_OFF_FLAG');
        this.tqWriteOffFlag = tqWriteOffFlag !== 'Y';
        // 刷新页面数据
        this.$bus.$on('refreshCashData', data => {
            this.queryTaps('refresh');
        })
    },
    methods: {
        /**
         * 兑付
         * <AUTHOR>
         * @date    2024/11/27 14:30
         */
        goCash() {
            if (!this.canCash) return;
            if (this.tapActive.pointType === 'TQPoint' && this.acctData.acctType === 'Terminal' && this.tqWriteOffFlag) {
                this.$dialog.confirm('暂未开通头曲积分终端兑付', '提示', {
                    cancelButton: false
                });
            } else {
                let param = {
                    tapActive: this.tapActive,
                    acctData: this.acctData
                }
                this.$nav.push('/pages/terminal2/terminal-cash/new-cash-page', param);
            }
        },
        /**
         * 获取积分类型
         * <AUTHOR>
         * @date    2024/11/27 9:50
         */
        async queryTaps() {
            try {
                const {success, data} = await this.$http.post('/loyalty/loyalty/member/terminalPortalPoint', {
                    companyCode: this.acctData.mdmOrgCode,
                    acctCode: this.acctData.acctCode,
                    acctType: this.acctData.acctType || 'Terminal'
                });
                if (success) {
                    let seq = 0;
                    let tapOption = data.filter((item) => item.purpose !== 'cash');
                    tapOption = tapOption.sort((a, b) => {
                        return a.index - b.index
                    });
                    this.tapOption = tapOption.map((item) => ({
                        ...item,
                        // 积分类型
                        val: item.pointType,
                        seq: seq++
                    }));
                    this.tapActive = this.tapOption[this.tapActiveNum];
                }
            } catch (e) {

            }
        },
        /**
         * 切换tab
         * <AUTHOR>
         * @date    2024/11/27 9:50
         */
        onTap(item) {
            this.tapActiveNum = item.seq;
        }
    }
}

</script>

<style lang="scss">
.terminal-cash {

    .lnk-tabs-container {
        margin-bottom: 24px;

        .lnk-tabs {
            position: static;
            border: none;
            z-index: 1 !important;

            .active {
                color: #6051F6;
                font-weight: bold;
            }

            .line {
                background-color: #6051F6 !important;
            }
        }
    }

    .tap-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 50px 32px;
        font-size: 28px;
    }

    .btn-wrap {
        display: flex;
        justify-content: center;
    }
}
</style>
