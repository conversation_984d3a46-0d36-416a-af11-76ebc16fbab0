<template>
    <link-page class="new-cash-page">
        <link-form :value="formData" ref="form">
            <link-form-item label="兑付备注" field="comments" vertical :required="commentsRequired">
                <link-textarea v-model="formData.comments"/>
            </link-form-item>
        </link-form>

        <view class="cash-list-title">可兑付列表({{ cashList.length }})</view>
        <link-checkbox-group v-model="chooseIds">
            <view class="cash-list"
                  v-for="(data, index) in cashList"
                  :key="data.id">
                <item :data="data" :key="data.productId" :arrow="false" class="cash-item">
                    <template #note>
                        <link-checkbox :val="data.id" toggleOnClickItem @tap="chooseItem(data)"/>
                        <view>
                            <view class="prod-name">{{ data.productName }}</view>
                            <view class="cash-row-item" @tap.stop="chooseProd(cashCopyList[index].productId, index, data)">
                                <view class="list-label">物料号</view>
                                <view class="list-value">
                                    <view class="choose-prod">
                                        <text>{{ data.productCode }}</text>
                                        <link-icon icon="icon-right" v-if="isMore(data)"/>
                                        <link-icon v-if="data.isSelectProd && cashCopyList[index].productCode && data.productCode !== cashCopyList[index].productCode"
                                                   icon="icon-close"
                                                   @tap.stop="clearProdInfo(data, index)"/>
                                    </view>
                                    <view class="red" v-if="data.isSelectProd && cashCopyList[index].productCode && data.productCode !== cashCopyList[index].productCode">
                                        原始物料号:{{cashCopyList[index].productCode}}
                                    </view>
                                </view>
                            </view>
                            <view v-if="!isWriteOff" class="cash-row-item">
                                <view class="list-label">
                                    {{pageParam.tapActive.purpose === 'deduction' ? '积分余额' : '剩余数量' }}
                                </view>
                                <view class="list-value">{{ data.point }}</view>
                            </view>
                            <view v-if="isWriteOff" class="cash-row-item">
                                <view class="list-label">总核销数量</view>
                                <view class="list-value">{{ data.writeOffNum }}</view>
                            </view>
                            <view v-if="isWriteOff" class="cash-row-item">
                                <view class="list-label">已兑付数量</view>
                                <view class="list-value">{{ data.cashAlreadyNum }}</view>
                            </view>
                            <view v-if="isWriteOff" class="cash-row-item">
                                <view class="list-label">兑付中数量</view>
                                <view class="list-value">{{ data.cashingNum }}</view>
                            </view>
                            <view v-if="isWriteOff" class="cash-row-item">
                                <view class="list-label">未兑付数量</view>
                                <view class="list-value">{{ data.cashWaitNum }}</view>
                            </view>
                            <view class="cash-row-item" v-if="data.dealerName">
                                <view class="list-label">经销商</view>
                                <view class="list-value">{{ data.dealerName }}</view>
                            </view>
                            <view class="cash-row-item" v-if="data.distributorName">
                                <view class="list-label">分销商</view>
                                <view class="list-value">{{ data.distributorName }}</view>
                            </view>
                            <view class="cash-row-item" v-if="data.supply.length === 0">
                                <view class="list-label">供货关系</view>
                                <view class="list-value red">{{ data.supplyResult }}</view>
                            </view>
                            <view class="cash-row-item" @tap.stop v-if="data.supply.length !== 0">
                                <link-number v-if="isDeduction"
                                             class="container-input"
                                             :precision="10"
                                             hideButton
                                             :nativeAttrs="{
                                                placeholder: '请输入兑付积分'
                                             }"
                                             :max="Number(data.point)"
                                             v-model="data.deductionIntegral">
                                </link-number>
                                <link-number v-else
                                             class="container-input"
                                             :precision="10"
                                             hideButton
                                             :nativeAttrs="{
                                                placeholder: '请输入兑付数量'
                                             }"
                                             :max="isWriteOff ? Number(data.cashWaitNum) : Number(data.point)"
                                             v-model="data.deductionIntegral">
                                </link-number>
                            </view>
                        </view>
                    </template>
                </item>
            </view>
        </link-checkbox-group>
        <link-sticky>
            <link-checkbox-group v-model="allSelectFlag" class="select-all">
                <item :arrow="false">
                    <link-checkbox val="all" slot="thumb" toggleOnClickItem @tap="selectAll"/>
                    <text slot="title">全选</text>
                </item>
            </link-checkbox-group>
            <link-button block autoLoading @tap="submitCash">提交申请</link-button>
        </link-sticky>

        <!-- 选择通兑产品弹窗 -->
        <link-dialog ref="prodDialog" noPadding position="bottom" class="dialog-bottom"
                     height="70vh" borderRadius="32rpx 32rpx 0 0">
            <view class="model-title">
                <view class="title">实际兑付产品</view>
                <view class="iconfont icon-close1" @tap="$refs.prodDialog.hide()"></view>
            </view>
            <link-search-input v-model="searchText" @change="searchProd" placeholder="请搜索物料号/产品编号"/>
            <scroll-view :scroll-y="true" style="height: calc(100% - 220px)">
                <block v-if="prodList.length">
                    <view v-for="(data) in prodList" :key="data.prodId">
                        <link-radio-group v-model="chooseProdId">
                            <item :arrow="false" :key="data.prodId" :title="data.prodName"
                                  :data="data" :note="'物料号' + data.prodCode">
                                <link-checkbox :val=data.prodId slot="thumb" toggleOnClickItem/>
                            </item>
                        </link-radio-group>
                    </view>
                </block>
                <view class="no-data" v-else>暂无数据</view>
            </scroll-view>
            <view class="blank"></view>
            <link-sticky class="bottom-btn">
                <link-button block autoLoading @tap="confirmProd">确认</link-button>
            </link-sticky>
        </link-dialog>
    </link-page>
</template>

<script>
export default {
    name: 'new-cash-page',
    data() {
        const userInfo = this.$taro.getStorageSync('token').result;
        const cashListOption = new this.AutoList(this, {
            module: 'terminal/terminal/dcnxIntegralDeduction',
            url: {
                queryByExamplePage: '/exportterminal/terminal/dcnxIntegralDeduction/queryPointDeductionBalanceInfo',
            },
            param: () => ({
                companyCode: this.pageParam.acctData.mdmOrgCode,
                acctType: this.pageParam.acctData.acctType,
                acctCode: this.pageParam.acctData.acctCode,
                pointTypeId: this.pageParam.tapActive.pointTypeId
            })
        });
        return {
            userInfo,
            cashListOption,
            formData: {},
            chooseIds: [],
            configIdList: [],
            cashList: [],
            cashCopyList: [],
            prodList: [],
            chooseProdId: '',
            prodListForSearch: [],
            selectedProdId: '',
            selectedProdIndex: 0,
            exchangeConfigId: '',
            // 兑付备注是否必填
            commentsRequired: false,
            exchangeFlag: false,
            prodDialogShow: false,
            allSelectFlag: [],
            searchText: '',
            showWriteoff: false // 是否走的核销通兑配置
        }
    },
    computed: {
        isWriteOff() {
            return this.pageParam.tapActive.purpose === 'writeoff';
        },
        isDeduction() {
            return this.pageParam.tapActive.purpose === 'deduction';
        }
    },
    async created() {
        const title = `新建${
            this.pageParam.tapActive.purpose === 'writeoff'
            ? '核销'
            : this.pageParam.tapActive.purpose === 'prize'
            ? '奖品'
            : this.pageParam.tapActive.purpose === 'voucher'
            ? '抵扣券'
            : this.pageParam.tapActive.purpose === 'coupon'
            ? '现金券'
            : '积分'
        }兑付`;
        this.$taro.setNavigationBarTitle({title: title});
        this.getCommentRequired();
        this.queryCashList();
    },
    methods: {
        /**
         * 全选
         * <AUTHOR>
         * @date    2024/12/3 10:37
         */
        selectAll() {
            this.$nextTick(() => {
                if (this.allSelectFlag.length) {
                    this.chooseIds = this.cashList.map(item => item.id);
                } else {
                    this.chooseIds = [];
                }
            });
        },
        /**
         * 搜索通兑产品
         * <AUTHOR>
         * @date    2024/12/3 0:24
         */
        searchProd() {
            const prodPickList = this.prodListForSearch.filter((item) =>
                item.prodName.indexOf(this.searchText) !== -1
                || item.prodCode.indexOf(this.searchText) !== -1)
            this.prodList = prodPickList;
        },
        /**
         * 确认通兑产品
         * <AUTHOR>
         * @date    2024/12/3 0:13
         */
        confirmProd() {
            this.$refs.prodDialog.hide();
            let data = this.prodList.filter(item => item.prodId === this.chooseProdId);
            if (!data.length) return;
            data = data[0];
            const index = this.selectedProdIndex;
            this.cashList[index].productId = data.prodId
            this.cashList[index].productCode = data.prodCode
            this.cashList[index].productName = data.prodName
            this.cashList[index].supply = data.supply
            this.cashList[index].queryWithBalanceId = data.id
            this.cashList[index].exchangeConfigIdFront = this.exchangeConfigId
            this.cashList[index].isSelectProd = true
            this.cashCopyList[index].productIdNow = data.prodId
            if (this.cashList[index].distributorName) this.cashList[index].distributorName = ''
            if (this.cashList[index].distributor) this.cashList[index].distributor = ''
            if (this.cashList[index].dealerName) this.cashList[index].dealerName = ''
            if (this.cashList[index].dealer) this.cashList[index].dealer = ''
            const supplyList = this.cashList[index].supply;
            if (supplyList.length === 0) {
                this.cashList[index].supplyResult = '供货关系不正确请检查'
            } else {
                for (let j = 0; j < supplyList.length; j++) {
                    switch (supplyList[j].type) {
                        case 'Distributor':
                            if (!!this.cashList[index].distributorName) break
                            else {
                                this.cashList[index].distributorName = supplyList[j].acctName
                                this.cashList[index].distributor = supplyList[j].acctId
                                break
                            }
                        case 'Dealer':
                            this.cashList[index].dealerName = supplyList[j].acctName
                            this.cashList[index].dealer = supplyList[j].acctId
                            break
                        default:
                            break
                    }
                }
            }
        },
        /**
         * 提交兑付
         * <AUTHOR>
         * @date    2024/12/2 14:40
         */
        async submitCash() {
            try {
                await this.$refs.form.validate();
                const chooseList = this.cashList.filter((item) => this.chooseIds.includes(item.id));
                if (!chooseList.length) {
                    this.$showError('请选择可兑付产品！');
                    return;
                }
                for (let i = 0; i < chooseList.length; i++) {
                    const selectedItem = chooseList[i];
                    const tip = this.isWriteOff || this.pageParam.tapActive.purpose === 'prize';
                    if (selectedItem.supply.length !== 0) {
                        if (!selectedItem.deductionIntegral) {
                            this.$showError( `请输入${tip ? '兑付数量' : '兑付积分'}！`);
                            return;
                        }
                        if (!(/^[0-9]+(.[0-9]{1,2})?$/).test(selectedItem.deductionIntegral)){
                            this.$showError(`请输入正确的${['prize', 'writeoff'].includes(this.pageParam.tapActive.purpose) ? '兑付数量' : '兑付积分'}，最多保留两位小数！`);
                            return;
                        }
                        if (this.isWriteOff
                            ? selectedItem.deductionIntegral > selectedItem.cashWaitNum
                            : selectedItem.deductionIntegral > selectedItem.point) {
                            this.$showError(`${tip ? '兑付数量不能大于未兑付数量！' : '兑付积分不能大于剩余积分！'}`)
                            selectedItem.deductionIntegral = ''
                            return;
                        }
                    }
                }
                await this.$dialog.confirm('确定要进行本次操作');
                const selectItems = this.$utils.deepcopy(chooseList);
                selectItems.forEach((item) => {
                    this.cashCopyList.some((cashItem) => {
                        if (item.productId === cashItem.productIdNow && item.isSelectProd) {
                            item.exchangeConfigId = item.exchangeConfigIdFront + '-' + item.queryWithBalanceId
                            return true;
                        } else {
                            return false;
                        }
                    });
                    item.pointTypeId = this.pageParam.tapActive.pointTypeId;
                    // 核销-通兑兑付新增入参
                    if (this.showWriteoff && item.isSelectProd) item.exchangeConfigId = item.exchangeConfigIdFront;
                    if (item.supply) delete item.supply;
                    if (item.hasOwnProperty('id')) delete item.id;
                    if (item.hasOwnProperty('dealerName')) delete item.dealerName;
                    if (item.hasOwnProperty('distributorName')) delete item.distributorName;
                    if (item.hasOwnProperty('isSelectProd')) delete item.isSelectProd;
                    if (item.hasOwnProperty('queryWithBalanceId')) delete item.queryWithBalanceId;
                    if (item.hasOwnProperty('exchangeConfigIdFront')) delete item.exchangeConfigIdFront;
                    if (!this.isWriteOff) delete item.oriProductName;
                })
                const {success, result} = await this.$http.post('/terminal/terminal/dcnxIntegralDeduction/applyIntegralDeductionV3', {
                    companyCode: this.pageParam.acctData.mdmOrgCode,
                    acctCode: this.pageParam.acctData.acctCode,
                    acctType: this.pageParam.acctData.acctType || 'Terminal',
                    username: this.userInfo.username,
                    fstName: this.userInfo.firstName,
                    items: selectItems,
                    remark: this.formData.comments
                });
                if (success) {
                    this.$message.success('提交成功！')
                    this.$nav.back();
                    this.$bus.$emit('refreshCashData');
                }
            } catch (e) {
                console.log('e', e);
            }
        },
        /**
         * 清除选择产品
         * <AUTHOR>
         * @date    2024/12/2 10:15
         */
        clearProdInfo(data, index) {
            if (this.cashList[index].productId !== this.cashCopyList[index].productId) {
                this.cashList[index].productId = this.cashCopyList[index].productId
                this.cashList[index].productCode = this.cashCopyList[index].productCode
                this.cashList[index].productName = this.cashCopyList[index].productName
                this.cashList[index].supply = this.cashCopyList[index].supply
                this.cashList[index].queryWithBalanceId = ''
                this.cashList[index].exchangeConfigIdFront = ''
                this.cashList[index].isSelectProd = false
                this.cashCopyList[index].productIdNow = ''
                const supplyList = this.cashCopyList[index].supply
                if (this.cashList[index].distributorName) this.cashList[index].distributorName = ''
                if (this.cashList[index].distributor) this.cashList[index].distributor = ''
                if (this.cashList[index].dealerName) this.cashList[index].dealerName = ''
                if (this.cashList[index].dealer) this.cashList[index].dealer = ''
                if (supplyList.length === 0) {
                    this.cashList[index].supplyResult = '供货关系不正确请检查'
                } else {
                    for (let j = 0; j < supplyList.length; j++) {
                        switch (supplyList[j].type) {
                            case 'Distributor':
                                if (!this.cashList[index].distributorName) {
                                    this.cashList[index].distributorName = supplyList[j].acctName
                                    this.cashList[index].distributor = supplyList[j].acctId
                                }
                                break
                            case 'Dealer':
                                this.cashList[index].dealerName = supplyList[j].acctName
                                this.cashList[index].dealer = supplyList[j].acctId
                                break
                            default:
                                break
                        }
                    }
                }
            }
        },
        /**
         * 选择兑付产品
         * <AUTHOR>
         * @date    2024/11/29 16:29
         */
        chooseItem(data, index) {
            this.$nextTick(() => {
                this.allSelectFlag = this.chooseIds.length === this.cashList.length ? ['all'] : [];
            });
        },
        /**
         * 获取兑付产品列表
         * <AUTHOR>
         * @date    2024/11/29 11:54
         */
        async queryCashList() {
            try {
                const {success, rows, configIdList, exchangeFlag} = await this.$http.post('/exportterminal/terminal/dcnxIntegralDeduction/queryPointDeductionBalanceInfo', {
                    companyCode: this.pageParam.acctData.mdmOrgCode,
                    acctType: this.pageParam.acctData.acctType,
                    acctCode: this.pageParam.acctData.acctCode,
                    pointTypeId: this.pageParam.tapActive.pointTypeId
                });
                if (success) {
                    rows.forEach((row, index) => {
                        row.deductionIntegral = ''
                        row.id = index
                        const supplyList = row.supply
                        if (supplyList.length === 0) {
                            row.supplyResult = '供货关系不正确请检查'
                        } else {
                            for (let j = 0; j < supplyList.length; j++) {
                                switch (supplyList[j].type) {
                                    case 'Distributor':
                                        if (!row.distributorName) {
                                            row.distributorName = supplyList[j].acctName
                                            row.distributor = supplyList[j].acctId
                                            break
                                        }
                                    case 'Dealer':
                                        row.dealerName = supplyList[j].acctName
                                        row.dealer = supplyList[j].acctId
                                        break
                                    default:
                                        break
                                }
                            }
                        }
                    })
                    this.cashList = rows;
                    this.cashCopyList = this.$utils.deepcopy(rows);
                    this.exchangeFlag = exchangeFlag;
                    configIdList && (this.configIdList = configIdList);
                    this.cashList.forEach((item) => {
                        item.isSelectProd = false;
                        item.oriProductId = item.productId;
                        item.oriProductName = item.productName;
                    });
                }
            } catch (e) {

            }
        },
        /**
         * 选择通兑产品
         * <AUTHOR>
         * @date    2024/11/28 17:08
         */
        async chooseProd(productId, index, selectData) {
            if (!this.isMore(selectData)) return;
            this.selectedProdId = productId;
            this.selectedProdIndex = index;
            // 获取积分奖品兑付-走单向产品通兑的积分类型id
            const data = await this.$utils.getCfgProperty('DCNX_OTHER_EXCHANGE_POINT_TYPE');
            // 核销通兑区分于积分、奖品通兑
            if (this.isWriteOff || data.includes(this.pageParam.tapActive.pointTypeId)) {
                this.showWriteoff = true;
                this.getWriteOffProduct(productId);
            } else {
                this.getPointsProduct(productId);
            }
        },
        /**
         * 积分、奖品获取通兑产品
         * <AUTHOR>
         * @date    2025/04/10
         */
        async getPointsProduct(productId) {
            try {
                const {success, id} = await this.$http.post('/terminal/link/exchangeConfigHead/queryMatchConfigHeadForProdQw', {
                    prodId: productId,
                    accntId: this.pageParam.acctData.id,
                    companyId: this.pageParam.acctData.mdmOrgCode,
                    organizationId: this.pageParam.acctData.orgId,
                    ids: this.configIdList
                });
                if (success) {
                    if (!id) {
                        this.$showError('暂无其他可兑付产品！');
                        return;
                    }
                    this.exchangeConfigId = id;
                    const res = await this.$http.post('/terminal/link/exchangeConfigProd/queryWithBalanceInfoPageQw', {
                        accntId: this.pageParam.acctData.id,
                        headId: id,
                        prodId: productId
                    })
                    if (res.success && res.rows) {
                        this.prodList = res.rows
                    } else {
                        this.prodList = []
                    }
                    this.prodListForSearch = this.$utils.deepcopy(this.prodList)
                    this.chooseProdId = '';
                    this.$refs.prodDialog.show();
                }
            } catch (e) {
                console.log('e', e)
            }
        },
         /**
         * 核销获取通兑产品
         * <AUTHOR>
         * @date    2025/04/10
         */
        async getWriteOffProduct(productId) {
            try {
                const {success, id, row} = await this.$http.post('/terminal/link/writeOffExchangeProd/queryMatchConfigForProd', {
                    oriProdId: productId,
                    ids: this.configIdList,
                    accntId: this.pageParam.acctData.id,
                    organizationId: this.pageParam.acctData.orgId
                });
                if (success) {
                    if (!id) {
                        this.$showError('暂无其他可兑付产品！');
                        return;
                    }
                    this.exchangeConfigId = id;
                    if (row) {
                        this.prodList = [row].map((item) => 
                            ({
                                prodId: item.actualProdId,
                                prodCode: item.actualProdCode,
                                prodName: item.actualProdName,
                                ...item
                            })
                        )
                    }
                    this.prodListForSearch = this.$utils.deepcopy(this.prodList)
                    this.chooseProdId = '';
                    this.$refs.prodDialog.show();
                }
            } catch(err) {
                console.log('获取核销获取通兑产品失败:' + err);
            }
        },
        /**
         * 兑付备注是否必填
         * <AUTHOR>
         * @date    2024/11/27 15:35
         */
        async getCommentRequired() {
            const {success, result} = await this.$http.post('/loyalty/loyalty/link/redemptionConf/queryForWxApp', {
                // 品牌公司id
                companyId: this.pageParam.acctData.companyId,
                // 积分类型/核销类型Id
                pointTypeId: this.pageParam.tapActive.pointTypeId,
                // 平台
                platform: 'QWAPP'
            });
            if (success) {
                this.commentsRequired = result;
            }
        },
        /**
         * 是否显示更多按钮
         * <AUTHOR>
         * @date    2025/04/21
         */ 
        isMore(data) {
            if (this.showWriteoff) {
                return this.exchangeFlag && !data.isSelectProd
            } else {
                return this.exchangeFlag
            }
        }
    }
}
</script>

<style lang="scss">
.new-cash-page {
    padding-bottom: 60px;

    .cash-list-title {
        font-size: 30px;
        color: #333333;
        line-height: 44px;
        padding: 28px 0 0 32px;
    }

    .red {
        color: red;
    }

    .cash-list {
        margin: 24px;

        .cash-item {
            border-radius: 20px;

            .link-item-note {
                display: flex;
                align-items: center;

                .link-checkbox {
                    margin-right: 20px;
                }
            }

            .prod-name {
                font-size: 32px;
                color: #212223;
                line-height: 48px;
                text-align: left;
                font-weight: bold;
                padding: 6px 0 8px 0;
            }

            .cash-row-item {
                display: flex;
                flex: 1;
                font-size: 28px;
                color: #999999;
                line-height: 44px;
                font-weight: 400;
                text-align: left;
                padding-top: 8px;

                .list-label {
                    width: 160px;
                }

                .list-value {
                    padding-left: 24px;
                    color: #333333;
                    flex: 1;

                    .choose-prod {
                        margin-bottom: 8px;

                        .icon-right {
                            color: #999;
                        }

                        .icon-close {
                            color: #999;
                            margin-left: 50px;
                        }
                    }
                }

                .container-input {
                    padding-left: 10px;
                    flex: 1;
                    background: #f8f8f8;
                    border-radius: 8px;
                    height: auto !important;
                    width: auto !important;

                    input {
                        text-align: left;
                        display: inline-block;
                        height: 80px;
                        line-height: 80px;
                    }
                }
            }
        }
    }

    .dialog-bottom {

        .model-title {
            display: flex;

            .title {
                font-size: 32px;
                color: #262626;
                text-align: center;
                line-height: 96px;
                height: 96px;
                flex: 1;
                margin-left: 76px;
            }

            .icon-close1 {
                color: #ddd;
                font-size: 48px;
                line-height: 96px;
                height: 96px;
                margin-right: 28px;
            }
        }

        .no-data {
            width: 100%;
            text-align: center;
            color: #999;
            margin: 150px 0;
        }
    }

    .select-all {
        width: 180px;
    }
}
</style>
