<!--
 融合终端详情
 <AUTHOR>
 @date	2023/11/23 17:52
-->
<template>
    <link-page class="parent-terminal-detail-page">
        <!--顶部背景-->
        <view class="top-container">
            <navigation-bar :backVisible="true"
                            :navBarAllHeight="200"
                            :backgroundImg="$imageAssets.homeMenuBgImage"
                            title="客户详情"
                            titleColor="#ffffff"
                            navBackgroundColor="transparent">
                <view class="top-content">
                    <view class="store-image" @tap="previewStoreUrl(clientDetails)">
                        <image :src="clientDetails.storeUrl || $imageAssets.terminalDefaultImage"></image>
                    </view>
                    <view class="store-content-cover">
                        <view class="store-content-code">{{clientDetails.acctCode}}</view>
                        <view class="title-level-code">
                            <view class="store-content-top">
                                <!--【客户一级分类】为“终端Terminal”的时候显示storeSigns字段-->
                                <view class="store-title" v-if="clientDetails.acctType === 'Terminal'">{{clientDetails.acctName}}</view>
                                <!--【客户一级分类】为“分销商Distributor”时展示billTitle字段-->
                                <view class="store-title" v-if="clientDetails.acctType === 'Distributor'">{{clientDetails.acctName || clientDetails.billTitle}}</view>
                            </view>
                        </view>
                        <view class="store-content-middle">
                            <!-- @edit by 邓佳柳 2024/11/28 四色标签 -->
                            <color-tag :value="clientDetails.fourColorLabel" v-if="clientDetails.fourColorLabel"/>
                            <view class="store-type" v-if="clientDetails.financingFlag">贷 | {{clientDetails.financingFlag | lov('YR_FINANCING_FLAG')}}</view>
                            <view class="store-type" v-if="clientDetails.acctType">{{clientDetails.acctType | lov('ACCT_TYPE')}}</view>
                            <view class="store-type" v-if="clientDetails.strategicFlag && clientDetails.strategicFlag === 'Y'">{{clientDetails.strategicFlag | lov('STRATEGIC_TAG')}}</view>
                            <template v-if="clientDetails.protocolBrand">
                                <view class="store-type" v-for="(item,index) in clientDetails.protocolBrand.split(',')">{{item | lov('BRAND_COM_NAME')}}</view>
                            </template>
                        </view>
                    </view>
                </view>
            </navigation-bar>
        </view>
        <view class="content">
            <!--title-->
            <view class="top-blank" v-if="tapsFix"
                  :style="{'height': duration + 'px','line-height': duration + 'px', 'padding-top': statusBarHeight + 'rpx'}">
                {{clientDetails.acctName}}
            </view>
            <!--状态栏-->
            <link-sticky top :duration="this.$device.isIphoneX ? duration * 2 - 12 : duration * 2 + 16">
                <view class="tap-container">
                    <view class="lnk-tabs">
                        <view class="lnk-tabs-item" :class="{'active': tab.val === tapsActive.val}"
                              v-for="(tab, index) in templateData" :key="index" @tap="switchTab(tab)">
                            <view class="label-name" style="width: 87px;">{{tab.name}}</view>
                            <view class="line" v-if="tab.val === tapsActive.val"></view>
                        </view>
                    </view>
                    <link-dropdown class="dropdown" v-model="showDropdownFlag">
                        <view class="iconfont dropdown-icon" :class="showDropdownFlag ?'icon-close':'icon-appstore'"></view>
                        <view slot="dropdown" class="dropdown-container">
                            <view v-for="(item, index) in templateData" :key="index" class="menu-item" @tap="switchTab(item, 'dropdown')">
                                <view class="iconfont menu-icon"></view>
                                <view class="menu-name">{{item.name}}</view>
                            </view>
                        </view>
                    </link-dropdown>
                </view>
            </link-sticky>
            <!-- 基础信息 -->
            <view class="card-content" v-if="tapsActive.val === 'basicInformation'">
                <view class="card-rows" v-if="basicInformationData.acctName">
                    <view class="card-rows-content">
                        <view class="label">门头名称</view>
                        <view class="value">{{basicInformationData.acctName}}</view>
                    </view>
                </view>
                <view class="card-rows" v-if="basicInformationData.billTitle">
                    <view class="card-rows-content">
                        <view class="label">营业执照名称</view>
                        <view class="value">{{basicInformationData.billTitle}}</view>
                    </view>
                </view>
                <view class="card-rows" v-if="basicInformationData.creditNo">
                    <view class="card-rows-content">
                        <view class="label">纳税人识别号</view>
                        <view class="value">{{basicInformationData.creditNo}}</view>
                    </view>
                </view>
                <view class="card-rows line" v-if="basicInformationData.addrDetailAddr">
                    <view class="card-rows-content">
                        <view class="label-address">门店地址</view>
                        <view class="value-address">{{basicInformationData.addrDetailAddr}}</view>
                    </view>
                </view>
            </view>
            <!-- 品牌公司终端 -->
            <view v-if="tapsActive.val === 'brandTerminal'">
                <link-auto-list :option="terminalListOption" :hideCreateButton="true" key="terminalListOption">
                    <template slot-scope="{data,index}">
                        <item :key="index + 'brand'" :data="data" :arrow="false" class="terminal-list-item" @tap="goToItem(data)">
                            <view class="terminal-list" slot="note">
                                <view class="media-list">
                                    <image class="media-list-logo" :src="data.storeUrl" @tap.stop="previewStoreUrl(data)" lazy-load="true"></image>
                                    <view class="store-content">
                                        <view class="store-content-top" v-if="data.acctType">
                                            <view class="store-title">{{data.childAcctName}}</view>
                                            <!--已认证-->
                                            <view class="store-level" v-if="data.acctStage === 'ykf'"><image :src="$imageAssets.storeStatusVerifiedImage"></image></view>
                                            <!--未认证-->
                                            <view class="store-level" v-if="data.acctStage === 'xk'"><image :src="$imageAssets.storeStatusUnverifiedImage"></image></view>
                                            <!--已失效-->
                                            <view class="store-level" v-if="data.acctStage === 'ysx'"><image :src="$imageAssets.storeStatusInvalidationImage"></image></view>
                                            <!--潜客-->
                                            <view class="store-level" v-if="data.acctStage === 'dkf' && !isYangShengOrYouXuan"><image :src="$imageAssets.storeStatusPotentialImage"></image></view>
                                        </view>
                                        <view class="store-content-middle">
                                            <view class="left">
                                                 <!-- @edit by 邓佳柳 2024/11/28 四色标签 -->
                                                <color-tag :value="data.childFourColorLabel" v-if="data.childFourColorLabel"/>
                                                <view class="store-type" v-if="data.financingFlag">贷 | {{data.financingFlag | lov('YR_FINANCING_FLAG')}}</view>
                                                <view class="store-type" v-if="data.acctType">{{data.acctType | lov('ACCT_TYPE')}}</view>
                                                <view class="store-type" v-if="data.acctCategory">{{data.acctCategory | lov('ACCNT_CATEGORY')}}</view>
                                                <view class="store-type" v-if="data.subAcctType">{{data.subAcctType | lov('SUB_ACCT_TYPE')}}</view>
                                                <view class="store-type" v-if="data.strategicFlag && data.strategicFlag === 'Y'">{{data.strategicFlag | lov('STRATEGIC_TAG')}}</view>
                                                <view class="store-type" v-if="data.doorSigns">{{data.doorSigns | lov('DOOR_SIGNS')}}</view>
                                                <view class="store-type" v-if="data.chainStoreFlag && data.chainStoreFlag === 'Y'">连锁终端</view>
                                                <view class="store-type" v-if="data.cumulativeSalesLevel">{{ '达成' + data.cumulativeSalesLevel }}</view>
                                                <view class="store-type" v-if="levelList.includes(data.acctLevel) || caplist.includes(data.capacityLevel)">
                                                    <text v-if="levelList.includes(data.acctLevel)">{{data.acctLevel | lov('ACCT_LEVEL')}}</text>
                                                    <text v-if="levelList.includes(data.acctLevel) && caplist.includes(data.capacityLevel)"> | </text>
                                                    <text v-if="caplist.includes(data.capacityLevel)">{{data.capacityLevel | lov('CAPACITY_LEVEL')}}</text>
                                                </view>
                                                <view class="store-type" v-if="data.codeMark">{{data.codeMark}}</view>
                                            </view>
                                        </view>
                                        <view class="store-content-representative">
                                            <view class="terminal-type">编码</view>
                                            <view class="terminal-name">{{data.childAcctCode}}</view>
                                        </view>
                                        <view class="store-content-representative">
                                            <view class="terminal-type-2">纳税人识别号</view>
                                            <view class="terminal-name">{{data.childCreditNo}}</view>
                                        </view>
                                        <view class="store-content-representative">
                                            <view class="terminal-type">店老板</view>
                                            <view class="terminal-name">{{data.contactName}}</view>
                                        </view>
                                        <view class="store-content-representative">
                                            <view class="terminal-type-2">店老板电话</view>
                                            <view class="terminal-name">{{data.mobilePhone}}</view>
                                        </view>
                                        <view class="store-content-address">
                                            <view class="store-address">{{data.salesmanBrandCom}}</view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </item>
                    </template>
                </link-auto-list>
            </view>
            <!-- 绑定融合终端 -->
            <view v-if="tapsActive.val === 'bindingTerminal'">
                <view class="prompt-info" v-if="bindingBtnFlag">
                    <view class="flex-container">
                        <link-icon icon="icon-warning-circle" class="icon"></link-icon>
                        <view class="text">左滑可解除绑定关系</view>
                    </view>
                </view>
                <link-auto-list :option="bindingTerminalOption" key="bindingTerminalOption">
                    <template slot-scope="{data,index}">
                        <link-swipe-action>
                        <link-swipe-option slot="option" @tap="deleteItem(data)" v-if="bindingBtnFlag">解绑</link-swipe-option>
                        <item :key="index + 'bind'" :data="data" :arrow="false" class="terminal-list-item">
                            <view class="terminal-list" slot="note">
                                <view class="media-list">
                                    <image class="media-list-logo" :src="data.storeUrl" @tap.stop="previewStoreUrl(data)" lazy-load="true"></image>
                                    <view class="store-content">
                                        <view class="store-content-top">
                                            <view class="store-title">{{data.childAcctName}}</view>
                                            <view class="status-container" :class="{'store-status-y': data.childAcctStatus === 'Y', 'store-status-n': data.childAcctStatus !== 'Y'}">
                                                {{ data.childAcctStatus | lov('WARN_MSG_TEMPLATE_STATUS') }}
                                            </view>
                                        </view>
                                        <view class="store-content-middle">
                                            <view class="left">
                                                <!-- @edit by 邓佳柳 2024/11/28 四色标签 -->
                                                <color-tag :value="data.fourColorLabel" v-if="data.fourColorLabel"/>
                                                <view class="store-type" v-if="data.financingFlag">贷 | {{data.financingFlag | lov('YR_FINANCING_FLAG')}}</view>
                                                <view class="store-type" v-if="data.acctType">{{data.acctType | lov('ACCT_TYPE')}}</view>
                                            </view>
                                        </view>
                                        <view class="store-content-representative" v-if="data.childAcctCode">
                                            <view class="terminal-type">客户编码</view>
                                            <view class="terminal-name">{{data.childAcctCode}}</view>
                                        </view>
                                        <view class="store-content-representative">
                                            <view class="terminal-type-2">纳税人识别号</view>
                                            <view class="terminal-name">{{data.childCreditNo}}</view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </item>
                        </link-swipe-action>
                    </template>
                </link-auto-list>
            </view>
            <!-- 四色标签记录 -->
            <template v-if="tapsActive.val === 'fourColorLable'">
                <view class="card-content" v-for="(item,index) in fourColorData" :key="index">
                    <view class="card-rows">
                        <view class="card-rows-content">
                            <view class="label time-text">{{ item.lastUpdated }}</view>
                        </view>
                    </view>
                    <view class="card-rows">
                        <view class="card-rows-content">
                            <view class="label code-text">{{ item.acctCode }}</view>
                        </view>
                    </view>
                    <view class="card-rows">
                        <view class="card-rows-content">
                            <view class="label code-text">{{ item.acctName }}</view>
                        </view>
                    </view>
                    <view class="card-rows">
                        <view class="card-rows-content">
                            <view class="label">{{ item.companyName }}</view>
                        </view>
                    </view>
                    <view class="card-rows">
                        <view class="card-rows-content-color">
                            <!-- @edit by 邓佳柳 2024/11/28 四色标签 -->
                            <color-tag :value="item.oldColor" v-if="item.oldColor"/>
                            <view class="store-color-type none-color" v-else>空值</view>
                            <link-icon icon="icon-ellipsis" class="icon"></link-icon>
                            <view class="text">转换为</view>
                            <link-icon icon="icon-ellipsis" class="icon"></link-icon>
                            <!-- @edit by 邓佳柳 2024/11/28 四色标签 -->
                            <color-tag :value="item.newColor" v-if="item.newColor"/>
                            <view class="store-color-type none-color" v-else>空值</view>
                        </view>
                    </view>
                </view>
            </template>
            <template v-if="tapsActive.val === 'collabora'">
                <collabora-msg :id='pageParam.data.id' :form='hisForm' @toEdit='toEdit'></collabora-msg>
            </template>
            <!-- 悬浮按钮  -->
            <link-fab-group @tap-fab-item="onTapFabItemByGroup">
                <link-fab-item icon="icon-dingjishenqing" label="百城旅游评选" @tap-icon="()=>selectHundredCityTours(basicInformationData)"></link-fab-item>
                <link-fab-item icon="icon-warning-circle" label="冻结" v-if="basicInformationData.acctStatus === 'Y'" @tap-icon="()=>onTapFabItem(basicInformationData)"></link-fab-item>
                <link-fab-item icon="icon-warning-circle" label="解冻" v-if="basicInformationData.acctStatus === 'N'" @tap-icon="()=>secureTapFabItem(basicInformationData)"></link-fab-item>
                <link-fab-item icon="icon-zhengce" label="绑定融合终端" v-if="bindingBtnFlag" @tap-icon="()=>secureIntegrateTerminals()"></link-fab-item>
                <link-fab-item icon="icon-zhengce" label="终端普查" v-if="" @tap-icon="()=>collabora()"></link-fab-item>
            </link-fab-group>
        </view>
    </link-page>
</template>

<script>
definePageConfig({
    navigationBarTitle: '融合终端详情',
    navigationStyle: "custom"
})
import collaboraMsg from './components/collabora-msg.vue'
import ColorTag from '@/pages/terminal2/components/ColorTag.vue';

export default {
    name: 'parent-terminal-detail-page',
    components:{
        collaboraMsg,
        ColorTag
    },
    data() {
        const terminalListOption = new this.AutoList(this, {
            module: 'action/link/accountParent',
            url: {
                queryByExamplePage: 'action/link/accountParent/queryBrandAccountPage',
            },
            pageSize: 10,
            param: () => {
                return {
                    oauth: 'ALL',
                    attr4: 'parent',
                    parentMainId: this.pageParam.data.id,
                    acctType: 'Terminal',
                    sort: 'created',
                    attr5 : 'distinct'
                }
            },
            hooks: {
                async afterLoad (data) {
                    data.rows.map(async (item) => {
                        if (!this.$utils.isEmpty(item.storePicPreKey) && item.acctType === 'Terminal') {
                            let urlData = this.$image.getSignedUrl(item.storePicPreKey);
                            this.$set(item, 'storeUrl', urlData);
                        } else {
                            this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
                        }
                    })
                },
            }
        });

        const bindingTerminalOption = new this.AutoList(this, {
            module: 'action/link/accountParent',
            url: {
                queryByExamplePage: 'action/link/accountParent/queryParentAccountPage',
            },
            param: () => {
                return {
                    oauth: 'ALL',
                    multiAcctMainId: this.pageParam.data.id,
                    source: 'child',
                    sort: 'created',
                }
            },
            hooks: {
                async afterLoad (data) {
                    data.rows.map(async (item) => {
                        if (!this.$utils.isEmpty(item.storePicPreKey) && item.acctType === 'Terminal') {
                            let urlData = this.$image.getSignedUrl(item.storePicPreKey);
                            this.$set(item, 'storeUrl', urlData);
                        } else {
                            this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
                        }
                    })
                }
            }
        });
        return {
            isCityCoordinator: false,    //是否城市协调人，默认不是
            hisForm:{}, //陈列检查历史
            levelList: [],
            caplist:[],
            bindingBtnFlag: false,    //是否能够查看绑定终端按钮以及提示
            showDropdownFlag: false,  //悬浮菜单
            bindingTerminalOption,
            terminalListOption,
            clientDetails: this.pageParam.data,
            duration: this.$device.isIphoneX ? 88 : 64,
            fixTop: 200,
            statusBarHeight: this.$device.systemInfo.statusBarHeight,
            tapsFix: false,
            templateData: [
                {name: '基础信息', val: 'basicInformation', seq: 1},
                {name: '品牌公司终端', val: 'brandTerminal', seq: 2},
                {name: '绑定融合终端', val: 'bindingTerminal', seq: 3},
                {name: '标签历史记录', val: 'fourColorLable', seq: 4},
                {name: '终端普查', val: 'collabora', seq: 5},
            ],
            tapsActive: {},
            basicInformationData: [],  //基础信息
            integraTerminalData: [],   //绑定融合终端信息
            fourColorData: []          //四色标签记录信息
        }
    },
     created() {
        this.tapsActive = this.templateData[0];
        this.getBasicInformationData();
        this.isOpenBindBtn();
        this.getTypeArray();
        this.getHisTory()
    },
    /**
     * 监听页面滚动函数
     * <AUTHOR>
     * @date 2020-08-05
     * @param e 距离顶部距离
     */
    onPageScroll(e) {
        this.tapsFix = e.scrollTop >= this.fixTop - this.duration;
    },
    methods: {
        toEdit(){
            this.collabora(true)
        },
        // 获取普查检查记录
        async getHisTory(){
            const url = 'action/link/terminalCensusCheck/queryByExamplePage';
            const param = {
                filtersRaw: [{id: 'acctId', property: 'acctId', value: this.pageParam.data.id, operator: '='}]
            }
            const {success, rows} = await this.$http.post(url, param)
            if(success){
                const newData = rows.find(i=>{
                    return i.acctId === this.pageParam.data.id
                })
                if(newData){
                    newData.collaboraBrands = newData.collaboraBrands.split(',')
                    newData.uncollaboraBrands = newData.uncollaboraBrands.split(',')
                }
                this.hisForm = newData || {id: null}
            }
        },
        onTapFabItemByGroup({e, label, icon}) {
        },

        async getTypeArray() {
            const list = await this.$lov.getLovByTypeArray(['ACCT_LEVEL','CAPACITY_LEVEL']);
            list[0].forEach(item => {
                this.levelList.push(item.val)
            });
            list[1].forEach(item => {
                this.caplist.push(item.val)
            });
        },
        /**
         * 获取是否能访问终端绑定按钮参数
         * <AUTHOR>
         * @date  2024-3-21 18:59:37
         * @param
         */
        async isOpenBindBtn() {
            const userInfo = this.$taro.getStorageSync('token').result;
            let codeStr = await this.$utils.getCfgProperty('OpenBindingBtn');
            codeStr = codeStr.split(',');
            this.bindingBtnFlag = codeStr.includes(userInfo.postnId);
            if(userInfo.positionType === 'CityCoordinator') {
                this.isCityCoordinator = true
            }
        },

        /**
         * 终端冻结功能
         * <AUTHOR>
         * @date 2024-3-20 17:05:50
         * @param row 当前终端基础信息
         */
        async onTapFabItem(row) {
            if(row.acctStatus === 'Y') {
                try {
                    const data  = await this.$http.post('action/link/accountParent/closeAccountParent', {id: row.id});
                    if(data.success) {
                        this.$message.success('融合终端状态已失效');
                        this.getBasicInformationData();
                    } else {
                        this.$showError(data.result);
                    }
                } catch (e) {
                    console.log(e);
                }
            }
        },
        /**
         * @description 百城旅游评选
         * <AUTHOR>
         * @date 2025/5/7
         */
        selectHundredCityTours(basicInformationData){
            this.$nav.push('pages/terminal2/hundred-cities-tour/hundred-cities-tour-select-page.vue', {
                acctData: basicInformationData,
                pageStatus: 'edit'
            });
        },
        /**
         * 终端生效功能
         * <AUTHOR>
         * @date 2024-3-20 17:05:50
         * @param row 当前终端基础信息
         */
        async secureTapFabItem(row) {
            if(row.acctStatus === 'N') {
                try {
                    const data  = await this.$http.post('action/link/accountParent/openAccountParent', {id: row.id});
                    if(data.success) {
                        this.$message.success('融合终端状态已生效');
                        this.getBasicInformationData();
                    } else {
                        this.$showError(data.result);
                    }
                } catch (e) {
                    console.log(e);
                }
            }
        },
        /**
         * 跳转陈列检查
         * <AUTHOR>
         * @date 2024-04-28
         */
        async collabora(edit = false){
            const lovOptions = await this.$lov.getLovByType('BRAND_COM_NAME')
            const newList = lovOptions.filter((i)=>{
                return !['5600', '5161', '1204', '5137', '5151'].includes(i.val)
            })
            const excludeArr =  newList.map((i)=>{
            	return i.val
            })
            this.$nav.push('/pages/terminal2/parent-terminal/terminal-collabora-page.vue', {
                name: this.clientDetails.acctName,
                data: this.clientDetails,
                id: this.pageParam.data.id,
                form: this.hisForm,
                edit,
                excludeArr
            })
        },
        /**
         * 绑定融合终端功能
         * <AUTHOR>
         * @date 2024-3-20 17:05:50
         * @param  当前终端基础信息
         */
        secureIntegrateTerminals() {
            this.$nav.push('/pages/terminal2/parent-terminal/paremt-terminal-bind-page.vue', {
                source: 'addTerminal',
                id: this.pageParam.data.id
            })
        },
        /**
         * 跳转品牌公司终端详情
         * <AUTHOR>
         * @date	2023/11/27 10:18
         */
       async goToItem(row) {
           const userInfo = this.$taro.getStorageSync('token').result
           const {orgType, positionType} = userInfo;
           let canReadAll = false
           if(orgType === 'Company'){ //销售公司
               canReadAll = true;
           }else if(orgType === 'BranchCompany'){ //品牌公司
               if(positionType === 'CityCoordinator'){ //百城协调人
                   canReadAll = false;
               }
           }
           try {
               const {success, result: data} = await this.$http.post('action/link/accnt/queryById', {id: row.childId});
               if (success) {
                   this.$nav.push('/pages/terminal/terminal/client-details-page', {
                       data,
                       claimList: true,
                       newFlag: false,
                       listOauth: 'MULTI_ORG',
                       pageFrom: 'parentTerminal',
                       canReadAll
                   });
               } else {
                   this.$showError('跳转终端获取信息失败，请稍后再试！' + result);
               }
           } catch (e) {
               console.log('e',e)
           }
        },
        /**
         * 导航栏切换
         * <AUTHOR>
         * @date 2020-04-02
         */
        async switchTab(val) {
            this.tapsActive = val;
            switch (val.val) {
                case 'basicInformation':
                    this.getBasicInformationData();
                    break;
                case 'brandTerminal':
                    await this.terminalListOption.methods.reload();
                    break;
                case 'bindingTerminal':
                    await this. bindingTerminalOption.methods.reload();
                    break;
                case 'fourColorLable':
                    this.getFourColorData();
                    break;
                default:
                    break;
            }
        },
        /**
         * 门头照片预览
         * <AUTHOR>
         * @date 2020-09-22
         * @param param
         */
        async previewStoreUrl(param) {
            const that = this;
            if (!that.$utils.isEmpty(param.storePicKey) && param.acctType === 'Terminal') {
                let imgUrl = await this.$image.getSignedUrl(param.storePicKey);
                const inOptions = {
                    current: imgUrl,
                    urls: [imgUrl]
                };
                that.$image.previewImages(inOptions)
            } else {
                const inOptions = {
                    current: that.$imageAssets.terminalDefaultImage,
                    urls: [that.$imageAssets.terminalDefaultImage]
                };
                that.$image.previewImages(inOptions)
            }
        },

        /**
         * 获取基础信息数据
         * <AUTHOR>
         * @date 2024-3-19 10:10:52
         * @param param
         */
        async getBasicInformationData() {
         try {
             const {success, result} = await this.$http.post('action/link/accountParent/queryById', {id: this.pageParam.data.id});
             if (success) {
                 this.clientDetails = Object.assign({}, result, this.clientDetails)
                 this.basicInformationData = result;
             } else {
                 this.$showError('基础信息获取失败，请稍后再试！' + result);
             }
         } catch (e) {
             this.$showError('基础信息获取失败，请稍后再试！' + e);
         }
        },

        /**
         * 获取四色标签记录数据
         * <AUTHOR>
         * @date 2024-3-19 10:10:52
         * @param param
         */
        async getFourColorData() {
            try {
                const {success, rows} = await this.$http.post('action/link/accountParent/queryColorRecordPage', {id: this.pageParam.data.id, oauth: 'ALL', sort: 'created', order: 'desc'});
                if (success) {
                    this.fourColorData = rows;
                } else {
                    this.$showError('基础信息获取失败，请稍后再试！' + rows);
                }
            } catch (e) {
                this.$showError('基础信息获取失败，请稍后再试！' + e);
            }
        },

        async deleteItem(item) {
            this.$dialog({
                title: '提示',
                content: '是否确认解除融合终端绑定关系？',
                cancelButton: true,
                onConfirm: async () => {
                    console.log('item2',item)
                    try {
                        const {success, result} = await this.$http.post('action/link/accountParent/unbindAccountParent',{id: item.childId})
                        if(success) {
                            this.$message.success('解除融合终端绑定关系成功！');
                            await this. bindingTerminalOption.methods.reload();
                        } else {
                            this.$showError(result);
                        }
                    } catch (e) {
                        console.log('e',e)
                    }
                },
                onCancel: () => {
                    this.$message.info('取消删除')
                }
            })

        },

        /**
         * 页面返回参数
         * <AUTHOR>
         * @date 22024-3-20 23:49:05
         * @param param
         */
        async onBack (param) {
            if (param&&param.refreshFlags) {
                await this.getHisTory()
                return
            }
            try {
                const bindIdArr = param.map(item => item.id)
                console.log('bindIdarr',bindIdArr)
                const {success, result} = await this.$http.post('action/link/accountParent/bindAccountParent',{ids: bindIdArr, multiAcctMainId: this.pageParam.data.id})
                if(success) {
                    this.$message.success('绑定融合终端成功！');
                    this.bindingTerminalOption.methods.reload();
                } else {
                    this.$showError(result);
                }
            } catch (e) {
                console.log('e',e)
            }
        }

    }
}
</script>

<style lang="scss">
.parent-terminal-detail-page {
    .top-container {
        .comp-navbar {
            width: 100vw;
            .placeholder-bar{
                background-color: transparent;
                width: 100%;
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                -webkit-box-pack: start;
                -ms-flex-pack: start;
                justify-content: flex-start;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;
                .icon-left {
                    width: 10%;
                    font-size: 34px;
                    color: #FFFFFF;
                    padding-left: 24px;
                }
                .navigator-back {
                    width: 46px;
                    height: 46px;
                    padding-left: 24px;
                }
                .bar-title {
                    width: 82%;
                    font-size: 34px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    text-align: center;
                }
            }
        }
        .top-content {
            @include flex;
            .store-image {
                margin-left: $margin-normal;
                margin-top: 32px;
                width: 128px;
                height: 128px;
                border-radius: 16px;
                overflow: hidden;
                box-shadow: 0 7px 49px 0 rgba(20,28,51,0.39);
                image {
                    width: 100%;
                    height: 100%;
                }
            }
            .store-content-cover {
                //margin-top: 32px;
                width: 80%;
                .store-content-code {
                    margin-left: 24px;
                    color: #fff;
                    font-size: 28px;
                    line-height: 28px;
                }
                .title-level-code {
                    @include flex-start-center;
                    @include space-between;
                    width: 100%;
                    .store-content-top {
                        @include flex-start-center;
                        @include space-between;
                        margin-left: 24px;
                        .store-title {
                            font-family: PingFangSC-Semibold,serif;
                            font-size: 32px;
                            color: #ffffff;
                            letter-spacing: 0;
                            line-height: 40px;
                            max-width: 370px;
                        }
                        .store-level {
                            margin-right: -3px;
                            width: 120px;
                            height: 44px;
                            image {
                                width: 100%;
                                height: 100%;
                            }
                        }
                        .store-level {
                            width: 120px;
                            height: 44px;
                            margin-left: 12px;
                            image {
                                width: 100%;
                                height: 100%;
                            }
                        }
                    }
                    .qr-code {
                        width: 80px;
                        height: 44px;
                        padding-right: 24px;
                        padding-left: 12px;
                        image {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }
                .store-content-middle {
                    @include flex-start-center;
                    /*padding-top: 24px;*/
                    height: 60px;
                    line-height: 60px;
                    margin-left: 14px;
                    flex-wrap: wrap;
                    .store-type {
                        border: 1px solid #ffffff;
                        border-radius: 8px;
                        font-size: 20px;
                        padding-left: 18px;
                        padding-right: 18px;
                        line-height: 36px;
                        color: #ffffff;
                        margin-right: 10px;
                        margin-top: 10px;
                    }
                }
            }
        }
    }

    .content {
        .top-blank {
            width: 100%;
            background: $color-primary;
            position: fixed;
            top: 0;
            font-size: 34px;
            color: #FFFFFF;
            letter-spacing: 0;
            text-align: center;
            z-index: 9999;
        }

        .tap-container {
            width: 100%;
            display: flex;
            height: 92px;
            overflow: hidden;

            .lnk-tabs::-webkit-scrollbar {
                display: none
            }

            .lnk-tabs {
                overflow-x: scroll;
                white-space: nowrap;
                border-top: 1px solid #f2f2f2;
                display: flex;
                background-color: #fff;
                color: #595959;
                width: 670px;
                z-index: 9999;

                &.marginTop {
                    margin-top: 80px;
                }

                .active {
                    color: $color-primary;
                }

                .lnk-tabs-item {
                    height: 92px;
                    line-height: 92px;
                    text-align: center;

                    .label-name {
                        width: 100%;
                        font-size: 28px;
                        margin-left: 30px;
                    }

                    .line {
                        height: 8px;
                        width: 56px;
                        border-radius: 16px 16px 0 0;
                        background-color: $color-primary;
                        box-shadow: 0 3px 8px 0 rgba(47, 105, 248, 0.63);
                        margin: -12px auto auto auto;
                    }
                }
            }


        }
    }

    .terminal-list-item {
        background: #FFFFFF;
        width: 702px;
        margin: 24px auto auto auto;
        border-radius: 16px;
    }

    .terminal-list {

        .media-list {
            @include flex;

            .media-list-logo {
                border-radius: 16px;
                width: 128px;
                height: 128px;
                overflow: hidden;
            }

            .store-content {
                width: 80%;

                .store-content-top {
                    @include flex-start-center;
                    @include space-between;
                    margin-left: 24px;

                    .store-title {
                        font-family: PingFangSC-Semibold,serif;
                        font-size: 32px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 36px;
                        width: 77%;
                        height: 36px;
                        overflow: hidden;
                    }
                    .store-level {
                        margin-right: -3px;
                        width: 120px;
                        height: 44px;
                        image {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }

                .store-content-middle {
                    display: flex;
                    justify-content: space-between;
                    padding-left: 32px;

                    .left {
                        @include flex-start-center;
                        flex-wrap: wrap;
                        margin-top: 10px;

                        .store-type {
                            white-space: nowrap;
                            border: 2px solid #2F69F8;
                            border-radius: 8px;
                            font-size: 20px;
                            padding-left: 18px;
                            padding-right: 18px;
                            line-height: 40px;
                            height: 40px;
                            color: #2F69F8;
                            margin-right: 10px;
                            margin-top: 10px;
                        }
                    }
                }

                .store-content-representative {
                    @include flex;
                    margin-left: 24px;
                    margin-top: 20px;
                    width: calc(100% - 24px);
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    align-items: baseline;

                    .terminal-type {
                        color: #8C8C8C;
                        min-width: 50px;
                        margin-right: 15px;
                    }

                    .terminal-type-2 {
                        color: #8C8C8C;
                        min-width: 135px;
                        margin-right: 15px;
                    }


                    .terminal-name {
                        font-family: PingFangSC-Regular,serif;
                        font-size: 24px;
                        color: #000000;
                        letter-spacing: 0;
                        padding-left: 8px;
                        width: calc(100% - 50px);
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                }

                .store-content-address {
                    margin-left: 24px;
                    margin-top: 18px;
                    font-family: PingFangSC-Regular,serif;
                    font-size: 24px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 32px;
                }
            }
        }
    }

    .card-content {
        background: #ffffff;
        border-radius: 16px;
        margin-bottom: 24px;
        margin-left: auto;
        margin-right: auto;
        margin-top: 24px;
        width: 702px;
        padding-top: 8px;
        padding-bottom: 40px;
        .card-title {
            @include flex-start-center;
            @include space-between;
            border-bottom: 2px solid #F2F2F2;
            font-size: 28px;
            padding: 17px 24px;
            .sync-info-title {
                color: #262626;
            }
        }
        .card-rows {
            font-family: PingFangSC-Regular,serif;
            font-size: 28px;
            letter-spacing: 0;
            line-height: 28px;
            padding: 32px 24px 0 24px;
            .card-rows-content {
                @include flex();
                @include space-between();
                .label {
                    color: #8C8C8C;
                }
                .time-text {
                    color: black;
                    font-size: 30px;
                    font-weight: bold;
                }
                .code-text {
                    color: #666666;
                    font-size: 26px;
                }
                .value {
                    color: #262626;
                }
                .label-address  {
                    color: #8C8C8C;
                    line-height: 40px;
                }
                .value-address {
                    text-align: right;
                    width: 80%;
                    line-height: 40px;
                    color: #262626;
                    word-break: break-all;
                }
            }
            .card-rows-content-color {
                display: flex;
                align-items: center;
                font-size: 20px;

                .icon{
                    font-size: 60px;
                    color: #cccccc;
                }

                .text{
                    width: 60px;
                    padding: 2px 8px;
                    background-color: #777777;
                    color: #ffffff;
                    border-radius: 18px;
                }
                .color-tag{
                    margin-top: 0px;
                }
            }
        }

        .line {
            margin-top: -8px;
        }
    }

    .store-content-middle {
        display: flex;
        justify-content: space-between;
        padding-left: 32px;

        .left {
            @include flex-start-center;
            flex-wrap: wrap;
            margin-top: 10px;

            .store-type {
                white-space: nowrap;
                border: 2px solid #2F69F8;
                border-radius: 8px;
                font-size: 20px;
                padding-left: 18px;
                padding-right: 18px;
                line-height: 40px;
                height: 40px;
                color: #2F69F8;
                margin-right: 10px;
                margin-top: 10px;
            }
        }
    }


    .none-color {
        background-color: #cccccc;
        color: white;;
        font-weight: bold;
    }

    .prompt-info {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-top: 12px;
    }

    .flex-container {
        display: flex;
        align-items: center;
    }

    .icon {
        margin-right: 6px;
        font-size: 18px;
        color: #8c8c8c;
    }

    .text {
        white-space: nowrap;
        color: #8c8c8c;
        font-size: 18px;
        margin-right: 6px;
    }

    .status-container {
        display: flex;
        align-items: center;
    }

    .store-status-y, .store-status-n {
        border: 1px solid #1E8BFF;
        color: #1E8BFF;
        padding: 2px 20px;
        border-radius: 6px;
        margin: 0 5px;
        white-space: nowrap;
    }

    .store-status-n {
        border-color: #cccccc;
        color: #cccccc;
    }


    .tab-contont {
        width: 100%;
    }

    .dropdown {
        position: relative;
        right: 0 !important;
        width: 92px;
        height: 92px;
        background: #FFFFFF;
        box-shadow: -3px 0 28px 0 rgba(7,44,105,0.16) !important;
        .dropdown-icon {
            line-height: 92px;
            text-align: center;
            font-size: 36px;
            color: #595959;
        }
        /*deep*/.link-dropdown-reference {
                    box-shadow: -3px 0 28px 0 rgba(7,44,105,0.16)!important;
                }
        /*deep*/.link-dropdown-content {
                    border-radius: 0 0 32px 32px;
                }
        /*deep*/.link-dropdown {
                    box-shadow: -3px 0 28px 0 rgba(7,44,105,0.16)!important;
                }
        .dropdown-container {
            @include flex-start-center();
            @include wrap();
            border-radius: 0 0 32px 32px;
            padding-bottom: 28px;
            .menu-item {
                width: 25%;
                @include flex-center-center();
                @include direction-column();
                .menu-icon {
                    color: $color-primary;
                    font-size: 48px;
                    padding-top: 28px;
                    padding-bottom: 30px;
                }
                .menu-name {
                    font-family: PingFangSC-Regular,serif;
                    font-size: 28px;
                    color: #595959;
                    letter-spacing: 0;
                    text-align: center;
                    line-height: 28px;
                    padding-bottom: 28px;
                }
            }
        }
    }


    .dropdown-container {
        @include flex-start-center();
        @include wrap();
        border-radius: 0 0 32px 32px;
        padding-bottom: 28px;
        .menu-item {
            width: 25%;
            @include flex-center-center();
            @include direction-column();
            .menu-icon {
                color: $color-primary;
                font-size: 48px;
                padding-top: 28px;
                padding-bottom: 30px;
            }
            .dropdown {
                right: 0;
                position: relative;
                width: 92px;
                height: 92px;
                background: #FFFFFF;
                box-shadow: -3px 0 28px 0 rgba(7,44,105,0.16) !important;
                .dropdown-icon {
                    line-height: 92px;
                    text-align: center;
                    font-size: 36px;
                    color: #595959;
                }
                /*deep*/.link-dropdown-reference {
                            box-shadow: -3px 0 28px 0 rgba(7,44,105,0.16)!important;
                        }
                /*deep*/.link-dropdown-content {
                            border-radius: 0 0 32px 32px;
                        }
                /*deep*/.link-dropdown {
                            box-shadow: -3px 0 28px 0 rgba(7,44,105,0.16)!important;
                        }
                .dropdown-container {
                    @include flex-start-center();
                    @include wrap();
                    border-radius: 0 0 32px 32px;
                    padding-bottom: 28px;
                    .menu-item {
                        width: 25%;
                        @include flex-center-center();
                        @include direction-column();
                        .menu-icon {
                            color: $color-primary;
                            font-size: 48px;
                            padding-top: 28px;
                            padding-bottom: 30px;
                        }
                        .menu-name {
                            font-family: PingFangSC-Regular,serif;
                            font-size: 28px;
                            color: #595959;
                            letter-spacing: 0;
                            text-align: center;
                            line-height: 28px;
                            padding-bottom: 28px;
                        }
                    }
                }
            }
            .menu-name {
                font-family: PingFangSC-Regular,serif;
                font-size: 28px;
                color: #595959;
                letter-spacing: 0;
                text-align: center;
                line-height: 28px;
                padding-bottom: 28px;
            }
        }
    }
    .store-type-level {
        border: 1px solid #ffffff;
        border-radius: 8px;
        font-size: 20px;
        padding-left: 18px;
        padding-right: 18px;
        line-height: 36px;
        color: #ffffff;
        margin-right: 10px;
        margin-top: 10px;
        @include flex;
        .acct-level {
            margin-right: 8px;
            line-height: 36px;
        }
        .line {
            padding: 0 8px;
        }
        .capacity-level {
            line-height: 36px;
        }
    }
}
</style>
