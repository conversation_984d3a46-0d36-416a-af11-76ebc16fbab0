<!--
 融合终端列表
 <AUTHOR>
 @date	2023/11/23 17:52
-->
<template>
    <link-page class="parent-terminal-list-page">
        <lnk-taps :taps="tapsOption" v-model="tapsActive" @switchTab="tabChange"></lnk-taps>
        <link-auto-list :option="terminalListOption" :hideCreateButton="true"
                        :searchInputBinding="{props:{placeholder:`终端名称/客户编码${tapsActive.val==='terminalCensus'?'':'/纳税人识别号'}`}}">
            <view slot="searchRight" class="search-container"  @tap="goMapList">
                <link-icon icon="icon-ditu"/>
                <view>地图</view>
            </view>
            <link-filter-group slot="filterGroup">
                <link-filter-item label="创建时间" :param="{sort:{field:'created',desc:true}}"/>
                <link-filter-item label="最新更新" :param="{sort:{field:'lastUpdated',desc:true}}"/>
            </link-filter-group>
            <template slot="other" v-if="tapsActive.val==='terminalCensus'">
                <link-fab-button  @tap="toAddCensus"/>
            </template>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="terminal-list-item" @tap="goToItem(data)">
                    <view class="terminal-list" slot="note">
                        <view class="media-list">
                            <image class="media-list-logo" :src="data.storeUrl" @tap.stop="previewStoreUrl(data)" lazy-load="true"></image>
                            <view class="store-content">
                                <view class="store-content-top" v-if="data.acctType">
                                    <view class="store-title">{{data.acctName}}</view>
                                </view>
                                <view class="store-content-middle">
                                    <view class="left">
                                        <!-- @edit by 邓佳柳 2024/11/28 四色标签 -->
                                        <color-tag :value="data.fourColorLabel" v-if="data.fourColorLabel"/>
                                        <view class="store-type" v-if="data.financingFlag">贷 | {{data.financingFlag | lov('YR_FINANCING_FLAG')}}</view>
                                        <view class="store-type" v-if="data.acctType">{{data.acctType | lov('ACCT_TYPE')}}</view>
                                        <template v-if="data.protocolBrand">
                                            <view class="store-type" v-for="(item,index) in data.protocolBrand.split(',')">{{item | lov('BRAND_COM_NAME')}}</view>
                                        </template>

                                    </view>
                                </view>
                                <view class="store-content-representative">
                                    <view class="terminal-type">编码</view>
                                    <view class="terminal-name">{{data.acctCode}}</view>
                                </view>
                                <view class="store-content-representative" v-show="tapsActive.val !== 'terminalCensus'">
                                    <view class="terminal-type-2">纳税人识别号</view>
                                    <view class="terminal-name">{{data.creditNo}}</view>
                                </view>
                                <view class="store-content-address">
                                    <view class="store-address">{{data.province}}{{data.city}}{{data.district}}{{tapsActive.val !== 'terminalCensus' ? data.address : data.addrDetaiAddr}}</view>
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
    import lnkTaps from '../../core/lnk-taps/lnk-taps'
	import ColorTag from '@/pages/terminal2/components/ColorTag.vue';

definePageConfig({
    navigationBarTitleText: '融合终端'
})
export default {
    name: 'parent-terminal-list-page',
    components: {
        lnkTaps,
        ColorTag
    },
    data() {
        const tapsOption = [
            {name: "融合终端", seq: "1", val: 'parentTerminal', property: 'type'},
            {name: "终端普查", seq: "2", val: 'terminalCensus', property: 'type'},
        ];
        const userInfo = this.$taro.getStorageSync('token').result
        let oauth = '';
        oauth = this.$utils.isPostnOauth() === 'MY_POSTN' ? 'MULTI_POSTN' : this.$utils.isPostnOauth();
        if (oauth === 'MY_ORG') {
            oauth = 'MULTI_ORG';
        }
        if (userInfo.positionType === 'CityManager') {
            oauth = 'MULTI_ORG';
        }
        const terminalListOption = new this.AutoList(this, {
            module: 'action/link/accountParent',
            url: {
                queryByExamplePage: 'action/link/es/accountParent/queryByExamplePage',
            },
            param: () => {
                return {
                    oauth: 'MY_ORG',
                    safeMark: 'parentSafe',
                    multiAcctMainFlag:'Y',
                    filtersRaw: [
                        {id: 'acctType', property: 'acctType', value: 'Terminal', operator: '='},
                    ]
                }
            },
			sortField: ' ',
            searchFields: ['acctName', 'createdByName', 'acctCode', 'creditNo'],
            filterOption: [
                {label: '贷款平台', field: 'financingFlag', type: 'lov',lov: 'YR_FINANCING_FLAG'},
                {label: '四色标签', field: 'fourColorLabel', type: 'lov',lov: 'Four_Color_Label'},
            ],
			sortOptions: null,
            hooks: {
                beforeLoad (options) {
                    this.loadList = true
                    if (this.$utils.isEmpty(options.param.sort.trim())) {
                        delete options.param.order;
                        delete options.param.sort;
                    }
                },
                async afterLoad (data) {
                    this.loadList = false
                    data.rows.map(async (item) => {
                        if(this.tapsActive.val==='terminalCensus'){
                            item.storePicPreKey = item.smallFrontPhoto
                        }
                        if (!this.$utils.isEmpty(item.storePicPreKey) && item.acctType === 'Terminal') {
                            let urlData = this.$image.getSignedUrl(item.storePicPreKey);
                            this.$set(item, 'storeUrl', urlData);
                        } else {
                            this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
                        }
                    })
                }
            }
        });
        return {
            loadList: false,
            tapsOption,
            tapsActive: tapsOption[0],
            terminalListOption,
            oauth
        }
    },
    methods: {
        /**
         * 监控返回函数
         * <AUTHOR>
         * @date 2020-09-15
         * @param param
         */
        onBack (param) {
            if (param&&param.refreshFlags) {
                this.terminalListOption.methods.reload();
            }
        },
        /**
         * 切换地图模式
         * <AUTHOR>
         * @date 2024-04-29
         */
        goMapList () {
            this.$nav.push('/pages/terminal/terminal/terminal-map-list-page', {
                source: 'parentTerminalList',
                newFlag: false
            })
        },
        // 新增普查
        async toAddCensus(){
            this.$nav.push('/pages/terminal2/parent-terminal/census-terminal-add-page', {
                sellProductFlag: true,
                listOauth: this.listOauth
            })
        },
        async tabChange(active){
            if(this.loadList){
                if(active.val === 'parentTerminal'){
                    this.tapsActive = this.tapsOption[1]
                }else{
                    this.tapsActive = this.tapsOption[0]
                }
                this.$message.error('数据正在加载中，请勿切换')
                return
            }
          this.terminalListOption.list = []
          if (active.val === 'parentTerminal') {
              this.terminalListOption.option.url = { queryByExamplePage: 'action/link/es/accountParent/queryByExamplePage' };
              this.terminalListOption.option.param = {
                  oauth: 'MY_ORG',
                  safeMark: 'parentSafe',
                  multiAcctMainFlag:'Y',
                  filtersRaw: [
                      {id: 'acctType', property: 'acctType', value: 'Terminal', operator: '='},
                  ],
              }
              this.terminalListOption.option.searchFields = ['acctName', 'createdByName', 'acctCode', 'creditNo'];
              this.terminalListOption.option.filterOption = [
                  {label: '贷款平台', field: 'financingFlag', type: 'lov',lov: 'YR_FINANCING_FLAG'},
                  {label: '四色标签', field: 'fourColorLabel', type: 'lov',lov: 'Four_Color_Label'},
              ]
              await this.terminalListOption.methods.reload();
          } else {
              this.terminalListOption.option.url = { queryByExamplePage: 'action/link/terminalCensus/queryByExamplePage'};
              this.terminalListOption.option.param = {}
              this.terminalListOption.option.searchFields = ['acctName', 'acctCode'];
              this.terminalListOption.option.filterOption = ''
              await this.terminalListOption.methods.reload();
          }
        },
        /**
         * 门头照片预览
         * <AUTHOR>
         * @date 2020-09-22
         * @param param
         */
        async previewStoreUrl(param) {
            const compressSuffix = '/suoluetu';
            const defaultSuffix = 'default';
            if (this.$utils.isEmpty(param.storeUrl) || param.storeUrl.indexOf(defaultSuffix) !== -1) {
                return;
            }
            const inOptions = {
                current: param.storeUrl.replaceAll(compressSuffix, ''),
                urls: [param.storeUrl]
            };
            this.$image.previewImages(inOptions);
        },

        /**
         * 跳转详情
         * <AUTHOR>
         * @date	2023/11/23 17:52
         */
        goToItem(data) {
           if( this.tapsActive.val==='terminalCensus'){
                this.$nav.push('/pages/terminal2/parent-terminal/census-terminal-detail-page.vue', {
                    oauth: this.oauth,
                    data
                });
            }else{
                this.$nav.push('/pages/terminal2/parent-terminal/parent-terminal-detail-page.vue', {
                    oauth: this.oauth,
                    data
                });
            }
        }
    }
}
</script>

<style lang="scss">
.parent-terminal-list-page {
    .search-container {
        padding-left: 12px;
        color: #8C8C8C;
        font-size: 28px;
        text-align: center;
    }
    .link-list{
        .link-sticky.link-sticky-top{
            top:92px !important;
        }
        .link-auto-list-wrapper{
            margin-top: 92px!important;
        }
    }
    .terminal-list-item {
        background: #FFFFFF;
        width: 702px;
        margin: 24px auto auto auto;
        border-radius: 16px;
    }

    .terminal-list {

        .media-list {
            @include flex;

            .media-list-logo {
                border-radius: 16px;
                width: 128px;
                height: 128px;
                overflow: hidden;
            }

            .store-content {
                width: 80%;

                .store-content-top {
                    @include flex-start-center;
                    @include space-between;
                    margin-left: 24px;

                    .store-title {
                        font-family: PingFangSC-Semibold,serif;
                        font-size: 32px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 36px;
                        width: 77%;
                        height: 36px;
                        overflow: hidden;
                    }
                }


                .store-content-middle {
                    display: flex;
                    justify-content: space-between;
                    padding-left: 32px;

                    .left {
                        @include flex-start-center;
                        flex-wrap: wrap;
                        margin-top: 10px;

                        .store-type {
                            white-space: nowrap;
                            border: 2px solid #2F69F8;
                            border-radius: 8px;
                            font-size: 20px;
                            padding-left: 18px;
                            padding-right: 18px;
                            line-height: 40px;
                            height: 40px;
                            color: #2F69F8;
                            margin-right: 10px;
                            margin-top: 10px;
                        }
                    }
                }

                .store-content-representative {
                    @include flex;
                    margin-left: 24px;
                    margin-top: 20px;
                    width: calc(100% - 24px);
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    align-items: baseline;
                    justify-content: space-between;

                    .terminal-type {
                        color: #8C8C8C;
                        min-width: 50px;
                        margin-right: 15px;
                    }

                    .terminal-name {
                        font-family: PingFangSC-Regular, serif;
                        font-size: 24px;
                        color: #000000;
                        letter-spacing: 0;
                        padding-left: 8px;
                        width: calc(100% - 50px);
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }

                    .terminal-type-2 {
                        color: #8C8C8C;
                        min-width: 135px;
                        margin-right: 18px;
                    }
                }

                .store-content-address {
                    margin-left: 24px;
                    margin-top: 18px;
                    font-family: PingFangSC-Regular,serif;
                    font-size: 24px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 32px;
                }
            }
        }
    }
}
</style>
