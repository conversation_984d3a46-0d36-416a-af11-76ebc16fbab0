<template>
    <link-page class="edit-approve-page">
        <lnk-taps :taps="tapsOptions" v-model="tapsActive" :onlyTabChange="true" @switchTab="switchTab"></lnk-taps>
        <view class="content-container">
            <view class="content-item basics-info" v-if="tapsActive.seq === '1'">
                <new-client-basics :formOption="basicOption"
                                   :listOauth="pageParam.listOauth"
                                   ref="clientBasics"
                                   :editFlag="pageParam.editFlag"
                                   :storeHeadLength="storeHeadLength"
                                   @addressModify="addressModify"
                                   @getLocation="getLocation"
                                   @checkType="checkCustomerType"
                                   :coordinate="coordinate"
                                   :isGuoJiao="isGuoJiao"
                                   :isJiaoLing="isJiaoLing"
                                   :isChuanDong="isChuanDong"
                                   :isDaHongYong="isDaHongYong"
                                   :isChuanDongOrXingJi="isChuanDongOrXingJi"
                                   :isNewShiJiaZhuang="isNewShiJiaZhuang"
                                   :isXingXiangDian="isXingXiangDian"
                                   @changeStatusVal="changeStatusVal"
                                   @storeHeadArr="storeHeadArr"></new-client-basics>
            </view>
            <view class="content-item sale-product" v-if="tapsActive.seq === '2'">
                <sale-product
                    :sellProductArr="sellProductArr"
                    :formOption="{...basicOption, acctId: basicOption.id, rowStatus: 'UPDATE'}"
                    @deleteIndex="deleteIndex"
                    :editApprove="true"
                    @addSellProduct="addSellProduct"
                    @changeStatus="changeStatus">
                </sale-product>
            </view>
            <view class="content-item contacts-info" v-if="tapsActive.seq === '3'">
                <contacts-info :contactsArr="contactsArr"
                               :acctId="pageParam.data.id"
                               :formOption="basicOption"
                               :editApprove="true"></contacts-info>
            </view>
            <view class="content-item basics-info" v-show="tapsActive.seq === '4'">
                <address-list ref="addressRef" :basicOption="basicOption" :acctId="pageParam.data.id" editFlag="edit"></address-list>
            </view>
        </view>
        <link-sticky class="bottom-sticky">
            <view class="sticky">
                <link-button block size="large" @tap="submit" :shadow="true" autoLoading>提交</link-button>
            </view>
        </link-sticky>
        <link-dialog ref="locationFailSelectAddress" class="location-select-address" position="poster">
            <view class="address-t">
                <view style="width: 100%;height: 30px;line-height: 30px">
                    <view style="width: 100%;text-align: center;">当前5G网络不稳定企微生态获取定位失败,请手动选择就近地址</view>
                </view>
                <view class="address-v">
                    <view>
                        <item title="所在地区">
                            <link-address placeholder="请选择所在地区"
                                          :province.sync="selectAddressObj.province"
                                          :city.sync="selectAddressObj.city"
                                          :district.sync="selectAddressObj.district"/>
                        </item>
                    </view>
                    <view class="t-left">
                        <view class="title">详细地址</view>
                        <view class="val">
                            <link-input type="text" v-model="selectAddressObj.addr" placeholder="请填写或选择单位名称"/>
                        </view>
                        <view class="ent-wrap" @tap="searchAddress()">
                            <view class="iconfont icon-sousuo"
                                  style="float: left;line-height: 30px;font-size: 12px;width: 10%"></view>
                            <view style="float: right;font-size: 10px;width: 80%;margin-left: 5px;">智能联想地址</view>
                        </view>
                    </view>
                </view>
                <scroll-view scroll-y="true" class="list-container">
                    <view v-for="(item1,index) in suggestionAddressData" :key="index">
                        <view class="list-item" @tap="selectAddress(item1)">
                            <view class="left-content">
                                <view class="row-1">
                                    <view class="name">{{item1.title}}</view>
                                    <view class="address">{{item1.address}}</view>
                                </view>
                            </view>
                            <view class="right-content">
                                <view v-if="item1._checked">
                                    <link-icon size="1.8em" style="color:#2F69F8;font-size: 16px" icon="icon-check"/>
                                </view>
                            </view>
                        </view>
                    </view>
                </scroll-view>
                <link-button slot="foot" @tap="confirmAddress" block>确定</link-button>
            </view>
        </link-dialog>
    </link-page>
</template>

<script>
import lnkTaps from '../../core/lnk-taps/lnk-taps.vue'
import newClientBasics from '../new-construction/components/new-client-basics.vue';
import saleProduct from '../new-construction/components/sale-product.vue'
import contactsInfo from '../new-construction/components/contacts-info.vue'
import addressList from "../../terminal/terminal/components/address-list.vue"
import Taro from "@tarojs/taro";
import editApprove from './mixins/edit-approve';
export default {
    name: "edit-approve-page",
    mixins: [editApprove()],
    components: {
        lnkTaps,
        newClientBasics,
        saleProduct,
        contactsInfo,
        addressList
    },
    data() {
        // 5G时定位出现问题后，选择省市区县智能联想地址选择获取经纬度
        const selectAddressObj = {
            province:'',
            city:'',
            district:'',
            addr: '',
        };
        const userInfo = this.$taro.getStorageSync('token').result;
        return {
            userInfo,
            initAcctCategory: '', //记录初始值
            changeFlag: false, //判断是否需要校验距离
            selectAddressObj, // 5G时定位出现问题后，选择省市区县智能联想地址选择获取经纬度
            suggestionAddressData: [],//根据输入智能联想地址列表
            openSettingNum: 1,//授权次数 默认为1 如果没授权的情况下 5G网络 定位问题 第一次先授权只要授过权次数加1 当次数>1 还是拿不到定位就给默认信息吧....
            basicOption: this.$utils.deepcopy(this.pageParam.data),
            coordinate: null,
            tapsOptions: [
                {defaultValue: "N", id: "222323332756804060", name: "基础信息", seq: "1", type: "INTERACTION_TYPE", val: "Vote"},
                {defaultValue: "N", id: "222323332756804060", name: "所售产品", seq: "2", type: "INTERACTION_TYPE", val: "Vote"},
                {defaultValue: "N", id: "222323332756804060", name: "联系人", seq: "3", type: "INTERACTION_TYPE", val: "Vote"},
                {defaultValue: "N", id: "222323332756804060", name: "收货地址", seq: "4", type: "INTERACTION_TYPE", val: "Vote"}
            ],                                                                      // 顶部状态栏
            tapsActive: {},
            sellProductArr: [],
            targetSellProduct: null,                                                //拷贝原始所售产品数据
            contactsArr: [],
            targetContacts: null,                                                   //拷贝原始联系人数据
            storeHeadLength: 0,                                                     // 门头照片长度
            locationFunData:{},                                                     //用于存放调用定位时的标识
            overhangFlag: false,                                                    // 超距标志
            allowDistance: 0,                                                       // 终端新建允许范围
            // 分品项标志
            prodPartFlag: false,
            editArsList: [], // 编辑终端时变动的收货地址
            defaultchainHeadStoreFlag: '',
            defaultchainStoreFlag: '',  
        }
    },
    onUnload() {
        const {success, result} = this.$http.post('action/link/accnt/checkEditAccount', {
            id: this.pageParam.data.id,
            attr1: 'DEL'
        });
    },
    async created() {
        this.editTerminalAddress();
        this.initAcctCategory = this.pageParam.data.acctCategory; //获取编辑时的中类
        
        console.log('返回携带终端数据33333', this.basicOption)
        // 获取分品项企业参数配置
        const prodPartCom = await this.$utils.getCfgProperty('PROD_PART_BRANCH_COM');
        this.prodPartFlag = prodPartCom.indexOf(this.userInfo.coreOrganizationTile.brandCompanyCode) > -1;
        this.init()
        this.targetBasicOption = this.$utils.deepcopy(this.pageParam.data)
        this.allowDistance = await this.getAllowDistance();
        const [prod, contacts] = await Promise.all([this.getProductInfo(), this.getContactsInfo()])
        this.sellProductArr.push(...prod)
        this.targetSellProduct = this.$utils.deepcopy(prod);
        if(contacts.length > 0) {
            contacts.forEach(item => {
                item.unfoldFlag = false
            })
            contacts[0].unfoldFlag = true
        }
        this.contactsArr.push(...contacts)
        this.targetContacts = this.$utils.deepcopy(contacts);
        this.defaultchainStoreFlag= this.basicOption.chainStoreFlag;
        this.defaultchainHeadStoreFlag= this.basicOption.chainHeadStoreFlag;
    },
    mounted() {
        this.$bus.$on('basicsListRefresh', data => {
          if(data&&data.chainHeadStoreId&&data.chainHeadStoreName&&this.basicOption) {
            this.$set(this.basicOption, 'chainHeadStoreId', data.chainHeadStoreId)
            this.$set(this.basicOption, 'chainHeadStoreName', data.chainHeadStoreName)
          }
        })
    },
    async onShow(){
        const location = this.$locations.QQGetLocation();
        if(location && this.getLocationFlag){
            this.getLocationFlag = false;
            if (this.$utils.isNotEmpty(location)){
                if(this.locationFunData.isDeliverFlag !== 'deliverAddr'){
                    this.basicOption.longitude = location.longitude;
                    this.basicOption.latitude = location.latitude;
                }
                let distance = await this.$locations.getDistance(Number(this.coordinate.latitude), Number(this.coordinate.longitude), Number(location.latitude), Number(location.longitude));
                let resData = {res: location, flag: 'success', distance: distance};
                let acctType = await this.$lov.getValByTypeAndName('ACCT_TYPE', this.basicOption.acctType);
                let acctCategory = await this.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', this.basicOption.acctCategory, 'ACCT_TYPE', acctType);
                this.chooseData = resData;
                if(this.locationFunData.isDeliverFlag !== 'deliverAddr')  {
                    this.overhangFlag = resData.distance * 1000 > this.allowDistance;
                }
                if (!this.unLimitFlag && this.overhangFlag && (acctCategory !== 'qdlx-2' && acctCategory !== 'qdlx-4' && acctType==='Terminal') && this.locationFunData.isDeliverFlag !== 'deliverAddr') {
                    this.overhangDialog();
                } else {
                    let address = await this.$locations.reverseTMapGeocoder(resData.res.latitude, resData.res.longitude, '终端财务信息编辑');
                    if(this.locationFunData.isDeliverFlag === 'deliverAddr'){
                        this.$set(this.basicOption, 'deliveryProvince', address['originalData'].result.addressComponent.province)
                        this.$set(this.basicOption, 'deliveryCity', address['originalData'].result.addressComponent.city)
                        this.$set(this.basicOption, 'deliveryDistrict', address['originalData'].result.addressComponent.district)
                        this.$set(this.basicOption, 'deliveryTownName', address['originalData'].result.addressComponent.townName)
                        this.$set(this.basicOption, 'deliveryTown', address['originalData'].result.addressComponent.town)
                        this.$set(this.basicOption, 'deliveryLatitude', resData.res.latitude);
                        this.$set(this.basicOption, 'deliveryLongitude', resData.res.longitude);
                        const chooseAddress = resData.res.address.replace(this.basicOption.province, '').replace(this.basicOption.city, '').replace(this.basicOption.district, '').replace(this.basicOption.townName, '')
                        this.$set(this.basicOption, 'deliveryDetailAddr', chooseAddress + resData.res.name)
                        return;
                    }
                    try {
                        this.$utils.showLoading()
                        const data = await this.$http.post('/action/link/alladdress/queryEffectiveByTownCode',{addrCode:address['originalData'].result.addressComponent.town})
                        if(data.success) {
                            if(data.adcodeFlag){
                                this.$set(this.basicOption, 'adcode', data.adcode)
                                this.$set(this.basicOption, 'province', data.province)
                                this.$set(this.basicOption, 'provinceCode', data.provinceCode)
                                this.$set(this.basicOption, 'city', data.city)
                                this.$set(this.basicOption, 'cityCode', data.cityCode)
                                this.$set(this.basicOption, 'district', data.district)
                                this.$set(this.basicOption, 'districtCode', data.districtCode)
                                this.$set(this.basicOption, 'town', data.townCode)
                                this.$set(this.basicOption, 'townName', data.townName)
                                const chooseAddress = resData.res.address.replace(this.basicOption.province, '').replace(this.basicOption.city, '').replace(this.basicOption.district, '').replace(this.basicOption.townName, '')
                                this.$set(this.basicOption, 'address', chooseAddress + resData.res.name)
                                this.$utils.hideLoading();
                            }else {
                                this.$set(this.basicOption, 'adcode', '')
                                this.$set(this.basicOption, 'town', address['originalData'].result.addressComponent.town)
                                this.$set(this.basicOption, 'townName', address['originalData'].result.addressComponent.townName)
                                this.$set(this.basicOption, 'province', address['originalData'].result.addressComponent.province)
                                this.$set(this.basicOption, 'provinceCode', this.basicOption.town.slice(0, 2) + '0000')
                                this.$set(this.basicOption, 'city', address['originalData'].result.addressComponent.city)
                                this.$set(this.basicOption, 'cityCode', address.result.ad_info.city_code.slice(3))
                                this.$set(this.basicOption, 'district', address['originalData'].result.addressComponent.district)
                                this.$set(this.basicOption, 'districtCode', this.basicOption.town.slice(0, 6))
                                const chooseAddress = resData.res.address.replace(this.basicOption.province, '').replace(this.basicOption.city, '').replace(this.basicOption.district, '').replace(this.basicOption.townName, '')
                                this.$set(this.basicOption, 'address', chooseAddress + resData.res.name)
                                this.$utils.hideLoading();
                            }
                        }
                    }catch (e) {
                        this.$utils.hideLoading();
                    }
                }
            }
        }
    },
    watch:{
        async 'basicOption.acctCategory'(newVal) {
            let acctType = await this.$lov.getValByTypeAndName('ACCT_TYPE', this.basicOption.acctType);
            if (newVal && acctType==='Terminal') {
                let newAcctCategoryVal = await this.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', newVal, 'ACCT_TYPE', acctType);
                let oldAcctCategoryVal = this.initAcctCategory;
                if (['qdlx-2', 'qdlx-4'].includes(newAcctCategoryVal) || newAcctCategoryVal == oldAcctCategoryVal || (!['qdlx-2', 'qdlx-4'].includes(newAcctCategoryVal) && !['qdlx-2', 'qdlx-4'].includes(oldAcctCategoryVal))) {
                    this.changeFlag = false
                } else {
                    this.changeFlag = true
                }
            }
        },
        // 是否股东门店/是否经销商门店都为空时 所售产品是否自营置空
        'basicOption.isShareHolderShop'(newVal, old) {
            if (newVal === 'N' && this.basicOption.ownStores === 'N') {
                this.sellProductArr.forEach(item => {
                    item.selfSupport = '';
                });
            }
        },
        'basicOption.ownStores'(newVal, old) {
            if (newVal === 'N' && this.basicOption.isShareHolderShop === 'N') {
                this.sellProductArr.forEach(item => {
                    item.selfSupport = '';
                });
            }
        }
    },
    methods: {
        /**
         * 历史国窖终端编辑时星火终端默认为否
         * <AUTHOR>
         * @date 2024-10-18
         */
        // async starfireFlagHandle() {
        //     // 当前终端为企业参数配置品牌公司时显示是否星火终端（目前仅限国窖公司："5600"）
        //     const brandCompanyCodeArr = await this.$utils.getCfgProperty('SPARK_TERMINAL');
        //     if (this.basicOption.acctType === 'Terminal' && brandCompanyCodeArr.includes(this.basicOption.mdmCompanyCode) && !this.basicOption.starfireFlag) {
        //         // 历史终端编辑时当数据还未刷时默认赋值为否
        //         this.$set(this.basicOption, 'starfireFlag', 'N');
        //     }
        // },
        /**
         * 编辑终端时修改地址
         * <AUTHOR>
         * @date 2024-10-18
         */
        editTerminalAddress() {
            this.$bus.$on('changeAddress', data => {
                if (data.row_status === 'NEW') {
                    const editIdList = this.$refs.addressRef.addressOption.list.map(item => (item.editId));
                    if (!editIdList.includes(data.editId)) this.$refs.addressRef.addressOption.list.unshift(data);
                    else {
                        this.$refs.addressRef.addressOption.list.forEach(sum => {
                            if (sum.editId === data.editId) Object.assign(sum, data);
                        })
                    }
                } else {
                    // 设置没有收货人姓名和地址时赋值
                    this.$refs.addressRef.addressOption.list.forEach(sum => {
                        if (sum.id === data.id) {
                            this.$set(sum, 'consignee', data.consignee);
                            this.$set(sum, 'mobilePhone', data.mobilePhone);
                            Object.assign(sum, data);
                        };
                    })
                }
                // 编辑或者新增的地址
                const newEditIdList = this.editArsList.map(val => (val.editId))
                if (newEditIdList.includes(data.editId)) {
                    this.editArsList.forEach(item => {
                        if (item.editId === data.editId) {
                            Object.assign(item, data);
                        }
                    });
                } else {
                    this.editArsList.push(data);
                }
            })
        },

        /**
         * 子组件搜索后父组件更改产品状态
         * <AUTHOR>
         * @date 2023-9-6
         */
        changeStatus(param) {
            this.sellProductArr.forEach(item => {
                param.forEach(elem => {
                    if(item.id === elem.id || item.rowId === elem.id) {
                        item.status = elem.status;
                    }
                })
            })
        },
		// @make add by 谭少奇 2023/07/27 15:45 动态编辑合作字段显隐
        changeStatusVal(val){
            this.isChuanDong = val
        },
        async submit() {
            this.$utils.showLoading()
            let acctType = await this.$lov.getValByTypeAndName('ACCT_TYPE', this.basicOption.acctType);
            let acctCategory = await this.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', this.basicOption.acctCategory, 'ACCT_TYPE', acctType);
            //判断超距
            if(!this.unLimitFlag && acctCategory !== 'qdlx-2' && acctCategory !== 'qdlx-4' && acctType==='Terminal'){
                if(!await this.checkOverhang()) {
                    this.$utils.hideLoading()
                    return
                }
            }
            if((this.basicOption.chainHeadStoreFlag!=this.defaultchainHeadStoreFlag)||
            (this.basicOption.chainStoreFlag!=this.defaultchainStoreFlag)){
                const data = await this.$http.post('action/link/accnt/validateChainHeadStoreUnbind',{id:this.basicOption.id},{
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$dialog({
                            title: '提示',
                            content: response.result,
                            cancelButton: false,
                        })
                    }
                });
                // if (!data.success) {
                //     this.$utils.showAlert(data.result);
                //     return
                // }
            }
            if(this.basicOption.chainStoreFlag==='N'){
                this.basicOption.chainHeadStoreFlag = this.defaultchainHeadStoreFlag
                this.basicOption.chainHeadStoreName = ''
            }else if(this.basicOption.chainHeadStoreFlag==='Y'){
                this.basicOption.chainHeadStoreName = ''
            }
            
            const targetArr = this.contactsArr
            //数据格式化
            const basicInfo = await this.formatBasicInfo()
            const contactsArr = await this.formatContacts(basicInfo.acctType, targetArr)
            const sellProduct = await this.formatSellProduct()
            // 分销商类型终端不能提交星火终端字段
            if (acctType !== 'Terminal') delete basicInfo.starfireFlag;
            //数据校验
            const msg = await this.checkData(contactsArr)
            if(msg) {
                this.$utils.hideLoading()
                this.showMsg(msg)
                return
            }
            const {flag, info} = await this.checkSupplier(sellProduct)
            if(!flag) return
            if(flag === 'confirm') {
                await this.changeProd(info)
                return
            }
            // 5600 校验是否自营字段必填
            const isCheck = await this.checkIsAutarky(basicInfo);
            if (isCheck) {
                this.$message['warn']('请维护所售产品是否自营');
                return
            }
            const param = {
                ...basicInfo,
                saleCateList: sellProduct,
                contactsList: contactsArr
            }
            // 编辑赋值收货地址
            param.addrList = this.editArsList.map(item => {
                delete item.editId;
                return item;
            });
            try {
                const data = await this.$http.post('action/link/accnt/editAccount', param)
                if(data.success) {
                    let pageParams = {
                        refreshFlag: true,
                        refreshContacts: true,
                        refreshSellFlag: true,
                        addressFlag: true
                    };
                    this.$bus.$emit("initFinancialVueContactList");//通知财务组件重新查询联系人列表
                    this.$nav.back(pageParams);
                }
            } catch (e) {
                this.$utils.hideLoading()
            }
            this.$utils.hideLoading()
        },
        /**
         * 获取新建终端允许距离
         * <AUTHOR>
         * @date 2023-05-06
         */
        getAllowDistance () {
            return new Promise(resolve => {
                this.$http.post('action/link/cfgProperty/queryByExamplePage', {
                    key: 'createTerminalLimitDistance'
                }).then(data => {
                    if (data.success) {
                        this.$taro.setStorageSync('allowDistance', Number(data.rows[0].value));
                        resolve(Number(data.rows[0].value));
                    }
                });
            })
        },
        /**
         * 首次添加子公司供货处理所售产品
         * <AUTHOR>
         * @date 2023-5-5
         */
        async changeProd({deleteProd, updateProd, newProd}) {
            const deleteUrl = 'action/link/saleCategory/deleteById'
            await Promise.all(deleteProd.map(async(item) => {
                await this.$http.post(deleteUrl, {id: item.id})
            }))
            const arr = [...updateProd, ...newProd]
            arr.forEach((item) => {
                item.id = item.row_status === 'UPDATE' ? item.id : null
                delete item.rowId
            })
            const data = await this.$http.post('action/link/saleCategory/batchUpsert', arr)
            if (data.success) {
                let param = {
                    refreshSellFlag: true,
                    isProdConfirm: true
                };
                this.$nav.back(param);
            }

        },
        /**
         * 校验是否首次添加子公司供货，首次添加会走开户流程
         * <AUTHOR>
         * @date 2023-5-5
         */
        checkSupplier(sellProduct) {
            this.$utils.hideLoading()
            return new Promise((resolve) => {
                const deleteProd = sellProduct.filter((item) => item.row_status === 'DELETE')
                const updateProd = sellProduct.filter((item) => item.row_status === 'UPDATE')
                const newProd = sellProduct.filter((item) => item.row_status === 'NEW')

                const flag1 = this.basicOption.acctStage === 'ykf'
                const flag2 = this.basicOption.directlyFlag === 'N'
                const flag3 = [...updateProd, ...newProd].some((item) => item.supplierManageMode === 'subsidiary' && item.status === 'Y')

                if(flag1 && flag2 && flag3) {
                    this.$dialog({
                        title: '提示',
                        content: '首次添加子公司产品需要重新走开户认证流程，本次基础信息、联系人信息修改内容将不会保存！',
                        cancelButton: true,
                        onConfirm: () => {
                            resolve({flag: 'confirm', info: {deleteProd, updateProd, newProd}})
                        },
                        onCancel: () => {
                            resolve({flag: false})
                        }
                    })
                } else {
                    resolve({flag: true})
                }
            })
        },
        /**
         * 校验是否超距
         * <AUTHOR>
         * @date 2022-12-5
         */
        async checkOverhang() {
            const that = this;
            if(!this.basicOption.latitude || !this.basicOption.longitude){
                this.$message['warn']('请重新定位门店地址');
                return false
            }
            //定位的超距
            if(this.overhangFlag){
                this.overhangDialog();
                return false
            }
            //提交判断超距离
            let overhangFlag = false;
            if(this.changeFlag){
                let distance = await this.$locations.getDistance(this.coordinate.latitude, this.coordinate.longitude, this.basicOption.latitude, this.basicOption.longitude);
                overhangFlag = distance * 1000 > this.allowDistance
                if(overhangFlag){
                    that.overhangDialog();
                    return false
                }
            }
            return true
        },
        showMsg(msg) {
            this.$dialog({
                title: '提示',
                content: h => (
                    <view style="padding: 36rpx">{msg}</view>
                ),
                cancelButton: false,
                confirmText: '确认',
                onConfirm:() => {},
                onCancel: () => {}
            });
        },
        /**
         * 过滤联系人信息对应的数组，如果没有填写联系人信息则不校验对应的信息
         * <AUTHOR>
         * @date 2023-7-18
         */
        getTargetContacts(arr = this.contactsArr) {
            const target = arr.filter(item => {
                return !!item.contactsName
                    || !!item.contactsSex
                    || !!item.mobilePhone
                    || !!item.fixedPhone
                    || !!item.birthdayType
                    || !!item.birthYear
                    || !!item.birthday
                    || !!item.comments
            })
            return target
        },
        /**
         * 数据校验
         * <AUTHOR>
         * @param targetContacts 联系人信息
         * @date 2023-7-18
         */
        async checkData(targetContacts) {
            let msg = ''
            msg = await this.checkBasic(msg) || await this.checkProduct(msg) || await this.checkContactInfo(msg, targetContacts)
            return msg
        },
       
        async checkBasic(msg) {
            // 汉字及大小写字母正则
            const reg = /^[A-Za-z0-9\u4e00-\u9fa5]+$/;
            // 将值列表显示值转为独立源代码
            let acctType = await this.$lov.getValByTypeAndName('ACCT_TYPE', this.basicOption.acctType);
            let acctCategoryVal = await this.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', this.basicOption.acctCategory, 'ACCT_TYPE', acctType);
            let subAcctTypeVal = await this.$lov.getValByTypeNameParentTypeParentVal('SUB_ACCT_TYPE', this.basicOption.subAcctType, 'ACCNT_CATEGORY', acctCategoryVal);
            if (acctType === 'Terminal' && (this.$utils.isEmpty(this.basicOption.acctType) || this.$utils.isEmpty(this.basicOption.acctCategory) || this.$utils.isEmpty(this.basicOption.subAcctType))) {
                msg = '请选择客户分类';
                return msg
            }
            if (acctType !== 'Terminal' && this.$utils.isEmpty(this.basicOption.acctType)) {
                msg = '请选择客户分类';
                return msg
            }
            if(!await this.checkCustomerType()) {
                return msg
            }
            if(this.isNewShiJiaZhuang) {
                if (this.$utils.isEmpty(this.basicOption.acctSort)) {
                    this.$message['warn']('请选择客户类型');
                    return false
                }
                if (this.$utils.isEmpty(this.basicOption.acctNature)) {
                    this.$message['warn']('请选择客户性质');
                    return false
                }
            }
            if (acctCategoryVal === 'qdlx-2' && this.$utils.isEmpty(this.basicOption.kaSystemName)) {
                msg = '请选择系统名称';
                return msg
            }
            if (acctCategoryVal === 'qdlx-2' && subAcctTypeVal !== 'LianSuoBianLi' && this.$utils.isEmpty(this.basicOption.xAttr71)) {
                msg = '请选择店号';
                return msg
            }
            if (acctType === 'Terminal' && this.$utils.isEmpty(this.basicOption.acctName)) {
                msg = '请输入门头名称';
                return msg
            }
            if (acctType === 'Distributor' && this.$utils.isEmpty(this.basicOption.acctName)) {
                msg = '请输入分销商名称';
                return msg
            }
            // if (!reg.test(this.basicOption.acctName)) {
            //     msg = acctType === 'Terminal' ? '请检查门头名称格式是否正确' : '请检查分销商名称输入格式是否正确';
            //     return msg
            // }
            // if (this.$utils.isNotEmpty(this.basicOption.simName) && !reg.test(this.basicOption.simName)) {
            //     msg = '请检查客户简称输入格式是否正确';
            //     return msg
            // }
            if (acctCategoryVal !== 'GroupBuy' && this.$utils.isEmpty(this.basicOption.province) && this.$utils.isEmpty(this.basicOption.city) && this.$utils.isEmpty(this.basicOption.district)) {
                msg = '请选择所在地区';
                return msg
            }
            if (acctCategoryVal !== 'GroupBuy' && this.$utils.isEmpty(this.basicOption.address)) {
                msg = '请输入门店地址详细地址';
                return false
            }
            if (acctType === 'Terminal' && this.storeHeadLength === 0 ) { //&& this.$utils.isEmpty(this.pageParam.editFlag)
                msg = '请上传门头照片';
                return msg
            }
            if ((acctCategoryVal === 'qdlx-4') && this.$utils.isEmpty(this.basicOption.businessStartTime)) {
              msg = '请选择营业开始时间';
              return msg
            }
            if ((acctCategoryVal === 'qdlx-4') && this.$utils.isEmpty(this.basicOption.businessEndTime)) {
              msg = '请选择营业结束时间';
              return msg
            }
           
            if ((acctCategoryVal === 'qdlx-4')) {
              const phone = this.basicOption.storePhone;
              
              if (this.$utils.isEmpty(phone)) {
                  msg = '请输入订餐号码/门店电话';
                  return msg
              }
              const reg = /^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/;
              if (!reg.test(phone)) {
                  msg = '订餐号码/门店电话输入号码不正确';
                  return msg
              }
            }
            if ((acctCategoryVal === 'qdlx-4') && this.$utils.isEmpty(this.basicOption.customerUnitPrice)) {
              msg = '请输入客单价';
              return msg
            }
            if ((acctCategoryVal === 'qdlx-4') && this.$utils.isEmpty(this.basicOption.cuisineCategory)) {
                msg = '请选择菜系';
              return msg
            }
            if ((acctCategoryVal === 'qdlx-4') && this.$utils.isEmpty(this.basicOption.isIntangibleCuisine)) {
              msg = '请选择是否有非遗菜';
              return msg
            }
            if ((acctCategoryVal === 'qdlx-4') && this.$utils.isEmpty(this.basicOption.isScenicRestaurant)) {
                msg = '请选择是否为景区餐饮店';
              return msg
            }
            if ((acctCategoryVal === 'qdlx-4') && this.$utils.isEmpty(this.basicOption.waiterCount)) {
                msg = '请输入服务员数量';
              return msg
            }
            if ((acctCategoryVal === 'qdlx-4') && this.$utils.isEmpty(this.basicOption.isAtmosphereStore)) {
                msg = '请选择是否为氛围物料店';
              return msg
            }
            if ((acctCategoryVal === 'qdlx-4') && this.$utils.isEmpty(this.basicOption.isCoreStore)) {
                msg = '请选择是否餐酒项目核心门店';
              return msg
            }

            const brandCompanyCode = this.userInfo.coreOrganizationTile.brandCompanyCode;
            const res = await this.$utils.getCfgProperty('IS_CHECK_ORGANIZATION');
            const isBrandCompanyCode = res && res.length && res.includes(brandCompanyCode);
            const checkRes = res.includes(this.basicOption.mdmCompanyCode) && (!this.userInfo.coreOrganizationTile.l3Code) || isBrandCompanyCode;
            if(acctType === 'Terminal' && this.$utils.isEmpty(this.basicOption.isExclusiveShop)){
                msg = '请选择是否泸州老窖官方形象店';
                return msg
            }
            if(acctType === 'Terminal' && this.$utils.isEmpty(this.basicOption.isShareHolderShop)){
                msg = '请选择是否专营公司股东门店';
                return msg
            }
            if (acctType === 'Terminal' && this.$utils.isEmpty(this.basicOption.chainStoreFlag)) {
                msg = '请选择是否为连锁模式';
                return msg
            }
            if (acctType === 'Terminal' && this.$utils.isEmpty(this.basicOption.doorSigns)) {
                msg = '请选择门头店招';
                return msg
            }
            if(acctType === 'Terminal'&& this.basicOption.doorSigns==='JingPin' && this.$utils.isEmpty(this.basicOption.competitiveGoodsType)){
                msg = '请选择竞品类型';
                return msg
            }
            if(this.isJiaoLing) {
                if(acctType === 'Terminal' && this.$utils.isEmpty(this.basicOption.channelMemberFlag)) {
                    msg = '请选择是否为精英荟会员'
                    return msg
                }
            } else {
                if(this.$utils.isNotEmpty(this.basicOption.channelMemberFlag)) {
                    delete this.basicOption.channelMemberFlag
                }
            }
            if(acctType === 'Terminal' && this.$utils.isEmpty(this.basicOption.ownStores)) {
                msg = '请选择是否经销商自有门店';
                return msg
            }
            if (this.basicOption.chainStoreFlag === 'Y' && this.basicOption.chainHeadStoreFlag === 'N' && this.$utils.isEmpty(this.basicOption.chainHeadStoreName)) {
                msg = '请选择连锁总店名称';
                return msg
            }
            if (acctType === 'Terminal' && this.$utils.isEmpty(this.basicOption.acctLevel)) {
                if(!this.isChuanDongOrXingJi || this.basicOption.accntPartner === 'cooperation') {
                    msg = '请选择客户规划等级';
                    return msg
                }
            }
            if (acctType === 'Terminal' && this.$utils.isEmpty(this.basicOption.capacityLevel)) {
                if(!this.isChuanDongOrXingJi || this.basicOption.accntPartner === 'cooperation') {
                    msg = '请选择容量级别';
                    return msg
                }
            }
            if (this.basicOption.judgmentFlag === 'Y' && this.$utils.isEmpty(this.basicOption.imageRoomNum)) {
                msg ='请输入形象包间数';
                return msg
            }
            if (acctCategoryVal === 'qdlx-4' && this.$utils.isEmpty(this.basicOption.roomTotalNum)) {
                msg = '请输入包间总数';
                return msg
            }
            if (acctCategoryVal === 'qdlx-4' && this.$utils.isEmpty(this.basicOption.hallTotalNum)) {
                msg = '请输入大厅总数';
                return msg
            }
            if (acctType === 'Terminal' && this.$utils.isEmpty(this.basicOption.xAttr50)) {
                msg = '请选择店区位';
                return msg
            }
            if (acctType === 'Terminal' && this.$utils.isEmpty(this.basicOption.area)) {
                msg = '请选择店面积';
                return msg
            }
            if (this.$utils.isNotEmpty(this.basicOption.deliveryProvince) && this.$utils.isNotEmpty(this.basicOption.deliveryCity) && this.$utils.isNotEmpty(this.basicOption.deliveryDistrict) && this.$utils.isEmpty(this.basicOption.deliveryDetailAddr)) {
                msg = '请输入收货地址详细地址';
                return msg
            }
            return msg
        },
        async checkProduct(msg = '') {
             //5161 5903 5902 大成鸿庐永粮-不校验
            if(!this.prodPartFlag && this.basicOption.acctStage !== 'xk' && this.sellProductArr.filter(item => item.status === 'Y').length === 0) {
                return '您尚未维护该终端的有效所售产品，请维护有效所售产品'
            }
            if (this.sellProductArr.filter(item => item.status === 'Y').length !== 0 && this.isChuanDongOrXingJi && this.basicOption.accntPartner !== 'cooperation') {
                return '未合作客户不可添加有效所售产品'
            }
            const checkInfo = await this.checkProdInfo(this.sellProductArr)
            if(!checkInfo.success) msg = checkInfo.result
            return msg
        },
        // 5600 校验是否自营必填
        async checkIsAutarky(basicOption) {
            const isShowCompanyCode = await this.$utils.getCfgProperty('IS_AUTARKY');
            let isShowWhether = false
            if ((basicOption.isShareHolderShop === 'Y' || basicOption.ownStores === 'Y') && basicOption.acctType === 'Terminal' && isShowCompanyCode.indexOf(basicOption.mdmCompanyCode) > -1) {
                isShowWhether = true;
            }
            let isTips = false
            if (!isShowWhether) return isTips
            this.sellProductArr.forEach(item => {
                if (!item.selfSupport) isTips = true
            })
            return isTips
        },
        /**
         * 所售产品信息校验 lzljqw-004-852
         * <AUTHOR>
         * @date 2023-7-11
         */
        async checkProdInfo(data) {
            try {
                const checkData = this.$utils.deepcopy(data);
                checkData.forEach(item => delete item.rowId)
                const result = await this.$http.post('action/link/saleCategory/checkSupplierUniqueForTerminal', checkData, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        return response
                    }
                })
                return result
            } catch (e) {
                const info = {
                    result: `所售产品校验出错`,
                    success: false,
                    ...e
                }
                return info
            }
        },
        async getOrgL4Id(orgId) {
            if(!orgId) return
            const param = {
                filtersRaw: [{id: 'id', property: 'id', value: orgId, operator: '='}],
                orgLevel: 'Y'
            }
            try {
                const data = await this.$http.post('action/link/orgnization/queryByExamplePage', param)
                return data.rows.length && data.rows[0].orgTile.l4Id
            } catch (e) {
                console.log(e)
            }
        },
        async checkContactInfo(msg = '', targetContacts) {
            //只有已开户的终端才需要联系人信息必填
            if(this.basicOption.acctStage !== 'xk') {
                if (targetContacts.length === 0) {
                    msg = '您尚未维护该终端的联系人，请维护联系人'
                } else if (targetContacts.filter(item => item.isEffective === 'Y').length === 0){
                    msg = "您尚未维护该终端有效的联系人，请维护联系人";
                }
            }

            //lzlj-002-3346校验手机号
            const reg = /^[1][3456789][0-9]{9}$/;
            const a = targetContacts.filter((item) => !reg.test(item.mobilePhone))
            if(!this.$utils.isEmpty(a)) {
                msg = "联系人号码格式不正确！"
            }
            const b = targetContacts.filter((item1) => (this.$utils.isEmpty(item1.contactsName) || this.$utils.isEmpty(item1.mobilePhone) || this.$utils.isEmpty(item1.contactsType)));
            if(!this.$utils.isEmpty(b)){
                msg = "您维护的联系人数据，姓名、联系电话、角色职务未完善，请完善。"
            }

            //国窖公司形象店大区不进行后续店老板校验,大成浓香、永粮、鸿泸仅校验客户大类为终端的店老板联系电话
            const companyId = this.basicOption.companyId
            const brandCompanySap = [
                '5161', //泸州大成浓香酒类销售有限公司
                '5902', //泸州永粮酒类销售有限公司
                '5903', //泸州鸿泸酒类销售有限公司
                '5151', //泸州老窖窖龄酒类销售股份有限公司
                '5153', //成都酒聚酒类销售有限公司
                '5137', //泸州老窖特曲酒类销售有限公司
                '5600' //国窖
            ]; //根据sap查询id
            const filtersRaw = [{id: 'sapCompCode', property: 'sapCompCode', value: '[' + brandCompanySap + ']', operator: 'in'}];
            const companyIdRows = await this.$http.post('action/link/orgnization/queryByExamplePage', {filtersRaw});
            if (!companyIdRows.success) {
                this.$nav.error('获取SAP失败:' + companyIdRows.result);
            }
            const companyIdArr = [];
            companyIdRows.rows.forEach((item) => {
                if(['5161', '5902', '5903', '5151', '5153'].includes(item.sapCompCode))
                    companyIdArr.push(item.id); //终端校验
            });
            const typeArr = [
                '分销商',
                'Distributor'
            ]
            const checkCompany
                = await this.getOrgL4Id(this.basicOption.orgId) === '328927981139325498'
                || (typeArr.includes(this.basicOption.acctType) && companyIdArr.includes(companyId))
            if(msg || checkCompany || companyId === companyIdRows.rows[companyIdRows.rows.findIndex((item) => item.sapCompCode === '5137')].id) return msg
            for (let i = 0;i < targetContacts.length; i++) {
                if(targetContacts[i].contactsType === 'ShopOwner') {
                    const data = await this.checkShopOwnerInfo(targetContacts[i]);
                    if(data.length > 0) {
                        const positionList = await this.checkPosition(data[0].id, companyId)
                        //国窖形象店大区不在校验范围内
                        const flag1 = companyId !== companyIdRows.rows[companyIdRows.rows.findIndex((item) => item.sapCompCode === '5600')].id || await this.checkPositionOrg(positionList)
                        if(positionList.length > 0 && flag1) {
                            const type = await this.$lov.getNameByTypeAndVal('STAFF_TYPE', data[0].staffType);
                            msg = `店老板电话不能是系统内人员登记电话，请检查修正！`
                        }
                    }
                }
            }
            return msg
        },
        /**
         * 查询人员
         * <AUTHOR>
         * @date 2023-4-13
         */
        async checkShopOwnerInfo(info) {
            if(!info.mobilePhone) return [];
            const params = {
                filtersRaw: [
                    {id: 'staffType', property: 'staffType', value: '[Internal, Service, Dealer]', operator: 'in'},
                    {id: 'contactPhone', property: 'contactPhone', value: info.mobilePhone},
                    {id: 'status', property: 'status', value: 'NORMAL'}
                ]
            };
            const {rows} = await this.$http.post('action/link/user/queryUsersByType/VendorEmployee', params);
            return rows;
        },
        /**
         * 查询职位信息
         * <AUTHOR>
         * @date 2023-4-13
         */
        async checkPosition(userId, companyId) {
            const params = {
                mvgMapperName: 'userPostn',
                mvgParentId: userId,
                mvgAttr7: 'onlyIsEffective',
                oauth: 'ALL',
                filtersRaw: [
                    {id: 'companyId', property: 'companyId', value: companyId}
                ]
            }
            const {rows} = await this.$http.post('action/link/mvg/queryRightListPage', params)
            return rows
        },
        /**
         * 查询职位所属组织是否属于形象店大区及其下级组织
         * <AUTHOR>
         * @param positionList 需要校验的职位列表
         * @param num 并发查询数量
         * @date 2023-5-29
         */
        checkPositionOrg(positionList, num = 4) {
            if(positionList.length === 0) return false
            let count = 0
            let status = 'pending'
            return new Promise((res) => {
                const run = (row) => {
                    if(!row) return
                    const param = {
                        filtersRaw: [{id: 'id', property: 'id', value: row.orgId, operator: '='}],
                        orgLevel: 'Y'
                    }
                    this.$http.post('action/link/orgnization/queryByExamplePage', param).then(onFulfilled, onRejected)
                }
                const onFulfilled = (data) => {
                    const rows = data.rows
                    const l = positionList.length
                    count++
                    //lzljqw-004-465 2023-6-1新增
                    if(rows && rows[0] && rows[0].orgTile.l4Id === '328927981139325498') {
                        status = 'fulfilled'
                        res(false)
                    } else if(status === 'pending'){
                        run(positionList[count + num - 1])
                    }
                    if(count === l && status === 'pending') {
                        res(true)
                    }
                }
                const onRejected = () => {
                    status = 'rejected'
                    res(true)
                }
                for (let i = 0; i < num; i++) {
                    run(positionList[i])
                }
            })
        },
        /**
         * 格式化基础信息
         * <AUTHOR>
         * @date 2023-4-19
         */
        async formatBasicInfo() {
            const basicInfo = this.$utils.deepcopy(this.basicOption);
            // 将值列表显示值转为独立源代码
            // 客户大类
            let acctType = await this.$lov.getValByTypeAndName('ACCT_TYPE', basicInfo.acctType);
            if (this.$utils.isEmpty(acctType) && this.$utils.isNotEmpty(this.pageParam.data.acctType)) {
                // 转换独立源代码为空，则取原有值
                acctType = this.pageParam.data.acctType;
            }
            // 客户中类
            let acctCategoryVal = await this.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', basicInfo.acctCategory, 'ACCT_TYPE', acctType);
            if (this.$utils.isEmpty(acctCategoryVal) && this.$utils.isNotEmpty(this.pageParam.data.acctCategory)) {
                // 转换独立源代码为空，则取原有值
                acctCategoryVal = this.pageParam.data.acctCategory;
            }
            // 客户小类
            let subAcctType = await this.$lov.getValByTypeNameParentTypeParentVal('SUB_ACCT_TYPE', basicInfo.subAcctType, 'ACCNT_CATEGORY', acctCategoryVal);
            if (this.$utils.isEmpty(subAcctType) && this.$utils.isNotEmpty(this.pageParam.data.subAcctType)) {
                // 转换独立源代码为空，则取原有值
                subAcctType = this.pageParam.data.subAcctType;
            }
            basicInfo.acctType = acctType;
            basicInfo.acctCategory = acctCategoryVal;
            basicInfo.subAcctType = subAcctType;

            //经纬度不允许为空字符串，影响es同步
            if(!basicInfo.latitude) delete basicInfo.latitude
            if(!basicInfo.longitude) delete basicInfo.longitude

            if (!['5161', '5902', '5903'].includes(basicInfo.mdmCompanyCode)
                || acctCategoryVal !== 'qdlx-4' || acctType !== 'Terminal') {
                basicInfo.trafficHighland = null;
            }

            if(this.$utils.isEmpty(basicInfo.deliveryProvince) || this.$utils.isEmpty(basicInfo.deliveryTownName)) {
                basicInfo.deliveryProvince = basicInfo.province
                basicInfo.deliveryCity = basicInfo.city
                basicInfo.deliveryDistrict = basicInfo.district
                basicInfo.deliveryTown = basicInfo.town
                basicInfo.deliveryTownName = basicInfo.townName
                basicInfo.deliveryDetailAddr = basicInfo.address
                basicInfo.deliveryLongitude = basicInfo.longitude
                basicInfo.deliveryLatitude = basicInfo.latitude
            }
            return basicInfo
        },
        /**
         * 格式化联系人信息
         * <AUTHOR>
         * @date 2023-4-19
         */
        async formatContacts(acctType = 'Terminal', targetArr = this.contactsArr) {
            const contactsArr = await Promise.all(JSON.parse(JSON.stringify(targetArr)).map(async (item) => {
                item.belongTo = acctType
                if(!item.id) item.id = await this.$newId()
                return item
            }))
            let targetContacts = JSON.parse(JSON.stringify(this.targetContacts))
            contactsArr.forEach(item => {
                delete item.unfoldFlag
                const index = targetContacts.findIndex((targetItem) => targetItem.id === item.id)
                if(index > -1) {
                    targetContacts.splice(index, 1)
                    item.row_status = 'UPDATE'
                } else {
                    delete item.id;
                    item.row_status = 'NEW'
                }
            })
            return [...contactsArr]
        },
        /**
         * 格式化所售产品
         * <AUTHOR>
         * @date 2023-4-19
         */
        async formatSellProduct() {
            const targetSellProduct = this.targetSellProduct.map(item => {
                item.row_status = 'UPDATE';
                item.attr5 = 'Y';
                return item
            })

            const sellProductArr = await Promise.all(this.sellProductArr.map(async (item) => {
                return item
            }))
            sellProductArr.forEach(item => {
                item.attr5 = 'Y';
                const index = targetSellProduct.findIndex((targetItem) => targetItem.id === item.id)
                if(index > -1) {
                    targetSellProduct.splice(index, 1)
                    item.row_status = 'UPDATE'
                } else {
                    delete item.id;
                    item.row_status = 'NEW'
                }
            })
            return [...targetSellProduct, ...sellProductArr]
        },
        /**
         * 获取终端所售产品信息
         * <AUTHOR>
         * @date 2023-04-19
         */
        async getProductInfo() {
            const param = {
                accntId: this.pageParam.data.id,
                partOauthFlag: this.prodPartFlag ? 'Y': '' //分品项-安全性
            }
            const {rows} = await this.$http.post('action/link/saleCategory/queryByExamplePage', param)
            const arr = rows.map((item) => {
                item.rowId = item.id
                return item
            })
            return arr
        },
        /**
         * 获取终端联系人信息
         * <AUTHOR>
         * @date 2023-04-19
         */
        async getContactsInfo() {
            const param = {
                attr1: this.pageParam.data.id
            }
            const {rows} = await this.$http.post('action/link/contacts/listByAcctId', param)
            return rows
        },
        /**
         * 校验客户分类数据是否异常
         * <AUTHOR>
         * @date 2023-04-19
         */
        async checkCustomerType() {
            if(!this.acctCascadeData) {
                const [ACCT_TYPE, ACCNT_CATEGORY, SUB_ACCT_TYPE] = await Promise.all([
                    this.$lov.getLovByType('ACCT_TYPE'),
                    this.$lov.getLovByType('ACCNT_CATEGORY'),
                    this.$lov.getLovByType('SUB_ACCT_TYPE'),
                ])
                this.acctCascadeData = {
                    ACCT_TYPE: ACCT_TYPE.filter(item => item.val === 'Terminal' || item.val === 'Distributor'),
                    ACCNT_CATEGORY,
                    SUB_ACCT_TYPE
                };
            }
            if(this.basicOption.acctType === '分销商' && this.basicOption.acctCategory && !this.basicOption.subAcctType) return true
            const p1 = this.acctCascadeData.ACCNT_CATEGORY.some((item) => item.name === this.basicOption.acctCategory && item.parentName === this.basicOption.acctType)
            const p2 = this.acctCascadeData.SUB_ACCT_TYPE.some((item) => item.name === this.basicOption.subAcctType && item.parentName === this.basicOption.acctCategory)
            if(p1 && p2) return true
            this.$showError('客户分类数据异常，请重新选择!');
            return false
        },
        /**
         * 门头照片长度
         * <AUTHOR>
         * @date 2023-04-19
         * @param param
         */
        storeHeadArr (param) {
            this.storeHeadLength = param.length;
            // 展示上一页面指定的页签(避免提交时报错门头照片不存在)
            if (this.pageParam.index) {
                this.tapsActive = this.tapsOptions[this.pageParam.index];
                this.pageParam.index = '';
            }
            if (param.length !== 0) {
                this.basicOption.storePicId = param[0].id;
            }else if(param.length === 0){
                this.basicOption.storePicId = '';
            }
        },
        /**
         * 页面初始化
         * <AUTHOR>
         * @date 2023-04-19
         */
        async init() {
            await this.initCoordinate();
            // await this.starfireFlagHandle();
            await this.initData();
            this.tapsActive = this.tapsOptions[0];
        },
        async initData() {
            this.basicOption.acctType = await this.$lov.getNameByTypeAndVal('ACCT_TYPE', this.basicOption.acctType);
            this.basicOption.acctCategory = await this.$lov.getNameByTypeAndVal('ACCNT_CATEGORY', this.basicOption.acctCategory);
            this.basicOption.subAcctType = await this.$lov.getNameByTypeAndVal('SUB_ACCT_TYPE', this.basicOption.subAcctType);
        },
        /**
         * taps切换事件
         * <AUTHOR>
         * @date 2023-04-19
         * @param val taps选中对象
         * @param key taps选中对象索引
         */
        switchTab (val, key) {
            this.tapsActive = val;
        },
        /**
         * 监听详细地址修改
         * <AUTHOR>
         * @date 2023-04-19
         * @param val 修改之后的详细地址
         */
        async addressModify({val, flag}) {
            let dealAddressData = this.basicOption.province + this.basicOption.city + this.basicOption.district + this.basicOption.townName + val;
            let latLng = await this.$locations.reverseTMapGgeocodingLaLon(dealAddressData);
            if (latLng.flag === 'success') {
                this.textareaInputFlag = true;
                let distance = await this.$locations.getDistance(this.coordinate.latitude, this.coordinate.longitude, latLng.res.result.location.lat, latLng.res.result.location.lng);
                let acctType = await this.$lov.getValByTypeAndName('ACCT_TYPE', this.basicOption.acctType);
                let acctCategory = await this.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', this.basicOption.acctCategory, 'ACCT_TYPE', acctType);
                if(flag) this.overhangFlag = distance * 1000 > this.allowDistance
                if (!this.unLimitFlag && this.overhangFlag && (acctCategory !== 'qdlx-2' && acctCategory !== 'qdlx-4' && acctType==='Terminal') && flag) {
                    this.overhangDialog()
                } else {
                    this.basicOption.longitude = latLng.res.result.location.lng;
                    this.basicOption.latitude = latLng.res.result.location.lat;
                }
            } else {
                this.$message.warn('输入地址有误，请检查详细地址');
            }
        },
        /**
         * 超距弹窗
         * <AUTHOR>
         * @date 2020-10-23
         */
        overhangDialog () {
            const that = this;
            that.$dialog({
                title: '提示',
                content: '当前门店地址和您定位位置的距离超过' + that.allowDistance + '米，请检查！',
                cancelButton: true,
                onConfirm: () => {
                    setTimeout(async function () {
                        await that.$locations.chooseLocation(that.coordinate.latitude, that.coordinate.longitude);
                        that.getLocationFlag = true;
                    }, 20);
                },
                onCancel: () => {
                }
            })
        },
        async getLocation(data={}) {
            const that = this;
            that.locationFunData = data;
            let acctType = await this.$lov.getValByTypeAndName('ACCT_TYPE', this.basicOption.acctType);
            let acctCategory = await this.$lov.getValByTypeNameParentTypeParentVal('ACCNT_CATEGORY', this.basicOption.acctCategory, 'ACCT_TYPE', acctType);
            const addressInfo = await that.$locations.getCurrentCoordinate()
            if(!addressInfo.errMsg) that.coordinate = addressInfo
            // 匹配5G 某些情况下 定位获取不到的问题
            // 手机系统没开定位getLocation:fail auth deny getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF
            // 手机系统定位开了 但是企业微信没有权限 getLocation:fail system permission denied  getLocation:fail:system permission denied
            if(that.coordinate.errMsg === 'getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF'
                || that.coordinate.errMsg === 'getLocation:fail system permission denied'
                || that.coordinate.errMsg === 'getLocation:fail:system permission denied'){
                let net = "";
                await Taro.getNetworkType({
                    success (res) {
                        net = res.networkType
                    }
                });
                if(net === '5g' && this.openSettingNum > 1){
                    that.$refs.locationFailSelectAddress.show();
                } else {
                    if (that.$utils.isEmpty(this.coordinate.latitude) && that.$utils.isEmpty(this.coordinate.longitude)) {
                        this.$dialog({
                            title: '提示',
                            content: '请确认手机地理位置授权是否打开，或者【设置】-【企业微信】位置权限管理是否打开？',
                            cancelButton: false,
                            confirmText: '去开启',
                            onConfirm: async () => {
                                let userLocation = await this.$locations.openSetting();
                                if (userLocation['scope.userLocation']) {
                                    that.coordinate = await that.$locations.getCurrentCoordinate();
                                }
                            }
                        });
                        this.openSettingNum ++;
                        return
                    }
                }
            }
            if (!that.$utils.isEmpty(that.coordinate)) {
                await this.$locations.chooseLocation(that.coordinate.latitude, that.coordinate.longitude);
                this.getLocationFlag = true;
                return;
            } else {
                let userLocation = await that.$locations.openSetting();
                if (userLocation['scope.userLocation']) {
                    that.coordinate = await that.$locations.getCurrentCoordinate();
                    that.$store.commit('coordinate/setCoordinate', that.coordinate);
                    await that.initCoordinate();
                }
            }
        },
        async initCoordinate() {
            this.coordinate =  await this.$locations.getCurrentCoordinate();
        },
        addSellProduct(array) {
            this.sellProductArr = array.map(item => {
                let opt = {
                    prodId: item.prodId,
                    prodCode: item.prodCode,
                    prodName: item.prodName,
                    prodSeries: item.prodSeries,
                    prodSecSeries: item.prodSecSeries,
                    prodThirdSeries: item.prodThirdSeries,
                    status: item.status || 'Y',
                    supplierCategory: item.supplierCategory ? item.supplierCategory:'',
                    accntId: this.pageParam.data.id,
                    row_status: 'NEW',
                    rowId: item.id,
                    supplierManageMode: item.supplierManageMode,
                    supplierName: item.supplierName,
                    supplierId: item.supplierId,
                    id:item.id,
                    supplierAcctType: item.supplierAcctType || item.acctType
                }
                if (item.selfSupport) {
                    opt.selfSupport = item.selfSupport
                }
                return opt
            });
        },
        /**
         * 删除选择
         * <AUTHOR>
         * @date 2020-11-17
         */
        deleteIndex (index) {
            this.sellProductArr.splice(index, 1);
        },
        /**
         *  @description: 根据选择的省市区和输入的详细地址联想
         *  @author: 康丰强
         *  @date: 2023/05/29
         */
        async searchAddress(){
            if(this.$utils.isEmpty(this.selectAddressObj.addr)){
                this.$showError("请输入详细地址");
                return false;
            }
            const address = this.selectAddressObj.province + this.selectAddressObj.city + this.selectAddressObj.district + this.selectAddressObj.addr;
            const data = await this.$locations.getTMapSuggestion(address);
            this.suggestionAddressData = [...data.data];
        },
        /**
         *  @description: 选择某一个联想地址
         *  @author: 康丰强
         *  @date: 2023/05/29
         */
        selectAddress(item){
            const that = this;
            that.$set(item, '_checked', true);
            that.suggestionAddressData.filter(function (val) {
                if (val.address !== item.address) {
                    that.$set(val, '_checked', false);
                }
            });
        },
        /**
         *  @description: 确认某一个联想地址
         *  @author: 康丰强
         *  @date: 2023/05/29
         */
        async confirmAddress(){
            const that = this;
            const address = this.suggestionAddressData.filter((item1) => item1._checked === true);
            if(this.$utils.isEmpty(address)){
                this.$message.info("请选择一个地址");
                return false;
            }
            that.basicOption.province = address[0].province;
            that.basicOption.city = address[0].city;
            that.basicOption.district = address[0].district;
            that.basicOption.address = address[0].title;
            that.basicOption.fifthGLocationFailStatus = 'Y';
            that.$refs.locationFailSelectAddress.hide();
        }
    }
}
</script>

<style lang="scss">
.edit-approve-page {
    .content-container{
        background: $bg-color-lite;
        .basics-info {
            padding-top: 90px;
        }
        .contacts-info {
            padding-top: 50px;
        }
        .sale-product {
            padding-top: 50px;
        }
    }
    .select-box {
        @include flex-start-center;
        border-bottom: 1px solid #F2F2F2;
        .select-left {
            width: 100%;
            padding-left: 24px;
            .prod-num {
                text {
                    font-family: PingFangSC-Regular,serif;
                    font-size: 28px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    line-height: 28px;
                    background: #A6B4C7;
                    border-radius: 8px;
                    padding: 6px 12px;
                }
                margin-top: 6px;
                margin-bottom: 20px;
            }
            .store-name {
                width: 100%;
                font-family: PingFangSC-Regular,serif;
                font-size: 32px;
                color: #262626;
                letter-spacing: 0;
                font-weight: bold;
            }
            .store-supplier {
                padding-top: 8px;
                font-size: 28px;
                color: #262626;
                letter-spacing: 0;
            }
        }
    }
    .bottom-sticky {
        .sticky {
            width: 100%;
            /*deep*/.link-button {
            width: 94%;
            height: 96px;
            margin-right: 24px;
            margin-left: 24px;
            }
        }
    }
    .location-select-address {
        width: 100% !important;
        .link-dialog-content .link-dialog-body {
            padding: 0 !important;
            -webkit-flex: 1;
            -ms-flex: 1;
            flex: 1;
            font-size: 28px;
            overflow: hidden;
            word-break: break-all;
        }
    }
    .address-t {
        width: 654px;
        height: 900px;
        border-radius: 16px;
        background-color: white;
        .list-container {
            height: 560px;
            .list-item {
                display: flex;
                border-bottom: 1px solid #F2F2F2;

                .left-content {
                    display: inline-block;
                    width: 75%;

                    .row-1 {

                        .name{
                            font-family: PingFangSC-Semibold,serif;
                            font-size: 28px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 32px;
                            margin-left: 24px;
                            margin-top: 20px;
                        }
                        .address{
                            margin-left: 24px;
                            margin-top: 10px;
                            font-family: PingFangSC-Regular,serif;
                            font-size: 24px;
                            color: #8C8C8C;
                            letter-spacing: 0;
                            line-height: 32px;
                            margin-bottom: 10px;
                        }
                    }

                    .row-2 {
                        width: 100%;
                        margin-top: 1vh;

                        .date {
                            color: gray;
                            font-size: 12px;
                        }
                    }
                }

                .right-content {
                    padding-left: 20px;
                    display: inline-block;
                    width: 15%;
                    line-height: 92px;
                    text-align: right;
                }
            }
        }

        .address-v {
            width: 100%;

            .t-left {
                padding: 20px;
                height: 60px;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #333333;
                    letter-spacing: 0;
                    line-height: 44px;
                    width: 25%;
                    float: left;

                }
                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                    text-align: right;
                    line-height: 44px;
                    width: 50%;
                    float: left;
                }
                .ent-wrap{
                    color: $main-color;
                    white-space: nowrap;
                    display: inline-block;
                    height: 60px;
                    line-height: 60px;
                    width: 20%;
                    float: left;
                    text-align: center;
                }
            }
        }
    }
}
</style>
