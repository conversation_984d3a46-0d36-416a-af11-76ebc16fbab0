<template>
    <link-page class="edit-financial-page">
        <link-form :rules="formRules" ref="form" :value="financialOption">
            <link-form-item label="客户性质" required :disabled="changeFlag">
                <view class="invoice-category">
                    <view>
                        <link-checkbox  v-model="financialOption.invoiceCategory" trueValue="company" falseValue="indivisual" class="basic-check" @change="clearTypeVal(financialOption)"/> 公司
                    </view>
                    <view class="checkbox-right">
                        <link-checkbox v-model="financialOption.invoiceCategory" trueValue="indivisual" falseValue="company" class="basic-check" @change="clearTypeVal(financialOption)"/> 个人
                    </view>
                </view>
            </link-form-item>
            <link-form-item label="类型" required>
                <link-lov type="INVOICE" v-model="financialOption.credentType" :parentVal="financialOption.invoiceCategory" parentType="INVOICE_CATE" :excludeLovs="getExcludeLov(financialOption)"/>
            </link-form-item>
            <view class="basics-container" v-if="financialOption.invoiceCategory !== 'indivisual'">
                <view class="basics-column">
                    <view class="label"><text class="asterisk">*</text>营业执照照片</view>
                    <view class="value">
                        <link-img :parentId="financialOption.id"
                                  ref="linkImgBusiness"
                                  v-if="licenseArr"
                                  img-key="attachmentPath"
                                  :realDeleteFlag="pageParam.data.auditStatus === 'failed'"
                                  :new-flag="(pageParam.editFlag !== 'edit' || $utils.isEmpty(licenseArr)) || (pageParam.editFlag === 'edit' && basicOption.acctStage !== 'ykf')"
                                  :del-flag="(pageParam.editFlag !== 'edit' || deepCopyParams.length === 0) || (pageParam.editFlag === 'edit' && basicOption.acctStage !== 'ykf')"
                                  :pathKeyArray="licenseArr"
                                  @imgUploadSuccess="financialPhoto"
                                  @imgUploadCacheSuccess="financialPhotoCache"
                                  @softDelete="softDelete"
                                  moduleType="billTitlePhoto"></link-img>
                    </view>
                </view>
            </view>
            <view class="basics-container" v-if="financialOption.invoiceCategory === 'indivisual'">
                <view class="basics-column">
                    <view class="label"><text class="asterisk">*</text>身份证人像面</view>
                    <view class="value">
                        <link-img :parentId="financialOption.id"
                                  ref="imgIdCardBack"
                                  img-key="attachmentPath"
                                  @softDelete="softDeleteCardBack"
                                  :realDeleteFlag="pageParam.data.auditStatus === 'failed'"
                                  :pathKeyArray="idCardBackArr"
                                  :new-flag="idCardBackArr.length === 0"
                                  :del-flag="pageParam.editFlag !== 'edit' || (pageParam.editFlag === 'edit' && basicOption.acctStage !== 'ykf')"
                                  @imgUploadSuccess="idCardBack"
                                  :count="1"
                                  :isCount="true"
                                  moduleType="IDCardBack"></link-img>
                    </view>
                </view>
                <view class="basics-column">
                    <view class="label"><text class="asterisk">*</text>身份证国徽面</view>
                    <view class="value">
                        <link-img :parentId="financialOption.id"
                                  img-key="attachmentPath"
                                  @softDelete="softDeleteCardFront"
                                  :pathKeyArray="idCardFrontArr"
                                  :realDeleteFlag="pageParam.data.auditStatus === 'failed'"
                                  :new-flag="idCardFrontArr.length === 0"
                                  :del-flag="pageParam.editFlag !== 'edit' || (pageParam.editFlag === 'edit' && basicOption.acctStage !== 'ykf')"
                                  @imgUploadSuccess="idCardFront"
                                  :count="1"
                                  :isCount="true"
                                  moduleType="IDCardFront"></link-img>
                    </view>
                </view>
            </view>
            <link-form-item :label="financialOption.invoiceCategory === 'indivisual' ? '姓名' : '营业执照名称'" required :disabled="changeFlag || canEditImg"
                            field="billTitle" :rules="financialOption.invoiceCategory === 'indivisual' ? personalValidator : billTitleValidator">
                <link-input :placeholder="financialOption.invoiceCategory === 'indivisual' ? '请输入姓名' : '请输入营业执照名称'" v-model="financialOption.billTitle"/>
            </link-form-item>
          <link-form-item label="身份证"
                          required
                          :disabled="changeFlag"
                          field="creditNo"
                          v-if="financialOption.invoiceCategory === 'indivisual'"
                          :rules="Validator.cardId()">
            <link-input placeholder="请输入身份证号" v-model="financialOption.creditNo"/>
          </link-form-item>
          <link-form-item label="统一社会信用代码"
                          v-if="financialOption.invoiceCategory !== 'indivisual'"
                          required
                          :disabled="changeFlag || canEditImg"
                          field="creditNo"  :rules="customValidator">
            <view  class="link-input-scan">
              <link-input v-model="financialOption.creditNo" placeholder="请输入统一社会信用代码"></link-input>
              <view class="scan-icon-box" @tap="tapSuffixIcon">
                <link-icon style="color: #2F69F8" icon="icon-scan" class="link-location" />
              </view>
            </view>
          </link-form-item>
            <link-form-item label="开户行名称"
                            field="accountBankName" :rules="accountBankNameValidator"
                            :required="['ValueAddedTax','ElectronicSpecialInvoices','AllElectricTickets'].includes(financialOption.credentType)">
                <link-input placeholder="请输入开户行名称" v-model="financialOption.accountBankName"/>
            </link-form-item>
            <link-form-item label="银行账户"
                            :required="['ValueAddedTax','ElectronicSpecialInvoices','AllElectricTickets'].includes(financialOption.credentType)">
                <link-number-keyboard placeholder="请输入银行账户" v-model="financialOption.mainBillAccntId" stringValue/>
            </link-form-item>
            <link-form-item label="注册电话"
                            field="billPhone"
                            :required="['ValueAddedTax','ElectronicSpecialInvoices','AllElectricTickets'].includes(financialOption.credentType)">
                <link-number-keyboard placeholder="请输入注册电话" v-model="financialOption.billPhone" stringValue/>
            </link-form-item>
            <link-form-item label="公司注册地址"
                            :required="['ValueAddedTax','ElectronicSpecialInvoices','AllElectricTickets'].includes(financialOption.credentType)"
                            :disabled="pageParam.editFlag === 'edit' && !$utils.isEmpty(pageParam.data.billAddr) && basicOption.acctStage === 'ykf'">
                <link-input placeholder="请输入公司注册地址" v-model="financialOption.billAddr"/>
            </link-form-item>
            <link-form-item label="注册日期" v-if="financialOption.invoiceCategory !== 'indivisual'">
                <link-input placeholder="请输入注册日期" v-model="financialOption.registrationDate"/>
            </link-form-item>
            <link-form-item label="经营期限" v-if="financialOption.invoiceCategory !== 'indivisual'">
                <link-input placeholder="请输入经营期限" v-model="financialOption.period"/>
            </link-form-item>
            <link-form-item label="法人姓名" v-if="financialOption.invoiceCategory !== 'indivisual'">
                <link-input placeholder="请输入法人姓名" v-model="financialOption.person"/>
            </link-form-item>
            <link-form-item label="经营业务范围" vertical v-if="financialOption.invoiceCategory !== 'indivisual'">
                <link-textarea placeholder="请输入经营业务范围" v-model="financialOption.business"/>
            </link-form-item>
            <link-form-item label="注册资本" v-if="financialOption.invoiceCategory !== 'indivisual'">
                <link-input placeholder="请输入注册资本" v-model="financialOption.capital"/>
            </link-form-item>
            <link-form-item label="联行号" :rules="verifyTaxpayerNumber" :arrow="false" field="taxpayerNumber">
                <link-number-keyboard placeholder="请输入联行号" v-model="financialOption.taxpayerNumber" stringValue/>
            </link-form-item>
            <list-title v-if="invoiceFlag"></list-title>
            <link-form-item label="收票人姓名" :required="collectFlag">
                <link-input placeholder="请输入收票人姓名" v-model="financialOption.receiveBillContact"/>
            </link-form-item>
            <link-form-item label="收票人联系电话" field="receiveBillPhone" :required="collectFlag">
                <link-input placeholder="请输入收票人联系电话" v-model="financialOption.receiveBillPhone"/>
            </link-form-item>
            <link-form-item label="收票人邮寄地区" :required="['PlainInvoice','ValueAddedTax'].includes(financialOption.credentType)">
                <link-address :province.sync="financialOption.receiveBillProvince"
                              :city.sync="financialOption.receiveBillCity"
                              :district.sync="financialOption.receiveBillDistrict"/>
            </link-form-item>
            <link-form-item label="收票人邮寄地址" :required="['PlainInvoice','ValueAddedTax'].includes(financialOption.credentType)">
                <link-input placeholder="请输入收票人邮寄地址" v-model="financialOption.receiveBillAddr" :nativeProps="{maxlength: 300}"/>
            </link-form-item>
            <link-form-item label="电票接收人姓名" field="fixedName">
                <link-input placeholder="请输入电票接收人姓名" v-model="financialOption.fixedName"/>
            </link-form-item>
            <link-form-item label="电票接收邮箱" field="receiveBillEmail">
                <link-input placeholder="请输入电票接收邮箱" v-model="financialOption.receiveBillEmail"/>
            </link-form-item>
            <link-form-item label="电票接收电话">
                <link-input placeholder="请输入电票接收电话" v-model="financialOption.ticketingPhone" stringValue/>
            </link-form-item>
            <list-title></list-title>
        </link-form>
        <view class="basics-container">
            <view class="basics-column">
                <view class="label">附件</view>
                    <link-img :parentId="financialOption.id"
                              ref="annexRef"
                              v-if="showImgList"
                              img-key="attachmentPath"
                              :realDeleteFlag="realDeleteFlag"
                              :new-flag="(pageParam.editFlag !== 'edit' || $utils.isEmpty(showImgList)) || (pageParam.editFlag === 'edit' && basicOption.acctStage !== 'ykf')"
                              :del-flag="(pageParam.editFlag !== 'edit' || deepCopyParams.length === 0) || (pageParam.editFlag === 'edit' && basicOption.acctStage !== 'ykf')"
                              :pathKeyArray="showImgList"
                              isCount
                              @imgUploadSuccess="annexSuccess"
                              @softDelete="annexDelete"
                              :noGetImgKeyList='true'
                              moduleType="accountList"></link-img>
            </view>
            <view class="basics-column" v-if="pageParam.isShiJiaZhuang">
                <view class="label"><text class="asterisk">*</text>客户资料申请表扫描件</view>
                <view class="value">
                    <link-img :parentId="financialOption.id"
                              :pathKeyArray="customerInformationArr"
                              @softDelete="softDeletePhoto"
                              :realDeleteFlag="realDeleteFlag"
                              img-key="attachmentPath"
                              :new-flag="true"
                              :del-flag="true"
                              @imgUploadSuccess="imgUploadSuccessBack"
                              moduleType="customerInformation"></link-img>
                </view>
            </view>
            <view v-if="pageParam.isShiJiaZhuang && financialOption.invoiceCategory === 'company'">
                <view class="basics-column">
                    <view class="label"><text class="asterisk">*</text>营业执照复印件</view>
                    <view class="value">
                        <link-img :parentId="financialOption.id"
                                  :pathKeyArray="copyBusinessLicenseArr"
                                  @softDelete="softDeletePhoto"
                                  :realDeleteFlag="realDeleteFlag"
                                  img-key="attachmentPath"
                                  :new-flag="true"
                                  :del-flag="true"
                                  @imgUploadSuccess="imgUploadSuccessBack"
                                  moduleType="copyBusinessLicense"></link-img>
                    </view>
                </view>
                <view class="basics-column">
                    <view class="label"><text class="asterisk">*</text>开户许可证</view>
                    <view class="value">
                        <link-img :parentId="financialOption.id"
                                  :pathKeyArray="openingPermitArr"
                                  @softDelete="softDeletePhoto"
                                  :realDeleteFlag="realDeleteFlag"
                                  img-key="attachmentPath"
                                  :new-flag="true"
                                  :del-flag="true"
                                  @imgUploadSuccess="imgUploadSuccessBack"
                                  moduleType="openingPermit"></link-img>
                    </view>
                </view>
                <view class="basics-column">
                    <view class="label"><text class="asterisk">*</text>开票资料</view>
                    <view class="value">
                        <link-img :parentId="financialOption.id"
                                  :pathKeyArray="invoicingDataArr"
                                  @softDelete="softDeletePhoto"
                                  :realDeleteFlag="realDeleteFlag"
                                  img-key="attachmentPath"
                                  :new-flag="true"
                                  :del-flag="true"
                                  @imgUploadSuccess="imgUploadSuccessBack"
                                  moduleType="invoicingData"></link-img>
                    </view>
                </view>
            </view>
            <view v-if="pageParam.isShiJiaZhuang && financialOption.invoiceCategory === 'indivisual'">
                <view class="basics-column">
                    <view class="label"><text class="asterisk">*</text>个人银行卡复印件</view>
                    <view class="value">
                        <link-img :parentId="financialOption.id"
                                  :pathKeyArray="copyBankCardArr"
                                  @softDelete="softDeletePhoto"
                                  :realDeleteFlag="realDeleteFlag"
                                  img-key="attachmentPath"
                                  :new-flag="true"
                                  :del-flag="true"
                                  @imgUploadSuccess="imgUploadSuccessBack"
                                  moduleType="copyBankCard"></link-img>
                    </view>
                </view>
            </view>
        </view>
        <view class="blank"></view>
        <link-sticky class="bottom-sticky">
            <link-button block size="large" @tap="changeFlag ? insertChangeFinancial() : submit()" autoLoading :shadow="shadow">提交</link-button>
        </link-sticky>
    </link-page>
</template>

<script lang="jsx">
    import linkImg from '../../core/lnk-img/lnk-img.vue'
    import ApproveUploadDialog from '../new-construction/components/approve-upload-dialog.vue';
    import {PageCacheManager} from "../../../utils/PageCacheManager";
    import changeFinancialDialog from '../../terminal/terminal/components/approve-upload-dialog.vue';
    import {ComponentUtils} from 'link-taro-component';

    const waitTime = 5;
    export default {
        name: "edit-financial-page",
        data () {
            const basicOption = {
                acctType: null,                                                        // 客户一级分类
                acctCategory: null,                                                    // 客户二级分类
                subAcctType: null,                                                     // 客户三级分类
                acctName: '',                                                          // 门头名称
                simName: null,                                                        // 客户简称
                province: null,                                                        // 省
                city: null,                                                            // 市
                district: null,                                                        // 区
                address: null,                                                         // 详细地址
                storePicPreKey: null,                                                  // 门头照片
                chainStoreFlag: 'N',                                                   // 是否为连锁模式
                chainHeadStoreFlag: 'N',                                               // 是否为连锁总店
                chainHeadStoreName: null,                                              // 连锁总店名称
                joinFlag: 'Y',                                                         // 是否已合作
                acctLevel: null,                                                       // 容量级别
                appreciationFlag: null,                                                // 是否为品鉴基地
                capacityLevel: null,                                                   // 容量级别
                imageRoomNum: null,                                                    // 形象包间数
                roomTotalNum: null,                                                    // 包间总数
                hallTotalNum: null,                                                    // 大厅总数
                xAttr50: null,                                                         // 店区位
                area: null,                                                            // 店面积
                comments: null,                                                        // 备注
                postnId: null,                                                         // 职位id
                orgId: null,                                                           // 组织id
                acctStage: null,                                                       // 客户阶段
                directlyFlag: null,                                                    // 是否存在子公司供货
                creditChangedFlag: null,                                               // 税号/身份证号是否改变，用以判断是否走唯一性校验，父客户等逻辑
                chainChangedFlag: null,                                                // 校验当前终端是否从连锁模式变为普通终端，重走唯一性校验，用于获取不同的校验返回信息
                saleCateList: [],                                                      // 用于校验[是否子公司供货]和实际所售产品数据是否匹配
                acctStatus: null,                                                      // 状态
                storePicId : null,                                                     // 门头照片的id
                auditStatus : null,                                                    // 审批状态
            };
            const initialData = {
                // 按钮等待时间
                buttonTime: 0,
                isXinJiuYeOrRongCheng: ['5600','1210','1204','5910'].includes(this.pageParam.data.mdmCompanyCode), //国窖、新酒业、怀旧、蓉城
                isOnlyTeQu: this.pageParam.data.mdmCompanyCode==='5137', //特曲
                isDaHongYongJiao: ['5161','5903','5902','5151','5153'].includes(this.pageParam.data.mdmCompanyCode), //大成、鸿庐、永粮、窖龄、酒聚
                isNewHuaiJiu: ['1204','5910'].includes(this.pageParam.data.mdmCompanyCode), //怀旧、蓉城
                financialOption: {
                    credentType: 'ValueAddedTax',                                          // 开票类型
                    billTitle: null,                                                       // 营业执照名称
                    creditNo: null,                                                        // 统一社会信用代码
                    accountBankName: null,                                                 // 开户行名称
                    mainBillAccntId: null,                                                 // 银行账户
                    billPhone: null,                                                       // 注册电话
                    billAddr: null,                                                        // 公司注册详细地址
                    taxpayerNumber: null,                                                  // 联行号
                    multiAcctFlag: 'Y',                                                    // 是否一户多开
                    multiAcctMainFlag : 'N',                                               // 是否为主户头
                    id: '',                                                                // id
                    multiAcctMainId: this.pageParam.data.id,                                // 主户头ID
                },
                basicOption,
                shadow: true,                                                           // 按钮阴影
                formRules: {
                    // 电话号码校验
                    receiveBillPhone: this.Validator.phone(),
                    // 邮箱校验
                    receiveBillEmail: this.Validator.email(),
                },
                flag: {
                    disabled: false,
                    readonly: true,
                    formDisabled: false,
                    formReadonly: false,
                },
                changeFinancialDialog: false,
                oneClick: true,
                cloneChangeInfo: null,
                pathKeyArray: [],
                licenseArr: [],
                idCardFrontArr: [],
                idCardBackArr: [],
                customerInformationArr: [],
                copyBankCardArr: [],
                copyBusinessLicenseArr: [],
                openingPermitArr: [],
                invoicingDataArr: [],
                submitFlag: true,
                destroyedFlag: true,
                deepCopyParams: [],
                saleCateList: [],
                onceClickFlag: true,                                                                 // 防抖控制参数
                changeFlag: false,                                                                   // 变更财务标志
                terminalData: null,                                                                  // 终端客户信息
                realDeleteFlag: false,                                                               // 是否真删除营业执照招聘控制参数
                attachmentId: [],                                                                    // 携带客户附件图片id
                accountImgArray: {},                  //附件图片
                annexArr:[],//附件内容
                uploadDialog: false,              // 附件上传附件弹框
                approveArr: [],                    //终端数组
                cacheLicenseArr:[],//适用变更财务信息，上传了图片但没提交，进日缓存的情况
                contentList:[],//联系人列表
                isYangShengOrYouXuan: ['1612'].includes(this.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode),
                orcEntranceFlag: 'N',  // 是否调用OCR营业执照识别接口
                orcIdCardFlag: 'N', // 是否调用OCR身份证识别接口
                isPhotoRecognition: false
            }
            return PageCacheManager.getInitialData({
                ctx: this,
                path: 'terminal2/edit-construction/edit-financial-page.vue',
                title: '终端客户-财务信息',
                initialData,
            })

        },
        components: {linkImg,ApproveUploadDialog,changeFinancialDialog},
        watch: {
            financialOption (val) {
                return val
            }
        },
        computed: {
            canEditImg(){
                return Boolean(this.pageParam.data.acctStage == 'xk' && this.pageParam.data.initialApprovedTime && this.pageParam.editFlag === 'edit')
            },
            /**
             * 收票人姓名、联系电话
             * <AUTHOR>
             * @date 2023-12-4
             */
            collectFlag() {
                if(this.isXinJiuYeOrRongCheng){
                    return true;
                } else if(this.isDaHongYongJiao){
                    return this.financialOption.credentType !== 'ElectronicInvoices' || this.financialOption.invoiceCategory === 'company';
                } else{
                    return ['PlainInvoice','ValueAddedTax'].includes(this.financialOption.credentType);
                }
            },
            /**
             * 电子普票不展示部分字段
             * <AUTHOR>
             * @date 2022-6-9
             */
            invoiceFlag() {
                return this.financialOption.credentType !== 'ElectronicInvoices';
            },
            /**
             * 附件展示列表
             * <AUTHOR>
             * @date 2023/09/04
             */
            showImgList(){
                const id = this.financialOption.id
                let newArr= []
                if(this.annexArr.length){
                    newArr =  this.annexArr
                }
                newArr = [...new Set([...newArr])]
                return  newArr
            }
        },
        async created() {
            this.deepCopyParams = this.$utils.deepcopy(this.pageParam.data.billTitleImgArr);
            // @ edit by 谭少奇 2023/09/06 已有主户头添加子户头清空携带附件内容
            if(this.pageParam.editFlag != 'add'){
                this.annexArr = JSON.parse(JSON.stringify(this.pageParam.data.annexArr))
            }else{
                this.annexArr = []
            }
            if (this.pageParam.editFlag !== 'edit' &&  !this.pageParam.__fromCache) {
                this.financialOption.id = await this.$newId();                                                              // id
                this.accountImgArray[this.financialOption.id] = {
                    attachmentObj: [],
                    accountId: this.financialOption.id
                }
            }else if (this.pageParam.__fromCache){
                this.accountImgArray[this.financialOption.id] = {
                    attachmentObj: [],
                    accountId: this.financialOption.id
                }
            }
            if(this.pageParam.data.auditStatus === 'failed') {
                this.realDeleteFlag = true;
                this.destroyedFlag = false;
            }
            if(this.pageParam.data.acctStage == 'xk' && this.pageParam.data.initialApprovedTime){
                this.realDeleteFlag = true;
                this.destroyedFlag = false;
            }
            if (this.pageParam.editFlag === 'edit' && this.pageParam.data.acctStage === 'ykf') {
                this.changeFlag = true;
                this.$taro.setNavigationBarTitle({title: `变更财务信息`});
                if (this.pageParam.data.invoiceCategory === 'company' && !this.$utils.isEmpty(this.pageParam.data.billTitleImgArr)) {
                    this.licenseArr = this.pageParam.data.billTitleImgArr;
                    this.attachmentId = this.pageParam.data.billTitleImgArr.map(item => item.id);
                    if(this.pageParam.__fromCache && !this.$utils.isEmpty(this.cacheLicenseArr)){
                        this.cacheLicenseArr.forEach(item =>{
                            this.attachmentId = this.attachmentId.filter(item2 => item2 !== item.id)
                        })
                    }
                }
                if (this.pageParam.data.invoiceCategory === 'indivisual' && !this.$utils.isEmpty(this.pageParam.data.iDCardArr)) {
                    this.idCardFrontArr = this.pageParam.data.iDCardArr.filter(item => item.moduleType === 'IDCardFront');
                    this.idCardBackArr = this.pageParam.data.iDCardArr.filter(item => item.moduleType === 'IDCardBack');
                    this.attachmentId = this.pageParam.data.iDCardArr.map(item => item.id);
                }
                if(this.pageParam.isShiJiaZhuang && this.pageParam.data.auditStatus !== 'failed2') {
                    for (let key in this.pageParam.shiJiaZhuangType) {
                        const type = key + 'Arr'
                        this[type] = this.pageParam.data[type]
                        if(this.$utils.isNotEmpty(this[type])) this.attachmentId = this.attachmentId.concat(this[type].map(item => item.id))
                    }
                }
                this.queryChangeFinancial();
            } else {
                this.initData();
                this.querySaleProduct();
                this.queryContent();
            }
            // 税号不可编辑时，上传营业执照照片不调用OCR接口，也不调用营业执照验证接口
            const orcEntranceFlag = await this.$utils.getCfgProperty('OCR_ENTRANCE_FLAG');
            this.orcEntranceFlag = this.changeFlag ? 'N' : orcEntranceFlag;
            // 身份证号不可以编辑时，上传身份证照不调用OCR接口
            const orcIdCardFlag = await this.$utils.getCfgProperty('OCR_IDCARD_FLAG');
            this.orcIdCardFlag = this.changeFlag ? 'N' : orcIdCardFlag;
        },
        destroyed () {
            if (this.destroyedFlag) {
                this.pageParam.data.billTitleImgArr = this.deepCopyParams;
                this.imgDestroyed()
            }
            if (this.pageParam.data.auditStatus === 'failed' && this.pageParam.data.multiAcctMainFlag !== 'Y') {
                this.$bus.$emit('reloadChildAccount'); // 刷新子户头图片
            }
            this.$utils.hideLoading();
        },
        methods: {
            /**
             * 根据选择类型判断对应选项内容
             * <AUTHOR>
             * @date 2024-09-09
             * @param formOption 对应户头内容
             */
            getExcludeLov(formOption){
                const {isOnlyTeQu, isDaHongYongJiao, isXinJiuYeOrRongCheng, isNewHuaiJiu} = this
                if (isXinJiuYeOrRongCheng && !isNewHuaiJiu) {
                    return []; // 如果isXinJiuYeOrRongCheng为真，则直接返回空数组
                }
                if (formOption.invoiceCategory === 'company') {
                  if(isOnlyTeQu || isNewHuaiJiu){
                      return ['ElectronicInvoices', 'ElectronicSpecialInvoices', 'ValueAddedTax', 'PlainInvoice'];
                  }
                  if(isDaHongYongJiao){
                      return ['ElectronicInvoices', 'ElectronicSpecialInvoices'];
                  }

                }else if(formOption.invoiceCategory === 'indivisual'){
                  if(isOnlyTeQu || isNewHuaiJiu){
                      return ['ElectronicInvoices', 'PlainInvoice'];
                  }
                  if(isDaHongYongJiao){
                      return ['ElectronicInvoices'];
                  }
                }
                return ['AllElectricInvoices', 'AllElectricTickets', 'AllElectricInvoicesPersional'];
            },
            uploadSuccess(flowAttachmentList) {
                this.oneClick = false
                this.changeFinancialApproval(this.cloneChangeInfo, flowAttachmentList)
            },
            clearTypeVal(formOption) {
                formOption.credentType = ''
            },
            /**
             * OCR识别营业执照/身份证照片
             * <AUTHOR>
             * @date	2023/10/27 16:08
             * @param actionType 识别类型：BizLicenseOCR(营业执照)、IDCardOCR(身份证)
             * @param imgInfo 识别的图片
             * @param index 识别的图片对应的图片数组下标
             * @param imgArr 所有图片
             */
            async phoneDistinguish(actionType, imgInfo, index, imgArr) {
                const path = this.$image.getSignedUrl(imgInfo.attachmentFilePath);
                const samllPath = this.$image.getSignedUrl(imgInfo.smallurl);
                const params = {
                    imageUrl: path,
                    smallImageUrl: samllPath,
                    actionType,
                    actionTypeName: actionType === 'BizLicenseOCR' ? '营业执照' : '身份证'
                }
                try {
                    const data = await this.$http.post('action/link/aiOcr/aiOcrEntrance', params, {
                        autoHandleError: false,
                        handleFailed: (res) => {
                            this.showInfoDialog('photo', res);
                        }
                    })
                    if (data.success) {
                        const info = JSON.parse(data.result.replyJson);
                        let ocrResult = {};
                        if (actionType === 'BizLicenseOCR') {
                            ocrResult = {
                                billTitle: info.Name,
                                creditNo: info.RegNum,
                                registrationDate: info.SetDate,
                                period: info.Period,
                                person: info.Person,
                                business: info.Business,
                                capital: info.Capital,
                                billAddr: info.Address
                            };
                        } else {
                            ocrResult = {
                                billTitle: info.Name,
                                creditNo: info.IdNum
                            };
                        }
                        this.financialOption = {
                            ...this.financialOption,
                           ...ocrResult
                        }
                        this.$utils.hideLoading();
                    } else {
                        this.$showError('图片无法识别，请重新拍照');
                    }
                } catch (e) {

                }
            },
            /**
           * @createdBy  张丽娟
           * @date  2021/3/9
           * @methods tapSuffixIcon
           * @para
           * @description  扫码获取营业执照注册号
           */
          async tapSuffixIcon(){
            if(this.changeFlag === true || this.canEditImg){
              return
            }
            const that = this;
            await wx.scanCode({
              onlyFromCamera: true,
              success(res) {
                let obj = {}
                if(res.result.indexOf('?') >-1 && res.result.indexOf('统一社会信用代码:') === -1 && res.result.indexOf('uniscid=')>-1){
                  let start = res.result.indexOf('?') + 1
                  let arr = res.result.slice(start).split('&')
                  arr.forEach(function(v){
                    var keyvalue=v.split("=");
                    var name=keyvalue[0];
                    var value=keyvalue[1];
                    if(name === 'uniscid'){
                      obj = {
                        val: value,
                      }
                    }else{
                      wx.showToast({
                        title: '扫码失败，请检查是否为正确的税号二维码！',
                        icon: 'none',
                        duration: 2000
                      });
                    }
                  })
                }else if(res.result.indexOf('统一社会信用代码:')>-1){
                  let arr = res.result.split(';')
                  arr.forEach(function(v){
                    var keyvalue=v.split(":");
                    var name=keyvalue[0];
                    var value=keyvalue[1];
                    if(name === '统一社会信用代码'){
                      obj = {
                        val: value,
                      }
                    }
                  })
                }else{
                  wx.showToast({
                    title: '扫码失败，请检查是否为正确的税号二维码！',
                    icon: 'none',
                    duration: 2000
                  });
                }
                that.financialOption.creditNo = obj.val;
              },
              fail(res){
                wx.showToast({
                  title: '未能识别税号二维码，请重新扫描!',
                  icon: 'none',
                  duration: 2000
                });
              }
            });

          },
            /**
             * 获取图片营业执照照片
             * <AUTHOR>
             * @date 2020-07-09
             */
            async getImgKeyList () {
                if(this.$utils.isEmpty(this.financialOption.id)){return }
                const that = this;
                let moduleTypeArr = '[IDCardFront, IDCardBack, billTitlePhoto]'
                if (this.pageParam.isShiJiaZhuang) moduleTypeArr = '[IDCardFront, IDCardBack, billTitlePhoto, customerInformation, copyBusinessLicense,openingPermit,invoicingData,copyBankCard]'
                let filtersRaw = [
                    {id: 'moduleType', property: 'moduleType', value: moduleTypeArr, operator: 'in'},
                    {id: 'headId', property: 'headId', value: `[${that.financialOption.id}]`, operator: 'in'}
                ];
                this.licenseArr = this.pageParam.data.billTitleImgArr;
                that.$http.post('action/link/attachment/queryByExamplePage', {
                    filtersRaw: filtersRaw,
                    uploadType: 'cos',
                    pageFlag: false,
                    sort: 'created',
                    order: 'desc',
                    queryFields: 'id,uploadType,attachmentPath,moduleType,headId,dataSource,created,smallurl'
                }).then((data) => {
                    if (data.success) {
                        that.financialAttachment =  data.rows;
                        data.rows.forEach(async item => {
                            if (item.moduleType === 'IDCardBack') {
                                await this.$image.deleteImage({id: item.id});
                            }
                            if (item.moduleType === 'IDCardFront') {
                                await this.$image.deleteImage({id: item.id});
                            }
                            if (item.moduleType === 'billTitlePhoto') {
                                await this.$image.deleteImage({id: item.id});
                            }
                            if(Object.keys(this.pageParam.shiJiaZhuangType).includes(item.moduleType)) {
                                let imgUrl = await this.$image.getSignedUrl(item.smallurl);
                                let opt = {
                                    imgUrl: imgUrl,
                                    ...item
                                };
                                const arrType = item.moduleType + 'Arr'
                                that[arrType].push(opt)
                            }
                        });
                    }
                })
            },
            /**
              * 软删除
              * <AUTHOR>
              * @date 1/19/21
              * @param index
              * @param item
            */
            async softDelete (index, item) {
                this.licenseArr.splice(index, 1);
                if (item.headId === this.financialOption.id) {
                    await this.$image.deleteImage({id: item.id});
                }
            },
            /**
              * 软删除
              * <AUTHOR>
              * @date 23/08/25
              * @param index
              * @param item
            */
            async annexDelete (index, item) {
                this.annexArr.splice(index, 1);
            },
            /**
              * 软删除
              * <AUTHOR>
              * @date 1/19/21
              * @param index
              * @param item
            */
            async softDeleteCardBack (index, item) {
                this.idCardBackArr.splice(index, 1);
                if (item.headId === this.financialOption.id) {
                    await this.$image.deleteImage({id: item.id});
                }
            },
            /**
              * 软删除
              * <AUTHOR>
              * @date 1/19/21
              * @param index
              * @param item
            */
            async softDeleteCardFront (index, item) {
                this.idCardFrontArr.splice(index, 1);
                if (item.headId === this.financialOption.id) {
                    await this.$image.deleteImage({id: item.id});
                }
            },
            softDeletePhoto (index, item) {
                const type = item.moduleType + 'Arr'
                this[type].splice(index, 1);
            },
            /**
             * 查询当前有效的财务变更数据
             * <AUTHOR>
             * @date 1/15/21
             * @param param
             */
            async queryChangeFinancial() {
                if(!!this.pageParam.__fromCache){return}
                let changeData = {
                    invoiceCategory: this.pageParam.data.invoiceCategory,
                    acctId: this.pageParam.data.id,
                    id: await this.$newId(),
                    credentType: this.pageParam.data.credentType,
                    creditNo: this.pageParam.data.creditNo,
                    billTitle: this.pageParam.data.billTitle,
                    accountBankName: this.pageParam.data.accountBankName,
                    mainBillAccntId: this.pageParam.data.mainBillAccntId,
                    billAddr: this.pageParam.data.billAddr,
                    billPhone: this.pageParam.data.billPhone,
                    taxpayerNumber: this.pageParam.data.taxpayerNumber,
                    receiveBillContact: this.pageParam.data.receiveBillContact,
                    receiveBillPhone: this.pageParam.data.receiveBillPhone,
                    receiveBillProvince: this.pageParam.data.receiveBillProvince,
                    receiveBillCity: this.pageParam.data.receiveBillCity,
                    receiveBillDistrict: this.pageParam.data.receiveBillDistrict,
                    receiveBillAddr: this.pageParam.data.receiveBillAddr,
                    fixedName: this.pageParam.data.fixedName,
                    receiveBillEmail: this.pageParam.data.receiveBillEmail,
                    ticketingPhone: this.pageParam.data.ticketingPhone,
                    registrationDate: this.pageParam.data.registrationDate,
                    period: this.pageParam.data.period,
                    person: this.pageParam.data.person,
                    business: this.pageParam.data.business,
                    capital: this.pageParam.data.capital,
                };
                this.$http.post('action/link/accntFinance/queryCurrentFinanceByAcctId', {acctId: this.pageParam.data.id}).then(data =>{
                    this.financialOption = changeData;
                    this.financialOption.row_status = this.pageParam.data.auditStatus === 'failed2' ? 'UPDATE' : 'NEW';
                    if (this.pageParam.data.auditStatus === 'failed2') {
                        this.financialOption.id = data.result.id
                        this.realDeleteFlag = true;
                        this.getImgKeyList()
                    }
                });
            },
            /**
              * 处理附件数据
              * <AUTHOR>
              * @date 1/19/21
              * @param param
            */
            dealAttachmentList () {
                let dealData = [];
                this.licenseArr.forEach(item => {
                    if (this.attachmentId.includes(item.id)) {
                        delete item.imgUrl;
                        item.headId = this.financialOption.id;
                        dealData.push(item)
                    }
                });
                this.idCardFrontArr.forEach(item => {
                    if (this.attachmentId.includes(item.id)) {
                        delete item.imgUrl;
                        item.headId = this.financialOption.id;
                        dealData.push(item)
                    }
                });
                this.idCardBackArr.forEach(item => {
                    if (this.attachmentId.includes(item.id)) {
                        delete item.imgUrl;
                        item.headId = this.financialOption.id;
                        dealData.push(item)
                    }
                });
                for (let key in this.pageParam.shiJiaZhuangType) {
                    const type = key + 'Arr'
                    this[type] && this[type].forEach(item => {
                        if (this.attachmentId.includes(item.id)) {
                            delete item.imgUrl;
                            item.headId = this.financialOption.id;
                            dealData.push(item)
                        }
                    })
                }
                return dealData
            },
            /**
             * 营业执照验真
             * <AUTHOR>
             * @date	2023/6/12 19:43
             * @param financial 财务信息
             */
            async checkInvoice(financial) {
                try {
                    const {success, licenseReal, result} = await this.$http.post('/action/link/aiOcr/getBaseinfoAiResult', {
                        creditNo: financial.creditNo, //税号
                        name: financial.billTitle //营业执照名称
                    });
                    if (success) {
                        return {
                            originCreditNo: financial.creditNo,
                            licenseReal,
                            licRealRes: result
                        };
                    } else {
                        return null;
                    }
                } catch (e) {

                }
            },
            /**
             * 营业执照校验弹框
             * <AUTHOR>
             * @date   2024/6/6 14:53
             * @param type 弹窗类型：licenseReal(营业执照校验失败)、photo(图片上传失败)
             * @param data 弹窗数据：billTitle(营业执照名称)、creditCode(税号)、result(图片上传失败信息)
             * @param originCreditNo 识别的税号
             */
            showInfoDialog(type, data, originCreditNo) {
                const that = this;
                this.buttonTime = waitTime;
                let timer = setInterval(() => {
                    that.buttonTime--;
                    if (that.buttonTime <= 0) {
                        that.buttonTime = 0;
                        clearTimeout(timer);
                    }
                }, 1000);
                const dfd = ComponentUtils.dfd();
                // 营业执照验证
                if (type === 'licenseReal') {
                    this.$dialog({
                        title: '提示',
                        content: () => {
                            return (
                                <view style="width:100%;font-size: 26rpx;padding: 10rpx;">
                                    <view>纳税人识别号：{data.creditCode}</view>
                                    <view style="margin-top: 10rpx;">营业执照名称：{data.biliTitle}</view>
                                    <view style="margin-top: 10rpx;">经营者姓名：{data.legalPerson}</view>
                                    <view style="font-size: 24rpx;margin-top: 10rpx;">
                                        {data.creditCode
                                            ? data.regMate === 'Yes'
                                                ? originCreditNo === data.creditCode
                                                    ? '根据工商注册号获取到的纳税人名称与填写的名称不一致，请点击复制按钮，进行修改！'
                                                    : '根据工商注册号获取到的纳税人识别号与填写的纳税人识别号不一致，请点击按钮复制，进行修改!'
                                                : '根据纳税人识别号获取到的营业执照名称不一致，请点击按钮复制，进行修改!'
                                            : '根据当前税号未查询到相关营业执照信息，请核对税号填写是否准确！'
                                        }
                                    </view>
                                </view>
                            )
                        },
                        slots: {
                            foot: (h, renderParam) => {
                                return (
                                    <block>
                                        <link-button label={`${data.creditCode ? '取消并复制' : '确认'}${this.buttonTime ? '(' + this.buttonTime + ')' : ''}`}
                                                     style="flex: 1"
                                                     disabled={Boolean(this.buttonTime)}
                                                     onTap={() => {renderParam.close(); dfd.reject()}}/>
                                    </block>
                                );
                            }
                            /*
                            <link-button label={`确认并提交${this.buttonTime ? '(' + this.buttonTime + ')' : ''}`}
                                                     style="flex: 1"
                                                     disabled={Boolean(this.buttonTime)}
                                                     onTap={() => {dfd.resolve(); renderParam.close()}}/>
                            */
                        }
                    });
                }
                // 上传营业执照图片
                if (type === 'photo') {
                    this.$dialog({
                        title: '提示',
                        content: data.result,
                        slots: {
                            foot: (h, renderParam) => {
                                return (
                                    <link-button label={`确定${this.buttonTime ? '(' + this.buttonTime + ')' : ''}`}
                                                 style="flex: 1"
                                                 disabled={Boolean(this.buttonTime)}
                                                 onTap={() => {renderParam.close(); dfd.reject()}}/>
                                );
                            }
                        }
                    });
                }
                return dfd.promise;
            },
            /**
             * 处理变更审核不通过附件数据
             * <AUTHOR>
             * @date	2023/10/12 9:35
             */
            dealFailed2AttachmentList() {
                !this.financialOption.attachmentList && (this.financialOption.attachmentList = []);
                this.licenseArr.forEach(item => {
                    if (item.headId !== this.financialOption.id) {
                        delete item.imgUrl;
                        item.headId = this.financialOption.id;
                        this.financialOption.attachmentList.push(item);
                    }
                });
                this.idCardFrontArr.forEach(item => {
                    if (item.headId !== this.financialOption.id) {
                        delete item.imgUrl;
                        item.headId = this.financialOption.id;
                        this.financialOption.attachmentList.push(item);
                    }
                });
                this.idCardBackArr.forEach(item => {
                    if (item.headId !== this.financialOption.id) {
                        delete item.imgUrl;
                        item.headId = this.financialOption.id;
                        this.financialOption.attachmentList.push(item);
                    }
                });
            },
            /**
              * 插入财务变更记录
              * <AUTHOR>
              * @date 1/15/21
            */
            async insertChangeFinancial() {
                // 营业执照识别时不允许提交
                if (this.isPhotoRecognition) {
                    return;
                }
                let licRealResult = {};
                let oriCreditNo = '';
                try {
                    this.$utils.showLoading();
                    if (this.checkData()) {
                        await this.$refs.form.validate()
                        if (this.pageParam.data.auditStatus !== 'failed2') {
                            this.financialOption.attachmentList = this.dealAttachmentList();
                        } else {
                            this.dealFailed2AttachmentList();
                        }
                        // 税号不可编辑时，上传营业执照照片不调用OCR接口，也不调用营业执照验真接口
                        if (!this.changeFlag && this.financialOption.invoiceCategory === 'company') {
                            // 营业执照验真
                            const {licenseReal, licRealRes, originCreditNo} = await this.checkInvoice(this.financialOption);
                            licRealResult = licRealRes;
                            oriCreditNo = originCreditNo;
                            this.financialOption.licenseReal = licenseReal;
                            if (this.financialOption.licenseReal === 'No') {
                                this.$utils.hideLoading();
                                await this.showInfoDialog('licenseReal', licRealRes, originCreditNo);
                                this.$utils.showLoading();
                            }
                        }
                        const nowTime = await this.$utils.getServerTime();
                        this.financialOption.businessInfoTime = this.$date.format(new Date(nowTime), 'YYYY-MM-DD HH:mm:ss');
                        let data = await this.$http.post('action/link/accntFinance/upsert', this.financialOption);
                        if (data.success) {
                            this.destroyedFlag = false;
                                this.cloneChangeInfo = JSON.parse(JSON.stringify(data.result || data.newRow))
                                this.changeFinancialDialog = true
                                // @make edit by 谭少奇 2023/09/05 变更财务信息取新传附件内容
                                this.changeFinancialApproval(this.cloneChangeInfo, this.showImgList)
                        }
                    }
                } catch (e) {
                    this.$utils.hideLoading();
                    // 取消并复制
                    if (this.financialOption.licenseReal === 'No') {
                        // 根据工商注册号进行查询，复制税号
                        if (licRealResult.regMate === 'Yes' && oriCreditNo !== licRealResult.creditCode) {
                            wx.setClipboardData({data: licRealResult.creditCode});
                        } else if (licRealResult.biliTitle) {
                            // 复制营业执照名称
                            wx.setClipboardData({data: licRealResult.biliTitle});
                        }
                    }
                    //判断是否是前端校验表单出错
                    if(!e.field) this.$showError('财务变更失败，请稍后重试！');
                } finally {
                    this.$utils.hideLoading();
                }
            },
            /**
             * 插入财务变更审批记录
             * <AUTHOR>
             * @date 1/15/21
             * @param data
             */
            async changeFinancialApproval(data, list) {
                data.flowAttachmentList = list.map(item => ({attachmentId: item.id}))
                try {
                    let result = await this.$http.post('action/link/accntFinance/createFinanceChangeFlow', data);
                    this.oneClick = true
                    if (result.success) {
                        let param = {refreshFlag: true};
                        this.$nav.back(param);
                    }
                    if(this.pageParam.__fromCache){
                        this.$nav.backAll();
                    }
                } catch (e) {
                    this.financialOption.row_status = 'UPDATE';
                }
            },
            /**
              * 查询所售产品
              * <AUTHOR>
              * @date 2020-11-13
            */
            querySaleProduct () {
                let accntId = this.pageParam.editFlag === 'add' ? this.pageParam.data.id : this.pageParam.mainAcctData.id;
                this.$http.post('action/link/saleCategory/queryByExamplePage', {
                    pageFlag: true,
                    onlyCountFlag: false,
                    oauth: 'ALL',
                    sort: 'created',
                    order: 'desc',
                    accntId: accntId
                }, {
                    handleFailed: (error) => {
                        console.log('查询所售产品失败', error);
                    }
                }).then(data => {
                    data.rows.forEach(item => {
                        delete item.id;
                        item.accntId = this.financialOption.id;
                        item.row_status = 'NEW';
                    });
                    this.saleCateList = data.rows;
                })
            },
            /**
             * 查询联系人
             * <AUTHOR>
             * @date 2020-11-13
             */
            queryContent () {
                let acctId = this.pageParam.editFlag === 'add' ? this.pageParam.data.id : this.pageParam.mainAcctData.id;
                let acctType = this.pageParam.editFlag === 'add' ? this.pageParam.data.acctType : this.pageParam.mainAcctData.acctType;
                this.$http.post('action/link/contacts/listByAcctId', {
                    pageFlag: true,
                    onlyCountFlag: false,
                    oauth: 'ALL',
                    sort: 'id',
                    order: 'desc',
                    attr1: acctId
                }, {
                    handleFailed: (error) => {
                        console.log('查询联系人失败', error);
                    }
                }).then(data => {
                    data.rows.forEach(async(item) => {
                        //2021-04-22前端生成联系人ID，并且把主户头联系人ID给子户头的parContactId字段，主户头联系人parContactId值为id
                        // 用于后续更新主户头的主要联系人时后台处理更新子户头的主要联系人
                        //delete item.id;
                        item.parContactId = item.id;
                        item.id = await this.$newId();
                        item.acctId = this.financialOption.id;
                        item.row_status = 'NEW';
                        item.belongTo = acctType;
                    });
                    this.contentList = data.rows;
                })
            },
            /**
              * 图片删除
              * <AUTHOR>
              * @date 2020-11-10
            */
            imgDestroyed () {
                this.licenseArr.forEach(async item => {
                    if (this.financialOption.id === item.headId) {
                        const option = {id: item.id};
                        await this.$image.deleteImage(option);
                    }
                })
            },
            /**
             * 校验基础数据
             * <AUTHOR>
             * @date 2020-09-15
             */
            checkData() {
                const that = this;
                if (that.$utils.isEmpty(that.financialOption.invoiceCategory)) {
                    that.$message['warn']('请选择客户类型');
                    return false
                }
                if (that.$utils.isEmpty(that.financialOption.credentType)) {
                    that.$message['warn']('请选择发票类型');
                    return false
                }
                if (that.$utils.isEmpty(that.financialOption.billTitle)) {
                    that.financialOption.invoiceCategory === 'indivisual' ? that.$message['warn']('请输入姓名'): that.$message['warn']('请输入营业执照名称');
                    return false
                }
                if (that.$utils.isEmpty(that.financialOption.creditNo)) {
                    that.financialOption.invoiceCategory === 'indivisual' ? that.$message['warn']('请输入身份证号'): that.$message['warn']('请输入统一社会信用代码');
                    return false
                }
                // 添加纳税人识别号或者身份证格式验证
                const creditNoReg = /^[A-HJ-NPQRTUWXY0-9]{15}$|^[A-HJ-NPQRTUWXY0-9]{17}$|^[A-HJ-NPQRTUWXY0-9]{18}$|^[A-HJ-NPQRTUWXY0-9]{20}$/g;
                //const idCard = /^[0-9]{18}$/g;
                //身份证号码15或者18位校验
                const regexp15 = /^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$/;
                const regexp18 = /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
                const flag = regexp15.test(that.financialOption.creditNo) || regexp18.test(that.financialOption.creditNo);
                if (that.financialOption.invoiceCategory === 'indivisual' && !flag) {
                    that.$message['warn']('请输入正确的身份证号');
                    return false;
                }
                if (that.financialOption.invoiceCategory !== 'indivisual' && !creditNoReg.test(that.financialOption.creditNo)) {
                    that.$message['warn']('请输入正确的统一社会信用代码');
                    return false;
                }
                if (['ValueAddedTax','ElectronicSpecialInvoices','AllElectricTickets'].includes(that.financialOption.credentType) && that.$utils.isEmpty(that.financialOption.accountBankName)) {
                    that.$message['warn']('请输入开户行名称');
                    return false
                }
                if (['ValueAddedTax','ElectronicSpecialInvoices','AllElectricTickets'].includes(that.financialOption.credentType) && that.$utils.isEmpty(that.financialOption.mainBillAccntId)) {
                    that.$message['warn']('请输入银行账户');
                    return false
                }
                if (['ValueAddedTax','ElectronicSpecialInvoices','AllElectricTickets'].includes(that.financialOption.credentType) && that.$utils.isEmpty(that.financialOption.billPhone)) {
                    that.$message['warn']('请输入注册电话');
                    return false
                }
                if (['ValueAddedTax','ElectronicSpecialInvoices','AllElectricTickets'].includes(that.financialOption.credentType) && that.$utils.isEmpty(that.financialOption.billAddr)) {
                    that.$message['warn']('请输入公司注册地址');
                    return false
                }
                if (this.collectFlag && that.$utils.isEmpty(that.financialOption.receiveBillContact)) {
                    that.$message['warn']('请输入收票人姓名');
                    return false
                }
                if (this.collectFlag && that.$utils.isEmpty(that.financialOption.receiveBillPhone)) {
                    that.$message['warn']('请输入收票人联系电话');
                    return false
                }
                if (['PlainInvoice','ValueAddedTax'].includes(that.financialOption.credentType) && (that.$utils.isEmpty(that.financialOption.receiveBillProvince) || that.$utils.isEmpty(that.financialOption.receiveBillCity) || that.$utils.isEmpty(that.financialOption.receiveBillDistrict))) {
                    that.$message['warn']('请输入收票人邮寄地区');
                    return false
                }
                if (['PlainInvoice','ValueAddedTax'].includes(that.financialOption.credentType) && that.$utils.isEmpty(that.financialOption.receiveBillAddr)) {
                    that.$message['warn']('请输入收票人邮寄地址');
                    return false
                }
                if (
                    (that.financialOption.credentType=== 'ElectronicInvoices' || that.financialOption.credentType=== 'ElectronicSpecialInvoices' ||
                    that.financialOption.credentType=== 'AllElectricTickets' || that.financialOption.credentType=== 'AllElectricInvoices' ||
                    that.financialOption.credentType=== 'AllElectricInvoicesPersional') && that.$utils.isEmpty(that.financialOption.receiveBillEmail) &&
                    that.$utils.isEmpty(that.financialOption.ticketingPhone)
                ) {
                    that.$message['warn']('请检查电票接收邮箱和电票接收电话');
                    return false
                }
                if (that.financialOption.invoiceCategory === 'company' && that.licenseArr.length === 0) {
                    that.$message['warn']('请上传营业执照照片');
                    return false
                }
                if (that.financialOption.invoiceCategory === 'indivisual' && that.idCardBackArr.length === 0) {
                    that.$message['warn']('请上传身份证人像面照片');
                    return false
                }
                if (that.financialOption.invoiceCategory === 'indivisual' && that.idCardFrontArr.length === 0) {
                    that.$message['warn']('请上传身份证国徽面照片');
                    return false
                }
                if(that.pageParam.isShiJiaZhuang) {
                    let msg
                    if(this.$utils.isEmpty(that.customerInformationArr)) {
                        that.$message['warn']('请上传客户资料申请表扫描件');
                        return false
                    }
                    if(that.financialOption.invoiceCategory === 'indivisual') {
                        if(this.$utils.isEmpty(that.copyBankCardArr)) msg = '请上传个人银行卡复印件'
                    }
                    if(that.financialOption.invoiceCategory === 'company') {
                        if(this.$utils.isEmpty(that.copyBusinessLicenseArr)) msg = '请上传营业执照复印件'
                        if(this.$utils.isEmpty(that.openingPermitArr)) msg = '请上传开户许可证'
                        if(this.$utils.isEmpty(that.invoicingDataArr)) msg = '请上传开票资料'
                    }
                    if(msg) {
                        that.$message['warn'](msg);
                        return false
                    }

                }
                that.financialOption.creditNo = that.financialOption.creditNo.replace(/\s*/g,"");
                return true
            },
            /**
             * 提交
             * <AUTHOR>
             * @date 2020-09-22
             */
            async submit() {
                 this.approveArr = []
                const that = this;
                // 营业执照验证时不允许提交
                if (this.isPhotoRecognition) {
                    return;
                }
                if (that.checkData()) {
                    //表单校验
                    let licRealResult = {};
                    let oriCreditNo = '';
                    try {
                        await this.$refs.form.validate()
                        if (!this.changeFlag && this.financialOption.invoiceCategory === 'company') {
                            // 营业执照验真
                            const {licenseReal, licRealRes, originCreditNo} = await this.checkInvoice(this.financialOption);
                            licRealResult = licRealRes;
                            oriCreditNo = originCreditNo;
                            this.financialOption.licenseReal = licenseReal;
                            if (this.financialOption.licenseReal === 'No') {
                                this.$utils.hideLoading();
                                await this.showInfoDialog('licenseReal', licRealRes, originCreditNo);
                                this.$utils.showLoading();
                            }
                        }
                    } catch (e) {
                        // 取消并复制
                        if (this.financialOption.licenseReal === 'No') {
                            // 根据工商注册号进行查询，复制税号
                            if (licRealResult.regMate === 'Yes' && oriCreditNo !== licRealResult.creditCode) {
                                wx.setClipboardData({data: licRealResult.creditCode});
                            } else if (licRealResult.biliTitle) {
                                // 复制营业执照名称
                                wx.setClipboardData({data: licRealResult.biliTitle});
                            }
                        }
                        return
                    }
                    if (!that.onceClickFlag) {
                        return
                    }
                    that.onceClickFlag = false;
                    that.$utils.showLoading();
                    that.destroyedFlag = false;
                    that.pageParam.editFlag === 'add' ? that.financialOption.row_status = 'NEW' : that.financialOption.row_status = 'UPDATE';
                    let insertOpt = {...that.basicOption, ...that.financialOption};
                    if(that.pageParam.editFlag === 'edit'){
                        insertOpt.updateFinanceOnly = 'Y'
                    }
                    let approveData = [];
                    approveData.push(insertOpt);
                    insertOpt.flowAttachmentList = that.showImgList.map((item)=>{
                        return { attachmentId: item.id }
                    })
                    // edit by 谭少奇 2023/09/21 优选公司处理附件
                    if(this.isYangShengOrYouXuan) {
                        if(that.financialOption.invoiceCategory !== 'indivisual'){
                           insertOpt.attachmentList = that.licenseArr
                        }else{
                           insertOpt.attachmentList = [...that.idCardBackArr, ...that.idCardFrontArr]
                        }
                    }
                    //子户头更改----------
                    if(that.financialOption.multiAcctMainFlag !== 'Y' && that.pageParam.editFlag === 'add'){
                        //新增子户头传参
                        insertOpt.attr4 = 'addChild';
                    }
                    const data = await that.$http.post('action/link/accnt/editChildrenAccount', insertOpt,{
                        handleFailed(error) {
                            that.$utils.hideLoading();
                            that.onceClickFlag = true;
                        }
                    })
                    if(data.success){
                        if(this.pageParam.updateMultiAcctFlag) {
                            const param = this.$utils.deepcopy(this.pageParam.data)
                            param.multiAcctFlag = 'Y'
                            this.$http.post('action/link/accnt/update', param)
                        }
                        let param = {refreshFlag: true};
                        this.$nav.back(param);
                    }else{
                        this.$nav.error('提交失败：'+ data.result);
                    }
                }
            },
            /**
             * 插入所售产品
             * <AUTHOR>
             * @date 2020-09-16
             * @param array
             */
            insertSellProduct (array) {
                if (array.length !== 0) {
                    this.$http.post('action/link/saleCategory/batchUpsert', array, {
                        handleFailed: (error)=> {
                            this.$utils.hideLoading();
                            this.onceClickFlag = true;
                        }
                    }).then(data => {
                        if (data.success) {}
                    })
                }
            },
            /**
             * 插入联系人
             * <AUTHOR>
             * @date 2020-09-16
             * @param array
             */
            insertContacts (array) {
                if (array.length !== 0) {
                    this.$http.post('action/link/contacts/batchUpsert', array, {
                        handleFailed: (error)=> {
                            this.$utils.hideLoading();
                        }
                    }).then(data => {
                        if (data.success) {}
                    })
                }
            },
            /**
             * 数据初始化
             * <AUTHOR>
             * @date 2020-09-06
             */
            async initData() {
                if(!!this.pageParam.__fromCache){return}
                if (this.pageParam.editFlag === 'edit') {
                    this.financialOption = {
                        invoiceCategory: this.pageParam.data.invoiceCategory || 'company',
                        creditChangedFlag: 'Y',
                        credentType: this.pageParam.data.credentType,                                                                                                                 // 开票类型
                        billTitle: this.pageParam.data.billTitle,                                                                                                                     // 营业执照名称
                        creditNo: this.pageParam.data.creditNo,                                                                                                                       // 统一社会信用代码
                        accountBankName: this.pageParam.data.accountBankName,                                                                                                         // 开户行名称
                        mainBillAccntId: this.pageParam.data.mainBillAccntId,                                                                                                         // 银行账户
                        billPhone: this.pageParam.data.billPhone,                                                                                                                     // 注册电话
                        billAddr: this.pageParam.data.billAddr,                                                                                                                       // 公司注册详细地址
                        taxpayerNumber: this.pageParam.data.taxpayerNumber,                                                                                                           // 联行号
                        multiAcctFlag: this.pageParam.data.multiAcctFlag,                                                                                                             // 是否一户多开
                        multiAcctMainFlag : this.pageParam.data.multiAcctMainFlag,                                                                                                    // 是否为主户头
                        id: this.pageParam.data.id,                                                                                                                                   // id
                        multiAcctMainId: this.pageParam.data.multiAcctMainId,                                                                                                         // 主户头ID
                        trafficHighland: this.pageParam.data.multiAcctMainFlag === 'Y' ? this.pageParam.data.trafficHighland : null,
                        receiveBillContact: this.pageParam.data.multiAcctMainFlag === 'Y' ? this.pageParam.data.receiveBillContact : null,                                            // 收票联系人
                        receiveBillPhone: this.pageParam.data.multiAcctMainFlag === 'Y' ? this.pageParam.data.receiveBillPhone : null,                                                // 收票联系方式
                        fixedName: this.pageParam.data.multiAcctMainFlag === 'Y' ? this.pageParam.data.fixedName : null,                                                // 电票接收邮箱
                        receiveBillEmail: this.pageParam.data.multiAcctMainFlag === 'Y' ? this.pageParam.data.receiveBillEmail : null,                                                // 电票接收邮箱
                        ticketingPhone: this.pageParam.data.multiAcctMainFlag === 'Y' ? this.pageParam.data.ticketingPhone : null,                                                    // 电票接收电话
                        receiveBillProvince: this.pageParam.data.multiAcctMainFlag === 'Y' ? this.pageParam.data.receiveBillProvince : null,                                          // 收票省份
                        receiveBillCity: this.pageParam.data.multiAcctMainFlag === 'Y' ? this.pageParam.data.receiveBillCity : null,                                                  // 收票城市
                        receiveBillDistrict: this.pageParam.data.multiAcctMainFlag === 'Y' ? this.pageParam.data.receiveBillDistrict : null,                                          // 收票区县
                        receiveBillAddr: this.pageParam.data.multiAcctMainFlag === 'Y' ? this.pageParam.data.receiveBillAddr : null ,                                                  // 收票人邮寄地址
                        row_status : this.pageParam.data.auditStatus === 'failed2' ? 'UPDATE' : 'NEW',
                        registrationDate: this.pageParam.data.registrationDate,
                        period: this.pageParam.data.period,
                        person: this.pageParam.data.person,
                        business: this.pageParam.data.business,
                        capital: this.pageParam.data.capital,
                    };
                    if (!this.$utils.isEmpty(this.pageParam.data.billTitleImgArr)) {
                        this.licenseArr = this.pageParam.data.billTitleImgArr;
                    }
                    this.basicOption = this.pageParam.data;
                    if (!this.$utils.isEmpty(this.pageParam.data.iDCardArr)) {
                        this.idCardFrontArr = this.pageParam.data.iDCardArr.filter(item => item.moduleType === 'IDCardFront');
                        this.idCardBackArr = this.pageParam.data.iDCardArr.filter(item => item.moduleType === 'IDCardBack');
                    }
                    if(!!this.pageParam.isShiJiaZhuang) {
                        for (let key in this.pageParam.shiJiaZhuangType) {
                            const type = key + 'Arr'
                            if(this.$utils.isNotEmpty(this.pageParam.data[type])) this[type] = this.pageParam.data[type]
                        }
                    }
                } else {
                    this.dealWithData();
                }
            },
            /**
              * 处理基本信息
              * <AUTHOR>
              * @date 2020-09-22
            */
            dealWithData () {
                //添加子户头需设置客户性质默认值
                this.$set(this.financialOption,'invoiceCategory',this.pageParam.data.invoiceCategory || 'company');
                this.basicOption = {
                    acctType: this.pageParam.data.acctType,                                                                                                 // 客户一级分类
                    acctCategory: this.pageParam.data.acctCategory,                                                                                         // 客户二级分类
                    subAcctType: this.pageParam.data.subAcctType,                                                                                           // 客户三级分类
                    acctName: this.pageParam.data.acctName,                                                                                                 // 门头名称
                    simName: this.pageParam.data.simName,                                                                                                   // 客户简称
                    province: this.pageParam.data.province,                                                                                                 // 省
                    city: this.pageParam.data.city,                                                                                                         // 市
                    district: this.pageParam.data.district,                                                                                                 // 区
                    address: this.pageParam.data.address,                                                                                                   // 详细地址
                    storePicPreKey: this.pageParam.data.storePicPreKey,                                                                                     // 门头照片
                    ownStores: this.pageParam.data.ownStores,                                                                                               // 是否经销商自有门店
                    isShareHolderShop: this.pageParam.data.isShareHolderShop,                                                                               // 是否专营公司股东门店
                    isExclusiveShop: this.pageParam.data.isExclusiveShop,                                                                                   // 是否泸州老窖官方形象店
                    chainStoreFlag: this.pageParam.editFlag === 'add' ? 'N' : this.pageParam.data.chainStoreFlag,                                           // 是否为连锁模式
                    chainHeadStoreFlag:this.pageParam.editFlag === 'add' ? 'N' : this.pageParam.data.chainHeadStoreFlag,                                    // 是否为连锁总店
                    chainHeadStoreId: this.pageParam.editFlag === 'add'  ? null : this.pageParam.data.chainHeadStoreId,                                     // 连锁总店id
                    chainHeadStoreName: this.pageParam.data.chainHeadStoreName,                                                                             // 连锁总店名称
                    joinFlag: this.pageParam.data.joinFlag,                                                                                                 // 是否已合作
                    acctLevel: this.pageParam.data.acctLevel,                                                                                               // 容量级别
                    mdmCompanyCode: this.pageParam.data.mdmCompanyCode,                                                                                     // 品牌公司代码
                    appreciationFlag: this.pageParam.data.appreciationFlag,                                                                                 // 是否为品鉴基地
                    capacityLevel: this.pageParam.data.capacityLevel,                                                                                       // 容量级别
                    imageRoomNum: this.pageParam.data.imageRoomNum,                                                                                         // 形象包间数
                    roomTotalNum: this.pageParam.data.roomTotalNum,                                                                                         // 包间总数
                    hallTotalNum: this.pageParam.data.hallTotalNum,                                                                                         // 大厅总数
                    xAttr50: this.pageParam.data.xAttr50,                                                                                                   // 店区位
                    area: this.pageParam.data.area,                                                                                                         // 店面积
                    comments: this.pageParam.data.comments,                                                                                                 // 备注
                    postnId: this.pageParam.data.postnId,                                                                                                   // 职位id
                    orgId: this.pageParam.data.orgId,                                                                                                       // 组织id
                    acctStage: 'xk',                                                                         // 客户阶段
                    directlyFlag: this.pageParam.data.directlyFlag,                                                                                         // 是否存在子公司供货
                    creditChangedFlag: 'Y',                                                                                                                 // 税号/身份证号是否改变，用以判断是否走唯一性校验，父客户等逻辑
                    chainChangedFlag: this.pageParam.data.chainChangedFlag,                                                                                 // 校验当前终端是否从连锁模式变为普通终端，重走唯一性校验，用于获取不同的校验返回信息
                    saleCateList: [],  // 用于校验[是否子公司供货]和实际所售产品数据是否匹配
                    acctStatus: 'Y',                                                                                                                        // 状态
                    storePicId : this.pageParam.data.storePicId,                                                                                            // 门头照片的id
                    auditStatus : 'new',                                                                                                                    // 审批状态
                    kaSystemName: this.pageParam.data.kaSystemName,                                                                                         // 系统名称
                    xAttr51: this.pageParam.data.xAttr51,                                                                                                   // 所属系统ID
                    xAttr71: this.pageParam.data.xAttr71,                                                                                                   // 店号
                    longitude: this.pageParam.data.longitude,                                                                                               // 经度
                    latitude: this.pageParam.data.latitude,                                                                                                 // 纬度
                    accuracy: this.pageParam.data.accuracy                                                                                                  // 精度
                };
                // 星火终端有值时需赋值
                // if (this.pageParam.data.starfireFlag) {
                //     this.basicOption.starfireFlag = this.pageParam.data.starfireFlag;
                // }
            },
            /**
              * 营业执照
              * <AUTHOR>
              * @date 2020-09-22
              * @param val
            */
            async financialPhoto (val) {
                this.licenseArr = val;
                // edit by 邓佳柳 2025/05/15 未审核的终端主户头，修改营业执照照片，不走OCR流程，需正常提交
                if(this.canEditImg) return
                // 营业执照上传成功后是否调用OCR识别接口
                this.isPhotoRecognition = this.orcEntranceFlag === 'Y';
                if (this.isPhotoRecognition) {
                    await this.phoneDistinguish('BizLicenseOCR', val.slice(-1)[0], val.length - 1, val);
                    this.isPhotoRecognition = false;
                }
            },
            /**
             * 营业执照-缓存使用
             * <AUTHOR>
             * @date 2020-09-22
             * @param val
             */
            //暂时无发现该代码用处，2022-07-06修改组件用法删除该方法
            financialPhotoCache(val){
                this.cacheLicenseArr = val;
            },
            /**
              * 身份证正面
              * <AUTHOR>
              * @date 2020-09-22
              * @param val
            */
            idCardFront (val) {
                this.idCardFrontArr = val
            },
            /**
              * 身份证反面
              * <AUTHOR>
              * @date 2020-09-22
              * @param val
            */
            idCardBack(val) {
                this.idCardBackArr = val
                // 身份证上传成功后是否调用OCR识别接口
                this.isPhotoRecognition = this.orcIdCardFlag === 'Y';
                if (this.isPhotoRecognition) {
                    this.phoneDistinguish('IDCardOCR', val.slice(-1)[0], val.length - 1, val);
                    this.isPhotoRecognition = false;
                }
            },
            /**
              * 附件
              * <AUTHOR>
              * @date 2023-08-25
              * @param val
            */
            annexSuccess (val) {
                this.annexArr = val
            },
            imgUploadSuccessBack( val) {
                const type = val[0].moduleType
                this[type + 'Arr'] = val
            },
            /**
             * 校验联行号
             * <AUTHOR>
             * @date 2020-11-07
             * @param param
             */
            verifyTaxpayerNumber (param) {
                if (this.$utils.isNotEmpty(param) && param.length !== 12) {
                    return '请检查联行号'
                }
            },
            /**
             * 校验统一社会信用代码
             * <AUTHOR>
             * @date 2020-09-16
             * @param value 输入的value
             */
            customValidator(value) {
                let reg = /^[A-HJ-NPQRTUWXY0-9]{15}$|^[A-HJ-NPQRTUWXY0-9]{17}$|^[A-HJ-NPQRTUWXY0-9]{18}$|^[A-HJ-NPQRTUWXY0-9]{20}$/g;
                let test = reg.test(value);
                if (!test) {
                    return '请检查统一社会信用代码'
                }
            },
            /**
             * 营业执照名称/姓名校验
             * <AUTHOR>
             * @date 2020-11-09
             * @param value
             */
            billTitleValidator (value) {
                let reg = /[\a-\z\A-\Z\u4E00-\u9FA50-\9]/g;
                let test = reg.test(value);
                if (!test) {
                    return '请检查营业执照名称格式是否正确';
                }
            },
            /**
             * 姓名校验
             * <AUTHOR>
             * @date 2020-11-09
             * @param value
             */
            personalValidator (value) {
                let reg = /[\a-\z\A-\Z\u4E00-\u9FA50-\9]/g;
                let test = reg.test(value);
                if (!test) {
                    return '请检查姓名格式是否正确';
                }
            },
            /**
             * 开户行名称校验
             * <AUTHOR>
             * @date 2020-11-09
             * @param value
             */
            accountBankNameValidator (value) {
                if  (
                    this.financialOption.credentType === 'ValueAddedTax' ||
                    this.financialOption.credentType === 'ElectronicSpecialInvoices' ||
                    !!value
                ) {
                    let reg = /[\a-\z\A-\Z\u4E00-\u9FA50-\9]/g;
                    let test = reg.test(value);
                    if (!test) {
                        return '请检查开户行名称格式是否正确';
                    }
                }
            },
          /**
           * @createdBy  张丽娟
           * @date  2020/9/25
           * @description 上传图片成功后
           */
          imgUploadSuccess(arr,accountId) {
            this.accountImgArray[accountId] = {
              attachmentObj: arr,
              accountId: accountId,
            }
          },
          /**
           * @createdBy  张丽娟
           * @date  2021/3/5
           * @description 创建开户审批工作流
           */
          insertApprove (insertData) {
            let data = insertData
            data.forEach((item,index)=>{
              let attachmentIds = this.showImgList.map((item)=>{return{
                attachmentId: item.id
              }})
              item.flowAttachmentList = attachmentIds
              Reflect.deleteProperty(item, 'annexArr')
            })
            const that = this;
            return new Promise((res, rej) => {
              that.$http.post('action/link/accnt/batchSubmitOpenAccount', data,
                {
                  handleFailed: (error)=> {
                    that.$utils.hideLoading();
                    that.onceClickFlag = true;
                    rej(error);
                  }
                }).then(data => {
                if (data.success) {
                  res(data.success)
                }
              })
            });
            // 插入所售产品和联系人
            if (this.financialOption.multiAcctMainFlag !== 'Y' && this.pageParam.editFlag === 'add') {
              this.insertSellProduct(this.saleCateList);
              this.insertContacts(this.contentList);
            }
          },
          /**
           * @createdBy  张丽娟
           * @date  2021/3/2
           * @description  附件上传完成，返回到列表界面
           */
          async gotoTerminalList(){
            this.$utils.showLoading();
            await this.insertApprove(this.approveArr)
            this.$utils.hideLoading()
            if(this.pageParam.__fromCache){
                return this.$nav.backAll();
            }
            let param = {refreshFlag: true};
            this.$nav.back(param);
          },
          /**
           * @createdBy  张丽娟
           * @date  2021/3/5
           * @description 获取初始化开户审批流附件
           */
          async fetchInitAttachmentArray (accntId) {
            let filtersRaw = [
              {id: "flowObjId", property: "flowObjId", value: accntId, operator: "="},
              {id: "approvalFlowType", property: "approvalFlowType", value: 'OpenAccount', operator: "="},
            ]
            const data = await this.$http.post('action/link/flowAttachment/v2/queryByExamplePage', {filtersRaw: filtersRaw});
            if (data.success) {
              this.accountImgArray = {}
              this.accountImgArray[accntId] = {
                attachmentObj: data.rows,
                accountId: accntId
              }
              this.annexArr = [...data.rows]
              data.rows.forEach((item)=>{
                item.smallurl = item.smallUrl
                item.id = item.attachmentId
              })
            }else{
              this.$utils.showAlert('获取附件失败！', {icon: 'none'});
            }
          },
        }
    }
</script>

<style lang="scss">
    .edit-financial-page {
        .invoice-category {
            display: flex;
            justify-content: space-between;
            .checkbox-right {
                margin-left: 34px;
            }
            .basic-check {
                font-size: 34px;
            }
        }
      .link-input-scan{
        height: 100%;
        padding-left: 10px;
        display: flex;
        align-items: center;
        .scan-icon-box{
          padding-left: 12px;
        }
      }
        /*deep*/.list-title {
                    padding: 12px;
                }
        .tips {
            padding-left: 24px;
        }
        .basics-container {
            background: #ffffff;
            margin-bottom: 24px;
            .basics-column {
                @include flex();
                @include direction-column();
                margin: auto 24px;
                border-bottom: 1px solid #F2F2F2;
                .label {
                    padding-top: 40px;
                    padding-bottom: 24px;
                    font-family: PingFangSC-Regular,serif;
                    font-size: 28px;
                    color: #595959;
                    letter-spacing: 0;
                    line-height: 28px;
                    .asterisk {
                        color: #FF5A5A;
                        padding-right: 2px;
                        margin-left: -14px;
                    }
                }
                .value{
                    font-family: PingFangSC-Regular,serif;
                    font-size: 28px;
                    color: #BFBFBF;
                    letter-spacing: 0;
                    line-height: 28px;
                    padding-bottom: 40px;
                    .text-area {
                        width: 100%;
                        color: #BFBFBF;
                    }
                }
            }
        }
        .authentication-num {
            .asterisk {
                color: #FF5A5A;
                padding-right: 2px;
            }
        }
        .blank {
            width: 100%;
            height: 24px;
            background: #f2f2f2;
        }
        .bottom-sticky {
            /*deep*/.link-button {
                        width: 94%;
                        height: 96px;
                        margin-right: 24px;
                        margin-left: 24px;
                    }
        }
        .phone-btn{
            letter-spacing: 10px;
            padding: 10px 5px 10px 15px;
            text-align: center;
            line-height: 40px;
            font-size: 30px;
            word-break: break-all;
            white-space:pre-wrap;
        }
    }
</style>
