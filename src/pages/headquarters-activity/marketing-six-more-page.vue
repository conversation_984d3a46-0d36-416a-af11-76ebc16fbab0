<template>
    <link-page class="marketing-six-more-page">
        <!-- 推广阶段 -->
        <link-auto-list :option="promotionOption" v-if="option === 'promotion'" class="prod-list">
            <template slot-scope="{data,index}">
                <view class="prod-item">
                    <view class="data">
                        <view class="title">阶段名称</view>
                        <view class="val">{{data.promotionStage | lov('PROMOTION_STAGE')}}</view>
                    </view>
                    <view class="data">
                        <view class="title">开始时间</view>
                        <view class="val">{{data.startTime.slice(0,10)}}</view>
                    </view>
                    <view class="data">
                        <view class="title">结束时间</view>
                        <view class="val">{{data.endTime.slice(0,10)}}</view>
                    </view>
                </view>
            </template>
        </link-auto-list>
        <!-- 活动品项 -->
        <link-auto-list :option="prodOption" v-if="option === 'prodOption'" class="prod-list">
            <template slot-scope="{data,index}">
                <view class="prod-item">
                    <view class="data">
                        <view class="title">产品编码</view>
                        <view class="val">{{data.productCode}}</view>
                    </view>
                    <view class="data">
                        <view class="title">产品名称</view>
                        <view class="val">{{data.productName}}</view>
                    </view>
                </view>
            </template>
        </link-auto-list>
        <!-- 参与组织 -->
        <link-auto-list :option="partOrgOption" v-if="option === 'partOrgOption'" class="prod-list">
            <template slot-scope="{data,index}">
                <view class="prod-item">
                    <view class="data">
                        <view class="title">组织编码</view>
                        <view class="val">{{data.organizationCode}}</view>
                    </view>
                    <view class="data">
                        <view class="title">组织名称</view>
                        <view class="val">{{data.organizationName}}</view>
                    </view>
                    <view class="data">
                        <view class="title">组织类型</view>
                        <view class="val">{{data.organizationType | lov('ORG_TYPE')}}</view>
                    </view>
                    <view class="data">
                        <view class="title">所属品牌公司</view>
                        <view class="val">{{data.companyName}}</view>
                    </view>
                </view>
            </template>
        </link-auto-list>
        <!-- 参与终端 -->
        <link-auto-list :option="partTerminalOption" v-if="option === 'partTerminalOption'" class="prod-list">
            <template slot-scope="{data,index}">
                <view class="prod-item">
                    <view class="data">
                        <view class="title">终端编码</view>
                        <view class="val">{{data.accountCode}}</view>
                    </view>
                    <view class="data">
                        <view class="title">终端名称</view>
                        <view class="val">{{data.accountName}}</view>
                    </view>
                    <view class="data">
                        <view class="title">纳税人识别号</view>
                        <view class="val">{{data.creditNo}}</view>
                    </view>
                    <view class="data">
                        <view class="title">所属品牌公司</view>
                        <view class="val">{{data.companyName}}</view>
                    </view>
                </view>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
export default {
    name: 'marketing-six-more-page',
    data() {
        // 推广阶段
        const promotionOption = new this.AutoList(this,{
            module: 'action/link/promotionStage',
            param: () => {
                return {
                    filtersRaw: [
                        {id: 'actId', property: 'actId', value: this.flowObjId}
                    ]
                }
            },
        });
        // 活动品项
        const prodOption = new this.AutoList(this,{
            module: 'action/link/activityProduct',
            param: () => {
                return {
                    filtersRaw: [
                        {id: 'activityId', property: 'activityId', value: this.flowObjId}
                    ]
                }
            },
        });
        // 参与组织
        const partOrgOption = new this.AutoList(this,{
            module: 'action/link/activityOrganization',
            param: () => {
                return {
                    activityId: this.flowObjId
                }
            },
        });
        // 参与终端
        const partTerminalOption = new this.AutoList(this,{
            module: 'action/link/activityAccount',
            param: () => {
                return {
                    activityId: this.flowObjId
                }
            }
        });
        return {
            flowObjId: this.pageParam.flowObjId,
            option: this.pageParam.option,
            prodOption,
            partOrgOption,
            partTerminalOption,
            promotionOption
        }
    },
    created() {
        this.$taro.setNavigationBarTitle({title: this.pageParam.title});
    }
}
</script>

<style lang="scss">
.marketing-six-more-page {
    padding: 20px;

    .prod-item {
        background: #fff;
        padding: 20px;
        margin: 20px 0;
        border-radius: 16px;

        .data {
            display: flex;
            font-size: 28px;
            margin-bottom: 10px;

            .title {
                color: #8C8C8C;
                margin-right: 20px;
            }

            .val {
                color: #262626;
                flex: 1;
                word-wrap: break-word;
                word-break:break-all;
                white-space: pre-wrap;
            }
        }
    }
}
</style>
