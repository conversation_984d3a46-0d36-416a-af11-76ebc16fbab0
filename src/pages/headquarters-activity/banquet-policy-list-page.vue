<!--
    @description: 总部活动-宴席政策列表
    @author: ran<PERSON><PERSON><PERSON>
    @date: 2023-06-28
-->
<template>
    <link-page class="banquet-policy-list-page">
        <link-auto-list :option="banquetPolicyList" :searchInputBinding="{props:{placeholder:'政策名称'}}">
            <link-filter-group slot="filterGroup">
                <link-filter-item label="创建时间（升序）" :param="{sort:{field:'created',desc: false}}"/>
                <link-filter-item label="更新时间（升序）" :param="{sort:{field:'lastUpdated',desc: false}}"/>
            </link-filter-group>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="product-list-item" @tap="goToItem(data)">
                    <view class="product-list" slot="note">
                        <view class="store-content">
                            <view class="store-content-top">
                                <view class="store-title">{{data.policyCode}}</view>
                            </view>
                            <view class="store-content-representative">
                                <view class="policy-type">{{data.policyName}}</view>
                            </view>
                            <view class="store-content-representative">
                                <view class="policy-type">政策有效期：</view>
                                <view class="policy-name">{{data.policyStartTime}}-{{data.policyEndTime}}</view>
                            </view>
                            <view class="store-content-representative">
                                <view class="policy-type">用酒要求数量：</view>
                                <view class="policy-name">{{data.alcoholUnitNum}}{{data.alcoholUnit}}</view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
export default {
    name: 'banquet-policy-list-page',
    data() {
        const banquetPolicyList = new this.AutoList(this,{
            module: 'action/link/banquetPolicy',
            url: {
                queryByExamplePage: 'action/link/banquetPolicy/queryByExamplePage',
            },
            param:{
                oauthBanquetOrgFlag: 'MY_ORG',
                filtersRaw: [
                    {id: 'policyStatus', property: 'policyStatus', value: 'Completed'}
                ],
            },
            hooks: {
                afterLoad (data) {
                    data.rows.forEach(async (item) => {
                        item.policyStartTime = item.policyStartTime.split (' ')[0];
                        item.policyEndTime = item.policyEndTime.split (' ')[0];
                    })
                }
            },
            rows:3,
            sortField: 'created',
            searchFields: ['policyCode','policyName'],
        });
        return {
            banquetPolicyList,   //宴席政策列表
        }
    },
    methods: {
        /**
         * 跳转到查看详情
         * @param data 详情信息
         */
        async goToItem(data) {
            this.$nav.push('/pages/headquarters-activity/banquet-policy-detail-page.vue', {
                data: data
            })
        },
    }
}
</script>

<style lang="scss">
.banquet-policy-list-page {
    .search-container {
        padding-left: 12px;
        color: #8C8C8C;
        font-size: 28px;
        text-align: center;
    }
    .product-list-item {
        background: #FFFFFF;
        width: 702px;
        margin: 24px auto auto auto;
        border-radius: 16px;
    }
    /*deep*/.link-item {
                padding: 0;
            }
    /*deep*/.link-item-icon {
                width: 0;
                padding-left: 0;
            }
    /*deep*/.link-dropdown-content {
                padding: 24px;
            }
    .product-list {
        padding: 24px 16px 24px 24px;
        .store-content {
            width: 80%;
            .store-content-top {
                @include flex-start-center;
                @include space-between;
                margin-left: 24px;
                .store-title {
                    font-family: PingFangSC-Semibold,serif;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 36px;
                    width: 77%;
                    height: 36px;
                    overflow: hidden;
                }
                .store-level {
                    margin-right: -3px;
                    width: 120px;
                    height: 44px;
                    image {
                        width: 100%;
                        height: 100%;
                    }
                }
            }

            .store-content-representative {
                @include flex;
                margin-left: 24px;
                margin-top: 20px;
                width: calc(100% - 10px);
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                .policy-type {
                    color: #000000;
                    min-width: 60px;
                    letter-spacing: 0;
                    font-size: 24px;
                }
                .policy-name {
                    font-size: 24px;
                    color: #000000;
                    letter-spacing: 0;
                    padding-left: 20px;
                    width: calc(100% - 50px);
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }
        }
    }
}

</style>
