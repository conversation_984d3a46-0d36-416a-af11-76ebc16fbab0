<template>
    <link-page class="banquet-policy-detail-page">
        <line-title title="基础信息" class="head-title"></line-title>
        <view class="market-activity-basic-info">
            <view class="basic-info-v" id="basic-info-v">
                <view style="width: 100%;height: 8px"></view>
                <view class="basic-info">
                    <view style="width: 100%;height: 8px"></view>
                    <view class="block-v">
                        <view class="title">政策类型</view>
                        <view class="val">
                            {{activityItemData['policyType'] | lov('policy_type')}}
                        </view>
                    </view>
                    <view class="block-v">
                        <view class="title">政策编码</view>
                        <view class="val" @longPress="copyActCode(activityItemData['policyCode'])">
                            {{activityItemData['policyCode']}}
                        </view>
                    </view>
                    <view class="block-v">
                        <view class="title">活动名称</view>
                        <view class="val">
                            {{activityItemData['activityName']}}
                        </view>
                    </view>
                    <view class="block-v">
                        <view class="title">活动开始时间</view>
                        <view class="val">
                            {{activityItemData['startTime']}}
                        </view>
                    </view>
                    <view class="block-v">
                        <view class="title">活动结束时间</view>
                        <view class="val">
                            {{activityItemData['endTime']}}
                        </view>
                    </view>
                    <view class="block-v">
                        <view class="title">用酒单位</view>
                        <view class="val">
                            {{activityItemData['alcoholUnit'] | lov('PROD_UNIT')}}
                        </view>
                    </view>
                    <view class="block-v">
                        <view class="title">用酒要求数量</view>
                        <view class="val">
                            {{activityItemData['alcoholNum']}}
                        </view>
                    </view>
                    <view class="block-v">
                        <view class="title">最低用酒桌数</view>
                        <view class="val">
                            {{activityItemData['requireTableNum']}}
                        </view>
                    </view>
                    <view class="block-v">
                        <view class="title">推荐费限额</view>
                        <view class="val">
                            ￥{{activityItemData['maxPrize']}}
                        </view>
                    </view>
                    <view class="block-v" v-if="activityItemData.policyType === 'byTable'">
                        <view class="title">每桌奖励费用</view>
                        <view class="val">{{activityItemData.perTableBonusAmount}}</view>
                    </view>
                    <view class="block-v">
                        <view class="title">开瓶率</view>
                        <view class="val">{{activityItemData.openRatio}}</view>
                    </view>
                    <view class="block-v">
                        <view class="title">最低开瓶数</view>
                        <view class="val">{{activityItemData.lowOpenCount}}</view>
                    </view>
                    <view class="block-v">
                        <view class="title">返利类型</view>
                        <view class="val">{{activityItemData.rebateType | lov('REBATE_TYPE')}}</view>
                    </view>
                    <view class="block-v" v-if="activityItemData.scenePicture">
                        <view class="title">是否需要现场拍照</view>
                        <view class="val">{{activityItemData.scenePicture | lov('IS_FLAG')}}</view>
                    </view>
                    <view class="block-v" v-if="activityItemData.scenePicture === 'Y'">
                        <view class="title">执行反馈要求</view>
                        <view class="val">{{activityItemData.registerRequest | lls('TMPL_SUB_BIZ_TYPE')}}</view>
                    </view>
                    <view class="block-v" v-if="activityItemData.scanFlag">
                        <view class="title">是否需要用酒扫码</view>
                        <view class="val">{{activityItemData.scanFlag | lov('IS_FLAG')}}</view>
                    </view>
                    <view class="block-v" v-if="activityItemData.autoEnd">
                        <view class="title">是否自动结束</view>
                        <view class="val">{{activityItemData.autoEnd | lov('IS_FLAG')}}</view>
                    </view>
                    <view class="block-v" v-if="activityItemData.autoDay">
                        <view class="title">自动结束时间（天）</view>
                        <view class="val">{{activityItemData.autoDay}}</view>
                    </view>
                    <view class="block-v" v-if="activityItemData.limitTimeFlag">
                        <view class="title">是否限制宴席单时长</view>
                        <view class="val">{{activityItemData.limitTimeFlag | lov('IS_FLAG')}}</view>
                    </view>
                    <view class="block-v" v-if="activityItemData.maxBanquetDay">
                        <view class="title">最长宴席单时长（天）</view>
                        <view class="val">{{activityItemData.maxBanquetDay}}</view>
                    </view>
                    <view class="block-v" v-if="activityItemData.limitCategoryFlag">
                        <view class="title">是否限制客户中类</view>
                        <view class="val">{{activityItemData.limitCategoryFlag | lov('IS_FLAG')}}</view>
                    </view>
                    <view class="block-v" v-if="activityItemData.accountCategory">
                        <view class="title">客户中类</view>
                        <view class="val">{{JSON.parse(activityItemData.accountCategory) | lov('ACCNT_CATEGORY') }}</view>
                    </view>
                    <view style="width: 100%;height: 8px"></view>
                </view>
            </view>
        </view>
        <line-title title="产品信息" class="head-title"></line-title>
        <link-auto-list :option="productList" hideCreateButton>
            <view slot="noMore"></view>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="product-list-item">
                    <view class="product-list" slot="note">
                        <view class="store-content">
                            <view class="store-content-top">
                                <view class="title">产品编码</view>
                                <view class="store-title">{{data.productCode}}</view>
                            </view>
                            <view class="store-content-top">
                                <view class="title">产品名称</view>
                                <view class="store-title">{{data.productName ? data.productName.substr(0,20) + '...' : ''}}</view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
        <line-title title="组织范围" class="head-title"></line-title>
        <link-auto-list :option="orgList" hideCreateButton>
            <view slot="noMore"></view>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="product-list-item">
                    <view class="product-list" slot="note">
                        <view class="store-content">
                            <view class="store-content-title">
                                <view class="store-title">{{data.organizationType|lov('ORG_TYPE')}}</view>
                                <view class="store-title">{{data.organizationCode}}</view>
                                <view class="store-title">{{data.organizationName ? data.organizationName.substr(0,4) + '...' : ''}}</view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>

        <!-- 宴席单信息 -->
        <view class="banquet-act-item">
            <view class="item-header" >
                <view>宴席单信息</view>
                <view class="right-btn" @tap="queryActivityList" v-if="activityTotal > 2">查看全部</view>
            </view>
            <link-auto-list :option="banquetActList" hideCreateButton>
                <template slot-scope="{data,index}">
                    <link-swipe-action class="banquet-activity-list-item">
                        <view class="act-item" @tap="gotoDetail(data)">
                            <view class="media-top">
                                <!-- 活动编码 -->
                                <view class="num-view" @longPress="copyActCode(data.feedbackCode)">
                                    <view class="num">{{data.feedbackCode}}</view>
                                </view>
                                <!-- 活动状态标识 -->
                                <status-button :label="data.status| lov('HEAD_FEEDBACK_STATUS')"></status-button>
                            </view>
                            <!-- 活动名称 -->
                            <view class="content-middle">
                                <view class="name">{{data.feedbackName.length > 20 ? data.feedbackName.substring(0,20) + '...' : data.feedbackName }}</view>
                            </view>
                            <view class="content-middle-line">
                                <view class="data">
                                    <view class="title">审批状态</view>
                                    <view class="val">{{data.approveStatus | lov('HEAD_FEEDBACK_APRO_STATUS')}}</view>
                                </view>
                                <view class="sum">
                                    <view class="title">用酒要求</view>
                                    <view class="val">{{data.alcoholRequest}}</view>
                                </view>
                            </view>
                            <view class="content-middle-line">
                                <view class="data">
                                    <view class="title">开始时间</view>
                                    <view class="val">{{data.startTime | date('YYYY-MM-DD')}}</view>
                                </view>
                                <view class="sum">
                                    <view class="title">结束时间</view>
                                    <view class="val">{{data.endTime | date('YYYY-MM-DD')}}</view>
                                </view>
                            </view>
                            <view class="content-middle-line">
                                <view class="data">
                                    <view class="title">提报人</view>
                                    <view class="val">{{data.creator}}</view>
                                </view>
                            </view>
                            <view class="content-middle-line">
                                <view class="data2">
                                    <view class="title">宴席地点</view>
                                    <view class="val">{{data.policyFullAddress}}</view>
                                </view>
                            </view>
                        </view>
                    </link-swipe-action>
                </template>
            </link-auto-list>
        </view>
    </link-page>
</template>

<script>
    import StatusButton from "../lzlj/components/status-button.vue";
    import LineTitle from "../lzlj/components/line-title.vue";

    definePageConfig({
        navigationBarTitleText: '宴席政策详情'
    });

    export default {
        name: "basic-info",
        components: {StatusButton,LineTitle},
        props: {
            activityItem: {
                type: Object,
                default: function () {
                    return {};
                }
            },
        },
        computed: {
            activityItemData: function () {
                return this.pageParam.data;
            },
        },
        data() {
            const productList = new this.AutoList(this,{
                module: 'action/link/activityProduct',
                param:{
                    activityId: this.pageParam.data.id
                },
                sortField: 'created',
                showFetchItemLoading: false
            });
            const orgList = new this.AutoList(this,{
                module: 'action/link/activityOrganization',
                param:{
                    activityId: this.pageParam.data.id
                },
                sortField: 'created',
                showFetchItemLoading: false
            });
            //宴席单
            const banquetActList = new this.AutoList(this, {
                module: '/action/link/headquarterFeedback',
                url: {
                    queryByExamplePage: '/export/link/headquarterFeedback/queryByExamplePage',
                },
                param: {
                    rows: 2,
                    activityType: 'Banquet',
                },
                hooks: {
                    beforeLoad (option) {
                        delete option.param.page;
                        option.param.rows = 2;
                        option.param.filtersRaw.push({id: 'activityId',property: 'activityId', operator: '=', value: this.pageParam.data.id})
                    },
                    afterLoad(data) {
                        this.activityTotal = data.total
                    }
                },
                sortField: 'created',
                sortDesc: 'desc',
                sortOptions: null,
            });
            return {
                activityTotal: '',
                banquetActList, //宴席单
                productList, //产品
                orgList //组织
            }
        },
        methods: {
            /**
             * 查看全部
             * <AUTHOR> */
            queryActivityList() {
                this.$nav.push('pages/headquarters-activity/banquet-activity-list-page', {
                    activityId: this.activityItemData.id,
                    source: this.pageParam.source ? this.pageParam.source : ''
                })
            },
            /**
             * 跳转宴席详情
             * <AUTHOR>
             * @date 2023-07-04
             */
            gotoDetail(data) {
                if (data.approveStatus === 'New' || data.approveStatus === 'Refuse') {
                    this.editFlag = true;
                    this.$nav.push('/pages/headquarters-activity/banquet-activity-apply-page.vue',{
                        data: data,
                        editFlag: this.editFlag
                    })
                } else {
                    // 获取模版子类型，将返回的登记要求字符串转化为数组
                    const regRegStr = data.registerRequest.replace(/"/g, '');
                    if (data.registerRequest.charAt(0) === "[") {
                        data.regReqList = regRegStr.slice(1, -1).split(',');
                    } else {
                        data.regReqList = regRegStr.slice(1, -1);
                    }
                    this.$nav.push('/pages/headquarters-activity/banquet-activity-detail-page',{
                        data: {...data}
                    })
                }

            },
            /**
             * 跳转宴席详情
             */
            clickAdd() {
                this.$nav.push('/pages/headquarters-activity/banquet-activity-apply-page',{policyName: this.activityItemData.policyName,id: this.activityItemData.id})
            },
            /**
             * 复制活动编码
             */
            copyActCode(text) {
                wx.setClipboardData({data: text});
            }
        }
    }
</script>

<style lang="scss">
.banquet-policy-detail-page {
    background-color: #F2F2F2;
    width: 100%;
    .market-activity-basic-info {
        .basic-info-v {
            .basic-info {
                background: #FFFFFF;
                border-radius: 16px;
                margin: 24px;

                .block-v {
                    padding: 0 24px;
                    display: flex;
                    justify-content: space-between;

                    .title {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        line-height: 60px;
                        float: left;
                        flex-wrap: nowrap;
                    }

                    .val {
                        flex: 1;
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 0;
                        text-align: right;
                        line-height: 60px;
                        float: left;
                        text-overflow: ellipsis;
                    }
                }

                .line {
                    margin: 32px 24px 32px 24px;
                    height: 0;
                    border: 2px dashed #DADEE9;
                }
            }
        }
    }
    .stair-title {
        width: 30%;
        margin-left: 24px;
        font-family: PingFangSC-Semibold, serif;
        font-size: 32px;
        color: #262626;
        letter-spacing: 1px;
        line-height: 32px;
    }
    .search-container {
        padding-left: 12px;
        color: #8C8C8C;
        font-size: 28px;
        text-align: center;
    }
    .product-list-item {
        background: #FFFFFF;
        width: 702px;
        margin: 24px auto auto auto;
        border-radius: 16px;
    }
    /*deep*/.link-item {
                padding: 0;
            }
    /*deep*/.link-item-icon {
                width: 0;
                padding-left: 0;
            }
    /*deep*/.link-dropdown-content {
                padding: 24px;
            }
    .product-list {
        .store-content {
            .store-content-top {
                @include flex-start-center;
                @include space-between;
                margin-left: 24px;
                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 60px;
                    width: 25%;
                    float: left;
                }
                .store-title {
                    font-family: PingFangSC-Semibold,serif;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 36px;
                    width: 100%;
                    height: 36px;
                    overflow: hidden;
                }
            }
            .store-content-title {
                display: flex;
                margin-left: 24px;
                width: 95%;
                .store-title {
                    font-family: PingFangSC-Semibold,serif;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 36px;
                    width: 33.3%;
                    height: 36px;
                    overflow: hidden;
                }
            }

            .store-content-representative {
                @include flex;
                margin-left: 24px;
                margin-top: 20px;
                width: calc(100% - 24px);
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                .policy-type {
                    color: #000000;
                    min-width: 50px;
                    letter-spacing: 0;
                    font-size: 24px;
                }
                .policy-name {
                    font-size: 24px;
                    color: #000000;
                    letter-spacing: 0;
                    padding-left: 8px;
                    width: calc(100% - 50px);
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }
        }
    }

    .banquet-act-item {
        margin: 24px;
        background: #fff;
        border-radius: 16px;
        font-size: 28px;

        .item-header {
            display: flex;
            justify-content: space-between;
            padding: 24px;
            border-bottom: 2px solid #ccc;

            .right-btn {
                color: #2F69F8;
            }
        }

        .banquet-activity-list-item {
            width: 100%;
            border-bottom: 2px solid #ccc;

            .act-item {
                padding: 10px 20px 20px 20px;
            }

            .media-top {
                width: 100%;
                @include flex-start-center;
                @include space-between;
                height: 80px;
                line-height: 80px;

                .left-content {
                    font-family: PingFangSC-Semibold;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 32px;
                    padding-top: 20px;
                }

                .right-content {
                    font-family: PingFangSC-Semibold;
                    font-size: 32px;
                    color: #FF5A5A;
                    letter-spacing: 0;
                    text-align: right;
                    line-height: 32px;
                    padding-top: 20px;
                }

                .num-view {
                    background: #A6B4C7;
                    border-radius: 8px;
                    line-height: 50px;

                    .num {
                        font-size: 28px;
                        color: #FFFFFF;
                        letter-spacing: 0;
                        line-height: 40px;
                        padding: 2px 8px;
                    }
                }

                .status-view {
                    width: 120px;
                    transform: skewX(-10deg);
                    border-radius: 4px;
                    background: #2F69F8;
                    box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                    height: 36px;

                    .status {
                        font-size: 20px;
                        color: #FFFFFF;
                        letter-spacing: 2px;
                        text-align: center;
                        line-height: 36px;
                    }
                }
            }

            .content-middle {
                @include flex-start-center;
                @include space-between;
                height: 80px;
                line-height: 80px;

                .content {
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    color: #000000;
                    letter-spacing: 0;
                }

                .name {
                    font-family: PingFangSC-Semibold;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 32px;
                }
            }
            .content-middle-line {

                .data {
                    width: 55%;
                    float: left;

                    .title {
                        font-family: PingFangSC-Regular;
                        font-size: 26px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        line-height: 56px;
                        width: 40%;
                        float: left;
                    }

                    .val {
                        font-family: PingFangSC-Regular;
                        font-size: 26px;
                        color: #000000;
                        letter-spacing: 0;
                        line-height: 56px;
                    }

                    .Submitted, .Feedback{
                        color: #2F69F8;
                    }

                    .Approve, .FeedbackApro{
                        color: #2EB3C2;
                    }

                    .Refused, .Refeedback{
                        color: #FF5A5A;
                    }

                }
                .data2 {
                    width: 100%;
                    float: left;

                    .title {
                        font-family: PingFangSC-Regular;
                        font-size: 26px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        line-height: 56px;
                        width: 22%;
                        float: left;

                    }

                    .val {
                        font-family: PingFangSC-Regular;
                        font-size: 26px;
                        color: #000000;
                        letter-spacing: 0;
                        line-height: 56px;
                    }

                    .Submitted, .Feedback{
                        color: #2F69F8;
                    }

                    .Approve, .FeedbackApro{
                        color: #2EB3C2;
                    }

                    .Refused, .Refeedback{
                        color: #FF5A5A;
                    }

                }
                .sum {
                    width: 45%;
                    float: left;

                    .title {
                        font-family: PingFangSC-Regular;
                        font-size: 26px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        line-height: 56px;
                        float: left;
                        width: 40%;
                    }

                    .val {
                        font-family: PingFangSC-Regular;
                        font-size: 26px;
                        color: #000000;
                        letter-spacing: 0;
                        line-height: 56px;
                        white-space:nowrap;
                    }
                }

            }
        }

        .link-auto-list-no-more {
            display: none !important;
        }
    }

    .link-input{
        width: 100%;
    }
}
</style>
