<!--
总部活动-一键报备
<AUTHOR>
@date 2023-04-12
-->
<template>
    <link-page class="report-share-page">
        <view class="report">
            <view>
                <view class="report-data">{{headRegData.activityType | lov('HEAD_ACT_TYPE')}}报备</view>
                <view class="report-data">终端名称：{{headRegData.terminalName}}</view>
                <view class="report-data">登记名称：{{headRegData.feedbackName}}</view>
                <view class="report-data">登记人：{{headRegData.creator}}</view>
                <view class="report-data">登记人职位：{{headRegData.creatorPostnName}}</view>
                <view class="report-data">登记人电话：{{headRegData.creatorTel}}</view>
                <view class="report-data">政策类型：{{headRegData.policyType | lov('MARKETSIX_POLICY_TYPE')}}</view>                
                <view class="report-data">活动日期：{{headRegData.activityDate ? headRegData.activityDate.substr(0,10) : ''}}</view>
                <view class="report-data">活动阶段：{{headRegData.stageName | lov('PROMOTION_STAGE')}}</view>
                <view class="report-data">活动时段：{{headRegData.feedbackStage | lov('HEAD_FEEDBACK_TIME')}}</view>
                <view class="report-data">包间号：{{headRegData.privateRoom}}</view>
                <view class="report-data">当日用餐包间数量：{{headRegData.privateNumber}}</view>
                <view class="report-data">参与人数(人/场)：{{headRegData.clientNumber}}</view>
                <block v-for="(prod, index) in prodList" :key="index">
                    <view class="report-data">小酒产品名称：{{prod.productName}}</view>
                    <view class="report-data">开瓶数量(瓶)：{{prod.productNumber}}</view>
                    <view class="report-data">开瓶扫码数量（瓶）：{{prod.scanNumber}}</view>
                </block>

            </view>
        </view>
        <link-sticky>
            <link-button block @tap="copyText">复制文本</link-button>
        </link-sticky>
    </link-page>
</template>
<script>
export default {
    name: "report-share-page",
    data() {
        return {
            headRegData: {},
            terminalData: {},
            sourceFlag: '',
            prodList: []
        }
    },
    created() {
        this.queryBasicInfo();
    },
    methods: {
        /**
         *  查询基础信息
         *  <AUTHOR>
         *  @date 2023-04-17
         */
        async queryBasicInfo() {
            try {
                const data = await this.$http.post('action/link/headquarterFeedback/queryById',{
                    id: this.pageParam.data.id
                })
                if(data.success) {
                    this.headRegData = data.result;
                    // 查询产品信息
                    const {success, rows} = await this.$http.post('action/link/headquarterProduct/queryByExamplePage', {
                        filtersRaw: [
                            {id: 'feedbackId', property: 'feedbackId', value: this.headRegData.id},
                            {id: 'productType', property: 'productType', value: 'Open'}
                        ]
                    });
                    if (success) {
                        this.prodList = rows;
                    }
                } else {
                    this.$showError("查询产品请求失败，请稍后重试！");
                }
            } catch (e) {
                this.$showError("查询产品请求失败，请稍后重试！" );
            }
        },
        async copyText() {
            const headRegData = this.headRegData;
            let activityType = await this.$lov.getNameByTypeAndVal('HEAD_ACT_TYPE',this.headRegData.activityType);
            let feedbackStage = await this.$lov.getNameByTypeAndVal('HEAD_FEEDBACK_TIME', this.headRegData.feedbackStage);
            let stageName = await this.$lov.getNameByTypeAndVal('PROMOTION_STAGE', this.headRegData.stageName);
            let activityDate = this.headRegData.activityDate ? this.headRegData.activityDate.substr(0,10) : '';
            let privateRoom = this.headRegData.privateRoom ? this.headRegData.privateRoom : '';
            let clientNumber = this.headRegData.clientNumber ? this.headRegData.clientNumber : '';
            let prodStr = '';
            this.prodList.map((item) => {
                prodStr = `${prodStr}\n小酒产品名称：${item.productName}\n开瓶数量(瓶)：${item.productNumber}\n开瓶扫码数量（扫码）：${item.scanNumber}`;
            })
            const text = `${activityType}报备\n终端名称：${headRegData.terminalName}\n登记名称：${headRegData.feedbackName}\n登记人：${headRegData.creator}\n登记人职位：${headRegData.creatorPostnName}\n登记人电话：${headRegData.creatorTel}\n活动日期：${activityDate}\n活动时段：${feedbackStage}\n活动阶段：${stageName}\n包间号：${privateRoom}\n当日用餐包间数量：${headRegData.privateNumber}\n参与人数(人/场)：${clientNumber}${prodStr}`;
            wx.setClipboardData({
                data: text,
                success: function () {
                    wx.showToast({
                        title: '复制成功',
                        icon: 'success'
                    })
                }
            })
        }
    }
}
</script>
<style lang="scss">
.report-share-page {
    background: white;
    .report {
        .report-data {
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #595959;
            letter-spacing: 0;
            line-height: 30px;
            padding: 32px 0 0 24px;
            clear: both;
        }
    }
}
</style>
