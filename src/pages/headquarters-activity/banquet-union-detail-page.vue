<!--
    总部活动-宴席联盟-详情
    <AUTHOR>
    @date 2023-12-29
-->
<template>
    <link-page class="client-details-page">
        <!--顶部背景-->
        <view class="top-container">
            <navigation-bar :backVisible="true"
                            :navBarAllHeight="fixTop"
                            :backgroundImg="$imageAssets.homeMenuBgImage"
                            :title="navigationBarTitle"
                            :titleColor="navigationBarTitleColor"
                            :navBackgroundColor="navBackgroundColor">
                <view class="top-content">
                    <view class="store-image" @tap="previewStoreUrl(head)">
                        <image :src="$env.cosUploadUrl + head.storePicKey || $imageAssets.terminalDefaultImage"></image>
                    </view>
                    <view class="store-content-cover">
                        <view class="store-content-code">{{head.acctCode}}</view>
                        <view class="title-level-code">
                            <view class="store-content-top">
                                <view class="store-title">{{head.acctName}}</view>
                            </view>
                        </view>
                        <view class="store-content-address">{{head.addrDetailAddr}}</view>
                    </view>
                </view>
            </navigation-bar>
            
        </view>
        <view class="content">
            <!--title-->
            <view class="top-blank" v-if="tapsFix"
                  :style="{'height': duration + 'px','line-height': duration+ 'px', 'padding-top':statusBarHeight +'rpx'}">
                {{head.acctName}}
            </view>
            <!--状态栏-->
            <link-sticky top :duration="this.$device.isIphoneX ? duration * 2 - 12 : duration * 2 + 16">
                <view class="tap-container">
                    <view class="lnk-tabs">
                        <view class="lnk-tabs-item">
                            <view class="label-name" style="width: 67px;">
                                联盟成员
                            </view>
                            <view class="line"></view>
                        </view>
                    </view>
                    <link-dropdown class="dropdown" v-model="showDropdownFlag">
                        
                    </link-dropdown>
                </view>
            </link-sticky>
            
            <!--联盟成员-->
            <link-auto-list :option="terminalListOption">
                <template slot-scope="{data,index}">
                    <item :key="index" :data="data" :arrow="false" class="acct-item" @tap="goToItem(data)">
                        <view slot="note" class="acct-item-note">
                            <view class="header-image">
                                <image :src="$env.cosUploadUrl + data.storePicKey || $imageAssets.terminalDefaultImage"></image>
                            </view>
                            <view class="acct-info">
                                <view class="item-top">
                                    <view class="left">
                                        <view class="name">{{data.acctName}}</view>
                                    </view>
                                </view>
                                <view class="item-middle">
                                    <view class="info-item">
                                        <view class="label">编码</view>
                                        <view class="value">{{data.acctCode}}</view>
                                    </view>
                                    <view class="info-item">
                                        <view class="address">{{data.addrDetailAddr}}</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </item>
                </template>
            </link-auto-list>
        </view>
    </link-page>
</template>

<script>
    import navigationBar from 'link-taro-component'
    import basicInfo from './components/basic-info'
    export default {
        name: "client-details-page",
        data() {
            const terminalListOption = new this.AutoList(this, {
                module: '/action/link/headquarterLeague',
                url: {queryByExamplePage: this.$env.baseURL + '/action/link/headquarterLeague/queryLeagueBodyDetail'},
                loadOnStart: true,
                param: this.pageParam.data,
                sortOptions: null,
                filterBar: {},
                hooks: {
                    beforeLoad(option){
                        delete option.param.sort
                        delete option.param.order
                    },
                    // afterLoad(data) {
                    //     data.rows = data.rows.filter(item => {
                    //         if (item.positionType === 'head') {
                    //             this.head = item
                    //         }else {
                    //             return item
                    //         }
                    //     })
                    // }
                }
            });
            return {
                terminalListOption,
                head: {},
                marginTop: true,
                tapsFix: false,
                showDropdownFlag: false,
                fixTop: 200,
                scrollTop: 0,
                scrollLeft: 0,
                duration: this.$device.isIphoneX ? 88 : 64,
                durationOrder: this.$device.isIphoneX ? 166 : 118,
                statusBarHeight: this.$device.systemInfo.statusBarHeight,
                navigationBarTitle: '联盟详情',
                navigationBarTitleColor: '#ffffff',
                navBackgroundColor: 'transparent',
                timer: null,                                                                                        // 计时器
                qrWidth: 220,
                qrHeight: 220
            }
        },
        components: {
            navigationBar,        // 页面top背景栏
            basicInfo            // 基础信息
        },
        destroyed () {
            clearTimeout(this.timer);
        },
        created() {
            this.$http.post('action/link/headquarterLeague/queryLeagueDetail', this.pageParam.data,).then((data) => {
                if (data.success) {
                    this.head = data.rows[0]
                }
            })
        },
        /**
         * 监听页面滚动函数
         * <AUTHOR>
         * @date 2024-01-04
         * @param e 距离顶部距离
         */
         onPageScroll(e) {
            this.tapsFix = e.scrollTop >= this.fixTop - this.duration;
            this.showDropdownFlag = false
        },
        methods: {
            /**
             * 照片预览
             * <AUTHOR>
             * @date 2020-09-22
             * @param param
             */
            async previewStoreUrl(param) {
                const that = this;
                if (!that.$utils.isEmpty(param.storePicKey)) {
                    let imgUrl = await this.$image.getSignedUrl(param.storePicKey);
                    const inOptions = {
                        current: imgUrl,
                        urls: [imgUrl]
                    };
                    that.$image.previewImages(inOptions)
                } else {
                    const inOptions = {
                        current: that.$imageAssets.terminalDefaultImage,
                        urls: [that.$imageAssets.terminalDefaultImage]
                    };
                    that.$image.previewImages(inOptions)
                }
            },
            /**
             * 查看成员详情
             * <AUTHOR>
             * @date 2024-01-04
             * @param data 详情信息
             */
            async goToItem(data) {
                const param = {
                    id: data.id,
                    acctCode: data.acctCode
                }
                this.$nav.push('/pages/terminal/terminal/client-details-page.vue', {data: param})
            },
        }
    }
</script>

<style lang="scss">
    .client-details-page {
        /*deep*/.link-fab-button {
        background: linear-gradient(128deg, rgba(47, 140, 248, 0.8) 12%, rgba(47, 105, 248, 0.8) 70%);
    }
        .comp-navbar {
            width: 100vw;
            .placeholder-bar{
                background-color: transparent;
                width: 100%;
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                -webkit-box-pack: start;
                -ms-flex-pack: start;
                justify-content: flex-start;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;
                .icon-left {
                    width: 10%;
                    font-size: 34px;
                    color: #FFFFFF;
                    padding-left: 24px;
                }
                .navigator-back {
                    width: 46px;
                    height: 46px;
                    padding-left: 24px;
                }
                .bar-title {
                    width: 82%;
                    font-size: 34px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    text-align: center;
                }
            }
        }
        .top-container {
            .top-content {
                @include flex;
                .store-image {
                    margin-left: $margin-normal;
                    margin-top: 32px;
                    width: 128px;
                    height: 128px;
                    border-radius: 16px;
                    overflow: hidden;
                    box-shadow: 0 7px 49px 0 rgba(20,28,51,0.39);
                    image {
                        width: 100%;
                        height: 100%;
                    }
                }
                .store-content-cover {
                    margin-top: 25px;
                    // @include flex-start-start;
                    // background-color: blueviolet;
                    width: 80%;
                    margin-top: 10px;
                    .store-content-code {
                        margin-left: 24px;
                        color: #fff;
                        font-size: 30px;
                        line-height: 28px;
                    }
                    .title-level-code {
                        @include flex-start-start;
                        @include space-between;
                        width: 100%;
                        .store-content-top {
                            @include flex-start-center;
                            @include space-between;
                            margin-left: 24px;
                            width: 100%;
                            .store-title {
                                margin-top: 10px;
                                font-family: PingFangSC-Semibold,serif;
                                font-size: 30px;
                                color: #ffffff;
                                letter-spacing: 0;
                                line-height: 30px;
                                // max-width: 370px;
                            }
                        }
                        
                    }
                    .store-content-address {
                        // max-width: 70%;
                        margin-left: 24px;
                        margin-top: 20px;
                        // background-color: #9aa9cf;
                        color: #fff;
                        font-size: 30px;
                        line-height: 30px;
                    }
                    .store-content-middle {
                        @include flex-start-center;
                        /*padding-top: 24px;*/
                        height: 60px;
                        line-height: 60px;
                        margin-left: 24px;
                        flex-wrap: wrap;
                        .store-type {
                            border: 1px solid #ffffff;
                            border-radius: 8px;
                            font-size: 20px;
                            padding-left: 18px;
                            padding-right: 18px;
                            line-height: 36px;
                            color: #ffffff;
                            margin-right: 10px;
                            margin-top: 10px;
                        }
                    }
                }
            }
        }
        .content {
            .top-blank {
                width: 100%;
                background: $color-primary;
                position: fixed;
                top: 0;
                font-family: PingFangSC-Semibold,serif;
                font-size: 34px;
                color: #FFFFFF;
                letter-spacing: 0;
                text-align: center;
                z-index: 9999;
            }
            .acct-item {
                margin: 24px 0;
                border-radius: 16px;
                .acct-item-note {
                    @include flex-start-start;
                    .header-image {
                        width: 100px;
                        height: 100px;
                        border-radius: 50%;
                        .status {
                            text-align: center;
                            color: #ffffff;
                            position: absolute;
                            background: rgba(0,0,0,0.50);
                            width: 80px;
                            height: 80px;
                            line-height: 80px;
                            border-radius: 50%;
                        }
                        image {
                            width: 100%;
                            height: 100%;
                        }
                    }
                    .acct-info {
                        margin-left: 24px;
                        flex: 1;
                        .item-top {
                            @include flex-start-center;
                            @include space-between;
                            .left {
                                @include flex-start-center;
                                .name {
                                    font-size: 32px;
                                    color: #262626;
                                    margin-right: 24px;
                                }
                            }
                        }
                        .item-middle {
                            .info-item {
                                @include flex-start-start;
                                width: 100%;
                                font-size: 28px;
                                margin-bottom: 8px;
                                line-height: 44px;
                                .label {
                                    color: #999999;
                                    width: 100px;
                                }

                                .value {
                                    color: #333333;
                                    flex: 1;
                                    @include flex-start-start;
                                    @include wrap;
                                }

                                .address {
                                    margin-top: 10px;
                                }
                            }
                        }
                    }
                }
            }
            .tap-container {
                width: 100%;
                display: flex;
                height: 92px;
                overflow: hidden;
                .lnk-tabs::-webkit-scrollbar {
                    display:none
                }
                .lnk-tabs {
                    overflow-x: scroll;
                    white-space: nowrap;
                    border-top: 1px solid #f2f2f2;
                    display: flex;
                    background-color: #fff;
                    color: #595959;
                    width: 670px;
                    z-index: 9999;
                    &.marginTop {
                        margin-top: 80px;
                    }
                    .lnk-tabs-item {
                        height: 92px;
                        line-height: 92px;
                        text-align: center;
                        .label-name {
                            width: 100%;
                            font-size: 28px;
                            margin-left: 10px;
                            color: #2F69F8;
                        }
                        .line {
                            height: 8px;
                            width: 56px;
                            border-radius: 16px 16px 0 0;
                            background-color: $color-primary;
                            box-shadow: 0 3px 8px 0 rgba(47,105,248,0.63);
                            margin: -12px auto auto auto;
                        }
                    }
                }
                .dropdown {
                    right: 0;
                    position: relative;
                    width: 92px;
                    height: 92px;
                    background: #FFFFFF;
                    box-shadow: -3px 0 28px 0 rgba(7,44,105,0.16) !important;
                    .dropdown-icon {
                        line-height: 92px;
                        text-align: center;
                        font-size: 36px;
                        color: #595959;
                    }
                    /*deep*/.link-dropdown-reference {
                                box-shadow: -3px 0 28px 0 rgba(7,44,105,0.16)!important;
                            }
                    /*deep*/.link-dropdown-content {
                                border-radius: 0 0 32px 32px;
                            }
                    /*deep*/.link-dropdown {
                                box-shadow: -3px 0 28px 0 rgba(7,44,105,0.16)!important;
                            }
                    .dropdown-container {
                        @include flex-start-center();
                        @include wrap();
                        border-radius: 0 0 32px 32px;
                        padding-bottom: 28px;
                        .menu-item {
                            width: 25%;
                            @include flex-center-center();
                            @include direction-column();
                            .menu-icon {
                                color: $color-primary;
                                font-size: 48px;
                                padding-top: 28px;
                                padding-bottom: 30px;
                            }
                            .menu-name {
                                font-family: PingFangSC-Regular,serif;
                                font-size: 28px;
                                color: #595959;
                                letter-spacing: 0;
                                text-align: center;
                                line-height: 28px;
                                padding-bottom: 28px;
                            }
                        }
                    }
                }
            }
            .customer-label-view {
                .content {
                    width: 702px;
                    margin: 24px auto;
                }
            }
            .lnk-tabs-order {
                white-space: nowrap;
                border-top: 1px solid #f2f2f2;
                display: flex;
                background-color: #F2F2F2;
                color: #595959;
                width: 100vw;
                z-index: 9999;
                padding-left: 24px;
                padding-right: 24px;
                &.marginTop {
                    margin-top: 94px;
                }
                .active {
                    color: #595959;
                }
                .lnk-tabs-item {
                    margin-top: 24px;
                    display: inline-block;
                    text-align: center;
                    .label-name-bg {
                        @include flex-center-center();
                        .label-name-on {
                            background: $color-primary;
                            color: #fff;
                            border-radius: 8px;
                            padding-top: 8px;
                            padding-bottom: 8px;
                            width: 95%;
                            font-size: 28px;
                        }
                        .label-name-off {
                            color: #8C8C8C;
                            border-radius: 8px;
                            padding-top: 8px;
                            padding-bottom: 8px;
                            width: 95%;
                            font-size: 28px;
                        }
                    }
                }
            }
            .edit-text {
                padding-bottom: 32px;
                padding-top: 32px;
                text-align: right;
                padding-right: 24px;
                font-family: PingFangSC-Regular,serif;
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
                line-height: 28px;
            }

            .financial-components {
                padding-bottom: 68px;
            }
            .capital-loading {
                .loading {
                    font-size: 40px;
                    margin-top: 24px;
                    width: 100%;
                    text-align: center;
                    /*deep*/.link-loading-tag {
                    margin: auto;
                }
                }
                .capital-loading-text {
                    margin-top: 10px;
                    font-size: 24px;
                    color: #999;
                    width: 100%;
                    text-align: center;
                }
            }
            .subsidiary-picker {
                background: #ffffff;
                border-radius: 8px;
                width: 702px;
                margin: 24px auto auto auto;
                padding-top: 24px;
                padding-bottom: 24px;
                text-align: center;
                .picker-text {
                    font-size: 28px;
                    color: #000000;
                    font-weight: bold;
                    display: inline-block;
                }
                .icon-down {
                    position: absolute;
                    padding-top: 10px;
                    font-size: 28px;
                    color: #2F69F8;
                    display: inline-block;
                }
            }
        }
        .link-fab-group {
            bottom: 45px !important;

            .link-fab-item-wrapper-list .link-fab-item-wrapper {
                margin-top: 10PX !important;
                //transition-delay: 0.2s;
            }

            .link-fab-item-wrapper-list {
                max-height: calc(100vh - 350px) !important;
                //max-height: calc(100vh - 600px) !important;
                flex-wrap: wrap-reverse !important;
                left: unset !important;
                align-items: flex-start;

                .link-fab-item-label {
                    position: static !important;
                    margin: 0 24px !important;
                }
            }

            /* prettier-ignore */
            $margin: 16PX;
            /* prettier-ignore */
            $itemSize: 44PX;
            &:not(.link-fab-group-show) {
                &,& .link-fab-item {
                    pointer-events: none !important;
                }
                .link-fab-item-wrapper {
                    /*兼容20个按钮的样式*/
                    @for $i from 11 through 20 {
                        &.link-fab-item-wrapper-#{$i} {
                            transform: translate3d(0, ($itemSize+$margin)*$i, 0);
                            opacity: 0;
                            pointer-events: none;

                            &, & .link-fab-item-label, & .link-fab-item-icon {
                                transition-delay: (0.02s*$i);
                            }
                        }
                    }
                }
            }
        }
    }
</style>
