<!--
    总部活动-宴席联盟-主页
    <AUTHOR>
    @date 2023-12-29
-->
<template>
    <link-page class="banquet-union-list-page">
        <link-auto-list :option="terminalListOption" 
                        :searchInputBinding="{props:{placeholder:'终端名称、编码、省市区'}}">
            <link-filter-group slot="filterGroup" space-between>
                <link-filter-item label="创建时间" :param="{sort:{field:'created',desc:true}}"/>
                <link-filter-item label="最新更新" :param="{sort:{field:'lastUpdated',desc:true}}"/>
            </link-filter-group>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="union-list-item" @tap="goToItem(data)">
                    <view class="union-list" slot="note">
                        <view class="list-cell">
                            <view class="media-list">
                                <image class="media-list-logo" :src="$env.cosUploadUrl + data.storePicKey || $imageAssets.terminalDefaultImage" @tap.stop="previewStoreUrl(data)" lazy-load="true"></image>
                                <view class="store-content">
                                    <view class="store-content-top" v-if="data.acctType">
                                        <view class="store-title">{{data.acctName}}</view>
                                        <!--盟主-->
                                        <view class="store-level" v-if="data.positionType === 'head'"><image src="../../static/images/banquet-union-icon/head-icon.png"></image></view>
                                        <!--成员-->
                                        <view class="store-level" v-else><image src="../../static/images/banquet-union-icon/body-icon.png"></image></view>
                                    </view>
                                    <view class="store-content-middle">
                                        <view class="left">
                                            <view class="store-type" v-if="data.acctType">{{data.acctType | lov('ACCT_TYPE')}}</view>
                                        </view>
                                    </view>
                                    <view class="store-content-representative">
                                        <view class="terminal-type">编码</view>
                                        <view class="terminal-name">{{data.acctCode}}</view>
                                    </view>
                                    <view class="store-content-address">
                                        <view class="store-address">{{data.addrDetailAddr}}</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>
<script>
import {getFiltersRaw} from "link-taro-component";
export default {
    name: "banquet-union-list-page",
    data () {
        // 获取客户一级分类值列表和客户二级分类值列表 只保留部分字段
        Promise.all([
            this.$lov.getLovByType('ACCT_TYPE'),
            this.$lov.getLovByType('ACCNT_CATEGORY')
        ]).then(([ACCT_TYPE, ACCNT_CATEGORY]) => {
            let acctTypeExcludeLov = ACCT_TYPE.filter(item => item.val !== 'Terminal' && item.val !== 'Distributor').map(value => value.val);
            let excludeLov = ACCNT_CATEGORY.filter(item => item.parentType === 'ACCT_TYPE' && item.parentVal !== 'Terminal' && item.parentVal !== 'Distributor').map(val => val.val);
            let arrOption = [
                {label: '客户大类', field: 'acctType', type: 'lov',lov: 'ACCT_TYPE',lovOption: {excludeLovs: acctTypeExcludeLov}},
                {label: '客户中类', field: 'acctCategory', type: 'lov',lov: 'ACCNT_CATEGORY',lovOption: {parentType: 'ACCT_TYPE', excludeLovs: excludeLov}}
            ];
            this.terminalListOption.option.filterOption = this.terminalListOption.option.filterOption.concat(arrOption)
        });
        const terminalListOption = new this.AutoList(this, {
            module: '/action/link/headquarterLeague',
            url: {queryByExamplePage: this.$env.baseURL + '/action/link/headquarterLeague/queryLeagueAccountPage'},
            sortField: ' ',
            param: {
                oauth: 'MY_ORG'
            },
            queryFields:'',sortOptions: null,
            searchFields: ['acctName', 'acctCode', 'addrDetailAddr'],
            filterOption: [
                {label: '渠道管理模式', field: 'channelManageMode', type: 'lov', lov: 'CHANNEL_MANAGE_MODE'}
            ],
            filterBar: {
                label: '创建时间',
                field: 'created',
                type: 'date',
                autoReload: false,              // 禁止点击自动刷新
            },
            hooks: {
                beforeLoad (option) {
                   option.param.attr2 = option.param.sort;
                   delete option.param.sort;
                   delete option.param.order;
                }
            }
        });
        return {
            terminalListOption
        }
    },
    async created() {
        this.$bus.$on('terminalListRefresh', async () => {
            await this.terminalListOption.methods.reload();
        });
    },
    methods: {
        /**
         * 门头照片预览
         * <AUTHOR>
         * @date 2020-09-22
         * @param param
         */
        async previewStoreUrl(param) {
            const compressSuffix = '/suoluetu';
            const defaultSuffix = 'default';
            if (this.$utils.isEmpty(param.storeUrl) || param.storeUrl.indexOf(defaultSuffix) !== -1) {
                return;
            }
            const inOptions = {
                current: param.storeUrl.replaceAll(compressSuffix, ''),
                urls: [param.storeUrl]
            };
            this.$image.previewImages(inOptions);
        },
        /**
         * 查看联盟详情
         * <AUTHOR>
         * @date 2023-12-29
         * @param data 详情信息
         */
        async goToItem(data) {
            const param = {
                id: data.id,
                acctCode: data.acctCode,
                positionType: data.positionType
            }
            this.$nav.push('/pages/headquarters-activity/banquet-union-detail-page.vue', {data: param})
        },
        /**
         * 筛选
         * <AUTHOR>
         * @date 2024-01-03
         * @param val 值
         */
        async handleChange(val) {
            const filterArr = getFiltersRaw(val);
            console.log(filterArr);
        },
        onFilterBarChange() {
            const {value} = this.terminalListOption.terminalListOption.filterBar;
            console.log(value);
        },
        /**
         * 刷新列表
         * <AUTHOR>
         * @date 2024-01-04
         */
        reLoad() {
            this.terminalListOption.methods.reload();
        }
    }
}
</script>

<style lang="scss">

.banquet-union-list-page {
    .terminal-count {
        padding: 8px 16px;
        margin-right: 8px;
        white-space: nowrap;
        display: inline-block;
        background-color: #f2f2f2;
        color: #333333;
        border-radius: 4px;
    }
    .search-container {
        padding-left: 12px;
        color: #8C8C8C;
        font-size: 28px;
        text-align: center;
    }
    .union-list-item {
        background: #FFFFFF;
        width: 702px;
        margin: 24px auto auto auto;
        border-radius: 16px;
    }
    /*deep*/.link-item {
                padding: 0;
            }
    /*deep*/.link-item-icon {
                width: 0;
                padding-left: 0;
            }
    /*deep*/.link-dropdown-content {
                padding: 24px;
            }
    .union-list {
        .list-cell {
            .media-list {
                @include flex;
                padding: 24px 16px 24px 24px;
                .media-list-logo {
                    /*box-shadow: 0 4px 31px 0 rgba(0,44,152,0.22);*/
                    border-radius: 16px;
                    width: 128px;
                    height: 128px;
                    overflow: hidden;
                }
                .store-content {
                    width: 80%;
                    .store-content-top {
                        @include flex-start-center;
                        @include space-between;
                        margin-left: 24px;
                        .store-title {
                            font-family: PingFangSC-Semibold,serif;
                            font-size: 32px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 36px;
                            width: 77%;
                            height: 36px;
                            overflow: hidden;
                        }
                        .store-level {
                            margin-right: -3px;
                            width: 120px;
                            height: 44px;
                            image {
                                width: 100%;
                                height: 100%;
                            }
                        }
                    }

                    .store-content-middle {
                        display: flex;
                        justify-content: space-between;
                        padding-left: 32px;
                        .left ,.right{
                            @include flex-start-center;
                            flex-wrap: wrap;
                            margin-top: 10px;
                            .store-type {
                                white-space: nowrap;
                                border: 2px solid #2F69F8;
                                border-radius: 8px;
                                font-size: 20px;
                                padding-left: 18px;
                                padding-right: 18px;
                                line-height: 40px;
                                height: 40px;
                                color: #2F69F8;
                                margin-right: 10px;
                                margin-top: 10px;
                            }
                        }
                    }
                    .store-content-representative {
                        @include flex;
                        margin-left: 24px;
                        margin-top: 20px;
                        width: calc(100% - 24px);
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        .terminal-type {
                            color: #8C8C8C;
                            min-width: 50px;

                        }
                        .terminal-name {
                            font-family: PingFangSC-Regular,serif;
                            font-size: 24px;
                            color: #000000;
                            letter-spacing: 0;
                            padding-left: 8px;
                            width: calc(100% - 50px);
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                        }
                    }
                    .store-content-address {
                        margin-left: 24px;
                        margin-top: 18px;
                        font-family: PingFangSC-Regular,serif;
                        font-size: 24px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 32px;
                    }
                }
            }
        }
        .content-bottom {
            display: flex;
            justify-content: space-between;
            padding: 0 24px 10px;
            .view-port {
                position: relative;
                width: 100%;
                overflow: hidden;
                .label {
                    width: 90%;
                    display: flex;
                    flex-wrap: wrap;
                    position: relative;
                    .label-item {
                        padding: 4px 10px;
                        color: #2F69F8;
                        background-color: rgb(233,242,255);
                        margin: 6px 8px;
                        border-radius: 10px;
                        height: 34px;
                    }
                    .grey {
                        background: #ececec;
                        color: #3c3c3c;
                    }
                }
                .label::before {
                    position: absolute;
                    right: -12%;
                    bottom: 2px;
                    background-color: white;
                    content: '';
                    z-index: 2;
                    width: 12%;
                    height: 52px;
                }
                .iconZhankai {
                    width: 10%;
                    position: absolute;
                    top: 0;
                    right: 0;
                    height: 100%;
                    text-align: right;
                }
            }
        }
    }
}
</style>
