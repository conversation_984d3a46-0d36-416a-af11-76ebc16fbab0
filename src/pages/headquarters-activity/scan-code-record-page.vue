<!--
总部活动-新增/编辑-开瓶扫码记录-可删除
<AUTHOR>
@date 2023-04-24
-->
<template>
    <link-page class="scan-code-record-page">
        <link-auto-list :option="autoList" hideCreateButton >
            <template slot-scope="{data,index}">
                <link-swipe-action :key="index" :data="data">
                    <link-swipe-option slot="option" @tap="deleteRow(data,index)"
                                       v-if="!pageParam.source || pageParam.source !== 'detail'">删除
                    </link-swipe-option>
                    <item :key="index" :data="data" :arrow="false" class="scan-list">
                        <view class="scan-item" slot="note">
                            <view class="list-cell">
                                <view class="bottle-info">
                                    <view class="bottle-row">
                                        <view class="font"><text class="textTitled">产品编码：</text><text class="textVal" >{{ data.productCode }}</text></view>
                                    </view>
                                </view>
                                <view class="arrowBox" >
                                    <view  class="info">{{ data.productName }}</view>
                                </view>
                                <view class="bottle-info">
                                    <view class="bottle-row">
                                        <view class="font"><text class="textTitled">盖内码：</text><text class="textVal" >{{data.prodQrCode.split('/').pop().substr(-8)}}</text></view>
                                        <view class="font"><text class="textTitlec">扫码人：</text><text class="textVal" >{{data.scanner}}</text></view>
                                    </view>
                                </view>
                                <view class="bottle-info">
                                    <view class="bottle-row">
                                        <view class="font"><text class="textTitled">扫码时间：</text><text class="textVal" >{{data.created}}</text></view>
                                    </view>
                                </view>
                                <view class="bottle-info">
                                    <view class="bottle-row">
                                        <view class="font"><text class="textTitled">扫码地址：</text><text class="textVal" >{{data.scanAddress}}</text></view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </item>
                </link-swipe-action>
            </template>

        </link-auto-list>
    </link-page>
</template>
<script>
export default {
    name: "scan-code-record-page",
    data() {
        const autoList = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/headquarterScanRecord/queryByExamplePage'
            },
            param: {
                feedbackId: this.pageParam.id,
                headId: this.pageParam.headId
            }
        });
        return{
            autoList,
            freshSourceFlag: '' //刷新来源
        }
    },
    create() {},
    methods: {
        /**
         *  删除信息行
         *  <AUTHOR>
         *  @date 2023-04-24
         */
        deleteRow(input, index) {
            if(this.autoList.list.length > 1) {
                this.autoList.list = [...this.autoList.list];
            }
            if (this.pageParam.status == 'Submitted') {
                this.$message.warn('当前登记活动已提交，禁止删除扫码记录！')
            } else {
                this.$taro.showModal({
                    title: '提示',
                    content: '是否删除此记录？',
                    success: async (res) => {
                        await this.deleteCurrentRow(input, index);

                    }
                })
            }
        },
        /**
         *  删除信息行的回调函数
         *  <AUTHOR>
         *  @date 2023-04-24
         */
        async deleteCurrentRow(input, index) {
                const data = await this.$http.post('action/link/headquarterScanRecord/deleteScanRecordById', {
                    id: this.autoList.list[index].id
                });
                if (data.success) {
                    this.$taro.showToast({
                        title: '删除成功'
                    });
                    this.autoList.methods.reload();
                    this.$bus.$emit('refreshRegUpserts', input);
                    // if(this.pageParam.freshSourceFlag === 'upsert') {
                    //     this.$bus.$emit('refreshRegUpsert');
                    //
                    // }
                    // if(this.pageParam.freshSourceFlag === 'basicInfo') {
                    //     this.$bus.$emit('refreshBasicInfo');
                    // }
                } else {
                    this.$taro.showToast({
                        title: '删除失败'
                    });
                }
            }
    }
}
</script>

<style lang="scss">
.scan-code-record-page{
    padding: 16px;
    .scan-list{
        background: #FFFFFF;
        border-radius: 16px;
        margin-right: 32px;
        .scan-item{
            background-color: #FFFFFF;
            position: relative;
            width: 100%;
            display: flex;
            flex-direction: column;
            .list-cell {
                width: 100%;
                position: relative;
                flex-direction: column;
                justify-content: space-between;
                align-items: start;
                .font{
                    color: #000000;
                    font-size: 24px;
                    .textTitle{
                        color:#666666;
                        font-size: 24px;
                        margin-left:2px;
                    }
                    .textVal{
                        color:#939393;
                        font-size: 24px;
                    }
                    /***同行文字位置调整*/
                    .textTitlec{
                        color:#939393;
                        font-size: 24px;
                        margin-left:80px;
                    }
                    .textTitleb{
                        color:#999999;
                        font-size: 24px;
                        margin-left:12px;
                    }
                    .textValc{
                        display: inline-block;
                        margin-left: 6px;
                        color: #2F69F8;
                        font-size: 24px;
                    }
                    /***盖类码*/
                    .textTitled{
                        color:#939393;
                        font-size: 24px;
                        margin-left:0px;
                    }
                    .textTitleF{
                        color:#000000;
                        font-size: 28px;
                        margin-left:80px;
                    }
                    .textVald{
                        display: inline-block;
                        margin-left: 6px;
                        color: #2F69F8;
                        font-size: 28px;
                    }
                    /***同行一左一右*/
                    .textRow{
                        display:flex;
                        align-items:center;
                        .textTitler{
                            color:#999999;
                            font-size: 24px;
                            margin-left: 12px;
                        }
                        .textValr{
                            display: inline-block;
                            color: #333333;
                            font-size: 24px;
                            margin-left: auto;
                        }
                    }

                }
                /**含箭头的text*/
                .arrowBox{
                    display: flex;
                    align-items:center;
                    .info{
                        color: #000000;
                        font-size: 32px;
                        font-weight: bold;
                        font-weight: bold;
                        margin: 12px 0;
                        display: -webkit-box;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        word-wrap: break-word;
                        white-space: normal !important;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                    }
                    .flexbox {
                        display: flex;
                        align-items: center;
                        margin-left: auto;
                    }
                }
                .bottle-info{
                    width: 100%;
                    margin: 8px 0;
                    .bottle-row{
                        display: flex;
                        flex-direction: row;
                    }
                }
            }
        }
    }
    .link-swipe-action {
        margin: 16px 16px 16px 0;
    }
    .link-swipe-action-content {
        //margin-right: 16px;
    }
}
</style>
