<!--
总部活动-宴席活动-一键转发-添加联系人
<AUTHOR>
@date 2023-07-31
@file banquet-forward-contact-page.vue
-->
<template>
    <link-page class="banquet-forward-contact-page">
        <image v-if="gowChatFlag" class="bottom-bg" :src="$imageAssets.banquetForwardImage"></image>
        <view v-if="!gowChatFlag" class="contacts-container" v-for="(item, index) in contactsArr" :key="index">
            <link-form ref="form" :value="item" :rules="formRules">
                <link-form-item :label="`联系人${index + 1}`" required field="phoneNum">
                    <view class="link-input-contact">
                        <link-input placeholder="请输入联系人电话" v-model="item.phoneNum" field="phoneNum" @blur="checkRepate()"/>
                        <view class="delete-icon-box" @tap="tapSuffixIcon(index)">
                            <link-icon icon="icon-close" class="icon-close"/>
                        </view>
                    </view>
                </link-form-item>
            </link-form>
        </view>
        <view v-if="!gowChatFlag" class="add-contacts" @tap="addContacts">
            <text class="iconfont icon-plus"></text>
            <text class="text">添加联系人</text>
        </view>
        <link-sticky>
            <link-button v-if="!gowChatFlag" block @tap="share">提交</link-button>
        </link-sticky>
        <!-- 小程序分享/名片海报分享提示 -->
        <link-dialog ref="chooseType">
            <view slot="head">
                提示
            </view>
            <view>
                可从企业微信发送至微信好友
            </view>
            <link-button slot="foot" @tap="cancelShare()">取消</link-button>
            <link-button slot="foot" open-type="share" class="share-wechat-button" @tap="shareApplet">分享到企微</link-button>
        </link-dialog>
    </link-page>
</template>

<script>
export default {
    name: 'banquet-forward-contact-page',
    data() {

        return {
            userInfo: {},
            contactsArr: [], // 联系人
            shareFlag: false, // 是否分享中
            formRules: {
                // 电话号码校验
                phoneNum: this.Validator.phone(),
            },
            updateFlag: false, //是否为编辑状态，首次保存为false
            shareId: '', //本条数据的id
            banquetItem: {}, // 宴席信息
            gowChatFlag: false, // 跳转到企微/微信，默认为false，转发到微信前为true
        }
    },
    onShareAppMessage(res) {
        // 进入分享
        this.shareFlag = true;
        // 自定义按钮与小程序提供分享按钮，均跳转到相同界面
        // 构建带参数的 path
        // const pathWithParams = `/pages/headquarters-activity/banquet-feedback-share-page?empId=${this.userInfo.id}&banquetItem=${encodeURIComponent(JSON.stringify(this.banquetItem))}`;
        const pathWithParams = `/pages/headquarters-activity/banquet-feedback-share-page?empId=${this.userInfo.id}&banquetId=${this.banquetItem.id}&creatorCode=${this.userInfo.username}`;

        return {
            title: '分享宴席执行反馈',
            path: pathWithParams
        }
    },
    async created() {
        this.userInfo = this.$taro.getStorageSync('token').result;
        this.banquetItem = this.pageParam.banquetItem;
        await this.queryContacts();
    },
    methods: {
        /**
         * @desc 检查重复号码
         * <AUTHOR>
         * @date 2023-08-02
         */
        checkRepate() {
            const phoneNumSet = new Set(); // 用于记录已存在的 phoneNum
            for (const contact of this.contactsArr) {
                const phoneNum = contact.phoneNum;
                if (phoneNumSet.has(phoneNum)) {
                    this.$message.warn('存在重复号码，请将重复号码删除！');
                    return true; // 发现重复号码
                } else {
                    phoneNumSet.add(phoneNum);
                }
            }
            return false; // 未发现重复号码
        },
        /**
         * @desc 查询联系人
         * <AUTHOR>
         * @date 2023-08-02
         */
        async queryContacts() {
            try {
                const data = await this.$http.post('action/link/forwardContact/queryByExamplePage',
                    {
                        filtersRaw: [{
                            id: 'headId',
                            property: 'headId',
                            value: this.pageParam.banquetItem.id,
                            operator: '='
                        }]
                    }
                );
                if (data.success) {
                    this.contactsArr = data.rows;
                    this.contactsArr.forEach(item => {
                        item.row_status = 'UPDATE';
                    })
                } else {
                    this.$message.error(data.message);
                }
            } catch (e) {
                throw e;
            }
        },
        /**
         * @desc 保存联系人
         * <AUTHOR>
         * @date 2023-07-31
         */
        async share() {
            // 保存逻辑
            try {
                const list = this.contactsArr;
                if(list.length === 0) {
                    this.$message.warn('转发对象不能为空，请添加联系人！');
                    return;
                }
                await Promise.all(
                    this.$refs.form.map(formRef => formRef.validate())
                );
                if (this.checkRepate()) {
                    return; // 如果发现了重复号码，就不执行后续操作
                }
                const data = await this.$http.post('action/link/forwardContact/batchUpsert', list);
                if (data.success) {
                    this.updateFlag = true;
                    this.$message.success('保存成功');
                    this.gowChatFlag = true;
                    this.$refs.chooseType.show();
                } else {
                    this.$message.error('未保存成功，请重试！');
                    return;
                }
            } catch (e) {
                throw e;
            }
        },
        /**
         * @desc 删除联系人
         * <AUTHOR>
         * @date 2023-07-31
         * @param key 删除元素索引
         */
        tapSuffixIcon(key) {
            const contact = this.contactsArr[key];
            if (!!contact.phoneNum) {
                contact.phoneNum = '';
            } else {
                this.$dialog({
                    title: '提示',
                    content: '是否删除当前联系人',
                    cancelButton: true,
                    confirmText: '确定',
                    onConfirm: () => {
                        this.contactsArr.splice(key, 1);
                        if(contact.id) {
                            this.deleteArr(contact.id);
                        }
                    },
                    onCancel: () => {

                    }
                });
                console.log('删除后this.contactsArr', this.contactsArr);
            }
        },
        /**
         * @desc 删除联系人-调接口
         * <AUTHOR>
         * @date 2023-08-03
         */
        async deleteArr(contactId) {
            const data = await this.$http.post('action/link/forwardContact/deleteById',
                {
                    id: contactId
                }
            );
            if(data.success) {
                this.$message.success('删除成功');
            } else {
                this.$message.error('删除失败');
            }
        },

        /**
         * @desc 添加联系人
         * <AUTHOR>
         * @date 2023-07-31
         // * @param key 删除元素索引
         */
        addContacts() {
            let newContacts = {
                row_status: this.updateFlag ? 'UPDATE' : 'NEW',
                headId: this.pageParam.banquetItem.id,
                phoneNum: '',             // 联系电话
                actForm: 'headquarter',
                actType: 'Banquet',
            };
            this.contactsArr.push(newContacts);
            console.log('插入this.contactsArr', this.contactsArr);
        },
        /**
         * @desc 返回
         * <AUTHOR>
         * @date 2023-07-31
         */
        async goBack() {
            this.$nav.back();
        },
        /**
         * @desc 分享小程序
         * <AUTHOR>
         * @data 2023-08-01
         */
        async shareApplet() {
            try {
                const data = await this.$http.post('action/link/headquarterFeedback/afterForward',
                    {
                        id: this.pageParam.banquetItem.id,
                        creatorCode: this.userInfo.username //员工工号
                    }
                );
                if (data.success) {
                    console.log('afterForward接口调用成功！');
                } else {
                    this.$message.error(data.message);
                }
                this.$refs.chooseType.hide();
                if (this.$device.systemInfo.model.indexOf('iPhone') !== -1) {
                    setTimeout(() => {
                        this.shareFlag = false;
                    }, 600);
                }
            } catch (e) {
                throw e;
            }
        },
        /**
         * @desc 取消分享
         * <AUTHOR>
         * @data 2023-08-04
         */
        cancelShare() {
            this.gowChatFlag = false;
            this.$refs.chooseType.hide();
        }
    },
}
</script>

<style lang="scss">
.banquet-forward-contact-page {
    background: #F2F2F2;

    .contacts-container {
        /*deep*/
        .link-icon {
            font-size: 28px;
        }

        /*deep*/
        .link-input-text-align-left {
            text-align: right;
        }

        /*deep*/
        .link-item {
            padding: 24px;
        }

        .title-container {
            display: flex;

            .contacts-text {
                width: 50%;
            }

            .delete-btn {
                width: 50%;
                text-align: right;
                font-family: PingFangSC-Regular, serif;
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
            }
        }

        .contacts-content {
        }

        padding-bottom: 40px;

        .link-input-contact {
            height: 100%;
            padding-left: 10px;
            display: flex;
            align-items: center;

            .delete-icon-box {
                padding-left: 12px;
            }
        }

    }

    .add-contacts {
        border: 2px dashed #2F69F8;
        border-radius: 8px;
        margin: 40px auto;
        height: 96px;
        width: 702px;
        text-align: center;

        .text {
            font-family: PingFangSC-Regular, serif;
            font-size: 32px;
            color: #2F69F8;
            letter-spacing: 0;
            line-height: 96px;
        }

        .icon-plus {
            font-size: 32px;
            line-height: 96px;
            color: #2F69F8;
        }
    }

    .blank {
        height: 180px;
        width: 100%;
    }
    .bottom-bg {
        width: 100%;
        height: 100vh;
    }
}
</style>

