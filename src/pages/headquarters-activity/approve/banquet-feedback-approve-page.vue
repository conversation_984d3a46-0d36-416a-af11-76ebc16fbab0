<!--
总部活动-宴席活动执行反馈-审批
<AUTHOR>
@date 2023-07-13
@file banquet-feedback-approve-page.vue
-->
<template>
    <link-page class="banquet-approve-page">
        <approval-history-point v-if="!$utils.isEmpty(approvalId)"
                                :approvalId="approvalId"></approval-history-point>
        <!--基础信息-->
        <banquet-basic-info v-if="!$utils.isEmpty(flowObjId)" :flowObjId="flowObjId" :templateList="templateList"></banquet-basic-info>
        <!--执行反馈-->
        <banquet-feedback v-if="dataLoaded" :flowObjId="flowObjId" :regReqList="regReqList"
                          :data="data"></banquet-feedback>
        <!--出库明细-->
        <banquet-outbound-details v-if="dataLoaded" :feedbackCode="feedbackCode"></banquet-outbound-details>
        <!-- 退货明细 -->
        <banquet-inbound-details v-if="dataLoaded" :feedbackCode="feedbackCode"></banquet-inbound-details>
        <!--消费者开瓶明细-->
        <banquet-consumer-open v-if="dataLoaded" :feedbackCode="feedbackCode"></banquet-consumer-open>
        <!--联系人明细-->
        <banquet-forward-contact-list :flowObjId="flowObjId"></banquet-forward-contact-list>
        <view class="approval-operator" v-if="!$utils.isEmpty(approvalId)">
            <approval-operator :approvalId="approvalId"></approval-operator>
        </view>
    </link-page>
</template>

<script>
import approvalHistoryPoint from '../../lzlj/approval/components/approval-history-point.vue'
import LineTitle from '../../lzlj/components/line-title.vue'
import banquetBasicInfo from '../../headquarters-activity/components/banquet-basic-info.vue'
import approvalOperator from "../../lzlj/approval/components/approval-operator.vue";
import banquetFeedback from "../components/banquet-feedback.vue";
import banquetOutboundDetails from "../../headquarters-activity/components/banquet-outbound-details.vue";
import banquetInboundDetails from '../../headquarters-activity/components/banquet-inbound-details.vue';
import banquetForwardContactList from "../../headquarters-activity/components/banquet-forward-contact-list.vue";
import banquetConsumerOpen from '../../headquarters-activity/components/banquet-consumer-open.vue';

export default {
    name: 'banquet-approve-page',
    components: {
        banquetFeedback,
        LineTitle,
        approvalHistoryPoint,
        banquetBasicInfo,
        banquetInboundDetails,
        approvalOperator,
        banquetOutboundDetails,
        banquetForwardContactList,
        banquetConsumerOpen
    },
    data() {
        return {
            feedbackCode: null,
            approvalId: null,
            flowObjId: null,
            registerRequest: null, //登记要求
            regReqList: [], //登记要求
            dataLoaded: false,
            data: {},
            approval_type: null,
            templateList: []
        }
    },
    async created() {
        let sceneObj = await this.$scene.ready();     // 消息场景对象
        if (sceneObj.query['approval_from'] === 'qw') {
            this.approvalId = sceneObj.query['approval_id'];
            this.flowObjId = sceneObj.query['flowObjId'];
            this.approval_type = sceneObj.query['approval_type'];
        } else {
            this.approvalId = this.pageParam.data.id; //审批传过来的审批数据id
            this.flowObjId = this.pageParam.data.flowObjId; //审批传过来的查询数据id
        }
        await this.queryTemplate();
        await this.dataLoad();
    },
    methods: {
        /**
         *  @desc 查询完再加载
         *  <AUTHOR>
         *  @date 2023-07-05
         **/
        async dataLoad() {
            await this.queryBasicInfo();
            this.dataLoaded = true;
        },
        /**
         * 查询信息
         *  <AUTHOR>
         *  @date 2023-07-05
         **/
        async queryBasicInfo() {
            try {
                const data = await this.$http.post('action/link/headquarterFeedback/queryById', {
                    id: this.flowObjId
                })
                if (data.success) {
                    this.registerRequest = data.result.registerRequest;
                    this.feedbackCode = data.result.feedbackCode;
                    this.data = data.result;
                    this.switchRegistList();
                } else {
                    this.$message.warn("查询该活动失败，请稍后重试！");
                }
            } catch (e) {
                this.$message.warn("查询该活动失败，请稍后重试！");
            }
        },
        /**
         *  @desc 登记要求（字符串转换为数组）
         *  <AUTHOR>
         *  @date 2023-06-29
         **/
        switchRegistList() {
            // 获取模版子类型，将返回的登记要求字符串转化为数组
            const regRegStr = this.registerRequest.replace(/"/g, '');
            if (this.registerRequest.charAt(0) === "[") {
                this.regReqList = regRegStr.slice(1, -1).split(',');
            } else {
                this.regReqList = regRegStr.slice(1, -1);
            }
        },
         /**
         * 获取宴席模板
         * <AUTHOR>
         * @date	2023/12/21 16:06
         */
         async queryTemplate() {
            try {
                const {success, result} = await this.$utils.getQwMpTemplate('BanquetTemplate');
                if (success) {
                    let resultOpt = JSON.parse(result);
                    this.templateList = JSON.parse(resultOpt.conf);
                }
            } catch (e) {
                console.log('err: 获取宴席模板失败', e);
            }
        }
    }
}
</script>

<style lang="scss">
.banquet-approve-page {

}
</style>
