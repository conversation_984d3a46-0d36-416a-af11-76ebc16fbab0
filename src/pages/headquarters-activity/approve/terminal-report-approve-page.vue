<!--
@createdBy  yangying
@date  2023/08/10
@description 终端名单提报审批
-->
<template>
    <link-page class="terminal-report-approve-page">
        <approval-history-point v-if="!$utils.isEmpty(approvalId)" :approvalId="approvalId"></approval-history-point>
        <activity-item :data="activityItem"/>
        <link-auto-list :option="terminalOption" :hideCreateButton="true" v-if="flowObjId">
            <template slot-scope="{data, index}">
                <terminal-list-item :data="data" :broadCompanyCode="broadCompanyCode" slot="note"/>
            </template>
        </link-auto-list>
        <view class="approval-operator" v-if="!$utils.isEmpty(approvalId)">
            <approval-operator :approvalId="approvalId"></approval-operator>
        </view>
    </link-page>
</template>

<script>
import approvalHistoryPoint from '../../lzlj/approval/components/approval-history-point';
import approvalOperator from '../../lzlj/approval/components/approval-operator';
import TerminalListItem from '../components/terminal-list-item';
import ActivityItem from '../components/activity-item';

export default {
    name: 'terminal-report-approve-page',
    components: {
        approvalHistoryPoint,
        approvalOperator,
        TerminalListItem,
        ActivityItem
    },
    data() {
        const terminalOption = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/submitAccount/queryByExamplePage'
            },
            param: () => {
                return {
                    filtersRaw: [{id: 'headId', property: 'headId', value: this.flowObjId}]
                }
            },
            sortOptions: null,
            hooks: {
                afterLoad (data) {
                    data.rows.map(async (item) => {
                        if (!this.$utils.isEmpty(item.storePicPreKey)) {
                            let urlData = this.$image.getSignedUrl(item.storePicPreKey);
                            this.$set(item, 'storeUrl', urlData);
                        } else {
                            this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
                        }
                    })
                }
            }
        });
        return {
            terminalOption,
            approvalId: null,
            flowObjId: null,
            activityId: null,
            activityItem: {},
            broadCompanyCode: ''
        }
    },
    async created() {
        let sceneObj = await this.$scene.ready();     // 消息场景对象
        if (sceneObj.query['approval_from'] === 'qw') {
            this.approvalId = sceneObj.query['approval_id'];
            this.flowObjId = sceneObj.query['flowObjId'];
        } else {
            this.approvalId = this.pageParam.data.id; //审批传过来的审批数据id
            this.flowObjId = this.pageParam.data.flowObjId; //审批传过来的查询数据id
            const approveData = this.pageParam.data.flowObjDetail ? JSON.parse(this.pageParam.data.flowObjDetail) : null;
            if (approveData && approveData.activityId) {
                this.activityId = approveData.activityId
            }
        }
        this.broadCompanyCode = await this.$utils.getCfgProperty('getPurchaseSumForOrder');
        await this.queryData();
    },
    methods: {
        /**
         * 获取活动信息
         * <AUTHOR>
         * @date	2023/8/10 11:52
         */
        async queryData() {
            try {
                this.$utils.showLoading();
                // 没有活动id时获取活动id
                if (!this.activityId) {
                    const data = await this.$http.post('action/link/headSubmit/queryById', {
                        id: this.flowObjId
                    });
                    data.success && (this.activityId = data.result.activityId);
                }
                // 获取活动信息
                const {success, result} = await this.$http.post('action/link/headquarterActivity/queryById', {
                    id: this.activityId
                })
                success && (this.activityItem = result);
            } catch (e) {
                this.$utils.hideLoading();
                console.log('e', e);
            } finally {
                this.$utils.hideLoading();
            }
        }
    }
}
</script>

<style lang="scss">
.terminal-report-approve-page {
    .terminal-list-item {
        margin: 24px;
    }

    .link-auto-list-no-more {
        display: none !important;
    }
}
</style>
