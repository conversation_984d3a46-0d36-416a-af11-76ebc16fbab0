<!--
@created<PERSON><PERSON>  tan<PERSON>
@date  2025/01/15
@description 品鉴权益兑付审批详情
-->
<template>
    <link-page class="appreciation-approve-page">
        <approval-history-point v-if="!$utils.isEmpty(approvalId)" :approvalId="approvalId"></approval-history-point>
        <view class="appreciation-content">
            <link-form v-model="formData" ref="form">
            <main-form-info :form-data="formData" :show-fieds="fields">
                <template slot="activity" slot-scope="{data}" v-if="flowStatus === 'Running'">
                   <view class="label minWidth"> <text class="required">*</text>{{data.label}}</view>
                    <view class="value">
                        <link-object
                                pageTitle="活动列表"
                                :value="formData.activityName"
                                :row="formData"
                                :disabled="flowStatus !== 'Running'"
                                :option="basicOption"
                                placeholder="请选择市场活动"
                                :map="{activityCode:'activityNum', activityName:'activityName'}">
                            <template v-slot="{data}">
                                <item :key="data.id" :data="data" :arrow="false" style="margin: 20rpx;">
                                    <view style="width: 100vw;clear: both;line-height: 40rpx;font-size: 24rpx;">
                                        <view>
                                            <view style="display: flex;justify-content: space-between;">
                                                <view style="background: #A6B4C7; font-size: 28rpx;color: #FFFFFF;
                                                    letter-spacing: 0;line-height: 40rpx;padding: 2rpx 8rpx;
                                                    border-radius: 8rpx;">{{data.activityNum}}</view>
                                                <view style="min-width: 60rpx; color: white;font-size: 20rpx;
                                                    letter-spacing: 0;text-align: center;text-decoration: none;
                                                    transform: skew(-30deg, 0);background: #2F69F8;padding: 0 10rpx;
                                                    margin-right: 10rpx;">
                                                    <view style="transform: skew(30deg, 0)">{{data.status | lov('MC_STATUS')}}</view>    
                                                </view>
                                            </view>
                                            <view style="font-family: PingFangSC-Semibold;font-size: 28rpx;color: #262626;
                                                letter-spacing: 0;margin: 20rpx 0;">
                                                {{data.activityName.length > 20 ? data.activityName.substring(0,20) + '...' : data.activityName}}
                                            </view>
                                            <view style="display: flex; flex-wrap: wrap;">
                                                <view style="display: flex; width: 50%;" v-for="(item,index) in activeField" :key="index">
                                                    <view style="min-width: 120rpx;">{{item.label}}</view>
                                                    <view style="margin-left: 10rpx; color:black;">
                                                        <text v-if="item.type === 'lov'" :style="{color:item.color}">{{data[item.key] | lov(item.lov)}}</text>
                                                        <text v-else-if="item.type === 'date'">{{data[item.key] | date(item.date)}}</text>
                                                        <text v-else>{{data[item.key]}}</text>
                                                    </view>
                                                </view>
                                            </view>
                                        </view>
                                    </view>
                                </item>
                            </template>
                            <template slot="top">
                                <link-fab-button  @tap="goCreate"/>
                            </template>
                        </link-object>
                    </view>
                </template>
            </main-form-info>
            </link-form>
        </view>
        <view class="approval-operator" v-if="!$utils.isEmpty(approvalId)">
            <approval-operator :approvalId="approvalId" @agreeMeth='agreeMeth'></approval-operator>
        </view>
    </link-page>
</template>

<script>
import approvalHistoryPoint from '../../lzlj/approval/components/approval-history-point';
import approvalOperator from '../../lzlj/approval/components/approval-operator';
import mainFormInfo from '../../public-components/main-form-info';
export default {
    name: 'appreciation-approve-page',
    components: {
        approvalHistoryPoint,
        approvalOperator,
        mainFormInfo
    },
    data() {
        const basicOption = new this.AutoList(this, {
            module: 'action/link/basic',
            url: {
                queryByExamplePage: 'export/link/es/marketAct/queryByExamplePage'
            },
            sortField: 'created',
            sortDesc: 'desc',
            searchFields: ['id'],
            hooks:{
                beforeLoad({param}){
                    const filtersRaw = [
                      {id: "statusForFilter", property: "statusForFilter", operator: "=", value: "ActualAmount"},//已实发
                      {id: "beneficiaryCode", property: "beneficiaryCode", operator: "=", value:  this.formData.acctCode}, //受益客户
                  ]
                  const search = param.filtersRaw.find(i=>{
                      return i.property === '[id]'
                  })
                  if(search){
                      filtersRaw.push({
                          id: "searchValue",
                          operator: "or like",
                          property: "[activityNum,activityName,cashApplyAmount,executor,exeCaseCode]",
                          value: search.value
                      })
                  }
                  param.filtersRaw = filtersRaw
                },
                afterLoad(data){
                    data.rows.forEach(i=>{
                        i.realAmountShow = (i.cashRealAmount + 0) + (i.prodRealAmount + 0)
                    })
                }
            }
        })	
        return {
            basicOption,
            approvalId: null,
            flowObjId: null,
            flowStatus: null,
            formData: {
                acctName: '测试终端',
                acctCode: 'A0383763',
            },
            activeField: [
                {label: '审批状态', key: 'aproStatus', type: 'lov', lov: 'APRO_STATUS',color:'#2EB3C2'},
                {label: '稽核状态', key: 'whetherAudit', type: 'lov', lov: 'WHETHER_AUDIT'},
                {label: '活动时间', key: 'startTime',type: 'date', date: 'YYYY-MM-DD'},
                {label: '提报人', key: 'executor'},
                {label: '实发金额', key: 'realAmountShow'}
            ],
            fields:[
                {label: '权益编码', key: 'id'},
                {label: '权益名称', key: 'rightsName'},
                {label: '终端编码', key: 'acctCode'},
                {label: '终端名称', key: 'acctName'},
                {label: '产品编码', key: 'prodCode'},
                {label: '产品名称', key: 'prodName'},
                {label: '公司编码', key: 'companyCode'},
                {label: '公司名称', key: 'companyName'},
                {label: '权益数额', key: 'rightsAmount'},
                {label: '市场活动', key: 'activity', slot: true}
            ]
        }
    },
    async created() {
        let sceneObj = await this.$scene.ready();     // 消息场景对象
        if (sceneObj.query['approval_from'] === 'qw') {
            this.approvalId = sceneObj.query['approval_id'];
            this.flowObjId = sceneObj.query['flowObjId'];
            this.formData = sceneObj.query.flowObjDetail ? JSON.parse(sceneObj.query.flowObjDetail) : null; 
            this.approvalNodeId = sceneObj.query['approvalNodeId'];
            this.flowStatus = sceneObj.query['flowStatus'];
        } else {
            this.approvalId = this.pageParam.data.id; //审批传过来的审批数据id
            this.flowObjId = this.pageParam.data.flowObjId; //审批传过来的查询数据id
            this.approvalNodeId = this.pageParam.data['approvalNodeId'];
            this.formData = this.pageParam.data.flowObjDetail ? JSON.parse(this.pageParam.data.flowObjDetail) : null;
            this.flowStatus = this.pageParam.data.flowStatus
        }
    },
    methods: {
        async agreeMeth(datas){
            if(!this.formData.activityCode){
                this.$message.error('请先选择市场活动')
                return 
            }
            const data = {
                ...datas,
                rightsCode: this.formData.id,
                rightsName: this.formData.rightsName,
                terminalCode: this.formData.acctCode,
                terminalName: this.formData.acctName,
                productCode: this.formData.prodCode,
                productName: this.formData.prodName,
                companyCode: this.formData.companyCode,
                companyName: this.formData.companyName,
                rightsNum: this.formData.rightsAmount,
                activityCode: this.formData.activityCode,
                activityName: this.formData.activityName,
                approvalNodeId: this.approvalNodeId,
                userId: this.$taro.getStorageSync('token').result.id
            };
            const url = 'action/link/examineRights/agreeApproval';
            const resp = await this.$http.post(url, data)
            if(resp.success){
                this.$bus.$emit('refreshApprovalList');
                this.$nav.back();
            }
        },
        goCreate(){
            this.$nav.relaunch('/pages/lj-market-activity/market-activity/new-market-activity-page')
        }
    }
}
</script>

<style lang="scss">
.appreciation-approve-page {
  .appreciation-content{
      margin-bottom: 40px;
  }
  .required{
      color: red;
      margin-right: 5px;
  }
  .market-activity-list-item {
        background: #FFFFFF;
        width: 95%;
        margin: 24px auto auto auto;
        border-radius: 16px;
    }
    .minWidth{
        min-width: 400px;
    }
}
</style>
