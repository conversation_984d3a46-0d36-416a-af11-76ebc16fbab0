<!--
@created<PERSON><PERSON>  yangying
@date  2023/08/21
@description 营销6.0活动审批
-->
<template>
    <link-page class="marketing-six-approve-page">
        <approval-history-point v-if="!$utils.isEmpty(approvalId)" :approvalId="approvalId"></approval-history-point>

        <line-title title="基础信息"/>
        <view class="basic-info">
            <view class="info-item">
                <view class="label">终端编码</view>
                <view class="value">{{terminalItem.accountCode}}</view>
            </view>
            <view class="info-item">
                <view class="label">终端名称</view>
                <view class="value">{{terminalItem.accountName}}</view>
            </view>
            <view class="info-item">
                <view class="label">终端地址</view>
                <view class="value">{{terminalItem.province}} {{terminalItem.city}} {{terminalItem.district}}</view>
            </view>
            <view class="info-item">
                <view class="label">详细地址</view>
                <view class="value">{{terminalItem.address}}</view>
            </view>
        </view>
        <view class="approval-operator" v-if="!$utils.isEmpty(approvalId)">
            <approval-operator :approvalId="approvalId"></approval-operator>
        </view>
    </link-page>
</template>

<script>
import approvalHistoryPoint from '../../lzlj/approval/components/approval-history-point';
import approvalOperator from '../../lzlj/approval/components/approval-operator';
import LineTitle from '../../lzlj/components/line-title';
export default {
    name: 'marketing-six-approve-page',
    components: {
        approvalHistoryPoint,
        approvalOperator,
        LineTitle,
    },
    data() {
        return {
            prodList: [],
            prodTotal: 0,
            promotionTotal: 0,
            promotionList: [],
            partOrgList: [],
            partOrgTotal: 0,
            partTerminalList: [],
            partTerminalTotal: 0,
            approvalId: null,
            flowObjId: null,
            approval_type: null,
            broadCompanyCode: '',
            terminalItem: {}
        }
    },
    async created() {
        let sceneObj = await this.$scene.ready();     // 消息场景对象
        if (sceneObj.query['approval_from'] === 'qw') {
            this.approvalId = sceneObj.query['approval_id'];
            this.flowObjId = sceneObj.query['flowObjId'];
            this.approval_type = sceneObj.query['approval_type'];
        } else {
            this.approvalId = this.pageParam.data.id; //审批传过来的审批数据id
            this.flowObjId = this.pageParam.data.flowObjId; //审批传过来的查询数据id
        }
        this.broadCompanyCode = await this.$utils.getCfgProperty('getPurchaseSumForOrder');
        await this.queryData();
    },
    methods: {
        /**
         * 获取终端数据
         * <AUTHOR>
         * @date	240708
         */
        async queryData() {
            try {
                const {success, result} = await this.$http.post('action/link/accountApprove/queryById', {
                    id: this.flowObjId
                })
                if (success) {
                    this.terminalItem = result;
                }
            } catch (e) {

            }
        },
    }
}

</script>

<style lang="scss">
.marketing-six-approve-page {
    .line-title {
        margin-bottom: 20px;
    }

    .basic-info {
        background: #fff;
        margin: 20px;
        padding: 20px;
        border-radius: 20px;

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            font-size: 28px;
            margin-bottom: 14px;

            .label {
                color: #8C8C8C;
                margin-right: 20px;
            }

            .value {
                color: #262626;
                flex: 1;
                word-wrap: break-word;
                word-break:break-all;
                white-space: pre-wrap;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                display: -webkit-box;
            }
        }
    }

    .prod-list {
        margin: 20px;

        .no-data {
            background: #fff;
            padding: 50px 20px;
            margin: 20px 0;
            border-radius: 16px;
            font-size: 28px;
            text-align: center;
            color: #8C8C8C;
        }
    }

    .prod-item {
        background: #fff;
        padding: 20px;
        margin: 20px 0;
        border-radius: 16px;

        .data {
            display: flex;
            font-size: 28px;
            margin-bottom: 10px;

            .title {
                color: #8C8C8C;
                margin-right: 20px;
            }

            .val {
                color: #262626;
                flex: 1;
                word-wrap: break-word;
                word-break:break-all;
                white-space: pre-wrap;
            }
        }
    }

}
</style>
