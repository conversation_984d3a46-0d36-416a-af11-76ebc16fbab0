<!--
总部活动-宴席活动-审批
<AUTHOR>
@date 2023-07-13
@file banquet-approve-page.vue
-->
<template>
    <link-page class="banquet-approve-page">
        <approval-history-point v-if="!$utils.isEmpty(approvalId)" :approvalId="approvalId"></approval-history-point>
        <banquet-basic-info v-if="!$utils.isEmpty(flowObjId)" :flowObjId="flowObjId"
                            ref="basicInfo" :templateList="templateList"></banquet-basic-info>
        <view class="approval-operator" v-if="!$utils.isEmpty(approvalId)">
            <approval-operator :approvalId="approvalId"></approval-operator>
        </view>
    </link-page>
</template>

<script>
import approvalHistoryPoint from '../../lzlj/approval/components/approval-history-point.vue'
import LineTitle from '../../lzlj/components/line-title.vue'
import banquetBasicInfo from '../../headquarters-activity/components/banquet-basic-info.vue'
import approvalOperator from "../../lzlj/approval/components/approval-operator.vue";

export default {
    name: 'banquet-approve-page',
    components: {
        LineTitle,
        approvalHistoryPoint,
        banquetBasicInfo,
        approvalOperator
    },
    data() {
        return {
            templateList: [], // 宴席模板
            approvalId: null,
            flowObjId: null,
            approval_type: null
        }
    },
    async created() {
        let sceneObj = await this.$scene.ready();     // 消息场景对象
        if (sceneObj.query['approval_from'] === 'qw') {
            this.approvalId = sceneObj.query['approval_id'];
            this.flowObjId = sceneObj.query['flowObjId'];
            this.approval_type = sceneObj.query['approval_type'];
        } else {
            this.approvalId = this.pageParam.data.id; //审批传过来的审批数据id
            this.flowObjId = this.pageParam.data.flowObjId; //审批传过来的查询数据id
        }
        await this.queryTemplate();
    },
    methods: {
        /**
         * 获取宴席模板
         * <AUTHOR>
         * @date	2023/12/21 16:06
         */
        async queryTemplate() {
            try {
                const {success, result} = await this.$utils.getQwMpTemplate('BanquetTemplate');
                if (success) {
                    let resultOpt = JSON.parse(result);
                    this.templateList = JSON.parse(resultOpt.conf);
                    console.log('this.templateList', this.templateList);
                }
            } catch (e) {

            }
        }
    }
}
</script>

<style lang="scss">
.banquet-approve-page {
    .banquet-plan-prod-item {
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
        padding: 12px;

        .content-middle-line {
            width: 100%;

            .data {
                width: 100%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    width: 20%;
                    float: left;
                    padding-left: 6px;
                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                }

                .Submitted, .Feedback {
                    color: #2F69F8;
                }

                .Approve, .FeedbackApro {
                    color: #2EB3C2;
                }

                .Refused, .Refeedback {
                    color: #FF5A5A;
                }

            }
        }
    }
}
</style>
