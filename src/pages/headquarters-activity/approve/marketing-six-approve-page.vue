<!--
@created<PERSON><PERSON>  yangying
@date  2023/08/21
@description 营销6.0活动审批
-->
<template>
    <link-page class="marketing-six-approve-page">
        <approval-history-point v-if="!$utils.isEmpty(approvalId)" :approvalId="approvalId"></approval-history-point>

        <line-title title="基础信息"/>
        <view class="basic-info">
            <view class="info-item">
                <view class="label">活动编码</view>
                <view class="value">{{activityItem.activityNum}}</view>
            </view>
            <view class="info-item">
                <view class="label">活动名称</view>
                <view class="value">{{activityItem.activityName}}</view>
            </view>
            <view class="info-item">
                <view class="label">活动主题</view>
                <view class="value">{{activityItem.activityType | lov('HEAD_ACT_TYPE')}}</view>
            </view>
            <view class="info-item">
                <view class="label">开始时间</view>
                <view class="value">{{activityItem.startTime | date('YYYY-MM-DD')}}</view>
            </view>
            <view class="info-item">
                <view class="label">结束时间</view>
                <view class="value">{{activityItem.endTime | date('YYYY-MM-DD')}}</view>
            </view>
            <view class="info-item">
                <view class="label">登记要求</view>
                <view class="value">{{activityItem.registerRequest}}</view>
            </view>
            <view class="info-item">
                <view class="label">活动状态</view>
                <view class="value">{{activityItem.activityStatus | lov('HEAD_ACT_STATUS')}}</view>
            </view>
            <view class="info-item">
                <view class="label">审批状态</view>
                <view class="value">{{activityItem.approveStatus | lov('HEAD_APRO_STATUS')}}</view>
            </view>
            <view class="info-item">
                <view class="label">申请时间</view>
                <view class="value">{{activityItem.applyTime}}</view>
            </view>
            <view class="info-item">
                <view class="label">申请人</view>
                <view class="value">{{activityItem.applyUserName}}</view>
            </view>
            <view class="info-item">
                <view class="label">所属组织</view>
                <view class="value">{{activityItem.sourceCompanyCode}}</view>
            </view>
            <view class="info-item">
                <view class="label">活动说明</view>
                <view class="value">{{activityItem.remark}}</view>
            </view>
        </view>
        <line-title title="推广阶段" :buttonName="promotionTotal > 3 ? '查看全部' : ''" @tap="showMore('promotion')"/>
        <view class="prod-list">
            <view class="prod-item" v-for="(data, index) in promotionList" :key="index">
                <view class="data">
                    <view class="title">阶段名称</view>
                    <view class="val">{{data.promotionStage | lov('PROMOTION_STAGE')}}</view>
                </view>
                <view class="data">
                    <view class="title">开始时间</view>
                    <view class="val">{{data.startTime.slice(0,10)}}</view>
                </view>
                <view class="data">
                    <view class="title">结束时间</view>
                    <view class="val">{{data.endTime.slice(0,10)}}</view>
                </view>
            </view>
        </view>

        <line-title title="活动品项" :buttonName="prodTotal > 3 ? '查看全部' : ''" @tap="showMore('prodOption')"/>
        <view class="prod-list">
            <view class="prod-item" v-for="(data, index) in prodList" :key="index">
                <view class="data">
                    <view class="title">产品编码</view>
                    <view class="val">{{data.productCode}}</view>
                </view>
                <view class="data">
                    <view class="title">产品名称</view>
                    <view class="val">{{data.productName}}</view>
                </view>
            </view>
        </view>

        <line-title title="参与组织" :buttonName="partOrgTotal > 3 ? '查看全部' : ''" @tap="showMore('partOrgOption')"/>
        <view class="prod-list">
            <view class="prod-item" v-for="(data, index) in partOrgList" :key="index">
                <view class="data">
                    <view class="title">组织编码</view>
                    <view class="val">{{data.organizationCode}}</view>
                </view>
                <view class="data">
                    <view class="title">组织名称</view>
                    <view class="val">{{data.organizationName}}</view>
                </view>
                <view class="data">
                    <view class="title">组织类型</view>
                    <view class="val">{{data.organizationType | lov('ORG_TYPE')}}</view>
                </view>
                <view class="data">
                    <view class="title">所属品牌公司</view>
                    <view class="val">{{data.companyName}}</view>
                </view>
            </view>
        </view>

        <line-title title="参与终端" :buttonName="partTerminalTotal > 3 ? '查看全部' : ''"  @tap="showMore('partTerminalOption')"/>
        <view class="prod-list">
            <view class="prod-item" v-for="(data, index) in partTerminalList" :key="index">
                <view class="data">
                    <view class="title">终端编码</view>
                    <view class="val">{{data.accountCode}}</view>
                </view>
                <view class="data">
                    <view class="title">终端名称</view>
                    <view class="val">{{data.accountName}}</view>
                </view>
                <view class="data">
                    <view class="title">纳税人识别号</view>
                    <view class="val">{{data.creditNo}}</view>
                </view>
                <view class="data">
                    <view class="title">所属品牌公司</view>
                    <view class="val">{{data.companyName}}</view>
                </view>
            </view>
            <view class="no-data" v-if="!partTerminalTotal">暂无数据</view>
        </view>
        <view class="approval-operator" v-if="!$utils.isEmpty(approvalId)">
            <approval-operator :approvalId="approvalId"></approval-operator>
        </view>
    </link-page>
</template>

<script>
import approvalHistoryPoint from '../../lzlj/approval/components/approval-history-point';
import approvalOperator from '../../lzlj/approval/components/approval-operator';
import LineTitle from '../../lzlj/components/line-title';
import TerminalListItem from "../components/terminal-list-item";
export default {
    name: 'marketing-six-approve-page',
    components: {
        approvalHistoryPoint,
        approvalOperator,
        LineTitle,
        TerminalListItem
    },
    data() {
        return {
            prodList: [],
            prodTotal: 0,
            promotionTotal: 0,
            promotionList: [],
            partOrgList: [],
            partOrgTotal: 0,
            partTerminalList: [],
            partTerminalTotal: 0,
            approvalId: null,
            flowObjId: null,
            approval_type: null,
            broadCompanyCode: '',
            activityItem: {}
        }
    },
    async created() {
        let sceneObj = await this.$scene.ready();     // 消息场景对象
        if (sceneObj.query['approval_from'] === 'qw') {
            this.approvalId = sceneObj.query['approval_id'];
            this.flowObjId = sceneObj.query['flowObjId'];
            this.approval_type = sceneObj.query['approval_type'];
        } else {
            this.approvalId = this.pageParam.data.id; //审批传过来的审批数据id
            this.flowObjId = this.pageParam.data.flowObjId; //审批传过来的查询数据id
        }
        this.broadCompanyCode = await this.$utils.getCfgProperty('getPurchaseSumForOrder');
        await this.queryData();
    },
    methods: {
        /**
         * 获取数据
         * <AUTHOR>
         * @date	2023/8/22 11:14
         */
        async queryData() {
            try {
                // 获取活动信息
                const {success, result} = await this.$http.post('action/link/headquarterActivity/queryById', {
                    id: this.flowObjId
                })
                if (success) {
                    // 处理登记要求
                    let arr = JSON.parse(result.registerRequest);
                    arr = await Promise.all(arr.map(async (item) =>
                        await this.$lov.getNameByTypeAndVal('TMPL_SUB_BIZ_TYPE', item)
                    ));
                    result.registerRequest = arr.join(',');
                    this.activityItem = result;
                }
                // 获取推广阶段
                const {success: promotionSuccess, rows: promotionList, total: promotionTotal} = await this.$http.post(
                    'action/link/promotionStage/queryByExamplePage', {
                        rows: 3,
                        totalFlag: true,
                        filtersRaw: [{id: 'actId', property: 'actId', value: this.flowObjId}],
                });
                if (promotionSuccess) {
                    this.promotionList = promotionList;
                    this.promotionTotal = promotionTotal;
                }
                // 获取活动品项
                const {success: prodSuccess, rows: prodList, total: prodTotal} = await this.$http.post(
                    'action/link/activityProduct/queryByExamplePage', {
                        rows: 3,
                        totalFlag: true,
                        filtersRaw: [
                            {id: 'activityId', property: 'activityId', value: this.flowObjId}
                        ]
                });
                if (prodSuccess) {
                    this.prodList = prodList;
                    this.prodTotal = prodTotal;
                }
                // 获取参与组织
                const {success: partOrgSuccess, rows: partOrgList, total: partOrgTotal} = await this.$http.post(
                    'action/link/activityOrganization/queryByExamplePage', {
                        rows: 3,
                        totalFlag: true,
                        filtersRaw: [
                            {id: 'activityId', property: 'activityId', value: this.flowObjId}
                        ]
                    });
                if (partOrgSuccess) {
                    this.partOrgList = partOrgList;
                    this.partOrgTotal = partOrgTotal;
                }
                // 获取参与终端
                const {success: partTerminalSuccess, rows: partTerminalList, total: partTerminalTotal} = await this.$http.post(
                    'action/link/activityAccount/queryByExamplePage', {
                        rows: 3,
                        totalFlag: true,
                        filtersRaw: [
                            {id: 'activityId', property: 'activityId', value: this.flowObjId}
                        ]
                    });
                if (partTerminalSuccess) {
                    this.partTerminalList = partTerminalList;
                    this.partTerminalTotal = partTerminalTotal;
                }
            } catch (e) {

            }
        },
        /**
         * 查看全部数据
         * <AUTHOR>
         * @date	2023/8/22 17:05
         */
        showMore(option) {
            const title = {
                prodOption: '活动品项',
                partOrgOption: '参与组织',
                partTerminalOption: '参与终端',
            }
            this.$nav.push('/pages/headquarters-activity/marketing-six-more-page.vue', {
                flowObjId: this.flowObjId,
                option,
                title: title[option]
            });
        },
    }
}

</script>

<style lang="scss">
.marketing-six-approve-page {
    .line-title {
        margin-bottom: 20px;
    }

    .basic-info {
        background: #fff;
        margin: 20px;
        padding: 20px;
        border-radius: 20px;

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            font-size: 28px;
            margin-bottom: 14px;

            .label {
                color: #8C8C8C;
                margin-right: 20px;
            }

            .value {
                color: #262626;
                flex: 1;
                word-wrap: break-word;
                word-break:break-all;
                white-space: pre-wrap;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                display: -webkit-box;
            }
        }
    }

    .prod-list {
        margin: 20px;

        .no-data {
            background: #fff;
            padding: 50px 20px;
            margin: 20px 0;
            border-radius: 16px;
            font-size: 28px;
            text-align: center;
            color: #8C8C8C;
        }
    }

    .prod-item {
        background: #fff;
        padding: 20px;
        margin: 20px 0;
        border-radius: 16px;

        .data {
            display: flex;
            font-size: 28px;
            margin-bottom: 10px;

            .title {
                color: #8C8C8C;
                margin-right: 20px;
            }

            .val {
                color: #262626;
                flex: 1;
                word-wrap: break-word;
                word-break:break-all;
                white-space: pre-wrap;
            }
        }
    }

}
</style>
