<!--
总部活动-门头变更费用申请-审批
<AUTHOR>
@date 2023-08-04
-->
<template>
	<link-page class="construct-special-approve-page">
		<approval-history-point v-if="!$utils.isEmpty(approvalId)" :approvalId="approvalId"></approval-history-point>
		<view>
			<view class="approve-edit" v-show="isApprove"  @tap="canEdit=!canEdit,item.applyAmounts = item.applyAmount">{{canEdit?'取消':'编辑'}}</view>
			<construct-special-info :item='item' :isApprove='canEdit' :fromApprove="true"></construct-special-info>
			<view>
				<view class="terminal-title">
					<view>终端名单(已审核数量{{choseNum}})</view>
				</view>
				<view  v-for="(items,index) in item.accountList" :key="index+'approve'">
					<terminal-item :item='items' :hideThree='hideThree'>
						<template v-slot:chose  v-if="isApprove || showPass">
							<view class="terminal-checkbox">
								<link-checkbox v-show="isApprove" v-model='items.selectFlag' trueValue="Y" falseValue="N"></link-checkbox>
								<text v-show="isApprove" slot="title">审核通过</text>
								<view v-show="showPass && items.selectFlag === 'Y'">已通过</view>
							</view>
						</template>
					</terminal-item>
				</view>
			</view>
		</view>

		<view class="approval-operator" v-if="!$utils.isEmpty(approvalId)">
			<link-button block @tap="changSubmit" v-show="canEdit">提交</link-button>
			<approval-operator v-show="!canEdit" :fromNeed="true" :approvalId="approvalId"></approval-operator>
		</view>
	</link-page>
</template>

<script>
	import approvalHistoryPoint from '../../lzlj/approval/components/approval-history-point.vue'
	import approvalOperator from "../../lzlj/approval/components/approval-operator.vue";
	import LineTitle from "../../lzlj/components/line-title.vue";
	import terminalItem from '../components/terminal-item'
	import constructSpecialInfo from '../components/construct-special-info'
	export default {
		name: 'construct-special-approve-page',
		components: {
			approvalHistoryPoint,
			approvalOperator,
			LineTitle,
			terminalItem,
			constructSpecialInfo
		},
		data() {
			return {
				chose:null,
				isApprove:true,
				item: {},
				approvalId: null,
				flowObjId: null,
				approval_type: null,
				canEdit:false, //是都可编辑
				showPass:false, //是否展示通过终端
				hideThree:false,
			}
		},
		computed:{
			choseNum(){
				let list = this.item.accountList || []
				let newChose = list.filter(i=>{
					return i.selectFlag === 'Y'
				})
				let num = 0
				if(newChose&&newChose.length){
					num = newChose.length
				}
				return num
			}
		},
		async created() {
			let sceneObj = await this.$scene.ready();     // 消息场景对象
            let flowObjDetail
			if (sceneObj.query['approval_from'] === 'qw') {
			    this.approvalId = sceneObj.query['approval_id'];
			    this.flowObjId = sceneObj.query['flowObjId'];
			    this.approval_type = sceneObj.query['approval_type'];
                flowObjDetail = sceneObj.query.flowObjDetail
			} else {
			    this.approvalId = this.pageParam.data.id; //审批传过来的审批数据id
			    this.flowObjId = this.pageParam.data.flowObjId; //审批传过来的查询数据id
                flowObjDetail = this.pageParam.data.flowObjDetail
			}
			await this.dataLoad(flowObjDetail);
			this.$bus.$on('approvalSuccess', (data)=> {
				this.isApprove = data;
			})
		},
		methods: {
			/**
			 *  @desc 查询完再加载
			 *  <AUTHOR>
			 *  @date 2023-08-04
			 **/
			async dataLoad(flowObjDetail) {
				const item = JSON.parse(flowObjDetail)
				this.showPass = this.pageParam.data.flowStatus === 'End'
				try {
					const itemData = this.$http.post('action/link/hundredCityBuild/queryById',{id: item.id})
					const listData = this.$http.post('action/link/simpleTerminalInfo/queryHundredCityBuildDetailPage', {
						actNum: item.actNum,
						actConstructionType: item.actConstructionType
					});
					const backData = await Promise.all([itemData,listData])
					let newItem = backData[0].result
					let rows = backData[1].rows
					rows.forEach(async (item) => {
						if (!this.$utils.isEmpty(item.smallUrl)) {
							let urlData = await this.$image.getSignedUrl(item.smallUrl);
							this.$set(item, 'storeUrl', urlData);
						} else {
							this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
						}
					})
					newItem.accountList = rows
					newItem.approveRemark = item.approveRemark
					this.item = newItem
                    this.hideThree = this.item.actConstructionType === 'ReplacingStoreSignage'
				} catch (error) {
					console.log(error)
				}

			},
			/**
			 *  @desc 编辑修改上传
			 *  <AUTHOR>
			 *  @date 2023-08-11
			 **/
			async changSubmit(){
				const items = JSON.parse(JSON.stringify(this.item))
				items.applyAmount = items.applyAmounts
				Reflect.deleteProperty(items, 'applyAmounts')
				try {
					let resData = await this.$http.post('action/link/hundredCityBuild/hundredCityBuildApproveEdit',{
						...items,
					})
					if(resData.success){
						this.canEdit = false
						this.$message.success('编辑修改成功')
						this.item.applyAmount = items.applyAmount
					}else{
						this.$message.error('编辑修改失败')
					}
				} catch (error) {
					console.log(error)
				}

			}
		}
	}
</script>

<style lang="scss">
	.construct-special-approve-page {
		.approve-edit{
			float:right;
			color:#2f69f8;
			font-size: 26rpx;
			padding: 10px 20px;
		}
		.basic-info-title {
			display: flex;
			justify-content: space-between;
			margin: 0 24px 0 0;

			.right {
				font-family: PingFangSC-Regular serif;
				font-size: 28px;
				color: #2F69F8;
				letter-spacing: 0;
				text-align: right;
				line-height: 28px;
				align-self: flex-end;
			}
		}
.terminal-checkbox{
	font-size: 24px;
	height: 12px;
	@include flex-center-center;
}
		.terminal-title {
			@include flex;
			@include space-between;
			background-color: white;
			margin: 10px;
			padding: 35px;
			font-size: 24px;

			&-add {
				font-size: 32px;
				color: #2f69f8;
			}
		}

		.basic-info {
			background: #FFFFFF;
			border-radius: 16px;
			margin: 24px;

			.block-v {
				padding: 0 24px;
				display: flex;

				.title {
					font-family: PingFangSC-Regular;
					font-size: 28px;
					color: #8C8C8C;
					letter-spacing: 0;
					line-height: 60px;
					width: 35%;
					float: left;
				}

				.val {
					font-family: PingFangSC-Regular;
					font-size: 28px;
					color: #262626;
					letter-spacing: 0;
					text-align: right;
					line-height: 60px;
					width: 65%;
					float: left;
					text-overflow: ellipsis;
				}

				.val-1 {
					font-family: PingFangSC-Regular;
					font-size: 28px;
					color: #262626;
					letter-spacing: 0;
					text-align: right;
					line-height: 60px;
					float: left;
					text-overflow: ellipsis;
					width: 70%;
				}

				.scan-code {
					color: #262626;
					width: 5%;
					text-align: right;
					font-size: 28px;
					padding-top: 8px;
				}
			}

			.line {
				margin: 32px 24px 32px 24px;
				height: 0;
				border: 2px dashed #DADEE9;
			}
		}
	}
</style>
