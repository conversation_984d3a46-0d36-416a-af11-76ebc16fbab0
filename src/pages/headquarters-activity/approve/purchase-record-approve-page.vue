<!--
审批-营销6.0进退货审批详情
-->
<template>
    <link-page class="purchase-record-approve-page">
        <approval-history-point
            :approvalId="approvalId"
            v-if="!$utils.isEmpty(approvalId)"
        ></approval-history-point>
        <view class="menu-stair">
            <view class="line">
                <view class="line-top"></view>
                <view class="line-bottom"></view>
            </view>
            <view class="stair-title">营销6.0进退货审批</view>
        </view>
        <view class="outbound-info">
            <view class="outbound-info-row">
                <view class="label">收发类型</view>
                <view class="value">{{
                    outboundData.shipType | lov("HEAD_TRANSCEIVER_TYPE")
                }}</view>
            </view>
            <view class="outbound-info-row">
                <view class="label">终端名称</view>
                <view class="value">{{ outboundData.terminalName }}</view>
            </view>
            <view class="outbound-info-row">
                <view class="label">公司</view>
                <view class="value">{{ outboundData.companyName }}</view>
            </view>
            <view class="outbound-info-row">
                <view class="label">产品名称</view>
                <view class="value">{{ outboundData.productName }}</view>
            </view>
            <view class="outbound-info-row">
                <view class="label">数量（瓶）</view>
                <view class="value">{{ outboundData.shipNumber }}</view>
            </view>
            <view class="outbound-info-row">
                <view class="label">备注</view>
                <view class="value">{{ outboundData.remark }}</view>
            </view>
        </view>
        <view
            class="approval-operator"
            v-if="approvalId && pageParam.source !== 'terminalDetails'"
        >
            <approval-operator
                :approvalId="approvalId"
                @approvalInfoResult="approvalInfoResult"
            ></approval-operator>
        </view>
    </link-page>
</template>

<script>
import ApprovalHistoryPoint from "../../lzlj/approval/components/approval-history-point.vue";
import ApprovalOperator from "../../lzlj/approval/components/approval-operator.vue";
import { $utils } from "../../utils/$utils";

export default {
    name: "purchase-record-approve-page",
    data() {
        return {
            approvalId: null,
            outboundData: {},
            flowObjId: null,
            approval_type: null,
        };
    },
    components: {
        ApprovalHistoryPoint,
        ApprovalOperator,
    },
    async created() {
        let sceneObj = await this.$scene.ready();
        console.log('sceneObj', sceneObj);
        if(sceneObj.query['approval_from'] === 'qw') {
            this.approvalId = sceneObj.query['approval_id'];
            this.flowObjId = sceneObj.query['flowObjId'];
            this.approval_type = sceneObj.query['approval_type'];
        } else {
            this.approvalId = this.pageParam.data.id; //审批传过来的审批数据id
            this.flowObjId = this.pageParam.data.flowObjId; //审批传过来的查询数据id
        }
    },
    methods: {
        approvalInfoResult(obj) {
            this.outboundData = JSON.parse(obj.flowObjDetail);
            this.getData();
        },
        async getData() {
            const data = await this.$http.post("action/link/purchaseRecord/queryById", {
                id: this.flowObjId,
                }
            );
            if (data.success) {
                this.$set(this.outboundData, 'terminalName', data.result.terminalName);
                this.$set(this.outboundData, 'companyName', data.result.companyName);
            }
        },
    },
};
</script>

<style lang="scss">
.purchase-record-approve-page {
    width: 100%;
    margin-bottom: 120px;
    .menu-stair {
        margin-left: 24px;
        padding: 24px 0;
        @include flex-start-center;
        .line {
            .line-top {
                width: 8px;
                height: 16px;
                background: #3fe0e2;
            }

            .line-bottom {
                width: 8px;
                height: 16px;
                background: #2f69f8;
            }
        }

        .stair-title {
            margin-left: 16px;
            font-family: PingFangSC-Semibold, serif;
            font-size: 32px;
            color: #262626;
            letter-spacing: 1px;
            line-height: 32px;
        }
    }
    .outbound-info {
        background: #ffffff;
        border-radius: 16px;
        margin-bottom: 24px;
        margin-left: auto;
        margin-right: auto;
        width: 702px;
        padding-top: 8px;
        padding-bottom: 20px;
        .outbound-info-row {
            font-family: PingFangSC-Regular, serif;
            font-size: 28px;
            letter-spacing: 0;
            line-height: 28px;
            padding: 32px 24px 0 24px;
            @include flex();
            @include space-between();
            .label {
                color: #8c8c8c;
                width: 30%;
            }
            .value {
                color: #262626;
                width: 70%;
                word-wrap: break-word;
                text-align: right;
            }
        }
    }
}
</style>
