<!--
总部活动-查看登记列表详情
<AUTHOR>
@date 2023-03-31
-->
<template>
    <link-page class="headquarters-register-detail-page" ref="page">
        <lnk-taps :taps="tapsOptions" v-model="tapsActive" @switchTab="switchTab"></lnk-taps>
        <!--基础信息-->
        <basic-info v-if="loadFlag" :registerItem="registerItem"  source="detail" :status='pageParam.data.status'></basic-info>
        <!--常规装进销存情况-->
        <purchase v-if="loadFlag" :registerItem="registerItem" id="purchase" source="detail" :status='pageParam.data.status'></purchase>
        <!--查看-反馈材料-->
        <feedback-materials v-if="loadFlag" id="feedback" :registerItem="registerItem"></feedback-materials>
        <!-- 备注 -->
        <register-remark ref="registerRemark" :headId="registerItem.id" v-if="registerItem.id && showRemark"/>

        <link-fab-group>
            <link-fab-item icon="icon-plus" label="新建登记" v-if="showNewButton" @tap-icon="()=>clickAdd()"/>
            <link-fab-item icon="icon-yijianbaobei1" label="一键报备" @tap-icon="()=>clickReport()"/>
            <link-fab-item icon="icon-beizhu" label="添加备注" v-if="showRemark" @tap-icon="()=>addRemark()"/>
            <link-fab-item icon="icon-zhongzhi" label="撤回" v-if="showRevoke" @tap-icon="()=>revoke()"/>
        </link-fab-group>
    </link-page>
</template>

<script lang="jsx">
import lnkTaps from '../core/lnk-taps/lnk-taps';
import BasicInfo from './components/basic-info';
import FeedbackMaterials from "./components/feedback-materials.vue";
import Purchase from "./components/purchase.vue"
import {ROW_STATUS} from "../../utils/constant";
import SiteMaterials from "./components/site-materials.vue";
import LnkNoAuth from "../core/lnk-no-auth/lnk-no-auth.vue";
import RegisterRemark from './components/register-remark';

export default {
    name: "headquarters-register-detail-page",
    components: {SiteMaterials, FeedbackMaterials, BasicInfo, Purchase, lnkTaps, LnkNoAuth, RegisterRemark},
    data() {
        const tapsOptions = [
            {name: '基础信息', seq: '1', val: 'basicInfo'},
            {name: '常规装进销存情况', seq: '2', val: 'purchase'},
            {name: '反馈材料', seq: '3', val: 'feedback'},
        ];     // 选项卡
        const tapsActive = tapsOptions[0];       // 选项卡默认选中
        const registerItem = {};    // 登记信息
        const userInfo = this.$taro.getStorageSync('token').result;         // 获取用户信息
        return {
            userInfo,
            tapsOptions,    // 选项卡
            tapsActive,     // 选项卡默认选中
            registerItem,   // 登记信息
            currentIndex: 0,
            regReqList: [], // 登记要求列表
            sourceFlag: '',
            loadFlag: false,
            picturesNeedUploaded: [],//需要上传的图片-后台查询对应业务场景配置的图片
            cameraRefresh: false,// 相机刷新标志
            imgAuthFlag: false, //图片模板无权限
            operationFlag: true,//是否可以操作照片的新建和删除
            uploadFlag: false,//是否可以上传照片
            showNewButton: true,//是否显示新建按钮
            mark: '', // 备注
            showRemark: false, // 是否显示添加备注按钮
            showRevoke: false, // 是否显示撤回按钮
        }
    },
    async created() {
        console.log('pageParam', this.pageParam);
        let sceneObj = await this.$scene.ready();     // 消息场景对象
        console.log({sceneObj});
        if (sceneObj.query['approval_from'] === 'qw') {
            this.registerItem.id = sceneObj.query['id'];
            await this.queryBasicInfo();
        } else {
            this.registerItem = this.pageParam.data;
            this.registerItem.feedbackId = this.pageParam.feedbackId;
        }
        if (this.registerItem.scanNumber < this.registerItem.productNumber) {
            this.uploadFlag = true;
        }
        this.$bus.$on('refreshFeedbackImg', async () => {
            this.uploadFlag = false;
        })
        this.tapsActive = this.tapsOptions[0];
        // 获取登记要求
        const regRegStr = this.registerItem.registerRequest.replace(/"/g, '');
        if (this.registerItem.registerRequest.charAt(0) === "[") {
            this.regReqList = regRegStr.slice(1, -1).split(',');
        } else {
            this.regReqList = regRegStr.slice(1, -1);
        }
        this.registerItem.regReqList = this.regReqList;
        await this.querySceneImg();
        if (this.registerItem.activityStatus === 'End') {
            this.showNewButton = false;
        }
        this.showRemark = this.registerItem.status === 'Submitted' && this.registerItem.approveStatus === 'Approved';
        this.showRevoke = this.registerItem.status === 'Submitted' && this.registerItem.createdBy === this.userInfo.id;
        this.loadFlag = true;
    },
    methods: {
        /**
         * 撤回登记
         * <AUTHOR>
         * @date	2023/12/13 11:38
         */
        async revoke() {
            await this.$dialog.confirm('是否确认撤回当前登记？');
            try {
                const {success, rows} = await this.$http.post('action/link/headquarterFeedback/singleUpdateStatus', {
                    id: this.registerItem.id
                })
                if (success) {
                    this.$message.success('撤回成功！');
                    if (this.pageParam.sourceFlag === 'terminalDetail') {
                        this.$bus.$emit('refreshTerminalList');  // 刷新终端列表
                    } else {
                        this.$bus.$emit('refreshRegList', this.registerItem.id);  // 刷新登记列表
                    }
                    this.$nav.redirect('/pages/headquarters-activity/headquarters-register-upsert-page.vue', {
                        data: rows,
                        id: this.registerItem.id,
                        sourceFlag: this.pageParam.sourceFlag === 'terminalDetail' ? 'terminalEdit' : 'regListEdit',
                        row_status: 'UPDATE',
                        feedbackCode: rows.feedbackCode,
                        feedbackId: this.pageParam.feedbackId,
                        activityId: rows.activityId,
                        activityType: this.pageParam.activityType,
                        activityStatus: this.pageParam.activityStatus,
                        activityName: this.pageParam.activityName,
                        registerRequest: this.pageParam.registerRequest,
                    })
                }
            } catch (e) {

            }
        },
        /**
         *  查询基础信息
         *  <AUTHOR>
         *  @date 2023-04-17
         */
        async queryBasicInfo() {
            try {
                const data = await this.$http.post('action/link/headquarterFeedback/queryById', {
                    id: this.registerItem.id
                })
                if (data.success) {
                    this.registerItem = data.result;
                } else {
                    this.$showError("查询基础信息失败，请稍后重试！");
                }
            } catch (e) {
                this.$showError("查询基础信息失败，请稍后重试！");
            }
        },
        /**
         * 添加备注
         * <AUTHOR>
         * @date    2023/8/4 14:46
         */
        addRemark() {
            this.$dialog({
                title: '新增备注',
                content: (h) => {
                    return (
                        <link-textarea style="width: 100%; padding: 0;" v-model={this.mark}
                                       nativeProps={{maxlength: 500}} placeholder="请填写备注"/>
                    )
                },
                cancelButton: true,
                onConfirm: async () => {
                    if (!this.mark) {
                        this.$showError('请填写备注！');
                        return Promise.reject('请填写备注！');
                    }
                    try {
                        this.$utils.showLoading();
                        const {success, newRow, result} = await this.$http.post('action/link/headMark/insert', {
                            headId: this.registerItem.id,
                            remark: this.mark
                        });
                        if (success) {
                            this.$refs.registerRemark.remarkOption.list.unshift(newRow);
                            this.mark = '';
                        } else {
                            this.$utils.hideLoading();
                            return Promise.reject(result);
                        }
                    } catch (e) {

                    } finally {
                        this.$utils.hideLoading();
                    }
                },
                onCancel: () => {

                }
            });
        },
        /**
         * desc 根据业务场景查询配置的需要维护的场景图片
         * <AUTHOR>
         * @date 2023-04-12
         */
        async querySceneImg() {
            this.picturesNeedUploaded = [];
            let picturesNeedUploaded = [];
            for (let i = 0; i < this.regReqList.length; i++) {
                let tempType = this.regReqList[i];
                const data = await this.$utils.getQwMpTemplate('HeadFeedbackData', tempType);
                if (!data.success) {
                    this.imgAuthFlag = true;
                    this.$utils.hideLoading();
                } else {
                    this.imgAuthFlag = false;
                }
                let resultOpt = JSON.parse(data.result);
                let temp = [];//当前业务场景配置的场景图片数组信息
                temp = JSON.parse(resultOpt.conf);
                picturesNeedUploaded.push(temp[0]);
            }
            this.picturesNeedUploaded = picturesNeedUploaded;
        },
        /**
         * tab页切换
         *  <AUTHOR>
         *  @date 2023-04-03
         **/
        switchTab(val, key) {
            this.currentIndex = parseInt(key);
            this.tapsActive = val;
            wx.pageScrollTo({
                selector: `#${val.val}`,
                duration: 500
            })
        },
        /**
         * 点击新建登记
         * <AUTHOR>
         * @date 2023-04-07
         */
        clickAdd() {
            // 状态为新建
            const paramData = {
                ...this.pageParam.data,
                status: 'New'
            }
            if (this.pageParam.sourceFlag === 'terminalDetail') {
                this.$nav.push('/pages/headquarters-activity/headquarters-register-upsert-page.vue', {
                    data: paramData,
                    accountId: this.pageParam.data.terminalId,
                    companyId: this.pageParam.data.companyId,
                    sourceFlag: 'terminalDetail',
                    row_status: ROW_STATUS.NEW,
                })
            }
            if (this.pageParam.sourceFlag === 'headRegDetail') {
                this.$nav.push('/pages/headquarters-activity/headquarters-register-upsert-page.vue', {
                    data: paramData,
                    activityId: this.pageParam.data.activityId,
                    sourceFlag: 'headRegDetail',
                    row_status: ROW_STATUS.NEW,
                })
            }
        },
        /**
         * 点击一键报备
         * @param data
         */
        clickReport() {
            this.$nav.push('/pages/headquarters-activity/report-share-page.vue', {
                data: this.registerItem,
                sourceFlag: 'headRegDetail'
            })
        },
    }
}
</script>

<style lang="scss">
.headquarters-register-detail-page {
    width: 100%;
    overflow: hidden;

    .lnk-tabs-container .lnk-tabs .lnk-tabs-content {
        justify-content: space-evenly !important;

        & > .lnk-tabs-item {
            &:nth-child(1) {
                width: 30% !important;
            }

            &:nth-child(2) {
                width: 40% !important;
            }

            &:nth-child(3) {
                width: 30% !important;
            }
        }
    }

    .menu-stair {
        width: 100%;
        margin-left: 24px;
        padding-top: 40px;
        @include flex-start-center;

        .line {
            clear: both;

            .line-top {
                width: 8px;
                height: 16px;
                background: #3FE0E2;
            }

            .line-bottom {
                width: 8px;
                height: 16px;
                background: #2F69F8;
            }
        }

        .stair-title {
            width: 60%;
            margin-left: 16px;
            font-family: PingFangSC-Semibold, serif;
            font-size: 32px;
            color: #262626;
            letter-spacing: 1px;
            line-height: 32px;
        }

        .edit {
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #2F69F8;
            letter-spacing: 0;
            line-height: 28px;
            text-align: right;
            width: 58%;
        }
    }
}
</style>
