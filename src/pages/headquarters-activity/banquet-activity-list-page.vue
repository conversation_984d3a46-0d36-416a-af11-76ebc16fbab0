<!--
总部活动-宴席活动列表
<AUTHOR>
@date 2023-06-28
@file banquet-activity-list-page
-->
<template>
    <link-page class="banquet-activity-list-page">
        <lnk-taps :taps="banquetTapOptions" v-model="banquetActStatusActive" @switchTab="switchTab"></lnk-taps>
        <link-auto-list :option="banquetActList" :searchInputBinding="{props:{placeholder: '活动名称/活动编码'}}">
            <link-filter-group slot="filterGroup">
                <!--<link-filter-item label="创建时间(升序)" :param="{sort:{field:'created',desc:false}}"/>-->
                <link-filter-item label="最近更新(升序)" :param="{sort:{field:'lastUpdated',desc:false}}"/>
            </link-filter-group>
            <template slot-scope="{data,index}">
            <link-swipe-action>
                <link-swipe-option slot="option" @tap="endBanquetAct(data,index)">结束</link-swipe-option>
                <item :key="index" :data="data" :arrow="false" class="banquet-activity-list-item"
                      @tap="gotoDetail(data)">
                    <view slot="note">
                        <view class="media-list">
                            <view class="media-top">
                                <!-- 活动编码 -->
                                <view class="num-view" @longPress="copyActCode(data.feedbackCode)">
                                    <view class="num">{{data.feedbackCode}}</view>
                                </view>
                                <!-- 转发标识 -->
                                <view class="tab-info">
                                    <view class="iconBar" style="float: right">
                                        <view style="color: #EC974A;flex: 1">
                                            <view v-if="data.forwardMark === 'Y'">
                                                <link-icon class="icon" icon="icon-xuni"/><text>转发</text>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                                <!-- 活动状态标识 -->
                                <status-button :label="data.status| lov('HEAD_FEEDBACK_STATUS')"></status-button>
                            </view>
                        </view>
                        <!-- 活动名称 -->
                        <view class="content-middle">
                            <view class="name">{{data.feedbackName.length > 20 ? data.feedbackName.substring(0,20) + '...' : data.feedbackName }}</view>
                        </view>
                        <view class="content-middle-line">
                            <view class="data">
                                <view class="title">审批状态</view>
                                <view class="val">{{data.approveStatus| lov('HEAD_FEEDBACK_APRO_STATUS')}}</view>
                            </view>
                            <view class="sum">
                                <view class="title">用酒要求</view>
                                <view class="val">{{data.alcoholRequest}}</view>
                            </view>
                        </view>
                        <view class="content-middle-line">
                            <view class="data">
                                <view class="title">开始时间</view>
                                <view class="val">{{data.startTime|date('YYYY-MM-DD')}}</view>
                            </view>
                            <view class="sum">
                                <view class="title">结束时间</view>
                                <view class="val">{{data.endTime|date('YYYY-MM-DD')}}</view>
                            </view>
                        </view>
                        <view class="content-middle-line">
                            <view class="data">
                                <view class="title">提报人</view>
                                <view class="val">{{data.creator}}</view>
                            </view>
                        </view>
                        <view class="content-middle-line">
                            <view class="sum">
                                <view class="title1">实时开瓶数量</view>
                                <view class="val">{{data.consumerCrackNum}}</view>
                            </view>
                        </view>
                        <view class="content-middle-line">
                            <view class="data2">
                                <view class="title">宴席地点</view>
                                <view class="val">{{data.policyFullAddress}}</view>
                            </view>
                        </view>
                        <view class="content-middle-line">
                            <view class="data2">
                                <view class="title">终端名称</view>
                                <view class="val">{{data.terminalName}}</view>
                            </view>
                        </view>
                    </view>
                </item>
              </link-swipe-action>
            </template>
        </link-auto-list>
        <link-fab-button icon="icon-plus" label="新建登记" @tap="clickAdd" v-if="!pageParam || pageParam.source !== 'approve'"/>
    </link-page>
</template>

<script>
import StatusButton from "../lzlj/components/status-button.vue";
import LnkTaps from "../core/lnk-taps/lnk-taps";

definePageConfig({
    navigationBarTitleText: '宴席单列表'
});

export default {
    name: 'banquet-activity-list-page',
    components: {StatusButton, LnkTaps},
    data() {
        // 顶部tab栏选项全部，草稿，审批中，待反馈，已实发
        const banquetTapOptions = [
            {name: '全部', seq: '1', val: 'All'},
            {name: '草稿', seq: '2', val: 'Draft'},
            {name: '审批中', seq: '3', val: 'Approving'},
            {name: '待反馈', seq: '4', val: 'Feedbacking'},
            {name: '已实发', seq: '5', val: 'Approved'},
        ];
        //顶部筛选栏参数filtersParamsList
        //230918核对宴席单列表筛选条件
        const filtersParamsList = {
            // All: [{id: 'status', property: 'status', operator: 'in', value: '[New, Processing, End, Closed]'}],
            All: [],
            Draft: [
                {id: 'approveStatus', property: 'approveStatus', operator: '=', value: 'New'}
                // {id: 'status', property: 'status', operator: 'in', value: '[New, Processing, End, Closed]'}
            ],
            Approving: [
                {id: 'approveStatus', property: 'approveStatus', operator: '=', value: 'Approving'}
                // {id: 'status', property: 'status', operator: 'in', value: '[New, Processing, End, Closed]'}
            ],
            Feedbacking: [
                {id: 'approveStatus', property: 'approveStatus', operator: '=', value: 'Approved'},
                {id: 'status', property: 'status', operator: 'in', value: '[New, Processing]'}
            ],
            Approved: [
                {id: 'approveStatus', property: 'approveStatus', operator: '=', value: 'FeedbackApproved'},
                // {id: 'status', property: 'status', operator: 'in', value: '[New, Processing, End, Closed]'}
            ],
        };

        const banquetActList = new this.AutoList(this, {
            module: 'action/link/headquarterFeedback',
            url: {
                queryByExamplePage: 'export/link/headquarterFeedback/queryByExamplePage',
            },
            param: {
                activityType: 'Banquet',
                attr6: 'notBanquet'
            },
            searchFields: ['feedbackName', 'status', 'feedbackCode','activityType'],
            hooks: {
                beforeLoad(option) {
                    if(this.pageParam.activityId){
                        option.param.filtersRaw.push({id: 'activityId', property: 'activityId', value: this.pageParam.activityId});
                    }
                }
            },
            sortField: 'created',
            sortDesc: 'desc',
            sortOptions: null,
        });

        return {
            banquetTapOptions,   // 顶部tab栏选项
            filtersParamsList,   //顶部筛选栏参数
            banquetActStatusActive: {},  // 顶部tab栏选项选中项
            banquetActList,  // 宴席活动列表
            endBanquetFlag: false,  // 是否显示结束宴席按钮
            editFlag: false, // 是否编辑
        }
    },
    async mounted() {
        this.banquetActStatusActive = this.banquetTapOptions[0];
        this.$bus.$on('refreshBanquetList', async () => {
            this.banquetActList.methods.reload()
        });
    },
    methods: {
        /**
         * tab页切换
         *  <AUTHOR>
         *  @date 2023-06-28
         *  全部：活动状态为未开始、进行中、已结束、提前结束
         *  进行中：活动状态为进行中
         *  已结束：活动状态为已结束
         */
        switchTab(item) {
            this.banquetActStatusActive = item;
            this.banquetActList.option.param.filtersRaw = this.filtersParamsList[item.val]
            this.banquetActList.methods.reload();
        },
        /**
         * 复制活动编码
         *  <AUTHOR>
         *  @date 2023-06-28
         */
        copyActCode(text) {
            wx.setClipboardData({data: text});
        },
        /**
         * 跳转宴席详情
         * <AUTHOR>
         * @date 2023-07-04
         */
        async gotoDetail(data) {
            if (data.approveStatus === 'New') {
                this.editFlag = true;
                this.$nav.push('/pages/headquarters-activity/banquet-activity-apply-page.vue', {
                    data: data,
                    editFlag: this.editFlag
                })
            } else if (data.approveStatus === 'Refuse') {
                this.editFlag = true;
                let existApproved = await this.getApprovedRecord(data.id); // 是否存在“审批通过”的审批记录
                let pageParam = {
                    data: data
                };
                if (existApproved) {
                    pageParam.editBasicFlag = true; // 若存在审批通过记录则编辑时只能编辑时间
                } else {
                    pageParam.editFlag = true;
                }
                this.$nav.push('/pages/headquarters-activity/banquet-activity-apply-page.vue', pageParam)
            } else {
                // 获取模版子类型，将返回的登记要求字符串转化为数组
                if (data.registerRequest) {
                    data.regReqList = JSON.parse(data.registerRequest.replace(/'/g, '"'));
                }
                this.$nav.push('/pages/headquarters-activity/banquet-activity-detail-page', {
                    data: {...data}
                });
            }
        },
        /**
         * 跳转宴席申请
         * <AUTHOR>
         * @date 2023-07-04
         */
        clickAdd() {
            this.$nav.push('/pages/headquarters-activity/banquet-activity-apply-page',{})
        },
        /**
         * 结束宴席活动
         * 活动审批状态等于审批通过或者等于执行反馈审批驳回
         * 且
         * 活动状态等于进行中或已提交
         * 的活动才可以结束
         * <AUTHOR>
         * @date 2023-07-07
         */
        async endBanquetAct(data,index) {
            if(['Approved', 'FeedbackRefuse'].includes(data.approveStatus) && ['Submitted', 'Processing'].includes(data.status)) {
                this.endBanquetFlag = true;
            } else {
                this.endBanquetFlag = false;
            }
            if(this.endBanquetFlag) {
                this.$taro.showModal({
                    title: '提示',
                    content: '结束活动不可逆转，是否确定结束？',
                    success: async (res) => {
                        if (res.confirm) {
                            await this.endCurrentBanquet(data, index);
                        }
                    }
                });
            } else {
                this.$showError('当前活动不可结束！');
            }
        },
        async endCurrentBanquet(data) {
            try{
                const reg = await this.$http.post('action/link/headquarterFeedback/beforeClosed', {
                    ...data,
                })
                if(reg.success) {
                    this.$set(data, 'status', 'Closed');
                    this.$message.success('结束活动成功！');
                    this.banquetActList.methods.reload();// 刷新登记列表
                } else {
                    this.$showError('结束活动失败，请稍后重试！');
                }
            } catch (e) {
                this.$showError('结束活动失败，请稍后重试！');
            }
        },
        /**
         * 获取宴席单是否有审批通过的记录
         * @param id 宴席编码
         * @des 用于判断审批驳回并且存在审批通过记录的单子只能编辑时间字段且隐藏保存按钮
         */
        async getApprovedRecord (id) {
            const url = 'action/link/flowRecord/v2/queryByExamplePage';
            const param = {
                filtersRaw: [
                    { id: 'flowObjId', property: 'flowObjId', value: id },
                    { id: 'approvalOpera', property: 'approvalOpera', value: '审批通过', operator: '='},
                ]
            };
            try {
                const res = await this.$http.post(url, param);
                if (res.success) {
                    return res.rows.length
                } else {
                    this.$showError('宴席审批记录查询失败，请稍后重试！');
                }
            } catch (e) {
                this.$showError('宴席审批记录查询失败，请稍后重试！');
            }
        }

    }
}
</script>

<style lang="scss">
.banquet-activity-list-page {
    background-color: #F2F2F2;
    font-family: PingFangSC-Regular;
    .link-input{
        width: 100%;
    }
    /*deep*/
    .lnk-tabs-container {
        height: 92px;
    }
    .tab-info {
        width: 30%;
        line-height: normal;
        .iconBar{
            display:flex;
        }
        .ai-error {
            color: #EA3232;
        }
    }
    .banquet-activity-list-item {
        background: #FFFFFF;
        width: 95%;
        margin: 24px auto auto auto;
        border-radius: 16px;

        .media-list {
            //@include media-list;

            .media-top {
                width: 100%;
                @include flex-start-center;
                @include space-between;
                height: 80px;
                line-height: 80px;

                .left-content {
                    font-family: PingFangSC-Semibold;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 32px;
                    padding-top: 20px;

                }

                .right-content {
                    font-family: PingFangSC-Semibold;
                    font-size: 32px;
                    color: #FF5A5A;
                    letter-spacing: 0;
                    text-align: right;
                    line-height: 32px;
                    padding-top: 20px;
                }

                .num-view {
                    background: #A6B4C7;
                    border-radius: 8px;
                    line-height: 50px;

                    .num {
                        font-size: 28px;
                        color: #FFFFFF;
                        letter-spacing: 0;
                        line-height: 40px;
                        padding: 2px 8px;
                    }
                }

                .status-view {
                    width: 120px;
                    transform: skewX(-10deg);
                    border-radius: 4px;
                    background: #2F69F8;
                    box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                    height: 36px;

                    .status {
                        font-size: 20px;
                        color: #FFFFFF;
                        letter-spacing: 2px;
                        text-align: center;
                        line-height: 36px;
                    }
                }
            }
        }

        .content-middle {
            width: 100%;
            @include flex-start-center;
            @include space-between;
            height: 80px;
            line-height: 80px;

            .content {
                font-family: PingFangSC-Regular;
                font-size: 26px;
                color: #000000;
                letter-spacing: 0;
            }

            .name {
                font-family: PingFangSC-Semibold;
                font-size: 32px;
                color: #262626;
                letter-spacing: 0;
                line-height: 32px;
            }
        }

        .content-middle-line {
            width: 100%;

            .data {
                width: 55%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    width: 40%;
                    float: left;

                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                }

                .Submitted, .Feedback{
                    color: #2F69F8;
                }

                .Approve, .FeedbackApro{
                    color: #2EB3C2;
                }

                .Refused, .Refeedback{
                    color: #FF5A5A;
                }

            }
            .data2 {
                width: 100%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    width: 22%;
                    float: left;

                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                }

                .Submitted, .Feedback{
                    color: #2F69F8;
                }

                .Approve, .FeedbackApro{
                    color: #2EB3C2;
                }

                .Refused, .Refeedback{
                    color: #FF5A5A;
                }

            }
            .sum {
                width: 45%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    float: left;
                    width: 40%;
                }

                .title1 {
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    float: left;
                    width: 60%;
                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                    white-space:nowrap;
                }
            }

        }
    }
}
</style>
