<!--
总部活动-宴席活动申请
<AUTHOR>
@date 2023-06-28
@file banquet-activity-apply-page
-->
<template>
    <link-page class="banquet-activity-apply-page">
        <link-form :value="formData" :rules="rules" ref="upsertBanquet">
            <block v-for="(item, index) in templateList" :key="index">
                <link-form-item v-if="!['noPictureReason'].includes(item.values.field)"
                                :label="item.base.label"
                                :field="item.values.field"
                                :required="item.base.require"
                                :disabled="!['startTime', 'endTime', 'mealTime', 'mealSession', 'takePicturesFlag'].includes(item.values.field)
                                && (item.values.disabled === 'true' || editBasicFlag) || (isRefused && item.values.field === 'activityName')">
                    <new-activity-basic-jsx-component :option="item"
                                                      :formData="formData"
                                                       v-if="!['banquet-consumer', 'beneficiary-terminal', 'policy-address',
                                                      'league-champion','league-member', 'subsidiary-dealer', 'actual-shipper'].includes(item.ctrlCode)
                                                      && !['feedbackCode', 'feedbackName', 'activityName', 'banquetProdCode',
                                                      'endTime', 'startTime'].includes(item.values.field)"/>
                    <!-- 活动编码 -->
                    <link-input v-if="item.values.field === 'feedbackCode'" :value="formData[item.values.field] || '宴席单提交后自动生成'"/>
                    <!-- 活动名称 -->
                    <link-input v-if="item.values.field === 'feedbackName'" :value="formData[item.values.field] || '宴席单提交后自动生成'"/>
                    <!-- 宴席政策 -->
                    <link-input v-if="item.values.field === 'activityName'"
                                v-model="formData[item.values.field]"
                                @tap="!editBasicFlag && !isRefused && afterSelectActName()"
                                suffixIcon="mp-arrow-right" inputReadonly/>
                    <!-- 宴席主家： 选择消费者 -->
                    <link-input v-if="item.ctrlCode === 'banquet-consumer'"
                                v-model="formData[item.values.field]"
                                @tap="!editBasicFlag && pickMasterName(item.base.label, item.values.disabled === 'true')"
                                suffixIcon="mp-arrow-right" inputReadonly/>
                    <!-- 场地地址2 -->
                    <block v-if="item.ctrlCode === 'other-address'">
                        <link-input v-if="editBasicFlag"
                                    class="address-info"
                                    v-model="formData[item.values.field]"
                                    :nativeProps="{maxlength: 300}"/>
                        <view v-else class="address-info" @tap="getLocations">
                            <view :class="addressData2 === '获取定位' ? 'address-tips' : 'address-data'">{{addressData2}}</view>
                            <link-icon style="color: #2F69F8" icon="icon-location" class="link-location"/>
                        </view>
                    </block>
                    <!-- 受益终端 -->
                    <link-input v-if="item.ctrlCode === 'beneficiary-terminal'"
                                v-model="formData[item.values.field]"
                                @tap="!editBasicFlag && pickBeneficiaryName(item.base.label, item.values.disabled === 'true')"
                                suffixIcon="mp-arrow-right" inputReadonly/>
                    <!-- 活动开始时间 -->
                    <link-date v-if="item.values.field === 'startTime'"
                               v-model="formData[item.values.field]"
                               @change="startTimeChange"
                               :view="item.values.view"
                               :value-format="item.values.valueFormat"
                               :display-format="item.values.displayFormat">
                    </link-date>
                    <!-- 活动结束时间 -->
                    <link-date v-if="item.values.field === 'endTime'"
                               v-model="formData[item.values.field]"
                               :disabled="banquetPolicy.autoEnd === 'Y'"
                               :view="item.values.view"
                               :value-format="item.values.valueFormat"
                               :display-format="item.values.displayFormat">
                    </link-date>
                    <!-- 联盟盟主 -->
                    <link-input v-if="item.ctrlCode === 'league-champion'"
                                v-model="formData[item.values.field]"
                                @tap="!editBasicFlag && pickLeagueChampion(item.base.label, item.values.disabled === 'true')"
                                suffixIcon="mp-arrow-right" inputReadonly/>
                    <!-- 联盟成员 -->
                    <link-input v-if="item.ctrlCode === 'league-member'"
                                v-model="formData[item.values.field]"
                                @tap="!editBasicFlag && pickLeagueMember(item.base.label, item.values.disabled === 'true')"
                                suffixIcon="mp-arrow-right" inputReadonly/>
                    <!-- 子公司/经销商 -->
                    <link-input v-if="item.ctrlCode === 'subsidiary-dealer'"
                                v-model="formData[item.values.field]"
                                @tap="!editBasicFlag && pickSubsidiaryDealer(item.base.label, item.values.disabled === 'true')"
                                suffixIcon="mp-arrow-right" inputReadonly/>
                    <!-- 详细地址 -->
                    <block v-if="item.ctrlCode === 'policy-address'">
                        <link-input v-if="editBasicFlag"
                                    class="address-info"
                                    v-model="formData[item.values.field]"
                                    :nativeProps="{maxlength: 300}"/>
                        <view v-else class="address-info" @tap="getLocation">
                            <view :class="addressData === '获取定位' ? 'address-tips' : 'address-data'">{{addressData}}</view>
                            <link-icon style="color: #2F69F8" icon="icon-location" class="link-location"/>
                        </view>
                    </block>
                    <!-- 计划用酒产品 -->
                    <link-object v-if="item.values.field === 'banquetProdCode'"
                                 pageTitle="选择计划用酒产品"
                                 :option="planProdOption"
                                 :row="formData"
                                 :map="{
                                     banquetProdId: 'productId',
                                     banquetProdCode: 'productCode'
                                 }"
                                 :value="formData[item.values.field]"
                                 :beforeSelect="beforePlanProd"
                                 :afterSelect="afterPlanProd">
                        <template v-slot="{data}">
                            <item :title="data.productCode" :key="data.productCode" :data="data" :content="data.productName"/>
                        </template>
                    </link-object>
                    <!-- 实际出货方 -->
                    <link-input v-if="item.ctrlCode === 'actual-shipper'"
                                v-model="formData[item.values.field]"
                                @tap="!editBasicFlag && pickActualShipper()"
                                suffixIcon="mp-arrow-right" inputReadonly/>
                </link-form-item>
                <view class="no-reason" v-if="item.values.field === 'noPictureReason' && formData.takePicturesFlag === 'Y'">
                    <link-form-item required label="免拍原因" field="noPictureReason">
                    </link-form-item>
                    <link-textarea
                        v-model="formData.noPictureReason"
                        placeholder="如果申请在反馈时免拍照，请填写免拍原因，提交之后会进入审批流程"
                        :nativeProps="{maxlength:100}">
                    </link-textarea>
                </view>
            </block>
            <link-form-item label="是否维护宴席合伙人" field="isBanquetPartner" v-if="formData.isBanquetPartner">
                <link-lov type="IF_BANQUEPARTNER" v-model="formData.isBanquetPartner" disabled/>
            </link-form-item>
            <link-form-item label="宴席合伙人" field="banquetPartner" required v-if="formData.isBanquetPartner == 'true'">
                <link-input v-model="formData.banquetPartner"
                            @tap="!editBasicFlag && selectPartner()"
                            suffixIcon="mp-arrow-right" inputReadonly/>
            </link-form-item>
            <link-form-item label="宴席合伙人手机号" field="partnerMobilePhone"  v-if="formData.isBanquetPartner == 'true'">
                <link-input v-model="formData.partnerMobilePhone" disabled/>
            </link-form-item>
        </link-form>
        <!-- 政策关联的产品范围 -->
        <view v-if="formData.activityId">
            <line-title title="政策关联的产品范围" class="head-title"></line-title>
            <link-auto-list :option="planProdOption">
                <template slot-scope="{data,index}">
                    <item :key="index" :data="data" :arrow="false" class="banquet-plan-prod-item">
                        <view slot="note">
                            <view class="content-middle-line">
                                <view class="data">
                                    <view class="title">产品编码</view>
                                    <view class="val">{{ data.productCode}}</view>
                                </view>
                            </view>
                            <view class="content-middle-line">
                                <view class="data">
                                    <view class="title">产品名称</view>
                                    <view class="val">{{ data.productName ? data.productName.substr(0,20) + '...' : ''}}</view>
                                </view>
                            </view>
                        </view>
                    </item>
                </template>
            </link-auto-list>
        </view>
        <link-sticky>
            <link-button v-if="!isShowSave" block @tap="save">保存</link-button>
            <link-button block @tap="commit">提交</link-button>
        </link-sticky>
    </link-page>
</template>

<script lang="jsx">
import {ROW_STATUS} from "../../utils/constant";
import {LovService} from "link-taro-component";
import Taro from "@tarojs/taro";
import LineTitle from "../lzlj/components/line-title.vue";
import { reverseTMapGeocoder } from "../../utils/locations-tencent";
import NewActivityBasicJsxComponent from '../lj-market-activity/market-activity/new-activity-basic-jsx-component';

export default {
    name: 'banquet-activity-apply-page',
    components: {
        NewActivityBasicJsxComponent,
        LineTitle
    },
    data() {
        const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
        const companyId = userInfo.coreOrganizationTile.l3Id;
        const option = new this.FormOption(this, {
            operator: 'NEW'
        });
        // 宴席合伙人
        const clueOption = new this.AutoList(this,{
            module: this.$env.appURL + '/action/link/consumer',
            url: {
                queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/consumerListSend'
            },
            sortOptions: null,
            exactSearchFields: [{
                field: 'acctName',
                showValue: '合伙人姓名', // 展示名,用于显示的字段=
                exactSearch: true,
                searchOnChange: true,
                clearOnChange: true
            }, {
                field: 'mobilePhone1',
                showValue: '合伙人手机号', // 展示名,用于显示的字段=
                exactSearch: true,
                searchOnChange: true,
                clearOnChange: true
            }],
            filterOption: [
                {label: '合伙人姓名', field: 'acctName', type: 'text'},
                {label: '合伙人手机号', field: 'mobilePhone1', type: 'text'}
            ],
            searchFields: ['acctName', 'mobilePhone1'],
            param: {
                rows: 25,
                // filterActivityId: this.formData.activityId,
                attr06: 'Y',
                filtersRaw: [
                    {id: 'companyId', property: 'companyId', value: companyId, operator: '='},
                    {id: 'consumerType', property: 'consumerType', value: 'ChannelConsumer', operator: '='},
                    {id: 'accntChannel', property: 'accntChannel', value: 'MarketingPlatform', operator: '='},
                    {id: 'empFlag', property: 'empFlag', value: 'N', operator: '='},
                    {id: 'followFlag', property: 'followFlag', value: 'Y', operator: '='}
                ],
                oauth: 'MY_POSTN_ONLY'
            },
            hooks: {
                beforeLoad (option) {
                    for (let i = 0; i < option.param.filtersRaw.length; i++) {
                        if (option.param.filtersRaw[i].property === 'acctName') {
                            option.param.filtersRaw[i].operator = 'like';
                        }
                    }
                }
            },
            renderFunc: (h, {data, index}) => {
                return (
                    <item key={index} data={data} arrow={false}>
                        <view style="width: 100%;padding: 5px 0 0 14px" slot="note">
                            <view style="font-family: PingFangSC-Semibold;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 16px;margin-bottom: 5px;">
                                {data.acctName}
                            </view>
                            <view style="font-family: PingFangSC-Regular;font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;line-height: 16px;margin-bottom: 5px">
                                {data.mobilePhone1}
                            </view>
                        </view>
                    </item>
                )
            }
        });
        // 宴席政策
        const banquetPolicyOption = new this.AutoList(this,{
            module: '/action/link/headquarterActivity',
            url: {
                queryByExamplePage: '/action/link/headquarterActivity/queryByExamplePage'
            },
            param: () => {
                return {
                    attr1 : 'Org',
                    // 宴席活动反选组织
                    attr7 : 'exclude',
                    filtersRaw: [
                        {id: 'activityType', property: 'activityType', value: 'Banquet', operator: '='},
                        {id: 'activityStatus', property: 'activityStatus', operator: '=', value: 'Processing'}
                    ],
                    attr3:"orderByASumActNum" //1107 edit by 邓佳柳 按照宴席关联单据量排序
                }
            },
            hooks: {
                beforeLoad({ param }) {
                    //1107 edit by 邓佳柳 按照宴席关联单据量排序
                    delete param.order
                    delete param.sort
                }
            },
            searchFields: ['activityName'],
            renderFunc: (h, {data, index}) => {
                return (
                    <item key={index} data={data} arrow={false}>
                        <view style="width: 100%;padding: 5px 0 0 14px" slot="note">
                            <view style="font-family: PingFangSC-Semibold;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 16px;margin-bottom: 5px;">
                                {data.activityNum}
                            </view>
                            <view style="font-family: PingFangSC-Regular;font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;line-height: 16px;margin-bottom: 5px">
                                {data.activityName}
                            </view>
                            <view style="font-family: PingFangSC-Regular;font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;line-height: 16px;margin-bottom: 5px">
                                政策有效期：{data.startTime.substr(0,10)} 至 {data.endTime.substr(0,10)}
                            </view>
                            <view style="font-family: PingFangSC-Regular;font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;line-height: 16px;margin-bottom: 5px">
                                用酒要求数量：{data.alcoholNum} {LovService.filter(data.alcoholUnit, 'PROD_UNIT')}
                            </view>
                        </view>
                    </item>
                )
            }
        });
        //宴席主家
        const masterOption = new this.AutoList(this, {
            module: this.$env.appURL + '/action/link/consumer',
            url: {
                queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/consumerListSend'
            },
            param: {
                rows: 25,
                filtersRaw: [
                    {id: 'companyId', property: 'companyId', value: userInfo.coreOrganizationTile.l3Id},
                    {id: 'consumerType', property: 'consumerType', value: 'ChannelConsumer'},
                    {id: 'accntChannel', property: 'accntChannel', value: 'MarketingPlatform'},
                    {id: 'empFlag', property: 'empFlag', value: 'N', operator: '='},
                    {id: 'followFlag', property: 'followFlag', value: 'Y', operator: '='}
                ],
                oauth: 'MY_POSTN_ONLY',
            },
            sortOptions: null,
            exactSearchFields: [{
                field: 'acctName',
                showValue: '消费者姓名', // 展示名,用于显示的字段=
                exactSearch: true,
                searchOnChange: true,
                clearOnChange: true
            }, {
                field: 'mobilePhone1',
                showValue: '消费者手机号', // 展示名,用于显示的字段=
                exactSearch: true,
                searchOnChange: true,
                clearOnChange: true
            }],
            filterOption: [
                {label: '消费者姓名', field: 'acctName', type: 'text'},
                {label: '消费者手机号', field: 'mobilePhone1', type: 'text'},
                {label: '客户性别', field: 'gender', type: 'lov', lov: 'GENDER'}
            ],
            searchFields: ['acctName', 'mobilePhone1'],
            hooks: {
                beforeLoad (option) {
                    for (let i = 0; i < option.param.filtersRaw.length; i++) {
                        if (option.param.filtersRaw[i].property === 'acctName') {
                            option.param.filtersRaw[i].operator = 'like';
                        }
                    }
                }
            },
            renderFunc: (h, {data, index}) => {
                return (
                    <item key={index} data={data} arrow={false} style="margin: 12px;border-radius: 8px; position: relative;overflow: hidden;padding: 20px 14px 14px 14px;">
                        <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                        <view style="width: 100%;">
                            <view style="width: 100%;font-size: 16px;color: #212223;line-height: 24px;font-weight: 600;margin-bottom: 8px;">{data.acctName}</view>
                            <view style={"background: #2F69F8;color: #fff;transform: skew(30deg, 0);display: flex;border-bottom-left-radius: 7px;position: absolute;right: -5px;top: 0;"}>
                                <text style={"font-size: 12px;transform: skew(-30deg, 0);padding: 4px 16px;"}>{data.fstName}跟进</text>
                            </view>

                            <view style="display: flex;margin-bottom: 8px;">
                                <view style="padding:0 5px;font-size: 11px;font-weight: 400;color: #3F66EF;line-height: 18px;background: #F0F5FF;border-radius: 2px;text-align: center; margin-right: 8px;">{LovService.filter(data.subAcctType, 'ACCT_SUB_TYPE')}</view>
                                <view style="padding:0 5px;font-size: 11px;font-weight: 400;color: #3F66EF;line-height: 18px;background: #F0F5FF;border-radius: 2px;text-align: center; margin-right: 8px;">{ LovService.filter(data.loyaltyLevel, 'ACCT_MEMBER_LEVEL') }</view>
                                {data.impFlag === 'Y' ? <view style="padding:0 5px;font-size: 11px;font-weight: 400;color: #FF461E;line-height: 18px;background: #FFF1EB;border-radius: 2px;text-align: center; margin-right: 8px;">重点客户</view> : ''}
                            </view>
                            <view style="width: 100%;font-size: 14px;display: flex;color: #317DF7;margin-bottom: 4px;line-height: 22px;">
                                <view style="color: #999999;width: 66px;">联系方式</view>
                                <view> {data.mobilePhone1}</view>
                            </view>
                            <view style="width: 100%;font-size: 14px;display: flex;color: #333333;margin-bottom: 4px;line-height: 22px;">
                                <view style="color: #999999; width:66px;">单位</view>
                                <view> {data.company}</view>
                            </view>
                            <view style="width: 100%;font-size: 14px;display: flex;color: #333333;margin-bottom: 4px;line-height: 22px;">
                                <view style="color: #999999;width: 66px;">职务</view>
                                <view> {data.position}</view>
                            </view>
                            <view style="width: 100%;font-size: 14px;display: flex;color: #333333;line-height: 22px;">
                                <view style="color: #999999;width: 66px;">所属客户</view>
                                <view> {data.belongToStore}</view>
                            </view>
                        </view>
                    </item>
                )
            },
            slots: {
                other: () => <link-fab-button onTap={this.addConsumer}></link-fab-button>,
                filterGroup: () => (
                    <link-filter-group>
                        <link-filter-item label="创建时间(升序)" param={{sort: {field: 'created', desc: false}}}/>
                        <link-filter-item label="最近更新(升序)" param={{sort: {field: 'lastUpdated', desc: false}}}/>
                        <view onTap={this.chooseStoreList}
                              style={
                                  this.isChosen ? 'padding: 8rpx 16rpx;'+
                                      'margin-right: 8rpx; white-space: nowrap;' +
                                      'display: inline-block; background: #EDF3FF;' +
                                      'border-radius: 4rpx;'
                                  : 'padding: 8rpx 16rpx; border-radius: 4rpx;'+
                                      'color: #2F69F8;margin-right: 8rpx;' +
                                      'white-space: nowrap;display: inline-block;' +
                                      'background-color: #f2f2f2;color: #333333;'
                              }
                        >所属客户</view>
                    </link-filter-group>
                )
            }
        });
        // 消费者
        const customerOption = new this.AutoList(this, {
            module: this.$env.appURL + '/action/link/accnt',
            url: {
                queryByExamplePage: this.$env.appURL + '/link/interCustTerminal/queryAccntPage'
            },
            param: {
                postnId: '',
                oauth: 'ALL',
            },
            searchFields: ['acctName'],
            renderFunc: (h, {data, index}) => {
                return (
                    <item key={index} data={data} className="select-box" arrow={false}>
                        <link-checkbox val={data.accntId} toggleOnClickItem slot="thumb"></link-checkbox>
                        <view slot="title" style="display: flex;">{data.acctName} <view style="margin-left: 1em;background: #EDF3FF;color: #2F69F8;border: 1px solid #EDF3FF;font-size:12px;padding: 3px;border-radius: 3px;">{LovService.filter(data.acctType, 'ACCT_TYPE')}</view></view>
                        <view slot="note">{data.province}{data.city}{data.district}{data.address}</view>
                    </item>)
            },
            hooks: {
                beforeLoad (option) {
                    delete option.param.order;
                    delete option.param.sort;
                    option.param.postnId = this.userInfo.postnId;
                }
            }
        });
        // 受益终端（同市场活动受益客户）、联盟成员、实际出货方
        const beneficiaryOption = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'export/link/accnt/queryAccntByCityOrgPage'
            },
            param: () => {
                // 是否是受益客户
                const isBeneficiary = !this.isLeagueMember && !this.isActualShipper;
                const isYouXuan = ['1216'].includes(userInfo.coreOrganizationTile.brandCompanyCode) // 优选公司
                return {
                    // 终端/分销商：已认证、主户头（经销商没有已认证）
                    multiAcctMainFlag: this.multiAcctMainCode.indexOf(userInfo.coreOrganizationTile.l3Code) > -1 ? 'Y' : undefined,
                    acctStage: 'ykf',
                    companyId: userInfo.coreOrganizationTile.l3Id,
                    // 职位的销售城市
                    attr2: userInfo.coreOrganizationTile.l6Id,
                    // 受益客户： 经销商、终端、分销商   联盟成员、实际出货方：终端、分销商
                    attr3: !isBeneficiary ? 'Terminal,Distributor' : isYouXuan ? 'Dealer' : 'Dealer,Terminal,Distributor',
                    // 实际出货方: memberAll    联盟成员: member
                    attr7: this.isActualShipper ? 'memberAll' : this.isLeagueMember ? 'member' : undefined,
                    // 联盟盟主id
                    dealerId: isBeneficiary ? undefined : this.formData.dealerId,
                    // 宴席政策id
                    activityId: this.formData.activityId
                }
            },
            sortOptions: null,
            searchFields: ['billTitle', 'acctName', 'acctCode'],
            hooks: {
                beforeLoad(option) {
                    if(!this.changeLm){
                        delete option.param.activityId;
                    }
                    delete option.param.order;
                    delete option.param.sort;
                },
            },
            renderFunc: (h, {data, index}) => {
                return (
                    <item arrow={false} key={index} data={data}>
                        {this.isActualShipper ?
                            <view style="display:flex;align-items:center;" slot="note">
                                <view style={"border: 1px solid #2F69F8;border-radius: 4px;font-size: 13px;" +
                                "padding: 0 9px;line-height: 20px;height: 20px;color: #2F69F8;margin-right:10px;"}>
                                    {LovService.filter(data.acctType, 'ACCT_TYPE')}
                                </view>
                                <view style="font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px">
                                    {data.acctName}
                                </view>
                                <view style="font-size: 14px;flex:1;text-align:right;">{data.acctCode}</view>
                            </view> :
                            <view style="padding: 5px 0 0 14px" slot="note">
                                <view style="margin-bottom: 5px">
                                    <view style="font-size: 16px;color: #262626;letter-spacing: 0;line-height: 16px;float:left">
                                        客户名称 : {data.acctName}
                                    </view>
                                    <view style="font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;line-height: 16px;padding-left: 5px;float: left;">
                                        {data.mobilePhone}
                                    </view>
                                </view>
                                <view style="color: gray;clear:both;padding-top:10px">
                                    <view style="font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px">
                                        客户编码：{data.acctCode}
                                    </view>
                                </view>
                                <view style="color: gray;clear:both;padding-top:10px">
                                    <view style="font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px">
                                        {LovService.filter(data.acctType, 'ACCT_TYPE')} {LovService.filter(data.channelManageMode, 'CHANNEL_MANAGE_MODE')} {LovService.filter(data.acctStage, 'TERMINAL_STATUS')}
                                    </view>
                                </view>
                                <view style="color: gray;clear:both;padding-top:10px">
                                    <view style="font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px">
                                        营业执照名称 : {data.billTitle}
                                    </view>
                                </view>
                                <view style="color: gray;clear:both;padding-top:10px">
                                    <view style="font-size: 14px;color: #262626;letter-spacing: 0;line-height: 18px">
                                        {data.acctType === 'Terminal' ? data.addrDetailAddr : data.billDetailAddr}
                                    </view>
                                </view>
                            </view>
                        }
                    </item>
                )
            }
        });
        //子公司／经销商、联盟盟主
        const actExecutivesOption = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/accnt/querySunCompByCityOrgPage'
            },
            param: () => {
                return {
                    filtersRaw: [
                        {id: 'acctStatus', property: 'acctStatus', value: 'Y'}
                    ],
                    companyId: userInfo.coreOrganizationTile.l3Id,
                    // 职位的销售城市
                    attr2: userInfo.coreOrganizationTile.l6Id,
                    // 查询经销商数据
                    attr3: 'Dealer',
                    // 联盟盟主 memberHead
                    attr7 : this.isLeagueChampion ? 'memberHead' : undefined
                }
            },
            sortOptions: null,
            searchFields: ['billTitle', 'acctName'],
            hooks: {
                beforeLoad(option) {
                    delete option.param.order;
                    delete option.param.sort;
                },
            },
            renderFunc: (h, {data, index}) => {
                return (
                    <item arrow={false} key={index} data={data}>
                        <view style="width: 100%;padding: 5px 0 0 14px" slot='note'>
                            <view style="margin-bottom: 5px">
                                <view style="font-family: PingFangSC-Semibold;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 16px;float:left">
                                    {data.billTitle}
                                </view>
                                <view style="font-family: PingFangSC-Regular;font-size: 14px;color: #8C8C8C;letter-spacing: 0;line-height: 14px;line-height: 16px;padding-left: 5px;float: left;">
                                    {data.mobilePhone}
                                </view>
                            </view>
                            <view style="color: gray;clear:both;padding-top:10px">
                                <view style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px">
                                    {LovService.filter(data.acctType, 'ACCT_TYPE')}-{LovService.filter(data.channelManageMode, 'CHANNEL_MANAGE_MODE')}
                                </view>
                            </view>
                            <view style="color: gray;clear:both;padding-top:10px">
                                <view style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px">
                                    {data.addrDetailAddr}
                                </view>
                            </view>
                        </view>
                    </item>
                )
            }
        });
        // 计划用酒产品
        const planProdOption = new this.AutoList(this, {
            module: 'action/link/activityProduct',
            url: {
                queryByExamplePage: 'action/link/activityProduct/queryByExamplePage'
            },
            param: () => {
                return {
                    filtersRaw: [
                        {id: 'activityId', property: 'activityId', value: this.formData.activityId, operator: '='},
                    ],
                }
            }
        });
        const rules = {
            banquetConsumerTel: this.Validator.phone(),
        }
        return {
            clueOption,
            fromBack: false,//是否下级页面返回
            addressData: '获取定位',
            choseAddress: false, // 是否获取场地地址2
            formData: {
                takePicturesFlag: 'N' // 免拍申请默认为否
            },
            rules,  // 校验规则
            banquetPolicyOption, // 宴席政策
            masterOption, // 宴席主家
            customerOption, // 消费者
            planProdOption,     // 计划用酒产品
            beneficiaryOption, // 受益终端、联盟成员、实际出货方
            actExecutivesOption, // 子公司/经销商、联盟盟主
            userInfo,//用户信息
            option,
            multiAcctMainCode: '', // 宴席流程中只需要主户头的专营公司编码
            templateList: [], // 模板配置
            banquetPolicy: {}, // 宴席政策
            isLeagueChampion: false, // false: 子公司/经销商  true: 联盟盟主
            isLeagueMember: false, // true: 联盟成员
            isActualShipper: false, // true: 实际出货方
            isChosen: false,
            editBasicFlag: false,   // 230801逻辑改动：从活动详情编辑基础信息,为true时，只可编辑时间字段
            bothEditFlag: false,
            editFlag: false,
            changeLm: false, //是否选择联盟
            isRefused: ['FeedbackRefuse', 'Refuse'].includes(this.pageParam.data?.approveStatus), // 审批驳回、执行反馈审批驳回
            isShowSave: this.pageParam?.source === 'banquetDetail' || this.pageParam?.editBasicFlag // 保存按钮是否显示
        }
    },
    computed:{
        addressData2(){
            let str = '获取定位'
            if(this.formData.otherAddress){
                str = this.formData.otherProvince + this.formData.otherCity + this.formData.otherDistrict + this.formData.otherAddress;
            }
            return str
        }
    },
    watch: {
        'formData.takePicturesFlag': {
            handler(newVal, oldVal) {
                if (newVal === 'N') {
                    this.formData.noPictureReason = ''
                }
            }
        }
    },
    async created() {
      this.$locations.QQClearLocation();  //清空地理位置
       if(this.pageParam.editFlag) {
            this.editFlag = true;
            await this.initQueryBanquet();
        };
       if(this.pageParam.id){ //宴席政策跳转
            this.formData.activityName = this.pageParam.activityName;
        };
       if(this.pageParam.editBasicFlag) {
           this.editBasicFlag = true;
           await this.initQueryBanquet();
       }
       if(this.editBasicFlag || this.editFlag) {
           this.bothEditFlag = true;
       }
        this.addressData = this.formData.policyFullAddress !== null && this.formData.policyFullAddress !== undefined
        && this.formData.policyFullAddress !== ''  ? this.formData.policyFullAddress : '获取定位';
        // 通过参数配置获取宴席流程中（受益客户、联盟成员和实际出货方）只需要主户头的专营公司编码
        this.multiAcctMainCode = await this.$utils.getCfgProperty('BANQUET_NO_SUB');
        // 获取模板
        await this.queryTemplate();
    },
  /**
   *  @description: 初始化所选地理信息
   *  @author: 彭杨
   *  @date: 2023/9/15
   */
    async onShow(){
        if(this.fromBack){
            this.fromBack = false
            return
        }
        const location = this.$locations.QQGetLocation();
        if(location){
            let addressInfo =  await reverseTMapGeocoder(location.latitude, location.longitude, '宴席活动申请');
            if(this.choseAddress){ //场地地址处理
                await this.setAddress(addressInfo)
                this.choseAddress = false
            }else{
                let address = {
                    latitude: addressInfo.wxMarkerData[0].latitude,
                    longitude: addressInfo.wxMarkerData[0].longitude,
                  policyProvince: addressInfo['originalData'].result.addressComponent['province'],
                  policyCity: addressInfo['originalData'].result.addressComponent['city'],
                  policyDistrict: addressInfo['originalData'].result.addressComponent['district'],
                  policyAddress: addressInfo.originalData.result['sematic_description']
                }
                this.formData = Object.assign({}, this.formData, address);
                this.addressData = this.formData.policyProvince + this.formData.policyCity + this.formData.policyDistrict + this.formData.policyAddress;
                try {
                    this.$utils.showLoading()
                    const data = await this.$http.post('/action/link/alladdress/queryEffectiveByDistrictCode',{addrCode:addressInfo['originalData'].result.addressComponent.adcode})
                    if(data.success) {
                        if(data.adcodeFlag){
                            this.$set(this.formData, 'adcode', data.adcode)
                            // this.$set(this.formData, 'province', data.province)
                            // this.$set(this.formData, 'city', data.city)
                            // this.$set(this.formData, 'district', data.district)
                            this.$set(this.formData, 'cityCode', data.cityCode)
                            this.$set(this.formData, 'provinceCode', data.provinceCode)
                            this.$set(this.formData, 'districtCode', data.districtCode)
                            // const chooseAddress = resData.res.address.replace(this.formData.province, '').replace(this.formData.city, '').replace(this.formData.district, '')
                            // this.$set(this.formData, 'address', chooseAddress + resData.res.name)
                            this.$utils.hideLoading();
                        }else {
                            this.$set(this.formData, 'adcode', '')
                            // this.$set(this.formData, 'province', address['originalData'].result.addressComponent.province)
                            // this.$set(this.formData, 'city', address['originalData'].result.addressComponent.city)
                            // this.$set(this.formData, 'district', address['originalData'].result.addressComponent.district)
                            this.$set(this.formData, 'cityCode', data.cityCode)
                            this.$set(this.formData, 'provinceCode', data.provinceCode)
                            this.$set(this.formData, 'districtCode', data.districtCode)
                            // const chooseAddress = resData.res.address.replace(this.formData.province, '').replace(this.formData.city, '').replace(this.formData.district, '')
                            // this.$set(this.formData, 'address', chooseAddress + resData.res.name)
                            this.$utils.hideLoading();
                        }
                    }else{
                        this.$set(this.formData, 'cityCode', null)
                        this.$set(this.formData, 'provinceCode', null)
                        this.$set(this.formData, 'districtCode', null)
                    }
                }catch (e) {
                    this.$set(this.formData, 'cityCode', null)
                    this.$set(this.formData, 'provinceCode', null)
                    this.$set(this.formData, 'districtCode', null)
                    this.$utils.hideLoading();
                }
            }

        }
    },
    methods: {
        onBack(){
            this.fromBack = true
        },
        /**
         * 设置场地地址内容
         * <AUTHOR>
         * @date 2024/04/10
         * */
        async setAddress(addressInfo){
            let address = {
                otherLatitude: addressInfo.wxMarkerData[0].latitude,
                otherLongitude: addressInfo.wxMarkerData[0].longitude,
                otherProvince: addressInfo['originalData'].result.addressComponent['province'],
                otherCity: addressInfo['originalData'].result.addressComponent['city'],
                otherDistrict: addressInfo['originalData'].result.addressComponent['district'],
                otherAddress: addressInfo.originalData.result['sematic_description']
            }
            this.formData = Object.assign({}, this.formData, address);
            try {
                this.$utils.showLoading()
                const data = await this.$http.post('/action/link/alladdress/queryEffectiveByDistrictCode',{addrCode:addressInfo['originalData'].result.addressComponent.adcode})
                if(data.success) {
                    this.$set(this.formData, 'otherCityCode', data.cityCode)
                    this.$set(this.formData, 'otherProvinceCode', data.provinceCode)
                    this.$set(this.formData, 'otherDistrictCode', data.districtCode)
                    this.$utils.hideLoading();
                }else{
                   this.$set(this.formData, 'otherCityCode', null)
                   this.$set(this.formData, 'otherProvinceCode', null)
                   this.$set(this.formData, 'otherDistrictCode', null)
                }
            }catch (e) {
                this.$set(this.formData, 'otherCityCode', null)
                this.$set(this.formData, 'otherProvinceCode', null)
                this.$set(this.formData, 'otherDistrictCode', null)
                this.$utils.hideLoading();
            }
        },
        /**
         * 新增消费者-宴席主家
         * <AUTHOR>
         * @date 2020-08-07
         *
         * */
        async addConsumer() {
            const id = await this.$newId();
            const accountItem = {
                id: id,
                row_status: ROW_STATUS.NEW,
                consumerDataType: 'ChannelConsumer',
                dataSource: 'MarketingPlatform',
                dataType: 'Consumer',
                accntSourceFrom: 'SalesAssistant',
                orgId: this.userInfo.orgId,
                fstName: this.userInfo.firstName,
                postnId: this.userInfo.postnId,
                belongToCompanyId: this.userInfo.coreOrganizationTile['l3Id'] || '',
                type: "ToBeFollowed",
                birthType: 'Yang',
                brandPreference: "",
                hobby: "",
                terminalFlag: 'N',
                listOfTags: {
                    accountId: id,
                    list: []
                }
            };
            this.$nav.push('/pages/lj-consumers/account/account-item-edit-page.vue', {
                data: accountItem,
                userInfo: this.userInfo,
                pageFrom: 'masterActivity',
                callback: (data) => {
                    this.$set(this.formData, 'banquetConsumerId', data.id);
                    this.$set(this.formData, 'banquetConsumerName', data.name);
                    this.$set(this.formData, 'banquetConsumerTel', data.phoneNumber);
                },
            })
        },
        /**
         * 选择开始时间
         * <AUTHOR>
         * @date	2023/12/28 16:40
         */
        startTimeChange(val) {
            if (this.banquetPolicy.autoEnd === 'Y' && this.banquetPolicy.autoDay) {
                this.setEndTime(val, this.banquetPolicy.autoDay);
            }
        },
        /**
         * 设置结束时间
         * <AUTHOR>
         * @date	2023/12/28 16:44
         * @param start 开始时间
         * @param autoDay 自动结束时间（天）
         */
        setEndTime(start, autoDay) {
            const date = new Date(start.replace(/-/g, '/'));
            // 将日期设置为开始时间 + 自动结束时间
            date.setDate(date.getDate() + Number(autoDay));
            const endTime = this.$date.format(date, 'YYYY-MM-DD HH:mm:ss');
            this.$set(this.formData, 'endTime', endTime);
        },
        /**
         * 获取宴席政策
         * <AUTHOR>
         * @date	2023/12/28 14:19
         */
        async queryBanquetPolicy() {
            try {
                const {success, result} = await this.$http.post('action/link/headquarterActivity/queryById', {
                    id: this.formData.activityId
                });
                if (success) {
                    this.banquetPolicy = result;
                }
            } catch (e) {

            }
        },
        /**
         * 获取宴席模板
         * <AUTHOR>
         * @date	2023/12/21 16:06
         */
        async queryTemplate() {
            try {
                const {success, result} = await this.$utils.getQwMpTemplate('BanquetTemplate');
                if (success) {
                    let resultOpt = JSON.parse(result);
                    this.templateList = JSON.parse(resultOpt.conf);
                }
            } catch (e) {
                console.log('err: 获取宴席模板出错', e);
            }
        },
        /**
         * 选择子公司/经销商
         * <AUTHOR>
         * @date	2023/12/25 20:13
         */
        async pickSubsidiaryDealer(title, readonlyFlag) {
            if (readonlyFlag) return;
            this.isLeagueChampion = false;
            const actExecutivesData = await this.$object(this.actExecutivesOption, {
                multiple: false,
                pageTitle: title
            });
            this.$set(this.formData, 'dealerName', actExecutivesData.billTitle);
            this.$set(this.formData, 'dealerId', actExecutivesData.id)
        },
        /**
         * 选择实际出货方
         * <AUTHOR>
         * @date	2023/12/25 20:02
         */
        async pickActualShipper(title, readonlyFlag) {
            if (readonlyFlag) return;
            if (this.$utils.isEmpty(this.formData.dealerId)) {
                this.$message.warn('请先选择联盟盟主！');
                return;
            }
            this.isLeagueMember = false;
            this.isActualShipper = true;
            this.changeLm = false;
            const actualShipper = await this.$object(this.beneficiaryOption, {
                multiple: false,
                pageTitle: title
            });
            if (actualShipper.acctType === 'Terminal') {
                this.$set(this.formData, 'terminalName', actualShipper.acctName);
                this.$set(this.formData, 'terminalId', actualShipper.id);
                this.$set(this.formData, 'terminalCode', actualShipper.acctCode);
            }
            if (['Distributor', 'Dealer'].includes(actualShipper.acctType)) {
                this.$set(this.formData, 'terminalName', actualShipper.billTitle);
                this.$set(this.formData, 'terminalId', actualShipper.id);
                this.$set(this.formData, 'terminalCode', actualShipper.acctCode);
            }
        },
        /**
         * 选择联盟成员
         * <AUTHOR>
         * @date	2023/12/25 19:58
         */
        async pickLeagueMember(title, readonlyFlag) {
            if (readonlyFlag) return;
            if (this.$utils.isEmpty(this.formData.dealerId)) {
                this.$message.warn('请先选择联盟盟主！');
                return;
            }
            this.isLeagueMember = true;
            this.isActualShipper = false;
            this.changeLm = false;
            const leagueMember = await this.$object(this.beneficiaryOption, {
                multiple: false,
                pageTitle: title
            });
            // 选择和之前相同的数据时不进行后续操作
            if (this.formData.memberId === leagueMember.id) return;
            if (leagueMember.acctType === 'Terminal') {
                this.$set(this.formData, 'memberName', leagueMember.acctName);
                this.$set(this.formData, 'memberId', leagueMember.id);
            }
            if (['Distributor', 'Dealer'].includes(leagueMember.acctType)) {
                this.$set(this.formData, 'memberName', leagueMember.billTitle);
                this.$set(this.formData, 'memberId', leagueMember.id);
            }
        },
        /**
         * 选择联盟盟主
         * <AUTHOR>
         * @date	2023/12/25 19:57
         */
        async pickLeagueChampion(title, readonlyFlag) {
            if (readonlyFlag) return;
            this.isLeagueChampion = true;
            const leagueChampion = await this.$object(this.actExecutivesOption, {
                multiple: false,
                pageTitle: title
            });
            // 选择和之前相同的数据时不进行后续操作
            if (this.formData.dealerId === leagueChampion.id) return;
            // 清空联盟成员和实际出货方
            this.$set(this.formData, 'memberId', null);
            this.$set(this.formData, 'memberName', null);
            this.$set(this.formData, 'terminalId', null);
            this.$set(this.formData, 'terminalName', null);
            this.$set(this.formData, 'terminalCode', null);
            // 赋值联盟盟主
            this.$set(this.formData, 'dealerName', leagueChampion.billTitle);
            this.$set(this.formData, 'dealerId', leagueChampion.id);
        },
        /**
         *  @description: 唤醒地图
         *  @author: 彭杨
         *  @date: 2023/9/15
         */
        async getLocation() {
            const that = this;
            const addressInfo = await this.$locations.getAddress();
            await that.$locations.chooseLocation(addressInfo.wxMarkerData[0].latitude, addressInfo.wxMarkerData[0].longitude);
        },
        // 获取场地地址2
        async getLocations(){
            const addressInfo = await this.$locations.getAddress();
            this.choseAddress = true
            await this.$locations.chooseLocation(addressInfo.wxMarkerData[0].latitude, addressInfo.wxMarkerData[0].longitude);
        },
        /**
         * desc 初始化查询活动
         * <AUTHOR>
         * @date 2023-07-06
         * @params
         */
        async initQueryBanquet() {
            try{
                const res = await this.$http.post('action/link/headquarterFeedback/queryById', {id: this.pageParam.data.id});
                if(res.success) {
                    this.formData = res.result;
                    this.formData = {...this.formData};
                    this.formData.row_status = 'UPDATE';
                    if (this.formData.activityId) {
                        // 获取宴席政策
                        await this.queryBanquetPolicy();
                    }
                }
            } catch (e) {
                this.$showError('初始化查询活动失败!');
            }
        },
        /**
         * desc 新增生成宴席名称
         * <AUTHOR>
         * @date 2023-08-21
         */
        async generateBanquetName() {
            const month = this.formData.startTime.substr(5, 2);
            const day = this.formData.startTime.substr(8, 2);
            let startTime = month + day;
            let banquetType = await this.$lov.getNameByTypeAndVal('BANQUET_TYPE', this.formData.banquetType);
            this.$set(this.formData, 'feedbackName', startTime + this.formData.banquetConsumerName + banquetType);
        },
        /**
         * desc 保存活动
         * <AUTHOR>
         * @date 2023-07-06
         * @params
         */
        async save () {
            await this.$refs.upsertBanquet.validate();
            if(this.formData.startTime > this.formData.endTime) {
                this.$message.warn('开始时间不能大于结束时间！');
                return Promise.reject('开始时间不能大于结束时间！');
            }
            if (this.formData.startTime.slice(0,10) > this.formData.mealTime.slice(0,10)) {
                this.$message.warn('宴席正餐时间不能早于活动开始时间！');
                return Promise.reject('宴席正餐时间不能早于活动开始时间！');
            }
            // 政策的是否限制宴席单时长为是，需要校验宴席单的开始时间和结束时间间隔不能大于政策的最长宴席单时长（天）
            if (this.banquetPolicy.limitTimeFlag === 'Y' && this.banquetPolicy.maxBanquetDay && this.formData.startTime && this.formData.endTime) {
                // 获取开始时间和结束时间时间差（毫秒）
                const sperate = new Date(this.formData.endTime.replace(/-/g, '/')) - new Date(this.formData.startTime.replace(/-/g, '/'));
                // 政策的最长宴席单时常转化为毫秒并和开始时间、结束时间的时间差进行比较
                if (this.banquetPolicy.maxBanquetDay * 24 * 3600 * 1000 < sperate) {
                    this.$showError(`开始时间和结束时间间隔不能超过${this.banquetPolicy.maxBanquetDay}天，请重新填写`);
                    return;
                }
            }
            try {
                this.$utils.showLoading();
                await this.generateBanquetName();
                // 宴席详情编辑保存时，调用优码接口
                if(this.editBasicFlag) {
                    const res = await this.$http.post('action/link/youma/getBanquetLotteryStatus', {
                        actNum: this.formData.feedbackCode,
                    });
                    if (res.success && res.lottery) {
                        console.log('res.lottery', res.lottery);
                    } else {
                        this.$utils.hideLoading();
                        this.$message.warn('已存在消费者开瓶扫码记录，不允许编辑宴席单信息！');
                        return;
                    }
                }
                const rowStatus = this.bothEditFlag ? ROW_STATUS.UPDATE : ROW_STATUS.NEW;
                if(!this.bothEditFlag) {
                    this.formData.activityType = 'Banquet';
                    this.formData.activityStatus = 'New';
                }
                const data = await this.$http.post('action/link/headquarterFeedback/saveOrCommitBanquetAct', {
                    ...this.formData,
                    row_status: rowStatus,
                    attr1: 'saveBanquetAct',
                });
                if (data.success) {
                    this.formData.id = data.rows.id;
                    this.$utils.hideLoading();
                    this.$bus.$emit('refreshBanquetList');  // 刷新终端列表
                    this.$bus.$emit('refreshBanquetBasic');  // 刷新基础信息
                    this.$message.success('保存成功！');
                    if(this.editBasicFlag) {
                        wx.navigateBack({delta: 2})
                    } else {
                        await this.goBack();
                    }
                } else {
                    this.$message.warn('保存出错！');
                }
            } catch (e) {
                console.log(e);
            } finally {
                this.$utils.hideLoading();
            }
        },
        /**
         * desc 提交活动
         * <AUTHOR>
         * @date 2023-07-06
         * @params
         */
        async commit() {
            await this.$refs.upsertBanquet.validate();
            if (this.formData.startTime > this.formData.endTime) {
                this.$message.warn('开始时间不能大于结束时间！');
                return Promise.reject('开始时间不能大于结束时间！');
            }
            // 政策的是否限制宴席单时长为是，需要校验宴席单的开始时间和结束时间间隔不能大于政策的最长宴席单时长（天）
            if (this.banquetPolicy.limitTimeFlag === 'Y' && this.banquetPolicy.maxBanquetDay && this.formData.startTime && this.formData.endTime) {
                // 获取开始时间和结束时间时间差（毫秒）
                const sperate = new Date(this.formData.endTime.replace(/-/g, '/')) - new Date(this.formData.startTime.replace(/-/g, '/'));
                // 政策的最长宴席单时常转化为毫秒并和开始时间、结束时间的时间差进行比较
                if (this.banquetPolicy.maxBanquetDay * 24 * 3600 * 1000 < sperate) {
                    this.$showError(`开始时间和结束时间间隔不能超过${this.banquetPolicy.maxBanquetDay}天，请重新填写`);
                    return;
                }
            }
            try {
                this.$utils.showLoading();
                await this.generateBanquetName();
                if (this.editBasicFlag) {
                    const res = await this.$http.post('action/link/youma/getBanquetLotteryStatus', {
                        actNum: this.formData.feedbackCode,
                    });
                    if (res.success && res.lottery) {
                        console.log('res.lottery', res.lottery);
                    } else {
                        this.$utils.hideLoading();
                        this.$message.warn('已存在消费者开瓶扫码记录，不允许编辑宴席单信息！');
                        return;
                    }
                }
                if (!this.bothEditFlag) {
                    this.formData.activityType = 'Banquet';
                    this.formData.activityStatus = 'New';
                }
                const rowStatus = this.bothEditFlag ? ROW_STATUS.UPDATE : ROW_STATUS.NEW;
                const data = await this.$http.post('action/link/headquarterFeedback/saveOrCommitBanquetAct', {
                    ...this.formData,
                    row_status: rowStatus,
                    attr1: 'commitBanquetAct',
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    this.$message.success('提交成功！');
                    this.$bus.$emit('refreshBanquetList');  // 刷新宴席列表
                    this.$bus.$emit('refreshBanquetBasic');  // 刷新基础信息
                    if(this.editBasicFlag) {
                        wx.navigateBack({delta: 2})
                    } else {
                        await this.goBack();
                    }
                } else {
                    this.$message.warn('提交出错！');
                }
            } catch (e) {
                console.log(e);
            } finally {
                this.$utils.hideLoading();
            }
        },
        /**
         * desc 选择宴席合伙人
         * <AUTHOR>
         * @date 2024-06-27
         * @params
         */
        async selectPartner(){
             const partnerData = await this.$object(this.clueOption, {multiple: false, pageTitle: '宴席合伙人'});
             this.$set(this.formData, 'banquetPartner', partnerData.acctName ? partnerData.acctName : null);
             this.$set(this.formData, 'partnerMobilePhone', partnerData.mobilePhone1 ? partnerData.mobilePhone1 : null);
        },
        /**
         * desc 选择宴席政策
         * <AUTHOR>
         * @date 2023-07-05
         * @params
         */
        async afterSelectActName() {
            const policyData = await this.$object(this.banquetPolicyOption, {multiple: false, pageTitle: '宴席政策'});
            this.banquetPolicy = policyData;
            // 重新选择的政策是自动结束，并且开始时间存在时，重新赋值结束时间
            if (this.formData.startTime && policyData.autoEnd === 'Y' && policyData.autoDay) {
                this.setEndTime(this.formData.startTime, policyData.autoDay)
            }
            if(this.formData.activityId === policyData.id){
                return
            }
            this.$set(this.formData, 'activityId', policyData.id);
            this.$set(this.formData, 'activityNum', policyData.activityNum);
            this.$set(this.formData, 'activityName', policyData.activityName);

            this.$set(this.formData, 'isBanquetPartner', policyData.isBanquetPartner ? policyData.isBanquetPartner : 'false');
            this.$set(this.formData, 'banquetPartner',  null);
            this.$set(this.formData, 'partnerMobilePhone', null);
            //@edit by tanshaoqi 2024/04/10 调整政策后清空终端数据
            this.$set(this.formData, 'terminalName', null);
            this.$set(this.formData, 'terminalId', null);
            this.$set(this.formData, 'terminalCode', null);
            //@edit by lld 2024/11/06 调整政策后清空计划用酒产品数据 手动刷新关联产品
            this.$set(this.formData, 'banquetProdCode', null);
            this.$set(this.formData, 'banquetProdId', null);
            this.planProdOption.methods.reload();
        },
        /**
         * desc 选择宴席主家
         * <AUTHOR>
         * @date 2023-07-06
         * @params
         * 230815-根据业务需求暂时注释代码
         */
        async pickMasterName() {
            const MasterData = await this.$object(this.masterOption, {multiple: false, pageTitle: '宴席主家'});
            this.$set(this.formData, 'banquetConsumerId', MasterData.id);
            this.$set(this.formData, 'banquetConsumerName', MasterData.acctName);
            this.$set(this.formData, 'banquetConsumerTel', MasterData.mobilePhone1);
        },
        /**
         * @desc 选择所属客户数据
         **/
        async chooseStoreList () {
            this.isChosen = !this.isChosen
            if (this.isChosen) {
                const list = await this.$object(this.customerOption, {
                    pageTitle: '请选择所属客户',
                    showInDialog: true,
                    multiple: false,
                    autoListProps: {searchInputBinding: {props: {placeholder: '搜索所属客户名称'}}}
                });
                this.masterOption.option.param['belongToStoreIdList'] = [list.id];
                this.masterOption.methods.reload();
            } else {
                delete this.masterOption.option.param['belongToStoreIdList'];
                this.masterOption.methods.reload();
            }
        },
        /**
         * desc 选择受益终端
         * @date 2023-04-23
         */
        async pickBeneficiaryName(title) {
            if(!this.formData.activityId){
                this.$message.warn('请先选择宴席政策！');
                return;
            }
            this.isLeagueMember = false;
            this.isActualShipper = false;
            this.changeLm = true
            const beneficiaryData = await this.$object(this.beneficiaryOption, {multiple: false, pageTitle: title});
            /*
            * 当客户大类=【Dealer】&【Distributor】时，企微前端查询出来显示在列表的数据及选择后显示在的活动头上的数据，取客户的营业执照名称【billTitle 】显示
            * 当客户大类=【Terminal】时，企微前端查出来显示在列表的数据及选择后显示在活动头上的数据，取客户的门头店招名称【acctName】；
            * */
            if (beneficiaryData.acctType === 'Terminal') {
                this.$set(this.formData, 'terminalName', beneficiaryData.acctName);
                this.$set(this.formData, 'terminalId', beneficiaryData.id);
                this.$set(this.formData, 'terminalCode', beneficiaryData.acctCode);
            } else if (beneficiaryData.acctType === 'Dealer' || beneficiaryData.acctType === 'Distributor') {
                this.$set(this.formData, 'terminalName', beneficiaryData.billTitle);
                this.$set(this.formData, 'terminalId', beneficiaryData.id);
                this.$set(this.formData, 'terminalCode', beneficiaryData.acctCode);
            }
            this.changeLm = false
        },
        /**
         * desc 选择小酒产品之前
         * @date 2023-04-23
         */
        beforePlanProd() {
            if (!this.formData.activityName) {
                this.$message.warn('请先选择宴席政策！');
                return Promise.reject();
            }
        },
        /**
         * desc 选择计划用酒产品
         * <AUTHOR>
         * @date 2023-07-05
         * @params
         */
        afterPlanProd(data) {
            this.$set(this.formData, 'banquetProdCode', data.productCode);
            this.$set(this.formData, 'banquetProdId', data.productId);
        },
        /**
         * desc 返回宴席活动列表
         * <AUTHOR>
         * @date 2023/07/06
         */
        async goBack() {
            this.$nav.back();
        }
    }
}
</script>

<style lang="scss">
.banquet-activity-apply-page {
    .address-info {
        display: flex;
        align-items: center;
        .icon-location {
            font-size: 32px;
        }
        .address-data{
            width: 300px;
            word-break: normal;
            color: #3b4144;
            text-align: right;
        }
        .address-tips{
            width: 300px;
            color: #2F69F8;
            text-align: right;
            display: block;
        }
    }
    .no-reason {
        background-color: #fff;
        padding-bottom: 20px;
    }
    .banquet-plan-prod-item {
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
        padding: 12px;
        .content-middle-line {
            width: 100%;

            .data {
                width: 100%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    width: 20%;
                    float: left;
                    padding-left: 6px;
                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                }

                .Submitted, .Feedback{
                    color: #2F69F8;
                }

                .Approve, .FeedbackApro{
                    color: #2EB3C2;
                }

                .Refused, .Refeedback{
                    color: #FF5A5A;
                }

            }

        }
    }

}
</style>
