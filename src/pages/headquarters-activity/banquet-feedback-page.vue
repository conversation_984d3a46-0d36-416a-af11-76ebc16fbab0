<!--
总部活动-宴席活动-执行反馈编辑页面
<AUTHOR>
@date 2023-07-12
@file banquet-feedback-page
-->
<template>
    <link-page class="banquet-feedback-page">
        <lnk-taps :taps="tapsOptions" v-model="tapsActive" @switchTab="switchTab"></lnk-taps>
        <!--基础信息-->
        <banquet-basic-info :banquetItem="banquetItem" :editBasicFlag="editBasicFlag" id="basicInfo" :templateList="templateList"></banquet-basic-info>
        <!--执行反馈-->
        <banquet-feedback-edit 
            :banquetItem="banquetItem"
            ref="getFormData"
            id="feedback"
            :isNoCount="isNoCount"
            :maxImgLength="maxImgLength"
            :banquetPolicy="banquetPolicy">
        </banquet-feedback-edit>
        <!--出库明细-->
        <banquet-outbound-details :banquetItem="banquetItem" id="OutboundDetails"></banquet-outbound-details>
        <!-- 退货明细 -->
        <banquet-inbound-details :banquetItem="banquetItem" id="inboundDetails"></banquet-inbound-details>
        <!--消费者开瓶明细-->
        <banquet-consumer-open :feedbackCode="pageParam.data.feedbackCode" id="consumerOpen"></banquet-consumer-open>
        <!--联系人明细-->
        <banquet-forward-contact-list :banquetItemId="banquetItem.id" id="forwardContact"></banquet-forward-contact-list>
        <link-sticky>
            <link-button block @tap="save" mode="stroke">保存</link-button>
            <link-button block @tap="approveCommit">提交审批</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
import lnkTaps from '../core/lnk-taps/lnk-taps';
import banquetBasicInfo from './components/banquet-basic-info.vue';
import banquetFeedbackEdit from './components/banquet-feedback-edit.vue';
import banquetOutboundDetails from './components/banquet-outbound-details.vue';
import banquetInboundDetails from './components/banquet-inbound-details.vue';
import banquetForwardContactList from './components/banquet-forward-contact-list.vue';
import banquetConsumerOpen from './components/banquet-consumer-open.vue';
export default {
    name: 'banquet-feedback-page',
    components: {lnkTaps, banquetBasicInfo, banquetFeedbackEdit, banquetInboundDetails, banquetOutboundDetails, banquetForwardContactList,banquetConsumerOpen},
    data() {
        const tapsOptions = [
            {name: '基础信息', seq: '1', val: 'basicInfo'},
            {name: '执行反馈', seq: '2', val: 'feedback'},
            {name: '出库明细', seq: '3', val: 'OutboundDetails'}, 
            {name: '退货明细', seq: '4', val: 'inboundDetails'},
            {name: '消费者开瓶明细', seq: '5', val: 'consumerOpen'},
            {name: '转发明细', seq: '6', val: 'forwardContact'}
        ];
        const tapsActive = 0;       // 选项卡默认选中
        return {
            tapsOptions,
            tapsActive,    // 选项卡默认选中
            banquetItem: {},   // 宴席信息
            banquetPolicy: {}, // 宴席政策
            templateList: [], // 宴席模板
            editBasicFlag: false,    // 是否可编辑
            userInfo: this.$taro.getStorageSync('token').result,
            isNoCount: false, // 登录职位是否与企业参数配置一致 
            maxImgLength: 9,
        }
    },
    async created() {
        this.tapsActive = this.tapsOptions[0];
        this.banquetItem = this.pageParam.data;
        // 大成公司执行反馈图片上传最大限制
        const res = await this.$utils.getCfgProperty('QW_OFFSITE_OPEN_RATE');
        this.isNoCount = res.includes(this.userInfo.coreOrganizationTile.brandCompanyCode);
        if (this.isNoCount) {
            const allImgLength = await this.$utils.getCfgProperty('COMMIT_BANQUET_FEEDBACK_MAX_PIC_SIZE');
            this.maxImgLength = Number(allImgLength);
        }
        await this.queryTemplate();
        await this.queryBanquetPolicy();
    },
    methods: {
        /**
         * 获取宴席政策
         * <AUTHOR>
         * @date	2023/12/28 14:19
         */
        async queryBanquetPolicy() {
            try {
                const {success, result} = await this.$http.post('action/link/headquarterActivity/queryById', {
                    id: this.pageParam.data.activityId
                });
                if (success) {
                    this.banquetPolicy = result;
                }
            } catch (e) {

            }
        },
        /**
         * 获取宴席模板
         * <AUTHOR>
         * @date	2023/12/21 16:06
         */
        async queryTemplate() {
            try {
                const {success, result} = await this.$utils.getQwMpTemplate('BanquetTemplate');
                if (success) {
                    let resultOpt = JSON.parse(result);
                    this.templateList = JSON.parse(resultOpt.conf);
                }
            } catch (e) {

            }
        },
        /**
         * 提交审批
         *  <AUTHOR>
         *  @date 2023-06-29
         **/
        async approveCommit() {
            try {
                const formDataInfo = await this.$refs.getFormData.getFormData();
                const banquetActFee = formDataInfo.actTableNum * formDataInfo.actTableReward;
                if (formDataInfo.banquetActFee > formDataInfo.banquetPlanFee) {
                    this.$message.warn('实际推荐费总额不能大于推荐费限额！');
                    this.$dialog({
                        title: '提示',
                        content: '实际推荐费总额不能大于推荐费限额！',
                        cancelButton: false,
                        onConfirm: () => {
                        },
                    });
                    return;
                }
                if (!!formDataInfo.banquetActFee && formDataInfo.banquetActFee > banquetActFee) {
                    this.$dialog({
                        title: '提示',
                        content: `实际推荐费总额不能超过${banquetActFee}，如需增大实际推荐费，请确认桌数`,
                        cancelButton: false,
                        onConfirm: () => {
                        },
                    });
                    return;
                }
                if (formDataInfo.banquetActFee !== 0 && !formDataInfo.banquetActFee) {
                    this.$dialog({
                        title: '提示',
                        content: '请填入实际推荐费总额！',
                        cancelButton: false,
                        onConfirm: () => {
                        },
                    });
                    return;
                }
                await this.$refs.getFormData.validateInfo();
                if (this.banquetItem.takePicturesFlag !== 'Y') {
                    await this.$refs.getFormData.getUploadPhotoInfo();
                };
                await this.$dialog.confirm('宴席执行反馈提交后视为宴席已结束，后续开瓶将不计入宴席开瓶数量内，是否确认提交？')
                this.$utils.showLoading();
                const data = await this.$http.post('action/link/headquarterFeedback/saveOrCommitBanquetFeedback', {
                    id: formDataInfo.id,
                    attr1: 'commitBanquetFeedback',
                    actTableNum: formDataInfo.actTableNum,
                    actTableReward: formDataInfo.actTableReward,
                    banquetActFee: formDataInfo.banquetActFee,
                    banquetPlanFee: formDataInfo.banquetPlanFee,
                    feedbackRemark: formDataInfo.feedbackRemark,
                });
                if (data.success) {
                    this.$bus.$emit('refreshFeedbackInfo');
                    this.$bus.$emit('refreshBanquetPhoto');
                    this.$bus.$emit('refreshScanCode');
                    this.$bus.$emit('refreshBanquetList');  // 刷新宴席列表
                    this.$bus.$emit('refreshFabButton', {data: data.rows});
                    Object.assign(this.banquetItem, data.rows);
                    this.$utils.hideLoading();
                    this.$message.success('提交成功！');
                    setTimeout(async () => {
                        await this.goBack();
                    }, 1000);
                } else {
                    this.$message.warn('提交出错！');
                }
            } catch (e) {
                console.log(e);
            } finally {
                this.$utils.hideLoading();
            }
        },
        /**
         * 保存
         *  <AUTHOR>
         *  @date 2023-06-29
         **/
        async save() {
            try {
                const formDataInfo = await this.$refs.getFormData.getFormData();
                const banquetActFee = formDataInfo.actTableNum * formDataInfo.actTableReward;
                if (formDataInfo.banquetActFee > formDataInfo.banquetPlanFee) {
                    this.$message.warn('实际推荐费总额不能大于推荐费限额！');
                    this.$dialog({
                        title: '提示',
                        content: '实际推荐费总额不能大于推荐费限额！',
                        cancelButton: false,
                        onConfirm: () => {
                        },
                    });
                    return;
                }
                if(!!formDataInfo.actTableNum && !!formDataInfo.banquetActFee && formDataInfo.banquetActFee > banquetActFee){
                    this.$dialog({
                        title: '提示',
                        content: `实际推荐费总额不能超过${banquetActFee}，如需增大实际推荐费，请确认桌数`,
                        cancelButton: false,
                        onConfirm: () => {
                        },
                    });
                    return;
                }
                if (formDataInfo.banquetActFee !== 0 && !formDataInfo.banquetActFee) {
                    this.$dialog({
                        title: '提示',
                        content: '请填入实际推荐费总额！',
                        cancelButton: false,
                        onConfirm: () => {
                        },
                    });
                    return;
                }
                await this.$refs.getFormData.validateInfo();
                if (this.banquetItem.takePicturesFlag !== 'Y') {
                    await this.$refs.getFormData.getUploadPhotoInfo();
                };
                this.$utils.showLoading();
                const data = await this.$http.post('action/link/headquarterFeedback/saveOrCommitBanquetFeedback', {
                    id: formDataInfo.id,
                    attr1: 'saveBanquetFeedback',
                    actTableNum: formDataInfo.actTableNum,
                    actTableReward: formDataInfo.actTableReward,
                    banquetActFee: formDataInfo.banquetActFee,
                    banquetPlanFee: formDataInfo.banquetPlanFee,
                    feedbackRemark: formDataInfo.feedbackRemark,
                });
                if (data.success) {
                    this.$bus.$emit('refreshBanquetList');
                    this.$bus.$emit('refreshFeedbackInfo');
                    this.$bus.$emit('refreshBanquetPhoto');
                    this.$bus.$emit('refreshScanCode');
                    Object.assign(this.banquetItem, data.rows);
                    this.$message.success('保存成功！');
                    this.$utils.hideLoading();
                    setTimeout(async () => {
                        await this.goBack();
                    }, 1000);
                } else {
                    this.$message.warn('保存出错！');
                }
            } catch (e) {
                console.log(e);
            } finally {
                this.$utils.hideLoading();
            }
        },
        /**
         * tab页切换
         *  <AUTHOR>
         *  @date 2023-06-29
         **/
        switchTab(val, key) {
            this.currentIndex = parseInt(key);
            this.tapsActive = val;
            wx.pageScrollTo({
                selector: `#${val.val}`,
                duration: 500
            })
        },
        /**
         * desc 返回
         * <AUTHOR>
         * @date 2023/07/14
         */
        async goBack() {
            this.$nav.back();
        }
    }
}
</script>

<style lang="scss">
.banquet-feedback-page {

}
</style>
