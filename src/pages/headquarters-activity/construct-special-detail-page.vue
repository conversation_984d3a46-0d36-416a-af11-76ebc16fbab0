<!--
总部活动-百城阵地建设新增
<AUTHOR>
@date 2023-08-03
-->
<template>
	<link-page class="special-detail-page">
		<construct-special-info :item='formData' :canEdit='canEdit' @goEditInfo="goEditInfo"></construct-special-info>
		<view class="basic-info-title">
			<view class="left">
				<line-title title="终端信息" class="head-title"></line-title>
			</view>
		</view>
		<view>
			<view class="terminal-title">
				<view>终端名单(已添加数量{{terminalNum}})</view>
			</view>
			<view v-for="(item,index) in terminal" :key="index+'terminal'">
				<terminal-item :item='item' :hideThree="hideThree">
					<template v-slot:chose  v-if="item.selectFlag">
						<view class="terminal-checkbox">
							<view v-show='item.selectFlag'>{{item.selectFlag === 'Y'?'已通过':'未通过'}}</view>
						</view>
					</template>
				</terminal-item>
			</view>		
		</view>
	</link-page>
</template>

<script>
	import LineTitle from "../lzlj/components/line-title.vue";
	import terminalItem from './components/terminal-item'
	import constructSpecialInfo from './components/construct-special-info'
	export default {
		name:'construct-special-detail-page',
		components:{
			terminalItem,
			constructSpecialInfo,
			LineTitle
		},
		data() {
			const rules = {}
			return {
				editBasicFlag:false,
				terminal:[],
			    formData: {
					endTime:'2023-09-18 23:59:59',
					terminal:[]
				},  //form对象
			    rules,  // 校验规则
				canEdit:false, //是否可编辑
				hideThree:false, //判断展示终端内容
			}
		},
		computed:{
			terminalNum(){
				return this.terminal?.length
			}
		},
		async created() {				
			this.canEdit = Boolean(['Refuse','New'].includes(this.pageParam.data.approveStatus))
		},
		mounted(){
			if(this.pageParam.data){				
				this.getDetail(this.pageParam.data.id)				
			}	
		},
		methods:{
			/**
			* 获取详情
			* <AUTHOR>
			* @date 2023-08-18
			* @param param
			*/
			async getDetail(id){
				try {
					const {success,result} = await this.$http.post('action/link/hundredCityBuild/queryById',{id})
					if(success){
						this.formData = result
                        this.hideThree = this.formData.actConstructionType === 'ReplacingStoreSignage'
						this.getTerDetail()
					}
				} catch (error) {
					console.log(error)
				}				
			}, 	
			/**
			* 获取终端详情
			* <AUTHOR>
			* @date 2023-08-08
			* @param param
			*/
			async getTerDetail(){
				try{
				   let {rows,success} = await this.$http.post('action/link/simpleTerminalInfo/queryHundredCityBuildDetailPage', {
					actNum: this.pageParam.data.actNum,
					actConstructionType:this.pageParam.data.actConstructionType
				});
					if(success){
						rows.forEach(async (item) => {
						    if (!this.$utils.isEmpty(item.smallUrl)) {
						        let urlData = await this.$image.getSignedUrl(item.smallUrl);
						        this.$set(item, 'storeUrl', urlData);
						    } else {
						        this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
						    }
						})
						this.terminal = rows
						this.formData.terminalNum = rows.length
					}
				} catch(e) {
					console.log(e)
				}
			},
         /**
         * 跳转至保存
         * <AUTHOR>
         * @date 2023-08-013
         * @param param
         */
            async goEditInfo(data){
				this.$nav.push('/pages/headquarters-activity/construct-special-apply-page.vue', {
					...data,
					terminal: this.terminal
				})
			},
			
		}
	}
</script>

<style lang="scss">
	.special-detail-page{
		background-color: #F2F2F2;
		font-family: PingFangSC-Regular;
		.label{
			@include flex-center-center;
			&-mp-info{
				@include flex-center-center;
				color:#2f69f8
			}
		}
		.terminal-title{
			@include flex;
			@include space-between;
			background-color:white;
			margin:10px;
			padding:35px;
			font-size: 24px;
			&-add{
				font-size: 32px;
				color: #2f69f8;
			}
		}
		.terminal-checkbox{
			font-size: 24px;
			height: 12px;
			@include flex-center-center;
		}
		
	}
</style>