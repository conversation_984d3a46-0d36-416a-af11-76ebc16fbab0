<!--
总部活动-宴席活动-一键转发生成口令弹窗
<AUTHOR>
@date 2024-12-20
-->
<template>
    <view class="banquet-share-dialog">
        <view class="share-dialog-mask" @tap="()=>$emit('shareFlag')"></view>
        <view class="share-code">
            <view class="share-code-title">
                生成口令
            </view>
            <view class="share-code-content">
                {{password}}
            </view>
            <link-button label="复制口令" @tap.stop="copyPassword"/>
        </view>
        <view class="share-icon-warp">
            <link-button open-type="share" mode="text" @tap.stop="share">
                <image :src="$imageAssets.banquetShare" class="share-image"></image>
                <view class="share-icon-title">
                    转发好友
                </view>
            </link-button>
        </view>
    </view>
</template>
<script>
export default {
    name: 'banquet-share-dialog',
    props: {
        banquetItem: { 
            type: Object,
            default:() => {
                return {}
            }
        }
    },
    data() {
        return {
            userInfo: this.$taro.getStorageSync('token').result,
            password: ''
        }
    },
    async created() {
        if (this.banquetItem.watchword) {
            this.password = this.banquetItem.watchword;
        } else {
            this.getPassword();
        } 
    },
    methods: {
        share() {
            this.$emit('shareFlag');
        },
        /**
         * 复制转发口令
         * <AUTHOR>
         * @date	2024/12/20 
        */
        copyPassword() {
            const that = this
            this.$taro.setClipboardData({
                data: that.password,
                success(){
                    console.log('复制成功');
                }
            })
        },
        /**
         * 获取转发口令
         * <AUTHOR>
         * @date	2024/12/20 
        */
        async getPassword() {
            try {
                const data = await this.$http.post('action/link/headquarterFeedback/generateWatchword', {
                    id: this.banquetItem.id,
                });
                if (data.success) {
                    this.password = data.data
                }
            }catch(err){
                console.log('err: 获取转发口令失败', err);
            }
        }
    },
}
</script>
<style lang="scss">
.banquet-share-dialog {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    pointer-events: auto;
    z-index: 2000;
    display: flex;
    justify-content: center;
    align-items: center;
    .share-dialog-mask { 
        position: fixed;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.9);
        pointer-events: auto;
        z-index: 2001;
    }
    .share-code {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        background: #fff;
        border-radius: 10px;
        width: 70%;
        height: 280px;
        z-index: 3000;
        .share-code-title {
            font-size: 24px;
        }
        .share-code-content {
            font-size: 40px;
            font-weight: 600;
            margin-top: 30px;
        }
        .link-button {
            width: 240px;
            height: 60px;
            margin-top: 30px;
        }
    }
    .share-icon-warp {
        position: fixed;
        bottom: 0;
        right: 0;
        left: 0;
        z-index: 3000;
        height: 240px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 20px 20px 0 0;
        background-color: #fff;
        .share-image {
            width: 80px;
            height: 80px;
            margin-bottom: 10px;
        }
        .share-icon-title {
            color: #333;
        }
    }
}
</style>