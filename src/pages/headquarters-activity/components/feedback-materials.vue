<!--
总部活动-登记列表详情-查看反馈材料
<AUTHOR>
@date 2023-04-04
-->
<template>
    <view class="feedback-materials">
        <!--反馈材料-->
        <view class="feedback-materials-v">
            <view class="menu-stair1">
                <view class="line">
                    <view class="line-top"></view>
                    <view class="line-bottom"></view>
                </view>
                <view class="stair-title1">反馈材料</view>
            </view>
        </view>
        <view class="feedback-materials-img">
            <view style="width: 100%;height: 8px"></view>
            <!--图片模板无权限-->
            <lnk-no-auth v-if="imgAuthFlag"></lnk-no-auth>
            <view v-if="!imgAuthFlag">
                <!--                <view v-for="(item1,index1) in dataList" :key="index1+1" class="upload-1">-->
                <view v-for="(group) in dataList" :key="group.name" class="upload-1">
                    <view>
                        <view class="pic-list">
<!--                            <view style="margin-left: 16px;margin-right:16px;font-family: PingFangSC-Semibold, serif;font-size: 16px;-->
<!--                            color: #262626;letter-spacing: 1px;line-height: 32px;">-->
<!--                                {{ group.name }}-->
<!--                            </view>-->
                            <line-title :title="group.name" class="head-title"></line-title>
                            <view class="uploaded-pic">
                                <view class="pic">
                                    <view class="pic-container">
                                        <!--                                        <view v-for="(item2,index2) in item1.picTypeList" :key="index2+ 1">-->
                                        <view class="pic-item" v-for="(item, index) in group.data" :key="index"
                                              v-if="group.data.length">
                                            <view class="pic-bg">
                                                <!--                                        <image :src="data.imgUrl" @tap="clickImg(pictureAll,data)" class="image"/>-->
                                                <image :src="item.imgUrl" class="image" @tap.stop="previewStoreUrl(dataList, item)"/>
                                            </view>
                                            <view class="data-source">{{ item.dataSource | lov('PIC_SOURCE') }}</view>
                                            <view class="data-source" style="font-size: 11px;line-height: 12px">
                                                {{ item.created | date('YYYY-MM-DD HH:mm:ss') }}
                                            </view>
                                            <!--                                            </view>-->
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <!--                    <view class="supernatant" v-if="dataList.length >= 4">-->
                    <!--                        <view class="title">-->
                    <!--                            共{{ dataList.length }}张-->
                    <!--                        </view>-->
                    <!--                    </view>-->
                </view>
                <view style="width: 100%;height: 8px"></view>
            </view>
        </view>


    </view>
</template>

<script>
import Taro from "@tarojs/taro";
import LineTitle from "../../lzlj/components/line-title.vue";

export default {
    name: "feedback-materials",
    components: {LineTitle},
    props: {
        registerItem: {
            type: Object,
            default: function () {
                return {};
            },
        }
    },
    computed: {
        registerItemData: function () {
            return this.registerItem;
        },
    },
    data() {
        return {
            imgAuthFlag: false, //图片模板无权限
            // picturesNeedUploaded: [],
            dataList: [],   //所有图片列表
            itemImgList: [],// 根据模版对应的每一项的图片列表
        }
    },
    mounted() {
        console.log('this.registerItemData', this.registerItemData)
        this.queryFeedbackPicList();
    },
    methods: {
        /**
         * 查询当前反馈材料的图片
         *  <AUTHOR>
         *  @date 2023-04-06
         */
        async queryFeedbackPicList() {
            const lovVals = this.registerItemData.regReqList;
            if (this.$utils.isEmpty(this.registerItemData.id)) {
                this.registerItemData.id = 'noMatchId'
            }
            const [lovData, attachmentData] = await Promise.all([
                this.$lov.getLovByParentTypeAndValue({
                    type: 'TMPL_SUB_BIZ_TYPE',
                    parentType: 'LNK_AUTO_TEMPLATE_TYPE',
                    parentVal: 'HeadFeedbackData',
                }).then(data => data.filter(i => lovVals.indexOf(i.val) > -1)),
                this.$http.post('action/link/attachment/queryByExamplePage', {
                    sort: 'created',
                    order: 'desc',
                    headId: this.registerItemData.id,
                    rows: 2000,
                    // moduleType: this.momoduleType,
                    queryFields: 'id,uploadType,attachmentPath,moduleType,headId,dataSource,created,smallurl'
                }).then(i => i.rows)
            ])
            console.log('lovData', lovData);
            //获取腾讯云上的图片
            const groupedData = lovData.reduce((prev, lov) => {
                prev.push({
                    ...lov,
                    data: attachmentData.filter(i => i.moduleType === lov.val)
                })
                return prev
            }, []);
            console.log('groupedData', groupedData);
            this.dataList = await Promise.all(groupedData.map(async group => {
                return {
                    ...group,
                    data: await Promise.all(group.data.map(async i => ({
                        ...i,
                        imgUrl: await this.$image.getSignedUrl(i.attachmentPath),
                    })))
                }
            }));
        },

        /**
         * 照片预览
         * <AUTHOR>
         * @date 2023-06-15
         * @param param
         */
        previewStoreUrl(dataList, param) {
            let imgList = [];
            // 获取所有图片
            for (let item of dataList) {
                imgList = [
                    ...imgList,
                    ...item.data
                ]
            }
            // 使用 filter 过滤掉 dataItem 或 dataItem.imgUrl 为空的项
            const imgUrls = imgList
                .filter(dataItem => dataItem && dataItem.imgUrl)
                .map(dataItem => dataItem.imgUrl);
            let index = imgUrls.findIndex(imgUrl => imgUrl === param.imgUrl);
            const inOptions = {
                current: imgUrls[index],
                urls: imgUrls
            };
            this.$image.previewImages(inOptions);
        },
        /**
         * 复制活动编码
         *  <AUTHOR>
         *  @date 2023-04-04
         */
        copyActCode(text) {
            wx.setClipboardData({data: text});
        },
    }
}
</script>

<style lang="scss">
.feedback-materials {
    margin-top: 40px;

    .feedback-materials-v {
        .menu-stair1 {
            width: 100%;
            margin-left: 24px;
            padding-top: 40px;
            @include flex-start-center;

            .line {
                clear: both;

                .line-top {
                    width: 8px;
                    height: 16px;
                    background: #3FE0E2;
                }

                .line-bottom {
                    width: 8px;
                    height: 16px;
                    background: #2F69F8;
                }
            }

            .stair-title1 {
                width: 30%;
                margin-left: 16px;
                font-family: PingFangSC-Semibold, serif;
                font-size: 32px;
                color: #262626;
                letter-spacing: 1px;
                line-height: 32px;
            }
        }
    }

    .feedback-materials-img {
        background: #FFFFFF;
        border-radius: 16px;
        margin: 24px;

        .pic-area {
            .pic-row {
                width: 100%;
                height: 315px;

                .pic-bg {
                    display: inline-block;
                    width: 100%;

                    .image {
                        width: 145px;
                        border-radius: 16px;
                    }
                }

                .data-source {
                    font-size: 24px;
                    width: 100%;
                    text-align: center;
                    line-height: 40px;
                }
            }
        }

        .pic-type {
            font-family: PingFangSC-Regular;
            line-height: 40px;
            background: #2F69F8;
            border-radius: 4px 0 16px 4px;
            font-size: 24px;
            color: #FFFFFF;
            width: auto;
            display: inline-block !important;
            padding: 0 20px;
            margin-top: 24px;
        }

        .pic-list {
            width: 100%;

            .line-title .stair-title {
                margin-left: 16px;
                font-size: 28px;
                color: #262626;
                letter-spacing: 1px;
                line-height: 28px;
            }
            .uploaded-pic {
                width: 100%;
                background-color: #FFFFFF;
                margin-top: 10px;
                padding-bottom: 10px;

                .pic {
                    width: 100%;
                    margin-top: 6px;

                    .scroll-bj {
                        width: 100%;
                        white-space: nowrap;
                        margin-left: 8px;

                        .scroll-container {
                            display: inline-block;
                            width: 100%;
                            height: 100%;
                            margin-left: 8px;

                            .pic-item {
                                display: inline-block;
                                @include direction-column;
                                -webkit-flex-shrink: 0;
                                -ms-flex-negative: 0;
                                flex-shrink: 0;
                                width: 146px;
                                margin-left: 24px;

                                .pic-bg {
                                    display: inline-block;
                                    width: 100%;

                                    .image {
                                        width: 145px;
                                        height: 144px;
                                        border-radius: 16px;
                                    }
                                }

                                .data-source {
                                    font-size: 24px;
                                    width: 100%;
                                    text-align: center;
                                    line-height: 40px;
                                }
                            }
                        }
                    }

                    .pic-container {
                        width: 100%;
                        height: 100%;
                        @include flex;
                        margin-left: 8px;
                        overflow-x: scroll;
                        overflow-y: hidden;

                        .pic-item {
                            @include flex;
                            @include direction-column;
                            -webkit-flex-shrink: 0;
                            -ms-flex-negative: 0;
                            flex-shrink: 0;
                            width: 146px;
                            margin-left: 24px;

                            .pic-bg {
                                width: 100%;

                                .image {
                                    width: 145px;
                                    height: 144px;
                                    border-radius: 16px;
                                }
                            }

                            .data-source {
                                font-size: 24px;
                                width: 100%;
                                text-align: center;
                                line-height: 40px;
                            }

                            .pic-copywriter {
                                margin-top: -28px;
                                position: relative;
                                letter-spacing: 1px;
                                font-size: 14px;
                                color: #FFFFFF;
                                line-height: 25px;
                                text-align: center;
                                white-space: nowrap;
                                background-image: linear-gradient(-180deg, rgba(0, 0, 0, 0.00) 0%, rgba(32, 61, 103, 0.40) 100%);
                            }
                        }

                        .pic-item:last-child {
                            padding-right: 16px;
                        }
                    }
                }
            }
        }

        .supernatant {
            width: 97px;
            height: 240px;
            background-image: linear-gradient(269deg, #FFFFFF 67%, rgba(255, 255, 255, 0.60) 100%);
            box-shadow: -4px 0 16px 0 rgba(38, 45, 63, 0.13);
            position: relative;
            top: -240px;
            left: 607px;
            text-align: center;

            .title {
                font-family: PingFangSC-Regular;
                font-size: 24px;
                color: #595959;
                line-height: 240px;
            }
        }

    }
}

</style>
