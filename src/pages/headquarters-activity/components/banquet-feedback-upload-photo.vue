<!--
总部活动-宴席列表详情-执行反馈-上传照片
<AUTHOR>
@date 2023-07-12
@file banquet-feedback-upload-photo
-->
<template>
    <view class="banquet-feedback-upload-photo">
        <view class="pic-v">
            <view class="item-header">
                <view class="title">{{moduleType | lov('TMPL_SUB_BIZ_TYPE')}}</view>
                <view style="color: #dd524d;float: left" v-if="required">*</view>
            </view>
            <view class="img-v">
                <lnk-img-watermark :parentId="activityId"
                                   :moduleType="moduleType"
                                   :delFlag="delFlag"
                                   :newFlag="operationFlag"
                                   :continueFlag="true"
                                   :isCount="isNoCount"
                                   :addLength="maxImgLength"
                                   :noInitImgList="isNoCount && shareFlag"
                                   :drawWatermarkCancleFlag="true"
                                   watermarkText="现场拍照"
                                   :useModuleName="useModuleName"
                                   :marketActivityFlag = "true"
                                   :moduleName="moduleName"
                                   :dataSourceShowFlag="true"
                                   :createdShowFlag="true"
                                   @imgUploadSuccess="imageArrLength"
                                   @imgDeleteSuccess="deleteImageArrLength"
                                   :album="false"
                                   ref="img"
                                   v-if="refreshFlag && !isNoPhoto">
                </lnk-img-watermark>
                <view class="is-no-photo" v-else>
                    免拍
                </view>
                <view style="width: 100%;height: 118px" v-if="!refreshFlag"></view>
            </view>
        </view>
    </view>
</template>

<script>
import lnkImgWatermark from "../../core/lnk-img-watermark/lnk-img-watermark.vue";
export default {
    name: 'banquet-feedback-upload-photo',
    components: {lnkImgWatermark},
    props: {
        required: {
            type: Boolean,
        },
        activityId: {
            type: String,
        },
        moduleType: {
            type: String,
        },
        cameraRefresh: {
            type: Boolean,
            default: false
        },
        useModuleName: {
            type: String,
        },
        moduleName: {
            type: String,
        },
        operationFlag: {
            type: Boolean,
        },
        shareFlag: {
            type: Boolean,
        },
        isNoPhoto: {
            type: Boolean // 是否免拍
        },
        isNoCount: {
            type: Boolean // 是否限制图片最多上传
        },
        maxImgLength: {
            type: Number // 图片最多上传张数
        }
    },
    data() {
        return {
            refreshFlag:true,
            delAllFlag:false,
            delFlag:true,
            imgLength:0,
            parentId: '',
            once: 0
        }
    },
    async created() {
        if(this.shareFlag && !this.isNoCount) {
            this.delFlag = false;
        }
    },
    methods: {
        refresh(){
            this.refreshFlag =  !this.refreshFlag;
            setTimeout(()=>{
                this.once=0;
                this.refreshFlag =  !this.refreshFlag;
            },100)
        },
        updatePic(All){
            if(this.$utils.isNotEmpty(All)){
                if (All==='delAll'){
                    this.delAllFlag = true;
                }
            }
            this.refresh();
        },
        imageArrLength(param) {
            if(!this.$utils.isEmpty(param)){
                this.imgLength = param.length;
            }else{
                this.imgLength = 0;
            }
            if(this.delAllFlag){
                // this.showInvolved = true;
                this.delAllFlag = false;
            }
        },
        deleteImageArrLength(imgList,ALL){
            if(this.$utils.isNotEmpty(ALL)){
                if (ALL==='delAll'){
                    this.$emit('repeatFun',this.moduleType,this._uid,ALL);
                }
            }else{
                this.$emit('repeatFun',this.moduleType,this._uid)
            }
        },
    }
}
</script>

<style lang="scss">
.banquet-feedback-upload-photo {
    .pic-v {
        background: white;
        .item-header {
            height: 88px;
            padding-left: 32px;
            font-size: 28px;
            line-height: 88px;
            color: #262626;
            letter-spacing: 0;
            border-bottom: 2px solid #F2F2F2;

            .title {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #595959;
                letter-spacing: 0;
                line-height: 88px;
                float: left;
                margin-right: 20px
            }
        }

        .img-v {
            padding-bottom: 24px;
        }
    }
    .is-no-photo {
        width: 145px;
        height: 144px;
        line-height: 144px;
        border-radius: 16px;
        margin-left: 32px;
        margin-top: 24px;
        background-color: #f2f2f2;
        text-align: center;
        font-size: 36px;
        color: #5e5e5e;
    }
}
</style>
