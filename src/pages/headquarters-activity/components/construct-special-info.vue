<template>
	<view class="construct-special-info">
		<view class="basic-info-title">
			<view class="left">
				<line-title title="基础信息" class="head-title"></line-title>
			</view>
			<view class="right edit-text" @tap="goEditBasicInfo" v-if="canEdit&&!isApprove&&(item.actConstructionType === 'IntegratedTerminalConstruction' ? addIn : addOver)">编辑</view>
		</view>

		<view class="basic-info">

			<view>
				<view class="val"></view>
				<view class="block-v">
					<view class="title">活动主题</view>
					<view class="val" @longPress="copyActCode(item.actType)">
						{{item.actType | lov('HEAD_ACT_TYPE')}}
					</view>
				</view>
				<view class="block-v">
					<view class="title">活动类型</view>
					<view class="val"> {{item.actConstructionType | lov('HEAD_ACT_CON_TYPE')}}</view>
				</view>
				<view class="block-v">
					<view class="title">活动开始时间</view>
					<view class="val"> {{item.startTime}} </view>
				</view>
				<view class="block-v">
					<view class="title">活动结束时间</view>
					<view class="val"> {{item.endTime}} </view>
				</view>
				<view class="block-v">
					<view class="title">活动状态</view>
					<view class="val"> {{item.actStatus | lov('HEAD_ACT_STATUS')}} </view>
				</view>
				<view class="block-v">
					<view class="title">审批状态</view>
					<view class="val"> {{item.approveStatus | lov('HEAD_APRO_STATUS')}}</view>
				</view>
				<view class="block-v">
					<view class="title">提报人</view>
					<view class="val"> {{item.applyName ? item.applyName : '暂无数据'}} </view>
				</view>
				<view class="block-v">
					<view class="title">所属城市</view>
					<view class="val"> {{item.cityName ? item.cityName : '暂无数据'}} </view>
				</view>
				<view class="block-v" v-show="item.actConstructionType === 'IntegratedTerminalConstruction'">
					<view class="title">建设类型</view>
					<view class="val"> {{item.actConstructionSubtype | lov('HEAD_ACT_CON_SUBTYPE')}} </view>
				</view>

				<!-- <view class="block-v">
					<view class="title">门头制作日期</view>
					<view class="val"> {{item.signDate}} </view>
				</view> -->
				<view class="approve-input" v-show="!isApprove">
					<view class="title">申请费用金额（元）</view>
					<view class="val">
						{{item.applyAmount}}
					</view>
				</view>
				<view class="approve-input" v-show="isApprove">
					<view class="title">申请费用金额（元）</view>
					<view>
						<link-number v-model="item.applyAmounts" :min="0"  hideButton :precision='2' />
					</view>
				</view>
				<view class="block-v">
					<view class="title">申请终端数量</view>
					<view class="val"> {{item.terminalNumber ? item.terminalNumber : '暂无数据'}} </view>
				</view>
				<view class="block-v">
					<view class="title">费用申请说明</view>
					<view class="val"> {{item.remark}} </view>
				</view>
				<view class="block-v" v-if="fromApprove">
					<view class="title">备注说明</view>
					<view class="val"> {{item.approveRemark ? item.approveRemark : '自动生成，无需填写'}} </view>
					<!-- <link-textarea placeholder="自动生成，无需填写" padding-start padding-end v-model="item.approveRemark" disabled mode='textarea'></link-textarea> -->
				</view>
                <!-- <view class="approve-textArea">
                    <view class="title">备注说明</view>
                    <view class="val"> {{item.approveRemark}} </view>
                </view> -->
			</view>
			<view style="width: 100%;height: 8px"></view>
		</view>
	</view>
</template>

<script>
	import LineTitle from "../../lzlj/components/line-title.vue";
	import Taro from "@tarojs/taro";
	import {getFeature, getSecurityFeatures, parseFeature} from "../../../utils/security";
	export default {
		name:'construct-special-info',
		components:{
			LineTitle,
		},
		data(){
			return {
				addOver:false,
				addIn:false
			}
		},
		props:{
			item:{
				type:Object,
				default:()=>{
					return {}
				}
			},
			terminal:{
				type:Array,
				default:()=>{
					return []
				}
			},
			canEdit:{
				type:Boolean,
			},
			isApprove:{
				type:Boolean,
				default:false
			},
			fromApprove:{
				type:Boolean,
				default:false
			},
		},
		async created(){
			this.features = getSecurityFeatures('ALL', '/pages/headquarters-activity/construct-special-list-page')
			this.addOver = this.getSecurityFlag('ADD_CONSTRUCTOVER')
			this.addIn = this.getSecurityFlag('ADD_CONSTRUCTIN')
		},
		methods:{
            /**
             * 根据编码获取对应的值列表或表达式结果
             * <AUTHOR>
             * @date 2023-08-28
             */
            getSecurityFlag(code) {
                const feature = getFeature(this.features, code)
                const exp = parseFeature(feature)
                return exp
            },
			/**
			 * 跳转至保存
			 * <AUTHOR>
			 * @date 2023-08-13
			 * @param param
			 */
			goEditBasicInfo(){
				const val = this.item.actConstructionType === 'IntegratedTerminalConstruction' ? 2 : 1
				this.$emit('goEditInfo',{val,data:this.item})
			}
		}
	}
</script>

<style lang="scss">
	@mixin title-label {
		font-family: PingFangSC-Regular;
		font-size: 28px;
		color: #8C8C8C;
		letter-spacing: 0;
		line-height: 60px;
		width: 35%;
		float: left;
	}
	.construct-special-info{
		.basic-info-title {
			display: flex;
			justify-content: space-between;
			margin: 0 24px 0 0;
			.right {
				font-family: PingFangSC-Regular serif;
				font-size: 28px;
				color: #2F69F8;
				letter-spacing: 0;
				text-align: right;
				line-height: 28px;
				align-self: flex-end;
			}
		}
		.terminal-checkbox{
			margin-top: 60px;
			font-size: 24px;
			@include flex-center-center;
		}
				.terminal-title {
					@include flex;
					@include space-between;
					background-color: white;
					margin: 10px;
					padding: 35px;
					font-size: 24px;
					&-add {
						font-size: 32px;
						color: #2f69f8;
					}
				}
				.edit-text{
						text-align: right;
						font-size: 32px;
						color: #2f69f8;
					}
				.basic-info {
					clear: both;
					background: #FFFFFF;
					border-radius: 16px;
					margin: 24px;

					.approve-input{
						@include flex;
						@include space-between;
						padding: 0 24px;
						.title {
							@include title-label;
							width: 50%;
						}
						.val {
							font-family: PingFangSC-Regular;
							font-size: 28px;
							color: #262626;
							letter-spacing: 0;
							text-align: right;
							line-height: 60px;
							width: 65%;
							float: left;
							text-overflow: ellipsis;
						}

					}
					.approve-textArea{
						.title {
							@include title-label;
							padding: 0 24px;
						}
                        .val {
                        	font-family: PingFangSC-Regular;
                        	font-size: 28px;
                        	color: #262626;
                        	letter-spacing: 0;
                        	text-align: right;
                        	line-height: 60px;
                        	// width: 65%;
                        	float: left;
                        	text-overflow: ellipsis;
                        }
					}
					.block-v {
						padding: 0 24px;
						display: flex;

						.title {
							@include title-label
						}

						.val {
							font-family: PingFangSC-Regular;
							font-size: 28px;
							color: #262626;
							letter-spacing: 0;
							word-break: break-all;
							text-align: right;
							line-height: 60px;
							width: 65%;
							float: left;
							text-overflow: ellipsis;
						}

						.val-1 {
							font-family: PingFangSC-Regular;
							font-size: 28px;
							color: #262626;
							letter-spacing: 0;
							text-align: right;
							line-height: 60px;
							float: left;
							text-overflow: ellipsis;
							width: 70%;
						}

						.scan-code {
							color: #262626;
							width: 5%;
							text-align: right;
							font-size: 28px;
							padding-top: 8px;
						}
					}

					.line {
						margin: 32px 24px 32px 24px;
						height: 0;
						border: 2px dashed #DADEE9;
					}
				}

	}
</style>
