<!--
总部活动-宴席列表详情-执行反馈组件-展示
<AUTHOR>
@date 2023-06-29
@file banquet-feedback
-->
<template>
    <view class="banquet-feedback">
        <line-title title="执行反馈" class="head-title"></line-title>
        <view class="feedback-info">
            <view style="width: 100%;height: 8px"></view>
            <view>
                <view class="val"></view>
                <view class="block-v">
                    <view class="title">实际宴席桌数</view>
                    <view class="val">{{ (item.actTableNum!== null&&item.actTableNum!==undefined) ? item.actTableNum : '暂无数据'}}</view>
                </view>
                <view class="block-v">
                    <view class="title">每桌奖励费用</view>
                    <view class="val">{{ (item.actTableReward!== null &&item.actTableReward!==undefined)? item.actTableReward : '暂无数据'}}</view>
                </view>
                <view class="block-v">
                    <view class="title">实际推荐费总额</view>
                    <view class="val">{{ (item.banquetActFee!== null&&item.banquetActFee!==undefined)? item.banquetActFee : '暂无数据'}}</view>
                </view>
                <view class="block-v">
                    <view class="title">推荐费限额</view>
                    
                    <view class="val">{{ (item.banquetPlanFee!== null &&item.banquetPlanFee!== undefined) ? item.banquetPlanFee : '暂无数据'}}</view>
                </view>
                <view class="fee-rule-desc">
                    <view class="title">推荐费规则说明</view>
                    <link-textarea v-model="item.feeRuleDesc" placeholder="暂无数据" disabled/>
                </view>
                <!-- 终端出货数量、终端退货、消费者开瓶，开瓶率-->
                <view class="block-v">
                    <view class="title">终端出货数量</view>
                    <view class="val">{{ item.salesoutNum !== null && item.salesoutNum !== undefined ? (item.salesoutNum === 0 ? '0瓶' : item.salesoutNum + '瓶') : '暂无数据' }}</view>
                </view>
                <view class="block-v">
                    <view class="title">终端退货</view>
                    <view class="val">{{item.consalesinNum !== null && item.consalesinNum !== undefined
                                ? (item.consalesinNum === 0 ? '0瓶' : item.consalesinNum + '瓶')
                                : '暂无数据' }}</view>
                </view>
                <view class="block-v">
                    <view class="title">消费者开瓶</view>
                    <view class="val">{{ item.consumerCrackNum !== null && item.consumerCrackNum !== undefined ? (item.consumerCrackNum === 0 ? '0瓶' : item.consumerCrackNum + '瓶') : '暂无数据' }}</view>
                </view>
                <view class="block-v">
                    <view class="title">开瓶率</view>
                    <view class="val">{{item.consumerCrackRatio ? item.consumerCrackRatio : '暂无数据'}}</view>
                </view>
            </view>

            <!-- 执行反馈扫码明细 -->
            <banquet-feedback-scan-code disabled :activityId="banquetItem.id" ref="scanCodeShow" v-if="banquetItem.id"></banquet-feedback-scan-code>
            <!-- 执行反馈图片  -->
            <view class="feedback-materials-img">
                <view style="width: 100%;height: 8px"></view>
                <!--图片模板无权限-->
                <lnk-no-auth v-if="imgAuthFlag"></lnk-no-auth>
                <view v-if="!imgAuthFlag">
                    <view v-for="(group) in dataList" :key="group.name" class="upload-1">
                        <view>
                            <view class="pic-list">
                                <line-title :title="group.name" class="head-title"></line-title>
                                <view class="uploaded-pic">
                                    <view class="pic">
                                        <view class="pic-container">
                                            <template v-if="group.data.length">
                                                <view class="pic-item" v-for="(item, index) in group.data" :key="index">
                                                    <view class="pic-bg">
                                                        <image :src="item.imgUrl" class="image" @tap.stop="previewStoreUrl(item, group.data)"/>
                                                    </view>
                                                    <view class="data-source">{{ item.dataSource | lov('PIC_SOURCE') }}</view>
                                                    <view class="data-source" style="font-size: 11px;line-height: 12px">
                                                        {{ item.created | date('YYYY-MM-DD HH:mm:ss') }}
                                                    </view>
                                                </view>
                                            </template>
                                        </view>
                                    </view>
                                    <view v-if="!group.data.length" class="no-image">暂无图片</view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view style="width: 100%;height: 8px"></view>
                </view>
            </view>
        <!--执行反馈备注-->
            <view class="feedbackRemark" style="padding: 6px 12px 12px">
                <view class="label" style="font-size: 14px; padding: 0px 0px 12px 4px; color: #8C8C8C;">备注</view>
                <view class="textarea-big">
                    <link-textarea class="text-area"
                                   mode="textarea"
                                   v-model="item.feedbackRemark"
                                   :nativeProps="{maxlength:500}"
                                   style="padding: 0px"
                                   disabled
                                   placeholder-style="color: #BFBFBF;">
                    </link-textarea>
                </view>
            </view>

        </view>
    </view>
</template>

<script>
import LineTitle from "../../lzlj/components/line-title.vue";
import BanquetFeedbackScanCode from "./banquet-feedback-scan-code.vue";

export default {
    name: 'banquet-feedback',
    components: {BanquetFeedbackScanCode, LineTitle},
    props: {
        banquetItem: {
            type: Object,
            default: function () {
                return {};
            }
        },
        flowObjId: {
            type: String
        },
        regReqList: {
            type: Array,
        },
        data: {
            type: Object,
        },
        banquetId: {
            type: [String]
        }
    },
    data() {
        return {
            item: {},
            imgAuthFlag: false, //图片模板无权限
            dataList: [],   //所有图片列表
        }
    },
    async created() {
        await this.$scene.ready();
        console.log('子组件created ready', this.banquetItem, this.banquetId)
        if (this.banquetId) {
            this.banquetItem.id = this.banquetId
        }
        this.item = {...this.banquetItem};
        if(this.flowObjId) {
            this.banquetItem.id = this.flowObjId;
        };
        if(this.regReqList) {
            this.banquetItem.regReqList = this.regReqList;
        };
        if(this.data) {
            this.item = {...this.data}; //执行反馈审批的基础数据
        };
        await this.queryBasicTerInfo();
        await this.queryFeedbackPicList();
        this.$bus.$on('refreshFeedbackInfo', async () => {
            await this.queryBasicInfo();
        });
        this.$bus.$on('refreshBanquetPhoto', async () => {
            await this.queryFeedbackPicList();
        });
        this.$bus.$on('refreshScanCode', async () => {
            await this.queryScanCode();
        });
    },
    methods: {
        /**
         * 查询终端出货数量、终端退货、消费者开瓶，开瓶率信息
         *  <AUTHOR>
         *  @date 2023-08-03
         **/
        async queryBasicTerInfo() {
            try {
                const data = await this.$http.post('action/link/headquarterFeedback/queryById', {
                    id: this.banquetItem.id
                })
                if (data.success) {
                    const consumerCrackRatio = data.result.consumerCrackRatio;
                    const consumerCrackRatioPercentage = (consumerCrackRatio || consumerCrackRatio === 0)
                        ? (consumerCrackRatio === 0 ? '0%' : (consumerCrackRatio * 100).toFixed(0) + '%')
                        : '';
                    this.$set(this.item, 'salesoutNum', data.result.salesoutNum);
                    this.$set(this.item, 'consalesinNum', data.result.consalesinNum);
                    this.$set(this.item, 'consumerCrackNum', data.result.consumerCrackNum);
                    this.$set(this.item, 'consumerCrackRatio', consumerCrackRatioPercentage);
                } else {
                    this.$message.warn("查询终端进出货等数据失败，请稍后重试！");
                }
            } catch (e) {
                this.$message.warn("查询终端进出货等数据失败，请稍后重试！");
            }
        },
        /**
         * 查询信息
         *  <AUTHOR>
         *  @date 2023-07-05
         **/
        async queryBasicInfo() {
            try {
                const data = await this.$http.post('action/link/headquarterFeedback/queryById', {
                    id: this.banquetItem.id
                })
                if (data.success) {
                    this.$set(this.item, 'actTableNum', data.result.actTableNum);
                    this.$set(this.item, 'actTableReward', data.result.actTableReward);
                    this.$set(this.item, 'banquetActFee', data.result.banquetActFee);
                    this.$set(this.item, 'banquetPlanFee', data.result.banquetPlanFee);
                    this.$set(this.item, 'feedbackRemark', data.result.feedbackRemark);
                } else {
                    this.$message.warn("查询该活动失败，请稍后重试！");
                }
            } catch (e) {
                this.$message.warn("查询该活动失败，请稍后重试！");
            }
        },
        /**
         *  @desc 查询当前扫码信息
         *  <AUTHOR>
         *  @date 2023-07-14
         */
        async queryScanCode() {
            this.$refs.scanCodeShow.reload();
        },
        /**
         *  @desc 查询当前反馈材料的图片
         *  <AUTHOR>
         *  @date 2023-07-14
         */
        async queryFeedbackPicList() {
            const lovVals = this.banquetItem.regReqList;
            if (!lovVals || lovVals.length === 0) {
                return;
            }
            if (this.$utils.isEmpty(this.banquetItem.id)) {
                this.banquetItem.id = 'noMatchId'
            }
            const [lovData, attachmentData] = await Promise.all([
                this.$lov.getLovByParentTypeAndValue({
                    type: 'TMPL_SUB_BIZ_TYPE',
                    parentType: 'LNK_AUTO_TEMPLATE_TYPE',
                    parentVal: 'HeadFeedbackData',
                }).then(data => data.filter(i => lovVals.indexOf(i.val) > -1)),
                this.$http.post('action/link/attachment/queryByExamplePage', {
                    sort: 'created',
                    order: 'desc',
                    headId: this.banquetItem.id,
                    rows: 2000,
                    // moduleType: this.momoduleType,
                    queryFields: 'id,uploadType,attachmentPath,moduleType,headId,dataSource,created,smallurl'
                }).then(i => i.rows)
            ])
            //获取腾讯云上的图片
            const groupedData = lovData.reduce((prev, lov) => {
                prev.push({
                    ...lov,
                    data: attachmentData.filter(i => i.moduleType === lov.val)
                })
                return prev
            }, []);
            this.dataList = await Promise.all(groupedData.map(async group => {
                return {
                    ...group,
                    data: await Promise.all(group.data.map(async i => ({
                        ...i,
                        imgUrl: await this.$image.getSignedUrl(i.attachmentPath),
                    })))
                }
            }));
        },
        /**
         * 照片预览
         * <AUTHOR>
         * @date 2023-07-14
         * @param param
         * @lastUpdated by lld 241217:左右滑动查看图片，传入图片数组
         */
         async previewStoreUrl (item, imgList) {
            const imgs = imgList.map(img => img.imgUrl);
            const inOptions = {
                current: item.imgUrl,
                urls: imgs
            };
            this.$image.previewImages(inOptions);
        },
    }
}
</script>

<style lang="scss">
.banquet-feedback {
    .feedback-info {
        background: #FFFFFF;
        border-radius: 16px;
        margin: 24px;

        .block-v {
            padding: 0 24px;
            display: flex;

            .title {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                line-height: 60px;
                width: 35%;
                float: left;
            }

            .val {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #262626;
                letter-spacing: 0;
                text-align: right;
                line-height: 60px;
                width: 65%;
                float: left;
                text-overflow: ellipsis;
            }
            .val-1 {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #262626;
                letter-spacing: 0;
                text-align: right;
                line-height: 60px;
                float: left;
                text-overflow: ellipsis;
                width: 70%;

            }
            .scan-code {
                color: #262626;
                width: 5%;
                text-align: right;
                font-size: 28px;
                padding-top: 8px;
            }
        }
        .fee-rule-desc {
            padding: 0 24px;
            .title {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                line-height: 60px;
                width: 35%;
                float: left;
            }
            .link-textarea {
                padding: 0;
                .link-textarea-content {
                    height: 200px !important;
                    
                }
            }
            .link-textarea-disabled {
                .link-textarea-content {
                    color: #262626;
                    background-color: #Fff;
                    font-size: 28px;
                    font-family: PingFangSC-Regular;
                    .link-textarea-placeholder-view {
                        font-family: PingFangSC-Regular;
                        color: #262626;
                        
                    }
                }
            }
        }

        .line {
            margin: 0px 24px 32px 32px;
            height: 0;
            border: 2px dashed #DADEE9;
        }
    }
    .pic-list {
        width: 100%;

        .uploaded-pic {
            background-color: #FFFFFF;
            margin-top: 10px;
            margin-left: 40px;
            padding-bottom: 10px;

            .pic {
                width: 100%;
                margin-top: 6px;

                .scroll-bj {
                    width: 100%;
                    white-space: nowrap;
                    margin-left: 8px;

                    .scroll-container {
                        display: inline-block;
                        width: 100%;
                        height: 100%;
                        margin-left: 8px;

                        .pic-item {
                            display: inline-block;
                            @include direction-column;
                            -webkit-flex-shrink: 0;
                            -ms-flex-negative: 0;
                            flex-shrink: 0;
                            width: 146px;
                            margin-left: 24px;

                            .pic-bg {
                                display: inline-block;
                                width: 100%;

                                .image {
                                    width: 145px;
                                    height: 144px;
                                    border-radius: 16px;
                                }
                            }

                            .data-source {
                                font-size: 24px;
                                width: 100%;
                                text-align: center;
                                line-height: 40px;
                            }
                        }
                    }
                }

                .pic-container {
                    width: 100%;
                    height: 100%;
                    @include flex;
                    margin-left: 8px;
                    overflow-x: scroll;
                    overflow-y: hidden;

                    .pic-item {
                        @include flex;
                        @include direction-column;
                        -webkit-flex-shrink: 0;
                        -ms-flex-negative: 0;
                        flex-shrink: 0;
                        width: 146px;
                        //height: 144px;
                        margin-left: 24px;

                        .pic-bg {
                            width: 100%;
                            //height: 144px;

                            .image {
                                width: 145px;
                                height: 144px;
                                border-radius: 16px;
                            }
                        }

                        .data-source {
                            font-size: 24px;
                            width: 100%;
                            //height: 40px;
                            text-align: center;
                            line-height: 40px;
                        }

                        .pic-copywriter {
                            margin-top: -28px;
                            position: relative;
                            letter-spacing: 1px;
                            font-size: 14px;
                            color: #FFFFFF;
                            line-height: 25px;
                            text-align: center;
                            white-space: nowrap;
                            background-image: linear-gradient(-180deg, rgba(0, 0, 0, 0.00) 0%, rgba(32, 61, 103, 0.40) 100%);
                        }
                    }

                    .pic-item:last-child {
                        padding-right: 16px;
                    }
                }
            }
            .no-image {
                width: 145px;
                height: 144px;
                border-radius: 16px;
                background-color: #f2f2f2;
                text-align: center;
                line-height: 144px;
                margin: 6px 0px 0px 16px;
                font-size: 24px;
                color: #5e5e5e;
            }

        }
    }
    .textarea-big {
        position: relative;
        .textarea-length {
            position: absolute;
            right: 0;
            bottom: 0;
            padding: 0px 20px 16px 0px;
            color: #8C8C8C;
            font-size: 28px;
        }
        .textarea-over-length {
            position: absolute;
            right: 0;
            bottom: 0;
            padding: 0px 40px 16px 0px;
            color: #ff5a5a;
            font-size: 28px;
        }
    }

}
</style>
