<!--
@created<PERSON><PERSON>  yangying
@date  2023/08/13
@description 终端名单提报-终端
-->
<template>
    <view class="terminal-list-item">
        <view class="media-list-item">
            <image class="media-list-logo" :src="data.storeUrl" lazy-load="true"></image>
            <view class="store-content">
                <view class="store-content-top" v-if="data.accountType">
                    <!--【客户一级分类】为“终端Terminal”的时候显示storeSigns字段-->
                    <view class="store-title" v-if="data.accountType === 'Terminal'">{{data.accountName}}</view>
                    <!--【客户一级分类】为“分销商Distributor”时展示billTitle字段-->
                    <view class="store-title" v-if="data.accountType === 'Distributor'">{{data.accountName || data.billTitle}}</view>
                    <!--已认证-->
                    <view class="store-level" v-if="data.accountStage === 'ykf' && !isYangShengOrYouXuan"><image :src="$imageAssets.storeStatusVerifiedImage"></image></view>
                    <!--未认证-->
                    <view class="store-level" v-if="data.accountStage === 'xk' && !isYangShengOrYouXuan"><image :src="$imageAssets.storeStatusUnverifiedImage"></image></view>
                    <!--已失效-->
                    <view class="store-level" v-if="data.accountStage === 'ysx' && !isYangShengOrYouXuan"><image :src="$imageAssets.storeStatusInvalidationImage"></image></view>
                    <!--潜客-->
                    <view class="store-level" v-if="data.accountStage === 'dkf' && !isYangShengOrYouXuan"><image :src="$imageAssets.storeStatusPotentialImage"></image></view>
                </view>
                <view class="store-content-middle">
                    <view class="left">
                        <view class="store-type" v-if="data.accountType">
                            {{data.accountType | lov('ACCT_TYPE')}}
                        </view>
                        <view class="store-type" v-if="data.accountCategory">
                            {{data.accountCategory | lov('ACCNT_CATEGORY')}}
                        </view>
                        <view class="store-type" v-if="data.accountLevel">
                            {{ data.accountLevel | lov('ACCT_LEVEL') }}
                        </view>
                        <view class="store-type" v-if="data.joinFlag && broadCompanyCode.indexOf(userInfo.coreOrganizationTile.brandCompanyCode) !== -1">
                            {{data.joinFlag | lov('JOIN_FLAG')}}
                        </view>
                        <view class="store-type" v-if="data.judgmentFlag === 'Y'">品鉴</view>
                        <view class="store-type" v-if="data.doorSigns !== undefined">{{data.doorSigns | lov('DOOR_SIGNS')}}</view>
                        <view class="store-type" v-if="data.isSpringAct === 'Y'">春雷行动</view>
                        <block v-if="data.displayPolicyType">
                            <view class="store-type"  v-for="(item, index) in data.displayPolicyType.split(',')" :key="index">{{item | lov('DISPLAY_POLICY_TYPE')}}</view>
                        </block>
                    </view>
                </view>
                <view class="store-content-representative">
                    <view class="terminal-type">编码</view>
                    <view class="terminal-name">{{data.accountCode}}</view>
                </view>
                <view class="store-content-representative">
                    <view class="terminal-type">业代</view>
                    <view class="terminal-name">{{data.salesManListString}}</view>
                </view>
                <view class="store-content-address">
                    <view class="store-address">{{data.province}}{{data.city}}{{data.district}}{{data.address}}</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'terminal-item',
    props: {
        data: Object,
        broadCompanyCode: String
    },
    data() {
        const userInfo = this.$taro.getStorageSync('token').result;
        const isYangShengOrYouXuan = ['1216', '1612'].includes(userInfo.coreOrganizationTile.brandCompanyCode);
        return {
            userInfo,
            isYangShengOrYouXuan
        }
    }
}
</script>

<style lang="scss">
.terminal-list-item {
    background: #fff;
    border-radius: 16px;

    .media-list-item {
        @include flex;
        padding: 24px 16px 24px 24px;
        .media-list-logo {
            border-radius: 16px;
            width: 128px;
            height: 128px;
            overflow: hidden;
        }
        .store-content {
            width: 80%;
            .store-content-top {
                @include flex-start-center;
                @include space-between;
                margin-left: 24px;
                .store-title {
                    font-family: PingFangSC-Semibold,serif;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 36px;
                    width: 77%;
                    height: 36px;
                    overflow: hidden;
                }
                .store-level {
                    margin-right: -3px;
                    width: 120px;
                    height: 44px;
                    image {
                        width: 100%;
                        height: 100%;
                    }
                }
            }

            .store-content-middle {
                display: flex;
                justify-content: space-between;
                padding-left: 32px;
                .left ,.right{
                    @include flex-start-center;
                    flex-wrap: wrap;
                    margin-top: 10px;
                    .store-type {
                        white-space: nowrap;
                        border: 2px solid #2F69F8;
                        border-radius: 8px;
                        font-size: 20px;
                        padding-left: 18px;
                        padding-right: 18px;
                        line-height: 40px;
                        height: 40px;
                        color: #2F69F8;
                        margin-right: 10px;
                        margin-top: 10px;
                    }
                }
            }
            .store-content-representative {
                @include flex;
                margin-left: 24px;
                margin-top: 20px;
                width: calc(100% - 24px);
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                font-size: 24px;

                .terminal-type {
                    color: #8C8C8C;
                    min-width: 50px;

                }
                .terminal-name {
                    font-family: PingFangSC-Regular,serif;
                    font-size: 24px;
                    color: #000000;
                    letter-spacing: 0;
                    padding-left: 8px;
                    width: calc(100% - 50px);
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }
            .store-content-address {
                margin-left: 24px;
                margin-top: 18px;
                font-family: PingFangSC-Regular,serif;
                font-size: 24px;
                color: #262626;
                letter-spacing: 0;
                line-height: 32px;
            }
        }
    }
}
</style>
