<!--
总部活动-投放终端详情-活动总览
<AUTHOR>
@date 2023-04-11
-->
<template>
    <view class="act-overview">
        <line-title :title="headTitle" class="head-title"></line-title>
        <view class="basic-info">
            <view class="form-title">
                <view class="form-item-title">产品名称</view>
                <view class="form-item">进货总数</view>
                <view class="form-item">使用总数</view>
                <view class="form-item">核销总数</view>
                <view class="form-item">当前库存</view>
            </view>
            <view class="form-row" v-for="(item, index) of inventoryList" :key="index">
                <view class="form-item-title">{{item.productName && item.productName.length > 20 ? item.productName.substring(0,20) + '...' : item.productName}}</view>
                <view class="form-item">{{item.inQtySum}}</view>
                <view class="form-item">{{item.usedSum}}</view>
                <view class="form-item">{{item.writeOffSum}}</view>
                <view class="form-item">{{item.inventorySum}}</view>
            </view>
        </view>
        <line-title title="其他信息" class="head-title"></line-title>
        <view class="basic-info-v">
            <view class="basic-info">
                <view style="width: 100%;height: 8px"></view>
                <view>
                    <view class="block-v">
                        <view class="title">累计参与人数</view>
                        <view class="val">{{totalData.clientSum || '暂无数据'}}</view>
                    </view>
                    <view class="block-v">
                        <view class="title">累计登记次数</view>
                        <view class="val">{{totalData.registerSum || '暂无数据'}}</view>
                    </view>
                </view>
                <view style="width: 100%;height: 8px"></view>
            </view>
        </view>
    </view>
</template>

<script>
import LineTitle from "../../lzlj/components/line-title.vue";

export default {
    name: "act-overview",
    components: {LineTitle},
    props:{
        terminalItem: {
            type: Object,
            default: function () {
                return {};
            }
        },
    },
    data() {

        return {
            inventoryList: [],  // 产品数据
            totalData: {},  // 其他信息数据
            headTitle:'',
        }
    },
    computed: {
        terminalItemData: function () {
            return this.terminalItem;
        },
    },
    created() {
        this.getInventoryData();
        this.getTotalData();
        // console.log('totalData',this.totalData);

    },
    methods:{
        /**
         * desc 获取小酒产品名称、进货数量、开瓶总数、当前库存数据
         * <AUTHOR>
         * @date 2023-04-10
         */
        async getInventoryData() {
            try {
                const data = await this.$http.post('action/link/headquarterPurchase/queryPutTerminalStockPage', {
                    terminalId: this.terminalItemData.id,
                    // attr1: 'approveTotalQty'
                })
                if(data.success) {
                    this.inventoryList = data.rows;
                    if(data.rows.length){
                        this.headTitle = `库存总数：${data.rows[0].stockSum}`;
                    }else{
                        this.headTitle = `库存总数：0`;
                    }
                } else {
                    this.headTitle = `库存总数：0`;
                    this.$showError("获取产品数据失败");
                }
            } catch (e) {
                this.headTitle = `库存总数：0`;
                this.$showError("获取产品数据失败，请稍后重试！");
            }
        },
        /**
         * desc 获取库存总数, 登记场次，累计登记人数数据
         * @returns {Promise<void>}
         * @date 2023-04-13
         */
        async getTotalData() {
            console.log('获取库存总数activityType', this.terminalItemData.activityType);
            try {
                const data = await this.$http.post('action/link/headquarterPurchase/queryTerminalStockSum', {
                    terminalId: this.terminalItemData.id,
                    activityType: this.terminalItemData.activityType,
                })
                if(data.success) {
                    this.totalData = data.rows;
                    // this.headTitle = `库存总数：${this.totalData.stockSum}`;
                } else {
                    this.$showError("获取库存总数数据失败");
                }
            } catch (e) {
                this.$showError("获取库存总数数据失败，请稍后重试！");
            }
        },

    }

}
</script>

<style lang="scss">
.act-overview {
    .basic-info {
        margin: 20px;
    }
    .form-title {
        display: flex;
        height: 90px !important;
        justify-content: space-around;
        align-items: center;
        font-family: PingFangSC-Medium;
        font-size: 22px;
        color: #333333;
        background: #c8d7fa;
        border: 1px solid rgba(230, 234, 244, 1);
        justify-content: center;
        align-items: center;
    }
    .form-row {
        display: flex;
        //justify-content: space-around;
        justify-content: center;
        align-items: center;
        font-family: PingFangSC-Regular;
        font-size: 20px;
        color: #666666;
        background: #ffffff;
        border: 1px solid rgba(230, 234, 244, 1);
    }
    .form-item-title {
        width: 236px;
        height: 100px;
        padding: 0px 22px 0px 24px;
        border-right: 1px solid rgba(230, 234, 244, 1);
        display: flex;
        justify-content: center;
        align-items: center; /* 垂直居中 */
        text-align: left;
        line-height: 32px;
    }
    .form-item {
        width: 60px;
        height: 90px;
        padding: 0px 22px 0px 24px;
        border-right: 1px solid rgba(230, 234, 244, 1);
        display: flex;
        justify-content: center;
        align-items: center; /* 垂直居中 */
        text-align: center;
    }
    .form-item:last-child {
        border-right: none;
    }
    .basic-info-v {
        .basic-info {
            background: #FFFFFF;
            border-radius: 16px;
            margin: 24px;

            .block-v {
                padding: 0 24px;
                display: flex;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 60px;
                    width: 35%;
                    float: left;
                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                    text-align: right;
                    line-height: 60px;
                    width: 65%;
                    float: left;
                    text-overflow: ellipsis;
                }
            }

            .line {
                margin: 32px 24px 32px 24px;
                height: 0;
                border: 2px dashed #DADEE9;
            }
        }
    }

}
</style>
