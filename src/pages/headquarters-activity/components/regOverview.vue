<!--
总部活动-投放终端详情-登记总览
<AUTHOR>
@date 2023-04-11
-->
<template>
    <view class="reg-overview">
        <link-auto-list :option="regOption" hideCreateButton>
            <template slot-scope="{data,index}">
                <link-swipe-action :key="index">
                    <link-swipe-option slot="option" @tap="endStage(data,index)">作废</link-swipe-option>
                    <item :key="index" :data="data" :arrow="false" class="headquarters-register-list-item"
                          @tap="gotoRegisterDetail(data)">
                        <!--        <view>-->
                        <view class="headquarters-register-list-item">
                            <view slot="note">
                                <view class="media-list">
                                    <view class="media-top">
                                        <!-- 编码 -->
                                        <view class="num-view" @longPress="copyActCode(data.feedbackCode)">
                                            <view class="num">{{ data.feedbackCode }}</view>
                                        </view>
                                        <!-- 活动状态标识 -->
                                        <status-button
                                            :label="data.status| lov('HEAD_FEEDBACK_STATUS')"></status-button>
                                    </view>
                                </view>
                                <!-- 登记名称 -->
                                <view class="content-middle">
                                    <view class="name">
                                        {{ data.feedbackName ? data.feedbackName.length > 20 ? data.feedbackName.substring(0, 20) + '...' : data.feedbackName : '' }}
                                    </view>
                                </view>
                                <view class="content-middle-line">
                                    <view class="data">
                                        <view class="title">登记人</view>
                                        <view class="val">{{ data.creator }}</view>
                                    </view>
                                    <view class="sum">
                                        <view class="title">包间号</view>
                                        <view class="val">
                                            {{ data.privateRoom ? data.privateRoom.length > 4 ? data.privateRoom.substring(0, 4) + '...' : data.privateRoom : '' }}
                                        </view>
                                    </view>
                                </view>
                                <view class="content-middle-line">
                                    <view class="data">
                                        <view class="title">活动日期</view>
                                        <view class="val">
                                            {{ data.activityDate ? data.activityDate.substr(0, 10) : data.activityDate }}
                                        </view>
                                    </view>
                                    <view class="sum">
                                        <view class="title">活动时段</view>
                                        <view class="val">{{ data.feedbackStage | lov('HEAD_FEEDBACK_TIME') }}</view>
                                    </view>
                                </view>
                                <view class="content-middle-line">
                                    <view class="data">
                                        <view class="title">开瓶数量</view>
                                        <view class="val">{{ data.productNumber }}</view>
                                    </view>
                                    <view class="sum">
                                        <view class="title">扫码数量</view>
                                        <view class="val">{{ data.scanNumber }}</view>
                                    </view>
                                </view>
                                <!--                    <view class="content-middle-line">-->
                                <!--                        <view class="data">-->
                                <!--                            <view class="title">更新时间</view>-->
                                <!--                            <view class="val">{{data.lastUpdated|date('YYYY-MM-DD')}}</view>-->
                                <!--                        </view>-->
                                <!--                    </view>-->
                            </view>
                        </view>
                    </item>
                </link-swipe-action>
            </template>
        </link-auto-list>
    </view>
</template>
<script>
import StatusButton from "../../lzlj/components/status-button";

export default {
    name: "reg-overview",
    components: {StatusButton},
    props: {
        regOption: {
            type: Object,
            default: () => {
                return {}
            }
        },
        userInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
        activityStatus: {
            type: String,
        },
        addItemFlag: {
            type: Boolean,
        },
    },
    data() {
        return {
            regReqList: [], // 登记要求列表
            editFlag: false, // 是否可编辑
        }
    },
    created() {
    },

    methods: {
        /**
         * 跳转到登记详情
         *  <AUTHOR>
         *  @date 2023-04-06
         */
        gotoRegisterDetail(data) {
            if (data.postnId === this.userInfo.postnId && data.createdBy === this.userInfo.id) {
                this.editFlag = true;
            }
            if (data.approveStatus === 'New' && this.editFlag) {
                this.$nav.push('/pages/headquarters-activity/headquarters-register-upsert-page.vue', {
                    data: data,
                    id: data.id,
                    accountId: data.terminalId,
                    feedbackId: data.id,
                    sourceFlag: 'terminalEdit',
                });
            } else {
                this.$nav.push('/pages/headquarters-activity/headquarters-register-detail-page.vue', {
                    data: data,
                    feedbackId: data.id,
                    sourceFlag: 'terminalDetail',
                    registerRequest: data.registerRequest,
                    activityStatus: this.activityStatus,
                })
            }
        },

        /**
         * 复制活动编码
         *  <AUTHOR>
         *  @date 2023-04-04
         */
        copyActCode(text) {
            wx.setClipboardData({data: text});
        },

        /**
         * desc 作废
         * <AUTHOR>
         * @date 2023-05-24
         */
        async endStage(data, index) {
            if (data.status === 'New') {
                this.$taro.showModal({
                    title: '提示',
                    content: '登记作废不可逆转，是否确定作废？',
                    success: async (res) => {
                        if (res.confirm) {
                            await this.endCurrentStage(data, index);
                        }
                    }
                });
            } else {
                this.$taro.showModal({
                    title: '提示！',
                    content: '仅新建状态下可作废！',
                    showCancel: false
                })
            }
        },
        async endCurrentStage(data, index) {
            try {
                const reg = await this.$http.post('action/link/headquarterFeedback/feedbackDiscard', {
                    id: data.id,
                })
                if (reg.success) {
                    this.$set(data, 'status', 'Inactive');
                    this.$message.success('作废成功！');
                    this.$bus.$emit('refreshTerminalItem', reg.rows);  // 刷新终端列表
                    console.log('作废');
                } else {
                    this.$message.warn('作废失败，请稍后重试！');
                }
            } catch (e) {
                this.$showError('作废失败，请稍后重试！');
            }
        }
    }
}
</script>
<style lang="scss">
@import "../../../styles/list-card";

.reg-overview {
    .link-auto-list {
        // 暂时保留，待业务确认
        //position: relative;

        //& > .link-sticky.link-sticky-top {
        //    position: absolute;
        //    bottom: calc(100% + 4px);
        //    //bottom: calc(100% + 0);
        //    top: 0 !important;
        //    z-index: 1 !important;
        //}
        .link-auto-list-wrapper {
            margin-top: 120px;
        }
    }
    .link-auto-list .link-auto-list-top-bar {
        border-bottom: solid 2px #eff1f3;
        position: relative;
        .link-search-input.link-search-input-no-padding-bottom {
            padding-bottom: 24px !important;
            position: absolute;
            width: 100%;
            //top: 0px;
        }
        .link-auto-list-query-bar {
            position: absolute;
            width: 50%;
            bottom: 0px;
            right: 0px;
            z-index: 1 !important;
        }
    }
    .link-card-list {
        padding: 0;

    }

    .link-card-list .link-swipe-option-container .link-swipe-option {
        font-size: 28px !important;
        height: 80px !important;

    }

    .link-swipe-action .link-swipe-option-container .link-swipe-option {
        margin-top: 12px;
        height: 90%;
    }

    .headquarters-register-list-item {
        background: #FFFFFF;
        width: 90%;
        margin: 24px auto auto auto;
        border-radius: 16px;

        .media-list {
            @include media-list;

            .media-top {
                width: 100%;
                @include flex-start-center;
                @include space-between;
                height: 80px;
                line-height: 80px;

                .left-content {
                    font-family: PingFangSC-Semibold;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 32px;
                    padding-top: 20px;

                }

                .right-content {
                    font-family: PingFangSC-Semibold;
                    font-size: 32px;
                    color: #FF5A5A;
                    letter-spacing: 0;
                    text-align: right;
                    line-height: 32px;
                    padding-top: 20px;
                }

                .num-view {
                    background: #A6B4C7;
                    border-radius: 8px;
                    line-height: 50px;

                    .num {
                        font-size: 28px;
                        color: #FFFFFF;
                        letter-spacing: 0;
                        line-height: 40px;
                        padding: 2px 8px;
                    }
                }

                .status-view {
                    width: 120px;
                    transform: skewX(-10deg);
                    border-radius: 4px;
                    background: #2F69F8;
                    box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                    height: 36px;

                    .status {
                        font-size: 20px;
                        color: #FFFFFF;
                        letter-spacing: 2px;
                        text-align: center;
                        line-height: 36px;
                    }
                }
            }
        }

        .content-middle {
            width: 100%;
            @include flex-start-center;
            @include space-between;
            height: 80px;
            line-height: 80px;

            .content {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
            }

            .name {
                font-family: PingFangSC-Semibold;
                font-size: 32px;
                color: #262626;
                letter-spacing: 0;
                line-height: 32px;
            }
        }

        .content-middle-line {
            width: 100%;
            height: 72px;

            .data {
                width: 58%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    width: 35%;
                    float: left;
                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                }

                .Submitted, .Feedback {
                    color: #2F69F8;
                }

                .Approve, .FeedbackApro {
                    color: #2EB3C2;
                }

                .Refused, .Refeedback {
                    color: #FF5A5A;
                }

            }

            .sum {
                width: 42%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    float: left;
                    width: 50%;
                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                    white-space: nowrap;
                }
            }

            .sum-2 {
                width: 58%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    float: left;
                    margin-right: 24px;
                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                }
            }
        }
    }

    .link-item {
        padding: 0px !important;
    }
}
</style>
