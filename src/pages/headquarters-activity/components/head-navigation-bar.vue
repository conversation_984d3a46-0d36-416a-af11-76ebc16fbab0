<template>
    <view class="ma-comp-navbar"
          :style="{height: navBarHeight + 'px', 'background-image': 'url(' + backgroundImg +')',zIndex:zIndex}">
        <!-- 占位栏 -->
        <view class="placeholder-bar" :style="{height: navBarHeight + 'px', 'padding-top': statusBarHeight/2 + 'px'}">
            <view @tap="backClick">
                <image class="navigator-back" :src="navigatorBack"></image>
            </view>
            <view class="bar-title" :style="[{color:titleColor}]">{{title}}</view>
        </view>
        <view>
            <slot></slot>
        </view>
    </view>
</template>

<script>
export default {
    props: {
        // 背景图
        backgroundImg: {
            default: null
        },
        // 导航栏背景色
        navBackgroundColor: {
            default: '#ffffff'
        },
        // 标题颜色
        titleColor: {
            default: '#000000'
        },
        // 标题文字
        title: {
            required: false,
            default: ''
        },
        // 是否显示后退按钮
        backVisible: {
            required: false,
            default: false
        },
        // home按钮的路径
        homePath: {
            required: false,
            default: ''
        },
        // 自定义返回方法
        udf: {
            type: Function,
            default: null,
        },
        //
        zIndex: {
            required: false,
            default: ''
        }
    },
    data() {
        return {
            navigatorBack: `${this.$env.imageAssetPath}/images/components/navigation-bar/left.png?${Math.random() / 9999}`,
            statusBarHeight: '', // 状态栏高度
            titleBarHeight: '', // 标题栏高度
            navBarHeight: '', // 导航栏总高度
        }
    },
    created() {
        const self = this
        wx.getSystemInfo({
            success(system) {
                self.statusBarHeight = system.statusBarHeight
                self.platform = system.platform
                let platformReg = /ios/i
                if (platformReg.test(system.platform)) {
                    self.titleBarHeight = 44
                } else {
                    self.titleBarHeight = 48
                }
                self.navBarHeight = self.statusBarHeight + self.titleBarHeight
            }
        })
    },
    methods: {
        backClick() {
            // eslint-disable-next-line
            if (getCurrentPages().length === 1) {
                // 打开分享卡片无法回退
                wx.redirectTo({
                    url: this.homePath
                })
            } else {
                if (this.udf) {
                    this.udf();
                } else {
                    wx.navigateBack({
                        delta: 1
                    })
                }
            }
        },
        homeClick() {
            wx.redirectTo({
                url: this.homePath
            })
        }
    }
}
</script>

<style lang="scss">
.ma-comp-navbar {
    position: sticky;
    top: 0;
    width: 100vw;

    .placeholder-bar {
        background-color: transparent;
        width: 100%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: start;
        -ms-flex-pack: start;
        justify-content: flex-start;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;

        .icon-left {
            width: 10%;
            font-size: 28px;
            color: #FFFFFF;
            padding-left: 24px;
        }

        .navigator-back {
            width: 46px;
            height: 46px;
            padding-left: 24px;
        }

        .bar-title {
            width: 82%;
            font-size: 32px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align: center;
        }
    }
}
</style>
