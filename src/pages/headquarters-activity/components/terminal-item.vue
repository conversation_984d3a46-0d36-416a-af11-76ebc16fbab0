<!--
总部活动-百城阵地形象建设-终端
<AUTHOR>
@date 2023-08-03
-->
<template>
	<view class="construct-terminal-list">
		<view class="construct-terminal-list-image">
			<image :src="item.storeUrl" @tap.stop="previewStoreUrl(item.storeUrl)" lazy-load="true"></image>
		</view>
		<view class="construct-terminal-list-content">
			<view class="construct-terminal-list-head"> 
				<view class="construct-terminal-list-content-title">{{item.acctName}} </view>
				<view class="construct-terminal-list-status">
					<!--已认证-->
					<view class="store-level" v-if="item.acctStage === 'Y'"><image :src="$imageAssets.storeStatusVerifiedImage"></image></view>
					<!--未认证-->
					<view class="store-level" v-else-if="item.acctStage === 'xk'"><image :src="$imageAssets.storeStatusUnverifiedImage"></image></view>
					<!--已失效-->
					<view class="store-level" v-else-if="item.acctStage === 'ysx'"><image :src="$imageAssets.storeStatusInvalidationImage"></image></view>
					<!--潜客-->
					<view class="store-level" v-else-if="item.acctStage === 'dkf'"><image :src="$imageAssets.storeStatusPotentialImage"></image></view>		
					<view class="store-level" v-else><image :src="$imageAssets.storeStatusVerifiedImage"></image></view>	
				</view>
					
			</view>
			<view class="construct-terminal-list-content-level" v-show="!hideThree&&item.acctLevel">
				<view class="store-type" v-for="(data,key) in ((item.acctLevel || '').split('|'))" :key="key+'type'">{{data}}</view>	
				<view class="store-type" v-if="item.strategicFlag">战略零售商</view>		
			</view>
			<view class="construct-terminal-list-content-level" v-show="hideThree">
				<view class="store-type" v-if="item.strategicFlag">战略零售商</view>
			</view>
			<view>
				<text>编码：</text>{{item.acctCode}}
			</view>
			<view class="construct-terminal-list-content-yd">
				<text>业代：</text>{{item.allEmp}}
			</view>
			<view>
				<text>店招门头：</text>{{item.doorSings | lov('DOOR_SIGNS')}}
			</view>
			<view>
				<text>税号：</text>{{item.creditNo}}
			</view>
			<view class="construct-terminal-list-isF">
				<view style="margin-left: -10rpx;">
					<text>是否连锁终端：</text>{{item.chainStoreFlag | lov('IS_FLAG')}}
				</view>
				<view><slot name='chose'/></view>
				
			</view>
			<view class="construct-terminal-list-content-yd">{{item.addrDetailAddr}}</view>
			<view style="color:#2f69f8" v-show="hideThree && item.agreementId" @tap=previewAgree(item.agreementId)>查看终端门头协议</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:'terminal-item',
		data() {
			return {}
		},
		props:{
			item:{
				type:Object,
				default:()=>{
					return {}
				}
			},
			hideThree:{
				type:Boolean,
				default:false
			}
		},
		methods:{
			/**
			 * 协议跳转
			 * <AUTHOR>
			 * @date 2023-08-16
			 * @param param
			 */
			async previewAgree(id){
				this.$nav.push('/pages/terminal/terminal/history-agreement-detail-page', {
                    data:{id,},
                    readonlyFlag: true
                });	
			},
			/**
			 * 图片查看
			 * <AUTHOR>
			 * @date 2023-08-16
			 * @param param
			 */
			previewStoreUrl(url){
				console.log('dianji',url)
			const inOptions = {
                current: url,
                urls: [url]
            };
            this.$image.previewImages(inOptions);
			}
		}
	}
</script>

<style lang="scss">
		.construct-terminal-list{
			overflow: hidden;
			// width: 100%;
			@include flex;
			@include space-between;
			// justify-content: space-around;
			background-color:white;
			margin:20px;
			padding:15px;
			border-radius:16px;
			&-image{
				width: 20%;
				image{
					border-radius: 16px;
					width: 128px;
					height: 128px;
					overflow: hidden;
				}
			}
			&-content{
				width: 75%;
				margin-left: 40px;
				font-size: 24px;
				color: #000000;
				view{
					font-family: PingFangSC-Regular,serif;
					letter-spacing: 0;
					padding-left: 8px;
					margin: 5px 0;
					// width: calc(100% - 50px);
					// overflow: hidden;
					// white-space: nowrap;
					// text-overflow: ellipsis;	
				}
				&-title{
		            font-family: PingFangSC-Semibold,serif;
		            font-size: 32px;
		            color: #262626;
		            letter-spacing: 0;
		            line-height: 36px;
		            width: 65%;
		            height: 36px;
		            overflow: hidden;
				}
				&-level{
					@include flex;
					flex-wrap: wrap;
					padding: 5px 0;
					color: #2f69f8;
					// width: calc(100% - 50px);
					// overflow: hidden;
					
					// white-space: nowrap;
					// text-overflow: ellipsis;	
					.store-type {
                                white-space: nowrap;
                                border: 2px solid #2F69F8;
                                border-radius: 8px;
                                font-size: 20px;
                                padding-left: 18px;
                                padding-right: 18px;
                                line-height: 40px;
                                height: 40px;
                                color: #2F69F8;
                                margin-right: 10px;
                                margin-top: 10px;
                            }
				}
				&-yd{
					padding: 5px 0;
					width: calc(80% - 65px);
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;	
				}
				text{
					color: #8C8C8C;
					min-width: 50px;
				}
			}
			&-head{
				@include flex;
				@include space-between;
				align-items: center;
				width: 98%
			}
			&-isF{
				@include flex;
				@include space-between;
				align-items: center;
				// width: 70%;
			}
			&-status{
				width:30%;
				.store-level {
					float: right;
				    width: 120px;
				    height: 44px;
				    image {
				        width: 100%;
				        height: 100%;
				    }
				}
			}
		}
</style>
