<!--
 * @Author: <EMAIL>
 * @Date: 2024-07-24 15:54:56
 * @Description:
 * @LastEditors: dengjialiu
 * @LastEditTime: 2024-07-26 15:07:18
-->
<template>
	<view class="info-title">
		<image v-if="showBg" :src="$imageAssets.cateringTitlebg" class="info-title-bg"></image>
		<view class="info-title-content">{{ data }}</view>
	</view>
</template>

<script>
	export default {
		name: 'info-title',
		props: {
			data: {
				type: String,
				require: false,
				default: '',
			},
			showBg: {
				type: Boolean,
				require: false,
				default: false,
			},
		},
		data() {
			return {};
		},
	};
</script>

<style lang="scss">
	.info-title {
		padding: 24px 24px 8px;
		background: linear-gradient(180deg, rgba(174, 201, 255, 0.32) 0%, rgba(174, 204, 255, 0) 100%);
		position: relative;
		.info-title-content {
			font-family: PingFangSC, PingFang SC;
			font-weight: 600;
			font-size: 28px;
			color: #333333;
			line-height: 40px;
			position: relative;
			z-index: 99;
		}
		.info-title-bg {
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			z-index: 9;
			border-radius: 16px 16px 0px 0px;
		}
	}
</style>
