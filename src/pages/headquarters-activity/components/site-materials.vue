<!--
总部活动-登记列表-新增-现场取证材料
<AUTHOR>
@date 2023-04-07
-->
<template>
    <view class="site-materials">
        <view class="pic-v">
            <view class="item-header">
                <!-- 小标题-->
                <view class="title">{{moduleType=='banquetTableNum'? tableNum:moduleType | lov('TMPL_SUB_BIZ_TYPE')}}</view>
                <view style="color: #dd524d;float: left" v-if="required">*</view>
<!--                <view class="iconfont icon-info-circle" style="float: right;margin-right: 10px;font-size: 14px;color: #8C8C8C;"-->
<!--                      v-if="$utils.isNotEmpty(valuesPlaceholder)||$utils.isNotEmpty(basePlaceholder)" @tap="showPlaceholder"></view>-->
            </view>
            <view class="img-v">
                <lnk-img-watermark :parentId="activityId"
                                   :moduleType="moduleType"
                                   :delFlag="delFlag"
                                   :newFlag="operationFlag"
                                   :continueFlag="true"
                                   :drawWatermarkCancleFlag="true"
                                   watermarkText="现场拍照"
                                   :useModuleName="useModuleName"
                                   :marketActivityFlag = "true"
                                   :moduleName="moduleName"
                                   :dataSourceShowFlag="true"
                                   :createdShowFlag="true"
                                   :aiUse="aiUse"
                                   @imgUploadSuccess="imageArrLength"
                                   @imgDeleteSuccess="deleteImageArrLength"
                                   ref="img"
                                   v-if="refreshFlag"
                                   :picTypeList="picTypeList"
                >
                </lnk-img-watermark>
                <view style="width: 100%;height: 118px" v-if="!refreshFlag"></view>
            </view>
        </view>
    </view>
</template>

<script>
import LnkImgWatermark from "../../core/lnk-img-watermark/lnk-img-watermark.vue";

export default {
    name: "site-materials",
    components: {
        LnkImgWatermark
    },
    props: {
        required: {
            type: Boolean,
        },
        activityId: {
            type: String,
        },
        feedbackId: {
            type: String,
        },
        cameraRefresh: {
            type: Boolean,
            default: false
        },
        moduleType: {
            type: String,
        },
        useModuleName: {
            type: String,
        },
        moduleName: {
            type: String,
        },
        operationFlag: {
            type: Boolean,
            default: false
        },
        basePlaceholder: {
            type: String,
        },
        valuesPlaceholder: {
            type: String,
        },
        picTypeList: {
            type: Array,
        },
        aiUse:{
            type: Boolean,
            default: false,
        },
        sourceFlag: {
            type: String,
            default: ''
        }
    },

    data() {
        return {
            refreshFlag:true,
            delAllFlag:false,
            delFlag:true,
            once:0,
            // picList:[],
            showDialog:false,
            imgLength:0,
            testName: '',
            parentId: '',
            tableNum: ''
        }
    },
    async created() {
        if(this.sourceFlag === 'terminalDetail' || this.sourceFlag === 'headRegDetail' || this.sourceFlag === 'forward'){
            this.delFlag = false;
        }
        if (this.moduleType === 'banquetTableNum') {
            this.tableNum = 'TableNum'
        }
    },
    methods: {
        refresh(){
            this.refreshFlag =  !this.refreshFlag;
            setTimeout(()=>{
                this.once=0;
                this.refreshFlag =  !this.refreshFlag;
            },100)
        },
        updatePic(All){
            if(this.$utils.isNotEmpty(All)){
                if (All==='delAll'){
                    this.delAllFlag = true;
                }
            }
            this.refresh();
        },

        imageArrLength(param) {
            if(!this.$utils.isEmpty(param)){
                this.imgLength = param.length;
            }else{
                this.imgLength = 0;
            }
            if(this.delAllFlag){
                // this.showInvolved = true;
                this.delAllFlag = false;
            }
        },
        deleteImageArrLength(imgList,ALL){
            if(this.$utils.isNotEmpty(ALL)){
                if (ALL==='delAll'){
                    this.$emit('repeatFun',this.moduleType,this._uid,ALL);
                }
            }else{
                this.$emit('repeatFun',this.moduleType,this._uid)
            }
        },
        showPlaceholder() {
            /*
            * 照片上传说明取值顺序
            * a:优先使用小程序模板配置的模板控件上的说明
            * b:如果a情况没有值，则使用模板控件上的说明
            * c:如果a、b情况都没有值 那么照片类型旁边的说明图标就隐藏。
            * */
            let msg = "";
            // 模板控件中的图片上传说明
            if(this.$utils.isNotEmpty(this.basePlaceholder)){
                msg = this.basePlaceholder
            }
            // 优先使用小程序模板中的图片上传说明
            if(this.$utils.isNotEmpty(this.valuesPlaceholder)){
                msg = this.valuesPlaceholder
            }
            this.$message({message: msg})
        },

    }
}
</script>

<style lang="scss">
.site-materials {
    .pic-v {
        background: white;
        .item-header {
            height: 88px;
            padding-left: 32px;
            font-size: 28px;
            line-height: 88px;
            color: #262626;
            letter-spacing: 0;
            border-bottom: 2px solid #F2F2F2;

            .title {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #595959;
                letter-spacing: 0;
                line-height: 88px;
                float: left;
                margin-right: 20px
            }
        }

        .img-v {
            padding-bottom: 24px;
        }
    }
}
</style>
