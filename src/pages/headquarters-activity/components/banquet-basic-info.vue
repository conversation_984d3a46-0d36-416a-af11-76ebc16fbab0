<!--
总部活动-宴席列表详情-基础信息
<AUTHOR>
@date 2023-06-29
@file banquet-basic-info
-->
<template>
    <view class="banquet-basic-info" :style="{'margin-top': flowObjFlag ? '0px' : '50px'}">
        <view class="basic-info-title">
            <view class="left">
                <line-title title="基础信息" class="head-title"></line-title>
            </view>
            <view class="right" v-if="editButtonFlag" @tap="editBasic">编辑</view>
        </view>
        <view class="basic-info">
            <view style="width: 100%;height: 8px"></view>
            <block v-for="(temp, index) in templateList" :key="index">
                <view class="block-v" v-if="!['feedbackCode', 'activityNum'].includes(temp.values.field)">
                    <view class="title">{{temp.base.label}}</view>
                    <view class="val" v-if="$utils.isEmpty(temp.values.lovType) && $utils.isEmpty(temp.values.cny) && $utils.isEmpty(temp.values.date)">
                        {{item[temp.values.field]}}
                    </view>
                    <view class="val" v-if="!$utils.isEmpty(temp.values.lovType)">
                        {{item[temp.values.field] | lov(`${temp.values.lovType}`)}}
                    </view>
                    <view class="val" v-if="!$utils.isEmpty(temp.values.cny)">
                        {{item[temp.values.field] | cny}}
                    </view>
                    <view class="val" v-if="!$utils.isEmpty(temp.values.date)">
                        {{item[temp.values.field] | date(`${temp.values.date}`)}}
                    </view>
                </view>
                <!-- 活动编码 -->
                <view class="block-v" v-if="temp.values.field === 'feedbackCode'">
                    <view class="title">{{temp.base.label}}</view>
                    <view class="val" @longPress="copyActCode(item.feedbackCode)">{{item.feedbackCode}}</view>
                </view>
                <!-- 政策编码 -->
                <view class="block-v" v-if="temp.values.field === 'activityNum'">
                    <view class="title">{{temp.base.label}}</view>
                    <view class="val blue" @tap="goPolicyDetail">{{item.activityNum}}</view>
                </view>
            </block>
            <!-- 宴席合伙人相关 -->
            <view class="block-v" v-if="item.isBanquetPartner">
                <view class="title">是否维护宴席合伙人</view>
                <view class="val">{{item.isBanquetPartner | lov('IF_BANQUEPARTNER')}}</view>
            </view>
            <view class="block-v" v-if="item.banquetPartner">
                <view class="title">宴席合伙人</view>
                <view class="val">{{item.banquetPartner}}</view>
            </view>
            <view class="block-v" v-if="item.partnerMobilePhone">
                <view class="title">宴席合伙人手机号</view>
                <view class="val">{{item.partnerMobilePhone}}</view>
            </view>
            <view style="width: 100%;height: 8px"></view>
        </view>
        <view v-if="nowPolicyId">
            <line-title title="政策关联的产品范围" class="head-title"></line-title>
            <link-auto-list :option="planProdOption">
                <template slot-scope="{data,index}">
                    <item :key="index" :data="data" :arrow="false" class="banquet-plan-prod-item">
                        <view slot="note">
                            <view class="content-middle-line">
                                <view class="data">
                                    <view class="title">产品编码</view>
                                    <view class="val">{{ data.productCode }}</view>
                                </view>
                            </view>
                            <view class="content-middle-line">
                                <view class="data">
                                    <view class="title">产品名称</view>
                                    <view class="val">{{ data.productName ? data.productName.substr(0,20) + '...' : ''}}</view>
                                </view>
                            </view>
                        </view>
                    </item>
                </template>
            </link-auto-list>
        </view>
    </view>
</template>

<script>
import LineTitle from "../../lzlj/components/line-title.vue";

export default {
    name: 'banquet-basic-info',
    components: {LineTitle},
    props: {
        // 宴席模板
        templateList: {
            type: Array,
            default: () => {
                return [];
            }
        },
        banquetItem: {
            type: Object,
            default: function () {
                return {};
            }
        },
        editBasicFlag: {
            type: Boolean
        },
        editButtonFlag: {
            type: Boolean
        },
        flowObjId: {
            type: String
        },
        banquetId: {
            type: [String]
        }
    },
    data() {
        // 计划用酒产品
        const planProdOption = new this.AutoList(this,{
            module: 'action/link/activityProduct',
            url: {
                queryByExamplePage: 'action/link/activityProduct/queryByExamplePage'
            },
            param: () => {
                return {
                    filtersRaw: [
                        {id: 'activityId', property: 'activityId', value: this.nowPolicyId, operator: '='},
                    ],
                }
            },
        });
        return {
            planProdOption,
            item: this.banquetItem,
            flowObjFlag: false,
            nowPolicyId: '',
        }
    },
    async created () {
        if (this.banquetId) {
            this.banquetItem.id = this.banquetId
        }
        if(!!this.flowObjId) {
            this.banquetItem.id = this.flowObjId;
            this.flowObjFlag = true;    //审批页面不显示基础信息标题
        }
        await this.queryBasicInfo();
        this.$bus.$on('refreshBanquetBasic', async () => {
            await this.queryBasicInfo();
        });
    },
    methods: {
        /**
         * 跳转政策详情
         * <AUTHOR>
         * @date	2023/12/11 18:39
         */
        async goPolicyDetail() {
            try {
                const {success, result} = await this.$http.post('action/link/headquarterActivity/queryById', {
                    id: this.item.activityId
                });
                if (success) {
                    this.$nav.push('/pages/headquarters-activity/banquet-policy-detail-page.vue', {
                        data: result,
                        source: this.flowObjFlag ? 'approve' : undefined // approve：审批页面
                    });
                }
            } catch (e) {

            }
        },
        /**
         * 查询信息
         *  <AUTHOR>
         *  @date 2023-07-05
         **/
        async queryBasicInfo() {
            try {
                const data = await this.$http.post('action/link/headquarterFeedback/queryById',{
                    id: this.banquetItem.id
                })
                if (data.success) {
                    const newMealTime = data.result.mealTime ? data.result.mealTime.slice(0, 10) : '';
                    this.item = {
                        ...data.result,
                        mealTime: newMealTime
                    };
                    this.nowPolicyId = data.result.activityId;
                    // return data.result.activityId;
                } else {
                    this.$message.warn("查询该活动失败，请稍后重试！");
                }
            } catch (e) {
                this.$message.warn("查询该活动失败，请稍后重试！" );
            }
        },
        /**
         * 编辑基础信息
         *  <AUTHOR>
         *  @date 2023-07-12
         **/
        async editBasic() {
            if(!this.editBasicFlag) {
                this.$message.warn("请确认当前宴席单状态，当前状态不可修改宴席信息！");
            } else {
                this.$nav.push('/pages/headquarters-activity/banquet-activity-apply-page.vue',{
                    editBasicFlag: this.editBasicFlag,
                    data: this.banquetItem,
                    source: 'banquetDetail' // 标记页面来源为详情，便于宴席编辑页面做判断
                })
            }
        },
        /**
         * 复制活动编码
         *  <AUTHOR>
         *  @date 2023-07-05
         */
        copyActCode(text) {
            wx.setClipboardData({data: text});
        },
    }
}
</script>

<style lang="scss">
.banquet-basic-info {
    .basic-info {
        background: #FFFFFF;
        border-radius: 16px;
        margin: 24px;

        .block-v {
            padding: 0 24px;
            display: flex;

            .title {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                line-height: 60px;
                width: 50%;
                float: left;
            }

            .val {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #262626;
                letter-spacing: 0;
                text-align: right;
                line-height: 60px;
                width: 50%;
                float: left;
                text-overflow: ellipsis;
            }

            .blue {
                color: #2F69F8;
                text-decoration: #2F69F8;
            }

            .val-1 {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #262626;
                letter-spacing: 0;
                text-align: right;
                line-height: 60px;
                float: left;
                text-overflow: ellipsis;
                width: 70%;

            }
        }

        .line {
            margin: 32px 24px 32px 24px;
            height: 0;
            border: 2px dashed #DADEE9;
        }
    }
    .basic-info-title {
        display: flex;
        justify-content: space-between;
        margin: 0 24px 0 0;
        .right{
            font-family: PingFangSC-Regular serif;
            font-size: 28px;
            color: #2F69F8;
            letter-spacing: 0;
            text-align: right;
            line-height: 28px;
            align-self: flex-end;
        }
    }
    .banquet-plan-prod-item {
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
        padding: 12px;
        .content-middle-line {
            width: 100%;

            .data {
                width: 100%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    width: 20%;
                    float: left;
                    padding-left: 6px;
                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                }

                .Submitted, .Feedback{
                    color: #2F69F8;
                }

                .Approve, .FeedbackApro{
                    color: #2EB3C2;
                }

                .Refused, .Refeedback{
                    color: #FF5A5A;
                }

            }

        }
    }
}
</style>
