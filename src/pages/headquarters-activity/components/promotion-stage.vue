<!--
总部活动-投放终端列表-推广阶段
<AUTHOR>
@date 2023-05-22
-->
<template>
    <view class="promotion-stage">
        <link-auto-list :option="stageOption">
            <template slot-scope="{data,index}">
                <link-swipe-action :key="index">
                    <link-swipe-option slot="option" @tap="endStage(data,index)">终止</link-swipe-option>
                    <item :key="index" :data="data" :arrow="false" class="media-list-rows" @tap="gotoEdit(data)">
                            <view slot="note" class="visit-list">
                                <view class="visit-content">
                                    <view class="visit-top">
                                        <view class="store-time">
                                            <view class="visit-time">
                                                <view class="stage">{{data.promotionStage | lov('PROMOTION_STAGE')}}</view>
                                                <view class="name">{{data.operator}}</view>
                                            </view>
                                        </view>
                                        <view class="item-tag">
                                            <view class="tag-content">{{data.stageStatus | lov('PROMOTION_APPROVAL')}}</view>
                                        </view>
                                    </view>
                                    <view class="visit-start-time">
                                        <view class="visit-duration">
                                            <view class="time">{{data.startTime | date('YYYY-MM-DD')}}</view>
                                            <view class="interim-left">···</view>
                                            <view class="duration">  {{data.timeInterval}}天  </view>
                                            <view class="interim-right">···</view>
                                            <view class="time">{{data.endTime | date('YYYY-MM-DD')}}</view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </item>
                </link-swipe-action>
            </template>
        </link-auto-list>
        <link-fab-button @tap="gotoCreate"/>
    </view>
</template>

<script>
import StatusButton from "../../lzlj/components/status-button.vue";

export default {
    name: "promotionStage",
    components: {StatusButton},
    props: {
        stageOption: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data() {
        return {
            showEndFlag: false  // 是否显示终止按钮
        }
    },
    methods: {
        /**
         * desc 新建
         * <AUTHOR>
         * @date 2023-05-24
         */
        gotoCreate() {
            this.$nav.push('/pages/headquarters-activity/new-promotion-stage-page', {
                terminalId: this.stageOption.option.param.filtersRaw[0].value
            });
        },
        /**
         * desc 编辑
         * <AUTHOR>
         * @date 2023-05-24
         */
        gotoEdit(data) {
            if(data.stageStatus === 'New') {
                this.$nav.push('/pages/headquarters-activity/edit-promotion-stage-page', {data: data});
            };
        },
        /**
         * desc 终止
         * <AUTHOR>
         * @date 2023-05-24
         */
        endStage(data,index) {
            if(data.stageStatus === 'Completed') {
                this.$message.warn('推广阶段状态为已完成时，不可终止！');
            }
            if(data.stageStatus === 'Submitted' || data.stageStatus === 'Processing') {
                this.$taro.showModal({
                    title: '提示',
                    content: '是否终止该推广阶段？',
                    success: async (res) => {
                        if (res.confirm) {
                            await this.endCurrentStage(data,index);
                        }
                    }
                });

            }
        },

        async endCurrentStage(data, index) {
            try{
                const reg = await this.$http.post('action/link/promotionStage/endStage', {
                    id: data.id,
                })
                if(reg.success) {
                    this.$set(data, 'approvalStatus', 'Closed');
                    this.$bus.$emit('promotionStageList');  // 刷新推广阶段列表
                    console.log('已终止');
                } else {
                    this.$message.warn('终止该推广阶段失败！');
                }
            } catch (e) {
                this.$message.warn('终止该推广阶段失败！');
            }
        }
    },
    async created() {
    },
}

</script>
<style lang="scss">
.promotion-stage {
    .link-card-list{
        padding: 0;

    }
    .link-card-list .link-swipe-option-container .link-swipe-option {
        font-size: 28px !important;
        height: 80px !important;

    }
    .link-swipe-action .link-swipe-option-container .link-swipe-option {
        margin-top: 20px;
        height: 85%;
    }
    .media-list-rows {
        background: #FFFFFF;
        border-radius: 32px;
        width: 702px;
        margin: 24px auto auto auto;
        .visit-list {
            padding: 16px;
            .visit-content {
                .visit-top {
                    @include flex-start-center;
                    @include space-between;
                    .store-time {
                        @include flex-start-center;
                        .visit-time {
                            @include flex-start-center();
                            .stage {
                                font-family: PingFangSC-Semibold,serif;
                                font-size: 32px;
                                color: #262626;
                            }
                            .name{
                                padding-left: 32px;
                                font-size: 28px;
                                color: #8C8C8C;
                            }
                        }
                    }
                    .item-tag {
                        height: 36px;
                        line-height: 36px;
                        text-align: center;
                        color: #ffffff;
                        background: #2F69F8;
                        box-shadow: 0 3px 4px 0 rgba(47,105,248,0.35);
                        border-radius: 8px;
                        padding-left: 24px;
                        padding-right: 24px;
                        font-size: 20px;
                        margin-right: 8px;
                        transform: skewX(-30deg);
                        .tag-content {
                            transform: skewX(30deg);
                        }
                    }
                }
                .visit-start-time {
                    padding-top: 24px;
                    @include flex-start-center;
                    @include space-between;
                    .visit-duration {
                        @include flex-start-center;
                        .time {
                            font-size: 28px;
                            color: #333333;
                            font-weight: bold;
                        }
                        .interim-left {
                            padding-left: 16px;
                            padding-right: 16px;
                            font-size: 32px;
                            font-weight: bold;
                            background-image: linear-gradient(90deg, rgba(191,191,191,0.20) 0%, #BFBFBF 100%);
                            -webkit-background-clip: text;
                            color:transparent;
                        }
                        .duration {
                            background: #959FB9;
                            border-radius: 18px;
                            /*width: 110px;*/
                            padding-right: 12px;
                            padding-left: 12px;
                            height: 36px;
                            line-height: 36px;
                            color: #ffffff;
                            font-size: 20px;
                            text-align: center;
                            width: 60px;
                        }
                        .interim-right {
                            padding-left: 16px;
                            padding-right: 16px;
                            font-size: 32px;
                            font-weight: bold;
                            background-image: linear-gradient(90deg, rgba(191,191,191,0.20) 0%, #BFBFBF 100%);
                            -webkit-background-clip: text;
                            color:transparent;
                        }
                    }
                }
            }
        }
    }
}
</style>
