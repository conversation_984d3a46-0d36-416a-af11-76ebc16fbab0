<!--
总部活动-登记列表详情-基础信息
<AUTHOR>
@date 2023-04-04
-->
<template>
    <view class="head-activity-basic-info" id="basicInfo">
        <!--基础信息-->
        <view class="basic-info-v">
            <view class="menu-stair">
                <view class="line">
                    <view class="line-top"></view>
                    <view class="line-bottom"></view>
                </view>
                <view class="stair-title">基础信息</view>
            </view>
            <view class="basic-info">
                <view style="width: 100%;height: 8px"></view>
                <view>
                    <view class="block-v">
                        <view class="title">总部活动类型</view>
                        <view class="val">{{item.activityType | lov('HEAD_ACT_TYPE')}}</view>
                    </view>
                    <view class="block-v">
                        <view class="title">终端名称</view>
                        <view class="val"> {{item.terminalName}} </view>
                    </view>
                    <view class="block-v">
                        <view class="title">登记名称</view>
                        <view class="val"> {{item.feedbackName}} </view>
                    </view>
                    <view class="block-v">
                        <view class="title">登记人</view>
                        <view class="val"> {{item.creator}} </view>
                    </view>
                    <view class="block-v">
                        <view class="title">登记人职位</view>
                        <view class="val"> {{item.creatorPostnName}} </view>
                    </view>
                    <view class="block-v">
                        <view class="title">登记人电话</view>
                        <view class="val"> {{item.creatorTel}} </view>
                    </view>
                    <view class="block-v">
                        <view class="title">政策类型</view>
                        <view class="val"> {{item.policyType | lov('MARKETSIX_POLICY_TYPE')}} </view>
                    </view>
                    <view class="block-v" v-if="item.activityDate">
                        <view class="title">活动日期</view>
                        <view class="val"> {{item.activityDate.substr(0,10)}} </view>
                    </view>
                    <view class="block-v">
                        <view class="title">活动阶段</view>
                        <view class="val"> {{item.stageName | lov('PROMOTION_STAGE')}} </view>
                    </view>
                    <!-- 240328迭代去除活动时段 -->
                    <!-- <view class="block-v">
                        <view class="title">活动时段</view>
                        <view class="val"> {{item.feedbackStage | lov('HEAD_FEEDBACK_TIME')}} </view>
                    </view> -->
                    <view class="block-v">
                        <view class="title">包间号</view>
                        <view class="val"> {{item.privateRoom}} </view>
                    </view>
                    <view class="block-v">
                        <view class="title">当日用餐包间数量</view>
                        <view class="val"> {{item.privateNumber}} </view>
                    </view>
                    <view class="block-v">
                        <view class="title">参与人数(人/场)</view>
                        <view class="val"> {{item.clientNumber}} </view>
                    </view>
                    <block v-for="(prod, index) in prodList" :key="index">
                        <view class="block-v">
                            <view class="title">小酒产品名称</view>
                            <view class="val"> {{prod.productName}} </view>
                        </view>
                        <view class="block-v">
                            <view class="title">开瓶数量</view>
                            <view class="val"> {{prod.productNumber}}瓶</view>
                        </view>
                        <view class="block-v" v-if="prod.scanNumber < prod.productNumber && canScan">
                            <view class="title">开瓶扫码</view>
                            <view class="val" @tap="clickScanCode(index)" >
                                <link-icon icon="icon-scan" style="color: #1E8BFF"/>
                            </view>
                        </view>
                        <view class="block-v" @tap="gotoOpenRecord(prod)">
                            <view class="title">开瓶扫码数量</view>
                            <view class="val-1"> {{prod.scanNumber}}瓶</view>
                            <view class="scan-code">
                                <link-icon icon="mp-arrow-right"></link-icon>
                            </view>
                        </view>
                    </block>
<!--                    <view class="line"></view>-->
                </view>
                <view style="width: 100%;height: 8px"></view>
            </view>
        </view>
    </view>
</template>

<script>
import Taro from "@tarojs/taro";
import {reverseTMapGeocoder} from "../../../utils/locations-tencent";
export default {
    name: "basic-info",
    props: {
        source: {
            type: String,
            default: ''
        },
        status:{
            type: String,
            default: ''
        },
        registerItem: {
            type: Object,
            default: function () {
                return {};
            }
        },
    },
    data() {
        const userInfo = this.$taro.getStorageSync('token').result
        return {
            userInfo,
            item: this.registerItem,
            coordinate: {}, // 存储地理经纬度信息
            addressData: {},  // 地理位置信息
            addressDataFull: '', //详细地理位置
            addressFlag: false, //是否显示地理位置
            clickScanFlag: false, //点击扫码
            prodList: [], // 产品列表
            freshSourceFlag: '' //刷新来源
        }
    },
    computed:{
        canScan(){
            const mineData =  this.userInfo.id === this.registerItem.createdBy

            return mineData && this.status !== 'Inactive'
        }
    },
    async mounted() {
        console.log('基础信息this.registerItem', this.registerItem);
        await this.queryBasicInfo();
        if(this.item.scanNumber < this.item.productNumber && this.item.status !== 'Inactive') {
            this.clickScanFlag = true;
        }
        this.$bus.$on('refreshBasicInfo',async () => {
            await this.queryScanNumber();
        })
        this.$bus.$on('refreshRegUpserts', async (data) => {
            await this.queryProd(data);
        });
    },
    methods: {
        /**
         *  查询基础信息
         *  <AUTHOR>
         *  @date 2023-04-17
         */
        async queryBasicInfo() {
            try {
                const data = await this.$http.post('action/link/headquarterFeedback/queryById',{
                    id: this.registerItem.id
                })
                if(data.success) {
                    this.item = data.result;
                    // 查询产品信息
                    await this.queryProd()
                } else {
                    this.$showError("查询产品请求失败，请稍后重试！");
                }
            } catch (e) {
                this.$showError("查询产品请求失败，请稍后重试！" );
            }
        },
        async queryProd(){
            try{
                const {success, rows} = await this.$http.post('action/link/headquarterProduct/queryByExamplePage', {
                    filtersRaw: [
                        {id: 'feedbackId', property: 'feedbackId', value: this.registerItem.id},
                        {id: 'productType', property: 'productType', value: 'Open'}
                    ]
                });
                if (success) {
                    this.prodList = rows;
                }
            }catch(e){
                //TODO handle the exception
            }
        },
        /**
         * desc 点击扫码
         * <AUTHOR>
         * @date 2023-04-24
         */
        async clickScanCode(index) {
            const that = this;
            await that.getAddress();
            if (that.$utils.isEmpty(this.coordinate.latitude) && that.$utils.isEmpty(this.coordinate.longitude)) {
                this.$dialog({
                    title: '提示',
                    content: '请确认手机地理位置授权是否打开，或者【设置】-【企业微信】位置权限管理是否打开？',
                    cancelButton: false,
                    confirmText: '去开启',
                    onConfirm: async () => {
                        let userLocation = await this.$locations.openSetting();
                        if (userLocation['scope.userLocation']) {
                            that.coordinate = await that.$locations.getCurrentCoordinate();
                        }
                    }
                });
            } else {
                await wx.scanCode({
                    onlyFromCamera: true,
                    success: async (res) => {
                        that.$utils.showLoading();
                        await that.afterGetCode(res.result, index);
                        that.$utils.hideLoading();
                    }
                });
            }
        },
        /**
         * 获取码之后的处理
         * <AUTHOR>
         * @date 2023-04-24
         */
        async afterGetCode(mark, index) {
            try {
                this.$utils.showLoading();
                const data = await this.$http.post('action/link/headquarterFeedback/headOpenScanVerification', {
                    mark: mark,
                    headId: this.item.id,
                    productId: this.prodList[index].productId,
                    province: this.addressData.province,
                    city: this.addressData.city,
                    district: this.addressData.district,
                    scanAddress: this.addressDataFull,
                    remark: this.addressData.remark,
                    productNumber: this.prodList[index].productNumber,
                    productType: 'Open',
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    this.$dialog({
                        title: '提示',
                        content: '扫码成功',
                        cancelButton: false,
                        confirmText: '确定',
                        onConfirm: async () => {
                            await this.queryScanNumber(index);
                        }
                    });
                } else {
                    this.$utils.hideLoading();
                    this.$dialog({
                        title: '提示',
                        content: data.message,
                        cancelButton: false,
                        confirmText: '确定',
                        onConfirm: () => {
                        }
                    });
                }
            } catch (e) {
                this.$utils.hideLoading();
                // this.$showError('获取扫码信息失败，请稍候再试');
            }
        },

        /**
         * 获取定位地址  百度经纬度逆解析
         */
        async getAddress() {
            this.coordinate = await this.$locations.getCurrentCoordinate();
            // 校验用户是否授权地理位置
            if (!this.$utils.isEmpty(this.coordinate.latitude) && !this.$utils.isEmpty(this.coordinate.longitude)) {
                let address = await reverseTMapGeocoder(this.coordinate.latitude, this.coordinate.longitude, '总部活动登记');
                this.addressData = address['originalData'].result.addressComponent;
                this.addressDataFull = address['originalData'].result.formatted_address;
                this.addressFlag = true;
            }
        },
        /**
         * desc 扫码成果查询扫码数量
         * <AUTHOR>
         * @date 2023-04-14
         */
        async queryScanNumber(index) {
            try {
                const data = await this.$http.post('action/link/headquarterProduct/queryByExamplePage', {
                    feedbackId: this.item.id,
                    filtersRaw: [
                        {id: 'productId', property: 'productId', value: this.prodList[index].productId, operator: '='},
                        {id: 'productType', property: 'productType', value: 'Open'}],
                });
                if (data.success) {
                    this.$set(this.prodList[index], 'scanNumber', data.rows[0].scanNumber);
                    this.$bus.$emit('refreshTerminalList');
                    this.$bus.$emit('refreshRegList', data.rows[0].feedbackId);
                    // if(this.item.scanNumber === this.item.productNumber) {
                    //     this.clickScanFlag = false;
                    //     this.$bus.$emit('refreshFeedbackImg');
                    // }
                } else {
                    this.$showError('查询扫码数量失败，请稍候再试');
                }
            } catch (e) {
                this.$showError('查询扫码数量失败');
            }
        },
        /**
         * desc 跳转到扫码记录
         * <AUTHOR>
         * @date 2023-04-24
         * @params
         */
        gotoOpenRecord(item) {
            this.$nav.push('/pages/headquarters-activity/scan-code-record-page.vue',{
                id: this.registerItem.id,
                source: this.status ==='Inactive' ? 'upsert' : this.source,
                headId: item.id,
            });
        },
        /**
         * 复制活动编码
         *  <AUTHOR>
         *  @date 2023-04-04
         */
        copyActCode(text) {
            wx.setClipboardData({data: text});
        },
    }
}
</script>

<style lang="scss">
.head-activity-basic-info {
    margin-top: 80px;
    .basic-info-v {
        .menu-stair {
            width: 100%;
            margin-left: 24px;
            padding-top: 40px;
            @include flex-start-center;

            .line {
                clear: both;

                .line-top {
                    width: 8px;
                    height: 16px;
                    background: #3FE0E2;
                }

                .line-bottom {
                    width: 8px;
                    height: 16px;
                    background: #2F69F8;
                }
            }

            .stair-title {
                width: 60%;
                margin-left: 16px;
                font-family: PingFangSC-Semibold, serif;
                font-size: 32px;
                color: #262626;
                letter-spacing: 1px;
                line-height: 32px;
            }
        }

        .basic-info {
            background: #FFFFFF;
            border-radius: 16px;
            margin: 24px;

            .block-v {
                padding: 0 24px;
                display: flex;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 60px;
                    width: 35%;
                    float: left;
                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                    text-align: right;
                    line-height: 60px;
                    width: 65%;
                    float: left;
                    text-overflow: ellipsis;
                }
                .val-1 {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                    text-align: right;
                    line-height: 60px;
                    float: left;
                    text-overflow: ellipsis;
                    width: 70%;

                }
                .scan-code {
                    color: #262626;
                    width: 5%;
                    text-align: right;
                    font-size: 28px;
                    padding-top: 8px;
                }
            }

            .line {
                margin: 32px 24px 32px 24px;
                height: 0;
                border: 2px dashed #DADEE9;
            }
        }
    }
}
</style>
