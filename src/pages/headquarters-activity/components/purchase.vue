<!--
总部活动-登记列表详情-查看常规装进销存情况
<AUTHOR>
@date 2023-04-04
-->
<template>
    <view class="purchase-info">
        <view class="purchase-info-v">
            <view class="menu-stair">
                <view class="line">
                    <view class="line-top"></view>
                    <view class="line-bottom"></view>
                </view>
                <view class="stair-title">常规装进销存情况
                </view>
            </view>
            <view class="basic-info">
                <view class="product-list">
                    <view v-for="(item,index) in productionPinList" :key="index">
                        <!-- 240401迭代 -->
                        <link-swipe-action>
                            <!-- <link-swipe-option slot="option" @tap="deleteRow(item,index)" v-if="Number(item.scanNumber) < 1">删除</link-swipe-option> -->
                            <item :arrow="false" class="product-list-item">
                                <view slot="note">
                                    <view class="media-list">
                                        <view class="media-top">
                                            <view class="num-view" @longPress="copyActCode(item.productCode)">
                                                <view class="num">{{ item.productCode }}</view>
                                            </view>
                                            <view style="color: #1E8BFF;" @tap="pickScanProduction(item)">查看扫码记录</view>
                                        </view>
                                    </view>
                                    <view class="content-middle">
                                        <view class="name">{{item.productName}}</view>
                                    </view>
                                    <view class="media-list">
                                        <view class="store-input">
                                            <view class="store-input-rows">
                                                <view class="label">库存:&nbsp;</view>
                                                <view class="value">{{item.productStockQty}}&nbsp;{{'瓶'}}</view>
                                            </view>
                                            <view class="store-input-rows">
                                                <view class="label">动销:&nbsp;</view>
                                                <view class="value">{{item.scanNumber}}&nbsp;{{'瓶'}}<link-icon style="color: #1E8BFF; text-align: right; font-size: 18px;" icon="icon-scan" v-if="canScan" @tap="clickScan(item, index)"/></view>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </item>
                        </link-swipe-action>

                    </view>
                </view>
                <view style="width: 100%;height: 8px"></view>
            </view>
        </view>
    </view>
</template>

<script>
import {reverseTMapGeocoder} from "../../../utils/locations-tencent";
export default {
    name: "purchase-info",
    props: {
        registerItem: {
            type: Object,
            default: function () {
                return {};
            }
        },
        source: {
            type: String,
            default: ''
        },
        status:{
            type: String,
            default: ''
        }
    },
    computed: {
        registerItemData: function () {
            return this.registerItem;
        },
        canScan(){
            const mineData =  this.userInfo.id === this.registerItem.createdBy
            return mineData && this.status !== 'Inactive'
        }
    },
    data() {
        const userInfo = this.$taro.getStorageSync('token').result
        return {
            productionPinList: [],  //活动动销列表
            coordinate: {}, // 存储地理经纬度信息
            userInfo
        }
    },
    created() {
        this.queryPurchase();
    },
    mounted() {
        this.$bus.$on('refreshRegUpserts', async (data) => {
            await this.queryPurchase()
        })
    },
    methods: {
        /**
         * 侧滑删除一行
         * <AUTHOR> @date 2023-04-25
         * */
        deleteRow(item, index) {
            this.$taro.showModal({
                title: '提示',
                content: '是否删除此动销记录？',
                success: async (res) => {
                    await this.deleteCurrentRow(item, index);
                }
            });
        },
        /**
         *  删除信息行的回调函数
         *  <AUTHOR>
         *  @date 2024-04-25
         */
        async deleteCurrentRow(item,index) {
            try{
                const data = await this.$http.post('action/link/headquarterProduct/deleteById', {
                    id: item.id
                });
                if (data.success) {
                    this.$taro.showToast({
                        title: '删除成功'
                    });
                    this.queryPurchase()
                } else {
                    this.$taro.showToast({
                        title: '删除失败'
                    });
                }
            }catch(e){
                //TODO handle the exception
            }


        },
        /**
         *  查询动销接口
         *  <AUTHOR>
         *  @date 2023-04-06
         */
        async queryPurchase() {
            try {
                this.productionPinList = []
                const data = await this.$http.post('action/link/headquarterProduct/queryByExamplePage',{
                    // activityId: this.registerItemData.activityId,
                    feedbackId: this.registerItemData.feedbackId ? this.registerItemData.feedbackId : this.registerItemData.id,
                    productType: 'Sales',
                    }
                )
                if(data.success) {
                    this.productionPinList = data.rows;
                } else {
                    this.$showError("查询产品请求失败，请稍后重试！");
                }
            } catch (e) {
                this.$showError("查询产品请求失败，请稍后重试！" );
            }
        },

        /**
         * 扫码产品记录查询
         * <AUTHOR>
         * @date 240318
         * */
         async pickScanProduction(item) {
             const param = {
                 id: item.feedbackId,
                 source: this.status ==='Inactive' ? 'upsert' : this.source,
                 headId: item.id
             }
            this.$nav.push('/pages/headquarters-activity/scan-code-record-page.vue', {
                ...param
            })
        },

        /**
         * 获取定位地址  百度经纬度逆解析
         */
         async getAddress() {
            this.coordinate = await this.$locations.getCurrentCoordinate();
            // 校验用户是否授权地理位置
            if (!this.$utils.isEmpty(this.coordinate.latitude) && !this.$utils.isEmpty(this.coordinate.longitude)) {
                let address = await reverseTMapGeocoder(this.coordinate.latitude, this.coordinate.longitude, '查看常规装进销存');
                this.addressData = address['originalData'].result.addressComponent;
                this.addressDataFull = address['originalData'].result.formatted_address;
                this.addressFlag = true;
            }
        },

        /**
         * desc 点击扫码
         * <AUTHOR>
         * @date 240318
         */
         async clickScan(item, index) {
            const that = this;
            await that.getAddress();
            if (that.$utils.isEmpty(this.coordinate.latitude) && that.$utils.isEmpty(this.coordinate.longitude)) {
                this.$dialog({
                    title: '提示',
                    content: '请确认手机地理位置授权是否打开，或者【设置】-【企业微信】位置权限管理是否打开？',
                    cancelButton: false,
                    confirmText: '去开启',
                    onConfirm: async () => {
                        let userLocation = await this.$locations.openSetting();
                        if (userLocation['scope.userLocation']) {
                            that.coordinate = await that.$locations.getCurrentCoordinate();
                        }
                    }
                });
            } else {
                await wx.scanCode({
                    onlyFromCamera: true,
                    success: async (res) => {
                        that.$utils.showLoading();
                        await that.afterGetCode(res.result, that.productionPinList[index], index);
                        that.$utils.hideLoading();
                    }
                });
            }
        },

        /**
         * 获取码之后的处理
         * <AUTHOR>
         * @date 240318
         */
         async afterGetCode(mark, item, index) {
            try {
                this.$utils.showLoading();
                const data = await this.$http.post('action/link/headquarterFeedback/headOpenScanVerification', {
                    mark: mark,
                    headId: this.registerItemData.feedbackId ? this.registerItemData.feedbackId : this.registerItemData.id,
                    province: this.addressData.province,
                    city: this.addressData.city,
                    district: this.addressData.district,
                    scanAddress: this.addressDataFull,
                    productType: 'Sales',
                    productId: item.productId,
                    productStockQty: item.productStockQty,
                    remark: this.addressData.remark,
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    this.$set(this.productionPinList[index], 'scanNumber', data.scanNumber);
                    this.$set(this.productionPinList[index], 'productNumber', data.productNumber);
                    this.$message.success('扫码成功');
                    setTimeout(() => {
                        this.clickScan(item, index);
                    }, 500);
                } else {
                    this.$utils.hideLoading();
                    this.$showError(data.message);
                }
            } catch (e) {
                console.log(e)
                this.$utils.hideLoading();
            }
        },
        /**
         * 复制活动编码
         *  <AUTHOR>
         *  @date 2023-04-04
         */
        copyActCode(text) {
            wx.setClipboardData({data: text});
        },
    }
}
</script>

<style lang="scss">
@import "../../../styles/list-card";
.purchase-info {
    //margin-top: 80px;
    .purchase-info-v {
        .menu-stair {
            width: 100%;
            margin-left: 24px;
            padding-top: 40px;
            @include flex-start-center;

            .line {
                clear: both;

                .line-top {
                    width: 8px;
                    height: 16px;
                    background: #3FE0E2;
                }

                .line-bottom {
                    width: 8px;
                    height: 16px;
                    background: #2F69F8;
                }
            }

            .stair-title {
                width: 60%;
                margin-left: 16px;
                font-family: PingFangSC-Semibold, serif;
                font-size: 32px;
                color: #262626;
                letter-spacing: 1px;
                line-height: 32px;
            }
        }
        .basic-info {
            background: #FFFFFF;
            border-radius: 16px;
            margin: 24px;
            .product-list {
                font-size: 28px;
                color: #262626;
                letter-spacing: 0;
                line-height: 38px;
                .link-swipe-action {
                    position: relative;
                    width: 100%;
                    overflow-x: hidden;
                }
                .product-list-item {
                    background: #FFFFFF;
                    margin: auto;
                    border-radius: 16px;

                    .media-list {
                        @include media-list;

                        .media-top {
                            width: 100%;
                            @include flex-start-center;
                            @include space-between;
                            height: 80px;
                            line-height: 80px;

                            .num-view {
                                background: #A6B4C7;
                                border-radius: 8px;
                                line-height: 50px;

                                .num {
                                    font-size: 28px;
                                    color: #FFFFFF;
                                    letter-spacing: 0;
                                    line-height: 40px;
                                    padding: 2px 8px;
                                }
                            }
                        }
                    }

                    .content-middle {
                        width: 100%;
                        @include flex-start-center;
                        @include space-between;
                        height: 80px;
                        line-height: 80px;

                        .content {
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #000000;
                            letter-spacing: 0;
                        }

                        .name {
                            font-family: PingFangSC-Semibold;
                            font-size: 36px;
                            color: #000000;
                            letter-spacing: 0;
                            line-height: 32px;
                        }
                    }

                    .content-middle-line {
                        width: 100%;

                        .data {
                            width: 50%;
                            float: left;

                            .title {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #000000;
                                letter-spacing: 0;
                                line-height: 56px;
                                width: 30%;
                                float: left;
                            }

                            .val {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #000000;
                                letter-spacing: 0;
                                line-height: 56px;
                                float: left;
                            }

                            .icon {
                                float: left;
                                line-height: 56px;
                                padding-left: 2%;
                            }

                            .Submitted, .Feedback {
                                color: #2F69F8;
                            }

                            .Approve, .FeedbackApro {
                                color: #2EB3C2;
                            }

                            .Refused, .Refeedback {
                                color: #FF5A5A;
                            }

                        }

                        .sum {
                            width: 50%;
                            float: left;

                            .title {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #000000;
                                letter-spacing: 0;
                                line-height: 56px;
                                float: left;
                                width: 35%;
                            }

                            .val {
                                font-family: PingFangSC-Regular;
                                font-size: 28px;
                                color: #000000;
                                letter-spacing: 0;
                                line-height: 56px;
                                white-space: nowrap;
                                float: left;
                            }

                            .icon {
                                padding-left: 2%;
                                float: left;
                                // line-height: 56px;
                            }
                        }
                    }

                    .store-input {
                        padding-top: 16px;
                        width: 100%;
                        @include flex-start-center();
                        @include space-between();
                        font-family: PingFangSC-Regular,serif;
                        font-size: 28px;
                        letter-spacing: 0;
                        .store-input-rows {
                            @include flex-start-center();
                            .label {
                                color: #000000;
                            }
                            .value {
                                padding-left: 8px;
                                color: #000000;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
