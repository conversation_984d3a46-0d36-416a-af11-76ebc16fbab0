<!--
 * @Author: <EMAIL>
 * @Date: 2024-07-24 09:48:35
 * @Description: 终端信息 -- 终端卡片
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-11-13
 -->

<template>
	<view class="terminal-list-item">
		<image class="media-list-logo" :src="data.storeUrl" lazy-load="true"></image>
		<view class="store-content">
			<!-- 终端标题 等级 -->
			<view class="store-content-top">
				<!--终端名称-->
				<view class="store-title">{{ data.acctName }}</view>
				<view class="store-level" v-if="data.cateringStatus === 'Approved'">餐饮终端</view>
			</view>
			<!-- 标签 -->
			<view class="store-content-middle">
				<view class="left">
					<!-- 四色标签 -->
					<color-tag :value="data.fourColorLabel" v-if="data.fourColorLabel" />
					<!-- 贷款状态 -->
					<view class="store-type financingFlag" v-if="data.financingFlag">贷 | {{ data.financingFlag | lov('YR_FINANCING_FLAG') }}</view>
					<!-- 客户大类  -->
					<view class="store-type acctType" v-if="typeList.includes(data.acctType)">
						{{ data.acctType | lov('ACCT_TYPE') }}
					</view>
					<!-- 客户中类 -->
					<view class="store-type acctCategory" v-if="categoryLst.includes(data.acctCategory)">
						{{ data.acctCategory | lov('ACCNT_CATEGORY') }}
					</view>
					<!-- 客户小类 -->
					<view class="store-type subAcctType" v-if="sublist.includes(data.subAcctType)">
						{{ data.subAcctType | lov('SUB_ACCT_TYPE') }}
					</view>
					<!-- 战略零售商标签 -->
					<view class="store-type" v-if="data.strategicFlag && data.strategicFlag === 'Y'">
						{{ data.strategicFlag | lov('STRATEGIC_TAG') }}
					</view>
					<!-- 客户规划等级 | 容量级别 -->
					<view class="store-type acctLevel" v-if="levelList.includes(data.acctLevel)">
						{{ data.acctLevel | lov('ACCT_LEVEL') }}
						<text v-if="caplist.includes(data.capacityLevel)">｜ {{ data.capacityLevel | lov('CAPACITY_LEVEL') }}</text>
					</view>
					<!-- 合作状态 -->
					<view
						class="store-type joinFlag"
						v-if="data.joinFlag && broadCompanyCode.indexOf(userInfo.coreOrganizationTile.brandCompanyCode) !== -1">
						{{ data.joinFlag | lov('JOIN_FLAG') }}
					</view>
					<!-- 是否为品鉴基地 -->
					<view class="store-type" v-if="data.judgmentFlag === 'Y'">品鉴</view>
					<!-- 门头 -->
					<view class="store-type" v-if="data.doorSigns !== undefined">{{ data.doorSigns | lov('DOOR_SIGNS') }} </view>
					<!-- 数字认证 -->
					<view class="store-type" v-if="data.terminalDigitization">{{ data.terminalDigitization | lov('TERMINAL_DIGITIZATION') }}</view>

					<!-- 是否春雷行动 -->
					<view class="store-type" v-if="data.isSpringAct === 'Y'">春雷行动</view>
					<!-- 陈列 -->
					<block v-if="data.displayPolicyType">
						<view class="store-type displayPolicyType" v-for="(item, index) in data.displayPolicyType.split(',')" :key="index">{{
							item | lov('DISPLAY_POLICY_TYPE')
						}}</view>
					</block>
					<view class="store-type" v-if="data.codeMark">{{ data.codeMark }}</view>
					<!-- 普查标签：有异常/无异常 -->
					<view class="store-type censusLabels" v-if="data.censusLabels">{{ data.censusLabels | lov('CENSUS_STATUS') }} </view>
				</view>
			</view>
			<view class="store-content-representative">
				<view class="terminal-type">编码</view>
				<view class="terminal-name">{{ data.acctCode }}</view>
			</view>
			<view class="store-content-representative">
				<view class="terminal-type">业代</view>
				<view class="terminal-name">{{ data.salesManListString }}</view>
			</view>
			<view class="store-content-address">
				<view class="store-address">{{ data.province }}{{ data.city }}{{ data.district }}{{ data.address }} </view>
			</view>
		</view>
	</view>
</template>

<script>
    import ColorTag from '@/pages/terminal2/components/ColorTag.vue';

	export default {
		name: 'terminal-card',
		props: {
			data: Object
		},
		components: { ColorTag },
		data() {
			const userInfo = this.$taro.getStorageSync('token').result;
			return {
				userInfo,
				typeList: [],
				categoryLst: [],
				sublist: [],
				levelList: [],
				caplist: [],
				broadCompanyCode: '',
			};
		},
		methods: {
			async getTypeArray() {
				const list = await this.$lov.getLovByTypeArray(['ACCT_TYPE', 'ACCNT_CATEGORY', 'SUB_ACCT_TYPE', 'ACCT_LEVEL', 'CAPACITY_LEVEL']);
				list[0].forEach((item) => {
					this.typeList.push(item.val);
				});
				list[1].forEach((item) => {
					this.categoryLst.push(item.val);
				});
				list[2].forEach((item) => {
					this.sublist.push(item.val);
				});
				list[3].forEach((item) => {
					this.levelList.push(item.val);
				});
				list[4].forEach((item) => {
					this.caplist.push(item.val);
				});
			},
		},
		async created() {
			this.getTypeArray();
			this.broadCompanyCode = await this.$utils.getCfgProperty('getPurchaseSumForOrder');
		},
	};
</script>

<style lang="scss">
	.terminal-list-item {
		background: #fff;
		border-radius: 16px;
		@include flex;
		padding: 24px;
		align-items: flex-start;
		width: 702px;
		box-sizing: border-box;
        margin: auto;
		.media-list-logo {
			width: 126px;
			height: 126px;
			background: #d8d8d8;
			border-radius: 16px;
			overflow: hidden;
		}

		.store-content {
			margin-left: 16px;
			width: 80%;
			.store-content-top {
				@include flex-start-center;
				@include space-between;

				.store-title {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 32px;
					color: #212223;
					line-height: 48px;
					// overflow: hidden;
					// white-space: nowrap;
					// text-overflow: ellipsis;
				}

				.store-level {
					width: 100px;
					transform: skewX(-10deg);
					border-radius: 4px;
					background: #2f69f8;
					box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
					height: 36px;
					font-size: 20px;
					color: #ffffff;
                    line-height: 36px;
                    text-align: center;
				}
			}

			.store-content-middle {
				display: flex;
				justify-content: space-between;
				overflow: hidden;

				.left,
				.right {
					@include flex-start-center;
					flex-wrap: wrap;

					.store-type {
						margin: 8px 8px 8px 0;
						white-space: nowrap;
						height: 44px;
						line-height: 44px;
						border-radius: 6px;
						border: 1px solid #2f69f8;
						font-size: 24px;
						color: #2f69f8;
						padding: 0 20px;
					}
				}
			}

			.store-content-representative {
				@include flex;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				font-size: 28px;
				margin: 8px 0;
				line-height: 44px;
				color: #333;

				.terminal-type {
					color: #999999;
					min-width: 56px;
				}

				.terminal-name {
					margin-left: 8px;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}

			.store-content-address {
				font-size: 28px;
				color: #333;
				line-height: 44px;
				padding-right: 60px;
			}
		}
	}
</style>
