<!--
营销6.0-活动项
<AUTHOR>
@date	2023/8/9
-->
<template>
    <item :key="index" :data="data" :arrow="false" class="activity-item">
        <view slot="note">
            <view class="media-list">
                <view class="media-top">
                    <!-- 活动编码 -->
                    <view class="num-view" @longPress="copyActCode(data.activityNum)">
                        <view class="num">{{data.activityNum}}</view>
                    </view>
                    <!-- 活动状态标识 -->
                    <status-button :label="data.activityStatus| lov('HEAD_ACT_STATUS')"></status-button>
                </view>
            </view>
            <!-- 活动名称 -->
            <view class="content-middle">
                <view class="name">
                    {{data.activityName && data.activityName.length > 20 ? data.activityName.substring(0, 20) + '...' : data.activityName }}
                </view>
            </view>
            <!--活动主题-->
            <view class="content-middle-line">
                <view class="data">
                    <view class="title">活动主题</view>
                    <view class="val">{{data.activityType|lov('HEAD_ACT_TYPE')}}</view>
                </view>
            </view>
            <view class="content-middle-line">
                <view class="data">
                    <view class="title">开始时间</view>
                    <view class="val">{{data.startTime|date('YYYY-MM-DD')}}</view>
                </view>
                <view class="sum">
                    <view class="title">结束时间</view>
                    <view class="val">{{data.endTime|date('YYYY-MM-DD')}}</view>
                </view>
            </view>
        </view>
    </item>
</template>

<script>
import StatusButton from '../../lzlj/components/status-button';

export default {
    name: 'activity-item',
    props: {
        index: Number,
        data: Object
    },
    components: {StatusButton},
    methods: {
        /**
         * 复制活动编码
         *  <AUTHOR>
         *  @date 2023-03-28
         */
        copyActCode(text) {
            wx.setClipboardData({data: text});
        },
    }
}
</script>

<style lang="scss">
@import "../../../styles/list-card";
.activity-item {
    background: #FFFFFF;
    width: 95%;
    margin: 24px auto auto auto;
    border-radius: 16px;

    .media-list {
        @include media-list;

        .media-top {
            width: 100%;
            @include flex-start-center;
            @include space-between;
            height: 80px;
            line-height: 80px;

            .left-content {
                font-family: PingFangSC-Semibold;
                font-size: 32px;
                color: #262626;
                letter-spacing: 0;
                line-height: 32px;
                padding-top: 20px;

            }

            .right-content {
                font-family: PingFangSC-Semibold;
                font-size: 32px;
                color: #FF5A5A;
                letter-spacing: 0;
                text-align: right;
                line-height: 32px;
                padding-top: 20px;
            }

            .num-view {
                background: #A6B4C7;
                border-radius: 8px;
                line-height: 50px;

                .num {
                    font-size: 28px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    line-height: 40px;
                    padding: 2px 8px;
                }
            }

            .status-view {
                width: 120px;
                transform: skewX(-10deg);
                border-radius: 4px;
                background: #2F69F8;
                box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                height: 36px;

                .status {
                    font-size: 20px;
                    color: #FFFFFF;
                    letter-spacing: 2px;
                    text-align: center;
                    line-height: 36px;
                }
            }
        }
    }

    .content-middle {
        width: 100%;
        @include flex-start-center;
        @include space-between;
        height: 80px;
        line-height: 80px;

        .content {
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #000000;
            letter-spacing: 0;
        }

        .name {
            font-family: PingFangSC-Semibold;
            font-size: 32px;
            color: #262626;
            letter-spacing: 0;
            line-height: 32px;
        }
    }

    .content-middle-line {
        width: 100%;

        .data {
            width: 51%;
            float: left;

            .title {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                line-height: 56px;
                width: 40%;
                float: left;

            }

            .val {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
                line-height: 56px;
            }

            .Submitted, .Feedback{
                color: #2F69F8;
            }

            .Approve, .FeedbackApro{
                color: #2EB3C2;
            }

            .Refused, .Refeedback{
                color: #FF5A5A;
            }

        }

        .sum {
            width: 49%;
            float: left;

            .title {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                line-height: 56px;
                float: left;
                width: 40%;
            }

            .val {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
                line-height: 56px;
                white-space:nowrap;
            }
        }

        .sum-2 {
            width: 58%;
            float: left;

            .title {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                line-height: 56px;
                float: left;
                margin-right: 24px;
            }

            .val {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
                line-height: 56px;
            }
        }
    }
}
</style>
