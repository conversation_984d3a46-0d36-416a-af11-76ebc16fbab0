<!--
总部活动-登记列表-新增-活动动销
<AUTHOR>
@date 2023-04-06
-->
<template>
    <view class="pin-register">
        <view class="menu-stair" style="margin-bottom: 12px">
            <view class="line">
                <view class="line-top"></view>
                <view class="line-bottom"></view>
            </view>
            <view class="stair-title">常规装进销存情况</view>
        </view>
        <view class="view">
            <view class="item-header">
                <view style="width: 50%;float: left">活动动销</view>
                <view style="float: left;text-align: right;width: 40%;padding-right: 12px;color: #2F69F8;"
                      @tap="pickProduction">添加
                </view>
            </view>
            <view class="product-list">
                <view v-for="(item,index) in productionPinList" :key="index" class="new-box">
                    <pin-item :item='item' @clickScan='scanClick' :index='index' @pickScanProduction='pickScanProduction' @deleteRow='deleteRow'></pin-item>
                </view>

            </view>
        </view>
    </view>
</template>

<script>

import {reverseTMapGeocoder} from "../../../utils/locations-tencent";
import pinItem from "./pin-item.vue";
export default {
    name: 'pin-register',
    components:{
        pinItem
    },
    props: {
        id: {
            type: String,
            default: ''
        },
        // productId: {
        //     type: String,
        //     default: ''
        // },
        status: {
            type: String,
            default: ''
        },
        approveStatus: {
            type: String,
            default: ''
        },
        prodNumber: {
            type: String,
            default: ''
        },
        operateValidate: {
            type: Function,
        },
        feedbackId: {
            type: String,
            default: ''
        },
        sourceFlag: {
            type: String,
            default: ''
        }
    },
    data() {
        const productionPinOption = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/activityProduct/querySalesProductPage',
            },
            searchFields: ['productName', 'productId'],
            param: () => {
                return {
                    feedbackId: this.feedbackId,
                    accountId: this.id,
                }
            },
            // 添加 -> 选择产品
            renderFunc: (h, {data, index}) => {
                return (
                    <item key = {index} data = {data} arrow = "false" >
                        <link-checkbox val = {data.id} toggleOnClickItem slot = "thumb"/>
                        <view style = "display: flex;flex: 1;flex-direction: column;justify-content: space-between;align-items: flex-start;overflow: hidden;" >
                            <view style = "margin:12px;">
                                <view style = "background: #A6B4C7;border-radius: 4px;line-height: 20px;" >
                                    <view style = "font-size: 14px;color: #FFFFFF;letter-spacing: 0;line-height: 20px;padding: 1px 4px;" > {data.productCode}
                                    </view>
                                </view>
                            </view>
                            <view style = "margin-left:12px;width:100%">
                                <view style = "font-family: PingFangSC-Semibold;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 16px;" > {data.productName}
                                </view>
                            </view>
                        </view>
                    </item>
                )
            },
            hooks: {
                afterLoad(data) {
                    console.log('执行afterLoad_data',data);
                    // const list =
                    // this.productId
                }
            }
        })
        return {
            productionPinOption,    //活动动销列表
            productDialogFlag: false,   //调整产品弹框
            // adjustedProductItem: {},    //调整的产品信息
            productionPinList: [],  //活动动销列表
            productId: '',
            coordinate: {}, // 存储地理经纬度信息
            prodList: [] // 扫码产品列表
        }
    },
    async created() {
        if(this.sourceFlag === 'regListEdit' || this.sourceFlag === 'terminalEdit') {
            this.getData()
        }
    },
    mounted(){
        this.$bus.$on('refreshRegUpserts', async (data) => {
            await this.getData()
        });
    },
    watch:{
        productionPinList: {
            handler(val){
                val.forEach((item)=>{
                    if(typeof(item.productStockQty) == 'string')
                        item.productStockQty = item.productStockQty.replace(/[^0-9]/g,'')
                    if(typeof(item.scanNumber) == 'string')
                        item.scanNumber = item.scanNumber.replace(/[^0-9]/g,'')
                    if (!this.$utils.isEmpty(item.id)) {
                        const updateData = {
                            id: item.id,
                            unit: item.unit,
                            scanNumber: item.scanNumber,
                            productStockQty: item.productStockQty,
                            updateFields: "id, unit, productStockQty"
                        }
                        this.$http.post('action/link/headquarterProduct/update', updateData)
                    }
                })
            },
            deep: true
        }
    },
    methods: {
        /**
         * 初始化数据
         * <AUTHOR>
         * @date 2024-03-29
         * */
        async getData(){
            this.productionPinList = []
            const data = await this.$http.post('action/link/headquarterProduct/queryByExamplePage', {
                feedbackId: this.feedbackId,
                productType: 'Sales'
            })
            if(data.success) {
                this.$nextTick(() => {
                    this.productionPinList = data.rows
                })
            }
        },
        /**
         * 查询活动动销列表
         * <AUTHOR>
         * @date 2023-04-06
         * */
        async queryProductionPinList() {
            try {
                const res = await this.$http.post('action/link/headquarterProduct/queryByExamplePage', {
                    feedbackId: this.feedbackId,
                    productType: 'Sales'
                });
            } catch (e) {
                this.$showError('查询活动动销列表数据失败！')
            }

        },
        /**
         * 插入产品
         * <AUTHOR>
         * @date 2023-04-06
         * */
        async insertProd(mapList) {
            try{
                const data = await this.$http.post('action/link/headquarterProduct/batchInsert', mapList);
            } catch (e) {
                this.$showError('添加产品失败！')
            }

        },

        /**
         * 添加产品
         * <AUTHOR>
         * @date 2023-04-06
         * */
        async pickProduction() {
            const data = await this.operateValidate();
            console.log(data)
            const list = await this.$object(this.productionPinOption, {
                pageTitle: "产品",
                multiple: true,
                // selected: this.productionPinList.map(item => item.id),
            });
            const mapList  = list.map(item=>{
                return {
                    productType: 'Sales',
                    feedbackId: this.feedbackId,
                    productId: item.id,
                    productCode: item.productCode,
                    productName: item.productName,
                    scanNumber: '0',
                    productStockQty: '0'
                }
            })
            // 插入
            await this.insertProd(mapList);
            // 在登记中查询
            await this.queryProductionPinList();
            await this.getData()
        },
        /**
         * 扫码产品记录查询
         * <AUTHOR>
         * @date 240318
         * */
        async pickScanProduction(item) {
            this.$nav.push('/pages/headquarters-activity/scan-code-record-page.vue', {
                id: item.feedbackId,
                headId: item.id,
                freshSourceFlag: 'upsert',
                approveStatus: this.approveStatus,
                status: this.status
            })
        },
        scanClick(data){
            this.clickScan(data.item,data.index)
        },
        /**
         * desc 点击扫码
         * <AUTHOR>
         * @date 240318
         */
         async clickScan(item, index) {
            await this.operateValidate();
            // if (this.$utils.isEmpty(item.id)) {
            //     await this.saveProd(item, index, 'new');
            // }
            const that = this;
            await that.getAddress();
            if (that.$utils.isEmpty(this.coordinate.latitude) && that.$utils.isEmpty(this.coordinate.longitude)) {
                this.$dialog({
                    title: '提示',
                    content: '请确认手机地理位置授权是否打开，或者【设置】-【企业微信】位置权限管理是否打开？',
                    cancelButton: false,
                    confirmText: '去开启',
                    onConfirm: async () => {
                        let userLocation = await this.$locations.openSetting();
                        if (userLocation['scope.userLocation']) {
                            that.coordinate = await that.$locations.getCurrentCoordinate();
                        }
                    }
                });
            } else {
                await wx.scanCode({
                    onlyFromCamera: true,
                    success: async (res) => {
                        that.$utils.showLoading();
                        await that.afterGetCode(res.result, that.productionPinList[index], index);
                        that.$utils.hideLoading();
                    }
                });
            }
        },
        /**
         * 获取码之后的处理
         * <AUTHOR>
         * @date 240318
         */
        async afterGetCode(mark, item, index) {
            try {
                this.$utils.showLoading();
                const data = await this.$http.post('action/link/headquarterFeedback/headOpenScanVerification', {
                    mark: mark,
                    headId: this.feedbackId,
                    province: this.addressData.province,
                    city: this.addressData.city,
                    district: this.addressData.district,
                    scanAddress: this.addressDataFull,
                    productType: 'Sales',
                    productId: item.productId,
                    productStockQty: item.productStockQty,
                    remark: this.addressData.remark,
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    this.$set(this.productionPinList[index], 'scanNumber', data.scanNumber);
                    this.$set(this.productionPinList[index], 'productNumber', data.productNumber);
                    // this.productionPinList.push(data)
                    // console.log(this.productionPinList)
                    this.$message.success('扫码成功');
                    setTimeout(() => {
                        this.clickScan(item, index);
                    }, 500);
                } else {
                    this.$utils.hideLoading();
                    this.$showError(data.message);
                }
            } catch (e) {
                console.log(e)
                this.$utils.hideLoading();
            }
        },
        /**
         * 获取定位地址  百度经纬度逆解析
         */
        async getAddress() {
            this.coordinate = await this.$locations.getCurrentCoordinate();
            // 校验用户是否授权地理位置
            if (!this.$utils.isEmpty(this.coordinate.latitude) && !this.$utils.isEmpty(this.coordinate.longitude)) {
                let address = await reverseTMapGeocoder(this.coordinate.latitude, this.coordinate.longitude, '新增-活动动销');
                this.addressData = address['originalData'].result.addressComponent;
                this.addressDataFull = address['originalData'].result.formatted_address;
                this.addressFlag = true;
            }
        },

        /**
         * 编辑产品信息调整数量
         * <AUTHOR>
         * @date 2023-04-06
         **/
        editProdItem() {
            this.$refs.prodBottom.show();
        },
        /**
         * 保存编辑的产品信息
         * <AUTHOR>
         * @date 2023-04-07
         **/
        async saveAdjustedProduct() {
            //如果id不为空时 为更新数据
            if (!this.$utils.isEmpty(this.adjustedProductItem.id)) {
                const updateData = {
                    id: this.adjustedProductItem.id,
                    unit: this.adjustedProductItem.unit,
                    scanNumber: this.adjustedProductItem.scanNumber,
                    productStockQty: this.adjustedProductItem.productStockQty,
                    updateFields: "id, unit, productStockQty"
                };
                await this.$http.post('action/link/headquarterProduct/update', updateData);
                this.$refs.prodBottom.hide();
            } else {
                this.$refs.prodBottom.hide();
            }

        },
        /**
         * 关闭底部弹窗
         * <AUTHOR>
         * @date 2023-04-06
         * @param param
         */
        closeDialog() {
            this.productDialogFlag = !this.productDialogFlag
        },

        /**
         * 侧滑删除一行
         * <AUTHOR> @date 2023-04-06
         * */
        deleteRow({item, index}) {
            this.$taro.showModal({
                title: '提示',
                content: '是否删除此动销记录？',
                success: async (res) => {
                    await this.deleteCurrentRow(item,index);
                }
            });
        },
        /**
         *  删除信息行的回调函数
         *  <AUTHOR>
         *  @date 2023-04-24
         */
        async deleteCurrentRow(item,index) {
            if(item.id){
                const data = await this.$http.post('action/link/headquarterProduct/deleteById', {
                    id: item.id
                });
                if (data.success) {

                    this.productionPinList.splice(index, 1);
                    this.$taro.showToast({
                        title: '删除成功'
                    });
                } else {
                    this.$taro.showToast({
                        title: '删除失败'
                    });
                }
            }else{
                this.productionPinList.splice(index, 1);
                this.$taro.showToast({
                    title: '删除成功'
                });
            }

        }
    }
}
</script>

<style lang="scss">
@import "../../../styles/list-card.scss";

.pin-register {
    .menu-stair {
        width: 100%;
        margin-left: 24px;
        padding-top: 40px;
        @include flex-start-center;

        .line {
            clear: both;

            .line-top {
                width: 8px;
                height: 16px;
                background: #3FE0E2;
            }

            .line-bottom {
                width: 8px;
                height: 16px;
                background: #2F69F8;
            }
        }

        .stair-title {
            width: 30%;
            margin-left: 16px;
            font-family: PingFangSC-Semibold, serif;
            font-size: 32px;
            color: #262626;
            letter-spacing: 1px;
            line-height: 32px;
        }

        .edit {
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #2F69F8;
            letter-spacing: 0;
            line-height: 28px;
            text-align: right;
            width: 58%;
        }
    }

    .view {
        // background: white;

        .item-header {
            height: 88px;
            width: 100%;
            padding-left: 32px;
            font-size: 28px;
            line-height: 88px;
            color: #262626;
            background: white;
            letter-spacing: 0;
            border-bottom: 2px solid #F2F2F2;
        }

        .product-list {
            font-size: 28px;
            color: #262626;
            letter-spacing: 0;
            line-height: 38px;
            .new-box{
                margin-top: 28px;
                padding: 20rpx;
            }
            .product-list-item {
                background: #FFFFFF;
                // width: 95%;
                padding: 20px;
                margin: auto;
                border-radius: 16px;

                .media-list {
                    @include media-list;

                    .media-top {
                        width: 100%;
                        @include flex-start-center;
                        @include space-between;
                        height: 80px;
                        line-height: 80px;

                        .left-content {
                            font-family: PingFangSC-Semibold;
                            font-size: 32px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 32px;
                            padding-top: 20px;

                        }

                        .right-content {
                            font-family: PingFangSC-Semibold;
                            font-size: 32px;
                            color: #FF5A5A;
                            letter-spacing: 0;
                            text-align: right;
                            line-height: 32px;
                            padding-top: 20px;
                        }

                        .num-view {
                            background: #A6B4C7;
                            border-radius: 8px;
                            line-height: 50px;

                            .num {
                                font-size: 28px;
                                color: #FFFFFF;
                                letter-spacing: 0;
                                line-height: 40px;
                                padding: 2px 8px;
                            }
                        }

                        .status-view {
                            width: 120px;
                            transform: skewX(-10deg);
                            border-radius: 4px;
                            background: #2F69F8;
                            box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                            height: 36px;

                            .status {
                                font-size: 20px;
                                color: #FFFFFF;
                                letter-spacing: 2px;
                                text-align: center;
                                line-height: 36px;
                            }
                        }
                    }
                }

                .content-middle {
                    width: 100%;
                    @include flex-start-center;
                    @include space-between;
                    height: 80px;
                    line-height: 80px;

                    .content {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #000000;
                        letter-spacing: 0;
                    }

                    .name {
                        font-family: PingFangSC-Semibold;
                        font-size: 32px;
                        color: #000000;
                        letter-spacing: 0;
                        line-height: 32px;
                    }
                }

                .content-middle-line {
                    width: 100%;

                    .data {
                        width: 50%;
                        float: left;

                        .title {
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #000000;
                            letter-spacing: 0;
                            line-height: 56px;
                            width: 30%;
                            float: left;
                        }

                        .val {
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #000000;
                            letter-spacing: 0;
                            line-height: 56px;
                            float: left;
                        }

                        .icon {
                            float: left;
                            line-height: 56px;
                            padding-left: 2%;
                        }

                        .Submitted, .Feedback {
                            color: #2F69F8;
                        }

                        .Approve, .FeedbackApro {
                            color: #2EB3C2;
                        }

                        .Refused, .Refeedback {
                            color: #FF5A5A;
                        }

                    }

                    .sum {
                        width: 50%;
                        float: left;

                        .title {
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #000000;
                            letter-spacing: 0;
                            line-height: 56px;
                            float: left;
                            width: 35%;
                        }

                        .val {
                            font-family: PingFangSC-Regular;
                            font-size: 28px;
                            color: #000000;
                            letter-spacing: 0;
                            line-height: 56px;
                            white-space: nowrap;
                            float: left;
                        }

                        .icon {
                            padding-left: 2%;
                            float: left;
                            // line-height: 56px;
                        }
                    }
                }

                .store-input {
                    padding-top: 16px;
                    width: 100%;
                    @include flex-start-center();
                    @include space-between();
                    font-family: PingFangSC-Regular,serif;
                    font-size: 28px;
                    letter-spacing: 0;
                    .store-input-rows {
                        @include flex-start-center();
                        .label {
                            color: #000000;
                        }
                        .value {
                            padding-left: 8px;
                            color: #000000;
                        }
                        .value-input {
                            @include flex-start-center;
                            .unit {
                                color: #262626;
                            }
                            input {
                                color: #262626;
                                margin: auto 8px;
                                text-align: center;
                                background: #F2F2F2;
                                border-radius: 8px;
                                width: 80px;
                                height: 48px;
                            }
                        }
                    }
                }
            }
        }

        .table-v {
            padding-bottom: 40px;
        }

        .table {
            display: table;
            border-collapse: collapse;
            border: 1px solid #ccc;
            width: 100%;
        }

        .table-caption {
            display: table-caption;
            margin: 0;
            padding: 0;
            font-size: 16px;
        }

        .table-column-group {
            display: table-column-group;
        }

        .table-column-16 {
            display: table-column;
            width: 320px;
        }

        .table-column-8 {
            display: table-column;
            width: 160px;
        }

        .table-row-group {
            display: table-row-group;
        }

        .table-row {
            display: table-row;
            height: 80px;
            line-height: 80px

        }

        .table-row-group .table-row:hover, .table-footer-group .table-row:hover {
            background: #f6f6f6;
        }

        .table-cell {
            display: table-cell;
            padding: 0 5px;
            border: 1px solid rgba(47, 105, 248, 0.10);
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #262626;
            letter-spacing: 0;
            line-height: 40px;
            vertical-align: middle;
            text-align: center;
        }

        .table-cell-title {
            display: table-cell;
            padding: 0 5px;
            border: 1px solid rgba(47, 105, 248, 0.10);
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #2F69F8;
            letter-spacing: 0;
            text-align: center;
            line-height: 28px;
            vertical-align: middle;
        }

        .table-header-group {
            display: table-header-group;
            background: rgba(47, 105, 248, 0.07);
            font-weight: bold;
        }

        .table-footer-group {
            display: table-footer-group;
        }
    }
    .link-dialog.link-dialog-no-padding .link-dialog-body {
        padding: 0;
        margin-bottom: 100px;
    }
    .model-title {
        display: flex;

        .title {
            font-family: PingFangSC-Regular, serif;
            font-size: 32px;
            color: #262626;
            letter-spacing: 0;
            text-align: center;
            line-height: 96px;
            height: 96px;
            width: 100%;
            margin-right: 80px;
            padding-left: 0;
            margin-left: 170px;
        }

        .icon-close {
            color: #BFBFBF;
            font-size: 48px;
            line-height: 96px;
            height: 96px;
            margin-right: 20px;
        }
    }
    .link-sticky .link-sticky-content {
        width: 95vw;
        position: relative;
        display: flex;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        margin: 0 20px;
        justify-content: center;
    }
    .link-button.link-button-mode-fill.link-button-status-primary {
        height: 100px;
    }
}
</style>
