<!--
登记备注
<AUTHOR>
@date	2023/8/4
-->
<template>
    <view class="agree-check-remark">
        <view class="check-head" v-if="remarkOption.list && remarkOption.list.length > 0">
            <view>登记备注</view>
        </view>
        <link-auto-list :option="remarkOption" :hideCreateButton="true">
            <template slot-scope="{data,index}">
                <view class="check-item">
                    <view class="row-item">
                        <view class="item-left">
                            <text>创建时间：</text>
                            <text>{{data.created}}</text>
                        </view>
                    </view>
                    <view class="row-item">
                        <view class="item-left">
                            <text>创建人：</text>
                            <text>{{data.operator}}</text>
                        </view>
                    </view>
                    <view class="row-item">
                        <view class="item-left">
                            <text>备注：</text>
                            <text>{{data.remark}}</text>
                        </view>
                    </view>
                </view>
            </template>
        </link-auto-list>
    </view>
</template>

<script lang="jsx">

export default {
    name: 'agree-check-remark',
    props: {
        headId: String
    },
    data() {
        const remarkOption = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/headMark/queryByExamplePage'
            },
            param: {
                filtersRaw: [{id: 'headId', property: 'headId', value: this.headId}]
            }
        })
        return {
            remarkOption
        }
    }
}
</script>

<style lang="scss">
.agree-check-remark {
    margin: 24px;
    background: #fff;
    border-radius: 16px;
    font-size: 28px;
    color: #262626;

    .check-head {
        display: flex;
        justify-content: space-between;
        padding: 24px;
        border-bottom: 2px solid #ccc;
        font-weight: 600;
    }

    .check-item {
        border-bottom: 2px solid #ccc;
        background: #fff;
        padding: 24px;
        border-radius: 16px;
        font-size: 28px;

        .row-item {
            display: flex;
            margin-bottom: 10px;

            .item-left {
                flex: 1;
            }
        }
    }

    .link-auto-list-no-more {
        display: none !important;
    }
}
</style>
