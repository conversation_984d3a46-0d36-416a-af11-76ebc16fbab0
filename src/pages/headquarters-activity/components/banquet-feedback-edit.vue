<!--
总部活动-宴席列表详情-执行反馈-编辑
<AUTHOR>
@date 2023-07-12
@file banquet-feedback-edit
-->
<template>
    <view class="banquet-feedback-edit">
        <line-title title="执行反馈" class="head-title"></line-title>
        <view style="width: 100%;height: 16px"></view>
        <link-form :value="formData" ref="banquetFeedback" :rules="rules">
            <link-form-item label="实际宴席桌数" field="actTableNum" required>
                <link-number-keyboard v-model="formData.actTableNum" :min="0" @input="changeBanquetActFee"/>
            </link-form-item>
            <template v-if="!shareFlag">
                <link-form-item label="每桌奖励费用" field="actTableReward" type="number" required v-if="showActTableReward">
                    <link-number-keyboard v-model="formData.actTableReward" :min="0" :disabled="showActTableReward"/>
                </link-form-item>
                <link-form-item label="实际推荐费总额" field="banquetActFee" >
                    <link-input v-model="formData.banquetActFee" type="number"/>
                </link-form-item>
                <link-form-item label="推荐费限额" field="banquetPlanFee">
                    <link-input v-model="formData.banquetPlanFee" disabled/>
                </link-form-item>
                <link-form-item class="fee-rule-desc" label="推荐费规则说明" field="feeRuleDesc">
                    <link-textarea v-model="formData.feeRuleDesc" disabled/>
                </link-form-item>

                <!--终端出货数量、终端退货、消费者开瓶，开瓶率-->
                <link-form-item label="终端出货数量(瓶)" field="salesoutNum">
                    <link-input v-model="formData.salesoutNum" disabled/>
                </link-form-item>
                <link-form-item label="终端退货(瓶)" field="consalesinNum">
                    <link-input v-model="formData.consalesinNum" disabled/>
                </link-form-item>
                <link-form-item label="消费者开瓶(瓶)" field="consumerCrackNum">
                    <link-input v-model="formData.consumerCrackNum" disabled/>
                </link-form-item>
                <link-form-item label="开瓶率" field="consumerCrackRatio">
                    <link-input v-model="formData.consumerCrackRatio" disabled/>
                </link-form-item>
            </template>
            <!-- 扫码按钮：是否需要用酒扫码为是时展示 -->
            <link-form-item label="实际用酒扫码" :class="{'share-flag-applied': !shareFlag}" v-if="banquetPolicy.scanFlag !== 'N'">
            </link-form-item>
            <!-- 扫码按钮 -->
            <view class="scan-wrap" v-if="banquetPolicy.scanFlag !== 'N'">
                <view class="iconfont icon-saoma1" @tap="scanCode"></view>
            </view>
            <!-- 扫码详情 -->
            <banquet-feedback-scan-code ref="scanCode"
                                        :activityId="formData.id"
                                        v-if="formData.id"
                                        :shareFlag="shareFlag"
            ></banquet-feedback-scan-code>
        </link-form>
        <!--执行反馈拍照-->
        <view v-if="banquetPolicy.scenePicture !== 'N' || banquetItem.takePicturesFlag !== 'Y'">
            <view style="margin: 8px 16px;font-size: 16px;color: #262626;letter-spacing: 1px;line-height: 32px;">执行反馈拍照</view>
            <lnk-no-auth v-if="imgAuthFlag"></lnk-no-auth>
            <view style="margin:12px 0">
                <view v-show="!imgAuthFlag">
                    <view v-for="(item,index) in picturesNeedUploaded" :key="index+1" class="upload-1">
                        <banquet-feedback-upload-photo :activityId="banquetItem.id"
                                                       :basePlaceholder="item.base.placeholder"
                                                       :valuesPlaceholder="item.values.placeholder"
                                                       :cameraRefresh="cameraRefresh"
                                                       :operationFlag="operationFlag"
                                                       :isNoCount="isNoCount"
                                                       :maxImgLength="maxImgLength"
                                                       ref="pic"
                                                       :moduleType="item.ctrlCode"
                                                       :object-type="'picturesNeedUploaded'"
                                                       :useModuleName="item.ctrlCode | lov('TMPL_SUB_BIZ_TYPE')"
                                                       :required="item.base.require"
                                                       :isNoPhoto="banquetItem.takePicturesFlag === 'Y'"
                                                       :shareFlag="shareFlag">
                        ></banquet-feedback-upload-photo>
                    </view>
                </view>
            </view>
        </view>
        <!--备注-->
        <view class="feedbackRemark" v-if="!shareFlag" :style="{'padding': 12 +'px','margin-bottom': 0 + 'px'}">
            <view class="label" style="font-size: 14px; padding: 0px 0px 12px 4px;">备注</view>
            <view class="textarea-big">
                <link-textarea class="text-area" :placeholder="'请输入备注'"
                               mode="textarea"
                               v-model="formData.feedbackRemark"
                               :nativeProps="{maxlength:500}"
                               style="padding: 0px"
                               placeholder-style="color: #BFBFBF;"
                ></link-textarea>
                <view v-if="!formData.feedbackRemark" class="textarea-length">0/500</view>
                <view v-else :class="formData.feedbackRemark.length < 501 ? 'textarea-length' : 'textarea-over-length'" v-model="formData.feedbackRemark.length" >{{formData.feedbackRemark.length+'/500'}}</view>
            </view>
        </view>
    </view>
</template>

<script>
import LineTitle from "../../lzlj/components/line-title.vue";
import banquetFeedbackUploadPhoto from "./banquet-feedback-upload-photo.vue";
import BanquetFeedbackScanCode from "./banquet-feedback-scan-code.vue";
export default {
    name: 'banquet-feedback-edit',
    components: {BanquetFeedbackScanCode, LineTitle, banquetFeedbackUploadPhoto},
    props: {
        banquetPolicy: {
            type: Object,
            default: () => {}
        },
        banquetItem: {
            type: Object,
            default: function () {
                return {};
            }
        },
        shareFlag: {
            type: Boolean,
        },
        isNoCount: {
            type: Boolean,
        },
        maxImgLength: {
            type: Number
        }
    },
    data() {
        return {
            formData: {},
            imgAuthFlag: false, //图片模板无权限
            picturesNeedUploaded: [],//需要上传的图片-后台查询对应业务场景配置的图片
            regReqList: [],//登记要求
            cameraRefresh: false,// 相机刷新标志
            operationFlag: true,//是否可以操作照片的新建和删除
            scannedCode: '', // 扫描的码
            addressInfo: {}, // 地址信息
            scanCodeFlag: false, // 是否扫码，默认为false，扫了码有扫码记录了为true
            showActTableReward: true, // 是否显示每桌奖励费用
            nowBanquetActFee: '', // 当前实际推荐费总额
            rules: {
                banquetActFee: this.Validator.number(),
            }
        }
    },
    async created() {
        // 通过 watch 监听 banquetItem 变化
        this.$watch('banquetItem', function (newVal, oldVal) {
            if (newVal && Object.keys(newVal).length > 0) {
                // banquetItem 有值后执行的逻辑
                this.formData.registerRequest = newVal.registerRequest;
                this.formData = {...newVal};
                this.regReqList = newVal.regReqList;
                console.log('分享页 this.banquetItem', newVal);
                console.log('分享页执行反馈 this.formData', this.formData);
                this.queryBasicTerInfo();
                this.querySceneImg();
                this.getAddressInfo();
            }
        }, {immediate: true}); // 立即执行一次
        this.getPolicyType();
    },
    methods: {
        /**
         *  实际推荐费总额为 实际宴席桌数 和 每桌奖励费用相乘
         *  <AUTHOR>
         *  @date 2023-08-03
         **/
        changeBanquetActFee() {
            if (this.formData.policyType == 'byTable' && this.formData.actTableNum && this.formData.actTableReward) {
                const banquetActFee = (this.formData.actTableNum * this.formData.actTableReward).toFixed(2);
                this.$set(this.formData, 'banquetActFee', banquetActFee);
            }
        },
        /**
         * 查询终端出货数量、终端退货、消费者开瓶，开瓶率信息
         *  <AUTHOR>
         *  @date 2023-08-03
         **/
        async queryBasicTerInfo() {
            try {
                const data = await this.$http.post('action/link/headquarterFeedback/queryById', {
                    id: this.banquetItem.id
                })
                if (data.success) {
                    const consumerCrackRatio = data.result.consumerCrackRatio;
                    const consumerCrackRatioPercentage = (consumerCrackRatio !== null && consumerCrackRatio !== undefined)
                        ? (consumerCrackRatio === 0 ? '0%' : (consumerCrackRatio * 100).toFixed(0) + '%')
                        : '0%';
                    this.$set(this.formData, 'salesoutNum', data.result.salesoutNum);
                    this.$set(this.formData, 'consalesinNum', data.result.consalesinNum);
                    this.$set(this.formData, 'consumerCrackNum', data.result.consumerCrackNum);
                    this.$set(this.formData, 'consumerCrackRatio', consumerCrackRatioPercentage);
                } else {
                    this.$message.warn("查询终端进出货等数据失败，请稍后重试！");
                }
            } catch (e) {
                this.$message.warn("查询终端进出货等数据失败，请稍后重试！");
            }
        },
        /**
         * desc 获取填写表单的值
         * <AUTHOR>
         * @date 2023-07-13
         * @params
         */
        async getFormData() {
            return this.formData;
        },
        /**
         * desc 获取当前上传的图片信息
         * <AUTHOR>
         * @date 2023-07-13
         * @params
         */
        async getUploadPhotoInfo() {
            for (let i = 0; i < this.picturesNeedUploaded.length; i++) {
                let item = this.picturesNeedUploaded[i];
                // 如果必输,则校验必输的值是否为空
                if (!!item.base['require'] && !this.$refs.pic[i].$refs.img.imgData.length) {
                    this.$message.warn(`请上传${item.base['label']}的图片！`);
                    return Promise.reject(false);
                }
            }
        },
        /**
         * desc 判断宴席单关联的政策类型
         * <AUTHOR>
         * @date 2023-08-21
         * @params
         */
        getPolicyType() {
            // 桌数型展示每桌奖励费用，后端已用perTableBonusAmount赋值
            // 场次型不展示每桌奖励费用
            if(this.formData.policyType == 'bySessions') {
                this.showActTableReward = false;
                this.formData.banquetActFee = this.formData.banquetPlanFee;
            };
        },
        /**
         * desc 校验必填信息
         * <AUTHOR>
         * @date 2023-07-13
         * @params
         */
        async validateInfo() {
            await this.$refs.banquetFeedback.validate();
            if(!this.shareFlag && this.banquetPolicy.scanFlag !== 'N' && this.$refs.scanCode.scanRecordOption.list.length == 0) {
                this.$message.error('实际用酒情况需扫码！');
                return Promise.reject('实际用酒情况需扫码！');
            }
        },
        /**
         * desc 获取当前地址
         * <AUTHOR>
         * @date 2023-07-13
         * @params
         */
        async getAddressInfo() {
            const that = this;
            this.coordinate = await that.$locations.getCurrentCoordinate();
            // 校验用户是否授权地理位置
            if (!this.$utils.isEmpty(this.coordinate.latitude) && !this.$utils.isEmpty(this.coordinate.longitude)) {
                let address = await that.$locations.reverseTMapGeocoder(this.coordinate.latitude, this.coordinate.longitude, '宴席活动执行反馈');
                console.log('address', address);
                this.addressInfo = {
                    province: address['originalData'].result.addressComponent['province'],
                    city: address['originalData'].result.addressComponent['city'],
                    district: address['originalData'].result.addressComponent['district'],
                    scanAddress: address['originalData'].result.formatted_address
                }
            }
        },
        /**
         * desc 执行反馈扫码
         * <AUTHOR>
         * @date 2023-07-12
         * @params
         */
        async scanCode() {
            const that = this;
            await wx.scanCode({
                onlyFromCamera: true, // 只允许从相机扫码
                success: async (res) => {
                    if (res.result) {
                        that.scannedCode = res.result;
                        that.getCodeData({
                            id: that.formData.id,
                            activityId: that.formData.activityId,
                            productId: that.formData.banquetProdId,
                            mark: res.result,
                            ...that.addressInfo,
                            feedbackRemark: that.formData.feedbackRemark,
                        });
                    }
                }
            });
        },

        /**
         * desc 获取扫码数据
         * <AUTHOR>
         * @date 2023-07-12
         */
        async getCodeData(param) {
            try {
                this.$utils.showLoading();
                const {success, rows} = await this.$http.post('action/link/headquarterFeedback/banquetScanVerification', param)
                if (success) {
                    this.$refs.scanCode && this.$refs.scanCode.scanRecordOption.list.unshift(rows);
                    this.$message.success('扫码成功！');
                    setTimeout(() => {
                        this.scanCode()
                    }, 500)
                }
            } catch (e) {
                console.log('扫码错误！');
            } finally {
                this.$utils.hideLoading();
            }
        },
        /**
         * desc 获取扫码信息
         * <AUTHOR>
         * @date 2023-07-13
         * @params
         */
        async getScanCodeFlag() {
            console.log('this.$refs.scanCode.scanRecordOption.list.length',this.$refs.scanCode.scanRecordOption.list.length);
            if(this.$refs.scanCode.scanRecordOption.list.length > 0) {
                this.scanCodeFlag = true;
            }
            console.log('edit中的this.scanCodeFlag',this.scanCodeFlag);
            return this.scanCodeFlag;
        },
        /**
         * desc 根据业务场景查询配置的需要维护的场景
         * <AUTHOR>
         * @date 2023-07-12
         */
        async querySceneImg() {
            // 是否需要拍照为否时不调用图片模板
            if (this.formData.scenePicture === 'N') {
                return;
            }
            this.picturesNeedUploaded = [];
            let picturesNeedUploaded = [];
            // 获取模版子类型，将返回的登记要求字符串转化为数组
            if (this.formData.registerRequest) {
                this.regReqList = JSON.parse(this.formData.registerRequest);
            }
            for (let i = 0; i < this.regReqList.length; i++) {
                let tempType = this.regReqList[i];
                const data = await this.$utils.getQwMpTemplate('HeadFeedbackData', tempType);
                if (!data.success) {
                    this.imgAuthFlag = true;
                    this.$utils.hideLoading();
                } else {
                    this.imgAuthFlag = false;
                }
                let resultOpt = JSON.parse(data.result);
                let temp = [];//当前业务场景配置的场景图片数组信息
                temp = JSON.parse(resultOpt.conf);
                picturesNeedUploaded.unshift(temp[0]);
            }
            this.picturesNeedUploaded = picturesNeedUploaded;
        },

    }
}
</script>

<style lang="scss">
.banquet-feedback-edit {
    .scan-wrap {
        background: #fff;
        padding-left: 13px;
        .icon-saoma1 {
            color: #2945E8;
            font-size: 184px;
        }
    }
    .textarea-big {
        position: relative;
        .textarea-length {
            position: absolute;
            right: 0;
            bottom: 0;
            padding: 0px 20px 16px 0px;
            color: #8C8C8C;
            font-size: 28px;
        }
        .textarea-over-length {
            position: absolute;
            right: 0;
            bottom: 0;
            padding: 0px 40px 16px 0px;
            color: #ff5a5a;
            font-size: 28px;
        }
    }
    .link-form-item.share-flag-applied  .link-item:before {
        position: absolute;
        top: 10px;
        left: 0;
        bottom: 0;
        width: 1em;
        content: "*";
        color: #ff5a5a;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
    }
    .fee-rule-desc {
        .link-item {
            display: block;
            .link-textarea {
                margin-top: 20px;
                padding: 0;
                width: 100%;
                .link-textarea-content {
                    height: 200px !important;
                }
            }
        }
    }
}
</style>
