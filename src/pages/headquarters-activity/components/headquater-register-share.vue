<!--
总部活动-营销6.0-一键转发到外部新增登记
<AUTHOR>
@date 2023-09-14
@file headquater-register-share
-->
<template>
    <link-page class="headquater-register-share">
        <template>
            <link-form :value="formData" :rules="rules" ref="upsertAct">
                <link-form-item label="总部活动" required>
                    <link-input v-model="formData.activityName" disabled/>
                </link-form-item>
                <link-form-item label="终端" required>
                    <link-input v-model="formData.accountName" disabled/>
                </link-form-item>
                <link-form-item label="政策类型" field="policyType" required>
                    <link-lov type="MARKETSIX_POLICY_TYPE" v-model="formData.policyType"/>
                </link-form-item>
                <link-form-item label="活动日期" field="activityDate" required>
                    <link-date view="YMD" v-model="formData.activityDate" value-format="YYYY-MM-DD"
                               display-format="YYYY-MM-DD" disabled/>
                </link-form-item>
                <link-form-item label="活动阶段" field="stageName">
                    <link-lov type="PROMOTION_STAGE" v-model="formData.stageName || '选择活动日期后显示'" disabled/>
                </link-form-item>
                <!-- 240328迭代 去除活动时段 -->
                <!-- <link-form-item label="活动时段" field="feedbackStage" required>
                    <link-lov type="HEAD_FEEDBACK_TIME" v-model="formData.feedbackStage"/>
                </link-form-item> -->
                <link-form-item label="包间号" field="privateRoom" required>
                    <link-input v-model="formData.privateRoom" @blur="afterPrivateRoom()"/>
                </link-form-item>
                <link-form-item label="当日用餐包间数量" field="privateNumber" required>
                    <link-number-keyboard v-model="formData.privateNumber" :min="1" :max="1000"/>
                </link-form-item>
                <link-form-item label="参与人数(人/场)" field="clientNumber" required>
                    <!-- <link-input v-model="formData.clientNumber" type="number"></link-input> -->
                    <link-number-keyboard  v-model="formData.clientNumber" :min="0" :max='99'/>
                </link-form-item>
            </link-form>
            <!-- 小酒产品 -->
            <link-swipe-action v-for="(item, index) in prodList" :key="index">
                <link-swipe-option slot="option" @tap="deleteItem(item, index)" v-if="item.scanNumber == 0">删除
                </link-swipe-option>
                <link-form :value="item">
                    <link-form-item label="小酒产品名称" field="productName" required>
                        <view @tap="chooseProduct(item, index)" class="productName">
                            <text :class="[item.productName ? '' : 'prod-grey']">
                                {{ item.productName ? item.productName : '选择小酒产品' }}
                            </text>
                            <link-icon icon="icon-right"/>
                        </view>
                    </link-form-item>
                    <link-form-item label="开瓶数量" field="productNumber" required>
                        <link-number-keyboard v-model="item.productNumber"  :min="0" :max='99'
                                              @input="changeProdNum(item, index)"/>
                    </link-form-item>
                    <link-form-item v-if="scanCodeNoBatchNumber === 'Y'" label="开瓶扫码" field="scanNumber">
                        <link-icon style="color: #1E8BFF; text-align: right; font-size: 24px;" icon="icon-scan"
                                   v-if="item.productNumber > item.scanNumber" @tap="clickScan(item, index)"/>
                    </link-form-item>
                    <link-form-item label="开瓶扫码数量" field="scanNumber">
                        <item slot="custom" title="开瓶扫码数量">
                            <view style="text-align: right; color: #333333" @tap="gotoOpenRecord(item)">
                                {{ item.scanNumber }}瓶
                            </view>
                        </item>
                    </link-form-item>
                </link-form>
            </link-swipe-action>
            <!-- 添加小酒产品按钮 -->
            <view class="add-prod" @tap="addProd">
                <text class="iconfont icon-plus"></text>
                <text class="text">添加小酒产品</text>
            </view>
            <!--动销登记-->
            <pin-register v-if="formData.id"
                          :id="formData.accountId"
                          :activityId='formData.activityId'
                          :feedbackId="formData.id"
                          :operateValidate="operateValidate"
                          :status="regStatus"
                          :approveStatus="formData.approveStatus"
                          ref="productionPin"></pin-register>
            <!--            :sourceFlag="pageParam.sourceFlag"-->
            <!--现场取证材料-->
            <view class="menu-stair" style="margin-bottom: 12px">
                <view class="line">
                    <view class="line-top"></view>
                    <view class="line-bottom"></view>
                </view>
                <view class="stair-title">反馈材料</view>
            </view>
            <!--图片模板无权限-->
            <lnk-no-auth v-if="imgAuthFlag"></lnk-no-auth>
            <view style="margin: 12px">
                <view v-show="!imgAuthFlag">
                    <view v-if="formData.id">
                        <view v-for="(item,index) in picturesNeedUploaded" :key="index+1" class="upload-1">
                            <site-materials :activityId="formData.id"
                                            :basePlaceholder="item.base.placeholder"
                                            :valuesPlaceholder="item.values.placeholder"
                                            :cameraRefresh="cameraRefresh"
                                            :operationFlag="operationFlag"
                                            :sourceFlag="'forward'"
                                            ref="pic"
                                            :moduleType="item.ctrlCode"
                                            :object-type="'picturesNeedUploaded'"
                                            :useModuleName="item.ctrlCode | lov('TMPL_SUB_BIZ_TYPE')"
                                            :required="item.base.require">
                            </site-materials>
                        </view>
                    </view>
                </view>
            </view>
            <link-sticky>
                <link-button block @tap="save">保存</link-button>
                <link-button block @tap="newSave">保存并新建</link-button>
            </link-sticky>
        </template>
        <link-dialog v-model="openFlag" title="">
            <view>
                {{startShow}}
            </view>
            <link-button slot="foot" @tap='openFlag = false'>确定</link-button>
        </link-dialog>
    </link-page>
</template>

<script>
import {ComponentUtils, DeviceService} from "link-taro-component";
import LnkNoAuth from "../../core/lnk-no-auth/lnk-no-auth";
import SiteMaterials from "./site-materials";
import FeedbackMaterials from "./feedback-materials";
import pinRegister from "./pin-register";
import Taro from "@tarojs/taro";
import {ROW_STATUS} from "../../../utils/constant";
import {reverseTMapGeocoder} from "../../../utils/locations-tencent";

export default {
    name: 'headquater-register-share',
    components: {LnkNoAuth, SiteMaterials, FeedbackMaterials, pinRegister},
    props: {
        formData1: {
            type: Object,
            default: function () {
                return {};
            }
        },
    },
    data() {
        // 选择小酒产品
        const productOption = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/activityProduct/queryByExamplePage'
            },
            searchFields: ['productName'],
            param: () => {
                return {
                    rows: 25,
                    feedbackId: this.formData.id,
                    filtersRaw: [
                        {id: 'activityId', property: 'activityId', value: this.formData.activityId}
                    ]
                }
            },
            renderFunc: (h, {data, index}) => {
                return (
                    <item arrow={false} title={data.productName} key={data.id} data={data} content={data.productCode}/>
                )
            }
        });
        return {
            openFlag: true,
            startShow: '',
            formData: {},
            rules: {
                activityName: this.Validator.required('活动名称必填'),
                accountName: this.Validator.required('终端必选'),
                activityDate: this.Validator.required('活动日期必填'),
                stageName: this.Validator.required('请先维护该活动日期的推广阶段！'),
                privateRoom: this.Validator.required('包间号必填！'),
                privateNumber: [
                    this.Validator.required('当日用餐包间数量必填！'),
                    this.Validator.number({min: 1, max: 1000})
                ],
                clientNumber: this.Validator.number({min: 0}),
            },
            productOption,
            coordinate: {}, // 存储地理经纬度信息
            addressData: {},  // 地理位置信息
            addressDataFull: '', //详细地理位置
            addressFlag: false, //是否显示地理位置
            cameraRefresh: false,// 相机刷新标志
            imgAuthFlag: false, //图片模板无权限
            picturesNeedUploaded: [],//需要上传的图片-后台查询对应业务场景配置的图片
            operationFlag: true,//是否可以操作照片的新建和删除
            temp: {},    //模版数据
            id: '', //登记活动id
            regReqList: [], //登记要求列表
            isChooseProd: false,  //是否可选择小酒产品
            isChooseAct: false,  //是否可选择活动，默认可选
            sourceFlag: '', //来源
            clickScanFlag: true, //点击扫码
            scanCodeList: [], //扫码记录列表
            isMounted: false, //是否已挂载
            createdTime: '', //编辑时获取创建时间
            regNameMMDD: '', //时间
            scanCodeNoBatchNumber: '', // 企业参数--是否无需指定特定小酒的批次号就可扫码(Y/N)
            batchNumber: [], // 企业参数--指定某些特定小酒的批次号才可扫码
            newSaveFlag: false, //保存并新建
            commitFlag: false, //提交标识
            privateRoomList: [],    // 当前...包间号列表
            prodList: [], // 产品列表
            choseStageName: 0,//设置选中活动阶段
            navigationBarTitle: '新增登记活动',
            navigationBarTitleColor: '#ffffff',
            zIndex: ComponentUtils.nextIndex(),
            navBackgroundColor: 'transparent',
            row_status: 'NEW',
            regStatus: 'NEW', // 登记活动状态默认为新建
        }
    },
    async mounted() {
        this.formData = {...this.formData1};
        // 政策类型默认值
        this.formData.policyType = await this.defaultPolicyType();
        this.$set(this.formData, 'id', await this.$newId());
        // 是否无需指定特定小酒的批次号就可扫码
        this.scanCodeNoBatchNumber = await this.$utils.getCfgProperty(
            "ALLOW_SCAN_CODE_VERIFICATION"
        );
        // 指定某些特定小酒的批次号才可扫码
        const batchNumber = await this.$utils.getCfgProperty(
            "SCANNABLE_CODE_BATCH_NUMBER"
        );
        const startShow = await this.$utils.getCfgProperty(
            "New_Feedback_Prompt"
        );
        this.startShow = startShow
        this.batchNumber = batchNumber.split(',');
        await this.querySceneImg();
        this.isMounted = true;
    },
    watch: {
        'formData.productNumber': {
            deep: true,
            async handler(newVal, oldVal) {
                // 在组件挂载后才开始监听
                if (!this.isMounted) return;
                if (newVal > oldVal) {
                    this.clickScanFlag = true;
                    this.formData.productNumber = newVal;
                }
                if (newVal < this.formData.scanNumber) {
                    this.$message.warn('重新编辑开瓶数量，开瓶数量需大于扫码数量！');
                }
                if (newVal === this.formData.scanNumber && this.formData.scanNumber) {
                    this.clickScanFlag = false;
                }
            },
        },
        'formData.accountName': {
            async handler(newValue, oldValue) {
                // lzljqw-004-708在填写完包间号后就自动生成登记名称
                // if (this.formData.feedbackCode && this.formData.accountName && this.formData.privateRoom) {
                if (!this.isMounted) return;
                if (this.formData.accountName && this.formData.privateRoom) {
                    await this.generateRegName();
                }
            }
        },
        'formData.privateRoom': {
            async handler(newValue, oldValue) {
                // lzljqw-004-708在填写完包间号后就自动生成登记名称
                // if (this.formData.feedbackCode && this.formData.accountName && this.formData.privateRoom) {
                if (!this.isMounted) return;
                if (this.formData.accountName && this.formData.privateRoom) {
                    await this.generateRegName();
                }
            }
        },
        // @make edit by 谭少奇 2023/07/20 14:45 数据请求新增已完成类型,数据为多条时需手动选择活动阶段数据
        'formData.activityDate': {
            // deep: true,
            async handler(newValue, oldValue) {
                if (!this.isMounted) return;
                if (this.formData.accountId && this.formData.activityDate) {
                    try {
                        const {rows, success} = await this.$http.post('action/link/promotionStage/queryByExamplePage', {
                            filtersRaw: [
                                // {id: 'approvalStatus', property: 'approvalStatus', value: 'Approved', operator: '='},
                                // {
                                //     id: 'stageStatus',
                                //     property: 'stageStatus',
                                //     value: "[Processing,Completed]",
                                //     operator: 'in'
                                // },
                                {
                                    id: 'startTime',
                                    property: 'startTime',
                                    value: this.formData.activityDate,
                                    operator: '<='
                                },
                                {id: 'endTime', property: 'endTime', value: this.formData.activityDate, operator: '>='},
                                {
                                    id: 'actId',
                                    property: 'actId',
                                    value: this.formData.activityId,
                                    operator: '='
                                },
                            ],
                            pageFlag: true,
                            rows: 5,
                            page: 1
                        })
                        if (success) {
                            if (rows.length === 0) {
                                this.$set(this.formData, 'stageName', '');
                                this.$dialog({
                                    title: '提示',
                                    content: '请先维护该终端的推广阶段再进行登记！',
                                    cancelButton: false,
                                    confirmText: '确定',
                                });
                            } else if (rows.length === 1) {
                                this.$set(this.formData, 'stageName', rows[0].promotionStage);
                                this.formData.promotionStageId = rows[0].id;
                            } else {
                                const allTime = await this.$lov.getLovByType('PROMOTION_STAGE')
                                rows.forEach((i, j) => {
                                    const choseObj = allTime.find(ite => {
                                        return ite.val == i.promotionStage
                                    })
                                    i.valName = choseObj?.name
                                })
                                this.$dialog({
                                    title: '请选择时间内的活动阶段',
                                    content: (h) => {
                                        let newArr = []
                                        rows.forEach((item, index) => {
                                            const style = {
                                                class: `${index === this.choseStageName ? 'chose-stage' : ''}`,
                                                style: `fontSize:16px;margin:15px`
                                            }
                                            newArr.push(h('view', [
                                                <view
                                                    {...style}
                                                    onTap={() => {
                                                        this.choseStageName = index
                                                        this.$set(this.formData, 'stageName', item.promotionStage);
                                                        this.$set(this.formData, 'promotionStageId', item.id);
                                                    }}>
                                                    {`${item.valName}: ${item.startTime}-${item.endTime}`}
                                                </view>
                                            ]))
                                        })
                                        return h('view', newArr)
                                    },
                                    cancelButton: true,
                                    onConfirm: () => {
                                    },
                                    onCancel: () => {
                                        this.$set(this.formData, 'stageName', null);
                                        this.$set(this.formData, 'promotionStageId', null);
                                        this.$set(this.formData, 'activityDate', null);
                                    }
                                })
                            }

                        }
                    } catch (e) {
                        this.$message.warn('根据活动日期查询活动阶段失败，请稍候再试！');
                    }
                } else {
                    this.$message.warn('请先选择终端，再选择活动日期！');
                    // this.$set(this.formData, 'activityDate', '');
                }
            }
        }
    },
    methods: {
        /**
         * 政策类型默认值
         * <AUTHOR>
         * @date	2023/12/12 20:49
         */
        async defaultPolicyType() {
            const lovList = await this.$lov.getLovByType('MARKETSIX_POLICY_TYPE');
            // 取序号最小的
            if (lovList && lovList.length) {
                lovList.sort((a, b) => a.seq - b.seq);
                return lovList[0].val;
            }
            return '';
        },
        /**
         * 检查并保存产品
         * <AUTHOR>
         * @date    2023/8/8 16:54
         */
        async checkAndSaveProd() {
            let msg = '';
            const prodList = this.$utils.deepcopy(this.prodList);
            for (let i = 0; i < prodList.length; i++) {
                if (!prodList[i].productName) {
                    msg = '请输入小酒产品名称';
                    break;
                }
                if (!prodList[i].productNumber && prodList[i].productNumber !== 0) {
                    msg = '请输入开瓶数量';
                    break;
                }
                if (prodList[i].scanNumber && prodList[i].productNumber < prodList[i].scanNumber) {
                    msg = '开瓶数量需大于扫码数量！';
                    break;
                }
                prodList[i].row_status = prodList[i].id ? 'UPDATE' : 'NEW';
            }
            if (msg) {
                this.$showError(msg);
            } else {
                const {success, result} = await this.$http.post('action/link/headquarterProduct/batchUpsert', prodList);
                if (success) {
                    this.prodList = result.map((item) => ({
                        ...item,
                        row_status: 'UPDATE'
                    }));
                } else {
                    msg = result;
                }
            }
            return msg;
        },
        /**
         * 查询小酒产品
         * <AUTHOR>
         * @date    2023/8/8 11:29
         */
        async queryProdList(id) {
            const {success, rows} = await this.$http.post('action/link/headquarterProduct/queryByExamplePage', {
                filtersRaw: [
                    {id: 'feedbackId', property: 'feedbackId', value: id || this.formData.id},
                    {id: 'productType', property: 'productType', value: 'Open'}
                ]
            });
            if (success) {
                this.prodList = rows;
            }
        },
        /**
         * 修改开瓶数量
         * <AUTHOR>
         * @date    2023/8/8 9:30
         */
        changeProdNum(item, index) {
            if (item.scanNumber && item.productNumber < item.scanNumber) {
                this.$message.warn('重新编辑开瓶数量，开瓶数量需大于扫码数量！');
            }
        },
        /**
         * 保存小酒产品
         * <AUTHOR>
         * @date    2023/8/8 10:17
         */
        async saveProd(item, index, status) {
            const url = status === 'update' ? 'action/link/headquarterProduct/update' : 'action/link/headquarterProduct/insert';
            const {success, newRow} = await this.$http.post(url, item);
            if (success) {
                this.$set(this.prodList, index, newRow);
            }
        },
        /**
         * 删除小酒产品
         * <AUTHOR>
         * @date    2023/8/7 19:31
         */
        async deleteItem(item, index) {
            if (item.id) {
                const {success, result} = await this.$http.post('action/link/headquarterProduct/deleteById', {
                    id: item.id
                });
                if (success) {
                    this.prodList.splice(index, 1);
                    this.$message.success('删除成功！');
                }
            } else {
                this.prodList.splice(index, 1);
                this.$message.success('删除成功！');
            }
        },
        /**
         * 添加小酒产品
         * <AUTHOR>
         * @date    2023/8/4 17:53
         */
        async addProd() {
            if (await this.checkAndSaveProd()) {
                return;
            }
            this.prodList.push({
                feedbackId: this.formData.id,
                scanNumber: 0,
                productType: 'Open'
            });
        },
        /**
         * 选择小酒产品
         * <AUTHOR>
         * @date    2023/8/7 17:30
         */
        async chooseProduct(data, index) {
            if (!this.formData.activityName) {
                this.$message.warn('请先选择活动！');
                return;
            }
            if (data.scanNumber) {
                return;
            }
            const chooseItem = await this.$object(this.productOption, {
                pageTitle: '选择小酒产品'
            });
            const itemIndex = this.prodList.findIndex(item => item.productId === chooseItem.productId);
            if (itemIndex !== -1) {
                this.$showError(`${chooseItem.productName}已经被选择，请重新选择产品！`);
            } else {
                this.$set(this.prodList[index], 'productId', chooseItem.productId);
                this.$set(this.prodList[index], 'productCode', chooseItem.productCode);
                this.$set(this.prodList[index], 'productName', chooseItem.productName);
                if (data.id) {
                    await this.saveProd(this.prodList[index], index, 'update');
                }
            }
        },
        /**
         * desc 自定义返回函数
         * <AUTHOR>
         * @date 2023-07-20
         * @params
         */
        udfBack() {
            this.$refs.udfBackDialog.show();
        },
        /**
         * desc 不点击保存就返回
         * <AUTHOR>
         * @date 2023-07-20
         * @params
         */
        async backSave() {
            try {
                await this.save();
            } catch (e) {
                this.$dialog({
                    title: '提示',
                    content: '若要保存信息，请先将必填信息填写完整！',
                    cancelButton: false,
                    initial: true,
                    onConfirm: () => {
                    }
                });
                this.$refs.udfBackDialog.hide();
            }
        },
        /**
         * desc 跳转到扫码记录
         * <AUTHOR>
         * @date 2023-04-24
         * @params
         */
        gotoOpenRecord(item) {
            this.$nav.push('/pages/headquarters-activity/scan-code-record-page.vue', {
                id: this.formData.id,
                headId: item.id,
                approveStatus: this.formData.approveStatus,
                freshSourceFlag: 'upsert',
            })
        },
        /**
         * desc 若进入该页面扫过码，则不可选择小酒产品、不可选择活动
         * <AUTHOR>
         * @date 2023-04-19
         */
        hadScan() {
            if (this.formData.scanNumber) {
                this.isChooseProd = true;
                this.isChooseAct = true;
            }
        },
        /**
         * desc 选择小酒产品之前
         * @date 2023-04-23
         */
        beforeSelectProd() {
            if (!this.formData.activityName) {
                this.$message.warn('请先选择活动！');
                return Promise.reject();
            }
        },
        /**
         * desc 选择小酒产品之后
         * @date 2023-04-18
         */
        afterSelectProd(data) {
            this.$set(this.formData, 'productName', data.productName);
        },
        /**
         * desc 新增生成登记名称
         * <AUTHOR> @date 2023-04-25
         */
        async generateRegName(created) {
            if (!created) {
                this.regNameMMDD = '';
            }
            if (this.formData.activityDate) {
                const month = this.formData.activityDate.substr(5, 2);
                const day = this.formData.activityDate.substr(8, 2);
                this.regNameMMDD = month + day;
            }
            if (!this.regNameMMDD && created) {
                const month = created.substr(5, 2);
                const day = created.substr(8, 2);
                this.regNameMMDD = month + day;
            }
            if (!this.formData.privateRoom) {
                this.formData.privateRoom = '';
            }
            this.$set(this.formData, 'feedbackName', this.regNameMMDD + this.formData.accountName + this.formData.privateRoom);
        },

        /**
         * desc 扫码/上传图片/添加动销前校验
         * <AUTHOR> @date 2023-04-18
         */
        async operateValidate() {
            // 编辑活动（保存过）不调用upsert接口
            if (this.row_status === 'NEW') {
                try {
                    await this.$refs.upsertAct.validate();
                } catch (e) {
                    this.$dialog({
                        title: '提示',
                        content: '请先将基础信息填写完成再扫码/添加动销！',
                        cancelButton: false,
                        confirmText: '确定',
                    });
                    throw e;
                }
                const data = await this.$http.post('action/link/headquarterFeedback/upsert', {
                    id: this.formData.id,
                    activityId: this.formData.activityId,
                    activityType: this.formData.activityType,
                    terminalId: this.formData.terminalId,
                    accountId: this.formData.accountId,
                    row_status: ROW_STATUS.NEW,
                    // promotionStageId: this.formData.promotionStageId ? this.formData.promotionStageId : this.pageParam.promotionStageId,
                    promotionStageId: this.formData.promotionStageId,
                    activityDate: this.formData.activityDate,
                    ...this.formData
                });
                if (data.success) {
                    // this.formData = data.newRow;
                    this.formData.feedbackCode = data.newRow.feedbackCode;
                    await this.generateRegName(data.newRow.created);
                }
            }
            if (this.row_status === 'UPDATE') {
                try {
                    await this.$refs.upsertAct.validate();
                } catch (e) {
                    this.$dialog({
                        title: '提示',
                        content: '请先将基础信息填写完成再扫码/添加动销！',
                        cancelButton: false,
                        confirmText: '确定',
                    });
                    throw e;
                }
            }
            this.row_status = 'UPDATE';
        },

        /**
         * desc 点击扫码
         * <AUTHOR>
         * @date 2023-04-14
         */
        async clickScan(item, index) {
            await this.operateValidate();
            if (this.$utils.isEmpty(item.productName)) {
                this.$showError('请先输入小酒产品名称！');
                return;
            }
            if (this.$utils.isEmpty(item.productNumber)) {
                this.$showError('请先输入开瓶数量！');
                return;
            }
            if (this.$utils.isEmpty(item.id)) {
                await this.saveProd(item, index, 'new');
            }
            const that = this;
            await that.getAddress();
            if (that.$utils.isEmpty(this.coordinate.latitude) && that.$utils.isEmpty(this.coordinate.longitude)) {
                this.$dialog({
                    title: '提示',
                    content: '请确认手机地理位置授权是否打开，或者【设置】-【企业微信】位置权限管理是否打开？',
                    cancelButton: false,
                    confirmText: '去开启',
                    onConfirm: async () => {
                        let userLocation = await this.$locations.openSetting();
                        if (userLocation['scope.userLocation']) {
                            that.coordinate = await that.$locations.getCurrentCoordinate();
                        }
                    }
                });
            } else {
                await wx.scanCode({
                    onlyFromCamera: true,
                    success: async (res) => {
                        that.$utils.showLoading();
                        await that.afterGetCode(res.result, this.prodList[index], index);
                        that.$utils.hideLoading();
                    }
                });
            }
        },
        /**
         * 获取码之后的处理
         * <AUTHOR>
         * @date 2023-04-14
         */
        async afterGetCode(mark, item, index) {
            try {
                this.$utils.showLoading();
                const data = await this.$http.post('action/link/headquarterFeedback/headOpenScanVerification', {
                    mark: mark,
                    headId: this.formData.id,
                    province: this.addressData.province,
                    city: this.addressData.city,
                    district: this.addressData.district,
                    scanAddress: this.addressDataFull,
                    productType: 'Open',
                    productId: item.productId,
                    productNumber: item.productNumber,
                    remark: this.addressData.remark,
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    this.$message.success('扫码成功');
                    await this.queryScanNumber(item, index);
                    setTimeout(() => {
                        this.clickScan(item, index);
                    }, 500);
                } else {
                    this.$utils.hideLoading();
                    this.$showError(data.message);
                }
            } catch (e) {
                this.$utils.hideLoading();
            }
        },

        /**
         * 获取定位地址  百度经纬度逆解析
         */
        async getAddress() {
            this.coordinate = await this.$locations.getCurrentCoordinate();
            // 校验用户是否授权地理位置
            if (!this.$utils.isEmpty(this.coordinate.latitude) && !this.$utils.isEmpty(this.coordinate.longitude)) {
                let address = await reverseTMapGeocoder(this.coordinate.latitude, this.coordinate.longitude, '总部活动营销6.0');
                this.addressData = address['originalData'].result.addressComponent;
                this.addressDataFull = address['originalData'].result.formatted_address;
                this.addressFlag = true;
            }
        },

        /**
         * desc 扫码成功查询扫码数量
         * <AUTHOR>
         * @date 2023-04-14
         */
        async queryScanNumber(item, index = -1) {
            // 删除扫码记录更新扫码数量
            if (index === -1) {
                index = this.prodList.findIndex(t => t.id === item.headId);
            }
            try {
                const data = await this.$http.post('action/link/headquarterProduct/queryByExamplePage', {
                    feedbackId: this.formData.id,
                    filtersRaw: [
                        {id: 'productId', property: 'productId', value: item.productId},
                        {id: 'productType', property: 'productType', value: 'Open'}],
                });
                if (data.success) {
                    if (data.rows.length && index !== -1) {
                        this.$set(this.prodList[index], 'scanNumber', data.rows[0].scanNumber);
                    }
                } else {
                    this.$showError('查询扫码数量失败，请稍候再试');
                }
            } catch (e) {
                this.$showError('查询扫码数量失败');
            }
        },
        /**
         * desc 根据业务场景查询配置的需要维护的场景图片
         * <AUTHOR>
         * @date 2023-04-12
         */
        async querySceneImg() {
            this.picturesNeedUploaded = [];
            let picturesNeedUploaded = [];
            // 获取模版子类型，将返回的登记要求字符串转化为数组
            // if (this.pageParam.sourceFlag === 'terminalList' || this.pageParam.sourceFlag === 'terminalDetail') {
            //     this.$set(this.pageParam, 'registerRequest', this.formData.registerRequest);
            // }
            const regRegStr = this.formData.registerRequest.replace(/"/g, '');
            if (this.formData.registerRequest.charAt(0) === "[") {
                this.regReqList = regRegStr.slice(1, -1).split(',');
            } else {
                this.regReqList = regRegStr.slice(1, -1);
            }
            for (let i = 0; i < this.regReqList.length; i++) {
                let tempType = this.regReqList[i];
                // console.log('当前模版类型tempType', tempType);
                const data = await this.$utils.getQwMpTemplate('HeadFeedbackData', tempType);
                if (!data.success) {
                    this.imgAuthFlag = true;
                    this.$utils.hideLoading();
                } else {
                    this.imgAuthFlag = false;

                }
                let resultOpt = JSON.parse(data.result);
                let temp = [];//当前业务场景配置的场景图片数组信息
                temp = JSON.parse(resultOpt.conf);
                picturesNeedUploaded.push(temp[0]);
            }
            this.picturesNeedUploaded = picturesNeedUploaded;
        },

        /**
         * desc 输入包间号后提示
         * <AUTHOR>
         * @date 2023-05-25
         */
        async afterPrivateRoom($event) {
            if (this.formData.privateRoom) {
                try {
                    const data = await this.$http.post('export/link/headquarterFeedback/queryByExamplePage', {
                        filtersRaw: [
                            {id: 'terminalId', property: 'terminalId', value: this.formData.accountId, operator: '='},
                            {
                                id: 'activityDate',
                                property: 'activityDate',
                                value: this.formData.activityDate,
                                operator: '='
                            },
                            {
                                id: 'feedbackStage',
                                property: 'feedbackStage',
                                value: this.formData.feedbackStage,
                                operator: '='
                            },
                            {
                                id: 'privateRoom',
                                property: 'privateRoom',
                                value: this.formData.privateRoom,
                                operator: 'NOT NULL'
                            },
                            {id: 'status', property: 'status', value: 'Inactive', operator: '<>'},
                        ],
                    });
                    if (data.success) {
                        if (data.rows.length > 0) {
                            const uniquePrivateRooms = new Set();
                            data.rows.forEach(item => {
                                if (item.privateRoom) {
                                    uniquePrivateRooms.add(item.privateRoom);
                                }
                            });
                            this.privateRoomList = Array.from(uniquePrivateRooms);
                            this.$message.warn('已存在' + this.privateRoomList.join(', ') + '的登记');
                        }
                    }
                } catch (e) {
                }
            }
        },
        /**
         * desc 新增必填校验逻辑
         * <AUTHOR>
         * @date 2023110/25
         */
        newCheckSave(){
			// 当关联的总部活动的推广阶段等于第二或第三阶段，且政策类型等于【无】或者【团购购酒】时，常规装登记的【动销数量】必填
			const newList = this.$refs.productionPin.productionPinList
			const stageName = ['Thesecondstage','Thethirdstage'].includes(this.formData.stageName)
			const policyType = ['None','Purchase'].includes(this.formData.policyType)
			const newData = newList.find(i=>{
				return i.scanNumber <= 0
			})
			if(stageName&&policyType){
				if(newData || !newList[0]){
					return false
				}
			}
            return true
        },
        /**
         * desc 保存活动信息
         * <AUTHOR>
         * @date 2023-04-10
         */
        async save() {
            try {
                this.$utils.showLoading();
                this.formData.approveStatus = 'New';
                this.formData.status = 'New';
                await this.$refs.upsertAct.validate();
                if (!this.prodList.length) {
                    this.$showError('至少添加一个小酒产品！');
                    this.$utils.hideLoading();
                    return;
                }
                if (await this.checkAndSaveProd()) {
                    this.$utils.hideLoading();
                    return;
                }
                const isTrue = this.newCheckSave()
                if(!isTrue){
                	this.$showError('请完善动销数量！');
                    this.$utils.hideLoading();
                	return
                }
                // await this.saveScanNumber();
                // await this.selectProSave();
                for (let i = 0; i < this.picturesNeedUploaded.length; i++) {
                    const item = this.picturesNeedUploaded[i];
                    // 判断是否有"打卡照片"
                    if (this.regReqList.includes('didipic')) {
                        if (item.ctrlCode === 'didipic' && this.$refs.pic[i].$refs.img.imgData.length > 0) {
                            this.$set(this.formData, 'signPicture', 'Y');
                        } else {
                            this.$set(this.formData, 'signPicture', 'N');
                        }
                    } else {
                        this.$set(this.formData, 'signPicture', 'N');
                    }
                    // 如果必输,则校验必输的值是否为空
                    if (!!item.base['require'] && !this.$refs.pic[i].$refs.img.imgData.length) {
                        this.$message.warn(`请上传${item.base['label']}的图片！`);
                        this.$utils.hideLoading();
                        return;
                        // return Promise.reject(false);
                    }
                }
                if (!this.formData.feedbackCode) {
                    const reg = await this.$http.post('action/link/headquarterFeedback/insert', {
                        promotionStageId: this.formData.promotionStageId,
                        activityDate: this.formData.activityDate,
                        ...this.formData,
                        row_status: ROW_STATUS.NEW
                    });
                    if (reg.success) {
                        this.$set(this.formData, 'feedbackCode', reg.newRow.feedbackCode);
                        await this.generateRegName(reg.newRow.created);
                        // this.$bus.$emit('refreshRegList', reg.newRow.id);  // 刷新登记列表
                        // this.$bus.$emit('refreshTerminalList');  // 刷新终端列表
                    } else {
                        this.$message.warn('保存出错，请检查网络！');
                    }
                }
                if (this.formData.feedbackCode) {
                    console.log('包含包间号', this.privateRoomList.includes(this.formData.privateRoom))
                    if (this.privateRoomList.includes(this.formData.privateRoom)) {
                        return;
                    }
                    const data = await this.$http.post('action/link/headquarterFeedback/update', {
                        promotionStageId: this.formData.promotionStageId,
                        ...this.formData
                    });
                    if (data.success) {
                        this.$message.success('保存成功！');
                        this.$utils.hideLoading();
                        if(!this.newSaveFlag) {
                            this.$emit('saveSuccess');
                        }
                        if(this.newSaveFlag) {
                            this.$emit('reset');
                        }
                    } else {
                        this.$message.warn('保存出错，请检查网络！');
                    }
                }
            } catch (e) {
                this.$utils.hideLoading();
                console.log(e);
                throw e
            }
        },
        /**
         * desc 选了产品点保存
         * <AUTHOR>
         * @date 2023-04-10
         */
        async selectProSave() {
            const reg = await this.$http.post('action/link/headquarterProduct/queryByExamplePage', {
                feedbackId: this.formData.id,
                filtersRaw: [
                    {id: 'productId', property: 'productId', value: this.formData.productId, operator: '='},
                    {id: 'productType', property: 'productType', value: 'Open'}],
            });
            if (reg.success) {
                if (reg.rows.length === 0) {
                    const data = await this.$http.post('action/link/headquarterFeedback/headOpenScanVerification', {
                        headId: this.formData.id,
                        productId: this.formData.productId,
                        productNumber: this.formData.productNumber,
                        productType: 'Open',
                        scanNumber: 0,
                    });
                    if (data.success) {
                        console.log('保存成功');
                    } else {
                        this.$showError('保存失败，请稍候再试');
                    }
                }
            }
        },
        /**
         * desc 保存时更新开瓶数量
         * <AUTHOR>
         * @date 2023-04-10
         */
        async saveScanNumber() {
            try {
                const scanNumber = this.formData.scanNumber ? this.formData.scanNumber : 0;
                if (this.formData.productNumber) {
                    const data = await this.$http.post('action/link/headquarterFeedback/headOpenScanVerification', {
                        headId: this.formData.id,
                        productId: this.formData.productId,
                        productNumber: this.formData.productNumber,
                        productType: 'Open',
                        scanNumber: scanNumber,
                    });
                    if (data.success) {
                        console.log('保存时更新开瓶数量成功');
                    } else {
                        this.$message.warn('保存时更新开瓶数量失败！');
                    }
                }
            } catch (e) {
                this.$message.warn('保存时更新开瓶数量失败，请检查网络！');
            }
        },
        /**
         * desc 保存并新建
         * <AUTHOR>
         * @date 2023-04-10
         */
        async newSave() {
            this.newSaveFlag = true;
            const isTrue = this.newCheckSave()
            if(!isTrue){
            	this.$showError('请完善动销数量！');
                this.$utils.hideLoading();
            	return
            }
            await this.save();
        },
    }
}
</script>

<style lang="scss">
.chose-stage:after {
    content: '*';
    color: red;
    position: relative;
    left: 0px;
    font-weight: bold;
    top: 6px;
}

.headquater-register-share {
    width: 100%;
    overflow: hidden;

    .view {
        background: white;
    }

    .add-prod {
        border: 2px solid #2F69F8;
        border-radius: 40px;
        margin: 20px;
        height: 80px;
        line-height: 80px;
        text-align: center;

        .text {
            font-family: PingFangSC-Regular, serif;
            font-size: 32px;
            color: #2F69F8;
            letter-spacing: 0;
        }

        .icon-plus {
            font-size: 32px;
            color: #2F69F8;
        }
    }

    .menu-stair {
        width: 100%;
        margin-left: 24px;
        padding-top: 40px;
        @include flex-start-center;

        .line {
            clear: both;

            .line-top {
                width: 8px;
                height: 16px;
                background: #3FE0E2;
            }

            .line-bottom {
                width: 8px;
                height: 16px;
                background: #2F69F8;
            }
        }

        .stair-title {
            width: 60%;
            margin-left: 16px;
            font-family: PingFangSC-Semibold, serif;
            font-size: 32px;
            color: #262626;
            letter-spacing: 1px;
            line-height: 32px;
        }

        .edit {
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #2F69F8;
            letter-spacing: 0;
            line-height: 28px;
            text-align: right;
            width: 58%;
        }
    }

    .productName {
        .prod-grey {
            color: #ddd;
        }

        .icon-right {
            color: #ddd;
            padding-left: 10px;
        }
    }

}
</style>
