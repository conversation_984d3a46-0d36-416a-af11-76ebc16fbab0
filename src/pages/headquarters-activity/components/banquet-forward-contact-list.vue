<!--
宴席活动-转发联系人列表
<AUTHOR>
@date 2023-08-03
@file banquet-forward-contact-list
-->
<template>
    <view class="banquet-forward-contact-list">
        <line-title title="转发联系人明细" class="head-title"></line-title>
        <link-auto-list :option="forwardContactList">
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="forward-contact-item">
                    <view slot="note">
                        <view class="content-middle-line">
                            <view class="data">
                                <view class="title">联系人{{ index + 1 }}</view>
                                <view class="val">{{ data.phoneNum }}</view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </view>
</template>

<script>
import LineTitle from "../../lzlj/components/line-title.vue";

export default {
    components: {LineTitle},
    name: 'banquet-forward-contact-list',
    props: {
        banquetItemId:  {
            type: String
        },
        flowObjId: {
            type: String
        }
    },
    data() {
        const forwardContactList = new this.AutoList(this, {
            module: 'action/link/forwardContact',
            url: {
                queryByExamplePage: 'action/link/forwardContact/queryByExamplePage'
            },
            param: () => {
                return {
                    filtersRaw: [
                        {
                            id: 'headId',
                            property: 'headId',
                            value: this.banquetItemId ? this.banquetItemId : this.flowObjId ? this.flowObjId : '',
                            operator: '='
                        }
                    ]
                }
            },
        })
        return {
            forwardContactList, // 转发联系人列表
        }
    },
    created() {

    },
    methods: {}
}
</script>

<style lang="scss">
.banquet-forward-contact-list {
    background-color: #F2F2F2;
    font-family: PingFangSC-Regular;
    .forward-contact-item {
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
        padding: 12px;

        .content-middle-line {
            width: 100%;

            .data {
                width: 100%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    width: 20%;
                    float: left;
                    padding-left: 6px;
                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                }
            }
        }
    }
}
</style>

