<template>
    <view class="password-authentication">
        <view class="password-authentication-main">
            <view class="title">
                输入口令
            </view>
            <view class="input-password-warp">
                <view class="v-code flex-content">
                    <block v-for="(item, index) in 4" :key="`code_${item}`">
                        <text 
                            :class="{'input-password': true, 'active-input': (vCodeValue.length === index || (item === 4 && vCodeValue.length === 4)) && isVFocus}"
                            @tap="tapFn"
                        >
                            {{vCodeValue.length>=item ? vCodeValue[index] : ''}}
                        </text>
                    </block>
                </view>
                <input type="number" class="dis-input" :maxlength="4" :focus="isVFocus" @input="showVCode" @blur="lostFocus"></input>
            </view>
            <link-button label="确认" @tap="enterPassword"></link-button>
        </view>
    </view>
</template>
<script>
export default {
    name: 'password-authentication',
    props: {
        banquetId: String 
    },
    data() {
        return {
            vCodeValue: '',
            isVFocus: false,
        }
    },
    methods: {
        showVCode(e) {
            this.vCodeValue = e.detail.value;
        },
        tapFn() {
            this.isVFocus = true;
        },
        lostFocus() {
            this.isVFocus = false
        },
        /**
         * 口令码验证 
         * <AUTHOR>
         * @date	2024/12/20 
         */ 
        async enterPassword() {
            try {
                const data = await this.$http.post('action/link/headquarterFeedback/verifyWatchword', {
                    id: this.banquetId,
                    watchword: this.vCodeValue
                });
                if (data.success) {
                    this.password = data.data
                    this.$emit('closPasswordVerify')
                } else {
                    this.$message.error('口令错误请输入正确口令');
                }
            }catch(err){
                console.log('err: 获取转发口令失败', err);
            }
           
        }
    },
}
</script>
<style lang="scss">
.password-authentication {
    width: 100%;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    .password-authentication-main {
        width: 70%;
        height: 320px;
        border-radius: 10px;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        .link-button {
            width: 240px;
            height: 60px;
            margin-top: 40px;
        }
        .title {
            font-size: 36px;
        }
        .input-password-warp {
            margin-top: 40px;
            .input-password {
                width: 75px;
                height: 72px;
                line-height: 72px;
                margin-right: 30px;
                border: 2px solid #cccccd;
                text-align: center;
                color: #333333;
                font-size: 38px;
                border-radius: 10px;
                &:last-child {
                    margin-right: 0;
                }
            }
            .dis-input{
                position: absolute;
                height: 0px;
                width: 0px;
            }
            .active-input {
                border-color: #079aff !important;
            }
        }
        .v-code {
            display: flex;
        }
    }    
}
</style>