<!--
总部活动-宴席列表详情-执行反馈-扫码
<AUTHOR>
@date 2023-07-13
@file banquet-feedback-scan-code.vue
-->
<template>
    <view class="banquet-feedback-scan-code">
        <link-auto-list :option="scanRecordOption">
            <template v-slot="{data, index}">
                <!--230801-根据业务需求，统一不可删除扫码记录-->
                <banquet-feedback-scan-code-item :data="data" :index="index" :shareFlag="shareFlag"/>
                <!-- 详情-不可删除 -->
<!--                <banquet-feedback-scan-code-item v-if="disabled" :data="data" :index="index"/>-->
                <!-- 执行反馈-可删除 -->
<!--                <link-swipe-action v-else>-->
<!--                    <link-swipe-option slot="option" @tap="deleteItem(data, index)">删除</link-swipe-option>-->
<!--                    <banquet-feedback-scan-code-item :data="data" :index="index"/>-->
<!--                </link-swipe-action>-->
            </template>
        </link-auto-list>
    </view>
</template>

<script>
import BanquetFeedbackScanCodeItem from './banquet-feedback-scan-code-item';
export default {
    name: 'banquet-feedback-scan-code',
    props: {
        disabled: Boolean,
        activityId: String,
        source: String,
        shareFlag: {
            type: Boolean,
        }
    },
    components: {BanquetFeedbackScanCodeItem},
    data() {
        const scanRecordOption = new this.AutoList(this, {
            module: 'action/link/headquarterScanRecord',
            url: {
                queryByExamplePage: 'action/link/headquarterScanRecord/queryByExamplePage'
            },
            param: () => {
                return {
                    filtersRaw: [
                        {id: 'headId', property: 'headId', value: this.activityId}
                    ],
                    attr2: "levelBox"
                }
            },
        })
        return {
            scanRecordOption,
        }
    },
    created() {

    },
    methods: {
        /**
         * desc 刷新列表数据
         * <AUTHOR>
         * @date 2023-07-13
         */
        reload() {
            this.scanRecordOption.methods.reload();
        },
        /**
         * desc 删除扫码记录
         * <AUTHOR>
         * @date 2023-07-13
         */
        // async deleteItem(data, index) {
        //     this.scanRecordOption.list.splice(index, 1);
        //     const url = 'action/link/headquarterScanRecord/deleteById'
        //     try {
        //         const {success, result} = await this.$http.post(url, {
        //             id: data.id
        //         });
        //         if (success) {
        //             this.$message.success('删除成功！');
        //         }
        //     } catch (e) {
        //         this.$showError('删除错误！');
        //     }
        // }
    }
}
</script>

<style lang="scss">
.banquet-feedback-scan-code {
    margin-top: 24px;

    .link-auto-list-wrapper {
        margin: 0 24px;
    }
    .link-swipe-action {
        width: 100%;
        border-radius: 24px;
    }
    .link-auto-list-no-more {
        display: none !important;
    }
}
</style>
