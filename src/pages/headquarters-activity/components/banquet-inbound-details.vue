<!--
总部活动-宴席列表详情-退货明细
<AUTHOR>
@date 2023/11/29
-->
<template>
    <view class="banquet-inbound-details">
        <line-title title="退货明细" class="head-title"></line-title>
        <link-auto-list :option="banquetInboundList">
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="banquet-inbound-details-item">
                    <view slot="note">
                        <view class="content-middle-line">
                            <view class="data">
                                <view class="title">扫码时间</view>
                                <view class="val">{{ data.created | date('YYYY-MM-DD HH:mm:ss') }}</view>
                            </view>
                        </view>
                        <view class="content-middle-line">
                            <view class="data">
                                <view class="title">扫码类型</view>
                                <view class="val">{{ data.scanType | lov('QDHY_INBOUND_SCAN_TYPE') }}</view>
                            </view>
                        </view>
                        <view class="content-middle-line">
                            <view class="data">
                                <view class="title">扫码产品</view>
                                <view class="val">{{ data.productCode }}</view>
                            </view>
                        </view>
                        <view class="content-middle-line">
                            <view class="data">
                                <view class="title">产品名称</view>
                                <view class="val">{{ data.productName ? data.productName.substr(0,20) + '...' : ''}}</view>
                            </view>
                        </view>
                        <view class="content-middle-line">
                            <view class="data">
                                <view class="title">扫码产品码</view>
                                <view class="val">{{ data.boxCode ? data.boxCode : ''}}</view>
                            </view>
                        </view>
                        <view class="content-middle-line">
                            <view class="data">
                                <view class="title">扫码单位</view>
                                <view class="val">{{ data.boxType | lov('QDHY_BOX_TYPE')}}</view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </view>
</template>

<script>
import LineTitle from "../../lzlj/components/line-title.vue";

export default {
    name: 'banquet-inbound-details',
    components: {LineTitle},
    props: {
        banquetItem: {
            type: Object,
            default: function () {
                return {};
            }
        },
        feedbackCode: {
            type: String
        }
    },
    data() {
        const banquetInboundList = new this.AutoList(this, {
            module: 'action/link/baseScanInDetail',
            url: {
                queryByExamplePage: 'action/link/baseScanInDetail/queryByExamplePage',
            },
            param: () => {
                return {
                    filtersRaw: [
                        {
                            id: 'headActivityId',
                            property: 'feedbackCode',
                            operator: '=',
                            value: this.banquetItem.feedbackCode
                        },
                    ]
                }
            },
            sortField: 'created',
            sortDesc: 'desc',
            sortOptions: null,
        });

        return {
            banquetInboundList,
            item: this.banquetItem,
        }
    },
    created() {
        if(this.feedbackCode) {
            this.banquetItem.feedbackCode = this.feedbackCode;
        }
    },
    methods: {
    }
}
</script>

<style lang="scss">
.banquet-inbound-details {
    background-color: #F2F2F2;
    font-family: PingFangSC-Regular;
    .banquet-inbound-details-item {
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
        padding: 12px;

        .content-middle-line {
            width: 100%;

            .data {
                width: 100%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    width: 25%;
                    float: left;
                    padding-left: 6px;
                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                }

                .Submitted, .Feedback{
                    color: #2F69F8;
                }

                .Approve, .FeedbackApro{
                    color: #2EB3C2;
                }

                .Refused, .Refeedback{
                    color: #FF5A5A;
                }

            }

        }
    }
}
</style>
