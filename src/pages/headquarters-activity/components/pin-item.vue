<template>
    <view class="pin-Item">
        <link-swipe-action>
            <link-swipe-option slot="option" @tap="deleteRow(item,index)" v-if="Number(item.scanNumber) < 1">删除</link-swipe-option>
            <view class="product-list-item">
                <view class="media-list">
                    <view class="media-top">
                        <view class="num-view">
                            <view class="num">{{ item.productCode }}</view>
                        </view>
                        <view style="color: #1E8BFF;" @tap="pickScanProduction(item)">查看扫码记录</view>
                    </view>
                </view>
                <view class="content-middle">
                    <view class="name">{{item.productName}}</view>
                </view>
                <view class="media-list">
                    <view class="store-input">
                        <view class="store-input-rows">
                            <view class="label">库存：</view>
                            <view class="value-input">
                                <input type="number" v-model="item.productStockQty" placeholder-style="color: #8C8C8C;">
                                <view class="unit">{{'瓶'}}</view>
                            </view>
                        </view>
                        <view class="store-input-rows">
                            <view class="label">动销：</view>
                            <view class="value">{{item.scanNumber || 0}}{{ '瓶'}}<link-icon style="color: #1E8BFF; text-align: right; font-size: 18px;" icon="icon-scan"  @tap="clickScan(item, index)"/></view>
                        </view>
                    </view>
                </view>
            </view>
        </link-swipe-action>
    </view>
</template>

<script>
    export default {
    	name: 'pin-Item',
        props:{
            item:{
                type:Object,
                default(){
                    return {}
                }
            },
			index:{type: Number}
        },
    	data() {
            return {}
        },
        methods:{
            /**
             * 删除
             * <AUTHOR>
             * @date 2024/04/01
             */
            deleteRow(item,index){
                if(Number(this.item.scanNumber)>0){
                    return
                }
                const data={
                    item,index
                }
               this.$emit('deleteRow',data)
            },
            /**
             * 扫码
             * <AUTHOR>
             * @date 2024/04/01
             */
            clickScan(item,index){
                const data ={
                    item,index
                }
                this.$emit('clickScan',data)
            },
            /**
             * 查看扫码记录
             * <AUTHOR>
             * @date 2024/04/01
             */
            pickScanProduction(item){
                this.$emit('pickScanProduction',item)
            }
        }
    }

</script>

<style lang="scss">
    .pin-Item {
        .link-swipe-action{
            width: 100%;
        }
    }
</style>
