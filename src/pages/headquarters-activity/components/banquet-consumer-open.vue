<!--
宴席活动-消费者开瓶明细
<AUTHOR>
@date 2023-06-28
@file banquet-consumer-open
-->
<template>
    <view class="banquet-consumer-open">
        <line-title title="消费者开瓶明细" class="head-title"></line-title>
        <link-auto-list :option="banquetConsumerOpenList">
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="banquet-consumer-open-item">
                    <view slot="note" class="open-item">
                        <view class="info-item">
                            <view class="box-code">盖内码：{{data.qrCodeIn}}</view>
                        </view>
                        <view class="info-item"></view>
                        <view class="info-item prod-name">{{data.productName}}</view>
                        <view class="info-item1">产品编码：{{data.productCode}}</view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </view>
</template>

<script>
import LineTitle from "../../lzlj/components/line-title.vue";

export default {
    name: 'banquet-consumer-open',
    components: {LineTitle},
    props: {
        feedbackCode: {
            type: String
        }
    },
    data() {
        const banquetConsumerOpenList = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/consumerOpenDetail/queryByExamplePage',
            },
            param: () => {
                return {
                    filtersRaw: [
                        {
                            id: 'feedbackCode',
                            property: 'feedbackCode',
                            operator: '=',
                            value: this.feedbackCode
                        },
                        {
                            id: 'campType',
                            property: 'campType',
                            operator: '=',
                            value: 'BANQUET_PROMOTION'
                        },
                    ]
                }
            },
            sortField: 'created',
            sortDesc: 'desc',
            sortOptions: null,
        });

        return {
            banquetConsumerOpenList
        }
    },
    created() {},
    methods: {
    }
}
</script>

<style lang="scss">
.banquet-consumer-open {
    .banquet-consumer-open-item {
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
        padding: 24px;

        .open-item {
            color: #262626;
            font-size: 28px;

            .info-item {
                display: flex;
                margin-bottom: 10px;
                .box-code {
                    display: flex;
                    font-size: 28px;
                }
            }
            .info-item1 {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
                font-size: 28px;
                .num1 {
                    display: flex;
                    align-items: center;

                    .bottle-num {
                        font-size: 30px;
                        font-weight: bold;
                        margin-right: 4px;
                    }
                }
            }
            .code-wrap {
                padding: 20px;
                background: #BBD5FF;
                border-radius: 20px;

                .code-cate {
                    margin-bottom: 10px;
                }

                .code-item {
                    font-size: 26px;
                    margin-bottom: 2px;
                }
            }

            .prod-name {
                font-size: 30px;
                font-weight: bold;
                font-size: 28px;
            }

            .tag-green {
                color: #95EC69;
            }

            .tag-red {
                color: red;
            }
        }
    }
}
</style>
