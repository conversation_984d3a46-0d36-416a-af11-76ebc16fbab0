<!--
总部活动-营销6.0-一键转发联系人
<AUTHOR>
@date 2023-09-12
@file headquarters-forward-contact-page
-->
<template>
    <link-page class="headquarters-forward-contact-page">
        <image v-if="gowChatFlag" class="bottom-bg" :src="$imageAssets.marketingSixForwardImage"></image>
        <link-form v-if="!gowChatFlag" :value="formData" :rules="rules" ref="upsertAct">
            <link-form-item v-if="pageParam.headRegFlag" label="总部活动" required>
                <link-input v-model="formData.activityName" disabled/>
            </link-form-item>
            <link-form-item v-if="pageParam.terminalFlag" label="总部活动" field="activityName" required>
                <link-object :option="headActOption"
                             :row="formData"
                             pageTitle="选择总部活动"
                             :map="{activityName: 'activityName'}"
                             :value="formData.activityName"
                             :beforeSelect="beforeSelectHeadAct"
                             :afterSelect="afterSelectHeadAct">
                    <template v-slot="{data}">
                        <item :title="data.activityName" :key="data.id" :data="data" :content="data.activityNum"/>
                    </template>
                </link-object>
            </link-form-item>
            <link-form-item v-if="pageParam.headRegFlag" label="终端" field="accountName" required>
                <link-object :option="accountOption"
                             :row="formData"
                             pageTitle="选择终端"
                             :map="{
                                accountName: 'accountName',
                                accountId: 'id'
                             }"
                             :value="formData.accountName"
                             :beforeSelect="beforeSelectTerminal"
                             :afterSelect="afterSelectTerminal">
                    <template v-slot="{data}">
                        <item :title="data.accountName" :key="data.id" :data="data" :content="data.accountCode"/>
                    </template>
                </link-object>
            </link-form-item>
            <link-form-item v-if="pageParam.terminalFlag" label="终端" required>
                <link-input v-model="formData.accountName" disabled/>
            </link-form-item>
            <link-form-item label="活动日期" field="activityDate" required>
                <link-date view="YMD" v-model="formData.activityDate" value-format="YYYY-MM-DD"
                           display-format="YYYY-MM-DD"></link-date>
            </link-form-item>
            <link-form-item label="活动阶段" field="stageName">
                <link-lov type="PROMOTION_STAGE" v-model="formData.stageName || '选择活动日期后显示'" disabled/>
            </link-form-item>
        </link-form>
        <view v-if="!gowChatFlag" class="contacts-container" v-for="(item, index) in contactsArr" :key="index">
            <link-form ref="form" :value="item" :rules="formRules">
                <link-form-item :label="`联系人${index + 1}`" required field="phoneNum">
                    <view class="link-input-contact">
                        <link-input placeholder="请输入联系人电话" v-model="item.phoneNum" field="phoneNum"
                                    @blur="checkRepate()"/>
                        <view class="delete-icon-box" @tap="tapSuffixIcon(index)">
                            <link-icon icon="icon-close" class="icon-close"/>
                        </view>
                    </view>
                </link-form-item>
            </link-form>
        </view>
        <view v-if="!gowChatFlag" class="add-contacts" @tap="addContacts">
            <text class="iconfont icon-plus"></text>
            <text class="text">添加联系人</text>
        </view>
        <link-sticky>
            <link-button v-if="!gowChatFlag" block @tap="shareValidate">提交</link-button>
        </link-sticky>

        <!-- 小程序分享/名片海报分享提示 -->
        <link-dialog ref="chooseType">
            <view slot="head">
                提示
            </view>
            <view>
                可从企业微信发送至微信好友
            </view>
            <link-button slot="foot" @tap="cancelShare()">取消</link-button>
            <link-button slot="foot" open-type="share" class="share-wechat-button" @tap="shareApplet">分享到企微
            </link-button>
        </link-dialog>
    </link-page>
</template>

<script>
import Taro from "@tarojs/taro";
import qs from 'querystring'

export default {
    name: 'headquarters-forward-contact-page',
    data() {
        // 从登记列表/详情中进入选择终端时的安全性
        const menuId = this.pageParam.menuId;
        const accessGroupOauth = this.$utils.getMenuAccessGroup(menuId, '/pages/terminal/terminal/es-terminal-list-page');
        let oauth = "";
        if (!this.$utils.isEmpty(accessGroupOauth)) {
            oauth = accessGroupOauth;
        } else {
            oauth = this.$utils.isPostnOauth() === 'MY_POSTN' ? 'MULTI_POSTN' : this.$utils.isPostnOauth();
            if (oauth === 'MY_ORG') {
                oauth = 'MULTI_ORG'
            }
            if (this.$taro.getStorageSync('token').result.positionType === 'SalesAreaManager') {
                oauth = 'MULTI_POSTN'
            }
            if (this.$taro.getStorageSync('token').result.positionType === 'CityManager') {
                oauth = 'MULTI_ORG'
            }
        }
        // 从登记列表/详情中进入选择终端
        const accountOption = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/activityAccount/queryTerminalPage'
            },
            searchFields: ['accountName', 'accountCode'],
            param: {
                // return {
                freeze: 'Y',
                activityId: this.pageParam.activityId,
                rows: 25,
                oauth: oauth,
                // }
            },
        });
        // 从投放终端中进入选择活动
        const headActOption = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/activityAccount/queryByExamplePage'
            },
            searchFields: ['activityName'],
            param: () => {
                return {
                    freeze: 'Y',
                    accountId: this.formData.accountId,
                    rows: 25,
                    filtersRaw: [
                        {id: 'activityStatus', property: 'activityStatus', value: 'Processing', operator: '='},
                        {id: 'approveStatus', property: 'approveStatus', operator: '=', value: 'Approved'},
                        {id: 'activityType', property: 'activityType', value: 'MarketingSix'}
                    ]
                }
            },
            sortField: 'created',
        });
        const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
        const formData = {};
        const rules = {
            activityName: this.Validator.required('活动名称必填'),
            accountName: this.Validator.required('终端必选'),
            activityDate: this.Validator.required('活动日期必填'),
            stageName: this.Validator.required('请先维护该活动日期的推广阶段！'),
        };
        return {
            formData,
            userInfo,
            accountOption,  // 从活动进入选择终端
            headActOption,  // 从终端进入选择活动
            rules,
            gowChatFlag: false, // 跳转到企微/微信，默认为false，转发到微信前为true
            contactsArr: [], // 联系人
            shareFlag: false, // 是否分享中
            updateFlag: false, //是否为编辑状态，首次保存为false
            formRules: {
                // 电话号码校验
                phoneNum: this.Validator.phone(),
            },
        }
    },
    watch: {
        'formData.activityName': {
            handler(newVal, oldVal) {
                if (this.formData.terminalName && oldVal) {
                    this.queryContacts();
                }
            },
            deep: true,
        },
        'formData.terminalName': {
            handler(newVal, oldVal) {
                if (this.formData.activityName && oldVal) {
                    this.queryContacts();
                }
            },
            deep: true,
        },
        'formData.activityDate': {
            // deep: true,
            async handler(newValue, oldValue) {
                if (this.formData.accountId && this.formData.activityDate) {
                    try {
                        const {rows, success} = await this.$http.post('action/link/promotionStage/queryByExamplePage', {
                            filtersRaw: [
                                // {id: 'approvalStatus', property: 'approvalStatus', value: 'Approved', operator: '='},
                                // {
                                //     id: 'stageStatus',
                                //     property: 'stageStatus',
                                //     value: "[Processing,Completed]",
                                //     operator: 'in'
                                // },
                                {
                                    id: 'startTime',
                                    property: 'startTime',
                                    value: this.formData.activityDate,
                                    operator: '<='
                                },
                                {id: 'endTime', property: 'endTime', value: this.formData.activityDate, operator: '>='},
                                {
                                    id: 'actId',
                                    property: 'actId',
                                    value: this.formData.activityId,
                                    operator: '='
                                },
                            ],
                            pageFlag: true,
                            rows: 5,
                            page: 1
                        })
                        if (success) {
                            if (rows.length === 0) {
                                this.$set(this.formData, 'stageName', '');
                                this.$dialog({
                                    title: '提示',
                                    content: '请联系管理员维护推广阶段！',
                                    cancelButton: false,
                                    confirmText: '确定',
                                });
                            } else if (rows.length === 1) {
                                this.$set(this.formData, 'stageName', rows[0].promotionStage);
                                this.formData.promotionStageId = rows[0].id;
                            } else {
                                const allTime = await this.$lov.getLovByType('PROMOTION_STAGE')
                                rows.forEach((i, j) => {
                                    const choseObj = allTime.find(ite => {
                                        return ite.val == i.promotionStage
                                    })
                                    i.valName = choseObj?.name
                                })
                                this.$dialog({
                                    title: '请选择时间内的活动阶段',
                                    content: (h) => {
                                        let newArr = []
                                        rows.forEach((item, index) => {
                                            const style = {
                                                class: `${index === this.choseStageName ? 'chose-stage' : ''}`,
                                                style: `fontSize:16px;margin:15px`
                                            }
                                            newArr.push(h('view', [
                                                <view
                                                    {...style}
                                                    onTap={() => {
                                                        this.choseStageName = index
                                                        this.$set(this.formData, 'stageName', item.promotionStage);
                                                        this.$set(this.formData, 'promotionStageId', item.id);
                                                    }}>
                                                    {`${item.valName}: ${item.startTime}-${item.endTime}`}
                                                </view>
                                            ]))
                                        })
                                        return h('view', newArr)
                                    },
                                    cancelButton: true,
                                    onConfirm: () => {
                                    },
                                    onCancel: () => {
                                        this.$set(this.formData, 'stageName', null);
                                        this.$set(this.formData, 'promotionStageId', null);
                                        this.$set(this.formData, 'activityDate', null);
                                    }
                                })
                            }
                        }
                    } catch (e) {
                        this.$message.warn('根据活动日期查询活动阶段失败，请稍候再试！');
                    }
                } else {
                    this.$message.warn('请先选择终端，再选择活动日期！');
                    this.$set(this.formData, 'activityDate', '');
                }
            }
        }

    },
    onShareAppMessage(res) {
        // 进入分享
        this.shareFlag = true;
        // 自定义按钮与小程序提供分享按钮，均跳转到相同界面
        // 构建带参数的 path
        const {
            activityId,
            activityDate,
            activityName,
            registerRequest,
            promotionStageId,
            stageName,
            terminalId,
            terminalCode,
            terminalName,
        } = this.formData

        const queryString = qs.stringify({
            activityId,
            activityDate,
            activityType: 'MarketingSix',
            activityName,
            registerRequest,
            promotionStageId,
            stageName,
            terminalId,
            terminalCode,
            terminalName,
            scene: 'marketingSix',
            creatorCode: this.userInfo.username
        })

        const pathWithParams = `/pages/headquarters-activity/headquaters-share-page?${queryString}`;
        return {
            title: '分享营销6.0登记',
            path: pathWithParams
        }
    },
    async created() {
        console.log('headRegFlag', this.pageParam.headRegFlag);
        console.log('terminalFlag', this.pageParam.terminalFlag);
        await this.initData();
    },

    methods: {
        /**
         * desc 初始化页面信息
         * <AUTHOR>
         * @date 2023-09-13
         */
        async initData() {
            if (this.pageParam.headRegFlag) {
                this.formData.activityId = this.pageParam.activityId;
                this.formData.activityName = this.pageParam.activityName;
                this.formData.registerRequest = this.pageParam.registerRequest;
            }
            if (this.pageParam.terminalFlag) {
                this.formData.accountName = this.pageParam.accountName;
                this.formData.terminalName = this.pageParam.accountName;
                this.formData.accountId = this.pageParam.terminalId;
                this.formData.terminalId = this.pageParam.terminalId;
                this.formData.terminalCode = this.pageParam.terminalCode;
            }
        },
        /**
         * desc 选择活动之前
         * <AUTHOR>
         * @date 2023-09-13
         */
        beforeSelectHeadAct() {
            return new Promise(async (resolve, reject) => {
                if (this.formData.activityName && this.formData.terminalName) {
                    try {
                        await this.$dialog({
                            title: '提示',
                            content: '切换总部活动后将更新联系人列表，是否确认继续',
                            confirmButton: true,
                            cancelButton: true,
                            onConfirm: () => {
                                resolve(); // 用户点击确认后，调用 resolve 来继续下一步操作
                            },
                            onCancel: () => {
                                // reject(); // 用户点击取消后，调用 reject 来取消下一步操作
                            },
                        });
                    } catch (error) {
                        // 处理对话框打开失败的情况
                        reject(error);
                    }
                } else {
                    resolve(); // 如果条件不满足，直接继续下一步操作
                }
            });
        },
        /**
         * desc 选择活动之后
         * <AUTHOR>
         * @date 2023-09-13
         */
        async afterSelectHeadAct(data) {
            this.$set(this.formData, 'registerRequest', data.registerRequest);
            this.$set(this.formData, 'activityId', data.activityId);
        },
        /**
         * desc 选择终端之前
         * <AUTHOR>
         * @date 2023-09-13
         */
        beforeSelectTerminal() {
            return new Promise(async (resolve, reject) => {
                if (this.formData.activityName && this.formData.terminalName) {
                    try {
                        await this.$dialog({
                            title: '提示',
                            content: '切换终端后将更新联系人列表，是否确认继续',
                            confirmButton: true,
                            cancelButton: true,
                            onConfirm: () => {
                                resolve(); // 用户点击确认后，调用 resolve 来继续下一步操作
                            },
                            onCancel: () => {
                                // reject(); // 用户点击取消后，调用 reject 来取消下一步操作
                            },
                        });
                    } catch (error) {
                        // 处理对话框打开失败的情况
                        reject(error);
                    }
                } else {
                    resolve(); // 如果条件不满足，直接继续下一步操作
                }
            });
        },
        /**
         * desc 选择终端之后
         * <AUTHOR>
         * @date 2023-09-13
         */
        async afterSelectTerminal(data) {
            this.$set(this.formData, 'accountId', data.id);
            this.$set(this.formData, 'terminalId', data.id);
            this.$set(this.formData, 'companyId', data.companyId);
            this.$set(this.formData, 'terminalCode', data.accountCode);
            this.$set(this.formData, 'terminalName', data.accountName);
        },
        /**
         * @desc 检查重复号码
         * <AUTHOR>
         * @date 2023-09-13
         */
        checkRepate() {
            const phoneNumSet = new Set(); // 用于记录已存在的 phoneNum
            for (const contact of this.contactsArr) {
                const phoneNum = contact.phoneNum;
                if (phoneNumSet.has(phoneNum)) {
                    this.$message.warn('存在重复号码，请将重复号码删除！');
                    return true; // 发现重复号码
                } else {
                    phoneNumSet.add(phoneNum);
                }
            }
            return false; // 未发现重复号码
        },
        /**
         * @desc 查询联系人
         * <AUTHOR>
         * @date 2023-08-02
         */
        async queryContacts() {
            try {
                const data = await this.$http.post('action/link/forwardContact/queryByExamplePage',
                    {
                        filtersRaw: [{
                            id: 'headId',
                            property: 'headId',
                            value: this.formData.activityId,
                            operator: '='
                        }, {
                            id: 'actForm',
                            property: 'actForm',
                            value: 'headquarter',
                            operator: '='
                        }, {
                            id: 'actType',
                            property: 'actType',
                            value: 'MarketingSix',
                            operator: '='
                        }, {
                            id: 'terminalId',
                            property: 'terminalId',
                            value: this.formData.terminalId,
                            operator: '='
                        }
                        ]
                    }
                );
                if (data.success) {
                    this.contactsArr = data.rows;
                    this.contactsArr.forEach(item => {
                        item.row_status = 'UPDATE';
                    })
                } else {
                    this.$message.error(data.message);
                }
            } catch (e) {
                throw e;
            }
        },
        /**
         * @desc 保存前校验联系人
         * <AUTHOR>
         * @date 2023-09-13
         */
        async shareValidate() {
            // 保存逻辑
            try {
                const list = this.contactsArr;
                if (list.length === 0) {
                    this.$message.warn('转发对象不能为空，请添加联系人！');
                    return;
                }
                if(!this.formData.stageName){
                    this.$message.warn('请选择存在活动阶段的活动日期！');
                    return;
                }
                await Promise.all(
                    this.$refs.form.map(formRef => formRef.validate())
                );
                // 如果发现了重复号码，就不执行后续操作
                if (this.checkRepate()) {
                    return;
                }
                // 校验联系人
                const res = await this.$http.post('action/link/headquarterFeedback/checkForward', {
                    activityId: this.formData.activityId,
                    activityType: 'MarketingSix',
                    activityDate: this.formData.activityDate,
                    promotionStageId: this.formData.promotionStageId,
                    terminalId: this.formData.terminalId,
                    terminalCode: this.formData.terminalCode,
                    terminalName: this.formData.terminalName,
                    creatorCode: this.userInfo.username //员工工号
                });
                if (res.success) {
                    await this.share();
                } else {
                    this.$message.error(res.message);
                    return;
                }
            } catch (e) {
                throw e;
            }
        },
        /**
         * @desc 保存联系人
         * <AUTHOR>
         * @date 2023-09-13
         */
        async share() {
            try {
                const list = this.contactsArr;
                const data = await this.$http.post('action/link/forwardContact/batchUpsert', list);
                if (data.success) {
                    this.updateFlag = true;
                    this.$message.success('保存成功');
                    this.gowChatFlag = true;
                    this.$refs.chooseType.show();
                } else {
                    this.$message.error('未保存成功，请重试！');
                    return;
                }
            } catch (e) {
                this.$message.error('未保存成功，' + e);
            }
        },
        /**
         * @desc 删除联系人
         * <AUTHOR>
         * @date 2023-09-13
         * @param key 删除元素索引
         */
        tapSuffixIcon(key) {
            const contact = this.contactsArr[key];
            if (!!contact.phoneNum) {
                contact.phoneNum = '';
            } else {
                this.$dialog({
                    title: '提示',
                    content: '是否删除当前联系人',
                    cancelButton: true,
                    confirmText: '确定',
                    onConfirm: () => {
                        this.contactsArr.splice(key, 1);
                        if (contact.id) {
                            this.deleteArr(contact.id);
                        }
                    },
                    onCancel: () => {

                    }
                });
                console.log('删除后this.contactsArr', this.contactsArr);
            }
        },
        /**
         * @desc 删除联系人-调接口
         * <AUTHOR>
         * @date 2023-09-13
         */
        async deleteArr(contactId) {
            const data = await this.$http.post('action/link/forwardContact/deleteById',
                {
                    id: contactId
                }
            );
            if (data.success) {
                this.$message.success('删除成功');
            } else {
                this.$message.error('删除失败');
            }
        },
        /**
         * @desc 添加联系人
         * <AUTHOR>
         * @date 2023-09-13
         */
        addContacts() {
            let newContacts = {
                row_status: this.updateFlag ? 'UPDATE' : 'NEW',
                headId: this.formData.activityId,
                phoneNum: '',             // 联系电话
                actForm: 'headquarter',
                actType: 'MarketingSix',
                terminalId: this.formData.terminalId
            };
            this.contactsArr.push(newContacts);
        },
        /**
         * @desc 分享小程序
         * <AUTHOR>
         * @data 2023-09-13
         */
        async shareApplet() {
            try {
                const data = await this.$http.post('action/link/headquarterFeedback/afterForward',
                    {
                        id: this.formData.activityId,
                        creatorCode: this.userInfo.username //员工工号
                    }
                );
                if (data.success) {
                    console.log('afterForward接口调用成功！');
                } else {
                    this.$message.error(data.message);
                }
                this.$refs.chooseType.hide();
                if (this.$device.systemInfo.model.indexOf('iPhone') !== -1) {
                    setTimeout(() => {
                        this.shareFlag = false;
                    }, 600);
                }
            } catch (e) {
                throw e;
            }
        },
        /**
         * @desc 取消分享
         * <AUTHOR>
         * @data 2023-09-13
         */
        cancelShare() {
            this.gowChatFlag = false;
            this.$refs.chooseType.hide();
        }
    }
}
</script>

<style lang="scss">
.headquarters-forward-contact-page {
    background: #F2F2F2;

    .contacts-container {
        /*deep*/
        .link-icon {
            font-size: 28px;
        }

        /*deep*/
        .link-input-text-align-left {
            text-align: right;
        }

        /*deep*/
        .link-item {
            padding: 24px;
        }

        .title-container {
            display: flex;

            .contacts-text {
                width: 50%;
            }

            .delete-btn {
                width: 50%;
                text-align: right;
                font-family: PingFangSC-Regular, serif;
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
            }
        }

        .contacts-content {
        }

        padding-top: 1px;

        .link-input-contact {
            height: 100%;
            padding-left: 10px;
            display: flex;
            align-items: center;

            .delete-icon-box {
                padding-left: 12px;
            }
        }

    }

    .add-contacts {
        border: 2px dashed #2F69F8;
        border-radius: 8px;
        margin: 40px auto;
        height: 96px;
        width: 702px;
        text-align: center;

        .text {
            font-family: PingFangSC-Regular, serif;
            font-size: 32px;
            color: #2F69F8;
            letter-spacing: 0;
            line-height: 96px;
        }

        .icon-plus {
            font-size: 32px;
            line-height: 96px;
            color: #2F69F8;
        }
    }

    .blank {
        height: 180px;
        width: 100%;
    }

    .bottom-bg {
        width: 100%;
        height: 100vh;
    }
}
</style>
