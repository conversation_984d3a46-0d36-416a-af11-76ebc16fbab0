<!--
总部活动-申请列表
<AUTHOR>
@date 2023-03-31
-->
<template>
    <link-page class="headquarters-apply-list-page">
        <lnk-taps :taps="headTapOptions" v-model="headActStatusActive" @switchTab="switchTab"></lnk-taps>
        <link-auto-list :option="headquartersList" :searchInputBinding="{props:{placeholder: '活动名称/活动编码'}}">
            <link-filter-group slot="filterGroup">
                <!--                <link-filter-item label="创建时间(升序)" :param="{sort:{field:'created',desc:false}}"/>-->
                <link-filter-item label="最近更新(升序)" :param="{sort:{field:'lastUpdated',desc:false}}"/>
            </link-filter-group>
            <template slot-scope="{data,index}">
                <view  @tap="gotoRegisterList(data)">
                    <activity-item :index="index" :data="data"/>
                </view>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
import Taro from "@tarojs/taro";
import LnkTaps from "../core/lnk-taps/lnk-taps";
import StatusButton from "../lzlj/components/status-button";
import ActivityItem from './components/activity-item';

export default {
    name: "headquarters-apply-list-page",
    components: {LnkTaps, StatusButton, ActivityItem},
    data () {
        // 顶部tab栏选项
        const headTapOptions = [
            {name: '全部', seq: '1', val: 'All'},
            {name: '进行中', seq: '2', val: 'Processing'},
            {name: '已结束', seq: '3', val: 'End'},
           ];

        //顶部筛选栏参数filtersParamsList
        const filtersParamsList = {
            All: [
                {id: 'activityStatus', property: 'activityStatus', operator: 'in', value: '[New, Processing, End, Closed]'},
                {id: 'activityType', property: 'activityType', operator: '<>', value: 'TerminalConstruction'},
                {id: 'approveStatus', property: 'approveStatus', operator: '=', value: 'Approved'}
            ],
            Processing: [
                {id: 'activityStatus', property: 'activityStatus', operator: '=', value: 'Processing'},
                {id: 'activityType', property: 'activityType', operator: '<>', value: 'TerminalConstruction'},
                {id: 'approveStatus', property: 'approveStatus', operator: '=', value: 'Approved'}
            ],
            End: [
                {id: 'activityStatus', property: 'activityStatus', operator: '=', value: 'End'},
                {id: 'activityType', property: 'activityType', operator: '<>', value: 'TerminalConstruction'},
                {id: 'approveStatus', property: 'approveStatus', operator: '=', value: 'Approved'}
            ],
        };

        // 总部活动列表
        const headquartersList = new this.AutoList(this, {
            module: 'action/link/headquarterActivity',
            url: {
                queryByExamplePage: 'action/link/headquarterActivity/queryByExamplePage',
            },
            param: {
                attr1 : 'Org',
                // 宴席活动反选组织
                attr7 : 'exclude',
                filtersRaw: [
                    {
                        id: 'activityStatus',
                        property: 'activityStatus',
                        operator: '=',
                        value: 'Processing'
                    },
                    {
                        id: 'approveStatus',
                        property: 'approveStatus',
                        operator: '=',
                        value: 'Approved'
                    },
                    {
                        id: 'activityType',
                        property: 'activityType',
                        operator: '<>',
                        value: 'TerminalConstruction'
                    }
                ]
                // queryFields:'created,activityNum,status,activityName,startTime,endTime'
            },
            searchFields: ['activityName', 'activityStatus', 'activityNum','activityType'],
            sortField: 'created',
            sortDesc: 'desc',
            sortOptions: null,
            filterOption:[
                {label: '活动主题', field: 'activityType', type: 'lov',lov: 'HEAD_ACT_TYPE',
                    lovOption: {excludeLovs: ['TerminalConstruction', 'ceshi']}},
                {label: '开始时间', field: 'startTime', type: 'date'},
                {label: '结束时间', field: 'endTime', type: 'date'}
            ]
        });

        const positionType = Taro.getStorageSync('token').result.positionType   //获取当前用户职位信息
        const postnType1 = ['Salesman', 'SalesTeamLeader', 'CustServiceSpecialist'];
        const postnType2 = [
            'GroupBuyManager',
            'AccountManager',
            'RegionalManager',
            'CustServiceManager',
            'VipManager',
            'ChannelSupervisor',
            'CustServiceSupervisor',
            'BattleCommander',
            'SalesSupervisor',
            'BLRegionManager',
            'BPRegionManager',
            'SalesManager',
            'SalesChannelManger',
            'RInternalStaff',
            'InternalStaff',
            'CInternalStaff',
            'CityManager',
            'SalesAreaManager',
            'SalesRegionManager',
            'DeptSpecialist',
            'RegionManager',
            'DeptManager',
            'HRManager',
            'InternalMarketStaff',
            'FileMaintainer',
            'HRSpecialist',
            'SysAdmin',
            'SalesGeneralManager',
            'GeneralManager',
            'BrandManager'
        ];
        return {
            headActStatusActive: {}, // 头部tab栏选中项
            hideCreateButton: true, // 是否隐藏新建按钮
            headquartersList, //总部申请活动
            headTapOptions, // 头部tab选项
            filtersParamsList, // 筛选条件
            positionType,           //当前登陆人职位类型
            postnType1,            //职位类型A
            postnType2,           //职位类型B，详情查看lzljqw-004-838
            oauth: ''
        }
    },
    async created() {
        this.headActStatusActive = this.headTapOptions[1];
        // if (this.postnType1.includes(this.positionType)) {
        //     this.headquartersList.option.param.attr1 = 'Position';
        //     this.oauth = 'MY_POSTN';
        // }
        // if (this.postnType2.includes(this.positionType)) {
        //     this.headquartersList.option.param.attr1 = 'Org';
        //     this.oauth = 'MY_ORG';
        // }
    },
    methods: {
        /**
         * tab页切换
         *  <AUTHOR>
         *  @date 2023-04-03
         *  全部：活动状态为未开始、进行中、已结束、提前结束
         *  进行中：活动状态为进行中
         *  已结束：活动状态为已结束
         */
        switchTab(item) {
            this.headActStatusActive = item;
            this.headquartersList.option.param.filtersRaw = this.filtersParamsList[item.val]
            this.headquartersList.methods.reload();
        },

        /**
         * 复制活动编码
         *  <AUTHOR>
         *  @date 2023-03-28
         */
        copyActCode(text) {
            wx.setClipboardData({data: text});
        },
        /**
         * 跳转总部登记列表
         * <AUTHOR>
         * @date 2023-04-04
         */
        gotoRegisterList(data) {
            if(data.activityType === 'Banquet') {
                this.$nav.push('/pages/headquarters-activity/banquet-policy-detail-page.vue', {
                    data: data
                })
            }
            else{
                this.$nav.push('/pages/headquarters-activity/headquarters-register-list-page', {
                    data: data,
                    activityId: data.id,
                    oauth: this.oauth
                })
            }
        },

        /**
         * 监控返回函数
         * <AUTHOR>
         * @date 2020-09-15
         * @param param
         */
        onBack (param) {
            if (this.$utils.isEmpty(param)) {
                return
            }
            if (param.refreshFlag) {
                this.headquartersList.methods.reload();
            }
        },
        /**
         * 返回上一页
         * <AUTHOR>
         * @date 2020-10-19
         * @param data 返回携带终端数据
         */
        goBack (data) {
            this.$nav.back()
        },
    }
}
</script>

<style lang="scss">
.headquarters-apply-list-page {
    background-color: #F2F2F2;
    font-family: PingFangSC-Regular;
    .link-input{
        width: 100%;
    }
    /*deep*/
    .lnk-tabs-container {
        height: 92px;
    }
}
</style>
