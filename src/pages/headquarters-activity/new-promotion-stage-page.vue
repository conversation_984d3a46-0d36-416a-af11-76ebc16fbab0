<!--
总部活动-投放终端列表-新建推广阶段
<AUTHOR>
@date 2023-05-24
-->
<template>
    <link-page class="new-promotion-stage-page">
        <link-form :value="formData" :rules="rules" ref="newPromotion">
            <link-form-item label="阶段" field="promotionStage" required>
                <link-lov type="PROMOTION_STAGE" v-model="formData.promotionStage"/>
            </link-form-item>
            <link-form-item label="开始时间" field="startTime" required>
                <link-date view="YMD" v-model="formData.startTime" value-format="YYYY-MM-DD"
                           display-format="YYYY-MM-DD"></link-date>
            </link-form-item>
            <link-form-item label="结束时间" field="endTime" required>
                <link-date view="YMD" v-model="formData.endTime" value-format="YYYY-MM-DD"
                           display-format="YYYY-MM-DD"></link-date>
            </link-form-item>
        </link-form>
        <link-sticky>
            <link-button block @tap="save">保存</link-button>
            <link-button block @tap="commit">提交</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
import {ROW_STATUS} from "../../utils/constant";

export default {
    name: 'new-promotion-stage-page',
    data() {

        return {
            formData: {},
            rules: {
                promotionStage: this.Validator.required('请选择阶段'),
                startTime: this.Validator.required('请选择开始时间'),
                endTime: this.Validator.required('请选择结束时间')
            }
        }
    },
    methods: {
        async save() {
            try {
                await this.$refs.newPromotion.validate();
                if(this.formData.startTime > this.formData.endTime) {
                    this.$message.warn('开始时间不能大于结束时间！');
                    return Promise.reject('开始时间不能大于结束时间！');
                }
                this.$set(this.formData, 'approvalStatus', 'New');
                this.$set(this.formData, 'stageStatus', 'New');
                const data = await this.$http.post('action/link/promotionStage/insert', {
                    ...this.formData,
                    terminalId: this.pageParam.terminalId,
                    row_status: ROW_STATUS.NEW
                });
                if(data.success) {
                    await this.goBack();
                    this.$bus.$emit('promotionStageList');  // 刷新推广阶段列表
                } else {
                    this.$showError('新建失败，请重试！');
                }
            } catch (e) {
                this.$showError('新建失败，请重试！');
            }
        },
        /**
         * desc 返回
         * <AUTHOR>
         * @date 2023-05-25
         */
        async goBack() {
            this.$nav.back();
        },
        /**
         * desc 提交
         * <AUTHOR>
         * @date 2023-06-13
         */
        async commit() {
            try {
                await this.$refs.newPromotion.validate();
                if(this.formData.startTime > this.formData.endTime) {
                    this.$message.warn('开始时间不能大于结束时间！');
                    return Promise.reject('开始时间不能大于结束时间！');
                }
                const data = await this.$http.post('action/link/promotionStage/submit', {
                    ...this.formData,
                    terminalId: this.pageParam.terminalId
                });
                if(data.success) {
                    await this.goBack();
                    this.$bus.$emit('promotionStageList');  // 刷新推广阶段列表
                } else {
                    // this.$showError('提交失败，请重试！');
                }
            } catch (e) {
                // this.$showError('提交失败，请重试！');
            }
        }
    }
}

</script>

<style lang="scss">
.new-promotion-stage-page {

}
</style>
