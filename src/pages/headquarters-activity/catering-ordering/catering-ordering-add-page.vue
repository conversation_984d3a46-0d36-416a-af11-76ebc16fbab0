<!--
 订单创建
 <AUTHOR>
 @date	2024/07/23
-->
<template>
    <link-page class="catering-ordering-add-page">
        <view v-if="step===1">
            <consumer-list @getConsumer='getConsumer'></consumer-list>
        </view>
        <view v-else-if="step===2">
            <terminal-list  @getTerminal='getTerminal'></terminal-list>
        </view>
        <view v-else>
            <view class="head-content">
                <view class="head-bg" :style="{'backgroundImage':`url(${imageBg})`}">
                    订餐编码：{{formData.feedbackCode}}
                </view>
                <view class="row-box">
                    <view class="content-row">
                        <view class="label">消费者信息</view>
                        <text>{{formData.consumerName}}</text>
                        <view class="line"></view>
                        <text>{{formData.consumerPhone}}</text>
                    </view>
                    <view class="content-row">
                        <view class="label">终端名称</view>
                        <text>{{formData.terminalName}}</text>
                    </view>
                    <view class="content-row">
                        <view class="label">终端地址</view>
                        <text>{{formData.address}}</text>
                    </view>
                </view>
            </view>
            <link-form v-model="formData" ref="orderForm">
                <link-form-item label="关联活动" field="activityName" required  @tap='changeActivity'>
                    <link-input v-model="formData.activityName" disabled placeholder="关联活动"/>
                </link-form-item>
                <link-form-item label="预约时间" field="appointedTime" required>
                    <link-date view="YMDHm" valueFormat="YYYY-MM-DD HH:mm" displayFormat="YYYY年MM月DD日 HH时mm分" v-model="formData.appointedTime"/>
                </link-form-item>
                <link-form-item label="活动抵扣券" field="activityCoupon" required @tap='changeCoupon'>
                    <link-input v-model="formData.activityCoupon" placeholder="请选择抵扣券" readonly/>
                </link-form-item>
                <link-form-item label="套餐餐标" field="menuMark" required @tap='changeMenu'>
                    <link-input v-model="formData.menuMark" disabled placeholder="请选择套餐餐标"/>
                </link-form-item>
                <link-form-item label="费用执行经销商" field="dealerName" required  @tap='changeDealer'>
                    <link-input v-model="formData.dealerName"  disabled placeholder="费用执行经销商"/>
                </link-form-item>
                <link-form-item label="包间号" field="privateRoom" required>
                    <link-input v-model="formData.privateRoom"  />
                </link-form-item>
                <link-form-item label="预约桌数" field="tableNumber" required>
                    <link-number v-model="formData.tableNumber" hideButton :precision="0" :max='999'/>
                </link-form-item>
                <link-form-item label="就餐人数" field="clientNumber" required>
                    <link-number v-model="formData.clientNumber" hideButton :precision="0" :max='999'/>
                </link-form-item>
                <link-form-item label="餐用酒数量(瓶)" field="wineNumber" required>
                    <link-number v-model="formData.wineNumber" hideButton :precision="0" :max='999'/>
                </link-form-item>
                <link-form-item label="备注" field="feedbackRemark" vertical>
                    <link-textarea style="padding-bottom: 12px" mode="textarea" v-model="formData.feedbackRemark" />
                </link-form-item>
            </link-form>
            <link-sticky>
                <link-button mode="stroke" block @tap='save("UPDATE")'>保存</link-button>
                <link-button block @tap='submit'>提交</link-button>
            </link-sticky>
        </view>
        <coupon-bottom
         :showDealer.sync="showCoupon"
         :mobilePhone='formData.consumerPhone'
         :didiCompanyId='formData.didiCompanyId'
         :exchangeEndtime='formData.appointedTime'
         @choose='choseCoupon'/>
        <menu-bottom :acct-code='formData.terminalCode' :showDealer.sync="showMenu" @choose='choseMenu'/>
        <dealer-bottom
        @choose='choseDealer'
        :mdmCompanyCode='userInfo.coreOrganizationTile.brandCompanyCode||"5600"'
        :city='formData.city'
        :showDealer.sync="showDealer"/>
        <activity-bottom :showDealer.sync="showActivity" @choose='choseActivity'/>

    </link-page>
</template>

<script>
    import consumerList from './components/consumser-list.vue'
    import terminalList from './components/terminal-list.vue'
    import dealerBottom from './components/dealer-bottom.vue'
    import couponBottom from './components/coupon-bottom.vue'
    import menuBottom from './components/menu-bottom.vue'
    import activityBottom from './components/activity-bottom.vue'
   definePageConfig({
       navigationBarTitleText: '消费者'
   })
    export default {
        name: 'catering-ordering-add-page',
        components: {consumerList, terminalList, dealerBottom, couponBottom, menuBottom, activityBottom},
        data(){
            const baseUrl = `${this.$env.imageAssetPath}/images/catering-ordering/`
            const userInfo = this.$taro.getStorageSync('token').result
            // 套餐餐标
            const setMenuOption = new this.AutoList(this, {
                url: {
                    queryByExamplePage: 'action/link/accntStoreMealLabel/queryByExamplePage'
                },
                param: () => {
                    return {
                        city: this.formData.city, //终端所属城市
                        mdmCompanyCode:  this.formData.mdmCompanyCode, //终端所属公司品牌编码
                        rows: 25,
                        filtersRaw: []
                    }
                }
            });
            return {
                imageBg: baseUrl + 'bg-order-title.png',
                userInfo,
                showDealer: false,
                showCoupon: false,
                showMenu: false,
                showActivity: false,
                setMenuOption,
                formData: {},
                step: 1,//创建订单进度 1、消费者选择；2、终端选择；3订单创建
            }
        },
        async created(){
            if(this.pageParam.id){
               await this.getDetail(this.pageParam.id)
            }
        },
        methods: {
            changeCoupon(){
                if(!this.formData.appointedTime){
                    this.$message.error('请先选择预约时间')
                    return
                }
                this.showCoupon = true
            },
            changeMenu(){
                if(!this.formData.appointedTime){
                    this.$message.error('请先选择预约时间')
                    return
                }
                this.showMenu = true
            },
            changeActivity(){
                this.showActivity = true
            },
            changeDealer(){
                this.showDealer = true
            },
            /**
             * 选择优惠券
             * @created  谭少奇
             * @date  2024/07/25
             */
            choseCoupon(data){
                this.formData.activityCoupon = data.businessName
                this.formData.activityCouponId = data.id
                this.formData.activityCouponAmount = data.disAmount
                this.formData.deliveryId = data.channelMemberId //出库单id
                this.formData.unionId = data.unionId //微信unionId
            },
            /**
             * 选择关联活动
             * @created  梁莉丹
             * @date  2024/10/17
             */
            choseActivity (data) {
                this.formData.actType = data.activityType
                this.formData.activityId = data.id
                this.formData.activityName = data.activityName
            },
            /**
             * 选择餐标
             * @created  谭少奇
             * @date  2024/07/25
             */
            choseMenu(data){
                this.formData.menuMarkId = data.id
                this.formData.menuMark = data.mealLabelName
            },
            /**
             * 选择经销商
             * @created  谭少奇
             * @date  2024/07/25
             */
            choseDealer(data){
                this.formData.dealerId = data.dealerId
                this.formData.dealerName = data.dealerName
                this.formData.dealerCode = data.dealerCode
            },
            async getDetail(id){
                const url = '/action/link/feedbackCatering/queryById'
                const {success,result} = await this.$http.post(url, {id})
                if(success){
                    this.formData = result
                    this.step = 3
                }
            },
            /**
             * 选择消费者
             * @created  谭少奇
             * @date  2024/07/25
             */
            getConsumer(data){
                this.$taro.setNavigationBarTitle({title: '订餐终端列表'});
                this.step = 2
                this.formData.consumerId = data.id //消费者id
                this.formData.consumerName = data.acctName //消费者名称
                this.formData.consumerPhone = data.mobilePhone1 //消费者手机号
                this.formData.didiCompanyId = data.companyId
            },
            /**
             * 选择终端
             * @created  谭少奇
             * @date  2024/07/25
             */
            async getTerminal(data){
                this.$taro.setNavigationBarTitle({title: '订餐创建'});
                this.$set(this.formData, 'city', data.city)
                this.$set(this.formData, 'mdmCompanyCode', data.mdmCompanyCode)
                this.formData.terminalId = data.id
                this.formData.terminalCode = data.acctCode
                this.formData.terminalName = data.acctName
                this.formData.province = data.province
                this.formData.city = data.city
                this.formData.district = data.district
                this.formData.address = data.address
                setTimeout(async ()=>{
                    await this.save('NEW')
                    this.step = 3
                },500)

            },
            /**
             * 订单保存
             * @created  谭少奇
             * @date  2024/07/25
             */
            async save(row_status = 'UPDATE', submit = false){
                const url = '/action/link/feedbackCatering/save'
                if(!this.formData.feedbackName){
                    this.formData.feedbackName = (this.formData.terminalName ? this.formData.terminalName+'-' :'') + this.$filter.date(new Date(), 'YYMMDD') +  '-' + this.formData.consumerName
                }
                const param = {
                    ...this.formData,
                    row_status
                }
                if(row_status !== 'NEW'){
                    await this.$refs.orderForm.validate()
                }
                try{
                    const {success,rows} = await this.$http.post(url,param)
                    if(success){
                       this.formData = {...rows, ...this.formData}
                       if(row_status !== 'NEW' && !submit){
                           let params = {refreshFlags: true};
                           this.$nav.back(params);
                       }
                    }
                }catch(e){
                    //TODO handle the exception
                }


            },
            /**
             * 订单提交
             * @created  谭少奇
             * @date  2024/07/25
             */
            async submit(){
                await this.$refs.orderForm.validate();
                const time = this.$filter.date(this.formData.appointedTime, 'YYYY-MM-DD HH:mm:ss') ? this.$filter.date(this.formData.appointedTime, 'YYYY-MM-DD HH:mm:ss') : this.formData.appointedTime + ':00';
                const checkSub = await this.$http.post('/action/link/feedbackCatering/checkMoreThan2Order', {
                    appointedTime: time,
                    terminalId: this.formData.terminalId
                });
                if (checkSub.success) {
                    if (checkSub.result) {
                        this.$dialog({
                            title: '提示',
                            content: '该订单存在同一个业务人员相同时间段在同一饭店订餐2桌及以上。',
                            cancelButton: true,
                            onConfirm: async () => {
                                this.submitInfo();
                            },
                            onCancel: () => {
                                console.log('取消提交');
                            }
                        })
                    } else {
                        this.submitInfo();
                    }
                } else {
                    this.$message.error({message: '查询审批校验接口失败！', customFlag: true});
                }
            },
            async submitInfo() {
                await this.save('UPDATE', true);
                const url = '/action/link/feedbackCatering/submit';
                const param = {
                    id: this.formData.id
                };
                try{
                    const data = await this.$http.post(url,param);
                    if(data.success){
                        let params = {refreshFlags: true};
                        this.$nav.back(params);
                    }
                } catch(e) {
                    console.log('订单提交失败：', e);
                }
            }
        }
    }
</script>

<style lang="scss">
    .catering-ordering-add-page{ 
        .active-box{
            font-size: 28px;
            color: #212223;
            line-height: 50px;
            .line{
                width: 5px;
                height: 28px;
                display: inline-block;
                background: #DADBE2;
                margin: 0 20px;
            }
        }
        padding-top: 10px;
        .head-bg{
            width: 662px;
            height: 80px;
            padding: 24px;
            border-radius: 16px;
            background-size: cover;
            background-repeat: no-repeat;
            font-size: 32px;
            font-weight: 600;
        }
        .head-content{
            margin: 24px 24px 0 24px;
            font-size: 28px;
            .row-box{
                padding: 24px;
                position: relative;
                border-radius: 16px;
                top: -40px;
                margin: 2px;
                background: linear-gradient( 90deg, #FFFFFF 0%,  #F8F8FF 100%);
            }
            .content-row{
                margin-top: 20px;
                display: flex;
                align-items: center;
                .label{
                    color: #999999;
                    min-width: 160px;
                }
                .line{
                    width: 5px;
                    height: 28px;
                    display: inline-block;
                    background: #DADBE2;
                    margin: 0 20px;
                }
            }
        }
    }
</style>
