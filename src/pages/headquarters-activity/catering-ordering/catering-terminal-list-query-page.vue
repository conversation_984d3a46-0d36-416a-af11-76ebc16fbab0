<!--
 * 餐饮终端订餐信息列表查询
 * @Author: 梁莉丹
 * @Date: 2024-10-10
 * @description: 业代仅可查询不可编辑 可查询登录人品牌公司的全部餐饮终端
-->
<template>
	<link-page class="catering-terminal-list-query-page">
		<link-auto-list :option="option" :hideCreateButton="true" :searchInputBinding="{ props: { placeholder: '终端名称' } }">
			<link-filter-group slot="filterGroup">
				<link-filter-item label="更新时间(升序)" :param="{ sort: { field: 'lastUpdated', desc: false } }" />
				<!-- <link-filter-item
					label="餐饮终端"
					:param="{ filter: [{ property: 'cateringStatus', operator: '=', value: 'Approved', id: 'cateringStatus' }] }" /> -->
			</link-filter-group>
			<template slot-scope="{ data, index }">
				<link-swipe-action>
					<item :key="index" :data="data" :arrow="false" class="terminal-item-wrap" @tap="goDetail(data)">
						<terminal-info-card :data="data" />
					</item>
				</link-swipe-action>
			</template>
		</link-auto-list>
	</link-page>
</template>

<script>
	definePageConfig({
		navigationBarTitleText: '餐饮终端订餐信息查询',
	});
	import TerminalInfoCard from '../components/terminal-info-card.vue';

	export default {
		name: 'catering-terminal-list-query-page',
		components: { TerminalInfoCard },
		data() {
			const userInfo = this.$taro.getStorageSync('token').result;
			let filtersRaw = [];
			filtersRaw = [
				{ id: 'acctType', property: 'acctType', value: 'Terminal', operator: '=' },
				{ id: 'multiAcctMainFlag', property: 'multiAcctMainFlag', value: 'Y', operator: '=' },
				{ id: 'dataSource', property: 'dataSource', value: 'WeChatWork', operator: '=' },
				{ id: 'cateringStatus', property: 'cateringStatus', value: 'Approved', operator: '='}
			];
			if (userInfo.positionType !== 'InternalStaff') {
				filtersRaw.push({ id: 'acctStatus', property: 'acctStatus', value: 'Y', operator: '=' });
			}
			const option = new this.AutoList(this, {
				module: 'export/link/es/simpleaccnt',
				param: {
					tagListFlag: 'Y', //是否需要终端标签字段
					filtersRaw: filtersRaw,
					oauth: 'MULTI_ORG',
					multiAcctMainId: null,
					cateringFlag: 'Y'
				},
				loadOnStart: false,
				queryFields:
					'id,acctType,censusLabels,isExclusiveShop,fourColorLabel,financingFlag,acctName,billTitle,' +
					'codeMark,acctStage,acctCategory,subAcctType,acctLevel,capacityLevel,salesmanBrandCom,joinFlag,' +
					'acctCode,multiAcctMainFlag,salesManListString,province,city,district,townName,address,storePicKey,' +
					'storePicPreKey,tagList,judgmentFlag,doorSigns,terminalDigitization,accntPartner,orgId,editApprovalStatus,' +
					'isSpringAct,displayPolicyType,salesmanAreaId,strategicFlag,mdmCompanyCode,cateringStatus',
				sortOptions: null,
				searchFields: ['acctName', 'acctCode'],
				hooks: {
					afterLoad: (data) => {
						data.rows.map((item) => {
							if (!this.$utils.isEmpty(item.storePicPreKey)) {
								let urlData = this.$image.getSignedUrl(item.storePicPreKey);
								this.$set(item, 'storeUrl', urlData);
							} else {
								this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
							}
						});
					},
					beforeLoad: (options) => {
						if (this.isEs === 'Y') {
							options.param.sort = 'yearScanCode';
							options.param.order = 'desc';
						} else {
							delete options.param.order;
							delete options.param.sort;
						}
					},
				},
			});
			return {
				option,
				isEs: false,
				userInfo,
			};
		},
		methods: {
			/**
			 * @description: 详情查看
			 * @param {object} 餐饮终端对象
			 * @Author: lld
			 * @Date: 2024-10-10
			 */
			goDetail(data) {
				// 只有餐饮终端才可进行订餐信息维护 
				this.$nav.push('/pages/headquarters-activity/catering-ordering/catering-terminal-detail-query-page', {
					data: data,
				});
			},
		},
		async mounted() {
			this.isEs = await this.$utils.getCfgProperty('useEsQuerySimpleAccountPage');
			this.option.methods.reload();
		},
	};
</script>

<style lang="scss">
	.catering-terminal-list-query-page {
		/*deep*/
		.link-item {
			margin: 24px;
			padding: 0;
			background-color: #f8f8f8;
		}
		.swipe-option {
			flex-direction: column;
		}
	}
</style>
