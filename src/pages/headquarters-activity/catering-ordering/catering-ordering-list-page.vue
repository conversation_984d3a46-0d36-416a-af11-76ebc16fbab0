<!--
 订餐列表
 <AUTHOR>
 @date	2024/07/23
-->
<template>
    <link-page class="catering-ordering-list-page">
        <lnk-taps :taps="orderTapsOption" v-model="orderTapsActive" @switchTab="switchTab"></lnk-taps>
        <link-auto-list :option="option">
            <template  slot="other">
                <link-fab-button  @tap="goCreateOrder"/>
            </template>
            <template v-slot="{data,index}">
                <link-card>
                    <view @tap="goOrderItem(data)" class="card">
                        <view class="card-tag">
                            <text>{{data.status | lov('CATERING_TYPE')}}</text>
                        </view>
                        <view class="card-code">
                            <text>{{data.feedbackCode}}</text>
                        </view>
                        <!-- $filter.date(data.appointedTime, 'YYMMDD')  -->
                        <view class="card-title">
                            {{data.feedbackName }}
                        </view>
                        <view class="card-content">
                            活动名称 <text>{{data.activityName}}</text>
                        </view>
                        <view  class="card-content">
                            预约时间 <text>{{data.appointedTime}}</text>
                        </view>
                        <view v-if="data.status === 'NeedVerify'" class="card-verify" @tap.stop="getCode(data)"> 查询核销二维码 </view>
                    </view>
                </link-card>

            </template>
        </link-auto-list>

        <link-dialog ref="dialog">
            <LinkQrCode :text="qrCodeUrl"/>
        </link-dialog>

    </link-page>
</template>

<script>
    import lnkTaps from '../../core/lnk-taps/lnk-taps'
    definePageConfig({
        navigationBarTitleText: '订餐列表'
    })
    export default {
        name: 'catering-ordering-list-page',
        components: {lnkTaps},
        data(){
            const userInfo = this.$taro.getStorageSync('token').result
            const option = new this.AutoList(this, {
                module: '/action/link/feedbackCatering',
                param: {
                    oauth: 'MY_POSTN'
                },
                searchFields: ['feedbackName', 'feedbackCode']
            })
            const orderTapsOption = [
                {name: '全部', seq: 1, val: 'all'},
                {name: '新建', seq: 2, val: 'New'},
                {name: '已取消', seq: 7, val: 'Cancel'},
                {name: '已提交', seq: 8, val: 'Submitted'},
                {name: '待确认', seq: 3, val: 'NeedConfirmed'},
                {name: '待核销', seq: 4, val: 'NeedVerify'},
                {name: '待反馈', seq: 5, val: 'NeedFeedback'},
                {name: '已完成', seq: 6, val: 'Completed'},
            ];
            return {
                userInfo,
                option,
                orderTapsOption,
                orderTapsActive: orderTapsOption[0],
                basicOption: new this.AutoList(this, {
                    module: 'action/link/basic',
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} title={data.name} data={data}>{data.val}</item>
                        )
                    }
                }),
                basicOption2: new this.AutoList(this, {
                    module: 'action/link/basic',
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} title={data.name} data={data}>{data.val}</item>
                        )
                    }
                }),
                qrCodeUrl:''
            }
        },
        methods:{
            /**
             * 监控返回函数
             * <AUTHOR>
             * @date 2024-07-29
             * @param param
             */
            onBack (param) {
                if (param&&param.refreshFlags) {
                    this.option.methods.reload();
                }
            },
            async switchTab(val){
                if(val.val==='all'){
                    this.option.option.param.filtersRaw = []
                    await this.option.methods.reload();
                }else{
                    this.option.option.param.filtersRaw = [
                        {id: 'status', property: 'status', value: val.val, operator: '='},
                    ]
                    await this.option.methods.reload();
                }

            },
            goCreateOrder(){
                this.$nav.push('/pages/headquarters-activity/catering-ordering/catering-ordering-add-page.vue')
            },
            goOrderItem(data){
                if(data.status === 'New'){
                    this.$nav.push('/pages/headquarters-activity/catering-ordering/catering-ordering-add-page.vue', {
                        id: data.id,
                    })
                }else{
                    this.$nav.push('/pages/headquarters-activity/catering-ordering/catering-ordering-detail-page.vue', {
                        id: data.id,
                        canEdit: !['Cancel', 'NeedConfirmed', 'Submitted'].includes(data.status)
                    })
                }

            },
            /**
             * @description: 获取核销二维码
             * @author: 邓佳柳
             * @Date: 2024-09-09 16:47:35
             * @param {*} data
             */
            async getCode(data) {
                // this.qrCodeUrl = 'writeOffCode=ff0e4dcee0af4b77a59801f3bcd6eaeb&couponId=760905701409964042'
                try {
                    console.log(data)
                    if (data.writeOffCode) {
                        this.qrCodeUrl = data.writeOffCode
                        this.$refs.dialog.show();
                    } else {
                        this.$message.error('未获取到核销二维码信息')
                    }
                } catch (e) {}
            },
        }
    }
</script>

<style lang="scss">
    .catering-ordering-list-page{
        .link-list{
            .link-sticky.link-sticky-top{
                top:92px !important;
            }
            .link-auto-list-wrapper{
                margin-top: 92px!important;
                padding: 10px 30px;
            }
        }
        .link-card {
            margin-top: 24px;
            padding: 0;
            .link-card-body .link-card-content {
                padding: 0;
            }
            .card{
                padding: 24rpx;
                line-height: 56px;
                font-size: 28px;
                position: relative;
                &-tag{
                    position: absolute;
                    right: 0;
                    top: 0;
                    color: #3F66EF;
                    padding: 6px 28px;
                    background: rgba(63,102,239,0.1);
                    border-radius: 0 16px 0 16px;
                    font-size: 24px;
                }
                &-code{
                    color: #3F66EF;
                    text{
                        font-size: 24px;
                        padding: 5px 10px;
                        background: linear-gradient( 90deg, #E5EBFF 0%, #F5F9FF 49%, #E5EBFF 100%);
                        border-radius: 2px;
                    }
                }
                &-title{
                    font-size: 32px;
                    font-weight: 600;
                }
                &-content{
                    color: #999999;;
                    text{
                        color: #212223;
                        margin-left: 20px;
                    }
                }
                &-verify {
                    position: absolute;
                    right: 24px;
                    top: 40%;
                    color: #3f66ef;
                    padding: 6px 28px;
                    background: rgba(63, 102, 239, 0.1);
                    border-radius: 16px;
                    font-size: 24px;
                }
            }
        }
        .verify-code {
            width: 500px;
            height: 500px;
            background: #f2f2f2;
        }
    }
</style>
