<!--
 * @Author: 邓佳柳
 * @Date: 2024-09-09 10:23:32
 * @Description: 餐饮终端套餐审批 - SalesAreaManager 片区经理进行审批
 * @LastEditors: 邓佳柳
 * @LastEditTime: 2024-09-11 10:41:59
-->

<template>
	<link-page class="catering-terminal-meallabel-approval-page">
		<approval-history-point v-if="!$utils.isEmpty(approvalId)" :approvalId="approvalId"></approval-history-point>
		<terminal-info-card :data="terminalInfo" />
		<!-- 套餐 -->
		<view class="catering-terminal-taocan">
			<info-title data="套餐餐标" :show-bg="true" />
			<!-- 套餐列表 编辑 新增 -->
			<view class="catering-terminal-taocan-list">
				<view class="cagtering-ordering-taocan-item" v-for="(item, index) in mealLabels" :key="index">
					<view class="taocan-item-title">套餐{{ index + 1 }}</view>
					<view class="text-area-box">
						<link-textarea
							disabled
							class="text-area-input-box"
							:placeholder="'请输入套餐信息...'"
							mode="textarea"
							v-model="item.mealLabelName"
							:nativeProps="{ maxlength: 100 }"
							placeholder-style="color: #BFBFBF;" />
						<text class="text-area-count">{{ (item.mealLabelName && item.mealLabelName.length) || 0 }} / {{ 100 }}</text>
					</view>
					<view class="env-img-box">
						<lnk-img
							:delFlag="isEditable"
							:newFlag="isEditable"
							:module-type="moduleType"
							:parent-id="item.id"
							:isCount="true"
							:count="3"
							:width="imgDeviceWidth"
							:height="imgDeviceWidth"></lnk-img>
					</view>
				</view>
			</view>
		</view>
		<approval-operator :showOwn='false' :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)"></approval-operator>
	</link-page>
</template>

<script>
	definePageConfig({
		navigationBarTitleText: '餐饮套餐审批信息维护',
	});
	import TerminalInfoCard from '../components/terminal-info-card.vue';
	import approvalHistoryPoint from '../../lzlj/approval/components/approval-history-point.vue';
	import InfoTitle from '../components/info-title.vue';
	import LnkImg from '../../core/lnk-img/lnk-img.vue';
	import ApprovalOperator from '../../lzlj/approval/components/approval-operator.vue';

	export default {
		name: 'catering-terminal-meallable-approval-page',
		components: { TerminalInfoCard, approvalHistoryPoint, InfoTitle, LnkImg, ApprovalOperator },
		data() {
			return {
				terminalInfo: {},
				approvalId: '',
				accntId: '',
				disabledFlag: false,
				isEditable: false,
				storeInfo: {
					id: '',
					storeDescription: '',
					orderInstructions: '',
				},
				mealLabels: [],
				moduleType: 'catering-terminal',
				imgDesignWidth: 196,
				imgDeviceWidth: 103,
			};
		},
		methods: {
			async getTerminalInfo() {
				let res = await this.$http.post('action/link/accnt/queryById', {
					id: this.accntId,
				});
				if (!res.success) return;
				this.terminalInfo = res.result;

				if (!this.$utils.isEmpty(this.terminalInfo.storePicPreKey)) {
					let urlData = this.$image.getSignedUrl(this.terminalInfo.storePicPreKey);
					this.$set(this.terminalInfo, 'storeUrl', urlData);
				} else {
					this.$set(this.terminalInfo, 'storeUrl', this.$imageAssets.terminalDefaultImage);
				}
			},
			async getStoreInfo() {
				try {
					let res = await this.$http.get(`action/link/accntStore/get/by/acctId?acctId=${this.accntId}`);
					if (res.success) {
						if (!this.$utils.isEmpty(res.data)) {
							// 套餐信息
							this.mealLabels = res.data.mealLabels;
						}
					}
				} catch (error) {}
			},
		},
		async created() {
			this.approvalId = (this.pageParam.data && this.pageParam.data.id) || '775684738363749407';
			this.accntId = (this.pageParam.data && this.pageParam.data.flowObjId) || '775679399589377162';
			this.$taro.getSystemInfo({
				success: (system) => {
					let { screenWidth, pixelRatio } = system;
					let ratio = 750 / screenWidth;
					console.log('pixelRatio :>> ', pixelRatio, ratio);
					this.imgDeviceWidth = this.imgDesignWidth / ratio;
				},
			});
			this.getTerminalInfo();
			this.getStoreInfo();
		},
	};
</script>

<style lang="scss">
	.catering-terminal-meallabel-approval-page {
		.terminal-list-item {
			margin: 24px auto;
		}
		.catering-terminal-taocan {
			background-color: #fff;
			min-height: 48px;
			width: 702px;
			margin: 24px auto;
			.catering-terminal-taocan-list {
				overflow: hidden;
				.link-swipe-action {
					width: 100%;
				}
				.cagtering-ordering-taocan-item {
					padding-bottom: 22px;
					border-top: 2px solid rgba(242, 242, 242, 0.7);

					&:first-of-type {
						border-top: 0;
					}
					&.view-taocan-item {
						overflow: hidden;
						width: 654px;
						height: 204px;
						background: #f3f7ff;
						border-radius: 12px;
						margin: 24rpx auto 16px;
					}
					.taocan-item-title {
						font-size: 28px;
						color: #6a6d75;
						line-height: 40px;
						margin-left: 24px;
						margin-top: 16px;
					}
					.text-area-input-box {
						// height: 120px;
					}
					.env-img-box {
						margin-top: 24px;
						/* deep */
						.lnk-img {
							/* deep */
							.lnk-img-item {
								margin-left: 24px;
							}
						}
					}
					.taocan-item-content {
						@include flex;
						align-items: center;
						font-size: 28px;
						color: #333333;
						line-height: 40px;
						.taocan-item-content-thumb-img {
							width: 124px;
							height: 124px;
							margin: 16px;
							border-radius: 16px;
						}
						.taocan-item-content-name {
							flex: 1;
						}
						.taocan-item-content-chakan {
							color: #9ea1ae;
							margin-right: 24px;
						}
					}
				}
				.catering-terminal-taocan-add {
					font-weight: 500;
					font-size: 28px;
					color: #3f66ef;
					line-height: 48px;
					@include flex;
					align-items: center;
					justify-content: center;
					padding-top: 10px;
					padding-bottom: 32px;
				}
			}
		}
		// textarea
		.text-area-box {
			position: relative;
			padding-bottom: 16px;
			.text-area-input-box {
				padding: 0;
				border: 0;
				/*deep*/
				.link-textarea-content {
					border: 0;
				}
			}
			.text-area-count {
				position: absolute;
				right: 24px;
				bottom: 0;
				color: #9ea1ae;
				font-size: 20px;
			}
		}
		.link-textarea.link-textarea-disabled .link-textarea-content {
			background-color: #fff;
		}
		.link-textarea .link-textarea-content {
			height: 200px !important;
		}
        .button-group{
            background-color: #fff;
        }
	}
</style>
