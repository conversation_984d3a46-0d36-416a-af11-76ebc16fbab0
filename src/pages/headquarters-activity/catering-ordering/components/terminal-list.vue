<template>
    <view class="termianls-list-chose">
        <link-auto-list :option="option">
            <template v-slot="{data,index}">
                <link-card>
                    <view @tap="chose(data)" class="card">
                        <view class="list-cell">
                            <view class="media-list">
                                <image class="media-list-logo" :src="data.storeUrl" @tap.stop="previewStoreUrl(data)"
                                    lazy-load="true"></image>
                                <view class="store-content">
                                    <view class="store-content-top" v-if="data.acctType">
                                        <!--【客户一级分类】为“终端Terminal”的时候显示storeSigns字段-->
                                        <view class="store-title" v-if="data.acctType === 'Terminal'">{{data.acctName}}</view>
                                        <!--【客户一级分类】为“分销商Distributor”时展示billTitle字段-->
                                        <view class="store-title" v-if="data.acctType === 'Distributor'">{{data.acctName || data.billTitle}}</view>
                                        <!--已认证-->
                                        <view class="store-level" v-if="data.acctStage === 'ykf'">
                                            <image :src="$imageAssets.storeStatusVerifiedImage"></image>
                                        </view>
                                        <!--未认证-->
                                        <view class="store-level" v-if="data.acctStage === 'xk'">
                                            <image :src="$imageAssets.storeStatusUnverifiedImage"></image>
                                        </view>
                                        <!--已失效-->
                                        <view class="store-level" v-if="data.acctStage === 'ysx'">
                                            <image :src="$imageAssets.storeStatusInvalidationImage"></image>
                                        </view>
                                        <!--潜客-->
                                        <view class="store-level"
                                            v-if="data.acctStage === 'dkf' && !isYangShengOrYouXuan">
                                            <image :src="$imageAssets.storeStatusPotentialImage"></image>
                                        </view>
                                    </view>
                                    <view class="store-content-middle">
                                        <view class="left">
                                            <color-tag :value="data.fourColorLabel" v-if="data.fourColorLabel" />
                                            <view class="store-type" v-if="data.financingFlag">贷 |
                                                {{data.financingFlag | lov('YR_FINANCING_FLAG')}}</view>
                                            <view class="store-type" v-if="typeList.includes(data.acctType)">
                                                {{data.acctType | lov('ACCT_TYPE')}}</view>
                                            <view class="store-type" v-if="categoryLst.includes(data.acctCategory)">
                                                {{data.acctCategory | lov('ACCNT_CATEGORY')}}</view>
                                            <view class="store-type" v-if="sublist.includes(data.subAcctType)">
                                                {{data.subAcctType | lov('SUB_ACCT_TYPE')}}</view>
                                            <!-- 战略零售商标签 -->
                                            <view class="store-type"
                                                v-if="data.strategicFlag && data.strategicFlag === 'Y'">
                                                {{data.strategicFlag | lov('STRATEGIC_TAG')}}</view>
                                            <view class="store-type" v-if="levelList.includes(data.acctLevel) || caplist.includes(data.capacityLevel)">
                                                <text v-if="levelList.includes(data.acctLevel)">{{data.acctLevel | lov('ACCT_LEVEL')}}</text>
                                                <text v-if="levelList.includes(data.acctLevel) && caplist.includes(data.capacityLevel)"> | </text>
                                                <text v-if="caplist.includes(data.capacityLevel)">{{data.capacityLevel | lov('CAPACITY_LEVEL')}}</text>
                                            </view>
                                            <view class="store-type"
                                                v-if="data.joinFlag && broadCompanyCode.indexOf(userInfo.coreOrganizationTile.brandCompanyCode) !== -1">
                                                {{data.joinFlag | lov('JOIN_FLAG')}}
                                            </view>
                                            <view class="store-type" v-if="data.judgmentFlag === 'Y'">品鉴</view>
                                            <view class="store-type" v-if="data.doorSigns !== undefined">
                                                {{data.doorSigns | lov('DOOR_SIGNS')}}</view>
                                            <!--                                            <view class="store-type" v-if="data.isExclusiveShop !== undefined&&data.isExclusiveShop == 'Y'">形象店</view>-->
                                            <view class="store-type" v-if="data.terminalDigitization">
                                                <text>{{data.terminalDigitization | lov('TERMINAL_DIGITIZATION')}}</text>
                                            </view>
                                            <!-- @edit by 谭少奇 2023/07/17 17:27 兴冀公司合作状态字段显示逻辑加入 -->
                                            <view class="store-type"
                                                v-if="data.accntPartner && (data.salesmanAreaId === '521074619762290688' || userInfo.coreOrganizationTile.l3Id === '411091729387553190')">
                                                <text>{{data.accntPartner | lov('ACCT_STATE')}}</text>
                                            </view>
                                            <view class="store-type" v-if="data.isSpringAct === 'Y'">春雷行动</view>
                                            <block v-if="data.displayPolicyType">
                                                <view class="store-type"
                                                    v-for="(item, index) in data.displayPolicyType.split(',')"
                                                    :key="index">{{item | lov('DISPLAY_POLICY_TYPE')}}</view>
                                            </block>
                                            <view class="store-type" v-if="data.codeMark">{{data.codeMark}}</view>
                                            <view class="store-type" v-if="data.censusLabels">
                                                {{data.censusLabels | lov('CENSUS_STATUS')}}</view>
                                        </view>
                                    </view>
                                    <view class="store-content-choose">
                                        <view class="store-content-representative">
                                            <view class="terminal-type">编码</view>
                                            <view class="terminal-name">{{data.acctCode}}</view>
                                        </view>
                                    </view>
                                    <view class="store-content-representative">
                                        <view class="terminal-type">业代</view>
                                        <view class="terminal-name">{{data.salesManListString}}</view>
                                    </view>
                                    <view class="store-content-address">
                                        <view class="store-address">
                                            {{data.province}}{{data.city}}{{data.district}}{{data.address}}</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </link-card>

            </template>
        </link-auto-list>
    </view>
</template>

<script>
	import ColorTag from '@/pages/terminal2/components/ColorTag.vue';

    export default {
        data() {
            const userInfo = this.$taro.getStorageSync('token').result
            console.log(userInfo)
            const isYangShengOrYouXuan = ['1612'].includes(userInfo.coreOrganizationTile.brandCompanyCode);
            let oauth = "";
            oauth = this.$utils.isPostnOauth() === 'MY_POSTN'? 'MULTI_POSTN': this.$utils.isPostnOauth();
            if (oauth === 'MY_ORG') {
                oauth = 'MULTI_ORG'
            }
            if(isYangShengOrYouXuan && userInfo.positionType === 'SalesAreaManager') {
                oauth = 'MULTI_POSTN'
            }
            if(userInfo.positionType === 'CityManager') {
                oauth = 'MULTI_ORG'
            }
            let mdmCompanyCode = userInfo.coreOrganizationTile.brandCompanyCode;        // 品牌公司代码
            let filtersRaw = [
                {id: 'acctType', property: 'acctType', value: '[Terminal, Distributor]', operator: 'in'},
                {id: 'dataSource', property: 'dataSource', value: 'WeChatWork', operator: '='},
                {id: 'multiAcctMainFlag', property: 'multiAcctMainFlag', value: 'Y', operator: '='},
                {id: 'cateringStatus', property: 'cateringStatus', value: 'Approved', operator: '='} //0912 查询餐饮终端
            ];
            if(userInfo.positionType !== 'InternalStaff'){
                filtersRaw.push({id: 'acctStatus', property: 'acctStatus', value: 'Y', operator: '='})
            }
            const option = new this.AutoList(this, {
                module: 'export/link/es/simpleaccnt',
                searchFields: ['acctName', 'acctCode'],
                queryFields: 'id,acctType,mdmCompanyCode,censusLabels,isExclusiveShop,fourColorLabel,financingFlag,acctName,billTitle,codeMark,acctStage,acctCategory,subAcctType,acctLevel,capacityLevel,salesmanBrandCom,joinFlag,acctCode,multiAcctMainFlag,salesManListString,province,city,district,townName,address,storePicKey,storePicPreKey,tagList,judgmentFlag,doorSigns,terminalDigitization,accntPartner,orgId,editApprovalStatus,isSpringAct,displayPolicyType,salesmanAreaId,strategicFlag,cateringStatus',
                param: {
                    tagListFlag: 'Y',  //是否需要终端标签字段
                    filtersRaw: filtersRaw,
                    oauth: 'MULTI_ORG',
                    cateringFlag: 'Y'
                },
                hooks: {
                    beforeLoad (options) {},
                    async afterLoad (data) {
                        data.rows.map(async (item) => {
                            let saleManName = '';
                            if (!this.$utils.isEmpty(item.storePicPreKey) && item.acctType === 'Terminal') {
                                let urlData = this.$image.getSignedUrl(item.storePicPreKey);
                                this.$set(item, 'storeUrl', urlData);
                            } else {
                                this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
                            }
                        })
                    }
                }

            })
            return {
                userInfo,
                option,
                typeList: [],
                categoryLst: [],
                sublist: [],
                levelList: [],
                caplist: [],
                broadCompanyCode: ''
            }
        },
		components: { ColorTag },
        async created() {
            this.getTypeArray()
            this.broadCompanyCode = await this.queryCfgProperty('getPurchaseSumForOrder');
        },
        methods: {
            /**
             * 根据key值获取参数配置
             * <AUTHOR>
             * @date 2024/07/24
             * @param String key 参数配置健值
             */
            async queryCfgProperty(key) {
                const data = await this.$http.post('export/link/cfgProperty/queryByExamplePage', {
                    filtersRaw: [{id: 'key', property: 'key', value: key}]
                });
                if (data.success && data.rows && data.rows.length) {
                    return data.rows[0].value;
                } else {
                    return 'noMatch';
                }
            },
            async getTypeArray() {
                const list = await this.$lov.getLovByTypeArray(['ACCT_TYPE', 'ACCNT_CATEGORY', 'SUB_ACCT_TYPE','ACCT_LEVEL','CAPACITY_LEVEL']);
                list[0].forEach(item => {
                    this.typeList.push(item.val)
                });
                list[1].forEach(item => {
                    this.categoryLst.push(item.val)
                });
                list[2].forEach(item => {
                    this.sublist.push(item.val)
                });
                list[3].forEach(item => {
                    this.levelList.push(item.val)
                });
                list[4].forEach(item => {
                    this.caplist.push(item.val)
                });
            },
            /**
             * 门头照片预览
             * <AUTHOR>
             * @date 2020-09-22
             * @param param
             */
            async previewStoreUrl(param) {
                const compressSuffix = '/suoluetu';
                const defaultSuffix = 'default';
                if (this.$utils.isEmpty(param.storeUrl) || param.storeUrl.indexOf(defaultSuffix) !== -1) {
                    return;
                }
                const inOptions = {
                    current: param.storeUrl.replaceAll(compressSuffix, ''),
                    urls: [param.storeUrl]
                };
                this.$image.previewImages(inOptions);
            },
            chose(data) {
                this.$emit('getTerminal', data)
            }
        }
    }
</script>

<style lang="scss">
    .termianls-list-chose {


        .link-card {
            margin-top: 24px;
            padding: 0;

            .link-card-body .link-card-content {
                padding: 0;
            }

            .card {
                padding: 24rpx;
                line-height: 56px;
                font-size: 28px;
                .list-cell{
                    .media-list {
                        @include flex;
                        padding: 24px 16px 24px 24px;
                        .media-list-logo {
                            /*box-shadow: 0 4px 31px 0 rgba(0,44,152,0.22);*/
                            border-radius: 16px;
                            width: 128px;
                            height: 128px;
                            overflow: hidden;
                        }
                        .store-content {
                            width: 80%;
                            .store-content-top {
                                @include flex-start-center;
                                @include space-between;
                                margin-left: 24px;
                                .store-title {
                                    font-family: PingFangSC-Semibold,serif;
                                    font-size: 32px;
                                    color: #262626;
                                    letter-spacing: 0;
                                    line-height: 36px;
                                    width: 77%;
                                    height: 36px;
                                    overflow: hidden;
                                }
                                .store-level {
                                    margin-right: -3px;
                                    width: 120px;
                                    height: 44px;
                                    image {
                                        width: 100%;
                                        height: 100%;
                                    }
                                }
                            }

                            .store-content-middle {
                                display: flex;
                                justify-content: space-between;
                                padding-left: 32px;
                                .left ,.right{
                                    @include flex-start-center;
                                    flex-wrap: wrap;
                                    margin-top: 10px;
                                    .store-type {
                                        white-space: nowrap;
                                        border: 2px solid #2F69F8;
                                        border-radius: 8px;
                                        font-size: 20px;
                                        padding-left: 18px;
                                        padding-right: 18px;
                                        line-height: 40px;
                                        height: 40px;
                                        color: #2F69F8;
                                        margin-right: 10px;
                                        margin-top: 10px;
                                    }
                                }
                                .item-tag {
                                    width: 58px;
                                    height: 40px;
                                    line-height: 36px;
                                    text-align: center;
                                    color: #ffffff;
                                    background: #2F69F8;
                                    box-shadow: 0 3px 4px 0 rgba(47,105,248,0.35);
                                    border-radius: 8px;
                                    padding-left: 27px;
                                    padding-right: 27px;
                                    font-size: 20px;
                                    margin-right: 8px;
                                    margin-top: 10px;
                                }
                            }
                            .store-content-representative {
                                @include flex;
                                align-items: center;
                                margin-left: 24px;
                                margin-top: 20px;
                                width: calc(100% - 24px);
                                overflow: hidden;
                                white-space: nowrap;
                                text-overflow: ellipsis;
                                .terminal-type {
                                    color: #8C8C8C;
                                    min-width: 50px;

                                }
                                .terminal-name {
                                    font-size: 24px;
                                    color: #000000;
                                    letter-spacing: 0;
                                    padding-left: 8px;
                                    width: calc(100% - 50px);
                                    overflow: hidden;
                                    white-space: nowrap;
                                    text-overflow: ellipsis;
                                }
                            }
                            .store-content-choose {
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                            }
                            .store-content-address {
                                margin-left: 24px;
                                margin-top: 18px;
                                font-family: PingFangSC-Regular,serif;
                                font-size: 24px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 32px;
                            }
                        }
                    }
                }
            }
        }
    }
</style>
