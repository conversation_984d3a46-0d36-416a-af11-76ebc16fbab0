<!--
市场活动筛选
@author:梁莉丹
@date 2024/10/17
 -->
<template>
  <view class="activity-bottom">
      <link-dialog ref="prodBottom" position="bottom" height="85vh" class="dialog-bottom" noPadding v-model="showDealer" @hide="dialogHide">
          <view class="model-title">
              <view class="iconfont icon-close" @tap="dialogHide"></view>
              <view class="title">关联活动</view>
          </view>
          <view class="dialog-content" style="height: calc(100% - 44px)">
              <scroll-view scroll-y="true" :style="{'height': 'calc(100% - 75px)'}">
                  <link-auto-list :option="autoList" hideCreateButton :scrollContent="true">
                      <template slot-scope="{data,index}">
                          <view slot="note">
                              <item :key="index" :data="data" :arrow="false" style="padding: 0;" @tap='choseData(data)'>
                                  <view class="active-box list-item">
                                      <text>{{data.activityName}}</text>
                                  </view>
                                  <view class="checked-items">
                                       <view class="iconfont icon-check colorBlue" v-if="choseId === data.id"></view>
                                  </view>
                              </item>
                          </view>
                      </template>
                  </link-auto-list>
              </scroll-view>
              <view class="link-dialog-foot-custom">
                  <link-button shadow @tap="chooseDealer" label="确定" style="width:100vw"/>
              </view>
          </view>
      </link-dialog>
  </view>
</template>

<script>
  export default {
    name: "activity-bottom",
    props: {
      showDealer: false,
    },
    data() {
        const userInfo = this.$taro.getStorageSync('token').result
        const autoList = new this.AutoList(this, {
          url: {
            queryByExamplePage: this.$env.baseURL + '/action/link/headquarterActivity/processing/catering/activity'
          },
          param: {
            orgId: userInfo.orgId
          },
          hooks: {
            beforeLoad ({param}) {
              delete param.sort
              delete param.order
              delete param.filtersRaw
            }
          },
        });
        return {
            autoList,
            selectData: {},
            choseId: null,
        }
    },
    methods: {
      choseData (data) {
        this.choseId = data.id
        this.selectData = data;
      },
      /**
       * 选择的数据
       * @created  谭少奇
       * @date  2023/11/30
       */
      chooseDealer(){
        this.$emit('choose', this.selectData);
        this.$emit('update:showDealer', false);
      },
      dialogHide(){
        this.$emit('update:showDealer', false);
      }
    }

  }
</script>

<style lang="scss">
.activity-bottom{
    .active-box{
        font-size: 28px;
        color: #212223;
        line-height: 50px;
        .line{
            width: 5px;
            height: 28px;
            display: inline-block;
            background: #DADBE2;
            margin: 0 20px;
        }
    }
    .checked-items{
        min-width: 50px;
        margin-left: 40px;
        .colorBlue{
            color: #337fff;
            font-size: 50px;
        }
    }

    .link-dialog-foot-custom{
        width: auto !important;
    }
  .link-dialog-body{
    position: relative;
  }
  .link-auto-list .link-auto-list-top-bar{
    border:none;
  }
  .link-item .link-item-body-right{
    margin: 0 24px;
  }
  .link-radio-group{
    width: 70px;
    .link-item{
      padding:24px 24px 24px 0;
      .link-item-thumb{
        padding-right: 0;
      }
      .link-item-icon{
        display:none;
      }
    }
    .link-item-active{
      background-color: #f6f6f6;
    }
  }
  .list-item{
    flex: 1;
    font-size: 28px;
    color: #212223;
  }
  .link-radio-group .link-item:active,.link-item-active{
    background-color: #f6f6f6;
  }
  .link-auto-list-no-more{
    display: none;
  }
  .dialog-bottom{
    .dialog-content{
      padding: 0 20px;
      position: relative;
      //.link-button{
      //  position: absolute;
      //  bottom: 0
      //}
    }
    .model-title {
      .title {
        font-family: PingFangSC-Regular,serif;
        font-size: 32px;
        color: #262626;
        letter-spacing: 0;
        text-align: center;
        line-height: 96px;
        height: 96px;
        width: 90%;
        padding-left: 0!important;
        margin-right: 80px;
        margin-left: 10vw;
      }
      .icon-close {
        color: #BFBFBF;
        font-size: 48px;
        line-height: 96px;
        height: 96px;
        margin-right: 30px;
        float:right
      }
    }
  }
}
</style>
