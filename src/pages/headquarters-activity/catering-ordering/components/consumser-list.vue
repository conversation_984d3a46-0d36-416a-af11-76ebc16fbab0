<template>
    <view class="consumer-list">
        <link-auto-list :option="option">
            <template v-slot="{data,index}">
                <link-card>
                    <view @tap="chose(data)" class="card">
                        <view class="card-title">
                            {{data.acctName}}
                        </view>
                        <view class="card-tag">
                            <text>客户推荐</text>
                            <text>拜访收集</text>
                        </view>
                        <view class="card-content">
                            <view class="label">联系方式</view>
                            <text>{{data.mobilePhone1}}</text>
                        </view>
                        <view class="card-content">
                            <view class="label">单位</view>
                            <text>{{data.companyName}}</text>
                        </view>
                        <view class="card-content">
                            <view class="label">职务</view>
                            <text>{{data.position}}</text>
                        </view>
                        <view class="card-content">
                            <view class="label">所属客户</view>
                            <text>{{data.recommenderAcctName}}</text>
                        </view>
                    </view>
                </link-card>

            </template>
        </link-auto-list>
    </view>
</template>

<script>
    export default {
        data(){
            const userInfo = this.$taro.getStorageSync('token').result
            const option = new this.AutoList(this, {
                module: 'action/link/basic',
                url:{
                    queryByExamplePage: this.$env.appURL + '/action/link/consumer/queryBasicConsumer'
                },
                searchFields: ['acctName', 'mobilePhone1'],
                request: ({url, param}) => {
                    console.log(param,'param====>')
                    param = [{
                        postnId: userInfo.postnId,
                        page: param.page,
                        rows:  param.rows,
                        filterList: param.filtersRaw
                    }]
                    const data = this.$http.post(url, param)
                    return data;
                },
                hooks: {
                    afterLoad(data){
                        data.rows = data.result.rows
                        data.total =  data.result.total
                    }
                }
            })
            return {
                option
            }
        },
        created(){
            // this.getList()
            // this.getList2()
        },

        methods:{
            async getList2(){
                const url = this.$env.appURL + '/reward/link/collectRecord/queryCollectRecordByConsumer'
                const param =[{
                    mobilePhone: '18615437946', //消费者的手机号
                    didiCompanyId: '58929495997939712', //, 公司id string 是 消费者的公司
                    exchangeEndtime: '2024-07-24 08:00:00' //订餐时间
                }]
                try{
                    const data = await this.$http.post(url, param);
                }catch(e){
                    //TODO handle the exception
                }
            },
            async getList(){
                console.log(this.$taro.getStorageSync('token').result)
                const userInfo = this.$taro.getStorageSync('token').result
                const url = this.$env.appURL + '/action/link/consumer/queryBasicConsumer'
                const param =[{
                        postnId: userInfo.postnId
                }]
                try{
                    const data = await this.$http.post(url, param);
                    console.log(data,1111111111)
                }catch(e){
                    //TODO handle the exception
                }

            },
            chose(data){
                this.$emit('getConsumer', data)
            }
        }
    }
</script>

<style lang="scss">
    .consumer-list{
        .link-card {
            margin: 24px;
            padding: 0;
            .link-card-body .link-card-content {
                padding: 0;
            }
            .card{
                padding: 24rpx;
                line-height: 56px;
                font-size: 28px;
                &-tag{
                    color: #3F66EF;
                    text{
                        font-size: 24px;
                        margin-right: 24px;
                        padding: 5px 10px;
                        background: linear-gradient( 90deg, #E5EBFF 0%, #F5F9FF 49%, #E5EBFF 100%);
                        border-radius: 2px;
                    }
                }
                &-title{
                    font-size: 32px;
                    font-weight: 600;
                }
                &-content{
                    color: #212223;
                    display: flex;
                    align-items: center;
                    .label{
                        color: #999999;
                        min-width: 120px;
                    }
                    text{
                        margin-left: 20px;
                    }
                }
            }
        }
    }
</style>
