<!--
 * @Author: 邓佳柳
 * @Date: 2024-09-09 10:23:32
 * @Description: 标记餐饮终端审批 - SalesAreaManager 片区经理进行审批
 * @LastEditors: 邓佳柳
 * @LastEditTime: 2024-09-10 17:57:22
-->

<template>
	<link-page class="catering-terminal-approval-page">
		<approval-history-point v-if="!$utils.isEmpty(approvalId)" :approvalId="approvalId"></approval-history-point>
		<terminal-info-card :data="terminalInfo" />
		<view class="approval-box">
			<view class="approval-content">升级为餐饮终端</view>
		</view>
		<approval-operator :showOwn='false' :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)"></approval-operator>
	</link-page>
</template>

<script>
	definePageConfig({
		navigationBarTitleText: '升级餐饮终端信息维护',
	});
	import TerminalInfoCard from '../components/terminal-info-card.vue';
	import approvalHistoryPoint from '../../lzlj/approval/components/approval-history-point.vue';
	import ApprovalOperator from '../../lzlj/approval/components/approval-operator.vue';

	export default {
		name: 'catering-terminal-approval-page',
		components: { TerminalInfoCard, approvalHistoryPoint, ApprovalOperator },
		data() {
			return {
				terminalInfo: {},
				approvalId: '',
				disabledFlag: false,
				accntId: '',
			};
		},
		methods: {
			async getTerminalInfo() {
				let res = await this.$http.post('action/link/accnt/queryById', {
					id: this.accntId,
				});
				this.terminalInfo = res.result;
				if (!this.$utils.isEmpty(this.terminalInfo.storePicPreKey)) {
					let urlData = this.$image.getSignedUrl(this.terminalInfo.storePicPreKey);
					this.$set(this.terminalInfo, 'storeUrl', urlData);
				} else {
					this.$set(this.terminalInfo, 'storeUrl', this.$imageAssets.terminalDefaultImage);
				}
			},
		},
		created() {
			this.approvalId = (this.pageParam.data && this.pageParam.data.id) || '775684738363749407';
			this.accntId = (this.pageParam.data && this.pageParam.data.flowObjId) || '775679399589377162';
			this.getTerminalInfo();
		},
	};
</script>

<style lang="scss">
	.catering-terminal-approval-page {
		.terminal-list-item {
			margin: 24px auto;
		}
		.approval-box {
			padding: 24px;
			background-color: #fff;
			margin-top: 24px;
			.approval-content {
				font-weight: 500;
				font-size: 40px;
				margin-bottom: 80px;
			}
		}
        .button-group{
            background-color: #fff;
        }
	}
</style>
