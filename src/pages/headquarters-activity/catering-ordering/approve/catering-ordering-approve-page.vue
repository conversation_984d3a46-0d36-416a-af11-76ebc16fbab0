<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-10-21
-->

<template>
	<link-page class="catering-ordering-approve-page">
		<approval-history-point v-if="!$utils.isEmpty(approvalId)" :approvalId="approvalId"></approval-history-point>
        <view class="catering-ordering-info">
            <view class="head-content">
               <view class="head-bg" :style="{'backgroundImage':`url(${imageBg})`}">
                   订餐编码：{{formOption.feedbackCode}}
               </view>
               <view class="row-box">
                   <view class="row-title">基本信息</view>
                   <view class="content-row">
                       <view class="label">消费者信息</view>
                       <view>
                           <text>{{formOption.consumerName}}</text>
                           <view class="line"></view>
                           <text>{{formOption.consumerPhone}}</text>
                       </view>
                   </view>
                   <view class="content-row">
                       <view class="label">终端名称</view>
                       <text>{{formOption.terminalName}}</text>
                   </view>
                   <view class="content-row">
                       <view class="label">包间号</view>
                       <text>{{formOption.privateRoom}}</text>
                   </view>
                   <view class="content-row">
                       <view class="label">终端地址</view>
                       <text>{{formOption.addressDetail}}</text>
                   </view>
                   <view class="content-row">
                       <view class="label">关联活动</view>
                       <text>{{formOption.activityName}}</text>
                   </view>
                   <view class="row-title">用餐信息</view>
                   <view class="content-row">
                       <view class="label">活动抵扣券</view>
                       <text>{{formOption.activityCoupon + formOption.activityCouponId}}</text>
                   </view>
                   <view class="content-row">
                       <view class="label">套餐餐标</view>
                       <text>{{formOption.menuMark}}</text>
                   </view>
                   <view class="content-row">
                       <view class="label">预约时间</view>
                       <text>{{formOption.appointedTime}}</text>
                   </view>
                   <view class="content-row">
                       <view class="label">费用执行经销商</view>
                       <text>{{formOption.dealerName + formOption.dealerCode}}</text>
                   </view>
                   <view class="content-row">
                       <view class="label">预约桌数</view>
                       <text>{{formOption.tableNumber}}</text>
                   </view>
                   <view class="content-row">
                       <view class="label">就餐人数</view>
                       <text>{{formOption.clientNumber}}</text>
                   </view>
                   <view class="content-row">
                       <view class="label">餐用酒数量</view>
                       <text>{{formOption.wineNumber}}(瓶)</text>
                   </view>
                   <view class="content-row">
                       <view class="label">备注</view>
                       <text>{{formOption.feedbackRemark}}</text>
                   </view>
               </view>
            </view>
            <view class="catering-ordering-tips" v-if="approvalDetails.approveRemark">
                <view class="catering-ordering-tips-label">
                    提示：
                </view>
                <view class="catering-ordering-tips-info">
                    {{approvalDetails.approveRemark}}
                </view>
            </view>
        </view>
		<approval-operator :showOwn='false' :approvalId="approvalId" @approvalInfoResult="approvalInfoResult" v-if="!$utils.isEmpty(approvalId)"></approval-operator>
	</link-page>
</template>

<script>
	definePageConfig({
		navigationBarTitleText: '餐饮订单审批',
	});
	import approvalHistoryPoint from '../../../lzlj/approval/components/approval-history-point.vue';
	import ApprovalOperator from '../../../lzlj/approval/components/approval-operator.vue';

	export default {
		name: 'catering-ordering-approve-page',
		components: { approvalHistoryPoint, ApprovalOperator },
		data() {
            const baseUrl = `${this.$env.imageAssetPath}/images/catering-ordering/`
			return {
				approvalId: '',
                accntId: '',
                imageBg: baseUrl + 'bg-order-title.png',
                wineAfterMeal: baseUrl + 'wineAfterMeal.jpg',
                formOption: {},
                moduleName: '餐后用酒',
                moduleName1: '菜单',
                moduleName2: '支付凭证',
                moduleName3: '发票',
                approvalDetails: {}
			};
		},
        created() {
			this.approvalId = this.pageParam.data?.id;
			this.accntId = this.pageParam.data?.flowObjId;
            this.getDetail(this.accntId);
		},
		methods: {
            async getDetail(id){
                try{
                    const {success, result} = await this.$http.post('/action/link/feedbackCatering/queryById',{id})
                        if(success){
                            this.formOption = result;
                        }
                }catch(e){
                    //TODO handle the exception
                }
            },
            // 审批单详情信息
            approvalInfoResult(data) {
                this.approvalDetails = JSON.parse(data.flowObjDetail);
            }
		}
	};
</script>

<style lang="scss">
	.catering-ordering-approve-page {
		.head-content{
            margin: 24px 24px 0 24px;
            font-size: 28px;
            .head-bg{
                width: 662px;
                height: 80px;
                padding: 24px;
                border-radius: 16px;
                background-size: cover;
                background-repeat: no-repeat;
                font-size: 32px;
                font-weight: 600;
            }
            .row-box{
                padding: 24px;
                position: relative;
                border-radius: 16px;
                top: -40px;
                margin: 2px;
                background: linear-gradient( 90deg, #FFFFFF 0%,  #F8F8FF 100%);
                .row-title{
                    margin-top: 30px;
                    font-weight: 600;
                    font-size: 32px;
                    color: #333333;
                }
            }
            .content-row{
                margin-top: 20px;
                display: flex;
                // align-items: center;
                justify-content: space-between;
                text-align: right;
                .label{
                    color: #999999;
                    min-width: 260px;
                    text-align: left;
                }
                .line{
                    width: 5px;
                    height: 28px;
                    display: inline-block;
                    background: #DADBE2;
                    margin: 0 20px;
                }
            }
        }
		.approval-box {
			padding: 24px;
			background-color: #fff;
			margin-top: 24px;
			.approval-content {
				font-weight: 500;
				font-size: 40px;
				margin-bottom: 80px;
			}
		}
        .button-group{
            background-color: #fff;
        }
        .catering-ordering-tips {
            border-radius: 16px;
            background-color: #fff;
            margin: 0 24px 24px 24px;
            padding: 24px;
            color: #333333;
            .catering-ordering-tips-label {
                font-weight: 600;
                font-size: 32px;
                margin-bottom: 10px;
            }
            .catering-ordering-tips-info {
                font-size: 28px;
            }
        }
	}
</style>
