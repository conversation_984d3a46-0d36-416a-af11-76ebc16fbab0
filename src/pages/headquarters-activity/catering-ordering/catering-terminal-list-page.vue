<!--
 * @Author: <EMAIL>
 * @Date: 2024-07-23 16:23:27
 * @Description: 餐饮终端订餐信息维护(只展示与业务员有所属关系的终端，不进行终端客户类型过滤；)
 * @LastEditors: janedeng072
 * @LastEditTime: 2024-10-17 11:29:54
-->
<template>
	<link-page class="catering-terminal-list-page">
		<link-auto-list :option="option" :hideCreateButton="true" :searchInputBinding="{ props: { placeholder: '终端名称' } }">
			<link-filter-group slot="filterGroup">
				<link-filter-item label="更新时间(升序)" :param="{ sort: { field: 'lastUpdated', desc: false } }" />
				<link-filter-item
					label="餐饮终端"
					:param="{ filter: [{ property: 'cateringStatus', operator: '=', value: 'Approved', id: 'cateringStatus' }] }" />
			</link-filter-group>
			<template slot-scope="{ data, index }">
				<link-swipe-action>
					<item :key="index" :data="data" :arrow="false" class="terminal-item-wrap" @tap="goDetail(data)">
						<terminal-info-card :data="data" />
					</item>
					<link-swipe-option slot="option" class="swipe-option" @tap="markCaterTag(data)">
						<block v-if="data.cateringStatus && data.cateringStatus === 'Approved'">
							<view>取消标记</view>
						</block>
						<block v-else>
							<view>标记为</view><view>餐饮终端</view>
						</block>
					</link-swipe-option>
				</link-swipe-action>
			</template>
		</link-auto-list>
	</link-page>
</template>

<script>
	definePageConfig({
		navigationBarTitleText: '餐饮终端订餐信息维护',
	});
	import TerminalInfoCard from '../components/terminal-info-card.vue';

	export default {
		name: 'catering-terminal-list-page',
		components: { TerminalInfoCard },
		data() {
			const userInfo = this.$taro.getStorageSync('token').result;
			let oauth = '';
			// 判断 职位安全性 （业务组织及下级的数据）/ 组织安全性
			const isPostnOauth = this.$utils.isPostnOauth();
			oauth = isPostnOauth === 'MY_POSTN' ? 'MULTI_POSTN' : 'MULTI_ORG';
			// YangShengOrYouXuan SalesAreaManager 走职位安全性
			const isYangShengOrYouXuan = ['1216', '1612'].includes(userInfo.coreOrganizationTile.brandCompanyCode);
			const isSalesAreaManager = this.$taro.getStorageSync('token').result.positionType === 'SalesAreaManager';
			if (isYangShengOrYouXuan && isSalesAreaManager) {
				oauth = 'MULTI_POSTN';
			}
			// CityManager 走组织安全性
			if (userInfo.positionType === 'CityManager') {
				oauth = 'MULTI_ORG';
			}

			let filtersRaw = [];
			filtersRaw = [
				{ id: 'acctType', property: 'acctType', value: 'Terminal', operator: '=' },
				{ id: 'multiAcctMainFlag', property: 'multiAcctMainFlag', value: 'Y', operator: '=' },
				{ id: 'dataSource', property: 'dataSource', value: 'WeChatWork', operator: '=' },
			];
			if (userInfo.positionType !== 'InternalStaff') {
				filtersRaw.push({ id: 'acctStatus', property: 'acctStatus', value: 'Y', operator: '=' });
			}
			const option = new this.AutoList(this, {
				module: 'export/link/es/simpleaccnt',
				param: {
					tagListFlag: 'Y', //是否需要终端标签字段
					filtersRaw: filtersRaw,
					oauth: oauth,
					multiAcctMainId: null,
				},
				loadOnStart: false,
				queryFields:
					'id,acctType,censusLabels,isExclusiveShop,fourColorLabel,financingFlag,acctName,billTitle,' +
					'codeMark,acctStage,acctCategory,subAcctType,acctLevel,capacityLevel,salesmanBrandCom,joinFlag,' +
					'acctCode,multiAcctMainFlag,salesManListString,province,city,district,townName,address,storePicKey,' +
					'storePicPreKey,tagList,judgmentFlag,doorSigns,terminalDigitization,accntPartner,orgId,editApprovalStatus,' +
					'isSpringAct,displayPolicyType,salesmanAreaId,strategicFlag,mdmCompanyCode,cateringStatus',
				sortOptions: null,
				searchFields: ['acctName', 'acctCode'],
				hooks: {
					afterLoad: (data) => {
						data.rows.map((item) => {
							if (!this.$utils.isEmpty(item.storePicPreKey)) {
								let urlData = this.$image.getSignedUrl(item.storePicPreKey);
								this.$set(item, 'storeUrl', urlData);
							} else {
								this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
							}
						});
					},
					beforeLoad: (options) => {
						console.log('options', options, this.isEs);
						if (this.isEs === 'Y') {
							options.param.sort = 'yearScanCode';
							options.param.order = 'desc';
						} else {
							delete options.param.order;
							delete options.param.sort;
						}
					},
				},
			});
			return {
				option,
				isEs: false,
				userInfo,
				hasOauth: false,
			};
		},
		methods: {
			/**
			 * @description: 维护详情
			 * @param {object} 餐饮终端对象
			 * @Author: <EMAIL>
			 * @Date: 2024-07-24 14:28:15
			 */
			goDetail(data) {
				// 只有餐饮终端才可进行订餐信息维护，其余终端不可进行订餐信息维护，点击后提示“该终端非餐饮终端，如需维护餐标请先进行餐饮终端升级”
				if (data.cateringStatus && data.cateringStatus === 'Approved') {
					this.$nav.push('/pages/headquarters-activity/catering-ordering/catering-terminal-detail-page', {
						data: data,
					});
                } else {
                    this.$dialog({
							title: '提示',
							content: '该终端非餐饮终端，如需维护餐标请先进行餐饮终端升级',
							cancelButton: false,
							onConfirm: async () => {},
						});
                }
			},
			/**
			 * @description: 标记为餐饮终端
			 * @author: 邓佳柳
			 * @Date: 2024-09-06 10:16:27
			 */
			async markCaterTag(data) {
				try {
					// if (data.cateringStatus && data.cateringStatus === 'Approved') {
					// 	this.$dialog({
					// 		title: '提示',
					// 		content: '该终端已被标记为餐饮终端',
					// 		cancelButton: false,
					// 		onConfirm: async () => {},
					// 	});
					// 	return;
					// }
					// if (data.cateringStatus && data.cateringStatus === 'Approving') {
					// 	this.$dialog({
					// 		title: '提示',
					// 		content: '该终端已在审批中，请误重复标记。',
					// 		cancelButton: false,
					// 		onConfirm: async () => {},
					// 	});
					// 	return;
					// }
					this.$utils.showLoading('提交中...');
					let res = await this.$http.post('action/link/accnt/catering/submit', {
						id: data.id,
						cateringStatus:data.cateringStatus&& data.cateringStatus === 'Approved' ? null : 'Approved'
					});
					if (res.success) {
						this.$utils.hideLoading();
						this.$message.success('操作成功');
						this.option.methods.reload();
					}
				} catch (e) {
					this.$utils.hideLoading();
					console.log('e :>> ', e);
				}
			},
		},
		async mounted() {
			this.isEs = await this.$utils.getCfgProperty('useEsQuerySimpleAccountPage');
			this.option.methods.reload();
			// 标记餐饮终端只有业务代表及业务主管可以进行。
			// 241011：只要能看到菜单就可做终端标记 取消权限控制
			// this.hasOauth = ['SalesSupervisor', 'Salesman'].includes(this.userInfo.positionType);
		},
	};
</script>

<style lang="scss">
	.catering-terminal-list-page {
		/*deep*/
		.link-item {
			margin: 24px;
			padding: 0;
			background-color: #f8f8f8;
		}
		.swipe-option {
			flex-direction: column;
		}
	}
</style>
