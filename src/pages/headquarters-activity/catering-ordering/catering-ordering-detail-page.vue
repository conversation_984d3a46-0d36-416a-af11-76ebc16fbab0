<!--
 订餐详情
 <AUTHOR>
 @date	2024/07/23
-->
<template>
    <link-page class="catering-ordering-detail-page">
        <view>
           <view class="head-content">
               <view class="head-bg" :style="{'backgroundImage':`url(${imageBg})`}">
                   订餐编码：{{formOption.feedbackCode}}
               </view>
               <view class="row-box">
                   <view class="row-title" v-if='formOption.status !== "NeedConfirmed"'>基本信息</view>
                   <view class="content-row">
                       <view class="label">消费者信息</view>
                       <view>
                           <text>{{formOption.consumerName}}</text>
                           <view class="line"></view>
                           <text>{{formOption.consumerPhone}}</text>
                       </view>
                   </view>
                   <view class="content-row">
                       <view class="label">终端名称</view>
                       <text>{{formOption.terminalName}}</text>
                   </view>
                   <view class="content-row">
                       <view class="label">包间号</view>
                       <text>{{formOption.privateRoom}}</text>
                   </view>
                   <view class="content-row">
                       <view class="label">终端地址</view>
                       <text>{{formOption.addressDetail}}</text>
                   </view>
                   <view class="content-row">
                       <view class="label">关联活动</view>
                       <text>{{formOption.activityName}}</text>
                   </view>
                   <view class="row-title" v-if='formOption.status !== "NeedConfirmed"'>用餐信息</view>
                   <view class="content-row">
                       <view class="label">活动抵扣券</view>
                       <text>{{formOption.activityCoupon + formOption.activityCouponId}}</text>
                   </view>
                   <view class="content-row">
                       <view class="label">套餐餐标</view>
                       <text>{{formOption.menuMark}}</text>
                   </view>
                   <view class="content-row">
                       <view class="label">预约时间</view>
                       <text>{{formOption.appointedTime}}</text>
                   </view>
                   <view class="content-row">
                       <view class="label">费用执行经销商</view>
                       <text>{{formOption.dealerName + formOption.dealerCode}}</text>
                   </view>
                   <view class="content-row">
                       <view class="label">预约桌数</view>
                       <text>{{formOption.tableNumber}}</text>
                   </view>
                   <view class="content-row">
                       <view class="label">就餐人数</view>
                       <text>{{formOption.clientNumber}}</text>
                   </view>
                   <view class="content-row">
                       <view class="label">餐用酒数量</view>
                       <text>{{formOption.wineNumber}}(瓶)</text>
                   </view>
                   <view class="content-row">
                       <view class="label">备注</view>
                       <text>{{formOption.feedbackRemark}}</text>
                   </view>
               </view>
           </view>
           <view class="img-box" v-if='formOption.status !== "NeedConfirmed"'>
               <view class="img-head">已上传图片</view>

               <view class="img-title">1.餐前照片</view>
               <view v-if="formOption.id" class="image-up-box">
                    <lnk-img-watermark :parentId="formOption.id"
                        moduleType="FeedWineBeforeMeal"
                        photoPageSource="storePunchCard"
                        :moduleName="moduleName"
                        :delFlag="false"
                        :rootId="formOption.id"
                        :newFlag="pageParam.canEdit">
                    </lnk-img-watermark>
               </view>
               <view class="img-title">2.餐后用酒全景照</view>
               <view v-if="formOption.id" class="image-up-box">
                    <lnk-img-watermark :parentId="formOption.id"
                        moduleType="FeedWineAfterMeal"
                        photoPageSource="storePunchCard"
                        :moduleName="moduleName"
                        :delFlag="false"
                        :rootId="formOption.id"
                        :newFlag="pageParam.canEdit">
                    </lnk-img-watermark>
               </view>
               <view class="img-title">3.菜单<text>（按套餐关联）</text></view>
               <view v-if="formOption.id" class="image-up-box">
                     <lnk-img-watermark :parentId="formOption.id"
                         moduleType="FeedMenu"
                         photoPageSource="storePunchCard"
                         :moduleName="moduleName1"
                         :delFlag="false"
                         :rootId="formOption.id"
                         :newFlag="pageParam.canEdit">
                     </lnk-img-watermark>
               </view>
               <view class="img-title">4.支付凭证</view>
               <view v-if="formOption.id" class="image-up-box">
                     <lnk-img-watermark :parentId="formOption.id"
                         moduleType="FeedPayment"
                         photoPageSource="storePunchCard"
                         :moduleName="moduleName2"
                         :delFlag="false"
                         :rootId="formOption.id"
                         :newFlag="pageParam.canEdit">
                     </lnk-img-watermark>
               </view>
               <view class="img-title">5.发票</view>
               <view v-if="formOption.id" class="image-up-box">
                     <lnk-img-watermark :parentId="formOption.id"
                         moduleType="FeedInvoice"
                         photoPageSource="storePunchCard"
                         :moduleName="moduleName3"
                         :delFlag="false"
                         :rootId="formOption.id"
                         :newFlag="pageParam.canEdit">
                     </lnk-img-watermark>
               </view>
           </view>
           <link-sticky>
               <link-button v-if="['NeedConfirmed', 'NeedVerify'].includes(formOption.status) && pageParam.source != 'feedback'" mode="stroke" block :disabled="formOption.optionType==='Cancel'" @tap='cancle'>取消</link-button>
               <link-button v-if="['Feedbacked'].includes(formOption.status) && pageParam.source != 'feedback'" block @tap='submit'>提交</link-button>
           </link-sticky>
        </view>
    </link-page>
</template>

<script>
    import linkImg from '../../core/lnk-img/lnk-img'
    import LnkImgWatermark from '../../core/lnk-img-watermark/lnk-img-watermark';
   definePageConfig({
       navigationBarTitleText: '订餐明细'
   })
   export default{
       name:'catering-ordering-detail-page',
       components: {linkImg,LnkImgWatermark},
       data(){
           const baseUrl = `${this.$env.imageAssetPath}/images/catering-ordering/`
           return {
                formOption: {},
                moduleName: '餐后用酒',
                moduleName1: '菜单',
                moduleName2: '支付凭证',
                moduleName3: '发票',
                imageBg: baseUrl + 'bg-order-title.png',
                wineAfterMeal: baseUrl + 'wineAfterMeal.jpg',
                menu: baseUrl + 'menu.png',
                payment: baseUrl + 'payment.png',
                invoice: baseUrl + 'invoice.png',
           }
       },
       created(){
           if(this.pageParam.id){
               this.getDetail(this.pageParam.id)
           }
       },
       methods:{
           /**
            * 订单保存
            * @created  谭少奇
            * @date  2024/07/25
            */
           async getDetail(id){
               try{
                   const {success, result} = await this.$http.post('/action/link/feedbackCatering/queryById',{id})
                   if(success){
                       this.formOption = result
                   }
               }catch(e){
                   //TODO handle the exception
               }
           },
           /**
            * 取消
            * @created  谭少奇
            * @date  2024/07/25
            */
           cancle(){
               this.$dialog({
                   title: '提示',
                   content: '确定取消当前订单？',
                   cancelButton: true,
                   confirmText: '确定取消',
                   cancelText: '我再想想',
                   onConfirm: async () => {
                       const url = '/action/link/feedbackCatering/cancel'
                       try {
                            const data = await this.$http.post(url, {id: this.formOption.id})
                            if(data.success){
                                let params = {refreshFlags: true};
                                this.$nav.back(params);
                            }
                       } catch (e) {
                           console.log('e',e)
                       }
                   },
                   onCancel: () => {
                       this.$message.info('取消操作')
                   }
               })
           },
           /**
            * 提交
            * @created  谭少奇
            * @date  2024/07/25
            */
           async submit(){
               const url = '/action/link/feedbackCatering/feedbackSubmit'
               const data = await this.$http.post(url, {id: this.formOption.id})
               if(data.success){
                   let params = {refreshFlags: true};
                   this.$nav.back(params);
               }
           },
           idCardBack(){

           },
           deleteIdCardBack(){

           }
       }
   }
</script>

<style lang="scss">
    .catering-ordering-detail-page{
        .image-up-box{
            // display: flex;
            .demo-img-item{
                width: 145px;
                height: 145px;
                display: inline-flex;
                image{
                    height: 100%;
                    width: 100%;
                    display: inline-block;
                    object-fit: cover;
                    border-radius: 16px;
                }
            }
        }
        .head-bg{
            width: 662px;
            height: 80px;
            padding: 24px;
            border-radius: 16px;
            background-size: cover;
            background-repeat: no-repeat;
            font-size: 32px;
            font-weight: 600;
        }
        .img-box{
            background: white;
            padding: 24px;
            margin: 24px;
            font-weight: 500;
            font-size: 28px;
            color: #333333;
            .img-head{
                font-size: 32px;
                font-weight: 600;
            }
            .img-title{
                margin: 20px 0;
                font-weight: 500;
                text{
                   font-weight: normal;
                   color: #999999;
                }
            }
        }
        .head-content{
            margin: 24px 24px 0 24px;
            font-size: 28px;
            .row-box{
                padding: 24px;
                position: relative;
                border-radius: 16px;
                top: -40px;
                margin: 2px;
                background: linear-gradient( 90deg, #FFFFFF 0%,  #F8F8FF 100%);
                .row-title{
                    margin-top: 30px;
                    font-weight: 600;
                    font-size: 32px;
                    color: #333333;
                }
            }
            .content-row{
                margin-top: 20px;
                display: flex;
                // align-items: center;
                justify-content: space-between;
                text-align: right;
                .label{
                    color: #999999;
                    min-width: 260px;
                    text-align: left;
                }
                .line{
                    width: 5px;
                    height: 28px;
                    display: inline-block;
                    background: #DADBE2;
                    margin: 0 20px;
                }
            }
        }
    }
</style>
