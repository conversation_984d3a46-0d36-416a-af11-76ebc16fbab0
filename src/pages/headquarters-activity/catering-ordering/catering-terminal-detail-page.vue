<!--
 * @Author: <EMAIL>
 * @Date: 2024-07-23 16:23:27
 * @Description:
 * @LastEditors: janedeng072
 * @LastEditTime: 2024-10-21 15:18:46
-->
<template>
	<link-page class="catering-terminal-detail-page">
		<terminal-info-card :data="data" />
		<!-- 门店介绍 -->
		<view class="catering-terminal-store-description">
			<info-title data="门店介绍" />
			<view v-if="!isEditable">
				<view :class="['text-area-content', 'part', { all: showMoreDes }]">{{ storeInfo.storeDescription }}</view>
				<view class="show-more-info" v-if="!showMoreDes && storeInfo.storeDescription" @tap="showAll('showMoreDes')">
					<text>展示完整</text>
					<link-icon icon="icon-down1" />
				</view>
			</view>
			<view v-else class="text-area-box">
				<link-textarea
					class="text-area-input-box"
					:placeholder="'请输入门店介绍信息...'"
					mode="textarea"
					v-model="storeInfo.storeDescription"
					:nativeProps="{ maxlength: 1000 }"
					placeholder-style="color: #BFBFBF;" />
				<text class="text-area-count">{{ storeInfo.storeDescription.length }} / {{ 1000 }}</text>
			</view>
		</view>
		<!-- 门店环境 -->
		<view class="catering-terminal-env">
			<info-title data="门店环境" />
			<view class="env-img-box">
				<lnk-img
					:delFlag="isEditable"
					:newFlag="isEditable"
					:module-type="moduleType"
					:parent-id="data.id"
					:isCount="true"
					:count="6"
					:width="imgDeviceWidth"
					:height="imgDeviceWidth"
					@imgUploadSuccess="imgUploadSuccessShop"
					@imgDeleteSuccess="imgDeleteSuccessShop"></lnk-img>
			</view>
		</view>
		<!-- 下单须知 -->
		<view class="catering-terminal-order-instructions">
			<info-title data="下单须知" />
			<view v-if="!isEditable">
				<view :class="['text-area-content', 'part', { all: showMoreNotice }]">{{ storeInfo.orderInstructions }}</view>
				<view class="show-more-info" v-if="!showMoreNotice && storeInfo.orderInstructions" @tap="showAll('showMoreNotice')">
					<text>展示完整</text>
					<link-icon icon="icon-down1" />
				</view>
			</view>
			<view v-else class="text-area-box">
				<link-textarea
					class="text-area-input-box"
					:placeholder="'请输入下单须知信息...'"
					mode="textarea"
					v-model="storeInfo.orderInstructions"
					:nativeProps="{ maxlength: 1000 }"
					placeholder-style="color: #BFBFBF;" />
				<text class="text-area-count">{{ storeInfo.orderInstructions.length }} / {{ 1000 }}</text>
			</view>
		</view>
		<link-button v-if="isEditable" class="bottom-button" size="mini" @tap="saveBaseInfo">保存</link-button>

		<!-- 套餐 -->
		<view class="catering-terminal-taocan">
			<info-title data="套餐餐标" :show-bg="true" />
			<!-- 套餐列表 查看 -->
			<view v-if="!isEditableMeal" class="catering-terminal-taocan-list">
				<view
					class="cagtering-ordering-taocan-item view-taocan-item"
					v-for="(meal, index) in mealLabels"
					:key="index"
					@tap="showMealInfo(meal)">
					<view class="taocan-item-title">套餐{{ index + 1 }}</view>
					<view class="taocan-item-content">
						<image @tap="previewImage(meal.mealLabelImgs, 0)" class="taocan-item-content-thumb-img" :src="meal.mealLabelImgs[0]" />
						<view class="taocan-item-content-name">{{ meal.mealLabelName }}</view>
						<view class="taocan-item-content-chakan" @tap="checkMeal(meal, index)">
							<text>查看</text>
							<link-icon icon="mp-arrow-right" />
						</view>
					</view>
				</view>
			</view>

			<!-- 套餐列表 编辑 新增 -->
			<view v-else class="catering-terminal-taocan-list">
				<link-swipe-action v-for="(item, index) in mealLabels" :key="index">
					<link-swipe-option slot="option" @tap="handleMealItemDelete(index)">删除 </link-swipe-option>
					<view class="cagtering-ordering-taocan-item">
						<view class="taocan-item-title">套餐{{ index + 1 }}</view>
						<view class="text-area-box">
							<link-textarea
								class="text-area-input-box"
								:placeholder="'请输入套餐信息...'"
								mode="textarea"
								v-model="item.mealLabelName"
								:nativeProps="{ maxlength: 100 }"
								placeholder-style="color: #BFBFBF;" />
							<text class="text-area-count">{{ (item.mealLabelName && item.mealLabelName.length) || 0 }} / {{ 100 }}</text>
						</view>
						<view class="env-img-box">
							<lnk-img
								:delFlag="!isOnApproving"
								:newFlag="!isOnApproving"
								:module-type="moduleType"
								:parent-id="item.id"
								:isCount="true"
								:count="3"
								:width="imgDeviceWidth"
								:height="imgDeviceWidth"
								@imgUploadSuccess="imgUploadSuccess($event, index)"
								@imgDeleteSuccess="imgDeleteSuccess"></lnk-img>
						</view>
					</view>
				</link-swipe-action>
				<view class="catering-terminal-taocan-add" @tap="addMeal" v-if="!isOnApproving">
					<link-icon icon="mp-plus" />
					<text>新增</text>
				</view>
			</view>
		</view>
		<view v-if="isEditableMeal && !isOnApproving" class="btn-box">
			<link-button class="save-button" size="mini" @tap="cancel">取消</link-button>
			<link-button class="save-button" mode="stroke" size="mini" @tap="save">提交</link-button>
		</view>
		<!-- 编辑 -->
		<link-button v-if="!isEditable && !isEditableMeal" class="bottom-button" size="mini" @tap="editHandle">编辑</link-button>

		<link-dialog ref="meal" position="bottom" :initial="true" :model="true">
			<view class="model-title">
				<view class="iconfont icon-left" @tap="closeDialog"></view>
				<view class="title" style="padding-left: 0">套餐{{ mealIndex + 1 }}</view>
				<view class="iconfont icon-close1" @tap="closeDialog"></view>
			</view>
			<view class="meal-info-box">
				<view class="meal-info-title"> 套餐图片 </view>
				<view class="meal-info-imgs">
					<view
						class="meal-info-img-item"
						v-for="(item, index) in meal.mealLabelImgs"
						:key="index"
						@tap="previewImage(meal.mealLabelImgs, index)">
						<image :src="item" />
					</view>
				</view>
				<view class="meal-info-title">套餐名称</view>
				<view class="meal-info-name">{{ meal.mealLabelName }}</view>
			</view>
			<view class="dailog-button-bottom">
				<link-button size="mini" @tap="closeDialog">确认</link-button>
			</view>
		</link-dialog>
	</link-page>
</template>

<script>
	import TerminalInfoCard from '../components/terminal-info-card.vue';
	import InfoTitle from '../components/info-title.vue';
	import LnkImg from '../../core/lnk-img/lnk-img.vue';

	definePageConfig({
		navigationBarTitleText: '餐饮终端订餐信息维护',
	});
	export default {
		name: 'catering-terminal-detail-page',
		components: { TerminalInfoCard, InfoTitle, LnkImg },
		data() {
			return {
				data: {},
				storeInfo: {
					id: '',
					storeDescription: '',
					orderInstructions: '',
				},
				isEditable: false, //编辑门店信息
				isEditableMeal: false, //编辑套餐信息
				showMoreDes: false,
				imgList: [],
				contentHeight: 272,
				maxlength: 1000,
				imgDesignWidth: 196,
				imgDeviceWidth: 103,
				showMoreNotice: false,
				mealLabels: [], //当前套餐
				moduleType: 'catering-terminal',
				checkMealInfo: false,
				meal: {},
				mealIndex: 0,
				initOssInfo: {}, //餐标初始化
				isOnApproving: false, //是否在审核中
				hasOauth: false,
				initIds: [], //初始化套餐id：{}对应套餐 用于提交时删除
				deleteMealLabels:[] //删除套餐
			};
		},
		watch: {
			'storeInfo.storeDescription'(val) {
				if (!this.$utils.isEmpty(val) && val.length > this.maxlength) {
					this.storeInfo.storeDescription = val.substring(0, this.maxlength);
				}
			},
			'storeInfo.orderInstructions'(val) {
				if (!this.$utils.isEmpty(val) && val.length > this.maxlength) {
					this.storeInfo.orderInstructions = val.substring(0, this.maxlength);
				}
			},
		},
		async created() {
			// 获取终端信息
			this.data = this.pageParam.data;
			// 获取门店信息
			await this.getStoreInfo();
			// 获取餐标图片访问地址
			// await this.getMealImgUrl();
			// 图片尺寸
			this.$taro.getSystemInfo({
				success: (system) => {
					let { screenWidth, pixelRatio } = system;
					console.log(screenWidth, pixelRatio);
					let ratio = 750 / screenWidth;
					this.imgDeviceWidth = this.imgDesignWidth / ratio;
					console.log(ratio, this.imgDeviceWidth);
				},
			});
			// 餐标维护只有业务代表及业务主管可进行操作
			// 241011: 删除权限控制
			// const userInfo = this.$taro.getStorageSync('token').result;
			// this.hasOauth = ['SalesSupervisor', 'Salesman'].includes(userInfo.positionType);
		},
		methods: {
			/**
			 * @description: 查看套餐
			 * @return {*}
			 * @Author: <EMAIL>
			 * @Date: 2024-07-27 16:11:23
			 */
			checkMeal(meal, index) {
				this.meal = meal;
				this.mealIndex = index;
				this.$refs.meal.show();
			},
			closeDialog() {
				this.$refs.meal.hide();
			},
			/**
			 * @description: 显示全部
			 * @return {*}
			 * @Author: <EMAIL>
			 * @Date: 2024-07-25 14:42:30
			 */
			showAll(key) {
				this[key] = true;
			},
			/**
			 * @description:    门店环境 图片上传成功
			 * @return {*}
			 * @Author: <EMAIL>
			 * @Date: 2024-07-25 11:52:39
			 */
			imgUploadSuccessShop(imgList) {
				console.log('添加成功 imgUploadSuccessShop', imgList);
			},
			/**
			 * @description: 门店环境 图片删除成功
			 * @return {*}
			 * @Author: <EMAIL>
			 * @Date: 2024-07-25 11:52:39
			 */
			imgDeleteSuccessShop(imgList) {
				console.log('删除成功 imgUploadSuccessShop', imgList);
			},
			/**
			 * @description: 获取腾讯云图片访问地址
			 * @return {*}
			 * @Author: <EMAIL>
			 * @Date: 2024-07-26 14:15:22
			 */
			getImgUrl() {
				(this.mealLabels || []).forEach(async (item) => {
					item.mealLabelImgs = [];
					(item.images || []).forEach(async (imageObj) => {
						let imgUrl = this.$image.getSignedUrl(imageObj.attachmentPath);
						item.mealLabelImgs.push(imgUrl);
						// 获取餐标审核状态
						if (item.approvalStatus === 'Approving') {
							this.isOnApproving = true;
						}
					});
				});
				console.log('getStoreInfo taocan', this.mealLabels);
			},
			/**
			 * @description: 查看套餐图片
			 * @return {*}
			 * @Author: <EMAIL>
			 * @Date: 2024-07-26 15:10:46
			 */
			previewImage(imgs, index) {
				wx.previewImage({
					current: imgs[index], // 当前显示图片的http链接
					urls: imgs, // 需要预览的图片http链接列表
				});
			},
			/**
			 * @description: 套餐餐标图片上传
			 * @return {*}
			 * @Author: <EMAIL>
			 * @Date: 2024-07-27 12:14:31
			 */
			imgUploadSuccess(imgList, index) {
				console.log('上传==========', imgList, index);
				this.mealLabels[index].mealLabelImgs = imgList.map((item) => {
					return this.$image.getSignedUrl(item.attachmentPath);
				});
				console.log('添加成功', this.mealLabels);
			},
			/**
			 * @description: 套餐餐标图片删除成功
			 * @return {*}
			 * @Author: <EMAIL>
			 * @Date: 2024-07-25 11:52:39
			 */
			imgDeleteSuccess(imgList) {
				imgList.forEach((item) => {
					this.mealLabels.forEach((meal) => {
						if (meal.id == item.headId) {
							meal.mealLabelImgs = [];
							let imgUrl = this.$image.getSignedUrl(item.attachmentPath);
							meal.mealLabelImgs.push(imgUrl);
						}
					});
				});
				console.log('删除成功', imgList, this.mealLabels);
			},
			/**
			 * @description: 添加套餐
			 * @return {*}
			 * @Author: <EMAIL>
			 * @Date: 2024-07-26 17:31:32
			 */
			async addMeal() {
				let id = await this.$newId();
				const mealObj = {
					id,
					mealLabelName: '',
				};
				this.mealLabels.push(mealObj);
				console.log('添加套餐', id, this.mealLabels);
			},
			/**
			 * @description: 餐饮套餐保存
			 * @return {*}
			 * @Author: <EMAIL>
			 * @Date: 2024-07-26 17:43:10
			 */
			async save() {
				try {
					// 保存基本信息
					if (this.isEditable) {
						let isFinish = await this.saveBaseInfo();
						if (!isFinish) return;
					}

					let { id: acctId, acctCode, acctName } = this.data;
					let { id } = this.storeInfo;
					// 验证套餐必填项
					// if (this.$utils.isEmpty(this.mealLabels)) {
					// 	this.$message.warn('请填写套餐餐标及上传照片');
					// 	return;
					// }
					let flag = true;
					this.mealLabels.forEach((item) => {
						if (this.$utils.isEmpty(item.mealLabelName) || this.$utils.isEmpty(item.mealLabelImgs)) {
							this.$message.warn('请填写套餐餐标及上传照片');
							flag = false;
							return;
						}
					});
					if (!flag) return;
					// 提交套餐信息
					let params = {
						id: id, //门店 ID
						acctId: acctId, //终端id
						acctCode: acctCode, //终端编码
						acctName: acctName, // 终端名称
						mealLabels: this.mealLabels, //提交的套餐
						deleteMealLabels:this.deleteMealLabels //删除的套餐
					};
					this.$utils.showLoading('提交中...');
					let res = await this.$http.post('action/link/accntStore/mealLabels/submit', params);
					if (res.success && res.code == 200) {
						this.isEditableMeal = false;
						this.$message.success('提交成功');
						this.getStoreInfo();
					} else {
						this.$message.warn(res.result);
					}
				} catch (error) {
				} finally {
					this.$utils.hideLoading();
				}
			},
			/**
			 * @description: 终端门店信息及套餐餐标
			 * @return {*} storeInfo
			 * @return {*} mealLabels
			 * @Author: <EMAIL>
			 * @Date: 2024-07-26 18:26:42
			 */
			async getStoreInfo() {
				try {
					let { id } = this.data;
					let res = await this.$http.get(`action/link/accntStore/get/by/acctId?acctId=${id}`);
					if (!this.$utils.isEmpty(res.data)) {
						// 门店信息
						this.storeInfo = res.data;
						// 套餐信息
						this.mealLabels = res.data.mealLabels;
						this.initIds = this.mealLabels.reduce((a, b) => {
							a.push(b.id)
							return a
						}, [])
						this.deleteMealLabels = []
						// 套餐餐标图片查看
						this.getImgUrl();
					} else {
						let storeId = await this.$newId();
						this.storeInfo = {
							id: storeId,
							storeDescription: '',
							orderInstructions: '',
						};
						this.mealLabels = [];
					}
				} catch (error) {
					console.log('error :>> ', error);
				}
			},
			/**
			 * 获取图片key
			 */
			async getImgKeyList(key) {
				return new Promise(async (resolve, reject) => {
                    if (this.$utils.isEmpty(key.id)) {
                        reject(false);
                    }
					this.$utils.showLoading();
					let data = await this.$http.post('action/link/attachment/queryByExamplePage', {
						uploadType: 'cos',
						pageFlag: false,
						sort: 'created',
						order: 'desc',
						moduleType: this.moduleType, // 所属模块
						headId: key.id,
						queryFields: 'id,uploadType,attachmentPath,moduleType,headId,dataSource,created,smallurl',
					});
					this.$utils.hideLoading();
					if (data.success) {
						this.$utils.hideLoading();
						resolve({ [key.id]: data.rows });
					}
				});
			},
			// 获取餐标图片
			getMealImgUrl() {
				Promise.all(
					this.mealLabels.map((item) => {
						return this.getImgKeyList(item);
					})
				).then((res) => {
					console.log('res', res);
					this.initOssInfo = Object.assign({}, ...res);
				});
			},
			/**
			 * @description: 查看套餐
			 * @return {*}
			 * @Author: <EMAIL>
			 * @Date: 2024-07-27 13:15:08
			 */
			showMealInfo(meal) {
				console.log(meal);
			},
			handleMealItemDelete(index) {
				let meal = this.mealLabels[index]
				if (this.initIds.includes(meal.id)) {
					// 远程删除
					this.deleteMealLabels.push(meal)
				}
				this.mealLabels.splice(index, 1);
			},
			/**
			 * @description: 编辑
			 * @author: 邓佳柳
			 * @Date: 2024-09-06 14:57:37
			 */
			editHandle() {
				this.isEditable = true;
				this.isEditableMeal = true;
				if (this.isOnApproving) {
					this.$message.warn('您的套餐正在审核中...');
				}
			},
			async saveBaseInfo() {
				let { id: acctId, acctCode, acctName } = this.data;
				let { id, storeDescription, orderInstructions } = this.storeInfo;

				if (this.$utils.isEmpty(storeDescription)) {
					this.$message.warn('请输入门店介绍信息');
					return false;
				}
				if (this.$utils.isEmpty(orderInstructions)) {
					this.$message.warn('请输入下单须知信息');
					return false;
				}
				let params = { id, acctId, acctCode, acctName, storeDescription, orderInstructions };
				let res = await this.$http.post('/action/link/accntStore/save', params);
				if (res.success && res.code == 200) {
					this.isEditable = false;
					return true;
				} else {
					this.$message.warn(res.result);
					return false;
				}
			},
			/**
			 * @description:
			 * @author: 邓佳柳
			 * @Date: 2024-09-06 15:46:48
			 */
			async cancel() {
				this.$utils.showLoading('取消中...');
				await this.getStoreInfo();
				this.$utils.hideLoading();
				this.isEditable = false;
				this.isEditableMeal = false;
			},
		},
	};
</script>

<style lang="scss">
	.catering-terminal-detail-page {
		background: #f8f8f8;
		padding: 24px;
		width: 100vw;
		box-sizing: border-box;
		.catering-terminal-store-description {
			margin-top: 24px;
			background-color: #fff;
			border-radius: 16px 16px 0px 0px;
			padding-bottom: 24px;
			overflow: hidden;
		}
		.catering-terminal-env {
			padding-bottom: 40px;
			background-color: #fff;
			min-height: 310px;
		}

		.show-more-info {
			font-size: 28px;
			color: #9ea1ae;
			line-height: 40px;
			padding: 8px 0;
			@include flex;
			align-items: center;
			justify-content: center;
		}
		.catering-terminal-order-instructions {
			padding-bottom: 24px;
			margin-bottom: 24px;
			background-color: #fff;
		}
		.catering-terminal-taocan {
			background-color: #fff;
			min-height: 48px;
			.catering-terminal-taocan-list {
				overflow: hidden;
				min-height: 300px;
				.link-swipe-action {
					width: 100%;
				}
				.cagtering-ordering-taocan-item {
					padding-bottom: 22px;
					border-top: 2px solid rgba(242, 242, 242, 0.7);
					&:first-of-type {
						border-top: 0;
					}
					&.view-taocan-item {
						overflow: hidden;
						width: 654px;
						height: 204px;
						background: #f3f7ff;
						border-radius: 12px;
						margin: 24rpx auto 16px;
					}
					.taocan-item-title {
						font-size: 28px;
						color: #6a6d75;
						line-height: 40px;
						margin-left: 24px;
						margin-top: 16px;
					}
					.text-area-input-box {
						// height: 120px;
					}
					.env-img-box {
						margin-top: 24px;
					}
					.taocan-item-content {
						@include flex;
						align-items: center;
						font-size: 28px;
						color: #333333;
						line-height: 40px;
						.taocan-item-content-thumb-img {
							width: 124px;
							height: 124px;
							margin: 16px;
							border-radius: 16px;
						}
						.taocan-item-content-name {
							flex: 1;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 3;
							overflow: hidden;
							text-overflow: ellipsis;
						}
						.taocan-item-content-chakan {
							color: #9ea1ae;
							margin-right: 24px;
						}
					}
				}
				.catering-terminal-taocan-add {
					font-weight: 500;
					font-size: 28px;
					color: #3f66ef;
					line-height: 48px;
					@include flex;
					align-items: center;
					justify-content: center;
					padding-top: 10px;
					padding-bottom: 32px;
				}
			}
		}
        .btn-box{
            display: flex;
            align-items: center;
            justify-content: space-around;
        }
		.save-button {
			width: 260px;
			margin: 48px;
		}
		.bottom-button {
			width: 702px;
			margin: 24px auto 48px;
		}
		.env-img-box {
			/* deep */
			.lnk-img {
				/* deep */
				.lnk-img-item {
					margin-left: 24px;
				}
			}
		}
		// textarea
		.text-area-box {
			position: relative;
			padding-bottom: 16px;
			.text-area-input-box {
				padding: 0;
				border: 0;
				/*deep*/
				.link-textarea-content {
					border: 0;
				}
			}
			.text-area-count {
				position: absolute;
				right: 24px;
				bottom: 0;
				color: #9ea1ae;
				font-size: 20px;
			}
		}
		.text-area-content {
			font-size: 28px;
			color: #333333;
			line-height: 40px;
			padding: 8px 24px;
			margin-bottom: 16px;
			box-sizing: border-box;
			&.part {
				height: 254px;
				overflow: hidden;
			}

			&.all {
				height: auto;
				overflow: auto;
			}
		}
		.model-title {
			@include flex;
			align-items: center;
			justify-content: space-between;
			.title {
				font-family: PingFangSC-Regular, serif;
				font-size: 32px;
				color: #262626;
				letter-spacing: 0;
				text-align: center;
				line-height: 96px;
				height: 96px;
				padding-left: 0 !important;
			}
			.icon-left {
				color: #bfbfbf;
				font-size: 32px;
			}
			.icon-close1 {
				color: #bfbfbf;
				font-size: 32px;
			}
		}
		.meal-info-box {
			height: 550px;
			.meal-info-title {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 28px;
				color: #333333;
				line-height: 40px;
				margin-bottom: 16px;
			}
			.meal-info-imgs {
				@include flex;
				align-items: center;
				flex-wrap: wrap;
			}
			.meal-info-img-item {
				width: 160px;
				height: 160px;
				border-radius: 16px;
				overflow: hidden;
				margin-right: 24px;
				margin-bottom: 40px;
				image {
					width: 100%;
					height: 100%;
				}
			}
			.meal-info-name {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 28px;
				color: #333333;
				line-height: 40px;
			}
		}
		.dailog-button-bottom {
			padding: 16px;
			border-top: 1px solid #f0f0f0;
			button {
				width: 100%;
			}
		}
	}
</style>
