<!--
总部活动-宴席活动-外部人员执行反馈
<AUTHOR>
@date 2023-07-31
@file banquet-feedback-share-page.vue
-->
<template>
    <link-page class="banquet-feedback-share-page">
        <image v-if="!startFlag" class="bottom-bg" :src="$imageAssets.banquetForwardImage"></image>
        <template v-else>
            <!--执行反馈-->
            <banquet-feedback-edit
                :banquetItem="banquetItem"
                ref="getFormData"
                id="feedback"
                :banquetPolicy="banquetPolicy"
                :shareFlag="shareFlag"
            ></banquet-feedback-edit>
            <link-sticky>
                <link-button v-if="startFlag" block @tap="save">保存</link-button>
            </link-sticky>
        </template>
        <!-- 获取用户信息提示 -->
        <link-dialog ref="getInfoTips">
            <view slot="head">
                微信授权
            </view>
            <view>
                我们需要获取您授权的用户信息
            </view>
            <button slot="foot" class="refuse-button" @tap="refuseUserInfo" style="width:50%">拒绝</button>
            <button slot="foot" class="allow-button" open-type="getUserInfo" @getuserinfo="bindGetUserInfo"
                    style="width:50%">允许
            </button>
        </link-dialog>

        <!-- 获取用户手机号提示 -->
        <link-dialog ref="getMobileTips">
            <view slot="head">
                微信授权
            </view>
            <view>
                我们还需要获取您的手机号信息
            </view>
            <button slot="foot" class="refuse-button" @tap="refuseMobile" style="width:50%">拒绝</button>
            <button slot="foot" class="allow-button" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber"
                    style="width:50%">允许
            </button>
        </link-dialog>
    </link-page>
</template>

<script>
import banquetFeedbackEdit from './components/banquet-feedback-edit.vue';
import Taro from "@tarojs/taro";
import lov from "../../store/modules/lov";
import store from "../../store/store";
import {env} from "../../../env";
import {DeviceService} from "link-taro-component";

export default {
    name: 'banquet-feedback-share-page',
    components: {banquetFeedbackEdit},
    data() {
        return {
            banquetPolicy: {}, // 宴席政策
            banquetItem: {},   // 宴席信息
            canIUseGetUserProfile: false, // 新获取微信用户信息方法API是否可用
            shareFlag: true, // 是否为外部分享 默认为false，可编辑；为true时为外部分享，不可编辑
            // decodedBanquetItem: {}, // 解码后的宴席信息
            banquetId: '', // 宴席id
            openId: '', // 微信用户的openId
            unionId: '', // 用户的unionId
            wxMobile: '',   // 微信用户手机号
            wxUserInfo: {},  // 微信用户信息
            startFlag: false, // 是否开始执行反馈，通过号码校验为true
            creatorCode: '' //转发人员工工号
        }
    },
    async created() {
        // 获取微信用户登录code
        const code = await this.getCode();
        console.log('code', code);
        Taro.setStorageSync('code', code);
        // 获取系统用户token
        const token = Taro.getStorageSync('token').token;
        console.log('token', token);
        if (token) {
            this.authorization = `bearer ${token}`;
        }
        // -------------------
        // 判断是否可使用【getUserProfile】方法
        if (wx.getUserProfile) {
            this.canIUseGetUserProfile = true;
        }
    },
    async onLoad(options) {
        // 隐藏返回首页按钮
        wx.hideHomeButton();
        console.log('options.banquetId', options.banquetId);
        this.banquetId = options.banquetId;
        this.creatorCode  = options.creatorCode ;
        this.$refs.getInfoTips.show();
    },
    // 监听this.wxMobile的值，当有值且不为空值时，执行await this.userValidate();
    watch: {
        wxMobile: {
            handler: async function (val, oldVal) {
                if (!!val) {
                    await this.userValidate();
                    await this.initAct();
                }
            },
            deep: true
        },
    },
    methods: {
        /**
         * 获取宴席政策
         * <AUTHOR>
         * @date	2023/12/28 14:19
         */
        async queryBanquetPolicy() {
            try {
                const {success, result} = await this.$http.post('action/link/headquarterActivity/queryById', {
                    id: this.banquetItem.activityId
                });
                if (success) {
                    this.banquetPolicy = result;
                }
            } catch (e) {

            }
        },
        /**
         *  @desc 用户信息校验
         *  <AUTHOR>
         *  @date 2023-08-02
         **/
        async userValidate() {
            try {
                const data = await this.$http.post('action/link/forwardContact/checkPhoneNum', {
                    headId: this.banquetId,
                    phoneNum: this.wxMobile,
                    actType: 'Banquet',
                    openId: this.openId,
                    unionId: this.unionId,
                     userCode: this.creatorCode,
                }, {noToken: true})
                if (data.success) {
                    const token = data.token
                    const expires = Date.now() + 1000 * 60 * 60 * 2
                    store.commit('user/setToken', token);
                    store.commit('user/setTokenTime', expires);
                    const userInfo = await this.getUserCtx();
                    userInfo.postnName = '外部联系人';
                    userInfo.firstName = this.wxMobile;
                    userInfo.userName = this.wxMobile;
                    const ret = {
                        expires,
                        result: userInfo,
                        token,
                    }
                    const loginInfo = {
                        ...ret,
                        ...env,
                        loginEnv: DeviceService.systemInfo['environment'],
                        crmcdntest: store.getters['httpsEnv/getHttpsEnv'],
                    }
                    store.commit('user/setUser', userInfo);
                    store.commit('user/setToken', token);
                    store.commit('user/setTokenTime', Date.now() + 1000 * 60 * 60 * 2);
                    Taro.setStorageSync('token', loginInfo);

                    this.startFlag = true;
                    console.log('验证之后this.startFlag', this.startFlag);
                    console.log('验证之后this.wxMobile', this.wxMobile);
                } else {
                    this.$message.warn('当前用户未进行联系人授权，不可进行执行反馈！');
                    return;
                }
            } catch (e) {
                console.log(e);
            }
        },
        /**
         *  @desc 获取用户信息
         *  <AUTHOR>
         *  @date 2023-08-09
         **/
        async getUserCtx() {
            try {
                const data = await this.$http.post('/login/base/user/getUserCtx')
                if (data.success || !data.result) {
                    return data.result;
                } else {
                    throw new Error(data)
                }
            } catch (e) {
                this.$message.warn("获取登录人信息失败，请稍后重试！" + JSON.stringify(e));
                throw e;
            }
        },
        /**
         *  @desc 初始化用户信息
         *  <AUTHOR>
         *  @date 2023-07-31
         **/
        async initAct() {
            try {
                const data = await this.$http.post('action/link/headquarterFeedback/queryById', {
                    id: this.banquetId
                })
                if (data.success) {
                    this.banquetItem = data.result;
                    await this.queryBanquetPolicy();
                } else {
                    this.$message.warn("查询该活动失败，请稍后重试！");
                }
            } catch (e) {
                this.$message.warn("查询该活动失败，请稍后重试！！");
            }
        },
        /**
         * 获取微信用户登录的code
         * <AUTHOR>
         * @data 2021-6-30
         */
        async getCode() {
            return new Promise((resolve) => {
                wx.login({
                    success: function (res) {
                        if (res.code) {
                            resolve(res.code);
                        }
                    }
                });
            })
        },
        /**
         * refuseUserInfo
         * <AUTHOR>
         * @data 2021-6-29
         * 用户拒绝授权用户信息
         */
        refuseUserInfo() {
            this.$refs.getInfoTips.hide();
            this.$message.primary('您拒绝授权用户信息，将无法进行执行反馈！');
        },
        /**
         * refuseMobile
         * <AUTHOR>
         * @data 2021-6-29
         * 用户拒绝授权手机号
         */
        refuseMobile() {
            this.$refs.getMobileTips.hide();
            this.$message.primary('您拒绝授权手机号，将无法进行执行反馈！');
        },
        /**
         * 获取用户授权信息
         * <AUTHOR>
         * @data 2021-6-24
         */
        async bindGetUserInfo(e) {
            console.log('得到用户授权', e)
            this.$refs.getInfoTips.hide();
            // 用户同意授权用户信息
            if (e.detail.encryptedData) {
                // 解密用户openId
                this.openId = await this.decodeWxUserInfo(e.detail.encryptedData, e.detail.iv);
            }
            if (e.detail.userInfo) {
                this.wxUserInfo = e.detail.userInfo;
            }
            // 继续询问授权
            this.queryAuthSetting();
        },
        /**
         * 获取用户授权信息-新API
         * <AUTHOR>
         * @date 7/27/21
         */
        getUserProfile() {
            this.$refs.getInfoTips.hide();
            wx.getUserProfile({
                desc: '用于匹配是否为执行反馈联系人', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
                success: async (res) => {
                    // 用户同意授权用户信息
                    if (res.encryptedData) {
                        // 解密用户openId
                        this.openId = await this.decodeWxUserInfo(res.encryptedData, res.iv);
                    }
                    if (res.userInfo) {
                        this.wxUserInfo = res.userInfo;
                    }
                    // 继续询问授权
                    this.queryAuthSetting();
                }
            });
        },

        /**
         * 获取用户手机号码
         * <AUTHOR>
         * @data 2021-6-28
         */
        async getPhoneNumber(e) {
            this.$refs.getMobileTips.hide();
            // 用户确认授权
            if (e.detail.encryptedData) {
                // 解密用户手机号
                this.wxMobile = await this.decodeWxUserMobile(e.detail.encryptedData, e.detail.iv);
                // 继续询问授权
                if (!this.wxMobile) {
                    this.queryAuthSetting();
                }
            } else {
                this.$message.primary('您拒绝授权手机号，将无法添加通讯录或注册会员');
            }
        },
        /**
         * 解密用户授权账号信息，返回用户唯一标识openId
         * <AUTHOR>
         * @date 7/27/21
         */
        async decodeWxUserInfo(encryptedData, iv) {
            const code = await this.getCode();
            const data = await this.$http.post('action/link/eleBusinessCard/decodeWxUserInfo', {
                encryptData: encryptedData,
                iv: iv,
                code
                // code: this.$taro.getStorageSync('code')
            }, {
                noToken: true,
                noEncryptData: true,
                header: {Authorization: this.authorization}
            });
            if (data.success) {
                this.unionId = data.unionid
                return data.openid;
            } else {
                return '';
            }
        },
        /**
         * 解密用户授权手机信息，返回用户微信绑定手机号
         * <AUTHOR>
         * @date 7/27/21
         */
        async decodeWxUserMobile(encryptedData, iv) {
            console.log('encryptedData', encryptedData);
            console.log('iv', iv);
            console.log('code', this.$taro.getStorageSync('code'));
            const code = await this.getCode();
            const data = await this.$http.post('action/link/eleBusinessCard/decodeWxUserMobile', {
                encryptData: encryptedData,
                iv: iv,
                code
                // code: this.$taro.getStorageSync('code')
            }, {
                noToken: true,
                noEncryptData: true,
                header: {Authorization: this.authorization}
            });
            if (data.success) {
                return data.openid;
            } else {
                return '';
            }
        },
        /**
         * 询问用户授权情况：用户微信账号与绑定手机号信息
         * <AUTHOR>
         * @data 2021-6-24
         */
        async queryAuthSetting() {
            // 用户信息未授权
            if (JSON.stringify(this.wxUserInfo) === '{}') {
                if (this.canIUseGetUserProfile) {
                    // this.$refs.getInfoTips.hide();
                    this.getUserProfile();
                } else {
                    this.$refs.getInfoTips.show();
                }
                return;
            }
            // 手机号未授权
            if (this.$utils.isEmpty(this.wxMobile)) {
                this.$refs.getMobileTips.show();
                return;
            }
            // 用户信息与手机号授权成功后增加有效的浏览次数
            // 第一次访问此页面，授权成功后会执行一次，页面缓存用户信息与手机号之后不会再次需要授权
            // await this.$http.post('action/link/eleCardBrowsingRecord/generateBrowsingRecord', {
            //     // wechatId: this.openId,
            //     // headId: this.cardData.id,
            //     // id: this.invalidId,
            //     wechatName: this.wxUserInfo.nickName,
            //     wechatPhoto: this.wxUserInfo.avatarUrl,
            //     mobile: this.wxMobile
            // }, {
            // });
        },
        /**
         *  @desc 保存
         *  <AUTHOR>
         *  @date 2023-07-31
         **/
        async save() {
            try {
                const actTableNum = await this.$refs.getFormData.formData.actTableNum;
                if (this.banquetPolicy.scenePicture !== 'N') {
                    await this.$refs.getFormData.getUploadPhotoInfo();
                }
                const data = await this.$http.post('action/link/headquarterFeedback/update', {
                    id: this.banquetId,
                    actTableNum: actTableNum,
                    updateFields: "actTableNum,rowVersion",
                });
                if (data.success) {
                    this.$message.success('保存成功！');
                    setTimeout(async () => {
                        this.$message.success('反馈已完成！');
                        this.startFlag = false;
                    }, 1000);
                } else {
                    this.$message.warn('保存出错！');
                }
            } catch (e) {
                console.log(e);
            }
        }
    }
}
</script>

<style lang="scss">
.banquet-feedback-share-page {
    .bottom-bg {
        width: 100%;
        height: 100vh;
    }

    .refuse-button {
        background: #fff;
        width: 50%;
        font-size: 28px;
        border-top: 1px solid #eff1f3 !important;
        border-right: 1px solid #eff1f3 !important;
        color: #333;
        border-radius: 0;
    }

    .allow-button {
        background: #fff;
        width: 50%;
        font-size: 28px;
        border-top: 1px solid #eff1f3 !important;
        color: #32CD32;
        border-radius: 0;
    }

    .allow-button::after,
    .refuse-button::after {
        border-radius: 0 !important;
        border: 0;
    }
}
</style>
