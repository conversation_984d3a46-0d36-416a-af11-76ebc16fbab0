<!--
总部活动-投放终端列表
<AUTHOR>
@date 2023-03-31
-->
<template>
    <link-page class="delivery-terminal-list-page">
        <link-auto-list :option="deliveryTerminalList" :searchInputBinding="{props:{placeholder:'终端名称'}}">
            <link-filter-group slot="filterGroup">
                <!--                <link-filter-item label="创建时间（升序）" :param="{sort:{field:'created',desc: false}}"/>-->
                <link-filter-item label="更新时间（升序）" :param="{sort:{field:'lastUpdated',desc: false}}"/>
            </link-filter-group>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="terminal-list-item" @tap="goToItem(data)">
                    <view class="terminal-list" slot="note">
                        <view class="list-cell">
                            <view class="media-list">
                                <!--左图片-->
                                <image class="media-list-logo" :src="data.storeUrl" @tap.stop="previewStoreUrl(data)"
                                       lazy-load="true"></image>
                                <!--右内容-->
                                <view class="store-content">
                                    <view class="store-content-top">
                                        <!--【客户一级分类】为“终端Terminal”的时候显示storeSigns字段-->
                                        <!--                                        <view class="store-title" v-if="data.acctType === 'Terminal'">{{data.acctName}}</view>-->
                                        <view class="store-title">{{ data.accountName }}</view>
                                        <!--                                        &lt;!&ndash;【客户一级分类】为“分销商Distributor”时展示billTitle字段&ndash;&gt;-->
                                        <!--                                        <view class="store-title" v-if="data.acctType === 'Distributor'">{{data.acctName || data.billTitle}}</view>-->
                                        <!--已认证-->
                                        <view class="store-level" v-if="data.accountStage === 'ykf'">
                                            <image :src="$imageAssets.storeStatusVerifiedImage"></image>
                                        </view>
                                        <!--未认证-->
                                        <view class="store-level" v-if="data.accountStage === 'xk'">
                                            <image :src="$imageAssets.storeStatusUnverifiedImage"></image>
                                        </view>
                                        <!--已失效-->
                                        <view class="store-level" v-if="data.accountStage === 'ysx'">
                                            <image :src="$imageAssets.storeStatusInvalidationImage"></image>
                                        </view>
                                        <!--潜客-->
                                        <view class="store-level" v-if="data.accountStage === 'dkf'">
                                            <image :src="$imageAssets.storeStatusPotentialImage"></image>
                                        </view>
                                    </view>
                                    <view class="store-content-middle">
                                        <view class="left">
                                            <view class="store-type" v-if="data.accountType">
                                                {{ data.accountType | lov('ACCT_TYPE') }}
                                            </view>
                                            <!--                                            <view class="store-type" v-if="data.acctCategory">{{data.acctCategory | lov('ACCNT_CATEGORY')}}</view>-->
                                            <view class="store-type" v-if="data.accountLevel">
                                                {{ data.accountLevel | lov('ACCT_LEVEL') }}
                                            </view>
                                            <!--                                            <view class="store-type" v-if="data.acctLevel !== undefined || data.capacityLevel !== undefined">-->
                                            <!--                                                <text v-if="data.acctLevel !== undefined">{{data.acctLevel | lov('ACCT_LEVEL')}}</text>-->
                                            <!--                                                <text v-if="data.capacityLevel !== undefined"> | {{data.capacityLevel | lov('CAPACITY_LEVEL')}}</text>-->
                                            <!--                                            </view>-->
                                            <view class="store-type" v-if="data.joinFlag">
                                                {{ data.joinFlag | lov('JOIN_FLAG') }}
                                            </view>
                                            <view class="store-type" v-if="data.judgmentFlag === 'Y'">品鉴</view>
                                            <view class="store-type" v-if="data.doorSigns !== undefined">
                                                {{ data.doorSigns | lov('DOOR_SIGNS') }}
                                            </view>
                                        </view>
                                    </view>
                                    <view class="store-content-representative">
                                        <view class="terminal-type">编码</view>
                                        <view class="terminal-name">{{ data.accountCode }}</view>
                                    </view>
                                    <!--                                    <view class="store-content-representative" v-if="(pageParam.source === 'advanceOrder' || pageParam.source === 'quotaTerminal'|| pageParam.source === 'quickEntryVisitTerminal') && !$utils.isEmpty(data.billTitle)">-->
                                    <!--                                        <view class="terminal-type" v-if="data.multiAcctMainFlag === 'Y'">主户头</view>-->
                                    <!--                                        <view class="terminal-type" v-if="data.multiAcctMainFlag === 'N'">子户头</view>-->
                                    <!--                                        <view class="terminal-name">{{data.billTitle}}</view>-->
                                    <!--                                    </view>-->
                                    <view class="store-content-representative">
                                        <view class="terminal-type">业代</view>
                                        <view class="terminal-name">{{ data.salesManListString }}</view>
                                    </view>
                                    <view class="store-content-address">
                                        <view class="store-address">
                                            {{ data.province }}{{ data.city }}{{ data.district }}{{ data.address }}
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <!--                            <view class="content-bottom" v-if="getValInArr(data.tagList).length > 0">-->
                            <!--                                <view class="view-port" :style="{'height': zhanKaiHash[index] ? 'auto' : '52rpx'}">-->
                            <!--                                    <view class="label">-->
                            <!--                                        <view-->
                            <!--                                            v-for="item in getValInArr(data.tagList)"-->
                            <!--                                            class="label-item"-->
                            <!--                                            :class="labelClassHah[item.type]"-->
                            <!--                                            :key="data.acctCode + item.tagValue">{{item.tagValue}}</view>-->
                            <!--                                    </view>-->
                            <!--                                    <view class="iconZhankai" @tap.stop="changeState(index)" v-if="data.tagList.length >= 3">-->
                            <!--                                        <link-icon icon="icon-shouqi" v-if="zhanKaiHash[index]"/>-->
                            <!--                                        <link-icon icon="icon-zhankai"v-else/>-->
                            <!--                                    </view>-->
                            <!--                                </view>-->
                            <!--                            </view>-->
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>

</template>

<script>
export default {
    name: 'delivery-terminal-list-page',
    data() {
        const positionType = this.$taro.getStorageSync('token').result.positionType   //获取当前用户职位信息
        const postnType1 = ['Salesman', 'SalesTeamLeader', 'CustServiceSpecialist'];
        const postnType2 = [
            'GroupBuyManager',
            'AccountManager',
            'RegionalManager',
            'CustServiceManager',
            'VipManager',
            'ChannelSupervisor',
            'CustServiceSupervisor',
            'BattleCommander',
            'SalesSupervisor',
            'BLRegionManager',
            'BPRegionManager',
            'SalesManager',
            'SalesChannelManger',
            'RInternalStaff',
            'InternalStaff',
            'CInternalStaff',
            'CityManager',
            'SalesAreaManager',
            'SalesRegionManager',
            'DeptSpecialist',
            'RegionManager',
            'DeptManager',
            'HRManager',
            'InternalMarketStaff',
            'FileMaintainer',
            'HRSpecialist',
            'SysAdmin',
            'SalesGeneralManager',
            'GeneralManager',
            'BrandManager'
        ];
        const deliveryTerminalList = new this.AutoList(this, {
            module: 'action/link/activityAccount',
            url: {
                queryByExamplePage: 'action/link/activityAccount/queryTerminalPage',
            },
            param: {
                tagListFlag: 'Y',  //是否需要终端标签字段
                filtersRaw: [
                    {id: 'accountType', property: 'accountType', value: 'Terminal', operator: '='},
                    {id: 'multiAcctMainFlag', property: 'multiAcctMainFlag', value: 'Y', operator: '='},
                    {id: 'dataSource', property: 'dataSource', value: 'WeChatWork', operator: '='},
                    {id: 'brandType', property: 'brandType', value: 'BranchCompany', operator: '='},
                    // {id: 'activityStatus', property: 'activityStatus', value: '[Processing, End, Closed]', operator: 'in'},
                ],
                oauth: '',
                multiAcctMainId: this.pageParam.multiAcctMainId
            },
            sortField: 'created',
            queryFields: 'id,companyId,freezeFlag,accountType,accountName,activityStatusList,billTitle,accountStage,accountCategory,accountLevel,joinFlag,accountCode,multiAcctMainFlag,salesManListString,province,city,district,address,storePicKey,storePicPreKey,judgmentFlag,doorSigns,stageName,stageStartTime,stageEndTime',
            // sortOptions: null,
            searchFields: ['accountName', 'accountCode'],
            filterOption: [
                {label: '推广阶段', field: 'stageName', type: 'lov', lov: 'PROMOTION_STAGE'},
                {label: '推广阶段开始时间', field: 'stageStartTime', type: 'date'},
                {label: '推广阶段结束时间', field: 'stageEndTime', type: 'date'}
            ],
            hooks: {
                afterLoad(data) {
                    data.rows.map(async (item) => {
                        if (!this.$utils.isEmpty(item.storePicPreKey)) {
                            let urlData = this.$image.getSignedUrl(item.storePicPreKey);
                            this.$set(item, 'storeUrl', urlData);
                        } else {
                            this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
                        }
                    })
                }

            }
        });
        return {
            deliveryTerminalList,   //投放终端列表
            positionType,           //当前登陆人职位类型
            postnType1,            //职位类型A
            postnType2,            //职位类型B，详情查看lzljqw-004-838
            oauth: null,
        }
    },
    created() {
        if (this.postnType1.includes(this.positionType)) {
            this.deliveryTerminalList.option.param.oauth = 'MULTI_POSTN';
            this.oauth = 'MY_POSTN';
        }
        if (this.postnType2.includes(this.positionType)) {
            this.deliveryTerminalList.option.param.oauth = 'MULTI_ORG';
            // this.oauth = 'MY_ORG';   // 23.07.24 取消查看终端下的登记活动安全性
        }
    },
    methods: {
        /**
         * 门头照片预览
         * <AUTHOR>
         * @date 2023-04-03
         * @param param
         */
        async previewStoreUrl(param) {
            if (!this.$utils.isEmpty(param.storePicKey)) {
                let imgUrl = this.$image.getSignedUrl(param.storePicKey);
                const inOptions = {
                    current: imgUrl,
                    urls: [imgUrl]
                };
                this.$image.previewImages(inOptions)
            } else {
                console.log(param.storeUrl);
                const inOptions = {
                    current: param.storeUrl,
                    urls: [param.storeUrl]
                };
                this.$image.previewImages(inOptions)
            }
        },
        /**
         * 跳转到查看详情
         * <AUTHOR>
         * @date 2023-04-03
         * @param data 详情信息
         */
        async goToItem(data) {
            this.$nav.push('/pages/headquarters-activity/delivery-terminal-detail-page.vue', {
                data: data,
                ...!!this.oauth ? {oauth: this.oauth} : {}
            })
        },
    }
}
</script>

<style lang="scss">
.delivery-terminal-list-page {
    .search-container {
        padding-left: 12px;
        color: #8C8C8C;
        font-size: 28px;
        text-align: center;
    }

    .terminal-list-item {
        background: #FFFFFF;
        width: 702px;
        margin: 24px auto auto auto;
        border-radius: 16px;
    }

    /*deep*/
    .link-item {
        padding: 0;
    }

    /*deep*/
    .link-item-icon {
        width: 0;
        padding-left: 0;
    }

    /*deep*/
    .link-dropdown-content {
        padding: 24px;
    }

    .terminal-list {
        .list-cell {
            .media-list {
                @include flex;
                padding: 24px 16px 24px 24px;

                .media-list-logo {
                    /*box-shadow: 0 4px 31px 0 rgba(0,44,152,0.22);*/
                    border-radius: 16px;
                    width: 128px;
                    height: 128px;
                    overflow: hidden;
                }

                .store-content {
                    width: 80%;

                    .store-content-top {
                        @include flex-start-center;
                        @include space-between;
                        margin-left: 24px;

                        .store-title {
                            font-family: PingFangSC-Semibold, serif;
                            font-size: 32px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 36px;
                            width: 77%;
                            height: 36px;
                            overflow: hidden;
                        }

                        .store-level {
                            margin-right: -3px;
                            width: 120px;
                            height: 44px;

                            image {
                                width: 100%;
                                height: 100%;
                            }
                        }
                    }

                    .store-content-middle {
                        display: flex;
                        justify-content: space-between;
                        padding-left: 32px;

                        .left, .right {
                            @include flex-start-center;
                            flex-wrap: wrap;
                            margin-top: 10px;

                            .store-type {
                                white-space: nowrap;
                                border: 2px solid #2F69F8;
                                border-radius: 8px;
                                font-size: 20px;
                                padding-left: 18px;
                                padding-right: 18px;
                                line-height: 40px;
                                height: 40px;
                                color: #2F69F8;
                                margin-right: 10px;
                                margin-top: 10px;
                            }
                        }
                    }

                    .store-content-representative {
                        @include flex;
                        margin-left: 24px;
                        margin-top: 20px;
                        width: calc(100% - 24px);
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;

                        .terminal-type {
                            color: #8C8C8C;
                            min-width: 50px;

                        }

                        .terminal-name {
                            font-family: PingFangSC-Regular, serif;
                            font-size: 24px;
                            color: #000000;
                            letter-spacing: 0;
                            padding-left: 8px;
                            width: calc(100% - 50px);
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                        }
                    }

                    .store-content-address {
                        margin-left: 24px;
                        margin-top: 18px;
                        font-family: PingFangSC-Regular, serif;
                        font-size: 24px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 32px;
                    }
                }
            }
        }

        .content-bottom {
            display: flex;
            justify-content: space-between;
            padding: 0 24px 10px;

            .view-port {
                position: relative;
                width: 100%;
                overflow: hidden;

                .label {
                    width: 90%;
                    display: flex;
                    flex-wrap: wrap;
                    position: relative;

                    .label-item {
                        padding: 4px 10px;
                        color: #2F69F8;
                        background-color: rgb(233, 242, 255);
                        margin: 6px 8px;
                        border-radius: 10px;
                        height: 34px;
                    }

                    .grey {
                        background: #ececec;
                        color: #3c3c3c;
                    }
                }

                .label::before {
                    position: absolute;
                    right: -12%;
                    bottom: 2px;
                    background-color: white;
                    content: '';
                    z-index: 2;
                    width: 12%;
                    height: 52px;
                }

                .iconZhankai {
                    width: 10%;
                    position: absolute;
                    top: 0;
                    right: 0;
                    height: 100%;
                    text-align: right;
                }
            }
        }
    }
}

</style>
