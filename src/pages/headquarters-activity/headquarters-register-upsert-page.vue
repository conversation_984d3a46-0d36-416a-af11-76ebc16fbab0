<!--
总部活动-登记列表-新增
<AUTHOR>
@date 2023-04-06
-->
<template>
    <link-page class="headquarters-register-upsert-page">
        <head-navigation-bar :backVisible="true"
                             :zIndex="zIndex"
                             :backgroundImg="$imageAssets.homeMenuBgImage"
                             :title="navigationBarTitle"
                             :titleColor="navigationBarTitleColor"
                             :navBackgroundColor="navBackgroundColor"
                             :udf="udfBack">
        </head-navigation-bar>
        <link-form :value="formData" :rules="rules" ref="upsertAct">
            <link-form-item v-if="headRegFlag" label="总部活动" required>
                <link-input v-model="formData.activityName" disabled/>
            </link-form-item>
            <link-form-item v-if="terminalFlag" label="总部活动" field="activityName" required>
                <link-object :option="headActOption"
                             :row="formData"
                             pageTitle="选择总部活动"
                             :map="{activityName: 'activityName'}"
                             :value="formData.activityName"
                             :disabled="isChooseAct"
                             :afterSelect="afterSelectHeadAct">
                    <template v-slot="{data}">
                        <item :title="data.activityName" :key="data.id" :data="data" :content="data.activityNum"/>
                    </template>
                </link-object>
            </link-form-item>
            <link-form-item v-if="headRegFlag" label="终端" field="accountName" required>
                <link-object :option="accountOption"
                             :row="formData"
                             pageTitle="选择终端"
                             :map="{
                                accountName: 'accountName',
                                accountId: 'id'
                             }"
                             :value="formData.accountName"
                             :afterSelect="afterSelectTerminal">
                    <template v-slot="{data}">
                        <item :title="data.accountName" :key="data.id" :data="data" :content="data.accountCode"/>
                    </template>
                </link-object>
            </link-form-item>
            <link-form-item v-if="terminalFlag" label="终端" required>
                <link-input v-model="formData.accountName" disabled/>
            </link-form-item>
            <link-form-item label="登记名称" field="feedbackName">
                <link-input :value="formData.feedbackName || '填写完基础信息自动生成'" disabled/>
            </link-form-item>
            <link-form-item label="登记人" field="creator">
                <link-input v-model="formData.creator" disabled/>
            </link-form-item>
            <link-form-item label="登记人职位" field="creatorPostnName">
                <link-input v-model="formData.creatorPostnName" disabled/>
            </link-form-item>
            <link-form-item label="登记人电话" field="creatorTel">
                <link-input v-model="formData.creatorTel" disabled/>
            </link-form-item>
            <!-- @make add by 谭少奇 2023/10/18 新增政策类型字段 -->
            <link-form-item label="政策类型" field="policyType" required>
                <link-lov type="MARKETSIX_POLICY_TYPE" v-model="formData.policyType"/>
            </link-form-item>
            <link-form-item label="活动日期" field="activityDate" required>
                <link-date view="YMD" v-model="formData.activityDate" value-format="YYYY-MM-DD"
                           display-format="YYYY-MM-DD"></link-date>
            </link-form-item>
            <link-form-item label="活动阶段" field="stageName">
                <link-lov type="PROMOTION_STAGE" v-model="formData.stageName || '选择活动日期后显示'" disabled/>
            </link-form-item>
            <!-- 240328迭代 去除活动时段 -->
            <!-- <link-form-item label="活动时段" field="feedbackStage" required>
                <link-lov type="HEAD_FEEDBACK_TIME" v-model="formData.feedbackStage"/>
            </link-form-item> -->
            <link-form-item label="包间号" field="privateRoom" required>
                <link-input v-model="formData.privateRoom" @blur="afterPrivateRoom($event)"/>
            </link-form-item>
            <link-form-item label="当日用餐包间数量" field="privateNumber" required>
                <link-number-keyboard v-model="formData.privateNumber" :min="1" :max="1000"/>
            </link-form-item>
            <link-form-item label="参与人数(人/场)" field="clientNumber" required>
                <!-- <link-input v-model="formData.clientNumber" type="number"></link-input> -->
                <link-number-keyboard  v-model="formData.clientNumber" :min="0" :max='99'/>
            </link-form-item>
            <!--            230525优化迭代，暂时去除-->
            <!--            <link-form-item label="是否有门槛" field="isDoorsill" required>-->
            <!--                <link-lov type="SPEND_ACT" v-model="formData.isDoorsill"/>-->
            <!--            </link-form-item>-->
            <!--            <link-form-item label="当日常规装库存(瓶)" required>-->
            <!--                <link-number-keyboard v-model="formData.productStock" type="number"/>-->
            <!--            </link-form-item>-->
        </link-form>
        <!-- 小酒产品 -->
        <link-swipe-action v-for="(item, index) in prodList" :key="index">
            <link-swipe-option slot="option" @tap="deleteItem(item, index)" v-if="item.scanNumber == 0">删除
            </link-swipe-option>
            <link-form :value="item">
                <link-form-item label="小酒产品名称" field="productName" required>
                    <view @tap="chooseProduct(item, index)" class="productName">
                        <text :class="[item.productName ? '' : 'prod-grey']">
                            {{ item.productName ? item.productName : '选择小酒产品' }}
                        </text>
                        <link-icon icon="icon-right"/>
                    </view>
                </link-form-item>
                <link-form-item label="开瓶数量" field="productNumber" required>
                    <link-number-keyboard v-model="item.productNumber" :min="0"  :max='99' @input="changeProdNum(item, index)"/>
                </link-form-item>
                <link-form-item v-if="scanCodeNoBatchNumber === 'Y'" label="开瓶扫码" field="scanNumber">
                    <link-icon style="color: #1E8BFF; text-align: right; font-size: 24px;" icon="icon-scan"
                               v-if="item.productNumber > item.scanNumber" @tap="clickScan(item, index)"/>
                </link-form-item>
                <link-form-item label="开瓶扫码数量" field="scanNumber" @tap="gotoOpenRecord(item)">
                    <item slot="custom" title="开瓶扫码数量">
                        <view style="text-align: right; color: #333333">{{
                                item.scanNumber
                            }}瓶
                        </view>
                    </item>
                </link-form-item>
            </link-form>
        </link-swipe-action>
        <!-- 添加小酒产品按钮 -->
        <view class="add-prod" @tap="addProd">
            <text class="iconfont icon-plus"></text>
            <text class="text">添加小酒产品</text>
        </view>
        <!--动销登记-->
        <pin-register v-if="formData.id"
                      :id="formData.accountId"
                      :activityId='formData.activityId'
                      :feedbackId="formData.id"
                      :sourceFlag="pageParam.sourceFlag"
                      :operateValidate="operateValidate"
                      :status="regStatus"
                      :approveStatus="approveStatus"
                      ref="productionPin"></pin-register>
        <!--现场取证材料-->
        <view class="menu-stair" style="margin-bottom: 12px">
            <view class="line">
                <view class="line-top"></view>
                <view class="line-bottom"></view>
            </view>
            <view class="stair-title">反馈材料</view>
        </view>
        <!--图片模板无权限-->
        <lnk-no-auth v-if="imgAuthFlag"></lnk-no-auth>
        <view style="margin: 12px">
            <view v-show="!imgAuthFlag">
                <view v-for="(item,index) in picturesNeedUploaded" :key="index+1" class="upload-1">
                    <view v-for="(item2,index2) in item.picTypeList" :key="index2+ 1">
                        <site-materials :activityId="formData.id"
                                    :basePlaceholder="item2.base.placeholder"
                                    :valuesPlaceholder="item2.values.placeholder"
                                    :cameraRefresh="cameraRefresh"
                                    :operationFlag="operationFlag"
                                    :involve-flag="involve(item2)"
                                    ref="pic"
                                    @involveFun="involveFun"
                                    :moduleType="item2.ctrlCode"
                                    :object-type="'picturesNeedUploaded'"
                                    :object-type-one-index="index" :object-type-two-index="index2"
                                    :picTypeList="picTypeList"
                                    :useModuleName="item2.ctrlCode | lov('TMPL_SUB_BIZ_TYPE')"
                                    :required="item2.base.require">
                        </site-materials>
                    </view>
                </view>
            </view>
            <view :key="keyIndex">
                <view v-for="(item,index) in scenePictureList" :key="index" class="upload-1">
                    <view v-for="(item22,index22) in item.picTypeList" :key="index22">
                        <site-materials v-if="timeControl(item22)" :activityId="formData.id"
                                    :basePlaceholder="item22.base.placeholder"
                                    :valuesPlaceholder="item22.values.placeholder"
                                    :cameraRefresh="cameraRefresh"
                                    :operationFlag="operationFlag"
                                    :involve-flag="involve(item22)"
                                    ref="pic"
                                    @involveFun="involveFun"
                                    :moduleType="item22.ctrlCode"
                                    :object-type="'scenePictureList'"
                                    :object-type-one-index="index" :object-type-two-index="index22"
                                    :picTypeList="picTypeList"
                                    :useModuleName="item22.ctrlCode | lov('TMPL_SUB_BIZ_TYPE')"
                                    :required="item22.require">
                        </site-materials>
                    </view>
                </view>
            </view>
        </view>
        <link-sticky>
            <link-button block autoLoading @tap="save">保存</link-button>
            <link-button block autoLoading @tap="commit">提交</link-button>
            <link-button block autoLoading @tap="newSave">保存并新建</link-button>
        </link-sticky>
        <link-dialog ref="udfBackDialog">
            <view slot="head">
                提示
            </view>
            <view>
                即将离开页面，是否保存信息？
            </view>
            <link-button slot="foot" @tap="directReturn">否</link-button>
            <link-button slot="foot" @tap="backSave">是</link-button>
        </link-dialog>
        <link-dialog v-model="openFlag" title="">
            <view>
                {{startShow}}
            </view>
            <link-button slot="foot" @tap='openFlag = false'>确定</link-button>
        </link-dialog>
    </link-page>

</template>

<script lang="jsx">
import pinRegister from './components/pin-register.vue';
import FeedbackMaterials from "./components/feedback-materials.vue";
import SiteMaterials from "./components/site-materials.vue";
import {reverseTMapGeocoder} from "../../utils/locations-tencent";
import {ROW_STATUS} from "../../utils/constant";
import LnkNoAuth from "../core/lnk-no-auth/lnk-no-auth.vue";
import Taro from "@tarojs/taro";
import {PageCacheManager} from "../../utils/PageCacheManager";
import {ComponentUtils} from "link-taro-component";
import headNavigationBar from "./components/head-navigation-bar.vue";

export default {
    name: "headquarters-register-upsert-page",
    components: {LnkNoAuth, SiteMaterials, FeedbackMaterials, pinRegister, headNavigationBar},
    data() {
        // 从登记列表/详情中进入选择终端时的安全性
        const menuId = this.pageParam.menuId;
        const accessGroupOauth = this.$utils.getMenuAccessGroup(menuId, '/pages/terminal/terminal/es-terminal-list-page');
        let oauth = "";
        if (!this.$utils.isEmpty(accessGroupOauth)) {
            oauth = accessGroupOauth;
        } else {
            oauth = this.$utils.isPostnOauth() === 'MY_POSTN' ? 'MULTI_POSTN' : this.$utils.isPostnOauth();
            if (oauth === 'MY_ORG') {
                oauth = 'MULTI_ORG'
            }
            if (this.$taro.getStorageSync('token').result.positionType === 'SalesAreaManager') {
                oauth = 'MULTI_POSTN'
            }
            if (this.$taro.getStorageSync('token').result.positionType === 'CityManager') {
                oauth = 'MULTI_ORG'
            }
        }
        // 从登记列表/详情中进入选择终端
        const accountOption = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/activityAccount/queryTerminalPage'
            },
            searchFields: ['accountName', 'accountCode'],
            param: {
                // return {
                freeze: 'Y',
                activityId: this.pageParam.activityId,
                rows: 25,
                oauth: oauth,
                // }
            },
        });
        // 从投放终端中进入选择活动
        const headActOption = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/activityAccount/queryByExamplePage'
            },
            searchFields: ['activityName'],
            param: {
                freeze: 'Y',
                accountId: this.pageParam.accountId,
                rows: 25,
                filtersRaw: [
                    {id: 'activityStatus', property: 'activityStatus', value: 'Processing', operator: '='},
                    {id: 'approveStatus', property: 'approveStatus', operator: '=', value: 'Approved'},
                    {id: 'activityType', property: 'activityType', value: 'MarketingSix'}
                ]
            },
            sortField: 'created',
        });
        // 选择小酒产品
        const productOption = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/activityProduct/queryByExamplePage'
            },
            searchFields: ['productName'],
            param: () => {
                return {
                    rows: 25,
                    feedbackId: this.formData.id,
                    filtersRaw: [
                        {id: 'activityId', property: 'activityId', value: this.pageParam.activityId}
                    ]
                }
            },
            renderFunc: (h, {data, index}) => {
                return (
                    <item arrow={false} title={data.productName} key={data.id} data={data} content={data.productCode}/>
                )
            }
        });
        const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
        const formData = {};
        // 从缓存读取未保存的数据
        const cacheData = PageCacheManager.getInitialData({
            ctx: this,
            path: 'headquarters-activity/headquarters-register-upsert-page.vue',
            title: '总部活动-新建登记',
            initialData: {
                formData
            },
        });
        if (cacheData.formData !== formData) {
            setTimeout(() => {
                this.formData = cacheData.formData
            }, 1000)
        }
        return {
            loading: false,
            keyIndex:0,
            openFlag:true,
            formData: {},
            accountOption,  // 从活动进入选择终端
            headActOption,  // 从终端进入选择活动
            productOption,  // 选择小酒产品
            rules: {
                activityName: this.Validator.required('活动名称必填'),
                accountName: this.Validator.required('终端必选'),
                // feedbackName: this.Validator.required('登记名称必填'),
                // feedbackStage: this.Validator.required('活动时段必填'),
                activityDate: this.Validator.required('活动日期必填'),
                stageName: this.Validator.required('请先维护该活动日期的推广阶段！'),
                privateRoom: this.Validator.required('包间号必填！'),
                privateNumber: [
                    this.Validator.required('当日用餐包间数量必填！'),
                    this.Validator.number({min: 1, max: 1000})
                ],
                clientNumber: this.Validator.number({min: 0}),
            },
            coordinate: {}, // 存储地理经纬度信息
            addressData: {},  // 地理位置信息
            addressDataFull: '', //详细地理位置
            addressFlag: false, //是否显示地理位置
            headRegFlag: false, //来自总部登记活动
            terminalFlag: false, //来自终端登记活动
            cameraRefresh: false,// 相机刷新标志
            imgAuthFlag: false, //图片模板无权限
            picturesNeedUploaded: [],//需要上传的图片-后台查询对应业务场景配置的图片
            scenePictureList: [{title: "必传反馈照片", picTypeList: []}],//场景图片部分二：前端需要写定的几种
            operationFlag: true,//是否可以操作照片的新建和删除
            temp: {},    //模版数据
            id: '', //登记活动id
            regReqList: [], //登记要求列表
            isChooseProd: false,  //是否可选择小酒产品
            isChooseAct: false,  //是否可选择活动，默认可选
            sourceFlag: '', //来源
            clickScanFlag: true, //点击扫码
            scanCodeList: [], //扫码记录列表
            isMounted: false, //是否已挂载
            createdTime: '', //编辑时获取创建时间
            regNameMMDD: '', //时间
            scanCodeNoBatchNumber: '', // 企业参数--是否无需指定特定小酒的批次号就可扫码(Y/N)
            batchNumber: [], // 企业参数--指定某些特定小酒的批次号才可扫码
            startShow: '', //企业参数--提示语
            newSaveFlag: false, //保存并新建
            newSaveHeadFlag: false, //从活动-保存并新建
            newSaveTerminalFlag: false, //从终端-保存并新建
            userInfo, //用户信息
            commitFlag: false, //提交标识
            privateRoomList: [],    // 当前...包间号列表
            prodList: [], // 产品列表
            choseStageName: 0,//设置选中活动阶段
            navigationBarTitle: '新增登记活动',
            navigationBarTitleColor: '#ffffff',
            zIndex: ComponentUtils.nextIndex(),
            navBackgroundColor: 'transparent',
            picTypeList: [],
            regStatus: 'NEW', // 登记活动状态默认为新建
            approveStatus: '' // 活动审批状态
        }
    },
    async mounted() {
        // 是否无需指定特定小酒的批次号就可扫码
        this.scanCodeNoBatchNumber = await this.$utils.getCfgProperty(
            "ALLOW_SCAN_CODE_VERIFICATION"
        );
        // 指定某些特定小酒的批次号才可扫码
        const batchNumber = await this.$utils.getCfgProperty(
            "SCANNABLE_CODE_BATCH_NUMBER"
        );
        const startShow = await this.$utils.getCfgProperty(
            "New_Feedback_Prompt"
        );
        this.startShow = startShow
        this.batchNumber = batchNumber.split(',');
        await this.initData();
        this.isMounted = true;
        this.$bus.$on('refreshRegUpsert', async (data) => {
            await this.queryScanNumber(data);
        });
        this.$bus.$on('refreshRegUpserts', async (data) => {
            await this.queryProdList(this.formData.id);
        });
        if (this.pageParam.data.status) {
            this.regStatus = this.pageParam.data.status
        }
        if (this.pageParam.data.approveStatus) {
            this.approveStatus = this.pageParam.data.approveStatus
        }

    },
    watch: {
        'formData.productNumber': {
            deep: true,
            async handler(newVal, oldVal) {
                // 在组件挂载后才开始监听
                if (!this.isMounted) return;
                if (newVal > oldVal) {
                    this.clickScanFlag = true;
                    this.formData.productNumber = newVal;
                }
                if (newVal < this.formData.scanNumber) {
                    this.$message.warn('重新编辑开瓶数量，开瓶数量需大于扫码数量！');
                }
                if (newVal === this.formData.scanNumber && this.formData.scanNumber) {
                    this.clickScanFlag = false;
                }
            },
        },
        'formData.accountName': {
            async handler(newValue, oldValue) {
                // lzljqw-004-708在填写完包间号后就自动生成登记名称
                // if (this.formData.feedbackCode && this.formData.accountName && this.formData.privateRoom) {
                if (!this.isMounted) return;
                if (this.formData.accountName && this.formData.privateRoom) {
                    await this.generateRegName();
                }
            }
        },
        'formData.privateRoom': {
            async handler(newValue, oldValue) {
                // lzljqw-004-708在填写完包间号后就自动生成登记名称
                // if (this.formData.feedbackCode && this.formData.accountName && this.formData.privateRoom) {
                if (!this.isMounted) return;
                if (this.formData.accountName && this.formData.privateRoom) {
                    await this.generateRegName();
                }
            }
        },
        // @make edit by 谭少奇 2023/07/20 14:45 数据请求新增已完成类型,数据为多条时需手动选择活动阶段数据
        'formData.activityDate': {
            // deep: true,
            async handler(newValue, oldValue) {
                if (!this.isMounted) return;
                // console.log('测试步骤，活动日期');
                if (this.formData.accountId && this.formData.activityDate) {
                    try {
                        const {rows, success} = await this.$http.post('action/link/promotionStage/queryByExamplePage', {
                            filtersRaw: [
                                {
                                    id: 'startTime',
                                    property: 'startTime',
                                    value: this.formData.activityDate,
                                    operator: '<='
                                },
                                {id: 'endTime', property: 'endTime', value: this.formData.activityDate, operator: '>='},
                                {
                                    id: 'actId',
                                    property: 'actId',
                                    value: this.formData.activityId,
                                    operator: '='
                                },
                            ],
                            pageFlag: true,
                            rows: 5,
                            page: 1
                        })
                        if (success) {
                            if (rows.length === 0) {
                                this.$set(this.formData, 'stageName', '');
                                this.$dialog({
                                    title: '提示',
                                    content: '请联系管理员维护推广阶段！',
                                    cancelButton: false,
                                    confirmText: '确定',
                                });
                            } else if (rows.length === 1) {
                                this.$set(this.formData, 'stageName', rows[0].promotionStage);
                                this.formData.promotionStageId = rows[0].id;
                            } else {
                                this.$message.warn('活动阶段不唯一，请联系管理员！');
                            }

                        }
                    } catch (e) {
                        this.$message.warn('根据活动日期查询活动阶段失败，请稍候再试！');
                    }
                } else {
                    this.$message.warn('请先选择终端，再选择活动日期！');
                    // this.$set(this.formData, 'activityDate', '');
                }
            }
        }
    },
    methods: {
        /**
         * 政策类型默认值
         * <AUTHOR>
         * @date	2023/12/12 20:49
         */
        async defaultPolicyType() {
            const lovList = await this.$lov.getLovByType('MARKETSIX_POLICY_TYPE');
            // 取序号最小的
            if (lovList && lovList.length) {
                lovList.sort((a, b) => a.seq - b.seq);
                return lovList[0].val;
            }
            return '';
        },
        /**
         * 检查并保存产品
         * <AUTHOR>
         * @date    2023/8/8 16:54
         */
        async checkAndSaveProd() {
            let msg = '';
            const prodList = this.$utils.deepcopy(this.prodList);
            for (let i = 0; i < prodList.length; i++) {
                if (!prodList[i].productName) {
                    msg = '请输入小酒产品名称';
                    break;
                }
                if (!prodList[i].productNumber && prodList[i].productNumber !== 0) {
                    msg = '请输入开瓶数量';
                    break;
                }
                if (prodList[i].scanNumber && prodList[i].productNumber < prodList[i].scanNumber) {
                    msg = '开瓶数量需大于扫码数量！';
                    break;
                }
                prodList[i].row_status = prodList[i].id ? 'UPDATE' : 'NEW';
            }
            if (msg) {
                this.$showError(msg);
            } else {
                const {success, result} = await this.$http.post('action/link/headquarterProduct/batchUpsert', prodList);
                if (success) {
                    this.prodList = result.map((item) => ({
                        ...item,
                        row_status: 'UPDATE'
                    }));
                } else {
                    msg = result;
                }
            }
            return msg;
        },
        /**
         * 查询小酒产品
         * <AUTHOR>
         * @date    2023/8/8 11:29
         */
        async queryProdList(id) {
            this.prodList = []
            const {success, rows} = await this.$http.post('action/link/headquarterProduct/queryByExamplePage', {
                filtersRaw: [
                    {id: 'feedbackId', property: 'feedbackId', value: id || this.formData.id},
                    {id: 'productType', property: 'productType', value: 'Open'}
                ]
            });
            if (success) {
                this.prodList = rows;
            }
        },
        /**
         * 修改开瓶数量
         * <AUTHOR>
         * @date    2023/8/8 9:30
         */
        changeProdNum(item, index) {
            if (item.scanNumber && item.productNumber < item.scanNumber) {
                this.$message.warn('重新编辑开瓶数量，开瓶数量需大于扫码数量！');
            }
        },
        /**
         * 保存小酒产品
         * <AUTHOR>
         * @date    2023/8/8 10:17
         */
        async saveProd(item, index, status) {
            const url = status === 'update' ? 'action/link/headquarterProduct/update' : 'action/link/headquarterProduct/insert';
            const {success, newRow} = await this.$http.post(url, item);
            if (success) {
                this.$set(this.prodList, index, newRow);
            }
        },
        /**
         * 删除小酒产品
         * <AUTHOR>
         * @date    2023/8/7 19:31
         */
        async deleteItem(item, index) {
            if (item.id) {
                const {success, result} = await this.$http.post('action/link/headquarterProduct/deleteById', {
                    id: item.id
                });
                if (success) {
                    this.prodList.splice(index, 1);
                    this.$message.success('删除成功！');
                }
            } else {
                this.prodList.splice(index, 1);
                this.$message.success('删除成功！');
            }
        },
        /**
         * 添加小酒产品
         * <AUTHOR>
         * @date    2023/8/4 17:53
         */
        async addProd() {
            if (await this.checkAndSaveProd()) {
                return;
            }
            this.prodList.push({
                feedbackId: this.formData.id,
                scanNumber: 0,
                productType: 'Open'
            });
        },
        /**
         * 选择小酒产品
         * <AUTHOR>
         * @date    2023/8/7 17:30
         */
        async chooseProduct(data, index) {
            if (!this.formData.activityName) {
                this.$message.warn('请先选择活动！');
                return;
            }
            if (data.scanNumber) {
                return;
            }
            const chooseItem = await this.$object(this.productOption, {
                pageTitle: '选择小酒产品'
            });
            const itemIndex = this.prodList.findIndex(item => item.productId === chooseItem.productId);
            if (itemIndex !== -1) {
                this.$showError(`${chooseItem.productName}已经被选择，请重新选择产品！`);
            } else {
                this.$set(this.prodList[index], 'productId', chooseItem.productId);
                this.$set(this.prodList[index], 'productCode', chooseItem.productCode);
                this.$set(this.prodList[index], 'productName', chooseItem.productName);
                if (data.id) {
                    this.saveProd(this.prodList[index], index, 'update');
                }
            }
        },
        /**
         * desc 自定义返回函数
         * <AUTHOR>
         * @date 2023-07-20
         * @params
         */
        udfBack() {
            this.$refs.udfBackDialog.show();
        },
        /**
         * desc 取消新建数据
         * <AUTHOR>
         * @date 2023-07-20
         * @params
         */
        directReturn() {
            let route;
            if (this.pageParam.sourceFlag === 'regListDetail' || this.pageParam.sourceFlag === 'headRegDetail' || this.pageParam.sourceFlag === 'regListEdit' || this.pageParam.sourceFlag === 'headReg') {
                route = 'pages/headquarters-activity/headquarters-register-list-page';
            }
            ;
            if (this.pageParam.sourceFlag === 'terminalList' || this.pageParam.sourceFlag === 'terminalDetail' || this.pageParam.sourceFlag === 'terminalEdit' || this.pageParam.sourceFlag === 'terminal') {
                route = 'pages/headquarters-activity/delivery-terminal-detail-page';
            }
            ;
            let pages = Taro.getCurrentPages();    //获取当前页面信息栈
            const targetIndex = pages.findIndex(function (item) {
                return item.route === route;
            });
            if (targetIndex === -1) {
                return this.$nav.backAll()
            }
            const num = Number(pages.length - (Number(targetIndex) + 1));
            setTimeout(() => {
                this.$bus.$emit('refreshRegList');
                this.$bus.$emit('refreshTerminalList');  // 刷新终端列表
                this.$nav.back(null, num);
            }, 1000)
        },
        /**
         * desc 不点击保存就返回
         * <AUTHOR>
         * @date 2023-07-20
         * @params
         */
        async backSave() {
            try {
                await this.save();
            } catch (e) {
                this.$dialog({
                    title: '提示',
                    content: '若要保存信息，请先将必填信息填写完整！',
                    cancelButton: false,
                    initial: true,
                    onConfirm: () => {
                    }
                });
                this.$refs.udfBackDialog.hide();
            }
        },
        /**
         * desc 初始化页面信息
         * <AUTHOR>
         * @date 2023-04-10
         * @params
         */
        async initData() {
            const policyType = await this.defaultPolicyType();
            // 从登记列表进入 -> 新建
            if (this.pageParam.sourceFlag === 'regListDetail') {
                this.formData.id = await this.$newId();
                this.formData.activityId = this.pageParam.activityId;
                this.formData.feedbackCode = this.pageParam.feedbackCode;
                this.formData.activityType = this.pageParam.activityType;
                this.formData.activityName = this.pageParam.activityName;
                this.formData.registerRequest = this.pageParam.registerRequest;
                this.formData = {...this.formData};
                this.headRegFlag = true;
                this.formData.creator = this.userInfo.firstName;
                this.formData.creatorPostnName = this.userInfo.postnName;
                this.formData.creatorTel = this.userInfo.contactPhone;
                this.formData.policyType = policyType;
                this.prodList = [{
                    feedbackId: this.formData.id,
                    scanNumber: 0,
                    productType: 'Open'
                }];
            }
            // 从登记详情进入 -> 新建
            if (this.pageParam.sourceFlag === 'headRegDetail') {
                this.formData.id = await this.$newId();
                this.formData.activityId = this.pageParam.activityId;
                this.formData.activityType = this.pageParam.data.activityType;
                this.formData.activityName = this.pageParam.data.activityName;
                this.formData = {...this.formData};
                this.pageParam.registerRequest = this.pageParam.data.registerRequest;
                this.formData.registerRequest = this.pageParam.data.registerRequest;
                this.headRegFlag = true;
                this.formData.creator = this.userInfo.firstName;
                this.formData.creatorPostnName = this.userInfo.postnName;
                this.formData.creatorTel = this.userInfo.contactPhone;
                this.formData.policyType = policyType;
                this.prodList = [{
                    feedbackId: this.formData.id,
                    scanNumber: 0,
                    productType: 'Open'
                }];
            }
            // 从终端列表进入 -> 新建
            if (this.pageParam.sourceFlag === 'terminalList') {
                this.formData.id = await this.$newId();
                this.formData.accountName = this.pageParam.data.accountName;
                this.formData.accountId = this.pageParam.accountId;
                this.formData.terminalId = this.pageParam.accountId;
                this.formData.activityType = this.pageParam.data.activityType;
                this.formData.companyId = this.pageParam.companyId;
                await this.initQueryActivity();
                this.formData = {...this.formData};
                this.terminalFlag = true;
                this.formData.creator = this.userInfo.firstName;
                this.formData.creatorPostnName = this.userInfo.postnName;
                this.formData.creatorTel = this.userInfo.contactPhone;
                this.formData.policyType = policyType;
                this.prodList = [{
                    feedbackId: this.formData.id,
                    scanNumber: 0,
                    productType: 'Open'
                }];
            }
            // 从终端详情进入 -> 新建
            if (this.pageParam.sourceFlag === 'terminalDetail') {
                this.formData.id = await this.$newId();
                this.formData.accountId = this.pageParam.accountId;
                this.formData.terminalId = this.pageParam.accountId;
                this.formData.accountName = this.pageParam.data.terminalName;
                this.formData.companyId = this.pageParam.companyId;
                await this.initQueryActivity();
                this.formData = {...this.formData};
                this.terminalFlag = true;
                this.formData.creator = this.userInfo.firstName;
                this.formData.creatorPostnName = this.userInfo.postnName;
                this.formData.creatorTel = this.userInfo.contactPhone;
                this.formData.policyType = policyType;
                this.prodList = [{
                    feedbackId: this.formData.id,
                    scanNumber: 0,
                    productType: 'Open'
                }];
            }
            // 从登记列表进入 -> 编辑
            if (this.pageParam.sourceFlag === 'regListEdit') {
                this.headRegFlag = true;
                const res = await this.$http.post('action/link/headquarterFeedback/queryById', {id: this.pageParam.id});
                if (res.success) {
                    this.formData = res.result;
                    this.formData.accountName = res.result.terminalName;
                    this.formData.accountId = res.result.terminalId;
                    this.formData = {...this.formData};
                    this.createdTime = res.result.created;
                    await this.generateRegName(this.createdTime);
                } else {
                    this.$showError('查询登记活动数据失败！');
                    await this.goBack();
                }
                this.formData.id = this.pageParam.id;
                this.formData.activityId = this.pageParam.activityId;
                this.formData.feedbackCode = this.pageParam.feedbackCode;
                this.formData = {...this.formData};
                await this.queryProdList(this.formData.id);
            }
            // 从终端列表进入 -> 编辑
            if (this.pageParam.sourceFlag === 'terminalEdit') {
                this.terminalFlag = true;
                const res = await this.$http.post('action/link/headquarterFeedback/queryById', {id: this.pageParam.id});
                console.log('从终端列表进编辑queryById', res);
                if (res.success) {
                    this.formData = res.result;
                    this.formData.activityType = res.result.activityType;
                    this.formData.accountId = res.result.terminalId;
                    this.formData.terminalId = res.result.terminalId;
                    this.formData.accountName = res.result.terminalName;
                    this.formData.activityId = res.result.activityId;
                    this.pageParam.activityId = res.result.activityId;
                    this.formData = {...this.formData};
                    this.pageParam.registerRequest = res.result.registerRequest;
                    this.formData.registerRequest = res.result.registerRequest;
                    this.createdTime = res.result.created;
                    await this.generateRegName(this.createdTime);
                } else {
                    this.$showError('查询登记活动数据失败！');
                    await this.goBack();
                }
                this.formData.id = this.pageParam.id;
                this.formData.feedbackCode = this.pageParam.data.feedbackCode;
                this.formData = {...this.formData};
                await this.queryProdList(this.formData.id);
            }

            //  保存并新建 -> 活动
            if (this.pageParam.sourceFlag === 'headReg') {
                this.formData.id = await this.$newId();
                this.formData.activityId = this.pageParam.activityId;
                this.formData.activityType = this.pageParam.activityType;
                this.formData.activityName = this.pageParam.activityName;
                this.formData.accountId = this.pageParam.accountId;
                this.formData.accountName = this.pageParam.accountName;
                this.formData.activityDate = this.pageParam.activityDate;
                this.formData.feedbackStage = this.pageParam.feedbackStage;
                this.formData.registerRequest = this.pageParam.registerRequest;
                this.formData.companyId = this.pageParam.companyId;
                this.formData.stageName = this.pageParam.stageName;
                this.formData.promotionStageId = this.pageParam.promotionStageId;
                this.formData.terminalId = this.pageParam.accountId;
                this.formData.terminalName = this.pageParam.accountName;
                // this.formData = {...this.pageParam};
                this.headRegFlag = true;
                this.formData.creator = this.userInfo.firstName;
                this.formData.creatorPostnName = this.userInfo.postnName;
                this.formData.creatorTel = this.userInfo.contactPhone;
                this.formData.policyType = policyType;
                this.prodList = [{
                    feedbackId: this.formData.id,
                    scanNumber: 0,
                    productType: 'Open'
                }];
                console.log('保存并新建 -> 活动formData', this.formData);
            }

            // 保存并新建 -> 终端
            if (this.pageParam.sourceFlag === 'terminal') {
                this.formData.id = await this.$newId();
                this.formData.activityType = this.pageParam.activityType;
                this.formData.activityName = this.pageParam.activityName;
                this.formData.terminalId = this.pageParam.accountId;
                this.formData.accountId = this.pageParam.accountId;
                this.formData.accountName = this.pageParam.accountName;
                this.formData.activityDate = this.pageParam.activityDate;
                this.formData.feedbackStage = this.pageParam.feedbackStage;
                this.formData.registerRequest = this.pageParam.registerRequest;
                this.formData.companyId = this.pageParam.companyId;
                this.formData.stageName = this.pageParam.stageName;
                this.formData.promotionStageId = this.pageParam.promotionStageId;
                await this.initQueryActivity();
                this.terminalFlag = true;
                this.formData.creator = this.userInfo.firstName;
                this.formData.creatorPostnName = this.userInfo.postnName;
                this.formData.creatorTel = this.userInfo.contactPhone;
                this.formData.policyType = policyType;
                this.prodList = [{
                    feedbackId: this.formData.id,
                    scanNumber: 0,
                    productType: 'Open'
                }];
                console.log('保存并新建 -> 终端formData', this.formData);
            }
            await this.querySceneImg();
        },
        /**
         * desc 初始化查询活动
         * <AUTHOR>
         * @date 2023-04-26
         * @params
         */
        async initQueryActivity() {
            await this.headActOption.methods.reload();
            this.$set(this.formData, 'activityName', this.headActOption.list[0].activityName);
            this.$set(this.formData, 'registerRequest', this.headActOption.list[0].registerRequest);
            this.$set(this.formData, 'activityId', this.headActOption.list[0].activityId);
            this.$set(this.pageParam, 'activityId', this.headActOption.list[0].activityId);
        },
        /**
         * desc 跳转到扫码记录
         * <AUTHOR>
         * @date 2023-04-24
         * @params
         */
        gotoOpenRecord(item) {
            this.$nav.push('/pages/headquarters-activity/scan-code-record-page.vue', {
                id: this.formData.id,
                headId: item.id,
                approveStatus: this.formData.approveStatus,
                freshSourceFlag: 'upsert',
            })
        },
        /**
         * desc 若进入该页面扫过码，则不可选择小酒产品、不可选择活动
         * <AUTHOR>
         * @date 2023-04-19
         */
        hadScan() {
            if (this.formData.scanNumber) {
                this.isChooseProd = true;
                this.isChooseAct = true;
            }
        },
        /**
         * desc 选择终端之后
         * <AUTHOR>
         * @date 2023-04-14
         */
        async afterSelectTerminal(data) {
            if(this.formData.terminalId === data.id){
                return
            }
            if (this.formData.feedbackCode && this.$refs.productionPin.productionPinList.length > 0) {
                const data = await this.$http.post('action/link/headquarterProduct/deleteByFeedbackId', {
                    feedbackId: this.formData.id,
                    productType: 'Sales'
                });
                if (data.success) {
                    this.$refs.productionPin.getData()
                    this.$set(this.formData, 'accountId', data.id);
                    this.$set(this.formData, 'terminalId', data.id);
                    this.$set(this.formData, 'companyId', data.companyId);
                    this.$set(this.formData, 'activityDate', '');
                    this.$message.warn('更新了终端，请重新选择活动动销！')
                }
            }else{
                this.$set(this.formData, 'accountId', data.id);
                this.$set(this.formData, 'terminalId', data.id);
                this.$set(this.formData, 'companyId', data.companyId);
                this.$set(this.formData, 'activityDate', '');
            }
        },
        /**
         * desc 选择活动之后
         * <AUTHOR>
         * @date 2023-04-18
         */
        async afterSelectHeadAct(data) {
            if(this.formData.activityId === data.activityId){
                return
            }
            this.$set(this.formData, 'registerRequest', data.registerRequest);
            this.$set(this.formData, 'activityId', data.activityId);
            this.$set(this.pageParam, 'activityId', data.activityId);
            await this.querySceneImg();
            // 如果再次选择活动之前已经选了产品，则清空产品（产品/动销都清空）
            console.log('测试终端进入新建再次选择活动时的this.formData', this.formData);
            if (this.formData.feedbackCode && this.prodList.length) {
                const data = await this.$http.post('action/link/headquarterProduct/deleteByFeedbackId', {
                    feedbackId: this.formData.id,
                    productType: 'Open'
                });
                if (data.success) {
                    this.$set(this.formData, 'productName', '');
                    this.$refs.productionPin.getData();
                    this.queryProdList()
                    this.$message.warn('更新了总部活动，请重新选择小酒产品！')
                }
            }
            if (this.formData.productName && !this.formData.feedbackCode) {
                this.$set(this.formData, 'productName', '');
            }
        },

        /**
         * desc 选择小酒产品之前
         * @date 2023-04-23
         */
        beforeSelectProd() {
            if (!this.formData.activityName) {
                this.$message.warn('请先选择活动！');
                return Promise.reject();
            }
        },
        /**
         * desc 选择小酒产品之后
         * @date 2023-04-18
         */
        afterSelectProd(data) {
            this.$set(this.formData, 'productName', data.productName);
        },
        /**
         * desc 新增生成登记名称
         * <AUTHOR> @date 2023-04-25
         */
        async generateRegName(created) {
            if (!created) {
                this.regNameMMDD = '';
            }
            if (this.formData.activityDate) {
                const month = this.formData.activityDate.substr(5, 2);
                const day = this.formData.activityDate.substr(8, 2);
                this.regNameMMDD = month + day;
            }
            if (!this.regNameMMDD && created) {
                const month = created.substr(5, 2);
                const day = created.substr(8, 2);
                this.regNameMMDD = month + day;
            }
            if (!this.formData.privateRoom) {
                this.formData.privateRoom = '';
            }
            this.$set(this.formData, 'feedbackName', this.regNameMMDD + this.formData.accountName + this.formData.privateRoom);
        },

        /**
         * desc 扫码/上传图片/添加动销前校验
         * <AUTHOR> @date 2023-04-18
         */
        async operateValidate() {
            // 编辑活动（保存过）不调用upsert接口
            if (this.pageParam.row_status === 'NEW') {
                try {
                    await this.$refs.upsertAct.validate();
                } catch (e) {
                    this.$dialog({
                        title: '提示',
                        content: '请先将基础信息填写完成再扫码/添加动销！',
                        cancelButton: false,
                        confirmText: '确定',
                    });
                    throw e;
                }
                const data = await this.$http.post('action/link/headquarterFeedback/upsert', {
                    id: this.formData.id,
                    activityId: this.formData.activityId,
                    activityType: this.formData.activityType,
                    terminalId: this.formData.terminalId,
                    accountId: this.formData.accountId,
                    row_status: ROW_STATUS.NEW,
                    promotionStageId: this.formData.promotionStageId ? this.formData.promotionStageId : this.pageParam.promotionStageId,
                    activityDate: this.formData.activityDate,
                    ...this.formData
                });
                if (data.success) {
                    // this.formData = data.newRow;
                    this.formData.feedbackCode = data.newRow.feedbackCode;
                    await this.generateRegName(data.newRow.created);
                }
            }
            if (this.pageParam.row_status === 'UPDATE') {
                try {
                    await this.$refs.upsertAct.validate();
                } catch (e) {
                    this.$dialog({
                        title: '提示',
                        content: '请先将基础信息填写完成再扫码/添加动销！',
                        cancelButton: false,
                        confirmText: '确定',
                    });
                    throw e;
                }
            }
            this.pageParam.row_status = 'UPDATE';
        },

        /**
         * desc 点击扫码
         * <AUTHOR>
         * @date 2023-04-14
         */
        async clickScan(item, index) {
            await this.operateValidate();
            if (this.$utils.isEmpty(item.productName)) {
                this.$showError('请先输入小酒产品名称！');
                return;
            }
            if (this.$utils.isEmpty(item.productNumber)) {
                this.$showError('请先输入开瓶数量！');
                return;
            }
            if (this.$utils.isEmpty(item.id)) {
                await this.saveProd(item, index, 'new');
            }
            const that = this;
            await that.getAddress();
            if (that.$utils.isEmpty(this.coordinate.latitude) && that.$utils.isEmpty(this.coordinate.longitude)) {
                this.$dialog({
                    title: '提示',
                    content: '请确认手机地理位置授权是否打开，或者【设置】-【企业微信】位置权限管理是否打开？',
                    cancelButton: false,
                    confirmText: '去开启',
                    onConfirm: async () => {
                        let userLocation = await this.$locations.openSetting();
                        if (userLocation['scope.userLocation']) {
                            that.coordinate = await that.$locations.getCurrentCoordinate();
                        }
                    }
                });
            } else {
                await wx.scanCode({
                    onlyFromCamera: true,
                    success: async (res) => {
                        that.$utils.showLoading();
                        await that.afterGetCode(res.result, this.prodList[index], index);
                        that.$utils.hideLoading();
                    }
                });
            }
        },
        /**
         * 获取码之后的处理
         * <AUTHOR>
         * @date 2023-04-14
         */
        async afterGetCode(mark, item, index) {
            try {
                this.$utils.showLoading();
                const data = await this.$http.post('action/link/headquarterFeedback/headOpenScanVerification', {
                    mark: mark,
                    headId: this.formData.id,
                    province: this.addressData.province,
                    city: this.addressData.city,
                    district: this.addressData.district,
                    scanAddress: this.addressDataFull,
                    productType: 'Open',
                    productId: item.productId,
                    productNumber: item.productNumber,
                    remark: this.addressData.remark,
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    this.$set(this.prodList[index], 'scanNumber', data.scanNumber);
                    this.$message.success('扫码成功');
                    setTimeout(() => {
                        this.clickScan(item, index);
                    }, 500);
                } else {
                    this.$utils.hideLoading();
                    this.$showError(data.message);
                }
            } catch (e) {
                this.$utils.hideLoading();
            }
        },

        /**
         * 获取定位地址  百度经纬度逆解析
         */
        async getAddress() {
            this.coordinate = await this.$locations.getCurrentCoordinate();
            // 校验用户是否授权地理位置
            if (!this.$utils.isEmpty(this.coordinate.latitude) && !this.$utils.isEmpty(this.coordinate.longitude)) {
                let address = await reverseTMapGeocoder(this.coordinate.latitude, this.coordinate.longitude, '总部活动-登记列表-新增');
                this.addressData = address['originalData'].result.addressComponent;
                this.addressDataFull = address['originalData'].result.formatted_address;
                this.addressFlag = true;
            }
        },

        /**
         * desc 扫码成功查询扫码数量
         * <AUTHOR>
         * @date 2023-04-14
         */
        async queryScanNumber(item, index = -1) {
            // 删除扫码记录更新扫码数量
            if (index === -1) {
                index = this.prodList.findIndex(t => t.id === item.headId);
            }
            try {
                const data = await this.$http.post('action/link/headquarterProduct/queryByExamplePage', {
                    feedbackId: this.formData.id,
                    filtersRaw: [
                        {id: 'productId', property: 'productId', value: item.productId},
                        {id: 'productType', property: 'productType', value: 'Open'}],
                });
                if (data.success) {
                    if (data.rows.length && index !== -1) {
                        this.$set(this.prodList[index], 'scanNumber', data.rows[0].scanNumber);
                    }
                } else {
                    this.$showError('查询扫码数量失败，请稍候再试');
                }
            } catch (e) {
                this.$showError('查询扫码数量失败');
            }
        },
        involveFun(type,oneIndex,twoIndex,involveValue,moduleType,uid){
            if(type === 'picturesNeedUploaded'){
                this.picturesNeedUploaded[oneIndex].picTypeList[twoIndex]['involve'] = involveValue;
            }else if(type === 'scenePictureList'){
                this.scenePictureList[oneIndex].picTypeList[twoIndex]['involve'] = involveValue;
            }

            //处理显示上的效果
            if(this.picInvolveRepeat.indexOf(moduleType)>-1){
                if(this.$utils.isNotEmpty(this.$refs['pic'])){
                    this.$refs['pic'].forEach((item)=>{
                        if(item._uid !== uid){
                            item.repeatInvolvedDispose(involveValue);
                        }
                    })
                }
                if(this.$utils.isNotEmpty(this.$refs['scene'])){
                    this.$refs['scene'].forEach((item)=>{
                        if(item._uid !== uid){
                            item.repeatInvolvedDispose(involveValue);
                        }
                    })
                }
            }

            for(let picIndex=0;picIndex<this.picturesNeedUploaded.length;picIndex++){
                this.picturesNeedUploaded[picIndex].picTypeList.forEach((item)=>{
                    if(item.ctrlCode===moduleType){
                        item['involve'] = involveValue;
                    }
                })
            }
            for(let scIndex=0;scIndex<this.scenePictureList.length;scIndex++){
                this.scenePictureList[scIndex].picTypeList.forEach((item)=>{
                    if(item.ctrlCode===moduleType){
                        item['involve'] = involveValue;
                    }
                })
            }
        },
        involve(item){
            if(this.$utils.isNotEmpty(item.values)){
                if(this.$utils.isNotEmpty(item.values.involve)){
                    if(item.values.involve === 'true'){
                        return true;
                    }else{
                        return false;
                    }
                }else{
                    return false;
                }
            }else{
                return false;
            }
        },
        timeControl(item){
            if(this.imgTimeFlag){
                if(item.ctrlCode === 'canqianchenlie'||item.ctrlCode==='canhoukaiping' || item.ctrlCode==='jiezhangdan'){
                    return true;
                }else{
                    return false;
                }
            }else{
                return true;
            }
        },
        /**
         * desc 根据业务场景查询配置的需要维护的场景图片
         * <AUTHOR>
         * @date 2023-04-12
         */
        async querySceneImg() {
            this.picturesNeedUploaded = [];
            let picturesNeedUploaded = [];
            // 获取模版子类型，将返回的登记要求字符串转化为数组
            if (this.pageParam.sourceFlag === 'terminalList' || this.pageParam.sourceFlag === 'terminalDetail') {
                this.$set(this.pageParam, 'registerRequest', this.formData.registerRequest);
            }
            const regRegStr = this.formData.registerRequest.replace(/"/g, '');
            if (this.formData.registerRequest.charAt(0) === "[") {
                this.regReqList = regRegStr.slice(1, -1).split(',');
            } else {
                this.regReqList = regRegStr.slice(1, -1);
            }
            for (let i = 0; i < this.regReqList.length; i++) {
                let tempType = this.regReqList[i];
                const data = await this.$utils.getQwMpTemplate('HeadFeedbackData', tempType);
                if (!data.success) {
                    this.imgAuthFlag = true;
                    this.$utils.hideLoading();
                } else {
                    this.imgAuthFlag = false;
                }
                let resultOpt = JSON.parse(data.result);
                let temp = [];//当前业务场景配置的场景图片数组信息
                temp = JSON.parse(resultOpt.conf);
                const defaultPicData = {
                    title: tempType,
                    picTypeList: temp
                };
                temp.forEach((item) => {
                    let picType = item.ctrlCode;
                    this.picTypeList.push(picType);
                });
                picturesNeedUploaded.push(defaultPicData);
                // picturesNeedUploaded.push(temp[0]);
            }
            this.picturesNeedUploaded = picturesNeedUploaded;
            this.keyIndex += 1;
            this.picTypeList = this.picTypeList.filter((item,index)=>{
                return this.picTypeList.indexOf(item) === index;
            });
            // this.picTypeList = picturesNeedUploaded.map(i => i.ctrlCode)
        },

        /**
         * desc 输入包间号后提示
         * <AUTHOR>
         * @date 2023-05-25
         */
        async afterPrivateRoom($event) {
            if (this.formData.privateRoom) {
                try {
                    const data = await this.$http.post('export/link/headquarterFeedback/queryByExamplePage', {
                        filtersRaw: [
                            {id: 'terminalId', property: 'terminalId', value: this.formData.accountId, operator: '='},
                            {
                                id: 'activityDate',
                                property: 'activityDate',
                                value: this.formData.activityDate,
                                operator: '='
                            },
                            {
                                id: 'feedbackStage',
                                property: 'feedbackStage',
                                value: this.formData.feedbackStage,
                                operator: '='
                            },
                            {
                                id: 'privateRoom',
                                property: 'privateRoom',
                                value: this.formData.privateRoom,
                                operator: 'NOT NULL'
                            },
                            {id: 'status', property: 'status', value: 'Inactive', operator: '<>'},
                        ],
                    });
                    if (data.success) {
                        if (data.rows.length > 0) {
                            const uniquePrivateRooms = new Set();
                            data.rows.forEach(item => {
                                if (item.privateRoom) {
                                    uniquePrivateRooms.add(item.privateRoom);
                                }
                            });
                            this.privateRoomList = Array.from(uniquePrivateRooms);
                            this.$message.warn('已存在' + this.privateRoomList.join(', ') + '的登记');
                        }
                    }
                } catch (e) {
                }
            }
        },
        /**
         * desc 新增必填校验逻辑
         * <AUTHOR>
         * @date 2023110/20
         */
        newCheckSave(){
			// 当关联的总部活动的推广阶段等于第二或第三阶段，且政策类型等于【无】或者【团购购酒】时，常规装登记的【动销数量】必填
			const newList = this.$refs.productionPin.productionPinList
			const stageName = ['Thesecondstage','Thethirdstage'].includes(this.formData.stageName)
			const policyType = ['None','Purchase'].includes(this.formData.policyType)
			const newData = newList.find(i=>{
				return i.scanNumber <= 0
			})
			if(stageName&&policyType){
				if(newData || !newList[0] ){
					return false
				}
			}
            return true

        },
        /**
         * desc 保存活动信息
         * <AUTHOR>
         * @date 2023-04-10
         */
        async save () {
            if (this.loading && !this.newSaveFlag && !this.commitFlag) {
                return;
            }
            try {
                this.loading = true;
                this.$utils.showLoading();
                this.formData.approveStatus = 'New';
                this.formData.status = 'New';
                await this.$refs.upsertAct.validate();
                if (!this.prodList.length) {
                    this.$showError('至少添加一个小酒产品！');
                    this.$utils.hideLoading();
                    return;
                }
                if (await this.checkAndSaveProd()) {
                    this.$utils.hideLoading();
                    return;
                }
				const isTrue = this.newCheckSave()
				if(!isTrue){
					this.$showError('请完善动销数量！');
                    this.$utils.hideLoading();
					return
				}
                for (let i = 0; i < this.picturesNeedUploaded.length; i++) {
                    const item = this.picturesNeedUploaded[i];
                    // 判断是否有"打卡照片"
                    if (this.regReqList.includes('didipic')) {
                        if (item.ctrlCode === 'didipic' && this.$refs.pic[i].$refs.img.imgData.length > 0) {
                            this.$set(this.formData, 'signPicture', 'Y');
                        } else {
                            this.$set(this.formData, 'signPicture', 'N');
                        }
                    } else {
                        this.$set(this.formData, 'signPicture', 'N');
                    }
                    // 如果必输,则校验必输的值是否为空
                    // 240319 修改提示逻辑
                    if (item.picTypeList[0].base['require'] && this.$refs.pic[i].$refs.img.imgData.length == 0) {
                        this.$message.warn(`请上传${item.picTypeList[0].base['label']}的图片！`);
                        this.$utils.hideLoading();
                        return Promise.reject(false);
                    }
                    // if (!!item.picTypeList.base['require'] && !this.$refs.pic[i].$refs.img.imgData.length) {
                    //     this.$message.warn(`请上传${item.picTypeList.base['label']}的图片！`);
                    //     this.$utils.hideLoading();
                    //     return Promise.reject(false);
                    // }
                }
                if (!this.formData.feedbackCode) {
                    const reg = await this.$http.post('action/link/headquarterFeedback/insert', {
                        promotionStageId: this.pageParam.promotionStageId,
                        activityDate: this.formData.activityDate,
                        ...this.formData,
                        row_status: ROW_STATUS.NEW
                    });
                    if (reg.success) {
                        this.$set(this.formData, 'feedbackCode', reg.newRow.feedbackCode);
                        await this.generateRegName(reg.newRow.created);
                        this.$bus.$emit('refreshRegList', reg.newRow.id);  // 刷新登记列表
                        this.$bus.$emit('refreshTerminalList');  // 刷新终端列表
                        // return Promise.resolve({success:true})
                    } else {
                        this.$message.warn('保存出错，请检查网络！');
                        return Promise.reject()
                    }
                }
                if (this.formData.feedbackCode) {
                    console.log('包含包间号', this.privateRoomList.includes(this.formData.privateRoom))
                    if (this.privateRoomList.includes(this.formData.privateRoom)) {
                        return;
                    }
                    const data = await this.$http.post('action/link/headquarterFeedback/update', {
                        promotionStageId: this.pageParam.promotionStageId,
                        ...this.formData,
                        attr7: 'Six'
                    });
                    if (data.success) {
                        if (this.pageParam.sourceFlag === 'regListDetail' || this.pageParam.sourceFlag === 'headRegDetail' || this.pageParam.sourceFlag === 'regListEdit' || this.pageParam.sourceFlag === 'headReg') {
                            this.$bus.$emit('refreshRegList', data.newRow.id, 'UPDATE');  // 刷新登记列表
                        }
                        if (this.pageParam.sourceFlag === 'terminalList' || this.pageParam.sourceFlag === 'terminalDetail' || this.pageParam.sourceFlag === 'terminalEdit' || this.pageParam.sourceFlag === 'terminal') {
                            this.$bus.$emit('refreshTerminalList');  // 刷新终端列表
                        }
                        this.$message.success('保存成功！');
                        if (!this.newSaveFlag && !this.commitFlag) {
                            setTimeout(async () => {
                                this.$utils.hideLoading();
                                this.loading = false;
                                await this.goBack();
                            }, 300);
                        }
                        return Promise.resolve({success:true})
                    }else{
                        this.$message.warn('保存出错，请检查网络！');
                        return Promise.reject()
                    }
                }
            } catch (e) {
                this.$utils.hideLoading();
                console.log(e);
                throw e
            }
        },
        /**
         * desc 保存并新建
         * <AUTHOR>
         * @date 2023-04-10
         */
        async newSave () {
            if (this.loading) {
                return;
            }
            this.loading = true;
            this.newSaveFlag = true;
            const isTrue = this.newCheckSave()
            if(!isTrue){
            	this.$showError('请完善动销数量！');
                this.$utils.hideLoading();
                this.loading = false;
            	return
            }
            await this.save();
            if (this.terminalFlag) {
                this.loading = false;
                const tempFormData = {...this.formData};
                this.$nav.redirect('/pages/headquarters-activity/headquarters-register-upsert-page.vue', {
                    // id: await this.$newId(),
                    row_status: ROW_STATUS.NEW,
                    accountId: tempFormData.accountId,
                    terminalId: tempFormData.accountId,
                    activityType: tempFormData.activityType,
                    activityName: tempFormData.activityName,
                    accountName: tempFormData.accountName,
                    activityDate: tempFormData.activityDate,
                    feedbackStage: tempFormData.feedbackStage,
                    registerRequest: tempFormData.registerRequest,
                    companyId: tempFormData.companyId,
                    stageName: tempFormData.stageName,
                    promotionStageId: tempFormData.promotionStageId,
                    sourceFlag: 'terminal'
                });
            }
            ;
            if (this.headRegFlag) {
                this.loading = false;
                const tempFormData = {...this.formData};
                this.$nav.redirect('/pages/headquarters-activity/headquarters-register-upsert-page.vue', {
                    // id: await this.$newId(),
                    row_status: ROW_STATUS.NEW,
                    accountId: tempFormData.accountId,
                    activityId: tempFormData.activityId,
                    activityType: tempFormData.activityType,
                    activityName: tempFormData.activityName,
                    accountName: tempFormData.accountName,
                    activityDate: tempFormData.activityDate,
                    feedbackStage: tempFormData.feedbackStage,
                    registerRequest: tempFormData.registerRequest,
                    terminalId: tempFormData.accountId,
                    terminalName: tempFormData.accountName,
                    companyId: tempFormData.companyId,
                    stageName: tempFormData.stageName,
                    promotionStageId: tempFormData.promotionStageId,
                    sourceFlag: 'headReg'
                });
            }
            ;
        },

        /**
         * desc 提交活动信息
         * <AUTHOR>
         * @date 2023-04-10
         */
        async commit () {
            if (this.loading) {
                return;
            }
            try {
                this.loading = true;
                this.commitFlag = true;
                const isTrue = this.newCheckSave()
                if(!isTrue){
                	this.$showError('请完善动销数量！');
                    this.$utils.hideLoading();
                    this.loading = false;
                	return
                }
                 const {success} = await this.save();
                if(!success){
                    this.$showError('提交失败！');
                    return
                }
                this.$utils.showLoading();
                const data = await this.$http.post('action/link/headquarterFeedback/feedbackCommit', {id: this.formData.id});
                if (data.success) {
                    this.$message.success('提交成功！');
                    if (this.pageParam.sourceFlag === 'regListDetail' || this.pageParam.sourceFlag === 'headRegDetail' || this.pageParam.sourceFlag === 'regListEdit') {
                        this.$bus.$emit('refreshRegList', data.rows.id);  // 刷新登记列表
                    }
                    if (this.pageParam.sourceFlag === 'terminalList' || this.pageParam.sourceFlag === 'terminalDetail' || this.pageParam.sourceFlag === 'terminalEdit' || this.newSaveTerminalFlag) {
                        this.$bus.$emit('refreshTerminalList');  // 刷新终端列表
                    }
                    setTimeout(async () => {
                        this.$utils.hideLoading();
                        await this.goBack();
                    }, 500);
                } else {
                    this.$showError('提交失败:' + data.result);
                    console.log('提交失败', data.result);
                }
            } catch (e) {
                this.$utils.hideLoading();
                this.loading = false;
                // this.$message.warn('请检查网络，并填写必填字段！');
            } finally {
                setTimeout(async () => {
                    this.loading = false;
                }, 1000);
                this.$utils.hideLoading();
            }
        },
        /**
         * desc 返回总部活动登记列表页面
         * <AUTHOR>
         * @date 2023/03/23
         */
        async goBack() {
            this.$nav.back();
        }
    }
}
</script>

<style lang="scss">
.chose-stage:after {
    content: '*';
    color: red;
    position: relative;
    left: 0px;
    font-weight: bold;
    top: 6px;
}

.headquarters-register-upsert-page {
    width: 100%;
    overflow: hidden;

    // .view {
    //     background: white;
    // }

    .add-prod {
        border: 2px solid #2F69F8;
        border-radius: 40px;
        margin: 20px;
        height: 80px;
        line-height: 80px;
        text-align: center;

        .text {
            font-family: PingFangSC-Regular, serif;
            font-size: 32px;
            color: #2F69F8;
            letter-spacing: 0;
        }

        .icon-plus {
            font-size: 32px;
            color: #2F69F8;
        }
    }

    .menu-stair {
        width: 100%;
        margin-left: 24px;
        padding-top: 40px;
        @include flex-start-center;

        .line {
            clear: both;

            .line-top {
                width: 8px;
                height: 16px;
                background: #3FE0E2;
            }

            .line-bottom {
                width: 8px;
                height: 16px;
                background: #2F69F8;
            }
        }

        .stair-title {
            width: 60%;
            margin-left: 16px;
            font-family: PingFangSC-Semibold, serif;
            font-size: 32px;
            color: #262626;
            letter-spacing: 1px;
            line-height: 32px;
        }

        .edit {
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #2F69F8;
            letter-spacing: 0;
            line-height: 28px;
            text-align: right;
            width: 58%;
        }
    }

    .productName {
        .prod-grey {
            color: #ddd;
        }

        .icon-right {
            color: #ddd;
            padding-left: 10px;
        }
    }

}
</style>
