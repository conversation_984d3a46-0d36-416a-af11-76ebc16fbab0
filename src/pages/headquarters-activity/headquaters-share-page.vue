<!--
总部活动-营销6.0-一键转发
<AUTHOR>
@date 2023-09-12
@file headquaters-share-page
-->
<template>
    <link-page class="headquaters-share-page">
        <image v-if="!startFlag" class="bottom-bg" :src="$imageAssets.marketingSixForwardImage"></image>
        <template v-else>
            <headquater-register-share
                :key="renderCount"
                @reset="renderCount++"
                :formData1="formData"
                @saveSuccess="showImage"
            ></headquater-register-share>
        </template>
        <!-- 获取用户信息提示 -->
        <link-dialog ref="getInfoTips">
            <view slot="head">
                微信授权
            </view>
            <view>
                我们需要获取您授权的用户信息
            </view>
            <button slot="foot" class="refuse-button" @tap="refuseUserInfo" style="width:50%">拒绝</button>
            <button slot="foot" class="allow-button" open-type="getUserInfo" @getuserinfo="bindGetUserInfo"
                    style="width:50%">允许
            </button>
        </link-dialog>

        <!-- 获取用户手机号提示 -->
        <link-dialog ref="getMobileTips">
            <view slot="head">
                微信授权
            </view>
            <view>
                我们还需要获取您的手机号信息
            </view>
            <button slot="foot" class="refuse-button" @tap="refuseMobile" style="width:50%">拒绝</button>
            <button slot="foot" class="allow-button" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber"
                    style="width:50%">允许
            </button>
        </link-dialog>
    </link-page>
</template>

<script>
import {ComponentUtils, DeviceService} from "link-taro-component";
import headquaterRegisterShare from "./components/headquater-register-share.vue";
import Taro from "@tarojs/taro";
import store from "../../store/store";
import {env} from "../../../env";

let _openId = null, _wxMobile = null, _wxUserInfo = null;
let _screenOption = null;

export default {
    name: 'headquaters-share-page',
    components: {headquaterRegisterShare},
    data() {
        return {
            renderCount: 1,
            formData: {}, // 活动信息
            startFlag: false, // 是否开始执行反馈，通过号码校验为true
            canIUseGetUserProfile: false, // 新获取微信用户信息方法API是否可用
            openId: _openId, // 微信用户的openId
            unionId: '', // 微信用户的unionId
            wxMobile: _wxMobile,   // 微信用户手机号
            wxUserInfo: _wxUserInfo,  // 微信用户信息
            activityId: '', // 活动id
        }
    },
    async created() {
        // -------------------
        // 获取微信用户登录code
        const code = await this.getCode();
        console.log('code', code);
        Taro.setStorageSync('code', code);
        // 获取系统用户token
        const token = Taro.getStorageSync('token').token;
        console.log('token', token);
        if (token) {
            this.authorization = `bearer ${token}`;
        }
        // -------------------
        // 判断是否可使用【getUserProfile】方法
        if (wx.getUserProfile) {
            this.canIUseGetUserProfile = true;
        }
    },
    async onLoad(options) {
        console.log(111, {...options})
        if (!options.activityId && !!_screenOption) {
            /*重定向初始化页面*/
            options = _screenOption
        } else {
            /*分享初始化页面（第一次）*/
            Object.entries(options).forEach(([key, val]) => {
                options[key] = decodeURIComponent(val)
            })
            _screenOption = options
        }
        // 隐藏返回首页按钮
        wx.hideHomeButton();
        console.log('options', {...options});
        this.formData.activityId = options.activityId;
        this.formData.activityType = options.activityType;
        this.formData.activityDate = options.activityDate;
        this.formData.activityName = options.activityName;
        this.formData.promotionStageId = options.promotionStageId;
        this.formData.stageName = options.stageName;
        this.formData.terminalId = options.terminalId;
        this.formData.accountId = options.terminalId;
        this.formData.terminalName = options.terminalName;
        this.formData.accountName = options.terminalName;
        this.formData.terminalCode = options.terminalCode;
        this.formData.registerRequest = options.registerRequest;
        this.formData.creatorCode = options.creatorCode
        console.log('share-page的formData', this.formData);
        if (!this.wxMobile) {
            this.$refs.getInfoTips.show();
        } else {
            // this.startFlag = true
            await this.userValidate()
        }
    },
    // 监听this.wxMobile的值，当有值且不为空值时，执行await this.userValidate();
    watch: {
        wxMobile: {
            handler: async function (val, oldVal) {
                if (!!val) {
                    await this.userValidate();
                    // await this.initAct();
                }
            },
            deep: true
        },
    },
    methods: {
        /**
         *  @desc 保存成功后显示海报
         *  <AUTHOR>
         *  @date 2023-09-15
         **/
        showImage() {
            this.startFlag = false;
        },
        /**
         *  @desc 用户信息校验
         *  <AUTHOR>
         *  @date 2023-08-02
         **/
        async userValidate() {
            try {
                const data = await this.$http.post('action/link/forwardContact/checkPhoneNum', {
                    headId: this.formData.activityId,
                    phoneNum: this.wxMobile,
                    actType: 'MarketingSix',
                    openId: this.openId,
                    unionId: this.unionId,
                    terminalId: this.formData.terminalId,
                    userCode: this.formData.creatorCode,//转发人员工工号
                }, {noToken: true})
                if (data.success) {
                    const token = data.token
                    const expires = Date.now() + 1000 * 60 * 60 * 2
                    store.commit('user/setToken', token);
                    store.commit('user/setTokenTime', expires);
                    const userInfo = await this.getUserCtx();
                    userInfo.postnName = '外部联系人';
                    userInfo.firstName = this.wxMobile;
                    userInfo.userName = this.wxMobile;
                    const ret = {
                        expires,
                        result: userInfo,
                        token,
                    }
                    const loginInfo = {
                        ...ret,
                        ...env,
                        loginEnv: DeviceService.systemInfo['environment'],
                        crmcdntest: store.getters['httpsEnv/getHttpsEnv'],
                    }
                    store.commit('user/setUser', userInfo);
                    store.commit('user/setToken', token);
                    store.commit('user/setTokenTime', Date.now() + 1000 * 60 * 60 * 2);
                    Taro.setStorageSync('token', loginInfo);

                    this.startFlag = true;
                    console.log('验证之后this.startFlag', this.startFlag);
                    console.log('验证之后this.wxMobile', this.wxMobile);
                } else {
                    this.$message.warn('当前用户未进行联系人授权，将无法进行活动登记！');
                    return;
                }
            } catch (e) {
                console.log(e);
            }
        },
        /**
         *  @desc 获取用户信息
         *  <AUTHOR>
         *  @date 2023-08-09
         **/
        async getUserCtx() {
            try {
                const data = await this.$http.post('/login/base/user/getUserCtx')
                if (data.success || !data.result) {
                    return data.result;
                } else {
                    throw new Error(data)
                }
            } catch (e) {
                this.$message.warn("获取登录人信息失败，请稍后重试！" + JSON.stringify(e));
                throw e;
            }
        },
        /**
         * 获取微信用户登录的code
         * <AUTHOR>
         * @data 2023-09-14
         */
        async getCode() {
            return new Promise((resolve) => {
                wx.login({
                    success: function (res) {
                        if (res.code) {
                            resolve(res.code);
                        }
                    }
                });
            })
        },
        /**
         * refuseUserInfo
         * <AUTHOR>
         * @data 2023-09-14
         * 用户拒绝授权用户信息
         */
        refuseUserInfo() {
            this.$refs.getInfoTips.hide();
            this.$message.primary('您拒绝授权用户信息，将无法进行活动登记！');
        },
        /**
         * refuseMobile
         * <AUTHOR>
         * @data 2023-09-14
         * 用户拒绝授权手机号
         */
        refuseMobile() {
            this.$refs.getMobileTips.hide();
            this.$message.primary('您拒绝授权手机号，将无法进行活动登记！');
        },
        /**
         * 获取用户授权信息
         * <AUTHOR>
         * @data 2023-09-14
         */
        async bindGetUserInfo(e) {
            console.log('得到用户授权', e)
            this.$refs.getInfoTips.hide();
            // 用户同意授权用户信息
            if (e.detail.encryptedData) {
                // 解密用户openId
                _openId = this.openId = await this.decodeWxUserInfo(e.detail.encryptedData, e.detail.iv);
            }
            if (e.detail.userInfo) {
                _wxUserInfo = this.wxUserInfo = e.detail.userInfo;
            }
            // 继续询问授权
            this.queryAuthSetting();
        },
        /**
         * 获取用户授权信息-新API
         * <AUTHOR>
         * @data 2023-09-14
         */
        getUserProfile() {
            this.$refs.getInfoTips.hide();
            wx.getUserProfile({
                desc: '用于匹配是否为执行反馈联系人', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
                success: async (res) => {
                    // 用户同意授权用户信息
                    if (res.encryptedData) {
                        // 解密用户openId
                        this.openId = await this.decodeWxUserInfo(res.encryptedData, res.iv);
                    }
                    if (res.userInfo) {
                        this.wxUserInfo = res.userInfo;
                    }
                    // 继续询问授权
                    this.queryAuthSetting();
                }
            });
        },

        /**
         * 获取用户手机号码
         * <AUTHOR>
         * @data 2023-09-14
         */
        async getPhoneNumber(e) {
            this.$refs.getMobileTips.hide();
            // 用户确认授权
            if (e.detail.encryptedData) {
                // 解密用户手机号
                _wxMobile = this.wxMobile = await this.decodeWxUserMobile(e.detail.encryptedData, e.detail.iv);
                console.log('wxMobile', this.wxMobile);
                // 继续询问授权
                if (!this.wxMobile) {
                    this.queryAuthSetting();
                }
            } else {
                this.$message.primary('您拒绝授权手机号，将无法进行活动登记！');
            }
        },
        /**
         * 解密用户授权账号信息，返回用户唯一标识openId
         * <AUTHOR>
         * @data 2023-09-14
         */
        async decodeWxUserInfo(encryptedData, iv) {
            const code = await this.getCode();
            const data = await this.$http.post('action/link/eleBusinessCard/decodeWxUserInfo', {
                encryptData: encryptedData,
                iv: iv,
                code
                // code: this.$taro.getStorageSync('code')
            }, {
                noToken: true,
                noEncryptData: true,
                header: {Authorization: this.authorization}
            });
            if (data.success) {
                this.unionId = data.unionid;
                return data.openid;
            } else {
                return '';
            }
        },
        /**
         * 解密用户授权手机信息，返回用户微信绑定手机号
         * <AUTHOR>
         * @data 2023-09-14
         */
        async decodeWxUserMobile(encryptedData, iv) {
            console.log('encryptedData', encryptedData);
            console.log('iv', iv);
            console.log('code', this.$taro.getStorageSync('code'));
            const code = await this.getCode();
            const data = await this.$http.post('action/link/eleBusinessCard/decodeWxUserMobile', {
                encryptData: encryptedData,
                iv: iv,
                code
                // code: this.$taro.getStorageSync('code')
            }, {
                noToken: true,
                noEncryptData: true,
                header: {Authorization: this.authorization}
            });
            if (data.success) {
                return data.openid;
            } else {
                return '';
            }
        },


        /**
         * 询问用户授权情况：用户微信账号与绑定手机号信息
         * <AUTHOR>
         * @data 2023-09-14
         */
        async queryAuthSetting() {
            // 用户信息未授权
            if (JSON.stringify(this.wxUserInfo) === '{}') {
                if (this.canIUseGetUserProfile) {
                    // this.$refs.getInfoTips.hide();
                    this.getUserProfile();
                } else {
                    this.$refs.getInfoTips.show();
                }
                return;
            }
            // 手机号未授权
            if (this.$utils.isEmpty(this.wxMobile)) {
                this.$refs.getMobileTips.show();
                return;
            }
        },
    }
}
</script>

<style lang="scss">
.headquaters-share-page {
    .bottom-bg {
        width: 100%;
        height: 100vh;
    }

    .refuse-button {
        background: #fff;
        width: 50%;
        font-size: 28px;
        border-top: 1px solid #eff1f3 !important;
        border-right: 1px solid #eff1f3 !important;
        color: #333;
        border-radius: 0;
    }

    .allow-button {
        background: #fff;
        width: 50%;
        font-size: 28px;
        border-top: 1px solid #eff1f3 !important;
        color: #32CD32;
        border-radius: 0;
    }

    .allow-button::after,
    .refuse-button::after {
        border-radius: 0 !important;
        border: 0;
    }
}
</style>
