<!--
总部活动-宴席活动-执行反馈-大成公司新版分享页
<AUTHOR>
@date 2024-12-20
-->
<template>
    <link-page class="banquet-new-share-page">
        <!-- 口令认证 -->
        <password-authentication v-if="isPass && !endFlag" :banquetId="banquetId" @closPasswordVerify="()=>isPass=false"></password-authentication>
        <view class="banquet-new-share-page-main" v-if="!isPass && !endFlag">
            <!-- 基础信息 -->
            <view class="banquet-basic-data">
                <line-title title="基础信息" class="head-title"></line-title>
                <view class="basic-data-warp">
                    <view class="basic-data-item" v-for="(item, index) in basicDataSetting" :key="`basic_${index}`">
                        <view class="label">
                            {{item.label}}
                        </view>
                        <view class="value" v-if="item.lovType">
                            {{banquetData[item.feild] | lov(item.lovType)}}
                        </view>
                        <view class="value" v-else>
                            {{banquetData[item.feild]}}
                        </view>
                    </view>
                </view>
            </view>
            <!--执行反馈-->
            <banquet-feedback-edit
                :banquetItem="banquetData"
                ref="getFormData"
                id="feedback"
                :isNoCount="true"
                :maxImgLength="allImgLength"
                :banquetPolicy="banquetPolicy"
                :shareFlag="true"
            ></banquet-feedback-edit>
            <link-sticky>
                <link-button block @tap="save">保存</link-button>
            </link-sticky>
        </view>
        <image v-if="endFlag" class="bottom-bg" :src="$imageAssets.banquetForwardImage"></image>
    </link-page>
</template>
<script>
import banquetFeedbackEdit from './components/banquet-feedback-edit.vue';
import lineTitle from "../lzlj/components/line-title.vue";
import passwordAuthentication from './components/password-authentication.vue'
import lnkImgWatermark from "../core/lnk-img-watermark/lnk-img-watermark.vue";
import store from "../../store/store";
import Taro from "@tarojs/taro";
export default {
    name: 'banquet-new-share-page',
    components: {banquetFeedbackEdit, lineTitle, passwordAuthentication, lnkImgWatermark},
    data() {
        return {
            basicDataSetting: [
                {
                    feild: 'feedbackCode',
                    label: '活动编码'
                },
                {
                    feild: 'feedbackName',
                    label: '活动名称'
                },
                {
                    feild: 'mealTime',
                    label: '宴席正餐时间'
                },
                {
                    feild: 'mealSession',
                    lovType: 'YXHD_FOODTIME',
                    label: '宴席正餐类型'
                },
                {
                    feild: 'banquetConsumerTel',
                    label: '宴席主家电话'
                },
                {
                    feild: 'policyAddress',
                    label: '详细地址'
                }
                
            ],
            token: '',
            banquetId: '',
            banquetData: {}, // 宴席信息
            banquetPolicy: {}, // 宴席政策
            isPass: true,
            endFlag: false, // 是否显示执行反馈完成后的背景图
            formData: {},
            allImgLength: 9
        }
    },
    async onLoad(options) {
        // 隐藏返回首页按钮
        wx.hideHomeButton();
        this.banquetId = options.banquetId;
        this.token = options.token;
        this.openId = options.openId; // 用户id
    },
    watch: {
        token(newVal, oldVal) {
            if (newVal) {
                this.initData();
            }
        }
    },
    methods: {
        /**
         * 保存执行反馈提交内容
         * <AUTHOR>
         * @date	2024/12/20 
        */
        async save() {
            if (!this.$refs.getFormData.formData.actTableNum) {
                this.$message.error('请填写实际宴席桌数')
                return
            }
            try {
                if (this.banquetPolicy.scenePicture !== 'N' && this.banquetData.takePicturesFlag !== 'Y') {
                    await this.$refs.getFormData.getUploadPhotoInfo();
                }
                const data = await this.$http.post('action/link/headquarterFeedback/update', {
                    id: this.banquetId,
                    actTableNum: this.$refs.getFormData.formData.actTableNum,
                    updateFields: "actTableNum,rowVersion",
                });
                if (data.success) {
                    this.$message.success('保存成功！');
                    setTimeout(async () => {
                        this.$message.success('反馈已完成！');
                        this.endFlag = true;
                    }, 1000);
                } else {
                    this.$message.warn('保存出错！');
                }
            } catch (e) {
                console.log(e);
            }
        },
         /**
         * 获取宴席订单信息
         * <AUTHOR>
         * @date	2024/12/20 
        */
        async getBanquetData() {
            try {
                const {success, result} = await this.$http.post('action/link/headquarterFeedback/queryById', {
                    id: this.banquetId
                });
                if (success) {
                    this.banquetData = result;
                    // 每次进入分享页置空已输入的实际宴席桌数
                    this.banquetData.actTableNum = '';
                    this.queryBanquetPolicy();
                }
            } catch (e) {
                console.log('e: 获取宴席订单信息', e)
            }
        },
        /**
         * 获取宴席政策
         * <AUTHOR>
         * @date	2024/12/20
         */
        async queryBanquetPolicy() {
            try {
                const {success, result} = await this.$http.post('action/link/headquarterActivity/queryById', {
                    id: this.banquetData.activityId
                });
                if (success) {
                    this.banquetPolicy = result;
                }
            } catch (e) {

            }
        },
        /**
         *  @desc 获取用户信息
         *  <AUTHOR>
         *  @date 2024-12-24
         **/
        async getUserCtx() {
            try {
                const data = await this.$http.post('/login/base/user/getUserCtx')
                if (data.success || !data.result) {
                    store.commit('user/setUser', data.result);
                    Taro.setStorageSync('token', {token: this.token, result: data.result});
                } else {
                    throw new Error(data)
                }
            } catch (e) {
                this.$message.warn("获取登录人信息失败，请稍后重试！" + JSON.stringify(e));
                throw e;
            }
        },
        /**
         *  @desc 初始化页面数据
         *  <AUTHOR>
         *  @date 2024-12-24
         */
        async initData() {
            const expires = Date.now() + 1000 * 60 * 60 * 2;
            store.commit('user/setToken', this.token);
            store.commit('user/setTokenTime', expires);
            Taro.setStorageSync('token', {token: decodeURIComponent(this.token)});
            await this.getUserCtx();
            const ImgLength = await this.$utils.getCfgProperty('SAVE_BANQUET_FEEDBACK_MAX_PIC_SIZE');
            this.allImgLength = Number(ImgLength);
            this.getBanquetData();
        }
    },
}
</script>
<style lang="scss">
.banquet-new-share-page {
    .basic-data-warp {
        background-color: #fff;
        .basic-data-item {
            font-size: 28px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 80px;
            padding: 0 24px;
            flex-shrink: 0;
            .value {
                max-width: 500px;
            }
        }
    }
    .head-title {
        margin-bottom: 10px;
    }
    .bottom-bg {
        width: 100%;
        height: 100vh;
    }
}
</style>