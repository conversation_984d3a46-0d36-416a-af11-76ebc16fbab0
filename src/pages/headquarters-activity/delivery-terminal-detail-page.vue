<!--
总部活动-投放终端详情
<AUTHOR>
@date 2023-04-11
-->
<template>
    <link-page class="delivery-terminal-detail-page">
        <!--顶部背景-->
        <view class="top-container">
            <navigation-bar :backVisible="true"
                            :navBarAllHeight="fixTop"
                            :backgroundImg="$imageAssets.homeMenuBgImage"
                            :title="navigationBarTitle"
                            :titleColor="navigationBarTitleColor"
                            :navBackgroundColor="navBackgroundColor">
                <view class="top-content">
                    <view class="store-image" @tap="previewStoreUrl(clientDetails)">
                        <image :src="clientDetails.storeUrl || $imageAssets.terminalDefaultImage"></image>
                        <!--                        <image :src="$imageAssets.terminalDefaultImage"></image>-->
                    </view>
                    <view class="store-content-cover">
                        <view class="title-level-code">
                            <view class="store-content-top">
                                <!--【客户一级分类】为“终端Terminal”的时候显示storeSigns字段-->
                                <view class="store-title">{{ clientDetails.accountName }}</view>
                                <!--【客户一级分类】为“分销商Distributor”时展示billTitle字段-->
                                <!--                                <view class="store-title" v-if="clientDetails.acctType === 'Distributor'">{{clientDetails.acctName || clientDetails.billTitle}}</view>-->
                                <!--已认证-->
                                <view class="store-level" v-if="clientDetails.accountStage === 'ykf'">
                                    <image :src="$imageAssets.storeStatusVerifiedImage"></image>
                                </view>
                                <!--未认证-->
                                <view class="store-level" v-if="clientDetails.accountStage === 'xk'">
                                    <image :src="$imageAssets.storeStatusUnverifiedImage"></image>
                                </view>
                                <!--已失效-->
                                <view class="store-level" v-if="clientDetails.accountStage === 'ysx'">
                                    <image :src="$imageAssets.storeStatusInvalidationImage"></image>
                                </view>
                                <!--潜客-->
                                <view class="store-level" v-if="clientDetails.accountStage === 'dkf'">
                                    <image :src="$imageAssets.storeStatusPotentialImage"></image>
                                </view>
                            </view>
                        </view>
                        <view class="store-content-middle">
                            <view class="store-type" v-if="clientDetails.accountType">
                                {{ clientDetails.accountType | lov('ACCT_TYPE') }}
                            </view>
                            <!--                            <view class="store-type" v-if="clientDetails.acctCategory">{{clientDetails.acctCategory | lov('ACCNT_CATEGORY')}}</view>-->
                            <!--                            <view class="store-type" v-if="clientDetails.acctLevel || clientDetails.capacityLevel">-->
                            <!--                                <text v-if="clientDetails.acctLevel !== undefined">{{clientDetails.acctLevel | lov('ACCT_LEVEL')}}</text>-->
                            <!--                                <text  v-if="clientDetails.acctLevel && clientDetails.capacityLevel"> | </text>-->
                            <!--                                <text v-if="clientDetails.capacityLevel !== undefined">{{clientDetails.capacityLevel | lov('CAPACITY_LEVEL')}}</text>-->
                            <!--                            </view>-->
                            <view class="store-type" v-if="clientDetails.accountLevel">
                                {{ clientDetails.accountLevel | lov('ACCT_LEVEL') }}
                            </view>
                        </view>
                    </view>
                </view>
            </navigation-bar>
        </view>
        <view class="content">
            <!--下滑时的title-->
            <view class="top-blank" v-if="tapsFix"
                  :style="{'height': duration + 'px','line-height': duration+ 'px', 'padding-top':statusBarHeight +'rpx'}">
                {{ clientDetails.accountName }}
            </view>
            <!--状态栏-->
            <!--            <link-sticky top :duration="duration * 2 - 12">-->
            <!--状态栏-->
            <view class="tap-container">
                <view class="lnk-tabs">
                    <view class="lnk-tabs-item" v-for="(tab, index) in tapOptions" :key="index"
                          @tap="switchTab(tab)">
                        <view class="label-name" style="width: 67px;">{{ tab.name }}</view>
                        <view class="line" v-if="tab.val === tapsActive.val"></view>
                    </view>
                </view>
            </view>
            <!--            </link-sticky>-->
            <!--选择活动主题-->
            <view slot="actType" style="position: relative;z-index: 2;display: inline-block">
                <view
                    style="
                    display: inline-flex;
                    padding: 7px 5px;
                    border-radius: 20px;
                    background-color: #F2F2F2;
                    "
                >
                    <picker :value="index" @change="pickerChange" :range="actTypeList">
                        <select-button :label="actTypeList[index]" :selected-flag="true" downIcon
                                       style="background: #fff; color:#2F69F8; border:1px solid #EDF3FF;"></select-button>
                    </picker>
                </view>
            </view>
            <!--推广阶段-->
           <!-- <view v-if="tapsActive.val === 'promotionStage'">
                <promotion-stage :stageOption="stageOption"></promotion-stage>
            </view> -->
            <!--活动总览-->
            <view v-if="tapsActive.val === 'actOverview'">
                <act-overview :terminalItem="terminalItem" :totalData="totalData" ref="actOverview"></act-overview>
            </view>
            <!--登记总览-->
            <view v-if="tapsActive.val === 'regOverview'">
                <reg-overview :regOption="regOption" :userInfo="userInfo" :activityStatus="activityStatus" :addItemFlag="addItemFlag"></reg-overview>
            </view>
        </view>
        <link-fab-group v-if="tapsActive.val === 'regOverview' || tapsActive.val === 'actOverview'">
            <link-fab-item icon="icon-fenxiang" label="一键转发" @tap-icon="()=>clickForward()"/>
            <link-fab-item icon="icon-plus" v-if="addItemFlag" label="新建登记" @tap-icon="()=>clickAdd()"/>
            <!-- <link-fab-item icon="icon-baocun" label="进退货" @tap-icon="()=>clickInOUt()"/> -->
        </link-fab-group>
        <!--进退货明细弹框-->
        <link-dialog ref="inOutBottom"
                     :noPadding="true"
                     v-model="inOutDialogFlag"
                     position="bottom"
                     borderRadius="32rpx 32rpx 0 0">
            <view class="model-title">
                <view class="title">进退货明细</view>
                <view class="iconfont icon-close" @tap="closeDialog"></view>
            </view>
            <link-form>
                <link-form-item label="收发类型">
                    <link-lov type="HEAD_TRANSCEIVER_TYPE" v-model="clientDetails.shipType"></link-lov>
                </link-form-item>
                <link-form-item label="终端">
                    <link-input v-model="clientDetails.accountName" readonly></link-input>
                </link-form-item>
                <link-form-item label="公司">
                    <link-input v-model="clientDetails.compnayName" readonly></link-input>
                </link-form-item>
                <link-form-item label="选择产品">
                    <link-object :option="productOption"
                                 :row="clientDetails"
                                 pageTitle="选择产品"
                                 :map="{productName: 'productName', productId: 'productId', productCode: 'productCode'}"
                                 :value="clientDetails.productName"
                                 :afterSelect="afterSelectProduct">
                        <template v-slot="{data}">
                            <item :title="data.productName" :key="data.id" :data="data" :content="data.productCode"/>
                        </template>
                    </link-object>
                </link-form-item>
                <link-form-item label="数量(瓶)">
                    <link-number v-model="clientDetails.shipNumber" :min="1"/>
                </link-form-item>
                <view class="remark" style="padding: 12px; margin-bottom: 100px;">
                    <view class="label" style="font-size: 14px; padding: 0px 0px 12px 4px;">备注</view>
                    <view class="textarea-big">
                        <link-textarea class="text-area" :placeholder="'请输入备注'"
                                       mode="textarea"
                                       v-model="clientDetails.remark"
                                       :nativeProps="{maxlength:500}"
                                       style="padding: 0px"
                                       placeholder-style="color: #BFBFBF;"></link-textarea>
                        <view v-if="!clientDetails.remark" class="textarea-length">0/500</view>
                        <view v-else :class="clientDetails.remark.length < 501 ? 'textarea-length' : 'textarea-over-length'" v-model="clientDetails.remark.length" >{{clientDetails.remark.length+'/500'}}</view>
                    </view>
                </view>
            </link-form>
            <view class="blank"></view>
            <link-sticky class="bottom-btn">
                <link-button class="sure-btn" size="normal" autoLoading @tap="saveInOut">提交</link-button>
            </link-sticky>
        </link-dialog>
    </link-page>
</template>
<script>
import LnkTaps from "../core/lnk-taps/lnk-taps.vue";
import ActOverview from "./components/actOverview.vue";
import RegOverview from "./components/regOverview.vue";
import Taro from "@tarojs/taro";
import SelectButton from "../echart/lzlj/components/select-button.vue";
import {ROW_STATUS} from "../../utils/constant";
// import PromotionStage from "./components/promotion-stage.vue";

export default {
    name: "delivery-terminal-detail-page",
    components: {SelectButton, ActOverview, LnkTaps, RegOverview},//PromotionStage
    data() {
        const tapOptions = [
            // {name: '推广阶段', seq: '1', val: 'promotionStage'},
            {name: '活动总览', seq: '1', val: 'actOverview'},
            {name: '登记总览', seq: '2', val: 'regOverview'},
        ];
        const userInfo = Taro.getStorageSync('token').result;
        const stageOption = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/promotionStage/queryByExamplePage',
            },
            param: {
                sort: 'created',
                order: 'desc',
                filtersRaw: [
                    {id: 'terminalId', property: 'terminalId', value: this.pageParam.data.id, operator: '='},
                ],
            },
            hooks: {
                afterLoad(data) {
                    data.rows.forEach(async item => {
                        const start = new Date(item.startTime);
                        const end = new Date(item.endTime);
                        // 计算天数差
                        const timeInterval = Math.round(Math.abs((end - start) / (24 * 60 * 60 * 1000)));
                        this.$set(item, 'timeInterval', timeInterval);
                    })
                }
            }
        });
        const regOption = new this.AutoList(this, {
            url: {queryByExamplePage: 'export/link/headquarterFeedback/queryByExamplePage'},
            param: () => {
                return {
                    terminalId: this.pageParam.data.id,
                    filtersRaw: [{
                        id: "activityType",
                        property: "activityType",
                        value: this.defaultActType,
                        operator: "="
                    }],
                    ...!!this.pageParam.oauth ? {oauth: this.pageParam.oauth} : {}
                    // activityType: this.actTypeData[this.index].value,
                }
            },
            searchFields: ['feedbackName', 'creator', 'privateRoom','feedbackStage','feedbackCode'],
            sortField: 'created',
            sortDesc: 'desc',
            sortOptions: null,
            filterOption: [
                {label: '活动状态', field: 'status', type: 'lov', lov: 'HEAD_FEEDBACK_STATUS'},
                {label: '活动时段', field: 'feedbackStage', type: 'lov', lov: 'HEAD_FEEDBACK_TIME'},
                {label: '活动日期', field: 'activityDate', type: 'date'},
                {label: '活动更新时间', field: 'lastUpdated', type: 'date'}
            ],
        });
        const productOption = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/activityProduct/queryShipProductPage'
            },
            searchFields: ['productName', 'productCode'],
            param: {
                accountId: this.pageParam.data.id,
            }
        });
        return {
            clientDetails: this.pageParam.data, // 客户详情
            fixTop: 200,    // 顶部背景高度
            tapsFix: false, // tab是否固定
            duration: this.$device.isIphoneX ? 88 : 64, // 顶部导航栏高度
            statusBarHeight: this.$device.systemInfo.statusBarHeight, // 状态栏高度
            showDropdownFlag: false,    // 是否显示下拉菜单
            templateData: [],   // 模板数据
            navigationBarTitle: '终端详情',
            navigationBarTitleColor: '#ffffff',
            navBackgroundColor: 'transparent',
            actTypeData: [], // 选择活动主题
            actTypeList: [], // 选择活动主题列表
            index: 0, // 选择活动主题下标
            tapOptions, // tab栏数据
            tapsActive: {}, // 当前选中的tab
            terminalItem: {}, // 终端详情
            inventoryItem: {}, // 小酒产品名称、进货数量、开瓶总数、当前库存
            totalData: {}, // 库存总数, 登记场次，累计参与人数
            stageOption, // 推广阶段
            regOption, // 登记总览登记列表
            defaultActType: '', // 默认活动主题
            inOutDialogFlag: false, // 进退货明细弹框
            productOption, // 选择产品
            userInfo,
            activityStatus: '', // 活动状态
            addItemFlag: false // 新增登记
        }
    },
    // 暂时保留
    // watch: {
    //     'clientDetails.remark'(val) {
    //         console.log('val', val);
    //         if(this.clientDetails.remark.length > 500) {
    //             console.log('1');
    //             this.$dialog({
    //                 title: '提示',
    //                 content: '备注最多输入500字！',
    //                 cancelButton: false,
    //                 initial: true,
    //                 onConfirm: () => {}
    //             });
    //         }
    //     }
    // },
    async mounted() {
        this.actTypeData = await this.$lov.getLovByType('HEAD_ACT_TYPE');
        this.actTypeList = [];
        this.actTypeData.forEach(item => {
            this.actTypeList.push(item.name)
        });
        this.tapsActive = this.tapOptions[0];
        this.terminalItem = this.pageParam.data;
        this.terminalItem.activityType = this.actTypeData[0].val;
        this.defaultActType = this.actTypeData[0].val;
        this.$bus.$on('refreshTerminalList', async () => {
            this.$utils.showLoading();
            this.regOption.methods.reload()
            this.$utils.hideLoading();
        });
        // 对活动状态做处理
        if(this.pageParam.data.activityStatusList) {
            this.activityStatus = this.pageParam.data.activityStatusList.split(',');
            if(this.activityStatus.includes('Processing')) {
                this.addItemFlag = true;
                this.activityStatus = 'Processing';
            }
            if(!this.activityStatus.includes('Processing') && this.activityStatus.includes('End')) {
                this.activityStatus = 'End';
            }
        }
        this.$bus.$on('refreshTerminalItem', async (item) => {
            const findIndex = this.regOption.list.findIndex(i => i.id === item.id)
            if (findIndex > -1) {
                this.regOption.list[findIndex] = item
            } else {
                this.regOption.list.unshift(item)
            }
        });
        this.$bus.$on('promotionStageList', async () => {
            this.stageOption.methods.reload()
        })
    },
    computed: {
        /**
         * 监听页面滚动函数
         * <AUTHOR>
         * @date 2020-08-05
         * @param e 距离顶部距离
         */
        onPageScroll(e) {
            this.tapsFix = e.scrollTop >= this.fixTop - this.duration;
            this.showDropdownFlag = false
        },
    },
    methods: {
        /**
         * 点击新建登记
         * <AUTHOR>
         * @date 2023-04-07
         */
        clickAdd() {
            if (this.pageParam.data.freezeFlag === 'Y') {
                this.$message.error('该终端7天内未完成100%核销，不允许新建登记')
            } else {
                this.$nav.push('/pages/headquarters-activity/headquarters-register-upsert-page.vue', {
                    accountId: this.pageParam.data.id,
                    companyId: this.pageParam.data.companyId,
                    data: this.pageParam.data,
                    // registerRequest: this.pageParam.data.registerRequest,
                    sourceFlag: 'terminalList',
                    row_status: ROW_STATUS.NEW
                })
            }
        },
        /**
         * 点击一键转发
         * <AUTHOR>
         * @date 2023-09-12
         */
        async clickForward() {
            if (this.pageParam.data.freezeFlag === 'Y') {
                this.$message.error('该终端7天内未完成100%核销，不允许一键转发')
            } else {
                this.$nav.push('/pages/headquarters-activity/headquarters-forward-contact-page.vue', {
                    terminalFlag: true,
                    accountName: this.clientDetails.accountName,
                    terminalId: this.pageParam.data.id,
                    terminalCode: this.clientDetails.accountCode,
                })
            }

        },
        /**
         * 点击进退货
         * <AUTHOR>
         * @date 2023-04-07
         */
        async clickInOUt() {
            this.$refs.inOutBottom.show();
            this.clientDetails.compnayName = await this.$lov.getNameByTypeAndVal('ACTIVITY_COMPANY', Taro.getStorageSync('token').result.coreOrganizationTile.l3Id);
        },
        /**
         * 选择产品之后
         * <AUTHOR>
         * @date 2023-04-23
         */
        afterSelectProduct(data) {
            this.$set(this.clientDetails, 'productId', data.productId);
        },
        /**
         * 保存进退货
         * <AUTHOR>
         * @date 2023-04-23
         */
        async saveInOut() {
            this.$dialog({
                title: '提示',
                content: '进退货将发起审批，审批通过后的进退货数量会计入库存中。',
                cancelButton: true,
                onConfirm: async () => {
                    this.$utils.showLoading('提交进退货...');
                    try {
                        const newId = await this.$newId();
                        const data = await this.$http.post('action/link/purchaseRecord/submit', {
                            productId: this.clientDetails.productId,
                            terminalId: this.clientDetails.id,
                            companyId: this.clientDetails.companyId,
                            shipType: this.clientDetails.shipType,
                            shipNumber: this.clientDetails.shipNumber,
                            productName: this.clientDetails.productName,
                            productCode: this.clientDetails.productCode,
                            approvalStatus: 'Approving',
                            id: newId,
                            remark: this.clientDetails.remark
                        })
                        if (data.success) {
                            await this.$refs.actOverview.getInventoryData();
                            await this.$refs.actOverview.getTotalData();
                            this.$message.success('提交进退货成功');
                            this.$refs.inOutBottom.hide();
                            this.clientDetails.shipType = '';
                            this.clientDetails.productName = '';
                            this.clientDetails.productId = '';
                            this.clientDetails.productCode = '';
                            this.clientDetails.shipNumber = '';
                            this.clientDetails.remark = '';
                        }
                    } catch (error) {
                        // this.$showError('提交进退货失败' + error.result);
                        console.log('error', error);
                    } finally {
                        this.$utils.hideLoading();
                    }
                },
                onCancel: () => {
                    console.log('取消');
                }
            })

        },
        /**
         * 关闭进退货弹框
         * <AUTHOR>
         * @date 2023-04-23
         */
        closeDialog() {
            this.inOutDialogFlag = !this.inOutDialogFlag;
        },
        /**
         * 切换活动类型
         * <AUTHOR>
         * @date 2023-04-07
         */
        async pickerChange(e) {
            if (this.index === this.actTypeList[Number(e.detail.value)]) {
                return
            }
            this.index = Number(e.detail.value);
            this.terminalItem.activityType = this.actTypeData[this.index].val;
            await this.$refs.actOverview.getInventoryData();
            await this.$refs.actOverview.getTotalData();
            if (this.tapsActive.val === 'regOverview') {
                this.defaultActType = this.actTypeData[this.index].val;
                this.regOption.methods.reload();
            }
        },
        /**
         * tab栏切换
         * <AUTHOR>
         * @date 2023-04-11
         */
        switchTab(tab) {
            this.tapsActive = tab;
        },
        /**
         * 门头照片预览
         * <AUTHOR>
         * @date 2023-04-11
         * @param param
         */
        async previewStoreUrl(param) {
            const that = this;
            if (!that.$utils.isEmpty(param.storePicKey)) {
                let imgUrl = await this.$image.getSignedUrl(param.storePicKey);
                const inOptions = {
                    current: imgUrl,
                    urls: [imgUrl]
                };
                that.$image.previewImages(inOptions)
            } else {
                const inOptions = {
                    current: that.$imageAssets.terminalDefaultImage,
                    urls: [that.$imageAssets.terminalDefaultImage]
                };
                that.$image.previewImages(inOptions)
            }
        },
    }
}
</script>

<style lang="scss">
.delivery-terminal-detail-page {
    .link-auto-list .link-auto-list-top-bar .link-auto-list-query-bar .link-auto-list-query-item > view.link-filter {
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-justify-content: flex-end;
        -ms-flex-pack: end;
        justify-content: flex-end;
        padding: 0 24px;
        font-size: 28px;
        background-color: #f2f2f2;
    }
    .link-sticky .link-sticky-content {
        width: 95vw;
        position: relative;
        display: flex;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        margin: 0 20px;
        justify-content: center;
    }

    .link-button.link-button-mode-fill.link-button-status-primary {
        height: 100px;
    }

    /*deep*/
    .link-fab-button {
        background: linear-gradient(128deg, rgba(47, 140, 248, 0.8) 12%, rgba(47, 105, 248, 0.8) 70%);
    }

    .top-container {
        .comp-navbar {
            width: 100vw;

            .placeholder-bar {
                background-color: transparent;
                width: 100%;
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                -webkit-box-pack: start;
                -ms-flex-pack: start;
                justify-content: flex-start;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;

                .icon-left {
                    width: 10%;
                    font-size: 34px;
                    color: #FFFFFF;
                    padding-left: 24px;
                }

                .navigator-back {
                    width: 46px;
                    height: 46px;
                    padding-left: 24px;
                }

                .bar-title {
                    width: 82%;
                    font-size: 34px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    text-align: center;
                }
            }
        }

        .top-content {
            @include flex;

            .store-image {
                margin-left: $margin-normal;
                margin-top: 32px;
                width: 128px;
                height: 128px;
                border-radius: 16px;
                overflow: hidden;
                box-shadow: 0 7px 49px 0 rgba(20, 28, 51, 0.39);

                image {
                    width: 100%;
                    height: 100%;
                }
            }

            .store-content-cover {
                margin-top: 32px;
                width: 80%;

                .title-level-code {
                    @include flex-start-center;
                    @include space-between;
                    width: 100%;

                    .store-content-top {
                        @include flex-start-center;
                        @include space-between;
                        margin-left: 24px;

                        .store-title {
                            font-family: PingFangSC-Semibold, serif;
                            font-size: 32px;
                            color: #ffffff;
                            letter-spacing: 0;
                            line-height: 40px;
                            max-width: 370px;
                        }

                        .store-level {
                            width: 120px;
                            height: 44px;
                            margin-left: 12px;

                            image {
                                width: 100%;
                                height: 100%;
                            }
                        }
                    }

                    .qr-code {
                        width: 80px;
                        height: 44px;
                        padding-right: 24px;
                        padding-left: 12px;

                        image {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }

                .store-content-middle {
                    @include flex-start-center;
                    /*padding-top: 24px;*/
                    height: 60px;
                    line-height: 60px;
                    margin-left: 24px;

                    .store-type {
                        border: 1px solid #ffffff;
                        border-radius: 8px;
                        font-size: 20px;
                        padding-left: 18px;
                        padding-right: 18px;
                        line-height: 36px;
                        color: #ffffff;
                        margin-right: 10px;
                    }
                }
            }
        }
    }

    .content {
        .top-blank {
            width: 100%;
            background: $color-primary;
            position: fixed;
            top: 0;
            font-family: PingFangSC-Semibold, serif;
            font-size: 34px;
            color: #FFFFFF;
            letter-spacing: 0;
            text-align: center;
            z-index: 9999;
        }

        .tap-container {
            width: 100%;
            display: flex;
            height: 92px;
            overflow: hidden;

            .lnk-tabs::-webkit-scrollbar {
                display: none
            }

            .lnk-tabs {
                overflow-x: scroll;
                white-space: nowrap;
                border-top: 1px solid #f2f2f2;
                display: flex;
                background-color: #fff;
                color: #595959;
                width: 100%;
                //z-index: 9999;

                &.marginTop {
                    margin-top: 80px;
                }

                .active {
                    color: $color-primary;
                }

                .lnk-tabs-item {
                    height: 92px;
                    line-height: 92px;
                    text-align: center;

                    .label-name {
                        width: 100%;
                        font-size: 28px;
                        margin-left: 10px;
                    }

                    .line {
                        height: 8px;
                        width: 56px;
                        border-radius: 16px 16px 0 0;
                        background-color: $color-primary;
                        box-shadow: 0 3px 8px 0 rgba(47, 105, 248, 0.63);
                        margin: -12px auto auto auto;
                    }
                }
            }
        }

        .lnk-tabs-order {
            white-space: nowrap;
            border-top: 1px solid #f2f2f2;
            display: flex;
            background-color: #F2F2F2;
            color: #595959;
            width: 100vw;
            z-index: 9999;
            padding-left: 24px;
            padding-right: 24px;

            &.marginTop {
                margin-top: 94px;
            }

            .active {
                color: #595959;
            }

            .lnk-tabs-item {
                margin-top: 24px;
                display: inline-block;
                text-align: center;

                .label-name-bg {
                    @include flex-center-center();

                    .label-name-on {
                        background: $color-primary;
                        color: #fff;
                        border-radius: 8px;
                        padding-top: 8px;
                        padding-bottom: 8px;
                        width: 95%;
                        font-size: 28px;
                    }

                    .label-name-off {
                        color: #8C8C8C;
                        border-radius: 8px;
                        padding-top: 8px;
                        padding-bottom: 8px;
                        width: 95%;
                        font-size: 28px;
                    }
                }
            }
        }
    }
    .model-title {
        display: flex;

        .title {
            font-family: PingFangSC-Regular, serif;
            font-size: 32px;
            color: #262626;
            letter-spacing: 0;
            text-align: center;
            line-height: 96px;
            height: 96px;
            width: 100%;
            padding-left: 0;
            margin-left: 80px;
        }

        .icon-close {
            color: #BFBFBF;
            font-size: 48px;
            line-height: 96px;
            height: 96px;
            margin-right: 20px;
        }
    }

    .textarea-big {
        position: relative;
        .textarea-length {
            position: absolute;
            right: 0;
            bottom: 0;
            padding: 0px 40px 16px 0px;
            color: #8C8C8C;
            font-size: 28px;
        }
        .textarea-over-length {
            position: absolute;
            right: 0;
            bottom: 0;
            padding: 0px 40px 16px 0px;
            color: #ff5a5a;
            font-size: 28px;
        }
    }
}
</style>
