<!--
@created<PERSON><PERSON>  yangying
@date  2023/08/3
@description 终端名单提报-已选中终端
-->
<template>
    <link-page class="check-terminal-report-page">
        <view class="menu-stair">
            <view class="line">
                <view class="line-top"></view>
                <view class="line-bottom"></view>
            </view>
            <view class="stair-title">活动基础信息</view>
        </view>
        <activity-item :data="pageParam.activityItem"/>

        <view class="menu-stair">
            <view class="line">
                <view class="line-top"></view>
                <view class="line-bottom"></view>
            </view>
            <view class="stair-title">终端清单</view>
        </view>
        <link-swipe-action v-for="(data, index) in terminalList" :key="index" class="terminal-wrap">
            <link-swipe-option slot="option" @tap="deleteItem(data, index)">删除</link-swipe-option>
            <terminal-list-item :data="data" :broadCompanyCode="broadCompanyCode"/>
        </link-swipe-action>

        <link-sticky>
            <link-button @tap="$nav.back()" block mode="stroke">新增</link-button>
            <link-button @tap="submit" block>提交</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
import TerminalListItem from './components/terminal-list-item';
import ActivityItem from "./components/activity-item";

export default {
    name: 'check-terminal-report-page',
    components: {TerminalListItem, ActivityItem},
    data() {
        return {
            terminalList: [],
            broadCompanyCode: '',
        }
    },
    async created() {
        this.terminalList = this.pageParam.list;
        this.broadCompanyCode = await this.$utils.getCfgProperty('getPurchaseSumForOrder');
    },
    methods: {
        /**
         * 提交
         * <AUTHOR>
         * @date	2023/8/4 9:56
         */
        async submit() {
            if (!this.terminalList.length) {
                this.$showError('至少选择一个终端！');
                return;
            }
            try {
                this.$utils.showLoading();
                const accountIds = this.terminalList.map(item => item.id);
                // const {success, newRow} = await this.$http.post('action/link/headSubmit/insert', {
                //     activityId: this.pageParam.activityItem.id,
                //     submitName: this.pageParam.activityItem.activityName
                // });
                // if (success) {
                    const data = await this.$http.post('action/link/headSubmit/submit', {
                        // id: newRow.id,
                        activityId: this.pageParam.activityItem.id,
                        accountIds: accountIds
                    });
                    if (data.success) {
                        this.$message.success('提交成功！');
                        this.$utils.hideLoading();
                        this.$nav.back(null, 2);
                    }
                // }
            } catch (e) {
                console.log('e', e);
                this.$utils.hideLoading();
            } finally {
                this.$utils.hideLoading();
            }
        },
        /**
         * 删除
         * <AUTHOR>
         * @date	2023/8/4 9:56
         */
        deleteItem(data, index) {
            // this.terminalList.splice(index, 1);
            this.pageParam.deleteItem(data, index);
        }
    }
}
</script>

<style lang="scss">
.check-terminal-report-page {
    padding: 16px 16px 0;

    .menu-stair {
        width: 100%;
        margin-left: 24px;
        padding: 24px 0;
        @include flex-start-center;

        .line {
            clear: both;

            .line-top {
                width: 8px;
                height: 16px;
                background: #3FE0E2;
            }

            .line-bottom {
                width: 8px;
                height: 16px;
                background: #2F69F8;
            }
        }

        .stair-title {
            width: 30%;
            margin-left: 16px;
            font-family: PingFangSC-Semibold, serif;
            font-size: 32px;
            color: #262626;
            letter-spacing: 1px;
            line-height: 32px;
        }

        .edit {
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #2F69F8;
            letter-spacing: 0;
            line-height: 28px;
            text-align: right;
            width: 58%;
        }
    }

    .activity-item {
        margin-bottom: 24px;
    }

    .terminal-wrap {
        width: auto;
        margin-bottom: 16px;
    }
}

</style>
