<!--
@created<PERSON><PERSON>  yangying
@date  2023/08/13
@description 终端名单提报
-->
<template>
    <link-page class="terminal-list-report-page">
        <link-checkbox-group v-model="chooseIds">
            <link-auto-list :option="terminalOption" :hideCreateButton="true"
                            :searchInputBinding="{props:{placeholder:'终端名称/客户编码/省市区(县)/详细地址'}}">
                <link-filter-group slot="filterGroup">
                    <link-filter-item label="创建时间" :param="{sort:{field:'created',desc:true}}"/>
                    <link-filter-item label="最新更新" :param="{sort:{field:'lastUpdated',desc:true}}"/>
                </link-filter-group>

                <template slot-scope="{data, index}">
                    <item :key="index" :data="data" :arrow="false" class="terminal-item-wrap" @tap="chooseItem(data)">
                        <link-checkbox :val="data.id" toggleOnClickItem slot="thumb"/>
                        <terminal-list-item :data="data" :broadCompanyCode="broadCompanyCode" slot="note"/>
                    </item>
                </template>
            </link-auto-list>
        </link-checkbox-group>

        <link-sticky>
            <link-button @tap="$nav.back()" block mode="stroke">返回</link-button>
            <link-button @tap="checkTermianl" block>保存({{chooseList.length}})</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
import TerminalListItem from './components/terminal-list-item';
export default {
    name: 'terminal-list-report-page',
    components: {TerminalListItem},
    data() {
        const userInfo = this.$taro.getStorageSync('token').result;
        const isYangShengOrYouXuan = ['1216', '1612'].includes(userInfo.coreOrganizationTile.brandCompanyCode);

        let oauth = this.$utils.isPostnOauth() === 'MY_POSTN' ? 'MULTI_POSTN' : this.$utils.isPostnOauth();
        if (oauth === 'MULTI_ORG' || oauth === 'MY_ORG' || userInfo.positionType === 'CityManager') {
            oauth = 'wxOrg';
        }
        if (oauth === 'MULTI_POSTN' || isYangShengOrYouXuan && userInfo.positionType === 'SalesAreaManager') {
            oauth = 'wxPostn';
        }

        const terminalOption = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/activityAccount/queryActivityTerminalRangePage'
            },
            sortField: ' ',
            param: () => {
                return {
                    activityId: this.pageParam.activityItem.id,
                    accountType: 'Terminal',
                    attr2: oauth,
                    filtersRaw: [
                        {id: 'accountCategory', property: 'accountCategory', value: 'qdlx-4'},
                        {id: 'multiAcctMainFlag', property: 'multiAcctMainFlag', value: 'Y'},
                        {id: 'accountStage', property: 'accountStage', value: 'ykf'},
                        {id: 'accountStatus', property: 'accountStatus', value: 'Y'}
                    ]
                }
            },
            sortOptions: null,
            searchFields: ['id', 'accountName', 'accountCode', 'province', 'city', 'district', 'address'],
            hooks: {
                beforeLoad (options) {
                    if (this.$utils.isEmpty(options.param.sort.trim())) {
                        delete options.param.order;
                        delete options.param.sort;
                    }
                },
                afterLoad (data) {
                    data.rows.map(async (item) => {
                        if (!this.$utils.isEmpty(item.storePicPreKey)) {
                            let urlData = this.$image.getSignedUrl(item.storePicPreKey);
                            this.$set(item, 'storeUrl', urlData);
                        } else {
                            this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
                        }
                    })
                }
            }
        });
        return {
            userInfo,
            terminalOption,
            isYangShengOrYouXuan,
            broadCompanyCode: '',
            chooseList: [],
            chooseIds: []
        }
    },
    async created() {
        this.broadCompanyCode = await this.$utils.getCfgProperty('getPurchaseSumForOrder');
    },
    methods: {
        /**
         * 选中/取消选中终端
         * <AUTHOR>
         * @date	2023/8/3 19:50
         */
        chooseItem(data) {
            const Idindex = this.chooseIds.findIndex((item) => item === data.id);
            if (Idindex === -1) {
                this.chooseList.push(data);
            } else {
                const index = this.chooseList.findIndex((item) => item.id === data.id);
                this.chooseList.splice(index, 1);
            }
        },
        /**
         * 查看已选择的终端
         * <AUTHOR>
         * @date	2023/8/3 19:40
         */
        checkTermianl() {
            if (!this.chooseList.length) {
                this.$showError('至少选择一个终端！');
                return;
            }
            this.$nav.push('/pages/headquarters-activity/check-terminal-report-page.vue', {
                list: this.chooseList,
                activityItem: this.pageParam.activityItem,
                // 删除选中终端
                deleteItem: (data, index) => {
                    this.chooseList.splice(index, 1);
                    const idIndex = this.chooseIds.findIndex((item) => item === data.id);
                    if (idIndex !== -1) this.chooseIds.splice(idIndex, 1);
                },
            });
        }
    }
}
</script>

<style lang="scss">
.terminal-list-report-page {
    .terminal-item-wrap {
        margin: 24px 16px;
        padding: 0 ;
        background: transparent;

        .link-item-thumb {
            padding-right: 16px;
        }
    }
}
</style>
