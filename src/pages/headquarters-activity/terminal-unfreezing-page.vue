<!--
总部活动-营销60.终端解冻
<AUTHOR>
@date 240703
-->
<template>
    <link-page class="teriminal-unfreezing-page">
        <link-radio-group v-model="selectId">
            <link-auto-list :option="teriminalList" :searchInputBinding="{props:{placeholder:'终端名称/终端编码'}}">
                <link-filter-group slot="filterGroup">
                    <link-filter-item label="更新时间（升序）" :param="{sort:{field:'lastUpdated',desc: false}}"/>
                </link-filter-group>
                <template slot-scope="{data,index}">
                    <item :key="index" :data="data" :arrow="false" class="terminal-list-item">
                        <link-checkbox :val="data.id" toggleOnClickItem slot="thumb"/>
                        <view class="terminal-list" slot="note">
                            <view class="list-cell">
                                <view class="media-list">
                                    <!--左图片-->
                                    <image class="media-list-logo" :src="data.storeUrl" @tap.stop="previewStoreUrl(data)"
                                        lazy-load="true"></image>
                                    <!--右内容-->
                                    <view class="store-content">
                                        <view class="store-content-top">
                                            <!--【客户一级分类】为“终端Terminal”的时候显示storeSigns字段-->
                                            <view class="store-title" v-if="data.acctType === 'Terminal'">{{data.accountName}}</view>
                                            <view class="store-title">{{ data.accountName }}</view>
                                            <!--【客户一级分类】为“分销商Distributor”时展示billTitle字段-->
                                            <view class="store-title" v-if="data.acctType === 'Distributor'">{{data.accountName || data.billTitle}}</view>
                                            <!--已认证-->
                                            <view class="store-level" v-if="data.accountStage === 'ykf'">
                                                <image :src="$imageAssets.storeStatusVerifiedImage"></image>
                                            </view>
                                            <!--未认证-->
                                            <view class="store-level" v-if="data.accountStage === 'xk'">
                                                <image :src="$imageAssets.storeStatusUnverifiedImage"></image>
                                            </view>
                                            <!--已失效-->
                                            <view class="store-level" v-if="data.accountStage === 'ysx'">
                                                <image :src="$imageAssets.storeStatusInvalidationImage"></image>
                                            </view>
                                            <!--潜客-->
                                            <view class="store-level" v-if="data.accountStage === 'dkf' && !isYangShengOrYouXuan">
                                                <image :src="$imageAssets.storeStatusPotentialImage"></image>
                                            </view>
                                        </view>
                                        <view class="store-content-middle">
                                            <view class="left">
                                                <view class="store-color-type" :class="getColorClass(data.fourColorLabel)" v-if="data.fourColorLabel">{{data.fourColorLabel | lov('Four_Color_Label')}}</view>
                                                <view class="store-type" v-if="data.financingFlag">贷 | {{data.financingFlag | lov('YR_FINANCING_FLAG')}}</view>
                                                <view class="store-type" v-if="data.accountType">{{ data.accountType | lov('ACCT_TYPE') }}</view>
                                                <view class="store-type" v-if="data.acctCategory">{{data.acctCategory | lov('ACCNT_CATEGORY')}}</view>
                                                <view class="store-type" v-if="data.accountLevel">{{ data.accountLevel | lov('ACCT_LEVEL') }}</view>
                                                <view class="store-type" v-if="sublist.includes(data.subAcctType)">{{data.subAcctType | lov('SUB_ACCT_TYPE')}}</view>
                                                <view class="store-type" v-if="data.strategicFlag && data.strategicFlag === 'Y'">{{data.strategicFlag | lov('STRATEGIC_TAG')}}</view>
                                                <view class="store-type" v-if="levelList.includes(data.acctLevel) || caplist.includes(data.capacityLevel)">
                                                    <text v-if="levelList.includes(data.acctLevel)">{{data.acctLevel | lov('ACCT_LEVEL')}}</text>
                                                    <text v-if="levelList.includes(data.acctLevel) && caplist.includes(data.capacityLevel)"> | </text>
                                                    <text v-if="caplist.includes(data.capacityLevel)">{{data.capacityLevel | lov('CAPACITY_LEVEL')}}</text>
                                                </view>
                                                <view class="store-type" v-if="data.joinFlag && broadCompanyCode.indexOf(userInfo.coreOrganizationTile.brandCompanyCode) !== -1">{{ data.joinFlag | lov('JOIN_FLAG') }}</view>
                                                <view class="store-type" v-if="data.judgmentFlag === 'Y'">品鉴</view>
                                                <view class="store-type" v-if="data.doorSigns !== undefined">{{ data.doorSigns | lov('DOOR_SIGNS') }}</view>
                                                <view class="store-type" v-if="data.terminalDigitization">
                                                    <text>{{data.terminalDigitization | lov('TERMINAL_DIGITIZATION')}}</text>
                                                </view>
                                                <view class="store-type" v-if="data.accntPartner && (data.salesmanAreaId === '521074619762290688' || userInfo.coreOrganizationTile.l3Id === '411091729387553190')">
                                                    <text>{{data.accntPartner | lov('ACCT_STATE')}}</text>
                                                </view>
                                                <view class="store-type" v-if="data.isSpringAct === 'Y'">春雷行动</view>
                                                <block v-if="data.displayPolicyType">
                                                    <view class="store-type"  v-for="(item, index) in data.displayPolicyType.split(',')" :key="index">{{item | lov('DISPLAY_POLICY_TYPE')}}</view>
                                                </block>
                                                <view class="store-type" v-if="data.codeMark">{{data.codeMark}}</view>
                                                <view class="store-type" v-if="data.censusLabels">{{data.censusLabels | lov('CENSUS_STATUS')}}</view>
                                            </view>
                                        </view>
                                        <view class="store-content-representative">
                                            <view class="terminal-type">编码</view>
                                            <view class="terminal-name">{{ data.accountCode }}</view>
                                        </view>
                                        <view class="store-content-address">
                                            <view class="store-address">
                                                {{ data.province }}{{ data.city }}{{ data.district }}{{ data.address }}
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </item>
                </template>
                <view slot="bottom" class="link-object-foot">
                    <link-button block @tap="approveData()" label="提交审批"/>
                </view>
            </link-auto-list>
        </link-radio-group>
    </link-page>

</template>

<script>
export default {
    name: 'teriminal-unfreezing-page',
    data() {
        const userInfo = this.$taro.getStorageSync('token').result;
        const isYangShengOrYouXuan = ['1612'].includes(userInfo.coreOrganizationTile.brandCompanyCode);
        const teriminalList = new this.AutoList(this, {
            module: 'action/link/accountApprove',
            param: {
                activeFlag: 'N',
                // mainUserId: userInfo.id, // 当前登录人id
                attr4: 'safe'
            },
            searchFields: ['accountName', 'accountCode'],
            hooks: {
                beforeLoad(option){
                    delete option.param.sort;
                    delete option.param.order;
                },
                afterLoad(data) {
                    data.rows.map(async (item) => {
                        if (!this.$utils.isEmpty(item.storePicPreKey)) {
                            let urlData = this.$image.getSignedUrl(item.storePicPreKey);
                            this.$set(item, 'storeUrl', urlData);
                        } else {
                            this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
                        }
                    })
                }

            }
        });
        return {
            teriminalList,
            typeList: [],
            categoryLst: [],
            sublist: [],
            levelList: [],
            caplist:[],
            broadCompanyCode: '', // 博大公司编码集
            isYangShengOrYouXuan,
            userInfo,
            selectId: '', // 选择的终端id
        }
    },
    async created() {
        this.broadCompanyCode = await this.queryCfgProperty('getPurchaseSumForOrder');
        this.getTypeArray();
    },
    methods: {
        async getTypeArray() {
            const list = await this.$lov.getLovByTypeArray(['ACCT_TYPE', 'ACCNT_CATEGORY', 'SUB_ACCT_TYPE','ACCT_LEVEL','CAPACITY_LEVEL']);
            list[0].forEach(item => {
                this.typeList.push(item.val)
            });
            list[1].forEach(item => {
                this.categoryLst.push(item.val)
            });
            list[2].forEach(item => {
                this.sublist.push(item.val)
            });
            list[3].forEach(item => {
                this.levelList.push(item.val)
            });
            list[4].forEach(item => {
                this.caplist.push(item.val)
            });
        },
        /**
         * 门头照片预览
         * <AUTHOR>
         * @date 2023-04-03
         * @param param
         */
        async previewStoreUrl(param) {
            if (!this.$utils.isEmpty(param.storePicKey)) {
                let imgUrl = this.$image.getSignedUrl(param.storePicKey);
                const inOptions = {
                    current: imgUrl,
                    urls: [imgUrl]
                };
                this.$image.previewImages(inOptions)
            } else {
                console.log(param.storeUrl);
                const inOptions = {
                    current: param.storeUrl,
                    urls: [param.storeUrl]
                };
                this.$image.previewImages(inOptions)
            }
        },
        /**
         * 提交审批
         * <AUTHOR>
         * @date 240703
         */
        async approveData() {
            if(!this.selectId){
                this.$message.warn('请选择终端数据')
                return
            }
            const url = '/action/link/accountApprove/submit'
            let param = {
                id: this.selectId
            }
            const res = await this.$http.post(url, param)
            if (res.success) {
                this.$message.success('提交成功!')
                this.teriminalList.methods.reload()
            } else {
                this.$message.error('提交失败：' + res.result)
            }
        },
        /**
         * 根据key值获取参数配置
         * <AUTHOR>
         * @date 8/11/21
         * @param key 参数配置健值
         */
        async queryCfgProperty(key) {
            const data = await this.$http.post('export/link/cfgProperty/queryByExamplePage', {
                filtersRaw: [{id: 'key', property: 'key', value: key}]
            })
            if (data.success && data.rows && data.rows.length) {
                return data.rows[0].value
            } else {
                return 'noMatch'
            }
        }
    }
}
</script>

<style lang="scss">
.teriminal-unfreezing-page {
    .search-container {
        padding-left: 12px;
        color: #8C8C8C;
        font-size: 28px;
        text-align: center;
    }

    .terminal-list-item {
        background: #FFFFFF;
        width: 702px;
        margin: 24px auto auto auto;
        border-radius: 16px;
    }

    /*deep*/
    .link-item {
        padding: 0;
    }

    /*deep*/
    .link-item-icon {
        width: 0;
        padding-left: 0;
    }

    /*deep*/
    .link-dropdown-content {
        padding: 24px;
    }

    .terminal-list {
        .list-cell {
            .media-list {
                @include flex;
                padding: 24px 16px 24px 24px;

                .media-list-logo {
                    /*box-shadow: 0 4px 31px 0 rgba(0,44,152,0.22);*/
                    border-radius: 16px;
                    width: 128px;
                    height: 128px;
                    overflow: hidden;
                }

                .store-content {
                    width: 80%;

                    .store-content-top {
                        @include flex-start-center;
                        @include space-between;
                        margin-left: 24px;

                        .store-title {
                            font-family: PingFangSC-Semibold, serif;
                            font-size: 32px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 36px;
                            width: 77%;
                            height: 36px;
                            overflow: hidden;
                        }

                        .store-level {
                            margin-right: -3px;
                            width: 120px;
                            height: 44px;

                            image {
                                width: 100%;
                                height: 100%;
                            }
                        }
                    }

                    .store-content-middle {
                        display: flex;
                        justify-content: space-between;
                        padding-left: 32px;

                        .left, .right {
                            @include flex-start-center;
                            flex-wrap: wrap;
                            margin-top: 10px;

                            .store-type {
                                white-space: nowrap;
                                border: 2px solid #2F69F8;
                                border-radius: 8px;
                                font-size: 20px;
                                padding-left: 18px;
                                padding-right: 18px;
                                line-height: 40px;
                                height: 40px;
                                color: #2F69F8;
                                margin-right: 10px;
                                margin-top: 10px;
                            }
                        }
                    }

                    .store-content-representative {
                        @include flex;
                        margin-left: 24px;
                        margin-top: 20px;
                        width: calc(100% - 24px);
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;

                        .terminal-type {
                            color: #8C8C8C;
                            min-width: 50px;

                        }

                        .terminal-name {
                            font-family: PingFangSC-Regular, serif;
                            font-size: 24px;
                            color: #000000;
                            letter-spacing: 0;
                            padding-left: 8px;
                            width: calc(100% - 50px);
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                        }
                    }

                    .store-content-address {
                        margin-left: 24px;
                        margin-top: 18px;
                        font-family: PingFangSC-Regular, serif;
                        font-size: 24px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 32px;
                    }
                }
            }
        }
        .link-object-foot{
            padding: 0;
            .link-object-foot-btn {
                font-size: 24px;
                display: flex;
                align-items: center;
                padding-top: 24px;
                padding-left: 36px;
                .link-checkbox{
                    margin-right: 0;
                }
                text{
                    line-height: 40px;
                }
            }
        }
    }
}

</style>
