<!--
总部活动-登记列表
<AUTHOR>
@date 2023-03-31
-->
<template>
    <link-page class="headquarters-register-list-page">
        <link-auto-list :option="registerList" :searchInputBinding="{props:{placeholder: '登记名称/登记编码/登记人'}}">
            <link-filter-group slot="filterGroup">
                <link-filter-item label="更新时间(升序)" :param="{sort:{field:'lastUpdated',desc:false}}"/>
            </link-filter-group>
            <template slot-scope="{data,index}">
                <link-swipe-action :key="index">
                    <link-swipe-option slot="option" @tap="endStage(data,index)">作废</link-swipe-option>
                    <item :key="index" :data="data" :arrow="false" class="headquarters-register-list-item"
                          @tap="gotoRegisterDetail(data)">
                        <view slot="note">
                            <view class="media-list">
                                <view class="media-top">
                                    <!-- 活动编码 -->
                                    <view class="num-view" @longPress="copyActCode(data.feedbackCode)">
                                        <view class="num">{{ data.feedbackCode }}</view>
                                    </view>
                                    <!-- 活动状态标识 -->
                                    <status-button :label="data.status| lov('HEAD_FEEDBACK_STATUS')"></status-button>
                                </view>
                            </view>
                            <!-- 登记名称 -->
                            <view class="content-middle">
                                <view class="name">{{data.feedbackName ? data.feedbackName.length > 20 ? data.feedbackName.substring(0,20) + '...' : data.feedbackName : ''}}</view>
                            </view>
                            <view class="content-middle-line">
                                <view class="data">
                                    <view class="title">登记人</view>
                                    <view class="val">{{data.creator}}</view>
                                </view>
                                <view class="sum">
                                    <view class="title">包间号</view>
                                    <view class="val">{{data.privateRoom ? data.privateRoom.length > 4 ? data.privateRoom.substring(0,4) + '...' : data.privateRoom : ''}}</view>
                                </view>
                            </view>
                            <view class="content-middle-line">
                                <view class="data">
                                    <view class="title">活动日期</view>
                                    <view class="val">{{data.activityDate ? data.activityDate.substr(0,10) : data.activityDate}}</view>
                                </view>
                                <!-- 240328迭代去除活动时段 -->
                                <!-- <view class="sum">
                                    <view class="title">活动时段</view>
                                    <view class="val">{{data.feedbackStage | lov('HEAD_FEEDBACK_TIME')}}</view>
                                </view> -->
                            </view>
                            <view class="content-middle-line">
                                <view class="data">
                                    <view class="title">开瓶数量</view>
                                    <view class="val">{{data.productNumber}}</view>
                                </view>
                                <view class="sum">
                                    <view class="title">扫码数量</view>
                                    <view class="val">{{data.scanNumber}}</view>
                                </view>
                            </view>
                            <!--                        <view class="content-middle-line">-->
                            <!--                            <view class="data">-->
                            <!--                                <view class="title">更新时间</view>-->
                            <!--                                <view class="val">{{ data.lastUpdated|date('YYYY-MM-DD') }}</view>-->
                            <!--                            </view>-->
                            <!--                        </view>-->
                        </view>
                    </item>
                </link-swipe-action>
            </template>
        </link-auto-list>
        <link-fab-group>
            <link-fab-item icon="icon-fenxiang" label="一键转发" @tap-icon="()=>clickForward()"/>
            <link-fab-item icon="icon-plus" label="新建登记" v-if="activityStatus === 'Processing'" @tap-icon="()=>clickAdd()"/>
            <link-fab-item icon="icon-tibao" label="名单提报" @tap-icon="()=>clickReport()"
                           v-if="['Processing', 'New'].includes(activityStatus) && pageParam.data.approveStatus === 'Approved'"/>
        </link-fab-group>
    </link-page>
</template>

<script>
import StatusButton from "../lzlj/components/status-button.vue";
import {ComponentUtils} from "link-taro-component";
import {ROW_STATUS} from "../../utils/constant";
import Taro from "@tarojs/taro";

export default {
    name: "headquarters-register-list-page",
    components: {StatusButton},
    data() {
        // 登记列表
        const registerList = new this.AutoList(this, {
            module: 'action/link/headquarterFeedback',
            url: {
                queryByExamplePage: 'export/link/headquarterFeedback/queryByExamplePage',
            },
            param: {
                activityId: this.pageParam.data.id,
                oauth: this.pageParam.oauth
            },
            searchFields: ['feedbackName', 'feedbackCode', 'creator'],
            sortField: 'created',
            sortDesc: 'desc',
            sortOptions: null,
            filterOption:[
                {label: '登记状态', field: 'status', type: 'lov',lov: 'HEAD_FEEDBACK_STATUS',
                    lovOption: {excludeLovs: ['Abnormal', 'Processing', 'End', 'Closed']}},
                {label: '活动日期', field: 'activityDate', type: 'date'},
                {label: '活动时段', field: 'feedbackStage', type: 'lov', lov: 'HEAD_FEEDBACK_TIME'}
            ]
        });
        const userInfo = Taro.getStorageSync('token').result;
        return {
            headActStatusActive: {}, // 头部tab栏选中项
            hideCreateButton: true, // 是否隐藏新建按钮
            registerList, // 登记列表
            currentActType: '', // 当前活动类型
            currentActId: '', // 当前活动id
            currentRegRequest: '', // 当前登记要求
            editFlag: false, // 是否可编辑
            activityStatus: '', // 活动状态
            userInfo // 用户信息
        }
    },
    async created() {
        await this.queryHeadInfo();
        this.$bus.$on('refreshRegList', async (id) => {
            if(id) {
                try {
                    this.$utils.showLoading();
                    const index = this.registerList.list.findIndex(i => i.id === id)
                    const data = await this.$http.post('action/link/headquarterFeedback/queryById', {id})
                    const row = data.result
                    if (index === -1) {
                        this.registerList.list.unshift(row)
                    } else {
                        this.registerList.list.splice(index, 1, row)
                    }
                } catch (e) {
                    this.$utils.hideLoading();
                } finally {
                    this.$utils.hideLoading();
                }
            }
        })
    },
    methods: {
        /**
         * 名单提报
         * <AUTHOR>
         * @date	2023/8/3 15:11
         */
        clickReport() {
            this.$nav.push('/pages/headquarters-activity/terminal-list-report-page.vue', {
                activityId: this.currentActId,
                activityItem: this.pageParam.data
            })
        },
        /**
         * 查询总部活动信息
         * <AUTHOR>
         * @date 2023-04-18
         */
        async queryHeadInfo() {
            try {
                const data = await this.$http.post('action/link/headquarterActivity/queryById', {
                    id: this.pageParam.data.id,
                });
                if (data.success) {
                    this.currentActId = data.result.id;
                    this.currentActType = data.result.activityType;
                    this.currentRegRequest = data.result.registerRequest;
                    this.activityStatus = data.result.activityStatus;
                } else {
                    this.$showError('查询总部活动信息失败，稍候再试！');
                }
            } catch (e) {
                this.$showError('查询总部活动信息失败，请检查网络，稍候再试！');
            }
        },

        /**
         * 点击新建登记
         * <AUTHOR>
         * @date 2023-04-07
         */
        async clickAdd() {
            this.$nav.push('/pages/headquarters-activity/headquarters-register-upsert-page.vue', {
                activityId: this.currentActId,
                activityType: this.currentActType,
                activityName: this.pageParam.data.activityName,
                registerRequest: this.currentRegRequest,
                sourceFlag: 'regListDetail',
                row_status: ROW_STATUS.NEW,
            })
        },
        /**
         * 点击一键转发
         * <AUTHOR>
         * @date 2023-09-12
         */
        async clickForward() {
            this.$nav.push('/pages/headquarters-activity/headquarters-forward-contact-page.vue', {
                headRegFlag: true,
                activityId: this.currentActId,
                activityName: this.pageParam.data.activityName,
                registerRequest: this.currentRegRequest
            })
        },
        /**
         * 跳转登记详情
         * <AUTHOR>
         * @date 2023-04-04
         */
        gotoRegisterDetail(data) {
            if(data.postnId === this.userInfo.postnId && data.createdBy === this.userInfo.id) {
                this.editFlag = true;
            }
            if(data.status === 'New' && this.editFlag) {
                this.$nav.push('/pages/headquarters-activity/headquarters-register-upsert-page.vue', {
                    data: data,
                    id: data.id,
                    feedbackCode: data.feedbackCode,
                    activityId: this.currentActId,
                    activityType: this.currentActType,
                    activityName: this.pageParam.data.activityName,
                    registerRequest: this.currentRegRequest,
                    sourceFlag: 'regListEdit',
                    row_status: ROW_STATUS.UPDATE,
                })
            } else {
                this.$nav.push('/pages/headquarters-activity/headquarters-register-detail-page.vue', {
                    data: data,
                    feedbackId: data.id,
                    activityId: this.currentActId,
                    activityType: this.currentActType,
                    activityStatus: this.activityStatus,
                    activityName: this.pageParam.data.activityName,
                    registerRequest: this.currentRegRequest,
                    sourceFlag: 'headRegDetail',
                })
            }
        },

        /**
         * desc 作废
         * <AUTHOR>
         * @date 2023-05-24
         */
        async endStage(data,index) {
            if(data.status === 'New') {
                this.$taro.showModal({
                    title: '提示',
                    content: '登记作废不可逆转，是否确定作废？',
                    success: async (res) => {
                        if (res.confirm) {
                            await this.endCurrentStage(data, index);
                        }
                    }
                });
            } else {
                this.$taro.showModal({
                    title: '提示！',
                    content: '仅新建状态下可作废！',
                    showCancel: false
                });
            }

        },
        async endCurrentStage(data, index) {
            try{
                const reg = await this.$http.post('action/link/headquarterFeedback/feedbackDiscard', {
                    id: data.id,
                })
                if(reg.success) {
                    this.$set(data, 'status', 'Inactive');
                    this.$message.success('作废成功！');
                    this.registerList.methods.reload();// 刷新登记列表
                } else {
                    this.$message.warn('作废失败，请稍后重试！');
                }
            } catch (e) {
                this.$showError('作废失败，请稍后重试！');
            }
        },

        /**
         * 复制活动编码
         *  <AUTHOR>
         *  @date 2023-03-28
         */
        copyActCode(text) {
            wx.setClipboardData({data: text});
        },

        /**
         * 监控返回函数
         * <AUTHOR>
         * @date 2020-09-15
         * @param param
         */
        onBack(param) {
            if (this.$utils.isEmpty(param)) {
                return
            }
            if (param.refreshFlag) {
                this.registerList.methods.reload();
            }
        },
        /**
         * 返回上一页
         * <AUTHOR>
         * @date 2020-10-19
         * @param data 返回携带终端数据
         */
        goBack(data) {
            this.$nav.back()
        },
    }
}

</script>

<style lang="scss">
@import "../../styles/list-card";

.headquarters-register-list-page {
    background-color: #F2F2F2;
    font-family: PingFangSC-Regular;

    .link-input {
        width: 100%;
    }
    .link-card-list{
        padding: 0;
    }
    .link-card-list .link-swipe-option-container .link-swipe-option {
        font-size: 28px !important;
        height: 80px !important;

    }
    .link-swipe-action .link-swipe-option-container .link-swipe-option {
        margin-top: 12px;
        height: 90%;
    }
    /*deep*/
    .lnk-tabs-container {
        height: 92px;
    }

    .headquarters-register-list-item {
        background: #FFFFFF;
        width: 95%;
        margin: 24px auto auto auto;
        border-radius: 16px;

        .media-list {
            @include media-list;

            .media-top {
                width: 100%;
                @include flex-start-center;
                @include space-between;
                height: 80px;
                line-height: 80px;

                .left-content {
                    font-family: PingFangSC-Semibold;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 32px;
                    padding-top: 20px;

                }

                .right-content {
                    font-family: PingFangSC-Semibold;
                    font-size: 32px;
                    color: #FF5A5A;
                    letter-spacing: 0;
                    text-align: right;
                    line-height: 32px;
                    padding-top: 20px;
                }

                .num-view {
                    background: #A6B4C7;
                    border-radius: 8px;
                    line-height: 50px;

                    .num {
                        font-size: 28px;
                        color: #FFFFFF;
                        letter-spacing: 0;
                        line-height: 40px;
                        padding: 2px 8px;
                    }
                }

                .status-view {
                    width: 120px;
                    transform: skewX(-10deg);
                    border-radius: 4px;
                    background: #2F69F8;
                    box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                    height: 36px;

                    .status {
                        font-size: 20px;
                        color: #FFFFFF;
                        letter-spacing: 2px;
                        text-align: center;
                        line-height: 36px;
                    }
                }
            }
        }

        .content-middle {
            width: 100%;
            @include flex-start-center;
            @include space-between;
            height: 80px;
            line-height: 80px;

            .content {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
            }

            .name {
                font-family: PingFangSC-Semibold;
                font-size: 32px;
                color: #262626;
                letter-spacing: 0;
                line-height: 32px;
            }
        }

        .content-middle-line {
            width: 100%;

            .data {
                width: 58%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    width: 35%;
                    float: left;
                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                }

                .Submitted, .Feedback {
                    color: #2F69F8;
                }

                .Approve, .FeedbackApro {
                    color: #2EB3C2;
                }

                .Refused, .Refeedback {
                    color: #FF5A5A;
                }

            }

            .sum {
                width: 42%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    float: left;
                    width: 45%;
                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                    white-space: nowrap;
                }
            }

            .sum-2 {
                width: 58%;
                float: left;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 56px;
                    float: left;
                    margin-right: 24px;
                }

                .val {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 56px;
                }
            }
        }
    }
}
</style>
