<!--
总部活动-百城阵地建设新增
<AUTHOR>
@date 2023-08-03
-->
<template>
	<link-page class="special-apply-page">
		<link-form :value="formData" :rules="rules" ref="upsertConstruct">
		    <link-form-item label="活动主题" field="actType">
				<link-lov type="HEAD_ACT_TYPE" v-model="formData.actType" disabled></link-lov>
			</link-form-item>
			<link-form-item label="活动类型" field="actConstructionType" >
			     <link-lov type="HEAD_ACT_CON_TYPE" v-model="formData.actConstructionType" disabled></link-lov>
			</link-form-item>
			<link-form-item label="活动开始时间" field="startTime" required>
			    <link-date view="YMDHms" v-model="formData.startTime" value-format="YYYY-MM-DD HH:mm:ss" display-format="YYYY-MM-DD HH:mm:ss">
			    </link-date>
			</link-form-item>
			<link-form-item label="活动结束时间" field="endTime">
			    <link-date view="YMDHms" v-model="formData.endTime" disabled value-format="YYYY-MM-DD HH:mm:ss" display-format="YYYY-MM-DD HH:mm:ss">
			    </link-date>
			</link-form-item>
			<link-form-item label="活动状态" field="actStatus">
				{{formData.actStatus | lov('HEAD_ACT_STATUS')}}
			<!-- <link-lov type="HEAD_ACT_STATUS" v-model="formData.actStatus" disabled></link-lov> -->
			</link-form-item>
			<!-- <link-form-item label="审批状态" field="approveStatus">
				 <link-lov type="HEAD_APRO_STATUS" v-model="formData.approveStatus"></link-lov>
			</link-form-item> -->
			<link-form-item label="提报人" field="feedbackName">
			    <link-input v-model="userInfo.firstName" disabled/>
			</link-form-item>
			<link-form-item label="所属城市" field="cityName" required>
			    <link-address :province.sync="formData.provinceName"  :city.sync="formData.cityName"
					@pick-province='handlerPickProvince'
		 		 	@pick-city='handlerPickCity' view="pc"/>
			</link-form-item>
			<link-form-item label="建设类型" field="actConstructionSubtype" required  v-if="!hideThree">
				<!-- <view class="newtss">
					<link-icon icon="mp-info" status="info" style="color:#2f69f8"/>
					<text>店内升级需双3A及以上,店招门头需3A+A及以上</text>
				</view> -->
				<link-lov type="HEAD_ACT_CON_SUBTYPE" v-model="formData.actConstructionSubtype" :disabled="editBasicFlag"></link-lov>
			</link-form-item>
			<!-- <link-form-item label="门头制作日期" field="signDate" required>
			    <link-date view="YMDHms"
			               v-model="formData.signDate"
			               value-format="YYYY-MM-DD HH:mm:ss"
			               display-format="YYYY-MM-DD HH:mm:ss">
			    </link-date>
			</link-form-item> -->
			<link-form-item label="申请费用金额（元）" field="applyAmount" required >
				<view class="newts">
					<link-icon icon="mp-info" status="info" style="color:#2f69f8"/>
					<text>{{!hideThree ? '店招单店不超过2万元，店内升级单店不超过1万元，以上据实际情况规划' : '店招单店不超过2万元，根据实际情况规划'}}</text>
				</view>
				<link-number v-model="formData.applyAmount" :min="0"  hideButton :precision='2' />
			</link-form-item>
			<link-form-item label="费用申请说明" field="remark" vertical required>
				<view slot="title" class="label">
				    <view class="label-text">费用申请说明</view>
					<view class="label-mp-info">
						<link-icon icon="mp-info" status="info"  style="color:#2f69f8"/>
						<text>需对每个终端费用明细金额进行说明，以终端名称+金额说明</text>
					</view>
				</view>
                <link-textarea placeholder="请填写提报终端的每个费用明细金额说明，如XX烟酒店+10000，300字以内" padding-start padding-end v-model="formData.remark" mode='textarea'></link-textarea>
			</link-form-item>
		</link-form>
		<view>
			<view class="terminal-title">
				<view>终端名单(已添加数量{{terminalNum}})</view>
				<view @tap='addTerminal' class="terminal-title-add">添加</view>
			</view>
			<list v-for="(item,index) in terminal" :key="index+'terminal'">
				<link-swipe-action>
					<item class="construct-terminal-list-item" :arrow="false">
							<view slot="note">
								<terminal-item :item='item' :hideThree="hideThree"></terminal-item>
							</view>
					</item>
					<link-swipe-option slot="option" @tap="delTerminal(item,index)">移除</link-swipe-option>
				</link-swipe-action>
			</list>
		</view>
		<link-sticky>
		    <link-button block @tap="save">保存</link-button>
		    <link-button block @tap="submit">提交</link-button>
		</link-sticky>
	</link-page>
</template>

<script>
	import Taro from "@tarojs/taro";
	import {DateService} from "link-taro-component";
	// import {reverseTMapGeocoder} from "../../utils/locations-tencent";
	import terminalItem from './components/terminal-item'
	import {PageCacheManager} from "../../utils/PageCacheManager";
	export default {
		name:'construct-special-apply-page',
		components:{
			terminalItem,
		},
		data() {
			const startTime = DateService.format(new Date(),'YYYY-MM-DD HH:mm:ss')
			const formData = {
					startTime,
					endTime: '2023-09-18 23:59:59', //结束时间默认09/18
					actType: 'TerminalConstruction', //主题默认百城
					actStatus: 'New' //状态默认为未开始
			}
			const initialData = {
                formData,
				terminal: [],
				userInfo:Taro.getStorageSync('token').result,// 获取用户信息
				rules: {}
            };
			const cacheData = PageCacheManager.getInitialData({
				ctx: this,
				path: 'headquarters-activity/construct-special-apply-page.vue',
				title: "百城阵地建设专项申请",
				initialData,
			});
			const masterOption = new this.AutoList(this, {
			    module:  'action/link/simpleTerminalInfo',
			    param: {
			        rows: 25,
			        filtersRaw: [],
					multiAcctMainId: null,
					oauth: "MULTI_POSTN",
					page: 1,
					attr1:'needLevel'
			    },
				filterOption: [
                        {label: '终端名称', field: 'acctName', type: 'text'},
						{label: '终端编码', field: 'acctCode', type: 'text'}
				],
			    sortOptions: null,
			    searchFields: ['acctName', 'acctCode'],
			    hooks: {
			        beforeLoad (option) {
						if(!this.hideThree){
							option.param.attr1='needLevel'
							option.param.attr2 = null
						}else{
							option.param.attr1 = null
							option.param.attr2 = 'doorNotNull'
						}
			        }
			    },
			    renderFunc:(h, {data,index}) => {
					if (!this.$utils.isEmpty(data.smallUrl)) {
						let urlData = this.$image.getSignedUrl(data.smallUrl);
						this.$set(data, 'storeUrl', urlData);
					}else{
						this.$set(data, 'storeUrl', this.$imageAssets.terminalDefaultImage);
					}
					let arr = this.terminal.map(i=>{
						return i.acctCode
					})
					let newArr = (data.acctLevel || '').split('|')
					data.chose =  arr.includes(data.acctCode) ? 'Y'	: 'N'
					return (
						<item key={index} data={data} arrow={false}>
                            <view slot="note">
							<view  style="padding: 26rpx;display:flex;justify-content: space-around;">
								<view>
									<image src={data.storeUrl} style='border-radius: 16rpx; width: 128rpx; height: 128rpx; overflow: hidden;' lazyLoad="true"></image>
									<view style='text-algin:center;' vShow={data.chose==='Y'}>
										<link-checkbox vModel={data.chose} trueValue="Y" falseValue="N" disabled></link-checkbox>
										<text slot="title">已选</text>
									</view>
								</view>
								<view style='font-size: 26rpx;line-height: 32rpx; color: #000000;font-family: PingFangSC-Regular,serif;letter-spacing: 0;padding-left: 30rpx;margin: 2px 0;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;'>
									<view style='font-family: PingFangSC-Semibold,serif; font-size: 30rpx;color: #262626;letter-spacing: 0;line-height: 18px;height: 18px; overflow: hidden;'> {data.acctName} </view>
									<view vShow={!this.hideThree&&data.acctLevel} style='display:flex;flex-wrap: wrap;padding: 5rpx 0;color: #2f69f8;'>
										{...this.renderChildren(newArr)}
                                        <view vShow={!this.hideThree && data.strategicFlag} style='white-space: nowrap; border: 2rpx solid #2F69F8;border-radius: 8rpx;font-size: 26rpx; padding-left: 18rpx;padding-right: 18rpx;line-height: 40rpx;height: 40rpx; color: #2F69F8;margin-right: 10rpx;margin-top: 10rpx;'>
                                            战略零售商
                                        </view>
									</view>
									<view vShow={this.hideThree && data.strategicFlag} style='display:flex;flex-wrap: wrap;padding: 5rpx 0;color: #2f69f8;'>
										<view style='white-space: nowrap; border: 2rpx solid #2F69F8;border-radius: 8rpx;font-size: 26rpx; padding-left: 18rpx;padding-right: 18rpx;line-height: 40rpx;height: 40rpx; color: #2F69F8;margin-right: 10rpx;margin-top: 10rpx;'>
											战略零售商
										</view>
									</view>
									<view style='font-size: 26rpx;marginTop:10rpx'>
										<text style='color: #8C8C8C;'>编码:</text>{data.acctCode}
									</view>
									<view style='font-size: 26rpx;marginTop:10rpx;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;'>
										<text style='color: #8C8C8C;'>业代:</text>{data.allEmp}
									</view>
									<view style='font-size: 26rpx;marginTop:10rpx'>
										<text style='color: #8C8C8C;'>店招门头:</text>{this.$lov.filter(data.doorSings, 'DOOR_SIGNS')}
									</view>
									<view style='font-size: 26rpx;marginTop:10rpx'>
										<text style='color: #8C8C8C;'>税号:</text>{data.creditNo}
									</view>
									<view style='font-size: 26rpx;marginTop:10rpx'>
										<text style='color: #8C8C8C;'>是否连锁终端:</text>{this.$lov.filter(data.chainStoreFlag, 'IS_FLAG')}
									</view>
									<view style='font-size: 26rpx;marginTop:10rpx;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;'>{data.addrDetailAddr}</view>
									<view vShow={this.hideThree&&data.agreementId} style='color:#2f69f8'>查看终端门头协议</view>
								</view>
								<view>
									{this.renderChild(data.acctStage)}
								</view>
							</view>
							</view>
						</item>
					)
			    },
			    slots: {
			        filterGroup: () => (
			            <link-filter-group>
			                <link-filter-item label="创建时间" param={{sort: {field: 'created', desc: false}}}/>
			                <link-filter-item label="最近更新" param={{sort: {field: 'lastUpdated', desc: false}}}/>
			            </link-filter-group>
			        )
			    }
			});
			return {
				hideThree:true, //是否3年以上
				editBasicFlag:false, //是否可编辑
				masterOption, //查询终端列表配置项
				...cacheData
			}
		},
		computed:{
			terminalNum(){
				return this.terminal?.length
			}
		},
		async created() {
			this.hideThree = Boolean(this.pageParam.val === 1)
			const type = !this.hideThree ? "IntegratedTerminalConstruction" : "ReplacingStoreSignage"
			this.$set(this.formData, 'actConstructionType', type)
		},
		async mounted(){
			if(this.pageParam.data){
				this.formData = this.pageParam.data
				this.terminal = this.pageParam.terminal || []
				// console.log(this.pageParam,'pageParam')
				// this.getDetail()
			}else{
				const endTime = await this.$utils.getCfgProperty('Hundred_City_Build_End_Time') //获取配置的活动默认结束时间
				this.formData.endTime = endTime
				// const addr = await this.$locations.getCurrentCoordinate()
				// if(addr.errMsg != "getLocation:ok")return
				// const addrs =  await reverseTMapGeocoder(addr.latitude, addr.longitude);
				// if(addrs.status === 0){
				// 	let { province, city } =addrs.result.address_component
				// 	let { adcode } = addrs.result.ad_info
				// 	this.$set(this.formData,'cityName',city)
				// 	this.$set(this.formData,'provinceName',province)
				// 	this.$set(this.formData,'cityCode',adcode)
				// }
			}
		},
		methods:{
			/**
			* 选择省份
			* <AUTHOR>
			* @date 2023-08-11
			* @param param
			*/
			handlerPickProvince(data){
				console.log(data,'handlerPickProvince')
			},
			/**
			* 选择市编码
			* <AUTHOR>
			* @date 2023-08-11
			* @param param
			*/
			handlerPickCity(data){
				console.log(data,'handlerPickProvince')
				this.formData.cityCode = data.cityCode
			},
			/**
			* 移除
			* <AUTHOR>
			* @date 2023-08-11
			* @param param
			*/
			delTerminal(item,index){
				this.terminal.splice(index,1)
			},
			/**
			* 提交
			* <AUTHOR>
			* @date 2023-08-03
			* @param param
			*/
			async submit(){
				try{
					await this.$refs.upsertConstruct.validate();
					if(this.formData.startTime > this.formData.endTime) {
						this.$message.warn('开始时间不能大于结束时间！');
						return Promise.reject('开始时间不能大于结束时间！');
					}
					let {success} = await this.$http.post('action/link/hundredCityBuild/hundredCityBuildSubmit', {
						...this.formData,
						terminalNumber:this.terminalNum,
						accountList:this.terminal,
					});
					if(success){
						this.$message.success('提交成功！');
						this.$bus.$emit('refreshConstructList');  // 刷新终端列表
						this.goBack()
					}else{
						this.$message.warn('提交失败！');
					}
				}catch(e){
					//TODO handle the exception
				}
			},
			/**
			* 获取详情
			* <AUTHOR>
			* @date 2023-08-08
			* @param param
			*/
			async getDetail(){
				try{
				   let {rows,success} = await this.$http.post('action/link/simpleTerminalInfo/queryHundredCityBuildDetail', {actNum: this.pageParam.data.actNum});
					if(success){
						rows.forEach(async (item) => {
						    if (!this.$utils.isEmpty(item.smallUrl)) {
						        let urlData = await this.$image.getSignedUrl(item.smallUrl);
						        this.$set(item, 'storeUrl', urlData);
						    } else {
						        this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
						    }
						})
						this.terminal = rows
					}
				} catch(e) {
					console.log(e)
				}
			},
			/**
			* 终端达成等级
			* <AUTHOR>
			* @date 2023-08-16
			* @param param
			*/
			renderChildren(arr){
				let newArr = arr.map((i,j)=>{
					return  <view style='white-space: nowrap; border: 2rpx solid #2F69F8;border-radius: 8rpx;font-size: 26rpx; padding-left: 18rpx;padding-right: 18rpx;line-height: 40rpx;height: 40rpx; color: #2F69F8;margin-right: 10rpx;margin-top: 10rpx;'>
								{i}
							</view>
				})
				return newArr
			},
			/**
			* 终端认证状态
			* <AUTHOR>
			* @date 2023-08-03
			* @param param
			*/
			renderChild(status){
				switch (status) {
					case 'Y':
						return <view style=" width: 120rpx;  height: 44rpx;"><image style=' width: 100%;height: 100%;' src={this.$imageAssets.storeStatusVerifiedImage}></image></view>
					case 'xk':
						return <view style=" width: 120rpx;  height: 44rpx;"><image style=' width: 100%;height: 100%;' src={this.$imageAssets.storeStatusUnverifiedImage}></image></view>
					case 'ysx':
						return <view style=" width: 120rpx;  height: 44rpx;"><image style=' width: 100%;height: 100%;' src={this.$imageAssets.storeStatusInvalidationImage}></image></view>
					case 'dkf':
						return <view style=" width: 120rpx;  height: 44rpx;"><image style=' width: 100%;height: 100%;' src={this.$imageAssets.storeStatusPotentialImage}></image></view>
					default:
						return <view style=" width: 120rpx;  height: 44rpx;"><image style=' width: 100%;height: 100%;' src={this.$imageAssets.storeStatusVerifiedImage}></image></view>
				}
			},
         /**
         * 添加终端
         * <AUTHOR>
         * @date 2023-08-03
         * @param param
         */
            async addTerminal(){
                 const MasterData = await this.$object(this.masterOption, {multiple: false, pageTitle: '终端列表'});
				 const choseTerminal = this.terminal.map(i=>{
					return i.acctCode
				 })
				 if(!choseTerminal.includes(MasterData.acctCode)){
					this.terminal.push(MasterData)
				 }
            },
         /**
         * 保存
         * <AUTHOR>
         * @date 2023-08-03
         * @param param
         */
            async save(){
				try{
					let {success} = await this.$http.post('action/link/hundredCityBuild/hundredCityBuildSave', {
						...this.formData,
						terminalNumber:this.terminalNum,
						accountList:this.terminal,
					});
					if(success){
						this.$message.success('保存成功');
						this.$bus.$emit('refreshConstructList');  // 刷新终端列表
						this.goBack()
					}else{
						this.$message.warn('保存失败！');
					}
				}catch(e){
					//TODO handle the exception
				}

			},
			/**
			 * desc 返回列表
			 * <AUTHOR>
			 * @date 2023/08/09
			 */
			async goBack() {
				if(this.pageParam.data){
					this.$nav.back(null,2);
				}else{
					this.$nav.back();
				}
			}

		}
	}
</script>
<style lang="scss">
    @mixin label-position($top:73px){
        position: absolute;
        left: 30px;
        top: $top;
        color:#2f69f8;
        font-size: 18px
    }
	.special-apply-page{
		background-color: #F2F2F2;
		font-family: PingFangSC-Regular;
		margin-bottom: 200px;
		.newtss{
            @include label-position
		}
		.newts{
            @include label-position(75px)
		}
		.label{
			@include flex;
			align-items: center;
			&-mp-info{
				@include flex;
				align-items: center;
				color:#2f69f8;
				font-size: 18px
			}
		}
		.next-input-item{
			display: inline-block;
			background-color: white;
			.link-input-content{
				width: 100%;
			}
			input{
				text-align: right;
			}
		}
		.terminal-title{
			@include flex;
			@include space-between;
			background-color:white;
			margin:10px;
			padding:35px;
			font-size: 24px;
			&-add{
				font-size: 32px;
				color: #2f69f8;
			}
		}
		.construct-terminal-list-item{
			margin: 15px;
			border-radius:16px;
			.construct-terminal-list{
				margin: 0;
				padding: 0;
			}
		}

	}
</style>
