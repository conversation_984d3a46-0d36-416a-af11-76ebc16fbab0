<!--
总部活动-百城阵地建设列表
<AUTHOR>
@date 2023-08-02
-->
<template>
	<link-page class="special-list-page">
		<lnk-taps :taps="headTapOptions" v-model="headActStatusActive" @switchTab="switchTab"></lnk-taps>
		<link-auto-list :option="option">
			<template v-slot:other v-if="hideCreateButton">
				<link-fab-group>
					<link-fab-item icon="icon-plus" label="店招换新(超三年)" v-if="addOver" @tap-icon="()=>goCreate(1)" />
					<link-fab-item icon="icon-plus" label="形象阵地建设" v-if="addIn" @tap-icon="()=>goCreate(2)" />
				</link-fab-group>
			</template>
			<template v-slot="{data,index}">
				<item :arrow="false" :data="data" :key="index+'list'" class="special-list-item" @tap='gotoDetail(data)'>
					<view slot="note">
						<view class="media-list">
							<view class="media-top">
								<!-- 编码 -->
								<view class="num-view" @longPress="copyActCode(data.actNum)">
									<view class="num">{{data.actNum}}</view>
								</view>

								<!-- 状态标识 -->
								<status-button :label="data.approveStatus | lov('HEAD_APRO_STATUS')"></status-button>
							</view>
						</view>
						<!-- 名称 -->
						<view class="content-middle">
							<view class="name">{{ data.actName ? data.actName : '百城形象阵地建设专项'}}</view>
						</view>
						<!--活动主题-->
						<view class="content-middle-line">
							<view class="data">
								<view class="title">活动状态</view>
								<view class="val">{{ data.actStatus | lov('HEAD_ACT_STATUS') }}</view>
							</view>
							<view class="sum">
								<view class="title">活动类型</view>
								<view class="val">{{ data.actConstructionType | lov('HEAD_ACT_CON_TYPE') }}</view>
							</view>
						</view>
						<view class="content-middle-line">
							<view class="data">
								<view class="title">开始时间</view>
								<view class="val">{{data.startTime|date('YYYY-MM-DD')}}</view>
							</view>
							<view class="sum">
								<view class="title">结束时间</view>
								<view class="val">{{data.endTime|date('YYYY-MM-DD')}}</view>
							</view>
						</view>
						<view class="content-middle-line">
							<view class="data">
								<view class="title">申请金额</view>
								<view class="val">{{data.applyAmount}}</view>
							</view>
						</view>
					</view>
				</item>
			</template>
		</link-auto-list>
	</link-page>
</template>

<script>
	import Taro from "@tarojs/taro";
	import LnkTaps from "../core/lnk-taps/lnk-taps";
	import StatusButton from "../lzlj/components/status-button";
	import {getFeature, getSecurityFeatures, parseFeature} from "../../utils/security";
	export default {
		name: "hconstrust-special-list-page",
		components: {
			LnkTaps,
			StatusButton
		},
		data() {
			const userInfo = Taro.getStorageSync('token').result; 
			let param ={
				filtersRaw:[]
			}
			if(!['Salesman','SalesSupervisor','CityCoordinator'].includes(userInfo.positionType)){
				param.oauth = 'MY_ORG'
			}
			const option = new this.AutoList(this, {
				module: 'action/link/hundredCityBuild',
				searchFields: ['actNum', 'actName','startTime','endTime','actType','applyAmount'],
				sortOptions: [],
				param,				
				filterOption: [{
						label: '活动类型',
						field: 'actConstructionType',
						type: 'lov',
						lov: 'HEAD_ACT_CON_TYPE',
						multiple: false,
					},
					{
						label: '建设类型',
						field: 'actConstructionSubtype',
						type: 'lov',
						lov: 'HEAD_ACT_CON_SUBTYPE',
					},
				],
			});
			const headTapOptions = [{
					name: '全部',
					seq: '1',
					val: 'All'
				},
				{
					name: '草稿',
					seq: '2',
					val: 'New'
				},
				{
					name: '审批中',
					seq: '3',
					val: 'Approving'
				},
				{
					name: '已审批',
					seq: '4',
					val: 'Approved'
				},
			];
			return {
				headTapOptions, // 顶部tab栏选项
				headActStatusActive: headTapOptions[0],
				option,
				hideCreateButton:false, //是否隐藏新建按钮
				userInfo,
				addOver:false,//店招换新
				addIn:false,//融合终端
			}
		},
		async created(){
			this.hideCreateButton = this.userInfo.positionType === "Salesman"
			this.features = getSecurityFeatures('ALL', '/pages/headquarters-activity/construct-special-list-page')
			this.addOver = this.getSecurityFlag('ADD_CONSTRUCTOVER')
			this.addIn = this.getSecurityFlag('ADD_CONSTRUCTIN')
			this.hideCreateButton = this.addOver || this.addIn
			
		},
		async mounted(){
			this.$bus.$on('refreshConstructList', async () => {
           		this.option.methods.reload()
        	});
		},
		methods: {
            /**
             * 根据编码获取对应的值列表或表达式结果
             * <AUTHOR>
             * @date 2023-08-28
             */
            getSecurityFlag(code) {
                const feature = getFeature(this.features, code)
                const exp = parseFeature(feature)
                return exp
            },
			/**
			 * 复制活动编码
			 * * <AUTHOR>
			 * @date 2023-08-20
			 * @param data
			 */
			copyActCode(text) {
			    wx.setClipboardData({data: text});
			},
			/**
			 * 跳转至详情
			 * <AUTHOR>
			 * @date 2023-08-02
			 * @param data
			 */
			gotoDetail(data) {
				this.$nav.push('/pages/headquarters-activity/construct-special-detail-page.vue', {
                    data: data
                })
			},
			/**
			 * 跳转至新增
			 * <AUTHOR>
			 * @date 2023-08-02
			 * @param val
			 */
			goCreate(val) {
				setTimeout(()=>{
					this.$nav.push('/pages/headquarters-activity/construct-special-apply-page.vue', {
						val,
					})
				},100)				
			},
			/**
			 * 切换顶部tab
			 * <AUTHOR>
			 * @date 2023-08-02
			 * @param val
			 */
			switchTab(val) {
				if(val.seq === '1'){
					this.option.option.param.filtersRaw = []
				}else{
					this.option.option.param.filtersRaw = [
						{id: 'approveStatus', property: 'approveStatus', operator: '=', value: val['val']}
					]
				}
				
				 this.option.methods.reload();
			}
		}
	}
</script>

<style lang="scss">
	@import "../../styles/list-card";

	.special-list-page {
		background-color: #F2F2F2;
		font-family: PingFangSC-Regular;

		.lnk-tabs-container {
			height: 92px;
		}

		.special-list-item {
			background: #FFFFFF;
			width: 95%;
			margin: 24px auto auto auto;
			border-radius: 16px;

			.media-list {
				@include media-list;

				.media-top {
					width: 100%;
					@include flex-start-center;
					@include space-between;
					height: 80px;
					line-height: 80px;

					.left-content {
						font-family: PingFangSC-Semibold;
						font-size: 32px;
						color: #262626;
						letter-spacing: 0;
						line-height: 32px;
						padding-top: 20px;

					}

					.right-content {
						font-family: PingFangSC-Semibold;
						font-size: 32px;
						color: #FF5A5A;
						letter-spacing: 0;
						text-align: right;
						line-height: 32px;
						padding-top: 20px;
					}

					.num-view {
						background: #A6B4C7;
						border-radius: 8px;
						line-height: 50px;

						.num {
							font-size: 28px;
							color: #FFFFFF;
							letter-spacing: 0;
							line-height: 40px;
							padding: 2px 8px;
						}
					}

					.status-view {
						width: 120px;
						transform: skewX(-10deg);
						border-radius: 4px;
						background: #2F69F8;
						box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
						height: 36px;

						.status {
							font-size: 20px;
							color: #FFFFFF;
							letter-spacing: 2px;
							text-align: center;
							line-height: 36px;
						}
					}
				}
			}

			.content-middle {
				width: 100%;
				@include flex-start-center;
				@include space-between;
				height: 80px;
				line-height: 80px;

				.content {
					font-family: PingFangSC-Regular;
					font-size: 28px;
					color: #000000;
					letter-spacing: 0;
				}

				.name {
					font-family: PingFangSC-Semibold;
					font-size: 32px;
					color: #262626;
					letter-spacing: 0;
					line-height: 32px;
				}
			}

			.content-middle-line {
				width: 100%;

				.data {
					width: 51%;
					float: left;

					.title {
						font-family: PingFangSC-Regular;
						font-size: 28px;
						color: #8C8C8C;
						letter-spacing: 0;
						line-height: 56px;
						width: 40%;
						float: left;

					}

					.val {
						font-family: PingFangSC-Regular;
						font-size: 28px;
						color: #000000;
						letter-spacing: 0;
						line-height: 56px;
					}

					.Submitted,
					.Feedback {
						color: #2F69F8;
					}

					.Approve,
					.FeedbackApro {
						color: #2EB3C2;
					}

					.Refused,
					.Refeedback {
						color: #FF5A5A;
					}

				}

				.sum {
					width: 49%;
					float: left;

					.title {
						font-family: PingFangSC-Regular;
						font-size: 28px;
						color: #8C8C8C;
						letter-spacing: 0;
						line-height: 56px;
						float: left;
						width: 40%;
					}

					.val {
						font-family: PingFangSC-Regular;
						font-size: 28px;
						color: #000000;
						letter-spacing: 0;
						line-height: 56px;
						white-space: nowrap;
					}
				}

				.sum-2 {
					width: 58%;
					float: left;

					.title {
						font-family: PingFangSC-Regular;
						font-size: 28px;
						color: #8C8C8C;
						letter-spacing: 0;
						line-height: 56px;
						float: left;
						margin-right: 24px;
					}

					.val {
						font-family: PingFangSC-Regular;
						font-size: 28px;
						color: #000000;
						letter-spacing: 0;
						line-height: 56px;
					}
				}
			}
		}
	}
</style>