<!--
总部活动-宴席活动详情-一键报备
<AUTHOR>
@date 2023-06-29
@file banquet-report-share-page
-->
<template>
    <link-page class="report-share-page">
        <view class="report">
            <view>
<!--                <view class="report-data">{{banquetData.activityType | lov('HEAD_ACT_TYPE')}}报备</view>-->
                <view class="report-data">{{banquetData.feedbackName}}报备</view>
                <view class="report-data">宴席单编码：{{banquetData.feedbackCode}}</view>
                <view class="report-data">宴席政策：{{banquetData.activityName ? banquetData.activityName : ''}}</view>
                <view class="report-data">政策编码：{{banquetData.activityNum}}</view>
                <view class="report-data">宴席类型：{{banquetData.banquetType | lov('BANQUET_TYPE')}}</view>
                <view class="report-data">宴席主家：{{banquetData.banquetConsumerName ? banquetData.banquetConsumerName : ''}}</view>
                <view class="report-data">宴席主家电话：{{banquetData.banquetConsumerTel ? banquetData.banquetConsumerTel : ''}}</view>
                <view class="report-data">受益终端：{{banquetData.terminalName ? banquetData.terminalName : ''}}</view>
                <view class="report-data">受益终端编码：{{banquetData.terminalCode ? banquetData.terminalCode : ''}}</view>
                <view class="report-data">宴席开始时间：{{banquetData.startTime ? banquetData.startTime : ''}}</view>
                <view class="report-data">宴席结束时间：{{banquetData.endTime ? banquetData.endTime : ''}}</view>
                <view class="report-data">宴席正餐时间：{{banquetData.mealDate ? banquetData.mealDate : ''}}</view>
                <view class="report-data">宴席地址：{{banquetData.policyFullAddress ? banquetData.policyFullAddress : ''}}</view>
                <view class="report-data">计划用酒产品：{{banquetData.banquetProdName ? banquetData.banquetProdName : ''}}</view>
                <view class="report-data">计划宴席桌数：{{banquetData.planTableNum}}</view>
                <view class="report-data">实际宴席桌数：{{banquetData.actTableNum}}</view>
                <view class="report-data">每桌奖励费用：{{banquetData.actTableReward}}</view>
                <view class="report-data">实际推荐费总额：{{banquetData.banquetActFee}}</view>
                <view class="report-data">推荐费限额：{{banquetData.banquetPlanFee}}</view>
                <view class="report-data">终端出货数：{{banquetData.salesoutNum}}</view>
                <view class="report-data">终端退货数：{{banquetData.consalesinNum}}</view>
                <view class="report-data">计划用酒瓶数：{{banquetData.planProdNum}}</view>
                <view class="report-data">消费者开瓶数：{{banquetData.consumerCrackNum}}</view>
                <view class="report-data">开瓶率：{{banquetData.consumerCrackRatio}}</view>
            </view>
        </view>
        <link-sticky>
            <link-button block @tap="copyText">复制文本</link-button>
        </link-sticky>
    </link-page>
</template>
<script>
definePageConfig({
    navigationBarTitleText: '宴席单一键报备'
})
export default {
    name: "report-share-page",
    data() {
        return {
            banquetData: {},
            terminalData: {},
            sourceFlag: '',
        }
    },
    created() {
        this.queryBasicInfo();
    },
    methods: {
        /**
         *  查询基础信息
         *  <AUTHOR>
         *  @date 2023-04-17
         */
        async queryBasicInfo() {
            try {
                const data = await this.$http.post('action/link/headquarterFeedback/queryById',{
                    id: this.pageParam.data.id
                })
                if(data.success) {
                    this.banquetData = data.result;
                } else {
                    this.$showError("查询产品请求失败，请稍后重试！");
                }
            } catch (e) {
                this.$showError("查询产品请求失败，请稍后重试！" );
            }
        },
        async copyText() {
            const data = this.banquetData;
            let banquetType = await this.$lov.getNameByTypeAndVal('BANQUET_TYPE', this.banquetData.banquetType);
            let activityName = this.banquetData.activityName ? this.banquetData.activityName : '';
            let banquetConsumerName = this.banquetData.banquetConsumerName ? this.banquetData.banquetConsumerName : '';
            let banquetConsumerTel = this.banquetData.banquetConsumerTel ? this.banquetData.banquetConsumerTel : '';
            let terminalName = this.banquetData.terminalName ? this.banquetData.terminalName : '';
            let startTime = this.banquetData.startTime ? this.banquetData.startTime : '';
            let endTime = this.banquetData.endTime ? this.banquetData.endTime : '';
            let policyFullAddress = this.banquetData.policyFullAddress ? this.banquetData.policyFullAddress : '';
            let banquetProdName = this.banquetData.banquetProdName ? this.banquetData.banquetProdName : '';
            const text1 = `${data.feedbackName}报备\n宴席单编码：${data.feedbackCode}\n宴席政策：${activityName}\n政策编码：${data.activityNum}\n宴席类型：${banquetType}\n`;
            const text2 = `宴席主家：${banquetConsumerName}\n宴席主家电话：${banquetConsumerTel}\n受益终端：${terminalName}\n受益终端编码：${data.terminalCode}\n宴席开始时间：${startTime}\n`;
            const text3 = `宴席结束时间：${endTime}\n宴席地址：${policyFullAddress}\n计划用酒产品：${banquetProdName}\n计划宴席桌数：${data.planTableNum}\n实际宴席桌数：${data.actTableNum}\n`;
            const text4 = `每桌奖励费用：${data.actTableReward}\n实际推荐费总额：${data.banquetActFee}\n推荐费限额：${data.banquetPlanFee}\n终端出货数：${data.salesoutNum}\n`;
            const text5 = `终端退货数：${data.consalesinNum}\n计划用酒瓶数：${data.planProdNum}\n消费者开瓶数：${data.consumerCrackNum}\n开瓶率：${data.consumerCrackRatio}\n`;
            const text = `${text1}${text2}${text3}${text4}${text5}`;
            wx.setClipboardData({
                data: text,
                success: function () {
                    wx.showToast({
                        title: '复制成功',
                        icon: 'success'
                    })
                }
            })
        }
    }
}
</script>
<style lang="scss">
.report-share-page {
    background: white;
    .report {
        .report-data {
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #595959;
            letter-spacing: 0;
            line-height: 30px;
            padding: 32px 0 0 24px;
            clear: both;
        }
    }
}
</style>
