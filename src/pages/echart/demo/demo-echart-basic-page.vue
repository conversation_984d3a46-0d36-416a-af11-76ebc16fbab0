<template>
    <link-page class="demo-echart-basic-page">
        <link-echart :option="echartOption" ref="linkEchart" :loading="loadingFlag"/>
        <list>
            <list-title>各种图例</list-title>
            <link-button v-for="item in options" :key="item.name" block :label="item.name" @tap="()=>echartOption = item.option"/>
            <link-button @tap="getEchart" block label="使用echart对象生成option"/>
            <list-title>释放图例</list-title>
            <link-button @tap="$refs.linkEchart.dispose()" block label="销毁"/>
            <list-title>加载状态</list-title>
            <item>
                <link-switch v-model="loadingFlag" :trueValue="true" :falseValue="false"/>
            </item>
            <list-title>同一个页面加载多个图表</list-title>
            <link-button @tap="()=>otherOption = options[1].option" label="加载图表" block/>
            <link-echart v-if="otherOption" :option="otherOption"/>
        </list>
    </link-page>
</template>

<script>

    import zhuzhuangtu from './1_zhuzhuangtu'
    import sandiantu from './2_sandiantu'
    import bingtu from './3_bingtu'
    import zhuxiantu from './4_zhexiantu'
    import shutu from './5_shutu'

    export default {
        name: "demo-echart-basic-page",
        data() {
            return {
                echartOption: null,
                loadingFlag: false,

                options: [
                    {name: '柱状图', option: zhuzhuangtu},
                    {name: '散点图', option: sandiantu},
                    {name: '饼图', option: bingtu},
                    {name: '折线图', option: zhuxiantu},
                    {name: '树图', option: shutu},
                ],

                otherOption: null,
            }
        },
        mounted() {
            this.echartOption = this.options[0].option
        },
        methods: {
            getEchart() {
                this.echartOption = (option) => {
                    /*
                    * link-echart的option可以是一个函数属性，
                    * 这个函数可以得到一个option对象，
                    * 你可以通过option.echarts得到echarts对象，
                    * 可以使用这个echarts对象生成比较复杂的一些图表的属性，比如渐变色等等
                    */
                    const {echarts, ...leftOptions} = option
                    console.log({
                        echarts, leftOptions,
                    })
                    return zhuzhuangtu
                }
            },
        }
    }
</script>

<style lang="scss">
</style>