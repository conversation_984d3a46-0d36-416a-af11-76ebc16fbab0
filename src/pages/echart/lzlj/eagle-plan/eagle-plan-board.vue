<!--
@created<PERSON>y  yangying
@date  2024/04/15
@description 鹰计划看板
-->
<template>
    <view class="eagle-plan-board">
        <!-- tab -->
        <view class="title-wrap" :style="activeTab === 'activity' ? tabLeftActiveBg : tabRightActiveBg">
            <view class="title-item" @tap="changeChooseTab('activity')">
                <view :class="{'is-active': activeTab === 'activity'}">活动进度</view>
            </view>
            <view class="title-item">
                <view :class="{'is-active': activeTab === 'rank'}" @tap="changeChooseTab('rank')">排名</view>
            </view>
        </view>
        <view class="eagle-content">
            <!-- 活动进度 -->
            <activity-progress v-if="this.activeTab === 'activity'"/>
            <!-- 排名 -->
            <rank v-if="this.activeTab === 'rank'"/>
        </view>
    </view>
</template>

<script>
import ActivityProgress from '../../../terminal2/eagle-plan/components/activity-progress.vue';
import rank from '../../../terminal2/eagle-plan/components/rank.vue'
export default {
    name: 'eagle-plan-board',
    props: {

    },
    components: {
        ActivityProgress,
        rank
    },
    data() {
        return {
            tabLeftActiveBg: '',
            tabRightActiveBg: '',
            activeTab: 'activity'
        }
    },
    created() {
        this.tabLeftActiveBg = `background-image: url(${this.$imageAssets.tabLeftActiveBg});background-size: 100% 100%`;
        this.tabRightActiveBg = `background-image: url(${this.$imageAssets.tabRightActiveBg});background-size: 100% 100%`;
    },
    methods: {
        /**
         * 切换tab
         * <AUTHOR>
         * @date   2024/4/15 11:03
         */
        changeChooseTab(tab) {
            this.activeTab = tab;
        }
    }
}
</script>

<style lang="scss">
    .eagle-plan-board {
        margin: 32px 24px 0 24px;
        .eagle-content{
            min-height: 600px;
        }
        .title-wrap {
            width: 100%;
            display: flex;
            font-size: 28px;
            line-height: 48px;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 27px 0 rgba(47,105,248,0.12);
            margin-bottom: 32px;
        }

        .title-item {
            flex: 1;
            padding: 12px 0;
            text-align: center;
            position: relative;
            color: #6A6D75;

            .is-active {
                color: #fff;
            }
        }
    }
</style>
