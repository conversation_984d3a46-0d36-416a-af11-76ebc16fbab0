<template>
  <link-page class="main-home-page">
    <me ref="me" @firstCallBack="firstCallBack"></me>
  </link-page>
</template>

<script>
import Me from "./me";

export default {
  name: "main-home-page",
  components: {Me},
  data() {
    return {
      callBackFlag: false,
      isBuild: this.$env.env.node === 'production',
      // isBuild: false,
      firstTab: undefined,
      switchPostnBrandCompanyCode: '',
      // firstTab: '组件示例',
      pcFlag: false,//是否为PC端企业微信打开小程序
    }
  },
  onShow() {
    if (this.callBackFlag && this.$refs.me.tabNow !== 'board') {
      //首页查询接口优化
      this.$refs.me.requestTips();
    }
  },
  watch: {
    switchPostnBrandCompanyCode(newVal, oldVal) {
      if (newVal && oldVal && newVal === oldVal) {
        return
      }
      // 品牌公司code有变化时，重新加载菜单【当前用户登录职位的所属品牌公司编码为“1210”时
      // 将终端模块的【拜访】、【拜访检查】、【日报】、【配额】、【订单】、【入库】、【库存】【终端看板】全部隐藏】
      this.$refs.me.handleMenuList();
    }
  },
  methods: {
    firstCallBack(val) {
      this.callBackFlag = val;
    },
    onBack(param) {
      if (this.$utils.isEmpty(param) || param.nofresh) {
        return
      }
      // 从个人中心-职位切换返回后，赋值品牌公司code
      this.switchPostnBrandCompanyCode = param.switchPostnBrandCompanyCode;
      this.$refs.me.afterRefresh();
    }
  }
}
</script>

<style lang="scss">
.main-home-page {

  .app-main-tabs-wrapper {

  }

  .app-main-tabs {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    z-index: 99;
    border-top: solid 2px $ibc;

    .app-main-tab-title {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: $disabled;

      .link-icon {
        font-size: 40px;
        margin-bottom: 8px;
      }

      text {
        font-size: 24px;
      }

      &.app-main-tab-title-active {
        color: $color-primary;
      }
    }
  }
}

.duce_view {
  height: 100%;
  text-align: center;
  padding: 400px 20px 0 20px;

  .duce_button{
    width: 100%;
    height: 600px;
    position: absolute;
    top: 200px;
  }
}
</style>
