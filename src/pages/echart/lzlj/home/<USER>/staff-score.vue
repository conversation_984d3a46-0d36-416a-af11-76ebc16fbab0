<!--
 * @Author: 谭少奇
 * @Date: 2024-04-17
 * @Description: 积分账户
-->
<template>
    <view class="staff-score">
        <view class="table-head">
            <line-title title="积分账户"></line-title>
            <view class='head-right' @tap='toDetail'>
                <text>查看明细</text>
                <link-icon icon="icon-right"/>
            </view>
        </view>
        <view class="rank-table">
            <view class="table-title">
                <view>品项</view>
                <view>授权积分(分)</view>
                <view>积分余额(分)</view>
            </view>
            <view v-if="!tableList.length" class="none-table">
                暂无数据
            </view>
            <view :class="index % 2 === 0?'table-content':'table-content bg-color'" v-for="(item,index) in  tableList" :key="index+'table'">
                <view>{{item.productItemName | lov('PROD_ITEMS')}}</view>
                <view>{{item.hisPointSum}}</view>
                <view>{{item.availablePoints}}</view>
            </view>
        </view>
    </view>
</template>

<script>
    import lineTitle from '../../components/line-title.vue'
    export default {
        name: 'staff-score',
        components: {lineTitle},
        data(){
            return {
                tableList: [],
            }
        },
        created(){
            this.getList()
        },
        methods:{
            /**
             @desc 积分详情跳转
             @auth tanshaoqi
             @date 2024/04/17
             **/
            toDetail(){
                this.$nav.push('/pages/terminal2/eagle-plan/score-detail-page.vue')
            },
            /**
             @desc 积分列表
             @auth tanshaoqi
             @date 2024/04/17
             **/
            async getList(){
                try{
                    const {success,data} = await this.$http.post('/loyalty/loyalty/member/innerEmpPortalPoint')
                    if(success){
                        this.tableList = data
                    }
                }catch(e){
                    //TODO handle the exception
                }

            }
        }
    }
</script>

<style lang="scss">
    .staff-score{
        padding: 20px;
        .table-head{
            display: flex;
            justify-content: space-between;
            align-items: center;
            .head-right{
                padding-top: 24px;
                font-size: 24px;
                color: #212223;
                text{
                    margin-right: 10px;
                }
            }
        }
        .rank-table{
            margin-top: 20px;
            font-size: 24px;
            line-height: 80px;
            .none-table{
                background: #fff;
                text-align: center;
                color: #595959;
                line-height: 120px;
            }
            .table-title{
                text-align: center;
                display: flex;
                font-weight: bold;
                background: #EFF4FF;
                border-radius: 16px 16px 0px 0px;
                view{
                    padding: 0 10px;
                    width: 300px;
                    overflow: hidden;
                }
            }
            .table-content{
                display: flex;
                background: #fff;
                text-align: center;
                view{
                    padding: 0 10px;
                    width: 300px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
            .bg-color{
                background: rgba(248,250,255,0.90);
            }
        }
    }
</style>
