<!--
 * 客户投入产出比(报表)
 * @Author:付常涛
 * @Date: 2023/10/30 19:13:18
-->
<template>
    <view class="consumer-input-output">
        <line-title title="客户投入产出比"></line-title>
        <scroll-view class="scroll-view-data" scroll-x="true">
            <view class="select-dimension">
                <select-button :key="index"
                               :label="item.name"
                               :selectedFlag="item.selectedFlag"
                               :value="item.value"
                               isBoard
                               @tap="chooseData($event,item)"
                               v-for="(item,index) in selectDimensionLevel"/>
            </view>
        </scroll-view>
        <view class="consumer-overview-chart">
            <view class="consumer-overview-left">
                <view class="title-line-item kv-title">
                   分类
                </view>
                <view class="other-line" :class="{'item-info': isCheckDetail && item.acctTypeName !== '其他'}"
                      v-for="(item, index) in inputOutputData" :key="index" @tap="gotoItem(item)">
                    {{item.acctTypeName}}
                </view>
            </view>
            <view class="consumer-overview-right">
                <view class="title-line-item">
                    <view class="title-item"></view>
                    <view class="title-item"></view>
                    <view class="title-item">消费者总数</view>
                    <view class="title-item">活动场次</view>
                    <view class="title-item">活动费用&nbsp;&nbsp;（万元）</view>
                    <view class="title-item">消费者动销金额（万元）</view>
                    <view class="title-item">费效比</view>
                </view>
                <view v-if="inputOutputData.length > 0">
                    <view class="data-line" v-for="(item, index) in inputOutputData" :key="index">
                        <!-- 消费者总数 -->
                        <view class="data-item" :class="{'item-info': isCheckDetail}" @tap="gotoBoardList('consumerList', item)">
                            <view class="show-data">{{item.consumerQty | removeZeroDecimal}}</view>
                        </view>
                        <!-- 活动场次 -->
                        <view class="data-item" :class="{'item-info': isCheckDetail}" @tap="gotoBoardList('terminal', item)">
                            <view class="show-data">{{item.actTimes | removeZeroDecimal}}</view>
                        </view>
                        <!-- 活动费用（万元） -->
                        <view class="data-item" :class="{'item-info': isCheckDetail}" @tap="gotoBoardList('activityCost', item)">
                            <view class="show-data">{{Number(item.actCost || 0).toFixed(2)}}</view>
                        </view>
                        <!-- 消费者动销金额（万元） -->
                        <view class="data-item" :class="{'item-info': isCheckDetail}" @tap="gotoBoardList('costDetail', item)">
                            <view class="show-data">{{Number(item.ordAmt || 0).toFixed(2)}}</view>
                        </view>
                        <!-- 费效比 -->
                        <view class="data-item">{{((item.ceRatio || 0) * 100).toFixed(2)}}%</view>
                    </view>
                </view>
                <view class="no-data" v-else>暂无数据</view>
            </view>
        </view>
    </view>
</template>
<script>
import LineTitle from "../../components/line-title";
import SelectButton from "../../components/select-button";
export default {
    name: 'consumer-input-output',
    components: {LineTitle, SelectButton},
    props: {
        dataRange: {
            type: String,
            default: ''
        },
        companyId: {
            type: String,
            default: "",
        },
        isCheckDetail: {
            type: Boolean,
            default: false
        }
    },
    data () {
        const userInfo = this.$taro.getStorageSync('token').result;
        const map = new Map([
            ['Mine', 'Salesman'],
            ['Team', 'Leader'],
            ['Area', 'Headquarters']
        ])
        return {
            inputOutputData: [],
            inputOutputDataList: [],
            selectDimensionLevel: [  // 动销总览-时间范围筛选
                {name: '全部', value: 'all', selectedFlag: false},
                {name: '本财年', value: 'year', selectedFlag: true},
                {name: '本季', value: 'quarter', selectedFlag: false},
                {name: '本月', value: 'month', selectedFlag: false}
            ],
            map,
            timeObj: {},
            timeScope: '本财年',
            userInfo,
            kVal: [],   // k序列数据
            vVal: [],   // v序列数据
        }
    },
    filters: {
        /**
         * @date 2023/7/8
         * @methods: removeZeroDecimal
         * @description: 将浮点数转为整数
         **/
        removeZeroDecimal (value) {
            // 将字符串转换为浮点数
            const floatValue = parseFloat(value);
            const integerValue = Math.floor(floatValue);
            // 判断浮点数是否为整数
            if (Number.isInteger(integerValue)) {
                return integerValue;
            }
            // 如果不是整数，则原样返回
            return value;
        }
    },
    created () {
        this.queryAllData()
    },
    watch: {
        dataRange (newVal) {
            if (newVal) {
                this.queryAllData();
            }
        }
    },
    methods: {
        /**
         * 跳转看板列表
         * @Author:付常涛
         * @Date: 2023/11/14 21:48:11
         * @param type  点击类型
         * @param item  行数据
         */
        gotoBoardList(type, item) {
            if (!this.isCheckDetail) return;
            const param = {
                dataAccess: this.dataRange, // 数据权限,
                companyId: this.companyId, // 公司ID
                timeRange: this.timeScope, // 数据时间范围
                acctTypeName: item.acctTypeName, // 客户大类
                type: type,
            };
            switch (type) {
                case 'consumerList':
                    param.title = '终端消费者详情';
                    param.dmpSrUrl = '/link/boardDetail/csmAcctFeeDetail';
                    param.feeType = 'csmQty'; // 产出比类型--消费者总数
                    break;
                case 'terminal':
                    param.title = '终端列表';
                    param.dmpSrUrl = '/link/boardDetail/csmAcctFeeDetail';
                    param.feeType = 'actTimes'; // 产出比类型--活动场次
                    break;
                case 'activityCost':
                    param.title = '终端活动费用列表';
                    param.dmpSrUrl = '/link/boardDetail/csmAcctFeeDetail';
                    param.feeType = 'actFee'; // 产出比类型--活动费用
                    break;
                case 'costDetail':
                    param.title = '终端动销明细';
                    param.dmpSrUrl = '/link/boardDetail/csmAcctFeeDetail';
                    param.feeType = 'csmAmt'; // 产出比类型--动销金额
                    break;
                case 'consumerDetail':
                    param.title = '消费者明细';
                    param.dateType = this.capitalizeFirstLetter(this.timeScope);
                    param.postId = this.userInfo.postId;
                    param.orgId = this.userInfo.orgId;
                    break;
                case 'activityDetail':
                    param.title = '活动明细';
                    param.dateType = this.dataRange;
                    param.url = '/link/appBoard/actCountUnder';
                    param.postId = this.userInfo.postId;
                    if (this.map.get(this.dataRange) === 'Headquarters') {
                        param.url = '/link/appBoard/queryActCountBySalesRegion';
                        param.orgId = this.userInfo.orgId;
                        delete param.postId;
                    } else {
                        param.postId = this.userInfo.postId;
                    }
                    break;
                default:
                    break;
            }
            this.$nav.push('/pages/echart/lzlj/consumer-kanban-new/components/consumer-board-detail-list-new-page', param)
        },
        /**
         * 点击分类列下的跳转详情
         * @Author:付常涛
         * @Date: 2023/11/14 21:31:44
         * @param item 行数据
         */
        gotoItem (item) {
            // console.log('item', item);
            if (!this.isCheckDetail) return;
            this.$nav.push('/pages/echart/lzlj/consumer-kanban-new/consumer-overview-item-new-page', {
                pageFrom: 'ConsumerInputOutput',
                item: {
                    ...item,
                    feeType: 'acct' // 分类
                },
                companyId: this.companyId,
                timeRange: this.timeScope,
                dataRange: this.dataRange,
            })
        },
        /**
         * @createdBy 曾宇
         * @date 2023/7/8
         * @methods: capitalizeFirstLetter
         * @description: 将字符串的第一个转为小写
         **/
        capitalizeFirstLetter (str) {
            if (str.length === 0) {
                return str;
            }
            const firstChar = str.charAt(0).toLowerCase();
            const remainingChars = str.slice(1);
            return firstChar + remainingChars;
        },
        /**
         * 查询消费者总览、会员总览、消费者等级分布数据
         * @Author:付常涛
         * @Date: 2023/11/13 17:17:53
         */
        async queryAllData(companyId) {
            try {
                this.$utils.showLoading()
                const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send", {
                    dmpSrUrl: '/link/board/csmAcctFee',
                    dataAccess: this.dataRange,
                    timeRange: this.timeScope,
                    companyId: companyId || this.companyId
                });
                if (data.success) {
                    this.inputOutputData = data.rows;
                } else {
                    this.$message.error({message: '查询消费者总览接口失败，请稍后重试！' + data.result, customFlag: true});
                }
            } catch (e) {
                this.$message.error({message: '查询消费者总览接口失败！' + e, customFlag: true});
            } finally {
                this.$utils.hideLoading();
            }
        },
        /**
         * @desc 选择时间查询维度
         * <AUTHOR>
         * @date 2021/6/3 16:31
         **/
        async chooseData($event, data) {
            //（全部All/本年Year/本季Quarter/本月Month
            switch (data.value) {
                case 'all':
                    this.timeScope = '总数';
                    break;
                case 'year':
                    this.timeScope = '本财年';
                    break;
                case 'quarter':
                    this.timeScope = '本财季';
                    break;
                case 'month':
                    this.timeScope = '本月'
                    break;
            }
            this.selectDimensionLevel.forEach((item) => {
                if (item.value === data.value) {
                    data.selectedFlag = true;
                } else {
                    item.selectedFlag = false;
                }
            });
            this.queryAllData();
        },
    }
}
</script>
<style lang="scss">
@import "../css/new-board";
.consumer-input-output {
    .consumer-overview-chart{
        text-align: center;
        display: flex;
        .title-line-item{
            color: #999999;
            height: 112px;
            background:  #F8FAFF;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .consumer-overview-left{
            border-left: 2px solid #F0F2F8;
            flex: 0 0 168px;
            .kv-title{
                width: 168px;
            }
            .other-line{
                width: 168px;
                padding: 24px 0;
            }
        }
        .consumer-overview-right{
            overflow-x: auto;
            flex: 1;
            .title-item{
                width: 168px;
                flex: none;
                background:  #F8FAFF;
                height: 112px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .title-line-item{
                display: flex;
            }
            .data-line{
                display: flex;
                padding: 24px 0;
            }
            .data-item{
                width: 168px;
                flex: none;
                .show-data {
                    margin: 0 6%;
                    width: 88%;
                    white-space: nowrap;
                    overflow: auto;
                }
            }
        }
    }
}
</style>
