<template>
    <link-page class="consumer-board-detail-list">
        <view class="head-info">
            <view class="tips" @tap="queryInfo">
                <link-icon icon="mp-info-lite" status="info"/>
                <view class="tips-content">数据指标说明</view>
            </view>
        </view>
        <view class="first-part" v-if="pageParam.type === 'terminalList'">
            <view class="table-data">
                <view class="title">
                    <view class="title-item">分类</view>
                    <view class="title-item">推荐人（客户）</view>
                    <view class="title-item">消费者数量</view>
                </view>
                <view class="data">
                    <view class="data-list" v-for="(item,index) in terminalList.list" :key="index">
                        <view class="data-list-item">{{ item.storeTypeName }}</view>
                        <view class="data-list-item">{{ item.storeName }}</view>
                        <view class="data-list-item" style="color: #2F69F8;" @tap="gotoTerminalConsumerList(item)">
                            {{ item.count }}
                            <link-icon icon="mp-arrow-right" style="color: #BFBFBF;font-size: 12px;margin-left: 5px"/>
                        </view>
                    </view>
                    <view class="load-more" v-if="terminalList.list.length > 0" @tap="loadMore('terminalList')">{{ terminalList.isNoMoreData ? '加载完成' : '加载更多' }}</view>
                    <view class="load-more" v-else>暂无数据</view>
                </view>
            </view>
        </view>
        <view class="first-part" v-if="pageParam.type === 'costDetail'">
            <view class="table-data">
                <view class="title">
                    <view class="title-item">推荐人（客户）</view>
                    <view class="title-item">动销订单笔数</view>
                    <view class="title-item">动销订单金额（万元）</view>
                </view>
                <view class="data">
                    <view class="data-list" v-for="(item,index) in saleOrderAmountList.list" :key="index">
                        <view class="data-list-item">{{ item.acctName }}</view>
                        <view class="data-list-item">{{ item.acctOrdQty | numberIntFomart }}</view>
                        <view class="data-list-item">{{ item.acctOrdAmt | numberFloatFomart }}</view>
                    </view>
                    <view class="load-more" v-if="saleOrderAmountList.list.length > 0" @tap="loadMore('costDetail')">{{ saleOrderAmountList.isNoMoreData ? '加载完成' : '加载更多' }}</view>
                    <view class="load-more" v-else>暂无数据</view>
                </view>
            </view>
        </view>
        <view class="first-part" v-if="pageParam.type === 'productDetail'">
            <view class="table-data">
                <view class="title">
                    <view class="title-item">产品名称</view>
                    <view class="title-item">动销数量（瓶）</view>
                    <view class="title-item">动销金额（元）</view>
                </view>
                <view class="data">
                    <view class="load-more" v-if="orderDetailUnderList.length === 0">暂无数据</view>
                    <view class="data-list" v-else v-for="(item,index) in orderDetailUnderList" :key="index">
                        <view class="data-list-item">{{ item.prodName }}</view>
                        <view class="data-list-item">{{ item.bottleTotal | numberIntFomart}}</view>
                        <view class="data-list-item">{{ item.orderAmount | numberFloatFomart}}</view>
                    </view>
                </view>
            </view>
        </view>
        <view class="second-part" v-if="pageParam.type === 'terminal'">
            <view class="table-data">
                <view class="title">
                    <view class="title-item">终端名称</view>
                    <view class="title-item">活动类型</view>
                    <view class="title-item">活动场次</view>
                    <view class="title-item">活动参与人数</view>
                </view>
                <view class="data">
                    <view class="data-list" v-for="(item,index) in actUnderList.list" :key="index">
                        <view class="data-list-item">{{ item.acctName }}</view>
                        <view class="data-list-item">{{ item.actMClassName }}</view>
                        <view class="data-list-item">{{ item.actTimes | numberIntFomart}}</view>
                        <view class="data-list-item">{{ item.actConsumerQty | numberIntFomart}}</view>
                    </view>
                    <view class="load-more" v-if="actUnderList.list.length > 0" @tap="loadMore('terminal')">{{ actUnderList.isNoMoreData ? '加载完成' : '加载更多' }}</view>
                    <view class="load-more" v-else>暂无数据</view>
                </view>
            </view>
        </view>
        <view class="second-part-long" v-if="pageParam.type === 'activityCost'">
            <view class="table-data">
                <view class="title">
                    <view class="title-item">客户名称</view>
                    <view class="title-item">活动消费者动销金额（万元）</view>
                    <view class="title-item">活动费用（万元）</view>
                    <view class="title-item">终端活动费效比</view>
                </view>
                <view class="data">
                    <view class="data-list" v-for="(item,index) in actAmountList.list" :key="index">
                        <view class="data-list-item">{{ item.acctName }}</view>
                        <view class="data-list-item">{{ item.acctOrdAmt | numberFloatFomart }}</view>
                        <view class="data-list-item">{{ item.actCost | numberFloatFomart }}</view>
                        <view class="data-list-item">{{ item.ceRatio | numberFloatFomart }} %</view>
                    </view>
                    <view class="load-more" v-if="actAmountList.list.length > 0" @tap="loadMore('activityCost')">{{ actAmountList.isNoMoreData ? '加载完成' : '加载更多' }}</view>
                    <view class="load-more" v-else>暂无数据</view>
                </view>
            </view>
        </view>
        <view class="third-part" v-if="pageParam.type === 'consumerDetail'">
            <consumer-distribution-board :show.sync="show"
                                         :type="pageParam.type"
                                         :companyId="param.companyId"
                                         :pageParam="pageParam"
                                         :param="pageParam"/>
        </view>
        <view class="forth-part" v-if="pageParam.type === 'activityDetail'">
            <view class="table-data">
                <view class="title">
                    <view class="title-item">活动类型</view>
                    <view class="title-item">活动场次</view>
                    <view class="title-item">活动参与人次</view>
                    <view class="title-item">活动消费者动销金额（万元）</view>
                    <view class="title-item">活动费用（万元）</view>
                    <view class="title-item">活动费效比</view>
                </view>
                <view class="data">
                    <view class="load-more" v-if="actCountUnderList.length === 0">暂无数据</view>
                    <view class="data-list" v-else v-for="(item,index) in actCountUnderList" :key="index">
                        <view class="data-list-item">{{ item.actTypeName }}</view>
                        <view class="data-list-item">{{ item.actQty | numberIntFomart }}</view>
                        <view class="data-list-item">{{ item.actParticipationQty | numberIntFomart }}</view>
                        <view class="data-list-item">{{ item.actDongxiaoAmount | numberFloatFomart}}</view>
                        <view class="data-list-item">{{ item.actAmount | numberFloatFomart}}</view>
                        <view class="data-list-item">{{ item.bilv | numberFloatFomart}}%</view>
                    </view>
                </view>
            </view>
        </view>
        <view class="fifth-part" v-if="pageParam.type === 'activityDetail' && pageParam.reportPostnType !== 'Headquarters'">
            <line-title title="覆盖客户"/>
            <view class="table-data">
                <view class="title">
                    <view class="title-item">所属客户</view>
                    <view class="title-item">数量</view>
                </view>
                <view class="data">
                    <view class="load-more" v-if="actCountUnderTerminalList.length === 0">暂无数据</view>
                    <view class="data-list" v-else v-for="(item,index) in actCountUnderTerminalList" :key="index">
                        <view class="data-list-item">{{ item.storeName }}</view>
                        <view class="data-list-item">{{ item.count }}</view>
                    </view>
                </view>
            </view>
        </view>
        <view class="fifth-part" v-if="pageParam.type === 'activityDetail' && pageParam.reportPostnType === 'Headquarters'">
            <line-title title="覆盖客户"/>
            <view class="table-data">
                <view class="title">
                    <view class="title-item">所属客户</view>
                    <view class="title-item">数量</view>
                </view>
                <view class="data">
                    <view class="data-list" v-for="(item,index) in actCountUnderTerminalListByC.list" :key="index">
                        <view class="data-list-item">{{ item.storeName }}</view>
                        <view class="data-list-item">{{ item.count }}</view>
                    </view>
                    <view class="load-more" v-if="actCountUnderTerminalListByC.list.length > 0" @tap="loadMore('actCountUnderTerminalListByC')">{{ actCountUnderTerminalListByC.isNoMoreData ? '加载完成' : '加载更多' }}</view>
                    <view class="load-more" v-else>暂无数据</view>
                </view>
            </view>
        </view>
        <view class="sixth-part" v-if="pageParam.type === 'consumerList'">
            <view>
                <link-search-input v-model="searchKeyword" @change="research" :placeholder="'推荐人（客户）'"/>
            </view>
            <view class="table-data">
                <view class="title">
                    <view class="title-item">推荐人（客户）</view>
                    <view class="title-item">消费者数量</view>
                </view>
                <view class="data">
                    <view class="data-list" v-for="(item,index) in totalConsumerList.list" :key="index">
                        <view class="data-list-info" :style="currentIndex === index ? 'background: #2F69F8; border-radius: 10px 10px 0px 0px; color: #fff;' : ''">
                            <view class="data-list-item" >{{ item.acctName }}</view>
                            <view class="data-list-item data-list-num" @tap="showDetail(item, index)">
                                <view>{{ item.consumerQty | numberIntFomart}}</view>
                                <link-icon :icon="currentIndex === index ? 'mp-arrow-bottom' : 'mp-arrow-top'"/></view>
                        </view>
                        <view v-show="currentIndex === index">
                            <consumer-distribution-board :show.sync="item.show"
                                                         :type="pageParam.type"
                                                         :companyId="param.companyId"
                                                         :pageParam="pageParam"
                                                         :param="item"/>
                        </view>
                    </view>
                    <view class="load-more" v-if="totalConsumerList.list.length > 0" @tap="loadMore('consumerList')">{{ totalConsumerList.isNoMoreData ? '加载完成' : '加载更多' }}</view>
                    <view class="load-more" v-else>暂无数据</view>
                </view>
            </view>
        </view>
        <view class="info-page-mask" @tap="showInfo=false" v-if="showInfo"></view>
        <view class="info-description" v-if="showInfo">
            <link-icon icon="mp-asc" class="info-icon"/>
            <view v-html="description" class="info-content"></view>
        </view>
        <water-mark></water-mark>
    </link-page>
</template>

<script>
import ConsumerDistributionBoard from "./consumer-distribution-board-new";
import LineTitle from "../../components/line-title";
import waterMark from "../../../../lzlj/components/water-mark";

export default {
    name: "consumer-board-detail-list",
    components: {ConsumerDistributionBoard, LineTitle, waterMark},
    data() {
        return {
            param: {},
            searchKeyword: '',
            actUnderList: new this.AutoList(this, {
                url: {
                    queryByExamplePage: this.$env.appURL + "/action/link/sendDmpSr/send"
                },
                param: {
                    rows: 5,
                },
                sortOptions: null,
                loadOnStart: false,
                hooks: {
                    beforeLoad(option) {
                        delete option.param.order;
                        delete option.param.sort;
                        option.param = Object.assign({}, option.param, this.param);
                    },
                    async afterLoad(data) {
                        this.deployNo = 'cunsumer_salesman004';
                        await this.queryReportTaskConfig();
                    }
                },
            }),           // 终端活动列表
            terminalList: new this.AutoList(this, { // 业代消费者运营明细
                url: {
                    queryByExamplePage: this.$env.dmpURL + this.pageParam.url
                },
                param: {
                    rows: 5,
                },
                sortOptions: null,
                loadOnStart: false,
                hooks: {
                    beforeLoad(option) {
                        delete option.param.order;
                        delete option.param.sort;
                        option.param = Object.assign({}, option.param, this.param);
                    },
                    async afterLoad(data) {
                        this.deployNo = 'cunsumer_salesman001';
                        await this.queryReportTaskConfig();
                    }
                },
            }),           // 消费者终端列表
            actAmountList: new this.AutoList(this, { // 业代消费者运营明细
                url: {
                    queryByExamplePage: this.$env.appURL + "/action/link/sendDmpSr/send"
                },
                param: {
                    rows: 5,
                },
                sortOptions: null,
                loadOnStart: false,
                hooks: {
                    beforeLoad(option) {
                        delete option.param.order;
                        delete option.param.sort;
                        option.param = Object.assign({}, option.param, this.param);
                    },
                    async afterLoad(data) {
                        this.deployNo = 'cunsumer_salesman005';
                        await this.queryReportTaskConfig();
                    }
                },
            }),          // 终端活动费用列表
            totalConsumerList: new this.AutoList(this, { // 业代消费者运营明细
                url: {
                    queryByExamplePage: this.$env.appURL + "/action/link/sendDmpSr/send"
                },
                param: {
                    rows: 5,
                },
                sortOptions: null,
                loadOnStart: false,
                hooks: {
                    beforeLoad(option) {
                        delete option.param.order;
                        delete option.param.sort;
                        option.param = Object.assign({}, option.param, this.param);
                    },
                    async afterLoad(data) {
                        this.deployNo = 'cunsumer_salesman003';
                        data.rows.forEach((item) => item.show = false);
                        this.$set(data.rows[0], 'show', true);
                        this.$set(this.currentItem, 'data', data.rows[0]);
                        this.$set(this.currentItem, 'index', 0);
                        this.currentIndex = 0;
                        await this.queryReportTaskConfig();
                    }
                },
            }),      // 终端消费者详情列表
            saleOrderAmountList: new this.AutoList(this, { // 业代消费者运营明细
                url: {
                    queryByExamplePage: this.$env.appURL + "/action/link/sendDmpSr/send"
                },
                param: {
                    rows: 5,
                },
                sortOptions: null,
                loadOnStart: false,
                hooks: {
                    beforeLoad(option) {
                        delete option.param.order;
                        delete option.param.sort;
                        option.param = Object.assign({}, option.param, this.param);
                    },
                    async afterLoad(data) {
                        this.deployNo = 'cunsumer_salesman006';
                        // data.rows.filter((item) => item.storeOrderCount != 0 && item.storeAmount != 0);
                        await this.queryReportTaskConfig();
                    }
                },
            }),    // 终端动销明细列表
            actCountUnderList: [],      // 业代活动明细列表
            actCountUnderTerminalList: [], // 业代活动终端明细列表
            actCountUnderTerminalListByC: new this.AutoList(this, { // 业代活动终端明细列表 - 管理层
                url: {
                    queryByExamplePage: this.$env.dmpURL + '/link/appBoard/actCountTerminalPage'
                },
                param: {
                    rows: 5,
                    dateType: '',
                    orgId: ''
                },
                sortOptions: null,
                loadOnStart: false,
                hooks: {
                    beforeLoad(option) {
                        delete option.param.order;
                        delete option.param.sort;
                        delete option.param.filtersRaw;
                        option.param.dateType = this.pageParam.dateType;
                        option.param.orgId = this.pageParam.orgId;
                    }
                },
            }),
            orderDetailUnderList: [],   // 产品明细列表
            show: false,
            deployNo: '',
            description: '',                                        // 指标说明
            showInfo: false,
            currentIndex: 0,
            currentItem: {},
            consumerLevelOption: null,     // 消费者分级
            consumerClassOption: null,     // 消费者分类
            capacityLevelBarYCategoryHeight: (this.$device.systemInfo.windowWidth - 24) * 0.6285714 < 220 ? (this.$device.systemInfo.windowWidth - 24) * 0.6285714 : 220,
            loadingFlag: false,
            consumerReport: new this.AutoList(this, {
                url: {
                    queryByExamplePage: this.$env.appURL + '/cscAnalysis/link/salesmanConsumerReport/queryByExamplePage'
                },
                param: {
                    rows: 5,
                    viewType: ''
                },
                sortOptions: null,
                loadOnStart: false,
                hooks: {
                    beforeLoad(option) {
                        delete option.param.order;
                        delete option.param.sort;
                    },
                },
            }),
        }
    },
    filters: {
        /**
         * @createdBy 曾宇
         * @date 2022/8/1
         * @methods: numberFloatFomart
         * @description: 浮点数数据转换
         **/
        numberFloatFomart(val){
            if (!Boolean(val)){
                return '0.00';
            } else {
                return parseFloat(val).toFixed(2);
            }
        },
        /**
         * @createdBy 曾宇
         * @date 2022/8/1
         * @methods: numberIntFomart
         * @description: 整数数据转换
         **/
        numberIntFomart(val){
            if (!Boolean(val)){
                return '0';
            } else {
                return parseInt(val);
            }
        }
    },
    created() {
        this.$taro.setNavigationBarTitle({title: this.pageParam.title});
    },
    async mounted() {
        let param = this.$utils.deepcopy(this.pageParam);
        delete param.title;
        delete param.type;
        delete param.url;
        this.param = param;
        console.log('2', this.param)
        if(this.pageParam.type === 'consumerDetail') {
            this.deployNo = 'cunsumer_salesman007';
            await this.queryReportTaskConfig();
            this.show = true;
        } else if(this.pageParam.type === 'productDetail'){
            await this.orderDetailUnder(this.pageParam.url, param);
        } else {
            if (this.pageParam.type === 'terminalList') {
                this.terminalList.methods.reload();
            } else if (this.pageParam.type === 'consumerList'){
                this.totalConsumerList.methods.reload();
            } else if (this.pageParam.type === 'terminal'){
                // 活动场次
                this.actUnderList.methods.reload();
            } else if (this.pageParam.type === 'costDetail'){
                this.saleOrderAmountList.methods.reload();
            } else if (this.pageParam.type === 'activityCost'){
                this.actAmountList.methods.reload();
            } else {
                await this.getListDataByType(this.pageParam.type, this.pageParam.url, param);
            }
        }
    },
    methods: {
        /**
         * @desc 加载更多
         * <AUTHOR>
         * @date 2022/11/17 16:03
         **/
        loadMore (type) {
            if (type === 'terminalList') {
                this.terminalList.methods.loadMore();
            }
            if (type === 'consumerList') {
                this.totalConsumerList.methods.loadMore();
            }
            if (type === 'terminal') {
                this.actUnderList.methods.loadMore();
            }
            if (type === 'costDetail') {
                this.saleOrderAmountList.methods.loadMore();
            }
            if (type === 'activityCost'){
                this.actAmountList.methods.loadMore();
            }
            if (type === 'actCountUnderTerminalListByC') {
                this.actCountUnderTerminalListByC.methods.loadMore();
            }

        },
        /**
         * 终端消费者详情列表--重新搜索
         * @Author:付常涛
         * @Date: 2023/11/15 18:54:26
         */
        async research () {
            if (this.$utils.isEmpty(this.searchKeyword)) {
                this.totalConsumerList.option.param.acctName = '';
            } else {
                this.totalConsumerList.option.param.acctName = this.searchKeyword;
            }
            await this.totalConsumerList.methods.reload();
        },

        /**
         * 使用indexof方法实现模糊查询
         * @param  {Array}  list     进行查询的数组
         * @param  {String} keyWord  查询的关键词
         * @return {Array}           查询的结果
         */
        fuzzyQuery (list, keyWord) {
            let arr = []
            if (keyWord === '') {
                return list
            }
            for (let i = 0; i < list.length; i++) {
                if (list[i].storeName.indexOf(keyWord) >= 0) {
                    arr.push(list[i])
                }
            }
            return arr
        },
        /**
         * 查询指标说明
         * @Author:付常涛
         * @Date: 2023/11/15 18:59:34
         */
        async queryReportTaskConfig() {
            const data = await this.$http.post(this.$env.dmpURL + '/link/reportTaskConfig/queryByExamplePage', {deployNo: this.deployNo}, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError('查询失败' + response.result);
                }
            });
            if(data.success) {
                this.description = data.rows[0].content;
            }
        },
        /**
         * 展示指标说明
         * @Author:付常涛
         * @Date: 2023/11/15 18:58:55
         */
        async queryInfo() {
            if(this.showInfo){
                this.showInfo = false;
                if(this.currentItem.index !== 'index') this.showDetail(this.currentItem.data, this.currentItem.index);
                if(this.pageParam.type === 'consumerDetail') this.show = true;
            } else {
                this.currentIndex = 'index';
               if(this.totalConsumerList.length > 0 && this.currentItem.index !== 'index') this.$set(this.totalConsumerList[this.currentItem.index], 'show', false);
               if(this.pageParam.type === 'consumerDetail') this.show = false;
               this.showInfo = true;
            }
        },
        /**
         * @createdBy 曾宇
         * @date 2022/8/1
         * @methods: getListDataByType
         * @description: 查询列表数据
         **/
        async getListDataByType(type, url, param){
            this.$utils.showLoading();
            const data = await this.$http.post(this.$env.dmpURL + url, param, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError('查询失败' + response.result);
                }
            });
            if (data.success) {
                switch (this.pageParam.type) {
                    case 'activityDetail':
                        this.deployNo = 'cunsumer_salesman008';
                        this.actCountUnderList = data.result;
                        this.actCountUnderList.forEach((item) => {
                            if(item.actDongxiaoAmount && item.actDongxiaoAmount != 0 && item.actAmount && item.actAmount != 0) {
                                item.bilv = parseFloat(Number(item.actDongxiaoAmount) / Number(item.actAmount)).toFixed(2);
                            }
                        });
                        if (this.pageParam.reportPostnType === 'Headquarters') {
                            this.actCountUnderTerminalListByC.methods.reload();
                        }
                        await this.actCountUnder4Terminal();
                        await this.queryReportTaskConfig();
                        break;
                    default:
                        break;
                }
                this.$utils.hideLoading();
            }
        },
        /**
         * @createdBy 曾宇
         * @date 2022/8/1
         * @methods: actCountUnder4Terminal
         * @description: 查询终端消费者详情列表
         **/
        async actCountUnder4Terminal() {
            let param = {
                postId: this.pageParam.postId,
                reportPostnType: this.pageParam.reportPostnType,
                dateType: this.pageParam.dateType
            };
            let url = '/link/appBoard/actCountUnder4Terminal';
            const data = await this.$http.post(this.$env.dmpURL + url, param, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError('查询失败' + response.result);
                }
            });
            if (data.success) {
                this.actCountUnderTerminalList = data.result;
            }
        },
        /**
         * @createdBy 曾宇
         * @date 2022/8/1
         * @methods: orderDetailUnder
         * @description: 查询订单列表
         **/
        async orderDetailUnder(url, param) {
            const data = await this.$http.post(this.$env.appURL + url, param, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError('查询失败' + response.result);
                }
            });
            if (data.success) {
                this.deployNo = 'operation-consumer002';
                this.orderDetailUnderList = data.rows;
                await this.queryReportTaskConfig();
            }
        },
        /**
         * @createdBy 曾宇
         * @date 2022/7/28
         * @methods: gotoTerminalConsumerList
         * @description: 跳转消费者列表
         **/
        gotoTerminalConsumerList(item) {
            const title = item.storeName + '消费者详情';
            const param = {
                title: title,
                storeId: item.storeId,
                consumerLevel: this.pageParam.consumerLevel,
                postId: this.pageParam.postId,
                reportPostnType: this.pageParam.reportPostnType,
            };
            this.$nav.push('/pages/echart/lzlj/consumer-kanban-new/components/consumer-terminal-list-page', param);
        },
        /**
         * 展开、关闭详情
         * @Author:付常涛
         * @Date: 2023/11/15 19:00:28
         */
        showDetail(data, index){
           this.currentIndex = this.currentIndex === index ? 'index' : index;
           this.totalConsumerList.list.forEach((item) => {
               if (item.storeId === data.storeId && !data.show) {
                   data.show = true;
               } else {
                   item.show = false;
               }
           });
           this.$set(this.currentItem, 'data', this.totalConsumerList.list[index]);
           this.$set(this.currentItem, 'index', this.currentIndex);
        }
    }
}
</script>

<style lang="scss">
@import "../css/board";
.consumer-board-detail-list{
    background: #f6f8fa;

    .first-part, .second-part, .second-part-long, .forth-part, .fifth-part, .sixth-part{
        margin: 0 24px;
        padding: 32px 20px;
        border-radius: 20px;
    }

    .info-page-mask{
        background: transparent;
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1;
    }

    .info-description{
        width: 90%;
        position: absolute;
        top: 100px;
        left: 24px;
        z-index: 999;
        background: #ffffff;
        border: 0.5px solid rgba(230,230,230,1);
        border-radius: 10px;
        box-shadow: 0px 3px 10px 0px rgba(199,199,199,0.5);

        .info-icon {
            width: 32px;
            height: 32px;
            position: absolute;
            top: -22px;
            left: 70px;
            color: #ffffff;
        }

        .info-content {
            height: 710px;
            overflow-y: auto;
        }

        .p {
            padding: 12px;
            margin: 0 12px;
            word-break: break-all;
        }

        .p:first-child {
            margin-top: 24px;
        }

        .p:last-child {
            margin-bottom: 24px;
        }

        .table{
            height: 340px;
            overflow-y: auto;
            padding: 12px;
        }
    }

    .third-part{
        padding: 24px;
    }

    .table-data {
        width: 100%;

        .title {
            width: 100%;
            background: #ECF0F9;
            display: flex;
            border-radius: 20px;
            font-size: 24px;
            color: #333333;
            letter-spacing: 0;
            text-align: center;
            line-height: 28px;
            font-weight: 500;

            .title-item {
                box-sizing: border-box;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0 10px;
                text-align: center;
                min-height: 80px;
            }
        }

        .data {
            width: 100%;
            height: 500px;
            overflow-y: auto;
            .data-list {
                width: 100%;
                display: flex;

                .data-list-item {
                    box-sizing: border-box;
                    padding: 14px 0;
                    display: flex;
                    min-height: 100px;
                    align-items: center;
                    justify-content: center;
                    border-bottom: 1px solid #EDF1F4;
                    font-size: 24px;
                    color: #333333;
                    letter-spacing: 0;
                    text-align: center;
                    line-height: 24px;
                    font-weight: 400;
                }
            }
        }
    }

    .first-part .table-data {
        .title .title-item {
            width: 33.3%;
        }
        .data .data-list .data-list-item{
            width: 33.3%;
        }
    }

    .second-part .table-data {
        .title .title-item {
            width: 25%;
        }
        .data .data-list .data-list-item{
            width: 25%;
        }
    }

    .second-part-long .table-data {
        overflow-x: auto;
        .title {
            width: 120%;
            .title-item {
                width: 30%;
            }
        }
        .data {
            width: 120%;
            .data-list {
                .data-list-item {
                    width: 30%;
                }
            }
        }
    }

    .forth-part .table-data {
        overflow-x: auto;
        .title {
            width: 180%;
            .title-item {
                width: 30%;
            }
        }
        .data {
            width: 180%;
            .data-list {
                .data-list-item {
                    width: 30%;
                }
            }
        }
    }

    .fifth-part {
        margin: 40px 24px;
        .report-line-title{
            padding-top: 0;
        }
        .table-data {
            margin-top: 24px;
            .title .title-item {
                width: 50%;
            }
            .data .data-list .data-list-item{
                width: 50%;
            }
        }
    }

    .sixth-part {
        margin-bottom: 40px;
        .table-data {
            .title .title-item {
                width: 40%;
            }

            .title .title-item:first-child {
                width: 60%;
            }

            .data {
                height: 100%;
                .data-list {
                    display: flex;
                    flex-direction: column;

                    .data-list-info {
                        width: 100%;
                        display: flex;

                        .data-list-item {
                            color: inherit;
                            width: 40%;
                        }

                        .data-list-item:first-child {
                            color: inherit;
                            width: 60%;
                        }

                        .data-list-num {
                            position: relative;
                            .link-icon {
                                position: absolute;
                                top: 40px;
                                right: 40px;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
