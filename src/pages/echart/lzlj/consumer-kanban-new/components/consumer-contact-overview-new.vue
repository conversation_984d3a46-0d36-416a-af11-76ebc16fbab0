<!--
 * 消费者触达总览(次)---报表
 * @Author:付常涛
 * @Date: 2023/10/30 16:53:42
-->
<template>
    <view class="consumer-contact-overview">
        <line-title title="消费者触达总览(次)"></line-title>
        <scroll-view class="scroll-view-data" scroll-x="true">
            <view class="select-dimension">
                <select-button :key="index"
                               :label="item.name"
                               :selectedFlag="item.selectedFlag"
                               :value="item.value"
                               isBoard
                               @tap="chooseData($event,item)"
                               v-for="(item,index) in selectDimensionLevel"/>
            </view>
        </scroll-view>
        <view class="consumer-overview-chart">
            <!-- 左侧固定列  -->
            <view class="consumer-overview-left">
                <!-- 标题  -->
                <view class="title-line-item kv-title">
                    K/V序列
                </view>
                <!-- 值  -->
                <view class="other-line">
                    <view class="consumer-kv-left">
                        <view class="kv-name">K序列</view>
                    </view>
                    <view class="consumer-kv-value">
                        <view class="kv-line" v-for="(item, key) in kLineList" :key="key + 'k'">{{item.name}}</view>
                    </view>
                </view>
                <!-- 值  -->
                <view class="other-line">
                    <view class="consumer-kv-left">
                        <view class="kv-name">V序列</view>
                    </view>
                    <view class="consumer-kv-value">
                        <view class="kv-line" v-for="(item, key) in vLineList" :key="key + 'v'">{{item.name}}</view>
                    </view>
                </view>
            </view>
            <!-- 右侧滑动列  -->
            <view class="consumer-overview-right">
                <!-- 标题  -->
                <view class="title-line-item">
                    <!-- 动态列 -->
                    <view class="activity-item" v-if="dynamicColumnList.length > 0">
                        <view class="activity-title">活动邀约</view>
                        <view class="activity-line-title" :style="{width: 168 * dynamicColumnList.length + 'rpx'}">
                            <view class="activity-line-item-title" v-for="(item, i) in dynamicColumnList" :key="i">{{item.name}}</view>
                        </view>
                    </view>
                    <!-- 静态列 -->
                    <view class="title-line-right">
                        <view class="title-item" v-for="(item) in staticColumnList" :key="item.val">{{item.name}}</view>
                    </view>
                </view>
                <!-- 值  -->
                <view class="kv-data-line" v-if="kVal.length > 0">
                    <view class="data-line" v-for="(item, index) in kVal" :key="index">
                        <view class="data-item" v-for="(subItem, subIndex) in item" :key="subIndex"
                              :class="{'item-info': isCheckDetail && subItem.val > 0 && subIndex !== item.length - 1 && subIndex !== item.length - 2}"
                              @tap="gotoItem(subItem, subIndex, index, 'K')">{{subItem.val}}</view>
                    </view>
                </view>
                <!-- 值  -->
                <view class="kv-data-line" v-if="vVal.length > 0">
                    <view class="data-line" v-for="(item, index) in vVal" :key="index">
                        <view class="data-item" v-for="(subItem, subIndex) in item" :key="subIndex"
                              :class="{'item-info': isCheckDetail && subItem.val > 0 && subIndex !== item.length - 1 && subIndex !== item.length - 2}"
                              @tap="gotoItem(subItem, subIndex, index, 'V')">{{subItem.val}}</view>
                    </view>
                </view>
                <!-- 没有数据的情况  -->
                <view class="no-data" v-if="kVal.length <=0 && vVal.length <=0">暂无数据</view>
            </view>
        </view>
    </view>
</template>
<script>
import LineTitle from "../../components/line-title";
import SelectButton from "../../components/select-button";
export default {
    name: 'consumer-contact-overview',
    components: {LineTitle, SelectButton},
    props: {
        dataRange: {
            type: String,
            default: ''
        },
        isCheckDetail: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: String,
            default: "",
        },
        orgId: {
            type: String,
            default: "",
        }
    },
    data () {
        const userInfo = this.$taro.getStorageSync('token').result;
        return {
            params: {},
            selectDimensionLevel: [  // 动销总览-时间范围筛选
                {name: '全部', value: 'all', selectedFlag: false},
                {name: '本财年', value: 'year', selectedFlag: true},
                {name: '本季', value: 'quarter', selectedFlag: false},
                {name: '本月', value: 'month', selectedFlag: false}
            ],
            timeScope: '本财年',
            userInfo,
            kLineList: [],  // k序列的动态行信息数组
            vLineList: [],  // v序列的动态行信息数组
            dynamicColumnList: [], // k序列和v序列的动态列信息数据
            staticColumnList: [
                {name: '礼赠', val: 'gift'},
                {name: '拜访', val: 'visit'},
                {name: '合计', val: 'heJi'},
                {name: '平均触达次数', val: 'pingJun'}
            ], // 静态列数据
            rowMap: [],     // 行对象映射
            columnMap: [],  // 列对象映射
            kVal: [],   // k序列数据
            vVal: [],   // v序列数据
        }
    },
    created () {
        this.switchSetting();
    },
    watch: {
        dataRange (newVal, oldVal) {
            if (newVal) {
                this.switchSetting();
            }
        }
    },
    methods: {
        /**
         * 消费者触达总览(次)-跳转详情
         * @Author:付常涛
         * @Date: 2023/11/14 17:13:45
         * @param item  当前点击对象
         * @param index  (动态列+静态列)活动下标
         * @param kvIndex  K、V序列下标
         * @param source  来源区分K、V序列
         */
        gotoItem (item, index, kvIndex, source) {
            // console.log(item, '----', index, kvIndex, source);
            if (!this.isCheckDetail) return;
            if (item.val <= 0) return
            let sourceFrom = 'activity'
            let columnsLength = this.dynamicColumnList.length + this.staticColumnList.length
            if (index === columnsLength - 3) {
                // 拜访
                sourceFrom = 'visit'
            } else if (index === columnsLength - 4) {
                // 礼赠
                sourceFrom = 'present'
            }
            const allColumn = this.dynamicColumnList.concat(this.staticColumnList);
            let kTypeName = '';
            let vTypeName = '';
            if (source === 'K') {
                kTypeName = this.kLineList[kvIndex].name === '合计' ? '' : this.kLineList[kvIndex].name;
            } else {
                vTypeName = this.vLineList[kvIndex].name === '合计' ? '' : this.vLineList[kvIndex].name;
            }
            this.$nav.push('/pages/echart/lzlj/consumer-kanban-new/consumer-overview-item-new-page', {
                pageFrom: 'ConsumerContact',
                item: {
                    actMClassName: allColumn[index].name, // 活动类型名称
                    kTypeName, // k序列名称
                    vTypeName, // v序列名称
                },
                companyId: this.companyId,
                timeRange: this.timeScope,
                dataRange: this.dataRange,
                sourceFrom
            })
        },
        /**
         * 初始化基础参数配置
         * @Author:付常涛
         * @Date: 2023/11/07 16:03:14
         */
        switchSetting () {
            let params = {
                dataAccess: this.dataRange, // Mine/Team/Area
                timeRange: this.timeScope, // '总数'/'本财年'/'本财季'/'本月'
            };
            this.params = params;
            this.queryTouchOverview();
        },
        /**
         * @desc 选择时间查询维度
         * <AUTHOR>
         * @date 2021/6/3 16:31
         **/
        async chooseData($event, data, type) {
            this.timeObj = $event;
            //（全部All/本年Year/本季Quarter/本月Month
            switch (data.value) {
                case 'all':
                    this.timeScope = '总数';
                    break;
                case 'year':
                    this.timeScope = '本财年';
                    break;
                case 'quarter':
                    this.timeScope = '本财季';
                    break;
                case 'month':
                    this.timeScope = '本月'
                    break;
            }
            this.selectDimensionLevel.forEach((item) => {
                if (item.value === data.value) {
                    data.selectedFlag = true;
                } else {
                    item.selectedFlag = false;
                }
            });
            this.switchSetting();
        },
        /**
         * 处理K、V序列行数据成数组
         * @Author:付常涛
         * @Date: 2023/11/07 15:28:26
         * @param item  当前行数据
         * @param target  K、V序列二维数组
         */
        handlerRowValue(item, target) {
            const row = [];
            // 动态列取值
            const dynamicFields = this.dynamicColumnList.map((i) => i.val);
            dynamicFields.forEach((field) => {
                row.push({val: item[field]});
            })
            // 静态列取值
            row.push(...[
                {val: item.presentTimes}, // 礼赠
                {val: item.visitTimes}, // 拜访
                {val: item.touchTimes}, // 合计
                {val: item.avgTouchTimes ? Number(item.avgTouchTimes).toFixed(2) : ''}, // 平均触达次数
            ])
            target.push(row);
        },
        /**
         * 排序目标数组
         * @Author:付常涛
         * @Date: 2023/11/16 20:26:16
         * @param arr  目标数组
         * @param lovType  值列表
         * @param equalField  判断相等的字段
         */
         async sortTargetArr(arr, lovType, equalField) {
            const res = await this.$lov.getLovByType(lovType);
            if (res && res.length > 0) {
                arr.forEach((item) => {
                    const lov = res.find((i) => i.name === item[equalField]);
                    if (lov) {
                        item.sort = Number(lov.seq);
                    } else {
                        item.sort = 0;
                    }
                });
                arr.sort((item1, item2) => item2.sort - item1.sort);
            }
            return this.$utils.deepcopy(arr);
        },
        /**
         * 处理后端返回的原始数据，处理成前端需要的展示的数据格式
         * @Author:付常涛
         * @Date: 2023/11/07 14:50:17
         * @param data 触达行数据
         * @param column 动态列活动类数据
         */
        async handlerData (data, column) {
            /* 活动邀约动态列 */
            let noSortColumn = column.rows.map(item => ({name: item.fieldName, val: item.field}));
            /* 活动邀约动态列--排序 */
            this.dynamicColumnList = await this.sortTargetArr(noSortColumn, 'ACT_CATEGORY', 'name');
            let kLineList  = [];
            let vLineList  = [];
            const kVal = []; // K序列二维数组
            const vVal = []; // V序列二维数组
            let totalItem = null; // 合计行
            data.rows.forEach(item => {
                if (item.seqType === 'K' && item.seq) {
                    kLineList.push({name: item.seq}); // K序列名
                } else if (item.seqType === 'V' && item.seq) {
                    vLineList.push({name: item.seq}); // V序列名
                } else if (item.seqType === 'ALL' && !item.seq) {
                    // 合计内容
                    totalItem = item;
                }
            });
            /* K/V序列排序 */
            kLineList = await this.sortTargetArr(kLineList, 'ACCT_SUB_TYPE', 'name');
            vLineList = await this.sortTargetArr(vLineList, 'ACCT_MEMBER_LEVEL', 'name');
            /* K/V的按序取值 */
            kLineList.forEach((item) => {
                const obj = data.rows.find((i) => i.seq === item.name && i.seqType === 'K');
                this.handlerRowValue(obj, kVal);
            });
            vLineList.forEach((item) => {
                const obj = data.rows.find((i) => i.seq === item.name && i.seqType === 'V');
                this.handlerRowValue(obj, vVal);
            });
            if (totalItem) {
                this.handlerRowValue(totalItem, kVal);
                this.handlerRowValue(totalItem, vVal);
            }
            this.kVal = kVal;
            this.vVal = vVal;
            if (totalItem) {
                kLineList.push({name: '合计', val: 'heJi'});
                vLineList.push({name: '合计', val: 'heJi'});
            }
            this.kLineList = kLineList;
            this.vLineList = vLineList;
            // console.log('this.kVal', this.$utils.deepcopy(this.kVal));
            // console.log('this.vVal', this.$utils.deepcopy(this.vVal));
            // console.log('this.kLineList', this.$utils.deepcopy(this.kLineList));
            // console.log('this.vLineList', this.$utils.deepcopy(this.vLineList));

            const kLineNumber = this.kLineList.length
            const vLineNumber = this.vLineList.length
            const columnNumber = this.dynamicColumnList.length + this.staticColumnList.length
            const columnList = this.dynamicColumnList.concat(this.staticColumnList)
            this.$store.commit('board/setColumnList', columnList);
            // 合计行的触达总览没有平均触达次数
            this.kVal[kLineNumber-1][columnNumber-1].val = null
            this.vVal[vLineNumber-1][columnNumber-1].val = null
        },
        /**
         * 查询消费者触达总览
         * @Author:付常涛
         * @Date: 2023/11/07 16:03:30
         */
        async queryTouchOverview (companyId, orgId) {
            this.$utils.showLoading();
            const orgParam = {};
            if (this.dataRange === 'Area') {
                orgParam.orgId = this.userInfo.orgId;
                // 我的区域的数据 且 为l3以上层级 且 选择了组织
                if((orgId || this.orgId) && (this.userInfo.orgType === "Company" || this.userInfo.orgType === "Group")) {
                    orgParam.orgId = orgId || this.orgId;
                }
                // 默认设为国窖类型的
                if(!orgId && !this.orgId && (this.userInfo.orgType === "Company" || this.userInfo.orgType === "Group")) {
                    orgParam.orgId = '58929586649432064';
                }
            }
            if (companyId || this.companyId) {
                orgParam.companyId = companyId || this.companyId;
            }
            const columnData = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send", {dmpSrUrl: '/link/board/dynamicActClass', ...this.params, ...orgParam}, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError('查询消费者触达总览的动态列活动类失败！' + response.result);
                }
            });
            if (columnData.success) {
                const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send", {...this.params, dmpSrUrl: '/link/board/csmTouchOverview', ...orgParam}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('查询消费者触达总览失败！' + response.result);
                    }
                });
                if (data.success) {
                    await this.handlerData(data, columnData)
                    this.$utils.hideLoading();
                }
                this.$utils.hideLoading();
            }
        },
    }
}
</script>
<style lang="scss">
@import "../css/new-board";
.consumer-contact-overview {
    .consumer-overview-chart{
        text-align: center;
        display: flex;
        border-bottom: 2px solid #F0F2F8;
        .title-line-item{
            color: #999999;
            height: 176px;
            background:  #F8FAFF;
        }
        .consumer-overview-left{
            border-right: 2px solid #F0F2F8;
            .kv-title{
                width: 222px !important;
                border-right: 2px solid #F0F2F8;
                line-height: 176px;
            }
            .kv-line{
                margin: 24px 0;
                white-space: nowrap;
                overflow-y: hidden;
                overflow-x: auto;
            }
            .other-line{
                width: 222px;
                display: flex;
                align-items: center;
                flex: 1;
                border-bottom: 2px solid #F0F2F8;
                .consumer-kv-left{
                    width: 62px;
                    display: flex;
                    justify-content: center;
                    .kv-name{
                        width: 28px;
                        height: auto;
                        word-wrap: break-word;
                        word-break:break-all;
                    }
                }
                .consumer-kv-value{
                    width: 160px;
                    flex:none;
                    border-left: 2px solid #F0F2F8;
                }
            }
        }
        .consumer-overview-right{
            overflow-x: auto;
            flex: 1;
            display: flex;
            flex-direction: column;
            .activity-item{
                background:  #F8FAFF;
                .activity-title {
                    height: 96px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    min-width: 160px;
                }
                .activity-line-title{
                    display:flex;
                    height: 80px;
                    width: 960px;
                    line-height: 80px;
                    border-top: 2px solid #F0F2F8;
                    .activity-line-item-title{
                        width: 168px;
                    }
                }
            }
            .title-item{
                width: 168px;
                flex: none;
                background:  #F8FAFF;
                &:last-of-type {
                    padding-right: 20px;
                }
            }
            .item-total{
                width: 104px !important;
            }
            .title-line-item{
                display: flex;
            }
            .title-line-right{
                display: flex;
                justify-content: center;
                align-items: center;
                height: 176px;
                background:  #F8FAFF;
                border-left: 2px solid #F0F2F8;
            }
            .kv-data-line{
                padding-right: 20px;
                border-bottom: 2px solid #F0F2F8;
                width: auto;
            }
            .data-line{
                display: flex;
                margin: 24px 0;
            }
            .data-item{
                width: 168px;
                flex: none;
            }
        }
    }
}
</style>
