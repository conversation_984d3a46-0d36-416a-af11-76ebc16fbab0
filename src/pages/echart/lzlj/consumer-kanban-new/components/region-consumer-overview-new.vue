<!--
 * 消费者建设总览/消费者动销总览(我团队、我区域)
 * 各业务消费者建设总览、各区域消费者建设总览
 * 各业务消费者动销总览、各区域消费者动销总览
 * @Author:付常涛
 * @Date: 2023/10/26 22:29:18
-->
<template>
    <view class="region-consumer-overview">
        <line-title :title="lineTitle" :filterName="dataRange === 'Area' ? areaName : ''" @chooseData="chooseOrgData"></line-title>
        <org-select :user-info="userInfo" :show.sync="dialogFlag" @choose="changeOrg"></org-select>
        <scroll-view class="scroll-view-data" scroll-x="true">
            <view class="select-dimension">
                <select-button :key="index"
                               :label="item.name"
                               :selectedFlag="item.selectedFlag"
                               :value="item.value"
                               isBoard
                               @tap="chooseData($event,item)"
                               v-for="(item,index) in selectDimensionLevel"/>
            </view>
        </scroll-view>
        <view class="consumer-overview-chart">
            <view class="consumer-overview-left">
                <view class="overview-line" v-if="dataRange === 'Area'">
                    <view class="title-line-item kv-title">
                        区域
                    </view>
                    <view class="other-line" :class="{'item-info': isCheckDetail}" v-for="(item, index) in lineData" :key="index" @tap="gotoSalesmanItem(item)">{{item.orgName}}</view>
                </view>
                <view class="overview-line" v-if="dataRange === 'Team'">
                    <view class="title-line-item kv-title">
                        业务代表
                    </view>
                    <view class="other-line" v-for="(item, index) in lineData" :key="index">{{item.staffName}}</view>
                </view>
                <view class="overview-line" v-if="dataRange === 'Team'">
                    <view class="title-line-item kv-title">
                        职位
                    </view>
                    <view class="other-line" v-for="(item, index) in lineData" :key="index">{{item.postnName}}</view>
                </view>
            </view>
            <view class="consumer-overview-right">
                <view class="title-line-item">
                    <view class="activity-item">
                        <view class="activity-title">K序列</view>
                        <view class="activity-line-title" v-if="lineData.length > 0">
                            <view class="activity-line-item-title" v-for="(item, index) in dynamicsK" :key="index + 'sK'">{{item.fieldName}}</view>
                        </view>
                    </view>
                    <view class="activity-item">
                        <view class="activity-title">V序列</view>
                        <view class="activity-line-title" v-if="lineData.length > 0">
                            <view class="activity-line-item-title" v-for="(item, index) in dynamicsV" :key="index + 'sV'">{{item.fieldName}}</view>
                        </view>
                    </view>
                </view>
                <view class="all-data">
                    <view class="kv-data-line" v-if="lineData.length > 0 && dynamicsK.length > 0">
                        <view class="data-line" v-for="(item, index) in lineData" :key="index + 'kv'">
                            <view class="data-item" :class="(isCheckDetail && item[lineItem.field] !== '0' && item[lineItem.field] !== 0) ? 'item-info' : ''" v-for="(lineItem, index) in dynamicsK" :key="index + 'lk'" @tap="gotoItem(item, lineItem, item[lineItem.field], 'K')">{{item[lineItem.field]}}</view>
                        </view>
                    </view>
                    <view class="kv-data-line" v-if="lineData.length > 0 && dynamicsV.length > 0">
                        <view class="data-line" v-for="(item, index) in lineData" :key="index + 'vv'">
                            <view class="data-item" :class="(isCheckDetail && item[lineItem.field] !== '0' && item[lineItem.field] !== 0) ? 'item-info' : ''" v-for="(lineItem, index) in dynamicsV" :key="index + 'lv'" @tap="gotoItem(item, lineItem, item[lineItem.field], 'V')">{{item[lineItem.field]}}</view>
                        </view>
                    </view>
                </view>
                <view class="no-data" v-if="lineData.length <=0">暂无数据</view>
                <view class="load-more" v-if="total > 5" @tap="loadMore()">{{ total > lineData.length ? '查看更多' : '' }}</view>
            </view>
        </view>
    </view>
</template>
<script>
import LineTitle from "../../components/line-title";
import SelectButton from "../../components/select-button";
import OrgSelect from "../../components/org-select";
export default {
    name: 'region-consumer-overview',
    components: {LineTitle, SelectButton, OrgSelect},
    props: {
        dataRange: {
            type: String,
            default: ''
        },
        lineTitle: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: ''
        },
        companyId: {
            type: String,
            default: "",
        },
        isCheckDetail: {
            type: Boolean,
            default: false
        }
    },
    data () {
        const userInfo = this.$taro.getStorageSync('token').result;
        return {
            total: 0,
            isShowData: false,
            orgId: '',
            dialogFlag: false,
            lineData: [],
            url: '',
            message: '',
            params: {},
            areaName: '全部',
            selectDimensionLevel: [  // 动销总览-时间范围筛选
                {name: '全部', value: 'all', selectedFlag: false},
                {name: '本财年', value: 'year', selectedFlag: true},
                {name: '本季', value: 'quarter', selectedFlag: false},
                {name: '本月', value: 'month', selectedFlag: false}
            ],
            timeObj: {},
            timeScope: '本财年',
            userInfo,
            target:'',
            dynamicsK: [], // 动态K序列
            dynamicsV: [], // 动态V序列
        }
    },
    async created () {
        // 首次默认进入时如果是l3层级以上，使用国窖公司
        if(!this.orgId && (this.userInfo.orgType === "Company" || this.userInfo.orgType === "Group")) {
            this.orgId = '58929586649432064';
            this.areaName = '泸州老窖国窖酒类销售股份有限公司';
        } else {
            this.orgId = this.userInfo.orgId;
            this.areaName = this.userInfo.orgName;
        }
        // 区分传参
        await this.queryPublicCfg();
        // 根据type配置请求参数
        await this.switchSetting();
    },
    watch: {
        dataRange (newVal, oldVal) {
            if (newVal) {
                this.switchSetting();
            }
        }
    },
    methods: {
        /**
         * 看板顶部选择了组织，xx总览右侧组织名称变更
         * @Author:何春霞
         * @Date:  2024-06-26
         */
        async changeAreaName(orgName, orgId) {
            this.$set(this, 'areaName', orgName);
            this.$set(this, 'orgId', orgId);
            await this.switchSetting(this.orgId);
        },
        /**
         * 加载更多数据,跳转新页面
         * @Author:付常涛
         * @Date: 2023/10/26 22:44:11
         */
        loadMore () {
            this.$nav.push('/pages/echart/lzlj/consumer-kanban-new/consumer-overview-list-new-page.vue', {
                params: this.params,
                url: this.url,
                dataRange: this.dataRange,
                message: this.message,
                type: this.type,
                lineTitle: this.lineTitle,
                isCheckDetail: this.isCheckDetail
            })
        },
        /**
         * 查询参数配置,动态改变请求参数(params.target)
         * @Author:付常涛
         * @Date: 2023/10/26 22:47:05
         */
        async queryPublicCfg () {
            const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', {key: 'Consumer_Sale_Amount'});
            if (data.success && data.value) {
                const companyIds = data.value.split(',');
                this.isShowData = companyIds.indexOf(this.userInfo.coreOrganizationTile['l3Id']) !== -1
            }
        },
        /**
         * @desc 选择组织数据
         * <AUTHOR>
         * @date 2023/7/4 22:44
         **/
        chooseOrgData () {
            this.dialogFlag = !this.dialogFlag;
        },
        /**
         * @desc 组织选择
         * <AUTHOR>
         * @date 2023/7/4 22:41
         * @param item 选择的组织
         **/
        async changeOrg(item) {
            if(Object.keys(item).length === 0) return;
            this.areaName = item.text;
            this.orgId = item.id;
            await this.switchSetting(this.orgId);
        },
        /**
         * 配置初始化
         * @Author:付常涛
         * @Date: 2023/11/10 15:36:11
         * @param orgId 组织ID
         */
        async switchSetting (orgId) {
            this.params = {
                dataAccess: this.dataRange,
                timeRange: this.timeScope
            };
            // console.log(this.type)
            switch (this.type) {
                case 'AreaBuildOverview':
                    // 各区域消费者建设总览
                    this.url = '/link/board/csmAreaConOverview';
                    this.message = '区域消费者建设总览';
                    this.params.orgId = this.orgId;
                    break;
                case 'TeamBuildOverview':
                    // 各业务消费者建设总览
                    this.url = '/link/board/csmStaffConOverview';
                    this.message = '业代消费者建设总览';
                    break;
                case 'TeamOrderOverview':
                    // 各业务消费者动销总览
                    this.url = '/link/board/csmStaffSaleOverview';
                    this.message = '业代消费者动销总览';
                    if(this.isShowData){
                        this.params.saleType = 'amt'
                    }else{
                        this.params.saleType = 'bot'
                    }
                    break;
                case 'AreaOrderOverview':
                    // 各区域消费者动销总览
                    this.url = '/link/board/csmAreaSaleOverview';
                    this.message = '区域消费者动销总览';
                    this.params.orgId = this.orgId;
                    if(this.isShowData){
                        this.params.saleType = 'amt'
                    }else{
                        this.params.saleType = 'bot'
                    }
                    break;
            }
            if (!this.$utils.isEmpty(orgId)) {
                this.params['orgId'] = orgId;
            }
            this.params['page'] = 1;
            this.params['rows'] = 5;
            // console.log(this.params)
            await this.queryDynamicColumn();
            await this.queryOverviewData();
        },
        /**
         * @desc 选择时间查询维度
         * <AUTHOR>
         * @date 2021/6/3 16:31
         **/
        async chooseData($event, data, type) {
            this.timeObj = $event;
            //（全部All/本年Year/本季Quarter/本月Month
            switch (data.value) {
                case 'all':
                    this.timeScope = '总数';
                    break;
                case 'year':
                    this.timeScope = '本财年';
                    break;
                case 'quarter':
                    this.timeScope = '本财季';
                    break;
                case 'month':
                    this.timeScope = '本月'
                    break;
            }
            this.selectDimensionLevel.forEach((item) => {
                if (item.value === data.value) {
                    data.selectedFlag = true;
                } else {
                    item.selectedFlag = false;
                }
            });
            await this.switchSetting(this.dataRange === 'Area' ? this.orgId : '');
        },
        /**
         * 查询动态K/V序列
         * @Author:付常涛
         * @Date: 2023/11/10 16:58:16
         */
        async queryDynamicColumn() {
            let overviewType = '';
            if (['AreaBuildOverview', 'TeamBuildOverview'].includes(this.type)) {
                overviewType = '消费者建设总览';
            } else if (['TeamOrderOverview', 'AreaOrderOverview'].includes(this.type)) {
                overviewType = '消费者动销总览';
            }
            const orgParam = {};
            if (['AreaBuildOverview', 'AreaOrderOverview'].includes(this.type)) {
                orgParam.orgId = this.orgId;
            }
            const promiseList = ['K', 'V'].map((val) => {
                return new Promise(async (resolve, reject) => {
                    const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send",
                        {dataAccess: this.dataRange, timeRange: this.timeScope, companyId: this.companyId, dmpSrUrl: '/link/board/dynamicKVType', seqType: val, overviewType, ...orgParam}, {
                            autoHandleError: false,
                            handleFailed: (response) => {
                                this.$utils.hideLoading();
                                this.$message.error({message: `查询${this.message}的${val}序列动态列失败！` + response.result, customFlag: true});
                                reject(response.result);
                            }
                    });
                    resolve(data);
                })
            });
            const [kColumns, vColumns] = await Promise.all(promiseList);
            /* K/V序列排序 */
            const kColumnsSort = await this.sortTargetArr(kColumns.rows, 'ACCT_SUB_TYPE', 'fieldName');
            const vColumnsSort = await this.sortTargetArr(vColumns.rows, 'ACCT_MEMBER_LEVEL', 'fieldName');
            switch (this.type) {
                case 'AreaBuildOverview':
                case 'TeamBuildOverview':
                    // 各业务消费者建设总览
                    this.dynamicsK = [...kColumnsSort, {field: 'consumerQty', fieldName: '合计'}];
                    this.dynamicsV = [...vColumnsSort, {field: 'consumerQty', fieldName: '合计'}];
                    break;
                case 'AreaOrderOverview':
                case 'TeamOrderOverview':
                    // 各区域消费者动销总览
                    // 各业务消费者动销总览
                    if(this.isShowData){
                        this.dynamicsK = [...kColumnsSort, {field: 'ordAmt', fieldName: '合计'}];
                        this.dynamicsV = [...vColumnsSort, {field: 'ordAmt', fieldName: '合计'}];
                    } else {
                        this.dynamicsK = [...kColumnsSort, {field: 'ordBotQty', fieldName: '合计'}];
                        this.dynamicsV = [...vColumnsSort, {field: 'ordBotQty', fieldName: '合计'}];
                    }
                    break;
            }
        },
        /**
         * 排序目标数组
         * @Author:付常涛
         * @Date: 2023/11/16 20:26:16
         * @param arr  目标数组
         * @param lovType  值列表
         * @param equalField  arr数组中判断相等的字段
         */
         async sortTargetArr(arr, lovType, equalField) {
            const res = await this.$lov.getLovByType(lovType);
            if (res && res.length > 0) {
                arr.forEach((item) => {
                    const lov = res.find((i) => i.name === item[equalField]);
                    if (lov) {
                        item.sort = Number(lov.seq);
                    } else {
                        item.sort = 0;
                    }
                });
                arr.sort((item1, item2) => item2.sort - item1.sort);
            }
            return this.$utils.deepcopy(arr);
        },
        /**
         * 查询区域、业代建设、动销总览
         * @Author:付常涛
         * @Date: 2023/11/10 16:33:11
         */
        async queryOverviewData (companyId) {
            this.$utils.showLoading();
            this.lineData = [];
            if(companyId || this.companyId) {
                this.params.companyId = companyId || this.companyId;
                console.log('companyId', companyId)
                console.log('this.companyId', this.companyId)
            }
            const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send",
                {...this.params, dmpSrUrl: this.url}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$message.error({message: `查询${this.message}数据失败！` + response.result, customFlag: true});
                    }
                });
            if (data.success) {
                this.$utils.hideLoading();
                // 处理业务代表重复的数据
                // const names = new Set();
                // for (const obj of data.rows) {
                //     if (names.has(obj.staffName)) {
                //         obj.staffName = null;
                //     } else {
                //         names.add(obj.staffName);
                //     }
                // }
                // 各区域消费者建设总览 前端按照“合计”数量降序排序
                if (this.type === 'AreaBuildOverview') {
                    const lineData = data.rows.sort((a, b) => {
                        return Number(b.consumerQty) - Number(a.consumerQty);
                    })
                    if (data.rows.length > 5) {
                        this.lineData = lineData.slice(0, 5);
                    } else {
                        this.lineData = lineData;
                    }
                    this.total = data.rows.length;
                } else if (this.type === 'AreaOrderOverview') {
                    // 各区域消费者动销总览 前端按照“合计”数量降序排序
                    let sortField = '';
                    if(this.isShowData) {
                        sortField = 'ordAmt';
                    } else {
                        sortField = 'ordBotQty';
                    }
                    const lineData = data.rows.sort((a, b) => {
                        return Number(b[sortField]) - Number(a[sortField]);
                    })
                    if (data.rows.length > 5) {
                        this.lineData = lineData.slice(0, 5);
                    } else {
                        this.lineData = lineData;
                    }
                    this.total = data.rows.length;
                } else {
                    this.lineData = data.rows;
                    this.total = data.total;
                }
            }
        },
        /**
         * 各区域消费者建设总览点击【区域】下钻
         * @Author:付常涛
         * @Date: 2023/11/20 21:07:14
         * @param item  行数据
         */
        gotoSalesmanItem (item) {
            if (!this.isCheckDetail) return;
            if (this.$utils.isEmpty(item.orgId)) return;
            let param = this.$utils.deepcopy(this.params);
            param['orgId'] = item.orgId;
            // console.log('点击区域下钻', JSON.parse(JSON.stringify(item)), param);
            let url = '';
            switch (this.type) {
                // 各区域消费者建设总览
                case 'AreaBuildOverview':
                    url = '/link/board/csmAreaConDetail';
                    this.message = '业代消费者建设总览';
                    break;
                // 各区域消费者动销总览
                case 'AreaOrderOverview':
                    url = '/link/board/csmStaffSaleOverview';
                    this.message = '业代消费者动销总览';
                    break;
            }
            this.$nav.push('/pages/echart/lzlj/consumer-kanban-new/consumer-overview-item-new-page', {
                pageFrom: 'RegionConsumer',
                orgId: item.orgId,
                companyId: this.companyId,
                url: url,
                type: this.type,
                params: param,
                summaryType: 'Yd'
            })
        },
        /**
         * 跳转详情
         * @Author:付常涛
         * @Date: 2023/11/20 11:24:17
         * @param row 当前行数据
         * @param column 当前列数据
         * @param value 当前数据
         * @param type 当前列K/V类型
         */
        gotoItem (row, column, value, type) {
            // console.log('下钻', row, row.staffName, row.staffId, column.fieldName, value, type);
            if (!this.isCheckDetail) return;
            if (value === '0' || value === 0) return;
            // 各业务消费者动销总览、各区域消费者动销总览
            if (this.type === 'TeamOrderOverview' || this.type === 'AreaOrderOverview') {
                let kTypeName = '';
                let vTypeName = '';
                if (type === 'K' && column.fieldName !== '合计') {
                    kTypeName = column.fieldName;
                } else if (type === 'V' && column.fieldName !== '合计') {
                    vTypeName = column.fieldName;
                }
                const item = {
                    kTypeName, // k序列名称
                    vTypeName, // v序列名称
                }
                if (this.type === 'TeamOrderOverview') {
                    // 各业务消费者动销总览
                    item.staffId = row.staffId; // 业务代表ID
                    item.postnId = row.postnId; // 职位ID
                } else if (this.type === 'AreaOrderOverview') {
                    // 各区域消费者动销总览
                    item.orgId = row.orgId;
                }
                this.$nav.push('/pages/echart/lzlj/consumer-kanban-new/consumer-overview-item-new-page', {
                    pageFrom: 'ConsumerOrder',
                    item,
                    companyId: this.companyId,
                    timeRange: this.timeScope,
                    dataRange: this.dataRange,
                })
            } else {
                /* 各业务消费者建设总览、各区域消费者建设总览：点击K/V序列跳转消费者列表 */
                this.gotoAccountList(row, column, value, type);
            }
        },
        /**
         * 跳转消费者列表
         * @Author:付常涛
         * @Date: 2023/11/20 11:31:44
         * @param row 当前行数据
         * @param column 当前列数据
         * @param value 当前数据
         * @param type 当前列K/V类型
         */
        gotoAccountList (row, column, value, type) {
            if (value === '0' || value === 0) return;
            let kTypeName = '';
            let vTypeName = '';
            if (type === 'K' && column.fieldName !== '合计') {
                kTypeName = column.fieldName;
            } else if (type === 'V' && column.fieldName !== '合计') {
                vTypeName = column.fieldName;
            }
            const filterInfo = {
                timeRange: this.timeScope,
                dataRange: this.dataRange,
            }
            if (this.type === 'AreaBuildOverview') {
                // 各区域消费者建设总览
                filterInfo.orgId = row.orgId; // 组织ID
            } else if (this.type === 'TeamBuildOverview') {
                // 各业务消费者建设总览
                filterInfo.staffId = row.staffId; // 业务代表ID
                filterInfo.postnId = row.postnId; // 职位ID
            }
            this.$nav.push('/pages/lj-consumers/account/account-list-new-page', {
                pageFrom: "ConsumerOverview",
                companyId: this.companyId,
                kTypeName,
                vTypeName,
                filterInfo
            })
        }
    }
}
</script>
<style lang="scss">
@import "../css/new-board";
.region-consumer-overview {
    .load-more{
        width: 100%;
        text-align: center;
        color: #6D96FA;
        margin-top: 32px;
        font-size: 24px;
        padding-bottom: 32px;
    }
    .consumer-overview-chart{
        text-align: center;
        display: flex;
        border-bottom: 2px solid #F0F2F8;
        .title-line-item{
            color: #999999;
            height: 176px;
            background:  #F8FAFF;
        }
        .overview-line {
            border-right: 2px solid #F0F2F8;
        }
        .consumer-overview-left{
            display: flex;
            .kv-title{
                width: 166px !important;
                line-height: 176px;
            }
            .kv-line{
                margin: 24px 0;
            }
            .other-line{
                width: 148px;
                height: 40px;
                padding: 24px 10px;
                flex: none;
                overflow-x: auto;
                overflow-y: hidden;
                white-space: nowrap;
            }
        }
        .consumer-overview-right{
            overflow-x: auto;
            flex: 1;
            display: flex;
            flex-direction: column;
            .activity-item{
                background:  #F8FAFF;
                .activity-title {
                    height: 96px;
                    display: flex;
                    padding-left: 20px;
                    align-items: center;
                    border-right: 2px solid #F0F2F8;
                }
                .activity-line-title{
                    display:flex;
                    height: 80px;
                    width: auto;
                    line-height: 80px;
                    border-top: 2px solid #F0F2F8;
                    border-right: 2px solid #F0F2F8;
                    .activity-line-item-title{
                        width: 160px;
                    }
                }
            }
            .title-item{
                width: 160px;
                flex: none;
                background:  #F8FAFF;
            }
            .item-total{
                width: 104px !important;
            }
            .title-line-item{
                display: flex;
            }
            .title-line-right{
                display: flex;
                justify-content: center;
                align-items: center;
                height: 176px;
                background:  #F8FAFF;
                border-left: 2px solid #F0F2F8;
            }
            .all-data{
                display: flex;
            }
            .kv-data-line{
                width: auto;
            }
            .data-line{
                display: flex;
            }
            .data-item{
                width: 160px;
                flex: none;
                height: 40px;
                padding: 24px 4px;
                overflow-x: auto;
                overflow-y: hidden;
            }
        }
    }
}
</style>
