<!--
 * 消费者动销总览
 * @Author:付常涛
 * @Date: 2023/10/30 17:59:34
-->
<template>
    <view class="consumer-order-overview">
        <line-title title="消费者动销总览"></line-title>
        <scroll-view class="scroll-view-data" scroll-x="true">
            <view class="select-dimension">
                <select-button :key="index"
                               :label="item.name"
                               :selectedFlag="item.selectedFlag"
                               :value="item.value"
                               isBoard
                               @tap="chooseData($event,item)"
                               v-for="(item,index) in selectDimensionLevel"/>
            </view>
        </scroll-view>
        <view class="consumer-order-overview-chart">
            <view class="consumer-order-title">
                <view class="order-line-title first-title-line">K/V序列</view>
                <view class="order-line-title">销售金额</view>
                <view class="order-line-title">销售瓶数</view>
            </view>
            <view class="consumer-order-data">
                <view class="kv-data-line" v-if="kVal.length > 0">
                    <view class="kv-data-line__left">
                        <view class="kv-name">K序列</view>
                    </view>
                    <view class="kv-data-line__right">
                        <view class="data-line" v-for="(item, index) in kVal" :key="index + 'k'">
                            <view class="data-line-item first-line">{{item.seq}}</view>
                            <view class="data-line-item" :class="{'item-info': isCheckDetail}" @tap="gotoItem(item)">{{item.ordAmt}}</view>
                            <view class="data-line-item item-info-other">{{item.ordBotQty}}</view>
                        </view>
                    </view>
                </view>
                <view class="kv-data-line" v-if="vVal.length > 0">
                    <view class="kv-data-line__left">
                        <view class="kv-name">V序列</view>
                    </view>
                    <view class="kv-data-line__right">
                        <view class="data-line" v-for="(item, index) in vVal" :key="index + 'v'">
                            <view class="data-line-item first-line">{{item.seq}}</view>
                            <view class="data-line-item" :class="{'item-info': isCheckDetail && Number(item.ordAmt) !== 0}" @tap="gotoItem(item)">{{item.ordAmt}}</view>
                            <view class="data-line-item item-info-other">{{item.ordBotQty}}</view>
                        </view>
                    </view>
                </view>
                <view class="no-data" v-if="kVal.length <=0 && vVal.length <=0">暂无数据</view>
            </view>
        </view>
    </view>
</template>
<script>
import LineTitle from "../../components/line-title";
import SelectButton from "../../components/select-button";
export default {
    name: 'consumer-order-overview',
    components: {LineTitle, SelectButton},
    props: {
        dataRange: {
            type: String,
            default: ''
        },
        companyId: {
            type: String,
            default: "",
        },
        isCheckDetail: {
            type: Boolean,
            default: false
        }
    },
    data () {
        const userInfo = this.$taro.getStorageSync('token').result;
        return {
            selectDimensionLevel: [  // 动销总览-时间范围筛选
                {name: '全部', value: 'all', selectedFlag: false},
                {name: '本财年', value: 'year', selectedFlag: true},
                {name: '本季', value: 'quarter', selectedFlag: false},
                {name: '本月', value: 'month', selectedFlag: false}
            ],
            timeScope: '本财年',
            userInfo,
            kVal: [],   // k序列数据
            vVal: [],   // v序列数据
        }
    },
    watch: {
        dataRange (newVal, oldVal) {
            if (newVal) {
                this.queryConsumerOrderOverview();
            }
        }
    },
    created () {
        this.queryConsumerOrderOverview();
    },
    methods: {
        /**
         * 选择时间查询维度
         * @Author:付常涛
         * @Date: 2023/11/14 19:55:23
         */
        async chooseData($event, data) {
            //（全部All/本年Year/本季Quarter/本月Month
            switch (data.value) {
                case 'all':
                    this.timeScope = '总数';
                    break;
                case 'year':
                    this.timeScope = '本财年';
                    break;
                case 'quarter':
                    this.timeScope = '本财季';
                    break;
                case 'month':
                    this.timeScope = '本月'
                    break;
            }
            this.selectDimensionLevel.forEach((item) => {
                if (item.value === data.value) {
                    data.selectedFlag = true;
                } else {
                    item.selectedFlag = false;
                }
            });
            await this.queryConsumerOrderOverview();
        },
        /**
         * 查询消费者动销总览数据
         * @Author:付常涛
         * @Date: 2023/11/14 19:55:01
         */
        async queryConsumerOrderOverview (companyId) {
            this.kVal = [];
            this.vVal = [];
            this.$utils.showLoading();
            const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send", {
                dmpSrUrl: '/link/board/csmSaleOverview',
                dataAccess: this.dataRange,
                timeRange: this.timeScope,
                companyId: companyId || this.companyId
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$message.error({message: '查询消费者动销总览数据失败！' + response.result, customFlag: true});
                }
            });
            if (data.success) {
                this.$utils.hideLoading();
                let totalItem = null;
                data.rows.map((item,index) => {
                    if (item.seqType === "K" && item.seq) {
                        item.ordAmt = Number(item.ordAmt).toFixed(2);
                        item.ordBotQty = Number(item.ordBotQty);
                        this.kVal.push(item);
                    } else if (item.seqType === "V" && item.seq) {
                        item.ordAmt = Number(item.ordAmt).toFixed(2);
                        item.ordBotQty = Number(item.ordBotQty);
                        this.vVal.push(item);
                    } else if (item.seqType === "ALL" && !item.seq) {
                        item.seq = "合计";
                        item.ordAmt = Number(item.ordAmt).toFixed(2);
                        item.ordBotQty = Number(item.ordBotQty);
                        totalItem = item;
                    }
                });
                /* K/V序列排序 */
                this.kVal = await this.sortTargetArr(this.kVal, 'ACCT_SUB_TYPE', 'seq');
                this.vVal = await this.sortTargetArr(this.vVal, 'ACCT_MEMBER_LEVEL', 'seq');
                if (totalItem) {
                    this.kVal.push(this.$utils.deepcopy(totalItem));
                    this.vVal.push(this.$utils.deepcopy(totalItem));
                }
            }
        },
        /**
         * 排序目标数组
         * @Author:付常涛
         * @Date: 2023/11/16 20:26:16
         * @param arr  目标数组
         * @param lovType  值列表
         * @param equalField  判断相等的字段
         */
         async sortTargetArr(arr, lovType, equalField) {
            const res = await this.$lov.getLovByType(lovType);
            if (res && res.length > 0) {
                arr.forEach((item) => {
                    const lov = res.find((i) => i.name === item[equalField]);
                    if (lov) {
                        item.sort = Number(lov.seq);
                    } else {
                        item.sort = 0;
                    }
                });
                arr.sort((item1, item2) => item2.sort - item1.sort);
            }
            return this.$utils.deepcopy(arr);
        },
        /**
         * 消费者动销总览--跳转详情
         * @Author:付常涛
         * @Date: 2023/11/14 19:51:02
         * @param item 行数据
         */
        gotoItem(item) {
            if (!this.isCheckDetail) return;
            if (Number(item.ordAmt) === 0) return;
            let kTypeName = '';
            let vTypeName = '';
            if (item.seqType === 'K') {
                kTypeName = item.seq;
            } else if (item.seqType === 'V') {
                vTypeName = item.seq;
            }
            this.$nav.push('/pages/echart/lzlj/consumer-kanban-new/consumer-overview-item-new-page', {
                pageFrom: 'ConsumerOrder',
                item: {
                    kTypeName, // k序列名称
                    vTypeName, // v序列名称
                },
                timeRange: this.timeScope,
                companyId: this.companyId,
                dataRange: this.dataRange,
            })
        }
    }
}
</script>
<style lang="scss">
@import "../css/new-board";
.consumer-order-overview {
    .consumer-order-overview-chart{
        text-align: center;
        .consumer-order-title{
            display: flex;
            width: 100%;
            background: #F8FAFF;
            height: 80px;
            line-height: 80px;
            .order-line-title{
                width: 224px;
            }
            .first-title-line{
                width: 390px !important;
            }
        }
        .consumer-order-data{
            .kv-data-line{
                display: flex;
                border-bottom: 2px solid #F0F2F8;
                &__left{
                    width: 88px;
                    flex: none;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    .kv-name{
                        width: 28px;
                        height: auto;
                        word-wrap: break-word;
                        word-break:break-all;
                    }
                }
                &__right{
                    border-left: 2px solid #F0F2F8;
                    .data-line{
                        margin: 24px 0;
                        display: flex;
                        .data-line-item{
                            flex: none;
                            width: 166px;
                            padding: 0 4px;
                            overflow-x: auto;
                            overflow-y: hidden;
                        }
                        .first-line{
                            width: 214px !important;
                        }
                    }
                }
            }
        }
    }
}
</style>
