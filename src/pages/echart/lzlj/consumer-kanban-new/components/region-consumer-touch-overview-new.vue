<!--
 * 消费者触达总览----各业务消费者触达总览、各区域消费者触达总览
 * @Author:付常涛
 * @Date: 2023/10/30 16:54:18
-->
<template>
    <view class="region-consumer-touch-overview">
        <line-title :title="lineTitle" :filterName="dataRange === 'Area' ? areaName : ''" @chooseData="chooseOrgData"></line-title>
        <org-select v-if="dataRange === 'Area'" :user-info="userInfo" :show.sync="dialogFlag" @choose="changeOrg"></org-select>
        <scroll-view class="scroll-view-data" scroll-x="true" v-if="dataRange">
            <view class="select-dimension">
                <select-button :key="index"
                               :label="item.name"
                               :selectedFlag="item.selectedFlag"
                               :value="item.value"
                               isBoard
                               @tap="chooseData($event,item)"
                               v-for="(item,index) in selectDimensionLevel"/>
            </view>
        </scroll-view>
        <view class="consumer-overview-chart">
            <!-- 左侧固定列  -->
            <view class="consumer-overview-left">
                <!-- 业务代表列  -->
                <view class="kv-line-title" v-if="dataRange !== 'Area'">
                    <!-- 标题  -->
                    <view class="title-line-item">业务代表</view>
                    <!-- 数据  -->
                    <view class="other-line" v-for="(item, index) in businessTouchList"
                          :class="{'item-info': isCheckDetail && dataRange === 'Team'}"
                          :style="{'height': item.lineLength * (rpxToPx(70)+rpxToPx(24)) + 2 * rpxToPx(24) + rpxToPx(2) + 'px'}"
                          @tap="gotoConsumerTouchDetail(item)"
                          :key="index">{{item.staffName}}</view>
                </view>
                <!-- 职位列  -->
                <view class="kv-line-title">
                    <!-- 标题  -->
                    <view class="title-line-item">{{dataRange === 'Area' ? '区域' : '职位'}}</view>
                    <!-- 数据  -->
                    <view class="other-line" v-for="(item, index) in businessTouchList"
                          :class="{'item-info': isCheckDetail && dataRange === 'Area'}"
                          @tap="gotoAreaDetail(item)"
                          :style="{'height': item.lineLength * (rpxToPx(70)+rpxToPx(24)) + 2 * rpxToPx(24) + rpxToPx(2) + 'px'}"
                          :key="index">{{dataRange === 'Area' ? item.orgName : item.postName}}</view>
                </view>
            </view>
            <!-- 右侧滑动列  -->
            <view class="consumer-overview-right">
                <!-- 会员等级列  -->
                <view class="kv-line-title">
                    <!-- 标题  -->
                    <view class="title-line-item kv-title">
                        K/V序列
                    </view>
                    <view v-for="(item, index) in businessTouchList" :key="index">
                        <!-- k序列行名子表 -->
                        <view class="other-line" v-if="item.kLineList && item.kLineList.length > 0">
                            <!-- 左侧总行名 -->
                            <view class="consumer-kv-left">
                                <view class="kv-name">K序列</view>
                            </view>
                            <!-- 右侧子行名 -->
                            <view class="consumer-kv-value">
                                <view class="kv-line" v-for="(subItem, index) in item.kLineList" :key="index + 'k'">{{subItem.name}}</view>
                            </view>
                        </view>
                        <!-- v序列行名子表 -->
                        <view class="other-line" v-if="item.vLineList && item.vLineList.length > 0">
                            <!-- 左侧总行名 -->
                            <view class="consumer-kv-left">
                                <view class="kv-name">V序列</view>
                            </view>
                            <!-- 右侧子行名 -->
                            <view class="consumer-kv-value">
                                <view class="kv-line" v-for="(subItem, index) in item.vLineList" :key="index + 'k'">{{subItem.name}}</view>
                            </view>
                        </view>
                    </view>
                    <view class="no-data" v-if="businessTouchList.length === 0">暂无数据</view>
                </view>
                <!-- 其他列  -->
                <view class="other-data-line">
                    <!-- 标题行  -->
                    <view class="title-line-item">
                        <!-- 动态标题行 -->
                        <view class="activity-item" v-if="dynamicColumnList.length > 0">
                            <view class="activity-title">活动邀约</view>
                            <view class="activity-line-title" :style="{width: 168 * dynamicColumnList.length + 'rpx'}">
                                <view class="activity-line-item-title" v-for="(item, i) in dynamicColumnList" :key="i">{{item.name}}</view>
                            </view>
                        </view>
                        <!-- 静态行 -->
                        <view class="title-line-right">
                            <view class="title-item" v-for="(item) in staticColumnList" :key="item.val">{{item.name}}</view>
                        </view>
                    </view>
                    <view v-for="(item, index) in businessTouchList" :key="index">
                        <!-- k序列子表 -->
                        <view class="kv-data-line" v-if="item.kVal && item.kVal.length > 0">
                            <!-- k序列单行 -->
                            <view class="data-line" v-for="(line, lineIndex) in item.kVal" :key="lineIndex">
                                <!-- 单个数据项 -->
                                <view class="data-item" v-for="(column, columnIndex) in line" :key="columnIndex"
                                      :class="{'item-info': isCheckDetail && column > 0 && ![columnLength-1].includes(columnIndex) && dataRange === 'Team'}"
                                      @tap="gotoItem(column, columnIndex, 'K', item.kLineList, lineIndex, dataRange === 'Area' ? item.orgId : item.staffId, item.postnId)">{{column}}</view>
                            </view>
                        </view>
                        <!-- v序列子表 -->
                        <view class="kv-data-line" v-if="item.vVal && item.vVal.length > 0">
                            <!-- v序列单行 -->
                            <view class="data-line" v-for="(line, lineIndex) in item.vVal" :key="lineIndex">
                                <!-- 单个数据项 -->
                                <view class="data-item" v-for="(column, columnIndex) in line" :key="columnIndex"
                                      :class="{'item-info': isCheckDetail && column > 0 &&![columnLength-1].includes(columnIndex) && dataRange === 'Team'}"
                                      @tap="gotoItem(column, columnIndex, 'V', item.vLineList, lineIndex, dataRange === 'Area' ? item.orgId : item.staffId, item.postnId)">{{column}}</view>
                            </view>
                        </view>
                        <!-- 暂无数据 -->
                        <view class="no-data" v-if="!item.vVal || !item.vVal || item.kVal.length <=0 && item.vVal.length <=0">暂无数据</view>
                    </view>
                </view>
            </view>
        </view>
        <view class="load-more" v-if="total > 5" @tap="loadMore()">加载更多</view>
    </view>
</template>
<script>
import LineTitle from "../../components/line-title";
import SelectButton from "../../components/select-button";
import OrgSelect from "../../components/org-select";
export default {
    name: 'region-consumer-touch-overview',
    components: {LineTitle, SelectButton, OrgSelect},
    props: {
        dataRange: {
            type: String,
            default: ''
        },
        lineTitle: {
            type: String,
            default: ''
        },
        companyId: {
            type: String,
            default: "",
        },
        type: {
            type: String,
            default: ''
        },
        isCheckDetail: {
            type: Boolean,
            default: false
        }
    },
    data () {
        const userInfo = this.$taro.getStorageSync('token').result;
        return {
            userInfo,
            url: '',        // 发送请求的路径
            params: {
                page: 1,
                rows: 5
            },     // 发送请求的参数
            orgId: '',
            dialogFlag: false,
            areaName: '全部',
            timeScope: '本财年',
            selectDimensionLevel: [  // 动销总览-时间范围筛选
                {name: '全部', value: 'all', selectedFlag: false},
                {name: '本财年', value: 'year', selectedFlag: true},
                {name: '本季', value: 'quarter', selectedFlag: false},
                {name: '本月', value: 'month', selectedFlag: false}
            ],
            businessTouchList: [],         // 各业务消费者触达总览（全部的数据）
            columnMap: [],          // 列数据的key值和展示值的映射数组
            rowMap: [],             // 行数据的key值和展示值的映射数组
            columnLength: 0,       // 所有列（动态列和静态列）的个数
            dynamicColumnList: [], // k序列和v序列的动态列信息数据
            total: 0,               // 后台的数据总数
            staticColumnList: [     // 静态列信息数组
                {name: '礼赠', val: 'presentTimes'},
                {name: '拜访', val: 'visitTimes'},
                {name: '合计', val: 'touchTimes'}
            ] // 静态列数据
        }
    },
    async created () {
        // 获取行和列的映射对象
        // 首次默认进入时如果是l3层级以上，使用国窖公司
        if(!this.orgId && (this.userInfo.orgType === "Company" || this.userInfo.orgType === "Group")) {
            this.orgId = '58929586649432064';
            this.areaName = '泸州老窖国窖酒类销售股份有限公司';
        } else {
            this.orgId = this.userInfo.orgId;
            this.areaName = this.userInfo.orgName;
        }
        this.switchSetting();
    },
    watch: {
        dataRange (newVal) {
            if (newVal) {
                this.resetData(newVal)
                this.chooseData({},  {name: '本财年', value: 'year', selectedFlag: true})
            }
        }
    },
    methods: {
        /**
         * 看板顶部选择了组织，xx总览右侧组织名称变更
         * @Author:何春霞
         * @Date:  2024-06-26
         */
         async changeAreaName(orgName, orgId) {
            this.$set(this, 'areaName', orgName);
            this.$set(this, 'orgId', orgId);
            await this.switchSetting(this.orgId);
            console.log('this.areaName', this.areaName);
            console.log('this.orgId', this.orgId);
        },
        /**
         * 重置数据
         * @Author:付常涛
         * @Date: 2024/01/06 14:21:57
         * @param dataRangeNewVal  新的范围
         */
        resetData (dataRangeNewVal) {
            this.params =  {
                page: 1,
                rows: 5
            }
            this.dialogFlag = false
            if (dataRangeNewVal === 'Area') {
                this.orgId = this.userInfo.orgId;
                this.areaName = this.userInfo.orgName;
            } else {
                this.areaName = '全部';
                this.orgId = this.userInfo.orgId;
            }
            this.businessTouchList = []
            this.dynamicColumnList = []
            this.columnLength = 0
            this.total = 0
        },
        /**
         * 各区域消费者触达总览-点击区域下钻
         * @Author:付常涛
         * @Date: 2023/11/21 11:09:24
         * @param item 行数据
         */
        gotoAreaDetail (item) {
            if (!this.isCheckDetail) return;
            if (this.dataRange !== 'Area') return
            // console.log('item', item)
            this.$nav.push('/pages/echart/lzlj/consumer-kanban-new/consumer-overview-list-new-page.vue', {
                url:'/link/board/csmAreaTouchDetail',
                params: {
                    dataAccess: this.dataRange,
                    timeRange: this.timeScope,
                    companyId: this.companyId,
                    orgId: item.orgId,
                    rows: 10,
                },
                isCollapse: true,      // 是否折叠
                isCheckDetail: true,    // 是否可以下钻
                lineTitle: item.orgName + '消费者触达总览',
                message: item.orgName + '消费者触达总览',
                dataRange: this.dataRange,
                type: 'AreaTouchDetailOverview',
            })
        },
        /**
         * @desc 将rpx转为px
         * <AUTHOR>
         * @date 2023/7/13
         * @param rpx 要转换的rpx值
         * @return px 返回的px值
         **/
        rpxToPx(rpx) {
            const systemInfo = wx.getSystemInfoSync();
            const pixelRatio = systemInfo.pixelRatio;
            const screenWidth = systemInfo.screenWidth;
            const designWidth = 750; // 设计稿宽度，可以根据实际情况进行调整
            const px = rpx * screenWidth / designWidth;
            // console.log('px', px);
            return Math.floor(px);
        },
        /**
         * 加载更多数据,跳转新页面
         * @Author:付常涛
         * @Date: 2023/11/21 20:57:07
         */
        loadMore () {
            this.$nav.push('/pages/echart/lzlj/consumer-kanban-new/consumer-overview-list-new-page.vue', {
                params: this.params,
                url: this.url,
                dataRange: this.dataRange,
                message: this.lineTitle,
                type: this.type,
                lineTitle: this.lineTitle,
                timeScope: this.timeScope,
                isCheckDetail: this.isCheckDetail,
                pageFrom: 'ConsumerContactRegion'
            })
        },
        /**
         * 点击业务代表跳转消费者触达明细(各业务消费者触达总览)
         * @Author:付常涛
         * @Date: 2023/11/20 14:54:10
         * @param item  行数据
         */
        gotoConsumerTouchDetail (item) {
            if (!this.isCheckDetail) return;
            if (this.dataRange !== 'Team') return
            let sourceFrom = 'activity'
            this.$nav.push('/pages/echart/lzlj/consumer-kanban-new/consumer-overview-item-new-page.vue', {
                pageFrom: 'ConsumerTouchDetail',
                item: {staffId: item.staffId, postnId: item.postnId},
                timeRange: this.timeScope,
                dataRange: this.dataRange,
                companyId: this.companyId,
                sourceFrom
            })
        },
        /**
         * 点击活动列，跳转消费者触达详情
         * @Author:付常涛
         * @Date: 2023/11/20 17:11:27
         * @param value 点击的数据
         * @param index 活动邀约动态列的序号
         * @param source K、V
         * @param sourceList K、V的动态行
         * @param sourceIndex K、V的动态行序号
         * @param id 业务代表ID或组织ID
         * @param postnId 职位ID
         */
        gotoItem (value, index, source, sourceList, sourceIndex, id, postnId) {
            const columnList = this.dynamicColumnList.concat(this.staticColumnList);
            const actMClassName = columnList[index].name;
            const typeName = sourceList[sourceIndex].name;
            // console.log(actMClassName, typeName);
            // console.log(value, source, id);
            if (!this.isCheckDetail) return;
            if (index === columnsLength - 1) return; // 合计列不能下钻
            if (this.dataRange !== 'Team') return; // 只有团队才能下钻
            if (value <= 0) return
            let sourceFrom = 'activity'
            let columnsLength = this.dynamicColumnList.length + this.staticColumnList.length
            if (index === columnsLength - 2) {
                sourceFrom = 'visit'
            } else if (index === columnsLength - 3) {
                sourceFrom = 'present'
            }
            let kTypeName = '';
            let vTypeName = '';
            if (source === 'K') {
                kTypeName = typeName === '合计' ? '' : typeName;
            } else {
                vTypeName = typeName === '合计' ? '' : typeName;
            }
            const item = {
                actMClassName, // 活动类型名称
                kTypeName, // k序列名称
                vTypeName, // v序列名称
            }
            if (this.type === 'TeamTouchOverview') {
                // 各业务消费者触达总览
                item.staffId = id;
                item.postnId = postnId;
            } else if (this.type === 'AreaTouchOverview') {
                // 各区域消费者触达总览
                item.orgId = id;
            }
            this.$nav.push('/pages/echart/lzlj/consumer-kanban-new/consumer-overview-item-new-page.vue', {
                pageFrom: 'ConsumerContactRegion',
                item,
                timeRange: this.timeScope,
                dataRange: this.dataRange,
                companyId: this.companyId,
                sourceFrom
            })
        },
        /**
         * 初始化基础参数配置
         * @Author:付常涛
         * @Date: 2023/11/11 13:43:55
         */
        async switchSetting (orgId) {
            let params = {};
            if (this.dataRange === 'Team') {
                // 各业务消费者触达总览
                this.url = '/link/board/csmStaffTouchOverview'
                params = {
                    dataAccess:this.dataRange,
                    timeRange: this.timeScope
                }
                delete this.params.orgId
            } else if (this.dataRange === 'Area') {
                // 各区域消费者触达总览
                this.url = '/link/board/csmAreaTouchOverview'
                params= {
                    dataAccess:this.dataRange,
                    orgId: this.orgId || this.userInfo.orgId,
                    timeRange: this.timeScope
                };
                delete this.params.postnId
                if (!this.$utils.isEmpty(orgId)) {
                   params['orgId'] = orgId;
                }
            } else if (this.pageFrom === 'areaDetail') {
                this.url = '/link/touchOverview/queryByOrgDetailPage'
                params = {
                    timeLevel: this.pageParam.timeScope,
                    orgId: this.pageParam.orgId
                };
            }
            Object.assign(this.params, params)
            await this.queryDynamicColumn();
            await this.queryTouchOverview();
        },
        /**
         * @desc 选择组织数据
         * <AUTHOR>
         * @date 2023/7/4 22:44
         **/
        chooseOrgData () {
            this.dialogFlag = !this.dialogFlag;
        },
        /**
         * @desc 组织选择
         * <AUTHOR>
         * @date 2023/7/4 22:41
         * @param item 选择的组织
         **/
        async changeOrg(item) {
            if(Object.keys(item).length === 0) return;
            this.areaName = item.text;
            this.orgId = item.id;
            this.switchSetting(this.orgId);
        },
        /**
         * @desc 选择时间查询维度
         * <AUTHOR>
         * @date 2021/6/3 16:31
         **/
        async chooseData($event, data, type) {
            this.timeObj = $event;
            //（全部All/本年Year/本季Quarter/本月Month
            switch (data.value) {
                case 'all':
                    this.timeScope = '总数';
                    break;
                case 'year':
                    this.timeScope = '本财年';
                    break;
                case 'quarter':
                    this.timeScope = '本财季';
                    break;
                case 'month':
                    this.timeScope = '本月'
                    break;
            }
            this.selectDimensionLevel.forEach((item) => {
                item.selectedFlag = item.value === data.value;
            });
            this.switchSetting();
        },
        /**
         * 获取活动邀约的动态列
         * @Author:付常涛
         * @Date: 2023/11/11 12:00:03
         */
        async queryDynamicColumn() {
            const orgParam = {};
            if (this.dataRange === 'Area') {
                orgParam.orgId = this.orgId;
            }
            const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send",
                {dataAccess: this.dataRange, timeRange: this.timeScope, dmpSrUrl: '/link/board/dynamicActClass', ...orgParam}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$message.error({message: `查询${this.lineTitle}的动态列失败！` + response.result, customFlag: true});
                        reject(response.result);
                    }
            });
            /* 活动邀约动态列 */
            let noSortColumn = data.rows.map((i) => ({name: i.fieldName, val: i.field}));
            /* 活动邀约动态列--排序 */
            const lovList = await this.$lov.getLovByType('ACT_CATEGORY');
            this.dynamicColumnList = this.sortTargetArr(noSortColumn, lovList, 'name');
        },
        /**
         * 查询消费者触达总览
         * @Author:付常涛
         * @Date: 2023/11/13 21:16:30
         */
        async queryTouchOverview (companyId) {
            this.$utils.showLoading();
            this.params.rows = 5;
            if(companyId || this.companyId) {
                this.params.companyId = companyId || this.companyId;
            }
            const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send",
                {...this.params, dmpSrUrl: this.url}, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError(`查询${this.lineTitle}失败！` + response.result);
                }
            });
            if (data.success) {
                await this.dealwithData(data.rows, data.total);
                this.columnLength = this.dynamicColumnList.length + this.staticColumnList.length
                this.$utils.hideLoading();
            }
        },
        /**
         * 处理后端数据成前端所需要的格式
         * @Author:付常涛
         * @Date: 2023/11/11 16:13:28
         * @param res  后端返回
         * @param total  后端返回的总数
         */
        async dealwithData(res, total) {
            let list = [];
            const deal = (obj, cur) => {
                const columnList = this.dynamicColumnList.concat(this.staticColumnList)
                if (cur.seqType === 'K' && cur.seq) {
                    const arr = [];
                    columnList.forEach((i) => {
                        arr.push(cur[i.val]);
                    });
                    obj.kLineList.push({name: cur.seq, data: arr});
                } else if (cur.seqType === 'V' && cur.seq) {
                    const arr = [];
                    columnList.forEach((i) => {
                        arr.push(cur[i.val]);
                    });
                    obj.vLineList.push({name: cur.seq, data: arr});
                } else if (cur.seqType === 'ALL' && !cur.seq) {
                    cur.seq = '总计';
                    const arr = [];
                    columnList.forEach((i) => {
                        arr.push(cur[i.val]);
                    });
                    obj.heji = arr;
                }
            }
            // 处理动态列、K/V
            for (let index = 0; index < res.length; index++) {
                const cur = res[index];
                let item = null;
                if (this.dataRange === 'Area') {
                    item = list.find((i) => i.orgId === cur.orgId);
                } else if (this.dataRange === 'Team') {
                    item = list.find((i) => i.staffId === cur.staffId && i.postName === cur.postName);
                }
                if (item) {
                    deal(item, cur);
                } else {
                    const newObj = {
                        kLineList: [],
                        kVal: [],
                        vLineList: [],
                        vVal: [],
                        heji: {}
                    };
                    if (this.dataRange === 'Area') {
                        newObj.orgId = cur.orgId;
                        newObj.orgName = cur.orgName;
                    } else if (this.dataRange === 'Team') {
                        newObj.staffId = cur.staffId;
                        newObj.staffName = cur.staffName;
                        newObj.postName = cur.postName;
                        newObj.postnId = cur.postnId;
                    }
                    deal(newObj, cur);
                    list.push(newObj);
                }
            }
            const kLov = await this.$lov.getLovByType('ACCT_SUB_TYPE');
            const vLov = await this.$lov.getLovByType('ACCT_MEMBER_LEVEL');
            list.forEach((i) => {
                /* K/V序列排序 */
                i.kLineList = this.sortTargetArr(i.kLineList, kLov, 'name');
                i.vLineList = this.sortTargetArr(i.vLineList, vLov, 'name');
                /* 按照顺序赋值 */
                i.kLineList.forEach((item) => {
                    i.kVal.push(item.data);
                    delete item.data;
                });
                i.vLineList.forEach((item) => {
                    i.vVal.push(item.data);
                    delete item.data;
                });
                // 处理合计列及统计K/V长度
                i.kLineList.push({name: '合计'});
                i.kVal.push(i.heji);
                i.vLineList.push({name: '合计'});
                i.vVal.push(i.heji);
                i.lineLength = i.kLineList.length + i.vLineList.length;
            });
            /* 按照<k序列>列的“合计”降序展示 */
            list.forEach((item) => {
                if(this.type === 'AreaTouchOverview'){
                    const kVal = item.kVal.length > 0 ? item.kVal[item.kVal.length - 1] : [];
                    item.sortSumTotal = kVal.length > 0 ? kVal[kVal.length - 1] : 0;
                }
            });
            if(this.type === 'AreaTouchOverview') {
                list.sort((a, b) => b.sortSumTotal - a.sortSumTotal);
                // 各区域消费者触达总览--请求所有数据后手动排序
                if (list.length > 5) {
                    this.total = list.length;
                    list = list.slice(0, 5);
                } else {
                    this.total = -1;
                }
            } else {
                this.total = total;
            }
            this.businessTouchList = list;
            // console.log('this.businessTouchList', JSON.parse(JSON.stringify(this.businessTouchList)));
        },
        /**
         * 排序目标数组(非异步)
         * @Author:付常涛
         * @Date: 2023/11/16 20:26:16
         * @param arr  目标数组
         * @param lovType  值列表
         * @param equalField  判断相等的字段
         */
        sortTargetArr(arr, lovType, equalField) {
            // const res = await this.$lov.getLovByType(lovType);
            const res = lovType;
            if (res && res.length > 0) {
                arr.forEach((item) => {
                    const lov = res.find((i) => i.name === item[equalField]);
                    if (lov) {
                        item.sort = Number(lov.seq);
                    } else {
                        item.sort = 0;
                    }
                });
                arr.sort((item1, item2) => item2.sort - item1.sort);
            }
            return this.$utils.deepcopy(arr);
        },
    }
}
</script>
<style lang="scss">
@import "../css/new-board";
.region-consumer-touch-overview {
    $line-height: 70px;
    .consumer-overview-chart{
        text-align: center;
        display: flex;
        .title-line-item{
            color: #999999;
            height: 176px;
            background:  #F8FAFF;
        }
        .consumer-overview-left{
            display: flex;
            .kv-line-title{
                width: 150px;
                border-right: 2px solid #F0F2F8;
                border-bottom: 2px solid #F0F2F8;
                display: flex;
                flex-direction: column;
            }
            .title-line-item{
                line-height: 176px;
            }
            .other-line{
                display: flex;
                align-items: center;
                justify-content: center;
                flex: none;
                border-bottom: 2px solid #F0F2F8;
            }
        }
        .consumer-overview-right{
            overflow-x: auto;
            flex: 1;
            display: flex;
            .kv-title{
                width: 222px !important;
                border-right: 2px solid #F0F2F8;
                line-height: 176px;
            }
            .kv-line{
                margin: 24px 0;
                height: $line-height;
                line-height: $line-height;
                white-space: nowrap;
                overflow-y: hidden;
                overflow-x: auto;
            }
            .other-line{
                width: 222px;
                display: flex;
                align-items: center;
                flex: 1;
                border-bottom: 2px solid #F0F2F8;
                .consumer-kv-left{
                    width: 62px;
                    display: flex;
                    justify-content: center;
                    .kv-name{
                        width: 28px;
                        height: auto;
                        word-wrap: break-word;
                        word-break:break-all;
                    }
                }
                .consumer-kv-value{
                    width: 160px;
                    flex:none;
                    border-left: 2px solid #F0F2F8;
                }
            }
            .other-data-line{
                display: flex;
                flex-direction: column;
            }
            .activity-item{
                background:  #F8FAFF;
                .activity-title {
                    height: 96px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
                .activity-line-title{
                    display:flex;
                    height: 80px;
                    width: 960px;
                    line-height: 80px;
                    border-top: 2px solid #F0F2F8;
                    .activity-line-item-title{
                        width: 160px;
                    }
                }
            }
            .title-item{
                width: 168px;
                flex: none;
                background:  #F8FAFF;
            }
            .item-total{
                width: 104px !important;
            }
            .title-line-item{
                display: flex;
                justify-content: center;
            }
            .title-line-right{
                display: flex;
                justify-content: center;
                align-items: center;
                height: 176px;
                background:  #F8FAFF;
                border-left: 2px solid #F0F2F8;
            }
            .kv-data-line{
                border-bottom: 2px solid #F0F2F8;
                width: auto;
            }
            .data-line{
                display: flex;
                margin: 24px 0;
            }
            .data-item{
                width: 168px;
                flex: none;
                height: $line-height;
                line-height: $line-height;
            }
        }
    }
    .load-more{
        width: 100%;
        text-align: center;
        color: #6D96FA;
        margin-top: 32px;
        font-size: 24px;
        padding-bottom: 32px;
    }
}
</style>
