<template>
    <view class="consumer-distribution-board" v-show="show">
        <line-title title="所属消费者分布情况" style="margin-left: 14px;"/>
        <view class="terminal-level-content">
            <link-echart :option="consumerLevelOption"
                         :force-use-old-canvas="false"
                         :height="capacityLevelBarYCategoryHeight+'px'"
                         ref="consumerLevelEchart" :loading="levelLoadingFlag"/>
        </view>
        <view class="terminal-level-content">
            <link-echart :option="consumerClassOption"
                         :force-use-old-canvas="false"
                         :height="capacityLevelBarYCategoryHeight+'px'"
                         ref="consumerClassEchart" :loading="classLoadingFlag" />
        </view>
    </view>
</template>

<script>
import {targetPieChartProgress} from "../../echart.utils";
import LineTitle from "../../components/line-title";

export default {
    name: "consumer-distribution-board",
    components: {LineTitle},
    props:{
        show: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: ''
        },
        pageParam: {
            type: Object,
            default: {}
        },
        companyId: {
            type: String,
            default: ''
        },
        param: {
            type: Object,
            default: {}
        }
    },
    data() {
        return {
            consumerLeverRows: {},
            consumerLevelOption: null,     // 消费者分级
            consumerClassOption: null,     // 消费者分类
            capacityLevelBarYCategoryHeight: (this.$device.systemInfo.windowWidth - 24) * 0.6285714 < 220 ? (this.$device.systemInfo.windowWidth - 24) * 0.6285714 : 220,
            levelLoadingFlag: false,
            classLoadingFlag: false
        };
    },
    watch: {
        show: {
            async handler(val) {
                if (val) {
                    this.$utils.showLoading();
                    switch (this.type) {
                        case 'consumerList':
                            this.$nextTick(async () => {
                                await this.accountCountUnderByType();
                                await this.accountCountUnderByLevel();
                            });
                            break;
                        case 'consumerDetail':
                            this.$nextTick(async () => {
                                await this.followConsumerTypeUnder();
                                await this.followConsumerLevelUnder();
                            });
                            break;
                        default:
                            break;
                    }
                    this.$utils.hideLoading();
                }
            },
            immediate: true
        }
    },
    created() {
        console.log('3', this.param)
    },
    methods: {
        /**
         * @createdBy 曾宇
         * @date 2022/8/1
         * @methods: followConsumerTypeUnder
         * @description: 查询业代跟进消费者分类
         **/
        async followConsumerTypeUnder() {
            let url = '/link/appBoard/followConsumerTypeUnder';
            let param = {
                postId: this.pageParam.postId,
                reportPostnType: this.pageParam.reportPostnType,
                dateType: this.pageParam.dateType
            };
            if (this.pageParam.reportPostnType === 'Headquarters') {
                url = '/link/appBoard/queryConTypeBySalesRegion'
                param = {
                    orgId: this.pageParam.orgId,
                    dateType: this.pageParam.dateType
                }
            }
            this.levelLoadingFlag = true;
            const data = await this.$http.post(this.$env.dmpURL + url, param, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.levelLoadingFlag = false;
                    this.$showError('查询业代跟进消费者分类失败:' + response.result);
                }
            });
            if (data.success) {
                const dataList = data.result;
                const seriesDataTemp = [];
                let totalSeriesData = [];
                for (let i = 0; i < dataList.length; i++) {
                    let name = await this.$lov.getNameByTypeAndVal('ACCT_SUB_TYPE', dataList[i].consumerType);
                    seriesDataTemp.push({
                        value: parseInt(dataList[i].typeCount),
                        name: name
                    })
                }
                // 计算总数
                let total = seriesDataTemp.reduce((total, item) => total + Number(item.value), 0);
                // 总计小于等于0，则说明数据为空，则为以下提示
                if (total <= 0) {
                    totalSeriesData = [{value: 0, name: '暂未统计到数据'}]
                } else {
                    totalSeriesData = [{value: total, name: '影响力K序列'}];
                }
                this.consumerLevelOption = echartInitConfig => targetPieChartProgress(echartInitConfig, seriesDataTemp, totalSeriesData, ['47%', '70%'], '47%', [], 225, 'number', null, null, 5, 'smallSize');
            }
            this.levelLoadingFlag = false;
        },
        /**
         * @createdBy 曾宇
         * @date 2022/8/1
         * @methods: followConsumerLevelUnder
         * @description: 查询业代跟进消费者分级
         **/
        async followConsumerLevelUnder() {
            this.classLoadingFlag = true;
            let url = '/link/appBoard/followConsumerLevelUnder';
            let param = {
                postId: this.pageParam.postId,
                reportPostnType: this.pageParam.reportPostnType,
                dateType: this.pageParam.dateType
            };
            if (this.pageParam.reportPostnType === 'Headquarters') {
                url = '/link/appBoard/queryConLeveBySalesRegion'
                param = {
                    orgId: this.pageParam.orgId,
                    dateType: this.pageParam.dateType
                }
            }
            const data = await this.$http.post(this.$env.dmpURL + url, param, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.classLoadingFlag = false;
                    this.$showError('查询业代跟进消费者分级失败:' + response.result);
                }
            });
            if (data.success) {
                const dataList = data.result;
                const seriesDataTemp = [];
                let totalSeriesData = [];
                for (let i = 0; i < dataList.length; i++) {
                    let name = await this.$lov.getNameByTypeAndVal('ACCT_MEMBER_LEVEL', dataList[i].consumerType);
                    seriesDataTemp.push({
                        value: parseInt(dataList[i].typeCount),
                        name: name
                    })
                }
                // 计算总数
                let total = seriesDataTemp.reduce((total, item) => total + Number(item.value), 0);
                // 总计小于等于0，则说明数据为空，则为以下提示
                if (total <= 0) {
                    totalSeriesData = [{value: 0, name: '暂未统计到数据'}]
                } else {
                    totalSeriesData = [{value: total, name: '购买力V序列'}];
                }
                this.consumerClassOption = echartInitConfig => targetPieChartProgress(echartInitConfig, seriesDataTemp, totalSeriesData, ['47%', '70%'], '47%', [], 225, 'number', null, null, 5, 'smallSize');
            }
            this.classLoadingFlag = false;
        },
        /**
         * 查询客户消费者分类
         * @Author:付常涛
         * @Date: 2023/11/14 23:09:43
         */
        async accountCountUnderByType() {
            this.levelLoadingFlag = true;
            const param = {
                dataAccess: this.pageParam.dataAccess,
                timeRange: this.pageParam.timeRange,
                acctTypeName: this.pageParam.acctTypeName,
                acctId: this.param.acctId,
                seqType: 'K',
                companyId: this.companyId
            };
            console.log('param.companyId', param.companyId)
            const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send", {...param, dmpSrUrl: '/link/boardDetail/csmAcctQtyDetail'}, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.levelLoadingFlag = false;
                    this.$showError('查询客户消费者分类失败:' + response.result);
                }
            });
            if (data.success) {
                const dataList = data.rows;
                let totalSeriesData = [];
                const seriesDataTemp = dataList.map((i) => ({name: i.kTypeName, value: parseInt(i.consumerQty)}));
                // 计算总数
                let total = seriesDataTemp.reduce((total, item) => total + Number(item.value), 0);
                // 总计小于等于0，则说明数据为空，则为以下提示
                if (total <= 0) {
                    totalSeriesData = [{value: 0, name: '暂未统计到数据'}]
                } else {
                    totalSeriesData = [{value: total, name: '影响力K序列'}];
                }
                this.consumerLevelOption = echartInitConfig => targetPieChartProgress(echartInitConfig, seriesDataTemp, totalSeriesData, ['47%', '70%'], '47%', [], 225, 'number', null, null, 5, 'smallSize');
            }
            this.levelLoadingFlag = false;
        },
        /**
         * 查询客户消费者分级
         * @Author:付常涛
         * @Date: 2023/11/15 18:50:16
         */
        async accountCountUnderByLevel() {
            this.classLoadingFlag = true;
            const param = {
                dataAccess: this.pageParam.dataAccess,
                timeRange: this.pageParam.timeRange,
                acctTypeName: this.pageParam.acctTypeName,
                acctId: this.param.acctId,
                seqType: 'V',
                companyId: this.companyId
            };
            console.log('param.companyId', param.companyId)
            const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send", {...param, dmpSrUrl: '/link/boardDetail/csmAcctQtyDetail'}, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.classLoadingFlag = false;
                    this.$showError('查询客户消费者分级失败:' + response.result);
                }
            });
            if (data.success) {
                const dataList = data.rows;
                let totalSeriesData = [];
                const seriesDataTemp = dataList.map((i) => ({name: i.vTypeName, value: parseInt(i.consumerQty)}));
                // 计算总数
                let total = seriesDataTemp.reduce((total, item) => total + Number(item.value), 0);
                // 总计小于等于0，则说明数据为空，则为以下提示
                if (total <= 0) {
                    totalSeriesData = [{value: 0, name: '暂未统计到数据'}]
                } else {
                    totalSeriesData = [{value: total, name: '购买力V序列'}];
                }
                this.consumerClassOption = echartInitConfig => targetPieChartProgress(echartInitConfig, seriesDataTemp, totalSeriesData, ['47%', '70%'], '47%', [], 225, 'number', null, null, 5, 'smallSize');
            }
            this.classLoadingFlag = false;
        }
    }
}
</script>

<style lang="scss">
@import "../css/board";
.consumer-distribution-board{
    background: #F7F9FB;

    .terminal-level-content{
        margin: 40px 0;
    }
}
</style>
