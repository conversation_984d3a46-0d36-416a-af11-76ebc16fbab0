<template>
    <link-page class="consumer-terminal-list-page">
        <view class="head-info">
            <view class="tips" @tap="queryInfo">
                <link-icon icon="mp-info-lite" status="info"/>
                <view class="tips-content">数据指标说明</view>
            </view>
        </view>
        <view class="info-description" v-if="showInfo">
            <link-icon icon="mp-asc" class="info-icon"/>
            <view v-html="description" class="info-content"></view>
        </view>
        <link-auto-list :option="autoList">
            <template slot-scope="{data,index}">
                <link-swipe-action>
                    <item :key="index" :data="data" :arrow="false" class="account-list-item">
                        <view class="account-list" slot="note">
                            <view class="list-cell">
                                <view class="media-info">
                                    <image class="media-list-logo" :src="data|headImgAccount(data)" @tap="gotoItem(data)"/>
                                    <view class="media-list-body" style="width: 70%" @tap="gotoItem(data)">
                                        <view class="media-list-text-top">{{data.consumerName}}</view>
                                        <view class="media-list-text-bottom uni-ellipsis"><view class="media-list-label">负责人：</view>{{data.fstName}}</view>
                                        <view class="media-list-text-bottom uni-ellipsis"><view class="media-list-label">手机号：</view>{{data.phoneNumber}}</view>
                                    </view>
                                    <view class="media-list-right">
                                        <view class="title">
                                            <link-icon icon="icon-weixinkuaizhao" class="title-icon"/>
                                            <view>{{data.consumerTypeName}}</view>
                                        </view>
                                    </view>
                                </view>
                                <view class="line"></view>
                                <view class="media-detail">
                                    <view class="media-detail-item">
                                        <view class="media-list-text-bottom uni-ellipsis"><view class="media-list-label">单位：</view>{{data.companyName}}</view>
                                    </view>
                                    <view class="media-detail-item">
                                        <view class="media-list-text-bottom uni-ellipsis"><view class="media-list-label">职务：</view>{{data.position}}</view>
                                        <view class="media-list-text-bottom uni-ellipsis"><view class="media-list-label">来源：</view>{{data.sourceFrom | lov('SOURCE_FROM')}}</view>
                                    </view>
                                    <view class="media-detail-item">
                                        <view class="media-list-text-bottom uni-ellipsis"><view class="media-list-label">触达次数：</view>{{data.touchNum | numberIntFomart}}</view>
                                        <view class="media-list-text-bottom uni-ellipsis"><view class="media-list-label">消费者费效比：</view>{{data.feeRatio}}</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </item>
                </link-swipe-action>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
import {ROW_STATUS} from "../../../../../utils/constant";
import Taro from "@tarojs/taro";

export default {
    name: "consumer-terminal-list-page",
    data() {
        const userInfo = Taro.getStorageSync('token').result;
        const autoList = new this.AutoList(this, {
            module: this.$env.appURL + '/action/link/consumer',
            url: {
                queryByExamplePage: this.$env.dmpURL + '/link/appBoard/consumerDetailByPage'
            },
            exactSearchFields: [
                {
                    field: 'consumerName',
                    showValue: '姓名',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: true
                }, {
                    field: 'phoneNumber',
                    showValue: '手机号',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: true
                }
            ],
            param: {},
            sortOptions: null,
            hooks: {
                beforeLoad(option) {
                    option.param.storeId = this.pageParam.storeId,
                    option.param.consumerLevel = this.pageParam.consumerLevel,
                    option.param.postId = this.pageParam.postId,
                    option.param.reportPostnType = this.pageParam.reportPostnType,
                    delete option.param.sort;
                    delete option.param.order;
                    for (let i = 0; i < option.param.filtersRaw.length; i++) {
                        if (option.param.filtersRaw[i].property === 'consumerName') {
                            option.param.filtersRaw[i].operator = 'like';
                        }
                    }
                }
            }
        });
        return {
            itemPath: '',
            autoList,
            userInfo,
            deployNo: '',
            description: '',                                        // 指标说明
            showInfo: false,
        }
    },
    filters: {
        /**
         * @createdBy 曾宇
         * @date 2022/8/1
         * @methods: numberFloatFomart
         * @description: 浮点数数据转换
         **/
        numberFloatFomart(val){
            if (!Boolean(val)){
                return '0.00';
            } else {
                return parseFloat(val).toFixed(2);
            }
        },
        /**
         * @createdBy 曾宇
         * @date 2022/8/1
         * @methods: numberIntFomart
         * @description: 整数数据转换
         **/
        numberIntFomart(val){
            if (!Boolean(val)){
                return '0';
            } else {
                return parseInt(val);
            }
        }
    },
    created() {
        this.$taro.setNavigationBarTitle({title: this.pageParam.title});
        this.deployNo = 'cunsumer_salesman002';
        this.queryFieldFlagCfg();
    },
    async mounted() {
        await this.queryReportTaskConfig();
    },
    methods: {
        /**
         * @createdBy 曾宇
         * @date 2022/8/4
         * @methods: queryInfo
         * @description: 查询指标说明
         **/
        async queryReportTaskConfig() {
            const data = await this.$http.post(this.$env.dmpURL + '/link/reportTaskConfig/queryByExamplePage', {deployNo: this.deployNo}, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError('查询失败' + response.result);
                }
            });
            if(data.success) {
                this.description = data.rows[0].content;
            }
        },
        async queryInfo() {
            if(this.showInfo){
                this.showInfo = false;
            } else {
                this.showInfo = true;
            }
        },
        /**
         * @desc 查询跳转新旧版本消费者参数配置
         * <AUTHOR>
         * @date 2022/12/14 09:47
         **/
        async queryFieldFlagCfg () {
            const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', { key: 'FIELD_FLAG'});
            if (data.success) {
                if (data.value === 'Y') {
                    this.itemPath = '/pages/lj-consumers/account/account-item-page';
                } else {
                    this.itemPath = '/pages/lj-consumers/account-old/account-item-page';
                }
            }
        },
        /**
         * @desc 跳转详情
         * <AUTHOR>
         * @date 2022/4/19 18:55
         **/
        gotoItem (data) {
            data.id = data.consumerId;
            this.$nav.push(this.itemPath, {
                data: data
            });
        },
    }
}
</script>

<style lang="scss">
.consumer-terminal-list-page{
    background: #f6f8fa;

    .head-info{
        height: 100px;
        display: flex;
        justify-content: space-between;
        .tips{
            display: flex;
            align-items: center;
            margin: 12px;
            padding: 12px 0;
            font-family: PingFangSC-Regular;
            font-size: 26px;
            color: #999;
            line-height: 26px;
            font-weight: 400;
            .tips-content{
                margin-left: 5px;
            }
        }
    }

    .info-description{
        width: 90%;
        position: absolute;
        top: 100px;
        left: 24px;
        z-index: 9999;
        background: #ffffff;
        border: 0.5px solid rgba(230,230,230,1);
        border-radius: 10px;
        box-shadow: 0px 3px 10px 0px rgba(199,199,199,0.5);

        .info-content {
            height: 710px;
            overflow-y: auto;
        }

        .info-icon {
            position: absolute;
            top: -22px;
            left: 70px;
            color: #ffffff;
        }
        .p {
            padding: 12px;
            margin: 0 12px;
            white-space: break-spaces;
        }

        .p:first-child {
            margin-top: 24px;
        }

        .p:last-child {
            margin-bottom: 24px;
        }

        .table{
            height: 340px;
            overflow-y: auto;
            padding: 12px;
        }
    }

    /*deep*/ .link-item{
                 padding: 28px 0 28px 28px;
             }
    .account-list {
        background-color: #FFFFFF;
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: column;

        .list-cell {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;

            .media-info {
                padding: 11px 15px;
                box-sizing: border-box;
                display: flex;
                width: 100%;
                flex-direction: row;
                justify-content: space-between;

                .media-list-logo {
                    height: 94px;
                    width: 94px;
                    margin-right: 20px;

                    image {
                        height: 100%;
                        width: 100%;
                    }
                }

                .media-list-body {
                    display: flex;
                    flex: 1;
                    flex-direction: column;
                    justify-content: space-between;
                    align-items: flex-start;
                    overflow: hidden;

                    .media-list-text-top {
                        width: 100%;
                        margin-bottom: 24px;
                        font-family: PingFangSC-Medium;
                        font-size: 28px;
                        color: #333333;
                        letter-spacing: 0;
                        line-height: 28px;
                        font-weight: 500;
                    }

                }
                .media-list-text-bottom {
                    width: 100%;
                    display: flex;
                    align-items: center;
                    margin-bottom: 12px;
                    font-family: PingFangSC-Regular;
                    font-size: 24px;
                    color: #666666;
                    line-height: 24px;
                    font-weight: 400;
                }

                .media-list-label {
                    color: #999999;
                }

                .media-list-right{
                    display: flex;
                    align-items: flex-start;
                    margin-right: 24px;
                    .title{
                        display: flex;
                        align-items: center;
                        font-family: PingFangSC-Medium;
                        font-size: 24px;
                        color: #2F69F8;
                        letter-spacing: 0;
                        text-align: right;
                        line-height: 24px;
                        font-weight: 500;
                        .title-icon{
                            margin-right: 16px;
                            font-size: 10px;
                            line-height: 10px;
                            color: #2F69F8;
                        }
                    }
                }

            }

            .line{
                width: 100%;
                height: 1px;
                background: #EEF3F5;
                margin-bottom: 24px;
            }

            .media-detail{
                width: 100%;
                display: flex;
                flex-direction: column;
                .media-detail-item {
                    display: flex;
                    flex-direction: row;

                    .media-list-text-bottom {
                        margin-bottom: 16px;
                        display: flex;
                        flex-direction: row;
                        flex: 1;
                        font-family: PingFangSC-Regular;
                        font-size: 24px;
                        color: #666666;
                        letter-spacing: 0;
                        line-height: 24px;
                        font-weight: 400;

                        .media-list-label {
                            color: #999999;
                        }
                    }
                }
            }
        }
    }

    .account-list-item{
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
    }
}
</style>
