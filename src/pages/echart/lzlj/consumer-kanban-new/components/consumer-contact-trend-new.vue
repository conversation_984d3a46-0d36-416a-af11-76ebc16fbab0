<!--
 * 消费者触达趋势--报表
 * @Author:付常涛
 * @Date: 2023/10/30 17:49:36
-->
<template>
    <view class="consumer-contact-trend" v-if="selectMap && selectMap.length > 0">
        <line-title title="消费者触达趋势"></line-title>
        <view class="filter-type-item" @tap="selectDataFunc">{{selectItem.name}}<link-icon icon="mp-desc"/></view>
        <link-echart :option="consumerTrendOption"
                     v-if="allData.length > 0"
                     :force-use-old-canvas="false"
                     :height="capacityLevelBarYCategoryHeight+'px'"
                     ref="consumerContactTrendEchart"/>
        <view class="no-data" v-else>暂无数据</view>
    </view>
</template>
<script>
import {targetComposeLineAndHistogramProgress} from "../../echart.utils";
import LineTitle from "../../components/line-title";

export default {
    name: 'consumer-contact-trend',
    components: {LineTitle},
    props: {
        companyId: {
            type: String,
            default: "",
        },
        dataRange: {
            type: String,
            default: ''
        }
    },
    data () {
        const userInfo = this.$taro.getStorageSync('token').result;
        return {
            allData: [],
            userInfo,
            selectItem: {
                name: '合计',
                val: 'heJi'
            },
            consumerTrendOption: null, // 消费者触达趋势
            capacityLevelBarYCategoryHeight: (this.$device.systemInfo.windowWidth - 24) * 0.6285714 < 220 ? (this.$device.systemInfo.windowWidth - 24) * 0.6285714 : 220,
        }
    },
    watch: {
        selectMap: {
            deep: true,
            handler(newVal) {
                this.selectData = '';
                this.selectItem = {
                    name: '合计',
                    val: 'heJi'
                };
                if (newVal && newVal.length > 0) {
                    this.selectItem = newVal[newVal.length-1]
                    this.queryConsumerTrend();
                }
            }
        }
    },
    created() {
        this.queryConsumerTrend()
    },
    computed: {
        /**
         * 触达类型的可选择范围
         * @Author:付常涛
         * @Date: 2023/11/16 19:31:33
         */
        selectMap() {
            const original = this.$store.getters['board/getColumnList'];
            return original.filter((i) => i.name !== '平均触达次数') || []
        }
    },
    methods: {
        /**
         * @desc 选择触达类型筛选数据
         * <AUTHOR>
         * @date 2023/7/14 00:21
         **/
        async selectDataFunc () {
            const data = await this.$select(this.selectMap, {
                pageTitle: '选择触达类型',
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data}>
                            <view slot="title">{data.name}</view>
                        </item>
                    )
                }
            });
            this.selectItem = data;
            await this.queryConsumerTrend();
        },
        /**
         * 查询消费者触达趋势数据
         * @Author:付常涛
         * @Date: 2023/11/16 19:34:06
         */
        async queryConsumerTrend (companyId) {
            let params = {
                dataAccess: this.dataRange,
                actMClassName: this.selectItem.name
            };
            if (companyId || this.companyId) {
                params.companyId = companyId || this.companyId;
            }
            // 查询合计、平均触达次数时actMClassName不需要传
            if (params.actMClassName === '合计' || params.actMClassName === '平均触达次数') delete params.actMClassName
            if (params.actMClassName === '礼赠') {
                params.touchType = 'present';
                delete params.actMClassName;
            } else if (params.actMClassName === '拜访') {
                params.touchType = 'visit';
                delete params.actMClassName;
            }
            try {
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send", {...params, dmpSrUrl: '/link/board/csmTouchTrend'}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError(`查询${this.message}数据失败！` + response.result);
                    }
                });
                const xAxisData = ['11月', '12月', '1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月'];
                this.allData = [];
                let allData = [];
                let seriesData = [
                    {
                        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                        type: 'bar'
                    }
                ];
                let Dec = 0; // 12月
                let Nov = 0; // 11月
                if (data.success) {
                    this.$utils.hideLoading();
                    seriesData[0].data.forEach((item, index)=> {
                        data.rows.forEach((dataItem) => {
                            let dataMonth = parseInt(dataItem.monthId, 10);
                            if ((index + 1) === dataMonth) {
                                if (this.selectItem.name === '平均触达次数') {
                                    if (dataMonth === 12) {
                                        Dec = parseInt(dataItem.avgTouchTimes, 10);
                                    }
                                    if (dataMonth === 11) {
                                        Nov = parseInt(dataItem.avgTouchTimes, 10);
                                    }
                                    seriesData[0].data[index] = parseInt(dataItem.avgTouchTimes, 10);
                                    allData.push(parseInt(dataItem.avgTouchTimes, 10));
                                } else {
                                    if (dataMonth === 12) {
                                        Dec = parseInt(dataItem.touchTimes, 10);
                                    }
                                    if (dataMonth === 11) {
                                        Nov = parseInt(dataItem.touchTimes, 10);
                                    }
                                    seriesData[0].data[index] = parseInt(dataItem.touchTimes, 10);
                                    allData.push(parseInt(dataItem.touchTimes, 10));
                                }
                            }
                        })
                    });
                    this.allData = allData.sort((a, b)=> {return b - a});
                }
                seriesData[0].data.splice(10, 2);
                seriesData[0].data.unshift(Dec);
                seriesData[0].data.unshift(Nov);
                this.capacityLevelBarYCategoryHeight = 10 + 50 * 6;
                const yAxisData = [{name: '触达次数', min: 0, max: allData[0], interval: Math.ceil(allData[0]/5)}];
                this.consumerTrendOption =  echartInitConfig =>targetComposeLineAndHistogramProgress(echartInitConfig,xAxisData, yAxisData, seriesData,[{name: null,value: ''}],false,0, '月份', {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'line',
                        crossStyle: {
                            color: '#999'
                        }
                    },
                    formatter: `{b}${yAxisData[0].name}: {c}`
                }, {
                    left: '15px',
                    right: '25px',
                    containLabel: true
                })
            } finally {
                this.$utils.hideLoading();
            }
        }
    }
}
</script>
<style lang="scss">
@import "../css/new-board";
.consumer-contact-trend {
    position: relative;
}
</style>
