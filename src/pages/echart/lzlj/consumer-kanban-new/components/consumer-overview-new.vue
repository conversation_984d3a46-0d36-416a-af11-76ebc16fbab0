<!--
 * 消费者建设总览
 * @Author:付常涛
 * @Date: 2023/10/26 21:52:24
-->
<template>
    <view class="consumer-overview-data">
        <line-title title="消费者建设总览"></line-title>
        <view class="consumer-overview-chart">
            <view class="consumer-overview-left">
                <view class="title-line-item kv-title">
                    K/V序列
                </view>
                <view class="other-line" v-if="kVal.length > 0">
                    <view class="consumer-kv-left">
                        <view class="kv-name">K序列</view>
                    </view>
                    <view class="consumer-kv-value">
                        <view class="kv-line" v-for="(item, index) in kVal" :key="index + 'k'">{{item.seq}}</view>
                    </view>
                </view>
                <view class="other-line" v-if="vVal.length > 0">
                    <view class="consumer-kv-left">
                        <view class="kv-name">V序列</view>
                    </view>
                    <view class="consumer-kv-value">
                        <view class="kv-line" v-for="(item, index) in vVal" :key="index + 'v'">{{item.seq}}</view>
                    </view>
                </view>
            </view>
            <view class="consumer-overview-right">
                <view class="title-line-item">
                    <view class="title-item">累计</view>
                    <view class="title-item item-total">占比</view>
                    <view class="title-item">财年新增</view>
                    <view class="title-item">财季新增</view>
                    <view class="title-item">本月新增</view>
                </view>
                <view class="kv-data-line" v-if="kVal.length > 0">
                    <view class="data-line" v-for="(item, index) in kVal" :key="index + 'k'">
                        <view class="data-item"  :class="(isCheckDetail && item.qtyTotal !== '0' && item.qtyTotal !== 0)? 'item-info' : ''" @tap="gotoAccountList(item.seqType, item.seq, item.qtyTotal, '总数')">{{item.qtyTotal}}</view>
                        <view class="data-item item-total">{{item.qtyRatio}}</view>
                        <view class="data-item" :class="(isCheckDetail && item.yearQtyInc !== '0' && item.yearQtyInc !== 0)? 'item-info' : ''" @tap="gotoAccountList(item.seqType, item.seq, item.yearQtyInc, '本财年')">{{item.yearQtyInc}}</view>
                        <view class="data-item" :class="(isCheckDetail && item.quarterQtyInc !== '0' && item.quarterQtyInc !== 0)? 'item-info' : ''" @tap="gotoAccountList(item.seqType, item.seq, item.quarterQtyInc, '本财季')">{{item.quarterQtyInc}}</view>
                        <view class="data-item" :class="(isCheckDetail && item.monthQtyInc !== '0' && item.monthQtyInc !== 0)? 'item-info' : ''" @tap="gotoAccountList(item.seqType, item.seq, item.monthQtyInc, '本月')">{{item.monthQtyInc}}</view>
                    </view>
                </view>
                <view class="kv-data-line" v-if="vVal.length > 0">
                    <view class="data-line" v-for="(item, index) in vVal" :key="index + 'v'">
                        <view class="data-item"  :class="(isCheckDetail && item.qtyTotal !== '0' && item.qtyTotal !== 0)? 'item-info' : ''" @tap="gotoAccountList(item.seqType, item.seq, item.qtyTotal, '总数')">{{item.qtyTotal}}</view>
                        <view class="data-item item-total">{{item.qtyRatio}}</view>
                        <view class="data-item" :class="(isCheckDetail && item.yearQtyInc !== '0' && item.yearQtyInc !== 0)? 'item-info' : ''" @tap="gotoAccountList(item.seqType, item.seq, item.yearQtyInc, '本财年')">{{item.yearQtyInc}}</view>
                        <view class="data-item" :class="(isCheckDetail && item.quarterQtyInc !== '0' && item.quarterQtyInc !== 0)? 'item-info' : ''" @tap="gotoAccountList(item.seqType, item.seq, item.quarterQtyInc, '本财季')">{{item.quarterQtyInc}}</view>
                        <view class="data-item" :class="(isCheckDetail && item.monthQtyInc !== '0' && item.monthQtyInc !== 0)? 'item-info' : ''" @tap="gotoAccountList(item.seqType, item.seq, item.monthQtyInc, '本月')">{{item.monthQtyInc}}</view>
                    </view>
                </view>
                <view class="no-data" v-if="kVal.length <=0 && vVal.length <=0">暂无数据</view>
            </view>
        </view>
    </view>
</template>
<script>
import LineTitle from "../../components/line-title";
export default {
    name: "consumer-overview-new",
    components: { LineTitle },
    props: {
        dataRange: {
            type: String,
            default: "",
        },
        companyId: {
            type: String,
            default: "",
        },
        isCheckDetail: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        const userInfo = this.$taro.getStorageSync("token").result;
        return {
            userInfo,
            kVal: [], // k序列数据
            vVal: [], // v序列数据
        };
    },
    created() {
        // 查询消费者总览数据
        this.queryConsumerOverview();
    },
    watch: {
        dataRange(newVal, oldVal) {
            if (newVal) {
                // 当安全性改变,查询消费者总览数据
                this.queryConsumerOverview();
            }
        },
    },
    methods: {
        /**
         * 查询消费者总览数据
         * @Author:付常涛
         * @Date: 2023/10/26 22:07:18
         */
        async queryConsumerOverview(companyId) {
            this.kVal = [];
            this.vVal = [];
            try {
                this.$utils.showLoading();
                const data = await this.$http.post(
                    this.$env.appURL + "/action/link/sendDmpSr/send",
                    {
                        dmpSrUrl: "/link/board/constructOverview",
                        dataAccess: this.dataRange,
                        companyId: companyId || this.companyId
                    },
                    {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$utils.hideLoading();
                            this.$message.error({
                                message:
                                    "查询消费者总览数据失败！" + response.result,
                                customFlag: true,
                            });
                        },
                    }
                );
                if (data.success) {
                    this.$utils.hideLoading();
                    let totalItem = null;
                    data.rows.map((item, index) => {
                        if (item.seqType === "K" && item.seq) {
                            item.qtyRatio = `${(item.qtyRatio * 100).toFixed(2)}%`;
                            this.kVal.push(item);
                        } else if (item.seqType === "V" && item.seq) {
                            item.qtyRatio = `${(item.qtyRatio * 100).toFixed(2)}%`
                            this.vVal.push(item);
                        } else if (item.seqType === "ALL" && !item.seq) {
                            item.seq = "总计";
                            item.qtyRatio = item.qtyRatio === 1 ? `${(item.qtyRatio * 100)}%` : `${(item.qtyRatio * 100).toFixed(2)}%`
                            totalItem = item;
                        }
                    });
                    /* K/V序列排序 */
                    this.kVal = await this.sortTargetArr(this.kVal, 'ACCT_SUB_TYPE');
                    this.vVal = await this.sortTargetArr(this.vVal, 'ACCT_MEMBER_LEVEL');
                    if (totalItem) {
                        this.kVal.push(this.$utils.deepcopy(totalItem));
                        this.vVal.push(this.$utils.deepcopy(totalItem));
                    }
                }
            } catch (error) {
                this.$message.error({
                    message: "查询消费者总览数据失败！" + error,
                    customFlag: true,
                });
            } finally {
                this.$utils.hideLoading();
            }
        },
        /**
         * 排序目标数组
         * @Author:付常涛
         * @Date: 2023/11/16 20:26:16
         * @param arr  目标数组
         * @param lovType  值列表
         */
        async sortTargetArr(arr, lovType) {
            const res = await this.$lov.getLovByType(lovType);
            if (res && res.length > 0) {
                arr.forEach((item) => {
                    const lov = res.find((i) => i.name === item.seq);
                    if (lov) {
                        item.sort = Number(lov.seq);
                    } else {
                        item.sort = 0;
                    }
                });
                arr.sort((item1, item2) => item2.sort - item1.sort);
            }
            return this.$utils.deepcopy(arr);
        },
        /**
         * 跳转消费者列表(下钻)---todo
         * @Author:付常涛
         * @Date: 2023/10/26 22:07:42
         * @param seqType k/v序列
         * @param seq 序列值
         * @param value 当前值
         * @param timeRange 数据时间范围
         */
        gotoAccountList(seqType, seq, value, timeRange) {
            if (!this.isCheckDetail) return;
            if (value === "0" || value === 0) return;
            let kTypeName = '';
            let vTypeName = '';
            if (seqType === 'K') {
                kTypeName = seq;
            } else if (seqType === 'V') {
                vTypeName = seq;
            }
            this.$nav.push(
                "/pages/lj-consumers/account/account-list-new-page",
                {
                    pageFrom: "ConsumerOverview",
                    companyId: this.companyId,
                    kTypeName,
                    vTypeName,
                    filterInfo: {
                        timeRange,
                        dataRange: this.dataRange,
                    },
                }
            );
        },
    },
};
</script>
<style lang="scss">
@import "../css/new-board";
.consumer-overview-data {
    .consumer-overview-chart {
        text-align: center;
        display: flex;
        .title-line-item {
            color: #999999;
            height: 80px;
            line-height: 80px;
            background: #f8faff;
        }
        .consumer-overview-left {
            .kv-title {
                width: 222px !important;
            }
            .kv-line {
                margin: 24px 0;
                white-space: nowrap;
                overflow: auto;
            }
            .other-line {
                width: 222px;
                display: flex;
                align-items: center;
                flex: 1;
                border-bottom: 2px solid #f0f2f8;
                .consumer-kv-left {
                    width: 62px;
                    display: flex;
                    justify-content: center;
                    .kv-name {
                        width: 28px;
                        height: auto;
                        word-wrap: break-word;
                        word-break: break-all;
                    }
                }
                .consumer-kv-value {
                    width: 160px;
                    flex: none;
                    border-left: 2px solid #f0f2f8;
                }
            }
        }
        .consumer-overview-right {
            overflow-x: auto;
            .title-item {
                width: 168px;
                flex: none;
                background: #f8faff;
            }
            .item-total {
                width: 104px !important;
            }
            .title-line-item {
                display: flex;
            }
            .kv-data-line {
                border-bottom: 2px solid #f0f2f8;
                width: 776px;
            }
            .data-line {
                display: flex;
                margin: 24px 0;
            }
            .data-item {
                width: 160px;
                flex: none;
                padding: 0 4px;
                overflow-x: auto;
                overflow-y: hidden;
            }
        }
    }
}
</style>
