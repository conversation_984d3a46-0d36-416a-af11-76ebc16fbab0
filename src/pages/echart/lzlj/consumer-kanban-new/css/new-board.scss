.consumer-contact-overview, .region-consumer-overview, .consumer-order-overview, .consumer-overview-data, .consumer-contact-trend,.consumer-input-output,.region-consumer-touch-overview, .consumer-touch-detail-overview{
    padding: 0 24px;
    margin-bottom: 36px;
    background: white;
    border-radius: 16px;
    font-size: 28px;
    font-weight: 400;
    /*deep*/.report-line-title{
        margin-bottom: 24px;
    }
    .activity-line-item-title{
        width: 152px;
        overflow-x: auto;
        white-space: nowrap;
        padding: 0 4px;
    }
    .item-info{
        color: #3F66EF;
        text-decoration: underline;
    }
    .scroll-view-data {
        margin-top: 24px;
        margin-bottom: 24px;
        .select-dimension {
            display: flex;
            .select-button{
                min-width: 60px;
                height: 30px;
                border: none !important;
                font-size: 28px;
                line-height: 28px;
                font-weight: 400;
                background: #F2F3F6;
            }
        }
    }
    .no-data{
        width: 100%;
        text-align: center;
        color: #6D96FA;
        margin-top: 32px;
        font-size: 24px;
        padding-bottom: 32px;
    }
    .item-info-other{
        color: red;
    }
}
