.operate-data-board ,.consumer-data-board, .consumer-board-detail-list, .consumer-distribution-board{
    .head-info{
        height: 100px;
        display: flex;
        justify-content: space-between;
        .tips{
            display: flex;
            align-items: center;
            margin: 12px;
            padding: 12px 0;
            font-family: PingFangSC-Regular;
            font-size: 26px;
            color: #999;
            line-height: 26px;
            font-weight: 400;
            .tips-content{
                margin-left: 5px;
            }
        }
    }

    .load-more{
        width: 100%;
        text-align: center;
        color: #6D96FA;
        margin-top: 32px;
        font-size: 24px;
        padding-bottom: 32px;
    }

    .first-part,.second-part, .second-part-long, .third-part, .forth-part, .fifth-part, .sixth-part{
        background: white;
        border-radius: 10px;
        margin-bottom: 32px;
        padding: 0 24px 24px;
    }

    .select-org-dimension {
        display: flex;
        margin: 12px 0;
        padding: 12px 0;
        .select-button{
            background-color: #A8C0D9;
            border: 1px solid rgba(168,192,217,1);
            border-radius: 28px;
            font-family: PingFangSC-Medium;
            font-size: 24px;
            color: #333333 !important;
            line-height: 24px;
            font-weight: 500;
        }
    }

    .report-line-title .stair-title{
        font-family: PingFangSC-Medium;
        font-size: 32px;
        text-align: left;
        line-height: 32px;
        font-weight: 500;
    }

    .select-other-dimension {
        display: flex;
        margin-top: 32px;
        .select-button {
            padding: 14px 24px;
            .link-icon {
                font-family: PingFangSC-Regular;
                font-size: 24px;
                color: #2F69F8;
                line-height: 24px;
                font-weight: 400;
            }
        }
    }

    .scroll-view-data {
        margin-top: 24px;

        .select-dimension {
            display: flex;
            .select-button{
                min-width: 60px;
                height: 30px;
                border: none !important;
                font-family: PingFangSC-Regular;
                font-size: 28px;
                line-height: 28px;
                font-weight: 400;
                background: #F2F3F6;
            }
        }
    }
}

.operate-data-board {
    //background: white;
    //padding: 0 24px;

    .act-overview{
        background: white;
        border-radius: 10px;
        padding: 0 24px;
    }

    .first-part{
        .title{
            display: flex;
            align-items: center;
            margin-top: 40px;
            font-family: PingFangSC-Medium;
            font-size: 28px;
            color: #333333;
            line-height: 28px;
            font-weight: 500;
            .title-icon{
                margin-right: 16px;
                font-size: 10px;
                line-height: 10px;
                color: #2F69F8;
            }
        }
    }

    .order-overview {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 40px 0;

        .order-all-list {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-around;
            width: 33%;

            .num{
                font-family: PingFangSC-Semibold;
                font-size: 36px;
                color: #222222;
                text-align: center;
                line-height: 36px;
                font-weight: 600;
                margin-bottom: 16px;
            }

            .label {
                font-family: PingFangSC-Regular;
                font-size: 24px;
                color: #666666;
                text-align: center;
                line-height: 24px;
                font-weight: 400;
            }

            .line{
                width: 2px;
                height: 40px;
                background: #DDDDDD;
            }
        }

        .order-all-list:last-child .line{
            background: transparent;
        }
    }
    .consumer-overview {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 24px 0;
        flex-wrap: wrap;

        .consumer-over-list {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-start;
            flex-grow: 2;
            width: 50%;
            margin-top: 28px;
            padding-bottom: 24px;
            .icon{
                display: flex;
                align-items: center;
                justify-content: center;
                width: 80px;
                height: 80px;
                border-radius: 40px;
                margin-right: 24px;
                .item-icon{
                    width: 40px;
                    height: 40px;
                    color: #fff;
                    font-size: 40px;
                }
            }

            .item-detail{
                display: flex;
                flex-direction: column;
                .num {
                    font-family: HelveticaNeue-Medium;
                    font-size: 40px;
                    color: #222222;
                    letter-spacing: 0;
                    line-height: 40px;
                    font-weight: 500;
                    margin-bottom: 6px;
                }
                .label{
                    font-family: PingFangSC-Regular;
                    font-size: 24px;
                    color: #666666;
                    letter-spacing: 0;
                    line-height: 24px;
                    font-weight: 400;
                }
            }
        }

        .consumer-all-list{
            width: 60px;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-around;
            flex-grow: 1;
            margin: 20px 0;
            padding: 0 16px;
            box-sizing: border-box;

            .line{
                width: 2px;
                height: 40px;
                background: #DDDDDD;
            }

            .num{
                font-family: PingFangSC-Semibold;
                font-size: 36px;
                color: #222222;
                text-align: center;
                line-height: 36px;
                font-weight: 600;
                margin-bottom: 16px;
            }

            .label{
                font-family: PingFangSC-Regular;
                font-size: 24px;
                color: #333333;
                text-align: center;
                line-height: 24px;
                font-weight: 400;
                .item-icon{
                    margin-left: 3px;
                }
            }
        }
        .consumer-all-list:last-child .line{
            background: transparent;
        }
    }
    .consumer-act-overview {
        padding-bottom: 28px;
    }
    .second-part{
        font-size: 24px;

        .table-data {
            margin-top: 32px;
            width: 100%;

            .title {
                width: 100%;
                background: #ECF0F9;
                display: flex;
                border-radius: 20px;
                font-family: PingFangSC-Medium;
                font-size: 24px;
                color: #333333;
                letter-spacing: 0;
                text-align: center;
                line-height: 28px;
                font-weight: 500;

                .title-item {
                    width: 25%;
                    box-sizing: border-box;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 0 10px;
                    text-align: center;
                    min-height: 80px;
                }
            }

            .data {
                width: 100%;
                height: 500px;
                overflow-y: auto;
                .data-list {
                    width: 100%;
                    display: flex;

                    .data-list-item {
                        box-sizing: border-box;
                        width: 25%;
                        padding: 14px 0;
                        display: flex;
                        min-height: 100px;
                        align-items: center;
                        justify-content: center;
                        border-bottom: 1px solid #EDF1F4;
                        font-family: PingFangSC-Regular;
                        font-size: 24px;
                        color: #333333;
                        letter-spacing: 0;
                        text-align: center;
                        line-height: 24px;
                        font-weight: 400;
                    }
                }
            }
        }
    }
    .forth-part {
        font-size: 24px;

        .table-data {
            margin-top: 32px;
            overflow-x: auto;
            width: 100%;

            .title {
                width: 150%;
                background: #ECF0F9;
                display: flex;
                border-radius: 20px;
                font-family: PingFangSC-Medium;
                font-size: 24px;
                color: #333333;
                letter-spacing: 0;
                text-align: center;
                line-height: 28px;
                font-weight: 500;

                .title-item {
                    width: 21.3%;
                    box-sizing: border-box;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 0 10px;
                    text-align: center;
                    min-height: 80px;
                }

                .title-item:first-child {
                    width: 22.2%;
                }
            }

            .data {
                width: 150%;
                height: 500px;
                overflow-y: auto;
                .data-list {
                    width: 100%;
                    display: flex;

                    .data-list-item {
                        box-sizing: border-box;
                        width: 21.3%;
                        padding: 14px 0;
                        display: flex;
                        min-height: 100px;
                        align-items: center;
                        justify-content: center;
                        text-align: center;
                        border-bottom: 1px solid #EDF1F4;
                    }

                    .data-list-item:first-child {
                        width: 22.2%;
                    }
                }
            }
        }
    }
    .load-more{
        width: 100%;
        text-align: center;
        color: #6D96FA;
        margin-top: 32px;
        padding-bottom: 32px;
    }
}
