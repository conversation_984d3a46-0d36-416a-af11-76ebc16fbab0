<!--
 * 各区域消费者触达总览、：点击区域下钻
 * @Author:付常涛
 * @Date: 2023/11/21 11:16:12
-->
<template>
    <link-page class="consumer-overview-list-page">
        <!--     建设和动销   -->
        <view class="consumer-overview-chart" v-if="!['TeamTouchOverview', 'AreaTouchOverview', 'AreaTouchDetailOverview'].includes(type)">
            <view class="consumer-overview-left">
                <view class="overview-line" v-if="dataRange === 'Area'">
                    <view class="title-line-item kv-title">
                        区域
                    </view>
                    <view class="other-line" :class="{'item-info': isCheckDetail}" v-for="(item, index) in lineData" :key="index" @tap="gotoSalesmanItem(item)">{{item.orgName}}</view>
                </view>
                <view class="overview-line" v-if="dataRange === 'Team'">
                    <view class="title-line-item kv-title">
                        业务代表
                    </view>
                    <view class="other-line" v-for="(item, index) in lineData" :key="index">{{item.staffName}}</view>
                </view>
                <view class="overview-line" v-if="dataRange === 'Team'">
                    <view class="title-line-item kv-title">
                        职位
                    </view>
                    <view class="other-line" v-for="(item, index) in lineData" :key="index">{{item.postnName || item.postName}}</view>
                </view>
            </view>
            <view class="consumer-overview-right">
                <view class="title-line-item">
                    <view class="activity-item">
                        <view class="activity-title">K序列</view>
                        <view class="activity-line-title" v-if="lineData.length > 0">
                            <view class="activity-line-item-title" v-for="(item, index) in dynamicsK" :key="index + 'sK'">{{item.fieldName}}</view>
                        </view>
                    </view>
                    <view class="activity-item">
                        <view class="activity-title">V序列</view>
                        <view class="activity-line-title" v-if="lineData.length > 0">
                            <view class="activity-line-item-title" v-for="(item, index) in dynamicsV" :key="index + 'sV'">{{item.fieldName}}</view>
                        </view>
                    </view>
                </view>
                <view class="all-data">
                    <view class="kv-data-line" v-if="lineData.length > 0 && dynamicsK.length > 0">
                        <view class="data-line" v-for="(item, index) in lineData" :key="index + 'kv'">
                            <view class="data-item" :class="(isCheckDetail && item[lineItem.field] !== '0' && item[lineItem.field] !== 0) ? 'item-info' : ''" v-for="(lineItem, index) in dynamicsK" :key="index + 'lk'" @tap="gotoItem(item, lineItem, item[lineItem.field], 'K')">{{item[lineItem.field]}}</view>
                        </view>
                    </view>
                    <view class="kv-data-line" v-if="lineData.length > 0 && dynamicsV.length > 0">
                        <view class="data-line" v-for="(item, index) in lineData" :key="index + 'vv'">
                            <view class="data-item" :class="(isCheckDetail && item[lineItem.field] !== '0' && item[lineItem.field] !== 0) ? 'item-info' : ''" v-for="(lineItem, index) in dynamicsV" :key="index + 'lv'" @tap="gotoItem(item, lineItem, item[lineItem.field], 'V')">{{item[lineItem.field]}}</view>
                        </view>
                    </view>
                </view>
                <view class="no-data" v-if="lineData.length <=0">暂无数据</view>
            </view>
        </view>
        <!--     触达   -->
        <view class="consumer-touch-overview-chart" v-else>
            <!--     触达不折叠ui   -->
            <template v-if="!isCollapse">
                <!-- 左侧固定列  -->
                <view class="consumer-overview-left">
                    <!-- 业务代表列  -->
                    <view class="kv-line-title" v-if="dataRange !== 'Area'">
                        <!-- 标题  -->
                        <view class="title-line-item">业务代表</view>
                        <!-- 数据  -->
                        <view class="other-line" v-for="(item, index) in lineData"
                              :class="{'item-info': isCheckDetail}"
                              :style="{'height': item.lineLength * (rpxToPx(70)+rpxToPx(24)) + 2 * rpxToPx(24) +  rpxToPx(2) + 'px'}"
                              @tap="gotoConsumerTouchDetail(item)"
                              :key="index">{{item.staffName}}</view>
                    </view>
                    <!-- 职位列  -->
                    <view class="kv-line-title">
                        <!-- 标题  -->
                        <view class="title-line-item">{{dataRange === 'Area' ? '区域' : '职位'}}</view>
                        <!-- 数据  -->
                        <view class="other-line" v-for="(item, index) in lineData"
                              :class="{'item-info': dataRange === 'Area' && isCheckDetail}"
                              @tap="gotoAreaDetail(item)"
                              :style="{'height': item.lineLength * (rpxToPx(70)+rpxToPx(24)) + 2 * rpxToPx(24) +  rpxToPx(2) + 'px'}"
                              :key="index">{{ dataRange === 'Area' ? item.orgName : item.postName}}</view>
                    </view>
                </view>
                <!-- 右侧滑动列  -->
                <view class="consumer-overview-right">
                    <!-- 会员等级列  -->
                    <view class="kv-line-title">
                        <!-- 标题  -->
                        <view class="title-line-item kv-title">
                            K/V序列
                        </view>
                        <view v-for="(item, index) in lineData" :key="index">
                            <!-- k序列行名子表 -->
                            <view class="other-line" v-if="item.kLineList && item.kLineList.length > 0">
                                <!-- 左侧总行名 -->
                                <view class="consumer-kv-left">
                                    <view class="kv-name">K序列</view>
                                </view>
                                <!-- 右侧子行名 -->
                                <view class="consumer-kv-value">
                                    <view class="kv-line" v-for="(subItem, index) in item.kLineList" :key="index + 'k'">{{subItem.name}}</view>
                                </view>
                            </view>
                            <!-- v序列行名子表 -->
                            <view class="other-line" v-if="item.vLineList && item.vLineList.length > 0">
                                <!-- 左侧总行名 -->
                                <view class="consumer-kv-left">
                                    <view class="kv-name">V序列</view>
                                </view>
                                <!-- 右侧子行名 -->
                                <view class="consumer-kv-value">
                                    <view class="kv-line" v-for="(subItem, index) in item.vLineList" :key="index + 'k'">{{subItem.name}}</view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <!-- 其他列  -->
                    <view class="other-data-line">
                        <!-- 标题行  -->
                        <view class="title-line-item">
                            <!-- 动态标题行 -->
                            <view class="activity-item" v-if="dynamicColumnList.length > 0">
                                <view class="activity-title">活动邀约</view>
                                <view class="activity-line-title" :style="{width: 168 * dynamicColumnList.length + 'rpx'}">
                                    <view class="activity-line-item-title" v-for="(item, i) in dynamicColumnList" :key="i">{{item.name}}</view>
                                </view>
                            </view>
                            <!-- 静态行 -->
                            <view class="title-line-right">
                                <view class="title-item" v-for="(item) in staticColumnList" :key="item.val">{{item.name}}</view>
                            </view>
                        </view>
                        <view v-for="(item, index) in lineData" :key="index">
                            <!-- k序列子表 -->
                            <view class="kv-data-line" v-if="item.kVal && item.kVal.length > 0">
                                <!-- k序列单行 -->
                                <view class="data-line" v-for="(line, lineIndex) in item.kVal" :key="lineIndex">
                                    <!-- 单个数据项 -->
                                    <view class="data-item" v-for="(column, columnIndex) in line" :key="columnIndex"
                                          :class="{'item-info': isCheckDetail && column > 0 && ![columnLength-1].includes(columnIndex) && dataRange === 'Team'}"
                                          @tap="gotoItemForTouch(column, columnIndex, 'K', item.kLineList, lineIndex, dataRange === 'Area' ? item.orgId : item.staffId, item.postnId)">{{column}}</view>
                                </view>
                            </view>
                            <!-- v序列子表 -->
                            <view class="kv-data-line" v-if="item.vVal && item.vVal.length > 0">
                                <!-- v序列单行 -->
                                <view class="data-line" v-for="(line, lineIndex) in item.vVal" :key="lineIndex">
                                    <!-- 单个数据项 -->
                                    <view class="data-item" v-for="(column, columnIndex) in line" :key="columnIndex"
                                          :class="{'item-info': isCheckDetail && column > 0 &&![columnLength-1].includes(columnIndex) && dataRange === 'Team'}"
                                          @tap="gotoItemForTouch(column, columnIndex, 'V', item.vLineList, lineIndex, dataRange === 'Area' ? item.orgId : item.staffId, item.postnId)">{{column}}</view>
                                </view>
                            </view>
                            <!-- 暂无数据 -->
                            <view class="no-data" v-if="!item.vVal || !item.vVal || item.kVal.length <=0 && item.vVal.length <=0">暂无数据</view>
                        </view>
                    </view>
                </view>
            </template>
            <!--     触达折叠ui展示（目前只有触达区域的下转）   -->
            <template v-else>
                <!-- 左侧固定列  -->
                <view class="consumer-overview-left">
                    <!-- 业务代表列  -->
                    <view class="kv-line-title">
                        <!-- 标题  -->
                        <view class="title-line-item">业务代表</view>
                        <!-- 数据  -->
                        <view class="other-line" v-for="(item, index) in lineData"
                              :class="{'item-info': isCheckDetail && type !== 'AreaTouchDetailOverview'}"
                              :style="{'height': getHeight(item)}"
                              @tap="gotoConsumerTouchDetail(item)"
                              :key="index">{{item.staffName}}</view>
                    </view>
                    <!-- 职位列  -->
                    <view class="kv-line-title">
                        <!-- 标题  -->
                        <view class="title-line-item">职位</view>
                        <!-- 数据  -->
                        <view class="other-line" v-for="(item, index) in lineData"
                              :style="{'height': getHeight(item)}"
                              :key="index">{{item.postName}}</view>
                    </view>
                </view>
                <!-- 右侧滑动列  -->
                <view class="consumer-overview-right">
                    <!-- 会员等级列  -->
                    <view class="kv-line-title">
                        <!-- 标题  -->
                        <view class="title-line-item kv-title">
                            K/V序列
                        </view>
                        <view v-for="(item, index) in lineData" :key="index">
                            <template v-if="!isCollapse">
                                <!-- k序列行名子表 -->
                                <view class="other-line" v-if="item.kLineList && item.kLineList.length > 0">
                                    <!-- 左侧总行名 -->
                                    <view class="consumer-kv-left">
                                        <view class="kv-name">K序列</view>
                                    </view>
                                    <!-- 右侧子行名 -->
                                    <view class="consumer-kv-value">
                                        <view class="kv-line" v-for="(subItem, index) in item.kLineList" :key="index + 'k'">{{subItem.name}}</view>
                                    </view>
                                </view>
                                <!-- v序列行名子表 -->
                                <view class="other-line" v-if="item.vLineList && item.vLineList.length > 0">
                                    <!-- 左侧总行名 -->
                                    <view class="consumer-kv-left">
                                        <view class="kv-name">V序列</view>
                                    </view>
                                    <!-- 右侧子行名 -->
                                    <view class="consumer-kv-value">
                                        <view class="kv-line" v-for="(subItem, index) in item.vLineList" :key="index + 'k'">{{subItem.name}}</view>
                                    </view>
                                </view>
                            </template>
                            <template v-else>
                                <!-- k序列行名子表 -->
                                <view class="other-line" v-if="item.kLineList && item.kLineList.length > 0">
                                    <!-- 右侧子行名 -->
                                    <view class="consumer-kv-value">
                                        <!-- 折叠标题 -->
                                        <view class="kv-line collapse-title" :class="{'collapse': item.isCollapseK}" @tap="changeCollapse(item, 'K')">K序列<link-icon icon="icon-down"/></view>
                                        <!-- 折叠项目 -->
                                        <view class="collapse-box" v-if="!item.isCollapseK">
                                            <view class="kv-line" v-for="(subItem, index) in item.kLineList.slice(0, item.kLineList.length-1)" :key="index + 'k'">{{subItem.name}}</view>
                                        </view>
                                    </view>
                                </view>
                                <!-- v序列行名子表 -->
                                <view class="other-line" v-if="item.vLineList && item.vLineList.length > 0">
                                    <!-- 右侧子行名 -->
                                    <view class="consumer-kv-value">
                                        <!-- 折叠标题 -->
                                        <view class="kv-line collapse-title" :class="{'collapse': item.isCollapseV}" @tap="changeCollapse(item, 'V')">V序列<link-icon icon="icon-down"/></view>
                                        <!-- 折叠项目 -->
                                        <view class="collapse-box" v-if="!item.isCollapseV">
                                            <view class="kv-line" v-for="(subItem, index) in item.vLineList.slice(0, item.vLineList.length-1)" :key="index + 'v'">{{subItem.name}}</view>
                                        </view>
                                    </view>
                                </view>
                            </template>
                        </view>
                    </view>
                    <!-- 其他列  -->
                    <view class="other-data-line">
                        <!-- 标题行  -->
                        <view class="title-line-item">
                            <!-- 动态标题行 -->
                            <view class="activity-item" v-if="dynamicColumnList.length > 0">
                                <view class="activity-title">活动邀约</view>
                                <view class="activity-line-title" :style="{width: 168 * dynamicColumnList.length + 'rpx'}">
                                    <view class="activity-line-item-title" v-for="(item, i) in dynamicColumnList" :key="i">{{item.name}}</view>
                                </view>
                            </view>
                            <!-- 静态行 -->
                            <view class="title-line-right">
                                <view class="title-item" v-for="(item) in staticColumnList" :key="item.val">{{item.name}}</view>
                            </view>
                        </view>
                        <view v-for="(item, index) in lineData" :key="index">
                            <!-- k序列子表 -->
                            <view class="kv-data-line" v-if="item.kVal && item.kVal.length > 0">
                                <!-- 合计行在二维数组的最后一行，但是在前端展示需要是第一行 -->
                                <view class="data-line">
                                    <!-- 单个数据项 -->
                                    <view class="data-item" v-for="(column, columnIndex) in item.kVal[item.kVal.length-1]" :key="columnIndex"
                                          :class="{'item-info': isCheckDetail && column > 0 && ![columnLength-1].includes(columnIndex)}"
                                          @tap="gotoItemForTouch(column, columnIndex, 'K', [{name: '合计'}], 0, item.staffId, item.postnId)">{{column}}</view>
                                </view>
                                <template v-if="!item.isCollapseK">
                                    <!-- k序列单行 -->
                                    <view class="data-line" v-for="(line, lineIndex) in item.kVal.slice(0, item.kVal.length-1)" :key="lineIndex">
                                        <!-- 单个数据项 -->
                                        <view class="data-item" v-for="(column, columnIndex) in line" :key="columnIndex"
                                              :class="{'item-info': isCheckDetail && column > 0 && ![columnLength-1].includes(columnIndex)}"
                                              @tap="gotoItemForTouch(column, columnIndex, 'K', item.kLineList, lineIndex, item.staffId, item.postnId)">{{column}}</view>
                                    </view>
                                </template>
                            </view>
                            <!-- v序列子表 -->
                            <view class="kv-data-line" v-if="item.vVal && item.vVal.length > 0">
                                <!-- 合计行在二维数组的最后一行，但是在前端展示需要是第一行 -->
                                <view class="data-line">
                                    <!-- 单个数据项 -->
                                    <view class="data-item" v-for="(column, columnIndex) in item.vVal[item.vVal.length-1]" :key="columnIndex"
                                          :class="{'item-info': isCheckDetail && column > 0 && ![columnLength-1].includes(columnIndex)}"
                                          @tap="gotoItemForTouch(column, columnIndex, 'V', [{name: '合计'}], 0, item.staffId, item.postnId)">{{column}}</view>
                                </view>
                                <template v-if="!item.isCollapseV">
                                    <!-- v序列单行 -->
                                    <view class="data-line" v-for="(line, lineIndex) in item.vVal.slice(0, item.vVal.length-1)" :key="lineIndex">
                                        <!-- 单个数据项 -->
                                        <view class="data-item" v-for="(column, columnIndex) in line" :key="columnIndex"
                                              :class="{'item-info': isCheckDetail && column > 0 &&![columnLength-1].includes(columnIndex)}"
                                              @tap="gotoItemForTouch(column, columnIndex, 'V', item.vLineList, lineIndex, item.staffId, item.postnId)">{{column}}</view>
                                    </view>
                                </template>
                            </view>
                            <!-- 暂无数据 -->
                            <view class="no-data" v-if="!item.vVal || !item.vVal || item.kVal.length <=0 && item.vVal.length <=0">暂无数据</view>
                        </view>
                    </view>
                </view>
            </template>
        </view>
        <view class="load-more" v-if="total > 20" @tap="loadMore()">{{ total > lineData.length ? '加载更多' : '' }}</view>
        <water-mark></water-mark>
    </link-page>
</template>
<script>
import waterMark from "../../../lzlj/components/water-mark";

export default {
    name: 'consumer-overview-list-page',
    components: {waterMark},
    data () {
        const dataRange = this.pageParam.dataRange || '';
        const url = this.pageParam.url || '';
        const message = this.pageParam.message || '';
        const params = this.pageParam.params || {};
        const type = this.pageParam.type || '';
        const isCheckDetail = this.pageParam.isCheckDetail || false;
        const isCollapse = this.pageParam.isCollapse || false;      // 是否可折叠
        const timeScope = this.pageParam.timeScope || false;
        const pageFrom = this.pageParam.pageFrom || '';
        return {
            pageFrom,
            timeScope,
            isCollapse,
            isCheckDetail,
            type,
            params,
            url,
            message,
            dataRange,
            lineData: [],
            total: 0,
            columnMap: [],          // 列数据的key值和展示值的映射数组
            rowMap: [],             // 行数据的key值和展示值的映射数组
            columnLength: 0,       // 所有列（动态列和静态列）的个数
            dynamicColumnList: [], // k序列和v序列的动态列信息数据
            staticColumnList: [     // 静态列信息数组
                {name: '礼赠', val: 'presentTimes'},
                {name: '拜访', val: 'visitTimes'},
                {name: '合计', val: 'touchTimes'}
            ], // 静态列数据
            dynamicsK: [], // 动态K序列
            dynamicsV: [], // 动态V序列
        }
    },
    async created () {
        this.params['page'] = 1;
        this.params.rows = 20;
        await this.$taro.setNavigationBarTitle({title: this.pageParam.lineTitle});
        if (['TeamTouchOverview', 'AreaTouchOverview', 'AreaTouchDetailOverview'].includes(this.type)) {
            await this.getColumnAndRowMap();
        }
        if (['TeamBuildOverview', 'AreaBuildOverview', 'TeamOrderOverview', 'AreaOrderOverview'].includes(this.type)) {
            // 获取K/V动态列
            await this.queryDynamicKvColumn();
        }
        await this.queryOverviewData();
    },
    computed: {
        /**
         * @desc 获取高度，区域和业务的每个块的高度是通过计算得到的，所以在折叠V序列和K序列时，高度会动态变化,需要动态计算
         * <AUTHOR>
         * @date 2023/7/21
         **/
        getHeight() {
            // V序列和K序列标题的高度
            const titleHeight = 2 * (this.rpxToPx(70) + this.rpxToPx(24) * 2) + this.rpxToPx(2)
            return (item) => {
                // K序列里被折叠项的高度
                const otherHeightK = Number(!item.isCollapseK) * (item.kLineList.length - 1) * (this.rpxToPx(70) + this.rpxToPx(24))
                // V序列里被折叠项的高度
                const otherHeightV = Number(!item.isCollapseV) * (item.vLineList.length - 1) * (this.rpxToPx(70) + this.rpxToPx(24))
                return titleHeight + otherHeightK + otherHeightV + 'px'
            }
        },
    },
    methods: {
        /**
         * 查询动态K/V序列
         * @Author:付常涛
         * @Date: 2023/11/21 20:17:33
         */
         async queryDynamicKvColumn() {
            let overviewType = '';
            if (['AreaBuildOverview', 'TeamBuildOverview'].includes(this.type)) {
                overviewType = '消费者建设总览';
            } else if (['TeamOrderOverview', 'AreaOrderOverview'].includes(this.type)) {
                overviewType = '消费者动销总览';
            }
            const orgParam = {};
            if (['AreaBuildOverview', 'AreaOrderOverview'].includes(this.type)) {
                orgParam.orgId = this.params.orgId;
            }
            const promiseList = ['K', 'V'].map((val) => {
                return new Promise(async (resolve, reject) => {
                    const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send",
                        {dataAccess: this.params.dataAccess, timeRange: this.params.timeRange, companyId: this.params.companyId, dmpSrUrl: '/link/board/dynamicKVType', seqType: val, overviewType, ...orgParam}, {
                            autoHandleError: false,
                            handleFailed: (response) => {
                                this.$utils.hideLoading();
                                this.$message.error({message: `查询${this.message}的${val}序列动态列失败！` + response.result, customFlag: true});
                                reject(response.result);
                            }
                    });
                    resolve(data);
                })
            });
            const [kColumns, vColumns] = await Promise.all(promiseList);
            /* K/V序列排序 */
            const kColumnsSort = await this.sortTargetArr(kColumns.rows, 'ACCT_SUB_TYPE', 'fieldName', true);
            const vColumnsSort = await this.sortTargetArr(vColumns.rows, 'ACCT_MEMBER_LEVEL', 'fieldName', true);
            switch (this.type) {
                case 'AreaBuildOverview':
                case 'TeamBuildOverview':
                    // 各业务消费者建设总览
                    this.dynamicsK = [...kColumnsSort, {field: 'consumerQty', fieldName: '合计'}];
                    this.dynamicsV = [...vColumnsSort, {field: 'consumerQty', fieldName: '合计'}];
                    break;
                case 'AreaOrderOverview':
                case 'TeamOrderOverview':
                    // 各区域消费者动销总览
                    // 各业务消费者动销总览
                    if(this.params.saleType === 'amt'){
                        this.dynamicsK = [...kColumnsSort, {field: 'ordAmt', fieldName: '合计'}];
                        this.dynamicsV = [...vColumnsSort, {field: 'ordAmt', fieldName: '合计'}];
                    } else {
                        this.dynamicsK = [...kColumnsSort, {field: 'ordBotQty', fieldName: '合计'}];
                        this.dynamicsV = [...vColumnsSort, {field: 'ordBotQty', fieldName: '合计'}];
                    }
                    break;
            }
        },
        /**
         * @desc 切换折叠状态
         * <AUTHOR>
         * @date 2023/7/20 11:24
         **/
        changeCollapse(item, type) {
            if (type === 'V') item.isCollapseV = !item.isCollapseV
            if (type === 'K') item.isCollapseK = !item.isCollapseK
        },
        /**
         * @desc 加载更多
         * <AUTHOR>
         * @date 2023/7/10 11:24
         **/
        async loadMore () {
            if (this.total === this.lineData.length) return;
            this.params['page']++;
            await this.queryOverviewData();
        },
        /**
         * 获取活动邀约的动态列
         * @Author:付常涛
         * @Date: 2023/11/11 12:00:03
         */
        async queryDynamicColumn() {
            const orgParam = {};
            if (this.params.dataAccess === 'Area') {
                orgParam.orgId = this.params.orgId;
            }
            const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send",
                {dataAccess: this.params.dataAccess, timeRange: this.params.timeRange, companyId: this.params.companyId, ...orgParam, dmpSrUrl: '/link/board/dynamicActClass'}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$message.error({message: `查询${this.lineTitle}的动态列失败！` + response.result, customFlag: true});
                        reject(response.result);
                    }
            });
            /* 活动邀约动态列 */
            let noSortColumn = data.rows.map((i) => ({name: i.fieldName, val: i.field}));
            /* 活动邀约动态列--排序 */
            const lovList = await this.$lov.getLovByType('ACT_CATEGORY');
            this.dynamicColumnList = await this.sortTargetArr(noSortColumn, lovList, 'name');
        },
        /**
         * 排序目标数组(非异步)
         * @Author:付常涛
         * @Date: 2023/11/21 15:00:15
         * @param arr  目标数组
         * @param lovType  值列表
         * @param equalField  判断相等的字段
         * @param isAsync  是否异步
         */
        async sortTargetArr(arr, lovType, equalField, isAsync) {
            let res = null;
            if (isAsync) {
                res = await this.$lov.getLovByType(lovType);
            } else {
                res = lovType;
            }
            if (res && res.length > 0) {
                arr.forEach((item) => {
                    const lov = res.find((i) => i.name === item[equalField]);
                    if (lov) {
                        item.sort = Number(lov.seq);
                    } else {
                        item.sort = 0;
                    }
                });
                arr.sort((item1, item2) => item2.sort - item1.sort);
            }
            return this.$utils.deepcopy(arr);
        },
        /**
         * 查询区域、业代建设、动销总览
         * @Author:付常涛
         * @Date: 2023/11/21 14:33:58
         */
        async queryOverviewData () {
            this.$utils.showLoading();
            if (this.dynamicColumnList.length === 0 && ['TeamTouchOverview', 'AreaTouchOverview', 'AreaTouchDetailOverview'].includes(this.type)) {
                await this.queryDynamicColumn();
            }
            const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send",
                {...this.params, dmpSrUrl: this.url}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError(`查询${this.message}数据失败！` + response.result);
                    }
                });
            if (data.success) {
                // 触达总览的数据需要特殊处理
                if (['TeamTouchOverview', 'AreaTouchOverview', 'AreaTouchDetailOverview'].includes(this.type)) {
                    // 动态列长度
                    this.columnLength = this.dynamicColumnList.length + this.staticColumnList.length
                    const newArr = await this.dealwithData(data.rows);
                    // console.log('newArr', JSON.parse(JSON.stringify(newArr)));
                    // 分别获取每个业代要渲染数据的二维数组
                    newArr.forEach((item) => {
                        if (this.isCollapse) {// 如果是折叠展示，默认全部折叠
                            this.$set(item, 'isCollapseK', true);
                            this.$set(item, 'isCollapseV', true);
                        }
                        if(this.type === 'AreaTouchOverview'){
                            item.sortSumTotal = item.heji.length > 0 ? item.heji[item.heji.length - 1] : 0;
                        }
                    })
                    if (this.type === 'AreaTouchOverview') {
                        this.lineData = newArr.sort((a, b) => b.sortSumTotal - a.sortSumTotal);
                        this.total = -1;
                    } else {
                        this.lineData = this.lineData.concat(newArr);
                        this.total = data.total;
                    }
                } else {
                    // 各区域消费者建设总览--按照“合计”数量降序排序
                    if (this.type === 'AreaBuildOverview') {
                        this.lineData = data.rows.sort((a, b) => {
                            return Number(b.consumerQty) - Number(a.consumerQty);
                        })
                        this.total = -1;
                    } else if (this.type === 'AreaOrderOverview') {
                        // 各区域消费者动销总览--按照“合计”数量降序排序
                        let sortField = '';
                        if(this.params.saleType === 'amt') {
                            sortField = 'ordAmt';
                        } else {
                            sortField = 'ordBotQty';
                        }
                        this.lineData = data.rows.sort((a, b) => {
                            return Number(b[sortField]) - Number(a[sortField]);
                        })
                        this.total = -1;
                    } else {
                        this.lineData = this.lineData.concat(data.rows);
                        this.total = data.total;
                    }
                }
                // console.log('lineData', JSON.parse(JSON.stringify(this.lineData)));
                this.$utils.hideLoading();
            }
        },
        /**
         * 处理后端数据成前端所需要的格式
         * @Author:付常涛
         * @Date: 2023/11/21 14:59:58
         * @param res  后端返回
         */
         async dealwithData(res) {
            const list = [];
            const deal = (obj, cur) => {
                const columnList = this.dynamicColumnList.concat(this.staticColumnList)
                if (cur.seqType === 'K' && cur.seq) {
                    const arr = [];
                    columnList.forEach((i) => {
                        arr.push(cur[i.val]);
                    });
                    obj.kLineList.push({name: cur.seq, data: arr});
                } else if (cur.seqType === 'V' && cur.seq) {
                    const arr = [];
                    columnList.forEach((i) => {
                        arr.push(cur[i.val]);
                    });
                    obj.vLineList.push({name: cur.seq, data: arr});
                } else if (cur.seqType === 'ALL' && !cur.seq) {
                    cur.seq = '总计';
                    const arr = [];
                    columnList.forEach((i) => {
                        arr.push(cur[i.val]);
                    });
                    obj.heji = arr;
                }
            }
            // 处理动态列、K/V
            for (let index = 0; index < res.length; index++) {
                const cur = res[index];
                let item = null;
                if (this.type === 'AreaTouchOverview') {
                    // 各区域消费者触达总览
                    item = list.find((i) => i.orgId === cur.orgId);
                } else if (this.type === 'TeamTouchOverview' || this.type === 'AreaTouchDetailOverview') {
                    // 各业务消费者触达总览
                    item = list.find((i) => i.staffId === cur.staffId && i.postName === cur.postName);
                }
                if (item) {
                    deal(item, cur);
                } else {
                    const newObj = {
                        kLineList: [],
                        kVal: [],
                        vLineList: [],
                        vVal: [],
                        heji: {}
                    };
                    if (this.type === 'AreaTouchOverview') {
                        newObj.orgId = cur.orgId;
                        newObj.orgName = cur.orgName;
                    } else if (this.type === 'TeamTouchOverview' || this.type === 'AreaTouchDetailOverview') {
                        newObj.staffId = cur.staffId;
                        newObj.staffName = cur.staffName;
                        newObj.postName = cur.postName;
                        newObj.postnId = cur.postnId;
                    }
                    deal(newObj, cur);
                    list.push(newObj);
                }
            }
            const kLov = await this.$lov.getLovByType('ACCT_SUB_TYPE');
            const vLov = await this.$lov.getLovByType('ACCT_MEMBER_LEVEL');
            list.forEach(async(i) => {
                /* K/V序列排序 */
                i.kLineList = await this.sortTargetArr(i.kLineList, kLov, 'name');
                i.vLineList = await this.sortTargetArr(i.vLineList, vLov, 'name');
                /* 按照顺序赋值 */
                i.kLineList.forEach((item) => {
                    i.kVal.push(item.data);
                    delete item.data;
                });
                i.vLineList.forEach((item) => {
                    i.vVal.push(item.data);
                    delete item.data;
                });
                // 处理合计列及统计K/V长度
                i.kLineList.push({name: '合计'});
                i.kVal.push(i.heji);
                i.vLineList.push({name: '合计'});
                i.vVal.push(i.heji);
                i.lineLength = i.kLineList.length + i.vLineList.length;
            });
            return list;
        },
        /**
         * 片区跳转业代详情
         * @Author:付常涛
         * @Date: 2023/11/21 21:50:30
         * @param item 行数据
         */
        gotoSalesmanItem (item) {
            if (!this.isCheckDetail) return;
            if (this.$utils.isEmpty(item.orgId)) return;
            let param = this.$utils.deepcopy(this.params);
            param['orgId'] = item.orgId;
            let url = '';
            switch (this.type) {
                // 各区域消费者建设总览
                case 'AreaBuildOverview':
                    url = '/link/board/csmAreaConDetail';
                    this.message = '业代消费者建设总览';
                    break;
                // 各区域消费者动销总览
                case 'AreaOrderOverview':
                    url = '/link/board/csmStaffSaleOverview';
                    this.message = '业代消费者动销总览';
                    break;
            }
            this.$nav.push('/pages/echart/lzlj/consumer-kanban-new/consumer-overview-item-new-page', {
                pageFrom: 'RegionConsumer',
                orgId: item.orgId,
                url: url,
                type: this.type,
                params: param,
                summaryType: 'Yd'
            })
        },
        /**
         * 点击K/V动态列进入详情-TeamBuildOverview
         * @Author:付常涛
         * @Date: 2023/11/21 20:07:57
         * @param row 当前行数据
         * @param column 当前列数据
         * @param value 当前数据
         * @param type 当前列K/V类型
         */
        gotoItem (row, column, value, type) {
            // console.log('下钻', row, column, value, type, this.isCheckDetail);
            if (!this.isCheckDetail) return;
            if (value === '0' || value === 0) return;
            if (this.type === 'TeamOrderOverview' || this.type === 'AreaOrderOverview') {
                let kTypeName = '';
                let vTypeName = '';
                if (type === 'K' && column.fieldName !== '合计') {
                    kTypeName = column.fieldName;
                } else if (type === 'V' && column.fieldName !== '合计') {
                    vTypeName = column.fieldName;
                }
                const item = {
                    kTypeName, // k序列名称
                    vTypeName, // v序列名称
                }
                if (this.type === 'TeamOrderOverview') {
                    // 各业务消费者动销总览
                    item.staffId = row.staffId; // 业务代表ID
                    item.postnId = row.postnId; // 职位ID
                } else if (this.type === 'AreaOrderOverview') {
                    // 各区域消费者动销总览
                    item.orgId = row.orgId;
                }
                this.$nav.push('/pages/echart/lzlj/consumer-kanban-new/consumer-overview-item-new-page', {
                    pageFrom: 'ConsumerOrder',
                    item,
                    timeRange: this.params.timeRange,
                    dataRange: this.params.dataAccess,
                    companyId: this.params.companyId
                })
            } else {
                this.gotoAccountList(row, column, value, type);
            }
        },
        /**
         * 跳转消费者列表
         * @Author:付常涛
         * @Date: 2023/11/20 11:31:44
         * @param row 当前行数据
         * @param column 当前列数据
         * @param value 当前数据
         * @param type 当前列K/V类型
         */
        gotoAccountList (row, column, value, type) {
            if (value === '0' || value === 0) return;
            let kTypeName = '';
            let vTypeName = '';
            if (type === 'K' && column.fieldName !== '合计') {
                kTypeName = column.fieldName;
            } else if (type === 'V' && column.fieldName !== '合计') {
                vTypeName = column.fieldName;
            }
            const filterInfo = {
                timeRange: this.params.timeRange,
                dataRange: this.params.dataAccess,
            }
            if (this.type === 'AreaBuildOverview') {
                // 各区域消费者建设总览
                filterInfo.orgId = row.orgId; // 组织ID
            } else if (this.type === 'TeamBuildOverview') {
                // 各业务消费者建设总览
                filterInfo.staffId = row.staffId; // 业务代表ID
                filterInfo.postnId = row.postnId; // 职位ID
            }
            this.$nav.push('/pages/lj-consumers/account/account-list-new-page', {
                pageFrom: "ConsumerOverview",
                kTypeName,
                vTypeName,
                filterInfo
            })
        },
        /**
         * 点击业务代表跳转消费者触达明细(各业务消费者触达总览)
         * @Author:付常涛
         * @Date: 2023/11/20 14:54:10
         * @param item  行数据
         */
        gotoConsumerTouchDetail (item) {
            // 页面设置不可跳转，不执行函数
            if (!this.isCheckDetail) return;
            // 页面是消费者触达区域下转进入，不执行函数
            if (this.type === 'AreaTouchDetailOverview') return
            let sourceFrom = 'activity'
            this.$nav.push('/pages/echart/lzlj/consumer-kanban-new/consumer-overview-item-new-page.vue', {
                pageFrom: 'ConsumerTouchDetail',
                item: {staffId: item.staffId, postnId: item.postnId},
                timeRange: this.timeScope,
                dataRange: this.dataRange,
                companyId: this.companyId,
                sourceFrom
            })
        },
        /**
         * 跳转该区域的业代详情明细
         * @Author:付常涛
         * @Date: 2023/11/21 21:47:29
         * @param item 行数据
         */
        gotoAreaDetail (item) {
            if (this.dataRange !== 'Area' || !this.isCheckDetail) return
            // console.log('item', item)
            this.$nav.push('/pages/echart/lzlj/consumer-kanban-new/consumer-overview-list-new-page.vue', {
                url:'/link/board/csmAreaTouchDetail',
                params: {
                    dataAccess: this.dataRange,
                    timeRange: this.timeScope,
                    orgId: item.orgId,
                    companyId: this.params.companyId,
                    rows: 20,
                },
                isCollapse: true,      // 是否折叠
                isCheckDetail: true,    // 是否可以下钻
                lineTitle: item.orgName + '消费者触达总览',
                message: item.orgName + '消费者触达总览',
                dataRange: this.dataRange,
                type: 'AreaTouchDetailOverview',
            })
        },
        /**
         * 跳转消费者触达详情
         * @Author:付常涛
         * @Date: 2023/11/21 16:21:38
         * @param value 值
         * @param index 列下标
         * @param source K、V
         * @param sourceList K、V的动态行
         * @param sourceIndex K、V的动态行序号
         * @param id 业务代表ID或组织ID
         * @param postnId 职位ID
         */
        gotoItemForTouch (value, index, source, sourceList, sourceIndex, id, postnId) {
            const columnList = this.dynamicColumnList.concat(this.staticColumnList);
            const actMClassName = columnList[index].name;
            const typeName = sourceList[sourceIndex].name;
            // console.log(actMClassName, typeName);
            // console.log(value, source, id, this.params.orgId);
            if (!this.isCheckDetail) return;
            if (value <= 0) return
            if (this.columnLength - 1 === index) return; // 合计列不能下钻
            if (!this.isCollapse && this.dataRange !== 'Team') return; // 只有团队才能下钻
            let sourceFrom = 'activity'
            let columnsLength = this.dynamicColumnList.length + this.staticColumnList.length
            if (index === columnsLength - 2) {
                sourceFrom = 'visit'
            } else if (index === columnsLength - 3) {
                sourceFrom = 'present'
            }
            let kTypeName = '';
            let vTypeName = '';
            if (source === 'K') {
                kTypeName = typeName === '合计' ? '' : typeName;
            } else {
                vTypeName = typeName === '合计' ? '' : typeName;
            }
            const item = {
                actMClassName, // 活动类型名称
                kTypeName, // k序列名称
                vTypeName, // v序列名称
            }
            if (this.type === 'TeamTouchOverview') {
                // 各业务消费者触达总览
                item.staffId = id;
                item.postnId = postnId;
            } else if (this.type === 'AreaTouchOverview') {
                // 各区域消费者触达总览
                item.orgId = id;
            } else if (this.type === 'AreaTouchDetailOverview') {
                item.staffId = id;
                item.postnId = postnId;
                item.orgId = this.params.orgId;
            }

            this.$nav.push('/pages/echart/lzlj/consumer-kanban-new/consumer-overview-item-new-page.vue', {
                pageFrom: this.pageFrom || 'ConsumerContact',
                item,
                timeRange: this.params.timeRange,
                dataRange: this.params.dataAccess,
                companyId: this.params.companyId,
                sourceFrom
            })
        },
        /**
         * @desc 获取行映射数组和列映射数组
         * <AUTHOR>
         * @date 2023/7/10
         **/
        async getColumnAndRowMap () {
            const columnLovTypeArr = ['ACT_CATEGORY']
            const rowLovTypeArr = ['ACCT_MEMBER_LEVEL', 'ACCT_SUB_TYPE']
            const columnMap = [
                {name: '品', val: 'Pin', seq: '105'},
                {name: '赠', val: 'Zeng', seq: '104'},
                {name: '游', val: 'You', seq: '103'}
            ]
            const rowMap = [
                {name: '合计', val: 'heJi'}
            ]
            const columnPromise = this.getMap(columnMap, columnLovTypeArr);
            const rowPromise = this.getMap(rowMap, rowLovTypeArr);
            const [columnMapResult, rowMapResult] = await Promise.all([columnPromise, rowPromise]);
            this.columnMap = columnMapResult;
            this.rowMap = rowMapResult;
            // console.log('this.columnMap', this.columnMap);
            // console.log('this.rowMap', this.rowMap);
        },
        /**
         * @desc 获取后端返回的动态行、列的字段和展示值的对应关系
         * <AUTHOR>
         * @param initialArr 初始化的数组
         * @param lovTypeArr 对应字段的多个值列表
         * @date 2023/7/3 10:38
         **/
        async getMap (initialArr, lovTypeArr) {
            for (const lovType of lovTypeArr) {
                const res = await this.$lov.getLovByType(lovType);
                if (res && res.length > 0) {
                    res.forEach(({name, val, seq}) => {
                        initialArr.forEach((item)=> {
                            if (item.val === val) {
                                item.seq = seq;
                            }
                        })
                        initialArr.push({name, val, seq})
                    })
                }
            }
            return initialArr;
        },
        rpxToPx(rpx) {
            const systemInfo = wx.getSystemInfoSync();
            const pixelRatio = systemInfo.pixelRatio;
            const screenWidth = systemInfo.screenWidth;
            const designWidth = 750; // 设计稿宽度，可以根据实际情况进行调整
            const px = rpx * screenWidth / designWidth;
            return Math.floor(px);
        },
    }
}
</script>
<style lang="scss">
.consumer-overview-list-page{
    background: white;
    padding: 0 24px;
    font-size: 28px;
    .item-info{
        color: #3F66EF;
        text-decoration: underline;
    }
    .load-more{
        width: 100%;
        text-align: center;
        color: #6D96FA;
        margin-top: 32px;
        font-size: 24px;
        padding-bottom: 32px;
    }
    .consumer-overview-chart{
        text-align: center;
        display: flex;
        border-bottom: 2px solid #F0F2F8;
        .title-line-item{
            color: #999999;
            height: 176px;
            background:  #F8FAFF;
        }
        .overview-line {
            border-right: 2px solid #F0F2F8;
        }
        .consumer-overview-left{
            display: flex;
            .kv-title{
                width: 166px !important;
                line-height: 176px;
            }
            .kv-line{
                margin: 24px 0;
                white-space: nowrap;
                overflow-y: hidden;
                overflow-x: auto;
            }
            .other-line{
                width: 148px;
                height: 40px;
                padding: 24px 10px;
                flex: none;
                overflow-x: auto;
                overflow-y: hidden;
                white-space: nowrap;
            }
        }
        .consumer-overview-right{
            overflow-x: auto;
            flex: 1;
            display: flex;
            flex-direction: column;
            .activity-item{
                background:  #F8FAFF;
                .activity-title {
                    height: 96px;
                    display: flex;
                    padding-left: 20px;
                    align-items: center;
                    border-right: 2px solid #F0F2F8;
                }
                .activity-line-title{
                    display:flex;
                    height: 80px;
                    width: auto;
                    line-height: 80px;
                    border-top: 2px solid #F0F2F8;
                    border-right: 2px solid #F0F2F8;
                    .activity-line-item-title{
                        width: 153px;
                        overflow-x: auto;
                        white-space: nowrap;
                        padding: 0 4px;
                    }
                }
            }
            .title-item{
                width: 160px;
                flex: none;
                background:  #F8FAFF;
            }
            .item-total{
                width: 104px !important;
            }
            .title-line-item{
                display: flex;
            }
            .title-line-right{
                display: flex;
                justify-content: center;
                align-items: center;
                height: 176px;
                background:  #F8FAFF;
                border-left: 2px solid #F0F2F8;
            }
            .all-data{
                display: flex;
            }
            .kv-data-line{
                width: auto;
            }
            .data-line{
                display: flex;
            }
            .data-item{
                width: 160px;
                flex: none;
                height: 40px;
                padding: 24px 0;
                overflow-y: hidden;
                overflow-x: auto;
            }
        }
    }
    .consumer-touch-overview-chart {
        text-align: center;
        display: flex;
        $line-height: 70px;
        .title-line-item{
            color: #999999;
            height: 176px;
            background:  #F8FAFF;
        }
        .consumer-overview-left{
            display: flex;
            .kv-line-title{
                width: 150px;
                border-right: 2px solid #F0F2F8;
                border-bottom: 2px solid #F0F2F8;
                display: flex;
                flex-direction: column;
            }
            .title-line-item{
                line-height: 176px;
            }
            .other-line{
                display: flex;
                align-items: center;
                justify-content: center;
                flex: none;
                border-bottom: 2px solid #F0F2F8;
            }
        }
        .consumer-overview-right{
            overflow-x: auto;
            flex: 1;
            display: flex;
            .kv-title{
                width: 222px !important;
                border-right: 2px solid #F0F2F8;
                line-height: 176px;
            }
            .kv-line{
                margin: 24px 0;
                height: $line-height;
                line-height: $line-height;
                white-space: nowrap;
                overflow-y: hidden;
                overflow-x: auto;
            }
            .other-line{
                width: 222px;
                display: flex;
                align-items: center;
                flex: 1;
                border-bottom: 2px solid #F0F2F8;
                .consumer-kv-left{
                    width: 62px;
                    display: flex;
                    justify-content: center;
                    .kv-name{
                        width: 28px;
                        height: auto;
                        word-wrap: break-word;
                        word-break:break-all;
                    }
                }
                .consumer-kv-value{
                    .collapse-title {
                        color: #3F66EF;
                        .link-icon {
                            transition: transform .5s ease;
                            transform: rotate(-180deg);
                        }
                    }
                    .collapse-title.collapse {
                        .link-icon {
                            transform: rotate(0deg);
                        }
                    }
                    width: 160px;
                    flex:none;
                    border-left: 2px solid #F0F2F8;
                }
            }
            .other-data-line{
                display: flex;
                flex-direction: column;
            }
            .activity-item{
                background:  #F8FAFF;
                .activity-title {
                    height: 96px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
                .activity-line-title{
                    display:flex;
                    height: 80px;
                    width: 960px;
                    line-height: 80px;
                    border-top: 2px solid #F0F2F8;
                    .activity-line-item-title{
                        flex: 1;
                        width: 153px;
                        overflow-x: auto;
                        white-space: nowrap;
                        padding: 0 4px;
                    }
                }
            }
            .title-item{
                width: 168px;
                flex: none;
                background:  #F8FAFF;
            }
            .item-total{
                width: 104px !important;
            }
            .title-line-item{
                display: flex;
                justify-content: center;
            }
            .title-line-right{
                display: flex;
                justify-content: center;
                align-items: center;
                height: 176px;
                background:  #F8FAFF;
                border-left: 2px solid #F0F2F8;
            }
            .kv-data-line{
                border-bottom: 2px solid #F0F2F8;
                width: auto;
            }
            .data-line{
                display: flex;
                margin: 24px 0;
            }
            .data-item{
                width: 168px;
                flex: none;
                height: $line-height;
                line-height: $line-height;
                overflow-y: hidden;
                overflow-x: auto;
            }
        }
    }
}
</style>
