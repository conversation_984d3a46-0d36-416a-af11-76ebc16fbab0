<!--
 * 消费者触达总览(次)、消费者动销总览、客户投入产出比点击分类下钻
 * 下钻页面
 * @Author:付常涛
 * @Date: 2023/11/14 16:37:33
-->
<template>
    <link-page class="consumer-overview-item-page">
        <!--    消费者动销详情    -->
        <view class="consumer-order-overview" v-if="pageFrom === 'ConsumerOrder'">
            <line-title title="消费者动销详情"></line-title>
            <view class="consumer-overview-item-chart">
                <view class="consumer-order-title">
                    <view class="order-line-title first-title-line">消费者</view>
                    <view class="order-line-title second-title-line">品项</view>
                    <view class="order-line-title">销售瓶数</view>
                    <view class="order-line-title">销售金额</view>
                </view>
                <view class="consumer-order-data">
                    <view class="data-line" v-for="(item, index) in orderDetailData" :key="index">
                        <view class="data-line-item first-line" @tap="gotoAccountItem(item)">{{item.consumerName}}</view>
                        <view class="other-data-line">
                            <view class="other-prod-line" v-for="(prodItem, index) in item.prodVos" :key="index + 'prod'">
                                <view class="data-line-item second-line">{{prodItem.prodTypeName}}</view>
                                <view class="data-line-item">{{prodItem.ordBotQty}}</view>
                                <view class="data-line-item">{{prodItem.ordAmt}}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <!--    消费者触达详情/各业务消费者触达详情    -->
        <view class="consumer-contact-overview" v-if="pageFrom === 'ConsumerContact' || pageFrom === 'ConsumerContactRegion'">
            <line-title title="消费者触达详情"></line-title>
            <view class="consumer-contact-item-chart">
                <!-- 左侧固定列 -->
                <view class="contact-chart-left">
                    <!-- 标题 -->
                    <view class="contact-title-item">消费者姓名</view>
                    <!-- 数据item -->
                    <view class="contact-data-item item-info" :style="{'height': item.itemList.length * 96 + 'rpx'}"
                          v-for="(item, index) in orderDetailData" :key="index">
                        <text class="item-info" @tap="gotoConsumerDetail(item)">{{item.consumerName}}</text>
                    </view>
                </view>
                <!-- 右侧滑动列 -->
                <view class="contact-chart-right">
                    <!-- 标题行 -->
                    <view class="contact-title-line">
                        <view class="contact-title-item">{{titleObj.typeTitle}}</view>
                        <view class="contact-title-item">{{titleObj.timeTitle}}</view>
                    </view>
                    <!-- 数据行 -->
                    <view class="contact-data-line" v-for="(item, index) in orderDetailData" :key="index">
                        <view class="contact-data-sub-line" v-for="(subItem, subIndex) in item.itemList" :key="subIndex">
                            <view class="contact-data-item" v-if="titleObj.actType === 'act'">{{subItem.actName}}</view>
                            <view class="contact-data-item" v-else-if="titleObj.actType === 'visit'">{{subItem.visitWayName}}</view>
                            <view class="contact-data-item" v-else="titleObj.actType === 'present'">{{subItem.presentType}}</view>
                            <view class="contact-data-item">{{subItem.auditTime}}</view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <!--    客户投入产出比    -->
        <view class="consumer-input-output-ratio" v-if="pageFrom === 'ConsumerInputOutput'">
            <line-title title="客户投入产出比"></line-title>
            <view class="consumer-overview-chart">
                <view class="consumer-overview-left">
                    <view class="title-line-item kv-title">客户名称</view>
                    <view class="other-line" v-for="(item, index) in orderDetailData" :key="index">
                        <text>{{item.acctName}}</text>
                    </view>
                </view>
                <view class="consumer-overview-right">
                    <view class="title-line-item">
                        <view class="title-item">
                            <link-dropdown-group placement="bottom" align="start">
                                <view class="filter-type-item" @tap="expansionLevel">
                                    客户等级<link-icon icon="mp-desc"/>
                                </view>
                            </link-dropdown-group>
                        </view>
                        <view class="title-item">跟进业务</view>
                        <view class="title-item">消费者数量</view>
                        <view class="title-item">活动场次</view>
                        <view class="title-item">活动费用</view>
                        <view class="title-item">消费者动销金额</view>
                        <view class="title-item">费效比</view>
                    </view>
                    <view class="data-line" v-for="(item, index) in orderDetailData" :key="index">
                        <view class="data-item">{{item.acctLevelName}}</view>
                        <view class="data-item">{{item.followStaffName}}</view>
                        <view class="data-item">{{item.consumerQty}}</view>
                        <view class="data-item">{{item.actTimes}}</view>
                        <view class="data-item">{{Number(item.actCost || 0).toFixed(2)}}</view>
                        <view class="data-item">{{Number(item.acctOrdAmt || 0).toFixed(2)}}</view>
                        <view class="data-item">{{(Number(item.ceRatio || 0) * 100).toFixed(2)}}%</view>
                    </view>
                </view>
            </view>
        </view>
        <!--    片区详情    -->
        <view class="region-consumer-overview" v-if="pageFrom === 'RegionConsumer'">
            <line-title title="片区详情"></line-title>
            <view class="consumer-overview-chart">
                <view class="consumer-overview-left">
                    <view class="overview-line">
                        <view class="title-line-item kv-title">
                            业务代表
                        </view>
                        <view class="other-line" v-for="(item, index) in orderDetailData" :key="index">{{item.staffName}}</view>
                    </view>
                    <view class="overview-line">
                        <view class="title-line-item kv-title">
                            职位
                        </view>
                        <view class="other-line" v-for="(item, index) in orderDetailData" :key="index">{{item.postName || item.postnName}}</view>
                    </view>
                </view>
                <view class="consumer-overview-right">
                    <view class="title-line-item">
                        <view class="activity-item">
                            <view class="activity-title">K序列</view>
                            <view class="activity-line-title" v-if="orderDetailData.length > 0">
                                <view class="activity-line-item-title" v-for="(item, index) in dynamicsK" :key="index + 'sK'">{{item.fieldName}}</view>
                            </view>
                        </view>
                        <view class="activity-item">
                            <view class="activity-title">V序列</view>
                            <view class="activity-line-title" v-if="orderDetailData.length > 0">
                                <view class="activity-line-item-title" v-for="(item, index) in dynamicsV" :key="index + 'sV'">{{item.fieldName}}</view>
                            </view>
                        </view>
                    </view>
                    <view class="all-data">
                        <view class="kv-data-line" v-if="orderDetailData.length > 0 && dynamicsK.length > 0">
                            <view class="data-line" v-for="(item, index) in orderDetailData" :key="index + 'kv'">
                                <view class="data-item" :class="item[lineItem.field] !== '0' && item[lineItem.field] !== 0 ? 'item-info' : ''" v-for="(lineItem, index) in dynamicsK" :key="index + 'lk'" @tap="gotoItem(item, lineItem, item[lineItem.field], 'K')">{{item[lineItem.field]}}</view>
                            </view>
                        </view>
                        <view class="kv-data-line" v-if="orderDetailData.length > 0 && dynamicsV.length > 0">
                            <view class="data-line" v-for="(item, index) in orderDetailData" :key="index + 'vv'">
                                <view class="data-item" :class="item[lineItem.field] !== '0' && item[lineItem.field] !== 0 ? 'item-info' : ''" v-for="(lineItem, index) in dynamicsV" :key="index + 'lv'" @tap="gotoItem(item, lineItem, item[lineItem.field], 'V')">{{item[lineItem.field]}}</view>
                            </view>
                        </view>
                    </view>
                    <view class="no-data" v-if="orderDetailData.length <=0">暂无数据</view>
                </view>
            </view>
        </view>
        <!--    消费者触达明细    -->
        <view class="consumer-touch-detail-overview" v-if="pageFrom === 'ConsumerTouchDetail'">
            <line-title title="消费者触达明细"></line-title>
            <!-- 按钮选择框 -->
            <scroll-view class="scroll-view-data" scroll-x="true">
                <view class="select-dimension">
                    <select-button :key="index"
                                   :label="item.name"
                                   :selectedFlag="item.selectedFlag"
                                   :value="item.value"
                                   isBoard
                                   @tap="chooseData($event,item)"
                                   v-for="(item,index) in selectDimensionLevel"/>
                </view>
            </scroll-view>
            <view class="consumer-overview-chart">
                <view class="consumer-overview-left">
                    <view class="overview-line">
                        <view class="title-line-item kv-title">
                            消费者姓名
                        </view>
                        <view class="other-line item-info" v-for="(item, index) in orderDetailData" :key="index"
                              @tap="gotoConsumerDetail(item)">
                            <text>{{item.consumerName}}</text>
                        </view>
                    </view>
                </view>
                <view class="consumer-overview-right">
                    <view class="title-line-item">
                        <view class="activity-item">
                            <view class="activity-title">活动邀约</view>
                            <view class="activity-line-title">
                                <view class="activity-line-item-title">
                                    <link-dropdown-group placement="bottom" align="start">
                                        <view class="filter-type-item">
                                            K序列<link-icon icon="mp-desc"/>
                                        </view>
                                        <view slot="dropdown">
                                            <link-dropdown-menu v-for="(item) in kSeqMap" :key="item.val" :label="item.name" @tap="selectChange(item, 'KSeq')"/>
                                        </view>
                                    </link-dropdown-group>
                                </view>
                                <view class="activity-line-item-title">
                                    <link-dropdown-group placement="bottom" align="start">
                                        <view class="filter-type-item">
                                            V序列<link-icon icon="mp-desc"/>
                                        </view>
                                        <view slot="dropdown">
                                            <link-dropdown-menu v-for="(item) in vSeqMap" :key="item.val" :label="item.name" @tap="selectChange(item, 'VSeq')"/>
                                        </view>
                                    </link-dropdown-group>
                                </view>
                                <view class="activity-line-item-title" v-for="(item, i) in dynamicColumnList" :key="i">{{item.name}}</view>
                            </view>
                        </view>
                        <view class="title-line-right">
                            <view class="title-item" v-for="(item) in staticColumnList" :key="item.val">{{item.name}}</view>
                        </view>
                    </view>
                    <view class="kv-data-line">
                        <view class="data-line" v-for="(item, index) in orderDetailData" :key="index">
                            <view class="data-item">{{item.kTypeName}}</view>
                            <view class="data-item">{{item.vTypeName}}</view>
                            <view class="data-item" v-for="(subItem, subIndex) in [...dynamicColumnList, ...staticColumnList]" :key="subIndex">{{item[subItem.val]}}</view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="load-more" v-if="orderDetailData.length > 0" @tap="loadMore()">{{ loadFinish ? '加载完成' : '加载更多' }}</view>
        <water-mark></water-mark>
    </link-page>
</template>
<script>
import LineTitle from "../components/line-title";
import SelectButton from "../components/select-button";
import waterMark from "../../../lzlj/components/water-mark";
export default {
    name: 'consumer-overview-item-page',
    components: {LineTitle, SelectButton, waterMark},
    data () {
        const sourceFrom = this.pageParam.sourceFrom || 'activity';
        const pageFrom = this.pageParam.pageFrom || '';
        const userInfo = this.$taro.getStorageSync('token').result;
        const item = this.pageParam.item || {};
        const timeRange = this.pageParam.timeRange || '';
        const dataRange = this.pageParam.dataRange || '';
        const companyId = this.pageParam.companyId || '';
        const orgId = this.pageParam.orgId || userInfo.orgId;
        const url = this.pageParam.url || '';
        const params = this.pageParam.params || {};
        const type = this.pageParam.type || '';
        const summaryType = this.pageParam.summaryType || '';
        const parentId = this.pageParam.parentId || '';
        return {
            total: 0,
            parentId,
            summaryType, // 总览类型
            type,
            params,
            url,
            orgId,
            dataRange,
            timeRange,
            companyId,
            orderDetailData: [], // 动销详情数据
            pageFrom,
            sourceFrom,
            userInfo,
            item,
            pagingPram: {   // 分页参数
              page: 1,
              rows: 20
            },
            loadFinish: false,  // 分页数据是否已经请求完
            columnMap: [],          // 列数据的key值和展示值的映射数组
            kSeqMap: [],            // k序列map
            vSeqMap: [],            // v序列map
            customLevelMap: [],     // 客户等级的筛选
            vquery: [],         // v序列筛选字段的数组
            kquery: [],         // k序列筛选字段的数组
            rowMap: [],             // 行数据的key值和展示值的映射数组
            columnNameList: [],     // 列名数组
            selectDimensionLevel: [  // 动销总览-时间范围筛选
                {name: '全部', value: '总数', selectedFlag: false},
                {name: '本财年', value: '本财年', selectedFlag: true},
                {name: '本季', value: '本财季', selectedFlag: false},
                {name: '本月', value: '本月', selectedFlag: false}
            ],
            selectDimension: '本财年',
            columnLength: [],       // 所有列（动态列和静态列）的个数
            dynamicColumnList: [], // k序列和v序列的动态列信息数据
            staticColumnList: [     // 静态列信息数组
                {name: '礼赠', val: 'presentTimes'},
                {name: '拜访', val: 'visitTimes'},
                {name: '合计', val: 'touchTimes'}
            ], // 静态列数据
            cusLevels: ['3A', '4A', '5A', '超级终端'],   // 查询客户投入产出比详情的等级
            isGuoJiao: false,           // 是否是国窖公司
            dynamicsK: [], // 动态K序列
            dynamicsV: [], // 动态V序列
        }
    },
    computed: {
        titleObj() {
            switch (this.sourceFrom) {
                case 'activity':
                    return {
                        typeTitle: '活动名称',
                        timeTitle: '活动时间',
                        actType: 'act'
                    };
                case 'present':
                    return {
                        typeTitle: '礼赠类型',
                        timeTitle: '礼赠时间',
                        actType: 'present',
                    };
                case 'visit':
                    return {
                        typeTitle: '拜访方式',
                        timeTitle: '拜访时间',
                        actType: 'visit',
                    };
            }
        },
    },
    created () {
        this.params['page'] = 1;
        this.params['rows'] = 20;
        this.pagingPram.page = 1;
        this.orderDetailData = [];
        this.vquery = [];        // v序列筛选字段的数组
        this.kquery = [];        // k序列筛选字段的数组
        this.switchSetting();
    },
    methods: {
        /**
         * @desc 点击拓展触发
         * <AUTHOR>
         * @date 2022/6/7 17:21
         **/
        async expansionLevel () {
            this.cusLevels =  [];
            this.pagingPram.page = 1;
            this.orderDetailData = [];
            await this.queryAcctFeeDetailsPage();
        },
        /**
         * @desc 查询是否是国窖
         * <AUTHOR>
         * @date 2022/6/7 17:21
         **/
        async queryIsGuoJiao() {
            const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', {key: 'GUOJIAO_ACCT_MEMBER_LEVEL'});
            if (data.success) {
                const companyIds = data.value.split(',');
                this.isGuoJiao = companyIds.indexOf(this.userInfo.coreOrganizationTile['l3Id']) !== -1
            }
        },
        selectChange (item, type) {
            // console.log(item, '测试')
            if (type === 'KSeq') {
                this.kquery = [item.name]
            } else if (type === 'VSeq') {
                this.vquery = [item.name]
            }
            this.pagingPram.page = 1
            this.orderDetailData = []
            this.queryActTouchDetail()
        },
        filterData (rawArr, field, filterArr) {
            const res = rawArr.filter((item) => {
                return filterArr.includes(item[field])
            })
            return res
        },
        /**
         * @desc 加载更多
         * <AUTHOR>
         * @date 2023/7/8
         **/
        async loadMore (type) {
            if (this.loadFinish) return
            this.pagingPram.page++;
            await this.switchSetting()
        },
        /**
         * 跳转消费者详情
         * @Author:付常涛
         * @Date: 2023/11/14 20:31:30
         * @param item 行数据
         */
        gotoConsumerDetail (item) {
            // let map = new Map([
            //     ['activity', {name: "活动记录", seq: "2", val: "activity"}],
            //     ['present', {name: "礼赠记录", seq: "3", val: "present"}],
            //     ['visit', {name: "拜访记录", seq: "4", val: "visit"}]
            // ])
            item.id = item.consumerId
            // const switchObj = map.get(this.sourceFrom)
            const switchObj = {name: "触达记录", seq: "2", val: "touchRecord"};
            this.$nav.push('/pages/lj-consumers/account/account-item-page', {
                pageFrom: 'ConsumerOverview',
                switchObj: switchObj,
                data: item
            })
        },
        /**
         * @desc 初始化页面设定
         * <AUTHOR>
         * @date 2023/7/4 23:45
         **/
        async switchSetting () {
            switch (this.pageFrom) {
                case 'ConsumerOrder': // 动销订单
                    await this.queryOrderDetail();
                    break;
                case 'ConsumerContact': // 触达详情
                case 'ConsumerContactRegion': // 触达详情
                    await this.queryTouchDetail();
                    break;
                case 'ConsumerInputOutput': // 客户投入产出比详情
                    await this.queryAcctFeeDetailsPage();
                    break;
                case 'RegionConsumer': // 区域消费者详情
                    // 各区域消费者建设总览
                    await this.queryRegionConsumerInfo();
                    break;
                case 'ConsumerTouchDetail': // 消费触达明细
                    if (this.columnMap.length === 0) {
                        await this.queryIsGuoJiao();
                        await this.getColumnAndRowMap();
                    }
                    await this.queryDynamicColumn();
                    await this.queryActTouchDetail();
                    break;
            }
        },
        /**
         * 查询动态K/V序列
         * @Author:付常涛
         * @Date: 2023/11/10 16:58:16
         * @param overviewType 动态列查询类型
         * @param orgParam 组织id
         */
         async queryDynamicColumnKv(overviewType, orgParam) {
            const promiseList = ['K', 'V'].map((val) => {
                return new Promise(async (resolve, reject) => {
                    const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send",
                        {dataAccess: this.params.dataAccess, timeRange: this.params.timeRange, orgId: this.params.orgId, companyId: this.params.companyId, dmpSrUrl: '/link/board/dynamicKVType', seqType: val, overviewType, ...orgParam}, {
                            autoHandleError: false,
                            handleFailed: (response) => {
                                this.$utils.hideLoading();
                                this.$message.error({message: `查询片区详情的${val}序列动态列失败！` + response.result, customFlag: true});
                                reject(response.result);
                            }
                    });
                    resolve(data);
                })
            });
            const [kColumns, vColumns] = await Promise.all(promiseList);
            /* K/V序列排序 */
            const kColumnsSort = await this.sortTargetArr(kColumns.rows, 'ACCT_SUB_TYPE', 'fieldName', true);
            const vColumnsSort = await this.sortTargetArr(vColumns.rows, 'ACCT_MEMBER_LEVEL', 'fieldName', true);
            if (this.type === 'AreaBuildOverview') {
                // 各区域消费者建设总览-
                this.dynamicsK = [...kColumnsSort, {field: 'consumerQty', fieldName: '合计'}];
                this.dynamicsV = [...vColumnsSort, {field: 'consumerQty', fieldName: '合计'}];
            } else if (this.type === 'AreaOrderOverview') {
                // 各区域消费者动销总览
                if(this.params.saleType === 'amt'){
                    this.dynamicsK = [...kColumnsSort, {field: 'ordAmt', fieldName: '合计'}];
                    this.dynamicsV = [...vColumnsSort, {field: 'ordAmt', fieldName: '合计'}];
                } else {
                    this.dynamicsK = [...kColumnsSort, {field: 'ordBotQty', fieldName: '合计'}];
                    this.dynamicsV = [...vColumnsSort, {field: 'ordBotQty', fieldName: '合计'}];
                }
            }
        },
        /**
         * 区域消费者详情数据
         * @Author:付常涛
         * @Date: 2023/11/20 21:31:07
         */
        async queryRegionConsumerInfo () {
            this.params['page'] = this.pagingPram.page;
            this.params['companyId'] = this.pageParam.companyId;
            if (this.dynamicsK.length === 0 || this.dynamicsV.length === 0) {
                let overviewType = '';
                if (['AreaBuildOverview', 'TeamBuildOverview'].includes(this.type)) {
                    overviewType = '消费者建设总览';
                } else if (['TeamOrderOverview', 'AreaOrderOverview'].includes(this.type)) {
                    overviewType = '消费者动销总览';
                }
                const orgParam = {};
                if (['AreaBuildOverview', 'AreaOrderOverview'].includes(this.type)) {
                    orgParam.orgId = this.orgId;
                }
                await this.queryDynamicColumnKv(overviewType, orgParam);
            }
            const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send",
                {...this.params, dmpSrUrl: this.url}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.loadFinish = this.pagingPram.rows * this.pagingPram.page >= data.total
                        this.$showError(`查询片区总览数据失败！` + response.result);
                    }
                });
            if (data.success) {
                this.$utils.hideLoading();
                this.loadFinish = this.pagingPram.rows * this.pagingPram.page >= data.total
                this.orderDetailData = this.orderDetailData.concat(data.rows);
            }
        },
        /**
         * 查询客户投入产出比详情
         * @Author:付常涛
         * @Date: 2023/11/14 21:03:17
         */
        async queryAcctFeeDetailsPage () {
            const params = Object.assign({
                dataAccess: this.dataRange, // 数据权限,
                timeRange: this.timeRange, // 数据时间范围
                companyId: this.companyId, // 公司id
                acctTypeName: this.item['acctTypeName'], // 客户大类
                feeType: this.item['feeType'], // 客户大类
            }, this.pagingPram)
            if (this.cusLevels.length > 0) params.acctLevelNames = this.cusLevels
            const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send", {...params, dmpSrUrl: '/link/boardDetail/csmAcctFeeDetail'} , {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.loadFinish = this.pagingPram.rows * this.pagingPram.page >= data.total
                    this.$showError('查询消费者动销明细数据失败！' + response.result);
                }
            });
            if (data.success) {
                this.$utils.hideLoading();
                this.loadFinish = this.pagingPram.rows * this.pagingPram.page >= data.total
                this.orderDetailData = this.orderDetailData.concat(data.rows);
            }
        },
        /**
         * 查询动销明细
         * @Author:付常涛
         * @Date: 2023/11/14 20:14:43
         */
        async queryOrderDetail () {
               const param = Object.assign({
                   dataAccess: this.dataRange, // 数据权限,
                   timeRange: this.timeRange, // 数据时间范围
                   companyId: this.companyId, // 公司id
                   kTypeName: this.item.kTypeName, // k序列名称
                   vTypeName: this.item.vTypeName, // V序列名称
               }, this.pagingPram)
               // 各业务消费者动销总览(TeamOrderOverview)
               if (this.item.staffId) {
                  param.staffId = this.item.staffId;
                  param.postnId = this.item.postnId;
               }
               // 各区域消费者动销总览(AreaOrderOverview)
               if (this.item.orgId) {
                  param.orgId = this.item.orgId;
               }
               const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send", {...param, dmpSrUrl: '/link/boardDetail/csmSaleDetail'}, {
                   autoHandleError: false,
                   handleFailed: (response) => {
                       this.$utils.hideLoading();
                       this.loadFinish = this.pagingPram.rows * this.pagingPram.page >= data.total
                       this.$showError('查询消费者动销明细数据失败！' + response.result);
                   }
               });
                if (data.success) {
                    this.$utils.hideLoading();
                    this.loadFinish = this.pagingPram.rows * this.pagingPram.page >= data.total
                    /* 将消费者归类整合成一条数据 */
                    const result = [];
                    data.rows.forEach((item) => {
                        const original = result.find((i) => i.consumerId === item.consumerId);
                        if (original) {
                            original.prodVos.push({prodTypeName: item.prodTypeName, ordBotQty: item.ordBotQty, ordAmt: item.ordAmt});
                        } else {
                            result.push({
                                consumerId: item.consumerId,
                                consumerName: item.consumerName,
                                prodVos: [{prodTypeName: item.prodTypeName, ordBotQty: item.ordBotQty, ordAmt: item.ordAmt}]
                            })
                        }
                    });
                    this.orderDetailData = this.orderDetailData.concat(result);
                }
        },
        /**
         * 查询触达明细数据
         * @Author:付常涛
         * @Date: 2023/11/14 16:49:47
         */
        async queryTouchDetail () {
            const param = Object.assign({
                dataAccess: this.dataRange, // 数据权限
                timeRange: this.timeRange, // 数据时间范围
                companyId: this.companyId, // 公司id
                kTypeName: this.item.kTypeName, // k序列名称
                vTypeName: this.item.vTypeName, // V序列名称
                touchType: this.titleObj.actType, // 触达类型
                actMClassName: this.item.actMClassName, // 活动类型
            }, this.pagingPram)
            // 触达类型为拜访、礼赠时,请求传参中不传活动类型
            if (['visit', 'present'].includes(param.touchType)) delete param.actMClassName
            if (this.pageFrom === 'ConsumerContactRegion' && this.dataRange === 'Team') {
                param.staffId = this.item.staffId;
                param.postnId = this.item.postnId;
            }
            if (this.pageFrom === 'ConsumerContact' && this.dataRange === 'Area') {
                // 各区域消费者触达总览->点击区域-->点击K/V动态行+活动邀约动态列
                param.staffId = this.item.staffId;
                param.postnId = this.item.postnId;
                param.orgId = this.item.orgId;
            }
            const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send", {...param, dmpSrUrl: '/link/boardDetail/csmTouchDetail'}, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError('查询消费者动销明细数据失败！' + response.result);
                }
            });
            if (data.success) {
                this.$utils.hideLoading();
                /* 将消费者归类整合成一条数据 */
                const result = [];
                data.rows.forEach((item) => {
                    const original = result.find((i) => i.consumerId === item.consumerId);
                    if (original) {
                        original.itemList.push({actName: item.actName, visitWayName: item.visitWayName, presentType: item.presentType, auditTime: item.auditTime});
                    } else {
                        result.push({
                            consumerId: item.consumerId,
                            consumerName: item.consumerName,
                            itemList: [{actName: item.actName, visitWayName: item.visitWayName, presentType: item.presentType, auditTime: item.auditTime}]
                        })
                    }
                });
                this.orderDetailData = this.orderDetailData.concat(result);
                this.loadFinish = this.pagingPram.rows * this.pagingPram.page >= data.total
            }

        },
        /**
         * 排序目标数组(非异步或异步)
         * @Author:付常涛
         * @Date: 2023/11/16 20:26:16
         * @param arr  目标数组
         * @param lovType  值列表
         * @param equalField  判断相等的字段
         * @param isAsync  是否异步
         */
         async sortTargetArr(arr, lovType, equalField, isAsync) {
            let res = null;
            if (isAsync) {
                res = await this.$lov.getLovByType(lovType);
            } else {
                res = lovType;
            }
            if (res && res.length > 0) {
                arr.forEach((item) => {
                    const lov = res.find((i) => i.name === item[equalField]);
                    if (lov) {
                        item.sort = Number(lov.seq);
                    } else {
                        item.sort = 0;
                    }
                });
                arr.sort((item1, item2) => item2.sort - item1.sort);
            }
            return this.$utils.deepcopy(arr);
        },
        /**
         * 获取活动邀约的动态列
         * @Author:付常涛
         * @Date: 2023/11/11 12:00:03
         */
         async queryDynamicColumn() {
            const {staffId, postnId} = this.item;
            const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send",
                {dataAccess: this.dataRange, timeRange: this.selectDimension, companyId: this.companyId ,dmpSrUrl: '/link/board/dynamicActClass', staffId, postnId}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$message.error({message: `查询活动邀约的动态列失败！` + response.result, customFlag: true});
                        reject(response.result);
                    }
            });
            /* 活动邀约动态列 */
            let noSortColumn = data.rows.map((i) => ({name: i.fieldName, val: i.field}));
            /* 活动邀约动态列--排序 */
            const lovList = await this.$lov.getLovByType('ACT_CATEGORY');
            this.dynamicColumnList = await this.sortTargetArr(noSortColumn, lovList, 'name', false);
            this.columnLength = this.dynamicColumnList.length + this.staticColumnList.length;
        },
        /**
         * 查询触达活动明细数据
         * @Author:付常涛
         * @Date: 2023/11/20 15:47:11
         */
        async queryActTouchDetail () {
            const {staffId, postnId} = this.item
            let param = Object.assign({
                dataAccess: this.dataRange, // 数据权限,
                companyId: this.companyId, // 公司id
                timeRange: this.selectDimension, // 数据时间范围
                postnId, // 职位id
                staffId // 业务代表id
            }, this.pagingPram)
            if (this.kquery.length > 0) {
                param.kTypeName = this.kquery[0];
            }
            if (this.vquery.length > 0) {
                param.vTypeName = this.vquery[0];
            }
            const data = await this.$http.post(this.$env.appURL + "/action/link/sendDmpSr/send", {...param, dmpSrUrl: '/link/board/csmStaffTouchDetail'}, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError('查询消费者触达活动明细数据失败！' + response.result);
                }
            });
            if (data.success) {
                this.$utils.hideLoading();
                this.orderDetailData = this.orderDetailData.concat(data.rows);
                this.loadFinish = this.pagingPram.rows * this.pagingPram.page >= data.total;
            }
        },
        /**
         * 选择时间查询维度
         * @Author:付常涛
         * @Date: 2023/11/20 17:03:02
         */
        async chooseData($event, data) {
            this.selectDimension = data.value
            this.selectDimensionLevel.forEach((item) => {
                if (item.value === data.value) {
                    data.selectedFlag = true;
                } else {
                    item.selectedFlag = false;
                }
            });
            this.pagingPram.page = 1
            this.orderDetailData = []
            await this.queryDynamicColumn();
            await this.queryActTouchDetail()
        },
        /**
         * @desc 获取动态列名数组
         * <AUTHOR>
         * @date 2021/6/3 16:31
         **/
        getColumnNameList (sourceObj) {
            // list行名数组 subList列名数组
            const columnNameList = Object.keys(sourceObj)
            const set = new Set([...this.columnNameList, ...columnNameList])
            this.columnNameList = [...set]
        },
        /**
         * @desc 获取行映射数组和列映射数组
         * <AUTHOR>
         * @date 2023/7/10
         **/
        async getColumnAndRowMap () {
            const columnLovTypeArr = ['ACT_CATEGORY']
            const columnMap = [
                {name: '品', val: 'Pin'},
                {name: '赠', val: 'Zeng'},
                {name: '游', val: 'You'}
            ]
            const rowMap = [
                {name: '合计', val: 'heJi'}
            ]
            this.columnMap = await this.getMap(columnMap, columnLovTypeArr)
            let vSeqMap =  await this.getMap([], ['ACCT_MEMBER_LEVEL'], 'type')
            let kSeqMap =  await this.getMap([], ['ACCT_SUB_TYPE'], 'type')
            this.rowMap = rowMap.concat(kSeqMap).concat(vSeqMap)
            // console.log('this.columnMap', this.columnMap);
            // console.log('kSeqMap', kSeqMap);
            // console.log('vSeqMap', vSeqMap);
            // console.log('this.rowMap', this.rowMap);
            await this.getKSeqMap()
            await this.getVSeqMap(vSeqMap)
        },
        /**
         * @desc 获取k序列的可筛选数据
         * <AUTHOR>
         * @date 2022/6/1 15:27
         **/
        async getKSeqMap() {
            const data = await this.$http.post(this.$env.appURL + '/action/link/mapConType/queryByExamplePage', {
                oauth: 'ALL',
                pageFlag: true,
                rows: 500,
                page: 1,
                distinctFields: 'type',
                filtersRaw: [
                    {
                        id: 'companyId',
                        property: 'companyId',
                        value: this.userInfo.coreOrganizationTile['l3Id'],
                        operator: '='
                    },
                    {id: 'status', property: 'status', value: 'Active', operator: '='}]
            });
            if (data.success) {
                for (const item of data.rows) {
                    let name = await this.$lov.getNameByTypeAndVal('ACCT_SUB_TYPE', item.type)
                    this.$set(item, 'name', name);
                    this.$set(item, 'val', item.type);
                }
                data.rows.forEach((item, index) => {
                    this.$set(item, 'seq', index + 1)
                });
                this.kSeqMap = data.rows;
            }
            this.kSeqMap = this.kSeqMap.map((item) => ({val:item.type, name:item.name}))
        },
        /**
         * @desc 获取v序列的可筛选数据
         * <AUTHOR>
         * @date 2022/6/1 15:27
         **/
        async getVSeqMap(lovData) {
            if (!Array.isArray(lovData)) lovData = await this.$lov.getLovByType('ACCT_MEMBER_LEVEL');
            if (this.isGuoJiao) {
                this.vSeqMap = await this.$lov.getLovByParentTypeAndValue({
                    type: 'ACCT_MEMBER_LEVEL',
                    parentType: 'ACCT_MEMBER_LEVEL_COMPANY',
                    parentVal: this.userInfo.coreOrganizationTile['l3Id']
                });
            } else {
                this.vSeqMap = lovData.filter(item => !item.parentId);
            }
            this.vSeqMap = this.vSeqMap.map((item) => ({val:item.val, name:item.name}))
        },
        /**
         * @desc 查询映射表，返回对应得映射数组
         * @date 2023/7/3 10:38
         * @param map 映射数组
         * @param valList 待映射得数组
         * @return Array 生成得二维数组
         **/
        queryMap (map, valList) {
            const nameList = []
            valList.forEach((valItem) => {
                for(let mapItem of map) {
                    if (mapItem.val === valItem) {
                        nameList.push(mapItem)
                        break;
                    }
                }
            })
            return nameList
        },
        /**
         * @desc 获取后端返回的动态行、列的字段和展示值的对应关系
         * <AUTHOR>
         * @param initialArr 初始化的数组
         * @param lovTypeArr 对应字段的多个值列表
         * @date 2023/7/3 10:38
         **/
        async getMap (initialArr, lovTypeArr) {
            for (const lovType of lovTypeArr) {
                const res = await this.$lov.getLovByType(lovType);
                if (res && res.length > 0) {
                    res.forEach(({name, val, parentId}) => {
                        initialArr.push({name, val, parentId})
                    })
                }
            }
            return initialArr
        },
        /**
         * 跳转消费者详情
         * @Author:付常涛
         * @Date: 2023/11/14 20:40:57
         * @param item 行数据
         */
        gotoAccountItem(item) {
            const switchObj = {name: "转化记录", seq: "3", val: "conversionRecord"};
            this.$nav.push('/pages/lj-consumers/account/account-item-page', {
                data: {
                    id: item.consumerId
                },
                pageFrom: 'ConsumerOverviewItem',
                switchObj: switchObj
            })
        },
        /**
         * 片区详情点击K/V列进入详情
         * @Author:付常涛
         * @Date: 2023/11/20 22:28:47
         * @param row 当前行数据
         * @param column 当前列数据
         * @param value 当前数据
         * @param type 当前列K/V类型
         */
        gotoItem (row, column, value, type) {
            // console.log('下钻', row, row.staffName, row.staffId, column.fieldName, value, type);
            if (value === '0' || value === 0) return;
            // 各区域消费者动销总览、各业务消费者动销总览
            if (this.type === 'TeamOrderOverview' || this.type === 'AreaOrderOverview') {
                let kTypeName = '';
                let vTypeName = '';
                if (type === 'K' && column.fieldName !== '合计') {
                    kTypeName = column.fieldName;
                } else if (type === 'V' && column.fieldName !== '合计') {
                    vTypeName = column.fieldName;
                }
                const item = {
                    staffId: row.staffId, // 业务代表ID
                    postnId: row.postnId, // 职位ID
                    kTypeName, // k序列名称
                    vTypeName, // v序列名称
                };
                if (this.type === 'AreaOrderOverview') {
                    // 各区域消费者动销总览
                    item.orgId = this.params.orgId;
                }
                // console.log('====', this.params.orgId);
                this.$nav.redirect('/pages/echart/lzlj/consumer-kanban-new/consumer-overview-item-new-page', {
                    pageFrom: 'ConsumerOrder',
                    companyId: this.params.companyId, // 公司ID
                    item,
                    timeRange: this.params.timeRange, // 数据时间范围
                    dataRange: this.params.dataAccess, // 数据权限
                })
            } else {
                /* 各区域消费者建设总览(AreaBuildOverview) */
                this.gotoAccountList(row, column, value, type);
            }
        },
        /**
         * @desc 跳转消费者列表
         * <AUTHOR>
         * @date 2023/6/30 09:28
         **/
        /**
         * 跳转消费者列表
         * @Author:付常涛
         * @Date: 2023/11/20 22:31:38
         * @param row 当前行数据
         * @param column 当前列数据
         * @param value 当前数据
         * @param type 当前列K/V类型
         */
        gotoAccountList (row, column, value, type) {
            if (value === '0' || value === 0) return;
            let kTypeName = '';
            let vTypeName = '';
            if (type === 'K' && column.fieldName !== '合计') {
                kTypeName = column.fieldName;
            } else if (type === 'V' && column.fieldName !== '合计') {
                vTypeName = column.fieldName;
            }
            // 各区域消费者建设总览的片区详情,点击K/V动态列下钻
            this.$nav.push('/pages/lj-consumers/account/account-list-new-page', {
                pageFrom: "ConsumerOverview",
                kTypeName,
                vTypeName,
                filterInfo: {
                    orgId: this.params.orgId, // 组织ID
                    staffId: row.staffId, // 业务代表ID
                    postnId: row.postnId, // 业务代表ID
                    timeRange: this.params.timeRange,
                    dataRange: this.params.dataAccess,
                },
            })
        }
    }
}
</script>
<style lang="scss">
@import "./css/new-board";
.consumer-overview-item-page {
    background: #f2f2f2;
    /*deep*/.report-line-title{
    margin-bottom: 24px;
}
    font-size: 28px;
    font-weight: 400;
    .consumer-order-overview{
        background: white;
        border-radius: 16px;
        margin: 24px;
        padding: 28px 24px 48px;
        .consumer-overview-item-chart{
            text-align: center;
            .consumer-order-title{
                display: flex;
                width: 100%;
                background: #F8FAFF;
                height: 80px;
                line-height: 80px;
                .order-line-title{
                    width: 160px;
                    flex: none;
                }
                .first-title-line{
                    width: 144px !important;
                }
                .second-title-line{
                    width: 192px !important;
                }
            }
            .consumer-order-data{
                .data-line{
                    margin: 24px 0;
                    display: flex;
                    border-bottom: 2px solid #F0F2F8;
                    .data-line-item{
                        flex: none;
                        width: 160px;
                    }
                    .other-prod-line{
                        display: flex;
                        margin: 24px 0;
                    }
                    .first-line{
                        width: 144px !important;
                        color: #3F66EF;
                        text-decoration: underline;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }
                    .second-line{
                        width: 192px !important;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        white-space: nowrap;
                    }
                }
            }
        }
    }
    .consumer-contact-overview{
        background: white;
        border-radius: 16px;
        margin: 24px;
        padding: 28px 24px 48px;
        .consumer-contact-item-chart {
            text-align: center;
            display: flex;
            .contact-chart-left {
                width: 33%;
                flex: none;
                overflow-x: auto;
                display: flex;
                flex-direction: column;
                .contact-title-item {
                    background: #F8FAFF;
                    height: 80px;
                    line-height: 80px;
                    width: 100%;
                    display: flex;
                    justify-content: center;
                }
                .contact-data-item {
                    .item-info{
                        color: #3F66EF;
                        text-decoration: underline;
                    }
                    width: 100%;
                    border-bottom: 2px solid #F0F2F8;
                    box-sizing: border-box;
                    padding: 25px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
            .contact-chart-right {
                flex: 1;
                overflow-x: auto;
                display: flex;
                flex-direction: column;
                .contact-title-line {
                    display: flex;
                    align-items: center;
                    width: 100%;
                    background: #F8FAFF;
                    height: 80px;
                    line-height: 80px;
                    .contact-title-item {
                        background: #F8FAFF;
                        flex:none;
                        width: 500px;
                    }
                }
                .contact-data-line {
                    flex: none;
                    display: flex;
                    flex-direction: column;
                    .contact-data-sub-line {
                        flex: none;
                        display: flex;
                        width: 1000px;
                        margin: 24px 0;
                        .contact-data-item {
                            flex:none;
                            width: 500px;
                            height: 50px;
                            line-height: 50px;
                        }
                    }
                }
            }
        }
    }
    .consumer-input-output-ratio {
        background: white;
        border-radius: 16px;
        font-size: 28px;
        font-weight: 400;
        padding: 28px 24px 48px;
        // 表格每行的高度
        $row-height: 100px;
        // 最后一列的宽度
        $last-line-width: 200px;
        .consumer-overview-chart{
            text-align: center;
            display: flex;
            .consumer-overview-left{
                border-left: 2px solid #F0F2F8;
                .kv-title{
                    width: 168px;
                    color: #999999;
                    height: 112px;
                    background:  #F8FAFF;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .other-line{
                    width: 168px;
                    height: $row-height;
                    padding: 24px 0;
                    display: flex;
                    align-items: center;
                }
            }
            .consumer-overview-right{
                overflow-x: auto;
                .title-item{
                    width: 168px;
                    flex: none;
                    background:  #F8FAFF;
                    height: 112px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    &:last-of-type {
                        width: $last-line-width;
                    }
                }
                .title-line-item{
                    display: flex;
                    color: #999999;
                    height: 112px;
                    background:  #F8FAFF;
                }
                .data-line{
                    display: flex;
                    padding: 24px 0;
                }
                .data-item{
                    width: 168px;
                    height: $row-height;
                    line-height: $row-height;
                    flex: none;
                    overflow-y: hidden;
                    overflow-x: auto;
                    &:last-of-type {
                        width: $last-line-width;
                    }
                }
            }
        }
    }
    .region-consumer-overview {
        padding: 28px 24px 48px;
        background: white;
        border-radius: 16px;
        font-size: 28px;
        font-weight: 400;
        .consumer-overview-chart{
            text-align: center;
            display: flex;
            border-bottom: 2px solid #F0F2F8;
            .title-line-item{
                color: #999999;
                height: 176px;
                background:  #F8FAFF;
            }
            .overview-line {
                border-right: 2px solid #F0F2F8;
            }
            .consumer-overview-left{
                display: flex;
                .kv-title{
                    width: 166px !important;
                    line-height: 176px;
                }
                .kv-line{
                    margin: 24px 0;
                }
                .other-line{
                    width: 148px;
                    height: 40px;
                    padding: 24px 10px;
                    flex: none;
                    overflow: auto;
                    white-space: nowrap;
                }
            }
            .consumer-overview-right{
                overflow-x: auto;
                flex: 1;
                display: flex;
                flex-direction: column;
                .activity-item{
                    background:  #F8FAFF;
                    .activity-title {
                        height: 96px;
                        display: flex;
                        padding-left: 20px;
                        align-items: center;
                        border-right: 2px solid #F0F2F8;
                    }
                    .activity-line-title{
                        display:flex;
                        height: 80px;
                        width: auto;
                        line-height: 80px;
                        border-top: 2px solid #F0F2F8;
                        border-right: 2px solid #F0F2F8;
                        .activity-line-item-title{
                            width: 160px;
                        }
                    }
                }
                .title-item{
                    width: 160px;
                    flex: none;
                    background:  #F8FAFF;
                }
                .item-total{
                    width: 104px !important;
                }
                .title-line-item{
                    display: flex;
                }
                .title-line-right{
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 176px;
                    background:  #F8FAFF;
                    border-left: 2px solid #F0F2F8;
                }
                .all-data{
                    display: flex;
                }
                .kv-data-line{
                    width: auto;
                }
                .data-line{
                    display: flex;
                }
                .data-item{
                    width: 160px;
                    flex: none;
                    height: 40px;
                    padding: 24px 4px;
                    overflow-y: hidden;
                    overflow-x: auto;
                }
            }
        }
    }
    .consumer-touch-detail-overview {
        background: white;
        border-radius: 16px;
        font-size: 28px;
        font-weight: 400;
        // 表格每行的高度
        $row-height: 100px;
        // 表格每列的宽度
        $line-width: 150px;
        .consumer-overview-chart{
            text-align: center;
            display: flex;
            .title-line-item{
                color: #999999;
                height: 176px;
                background:  #F8FAFF;
            }
            .title-line-right{
                display: flex;
                justify-content: center;
                align-items: center;
                height: 176px;
                background:  #F8FAFF;
                border-left: 2px solid #F0F2F8;
            }
            .consumer-overview-left{
                display: flex;
                .kv-title{
                    width: 222px !important;
                    border-right: 2px solid #F0F2F8;
                    line-height: 176px;
                }
                .kv-line{
                    margin: 24px 0;
                }
                .other-line{
                    width: 168px;
                    height: $row-height;
                    margin: 24px 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    overflow-x: auto;
                    overflow-y: hidden;
                }
            }
            .consumer-overview-right{
                overflow-x: auto;
                flex: 1;
                display: flex;
                flex-direction: column;
                .activity-item{
                    background:  #F8FAFF;
                    .activity-title {
                        height: 96px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }
                    .activity-line-title{
                        display:flex;
                        height: 80px;
                        line-height: 80px;
                        border-top: 2px solid #F0F2F8;
                        .activity-line-item-title{
                            // width: 168px;
                            width: 160px;
                        }
                    }
                }
                .title-item{
                    width: 168px;
                    flex: none;
                    background:  #F8FAFF;
                }
                .item-total{
                    width: 104px !important;
                }
                .title-line-item{
                    display: flex;
                }
                .title-line-right{
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 176px;
                    background:  #F8FAFF;
                    border-left: 2px solid #F0F2F8;
                }
                .kv-data-line{
                    border-bottom: 2px solid #F0F2F8;
                    width: 776px;
                }
                .data-line{
                    display: flex;
                    margin: 24px 0;
                }
                .data-item{
                    width: 168px;
                    flex: none;
                    height: $row-height;
                    line-height: $row-height;
                    overflow-y: hidden;
                    overflow-x: auto;
                }
            }
        }
    }
    .load-more{
        width: 100%;
        text-align: center;
        color: #6D96FA;
        margin-top: 32px;
        font-size: 24px;
        padding-bottom: 32px;
    }
    .item-info{
        color: #3F66EF;
        text-decoration: underline;
    }
}
</style>
