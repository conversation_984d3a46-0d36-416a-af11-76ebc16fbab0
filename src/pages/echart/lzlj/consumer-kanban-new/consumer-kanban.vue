<!--
 * 消费者看板（新）
 * @Author:付常涛
 * @Date: 2023/10/26 20:48:17
-->
<template>
    <link-page class="consumer-kanban">
<!--        <position-bottom :user-info="userInfo" :show.sync="dialogFlag" @choose="changeOrg" from='consumer'></position-bottom>-->
        <consumer-company-select :companyId="companyId" :user-info="userInfo" :show.sync="dialogFlag" @choose="changeOrg" from='consumer'></consumer-company-select>
        <!--组织/安全性筛选-->
        <view class="filter-type">
            <!-- 仅集团/销售公司可见 -->
            <view class="select-dimension">
                <select-button  v-if="userInfo.orgType === 'Group' || userInfo.orgType === 'Company'" :label="orgName" :selected-flag="true" @tap="dialogFlag=true" downIcon :showLength='12'></select-button>
            </view>
            <view class="filter-type-item" @tap="chooseOauthData">{{pageOauthName}}<link-icon icon="mp-desc"/></view>
        </view>
        <!--消费者建设总览-->
        <consumer-overview-new ref="consumerOverviewNew" :companyId="companyId" :dataRange="dataRange" v-if="dataRange" :isCheckDetail="isCheckDetail"/>
        <region-consumer-overview-new ref="regionConsumerOverviewNewConstr" :companyId="companyId" :dataRange="dataRange" v-if="dataRange === 'Team'" lineTitle="各业务消费者建设总览" type="TeamBuildOverview" :isCheckDetail="isCheckDetail"/>
        <region-consumer-overview-new ref="regionConsumerOverviewNewConstr" :companyId="companyId" :dataRange="dataRange" v-if="dataRange === 'Area'" lineTitle="各区域消费者建设总览" type="AreaBuildOverview" :isCheckDetail="isCheckDetail"/>
        <!--消费者触达总览-->
        <consumer-contact-overview-new ref="consumerContactOverviewNew" :orgId="orgId" :companyId="companyId" :dataRange="dataRange" v-if="dataRange" :isCheckDetail="isCheckDetail"/>
        <region-consumer-touch-overview-new ref="regionConsumerTouchOverviewNew" :companyId="companyId" :dataRange="dataRange" v-if="dataRange === 'Team'" lineTitle="各业务消费者触达总览" type="TeamTouchOverview" :isCheckDetail="isCheckDetail"/>
        <region-consumer-touch-overview-new ref="regionConsumerTouchOverviewNew" :companyId="companyId" :dataRange="dataRange" v-if="dataRange === 'Area'" lineTitle="各区域消费者触达总览" type="AreaTouchOverview" :isCheckDetail="isCheckDetail"/>
        <!--消费者触达趋势-->
        <consumer-contact-trend-new ref="consumerContactTrendNew" :companyId="companyId" :dataRange="dataRange" v-if="dataRange"/>
        <!--消费者动销总览-->
        <consumer-order-overview-new ref="consumerOrderOverviewNew" :companyId="companyId" :dataRange="dataRange" v-if="dataRange" :isCheckDetail="isCheckDetail"/>
        <region-consumer-overview-new ref="regionConsumerOverviewNewSales" :companyId="companyId" :dataRange="dataRange" v-if="dataRange === 'Team'" lineTitle="各业务消费者动销总览" type="TeamOrderOverview" :isCheckDetail="isCheckDetail"/>
        <region-consumer-overview-new ref="regionConsumerOverviewNewSales" :companyId="companyId" :dataRange="dataRange" v-if="dataRange === 'Area'" lineTitle="各区域消费者动销总览" type="AreaOrderOverview" :isCheckDetail="isCheckDetail"/>
        <!--客户投入产出比-->
        <consumer-input-output-new ref="consumerInputOutputNew" :companyId="companyId" :dataRange="dataRange" v-if="dataRange" :isCheckDetail="isCheckDetail"/>
        <water-mark></water-mark>
    </link-page>
</template>

<script>
import ConsumerOverviewNew from "./components/consumer-overview-new";
import RegionConsumerOverviewNew from "./components/region-consumer-overview-new";
import ConsumerContactOverviewNew from "./components/consumer-contact-overview-new";
import RegionConsumerTouchOverviewNew from "./components/region-consumer-touch-overview-new";
import ConsumerContactTrendNew from "./components/consumer-contact-trend-new";
import ConsumerOrderOverviewNew from "./components/consumer-order-overview-new";
import ConsumerInputOutputNew from "./components/consumer-input-output-new";
import SelectButton from "../components/select-button.vue";
import ConsumerCompanySelect from "../components/consumer-company-select.vue";
import waterMark from "../../../lzlj/components/water-mark";

export default {
    name: 'consumer-kanban',
    components: {
        ConsumerCompanySelect,
        SelectButton,
        ConsumerOverviewNew,
        RegionConsumerOverviewNew,
        ConsumerContactOverviewNew,
        RegionConsumerTouchOverviewNew,
        ConsumerContactTrendNew,
        ConsumerOrderOverviewNew,
        ConsumerInputOutputNew,
        waterMark
    },
    props: {
        secMenus: {
            type: Array,
            default: []
        }
    },
    data () {
        const userInfo = this.$taro.getStorageSync('token').result;
        let orgCompanyCode = userInfo.coreOrganizationTile.brandCompanyCode
        let {orgId, orgName:orgName,orgCode, positionType, orgType} = userInfo
        // 为销售公司
        if(orgType === "Company" || orgType === "Group"){
            orgId = '58929586649432064';
            orgName = '泸州老窖国窖酒类销售股份有限公司';
            orgCompanyCode = '5600'
            orgCode = 'GS100283'
        }
        return {
            userInfo,
            isCheckDetail: true, // 是否查看详情数据---20231228取消限制职位查看详情
            dataRange: '', // 数据范围
            pageOauth: '',
            pageOauthName: '',
            orgName,
            orgId,
            companyId: orgId,
            dialogFlag: false
        }
    },
    methods: {
        /**
         * 系统参数控制是否下钻查询明细数据
         * @Author:付常涛
         * @Date: 2023/10/26 21:41:01
         */
        async queryCfg () {
            const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', { key: 'CONSUMER_KB_POSTN_TYPE'});
            if (data.success && data.value) {
                const postnTypes = data.value.split(',');
                this.isCheckDetail = postnTypes.indexOf(this.userInfo.positionType) !== -1;
            }
        },
        /**
         * 切换安全性
         * @Author:付常涛
         * @Date: 2023/10/26 21:41:12
         * @param oauth 选中安全性值
         */
        async pageOauthChange(oauth) {
            this.$utils.showLoading();
            if (oauth.securityMode === 'MY') {
                this.dataRange = 'Mine';
            } else if (oauth.securityMode === 'MY_POSTN') {
                this.dataRange = 'Team';
            } else if (oauth.securityMode === 'MY_ORG') {
                this.dataRange = 'Area';
            } else {
                this.dataRange = '';
            }
            this.pageOauth = oauth.securityMode;
            this.pageOauthName = oauth.name;
            await this.changeOrgByOauth({orgId: this.orgId, orgName: this.orgName, companyId: this.companyId});
            this.$utils.hideLoading();
        },
        /**
         * 选择页面安全性
         * @Author:付常涛
         * @Date: 2023/10/26 21:41:33
         */
        chooseOauthData() {
            this.$actionSheet(() => (
                <link-action-sheet title="请选择数据范围" onCancel={() => {}}>
                    {this.secMenus.map((item) => {return <link-action-sheet-item label={item.name} onTap={() => this.pageOauthChange(item)}/>})}
                </link-action-sheet>
            ));
        },
        /**
         * 选择组织
         * @Author:何春霞
         * @Date: 2024-06-12
         */
        async changeOrg(item) {
            if(Object.keys(item).length === 0) return;
            this.orgName = item.orgName;
            this.orgId = item.orgId;
            this.companyId = item.id;
            // console.log('item.id', item.id)
            this.$refs.consumerOverviewNew && await this.$refs.consumerOverviewNew.queryConsumerOverview(this.companyId);
            this.$refs.regionConsumerOverviewNewConstr && await this.$refs.regionConsumerOverviewNewConstr.queryOverviewData(this.companyId);
            this.$refs.regionConsumerOverviewNewSales && await this.$refs.regionConsumerOverviewNewSales.queryOverviewData(this.companyId);
            this.$refs.regionConsumerOverviewNewConstr && await this.$refs.regionConsumerOverviewNewConstr.changeAreaName(this.orgName, this.orgId);
            this.$refs.regionConsumerOverviewNewSales && await this.$refs.regionConsumerOverviewNewSales.changeAreaName(this.orgName, this.orgId);
            this.$refs.consumerContactOverviewNew && await this.$refs.consumerContactOverviewNew.queryTouchOverview(this.companyId, this.orgId);
            this.$refs.regionConsumerTouchOverviewNew && await this.$refs.regionConsumerTouchOverviewNew.queryTouchOverview(this.companyId);
            this.$refs.regionConsumerTouchOverviewNew && await this.$refs.regionConsumerTouchOverviewNew.changeAreaName(this.orgName, this.orgId);
            this.$refs.consumerContactTrendNew && await this.$refs.consumerContactTrendNew.queryConsumerTrend(this.companyId);
            this.$refs.consumerOrderOverviewNew && await this.$refs.consumerOrderOverviewNew.queryConsumerOrderOverview(this.companyId);
            this.$refs.consumerInputOutputNew && await this.$refs.consumerInputOutputNew.queryAllData(this.companyId);
        },
        async changeOrgByOauth(item) {
            if(Object.keys(item).length === 0) return;
            this.orgName = item.orgName;
            this.orgId = item.orgId;
            this.companyId = item.companyId;
            this.$refs.regionConsumerOverviewNewConstr && await this.$refs.regionConsumerOverviewNewConstr.changeAreaName(this.orgName, this.orgId);
            this.$refs.regionConsumerOverviewNewSales && await this.$refs.regionConsumerOverviewNewSales.changeAreaName(this.orgName, this.orgId);
            this.$refs.consumerContactOverviewNew && await this.$refs.consumerContactOverviewNew.queryTouchOverview(this.companyId, this.orgId);
            this.$refs.regionConsumerTouchOverviewNew && await this.$refs.regionConsumerTouchOverviewNew.changeAreaName(this.orgName, this.orgId);
        }
    },
    async created() {
        // 请求系统参数控制是否下钻查询明细数据
        // this.queryCfg(); // 20231228取消限制职位查看详情\
        const oauth = this.secMenus.find((item) => item.securityMode === 'MY_ORG') ? this.secMenus.find((item) => item.securityMode === 'MY_ORG') : this.secMenus.find((item) => item.securityMode === 'MY_POSTN') ? this.secMenus.find((item) => item.securityMode === 'MY_POSTN') : this.secMenus.find((item) => item.securityMode === 'MY') || {};
        this.pageOauth = oauth.securityMode;
        this.pageOauthName = oauth.name;
        if (oauth.securityMode === 'MY') {
            this.dataRange = 'Mine';
        } else if (oauth.securityMode === 'MY_POSTN') {
            this.dataRange = 'Team';
        } else if (oauth.securityMode === 'MY_ORG') {
            this.dataRange = 'Area';
        } else {
            this.dataRange = '';
        }
    }
};
</script>

<style lang="scss">
.consumer-kanban{
    .filter-type {
        display: flex;
        height: 40px;
        padding: 12px 10px;
        justify-content: space-between;
        .select-dimension {
            max-width: 70%;
            height: 40px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
        }
        .filter-type-item {
            width: 30%;
            height: 40px;
            font-size: 28px;
            color: #333333;
            line-height: 40px;
            font-weight: 400;
            text-align: center;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            .link-icon {
                width: 16px;
                height: 12px;
                color: #CCCCCC;
                margin-left: 8px;
            }
        }
    }
}
</style>
