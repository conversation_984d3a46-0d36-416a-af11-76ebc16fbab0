<template>
    <view class="terminal-customer-label-page">
        12
        <link-echart :option="customerLabelOption" ref="linkEchart"/>
    </view>
</template>

<script>
export default {
    name: "terminal-customer-label-page",
    data() {
        return {
            customerLabelOption: null
        }
    },
    mounted() {
        this.customerLabelOption = {
            xAxis: {
                type: 'category',
                data: ['本月进货', '本月库存', '本月动销']
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    data: [14, 5, 10],
                    type: 'bar',
                    showBackground: true
                }
            ]
        }
    }
}
</script>

<style scoped>

</style>
