<template>
    <link-page class="role-board-page">
        <view style="padding-top: 12px"></view>
        <!--   终端客户概览     -->
        <view class="boardBox" id="customerPreview" v-if="dataFlag(0)">
            <customer-preview ref="customer" :user-type="userType" :org-data="this.customerOrgData" ></customer-preview>
        </view>
        <!--   终端荐酒员概览     -->
        <view class="boardBox" id="drinkser" v-if="dataFlag(1)">
            <terminal-recommender-drinkser :org-data="this.customerOrgData"></terminal-recommender-drinkser>
        </view>
        <!--   所辖人员拜访执行情况     -->
        <view class="boardBox" id="personnel" v-if="dataFlag(2)">
            <personnel-visit-execution-status :user-type="userType" :first-loading="menu[2].loading" :org-data="this.customerOrgData"></personnel-visit-execution-status>
        </view>
        <!--   拜访覆盖客户板块     -->
        <view class="boardBox coverCustomers" id="coverCustomers" v-if="dataFlag(3)">
            <visit-cover-customers :user-type="userType" :first-loading="menu[3].loading" :org-data="this.customerOrgData"></visit-cover-customers>
        </view>
        <!--   配额执行情况     -->
        <view class="boardBox" id="enforcement" v-if="dataFlag(4)">
            <quota-enforcement ref="quota" :user-type="userType" :first-loading="menu[4].loading" :org-data="this.quotaOriData"> </quota-enforcement>
        </view>
        <!--   终端动销情况     -->
        <view class="boardBox" id="terminalSales" v-if="dataFlag(5)">
            <terminal-sales ref="sales" :user-type="userType" :org-data="this.salesOrgData" :first-loading="menu[5].loading"></terminal-sales>
        </view>
        <!--   终端热力图     -->
        <view class="boardBox" id="heatmap" v-if="false">
            <terminal-heatmap :first-loading="menu[6].loading"> </terminal-heatmap>
        </view>
        <!--   终端124目标达成    -->
        <view class="boardBox" id="attainment" v-if="dataFlag(7)">
            <terminal-goal-attainment :user-type="userType" :first-loading="menu[7].loading" :org-data="goalOriData"  ref="goal"> </terminal-goal-attainment>
        </view>
        <!--   终端124目标统计    -->
        <view class="boardBox" id="target" v-if="dataFlag(8)">
            <terminal-target-statistics :user-type="userType" :first-loading="menu[7].loading" :org-data="goalOriData"  ref="goal"> </terminal-target-statistics>
        </view>
    </link-page>
</template>

<script>
import CustomerPreview from "./components/customer-preview";
import LnkTaps from "../../../core/lnk-taps/lnk-taps";
import terminalRecommenderDrinkser from './components/terminal-recommender-drinkser.vue';
import visitCoverCustomers from './components/visit-cover-customers.vue';
import personnelVisitExecutionStatus from './components/personnel-visit-execution-status.vue';
import quotaEnforcement from "./components/quota-enforcement.vue";
import terminalHeatmap from "./components/terminal-heatmap.vue";
import terminalGoalAttainment from "./components/terminal-goal-attainment.vue";
import Taro from "@tarojs/taro";
import TerminalSales from "./components/terminal-sales";
import TerminalTargetStatistics from "./components/terminal-target-statistics";
import {taro} from "../../../../utils/taro";
export default {
    name: "role-board-page",
    components: {TerminalSales, LnkTaps, CustomerPreview,terminalRecommenderDrinkser,visitCoverCustomers,personnelVisitExecutionStatus,quotaEnforcement,terminalHeatmap,terminalGoalAttainment,TerminalTargetStatistics},
    data(){
        const userInfo = Taro.getStorageSync('token').result;
        return{
            userType: 'Manager', // 角色（业代、主管、经理内勤）
            once:true,
            menu:[],
            resHeight:[],
            userInfo,
            bserver:null,
            customerOrgData: {},
            salesOrgData: {},
            screenHeight:600,
            quotaOriData:{},
            goalOriData:{},
            roleBoardOption:[
                {name: '终端总览看板', seq: '1', val: 'terminal'},
                {name: '市场总览看板', seq: '2', val: 'activity'},
            ],
            roleBoardActive: {name: '终端总览看板', seq: '1', val: 'terminal'}
        }
    },
    async onLoad() {
        this.customerOrgData.text = this.userInfo.orgName;
        this.customerOrgData.orgId =  this.userInfo.orgId;
        this.salesOrgData.text = this.userInfo.orgName;
        this.salesOrgData.orgId =  this.userInfo.orgId;
        this.quotaOriData.text = this.userInfo.orgName;
        this.quotaOriData.orgId =  this.userInfo.orgId;
        this.goalOriData.text = this.userInfo.orgName;
        this.goalOriData.orgId =  this.userInfo.orgId;
        this.screenHeight = wx.getSystemInfoSync().screenHeight;
        await this.getPosition();
        this.initView()
    },
    onReady() {
        setTimeout(()=>{
            this.onElementLinstener();
            this.$utils.hideLoading();
        }, 2000);
    },
    computed: {
        dataFlag(){
            return (index) => {
                return this.menu.length > 0 && this.menu[index].flag
            }
        }
    },
    onUnload() {
        wx.createIntersectionObserver().disconnect()
    },
    methods:{
        /**
         @desc: 获取角色看板权限
         @author: wangbinxin
         @date 2022-08-10 16-43
         **/
        async getPosition(){
            this.$utils.showLoading();
            const queryData =  await this.$http.post('action/link/cfgProperty/queryByExamplePage', {
                filtersRaw: [{
                    id: 'key',
                    property: 'key',
                    operator: 'in',
                    value: '[Terminal_Role_Salesman,Terminal_Role_SalesSupervisor,Terminal_Role_Manager]'
                }, {
                    id: 'value',
                    property: 'value',
                    operator: 'like',
                    value: this.userInfo.positionType
                }]
            });
            if(queryData.success){
                if(queryData.rows.length > 0){
                    queryfor:
                        for (let i = 0; i < queryData.rows.length; i++) {
                            let arr = queryData.rows[i].value.split(',');
                            for (let j = 0; j < arr.length; j++) {
                                if(arr[j] === this.userInfo.positionType){
                                    let userType = queryData.rows[i].key.split('_');
                                    this.userType = userType[userType.length-1];
                                    break queryfor;
                                }
                            }
                        }
                }
            }else{
                this.$showError('获取用户信息失败' + queryData.result);
            }
        },
        onElementLinstener() {
            // 创建节点侦查器
            let arr = this.menu.filter((item, index) => {
                item.index = index;
                return item.flag && !item.loading && item.select !== '#heatmap'
            });
            arr.forEach((obj) => {
                wx.createIntersectionObserver().relativeToViewport().observe(obj.select, res => {
                    if(!this.menu[obj.index].loading){
                        this.menu[obj.index].loading = true;
                    }
                });
            })
        },
        /**
         *  @description: 根据职位控制菜单展示
         *  @author: 吕志平
         *  @date: 2022年7月14日
         */
        initView(){
            this.menu = [
                {
                    select:'#customerPreview',
                    flag:true,
                    loading:true
                },{
                    select:'#drinkser',
                    flag:true,
                    loading:true
                },{
                    select:'#personnel',
                    flag: this.userType === 'Salesman'?false:true,
                    loading:false
                },{
                    select:'#coverCustomers',
                    flag:true,
                    loading:false
                },{
                    select:'#enforcement',
                    flag: this.userType === 'Salesman'?false:true,
                    loading:false
                },{
                    select:'#terminalSales',
                    flag:true,
                    loading:false
                },{
                    select:'#heatmap',
                    flag: this.userType !== 'Salesman' && this.userType !== 'SalesSupervisor'?true:false,
                    loading:false
                },{
                    select:'#attainment',
                    //flag: this.userInfo.positionType === 'Salesman'?false:true,
                    flag: false,
                    loading:false
                },{
                    select: '#target',
                    flag: true,
                    loading:false
                }
            ];
        },
        /**
         *  @description: 监听所有返回本页面 详细看文档
         *  @author: 吕志平
         *  @date: 2022年6月28日
         */
        onBack(param){
            if(this.$utils.isNotEmpty(param)){
                if(this.$utils.isNotEmpty(param.data)){
                    if(this.$utils.isNotEmpty(param.source) && param.source==='customer'){
                        this.customerOrgData = param.data;
                        setTimeout(() => {
                            this.$refs.customer.getCustomerInfo();
                            this.$refs.customer.customerChart();
                        }, 300)
                    }else if(this.$utils.isNotEmpty(param.source) && param.source==='sales'){
                        this.salesOrgData = param.data;
                        setTimeout(() => {
                            this.$refs.sales.customerChart();
                        }, 300)
                    }else if(this.$utils.isNotEmpty(param.source) && param.source==='quota'){
                        this.quotaOriData = param.data;
                        setTimeout(() => {
                            this.$refs.quota.getQuataData();
                        }, 300)
                    }
                    else if(this.$utils.isNotEmpty(param.source) && param.source==='goal'){
                        this.goalOriData = param.data;
                        setTimeout(() => {
                            // this.$refs.quota.getQuataData();
                        }, 300)
                    }
                }
            }
        }
    }
}

</script>

<style lang="scss">
.role-board-page{
    .lnk-tabs{
        position: relative;
        border-bottom: 2px solid #f2f2f2!important;
    }
    .boardBox{
        background: #ffffff;
        margin: 0 14px 24px;
        border-radius: 12px;
    }
}
</style>
