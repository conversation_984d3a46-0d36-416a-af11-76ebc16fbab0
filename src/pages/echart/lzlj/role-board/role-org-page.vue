<template>
    <link-page class="role-org-page">
        <link-auto-list :option="autoList" hideCreateButton :searchInputBinding="{props:{placeholder:'组织名称'}}">

            <template slot-scope="{data,index}">
                <link-radio-group v-model="selectitem">
                    <list>
                        <item :key="index" :data="data" :arrow="false" class="scan-list" >
                            <view class="scan-item"  slot="note"  >
                                <view class="flex" style="margin: 11rpx 0;flex-direction: column;">
                                    <view style="line-height: 20rpx;">
                                        <text >{{data.companyName}}</text>
                                    </view>
                                    <view style="line-height: 28rpx;font-size: 28rpx; font-weight: 500; color: #333333; margin-top: 8px;" >{{data.text}}</view>
                                </view>
                            </view>
                            <link-checkbox ref="checkbox" :val="data.id" toggleOnClickItem slot="thumb" @tap="checkItem(data)" ></link-checkbox>
                        </item>
                    </list>
                </link-radio-group>
            </template>
        </link-auto-list>
        <link-sticky>
            <link-button block @tap="back" >确认</link-button>
        </link-sticky>
    </link-page>
</template>

<script>


export default {

    name: "role-org-page",
    data(){
        const autoList = new this.AutoList(this, {
            module: 'action/link/orgnization',
            searchFields: ['text'],
            param: {
                oauth: 'MY_ORG',
                filtersRaw: [
                    {"id": "isEffective", "property": "isEffective", "value": "Y"}
                ]
            },
        });
        return{
            autoList,
            selectitem:"",
            data:{}

        }
    },
    created() {
        console.log(this.pageParam)
    },
    watch:{
    },
    methods:{
        checkItem(item){
            this.data=item
        },
        /**
         *  @description: 返回上一个界面
         *  @author: 吕志平
         *  @date: 2021年10月26日19:33:55
         */
        back() {
            if(!this.$utils.isEmpty(this.data)){
                this.$bus.$emit("orgBack", {data:this.data,source:this.pageParam.source});
                this.$nav.back({data:this.data,source:this.pageParam.source, nofresh: true})
            }
        },
    }
}
</script>

<style lang="scss">

.link-item-icon{
    width:0px;
    padding-left: 0px;
}

.role-org-page{
    padding: 0;
    margin: 0;
    /*deep*/
    .link-auto-list .link-auto-list-top-bar {
        border-bottom: none !important;
    }
    /*deep*/
    .link-search-input{
        padding: 26px;
    }
    .scan-list{
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
        height: 146px;
        .scan-item{
            background-color: #FFFFFF;
            position: relative;
            width: 100%;
            display: flex;
            flex-direction: column;
        }
    }
}
</style>
