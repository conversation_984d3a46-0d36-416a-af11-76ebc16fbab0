<!--
@file 终端看板功能开发 拜访覆盖客户模块
<AUTHOR>
@Created 2022/6/29
-->
<template>
  <link-page class="visit-cover-customers" id="bg-dialog-model">
    <line-title :title="headTitle" class="head-title"></line-title>
    <view class="head-button">
        <view v-if="isZhuGuan" style="margin-bottom: 5px">
            <select-button label="个人" @tap="oauthButtonSwitch('', 'my')" class="basic-btn" :selected-flag="showMy"></select-button>
            <select-button label="团队" @tap="oauthButtonSwitch('', 'other')" class="basic-btn" :selected-flag="!showMy"></select-button>
        </view>
        <view>
            <select-button label="本周" @tap="dateButtonSwitch('WEEK')" class="basic-btn" :selected-flag="dateButtonFlag"></select-button>
            <select-button label="本月" @tap="dateButtonSwitch('MONTH')" class="basic-btn" :selected-flag="!dateButtonFlag"></select-button>
        </view>
    </view>
    <!-- <view class="dialog-box">
      <link-icon icon="mp-info-lite" status="primary" class="dialog-icon" />
      <view class="dialog-button" mode="text" @tap="() => {}" disabled> 点击未拜访数查看客户明细! </view>
    </view> -->
    <view class="basic-info"  v-show='!loadingShow'>
      <view class="form-title" v-if="userType !== 'Salesman' && userType !== 'SalesSupervisor'">
        <view class="customer"> 客户等级 </view>
        <view class="customer"> 客户数量 </view>
        <view class="customer"> 已拜访数 </view>
        <view class="customer"> 拜访覆盖率 </view>
        <view class="customer"> 未拜访数 </view>
      </view>
      <view class="form-title" v-else>
        <view class="customers-leavel"> 客户等级 </view>
        <view class="customers-count"> 客户数量 </view>
        <view class="visited-count"> 已拜访数 </view>
        <view class="unvisited-count"> 未拜访数 </view>
      </view>
      <view class="form-row" v-for="(item, index) of visitOverViewData" :key="index">
        <view class="customers-leavel">{{ item.acctLevel|lovFilter(lovArr) }} </view>

        <view class="customers-count"> {{ item.terminalAmt }}</view>

        <view class="visited-count" v-if="userType == 'Salesman'">
          <link-button @tap="unvisitedDetails('VISIT', item)" mode="text" class="table-button">
            {{ item.visitTerminalAmt }}
          </link-button>
          <view class="to-detail">></view>
        </view>
        <view class="visited-count" v-else>
          {{ item.visitTerminalAmt }}
        </view>

        <view class="visited-count" v-if="userType !== 'Salesman' && userType !== 'SalesSupervisor'"> {{ item.averageVisitAmt }}% </view>

        <view class="unvisited-count">
          <link-button @tap="unvisitedDetails('UNVISIT', item)" mode="text"  class="table-button">
            {{ item.unVisitTerminalAmt }}
          </link-button>
          <view class="to-detail">></view>
        </view>
      </view>
    </view>
    <view v-show='loadingShow' class="load-box">
        <link-loading class="load" type="gamma"/>
    </view>
    <link-dialog v-model="openFlag" :title="topFormTitle" noPadding width="96vw" :enableScroll="false">
      <view class="unvisitedDetails-form">
        <link-button @tap="unvisitedDetails()" mode="text" style="position: absolute; font-size: 35px; color: #999999; top: 0px; right: 6px; transform: rotate(-315deg)">+</link-button>
        <view class="cutting-line" style="margin-top: 3px; height: 1px; background: #d9d9d9; margin-bottom: 13px"></view>
        <scroll-view scroll-y="true" :style="{'height': 'calc(60vh - 44px)'}">
          <view class="form-title" v-if="reportType == 'VISIT' || userType === 'Salesman'">
              <view class="customer" style="flex: 2"> 客户编码 </view>
              <view class="customer" style="flex: 2"> 客户名称 </view>
              <view class="customer"><view>终端</view><view>等级</view></view>
              <view class="customer" v-if="reportType == 'VISIT'"><view>拜访</view><view>次数</view></view>
              <view class="customer" style="flex: 2"> 最近拜访日期 </view>
          </view>
          <view class="form-title" v-else>
              <view class="customer" style="flex: 2"> {{userType ==='SalesSupervisor'?'员工姓名':'组织名称'}} </view>
              <view class="customer"> 客户总数 </view>
              <view class="customer" style="flex: 2"> 未拜访客户数 </view>
          </view>
          <view>
              <view v-if="userType === 'Salesman'" style="padding-bottom: 15px">
                  <view v-if="(reportType == 'VISIT'?visitBoardList:unVisitBoardList).length">
                      <view v-for="(item, index) of (reportType == 'VISIT'?visitBoardList:unVisitBoardList)" class="form-row" :key="index">
                          <view class="customer" style="flex: 2">{{ item.acctCode }}</view>
                          <view class="customer" style="flex: 2">{{ item.acctName }}</view>
                          <view class="customer">{{ item.acctLevel|lovFilter(lovArr) }}</view>
                          <view class="customer" v-if="reportType == 'VISIT'">{{ item.amount }}</view>
                          <view class="customer" style="flex: 2"> {{ item.visitTime | deteleData() }} </view>
                      </view>
                  </view>
                  <view class="no-mess" v-else> 暂无数据 </view>
              </view>
              <view v-else style="padding-bottom: 15px">
                  <view v-if="unVisitBoardList.length">
                      <view v-for="(item, index) of unVisitBoardList" :key="index" class="form-row">
                          <view class="customer" style="flex: 2">{{ userType ==='SalesSupervisor'?item.acctName:item.orgName }}</view>
                          <view class="customer">{{ item.amount }}</view>
                          <view class="customer" style="flex: 2">{{ item.unVisitTerminalAmt }}</view>
                      </view>
                  </view>
                  <view style="text-align: center; width: 100%; margin-top: 20px; font-size: 15px" v-else> 暂无数据 </view>
              </view>
          </view>
        </scroll-view>
      </view>
    </link-dialog>
    <view class="bg-dialog-model" v-show="openFlag"></view>
  </link-page>
</template>

<script>
import LineTitle from "../../../../lzlj/components/line-title";
import SelectButton from "../../components/select-button";
import Taro from "@tarojs/taro";
export default {
  name: "visit-cover-customers",
  components: {SelectButton, LineTitle},
  data() {
    return {
      loadingShow: false,
      lastRang: '',
      lovArr: [], // 父值列表为BRAND_COM_NAME，父值为当前用户brandCompanyCode，值列表为ACCT_LEVEL的值
      dateButtonFlag: true, //顶部按钮切换参数
      showMy: 'show',
      headTitle: "拜访覆盖客户", //模块标题
      userInfo: {}, //用户信息
      dialogFlag: false,
      WEEKData: {
        dataLoad: false
      }, // 本周数据
      MONTHData: {
        dataLoad: false
      }, // 本月数据
        WEEKmyData: {
            dataLoad: false
        }, // 本周-个人数据
        WEEKotherData: {
            dataLoad: false
        }, // 本周-团队数据
        MONTHmyData: {
            dataLoad: false
        }, // 本月-个人数据
        MONTHotherData: {
            dataLoad: false
        }, // 本月-团队数据
      visitOverViewData: {}, //拜访覆盖终端统计数据
      visitDetail: {}, //拜访详情列表数据
      topFormTitle: "拜访详情", //弹窗题目
      openFlag: false, //弹窗显示开关
      reportType: '', //扫码还是未扫码数据
      unVisitBoardList: [], //未拜访详情数据
      visitBoardList: [], //已拜访详情数据
      orgBox:false,
      isZhuGuan: false
    };
  },
    props:{
        firstLoading:{
            type: Boolean,
            default: false
        },
        userType: {
            type: String
        },
        orgText: {
            type: String,
            default: '选择组织'
        },
        orgId: {
            type: String,
            default: ''
        },
        orgCompanyCode: {
            type: String,
            default: ''
        },
        // 筛选5码参数
        threeParams:{
            type: Object,
            default(){
                return {}
            }
        }
    },
    watch:{
        firstLoading(newVal, oldVal) {
            this.getVisitOverViewData("WEEK", 'my');
        },
        // orgId() {
        //     this.orgBackGetInfo()
        // }
    },
  methods: {
    /**
     *
     * <AUTHOR>
     * @date   2022/7/5
     * 切换周或者月按钮
     */
    dateButtonSwitch(rang) {
        let dt
        if(this.isZhuGuan) {
            const type =  this.showMy ? 'my' : 'other'
            dt = this[rang + type + 'Data'];
        } else {
            dt = this[rang+'Data'];
        }
      if(!dt.dataLoad){
        this.getVisitOverViewData(rang);
      }else{
        this.visitOverViewData = dt;
      }
      if(rang === 'WEEK' && this.dateButtonFlag) return
      if(rang === 'MONTH' && !this.dateButtonFlag) return
      this.dateButtonFlag = !this.dateButtonFlag;
    },
      oauthButtonSwitch(rang, type) {
          rang = rang || this.lastRang
          if(type === 'my') {
              if(this.showMy) return
              this.showMy = true
          }
          if(type === 'other') {
              if(!this.showMy) return
              this.showMy = false
          }
          let dt = this[rang + type + 'Data'];
          if(!dt.dataLoad){
              this.getVisitOverViewData(rang, type);
          }else{
              this.visitOverViewData = dt;
          }
      },
    /**
     *
     * <AUTHOR>
     * @date   2022/7/5
     * 弹出拜访或未拜访详情表格弹窗
     */
    async unvisitedDetails(reportType, obj) {
        this.reportType = reportType;
        this.openFlag = !this.openFlag;
        if (this.openFlag) {
            this.$utils.showLoading();
            this.getVisitDetailData(reportType, obj);
            reportType == "VISIT" ? (this.topFormTitle = "拜访详情") : (this.topFormTitle = "未拜访详情");
        }
    },
    /**
     * @createdBy  刘怡
     * @date   2022/6/27
     * @para
     * @description 拜访覆盖终端统计接口
     *
     */
    async getVisitOverViewData(rang, type, orgId) {
      if(this.isZhuGuan) {
        if(rang) this.lastRang = rang
        if(!rang) rang = this.lastRang
        if(!type) type = this.showMy ? 'my' : 'other'
      }
      let param;
      if (this.userType !== "Salesman" && this.userType !== "SalesSupervisor") {
        param = {
          oauth: this.isZhuGuan && type === 'my' ? 'MY' : 'MULTI_ORG',
          brandComCode: this.orgCompanyCode,
          orgId: orgId ? orgId : this.orgId,
          periodType: rang || this.lastRang,
        };
      } else {
        param = {
          oauth: this.isZhuGuan && type === 'my' ? 'MY' : 'MULTI_POSTN',
          postnId: this.userInfo.postnId,
          periodType: rang || this.lastRang,
		  brandComCode: this.orgCompanyCode,
        };
      }
      param.role = this.userType;
      const params = {
          ...param,
          ...this.threeParams,
      }
      // this.$utils.showLoading();
      this.loadingShow = true
      try {
        const data = await this.$http.post("export/link/terminalBoard/visit/overview", params);
        if (data) {
          if(this.isZhuGuan) {
            this[rang + type + 'Data'] = this.$utils.deepcopy(data.result);
            this[rang + type + 'Data'].dataLoad = true;
          } else {
            this[rang + 'Data'] = this.$utils.deepcopy(data.result);
            this[rang + 'Data'].dataLoad = true;
          }
          this.visitOverViewData = data.result;
          // this.$utils.hideLoading();
          this.loadingShow = false
        } else {
          // this.$utils.hideLoading();
          this.loadingShow = false
          this.$showError("拜访覆盖终端统计接口请求失败，请稍后重试！" + data.result);
        }
      } catch (error) {
        // this.$utils.hideLoading();
        this.loadingShow = false
        this.$showError("拜访覆盖终端统计接口请求失败，请稍后重试！" + error.errMsg);
      }
    },
    /**
     *
     * @createdBy  刘怡
     * @date   2022/6/27
     * @para
     * @description 已拜访与未拜访详情接口
     *
     */
    async getVisitDetailData(reportType, obj) {
      let param;
      if (this.userType !== "Salesman" && this.userType !== "SalesSupervisor") {
        param = {
          oauth: "MULTI_ORG",
          orgId: this.orgId,
          role: 'Manager'
        };
      } else {
        param = {
          oauth: "MULTI_POSTN",
          postnId: this.userInfo.postnId,
          role: this.userType
        };
      }
      param.reportType = reportType;
      param.acctLevel = obj.acctLevel;
      param.periodType = this.dateButtonFlag?'WEEK':'MONTH';
      const params = {
          ...param,
          ...this.threeParams,
      }
      try {
        const data = await this.$http.post("export/link/terminalBoard/visit/visitDetail", params);
        this.$utils.hideLoading();
        if (data) {
          this.visitDetail = data.result;
          this.unVisitBoardList = this.visitDetail.unVisitBoardList || [];
          this.visitBoardList = this.visitDetail.visitBoardList || [];
        } else {
          this.$showError("拜访覆盖终端统计接口请求失败，请稍后重试！" + data.result);
        }
      } catch (error) {
        this.$utils.hideLoading();
        this.$showError("拜访覆盖终端统计接口请求失败，请稍后重试！" + error.errMsg);
      }
    },

      async orgBackGetInfo(orgId){
          let param = { type: 'ACCT_LEVEL', parentType: 'BRAND_COM_NAME', parentVal: this.orgCompanyCode || 5137};
          this.lovArr = await  this.$lov.getLovByParentTypeAndValue(param);
          if(this.dateButtonFlag){
             await this.getVisitOverViewData('WEEK', '', orgId);
          }else{
              await this.getVisitOverViewData('MONTH', '', orgId);
          }
      }
  },
  filters: {
    deteleData(str) {
      return str?str.split(" ")[0].trim().replaceAll("-", "/"):'';
    },
    lovFilter(val, arr){
      let data= arr.filter((item) => {
        return item.val === val;
      })
      return data.length > 0 ? data[0].name : val;
    }
  },
  async created(){
    this.isZhuGuan = this.userType === 'SalesSupervisor'
    const userInfoData = await this.$taro.getStorageSync("token").result;
    this.userInfo = userInfoData;
      if(this.userType !=='Salesman' && this.userType !=='SalesSupervisor'){
          this.orgBox = true;
      }
      // this.orgId = this.userInfo.orgId;
      // this.orgText = this.userInfo.orgName;
      // this.orgCompanyCode = this.userInfo.coreOrganizationTile.brandCompanyCode;
    let param = { type: 'ACCT_LEVEL', parentType: 'BRAND_COM_NAME', parentVal: this.orgCompanyCode || 5137};
    this.lovArr = await  this.$lov.getLovByParentTypeAndValue(param);
  }
};
</script>

<style lang="scss">
.visit-cover-customers {
  padding: 32px 24px 40px 24px;
    .load-box{
        text-align: center;
        height: 200px;
        line-height: 200px;
        padding: 80px 0;
        .load{
            font-size: 160px;
            color: #1F74FF;
        }
    }
    .scroll-view-data{
        margin-top: 24px;
        margin-bottom: 24px;
        .select-dimension{
            display: flex;
            margin-left: 24px;
        }
    }

  .head-title {
    margin: 0px;
    padding: 0px;
    margin-bottom: 32px;
    font-family: PingFangSC-Medium;
    font-size: 32px;
    color: #ffffff;
    text-align: left;
    line-height: 32px;
    font-weight: 500;
  }
  .head-button {
    margin-bottom: 32px;
    .basic-btn {
      display: inline-block;
    }
  }
  .basic-info{
    .form-title {
      background: #c8d7fa;
      border: 1px solid rgba(230, 234, 244, 1);
      display: flex;
      justify-content: space-around;
      flex-direction: row;
      font-family: PingFangSC-Medium;
      font-size: 24px;
      color: #333333;
      letter-spacing: 0;
      text-align: center;
      white-space: nowrap;
      line-height: 28px;
      font-weight: 500;
      .customer{
        padding: 22px 0;
        border-right: 1px solid rgba(230, 234, 244, 1);
        flex: 1
      }
      .customers-leavel {
        padding: 22px 32px 22px 34px;
        border-right: 1px solid rgba(230, 234, 244, 1);
      }
      .customers-count {
        padding: 22px 32px 22px 34px;
        border-right: 1px solid rgba(230, 234, 244, 1);
      }
      .visited-count {
        padding: 22px 32px 22px 34px;
        border-right: 1px solid rgba(230, 234, 244, 1);
      }
      .unvisited-count {
        padding: 22px 32px 22px 34px;
      }
    }
    .form-row {
      background: #ffffff;
      border: 1px solid rgba(230, 234, 244, 1);
      display: flex;
      justify-content: space-around;
      flex-direction: row;
      white-space: nowrap;
      height: 80px;
      line-height: 80px;
      font-family: PingFangSC-Regular;
      font-size: 24px;
      color: #666666;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      min-height: 100px;
      .customers-leavel {
        width: 164px;
        border-right: 1px solid rgba(230, 234, 244, 1);
      }
      .customers-count {
        width: 164px;
        border-right: 1px solid rgba(230, 234, 244, 1);
      }
      .to-detail{
          color: #2F69F8;
          display: inline-block;
          font-size: 30px;
          left: -4px;
          position:relative;
          top: 1px;
      }
      .visited-count {
        width: 164px;
        border-right: 1px solid rgba(230, 234, 244, 1);
        .link-button-status-primary{
            color: #666666;
        }
        .link-button-size-normal{
            font-size: 24px;
        }
      }
      .unvisited-count {
        width: 164px;
        .link-button-status-primary{
            color: #666666;
        }
        .link-button-size-normal{
            font-size: 24px;
        }
      }
    }
  }
  .unvisitedDetails-form {
    width: 100%;
    padding: 24px 24px 32px 24px;
    .form-title, .form-row{
      display: flex;
      min-height: 72px;
      background: #c8d7fa;
      border: 1px solid #e6eaf4;
      font-family: PingFangSC-Medium;
      color: #333333;
      letter-spacing: 0;
      text-align: center;
      font-weight: 500;
      font-size: 24px;
      justify-content: center;
      flex-direction: row;
      .customer{
        padding: 24px 0;
        text-align: center;
        border-right: 2px solid rgba(230, 234, 244, 1);
        flex: 1
      }
    }
    .form-row{
      background: #FFFFFF;
    }
    .no-mess{
      text-align: center;
      width: 100%;
      margin-top: 40px;
      font-size: 30px
    }
  }

  .dialog-box {
    text-align: right;
    .dialog-icon {
      display: inline-block;
      vertical-align: baseline;
      margin-right: -10px;
      color: #999999;
    }
    .dialog-button {
      display: inline-block;
      width: 314px;
      height: 24px;
      font-family: PingFangSC-Regular;
      font-size: 24px;
      color: #999999;
      line-height: 24px;
      font-weight: 400;
      margin-top: 34px;
    }
  }
  .bg-dialog-model{
    width: 100%;
    height: 100vh;
    background: #FFFFFF;
  }
}
</style>
