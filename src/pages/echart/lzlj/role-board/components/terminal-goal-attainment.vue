<!--
@file 终端124目标达成
<AUTHOR>
@Created 2022/7/11
-->
<template>
  <link-page class="terminal-goal-attainment" style="padding-left: 12px; padding-top: 16px; padding-bottom: 5px" v-if="userType !== 'Salesman'">
    <line-title title="终端124目标达成" style="margin-left: 0px; padding-top: 0px"></line-title>
    <scroll-view scroll-x="true" class="scroll-view-data" v-if="userType !== 'Salesman' && userType !== 'SalesSupervisor'">
      <view class="select-dimension" style="display: flex; margin-bottom: 16rpx; margin-top: 32rpx">
        <select-button :label="orgText" :selected-flag="true" @tap="dialogFlag=true;" downIcon></select-button>
      </view>
      <view>
        <link-echart :force-use-old-canvas='false':option="FormData" :loading="loadingFlag" />
      </view>
    </scroll-view>
      <position-bottom :user-info="userInfo" :show.sync="dialogFlag" @choose="changeOrg"></position-bottom>
  </link-page>
</template>

<script>
import SelectButton from "../../components/select-button";
import LineTitle from "../../../../lzlj/components/line-title";
import PositionBottom from "../../components/position-bottom";
import Taro from "@tarojs/taro";
export default {
  name: "terminal-goal-attainment",
  components: {PositionBottom, LineTitle, SelectButton},
  data() {
      const userInfo = Taro.getStorageSync('token').result;
    return {
      userInfo,
      FormData: {},
      BardataArry: [], //柱状图数据
      loadingFlag: false, //图表组件是否加载表示
        dialogFlag: false,
        orgText: '选择组织',
        orgId: null,
        orgCompanyCode:'',
    };
  },
  props: {
      userType: {
          type: String
      },
    orgData: {
      type: Object,
      default: () => {
        return {};
      },
    },
      firstLoading:{
          type: Boolean,
          default: false
      }
  },
  async created() {
    let data = [
      ["1A", 176, 162],
      ["2A", 44, 82],
      ["3A", 23, 41],
      ["4A", 8, 24],
      ["5A", 1, 3],
    ];
    for (let i = 0; i < data.length; i++) {
      data[i][3] = `${Math.floor((data[i][1] / data[i][2]) * 100)}%`;
    }
    this.BardataArry = data;
    this.drow();
      this.orgId = this.userInfo.orgId;
      this.orgText = this.userInfo.orgName;
      this.orgCompanyCode = this.userInfo.coreOrganizationTile.brandCompanyCode;
  },
  methods: {
      async changeOrg(item) {
          if(Object.keys(item).length === 0)return;
          this.orgText = item.text;
          this.orgId = item.id;
          this.orgCompanyCode = item.orgTile.brandCompanyCode?item.orgTile.brandCompanyCode : '';
      },
    gotoOrg() {
      this.$nav.push("/pages/echart/lzlj/role-board/role-org-page.vue", {
        source: "goal",
      });
    },
    drow() {
      this.loadingFlag = true;
      this.FormData = {
        color: [
          {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#6392FA", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#2F69F8", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
          {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#C9E9FF", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#98CEFF", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        ],

        legend: {
          data: ["达成率/数", "目标数"],
          itemWidth: 8,
          itemHeight: 8,
          selectorLabel: {
            verticalAlign: "bottom",
          },
          top: -3,
          right: 20,
          itemGap: 15,
        },
        grid: {
          top: 20,
        },

        xAxis: {
          type: "value",
          splitLine: {
            lineStyle: {
              type: "dashed",
              width: 1,
            },
          },
        },
        yAxis: {
          type: "category",
          data: ["5A", "4A", "3A", "2A", "1A"],
        },
        dataset: {
          dimensions: ["comer", "达成率/数", "目标数", "aaa"],
          source: this.BardataArry,
        },
        series: [
          {
            name: "达成率/数",
            type: "bar",
            label: {
              show: true,
              position: "insideRight",
              formatter: "{@aaa}  {@[1]}",
              offset: [32, 0],
              color: "black",
            },
            z: 2,
          },
          {
            name: "目标数",
            type: "bar",
            label: {
              show: true,
              position: "insideBottomRight",
              offset: [0, 20],
              color: "black",
            },
            barGap: "-50%",
            z: 1,
          },
        ],
      };
      this.loadingFlag = false;
    },
  },
};
</script>

<style lang="scss"></style>
