<!--
@file 终端看板功能开发 所辖人员拜访执行情况模块
<AUTHOR>
@Created 2022/6/29
-->
<template>
  <link-page class="personnel-visit-execution-status" v-if="userType !== 'Salesman'">
    <line-title title="所辖人员拜访执行情况" class="head-title"></line-title>
      <view class="head-button" v-if="userType === 'SalesSupervisor'">
          <select-button label="个人" @tap="visitDetailData('my')" class="basic-btn" :selected-flag="dateButtonFlag"></select-button>
          <select-button label="团队" @tap="visitDetailData('other')" class="basic-btn" :selected-flag="!dateButtonFlag"></select-button>
      </view>
    <view v-for="(item, index) in this.titleArry" :key="index" class="item-box">
      <view class="box-title" v-if="showFlag(item.title)">
        <view class="dot"> </view>
        <view class="text">
          {{ item.title }}
        </view>
      </view>
      <view class="box-content" v-if="showFlag(item.title)">
        <view class="box-content-column">
          <view class="center" style="text-align: center">
            <view class="number" v-if="item.title == '拜访次数'"> {{ visitData.todayVisitSum || 0 }} </view>
            <view class="number" v-if="item.title == '拜访人数'"> {{ visitData.todayVisitorSum || 0 }} </view>
            <view class="number" v-if="item.title == '拜访核心终端'"> {{ visitData.todayCoreTerminalSum || 0 }} </view>
            <view class="number" v-if="item.title == '拜访终端'"> {{ visitData.todayTerminalSum || 0 }} </view>

            <view class="text"> 今日 </view>
            <view class="line"></view>
          </view>
        </view>
        <view class="box-content-column">
          <view class="center">
            <view class="number" v-if="item.title == '拜访次数'"> {{ visitData.weekVisitSum || 0 }} </view>
            <view class="number" v-if="item.title == '拜访人数'"> {{ visitData.weekVisitorSum || 0 }} </view>
            <view class="number" v-if="item.title == '拜访终端'"> {{ visitData.weekTerminalSum || 0 }} </view>
            <view class="number" v-if="item.title == '拜访核心终端'"> {{ visitData.weekCoreTerminalSum || 0 }} </view>
            <view class="text"> 本周 </view>
            <view class="line"></view>
          </view>
        </view>
        <view class="box-content-column">
          <view class="center">
            <view class="number" v-if="item.title == '拜访次数' || item.title == '拜访人数'">
              <view v-if="item.title == '拜访次数'"> {{ visitData.monthVisitSum || 0 }}</view>
              <view v-else>{{ visitData.monthVisitorSum || 0 }} </view>
            </view>
            <view class="number" v-if="item.title == '拜访终端' || item.title == '拜访核心终端'">
              <view v-if="item.title == '拜访终端'">
                <view style="text-indent: 12px; display: inline-block">
                  {{ visitData.monthTerminalSum || 0 }}
                </view>
                <view style="display: inline-block; widfth: 30px; height: 18px; font-size: 13px; text-align: center; line-height: 18px; margin-left: -2px">
                  / {{ visitData.terminalTotalAmt || 0 }}
                </view>
                <link-icon @tap="showTip('terminalTotal')" icon="mp-info-lite" status="primary" class="dialog-icon" style="display: inline-block; vertical-align: baseline; margin-left: -3px; color: #999999" />
              </view>
              <view v-else>
                <view style="text-indent: 12px; display: inline-block">
                  {{ visitData.monthCoreTerminalSum || 0 }}
                </view>
                <view style="display: inline-block; widfth: 30px; height: 18px; font-size: 13px; text-align: center; line-height: 18px; margin-left: -2px">
                  / {{ visitData.coreTerminalAmt || 0 }}
                </view>
                <link-icon @tap="showTip('coreTerminal')" icon="mp-info-lite" status="primary" class="dialog-icon" style="display: inline-block; vertical-align: baseline; margin-left: -3px; color: #999999" />
              </view>
              <link-dialog v-model="tipOpen" :enableScroll="false">{{tips}}</link-dialog>
            </view>
            <view class="text"> 本月 </view>
          </view>
        </view>
      </view>
    </view>
  </link-page>
</template>

<script>
import LineTitle from "../../../../lzlj/components/line-title";
import SelectButton from "../../components/select-button";
import PositionBottom from "../../components/position-bottom";
import Taro from "@tarojs/taro";
export default {
  name: "personnel-visit-execution-status",
  components: {LineTitle, SelectButton},
  data() {
    return {
      oauthType: 'my',
      dateButtonFlag: true,
      // orgCompanyCode:'',
      tipOpen: false, // 展示提示框
      tips: '', // 提示框展示
      titleArry: [{title: "拜访次数"}, {title: "拜访人数"}, {title: "拜访终端"}, {title: "拜访核心终端"}],
      userInfo: {}, //用户信息
      visitData: {}, //所辖人员拜访数据
      sumData: {}, // 拜访终端总数/拜访核心终端总数，从别的组件传过来
      orgBox:false
    };
  },
    props:{
        userType: {
            type: String
        },
        firstLoading:{
            type: Boolean,
            default: false
        },
        orgText: {
            type: String,
            default: '选择组织'
        },
        orgCompanyCode: {
            type: String,
            default: ''
        },
        orgId: {
            type: String,
            default: ''
        },
        // 筛选5码参数
        threeParams:{
            type: Object,
            default(){
                return {}
            }
        }
    },
    watch:{
        firstLoading(newVal, oldVal) {
            this.visitDetailData();
        },
        orgId() {
            this.visitDetailData();
        }
    },
  methods: {
      showFlag(title) {
          if(this.userType === 'SalesSupervisor' && this.oauthType === 'my' && title === '拜访人数') return false
          return true
      },
      async changeOrg(item) {
          if(Object.keys(item).length === 0)return;
          this.orgText = item.text;
          this.orgId = item.id;
          this.orgCompanyCode = item.orgTile.brandCompanyCode?item.orgTile.brandCompanyCode : '';
      },
      gotoOrg(){
          this.$nav.push('/pages/echart/lzlj/role-board/role-org-page.vue',{source:'personnel'})
      },
      /**
       @param type通过判断展示tips内容
       @desc: 展示tips
       @author: wangbinxin
       @date 2022-08-09 19-57
       **/
      showTip(type){
          this.tips = type === 'terminalTotal'?'本月拜访终端/截止当日的终端总数':'本月拜访核心终端数/截止当日的核心终端总数';
          this.tipOpen = true;
      },
    /**
     * @createdBy  刘怡
     * @date   2022/6/27
     * @para
     * @description 请求所辖人员拜访执行接口
     *
     */
    async visitDetailData(type) {
      //lzlj-002-4198主管角色相关指标优化
      const flag = this.userType === 'SalesSupervisor'
      if(flag) {
        if(this.oauthType === type) return
        if(type) this.oauthType = type
        if(type === 'my') this.dateButtonFlag = true
        if(type === 'other') this.dateButtonFlag = false
      }
      this.$utils.showLoading();
      let param = {};
      if (!this.orgBox) {
        param = {
          oauth: flag && this.oauthType === 'my' ? 'MY' : 'MULTI_POSTN',
          postnId: this.userInfo.postnId
        };
      } else {
        param = {
          oauth: flag && this.oauthType === 'my' ? 'MY' : "MULTI_ORG",
          orgId: this.orgId
        };
      }
      param.role = this.userType;
      param.sapCompCode =  this.orgCompanyCode;
      const params = {
          ...param,
          ...this.threeParams,
      }
      try {
        const data = await this.$http.post("export/link/terminalBoard/visit/detail", params);
        if (data) {
          this.visitData = {};
          this.visitData = Object.assign(data.result, this.sumData);
          this.$utils.hideLoading();
        } else {
          this.$utils.hideLoading();
          this.$showError("所辖人员拜访执行接口请求失败，请稍后重试！" + data.result);
        }
      } catch (error) {
        this.$utils.hideLoading();
        this.$showError("所辖人员拜访执行接口请求失败，请稍后重试！" + error.errMsg);
      }
    },
  },
  async mounted() {
    const userInfoData = await this.$taro.getStorageSync("token").result;
    this.userInfo = userInfoData;
      // this.orgId = this.userInfo.orgId;
      // this.orgText = this.userInfo.orgName;
      // this.orgCompanyCode = this.userInfo.coreOrganizationTile.brandCompanyCode;
    this.$bus.$on("totalObj",(data) => {
      this.sumData = data;
    })
      if(this.userType !=='Salesman' && this.userType !=='SalesSupervisor'){
          this.orgBox = true;
      }
    // this.visitDetailData();
  },
};
</script>

<style lang="scss">
.personnel-visit-execution-status {
  padding: 32px 24px 40px 24px;
  .head-title {
    margin: 0px;
    padding: 0px;
    margin-bottom: 48px;
    font-family: PingFangSC-Medium;
    font-size: 32px;
    color: #ffffff;
    text-align: left;
    line-height: 32px;
    font-weight: 500;
  }
  .head-button {
    margin-bottom: 32px;
    .basic-btn {
      display: inline-block;
    }
  }
  .item-box {
    padding: 0px 24px 0px 24px;
    background-color: #ffffff;
    .box-title {
      height: 28px;
      font-family: PingFangSC-Medium;
      font-size: 28px;
      color: #333333;
      line-height: 28px;
      font-weight: 500;
      margin-bottom: 42px;
      .dot {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: #2f69f8;
      }
      .text {
        display: inline-block;
        margin-left: 16px;
        vertical-align: sub;
      }
    }
    .box-content {
      padding: 0px 0px 40px 0px;
      background: #ffffff;
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      .box-content-column {
        white-space: nowrap;
        position: relative;
        text-align: center;
        width: 100%;
        .center {
          .number {
            font-family: PingFangSC-Semibold;
            font-size: 36px;
            color: #333333;
            line-height: 36px;
            font-weight: 600;
            margin-bottom: 16rpx;
          }
          .text {
            font-family: PingFangSC-Regular;
            font-size: 26px;
            color: #666666;
            line-height: 26px;
            font-weight: 400;
          }
          .line {
            width: 1px;
            height: 40px;
            background: #dddddd;
            position: absolute;
            top: 22rpx;
            right: 0px;
          }
        }
      }
    }
  }
}
</style>
