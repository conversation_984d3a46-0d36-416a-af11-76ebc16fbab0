<!--
@file 终端看板功能开发 终端分品项统计情况
<AUTHOR>
@Created 2022/11/25
-->
<template>
    <link-page class="prod-item-statistics">
        <line-title :title="headTitle" class="head-title"></line-title>
        <view class="head-button">
            <select-button label="品项动销情况" class="base-btn" selected-flag/>
        </view>
        <view class="prod-table"  v-show='!loadingShow'>
            <view class="prod-thead">
                <view class="prod-td">{{`产品业务${isShow ? '中' : '小' }类`}}</view>
                <view class="prod-td">财年动销/件</view>
                <view class="prod-td">动销占比</view>
            </view>
            <view class="prod-tbody">
                <view v-for="item in prodStatisticswData" class="prod-tr">
                    <view class="prod-td">{{item.target | lovFilter(prodSmallLov)}}</view>
                    <view class="prod-td">{{item.amount}}</view>
                    <view class="prod-td">{{item.amountRate + '%'}}</view>
                </view>
            </view>
        </view>
        <view v-show='loadingShow' class="load-box">
            <link-loading class="load" type="gamma"/>
        </view>
    </link-page>
</template>

<script>
import LineTitle from "../../../../lzlj/components/line-title";
import SelectButton from "../../components/select-button";
export default {
    name: "prod-item-statistics",
    components: {SelectButton, LineTitle},
    data() {
        return {
            headTitle: "终端分品项统计情况", //模块标题
            userInfo: {}, //用户信息
            prodStatisticswData: {}, //统计数据
            loadingShow: false
        };
    },
    props:{
        userType: {
            type: String
        },
        orgId: {
            type: String,
            default: ''
        },
        isShow: {
            type: Boolean,
            default: false
        },
        // 筛选5码参数
        threeParams:{
            type: Object,
            default(){
                return {}
            }
        }
    },
    watch:{
        orgId() {
            this.getProdStatisticsData()
        },
        async isShow(val){
            const lovStr = val ? 'PROD_BUS_M_CLASS' :'PROD_BUS_S_CLASS'
            this.prodSmallLov = await this.$lov.getLovByType(lovStr)
        }
    },
    methods: {
        /**
         * @createdBy 康丰强
         * @date   2022/11/25
         * @description 终端分品项统计数据
         *
         */
        async getProdStatisticsData() {
            let param;
            if (this.userType !== "Salesman" && this.userType !== "SalesSupervisor") {
                param = {
                    oauth: "MULTI_ORG",
                    orgId: this.orgId,
                };
            } else {
                param = {
                    oauth: "MULTI_POSTN",
                    postnId: this.userInfo.postnId,
                };
            }
            param.role = this.userType;
            const params = {
                ...param,
                ...this.threeParams,
            }
            // this.$utils.showLoading();
            this.loadingShow = true
            try {
                const data = await this.$http.post("export/link/terminalBoard/sale/item", params);
                if (data) {
                    this.prodStatisticswData = data.result;
                    // this.$utils.hideLoading();
                    this.loadingShow = false
                } else {
                    // this.$utils.hideLoading();
                    this.loadingShow = false
                    this.$showError("终端分品项统计接口请求失败，请稍后重试！" + data.result);
                }
            } catch (error) {
                // this.$utils.hideLoading();
                this.loadingShow = false
                this.$showError("终端分品项统计统计接口请求失败，请稍后重试！" + error.errMsg);
            }
        }
    },
    filters: {
        lovFilter(val, arr){
            if(val === 'other') return '其他'
            const data= arr.filter((item) => {
                return item.val === val;
            })
            return data.length > 0 ? data[0].name : val;
        }
    },
    async created(){
        this.userInfo = await this.$taro.getStorageSync("token").result;
        const lovStr = this.isShow ? 'PROD_BUS_M_CLASS' :'PROD_BUS_S_CLASS'
        this.prodSmallLov = await this.$lov.getLovByType(lovStr)
        this.getProdStatisticsData()
    }
};
</script>

<style lang="scss">
.prod-item-statistics {
    .load-box{
        text-align: center;
        height: 200px;
        line-height: 200px;
        padding: 80px 0;
        .load{
            font-size: 160px;
            color: #1F74FF;
        }
    }
    padding: 32px 24px 40px 24px;

    .scroll-view-data{
        margin-top: 24px;
        margin-bottom: 24px;
        .select-dimension{
            display: flex;
            margin-left: 24px;
        }
    }

    .head-title {
        margin: 0px;
        padding: 0px;
        margin-bottom: 32px;
        font-family: PingFangSC-Medium;
        font-size: 32px;
        color: #ffffff;
        text-align: left;
        line-height: 32px;
        font-weight: 500;
    }
    .head-button {
        margin-bottom: 32px;
        .base-btn {
            display: inline-block;
        }
    }
    .prod-table {
        font-size: 24px;
        text-align: center;
        .prod-td {
            flex: 1;
            border: 1px solid rgba(230, 234, 244, 1);
            padding: 20px 0;
        }
        .prod-thead {
            display: flex;
            background: #c8d7fa;
            font-family: PingFangSC-Medium;
            color: #333333;
            font-weight: 500;
        }
        .prod-tbody {
            font-family: PingFangSC-Regular;
            color: #666666;
            font-weight: 400;
            .prod-tr {
                display: flex;
            }
        }
    }
}
</style>
