<template>
  <link-page class="terminal-heatmap" style="padding-left: 12px; padding-top: 16px; padding-bottom: 5px" v-if="this.userInfo.positionType !== 'Salesman'">
    <line-title title="终端热力图" style="margin-left: 0px; padding-top: 0px"></line-title>
    <select-button label="本季" @tap="switchButtonFlag()" :selected-flag="ButtonFlag" style="width: 160rpx; display: inline-block; margin-top: 16px"></select-button>
    <select-button label="本财年" @tap="switchButtonFlag()" :selected-flag="!ButtonFlag" style="width: 160rpx; display: inline-block; margin-bottom: 16px"></select-button>
    <view>
      <!-- <link-echart :force-use-old-canvas='false':option="FormData" id="echats" style="width: 90vw; height: 300px" ref="heatmap" :loading="loadingFlag"/> -->
      热力图组件
    </view>
  </link-page>
</template>

<script>
import SelectButton from "../../components/select-button";
import LineTitle from "../../../../lzlj/components/line-title";
export default {
  name: "terminal-heatmap",
  components: {LineTitle, SelectButton},
  data() {
    return {
      userInfo: {}, //用户信息
      FormData: {},
      ButtonFlag: true,
      loadingFlag: false, //图表组件是否加载表示
    };
  },
    props: {
        firstLoading:{
            type: Boolean,
            default: false
        }
    },
    watch:{
        firstLoading(newVal, oldVal) {

        }
    },
  methods: {
    gotoOrg() {
      this.$nav.push("/pages/echart/lzlj/role-board/role-org-page.vue", {
        source: "customer",
      });
    },
    switchButtonFlag() {
      this.ButtonFlag = !this.ButtonFlag;
    },
    drow() {
      this.loadingFlag = true;
      this.FormData = {};
      this.loadingFlag = false;
    },
  },
  async created() {
    const userInfoData = await this.$taro.getStorageSync("token").result;
    this.userInfo = userInfoData;
    this.drow();
  },
};
</script>

<style lang="scss">
// * {
//   width: 90vw;
//   height: 300px;
// }
</style>
