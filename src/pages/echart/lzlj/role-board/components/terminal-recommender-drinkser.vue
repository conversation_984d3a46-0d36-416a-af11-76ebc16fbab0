<!--
@file 终端荐酒员
<AUTHOR>
@Created 2022/6/29
-->
<template>
  <link-page class="terminal-recommender-drinkser" id="terminal-recommender-drinkser">
    <line-title title="终端荐酒员概览" style="margin-left: 0px; padding-top: 0px"></line-title>
    <view class="terminal-overview-content" style="padding: 23px 0px 0px 0px; background: #ffffff; display: flex; flex-direction: row; justify-content: space-around">
      <view class="terminal-overview-content-column" style="flex: 1">
        <view class="center" style="text-align: center">
          <view class="number" style="font-size: 18px; color: #333333; line-height: 18px; font-weight: 600; margin-bottom: 8px">
            {{ this.restaurantTotalAmt ? this.restaurantTotalAmt : 0 }}
          </view>
          <view class="text" style="font-size: 13px; color: #666666; line-height: 13px; font-weight: 500"> 餐饮终端数 </view>
        </view>
      </view>
      <view class="line" style="height: 20px; width: 1px; background: #dddddd; margin-top: 10px"></view>
      <view class="terminal-overview-content-column" style="flex: 1">
        <view class="center" style="text-align: center">
          <view class="number" style="font-size: 18px; color: #333333; line-height: 18px; font-weight: 600; margin-bottom: 8px">
            {{ this.registerTerminalAmt ? this.registerTerminalAmt : 0 }}
          </view>
          <view class="text" style="font-size: 13px; color: #666666; line-height: 13px; font-weight: 500"> 注册终端数 </view>
        </view>
      </view>
      <view class="line" style="height: 20px; width: 1px; background: #dddddd; margin-top: 10px"></view>
      <view class="terminal-overview-content-column" style="flex: 1">
        <view class="center" style="text-align: center">
          <view class="number" style="font-size: 18px; color: #333333; line-height: 18px; font-weight: 600; margin-bottom: 8px">
            {{ this.winePromoterAmt ? this.winePromoterAmt : 0 }}
          </view>
          <view class="text" style="font-size: 13px; color: #666666; line-height: 13px; font-weight: 500"> 荐酒员总数 </view>
        </view>
      </view>
    </view>
    <view class="cutting-line" style="margin-top: 14px; height: 1px; background: #eef3f5"></view>
    <view
      :class="`switch-button ${this.positionType == 'Salesman' ? '' : ' display-none'}`"
      style="display: block; padding: 16px 153px 0px 12px; border-radius: 15px; color: #333333; white-space: nowrap"
    >
      <select-button label="扫码概览" @tap="finishChangge()" :selected-flag="finishButtonFlag" style="width: 160rpx; display: inline-block"></select-button>
      <select-button label="未扫码客户" @tap="finishChangge()" :selected-flag="!finishButtonFlag" style="width: 160rpx; display: inline-block"></select-button>
    </view>
    <view class="finish-form" v-if="this.finishButtonFlag && !this.openFlag">
      <link-echart :force-use-old-canvas='false':option="finishFormData" class="finish-form-total" :loading="loadingFlag" />
    </view>
    <view class="unfinished-form" v-if="!this.finishButtonFlag" style="padding: 24px 40px 16px 41px; display: flex; flex-direction: row; justify-content: space-around">
      <view class="week-rids" style="width: 105px; height: 105px; background: linear-gradient(#abc5ff61 100%, #2f69f82b 100%); border-radius: 50%; position: relative; margin-right: 60px">
        <view
          class="week-rids-linner"
          style="
            text-align: center;
            line-height: 30px;
            width: 85px;
            height: 85px;
            background-color: #ffff;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            justify-content: center;
            flex-direction: column;
          "
        >
          <view>
            <link-button @tap="closeTopForm('week')" mode="text" class="num-button" style="display: inline; line-height: 20px; font-size: 20px; color: #2f69f8">
              {{ this.weekUnScan.cnt || 0 }}
            </link-button>
            <view class="unfinished-form-text" style="margin-left: -3px; font-size: 15px; color: #9ab7f5; display: inline"> > </view>
          </view>
          <link-button @tap="closeTopForm('week')" mode="text" class="text-button" style="font-size: 13px; color: #333333">本周未扫码</link-button>
        </view>
      </view>
      <view class="month-rids" style="width: 105px; height: 105px; background: linear-gradient(#abc5ff61 100%, #2f69f82b 100%); border-radius: 50%; position: relative">
        <view
          class="month-rids-linner"
          style="
            text-align: center;
            line-height: 30px;
            width: 85px;
            height: 85px;
            background-color: #ffff;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            justify-content: center;
            flex-direction: column;
          "
        >
          <view>
            <link-button @tap="closeTopForm('month')" mode="text" class="num-button" style="display: inline; line-height: 20px; font-size: 20px; color: #2f69f8">
              {{this.monthUnScan.cnt || 0 }}
            </link-button>
            <view class="unfinished-form-text" style="margin-left: -3px; font-size: 15px; color: #9ab7f5; display: inline"> > </view>
          </view>
          <link-button @tap="closeTopForm('month')" mode="text" class="text-button" style="font-size: 13px; color: #333333">本月未扫码</link-button>
        </view>
      </view>
    </view>

    <view class="dialog-box" style="text-align: right; margin-top: 10px" v-if="this.finishButtonFlag">
      <link-button
        mode="text"
        @tap="closeTopForm('top')"
        style="display: inline-block; width: 170px; font-family: PingFangSC-Regular; font-size: 13px; color: #2f69f8; line-height: 13px; font-weight: 600"
      >
        点击查看扫码T0P20 >
      </link-button>
    </view>
    <view class="dialog-box" style="text-align: right" v-if="!this.finishButtonFlag">
      <link-icon icon="mp-info-lite" status="primary" class="dialog-icon" style="display: inline-block; vertical-align: baseline; margin-right: -5px; color: #999999" />
      <link-button
        mode="text"
        @tap="() => {}"
        style="display: inline-block; width: 108px; height: 12px; font-family: PingFangSC-Regular; font-size: 12px; color: #999999; line-height: 12px; margin-right: 12px; font-weight: 400"
      >
        点击查看客户明细!
      </link-button>
    </view>
    <link-dialog v-model="openFlag" :title="topName" noPadding width="96vw" :enableScroll="false">
      <view class="top-form" style="width: 100%; padding: 12px 12px 25px 12px">
        <link-button @tap="closeTopForm()" mode="text" style="position: absolute; font-size: 35px; color: #999999; top: 0px; right: 6px; transform: rotate(-315deg)">+</link-button>
        <scroll-view scroll-y="true" :style="{'height': 'calc(60vh - 44px)'}">
          <view :class="`swicthTag ${finishButtonFlag ? '' : ' display-none'}`" style="display: flex; justify-content: space-between; height: 25px; padding-top: 8px">
            <link-button
              :class="`${this.dateRangFlag == 1 ? 'bag-blue' : ' '}`"
              block
              :selected-flag="dateRangFlag == 1"
              @tap="changgeDateRangFlag(1)"
              mode="text"
              style="width: 24%; display: block; background: #6e97fccc; color: white; height: 23px; margin: 0px"
            >周</link-button
            >
            <link-button
              :class="`${this.dateRangFlag == 2 ? 'bag-blue' : ' '}`"
              block
              :selected-flag="dateRangFlag == 2"
              @tap="changgeDateRangFlag(2)"
              mode="text"
              style="width: 24%; display: block; background: #6e97fccc; color: white; height: 23px; margin: 0px"
            >月</link-button
            >
            <link-button
              :class="`${this.dateRangFlag == 3 ? 'bag-blue' : ' '}`"
              block
              @tap="changgeDateRangFlag(3)"
              mode="text"
              style="width: 24%; display: block; background: #6e97fccc; color: white; height: 23px; margin: 0px"
            >季</link-button
            >
            <link-button
              :class="`${this.dateRangFlag == 4 ? 'bag-blue' : ' '}`"
              block
              @tap="changgeDateRangFlag(4)"
              mode="text"
              style="width: 24%; display: block; background: #6e97fccc; color: white; height: 23px; margin: 0px"
            >年</link-button
            >
          </view>
          <view class="cutting-line" style="margin-top: 3px; height: 1px; background: #d9d9d9; margin-bottom: 13px"></view>
          <view v-if="finishButtonFlag">
            <view v-if="topData[0].length !== 0">
              <view class="form-title">
                <view class="customer"> 排行 </view>
                <view class="customer" style="flex: 2"> 荐酒员 </view>
                <view class="customer" style="flex: 2"> 客户编码 </view>
                <view class="customer" style="flex: 3"> 客户名称 </view>
                <view class="customer"> 扫码数(瓶) </view>
              </view>
              <view class="form-row" v-for="(item, index) of topData[0]" :key="index">
                <view class="customer">{{ index + 1 }}</view>
                <view class="customer" style="flex: 2">{{ item.promoterName }} </view>
                <view class="customer" style="flex: 2">{{ item.acctCode }}</view>
                <view class="customer" style="flex: 3"> {{ item.acctName }}</view>
                <view class="customer"> {{ item.amount }}</view>
              </view>
            </view>
            <view style="text-align: center" v-else> 暂无数据 </view>
          </view>
          <view v-else>
            <view v-if="showForm">
              <view class="form-title">
                <view class="customer"> 客户编码 </view>
                <view class="customer"> 客户名称 </view>
                <view class="customer"> 客户等级 </view>
              </view>
              <view class="form-row" v-for="(item, index) of (topType==='week'?weekUnScan.list:monthUnScan.list)" :key="index">
                <view class="customer">{{ item.acctCode}}</view>
                <view class="customer">{{ item.acctName}}</view>
                <view class="customer">{{ item.acctLevel|lovFilter(lovArr) }}</view>
              </view>
            </view>
            <view style="text-align: center" v-else> 暂无数据 </view>
          </view>
        </scroll-view>
      </view>
    </link-dialog>
    <view class="bg-dialog-model" v-show="openFlag"></view>
  </link-page>
</template>
<script>
import SelectButton from "../../components/select-button";
import LineTitle from "../../../../lzlj/components/line-title";
import Taro from "@tarojs/taro";
export default {
  name: "terminal-recommender-drinkser",
  components: {LineTitle, SelectButton},
    props:{
        userType: {
            type: String
        },
        orgText: {
            type: String,
            default: '选择组织'
        },
        orgCompanyCode:{
            type: String,
            default: ''
        },
        orgId: {
            type: String,
            default: ''
        }
    },
  data() {
    const userInfo = Taro.getStorageSync('token').result;
    return {
      loadTop: false, // 是否加载top20数据
      lovArr: [], // 父值列表为BRAND_COM_NAME，父值为当前用户brandCompanyCode，值列表为ACCT_LEVEL的值
      firstLoad: true, // 首次加载未扫码信息
      finishButtonFlag: true, //控制按钮样式切换
      weekDataOption: null, //本周未扫码饼图数据
      monthDataOption: null, //本月未扫码饼图数据
      topType: 'week', //top弹窗标题
      finishFormData: null, //本周未扫码饼图数据
      openFlag: false, //top弹窗显示开关
      topData: [[]], //top20排名数据
      restaurantTotalAmt: null, //餐饮终端数
      registerTerminalAmt: null, //注册终端数
      winePromoterAmt: null, //荐酒员总数
      userInfo,    //用户信息
      actTerminalCntArry: [], //活跃终端数组
      actWinePromoterCntArry: [], //活跃荐酒员数组
      winePromoterScanCntArry: [], //荐酒员扫码数组
      weekTopData: [], //TOP周数据数组
      monthTopData: [], //TOP月数据数组
      quarterTopData: [], //TOP季度数据数组
      yearTopData: [], //TOP年数据数组
      positionType: null, //职位信息
      dateRangFlag: 1, //切换时间
      weekUnScan: {}, //周未扫码详情数据
      monthUnScan: {}, //月未扫码详情数据
      orgBox: false, //判断是否是经理/内勤
      loadingFlag: false, //图表组件是否加载表示
    };
  },
  computed: {
    topName(){
      return this.topType === 'week'? '本周未扫码客户详情' : this.topType === 'top'? '扫码排行TOP20': this.topType === 'month'? '本月未扫码客户详情' : '';
    },
    showForm(){
      if(!this.topType || this.topType === 'top'){
        return false
      }else{
        return this[`${this.topType}UnScan`].list && this[`${this.topType}UnScan`].list.length > 0?true:false
      }
    }
  },
  methods: {
      async changeOrg(item) {
          if(Object.keys(item).length === 0)return;
          this.orgText = item.text;
          this.orgId = item.id;
      },
      /**
       * 选择组织
       * <AUTHOR>
       * @date 2022年6月28日
       */
      gotoOrg(){
          this.$nav.push('/pages/echart/lzlj/role-board/role-org-page.vue',{source:'drinkser'})
      },
    /**
     * @createdBy  刘怡
     * @date  2022/6/27
     * @para
     * @description 切换filish按钮样式
     */
    finishChangge() {
      this.finishButtonFlag = !this.finishButtonFlag;
      if (!this.finishButtonFlag && this.firstLoad) {
        this.firstLoad = false;
        this.getUnScanCodeData();
      }
    },
    /**
     * @createdBy  刘怡
     * @date  2022/6/29
     * @para
     * @description 切换Dateswicth 按钮
     */
    changgeDateRangFlag(dateRange) {
      this.dateRangFlag = dateRange;
      switch (dateRange) {
        case 1:
          this.topData = this.weekTopData;
          break;
        case 2:
          this.topData = this.monthTopData;
          break;
        case 3:
          this.topData = this.quarterTopData;
          break;
        case 4:
          this.topData = this.yearTopData;
          break;
      }
    },
    /**
     * @createdBy  刘怡
     * @date  2022/6/27
     * @para
     * @description 扫码概览tag下  echart组件的数据，配置可以参照echart 官方文档
     */
    draw() {
      this.loadingFlag = true;
      this.finishFormData = {
        //三种图例的颜色配置
        color: [
          '#C079ED',
          {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#C9E9FF", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#98CEFF", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
          {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#6392FA", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#2F69F8", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        ],
        //图例配置
        legend: {
          data: ["活跃终端数", "活跃荐酒员数", "荐酒员扫码数"],
          itemWidth: 20
        },
        grid: {
          left: 55,
          bottom: 20,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        //x轴配置
        xAxis: {
          type: "category",
          data: ["周", "月", "季", "年"],
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          nameLocation: "middle",
          axisTick: {lineStyle: {type: "dotted"}},
          splitLine: {
            lineStyle: {
              type: "dashed",
              width: 1,
            },
          },
          silent: false,
          axisLabel: {
            margin: 12,
            fontSize: 12,
          },
        },
        //数据源
        series: [
          {
            name: "活跃终端数",
            type: "bar",
            data: this.actTerminalCntArry
          },
          {
            name: "活跃荐酒员数",
            type: "bar",
            data: this.actWinePromoterCntArry
          },
          {
            name: "荐酒员扫码数",
            type: "bar",
            data: this.winePromoterScanCntArry
          },
        ],
      };
      this.loadingFlag = false;
    },
    /**
     * @createdBy  刘怡
     * @date   2022/6/27
     * @para
     * @description top20排名的模态弹窗的开关
     * titlestr：标题
     */
    async closeTopForm(type) {
      if(!this.loadTop && type === 'top'){
        await this.getScanCodeData('top');
        this.loadTop = true;
      }
      this.topType = type;
      this.openFlag = !this.openFlag;
      // 跳转至当前展示列表处，防止被echarts遮挡
      /*setTimeout(()=>{
        wx.pageScrollTo({
          selector: '#terminal-recommender-drinkser',
        })
      }, 100)*/
    },
    /**
     * @createdBy  刘怡
     * @date   2022/6/27
     * @para
     * @description 获取荐酒员未扫码概览与详情接口
     *
     */
    async getUnScanCodeData() {
      this.$utils.showLoading();
      try {
        const data = await this.$http.post("export/link/terminalBoard/wine/unScanCode", {
          oauth: "MULTI_POSTN",
          postnId: this.userInfo.postnId,
          role: this.userType,
        });
        if (data) {
          this.unScanCode = data.result;
          this.weekUnScan = {
            cnt: this.unScanCode.weekUnScanAccntCnt,
            list: data.result.weekUnScanDetailList
          }
          this.monthUnScan = {
            cnt: this.unScanCode.monthUnScanAccntCnt,
            list: data.result.monthUnScanDetailList
          }
          this.$utils.hideLoading();
        } else {
          this.$utils.hideLoading();
          this.$showError("终端荐酒员概览信息接口请求失败，请稍后重试！" + data.result);
        }
      } catch (error) {
        this.$utils.hideLoading();
        this.$showError("终端荐酒员概览信息接口请求失败，请稍后重试！" + error.errMsg);
      }
    },
    /**
     * @createdBy  刘怡
     * @date   2022/6/27
     * @para
     * @description 请求荐酒员扫码排行详情接口数据
     *
     */
    async getScanCodeData(type) {
      this.$utils.showLoading();
      let param;
      if (this.orgBox) {
        param = {
          oauth: "MULTI_ORG",
          orgId: this.orgId,
        };
      } else {
        param = {
          oauth: "MULTI_POSTN",
          postnId: this.userInfo.postnId,
        };
      }
      param.queryType = type?'RANK':'AMOUNT'; // 查询图表数据/排行榜信息
      param.role = this.userType;
      if(this.userInfo.coreOrganizationTile.brandCompanyCode){
        param.brandComCode = this.orgCompanyCode
      }
      try {
        const data = await this.$http.post("export/link/terminalBoard/wine/scanCode", param);
        if (data.success) {
          this.$utils.hideLoading();
          if(Object.keys(data.result).length < 1)return;
          let arr = ['week', 'month', 'quarter', 'year'];
          //柱状图数据
          if(type){
            arr.forEach((item) => {
              this[`${item}TopData`].push(data.result[`${item}ScanDetailSort`])
            });
            this.topData = this.weekTopData;
          }else{
              this.actTerminalCntArry = [];
              this.actWinePromoterCntArry = [];
              this.winePromoterScanCntArry = [];
            arr.forEach((item) => {
              this.actTerminalCntArry.push(data.result[`${item}ActTerminalCnt`]);
              this.actWinePromoterCntArry.push(data.result[`${item}ActWinePromoterCnt`]);
              this.winePromoterScanCntArry.push(data.result[`${item}WinePromoterScanCnt`]);
            });
          }
        } else {
          this.$utils.hideLoading();
          this.$showError("查询荐酒员扫码概览与排行详情接口，请稍后重试！" + data.result);
        }
      } catch (error) {
        this.$utils.hideLoading();
        this.$showError("查询荐酒员扫码概览与排行详情接口，请稍后重试！" + error.errMsg);
      }
    },
    /**
     * @createdBy  刘怡
     * @date   2022/6/27
     * @para
     * @description 请求荐酒员扫码概览接口数据
     *
     */
    async getOverviewData() {
      this.$utils.showLoading();
      let param;
      if (this.orgBox) {
        param = {
          oauth: "MULTI_ORG",
          orgId: this.orgId,
          role: this.userType
        };
      } else {
        param = {
          oauth: "MULTI_POSTN",
          postnId: this.userInfo.postnId,
          role: this.userType
        };
      }
      try {
        const data = await this.$http.post("export/link/terminalBoard/wine/overview", param);
        if (data.success) {
          this.restaurantTotalAmt = data.result.restaurantTotalAmt;
          this.registerTerminalAmt = data.result.registerTerminalAmt;
          this.winePromoterAmt = data.result.winePromoterAmt;
          this.$utils.hideLoading();
        } else {
          this.$utils.hideLoading();
          this.$showError("查询荐酒员扫码概览与排行详情接口，请稍后重试！" + data.result);
        }
      } catch (error) {
        this.$utils.hideLoading();
        this.$showError("查询荐酒员扫码概览与排行详情接口，请稍后重试！" + error.errMsg);
      }
    },
   async orgBackGetInfo(){
       this.finishFormData = null;
        await this.getScanCodeData();
        await this.getOverviewData();
        this.draw();
    }
  },
  filters: {
    lovFilter(val, arr){
      let data= arr.filter((item) => {
        return item.val === val;
      })
      return data.length > 0 ? data[0].name : val;
    }
  },
  async created() {
    //获取用户信息
    const userInfoData = await this.$taro.getStorageSync("token").result;
    this.positionType = userInfoData.positionType;
    this.userInfo = userInfoData;
    if (this.userInfo.positionType !== "Salesman" && this.userInfo.positionType !== "SalesSupervisor") {
      this.orgBox = true;
    }
    // this.orgId = this.userInfo.orgId;
    // this.orgText = this.userInfo.orgName;
    let param = { type: 'ACCT_LEVEL', parentType: 'BRAND_COM_NAME', parentVal: userInfoData.coreOrganizationTile.brandCompanyCode};
    this.lovArr = await this.$lov.getLovByParentTypeAndValue(param);
    await this.getScanCodeData();
    await this.getOverviewData();
    this.draw();
  },
  watch: {
    orgId() {
        this.orgBackGetInfo();
    }
  },
};
</script>

<style lang="scss">
.terminal-recommender-drinkser{
  padding: 16px 12px;
  .bag-blue {
    background-color: #2f69f8 !important;
    color: #ffffff !important;
    font-weight: 1000 !important;
  }
  .display-none {
    display: none !important;
  }
  .form-title, .form-row{
    display: flex;
    min-height: 72px;
    background: #c8d7fa;
    border: 2px solid #e6eaf4;
    font-family: PingFangSC-Medium;
    color: #333333;
    letter-spacing: 0;
    text-align: center;
    line-height: 24px;
    font-weight: 1000;
    font-size: 24px;
    justify-content: start;
    flex-direction: row;
    .customer{
      padding: 24px 0;
      text-align: center;
      border-right: 2px solid rgba(230, 234, 244, 1);
      flex: 1
    }
  }
  .form-row{
    background-color: #ffffff;
  }
  .bg-dialog-model{
    width: 100%;
    height: 100vh;
    background: #FFFFFF;
  }
}
</style>
