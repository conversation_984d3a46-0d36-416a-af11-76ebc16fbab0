<!--
@file 配额执行情况
<AUTHOR>
@Created 2022/7/11
-->
<template>
  <link-page class="quota-enforcement" style="padding-left: 12px; padding-top: 16px; padding-bottom: 5px">
    <line-title title="配额执行情况" style="margin-left: 0px; padding-top: 0px"></line-title>
    <scroll-view scroll-x="true" class="scroll-view-data"  v-show='!loadingShow'>
      <view style="font-family: PingFangSC-Medium; font-size: 24rpx; color: #999999; font-weight: 500; text-align: right">
        <view style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background: #2f69f8"> </view>
        <view style="display: inline-block; margin-right: 24px"> 配额执行/件 </view>
      </view>
      <view>
        <link-echart :force-use-old-canvas='false':option="formData" class="finish-form-total" :loading="loadingFlag" />
      </view>
    </scroll-view>
    <view v-show='loadingShow' class="load-box">
        <link-loading class="load" type="gamma"/>
    </view>
  </link-page>
</template>

<script>
import LineTitle from "../../../../lzlj/components/line-title";
export default {
  name: "quota-enforcement",
  components: {LineTitle},
  data() {
    return {
        loadingShow: false,
      userInfo: {}, //用户信息
      formData: {},
      quataDataArry: [], //配额执行情况数据
      orgBox: false,
      loadingFlag: false, //图表组件是否加载表示
    };
  },
  props: {
      userType: {
        type: String
      },
      firstLoading:{
          type: Boolean,
          default: false
      },
      orgText: {
          type: String,
          default: '选择组织'
      },
      orgId: {
          type: String,
          default: ''
      }
  },
    watch:{
        firstLoading(newVal, oldVal) {
          this.getQuataData();
        },
        // orgId() {
        //     this.getQuataData();
        // }
    },
  methods: {
    drow() {
      this.loadingFlag = true;
      const max = Math.max(...this.quataDataArry); //最大值为边界
      let min = Math.min(...this.quataDataArry);
      min = min < 0 ? min : 0; //为负数坐标为负，大于0原点坐标为0
      this.formData = {
        color: ["#2F69F8"],

        radar: {
          axisName: {
            show: true,
            fontSize: 10
          },
          indicator: [
            {name:  `${this.quataDataArry[0]}\n配额申请数`, max: max, min: min, color: "#000000"},
            {name: `${this.quataDataArry[1]}\n\n配额余额`, max: max, min: min, color: "#000000"},
            {name: `${this.quataDataArry[2]}\n配额执行数`, max: max, min: min, color: "#000000"},
            {name: `${this.quataDataArry[3]}\n配额占用数`, max: max, min: min, color: "#000000"},
            {name: `${this.quataDataArry[4]}\n\n库存余额`, max: max, min: min, color: "#000000"},
          ],
          splitNumber: 3,
          radius: "62%",
          nameGap: 15,
          splitArea: {
            show: true,
            areaStyle: {
              color: ["#ffffff"],
            },
          },
          splitLine: {
            lineStyle: {
              color: "#E6EAF4",
            },
          },
          axisLabel: {show: false},
        },
        series: [
          {
            type: "radar",
            data: [
              {
                value: this.quataDataArry,
                areaStyle: {
                  color: "#2F69F8",
                }
              },
            ],
          },
        ],
      };
      this.loadingFlag = false;
    },
    /**
     * @createdBy  刘怡
     * @date   2022/6/27
     * @para
     * @description 获取配额执行情况接口数据
     *
     */
    async getQuataData(orgId) {
      this.loadingShow = true
      let param = {};
      if (this.orgBox) {
        param = {
          oauth: "MULTI_ORG",
          orgId: orgId ? orgId : this.orgId,
        };
      } else {
        param = {
          oauth: "MULTI_POSTN",
          postnId: this.userInfo.postnId,
        };
      }
      param.role = this.userType;
      try {
        const data = await this.$http.post("export/link/terminalBoard/quota/execute", param);
        if (data) {
            this.quataDataArry = [data.result.quotaApplyAmt, data.result.quotaRmdAmt, data.result.quotaExecuteAmt, data.result.quotaOccupyAmt, data.result.stockRmdAmt]; //清空之前的数据
          this.drow();
          this.loadingShow = false
        } else {
          this.loadingShow = false
          this.$showError("查询配额执行情况失败，请稍后重试：" + data.result);
        }
      } catch (error) {
        this.loadingShow = false
        this.$showError("查询配额执行情况出错，请稍后重试！");
      }
    },
  },
  async created() {
    const userInfoData = await this.$taro.getStorageSync("token").result;
    this.userInfo = userInfoData;
    if (this.userType !== "Salesman" && this.userType !== "SalesSupervisor") {
      this.orgBox = true;
    }
  },
};
</script>

<style lang="scss">
    .quota-enforcement{
        .load-box{
            text-align: center;
            height: 200px;
            line-height: 200px;
            padding: 80px 0;
            .load{
                font-size: 160px;
                color: #1F74FF;
            }
        }
    }
    
</style>
