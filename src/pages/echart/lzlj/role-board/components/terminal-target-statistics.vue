<!--
@file 124目标达成
<AUTHOR>
@Created 2022/10/8
-->
<template>
    <link-page class="terminal-target-statistics" style="padding-left: 12px; padding-top: 16px; padding-bottom: 5px">
        <line-title title="124目标达成" style="margin-left: 0px; padding-bottom: 10px"></line-title>
        <scroll-view scroll-x="true">
            <view class="top-title">
                <view class="title-blue basic-title">124目标</view>
                <view class="title-green basic-title">124达成</view>
            </view>
             <link-echart :force-use-old-canvas='false' :option="formData" :height=" 240 + 'px'" :loading="loadingFlag" />
            <view class="dialog-box">
                <link-icon icon="mp-info-lite" status="primary" class="dialog-icon" />
                <link-button class="dialog-button" mode="text" @tap="() => {}" disabled> 124目标达成率=达成终端数量/目标终端数量! </link-button>
            </view>
        </scroll-view>
    </link-page>
</template>

<script>
import LineTitle from "../../../../lzlj/components/line-title";
export default {
    name: "terminal-target-statistics",
    components: {LineTitle},
    data() {
        return {
            showArr: '[58929586649432064, 58929392503488512, 58929495997939712, 362323365614457283, 58933296855252992, 455785487879303220, 145202148358501210, 451077192125577418]',
            userInfo: {}, //用户信息
            formData: {},
            reportInfo: {},
            loadingFlag: false, //图表组件是否加载表示
            dialogFlag: false,
            cfgPropertyObj: ''
        };
    },
    props: {
        userType: {
            type: String
        },
        orgCompanyCode: {
            type: String,
            default: ''
        },
        orgId: {
            type: String,
            default: ''
        },
        orgInfo: {
            type: Object,
            default: null
        }
    },
    watch: {
        // orgId() {
        //     this.searchData(this.orgInfo);
        // }
    },
    methods: {
        getSeries(data, type){
            const target = data.map((item, index) => {
                const arr = new Array(data.length);
                arr.splice(index, 1, item);
                return arr;
            })
            return [data, ...target].map((item, index) => {
                return {
                    type: 'radar',
                    symbol: index === 0 ? 'circle' : 'none',
                    symbolSize: 6,
                    itemStyle: {color: index === 0 ? type : 'transparent'},
                    lineStyle: {color: index === 0 ? type : 'transparent'},
                    tooltip: {
                        show: index === 0 ? false : true,
                        formatter: () => {
                            return`目标等级:${this.reportInfo.indicatorArr[index - 1]}\n目标数量:${this.reportInfo.targetArr[index - 1]}\n达成数据:${this.reportInfo.reachArr[index - 1]}\n达成率:${this.reportInfo.achievementRateArr[index - 1]}`
                        }
                    },
                    z: index === 0 ? 1 : 2,
                    data: [item]
                }
            })
        },
        draw() {
            const max = Math.max(...this.reportInfo.reachArr, ...this.reportInfo.targetArr); //最大值为边界
            let min = Math.min(...this.reportInfo.reachArr, ...this.reportInfo.targetArr);
            min = min < 0 ? min : 0; //为负数坐标为负，大于0原点坐标为0
            this.formData = {
                tooltip: {},
                radar: {
                    axisName: {
                        show: true,
                        fontSize: 10
                    },
                    indicator: this.reportInfo.indicatorArr.map((item, index) => {
                        return {
                            name: item + '\n' + this.reportInfo.achievementRateArr[index],
                            max: max,
                            min: min,
                            color: "#000000"
                        }
                    }),
                    splitNumber: 3,
                    radius: "62%",
                    nameGap: 15,
                    splitArea: {
                        show: true,
                        areaStyle: {
                            color: ["#ffffff"],
                        },
                    },
                    splitLine: {
                        lineStyle: {
                            color: "#E6EAF4",
                        },
                    },
                    axisLabel: {show: false},
                },
                series: [
                    ...this.getSeries(this.reportInfo.targetArr, '#4375fc'),
                    ...this.getSeries(this.reportInfo.reachArr, '#2bd039')
                ]
            };
            this.loadingFlag = false;
        },
        /**
         * @createdBy  康丰强
         * @date   2022/10/10
         * @description 根据角色类型获取对应的安全性
         */
        getOauth() {
            return this.userType !== "Salesman" && this.userType !== "SalesSupervisor" ? 'MULTI_ORG' : 'MULTI_POSTN'
        },
        async queryCfgPropertyObj() {
            const data = await this.$utils.getCfgProperty('show_company_arr');
            if (data !== 'noMatch') {
                this.cfgPropertyObj = data;
            }
        },
        /**
         * @createdBy  康丰强
         * @date   2022/10/10
         * @description 获取124目标达成数据
         */
        async searchData(orgInfo) {
            this.loadingFlag = true;
            const nowDate = new Date()
            let param = {};
            param = {
                role: this.userType,
                dataSource: 'WeChatWork',
                mdmCompanyCode: orgInfo && orgInfo.brandCompanyCode ? orgInfo.brandCompanyCode : this.orgCompanyCode,
                multiAcctMainFlag: 'Y',
                brandComOrgType: 'BranchCompany',
                oauth: this.getOauth(),
                fiscalYear: nowDate.getMonth() >= 10 ? nowDate.getFullYear() + 1 : nowDate.getFullYear(),
                salesmanRegionId: orgInfo && orgInfo.orgType === 'Region' ? this.orgId : '',
                salesmanAreaId: orgInfo && orgInfo.orgType === 'SalesRegion' ? this.orgId : '',
                salesmanCityId: orgInfo && orgInfo.orgType === 'SalesCity' ? this.orgId : ''
            };
            try {
                const data = await this.$http.post("export/link/terminal124Report/query124GoalOverview", param, {timeout: 60000*3});
                if (data.success) {
                    if(data.rows.length === 0) return
                    this.reportInfo = {
                        reachArr: [],
                        targetArr: [],
                        indicatorArr: [],
                        achievementRateArr: []
                    }
                    data.rows.forEach(item => {
                        this.reportInfo.reachArr.push(item.cumulativeLevelQty)
                        this.reportInfo.targetArr.push(item.planningLevelQty)
                        this.reportInfo.indicatorArr.push(item.planningSalesLevel)
                        this.reportInfo.achievementRateArr.push(item.achievementRate)
                    })
                    this.loadingFlag = false
                        this.draw();
                } else {
                    this.loadingFlag = false
                    this.$showError("查看124目标达成看板失败，请稍后重试：" + data.result);
                }
            } catch (error) {
                this.loadingFlag = false
                this.$showError("查看124目标达成看板出错，请稍后重试！");
            } finally {
                this.loadingFlag = false
            }
        },
    },
    created() {
        this.userInfo = this.$taro.getStorageSync("token").result;
        this.searchData(this.userInfo.coreOrganizationTile);
        this.queryCfgPropertyObj()
    },
};
</script>

<style lang="scss">
.terminal-target-statistics {
    .load-box{
        text-align: center;
        height: 200px;
        line-height: 200px;
        padding: 80px 0;
        .load{
            font-size: 160px;
            color: #1F74FF;
        }
    }
    .top-title {
        font-family: PingFangSC-Medium;
        font-size: 24px;
        color: #999999;
        font-weight: 500;
        display: flex;
        justify-content: space-between;

        .basic-title::before {
            content: ' ';
            display: inline-block;
            vertical-align: middle;
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }

        .title-blue::before {
            background-color: #4375fc;
        }

        .title-green::before {
            background-color: #2bd039;
        }
    }

    .scroll-view-data{
        margin-top: 24px;
        margin-bottom: 24px;
        .select-dimension{
            display: flex;
            margin-left: 24px;
        }
    }

    .dialog-box {
        text-align: right;
        .dialog-icon {
            display: inline-block;
            vertical-align: baseline;
            margin-right: -10px;
            color: #999999;
        }
        .dialog-button {
            display: inline-block;
            height: 24px;
            font-family: PingFangSC-Regular;
            font-size: 24px;
            color: #999999;
            line-height: 24px;
            font-weight: 400;
        }
    }
}
</style>
