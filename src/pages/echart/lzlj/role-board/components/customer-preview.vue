<template>
    <link-page class="customer-preview">
        <view>
            <line-title  title="终端客户概览"></line-title>
            <view style="display: flex;height: 54px;margin-top: 24px;align-items: center;">
                <view  style="display: flex;flex: 1;flex-direction: column;text-align: center;">
                    <view style="flex: 1;font-weight: 600">
                        {{this.textInfo.terminalTotalAmt}}
                    </view>
                    <view style="flex: 1;color:#666666;margin-top: 8px;font-size: 13px">
                        终端总数
                    </view>
                </view>
                <view class="lines"></view>
                <view  style="display: flex;flex: 1;flex-direction: column;text-align: center;">
                    <view style="flex: 1;font-weight: 600">
                        {{this.textInfo.authTerminalAmt}}
                    </view>
                    <view style="flex: 1;color:#666666;margin-top: 8px;font-size: 13px">
                        认证数量
                    </view>
                </view>
                <view class="lines"></view>
                <view  style="display: flex;flex: 1;flex-direction: column;text-align: center;">
                    <view style="flex: 1;font-weight: 600">
                        {{this.textInfo.coreTerminalAmt}}
                    </view>
                    <view style="flex: 1;color:#666666;margin-top: 8px;font-size: 13px">
                        核心数量
                    </view>
                </view>
            </view>
            <view style="margin: 14px 12px;height: 1px;background: #EEF3F5;">
            </view>
            <view>
                <scroll-view scroll-x="true" class="scroll-view-data" >
                    <view class="select-dimension" :style="{marginLeft:isAllType?'24rpx':'-6rpx'}">
                        <select-button :label="isShow ? selectDataShow[pickIndex1] : '分类分布'" v-if="!isShow || isAllType " :selected-flag="overviewParam.type==='Sort'"   @tap="customerChart('Sort')"></select-button>
                        <view class="select-dimension" v-else>
                            <picker  :value="pickIndex1" @change="pickerChange" :range="selectDataShow" >
                                <select-button :label="selectDataShow[pickIndex1]" :showLength='4' :selected-flag="true" downIcon></select-button>
                            </picker>
                        </view>
                        <select-button label="等级分布" :selected-flag="overviewParam.type==='Grade'"  @tap="customerChart('Grade')"></select-button>
                        <select-button label="A&T分布" :selected-flag="overviewParam.type==='AorT'"  @tap="customerChart('AorT')"></select-button>
                        <select-button label="品项分布" :selected-flag="overviewParam.type==='prod'"  @tap="customerChart('prod')" v-if="!isShow && userType == 'HeadLeader'"></select-button>
                    </view>
                </scroll-view>
                <view v-show="!loadingShow">
                    <view class="prod-table" v-show="overviewParam.type === 'prod'"  v-if="!isShow && userType == 'HeadLeader'">
                        <view class="prod-thead">
                            <view class="prod-td">产品业务大类</view>
                            <view class="prod-td">终端数量</view>
                        </view>
                        <view class="prod-tbody">
                            <view v-for="item in terminalProdBoard" class="prod-tr">
                                <view class="prod-td">{{item.target | lovFilter(prodLargeLov)}}</view>
                                <view class="prod-td">{{item.amount}}</view>
                            </view>
                        </view>
                    </view>
                    <link-echart :force-use-old-canvas='false' :loading="terminalOptionFlag" :option="terminalOption" v-show="overviewParam.type !== 'AorT' && overviewParam.type !== 'prod'" :height=" 240 + 'px'"/>
                    <link-echart :force-use-old-canvas='false' :loading="ATOptionFlag" :option="ATOption" v-show="overviewParam.type ==='AorT'" :height=" 240 + 'px'"/>
                </view>
                <view v-show='loadingShow' class="load-box">
                    <link-loading class="load" type="gamma"/>
                </view>
            </view>
        </view>
    </link-page>
</template>

<script>
import LineTitle from "../../../../lzlj/components/line-title";
import SelectButton from "../../components/select-button";
import Taro from "@tarojs/taro";
import {annularChart,doubleDataHistogram} from "../role-echart.utils";
import {barYCategory} from "../../echart.utils";
export default {
    name: "customer-preview",
    components: {SelectButton, LineTitle},
    props:{
        userType: {
            type: String
        },
        orgText: {
            type: String,
            default: '选择组织'
        },
        orgCompanyCode: {
            type: String,
            default: ''
        },
        orgId: {
            type: String,
            default: ''
        },
        // 是否大成特殊公司
        isShow:{
            type: Boolean,
            default: false
        },
        // 筛选5码参数
        threeParams:{
            type: Object,
            default(){
                return {}
            }
        }
    },
    data(){
        const userInfo = Taro.getStorageSync('token').result;
        return{
            loadingShow: false, //显示局部加载
            isAllType: true, //是否查询全部分类
            pickIndex1: 0,
            textInfo:{
                terminalTotalAmt:0,
                authTerminalAmt:0,
                coreTerminalAmt:0
            },
            overviewParam:{
              type:'Sort'
            },
            capacityGradeOption:null,
            ATOption: null,
            ATOptionFlag:true,
            terminalOption:null,
            terminalOptionFlag:true,
            userInfo,
            orgBox:false,
            terminalProdBoard: {},
            prodLargeLov: [],
            selectDataShow:['分类分布']
        }
    },
    async created() {
        if(this.userType !=='Salesman' && this.userType !=='SalesSupervisor'){
            this.orgBox = true;
        }
       await this.getCustomerInfo();
       await this.customerChart();
       this.prodLargeLov = await this.$lov.getLovByType('PROD_BUS_L_CLASS')
    },
    watch: {
        // orgId() {
        //     this.init()
        // },
        threeParams(val){
            if(val){
                this.isAllType = true
                this.pickIndex1 = 0
                this.selectDataShow = ['分类分布']
			}
        }
    },
    filters: {
        lovFilter(val, arr){
            if(val === 'other') return '其他'
            const data= arr.filter((item) => {
                return item.val === val;
            })
            return data.length > 0 ? data[0].name : val;
        }
    },
    methods:{
        pickerChange(val){
            const index = Number(val.detail.value)
            if(index){
                const type = this.selectData[index-1].value
                this.pickIndex1 = index
                this.customerChart('Sort',type)
            }else{
                this.pickIndex1 = 0
                this.customerChart('Sort')
            }
        },
        /**
         * 切换对应条件初始化数据
         * <AUTHOR>
         * @date 2023/11/20
         */
        init(orgId){
            this.getCustomerInfo(orgId)
            this.overviewParam.type = 'Sort'
            this.customerChart('', '', orgId);
        },
        async getProdItemData(orgId) {
            let param;
            if (this.userType !== "Salesman" && this.userType !== "SalesSupervisor") {
                param = {
                    oauth: "MULTI_ORG",
                    orgId: orgId ? orgId : this.orgId,
                };
            } else {
                param = {
                    oauth: "MULTI_POSTN",
                    postnId: this.userInfo.postnId,
                };
            }
            param.role = this.userType;
            const params = {
                ...param,
                ...this.threeParams,
            }
            this.loadingShow = true
            const {result} = await this.$http.post('/export/link/terminalBoard/acct/itemClass', params)
            this.terminalProdBoard = result
            this.loadingShow = false
        },
        /**
         * 选择组织
         * <AUTHOR>
         * @date 2022年6月28日
         */
        gotoOrg(){
            this.$nav.push('/pages/echart/lzlj/role-board/role-org-page.vue',{source:'customer'})
        },
        async customerChart(type,queryType, orgId){
            if(this.$utils.isNotEmpty(type)){
                this.overviewParam.type = type;
                if(type !== 'Sort'){
                    this.isAllType = true
                }else{
                    if(this.pickIndex1){
                        queryType = this.selectData[this.pickIndex1-1].value
                        this.isAllType = false
                    }
                }
            }
            try{
                if(this.overviewParam.type === 'prod') {
                    await this.getProdItemData(orgId)
                } else if (this.overviewParam.type === 'AorT') {
                    await this.AOrTBar(orgId);
                } else {
                    await this.terminalNumberPie(queryType, orgId);
                }
            }catch (e){
                this.$utils.hideLoading();
            }
        },
        /**
         * @createdBy  吕志平
         * @date  2022年6月21日
         * @description 分类分布环形图
         */
        async terminalNumberPie(queryType, orgId){
            let numberData = await this.terminalNumPieData(queryType, orgId);
            this.terminalOption = null;
            this.terminalOptionFlag = true;
            let seriesData = []
            let selectData = []
            let selectDataShow = ['分类分布']
            let totalNum  = 0
            let lovType = 'ACCNT_CATEGORY'
            if(this.overviewParam.type === 'Grade'){
                lovType = 'ACCT_LEVEL'
            }
            if(queryType){
                lovType = 'SUB_ACCT_TYPE'
            }
            let total = 0
            // 国窖数据处理
            if(this.orgCompanyCode === '5600' && this.overviewParam.type === 'Grade'){
                const hasOther = numberData.find(i=>{
                    return i.target === 'other'
                })
                if(!hasOther){
                    numberData.push({target: 'other', amount: 0})
                }
                const newData = numberData.filter(i=>{
                    return i.target === 'other' || i.target === 'belowB'
                })
                newData.forEach((ites,j)=>{
                    total = total + Number(ites.amount)
                })
                numberData.forEach((ite,ind)=>{
                    if(ite.target === 'other'){
                       ite.amount = total
                    }else if(ite.target === 'belowB'){
                        ite.amount = 0
                    }
                })
            }else if(this.orgCompanyCode !== '5600' && this.overviewParam.type === 'Grade'){
                //非国窖数据移除other数据显示
                const hasOther = numberData.find((i) => i.target === 'other')
                if(hasOther){
                    numberData = numberData.filter((i) => i.target !== 'other')
                }
            }
            for(let i in numberData){
                if(Number(numberData[i].amount)>0){
                    let name = numberData[i].target === 'other'?`${(this.orgCompanyCode === '5600' && this.overviewParam.type === 'Grade')?'B':'A'}以下`:await this.$lov.getNameByTypeAndVal(lovType, numberData[i].target);
                    seriesData.push({
                        value: Number(numberData[i].amount),
                        name: name ? name : numberData[i].target
                    })
                    selectData.push({
                        value: numberData[i].target,
                        name: name ? name : numberData[i].target
                    })
                    selectDataShow.push(name)
                    totalNum += Number(numberData[i].amount)
                }
            }

            let totalSeriesData = [{value: totalNum, name: this.pickIndex1 > 0 && this.overviewParam.type === 'Sort' ? '终端数量' : '终端总数'}]
            if(!queryType && this.overviewParam.type == 'Sort'){
               this.selectDataShow = selectDataShow
               this.selectData = selectData
               this.isAllType = false
               // this.pickIndex1 = 0
            }
            this.terminalOption = echartInitConfig=>annularChart(echartInitConfig, seriesData, totalSeriesData,['40%', '70%'],'40%',[],225,'valuePercent','','',20, 'largeSize', 24);

            this.terminalOptionFlag = false;
        },
        /**
         * @createdBy  吕志平
         * @date  2022年6月28日
         * @description 终端客户概览信息
         */
        async getCustomerInfo(orgId){
            let param = { }
            if(this.orgBox){
                param = {
                    oauth: 'MULTI_ORG',
                    orgId: orgId ? orgId : this.orgId,
                    role: this.userType,
                }
            }else{
                param = {
                    oauth: 'MULTI_POSTN',
                    postnId: this.userInfo.postnId,
                    role: this.userType,
                }
            }
            param.sapCompCode = this.orgCompanyCode;
            const params = {
                ...param,
                ...this.threeParams,
            }
            try{
                this.loadingShow = true
                const data = await this.$http.post('export/link/terminalBoard/acct/overview',params);
                if(data.success){
                    this.textInfo.terminalTotalAmt = data.result.terminalTotalAmt;  // 终端总数
                    this.textInfo.authTerminalAmt = data.result.authTerminalAmt;   // 认证终端总数
                    this.textInfo.coreTerminalAmt = data.result.coreTerminalAmt;   // 核心终端总数
                    this.$bus.$emit("totalObj", {
                      terminalTotalAmt: data.result.terminalTotalAmt,
                      coreTerminalAmt: data.result.coreTerminalAmt
                    });
                    this.loadingShow = false
                }else{
                    this.$showError('获取终端预览数据失败');
                    this.loadingShow = false
                }
            }catch(e){
                this.$showError('获取终端预览数据异常', JSON.stringify(e));
                this.loadingShow = false
            }
        },
        /**
         * @createdBy  吕志平
         * @date  2022年6月28日
         * @description 图表处理接口
         */
        async terminalNumPieData(queryType = null, orgId){
            let param = { }
            if(this.orgBox){
                param = {
                    oauth: 'MULTI_ORG',
                    orgId: orgId ? orgId : this.orgId,
                    reportType: this.overviewParam.type,
                    mdmCompanyCode: this.orgCompanyCode,
                    role: this.userType
                }
            }else{
                param = {
                    oauth: 'MULTI_POSTN',
                    postnId: this.userInfo.postnId,
                    reportType: this.overviewParam.type,
                    mdmCompanyCode: this.orgCompanyCode,
                    role: this.userType
                }
            }
            if(queryType){
                param.reportType = 'SubSort'
                param.queryType = queryType
            }
            const params = {
                ...param,
                ...this.threeParams,
            }
            this.loadingShow = true
            let data = await this.$http.post('export/link/terminalBoard/acct/distribute', params);
            if(!data.success){
                this.loadingShow = false
                this.$showError('获取终端数据失败');
                return
            }
            this.loadingShow = false
            return data.result
        },
        /**
         * @createdBy  吕志平
         * @date  2022年6月28日
         */
        async AOrTBar(orgId){
            let data = await this.terminalNumPieData(null, orgId);
            let levelData = data
            this.ATOption = null;
            this.ATOptionFlag = true;
            let seriesDataOne = [{},{},{},{},{},{}];
            let seriesDataTwo = [{},{},{},{},{},{}];
            for(let i in levelData){
                switch (levelData[i].target) {
                    case '5A': { seriesDataOne[0]={
                                    value: Number(levelData[i].amount),
                                    name: '5A&5T'
                                };break;}
                    case '4A': { seriesDataOne[1]={
                        value: Number(levelData[i].amount),
                        name: '4A&4T'
                    };break;}
                    case '3A': { seriesDataOne[2]={
                        value: Number(levelData[i].amount),
                        name: '3A&3T'
                    };break;}
                    case '2A': { seriesDataOne[3]={
                        value: Number(levelData[i].amount),
                        name: '2A&2T'
                    };break;}
                    case 'A': { seriesDataOne[4]={
                        value: Number(levelData[i].amount),
                        name: '1A&1T'
                    };break;}
                    case 'A以下':{ seriesDataOne[5]={
                        value: Number(levelData[i].amount),
                        name: 'A以下'
                    };break;}
                    case '5T': { seriesDataTwo[0]={
                        value: Number(levelData[i].amount),
                        name: '5A&5T'
                    };break;}
                    case '4T': { seriesDataTwo[1]={
                        value: Number(levelData[i].amount),
                        name: '4A&4T'
                    };break;}
                    case '3T': { seriesDataTwo[2]={
                        value: Number(levelData[i].amount),
                        name: '3A&3T'
                    };break;}
                    case '2T': { seriesDataTwo[3]={
                        value: Number(levelData[i].amount),
                        name: '2A&2T'
                    };break;}
                    case 'T': { seriesDataTwo[4]={
                        value: Number(levelData[i].amount),
                        name: '1A&1T'
                    };break;}
                }
            }
            this.ATOption = echartInitConfig=>doubleDataHistogram(echartInitConfig,seriesDataOne,seriesDataTwo,false,{}, {}, '客户等级', '容量等级');
            this.ATOptionFlag = false;
        },
    }
}
</script>

<style lang="scss">
.customer-preview{
    .load-box{
        text-align: center;
        height: 200px;
        line-height: 200px;
        padding: 80px 0;
        .load{
            font-size: 160px;
            color: #1F74FF;
        }
    }
    .lines{
        width: 2px;
        height: 40px;
        background-color: #DDDDDD;
    }
    .scroll-view-data{
        margin-top: 24px;
        margin-bottom: 24px;
        .select-dimension{
            display: flex;
            margin-left: 24px;
            justify-content: space-around;
        }
    }
    .prod-table {
        font-size: 24px;
        text-align: center;
        .prod-td {
            flex: 1;
            border: 1px solid rgba(230, 234, 244, 1);
            padding: 20px 0;
        }
        .prod-thead {
            display: flex;
            background: #c8d7fa;
            font-family: PingFangSC-Medium;
            color: #333333;
            font-weight: 500;
        }
        .prod-tbody {
            font-family: PingFangSC-Regular;
            color: #666666;
            font-weight: 400;
            .prod-tr {
                display: flex;
            }
        }
    }
}
</style>
