<template>
    <link-page class="terminal-table">
        <view v-if="!orgFlag">
    <!--标题-->
            <view class="title">
                <view class="textOF topText" >top10</view>
                <view class="textOF titleText" >客户编码</view>
                <view class="textOF titleText" >客户名称</view>
                <view class="textOF titleText" >本月动销</view>
            </view>
    <!-- 行 -->
            <view class='rowBox' >
                <view v-for="(data,index) in rankingList" class="row" >
                    <view class="textOF topText" style="color: #333333">top{{index+1}}</view>
                    <view class="textOF titleText" >{{data.acctCode}}</view>
                    <view class="textOF titleText" >{{data.acctName}}</view>
                    <view class="textOF titleText" style="color: #333333">{{data.curMonthSales}}</view>
                </view>
            </view>
        </view>
        <view v-else>
            <!--标题-->
            <view class="title">
                <view class="textOF topText" >top10</view>
                <view class="textOF titleText" >组织名称</view>
                <view class="textOF titleText" >本月动销/件</view>
            </view>
            <!-- 行 -->
            <view class='rowBox'>
                <view v-for="(data,index) in rankingList" class="row" >
                    <view class="textOF topText" style="color: #333333">top{{index+1}}</view>
                    <view class="textOF titleText" >{{data.orgName}}</view>
                    <view class="textOF titleText" style="color: #333333">{{data.amount}}</view>
                </view>
            </view>
        </view>
    </link-page>
</template>

<script>
export default {
    name: "terminal-table",
    props:{
        rankingList:{
            type:Array,
            default:[]
        },
        orgFlag:{
            type:Boolean,
            default:false
        }
    }
}
</script>

<style lang="scss">
.terminal-table{
    .title{
        background-color: #C8D7FA;
        margin: 0px 24px;
        display: flex;
        text-align: center;
        height: 72px;
        line-height: 72px;
        border: 2px solid rgba(230,234,244,1);
        border-right: 0px;
    }
    .rowBox{
        display: flex;
        flex-direction: column;
        text-align: center;margin: 0px 24px
    }
    .row{
        display: flex;
        font-size: 24px;
        color: #666666;
        border-left: 2px solid rgba(230,234,244,1);
        border-bottom: 2px solid rgba(230,234,244,1);
        .textOF{
            min-height: 40px;
            padding: 10px 0;
        }
    }
    .topText{
        flex: 1;
        border-right: 2px solid rgba(230,234,244,1);
        font-size:24px
    }
    .titleText{
        flex: 2;
        border-right: 2px solid rgba(230,234,244,1);
        font-size:24px;
    }
}
</style>
