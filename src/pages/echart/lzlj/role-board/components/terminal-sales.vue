<template>
    <link-page class="terminal-sales">
        <view>
            <line-title  title="终端动销情况" ></line-title>
            <view class="content">
                <scroll-view scroll-x="true" class="scroll-view-data" >
                    <view class="select-dimension">
                        <select-button label="进销存" v-if="!isShow"  :selected-flag="overviewParam.type==='SALE'"   @tap="customerChart('SALE')"></select-button>
                        <select-button label="动销排名" :selected-flag="overviewParam.type==='SALERANK'"  @tap="customerChart('SALERANK')" v-if="userType !=='HeadLeader'"></select-button>
                        <select-button label="渠道占比" v-if="!isShow || isAllType " :selected-flag="overviewParam.type==='CHANNEL'"  @tap="customerChart('CHANNEL')"></select-button>
                        <view class="select-dimension" v-else>
                            <picker  :value="pickIndex1" @change="pickerChange" :range="selectDataShow" >
                                <select-button :label="selectDataShow[pickIndex1]" :showLength='4' :selected-flag="true" downIcon></select-button>
                            </picker>
                        </view>
                    </view>
                </scroll-view>
                <view v-show='!loadingShow'>
                    <view >
                        <link-echart :force-use-old-canvas='false':option="CHANNELOption" v-show="overviewParam.type ==='CHANNEL'" :height=" 240 + 'px'" />
                        <view style="min-height: 240px;width: 100%" v-show="overviewParam.type ==='SALERANK'" >
                            <terminal-table :org-flag="orgBox" :ranking-list="rankingList"></terminal-table>
                        </view>
                        <link-echart :force-use-old-canvas='false':option="SALEOption" v-show="overviewParam.type ==='SALE'" :height=" 240 + 'px'"/>
                    </view>
                    <view>
                      <view class="tips" v-if="overviewParam.type==='CHANNEL'">
                        <link-icon icon="mp-info-lite" status="primary" class="tips-icon"/>
                       <view class="tips-mess">若某一渠道本月动销为负，则不纳入统计</view>
                      </view>
                    </view>
                </view>

                <view v-show='loadingShow' class="load-box">
                    <link-loading class="load" type="gamma"/>
                </view>
            </view>
        </view>
    </link-page>
</template>

<script>
import LineTitle from "../../../../lzlj/components/line-title";
import SelectButton from "../../components/select-button";
import Taro from "@tarojs/taro";
import {annularChart,doubleDataHistogram} from "../role-echart.utils";
import TerminalTable from "./terminal-table";
export default {
    name: "terminal-sales",
    components: {SelectButton, TerminalTable, LineTitle},
    props:{
        userType: {
          type: String
        },
        firstLoading:{
            type: Boolean,
            default: false
        },
        orgText: {
            type: String,
            default: '选择组织'
        },
        orgId: {
            type: String,
            default: ''
        },
        // 是否大成特殊公司
        isShow:{
            type: Boolean,
            default: false
        },
        orgCompanyCode:{
            type: String,
            default: ''
        },
        // 筛选5码参数
        threeParams:{
            type: Object,
            default(){
                return {}
            }
        }
    },
    data(){
        const userInfo = Taro.getStorageSync('token').result;
        return{
            loadingShow: false,
            isAllType: true,
            selectDataShow: ['渠道占比'],
            pickIndex1: 0,
            textInfo:{
                terminalTotalAmt:0,
                authTerminalAmt:0,
                coreTerminalAmt:0
            },
            overviewParam:{
                type:'SALE'
            },
            rankingList:[],
            SALERANKdata: {},
            capacityGradeOption:null,
            SALEOption: null,
            SALEdata: {},
            CHANNELOption:null,
            CHANNELdata: {},
			SUBCHANNELdata: {},
            userInfo,
            // orgCompanyCode:'',
            orgBox:false
        }
    },
    async created() {
        if(this.userType !=='Salesman' && this.userType !=='SalesSupervisor'){
            this.orgBox = true;
        }
    },
    watch:{
        firstLoading(newVal, oldVal) {
          if(this.isShow){
              if(this.userType !=='HeadLeader'){
                  this.overviewParam.type = 'SALERANK'
                  this.customerChart('SALERANK');
              }else{
                  this.overviewParam.type = 'CHANNEL'
                  this.customerChart('CHANNEL');
              }
          }else{
              this.customerChart('SALE');
          }
        },
        // orgId() {
        //     this.cleanData()
        // },
        threeParams(val){
            if(val){
                this.isAllType = true
                this.pickIndex1 = 0
                this.selectDataShow = ['渠道占比']
            }
        },
        isShow(val){
            if(val){
               this.overviewParam.type = this.userType !=='HeadLeader' ? 'SALERANK' : 'CHANNEL'
            }
        }
    },
    methods:{
        /**
         * 切换对应条件初始化数据
         * <AUTHOR>
         * @date 2023/12/7
         */
        pickerChange(val){
            const index = Number(val.detail.value)
            if(index){
                const type = this.selectData[index-1].value
                this.pickIndex1 = index
                this.customerChart('CHANNEL',type)
            }else{
                this.pickIndex1 = 0
                this.customerChart('CHANNEL')
            }
        },
        /**
         @desc: 清空缓存同时重新查询当前type数据
         @author: wangbinxin
         @date 2022-09-08 15-59
         **/
        cleanData(orgId){
          this.SALERANKdata = {};
          this.SALEdata = {};
          this.CHANNELdata = {};
          this.customerChart('', '', orgId);
        },
        /**
         * 选择组织
         * <AUTHOR>
         * @date 2022年6月28日
         */
        gotoOrg(){
            this.$nav.push('/pages/echart/lzlj/role-board/role-org-page.vue',{source:'sales'})
        },
        async customerChart(type,queryType, orgId){
            if(this.$utils.isNotEmpty(type)){
                this.overviewParam.type = type;
            }else {
                type = this.overviewParam.type;
            }
            if(type !== 'CHANNEL'){
                this.isAllType = true
            }else{
                if(this.pickIndex1){
                    queryType = this.selectData[this.pickIndex1-1].value
                    this.isAllType = false
                }
            }
            try {
                this.loadingShow = true
                if (this.overviewParam.type === 'SALERANK') {
                    this.rankingList = this[type+'data'].load?this[type+'data'].list:await this.terminalNumPieData('', orgId);
                } else if(this.overviewParam.type === 'SALE'){
                    await this.SALEBar(orgId);
                } else if(this.overviewParam.type === 'CHANNEL'){
                    await this.CHANNELPie(queryType, orgId);
                }
                this.loadingShow = false
            }catch (e){
                this.loadingShow = false
            }
        },
        /**
         * @createdBy  吕志平
         * @date  2022年6月21日
         * @description 分类分布环形图
         */
        async CHANNELPie(queryType, orgId){
            let numberData = []
            if(queryType){
                numberData = this.SUBCHANNELdata.type === queryType ? this.SUBCHANNELdata.list : await this.terminalNumPieData(queryType, orgId);
            }else{
                numberData = this.CHANNELdata.load ? this.CHANNELdata.list : await this.terminalNumPieData('', orgId)
            }

            this.CHANNELOption = null
            let seriesData = []
            let totalNum  = 0
            let selectData = []
            let selectDataShow = ['渠道占比']
            let lovType = 'ACCNT_CATEGORY'
            if(queryType){
                lovType = 'SUB_ACCT_TYPE'
            }
            for(let i in numberData){
                if(Number(numberData[i].amount)>0){
                    let name = await this.$lov.getNameByTypeAndVal(lovType, numberData[i].target)
                    if(!name && numberData[i].target === 'other') name = '其他'
                    seriesData.push({
                        value: Number(numberData[i].amount),
                        name: (name ? name : numberData[i].target).padEnd(5) // 5个字符就会换行
                    })
                    selectData.push({
                        value: numberData[i].target,
                        name: name ? name : numberData[i].target
                    })
                    selectDataShow.push(name)
                    totalNum += Number(numberData[i].amount)
                }
            }
            if(!queryType && this.overviewParam.type === 'CHANNEL'){
               this.selectDataShow = selectDataShow
               this.selectData = selectData
               this.isAllType = false
               // this.pickIndex1 = 0
            }
            let pieColor = [
            ];
            var totalSeriesData = [{value: Math.round(totalNum*100)/100, name: '动销总数/件'}]
            this.CHANNELOption = echartInitConfig=>annularChart(echartInitConfig, seriesData, totalSeriesData,['40%', '70%'],'40%',pieColor,225, '','','',20, 'largeSize', 20);
        },
        /**
         * @createdBy  吕志平
         * @date  2022年6月28日
         * @description 图表处理接口
         */
        async terminalNumPieData(queryType, orgId){
            let param = { }
            if(this.orgBox){
                param = {
                    oauth: 'MULTI_ORG',
                    orgId: orgId ? orgId : this.orgId,
                    reportType: this.overviewParam.type,
                    mdmCompanyCode: this.orgCompanyCode,
                    queryType : 'ORG'
                }
            }else{
                param = {
                    oauth: 'MULTI_POSTN',
                    postnId: this.userInfo.postnId,
                    reportType: this.overviewParam.type,
                    mdmCompanyCode: this.orgCompanyCode
                }
            }
            param.role = this.userType;
            if(queryType){
                param.queryType = queryType
                param.reportType = 'SUBCHANNEL'
            }
            const params = {
                ...param,
                ...this.threeParams,
            }
            let data = await this.$http.post('export/link/terminalBoard/sale/rate', params, {timeout: 60000*3});
            if(!data.success){
                this.$showError('获取终端数据失败');
                return
            }
            this[param.reportType+'data'].load = true;
            this[param.reportType+'data'].list = data.result.length>0?data.result.splice(0, 10) : [];
            if(queryType){
                this[param.reportType+'data'].type = queryType;
            }
            return this[param.reportType+'data'].list;
        },
        /**
         * @createdBy  吕志平
         * @date  2022年7月5日
         */
        async SALEBar(orgId){
            let data = this.SALEdata.load?this.SALEdata.list:await this.terminalNumPieData('', orgId);
            this.SALEOption = null
            let seriesDataOne = [{},{},{}];
            let seriesDataTwo = [{},{},{}];
            seriesDataOne[0]={
                value: Number(data[0].curMonthScanCode),
                name: '进货'
            };
            seriesDataTwo[0]={
                value: Number(data[0].lastMonthScanCode),
                name: '进货'
            };
            seriesDataOne[1]={
                value: Number(data[0].curMonthInventory),
                name: '库存'
            };
            seriesDataTwo[1]={
                value: Number(data[0].lastMonthInventory),
                name: '库存'
            };
            seriesDataOne[2]={
                value: Number(data[0].curMonthSales),
                name: '动销'
            };
            seriesDataTwo[2]={
                value: Number(data[0].lastMonthSales),
                name: '动销'
            };

            let legend ={
                        data: ["本月/件", "上月/件"],
                        itemWidth: 6,
                        itemHeight: 6,
                        selectorLabel: {
                            verticalAlign: "bottom",
                        },
                        top: 24,
                        width: "83%",
                        itemGap: 30,
            }
            let tooltipFormatter = function(params){
                let result = '';
                params.forEach((item, index) => {
                    result += item.seriesName + "：";
                    result += isNaN(item.value) ? 0 : item.value;
                    if(index < params.length -1){
                        result += '\n'
                    }
                });
                if(params[1].value !== 0){
                    result += '\n';
                    let probability = (params[0].value - params[1].value) / params[1].value * 100;
                    probability = Math.round(probability * 100) / 100;
                    result = result + '环比增长率' + probability + '%'
                }
                return result;
            }
            this.SALEOption = echartInitConfig=>doubleDataHistogram(echartInitConfig,seriesDataOne,seriesDataTwo,false,{},legend,'本月/件','上月/件', false, tooltipFormatter);
        },
    }
}
</script>

<style lang="scss">
.terminal-sales{
    .load-box{
        text-align: center;
        height: 200px;
        line-height: 200px;
        padding: 80px 0;
        .load{
            font-size: 160px;
            color: #1F74FF;
        }
    }
    .scroll-view-data{
        margin-top: 24px;
        margin-bottom: 24px;
        .select-dimension{
            display: flex;
            margin-left: 24px;
        }
    }
    .content{
        min-height: 288px;
        .tips{
            width: 100%;
            text-align: right;
            color: #999999;
            .tips-icon {
                color: #999999;
                display: inline-block;
            }
            .tips-mess {
                display: inline-block;
                font-size: 24px;
                line-height: 24px;
                font-weight: 400;
                margin: 20px 20px 20px 0;
            }
        }
    }
}
</style>

