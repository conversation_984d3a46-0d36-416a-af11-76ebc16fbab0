<template>
    <view class="role-board">
      <view style="padding-top: 12px"></view>
      <position-bottom :user-info="userInfo" :show.sync="dialogFlag" @choose="changeOrg" from='terminal'></position-bottom>
      <prod-bottom :user-info="userInfo"  :showProd.sync="showProd" :org-id='orgId' @choose="pickerTypeChange" v-if="isShow"></prod-bottom>
      <view class="head-info">
        <scroll-view scroll-x="true" class="scroll-view-data" :style="{width:isShow?'33%':'100%'}">
          <view class="select-dimension">
            <select-button :label="orgText" :selected-flag="true" @tap="dialogFlag=true;" downIcon :showLength='isShow ? 4 : 8'></select-button>
          </view>
        </scroll-view>
        <scroll-view scroll-x="true" class="scroll-view-data" v-if="isShow">
            <view class="select-dimension">
                <picker  :value="pickIndex1" @change="pickerChange" :range="dataFiveOption" >
                    <select-button :label="dataFiveOption[pickIndex1]" :selected-flag="true" downIcon></select-button>
                </picker>
            </view>
        </scroll-view>
        <scroll-view scroll-x="true" class="scroll-view-data"  v-if="isShow">
            <view class="select-dimension">
                <select-button :label="dataTypeOptionObj.value | lov('PROD_BUS_M_CLASS')" :showLength='4' :selected-flag="true"  @tap='showProd=true' downIcon></select-button>
            </view>
        </scroll-view>
      </view>
      <!--   终端客户概览     -->
      <view class="boardBox" id="customerPreview" v-if="dataFlag(0)">
        <customer-preview ref="customer" :user-type="userType"  :org-id="orgId" :orgCompanyCode='orgCompanyCode' :isShow='isShow' :org-text="orgText" :three-params='threeParams'/>
      </view>
      <!--   终端荐酒员概览     -->
      <view class="boardBox" id="drinkser" v-if="dataFlag(1) && !isShow">
        <terminal-recommender-drinkser  ref="drinkser" :user-type="userType" :org-id="orgId" :org-text="orgText"/>
      </view>
      <!--   所辖人员拜访执行情况     -->
      <view class="boardBox" id="personnel" v-if="dataFlag(2)">
        <personnel-visit-execution-status ref="personnel" :user-type="userType" :orgCompanyCode='orgCompanyCode' :first-loading="menu[2].loading" :org-id="orgId"  :three-params='threeParams' :org-text="orgText"/>
      </view>
      <!--   拜访覆盖客户板块     -->
      <view class="boardBox coverCustomers" id="coverCustomers" v-if="dataFlag(3)">
        <visit-cover-customers  ref="coverCustomers" :user-type="userType" :orgCompanyCode='orgCompanyCode' :three-params='threeParams'  :first-loading="menu[3].loading" :org-id="orgId" :org-text="orgText"/>
      </view>
      <!--   配额执行情况     -->
     <view class="boardBox" id="enforcement" v-if="dataFlag(4) && !isShow">
        <quota-enforcement ref="quota" :user-type="userType" :first-loading="menu[4].loading" :org-id="orgId" :org-text="orgText"/>
      </view>
      <!--   终端动销情况     -->
     <view class="boardBox" id="terminalSales" v-if="dataFlag(5)">
        <terminal-sales ref="sales" :user-type="userType" :orgCompanyCode='orgCompanyCode'  :three-params='threeParams' :isShow='isShow' :first-loading="menu[5].loading" :org-id="orgId" :org-text="orgText"/>
      </view>
      <!--   终端热力图     -->
     <view class="boardBox" id="heatmap" v-if="false">
        <terminal-heatmap :first-loading="menu[6].loading"  :org-id="orgId" :org-text="orgText"/>
      </view>
      <!--   终端124目标达成    -->
      <view class="boardBox" id="attainment" v-if="dataFlag(7) && !isShow">
        <terminal-goal-attainment :user-type="userType" :first-loading="menu[7].loading"  :org-data="goalOriData" ref="goal" :org-id="orgId" :org-text="orgText"/>
      </view>
      <!--   终端124目标统计    -->
      <view class="boardBox" id="target" v-if="dataFlag(8) && !isShow">
        <terminal-target-statistics :user-type="userType" :first-loading="menu[8].loading"
                                    :org-data="goalOriData" ref="target" :org-id="orgId" :org-info="orgInfo"
                                    :orgCompanyCode="orgCompanyCode"/>
      </view>
      <!--   终端分品项统计情况    -->
     <view class="boardBox" id="statistics"  v-if="dataFlag(9)">
        <prod-item-statistics :user-type="userType" :org-data="goalOriData" ref="goal" :isShow='isShow'  :three-params='threeParams' :org-id="orgId" :org-info="orgInfo"/>
      </view>
    </view>
</template>

<script>
import CustomerPreview from "./components/customer-preview";
import terminalRecommenderDrinkser from './components/terminal-recommender-drinkser.vue';
import visitCoverCustomers from './components/visit-cover-customers.vue';
import personnelVisitExecutionStatus from './components/personnel-visit-execution-status.vue';
import quotaEnforcement from "./components/quota-enforcement.vue";
import terminalHeatmap from "./components/terminal-heatmap.vue";
import terminalGoalAttainment from "./components/terminal-goal-attainment.vue";
import Taro from "@tarojs/taro";
import TerminalSales from "./components/terminal-sales";
import TerminalTargetStatistics from "./components/terminal-target-statistics";
import prodItemStatistics from "./components/prod-item-statistics";
import SelectButton from "../components/select-button";
import PositionBottom from "../components/position-bottom";
import ProdBottom from "../components/prod-bottom";
export default {
    name: "role-board",
    components: {
        TerminalSales, CustomerPreview,terminalRecommenderDrinkser,
        visitCoverCustomers,personnelVisitExecutionStatus,quotaEnforcement,
        terminalHeatmap,terminalGoalAttainment, TerminalTargetStatistics,
        prodItemStatistics, SelectButton, PositionBottom, ProdBottom
        },
    data(){
        const userInfo = Taro.getStorageSync('token').result;
        let orgCompanyCode = userInfo.coreOrganizationTile.brandCompanyCode
        let {orgId, orgName:orgText, positionType, orgType} = userInfo
        //@edit by 谭少奇 2024/01/31 判断是否销售公司
        if(orgType === "Company"){
            orgId = '58929586649432064';
            orgText = '泸州老窖国窖酒类销售股份有限公司-new';
            orgCompanyCode = '5600'
            // userInfo.positionType = 'BrandSysAdmin'
        }
        return{
            // 分品项公司编码
            prodPartCom: '',
            showProd:false,
            pickIndex1: 0, //标记dataFiveOption选中数据下标
            dataFiveOption: ['所有终端 ', '五码终端'],
            dataFiveOptionObj: [],
            dataTypeOptionObj: {
                value:'B00080001'
            },
            // 是否大成浓香、鸿泸、永粮
            isShow: false,
            userType: 'Manager', // 角色（业代、主管、经理内勤）
            once:true,
            menu:[],
            resHeight:[],
            userInfo,
            bserver:null,
            customerOrgData: {},
            drinkserOrgData: {},
            coverCustomersOrgData: {},
            personnelOrgData: {},
            salesOrgData: {},
            screenHeight:600,
            quotaOriData:{},
            goalOriData:{},
            roleBoardOption:[
                {name: '终端总览看板', seq: '1', val: 'terminal'},
                {name: '市场总览看板', seq: '2', val: 'activity'},
            ],
            roleBoardActive: {name: '终端总览看板', seq: '1', val: 'terminal'},
            orgId,
            orgText,
            orgCompanyCode,//公司SAP编码
            dialogFlag: false,
            orgInfo: null
        }
    },
    async created() {
        this.customerOrgData.text = this.userInfo.orgName;
        this.customerOrgData.orgId =  this.userInfo.orgId;
        this.drinkserOrgData.text = this.userInfo.orgName;
        this.drinkserOrgData.orgId =  this.userInfo.orgId;
        this.coverCustomersOrgData.text = this.userInfo.orgName;
        this.coverCustomersOrgData.orgId =  this.userInfo.orgId;
        this.personnelOrgData.text = this.userInfo.orgName;
        this.personnelOrgData.orgId =  this.userInfo.orgId;
        this.salesOrgData.text = this.userInfo.orgName;
        this.salesOrgData.orgId =  this.userInfo.orgId;
        this.quotaOriData.text = this.userInfo.orgName;
        this.quotaOriData.orgId =  this.userInfo.orgId;
        this.goalOriData.text = this.userInfo.orgName;
        this.goalOriData.orgId =  this.userInfo.orgId;
        this.screenHeight = wx.getSystemInfoSync().screenHeight;
        // 获取分品项企业参数配置
        this.prodPartCom = await this.$utils.getCfgProperty('PROD_PART_BRANCH_COM');
        // 是否大成浓香、鸿泸、永粮
        this.isShow = this.prodPartCom.indexOf(this.userInfo.coreOrganizationTile.brandCompanyCode) > -1;
        await this.getPosition();
        this.initView();
        // 根据类型获取值列表数据
       const fiveOptions = await this.$lov.getLovByType('5MARK_TERMINAL_BIG_SCREEN')
       this.dataFiveOptionObj = fiveOptions.map(i=>{
           return {
               value: i.val,
               label: i.name
           }
       })
       this.dataFiveOption = fiveOptions.map(i=> {return i.name})
       this.getType()
        setTimeout(()=>{
            this.onElementLinstener();
            this.$utils.hideLoading();
        }, 1000);
    },
    computed: {
        dataFlag(){
            return (index) => {
                return this.menu.length > 0 && this.menu[index] && this.menu[index].flag
            }
        },
        // 大成浓香、鸿泸、永粮公司筛选参数
        threeParams(){
            let params = {}
            if(this.isShow){
               params.brandComType = 'DCYLHL';
               params.terminalProd = this.dataTypeOptionObj.value;
               params.terminalType = this.dataFiveOptionObj[this.pickIndex1]?.value;
               if(params.terminalType === 'fiveCode'){
                   params.terminalType = 'fiveMark'
               }else if(!params.terminalType){
                   params.terminalType = 'fiveMark'
               }
            }
            return params
        }
    },
    destroyed() {
        wx.createIntersectionObserver().disconnect()
    },
    methods:{
        /**
         @desc: 更新子组件
         @author: 谭少奇
         @date 2023/12/05
         **/
        updateChild(){
            // 终端概览
            if(this.$refs.customer){
               this.$refs.customer.init()
            }
            // 所辖人员拜访执行
            if(this.$refs.personnel){
               this.$refs.personnel.visitDetailData()
            }
            // 拜访覆盖客户板块
            if(this.$refs.coverCustomers){
               this.$refs.coverCustomers.orgBackGetInfo()
            }
            // 动销
            if(this.$refs.sales){
               this.$refs.sales.cleanData()
            }
            // 终端分品项统计
            if(this.$refs.goal){
               this.$refs.goal.getProdStatisticsData()
            }
        },
        /**
         @desc: 查询分品项
         @author: 谭少奇
         @date 2023/11/21
         **/
        async getType(){
            const params = {
                mvgMapperName: 'userBusClass',
                mvgAttr6: 'groupByMClass',
                oauth: 'ALL',
                mvgAttr7: this.orgId,
                mvgAttr2: this.userInfo.id,
                mvgParentId: this.userInfo.id
            }
            const data =  await this.$http.post('action/link/mvg/queryRightListPage',params)
            const newData = []
            if(data.success){
                data.rows.forEach(i=>{
                    if(i.mClassCode){
                        const newObj = {
                            value: i.mClassCode,
                        }
                        newData.push(newObj)
                    }
                })
                // 判断是否含有老头曲有取无取首项
                const showdata = newData.find(i=>{
                    return i.value === 'B00080001'
                })
                this.dataTypeOptionObj = showdata ? showdata : (newData.length ? newData[0] : { value:null})

                if(this.isShow && !showdata){
                    this.updateChild()
                }
            }

        },
        /**
         @desc: 切换是否五码
         @author: 谭少奇
         @date 2023/11/20
         **/
        pickerChange(item){
            this.pickIndex1 = Number(item.detail.value)
            this.$nextTick(function(){
                this.updateChild()
            })
        },
        /**
         @desc: 选择分品项数据
         @author: 谭少奇
         @date 2023/11/30
         **/
        pickerTypeChange(item){
            this.dataTypeOptionObj = {
                value: item.mClassCode
            }
            this.$nextTick(function(){
                this.updateChild()
            })
        },
        async changeOrg(item) {
            if(Object.keys(item).length === 0) return;
            this.orgText = item.text;
            this.orgId = item.id;
            this.orgInfo = item
            this.orgCompanyCode = item.sapCompCode || item.topSapCompCode || ''
            this.isShow = this.prodPartCom.indexOf(item.topSapCompCode) > -1;
            this.$refs.customer && await this.$refs.customer.init(this.orgId);
            this.$refs.coverCustomers && await this.$refs.coverCustomers.orgBackGetInfo(this.orgId);
            this.$refs.quota && await this.$refs.quota.getQuataData(this.orgId);
            this.$refs.sales && await this.$refs.sales.cleanData(this.orgId);
            this.$refs.target && await this.$refs.target.searchData(this.orgInfo);
        },
        /**
         @desc: 获取角色看板权限
         @author: wangbinxin
         @date 2022-08-10 16-43
         **/
        async getPosition(){
            this.$utils.showLoading();
            const queryData =  await this.$http.post('export/link/cfgProperty/queryByExamplePage', {
                filtersRaw: [{
                    id: 'key',
                    property: 'key',
                    operator: 'in',
                    value: '[Terminal_Role_Salesman,Terminal_Role_SalesSupervisor,Terminal_Role_Manager,Terminal_Role_HeadLeader]'
                }, {
                    id: 'value',
                    property: 'value',
                    operator: 'like',
                    value: this.userInfo.positionType
                }]
            });
            if(queryData.success){
                if(queryData.rows.length > 0){
                    queryfor:
                        for (let i = 0; i < queryData.rows.length; i++) {
                            let arr = queryData.rows[i].value.split(',');
                            for (let j = 0; j < arr.length; j++) {
                                if(arr[j] === this.userInfo.positionType){
                                    let userType = queryData.rows[i].key.split('_');
                                    this.userType = userType[userType.length-1];
                                    break queryfor;
                                }
                            }
                        }
                }
            }else{
                this.$showError('获取用户信息失败' + queryData.result);
            }
        },
        onElementLinstener() {
            // 创建节点侦查器
            let arr = this.menu.filter((item, index) => {
                item.index = index;
                return item.flag && !item.loading && item.select !== '#heatmap'
            });
            arr.forEach((obj) => {
                wx.createIntersectionObserver().relativeToViewport().observe(obj.select, res => {
                    if(!this.menu[obj.index].loading){
                        this.menu[obj.index].loading = true;
                    }
                });
            })
        },
        /**
         *  @description: 根据职位控制菜单展示
         *  @author: 吕志平
         *  @date: 2022年7月14日
         */
        initView(){
            this.menu = [
                {
                    select:'#customerPreview',
                    flag:true,
                    loading:true
                },{
                    select:'#drinkser',
                    flag: this.userInfo.coreOrganizationTile.brandCompanyCode === '5137' || this.userInfo.coreOrganizationTile.brandCompanyCode === '5161',
                    loading:true
                },{
                    select:'#personnel',
                    flag: this.userType === 'Salesman' || this.userType === 'HeadLeader' ? false : true,
                    loading:false
                },{
                    select:'#coverCustomers',
                    flag:true,
                    loading:false
                },{
                    select:'#enforcement',
                    flag: false,
                    loading:false
                },{
                    select:'#terminalSales',
                    flag:false,
                    loading:false
                },{
                    select:'#heatmap',
                    flag: this.userType !== 'Salesman' && this.userType !== 'SalesSupervisor'?true:false,
                    loading:false
                },{
                    select:'#attainment',
                    flag: false,
                    loading:false
                },
                {
                    select: '#target',
                    flag: false,
                    loading:false
                },
                {
                    select: '#statistics',
                    flag: this.userType ==='HeadLeader',
                    loading:false
                }

            ];
            if( this.userInfo.coreOrganizationTile.brandCompanyCode === '5600' ||
                this.userInfo.coreOrganizationTile.brandCompanyCode === '5137' ||
                this.$utils.isEmpty(this.userInfo.coreOrganizationTile.brandCompanyCode)){
                this.menu[4].flag = true;
            }
            if( this.userInfo.coreOrganizationTile.brandCompanyCode === '5600' ||
                this.userInfo.coreOrganizationTile.brandCompanyCode === '5137' ||
                this.userInfo.coreOrganizationTile.brandCompanyCode === '5151' ||
                this.userInfo.coreOrganizationTile.brandCompanyCode === '5161' ||
                this.userInfo.coreOrganizationTile.brandCompanyCode === '5903' ||
                this.userInfo.coreOrganizationTile.brandCompanyCode === '5152' ||
                this.userInfo.coreOrganizationTile.brandCompanyCode === '5902' ||
                this.userInfo.coreOrganizationTile.brandCompanyCode === '5906' ||
                this.$utils.isEmpty(this.userInfo.coreOrganizationTile.brandCompanyCode)){
                this.menu[5].flag = true;
                this.menu[8].flag = true;
            }
        },
        /**
         @param role-org-page界面返回数据
         @desc: 根据职位更新对应图表
         @author: wangbinxin
         @date 2022-08-25 10-34
         **/
        changeRef(param){
            if(this.$utils.isNotEmpty(param)){
                if(this.$utils.isNotEmpty(param.data)){
                    if(this.$utils.isNotEmpty(param.source) && param.source==='goal'){
                        this.goalOriData = param.data;
                    }
                }
            }
        }
    },
    mounted() {
        this.$bus.$on('orgBack', data => {
            this.changeRef(data);
        })
    }
}

</script>

<style lang="scss">
.role-board{
    .lnk-tabs{
        position: relative;
        border-bottom: 2px solid #f2f2f2!important;
    }
    .boardBox{
        background: #ffffff;
        margin: 0 14px 24px;
        border-radius: 12px;
    }
    .head-info {
        display: flex;
        position: relative;
        height: 52px;
        .scroll-view-data{
            width: 33%;
            .select-dimension{
                display: flex;
                margin-left: 24px;
            }
        }
    }
}
</style>
