import {$utils} from "src/utils/$utils";


/**
 *  @description: 绘制饼图基础色
 */
export function echartColor(echartInitConfig, params,pieColor) {
    let colorList = [];
    if(pieColor.length>0){
        colorList = pieColor
    }else{
        let baseColorList = [
            {
                c1: '#FFB701',  //管理
                c2: '#FF5A5A'
            },{
                c1: '#6392FA',  //管理
                c2: '#4179F4'
            },{
                c1: '#69CAFF',  //管理
                c2: '#36ACEB'
            },{
                c1: '#81F3EF',  //管理
                c2: '#5FCACE'
            },
            {
                c1: '#FFEF7F',  //管理
                c2: '#FFD54A'
            },
            {
                c1: '#FFA762',  //实践
                c2: '#FF892C'
            },
            {
                c1: '#FF7B76',//操作
                c2: '#FF544D'
            },
            {
                c1: '#AF6DFF',//操作
                c2: '#8A2FF8'
            },
            {
                c1: '#6392FA',//操作
                c2: '#4179F4'
            },
            {
                c1: '#69CAFF',//操作
                c2: '#36ACEB'
            },
            {
                c1: '#81F3EF',//操作
                c2: '#5FCACE'
            },
        ]
        colorList = $utils.deepcopy(baseColorList);
        if(params.dataIndex >= colorList.length ){
            let num = Math.ceil((params.dataIndex+1)/baseColorList.length);
            for(let i=0; i < num-1;i++){
                // @ts-ignore
                colorList = colorList.concat(baseColorList);
            }
        }
    }
    return new echartInitConfig.echarts.graphic.LinearGradient(0, 0, 0, 1, [{ //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
        offset: 0,
        color: colorList[params.dataIndex]['c1']
    }, {
        offset: 1,
        color: colorList[params.dataIndex]['c2']
    }])
}



/**
 *  @description: 双柱图
 *  @author: 吕志平
 *  @date: 2022年6月20日
 */
export function doubleDataHistogram(echartInitConfig, seriesDataOne, seriesDataTwo, colorStyle = {}, yAxisData = {},legend ={}, seriesNameOne = '',seriesNameTwo = '', labelShow = true, tooltipFormatter) {
    let xAxisData = new Array();
    let seriesDataTempOne = new Array();
    let seriesDataTempTwo = new Array();
    seriesDataOne.forEach((item) => {
        xAxisData.push(item.name);
        seriesDataTempOne.push(item.value);
    });
    seriesDataTwo.forEach((item) => {
        seriesDataTempTwo.push(item.value);
    });
    if ($utils.isEmpty(colorStyle)) {
        colorStyle = {colorA: '#6392FA', colorB: '#2F69F8'};
    }
    let series = [
        {
            name: seriesNameOne,
            type: 'bar',
            stack: 'one',
            barWidth: '10px',
            barGap:'190%',
            itemStyle: {
                color: '#98CEFF'
            },
            label: {
                position: "top",
                borderWidth: 20,
                fontSize: 12,
                fontFamily: "PingFangSC-Medium",
                color: "#333333",
                align: "center",
            },
            data: seriesDataTempOne
        },
        {
            name: seriesNameTwo,
            type: 'bar',
            stack: 'two',
            barWidth: '10px',
            itemStyle: {
                normal: {
                    color: new echartInitConfig.echarts.graphic.LinearGradient(
                        0, 0, 0, 1,
                        [
                            {offset: 0, color: '#6392FA'},
                            {offset: 1, color: '#2F69F8'},
                        ]
                    )
                },
                emphasis: {
                    color: new echartInitConfig.echarts.graphic.LinearGradient(
                        0, 0, 0, 1,
                        [
                            {offset: 0, color: '#6392FA'},
                            {offset: 1, color: '#2F69F8'},
                        ]
                    )
                }
            },
            label: {
                position: "top",
                offset: [0, -10],
                borderWidth: 20,
                fontSize: 12,
                fontFamily: "PingFangSC-Medium",
                color: "#333333",
                align: "center",
            },
            data: seriesDataTempTwo
        }
    ];
    if(!labelShow){
        series.forEach((item) => {
            delete item.label
        })
    }
    let tooltip = {
        trigger: 'axis',
        axisPointer: {            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow',        // 默认为直线，可选为：'line' | 'shadow'
            label: {
                formatter: '{value}'
            }
        }
    }
    if(tooltipFormatter){
        tooltip.formatter = tooltipFormatter;
    }
    return {
        grid: {
            left: '8px',
            right: '5px',
            bottom: '16px',
            containLabel: true
        },
        legend: legend,
        tooltip,
        xAxis: [
            {
                type: 'category',
                data: xAxisData,
                axisPointer: {
                    type: 'shadow'
                },
                axisTick: {
                    alignWithLabel: true
                },
                axisLabel: {
                    interval: 0, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
                    rotate: 45,   //调整数值改变倾斜的幅度（范围-90到90）
                    textStyle: {color: '#8C8C8C', fontSize: 10, fontFamily: 'PingFangSC-Medium'}
                },
            }
        ],
        yAxis: [
            {
                type: 'value',
                axisPointer: {
                    type: 'shadow'
                },
                axisTick: {
                    show: false
                },
                axisLine: {lineStyle: {color: '#EBEDF5'}},
                axisLabel: {
                    color: '#8C8C8C',
                    fontSize: 10,
                    lineHeight: 10,
                    fontFamily: 'PingFangSC-Medium'
                },
            }
        ],
        series
    };

}


/**
 *  @description: 环形图
 *  @author: 吕志平
 *  @date: 2022年6月21日
 *  @param echartInitConfig
 *  @param seriesData 数据项，例如： [{value: 23907500, name: '北部组'},{value: 239075, name: '西北组'}]
 *  @param totalSeriesData 数据总量，在饼图中间展示
 *  @param outRadius 外环圆角
 *  @param inRadius 内环圆角
 * @param pieColor
 * @param startAngle
 * @param labelType
 * @param labelUnit
 * @param totalUnit
 * @param minAngle
 */
export function annularChart(echartInitConfig, seriesData, totalSeriesData, outRadius = ['47%', '70%'], inRadius = '47%',pieColor=[],startAngle = 225, labelType, labelUnit, totalUnit, minAngle = 3, labelFontSize = 'largeSize', titleFontSizeOne = 14,titleFontSizeTwo = 14) {
    return {
        grid: {
            left: '12px',
            right: '12px',
            bottom: '10px',
            top: '10px',
            containLabel: true
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        series: [
            {
                name: '外环',
                itemStyle: {
                    emphasis: {},
                    normal: {
                        color: function (params) {
                            return echartColor(echartInitConfig, params, pieColor);
                        }
                    }
                },
                type: 'pie',
                z: 1,
                avoidLabelOverlap: true,   //是否启用防止标签重叠策略
                minAngle: minAngle,           　　 //最小的扇区角度（0 ~ 360），用于防止某个值过小导致扇区太小影响交互
                radius: outRadius,
                center: ['50%', '50%'],
                hoverAnimation: false,
                silent: false,
                clockwise: true,
                startAngle: startAngle,
                data: seriesData,
                labelLine: {
                    normal: {
                        smooth: 0.5,
                        length: 12,
                        length2: 6
                    }
                },
                tooltip: {
                    confine: true,
                    trigger: 'item',
                    show: true,
                    formatter: '{b} : {c} ({d}%)'
                },
                label: {
                    position: 'outside',    // 标签的位置。'outside'饼图扇区外侧，通过视觉引导线连到相应的扇区。'inside','inner' 同 'inside',饼图扇区内部。'center'在饼图中心位置。
                    normal: {
                        formatter: function (params) {
                            if(labelFontSize === 'smallSize'){
                                params.percent = params.percent.toFixed(2)
                            }
                            let string
                            if(labelType === 'valuePercent'){
                                let text = params.name;
                                string =  '{c1|' + text  +'}'   //然后return你需要的legend格式即可
                            }else if(labelType === 'value'){
                                let text = params.name;
                                string =  '{c1|' + text + '}\n' + '{d1|' + params.value + labelUnit +'}'   //然后return你需要的legend格式即可
                            }else{
                                let text = params.name;
                                if(labelFontSize === 'largeSize'){
                                    if(text.length<=4){
                                        string =  '{c1|' + text + '}' + ' ' + '{d1|' + params.percent +'%'+'}'   //然后return你需要的legend格式即可（7）
                                    }else if(text.length > 4 && text.length<= 11){
                                        string =  '{c1|' + text.slice(0,7) + '}\n' + '{c1|' + text.slice(7) + '}'+' ' + '{d1|' + params.percent +'%'+'}'   //然后return你需要的legend格式即可(7+ 4)
                                    }else if(text.length > 11){
                                        string =  '{c1|' + text.slice(0,7) + '}\n' + '{c1|' + text.slice(7,14) + '}\n' + '{c1|' + text.slice(14) + '}'+ ' ' + '{d1|' + params.percent +'%'+'}'   //然后return你需要的legend格式即可(7+7+4)
                                    }
                                }else{
                                    let windowWidth = wx.getSystemInfoSync().windowWidth
                                    if(windowWidth <= 360){
                                        text.length < 5 ? string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text + '}'
                                            : string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text.slice(0,3) + '...' + '}'
                                    }else if(windowWidth > 360 && windowWidth <= 375){
                                        text.length < 7 ? string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text + '}'
                                            : string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text.slice(0,5) + '...' + '}'
                                    }else if(windowWidth > 375 && windowWidth <= 425){
                                        text.length < 9 ? string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text + '}'
                                            : string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text.slice(0,7) + '...' + '}'
                                    }else if(windowWidth > 400 && windowWidth <= 450){
                                        text.length < 13 ? string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text + '}'
                                            : string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text.slice(0,11) + '...' + '}'
                                    }else if(windowWidth > 450 && windowWidth <= 500){
                                        text.length < 15 ? string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text + '}'
                                            : string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text.slice(0,13) + '...' + '}'
                                    }else{
                                        string =   '{d1|' + params.percent +'%'+'}'+ '{c1|' + text
                                    }
                                }
                            }
                            return string
                        },
                        textStyle: {fontSize: labelFontSize === 'largeSize' ? 12: 9, fontFamily: 'PingFangSC-Regular'},
                        rich: {
                            c1: {
                                color: '#8c8c8c',
                                lineHeight: labelFontSize === 'largeSize' ? 12: 9,
                                fontSize: labelFontSize === 'largeSize' ? 12: 9,
                                fontFamily: 'PingFangSC-Semibold'
                            },
                            d1: {
                                color: '#000',
                                lineHeight: labelFontSize === 'largeSize' ? 20: 9,
                                fontSize: labelFontSize === 'largeSize' ? 12: 9,
                                fontFamily: 'PingFangSC-Regular'
                            },
                        }
                    },

                    emphasis: {
                        show: true,
                        textStyle: {
                            fontSize: '18',
                            fontWeight: 'bold'
                        },
                        hoverAnimation: false,
                        silent: false,
                    }
                }
            },
            {
                name: '内环',
                z: 2,
                color: '#fff',
                type: 'pie',
                radius: inRadius,
                center: ['50%', '50%'],
                hoverAnimation: false,
                avoidLabelOverlap: true,
                silent: false,
                clockwise: true,
                startAngle: startAngle, //起始角度
                data: totalSeriesData,
                tooltip: {
                    show: false,
                },
                labelLine: {
                    normal: {
                        lineStyle: {color: '#DDDDDD'},
                        smooth: 0.2,
                        length: 8,
                        length2: 16
                    }
                },
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        hoverAnimation: false,
                        formatter: function (params) {
                            if($utils.isNotEmpty(totalUnit)){
                                return '{c1|' + totalSeriesData[0].value + '}\n{d1|' + totalSeriesData[0].name + '}\n{e1|' + totalUnit + '}';
                            }else{
                                return '{c1|' + totalSeriesData[0].value + '}\n{e1|'+''+ '}\n{d1|' + totalSeriesData[0].name + '}';
                            }
                        },
                        rich: {
                            c1: {
                                color: '#262626',
                                lineHeight: titleFontSizeOne,
                                fontSize: titleFontSizeOne,
                                fontFamily: 'PingFangSC-Semibold'
                            },
                            d1: {
                                color: '#333333',
                                lineHeight: titleFontSizeTwo,
                                fontSize: titleFontSizeTwo,
                                fontFamily: 'PingFangSC-Regular'
                            },
                            e1: {
                                color: '#8C8C8C',
                                lineHeight: 9,
                                fontSize: 9,
                                fontFamily: 'PingFangSC-Regular'
                            },
                        }
                    },
                    emphasis: {
                        show: true,
                        textStyle: {
                            fontSize: '18',
                            fontWeight: 'bold'
                        },
                        hoverAnimation: false,
                        silent: false,
                    }
                }
            }
        ]
    };
}
