import {$utils} from "src/utils/$utils";

/**
 *  @description: 绘制饼图基础色
 *  @author: 马晓娟
 *  @date: 2020/10/28 15:58
 */
export function echartColor(echartInitConfig, params,pieColor) {
  let colorList = new Array()
  if(pieColor.length>0){
    colorList = pieColor
  }else{
    let baseColorList = [
        {
            c1: '#FFB701',  //管理
            c2: '#FF5A5A'
        },{
            c1: '#6392FA',  //管理
            c2: '#4179F4'
        },{
            c1: '#69CAFF',  //管理
            c2: '#36ACEB'
        },{
            c1: '#81F3EF',  //管理
            c2: '#5FCACE'
        },
        {
            c1: '#FFEF7F',  //管理
            c2: '#FFD54A'
        },
        {
            c1: '#FFA762',  //实践
            c2: '#FF892C'
        },
        {
            c1: '#FF7B76',//操作
            c2: '#FF544D'
        },
        {
            c1: '#AF6DFF',//操作
            c2: '#8A2FF8'
        },
        {
            c1: '#6392FA',//操作
            c2: '#4179F4'
        },
        {
            c1: '#69CAFF',//操作
            c2: '#36ACEB'
        },
        {
            c1: '#81F3EF',//操作
            c2: '#5FCACE'
        },
    ]
      colorList = $utils.deepcopy(baseColorList);
      if(params.dataIndex >= colorList.length ){
          let num = Math.ceil((params.dataIndex+1)/baseColorList.length);
          for(let i=0; i < num-1;i++){
              colorList = colorList.concat(baseColorList);
          }
      }
  }
  return new echartInitConfig.echarts.graphic.LinearGradient(0, 0, 0, 1, [{ //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
    offset: 0,
    color: colorList[params.dataIndex].c1
  }, {
    offset: 1,
    color: colorList[params.dataIndex].c2
  }])
}

/**
 *  @description: 绘制饼图相关参数
 *  @author: 马晓娟
 *  @date: 2020/10/28 16:08
 *  @param echartInitConfig
 *  @param seriesData 数据项，例如： [{value: 23907500, name: '北部组'},{value: 239075, name: '西北组'}]
 *  @param totalSeriesData 数据总量，在饼图中间展示
 *  @param outRadius 外环圆角
 *  @param inRadius 内环圆角
 * @param pieColor
 * @param startAngle
 * @param labelType
 * @param labelUnit
 * @param totalUnit
 * @param minAngle
 */
export function targetPieChartProgress(echartInitConfig, seriesData, totalSeriesData, outRadius = ['47%', '70%'], inRadius = '47%',pieColor=[],startAngle = 225, labelType, labelUnit, totalUnit, minAngle = 3, labelFontSize = 'largeSize', titleFontSize = 14) {
  return {
    grid: {
      left: '12px',
      right: '12px',
      bottom: '10px',
      top: '10px',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    series: [
      {
        name: '外环',
        itemStyle: {
          emphasis: {},
          normal: {
            color: function (params) {
              return echartColor(echartInitConfig, params, pieColor);
            }
          }
        },
        type: 'pie',
        z: 1,
        avoidLabelOverlap: true,   //是否启用防止标签重叠策略
        // roseType: 'area',
        minAngle: minAngle,           　　 //最小的扇区角度（0 ~ 360），用于防止某个值过小导致扇区太小影响交互
        radius: outRadius,
        center: ['50%', '50%'],
        hoverAnimation: false,
        silent: false,
        clockwise: true,
        startAngle: startAngle,
        data: seriesData,
        labelLine: {
          normal: {
            smooth: 0.5,
            length: 12,
            length2: 6
          }
        },
        tooltip: {
          confine: true,
          trigger: 'item',
          show: true,
          formatter: '{b} : {c} ({d}%)'
        },
        label: {
          position: 'outside',    // 标签的位置。'outside'饼图扇区外侧，通过视觉引导线连到相应的扇区。'inside','inner' 同 'inside',饼图扇区内部。'center'在饼图中心位置。
          normal: {
            formatter: function (params) {
              if(labelFontSize === 'smallSize'){
                params.percent = params.percent.toFixed(2)
              }
              let string
              if(labelType === 'valuePercent'){
                let text = params.name;
                 string =  '{c1|' + text + '}\n' + '{d1|' + params.value + labelUnit +','+ params.percent +'%'+'}'   //然后return你需要的legend格式即可
              }else if(labelType === 'value'){
                  let text = params.name;
                string =  '{c1|' + text + '}\n' + '{d1|' + params.value + labelUnit +'}'   //然后return你需要的legend格式即可
              }else if(labelType === 'number'){
                  let text = params.name;
                  string =  '{c2|' + text + '}\n' + '{d2|' + params.percent+'% ，' + params.value +'}'   //然后return你需要的legend格式即可
              }else{
                let text = params.name;
                if(labelFontSize === 'largeSize'){
                  if(text.length<=4){
                    string =  '{c1|' + text + '}' + ' ' + '{d1|' + params.percent +'%'+'}'   //然后return你需要的legend格式即可（7）
                  }else if(text.length > 4 && text.length<= 11){
                    string =  '{c1|' + text.slice(0,7) + '}\n' + '{c1|' + text.slice(7) + '}'+' ' + '{d1|' + params.percent +'%'+'}'   //然后return你需要的legend格式即可(7+ 4)
                  }else if(text.length > 11){
                    string =  '{c1|' + text.slice(0,7) + '}\n' + '{c1|' + text.slice(7,14) + '}\n' + '{c1|' + text.slice(14) + '}'+ ' ' + '{d1|' + params.percent +'%'+'}'   //然后return你需要的legend格式即可(7+7+4)
                  }
                }else{
                  let windowWidth = wx.getSystemInfoSync().windowWidth
                  if(windowWidth <= 360){
                    text.length < 5 ? string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text + '}'
                                    : string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text.slice(0,3) + '...' + '}'
                  }else if(windowWidth > 360 && windowWidth <= 375){
                    text.length < 7 ? string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text + '}'
                        : string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text.slice(0,5) + '...' + '}'
                  }else if(windowWidth > 375 && windowWidth <= 425){
                    text.length < 9 ? string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text + '}'
                        : string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text.slice(0,7) + '...' + '}'
                  }else if(windowWidth > 400 && windowWidth <= 450){
                    text.length < 13 ? string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text + '}'
                        : string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text.slice(0,11) + '...' + '}'
                  }else if(windowWidth > 450 && windowWidth <= 500){
                    text.length < 15 ? string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text + '}'
                        : string =  '{d1|' + params.percent +'%'+'}'+ '{c1|' + text.slice(0,13) + '...' + '}'
                  }else{
                    string =   '{d1|' + params.percent +'%'+'}'+ '{c1|' + text
                  }
                }
              }
                return string
            },
            textStyle: {fontSize: labelFontSize === 'largeSize' ? 12: 9, fontFamily: 'PingFangSC-Regular'},
            rich: {
              c1: {
                color: '#8c8c8c',
                lineHeight: labelFontSize === 'largeSize' ? 12: 9,
                fontSize: labelFontSize === 'largeSize' ? 12: 9,
                fontFamily: 'PingFangSC-Semibold'
              },
              d1: {
                color: '#000',
                lineHeight: labelFontSize === 'largeSize' ? 20: 9,
                fontSize: labelFontSize === 'largeSize' ? 12: 9,
                fontFamily: 'PingFangSC-Regular'
              },
              c2: {
                color: '#999999',
                lineHeight: labelFontSize === 'largeSize' ? 12: 9,
                fontSize: labelFontSize === 'largeSize' ? 12: 9,
                fontFamily: 'PingFangSC-Semibold'
              },
              d2: {
                color: '#333333',
                lineHeight: labelFontSize === 'largeSize' ? 20: 9,
                fontSize: labelFontSize === 'largeSize' ? 12: 9,
                fontFamily: 'PingFangSC-Regular'
              },
            }
          },

          emphasis: {
            show: true,
            textStyle: {
              fontSize: '18',
              fontWeight: 'bold'
            },
            hoverAnimation: false,
            silent: false,
          }
        }
      },
      {
        name: '内环',
        z: 2,
        color: '#fff',
        type: 'pie',
        radius: inRadius,
        center: ['50%', '50%'],
        hoverAnimation: false,
        avoidLabelOverlap: true,
        silent: false,
        clockwise: true,
        startAngle: startAngle, //起始角度
        data: totalSeriesData,
        tooltip: {
          show: false,
        },
        labelLine: {
          normal: {
            lineStyle: {color: '#DDDDDD'},
            smooth: 0.2,
            length: 8,
            length2: 16
          }
        },
        label: {
          normal: {
            show: true,
            position: 'center',
            hoverAnimation: false,
            formatter: function (params) {
              if($utils.isNotEmpty(totalUnit)){
                return '{c1|' + totalSeriesData[0].value + '}\n{d1|' + totalSeriesData[0].name + '}\n{e1|' + totalUnit + '}';
              }else{
                return '{c1|' + totalSeriesData[0].value + '}\n{d1|' + totalSeriesData[0].name + '}';
              }
            },
            rich: {
              c1: {
                color: '#262626',
                lineHeight: 18,
                fontSize: titleFontSize,
                fontFamily: 'PingFangSC-Semibold'
              },
              d1: {
                color: '#8C8C8C',
                lineHeight: 16,
                fontSize: titleFontSize - 2,
                fontFamily: 'PingFangSC-Regular'
              },
              e1: {
                color: '#8C8C8C',
                lineHeight: 12,
                fontSize: 9,
                fontFamily: 'PingFangSC-Regular'
              },
            }
          },
          emphasis: {
            show: true,
            textStyle: {
              fontSize: '18',
              fontWeight: 'bold'
            },
            hoverAnimation: false,
            silent: false,
          }
        }
      }
    ]
  };
}

/**
 *  @description: 简单柱状图
 *  @author: 马晓娟
 *  @date: 2020/10/28 21:57
 */
export function targetSimpleHistogramProgress(echartInitConfig, seriesData, colorStyle = {}, yAxisData = {}) {
  let xAxisData = new Array();
  let seriesDataTemp = new Array();
  seriesData.forEach((item) => {
    xAxisData.push(item.name);
    seriesDataTemp.push(item.value);
  });
  if ($utils.isEmpty(colorStyle)) {
    colorStyle = {colorA: '#6392FA', colorB: '#2F69F8'};
  }
  return {
    grid: {
      left: '8px',
      right: '5px',
      bottom: '16px',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {            // 坐标轴指示器，坐标轴触发有效
        type: 'shadow',        // 默认为直线，可选为：'line' | 'shadow'
        label: {
          formatter: '{value}'
        }
      }
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisPointer: {
          type: 'shadow'
        },
        axisTick: {
          alignWithLabel: true
        },
        axisLabel: {
          interval: 0, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
          rotate: 45,   //调整数值改变倾斜的幅度（范围-90到90）
          textStyle: {color: '#8C8C8C', fontSize: 10, fontFamily: 'PingFangSC-Medium'}
        },
      }
    ],
    yAxis: [
      {
        type: 'value',
        min: yAxisData['min'] || 0,
        max: yAxisData['max'] || 1000,
        interval: yAxisData['interval'] || 200,
        axisPointer: {
          type: 'shadow'
        },
        axisTick: {
          show: false
        },
        axisLine: {lineStyle: {color: '#EBEDF5'}},
        axisLabel: {
          color: '#8C8C8C',
          fontSize: 10,
          lineHeight: 10,
          fontFamily: 'PingFangSC-Medium'
        },
      }
    ],
    series: [
      {
        name: '',
        type: 'bar',
        stack: 'one',
        barWidth: '10px',
        itemStyle: {
          normal: {
            color: new echartInitConfig.echarts.graphic.LinearGradient(
                0, 0, 0, 1,
                [
                  {offset: 0, color: colorStyle['colorA']},
                  {offset: 1, color: colorStyle['colorB']},
                ]
            )
          },
          emphasis: {
            color: new echartInitConfig.echarts.graphic.LinearGradient(
                0, 0, 0, 1,
                [
                  {offset: 0, color: colorStyle['colorA']},
                  {offset: 1, color: colorStyle['colorB']},
                ]
            )
          }
        },
        data: seriesDataTemp
      }
    ]
  };

}
/**
 * @createdBy  张丽娟
 * @date  2020/11/2
 * @methods barYCategory
 * @para
 * @description 柱状图，横坐标为y轴
 */
export function barYCategory(echartInitConfig, seriesData,sort = true,unit) {
  function object(a,b) {
    return a.value-b.value;
  }
  if(sort){
    seriesData.sort(object);
  }
  let idsStr = seriesData.map(function(obj,index){
    return obj.name;
  }).join(",");
  let qsName = idsStr.split(',')
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      position: function(point, params, dom, rect, size){ // point: 鼠标位置
        var tipHeight = point[1] + size.contentSize[1]; // contentSize: 提示dom 窗口大小
        if(tipHeight > size.viewSize[1] ){              // viewSize: echarts 容器大小
          return [point[0]+40, point[1]-size.contentSize[1]];
        } else if(point[1] < size.contentSize[1]){
          return [point[0]+40, point[1]+20];
        } else {
          return point;
        }
      },
      formatter: function (params) {
        if(params.length > 0){
          if(unit){
            return params[0].name + ": "+  params[0].value + unit
          }else{
            return params[0].name + ": "+  params[0].value
          }
        }
      },
    },
    legend: {
      show: false,
    },
    grid: {
      left: 16,
      right: 16,
      bottom: 0,
      top: 16,
      containLabel: true
    },
    xAxis: {
      type: 'value',
      show: false,
      boundaryGap: [0, 0.01]
    },
    yAxis: {
      type: 'category',
      boundaryGap: [0, 20],
      axisTick: {
        show: false
      },
      axisLine: {lineStyle: {color: '#fff'}},
      axisLabel: {
        color: '#909AA9',
        fontSize: 10,
        lineHeight: 10,
        fontFamily: 'PingFangSC-Medium'
      },

      data: qsName
    },
    series: [
      {
        name: '',
        type: 'bar',
        barWidth: 10,
        barGap: 20,
        label: {
          normal: {
            show: true,//显示数字
            position: 'right',
            textStyle: {
              fontSize: '10',//柱状上的显示的文字
              color: '#909AA9',
            }
          }
        },
        itemStyle: {
          color: function (params){
            const index = params.dataIndex;
            const colorList = index % 2 === 1 ? ['#2F69F8','#6392FA'] : ['#98CEFF','#C9E9FF']
            return new echartInitConfig.echarts.graphic.LinearGradient(0, 0, 1, 0,
                [
                  {offset: 0, color: colorList[0]},
                  {offset: 1, color: colorList[1]}
                ]);
          },
        },
        data: seriesData
      }
    ]
  };
}
export function dBarYCategory(echartInitConfig, seriesData,sort = true,unit) {
    function object(a,b) {
        return a.value-b.value;
    }
    if(sort){
        seriesData.sort(object);
    }
    let idsStr = seriesData.map(function(obj,index){
        return obj.name;
    }).join(",");
    let qsName = idsStr.split(',')
    return {
        color: [
            {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                    {
                        offset: 0,
                        color: '#6392FA' // 0% 处的颜色
                    },
                    {
                        offset: 1,
                        color: '#2F69F8' // 100% 处的颜色
                    }
                ],
                global: false // 缺省为 false
            },
            {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                    {
                        offset: 0,
                        color: '#F1F9FF' // 0% 处的颜色
                    },
                    {
                        offset: 1,
                        color: '#E4F2FF' // 100% 处的颜色
                    }
                ],
                global: false // 缺省为 false
            }
        ],
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            x: 'right',
            y: 'top',
            itemWidth: 6,
            itemHeight: 6
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
            width: '90%',
            height: '90%'
        },
        xAxis: {
            type: 'value',
            boundaryGap: [0, '20%'],
            axisTick: {
                show: false, // 是否显示坐标轴刻度 默认显示
            },
            axisLine: {
                lineStyle: { // 坐标轴线线的颜色
                    color: '#D3D8E5',
                    width: 1
                }
            },
            splitLine :{
                lineStyle:{
                    type:'dashed'//虚线
                }
            },
            axisLabel: {
                color: '#666666',
                rotate: 40
            }
        },
        yAxis: {
            type: 'category',
            data: qsName,
            // boundaryGap: [0, 20],
            axisTick: {
                show: false, // 是否显示坐标轴刻度 默认显示
            },
            axisLine: {
                lineStyle: { // 坐标轴线线的颜色
                    color: '#D3D8E5',
                    width: 1
                }
            },
            axisLabel: {
                color: '#666666',
                fontSize: 12,
                lineHeight: 12,
                fontFamily: 'PingFangSC-Medium',
                fontWeight: 400
            },
        },
        series: [
            {
                name: '达成数',
                type: 'bar',
                data: seriesData,
                barWidth: 12, // 柱的宽度
                // 柱上面的数值配置
                label: {
                    show: true, // 显示值
                    position: "right", // 显示位置
                    color: "#333333",
                },
                // 每一个条目的样式配置
                itemStyle: {
                    barBorderRadius: [0, 34, 34, 0], // 圆角
                }
            }
        ],
        trigger: 'axis',
        axisPointer: {
            type: 'shadow'
        },
        position: function(point, params, dom, rect, size){ // point: 鼠标位置
            var tipHeight = point[1] + size.contentSize[1]; // contentSize: 提示dom 窗口大小
            if(tipHeight > size.viewSize[1] ){              // viewSize: echarts 容器大小
                return [point[0]+40, point[1]-size.contentSize[1]];
            } else if(point[1] < size.contentSize[1]){
                return [point[0]+40, point[1]+20];
            } else {
                return point;
            }
        }
    };

}

/**
 *  @description: 绘制折线图，或柱状图或折线图与柱状图组合
 *  @author: 马晓娟
 *  @date: 2020/10/28 21:49
 *  @param echartInitConfig
 *  @param headData 折线图顶部展示的数据，数组格式
 *  @param xAxisData 折线图x轴展示的数据，数组格式
 *  @param yAxisData 折线图y轴展示的数据，数组格式
 *  @param seriesData 折线的数据，例：seriesData = [{data: [2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 15.6, 12.2, 32.6, 20.0, 6.4, 3.3],colorStyle: {colorA: '#6392FA', colorB: '#2F69F8'}, name: '活动营销', shadowColor: {colorA: '#6392FA', colorB: '#2F69F8'}, stack: 'one', type: 'line'}];
 */
export function targetComposeLineAndHistogramProgress(echartInitConfig, xAxisData, yAxisData, seriesData, headData = [{name: null,value: ''}],legendFlag= false,rotateAng = 0, xAxisName = '', toolTip = {
  trigger: 'axis',
  axisPointer: {
    type: 'cross',
    crossStyle: {
      color: '#999'
    }
  },
  formatter: '{b}: {c}'
}, customGrid) {
  let seriesTemp = new Array();
  let areaStyle = {};
  let colorArr = new Array();
  seriesData.forEach((item) => {
    if ($utils.isEmpty(item.colorStyle)) {
      item['colorStyle'] = {colorA: '#2F61F8', colorB: '#8A2FF8'};
    }
      if ($utils.isEmpty(item.shadowColor)) {
      item['shadowColor'] = {colorA: '#fcf7ec', colorB: '#fefdfd'};
      areaStyle = {
        normal: {
          color: new echartInitConfig.echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {offset: 0, color: item.shadowColor.colorA},
            {offset: 1, color: item.shadowColor.colorB},
          ])
        }
      }
    }
    let itemStyle = {}
    let lineStyle = {}
    if(item.type === 'line'){
      lineStyle = {
        normal: {
          width: 2,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1, //从左到右
            y2: 0,
            colorStops: [{
              offset: 0,
              color: item.colorStyle.colorA// 0% 处的颜色
            }, {
              offset: 1,
              color: item.colorStyle.colorB   // 100% 处的颜色
            }],
            globalCoord: false // 缺省为 false
          }
        }
      }
      let colorItem = item.colorStyle.colorB
      colorArr.push(colorItem)
     }else{
      itemStyle = {
        normal: {
          color: new echartInitConfig.echarts.graphic.LinearGradient(
              0, 1, 0, 0, //左、下、右、上
              [
                {offset: 0, color: item.colorStyle.colorA},
                {offset: 1, color: item.colorStyle.colorB},
              ]
          )
        },
        emphasis: {
          color: new echartInitConfig.echarts.graphic.LinearGradient(
              0, 1, 0, 0,
              [
                {offset: 0, color: item.colorStyle.colorA},
                {offset: 1, color: item.colorStyle.colorB},
              ]
          )
        }
      }
    }
    if(item.type === 'bar'){
      seriesTemp.push({
        name: item.name || '组合图',
        type: 'bar',
        barGap: item.barGap || null,
        z: item.z,
        yAxisIndex: item.yAxisIndex || null,
        barWidth: item.barWidth || null,
        symbol: 'none',  //取消折点圆圈
        itemStyle: itemStyle || null,
        data: item.data,
      })
    }else{
      seriesTemp.push({
        name: item.name || '组合图',
        type:  'line',
        yAxisIndex: item.yAxisIndex || null,
        symbol: 'none',  //取消折点圆圈
        lineStyle: lineStyle || null,
        data: item.data,
        areaStyle: item.isShadowColor ? areaStyle : null
      })
    }
  });
  let yAxisTemp = new Array();
    yAxisData.forEach((item) => {
      yAxisTemp.push({
          type: item.type || 'value',
          name: item.name || null,
          min: item.min || 0,
          max: item.max || 1000,
          nameTextStyle: {
              color: '#909AA9'
          },
          axisLine: item.axisLine || { lineStyle: {color: '#909AA9'}},
          interval: item.interval || 200,
          axisTick: item.axisTick || {            // 坐标轴小标记
            length: 5,         // 属性length控制线长
            lineStyle: {       // 属性lineStyle控制线条样式
              color: '#909AA9',
              width: 1
            }
          },
          splitLine: item.splitLine || {            // 坐标轴线
            show: true,        // 默认显示，属性show控制显示与否
            lineStyle: {       // 属性lineStyle控制线条样式
              color: '#d7d7d7',
              width: 1,
              type: 'solid'
            }
          },
          axisLabel:  item.axisLabel || {formatter: '{value}', color: '#909AA9', fontSize: 10, lineHeight: 10, fontFamily: 'PingFangSC-Medium'},
        })
    });
  const grid = customGrid ? customGrid : xAxisName ? {} : {
    left: '8px',
    right: '5px',
    bottom: '16px',
    containLabel: true
  }
  return {
        grid,
        color: colorArr,      //关键加上这句话，legend的颜色和折线的自定义颜色就一致了
        legend: legendFlag ? {
                orient: 'horizontal',
                top: '9px',
                bottom: '15px',
                data: headData,
                icon: 'circle',
                itemWidth: 8,  // 设置宽度
                itemHeight: 8, // 设置高度
                itemGap: 22, // 设置间距
                formatter: (params) => {
                    const total = headData
                    for (let i = 0; i < total.length; i++) {// this.pieXY  这个数据中有名称和次数
                        if (total[i].name === params) {   //两个名称进行对比，取出对应的次数
                            return '{a|'+params +'}' + ' ' +'{b|'+ total[i].value +'}'   //然后return你需要的legend格式即可
                        }
                    }
                },
                textStyle:{
                    fontSize: 12,
                    rich:{
                        a:{
                            fontSize:12,
                            verticalAlign:'center',
                            align:'center',
                            lineHeight:15,
                            color: '#8C8C8C',
                            fontFamily: 'PingFangSC-Regular'
                        },
                        b:{
                            fontSize:12,
                            align:'center',
                            lineHeight:15,
                            color: '#000',
                            fontFamily: 'PingFangSC-Regular'
                        }
                    }
                }
            }:
            {
                data: headData,
            },
        tooltip: toolTip,
        xAxis: [
            {
                name: xAxisName,
                nameGap: 0,
                type: 'category',
                data: xAxisData,
                axisPointer: {
                    type: 'shadow'
                },
                axisTick: {
                    show: false
                },
                nameTextStyle: {
                    color: '#909AA9'
                },
                axisLine: {lineStyle: {color: '#EBEDF5'}},
                axisLabel: {
                    color: '#909AA9',
                    fontSize: 10,
                    lineHeight: 10,
                    fontFamily: 'PingFangSC-Medium',
                    interval: 0, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
                    rotate: rotateAng,   //调整数值改变倾斜的幅度（范围-90到90）
                },
            }
        ],
        yAxis: yAxisTemp,
        dataZoom: [
            {
                type: 'inside',
                throttle: '50',
                minValueSpan: 15,
                maxValueSpan: 20,
                start: 0,
                zoomLock: false,
                end: 15
            }
        ],
        series: seriesTemp
    };
}


export function CharacterWrap(params,provideNumber){
    var newParamsName = "";
    var paramsNameNumber = params.length;
    // var provideNumber = 5;  //一行显示几个字
    var rowNumber = Math.ceil(paramsNameNumber / provideNumber);
    if (paramsNameNumber > provideNumber) {
        for (var p = 0; p < rowNumber; p++) {
            var tempStr = "";
            var start = p * provideNumber;
            var end = start + provideNumber;
            if (p == rowNumber - 1) {
                tempStr = params.substring(start, paramsNameNumber);
            } else {
                tempStr = params.substring(start, end) + "\n";
            }
            newParamsName += tempStr;
        }
    } else {
        newParamsName = params;
    }
    return newParamsName
}


