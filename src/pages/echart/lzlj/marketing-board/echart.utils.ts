

/**
 * 折线图
 * <AUTHOR>
 * @date	2023/6/28 16:29
 * @param isShowSlider 是否滑动显示
 */
export function targetLineChart(echartInitConfig, seriesData, xAxisData = [], isShowSlider = false) {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: true, // true:不从0刻度开始; false:从0刻度开始
      axisTick: {
        show: false
      },
      data: xAxisData
    },
    yAxis: {
      type: 'value',
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#E6EAF4',
          type: 'dashed'//虚线
        }
      }
    },
    series: seriesData,
    dataZoom: [
      {
        type: 'slider',
        xAxisIndex: 0,
        startValue: 0,
        endValue: 4,
        height: 6,
        show: isShowSlider,
        filterMode: 'none',
        zoomLock: true,
        bottom: 0,
        showDetail: false,
        handleSize: '0%',
        dataBackground: {
          lineStyle: {
            opacity: 0
          },
          areaStyle: {
            opacity: 0
          }
        },
        fillerColor: '#D8D8D8',
        borderColor: '#fff'
      },
      {
        type: 'inside',
        xAxisIndex: 0,
        startValue: 0,
        endValue: 4,
        height: 10,
        filterMode: 'none',
        zoomLock: true
      }
    ],
  }
}

/**
 * 柱状图-横坐标为y轴
 * <AUTHOR>
 * @date	2023/6/29 17:52
 */
export function targetColumnYChart(
  echartInitConfig,
  yAxisData = [],
  seriesData,
  top= '4%',
  legend= true,
  config: {
    legendItemHeight: 14,
    legendItemWidth: 25,
    yLineType: 'solid',
    yLineColor: '#333',
    xLineColor: '#333'
  }) {
  return {
    legend: {
      show: legend,
      icon: 'circle',
      textStyle: {
        fontSize: 12
      },
      itemHeight: config.legendItemHeight,
      itemWidth: config.legendItemWidth,
      selectedMode: false
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: top,
      containLabel: true
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01],
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#E6EAF4',
          type: 'dashed' //虚线
        }
      },
      axisLine: {
        lineStyle: {
          color: config.xLineColor
        }
      },
    },
    yAxis: {
      type: 'category',
      boundaryGap: [0, 20],
      z: 4,
      axisTick: {
        show: false,
        alignWithLabel: true
      },
      axisLine: {
        lineStyle: {
          type: config.yLineType,
          color: config.yLineColor
        }
      },
      axisLabel: {
        show: true,
        textStyle: {
          fontSize: 12,
          lineHeight: 14,
          color: '#999'
        },
        interval: 0,
        formatter: function (params) {    //字数太长显示...
          let val = "";
          let val1 = "";
          if (params.length > 0 && params.length < 6) {
            return params;
          }
          if (params.length < 11 && params.length > 5) {
            val = params.substring(0, 5) + "\n";
            val1 = params.substring(5, 10);
            return val.concat(val1);

          }
          if (params.length > 10) {
            val = params.substring(0, 5) + "\n";
            val1 = params.substring(5, 9) + '....';
            return val.concat(val1);
          }
        }
      },
      data: yAxisData,
    },
    series: seriesData
  }
}
