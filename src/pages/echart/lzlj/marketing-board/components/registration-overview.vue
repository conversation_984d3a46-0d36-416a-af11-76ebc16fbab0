<!--
营销6.0看板-登记人员总览
<AUTHOR>
@date	2023/6/29 15:22
-->
<template>
    <view class="registration-overview">
        <line-title title="登记人员总览" tips="此图以活动次数进行排序"></line-title>

        <!-- 分割线 -->
        <view class="split-line"></view>

        <!-- 图表 -->
        <view class="echart-area">
            <link-echart :option="registerOption" v-if="showEcharts" :height="echartsHeight + 'px'"
                         :force-use-old-canvas="false"/>
            <view v-else>暂无数据</view>
        </view>
        <view class="more" @tap="showMore()" v-if="showEcharts">
            点击查看登记人员排行榜top20>
        </view>

        <!-- 排行榜弹窗 -->
        <rank-list ref="rankList" :table-data="tableData" :label-config="labelConfig"
                   :tabs="overviewBoard" :chooseTarget="chooseTarget" @changeTab="changeTab"/>
    </view>
</template>

<script>
import LineTitle from '../../../../lzlj/components/line-title';
import RankList from './rank-list';
import {targetColumnYChart} from '../echart.utils';

export default {
    name: 'registration-overview',
    components: {LineTitle, RankList},
    props: {
        safeType: String,
        pickOrg: Object,
        userInfo: Object,
        dimensions: Object
    },
    data() {
        return {
            overviewBoard: [
                {name: '活动场次数', label: 'joinRegisterSum', color: '#8BF9A3', topType: 'userRegister'},
                {name: '打卡天数', label: 'signPictureSum', color: '#9BBBFA', topType: 'userSign'},
                {name: '小酒扫码数(瓶)', label: 'productScanSum', color: '#FBE259', topType: 'userScan'},
                {name: '小酒开瓶数(瓶)', label: 'productOpenSum', color: '#FBCCD2', topType: 'userOpen'},
                {name: '常规装动销数(瓶)', label: 'productSaleSum', color: '#65D9DC', topType: 'userSales'}
            ], // 指标
            chooseTarget: {}, // 选中的指标
            registerOption: null,
            tableData: [],
            labelConfig: [],
            showEcharts: false,
            echartsHeight: 130
        }
    },
    async mounted() {
        this.chooseTarget = this.overviewBoard[0];
        await this.queryEcharts();
    },
    methods: {
        /**
         * 切换选中页签
         * <AUTHOR>
         * @date	2023/7/6 14:38
         * @param tab 当前选中tab
         */
        changeTab(tab) {
            this.chooseTarget = tab;
            this.showMore();
        },
        /**
         * 获取图表数据
         * <AUTHOR>
         * @date	2023/7/5 14:11
         * @param dimensions 当前选中的时间范围
         * @param size 当前查询的数据条数（10：echarts图表  20：排行榜）
         */
        async queryEcharts(dimensions = {}, size = 10) {
            size === 10 && (this.showEcharts = false);
            const dimension = dimensions.value ? dimensions : this.dimensions;
            // 表格使用/headActivityBoardByUserTop接口
            const url = size === 10
                ? 'export/link/headActivityBoard/headActivityBoardByUserTop'
                : 'export/link/headActivityBoard/headActivityBoardByUserTargetTop';
            const param = {
                timeRange: dimension.value,
                safeType: this.safeType,
                attr1: this.safeType === 'Position' ? this.userInfo.postnId : this.pickOrg.orgId,
                topType: this.chooseTarget.topType,
                size: size
            };
            size === 10 && (delete param.topType);
            try {
                this.$utils.showLoading();
                const {success, rows} = await this.$http.post(url, param);
                if (success) {
                    this.$utils.hideLoading();
                    if (!rows.length) {
                        return;
                    }
                    this.showEcharts = true;
                    if (size === 10) {
                        rows.reverse();
                        this.echartsHeight = rows.length > 1 ? 50 + 90 * rows.length : 200;
                        let obj = {};
                        const arr1 = rows.map((item) => item.fstName);
                        this.overviewBoard.map((t) => {
                            obj[t.label] = rows.map((item) => item[t.label] || 0);
                            return {}
                        });
                        this.setEcharts(arr1, obj);
                    } else {
                        this.tableData = rows;
                        this.tableData.unshift({
                            index: '排行',
                            userCode: '员工号',
                            fstName: '姓名',
                            [this.chooseTarget.label]: this.chooseTarget.name
                        });
                        this.$refs.rankList.$refs.rankDialog.show();
                    }
                }
            } catch (e) {
                this.$utils.hideLoading();
            }
        },
        /**
         * 绘制图表
         * <AUTHOR>
         * @date	2023/7/3 15:18
         */
        setEcharts(arr1, obj) {
            this.registerOption = null;
            const seriesData = this.overviewBoard.map((item) => ({
                name: item.name,
                type: 'bar',
                barWidth: 9,
                itemStyle: {
                    normal: {
                        color: item.color
                    }
                },
                label: {
                    show: true, //显示数字
                    position: 'right',
                    textStyle: {
                        fontSize: '9', //柱状上的显示的文字
                        // color: '#333',
                        fontWeight: 500
                    }
                },
                data: obj[item.label]
            }));
            this.registerOption = echartInitConfig => targetColumnYChart(echartInitConfig,
                arr1, seriesData, 70, true);
        },
        /**
         * 查看排行榜
         * <AUTHOR>
         * @date	2023/7/3 15:18
         */
        showMore() {
            this.labelConfig = [
                {label: '排行', field: 'index', width: '12%'},
                {label: '员工号', field: 'userCode', width: '34%'},
                {label: '姓名', field: 'fstName', width: '22%'},
                {label: this.chooseTarget.name, field: this.chooseTarget.label, width: '32%'}
            ];
            this.queryEcharts({}, 20);
        },
    }
}
</script>

<style lang="scss">
.registration-overview {
    background: #fff;
    border-radius: 24px;
    margin-bottom: 24px;
    padding-bottom: 24px;

    .split-line {
        height: 1px;
        background: #ddd;
        margin: 24px;
    }

    .echart-area {
        width: 100%;
        min-height: 200px;
        @include flex-center-center;
        font-size: 26px;
        color: #ccc;
    }

    .more {
        color: #1364D0;
        margin: 10px;
        text-align: right;
        font-size: 28px;

        .red {
            color: red;
        }
    }
}
</style>
