<!--
营销6.0看板-营销6.0总览
<AUTHOR>
@date	2023/6/28
-->
<template>
    <view class="marketing-overview">
        <line-title title="营销6.0总览" tips="点击可查看各指标对应本周/月/年走势"></line-title>
        <!-- 指标说明 -->
        <link-alert icon="mp-info" status="primary" class="top-alert" v-if="showAlert">{{chooseAlert.name + '：' + chooseAlert.tip}}</link-alert>
        <!-- 总览指标 -->
        <view class="board-wrap">
            <view v-for="(item, index) in overviewBoard" class="board-item-wrap" @tap="chooseItem(item)" :key="index">
                <view class="line"></view>
                <view class="board-item" :class="{blue: item.label === chooseTarget.label}">
                    <link-icon icon="mp-info" class="item-icon" @tap.stop="changeAlert(item)"/>
                    <view class="total">{{overviewData[item.label]}}</view>
                    <view class="label">{{item.name}}</view>
                </view>
            </view>
        </view>

        <!-- 分割线 -->
        <view class="split-line"></view>

        <!-- 总览折线图 -->
        <view class="echart-area">
            <link-echart :option="marketingOption" v-if="showEcharts"
                         :force-use-old-canvas="false"/>
            <view v-else>暂无数据</view>
        </view>
    </view>
</template>

<script>
import LineTitle from '../../../../lzlj/components/line-title';
import {targetLineChart} from '../echart.utils';
import log from "../../../../../store/modules/log";
export default {
    name: 'marketing-overview',
    components: {LineTitle},
    props: {
        dimensions: Object,
        safeType: String,
        userInfo: Object,
        pickOrg: Object
    },
    data() {
        return {
            showEchart: true, // 图表是否显示
            marketingOption: null,
            overviewData: {}, // 营销6.0总览数据
            showAlert: false, // 顶部提示框是否显示
            overviewBoard: [
                {name: '合作餐饮总数', label: 'cooperateAccountSum', label2: 'registerAccountSum', graphType: 'graphCooperate',
                    tip: 'a、紫色，展示当前组织，当前所选时间范围内被添加到营销6.0活动中的合作餐饮总数；b、粉色，展示当前组织，当前所选时间范围内有登记记录的终端总数'},
                {name: '驻店人数', label: 'registerUserSum', graphType: 'graphUser',
                    tip: '展示当前组织，当前所选时间范围内做过登记的驻店人数'},
                {name: '活动场次数', label: 'joinRegisterSum', graphType: 'graphRegister',
                    tip: '展示当前组织，当前所选时间范围内状态为“新建”、“已提交”的登记总数'},
                {name: '活动参与人次数', label: 'clientUserSum', graphType: 'graphClient',
                    tip: '展示当前组织，当前所选时间范围内状态为“新建”、“已提交”的登记中的参与人数总和'},
                {name: '小酒开瓶总数(瓶)', label: 'productOpenSum', graphType: 'graphOpen',
                    tip: '展示当前组织，当前所选时间范围内状态为“新建”、“已提交”的登记中的小酒开瓶总数'},
                {name: '小酒扫码总数(瓶)', label: 'productScanSum', graphType: 'graphScan',
                    tip: '展示当前组织，当前所选时间范围内状态为“新建”、“已提交”的登记中的小酒扫码总数'},
                {name: '小酒库存总数(瓶)', label: 'productStockSum', graphType: 'graphStock',
                    tip: '展示当前组织，当前所选时间范围内的小酒库存总数'},
                {name: '常规装动销总数(瓶)', label: 'productSaleSum', graphType: 'graphSales',
                    tip: '展示当前组织，当前所选时间范围内状态为“新建”、“已提交”的登记中的常规装动销总数'}
            ], // 总览指标
            chooseTarget: {}, // 选中的总览指标
            chooseAlert: {}, // 选中的弹窗指标
            showEcharts: false, // 是否显示图表
            timer: null
        }
    },
    created() {
        this.chooseTarget = this.overviewBoard[0];
        this.chooseAlert = this.overviewBoard[0];
    },
    async mounted() {
        await this.queryMarketingOverview();
        await this.queryMarketingEcharts();
    },
    methods: {
        /**
         * 选中展示的指标说明
         * <AUTHOR>
         * @date	2023/7/18 15:51
         */
        changeAlert(item) {
            clearInterval(this.timer);
            this.showAlert = true;
            this.chooseAlert = item;
            this.timer = setTimeout(() => {
                this.showAlert = false;
            }, 3000);
        },
        /**
         * 绘制折线图
         * <AUTHOR>
         * @date	2023/6/29 11:55
         */
        setMarketingEcharts(arr1, arr2 = []) {
            this.marketingOption = null;
            let seriesData = [
                {
                    name: this.chooseTarget.name,
                    type: 'line',
                    data: arr1,
                    label: {
                        show: true,
                        position: 'top'
                    },
                    symbol: 'circle',
                    symbolSize: '8',
                    itemStyle: {
                        normal: {
                            color: '#0C60CF',//改变折线点的颜色
                            lineStyle: {
                                color: '#6654E3' //改变折线颜色
                            }
                        }
                    }
                }
            ];
            if (arr2.length) {
                seriesData.push({
                    name: '有登记终端数',
                    type: 'line',
                    data: arr2,
                    label: {
                        show: true,
                        position: 'bottom'
                    },
                    symbol: 'circle',
                    symbolSize: '8',
                    itemStyle: {
                        normal: {
                            color: '#D886C1',//改变折线点的颜色
                            lineStyle: {
                                color: '#DC93C8' //改变折线颜色
                            }
                        }
                    }
                })
            }
            this.marketingOption = echartInitConfig => targetLineChart(echartInitConfig, seriesData, this.dimensions.range);
        },
        /**
         * 获取折线图数据
         * <AUTHOR>
         * @date	2023/6/29 11:55
         * @param dimensions 当前选中的时间范围
         */
        async queryMarketingEcharts(dimensions = {}) {
            const dimension = dimensions.value ? dimensions : this.dimensions;
            try {
                this.$utils.showLoading();
                const {success, rows} = await this.$http.post('export/link/headActivityBoard/headActivityBoardByTimeGroup', {
                    timeRange: dimension.value,
                    safeType: this.safeType,
                    attr1: this.safeType === 'Position' ? this.userInfo.postnId : this.pickOrg.orgId,
                    graphType: this.chooseTarget.graphType
                });
                if (success) {
                    this.$utils.hideLoading();
                    if (!rows.length) {
                        this.showEcharts = false;
                        return;
                    }
                    this.showEcharts = true;
                    const arr1 = new Array(dimension.rangeNum.length).fill(0);
                    const arr2 = new Array(dimension.rangeNum.length).fill(0);
                    dimension.rangeNum.map((item, index) => {
                        const obj = rows.find((t) => t[dimension.field] === item);
                        if (this.chooseTarget.label2) {
                            !!obj && obj.accountSum && (arr1[index] = obj.accountSum);
                            !!obj && obj[this.chooseTarget.label2] && (arr2[index] = obj[this.chooseTarget.label2]);
                        } else {
                            !!obj && obj[this.chooseTarget.label] && (arr1[index] = obj[this.chooseTarget.label]);
                        }
                        return {};
                    });
                    if (this.chooseTarget.label2) {
                        this.setMarketingEcharts(arr1, arr2);
                    } else {
                        this.setMarketingEcharts(arr1);
                    }
                }
            } catch (e) {
                this.$utils.hideLoading();
            }
        },
        /**
         * 获取营销6.0总览数据
         * <AUTHOR>
         * @date	2023/6/28 17:41
         * @param dimensions 当前选中的时间范围
         * @param pickOrgId 当前选中的组织id
         */
        async queryMarketingOverview(dimensions = {}, pickOrgId = '') {
            const dimension = dimensions.value ? dimensions : this.dimensions;
            const orgId = pickOrgId ? pickOrgId : this.pickOrg.orgId;
            try {
                this.$utils.showLoading();
                const {success, rows} = await this.$http.post('export/link/headActivityBoard/countHeadActivityBoardByTime', {
                    timeRange: dimension.value,
                    safeType: this.safeType,
                    attr1: this.safeType === 'Position' ? this.userInfo.postnId : orgId
                });
                if (success) {
                    this.$utils.hideLoading();
                    this.overviewData = rows;
                }
            } catch (e) {
                this.$utils.hideLoading();
            }
        },
        /**
         * 选中总览指标
         * <AUTHOR>
         * @date	2023/6/29 14:30
         * @param item 当前选中的纵览指标
         */
        async chooseItem(item) {
            this.chooseTarget = item;
            await this.queryMarketingEcharts();
        }
    }
}
</script>

<style lang="scss">
.marketing-overview {
    background: #fff;
    border-radius: 24px;
    margin-bottom: 24px;
    padding-bottom: 24px;

    .top-alert {
        margin-top: 24px;
    }

    .board-wrap {
        padding: 24px 24px 0 24px;
        display: flex;
        flex-wrap: wrap;

        .board-item-wrap {
            width: 33%;
            @include flex-start-center;
            margin-bottom: 24px;

            .board-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                flex: 1;
                position: relative;

                .total {
                    margin-bottom: 14px;
                    font-size: 30px;
                    font-weight: bold;
                }

                .item-icon {
                    padding: 0 10px 10px 10px;
                    position: absolute;
                    top: 0;
                    right: 0;
                    color: #5C8DFA;
                    font-size: 26px;
                }

                .label {
                    font-size: 24px;
                }
            }

            .blue {
                color: #543FE0;
            }

            .red {
                color: red;
            }

            .line {
                width: 2px;
                height: 50px;
                background-color: #ddd;
            }

            &:nth-child(3n+1) {
                .line {
                    display: none;
                }
            }
        }
    }

    .split-line {
        height: 1px;
        background: #ddd;
        margin: 24px;
    }

    .echart-area {
        width: 100%;
        min-height: 200px;
        @include flex-center-center;
        font-size: 26px;
        color: #ccc;
    }
}
</style>
