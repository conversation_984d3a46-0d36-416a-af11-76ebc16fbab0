<!--
营销6.0看板-活动用酒总览
<AUTHOR>
@date	2023/6/29 15:19
-->
<template>
    <view class="activity-wine-overview">
        <line-title title="活动用酒总览" tips="此图以小酒开瓶数量进行排序"></line-title>

        <!-- 分割线 -->
        <view class="split-line"></view>

        <!-- 四种指标图表 -->
        <view class="echart-area">
            <link-echart :option="activityOption" v-if="showActivityEcharts" :height="echartsActivityHeight + 'px'"
                         :force-use-old-canvas="false"/>
            <view v-else>暂无数据</view>
        </view>
        <view class="more" @tap="showMore('overviewBoard')" v-if="showActivityEcharts">
            点击查看参与活动次数排行榜top20>
        </view>

        <!-- 常规装动销数图表 -->
        <view class="select-dimension">
            <select-button label="常规装动销数(瓶)" :selected-flag="true" :isFormatName="false"></select-button>
        </view>
        <view class="echart-area">
            <link-echart :option="commonOption" v-if="showCommonEcharts" :height="echartsCommonHeight + 'px'"
                         :force-use-old-canvas="false"/>
            <view v-else>暂无数据</view>
        </view>
        <view class="more" @tap="showMore('common')" v-if="showCommonEcharts">
            点击查看常规装动销数排行榜top20>
        </view>

        <!-- 参与活动次数排行榜弹窗 -->
        <rank-list ref="overviewBoard" :table-data="tableData" :label-config="labelConfig"
                   :tabs="overviewBoard" :chooseTarget="chooseTarget" @changeTab="changeTab"/>

        <!-- 常规装动销数排行榜弹窗 -->
        <rank-list ref="common" :table-data="tableData" :label-config="labelConfig"
                    :chooseTarget="common[0]"/>
    </view>
</template>

<script>
import LineTitle from '../../../../lzlj/components/line-title';
import SelectButton from '../../components/select-button';
import RankList from './rank-list';
import {targetColumnYChart} from '../echart.utils';

export default {
    name: 'activity-wine-overview',
    components: {LineTitle, SelectButton, RankList},
    props: {
        indexName: Object,
        safeType: String,
        pickOrg: Object,
        userInfo: Object,
        dimensions: Object
    },
    data() {
        return {
            overviewBoard: [
                {name: '小酒开瓶数(瓶)', label: 'productOpenSum', color: '#FBE259',
                    topType: 'wineOpenType', barWidth: 9, fontSize: 9},
                {name: '小酒扫码数(瓶)', label: 'productScanSum', color: '#9BBBFA',
                    topType: 'wineScanType', barWidth: 9, fontSize: 9},
                {name: '小酒库存数(瓶)', label: 'productStockSum', color: '#76F792',
                    topType: 'wineStockType', barWidth: 9, fontSize: 9},
                {name: '小酒进货数(瓶)', label: 'productInSum', color: '#FBCCD2',
                    topType: 'wineInType', barWidth: 9, fontSize: 9}
            ], // 指标
            common: [
                {name: '常规装动销数(瓶)', label: 'productSaleSum', color: '#6595F2',
                    topType: 'wineSaleType', barWidth: 10, barGap: 20, fontSize: 10}
            ], // 常规装动销数
            chooseTarget: {}, // 选中的指标
            tableData: [], // 排行榜数据
            labelConfig: [], // 排行榜表头配置
            orgType: {
                true: {label: '终端名称', field: 'accountName'},
                false: {label: '组织名称', field: 'organizationName'}
            },
            activityOption: null,
            commonOption: null,
            echartsActivityHeight: null, // 四种指标图表高度
            echartsCommonHeight: null,  // 规装动销数图表高度
            showActivityEcharts: false,  // 是否显示四种指标图表
            showCommonEcharts: false  // 是否显示常规装动销数图表
        }
    },
    async mounted() {
        this.chooseTarget = this.overviewBoard[0];
        await this.queryEcharts('overviewBoard');
        await this.queryEcharts('common');
    },
    methods: {
        /**
         * 切换选中页签
         * <AUTHOR>
         * @date	2023/7/5 17:57
         * @param tab 当前选中tab
         */
        changeTab(tab) {
            this.chooseTarget = tab;
            this.showMore('overviewBoard');
        },
        /**
         * 获取图表数据
         * <AUTHOR>
         * @date	2023/7/5 14:11
         * @param target 指标(overviewBoard: 四种指标  common: 常规装动销数)
         * @param dimensions 当前选中的时间范围
         * @param size 当前查询的数据条数(10：echarts图表  20：排行榜)
         */
        async queryEcharts(target, dimensions = {}, size = 10) {
            const dimension = dimensions.value ? dimensions : this.dimensions;
            const fourEchart = size === 10 && target !== 'common';
            fourEchart && (this.showActivityEcharts = false);
            const commonEchart = size === 10 && target === 'common';
            commonEchart && (this.showCommonEcharts = false);
            // 常规装动销数柱状图、四种指标表格使用/headActivityBoardByWineTop接口
            const url = fourEchart
                ? 'export/link/headActivityBoard/headActivityBoardByWineAllTop'
                : 'export/link/headActivityBoard/headActivityBoardByWineTop';
            const param = {
                timeRange: dimension.value,
                safeType: this.safeType,
                attr1: this.safeType === 'Position' ? this.userInfo.postnId : this.pickOrg.orgId,
                topType: target === 'overviewBoard' ? this.chooseTarget.topType : this.common[0].topType,
                size: size
            };
            // 接口/headActivityBoardByWineAllTop不需要topType参数
            fourEchart && (delete param.topType);
            try {
                this.$utils.showLoading();
                const {success, rows} = await this.$http.post(url, param);
                if (success) {
                    this.$utils.hideLoading();
                    if (size === 10) {
                        if (!rows.length) {
                            return;
                        }
                        if (fourEchart) {
                            this.showActivityEcharts = true;
                            this.echartsActivityHeight = rows.length > 1 ? 50 + 80 * rows.length : 200;
                        }
                        if (commonEchart) {
                            this.showCommonEcharts = true;
                            this.echartsCommonHeight = 40 + 50 * rows.length;
                        }
                        rows.reverse();
                        let obj = {};
                        const arr1 = rows.map((item) => item.organizationName || item.accountName || this.pickOrg.text);
                        this[target].map((t) => {
                            obj[t.label] = rows.map((item) => item[t.label] || 0);
                            return {}
                        })
                        this.setEcharts(target, arr1, obj);

                    } else {
                        rows.map((item) => {
                            if (!item[this.indexName.field]) {
                                item[this.indexName.field] = this.pickOrg.text;
                            }
                            return null;
                        })
                        this.tableData = rows;
                        this.tableData.unshift({
                            index: '排行',
                            [this.indexName.field]: this.indexName.label,
                            [this.chooseTarget.label]: this.chooseTarget.name
                        });
                        this.$refs[target].$refs.rankDialog.show();
                    }
                }
            } catch (e) {
                this.$utils.hideLoading();
            }
        },
        /**
         * 绘制图表
         * <AUTHOR>
         * @date	2023/6/29 19:12
         * @param target 指标(overviewBoard: 四种指标  common: 常规装动销数)
         * @param arr1 类目轴类目
         * @param obj 各指标对应数据
         */
        setEcharts(target, arr1, obj) {
            const option = target === 'overviewBoard' ? 'activityOption' : 'commonOption';
            const top = target === 'overviewBoard' ? 70 : 10;
            this[option] = null;
            const seriesData = this[target].map((item) => ({
                name: item.name,
                type: 'bar',
                barWidth: item.barWidth,
                barGap: item.barGap,
                itemStyle: {
                    normal: {
                        color: item.color
                    }
                },
                label: {
                    show: true, //显示数字
                    position: 'right',
                    textStyle: {
                        fontSize: item.fontSize, //柱状上的显示的文字
                        // color: '#333',
                        fontWeight: 500
                    }
                },
                data: obj[item.label]
            }));
            this[option] = echartInitConfig => targetColumnYChart(echartInitConfig,
                arr1, seriesData, top, target === 'overviewBoard');
        },
        /**
         * 查看排行榜top20
         * <AUTHOR>
         * @date	2023/6/30 9:47
         * @param type 排行榜类型 (overviewBoard:活动参与次数   common:常规装动销数)
         */
        async showMore(type) {
            const target = type === 'overviewBoard' ? this.chooseTarget : this.common[0];
            this.labelConfig = [
                {label: '排行', field: 'index', width: '10%'},
                {label: this.indexName.label, field: this.indexName.field, width: '50%'},
                {label: target.name, field: target.label, width: '40%'}
            ];
            await this.queryEcharts(type, {}, 20);
        },
    }
}
</script>

<style lang="scss">
.activity-wine-overview {
    background: #fff;
    border-radius: 24px;
    margin-bottom: 24px;
    padding-bottom: 24px;

    .split-line {
        height: 1px;
        background: #ddd;
        margin: 24px;
    }

    .select-dimension {
        margin: 24px;
        display: flex;
    }

    .echart-area {
        width: 100%;
        min-height: 200px;
        @include flex-center-center;
        font-size: 26px;
        color: #ccc;
    }

    .more {
        color: #1364D0;
        margin: 10px;
        text-align: right;
        font-size: 28px;

        .red {
            color: red;
        }
    }
}
</style>
