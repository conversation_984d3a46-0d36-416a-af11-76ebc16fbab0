<!--
营销6.0看板-终端总览
<AUTHOR>
@date	2023/6/29 15:11
-->
<template>
    <view class="terminal-overview">
        <line-title title="终端总览" buttonName="↑↓更新排序" @tap="changeOrder"></line-title>

        <!-- 分割线 -->
        <view class="split-line"></view>

        <!-- 终端总览指标 -->
        <scroll-view scroll-x="true" class="acitve-scroll-view-data">
            <view class="select-dimension">
                <select-button :label="item.name" v-for="(item, index) in overviewBoard" :key="index" is-board
                               @tap="chooseItem(item)" :selected-flag="item.label === chooseTarget.label" ></select-button>
            </view>
        </scroll-view>

        <!-- 图表 -->
        <view class="echart-area">
            <link-echart :option="terminalOption" v-if="showEcharts" :height="echartsHeight + 'px'"
                         :force-use-old-canvas="false"/>
            <view v-else>暂无数据</view>
        </view>

        <view class="more" @tap="showMore" v-if="showEcharts">
            点击查看{{chooseTarget.name}}排行榜top20>
        </view>

        <!-- 排行榜 -->
        <rank-list ref="rankList" :table-data="tableData" :label-config="labelConfig" :chooseTarget="chooseTarget"/>
    </view>
</template>

<script>
import LineTitle from '../../../../lzlj/components/line-title';
import SelectButton from '../../components/select-button';
import RankList from './rank-list';
import {targetColumnYChart} from '../echart.utils'

export default {
    name: 'terminal-overview',
    components: {LineTitle, SelectButton, RankList},
    props: {
        indexName: Object,
        safeType: String,
        pickOrg: Object,
        userInfo: Object,
        dimensions: Object
    },
    data() {
        return {
            overviewBoard: [
                {name: '活动场次数', label: 'joinRegisterSum', color: '#EF9F1B', topType: 'registerType'},
                {name: '活动参与人次数', label: 'clientUserSum', color: '#6595F2', topType: 'clientType'},
                {name: '合作餐饮数量', label: 'cooperateAccountSum', color: '#16B73A', topType: 'cooperateType'}
            ], // 指标
            chooseTarget: {}, // 选中的指标
            terminalOption: null,
            labelConfig: [],
            tableData: [],
            desc: true, // 排序方式（默认降序）
            showEcharts: false,
            echartsHeight: 240
        }
    },
    created() {
        this.chooseTarget = this.overviewBoard[0];
    },
    mounted() {
        this.queryEcharts();
    },
    methods: {
        /**
         * 改变图标排序方式
         * <AUTHOR>
         * @date	2023/8/11 14:15
         */
        changeOrder() {
            this.desc = !this.desc;
            this.queryEcharts();
        },
        /**
         * 获取图表数据
         * <AUTHOR>
         * @date	2023/7/4 19:30
         * @param dimensions 当前选中的时间范围
         * @param size 当前查询的数据条数（10：echarts图表  20：排行榜）
         */
        async queryEcharts(dimensions = {}, size = 10) {
            size === 10 && (this.showEcharts = false);
            const dimension = dimensions.value ? dimensions : this.dimensions;
            try {
                this.$utils.showLoading();
                const {success, rows} = await this.$http.post('export/link/headActivityBoard/headActivityBoardBySafeGroupTop', {
                    timeRange: dimension.value,
                    safeType: this.safeType,
                    attr1: this.safeType === 'Position' ? this.userInfo.postnId : this.pickOrg.orgId,
                    topType: this.chooseTarget.topType,
                    attr3: this.desc ? 'descOrder' : 'ascOrder',
                    size: size
                });
                if (success) {
                    this.$utils.hideLoading();
                    if (!rows.length) {
                        return;
                    }
                    this.showEcharts = true;
                    if (size === 10) {
                        rows.reverse();
                        const arr1 = rows.map((item) => item.organizationName || item.accountName || this.pickOrg.text);
                        const arr2 = rows.map((item) => item[this.chooseTarget.label]);
                        this.echartsHeight = 40 + 50 * rows.length;
                        this.setEcharts(arr1, arr2);
                    } else {
                        rows.map((item) => {
                            if (!item[this.indexName.field]) {
                                item[this.indexName.field] = this.pickOrg.text;
                            }
                            return null;
                        })
                        this.tableData = rows;
                        this.tableData.unshift({
                            index: '排行',
                            [this.indexName.field]: this.indexName.label,
                            [this.chooseTarget.label]: '登记次数'
                        });
                        this.$refs.rankList.$refs.rankDialog.show();
                    }
                }
            } catch (e) {
                this.$utils.hideLoading();
            }
        },
        /**
         * 查看排行榜top20
         * <AUTHOR>
         * @date	2023/6/30 9:47
         */
        async showMore() {
            this.labelConfig = [
                {label: '排行', field: 'index', width: '10%'},
                {label: this.indexName.label, field: this.indexName.field, width: '50%'},
                {label: this.chooseTarget.name, field: this.chooseTarget.label, width: '40%'}
            ];
            await this.queryEcharts(this.dimensions, 20);
        },
        /**
         * 绘制图表
         * <AUTHOR>
         * @date	2023/6/29 19:12
         * @param arr1 类目轴数据
         * @param arr2 展示数据
         */
        setEcharts(arr1, arr2) {
            this.terminalOption = null;
            this.terminalOption = echartInitConfig => targetColumnYChart(echartInitConfig,
                arr1,
                [
                    {
                        name: this.chooseTarget.name,
                        type: 'bar',
                        barWidth: 10,
                        barGap: 20,
                        itemStyle: {
                            normal: {
                                color: this.chooseTarget.color
                            }
                        },
                        label: {
                            show: true, //显示数字
                            position: 'right',
                            textStyle: {
                                fontSize: '10', //柱状上的显示的文字
                                color: '#333',
                                fontWeight: 500
                            }
                        },
                        data: arr2
                    }
                ], 20, false
            );
        },
        /**
         * 选择指标
         * <AUTHOR>
         * @date	2023/6/29 15:39
         */
        async chooseItem(item) {
            if (this.chooseTarget.label === item.label) {
                return;
            }
            this.chooseTarget = item;
            await this.queryEcharts();
        }
    }
}
</script>

<style lang="scss">
.terminal-overview {
    background: #fff;
    border-radius: 24px;
    margin-bottom: 24px;
    padding-bottom: 24px;

    .split-line {
        height: 1px;
        background: #ddd;
        margin: 24px;
    }

    .acitve-scroll-view-data {
        margin: 24px 24px 0 24px;

        .select-dimension {
            display: flex;

            .select-button {
                background-color: #F2F3F6;
                color: #666666;
            }
        }
    }

    .echart-area {
        width: 100%;
        min-height: 200px;
        @include flex-center-center;
        font-size: 26px;
        color: #ccc;
    }

    .more {
        color: #1364D0;
        margin: 10px;
        text-align: right;
        font-size: 28px;

        .red {
            color: red;
        }
    }
}
</style>
