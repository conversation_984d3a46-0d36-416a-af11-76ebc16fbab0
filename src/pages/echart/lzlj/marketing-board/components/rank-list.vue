<!--
营销6.0看板-排行榜表格弹窗
<AUTHOR>
@date	2023/6/30
-->
<template>
    <link-dialog ref="rankDialog" :enableScroll="false" width="90vw" class="rank-list">
        <view class="count-verify-scan-table">
            <!-- tab栏 -->
            <view class="dialog-title title-right" v-if="tabs && tabs.length">
                <scroll-view :scroll-x="true" class="rank-tabs">
                    <view class="tab-wrap">
                        <view v-for="(item, index) in tabs" :key="index" class="tab-item"
                              :class="{'active': item.label === chooseTarget.label}"
                              @tap="changeActive(item, index)">{{item.name}}</view>
                    </view>
                </scroll-view>
                <link-icon icon="mp-close" class="close-icon" @tap="$refs.rankDialog.hide()"/>
            </view>
            <!-- 标题 -->
            <view class="dialog-title" v-else>
                <view>{{chooseTarget.name}}排行榜</view>
                <link-icon icon="mp-close" class="close-icon" @tap="$refs.rankDialog.hide()"/>
            </view>
            <!-- 分割线 -->
            <view class="divide"></view>
            <!-- 表格内容 -->
            <scroll-view class="list-wrap" :scroll-y="true">
                <view v-for="(table, tableIndex) in tableData" :key="tableIndex" class="table-column">
                    <view v-for="(config, configIndex) in labelConfig" :key="configIndex" :style="{width: config.width}"
                          class="table-row" :class="{'bg-blue': tableIndex === 0}">
                        <view v-if="tableIndex === 0" class="table-head">{{config.label}}</view>
                        <view v-else class="data-item">
                            <view v-if="config.field === 'index'" class="detail">{{tableIndex}}</view>
                            <view v-else class="detail">{{table[config.field]}}</view>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </view>
    </link-dialog>
</template>

<script>
export default {
    name: 'rank-list',
    props: {
        labelConfig: {
            type: Array,
            default: []
        },
        tableData: {
            type: Array,
            default: []
        },
        tabs: {
            type: Array,
            default: () => []
        },
        chooseTarget: Object
    },
    data() {
        return {

        }
    },
    methods: {
        /**
         * 切换选中
         * <AUTHOR>
         * @date	2023/7/5 17:53
         */
        changeActive(item, index) {
            this.$emit('changeTab', item);
        }
    }
}
</script>

<style lang="scss">
.rank-list {

    .count-verify-scan-table {
        width: 100%;

        .dialog-title {
            position: relative;
            font-size: 32px;
            color: #333333;
            font-weight: 500;
            @include flex-center-center;

            .rank-tabs {
                width: calc(100% - 50px);

                .tab-wrap {
                    display: flex;

                    .tab-item {
                        white-space: nowrap;
                        text-align: center;
                        //min-width: 25%;
                        font-size: 26px;
                        font-weight: bold;
                        padding: 10px 16px;
                    }
                }

                .active {
                    color: #1364D0;
                }
            }

            .close-icon {
                position: absolute;
                right: 0;
                color: #999;
                font-size: 32px;
            }
        }

        .title-right {
            @include flex-start-center;
        }

        .divide {
            height: 2px;
            margin: 24px 0;
            background-color: #ccc;
        }
    }

    .list-wrap {
        width: 100%;
        font-size: 26px;
        overflow: scroll;
        max-height: 800px;
        min-height: 240px;

        .table-column {
            display: flex;

            .table-row {
                border-right: 1px solid #E6EAF4;
                border-bottom: 1px solid #E6EAF4;
                word-break: break-all;
                white-space: pre-wrap;
                padding: 8px 10px;

                &:first-child {
                    border-left: 1px solid #E6EAF4;
                }

                .data-item {
                    height: 72px;
                    width: 100%;
                    @include flex-center-center;
                }

                .detail {
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    overflow: hidden;
                    text-align: center;
                }
            }
        }

        .bg-blue {
            height: 60px;
            background-color: #C8D7FA;
            @include flex-center-center;
            overflow: hidden;
        }

        .table-head {
            white-space: nowrap;
            font-weight: bold;
            text-overflow: ellipsis;
            overflow: hidden;
        }
    }
}
</style>
