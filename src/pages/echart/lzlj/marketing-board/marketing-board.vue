<!--
营销6.0看板
<AUTHOR>
@date	2023/6/27 14:33
-->
<template>
    <view class="marketing-board">
        <!-- 选择组织弹框 -->
        <position-bottom :user-info="userInfo" :show.sync="dialogFlag" @choose="changeOrg"></position-bottom>
        <!-- 头部指标 -->
        <scroll-view scroll-x="true" class="head-info">
            <view class="select-dimension">
                <!-- 组织 -->
                <select-button :label="pickOrg.text" :selected-flag="true" @tap="showOrgDialog" :downIcon="!positionTypeA"></select-button>
                <!-- 本周/月/财年 -->
                <picker :value="dimensionIndex" @change="dimensionChange" :range="dimensions">
                    <select-button :label="dimensions[dimensionIndex]" :selected-flag="true" downIcon></select-button>
                </picker>
            </view>
        </scroll-view>
        <!-- 营销6.0总览 -->
        <marketing-overview :safeType="safeType" :pickOrg="pickOrg" :dimensions="choosedimension"
                            :userInfo="userInfo" ref="marketingOverview"/>
        <!-- 终端总览 -->
        <terminal-overview :indexName="indexName" :safeType="safeType" :pickOrg="pickOrg"
                           :userInfo="userInfo" :dimensions="choosedimension"
                           ref="terminalOverview"/>
        <!-- 活动用酒总览 -->
        <activity-wine-overview :indexName="indexName" :safeType="safeType" :pickOrg="pickOrg"
                                :userInfo="userInfo" :dimensions="choosedimension"
                                ref="activityWineOverview"/>
        <!-- 登记人员总览 -->
        <registration-overview :safeType="safeType" :pickOrg="pickOrg" :userInfo="userInfo"
                               :dimensions="choosedimension" ref="registrationOverview"/>
    </view>
</template>

<script>
import Taro from '@tarojs/taro';
import MarketingOverview from './components/marketing-overview';
import TerminalOverview from './components/terminal-overview';
import ActivityWineOverview from './components/activity-wine-overview';
import RegistrationOverview from './components/registration-overview';
import PositionBottom from '../components/position-bottom';
import SelectButton from '../components/select-button';

export default {
    name: 'marketing-board',
    components: {MarketingOverview, TerminalOverview, ActivityWineOverview, RegistrationOverview, PositionBottom, SelectButton},
    data() {
        const userInfo = Taro.getStorageSync('token').result;
        console.log('userInfo', userInfo);
        return {
            userInfo, // 用户信息
            positionTypeA: false, // 是否是A类职位
            safeType: '', //数据安全性：Org(组织安全性)、Position(职位安全性)
            orgTypeA: false, // 是否是A类组织类型
            dimensions: ['本周', '本月', '本财年'], // 可选时间维度
            dimensionsArr: [{
                label: '本周',
                value: 'week',
                field: 'actDayWeek',
                range: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                rangeNum: [1, 2, 3, 4, 5, 6, 7]
            }, {
                label: '本月',
                value: 'month',
                field: 'actWeekMonth',
                range: ['第一周', '第二周', '第三周', '第四周', '第五周', '第六周'],
                rangeNum: [1, 2, 3, 4, 5, 6]
            }, {
                label: '本财年',
                value: 'fiscalYear',
                field: 'actMonthYear',
                range: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                rangeNum: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
            }], // 可选时间维度对应值
            dimensionIndex: 0, // 选中的时间维度下标
            choosedimension: {}, // 选中的时间维度
            dialogFlag: false, // 是否打开选择组织弹框
            pickOrg: {}, // 选中的组织
            indexNameObj: {
                true: {label: '终端名称', field: 'accountName'},
                false: {label: '组织名称', field: 'organizationName'}
            },
            indexName: {},
        }
    },
    created() {
        this.choosedimension = this.dimensionsArr[this.dimensionIndex];
        this.pickOrg.orgId = this.userInfo.orgId;
        this.pickOrg.text = this.userInfo.orgName;
        this.pickOrg.orgType = this.userInfo.orgType;
        this.positionTypeA = this.getPositionTypeA(this.userInfo.positionType);
        this.safeType = this.positionTypeA ? 'Position' : 'Org';
        this.orgTypeA = this.getOrgTypeA(this.pickOrg.orgType);
        this.indexName = this.indexNameObj[this.positionTypeA || this.orgTypeA];
    },
    methods: {
        /**
         * 打开选择组织弹框
         * <AUTHOR>
         * @date	2023/7/6 21:26
         */
        showOrgDialog() {
            !this.positionTypeA && (this.dialogFlag = true);
        },
        /**
         * 获取所有图表
         * <AUTHOR>
         * @date	2023/7/3 16:43
         */
        async queryEcharts() {
            await this.$refs.marketingOverview.queryMarketingOverview(this.choosedimension, this.pickOrg.orgId);
            await this.$refs.marketingOverview.queryMarketingEcharts(this.choosedimension);
            await this.$refs.terminalOverview.queryEcharts(this.choosedimension);
            await this.$refs.activityWineOverview.queryEcharts('overviewBoard', this.choosedimension);
            await this.$refs.activityWineOverview.queryEcharts('common', this.choosedimension);
            await this.$refs.registrationOverview.queryEcharts(this.choosedimension);
        },
        /**
         * 选中组织
         * <AUTHOR>
         * @date	2023/6/28 15:08
         * @param item 选中的数据
         */
        async changeOrg(item) {
            if (Object.keys(item).length === 0 || item.orgId === this.pickOrg.orgId) return;
            this.pickOrg = item;
            this.orgTypeA = this.getOrgTypeA(this.pickOrg.orgType);
            this.indexName = this.indexNameObj[this.positionTypeA || this.orgTypeA];
            await this.queryEcharts();
        },
        /**
         * 切换时间维度
         * <AUTHOR>
         * @date	2023/6/28 14:50
         */
        async dimensionChange(e) {
            if (this.dimensionIndex === this.dimensions[Number(e.detail.value)]) {
                return;
            }
            this.dimensionIndex = Number(e.detail.value);
            this.choosedimension = this.dimensionsArr[this.dimensionIndex];
            await this.queryEcharts();
        },
        /**
         * 判断组织类型(后续图表中显示终端名称/组织名称)
         * <AUTHOR>
         * @date	2023/6/30 16:49
         * @param orgType 当前组织类型
         * A类：销售城市SalesCity、销售战区SalesZone、销售区县SalesArea、销售省区Province、销售子公司SalesCompany、销售小组SalesTeam
         * B类：销售片区SalesRegion、销售大区Region、销售公司Company、专营公司BranchCompany、内设部门Department、集团Group
         * A类纵坐标显示终端名称，B类纵坐标显示组织名称
         */
        getOrgTypeA(orgType) {
            const org = [
                'SalesCity',          // 销售城市
                'SalesZone',          // 销售战区
                'SalesArea',          // 销售区县
                'Province',           // 销售省区
                'SalesCompany',       // 销售子公司
                'SalesTeam',          // 销售小组
            ];
            return org.includes(orgType);
        },
        /**
         * 职位类型是否是A类型
         * <AUTHOR>
         * @date	2023/6/27 15:09
         * A类：业务代表Salesman、客服专员CustServiceSpecialist、小组组长SalesTeamLeader、
         * B类：团购经理GroupBuyManager、客户经理AccountManager、区域经理RegionalManager、客服经理CustServiceManager、VIP经理VipManager、
         *     渠道主管ChannelSupervisor、客服主管CustServiceSupervisor、会战指挥长BattleCommander、业务主管SalesSupervisor、
         *     国窖品联部主管BLRegionManager、品牌推广部主管BPRegionManager、战区经理SalesManager、渠道经理、SalesChannelManger
         *     大区内勤RInternalStaff、片区内勤	InternalStaff、城市内勤CInternalStaff、城市经理CityManager、片区经理SalesAreaManager、
         *     大区经理SalesRegionManager、部门专员	DeptSpecialist、部门主管RegionManager、部门经理DeptManager、人资主管HRManager、
         *     片区市场人员InternalMarketStaff、客户档案维护员FileMaintainer、人资专员HRSpecialist、系统管理员SysAdmin、
         *     销售公司总经理SalesGeneralManager、股份公司总经理GeneralManager、品牌公司总经理BrandManager
         */
        getPositionTypeA(positionType) {
            const position = [
                'Salesman',                 // 业务代表
                'CustServiceSpecialist',    // 客服专员
                'SalesTeamLeader',          // 小组组长
                // 'GroupBuyManager',          // 团购经理
                // 'AccountManager',           // 客户经理
                // 'RegionalManager',          // 区域经理
                // 'CustServiceManager',       // 客服经理
                // 'VipManager',               // VIP经理
                // 'ChannelSupervisor',        // 渠道主管
                // 'CustServiceSupervisor',    // 客服主管
                // 'BattleCommander',          // 会战指挥长
                // 'SalesSupervisor',          // 业务主管
                // 'BLRegionManager',          // 国窖品联部主管
                // 'BPRegionManager',          // 品牌推广部主管
                // 'SalesManager',             // 战区经理
                // 'SalesChannelManger',       // 渠道经理
            ];
            return position.includes(positionType);
        }
    }
}
</script>

<style lang="scss">
.marketing-board {
    padding-bottom: 24px;

    .head-info {
        margin: 24px 0;

        .select-dimension{
            display: flex;
            margin-left: 24px;
        }
    }
}
</style>
