/**
* @createdBy  张丽娟
* @date  2020/12/21
* @description 详情报表-库存
*/
<template>
  <link-page class="data-board-content">
    <view class="area-name">{{userInfo.orgName}}</view>
    <view class="stock-dealer">
      <line-title title="经销商库存统计"></line-title>
      <list class="swiper-list">
        <link-swipe-action>
          <item title="销售区域" :desc="stockDealerParam.orgName" @tap="showOrgDialog('stock')" style="background-color: transparent;padding: 14px 0"/>
          <link-swipe-option slot="option" color="primary" @tap="gotoCurrentOrg('stock')">回到当前组织</link-swipe-option>
        </link-swipe-action>
        <link-swipe-action>
          <item title="客户" :desc="stockDealerParam.acctName" @tap="showAccntDialog('stock')" style="background-color: transparent;padding: 14px 0"/>
          <link-swipe-option slot="option" color="primary" @tap="clearAccntName('stock')">清除</link-swipe-option>
        </link-swipe-action>
        <link-swipe-action>
          <item title="产品" :desc="stockDealerParam.prodName" @tap="showProdDialog('stock')" style="background-color: transparent;padding: 14px 0"/>
          <link-swipe-option slot="option" color="primary" @tap="clearProdName('stock')">清除</link-swipe-option>
        </link-swipe-action>
      </list>
      <view class="data-board-table">
        <view class="head">
          <view class="row-item item-1">客户名称</view>
          <view class="row-item item-2">产品</view>
          <view class="row-item item-3">库存量(件)</view>
        </view>
          <link-auto-list :option="autoList" hideCreateButton>
            <template slot-scope="{data,index}">
              <view class="row" :key="index" :data="data" >
                <view class="row-item item-1" @tap="showMessageDialog(data.acctName)">
                  <view class="text" >
                    {{data.acctName}}
                  </view>
                </view>
                <view class="row-item item-2" @tap="showMessageDialog(data.prodName)">
                  <view class="text">
                    {{data.prodName}}
                  </view>
                </view>
                <view class="row-item item-3" @tap="showMessageDialog(data.count)">
                  <view class="text">
                    {{data.count.toFixed(2)}}
                  </view>
                </view>
              </view>
            </template>
            <view slot="top" v-if="autoList.list.length>0">
              <view class="row">
                <view class="row-item item-1" style="height: 39.5px">
                  <view class="text" >
                    总计
                  </view>
                </view>
                <view class="row-item item-2" style="height: 39.5px">
                  <view class="text">
                  </view>
                </view>
                <view class="row-item item-3" style="height: 39.5px" @tap="showMessageDialog(autoListNum)">
                  <view class="text">
                    {{autoListNum}}
                  </view>
                </view>
              </view>
            </view>
            <view slot="noMore">
              加载完成
            </view>
            <view slot="noData">
              暂无数据
            </view>
          </link-auto-list>
      </view>
    </view>
  </link-page>
</template>

<script>
  import LineTitle from "../../../../lzlj/components/line-title";
  import SelectButton from "../../components/select-button";
  import {PreloadImg} from "../../../../../utils/service/PreloadImg";

  export default {
    name: "data-board-content",
    data() {
        const accessGroupOauth = this.$utils.getMenuAccessGroup('', '/pages/echart/lzlj/dealer-data-board/data-board-page');
        let param = {};
        if(this.$utils.isEmpty(accessGroupOauth)){
            param['filtersRaw'] = [
                {id: 'parentOrgId', property: 'parentOrgId', value: '', operator: '='},
                {id: "isEffective_0", property: "isEffective", value: "Y"},
                {id: "isLaunch", property: "isLaunch", value: "Y"},
                {id: "orgType_0", property: "orgType", value: "Dealer",operator: '<>'}
            ]
        } else {
            param['oauth'] = accessGroupOauth;
            param['filtersRaw'] = [
                {id: "isEffective_0", property: "isEffective", value: "Y"},
                {id: "isLaunch", property: "isLaunch", value: "Y"},
                {id: "orgType_0", property: "orgType", value: "Dealer",operator: '<>'}
            ]
        }
        let orgOpiton = new this.AutoList(this, {
        module: 'action/link/orgnization',
        searchFields: ['text','id'],
        param: param,
        sortOptions: null,
        filterOption: null,
        renderFunc: (h, {data, index}) => {
          return (
                  <item key={index} data={data} arrow="false">
                    <view style="display: flex;align-items: center;width: 100%;border-bottom: #f2f2f2">
                      <view
                              style="display: flex;flex-direction: column;justify-content: space-between;align-items: flex-start;overflow: hidden;">
                        <view style="margin-bottom: 10px;">
                          <view style="background: #A6B4C7;border-radius: 4px;line-height: 20px;">
                            <view
                                    style="font-size: 14px;color: #FFFFFF;letter-spacing: 0;line-height: 20px;padding: 1px 4px;">{data.id}
                            </view>
                          </view>
                        </view>
                        <view style="width:100%;">
                          <view
                                  style="font-family: PingFangSC-Semibold;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 20px;text-overflow:ellipsis;-webkit-line-clamp: 2;-webkit-box-orient: vertical;display: -webkit-box;overflow: hidden;word-break: break-all;word-wrap: break-word;white-space: normal">
                            {data.text}
                          </view>
                        </view>
                        <view
                                style="width: 100%;height: 1px;background: #f2f2f2;position: absolute;bottom: 0;left: 0; right: 0;margin: 0 12px;"></view>
                      </view>
                    </view>
                  </item>
          )
        },

      });
      let accntOption = new this.AutoList(this, {
        url: {
          queryByExamplePage: 'action/link/accnt/queryByCityOrgPage'
        },
        stayFields: "acctName,id,acctCode",
        searchFields: ['acctName','id','acctCode'],
        param: {
          attr2: null,
          filtersRaw: [
            {id: 'acctType', property: 'acctType', value: 'Dealer', operator: '='},
            // {id: 'acctStatus', property: 'acctStatus', value: 'Cooperate', operator: '='},
          ]
        },
        sortOptions: null,
        filterOption: null,
        renderFunc: (h, {data, index}) => {
          return (
                  <item key={index} data={data} arrow="false">
                    <view style="display: flex;align-items: center;width: 100%;border-bottom: #f2f2f2">
                      <view
                              style="display: flex;flex-direction: column;justify-content: space-between;align-items: flex-start;overflow: hidden;">
                        <view style="margin-bottom: 10px;">
                          <view style="background: #A6B4C7;border-radius: 4px;line-height: 20px;">
                            <view
                                    style="font-size: 14px;color: #FFFFFF;letter-spacing: 0;line-height: 20px;padding: 1px 4px;">{data.acctCode}
                            </view>
                          </view>
                        </view>
                        <view style="width:100%;">
                          <view
                                  style="font-family: PingFangSC-Semibold;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 20px;text-overflow:ellipsis;-webkit-line-clamp: 2;-webkit-box-orient: vertical;display: -webkit-box;overflow: hidden;word-break: break-all;word-wrap: break-word;white-space: normal">
                            {data.acctName}
                          </view>
                        </view>
                        <view
                                style="width: 100%;height: 1px;background: #f2f2f2;position: absolute;bottom: 0;left: 0; right: 0;margin: 0 12px;"></view>
                      </view>
                    </view>
                  </item>
          )
        },
      });
      let prodOption = new this.AutoList(this, {
        url: {
          queryByExamplePage: 'action/link/product/queryFieldsByExamplePage'
        },
        stayFields: "prodName,id,prodCode",
        searchFields:['prodName','id','prodCode'],
        sortOptions: null,
          param: {
              filtersRaw: [
                  {
                      id: 'source',
                      property: 'source',
                      value: 'MDM',
                      operator: '='
                  }
              ],
          },
        filterOption: null,
        renderFunc: (h, {data, index}) => {
          return (
                  <item key={index} data={data} arrow="false">
                    <view style="display: flex;align-items: center;width: 100%;border-bottom: #f2f2f2">
                      <view
                              style="display: flex;flex-direction: column;justify-content: space-between;align-items: flex-start;overflow: hidden;">
                        <view style="margin-bottom: 10px;">
                          <view style="background: #A6B4C7;border-radius: 4px;line-height: 20px;">
                            <view
                                    style="font-size: 14px;color: #FFFFFF;letter-spacing: 0;line-height: 20px;padding: 1px 4px;">{data.prodCode}
                            </view>
                          </view>
                        </view>
                        <view style="width:100%;">
                          <view
                                  style="font-family: PingFangSC-Semibold;font-size: 16px;color: #262626;letter-spacing: 0;line-height: 20px;text-overflow:ellipsis;-webkit-line-clamp: 2;-webkit-box-orient: vertical;display: -webkit-box;overflow: hidden;word-break: break-all;word-wrap: break-word;white-space: normal">
                            {data.prodName}
                          </view>
                        </view>
                        <view
                                style="width: 100%;height: 1px;background: #f2f2f2;position: absolute;bottom: 0;left: 0; right: 0;margin: 0 12px;"></view>
                      </view>
                    </view>
                  </item>
          )
        },
      });
      let autoList = new this.AutoList(this, {
        url: {
          queryByExamplePage: 'action/link/invPro/dealerInvReportPage'
        },
        searchFields: null,
        sortOptions: null,
        filterOption: null,
        hooks: {
          beforeLoad(option) {
            let filtersRaw = []

            if(this.$utils.isNotEmpty(this.stockDealerParam.acctId)){
              filtersRaw.push({
                id: 'accountId',
                property: 'accountId',
                value: this.stockDealerParam.acctId,
                operator: '='
              })
            }
            if(this.$utils.isNotEmpty(this.stockDealerParam.prodId)){
              filtersRaw.push({
                id: 'prodId',
                property: 'prodId',
                value: this.stockDealerParam.prodId,
                operator: '='
              })
            }
            option.param.filtersRaw = filtersRaw;
            option.param.orgId = this.stockDealerParam.orgId
          },
          afterLoad (data) {
            this.autoListNum = data.num.toFixed(2)
          }
        }
      });
      return {
          accessGroupOauth,//访问组安全性
        stockDealerData: [],
        orgOpiton,
        accntOption,
        prodOption,
        autoList,
        stockDealerParam:{
          orgName: this.userInfo.orgName,
          orgId: this.userInfo.orgId,
          prodName: '',
          parodId: '',
          acctId: '',
          acctName: ''
        },
        autoListNum: null
      }
    },
    components: {LineTitle,SelectButton},
    props:{
      userInfo: {
        type: Object,
        default: function () {
          return {}
        }
      },
    },
    created() {
        if(this.$utils.isEmpty(this.accessGroupOauth)){
            this.orgOpiton.option.param.filtersRaw[0].value = this.userInfo.orgId
        }
        if(this.userInfo.orgType === 'Company'){
            let orgType = {id: "orgType", property: "orgType", value: "BranchCompany"}
            this.orgOpiton.option.param.filtersRaw.push(orgType)
        }
      this.accntOption.option.param.attr2 = this.userInfo.orgId
      this.currentMonthDate = this.$utils.getCurrentMonthDate();
    },
    methods: {
      /**
       * @createdBy  张丽娟
       * @date  2020/11/25
       * @methods showOrgDialog
       * @para
       * @description 当前组织的下级职位弹框
       */
      async showOrgDialog(val){
        const data = await this.$object(this.orgOpiton, {
          pageTitle: "销售区域",
          multiple: false
        });
        this.stockDealerParam.orgName = data.text
        this.stockDealerParam.orgId = data.id
        this.accntOption.option.param.attr2 = data.id
        this.autoList.methods.reload()
      },
      /**
       * @createdBy  张丽娟
       * @date  2020/11/25
       * @methods
       * @para
       * @description 客户弹框
       */
      async showAccntDialog(val){
        const data = await this.$object(this.accntOption, {
          pageTitle: "客户",
          multiple: false,
        });
        this.stockDealerParam.acctId = data.id
        this.stockDealerParam.acctName = data.acctName
        this.autoList.methods.reload()
      },
      /**
       * @createdBy  张丽娟
       * @date  2020/11/25
       * @methods showProdDialog
       * @para
       * @description  产品弹框
       */
      async showProdDialog(val){
        const data = await this.$object(this.prodOption, {
          pageTitle: "产品",
          multiple: false
        });
        this.stockDealerParam.prodId = data.id
        this.stockDealerParam.prodName = data.prodName
        this.autoList.methods.reload()
      },
      /**
       * @createdBy  张丽娟
       * @date  2020/11/25
       * @methods gotoCurrentOrg
       * @para
       * @description 回到当前组织
       */
      gotoCurrentOrg(val){
        this.stockDealerParam.orgId = this.userInfo.orgId
        this.stockDealerParam.orgName = this.userInfo.orgName
        this.autoList.methods.reload()
      },
      /**
       * @createdBy  张丽娟
       * @date  2020/11/25
       * @methods clearAccntName
       * @para
       * @description 清空客户
       */
      clearAccntName(val){
        this.stockDealerParam.acctId = ''
        this.stockDealerParam.acctName = ''
        this.autoList.methods.reload()
      },
      /**
       * @createdBy  张丽娟
       * @date  2020/11/25
       * @methods clearProdName
       * @para
       * @description 清空产品
       */
      clearProdName(val){
        this.stockDealerParam.prodName = ''
        this.stockDealerParam.prodId = ''
        this.autoList.methods.reload()
      },
      /**
       * @createdBy  张丽娟
       * @date  2020/11/25
       * @methods showMessageDialog
       * @para
       * @description 展示表格数据
       */
      showMessageDialog(val){
        this.$message.primary(val)
      },
      /**
       *  @description: 跳转详情
       *  @author: 马晓娟
       *  @date: 2020/10/20 09:48
       */
      gotoItem (type) {
        this.$nav.push('/pages/echart/lzlj/dealer-data-board/data-board-content-page', {type: type});
      },
    }
  }
</script>

<style lang="scss">
  .data-board-content {
    background: white;
    height: 100vh;
    .swiper-list{
      margin: 10px 24px 0;
      width: calc(100% - 48px)
    }
    .scroll-view-data{
      margin-top: 24px;
      margin-bottom: 24px;
      .select-dimension{
        display: flex;
        margin-left: 24px;
      }
    }
    .link-swipe-action{
      border-bottom: 1px solid #eff1f3;
      width: 100%;
    }
    .area-name {
      width: 100%;
      font-size: 24px;
      color: #2F69F8;
      letter-spacing: 0;
      line-height: 72px;
      background: #F0F4FE;
      height: 72px;
      text-align: center;
    }

    .data-board-menu {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      padding: 64px;
      .list-item {
        width: 46%;
        background: #FFFFFF;
        box-shadow: 0 4px 27px 0 rgba(47,105,248,0.12);
        border-radius: 16px;
        height: 296px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-bottom: 32px;
        image{
          height: 96px;
          width: 96px;
          margin-bottom: 32px;
        }
        font-size: 28px;
        color: #262626;
        letter-spacing: 0;
        text-align: center;
        line-height: 28px;
      }
    }
    .data-board-table{
      margin: 24px 24px 24px;
      .head{
        background: #6D96FA;
        display: flex;
        font-size: 24px;
        letter-spacing: 0;
        line-height: 32px;
        color: white;
        .row-item{
          padding: 16px 12px;
          text-align: center;
          color: #fff;
          height: 80px;
          display:flex;
          align-items:center;
          justify-content: center;
          .text{
            display: inline-block;
            font-size:24px;
            overflow:hidden;
            text-overflow:-o-ellipsis-lastline;
            text-overflow:ellipsis;
            display:-webkit-box;
            -webkit-line-clamp:2;
            line-clamp:2;
            -webkit-box-orient:vertical;
          }
        }
        .item-1{
          width: 35%;
        }
        .item-2{
          width: 35%;
        }
        .item-3{
          width: 30%;
        }
      }
      .row{
        display: flex;
        font-size: 24px;
        color: #262626;
        letter-spacing: 0;
        line-height: 32px;
        .row-item{
          padding: 16px 12px;
          text-align: center;
          border-right: 1px solid #DEE7FE;
          border-bottom: 1px solid #DEE7FE;
          color: #262626;
          height: 80px;
          display:flex;
          align-items:center;
          justify-content: center;
          .text{
            display: inline-block;
            font-size:24px;
            overflow:hidden;
            text-overflow:-o-ellipsis-lastline;
            text-overflow:ellipsis;
            display:-webkit-box;
            -webkit-line-clamp:2;
            line-clamp:2;
            -webkit-box-orient:vertical;
          }
        }
        .item-1{
          width: 35%;
          text-align: left;
          background-color: #F4F7FE;
        }
        .item-2{
          width: 35%;
          text-align: left;
        }
        .item-3{
          width: 30%;
          text-align: left;
        }
        .row-item:first-child{
          border-left: 1px solid #DEE7FE;
        }
      }
      .link-sticky-top{
        top: none!important;
        .link-sticky-content{
          width: 100%;
        }
      }
    }
  }
</style>
