/**
* @createdBy  张丽娟
* @date  2020/12/21
* @description 目标配额达成
*/
<template>
    <link-page class="quota-goal-reach">
        <view class="area-name">{{userInfo.orgName}}</view>
        <view class="target-implementation-progress">
            <line-title title="目标/配额执行进度统计"></line-title>
            <scroll-view scroll-x="true" class="scroll-view-data">
                <view class="select-dimension">
                    <select-button :label="targetImplementationParam.orgName?targetImplementationParam.orgName: '全部片区'"
                                   :selected-flag="targetImplementationParam.orgName !== null"
                                   @tap="tapFilterOrganization('implementation')" downIcon></select-button>
                    <select-button label="目标达成" :selected-flag="targetImplementationParam.type === 'targetCom'"
                                   @tap="qoalImplementQuery(null,'targetCom')"></select-button>
                    <select-button label="配额达成" :selected-flag="targetImplementationParam.type  === 'quotaCom'"
                                   @tap="qoalImplementQuery(null,'quotaCom')"></select-button>
                    <select-button label="本月" :isLastDay="true"
                                   :selected-flag="targetImplementationParam.dateType === 'month'"
                                   @tap="qoalImplementQuery($event,'month')" value="month"></select-button>
                    <select-button label="本季" :isLastDay="true"
                                   :selected-flag="targetImplementationParam.dateType === 'quarter'"
                                   @tap="qoalImplementQuery($event,'quarter')" value="quarter"></select-button>
                    <select-button label="本年" :isLastDay="true"
                                   :selected-flag="targetImplementationParam.dateType === 'year'"
                                   @tap="qoalImplementQuery($event,'year')" value="year"></select-button>
                </view>
            </scroll-view>
            <cover-view>
                <view class="target-progress" v-show="showEchart">
                    <view class="goal-count">
                        <link-echart :option="targetProgressOption" dispatchActionName="女"
                                     :height="targetProgressOptionHeight + 'px'"/>
                    </view>
                </view>
            </cover-view>
        </view>
        <view class="goal-quota-content">
            <line-title title="目标/配额执行统计"></line-title>
            <scroll-view scroll-x="true" class="scroll-view-data">
                <view class="select-dimension">
                    <select-button :label="goalQuotaParam.orgName?goalQuotaParam.orgName: '全部片区'"
                                   :selected-flag="goalQuotaParam.orgName !== null"
                                   @tap="tapFilterOrganization('quota')" downIcon></select-button>
                    <select-button label="目标达成" :selected-flag="goalQuotaParam.type === 'targetCom'"
                                   @tap="qoalQuotaQuery(null,'targetCom')"></select-button>
                    <select-button label="配额达成" :selected-flag="goalQuotaParam.type  === 'quotaCom'"
                                   @tap="qoalQuotaQuery(null,'quotaCom')"></select-button>
                    <select-button label="本月" :isLastDay="true" ref="monthButton"
                                   :selected-flag="goalQuotaParam.dateType === 'month'"
                                   @tap="qoalQuotaQuery($event,'month')" value="month"></select-button>
                    <select-button label="本季" :isLastDay="true" :selected-flag="goalQuotaParam.dateType === 'quarter'"
                                   @tap="qoalQuotaQuery($event,'quarter' )" value="quarter"></select-button>
                    <select-button label="本年" :isLastDay="true" :selected-flag="goalQuotaParam.dateType === 'year'"
                                   @tap="qoalQuotaQuery($event,'year')" value="year"></select-button>
                </view>
            </scroll-view>
            <view class="goal-count">
                <link-echart :option="goalQquotaOption" width="100%" v-show="showEchart"/>
                <view class="goal-quota-table">
                    <view class="table-row-title">
                        <view class="column-title">
                            {{'区域'}}
                        </view>
                        <view class="column-title">
                            {{'销量目标'}}
                        </view>
                        <view class="column-title">
                            {{'实际销量'}}
                        </view>
                        <view class="column-title">
                            {{'达成率'}}
                        </view>
                    </view>
                    <view class="echart-no-more" v-if="goalQuotaData.length === 0">
                        暂无数据
                    </view>
                    <view v-for="(data,index) in goalQuotaData" :key="index" :data="data" class="table-row">
                        <view class="column"
                              @tap="showMessageDialog(goalQuotaParam.type === 'targetCom' ? (data.region) : (data.orgName))">
                            <view class="text">
                                {{goalQuotaParam.type === 'targetCom' ? (data.region || '') : (data.orgName || '')}}
                            </view>
                        </view>
                        <view class="column"
                              @tap="showMessageDialog(goalQuotaParam.type === 'targetCom' ? (data.targetValue) : (data.Amount))">
                            <view class="text">
                                {{goalQuotaParam.type === 'targetCom' ? data.targetValue : data.Amount}}
                            </view>
                        </view>
                        <view class="column"
                              @tap="showMessageDialog(goalQuotaParam.type === 'targetCom' ? (data.actualPayment) : (data.AlreadyAmount))">
                            <view class="text">
                                {{goalQuotaParam.type === 'targetCom' ?data.actualPayment: data.AlreadyAmount}}
                            </view>
                        </view>
                        <view class="column">
                            <view class="text">
                                {{goalQuotaParam.type === 'targetCom' ? data.achievementRate + '%' : data.CompletionRate
                                + '%'}}
                            </view>
                        </view>
                    </view>
                </view>
            </view>

        </view>
        <link-dialog ref="positionBottom" position="bottom" height="65vh" class="dialog-bottom" noPadding
                     v-model="dialogFlag" @hide="showEchart = true">
            <view class="model-title">
                <view @tap="backToPrimaryOrganization()" style="width: 40px;color:#2F69F8;line-height: 44px;">返回</view>
                <view class="title" style="padding-left:0;">组织片区</view>
                <view class="iconfont icon-close" @tap="dialogFlag = false;showEchart = true"
                      style="margin-right: 15px;"></view>
            </view>
            <view class="dialog-content">
                <scroll-view scroll-y="true" :style="{'height': 'calc(60vh - 44px)'}"
                             v-if="excludeDealerOrgnizationData.length > 0">
                    <list>
                        <item v-for="(item,index) in excludeDealerOrgnizationData" @tap="clickOrginzation(item)"
                              arrow="false" :key="index">
                            <view class="list-item" slot="title">
                                {{item.text}}
                            </view>
                        </item>
                    </list>
                </scroll-view>
                <view class="link-auto-list-no-data" v-else>
                    <image :src="$imageAssets.noDataImage" mode="aspectFit"/>
                    <text>暂无数据</text>

                </view>
            </view>
        </link-dialog>
    </link-page>
</template>

<script>
    import LineTitle from "../../../../lzlj/components/line-title";
    import Taro from "@tarojs/taro";
    import SelectButton from "../../components/select-button";
    import {targetComposeLineAndHistogramProgress, targetPieChartProgress} from "../../echart.utils";

    export default {
        name: "quota-goal-reach",
        components: {LineTitle, SelectButton},
        data() {
            const accessGroupOauth = this.$utils.getMenuAccessGroup('', '/pages/echart/lzlj/dealer-data-board/data-board-page');
            return {
                accessGroupOauth,//访问组安全性
                total: [],
                currentMonthDate: {},
                dialogFlag: false,
                goalQquotaOption: {}, //BarChart
                targetProgressOption: {}, //PieChart
                targetProgressOptionHeight: (this.$device.systemInfo.windowWidth - 24) * 0.647564 < 226 ? (this.$device.systemInfo.windowWidth - 24) * 0.647564 : 226,
                loadingFlag: false,
                goalQuotaParam: {
                    orgId: this.userInfo.orgId,
                    orgName: this.userInfo.orgName,
                    dateType: 'month',
                    startDate: '',
                    endDate: '',
                    type: 'targetCom'
                },
                targetImplementationParam: {
                    orgId: this.userInfo.orgId,
                    startDate: '',
                    endDate: '',
                    dateType: 'month',
                    orgName: this.userInfo.orgName,
                    type: 'targetCom'

                },
                currentSeries: 1,
                goalQuotaData: [],
                showEchart: true
            }
        },
        props: {
            orgnizationData: {
                type: Array,
                default: function () {
                    return []
                }
            },
            excludeDealerOrgnizationData: {
                type: Array,
                default: function () {
                    return []
                }
            },
            userInfo: {
                type: Object,
                default: function () {
                    return {}
                }
            },
        },
        async mounted() {
            this.currentMonthDate = this.$utils.getCurrentMonthDate();
            this.goalQuotaParam.startDate = this.currentMonthDate.startDate
            this.goalQuotaParam.endDate = this.currentMonthDate.endDate
            this.targetImplementationParam.startDate = this.currentMonthDate.startDate
            this.targetImplementationParam.endDate = this.currentMonthDate.endDate
            if (this.$utils.isEmpty(this.accessGroupOauth)) {
                this.goalQquotaContent()
                this.fetchProgressTableData()
                this.targetProgressPie()
            }
        },
        methods: {
            showMessageDialog(val) {
                this.$message.primary(val)
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/10/28
             * @methods tapFilterOrganization
             * @para
             * @description 片区选择
             */
            tapFilterOrganization(param) {
                this.currentSeries = param
                this.dialogFlag = true
                this.showEchart = false
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/10/28
             * @methods clickOrginzation
             * @para
             * @description 选择片区
             */
            async clickOrginzation(item) {
                switch (this.currentSeries) {
                    case 'quota': {
                        this.goalQuotaParam.orgId = item.id
                        this.goalQuotaParam.orgName = item.text
                        this.goalQquotaContent()
                        this.fetchProgressTableData()
                        break
                    }
                    case 'implementation': {
                        this.targetImplementationParam.orgId = item.id
                        this.targetImplementationParam.orgName = item.text
                        this.targetProgressPie()
                        break
                    }
                }
                this.dialogFlag = false
                this.showEchart = true
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/10
             * @methods backToPrimaryOrganization
             * @para
             * @description 回到主要组织
             */
            async backToPrimaryOrganization() {
                switch (this.currentSeries) {
                    case 'quota': {
                        this.goalQuotaParam.orgId = this.userInfo.orgId
                        this.goalQuotaParam.orgName = this.userInfo.orgName
                        this.goalQquotaContent()
                        this.fetchProgressTableData()
                        break
                    }
                    case 'implementation': {
                        this.targetImplementationParam.orgId = this.userInfo.orgId
                        this.targetImplementationParam.orgName = this.userInfo.orgName
                        this.targetProgressPie()
                        break
                    }
                }
                this.dialogFlag = false
                this.showEchart = true
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/10/28
             * @methods qoalQuotaQuery
             * @para
             * @description 目标/配额执行统计查询
             */
            async qoalQuotaQuery(val, param) {
                switch (param) {
                    case 'targetCom': {
                        this.goalQuotaParam.type = param
                        await this.goalQquotaContent()
                        await this.fetchProgressTableData()
                        break
                    }
                    case 'quotaCom': {
                        this.goalQuotaParam.type = param
                        await this.goalQquotaContent()
                        await this.fetchProgressTableData()
                        break
                    }
                    case 'month': {
                        this.goalQuotaParam.dateType = param
                        this.goalQuotaParam.startDate = val.startDate
                        this.goalQuotaParam.endDate = val.endDate
                        await this.goalQquotaContent()
                        await this.fetchProgressTableData()
                        break
                    }
                    case 'quarter': {
                        this.goalQuotaParam.dateType = param
                        this.goalQuotaParam.startDate = val.startDate
                        this.goalQuotaParam.endDate = val.endDate
                        await this.goalQquotaContent()
                        await this.fetchProgressTableData()
                        break
                    }
                    case 'year': {
                        this.goalQuotaParam.dateType = param
                        this.goalQuotaParam.startDate = val.startDate
                        this.goalQuotaParam.endDate = val.endDate
                        await this.goalQquotaContent()
                        await this.fetchProgressTableData()
                        break
                    }
                }

            },
            /**
             * @createdBy  张丽娟
             * @date  2020/10/28
             * @methods qoalImplementQuery
             * @para
             * @description 目标执行进度统计 查询
             */
            qoalImplementQuery(val, param) {
                switch (param) {
                    case 'targetCom': {
                        this.targetImplementationParam.type = param
                        this.targetProgressPie()
                        break
                    }
                    case 'quotaCom': {
                        this.targetImplementationParam.type = param
                        this.targetProgressPie()
                        break
                    }
                    case 'month': {
                        this.targetImplementationParam.dateType = param
                        this.targetImplementationParam.startDate = val.startDate
                        this.targetImplementationParam.endDate = val.endDate
                        this.targetProgressPie()
                        break
                    }
                    case 'quarter': {
                        this.targetImplementationParam.dateType = param
                        this.targetImplementationParam.startDate = val.startDate
                        this.targetImplementationParam.endDate = val.endDate
                        this.targetProgressPie()
                        break
                    }
                    case 'year': {
                        this.targetImplementationParam.dateType = param
                        this.targetImplementationParam.startDate = val.startDate
                        this.targetImplementationParam.endDate = val.endDate
                        this.targetProgressPie()
                        break
                    }
                }
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/10/27
             * @methods targetProgressPie
             * @para
             * @description 目标执行进度统计 - 饼图
             */
            async targetProgressPie() {
                let targetData = await this.fetchTargetProgress()
                let seriesData = []
                let totalSeriesData = []
                if (this.targetImplementationParam.type === 'targetCom') {
                    // 目标达成
                    seriesData = [
                        {value: this.computeDecimal(targetData.allActualPayment), name: '已完成'},
                        {
                            value: this.computeDecimal(targetData.allTargetValue - targetData.allActualPayment),
                            name: '未完成'
                        }
                    ];
                    totalSeriesData = [{value: this.computeDecimal(targetData.allTargetValue), name: '总目标'}];
                } else {
                    // 配额执行
                    seriesData = [
                        {value: this.computeDecimal(targetData.allAlreadyAmount), name: '已执行'},
                        {value: this.computeDecimal(targetData.allAmount - targetData.allAlreadyAmount), name: '未执行'}
                    ];
                    totalSeriesData = [{value: this.computeDecimal(targetData.allAmount), name: '总配额'}];
                }
                let pieColor = [
                    //     {
                    //   c1: '#FFB701',  //管理
                    //   c2: '#FF5A5A'
                    // },{
                    //   c1: '#6392FA',  //管理
                    //   c2: '#2F69F8'
                    // }
                ];
                this.targetProgressOption = echartInitConfig => targetPieChartProgress(echartInitConfig, seriesData, totalSeriesData, ['45%', '70%'], '45%', pieColor, 90);
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/21
             * @methods fetchTargetProgress
             * @para
             * @description 目标执行进度统计 - 饼图-获取数据
             */
            async fetchTargetProgress() {
                this.$utils.showLoading()
                let param = {
                    orgsId: [
                        {
                            orgId: this.targetImplementationParam.orgId
                        },
                    ],
                    startDate: this.targetImplementationParam.startDate,
                    endDate: this.targetImplementationParam.endDate,
                }
                let url = ''
                if (this.targetImplementationParam.type === 'targetCom') {
                    // 目标达成
                    url = 'action/link/dealerPortal/actualPaymentInfo'
                } else {
                    // 配额执行
                    url = 'action/link/orderSys/queryQuotaExecutionStatisticsByRegion'
                }
                let data = await this.$http.post(url, param);
                if (!data.success) {
                    this.$utils.hideLoading()
                    this.$showError('获取报表数据异常,请重试。');
                    return
                }
                this.$utils.hideLoading()
                let targetData = data.rows[0]
                return targetData
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/23
             * @methods fetchProgressTableData
             * @para
             * @description 目标/配额执行统计-表格
             */
            async fetchProgressTableData() {
                this.$utils.showLoading();
                let param = {
                    orgsId: [
                        {
                            orgId: this.goalQuotaParam.orgId
                        },
                    ],
                    startDate: this.goalQuotaParam.startDate,
                    endDate: this.goalQuotaParam.endDate,
                    summaryFlag: this.goalQuotaParam.orgType === 'SalesCity' ? 'Y' : 'N'
                }
                let url = ''
                if (this.goalQuotaParam.type === 'targetCom') {
                    // 目标达成
                    url = 'action/link/dealerPortal/actualPaymentInfo'
                } else {
                    // 配额执行
                    url = 'action/link/orderSys/queryQuotaExecutionStatisticsByRegion'
                }
                let data = await this.$http.post(url, param);
                if (!data.success) {
                    this.$utils.hideLoading();
                    this.$showError('获取报表数据异常，请重试。');
                    return
                }
                this.$utils.hideLoading();
                if (data.rows.length > 0) {
                    this.goalQuotaData = data.rows[0].data
                    if (this.goalQuotaParam.type === 'targetCom') {
                        this.goalQuotaData.forEach((item, index) => {
                            item.targetValue = this.computeDecimal(item.targetValue)
                            item.actualPayment = this.computeDecimal(item.actualPayment)
                            item.achievementRate = this.computeDecimal(item.achievementRate * 100)
                        })
                    } else {
                        this.goalQuotaData.forEach((item, index) => {
                            item.Amount = this.computeDecimal(item.Amount)
                            item.AlreadyAmount = this.computeDecimal(item.AlreadyAmount)
                            item.CompletionRate = this.computeDecimal(item.CompletionRate * 100)
                        })
                    }
                }
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/12/22
             * @methods computeDecimal
             * @para
             * @description  处理小数点
             */
            computeDecimal(val) {
                let returnValue = 0
                if (val) {
                    returnValue = Number(val).toFixed(2)
                    if (returnValue.indexOf('.00') > -1) returnValue = returnValue.substring(0, returnValue.indexOf('.00'));
                }
                return returnValue
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/23
             * @methods goalQquotaData
             * @para
             * @description 目标/配额执行进度统计-饼图
             */

            async goalQquotaData() {
                this.$utils.showLoading();
                let param = {
                    orgsId: [
                        {
                            orgId: this.goalQuotaParam.orgId
                        },
                    ],
                    startDate: this.goalQuotaParam.startDate,
                    endDate: this.goalQuotaParam.endDate,
                    summaryFlag: this.goalQuotaParam.orgType === 'SalesCity' ? 'Y' : 'N'
                }
                let url = ''
                if (this.goalQuotaParam.type === 'targetCom') {
                    // 目标达成
                    url = 'action/link/dealerPortal/actualPaymentInfoByMonth'
                } else {
                    // 配额执行
                    url = 'action/link/orderSys/queryQuotaExecutionStatistics'
                }
                let data = await this.$http.post(url, param);
                if (!data.success) {
                    this.$utils.hideLoading();
                    this.$showError('获取报表数据异常，请重试。');
                    return
                }
                this.$utils.hideLoading();
                let areaData = data.rows
                return areaData
            },

            /**
             * @createdBy  张丽娟
             * @date  2020/10/27
             * @methods goalQquotaContent
             * @para
             * @description 目标/配额执行统计 - 柱形图
             */
            async goalQquotaContent() {
                let quotaData = await this.goalQquotaData()
                if (quotaData.length === 0) {
                    return
                }
                this.goalQquotaOption = {}
                let [xAxisData, headData, seriesBar1, seriesBar2, seriesLine3, seriesBarNum1, seriesBarNum2, seriesAve] = [[], [], [], [], [], 0, 0, 0]
                if (this.goalQuotaParam.type === 'targetCom') {
                    // 横坐标为月份的数据
                    let mounthData = quotaData[0].data
                    //分离出横坐标数组，纵坐标数组及字段值
                    mounthData.forEach((item, index) => {
                        xAxisData.push(item.months)
                        seriesBar1.push(Number(item.targetValue))
                        seriesBar2.push(Number(item.actualPayment))
                        seriesLine3.push(Number(item.achievementRate))
                        seriesBarNum1 += Number(item.targetValue)
                        seriesBarNum2 += Number(item.actualPayment)
                    })
                } else if (this.goalQuotaParam.type === 'quotaCom') {
                    quotaData.forEach((item, index) => {
                        xAxisData.push(item.month)
                        // 每个月份的数据
                        seriesBar1.push(this.computeDecimal(item.Amount))
                        seriesBar2.push(this.computeDecimal(item.AlreadyAmount))
                        seriesLine3.push(this.computeDecimal(item.CompletionRate))
                        seriesBarNum1 += Number(item.Amount)
                        seriesBarNum2 += Number(item.AlreadyAmount)
                    })
                    seriesAve = seriesBarNum1 > 0 ? seriesBarNum2 / seriesBarNum1 : 0
                }
                seriesBarNum1 = this.computeDecimal(seriesBarNum1)
                seriesBarNum2 = this.computeDecimal(seriesBarNum2)
                seriesAve = this.computeDecimal(seriesAve)
                if (this.goalQuotaParam.type === 'targetCom') {
                    headData = [
                        {value: Number(seriesBarNum1), name: '销售目标'},
                        {value: Number(seriesBarNum2), name: '实际销量'},
                        {value: Number(seriesAve), name: '达成率'}
                    ]
                } else if (this.goalQuotaParam.type === 'quotaCom') {
                    headData = [
                        {value: Number(seriesBarNum1), name: '配额数量'},
                        {value: Number(seriesBarNum2), name: '已用数量'},
                        {value: Number(seriesAve), name: '达成率'}
                    ]
                }
                var arr = seriesBar1.concat(seriesBar2)
                var max = Math.max(...arr)
                max = Math.ceil(max * 1.1 / 100) * 100
                let yAxisData = [
                    {
                        type: 'value',
                        min: 0,
                        max: max,
                        interval: max / 5,
                        yAxisIndex: 0,
                        axisLabel: {
                            formatter: '{value}',
                            color: '#909AA9',
                            fontSize: 10,
                            lineHeight: 10,
                            fontFamily: 'PingFangSC-Medium'
                        },
                        axisTick: {
                            show: false
                        },
                        axisLine: {show: false},
                        splitLine: {    //网格线
                            lineStyle: {
                                type: 'dashed'    //设置网格线类型 dotted：虚线   solid:实线
                            },
                            show: false //隐藏或显示
                        },
                    },
                    {
                        type: 'value',
                        min: 0,
                        max: 100,
                        interval: 20,
                        axisLabel: {
                            formatter: '{value} %',
                            color: '#909AA9',
                            fontSize: 10,
                            lineHeight: 10,
                            yAxisIndex: 1,
                            fontFamily: 'PingFangSC-Medium'
                        },
                        axisTick: {
                            show: false
                        },
                        axisLine: {show: false},
                        splitLine: {    //网格线
                            lineStyle: {
                                type: 'dashed'    //设置网格线类型 dotted：虚线   solid:实线
                            },
                            show: true //隐藏或显示
                        },
                    }
                ]
                let seriesData = [
                    {
                        type: 'bar',
                        data: seriesBar1,
                        name: headData[0].name,
                        colorStyle: {colorA: '#C9E9FF', colorB: '#98CEFF'},
                        z: 0,
                        barGap: "-100%", /*这里设置包含关系，只需要这一句话*/
                        yAxisIndex: 0, //这里要设置哪个y轴，默认是最左边的是0，然后1，2顺序来。
                        // shadowColor: {colorA: '#6392FA', colorB: '#2F69F8'},
                        barWidth: '10px',
                        axisTick: {
                            show: false
                        },
                    },
                    {
                        data: seriesBar2,
                        type: 'bar',
                        name: headData[1].name,
                        colorStyle: {colorA: '#6392FA', colorB: '#2F69F8'},
                        z: 10,
                        yAxisIndex: 0, //这里要设置哪个y轴，默认是最左边的是0，然后1，2顺序来。
                        // shadowColor: {colorA: '#C9E9FF', colorB: '#98CEFF'},
                        barWidth: '10px',
                    },
                    {
                        type: 'line',
                        data: seriesLine3,
                        yAxisIndex: 1, //这里要设置哪个y轴，默认是最左边的是0，然后1，2顺序来。
                        colorStyle: {colorA: '#FFB701', colorB: '#FF5A5A'},
                        name: headData[2].name,
                        shadowColor: {colorA: '#FFB701', colorB: '#FF5A5A'},
                    }
                ]
                let legendFlag = true
                this.goalQquotaOption = echartInitConfig => targetComposeLineAndHistogramProgress(echartInitConfig, xAxisData, yAxisData, seriesData, headData, legendFlag);
            },
        }
    }
</script>

<style lang="scss">
    .quota-goal-reach {
        background: white;
        padding: 0 0px 60px;

        .link-auto-list-no-data {
            margin-top: 10vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            & > image {
                width: 50vw;
                height: 50vw;
                border-radius: 750px;
                margin-bottom: 24px;
            }

            & > text {
                font-size: 28px;
            }
        }

        .echart-no-more {
            display: flex;
            -webkit-align-items: center;
            -ms-flex-align: center;
            align-items: center;
            -webkit-justify-content: center;
            -ms-flex-pack: center;
            justify-content: center;
            -webkit-flex-direction: column;
            -ms-flex-direction: column;
            flex-direction: column;
            padding: 40 rpx 0;
            font-size: 20 rpx;
            color: #999;
        }

        .goal-quota-table {
            margin: 30px 48px 30px;

            .table-row-title {
                display: flex;
                width: 100%;
                background: #6D96FA;

                .column-title {
                    font-size: 24px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 72px;
                    line-height: 72px;
                    width: 25%;
                    text-align: center;
                    color: #fff;
                }

                .column-title:nth-child(1) {
                    min-width: 200px;
                }
            }

            .table-row {
                display: flex;
                width: 100%;

                .column {
                    line-height: 32px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 80px;
                    border-right: 2px solid #DEE7FE;
                    width: 22%;
                    padding: 16px 12px;
                    text-align: left;
                    border-bottom: 2px solid #DEE7FE;
                    color: #262626;

                    .text {
                        display: inline-block;
                        font-size: 24px;
                        overflow: hidden;
                        text-overflow: -o-ellipsis-lastline;
                        text-overflow: ellipsis;
                    }
                }

                .column:nth-child(1) {
                    width: 34%;
                    background-color: #F4F7FE;

                    .text {
                        display: inline-block;
                        font-size: 24px;
                        overflow: hidden;
                        text-overflow: -o-ellipsis-lastline;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        line-clamp: 2;
                        -webkit-box-orient: vertical;
                    }
                }

                .column:nth-child(5n+1) {
                    border-left: 2px solid #DEE7FE;
                }
            }
        }

        .line-title {
            margin-left: 32px;
        }

        .area-name {
            width: 100%;
            font-size: 24px;
            color: #2F69F8;
            letter-spacing: 0;
            line-height: 72px;
            background: #F0F4FE;
            height: 72px;
            text-align: center;
        }

        .goal-quota-content {
            margin-top: 8px;
        }

        .dialog-bottom {
            .dialog-content {
                padding: 0 20px;
            }

            .model-title {
                display: flex;

                .title {
                    font-family: PingFangSC-Regular, serif;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    text-align: center;
                    line-height: 96px;
                    height: 96px;
                    width: 90%;
                    margin-right: 80px;
                    padding-left: 0;
                }

                .icon-close {
                    color: #BFBFBF;
                    font-size: 48px;
                    line-height: 96px;
                    height: 96px;
                }
            }
        }

        .goal-count {
            background: #FFFFFF;
            border: 2px solid #EBEDF5;
            border-radius: 16px;
            margin: 0 24px 16px;
        }

        .scroll-view-data {
            margin-top: 24px;
            margin-bottom: 24px;

            .select-dimension {
                display: flex;
                margin-left: 24px;
            }
        }
    }
</style>
