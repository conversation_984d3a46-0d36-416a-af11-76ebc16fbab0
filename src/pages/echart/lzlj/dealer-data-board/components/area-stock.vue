/**
* @createdBy  张丽娟
* @date  2020/12/21
* @description 区域库存
*/
<template>
  <link-page class="area-stock">
    <view class="area-name">{{userInfo.orgName}}</view>
    <view class="goal-quota-content">
      <line-title :title="quotaStockSalesParam.type === 'stockSales' ? '区域库存统计': '销量统计'"></line-title>
      <scroll-view scroll-x="true" class="scroll-view-data">
        <view class="select-dimension">
          <select-button :label="quotaStockSalesParam.orgName?quotaStockSalesParam.orgName: '全部片区'"
                         :selected-flag="quotaStockSalesParam.orgName !== null "
                         @tap="tapFilterOrganization('stockSales')" downIcon></select-button>
          <select-button label="库存" :selected-flag="quotaStockSalesParam.type === 'stockSales'"
                         @tap="qoalStockSalesQuery(null,'stockSales')"></select-button>
          <select-button label="销量" :selected-flag="quotaStockSalesParam.type === 'sales'"
                         @tap="qoalStockSalesQuery(null,'sales')"></select-button>
          <select-button label="本月" ref="monthButton" v-if="quotaStockSalesParam.type === 'sales'"
                         :selected-flag="quotaStockSalesParam.dateType === 'month'"
                         @tap="qoalStockSalesQuery($event,'month')" value="month"></select-button>
          <select-button label="本季" v-if="quotaStockSalesParam.type === 'sales'"
                         :selected-flag="quotaStockSalesParam.dateType === 'quarter'"
                         @tap="qoalStockSalesQuery($event,'quarter' )" value="quarter"></select-button>
          <select-button label="本年" v-if="quotaStockSalesParam.type === 'sales'"
                         :selected-flag="quotaStockSalesParam.dateType === 'year'"
                         @tap="qoalStockSalesQuery($event,'year')" value="year"></select-button>
        </view>
      </scroll-view>
      <view class="goal-count">
        <view v-show="showEchart">
          <link-echart :option="stockSalesEchartOption" :height="stockSalesEchartOptionHeight + 'px'"/>
        </view>
        <view class="data-board">
          <view class="title">
            <view class="title-item column-1">
              区域
            </view>
            <view class="title-item column-2">
              库存(件)
            </view>
            <view class="title-item column-3">
              占比
            </view>
          </view>
          <view class="content">
            <view class="list" v-for="(item,index) in stockTable" :key="index">
              <view class="list-item column-1" @tap="showMessageDialog(item.orgName)">
                <view class="text">{{item.orgName}}</view>
              </view>
              <view class="list-item column-2" @tap="showMessageDialog(item.totalQty)">
                <view class="text">{{item.totalQty}}</view>
              </view>
              <view class="list-item column-3">
                <view class="text"> {{item.radio + '%'}}</view>
              </view>
            </view>
          </view>
          <view class="echart-no-more" v-if="stockTable.length === 0">
            暂无数据
          </view>
        </view>
      </view>
    </view>
    <view class="target-implementation-progress">
      <line-title :title="quotaStockSalesParam.type === 'stockSales' ? '区域库存统计': '销量统计'"></line-title>
      <scroll-view scroll-x="true" class="scroll-view-data">
        <view class="select-dimension">
          <select-button :label="quotaStockParam.orgName?quotaStockParam.orgName: '全部片区'"
                         :selected-flag="quotaStockParam.orgName !== null"
                         @tap="tapFilterOrganization('stock')" downIcon></select-button>
        </view>
      </scroll-view>
      <view class="target-progress" v-if="showEchart">
        <link-echart :option="targetImplementationOption" :height="barYCategoryHeight+'px'"/>
        <view class="echart-no-more" v-if="stockBarRows.length === 0">
          暂无数据
        </view>
      </view>
    </view>
    <link-dialog ref="positionBottom" position="bottom" height="65vh" class="dialog-bottom" noPadding
                 v-model="dialogFlag" @hide="showEchart = true">
      <view class="model-title">
        <view @tap="backToPrimaryOrganization()" style="width: 40px;color:#2F69F8;line-height: 44px;">返回</view>
        <view class="title" style="padding-left:0;">组织片区</view>
        <view class="iconfont icon-close" @tap="dialogFlag = false;showEchart = true"
              style="margin-right: 15px;"></view>
      </view>
      <view class="dialog-content">
        <scroll-view scroll-y="true" :style="{'height': 'calc(60vh - 44px)'}"
                     v-if="excludeDealerOrgnizationData.length>0">
          <list>
            <item v-for="(item,index) in excludeDealerOrgnizationData" @tap="clickOrginzation(item)"
                  arrow="false" :key="index">
              <view class="list-item" slot="title">
                {{item.text}}
              </view>
            </item>
          </list>
        </scroll-view>
        <view class="link-auto-list-no-data" v-else>
          <image :src="$imageAssets.noDataImage" mode="aspectFit"/>
          <text>暂无数据</text>
        </view>
      </view>
    </link-dialog>
  </link-page>
</template>

<script>
  import LineTitle from "../../../../lzlj/components/line-title";
  import Taro from "@tarojs/taro";
  import SelectButton from "../../components/select-button";
  import {targetPieChartProgress, barYCategory} from "../../echart.utils";

  export default {
    name: "area-stock",
    components: {LineTitle, SelectButton},
    data() {
      const accessGroupOauth = this.$utils.getMenuAccessGroup('', '/pages/echart/lzlj/dealer-data-board/data-board-page');
      return {
        accessGroupOauth,//访问组安全性
        stockSalesEchartOption: null, //PieChart
        stockSalesEchartOptionHeight: (this.$device.systemInfo.windowWidth - 24) * 0.687679 < 240 ? (this.$device.systemInfo.windowWidth - 24) * 0.687679 : 240,
        targetImplementationOption: null, //barYCategory
        loadingFlag: false,
        barYCategoryHeight: null,
        quotaStockSalesParam: {
          orgId: this.userInfo.orgId,
          orgName: this.userInfo.orgName,
          type: 'stockSales',
          dateType: 'month',
          startDate: '',
          endDate: ''
        },
        quotaStockParam: {
          orgId: this.userInfo.orgId,
          orgName: this.userInfo.orgName,
        },
        currentSeries: 1,
        dialogFlag: false,
        currentMonthDate: null,
        stockTable: [],
        stockBarRows: [], // 库存柱状图数据
        showEchart: true,
      }
    },
    props: {
      orgnizationData: {
        type: Array,
        default: function () {
          return []
        }
      },
      userInfo: {
        type: Object,
        default: function () {
          return {}
        }
      },
      excludeDealerOrgnizationData: {
        type: Array,
        default: function () {
          return []
        }
      },
    },
    mounted() {
      this.currentMonthDate = this.$utils.getCurrentMonthDate();
      this.quotaStockSalesParam.startDate = this.currentMonthDate.startDate
      this.quotaStockSalesParam.endDate = this.currentMonthDate.endDate
      if (this.$utils.isEmpty(this.accessGroupOauth)) {
        this.stockSalesEchart()
        this.targetImplementationBar()
      }
    },
    methods: {
      showMessageDialog(val) {
        this.$message.primary(val)
      },
      /**
       * @createdBy  张丽娟
       * @date  2020/10/28
       * @methods tapFilterOrganization
       * @para
       * @description 选择片区
       */
      tapFilterOrganization(param) {
        this.currentSeries = param
        this.dialogFlag = true
        this.showEchart = false
      },
      /**
       * @createdBy  张丽娟
       * @date  2020/10/28
       * @methods qoalStockSalesQuery
       * @para
       * @description 业务组库存/销量统计 查询
       */
      qoalStockSalesQuery(val, param) {
        switch (param) {
          case 'stockSales': {
            this.quotaStockSalesParam.type = param
            this.stockSalesEchart()
            break
          }
          case 'sales': {
            this.quotaStockSalesParam.type = param
            this.stockSalesEchart()
            break
          }
          case 'month': {
            this.quotaStockSalesParam.dateType = param
            this.quotaStockSalesParam.startDate = val.startDate
            this.quotaStockSalesParam.endDate = val.endDate
            this.stockSalesEchart()
            break
          }
          case 'quarter': {
            this.quotaStockSalesParam.dateType = param
            this.quotaStockSalesParam.startDate = val.startDate
            this.quotaStockSalesParam.endDate = val.endDate
            this.stockSalesEchart()
            break
          }
          case 'year': {
            this.quotaStockSalesParam.dateType = param
            this.quotaStockSalesParam.startDate = val.startDate
            this.quotaStockSalesParam.endDate = val.endDate
            this.stockSalesEchart()
            break
          }
        }
      },
      /**
       * @createdBy  张丽娟
       * @date  2020/10/28
       * @methods clickOrginzation
       * @para
       * @description 选择片区
       */
      async clickOrginzation(item) {
        console.log(this.currentSeries, 'this.currentSeries')
        switch (this.currentSeries) {
          case 'stockSales': {
            this.quotaStockSalesParam.orgId = item.id
            this.quotaStockSalesParam.orgName = item.text
            await this.stockSalesEchart()
            break
          }
          case 'stock': {
            this.quotaStockParam.orgId = item.id
            this.quotaStockParam.orgName = item.text
            await this.targetImplementationBar()
            break
          }
        }
        this.dialogFlag = false
        this.showEchart = true
      },
      /**
       * @createdBy  张丽娟
       * @date  2020/11/23
       * @methods fetchStockData
       * @para
       * @description 获取库存-销量
       */
      async fetchStockData() {
        this.$utils.showLoading();
        let param = {}
        let url = ''
        if (this.quotaStockSalesParam.type === 'stockSales') {
          // 库存
          url = 'action/link/invPro/areaInvProdReport'
          param = {
            attr1: this.quotaStockSalesParam.orgId,
            pageFlag: false,
          }
        } else {
          // 销量
          url = 'action/link/saleorderitem/areaOrderItemReportPage'
          param = {
            attr1: this.quotaStockSalesParam.orgId,
            pageFlag: false,
            startTime: this.quotaStockSalesParam.startDate,
            endTime: this.quotaStockSalesParam.endDate,
          }
        }
        let data = await this.$http.post(url, param);
        if (!data.success) {
          this.$utils.hideLoading();
          this.$showError('获取报表数据异常，请重试。');
          return
        }
        this.$utils.hideLoading();
        let targetData = data
        return targetData
      },
      /**
       * @createdBy  张丽娟
       * @date  2020/11/24
       * @methods fetchStockBarData
       * @para
       * @description 库存柱状图数据
       */
      async fetchStockBarData() {
        this.$utils.showLoading()
        let param = {
          attr1: this.quotaStockParam.orgId,
          pageFlag: false,
        }
        let url = 'action/link/invPro/areaInvProdReport'
        let data = await this.$http.post(url, param);
        if (!data.success) {
          this.$utils.hideLoading()
          this.$showError('获取报表数据异常，请重试。');
          return
        }
        this.$utils.hideLoading()
        let targetData = data
        return targetData
      },
      /**
       * @createdBy  张丽娟
       * @date  2020/10/27
       * @methods stockSalesEchart
       * @para
       * @description 业务组库存/销量统计 - 饼图
       */
      async stockSalesEchart() {
        let stockData = await this.fetchStockData()
        this.stockTable = []
        this.stockTable = stockData.rows
        let seriesData = []
        let totalSeriesData = []
        if (this.quotaStockSalesParam.type === 'stockSales') {
          // 库存
          stockData.rows.forEach((item, index) => {
            item.radio = stockData.sum ? (item.totalQty * 100 / stockData.sum).toFixed(2) : 0
            seriesData.push({
              value: item.totalQty,
              name: item.orgName
            })
          })
          totalSeriesData = [{value: stockData.sum, name: '库存总计'}]
        } else {
          // 销量
          stockData.rows.forEach((item, index) => {
            item.radio = stockData.sum ? (item.totalQty / stockData.sum * 100).toFixed(2) : 0
            seriesData.push({
              value: item.totalQty,
              name: item.orgName
            })
          })
          totalSeriesData = [{value: stockData.sum ? stockData.sum : 0, name: '销量总计'}]
        }
        this.stockSalesEchartOption = {}
        this.stockSalesEchartOption = echartInitConfig => targetPieChartProgress(echartInitConfig, seriesData, totalSeriesData, ['40%', '60%'], '40%', [], 225);
      },
      /**
       * @createdBy  张丽娟
       * @date  2020/10/27
       * @methods targetImplementationBar
       * @para
       * @description 区域库存统计 - y轴柱形图
       */
      async targetImplementationBar() {
        let stockData = await this.fetchStockBarData()
        this.stockBarRows = stockData.rows
        let seriesData = []
        // 库存
        stockData.rows.forEach((item, index) => {
          //item.radio = (item.totalQty / stockData.sum).toFixed(2)
          seriesData.push({
            value: item.totalQty,
            name: item.orgName
          })
        })
        this.barYCategoryHeight = 32 + 26 * seriesData.length
        this.targetImplementationOption = {}
        this.targetImplementationOption = echartInitConfig => barYCategory(echartInitConfig, seriesData);
      },
      /**
       * @createdBy  张丽娟
       * @date  2020/11/24
       * @methods backToPrimaryOrganization
       * @para
       * @description 返回当前登录人所属的组织
       */
      backToPrimaryOrganization() {
        switch (this.currentSeries) {
          case 'stockSales': {
            this.quotaStockSalesParam.orgId = this.userInfo.orgId
            this.quotaStockSalesParam.orgName = this.userInfo.orgName
            this.stockSalesEchart()
            break
          }
          case 'stock': {
            this.quotaStockParam.orgId = this.userInfo.orgId
            this.quotaStockParam.orgName = this.userInfo.orgName
            this.targetImplementationBar()
            break
          }
        }
        this.dialogFlag = false
        this.showEchart = true
      },
    }
  }
</script>

<style lang="scss">
  .area-stock {
    background: white;
    padding-bottom: 68px;

    .link-auto-list-no-data {
      margin-top: 10vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      & > image {
        width: 50vw;
        height: 50vw;
        border-radius: 750px;
        margin-bottom: 24px;
      }

      & > text {
        font-size: 28px;
      }
    }

    .line-title {
      margin-left: 32px;
    }

    .area-name {
      width: 100%;
      font-size: 24px;
      color: #2F69F8;
      letter-spacing: 0;
      line-height: 72px;
      background: #F0F4FE;
      height: 72px;
      text-align: center;
    }

    .scroll-view-data {
      margin-top: 24px;
      margin-bottom: 24px;

      .select-dimension {
        display: flex;
        margin-left: 24px;
      }
    }

    .echart-no-more {
      display: flex;
      -webkit-align-items: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      padding: 40 rpx 0;
      font-size: 20 rpx;
      color: #999;
    }

    .target-implementation-progress {
      margin-top: 8px;

      .target-progress {
        margin: 0 24px;
        border: 2px solid #EBEDF5;
        border-radius: 16px;
      }
    }

    .goal-quota-content {
      margin-top: 8px;

      .goal-count {
        border: 2px solid #EBEDF5;
        border-radius: 16px;
        margin: 0 24px;

        .data-board {
          margin: 0px 48px 32px;
          box-sizing: border-box;
          font-size: 24px;
          line-height: 24px;
          letter-spacing: 0;
          background-color: white;

          .title {
            font-family: PingFangSC-Regular, serif;
            font-size: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #6D96FA;
            color: white;
            padding: 24px 0;
            text-align: center;

            .title-item {
            }
          }

          .content {
            font-family: PingFangSC-Regular, serif;
            font-size: 24px;
            color: #262626;
            letter-spacing: 0;
            text-align: center;

            .list {
              display: flex;
              justify-content: center;
              align-items: center;
              //height: 80px;
              line-height: 32px;
            }

            .list-item {
              border-right: 1px solid #DEE7FE;
              /*text-align: center;*/
              text-align: left;
              border-bottom: 1px solid #DEE7FE;
              color: #262626;
              height: 80px;
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 0 12px;
            }

            .text {
              display: inline-block;
              font-size: 24px;
              overflow: hidden;
              text-overflow: -o-ellipsis-lastline;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              -webkit-box-orient: vertical;
            }

            .list-item:first-child {
              border-left: 1px solid #DEE7FE;
            }
          }

          .list-item:first-child {
            background-color: #F4F7FE;
          }

          .column-1 {
            width: 33.3%;
            min-width: 200px;
          }

          .column-2 {
            width: 33.3%;
          }

          .column-3 {
            width: 33.3%;
          }
        }
      }

    }

    .dialog-bottom {
      .dialog-content {
        padding: 0 20px;
      }

      .model-title {
        display: flex;

        .back {

        }

        .title {
          font-family: PingFangSC-Regular, serif;
          font-size: 32px;
          color: #262626;
          letter-spacing: 0;
          text-align: center;
          line-height: 96px;
          height: 96px;
          width: 90%;
        }

        .icon-close {
          color: #BFBFBF;
          font-size: 48px;
          line-height: 96px;
          height: 96px;
        }
      }
    }

  }
</style>
