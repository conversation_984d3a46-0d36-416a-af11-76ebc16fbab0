<template>
    <link-page class="purchase-invoice">
        <view class="purchase-invoice-top">
            <view class="request-date">时间：{{requestDate}}</view>
            <view class="purchase-invoice-org">
                <view class="pick-org" @tap="selectedFlag&&tapFilterOrganization()">
                    <view class="org-name">{{orgObj.brandName}}</view>
                    <link-icon icon="icon-down1" class="link-icon" v-if="selectedFlag"></link-icon>
                </view>
                <view class="refresh" @tap="refresh">
                    <link-icon icon="icon-zhongzhi" class="link-icon"></link-icon>
                    <text>刷新</text>
                </view>
            </view>
        </view>
        <view v-if="purchaseInvoiceData.length>0">
            <view v-for="(data, index) in purchaseInvoiceData" :key="index" class="content">
                <view class="group-name">{{data.groupName}}</view>
                <view class="group">
                    <view class="group-view">
                        <image class="img" :src="$imageAssets.dangqiankucun"></image>
                        <view class="title">当前库存</view>
                        <view class="num">{{data['currentStock']['finalStock']}}</view>
                    </view>
                    <view class="group-view">
                        <image class="img" :src="$imageAssets.jinridongxiao"></image>
                        <view class="title">今日动销</view>
                        <view class="num">{{data['currentMove']['finalStock']}}</view>
                    </view>
                    <view class="group-view">
                        <image class="img" :src="$imageAssets.benyuedongxiao"></image>
                        <view class="title">本月累计动销</view>
                        <view class="num">{{data['monthMove']['finalStock']}}</view>
                    </view>
                    <view class="group-view">
                        <image class="img" :src="$imageAssets.benniandongxiao"></image>
                        <view class="title">本年累计动销</view>
                        <view class="num">{{data['yearMove']['finalStock']}}</view>
                    </view>
                </view>
            </view>
        </view>
        <view class="link-auto-list-no-data" v-else>
            <image :src="$imageAssets.noDataImage" mode="aspectFit"/>
            <text>暂无数据</text>
        </view>
        <link-dialog ref="positionBottom" position="bottom" height="65vh" class="dialog-bottom" noPadding
                     v-model="dialogFlag" @hide="showEchart = true">
            <view class="model-title">
                <view @tap="backToPrimaryOrganization()" style="width: 40px;color:#2F69F8;line-height: 44px;">返回</view>
                <view class="title" style="padding-left:0;">品牌公司</view>
                <view class="iconfont icon-close" @tap="dialogFlag = false;showEchart = true"
                      style="margin-right: 15px;"></view>
            </view>
            <view class="dialog-content">
                <scroll-view scroll-y="true" :style="{'height': 'calc(60vh - 44px)'}"
                             v-if="orgnizationData.length > 0">
                    <list>
                        <item v-for="(item,index) in orgnizationData" @tap="clickOrginzation(item)"
                              arrow="false" :key="index">
                            <view class="list-item" slot="title">
                                {{item.text}}
                            </view>
                        </item>
                    </list>
                </scroll-view>
                <view class="link-auto-list-no-data" v-else>
                    <image :src="$imageAssets.noDataImage" mode="aspectFit"/>
                    <text>暂无数据</text>
                </view>
            </view>
        </link-dialog>
    </link-page>
</template>
<script>
    import Taro from "@tarojs/taro";
    import SelectButton from "../../components/select-button";
    export default {
        name:'purchase-invoice',
        components: {SelectButton},
        data(){
            const requestDate =  this.$date.format(new Date(Date.now()), 'YYYY-MM-DD HH:mm:ss');
            const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
            const orgObj = {brandCode: '', brandName: '' ,};
            const selectedFlag = this.$utils.isEmpty(userInfo.coreOrganizationTile['l3Id']);
            return {
                orgObj,
                userInfo,
                requestDate,
                selectedFlag,
                dialogFlag: false,
                orgnizationData:[],
                purchaseInvoiceData:[],
            }
        },
        async created () {
            if(this.userInfo.orgType === 'BranchCompany'){
                this.orgObj = {
                    brandCode: this.userInfo.coreOrganizationTile['l3Code'],
                    brandName: this.userInfo.coreOrganizationTile['l3Name']
                };
                await this.queryPurchaseInvoiceData();
            } else if (this.userInfo.orgType === 'Company'){
                await this.fetchOrgnizationData();
            }
        },
        methods: {
            /**
             * @createdBy  宋燕荣
             * @date  2022/03/16
             * @methods tapFilterOrganization
             * @para
             * @description 专营公司选择
             */
            async tapFilterOrganization() {
                this.dialogFlag = true;
            },
            async fetchOrgnizationData(){
                this.$utils.showLoading()
                let orgId = this.userInfo.coreOrganizationTile['l2Id'];
                let param = {
                    filtersRaw: [
                        {id: 'parentOrgId', property: 'parentOrgId', value: orgId, operator: '='},
                        {id: "isEffective_0", property: "isEffective", value: "Y"},
                        {id: "isLaunch", property: "isLaunch", value: "Y"},
                        {id: "orgType", property: "orgType", value: "BranchCompany"},
                        {id: "orgCode", property: "orgCode", operator: 'IN', value: "[GS100283, GS100276, GS100277, GS100285, BM100663, GS100278 ,GS100300]"}
                    ],
                    pageFlag: true
                };
                const data = await this.$http.post('export/link/orgnization/queryByExamplePage', param);
                if (!data.success) {
                    this.$utils.hideLoading()
                    this.$utils.showAlert('获取组织数据出错！', {icon: 'none'});
                    return
                }
                this.$utils.hideLoading()
                this.orgnizationData = data.rows;
                if(this.userInfo.orgType === 'Company'){
                    const orgExit = this.orgnizationData.filter((item1) => item1.sapCompCode === '5600');
                    this.orgObj = {
                        brandCode: orgExit[0]['orgCode'],
                        brandName: orgExit[0]['text']
                    };
                    await this.queryPurchaseInvoiceData();
                }
            },
            /**
             * @createdBy  宋燕荣
             * @date  2022/03/16
             * @methods backToPrimaryOrganization
             * @para
             * @description 回到主要组织
             */
            async backToPrimaryOrganization() {
                // this.orgObj = {
                //     brandCode: this.userInfo.coreOrganizationTile['l3Code'] || this.userInfo.coreOrganizationTile['l2Code'],
                //     brandName: this.userInfo.coreOrganizationTile['l3Name'] || this.userInfo.coreOrganizationTile['l2Name'] ,
                // };
                this.dialogFlag = false
            },
            /**
             * @createdBy  宋燕荣
             * @date  2022/03/16
             * @methods clickOrginzation
             * @para
             * @description 选择片区
             */
            async clickOrginzation(item) {
                this.orgObj.brandCode = item.orgCode;
                this.orgObj.brandName = item.text;
                this.dialogFlag = false
                await this.queryPurchaseInvoiceData();
            },
            /**
             * @createdBy  宋燕荣
             * @date  2022/03/16
             * @methods refresh
             * @para
             * @description 刷新
             */
            async refresh(){
                await this.queryPurchaseInvoiceData();
                this.requestDate =  this.$date.format(new Date(Date.now()), 'YYYY-MM-DD HH:mm:ss');
            },
            async queryPurchaseInvoiceData(){
                if(this.$utils.isEmpty(this.orgObj.brandCode)){
                    return
                }
                this.$utils.showLoading()
                const data = await this.$http.post('export/link/dealerPortal/queryDealerStock', {brandCode:this.orgObj.brandCode});
                if (!data.success) {
                    this.$utils.hideLoading()
                    this.$utils.showAlert('获取数据出错！', {icon: 'none'});
                    return
                }
                this.$utils.hideLoading()
                this.purchaseInvoiceData = data.rows;
            },
        }
    }
</script>
<style lang="scss">
    .purchase-invoice{
        .link-auto-list-no-data {
            margin-top: 10vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            & > image {
                width: 50vw;
                height: 50vw;
                border-radius: 750px;
                margin-bottom: 24px;
            }

            & > text {
                font-size: 28px;
            }
        }
        .purchase-invoice-top{
            background-color: white;
            .request-date{
                font-family: PingFangSC-Regular;
                font-size: 24px;
                color: #999999;
                text-align: left;
                line-height: 24px;
                font-weight: 400;
                padding: 24px 24px 0 24px;
            }
            .purchase-invoice-org{
                width: 100%;
                height: 88px;
                .pick-org{
                    width: 80%;
                    height: 92px;
                    float: left;
                    .org-name{
                        //width: 76%;
                        font-family: PingFangSC-Medium;
                        font-size: 32px;
                        color: #333333;
                        text-align: left;
                        font-weight: 500;
                        padding: 24px 0 24px 24px;
                        float: left;
                        //min-width: 110px;
                        white-space: nowrap;
                    }
                    .link-icon{
                        font-size: 36px;
                        width: 20px;
                        color: #bfbfbf;
                        margin-right: 8px;
                        margin-left: 20px;
                        float: left;
                        height: 92px;
                        line-height: 92px;
                    }
                }
                .refresh{
                    height: 92px;
                    line-height: 92px;
                    color: #bfbfbf;
                    font-size: 24px;
                }
            }
        }
        .content {
            width: 94%;
            background: #fff;
            margin: 24px auto auto auto;
            border-radius: 16px;
            height: 360px;
            padding-top: 10px;
            .group-code{
                background: #A6B4C7;
                border-radius: 8px;
                width: 25%;
                text-align: center;
                margin: 32px 0 16px 24px;
                .code{
                    font-size: 24px;
                    color: #FFFFFF !important;
                    letter-spacing: 0;
                    line-height: 40px;
                    padding: 2px 8px;
                }
            }
            .group-name{
                font-family: PingFangSC-Semibold;
                font-size: 28px;
                color: #262626;
                letter-spacing: 0;
                line-height: 28px;
                margin: 24px
            }
            .group{
                width: 100%;
                margin-top: 48px;
                .group-view{
                    width: 25%;
                    text-align: center;
                    float: left;
                    .title{
                        font-family: PingFangSC-Regular;
                        font-size: 24px;
                        color: #666666;
                        letter-spacing: 0;
                        text-align: center;
                        font-weight: 400;
                    }
                    .num{
                        font-family: PingFangSC-Medium;
                        font-size: 36px;
                        color: #222222;
                        letter-spacing: 0;
                        text-align: center;
                        font-weight: 500;
                        margin-top: 24px;
                    }
                    .img{
                        width: 40px;
                        height: 40px;
                    }
                }
            }
        }
        .dialog-bottom {
            .dialog-content {
                padding: 0 20px;
            }

            .model-title {
                display: flex;

                .title {
                    font-family: PingFangSC-Regular, serif;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    text-align: center;
                    line-height: 96px;
                    height: 96px;
                    width: 90%;
                    margin-right: 80px;
                    padding-left: 0;
                }

                .icon-close {
                    color: #BFBFBF;
                    font-size: 48px;
                    line-height: 96px;
                    height: 96px;
                }
            }
        }
    }
</style>
