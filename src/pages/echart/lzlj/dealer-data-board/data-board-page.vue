<template>
    <link-page class="dealer-data-board-page">
        <lnk-taps :taps="dataBoardOption" v-model="dataBoardActive"></lnk-taps>
        <!--经销商进销存-->
        <view v-if="dataBoardOption.length > 0">
            <purchase-invoice></purchase-invoice>
        </view>
        <!--目标配额达成-->
<!--        <view v-if="dataBoardActive.val === 'goalQuota'">
            <quota-goal-reach ref="quota" :orgnizationData="orgnizationData" :userInfo="userInfo"
                              :excludeDealerOrgnizationData="excludeDealerOrgnizationData"></quota-goal-reach>
        </view>
        &lt;!&ndash;区域库存&ndash;&gt;
        <view v-if="dataBoardActive.val === 'areaStock'">
            <area-stock ref="stock" :orgnizationData="orgnizationData" :userInfo="userInfo"
                        :excludeDealerOrgnizationData="excludeDealerOrgnizationData"></area-stock>
        </view>
        &lt;!&ndash;详情报表-经销商库存&ndash;&gt;
        <view v-if="dataBoardActive.val === 'saleStock'">
            <data-board-content :userInfo="userInfo" ref="detail"></data-board-content>
        </view>
        &lt;!&ndash;详情报表-经销商销量&ndash;&gt;
        <view v-if="dataBoardActive.val === 'stock'">
            <data-board-content-stock :userInfo="userInfo" ref="detail"></data-board-content-stock>
        </view>-->
    </link-page>
</template>

<script>
    import LnkTaps from "../../../core/lnk-taps/lnk-taps";
    import PurchaseInvoice from "./components/purchase-invoice";
    import QuotaGoalReach from "./components/quota-goal-reach";
    import AreaStock from "./components/area-stock";
    import DataBoardContent from "./components/data-board-content";
    import DataBoardContentStock from "./components/data-board-content-stock";
    import Taro from "@tarojs/taro";

    export default {
        name: "data-board-page",
        components: {PurchaseInvoice, DataBoardContent, AreaStock, QuotaGoalReach, LnkTaps, DataBoardContentStock},
        data() {
            const accessGroupOauth = this.$utils.getMenuAccessGroup('', '/pages/echart/lzlj/dealer-data-board/data-board-page');
            const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
            let dataBoardOption = [
                /*{name: '目标配额达成', seq: '2', val: 'goalQuota'},
                {name: '区域库存', seq: '3', val: 'areaStock'},
                {name: '经销商库存', seq: '4', val: 'saleStock'},
                {name: '经销商销量', seq: '5', val: 'stock'}*/
            ];
            var orgCodeArr = ['GS100283', 'GS100276', 'GS100277', 'GS100285', 'BM100663', 'GS100278', 'GS100300'];
            if (userInfo.orgType === 'Company' || (userInfo.orgType === 'BranchCompany' && orgCodeArr.indexOf(userInfo.orgCode) !== -1)) {
              dataBoardOption = [
                {name: '进销存', seq: '1', val: 'purchase'},
                /*{name: '目标配额达成', seq: '2', val: 'goalQuota'},
                {name: '区域库存', seq: '3', val: 'areaStock'},
                {name: '经销商库存', seq: '4', val: 'saleStock'},
                {name: '经销商销量', seq: '5', val: 'stock'}*/
              ]
               // this.$taro.setNavigationBarTitle({title: '进销存'});
            }
            return {
                accessGroupOauth,//访问组安全性
                dataBoardActive: {},
                dataBoardOption,
                orgnizationData: [],
                userInfo,
                dealerOrgnizationData: [],
                excludeDealerOrgnizationData: [],
            }
        },
        created() {
            //this.dataBoardActive = [0]
            this.fetchOrgnizationData()
        },
        methods: {
            /**
             *  @description: 切换tabs
             *  @author: 马晓娟
             *  @date: 2020/9/3 18:06
             */
            changeTab(val, key) {
                this.dataBoardActive = val;
                console.log(val, key)
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/10/27
             * @methods fetchOrgnizationData
             * @para
             * @description 获取组织数据
             */
            async fetchOrgnizationData() {
                this.userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
                let orgId = this.userInfo.orgId;
                let param = {};
                //匹配访问组安全性，如果有配置访问组安全性查询可选组织时安全性走访问组安全性，否则按原有逻辑。
                if (this.$utils.isEmpty(this.accessGroupOauth)) {
                    param = {
                        filtersRaw: [
                            {id: 'parentOrgId', property: 'parentOrgId', value: orgId, operator: '='},
                            {id: "isEffective_0", property: "isEffective", value: "Y"},
                            {id: "isLaunch", property: "isLaunch", value: "Y"},
                        ],
                        pageFlag: true
                    }
                } else {
                    param = {
                        oauth: this.accessGroupOauth,
                        filtersRaw: [
                            {id: "isEffective_0", property: "isEffective", value: "Y"},
                            {id: "isLaunch", property: "isLaunch", value: "Y"},
                        ],
                        pageFlag: true
                    }
                }
                if (this.userInfo.orgType === 'Company') {
                    let orgType = {id: "orgType", property: "orgType", value: "BranchCompany"}
                    param.filtersRaw.push(orgType)
                }
                const data = await this.$http.post('action/link/orgnization/queryByExamplePage', param);
                if (!data.success) {
                    this.$utils.showAlert('获取组织数据出错！', {icon: 'none'});
                    return
                }
                this.orgnizationData = data.rows;
                this.dealerOrgnizationData = data.rows.filter(function (item) {
                    return item.orgType === 'Dealer';
                });
                this.excludeDealerOrgnizationData = data.rows.filter(function (item) {
                    return item.orgType !== 'Dealer';
                });
            },
        }
    }
</script>

<style lang="scss">
    .dealer-data-board-page {
        /*margin-top: 94px;*/
        .lnk-tabs {
            border-bottom: 1px solid #f2f2f2;
            position: relative;
            /*z-index: 1503 !important;*/
            border-bottom: 2px solid #f2f2f2 !important;
        }
    }
</style>
