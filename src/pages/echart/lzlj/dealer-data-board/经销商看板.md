# 经销商看板


------
* 初始文档
```
创建时间：2022年2月16日
创建人：  吕志平
```
* 模块介绍
> 展示4个区间的数据报表,目标配额达成,区域库存,经销商库存,经销商销量
>

* 涉及对象
> * 经销商-本系统


* 是否共用
> 否


* 数据存储
> * 数据来源 本系统数据库
> * 存储方式 本系统数据库
> * 是否同步 否

* 缓存机制
> * 是否缓存 否

* 安全性
> *  (1) 根据传递的组织id展示数据,默认取当前登录职位的所属组织id

* 状态流转
> * 无


* 涉及组件
> * scroll-view
> * AutoList
> * link-page
> * link-taps
> * link-echart


## 模块实现
###涉及页面
#### 一 活动看板
#####  1、vue页面路径
> * 1、页面完整路径
>
>  src/pages/echart/lzlj/dealer-data-board/data-board-page.vue

##### 2、页面涉及组件
>> 2.1 目标配额达成
>>#####  2.1.1、vue页面路径
>> src/pages/echart/lzlj/dealer-data-board/components/quota-goal-reach.vue
>>#####  2.1.2、实现功能
>> * (1).可以选择当前职位所属组织下的片区组织进行维度查询
>> * (2).可以选择时间维度来展示目标配额达成数据(本月,本季度,本年)
>> * (3).也可以选择目标达成,配额达成,来展示对应的达成数据.
>
>> 2.2 区域库存
>>#####  2.2.1、vue页面路径
>> src/pages/echart/lzlj/dealer-data-board/components/area-stock.vue
>>#####  2.2.2、实现功能
>> * (1).首先可以选择库存和销量两个大类进行区别展示
>> * (2).当选择库存时没有时间维度,选择销量时有时间维度进行选择(本月,本季度,本年)
>> * (3).可以选择当前职位所属组织下的片区组织进行维度查询
>
>> 2.3 经销商库存
>>#####  2.3.1、vue页面路径
>> src/pages/echart/lzlj/dealer-data-board/components/data-board-content.vue
>>#####  2.3.2、实现功能
>> * (1).左滑清除当前查询数据的条件项目(销售区域,客户,产品)
>> * (2).选择经销区域,客户,产品来展示库存量数据.
>
>> 2.4 经销商库存
>>#####  2.4.1、vue页面路径
>> src/pages/echart/lzlj/dealer-data-board/components/data-board-content.vue
>>#####  2.4.2、实现功能
>> * (1).左滑清除当前查询数据的条件项目(销售区域,客户,产品)
>> * (2).选择经销区域,客户,产品来展示库存量数据.
>> * (3).选择销量时有时间维度进行选择(本月,本季度,本年)


## 配置页面
> * 无


------ 经销商模块内容结束 ------


