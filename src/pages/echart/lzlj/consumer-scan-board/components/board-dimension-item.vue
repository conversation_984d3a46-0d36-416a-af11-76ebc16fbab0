<!--
@createdBy 黄鹏
@date 2024/04/22
@description: --- 看板维度切换模块
-->
<template>
    <view class="board-dimension-item">
        <line-title :title="title"
                    :filterName="dataRange === 'Area' ? areaName : ''"
                    @chooseData="chooseOrgData"></line-title>
        <org-select :user-info="userInfo" :show.sync="dialogFlag" @choose="changeOrg"></org-select>
        <scroll-view class="scroll-view-data" scroll-x="true">
            <view class="select-dimension">
                <select-button :key="index"
                               :label="item.name"
                               :selectedFlag="item.selectedFlag"
                               :value="item.value"
                               isBoard
                               @tap="chooseData($event,item)"
                               v-for="(item,index) in selectDimensionLevel"/>
            </view>
        </scroll-view>
    </view>
</template>

<script>
    import LineTitle from "../../components/line-title";
    import SelectButton from "../../components/select-button";
    import OrgSelect from "../../components/org-select";

    export default {
        name: "board-dimension-item",
        components: {LineTitle, SelectButton, OrgSelect},
        props: {
            dataRange: {
                type: String,
                default: ''
            },
            title: {
                type: String,
                default: ''
            }
        },
        data() {
            const userInfo = this.$taro.getStorageSync('token').result;
            return {
                userInfo,
                areaName: '全部',
                orgId: '',
                dialogFlag: false,
                selectDimensionLevel: [
                    {name: '全部', value: 'allTime', selectedFlag: false},
                    {name: '本财年', value: 'thisFiscalYear', selectedFlag: true},
                    {name: '本季', value: 'thisFiscalQuarter', selectedFlag: false},
                    {name: '本月', value: 'thisFiscalMonth', selectedFlag: false}
                ],
                dateType: 'thisFiscalYear'
            }
        },
        mounted () {
            this.initDimension();
        },
        methods: {
            /**
             * @createdBy 黄鹏
             * @date 2024/04/22
             * @methods: chooseOrgData
             * @para:
             * @description: 触发选择组织弹框
             **/
            chooseOrgData() {
                this.dialogFlag = !this.dialogFlag;
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/04/22
             * @methods: changeOrg
             * @para:
             * @description: 区域安全性下组织更改
             **/
            async changeOrg(item) {
                if (Object.keys(item).length === 0) return;
                this.areaName = item.text;
                this.orgId = item.id;
                this.$emit('dimension-change', this.title, this.orgId, this.dateType);
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/04/22
             * @methods: chooseData
             * @para:
             * @description: 数据维度切换
             **/
            async chooseData($event, data) {
                this.dateType = data.value;
                this.selectDimensionLevel.forEach((item) => {
                    item.selectedFlag = item.value === data.value;
                });
                this.$emit('dimension-change', this.title, this.orgId, this.dateType);
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/04/23
             * @methods: initDimension
             * @para:
             * @description: 初始化数据筛选维度
             **/
            initDimension () {
                this.areaName = this.userInfo.orgName;
                this.orgId = this.userInfo.orgId;
                this.dateType = 'thisFiscalYear';
                this.selectDimensionLevel.forEach((item) => {
                    item.selectedFlag = item.value === this.dateType;
                });
                this.$emit('dimension-change', this.title, this.orgId, this.dateType);
            }
        }
    }
</script>

<style lang="scss">
    .board-dimension-item {
        padding: 0 24px;
        margin-bottom: 36px;
        background: white;
        border-radius: 16px;
        font-size: 28px;
        font-weight: 400;
        /*deep*/
        .report-line-title {
            margin-bottom: 24px;

            .filter-dropdown-item {
                flex: 1;
            }
        }

        .activity-line-item-title {
            width: 152px;
            overflow-x: auto;
            white-space: nowrap;
            padding: 0 4px;
        }

        .item-info {
            color: #3F66EF;
            text-decoration: underline;
        }

        .scroll-view-data {
            margin-top: 24px;
            margin-bottom: 24px;

            .select-dimension {
                display: flex;

                .select-button {
                    min-width: 60px;
                    height: 30px;
                    border: none !important;
                    font-size: 28px;
                    line-height: 28px;
                    font-weight: 400;
                    background: #F2F3F6;
                }
            }
        }

        .no-data {
            width: 100%;
            text-align: center;
            color: #6D96FA;
            margin-top: 32px;
            font-size: 24px;
            padding-bottom: 32px;
        }

        .item-info-other {
            color: red;
        }
    }
</style>
