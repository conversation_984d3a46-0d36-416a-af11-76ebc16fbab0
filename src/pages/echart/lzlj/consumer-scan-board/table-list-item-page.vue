<!--
@createdBy 黄鹏
@date 2024/04/26
@description: ---  最小维度列表页面
-->
<template>
    <scroll-view class="table-list-item-page table"
                 scroll-x="false"
                 scroll-y="true"
                 :upper-threshold="1000"
                 :scroll-into-view="tagLineId"
                 @scrolltoupper="scrollToUpper"
                 @scrolltolower="scrollToLower">
        <view class="report-line-title">
            <view class="line">
                <view class="line-top"></view>
                <view class="line-bottom"></view>
            </view>
            <view class="stair-title">{{lineTitle}}</view>
        </view>
        <view class="table-con" v-if="pageParam.title === '消费者开瓶总览'">
            <view class="table-left">
                <view class="table-head">
                    <view class="word4 table-cell" v-if="dataRange === 'Mine'">客户名称</view>
                    <view class="word4 table-cell">产品大类</view>
                </view>
                <view class="table-desc-line" v-for="(row, index) in tableList" :key="index">
                    <view class="word8 table-cell" v-if="row.type === '合计'">合计</view>
                    <view v-else class="display-flex">
                        <view class="word4 table-cell" v-if="dataRange === 'Mine'">{{row.acctName}}</view>
                        <view class="word4 table-cell">{{row.matLClassName}}</view>
                    </view>
                </view>
            </view>
            <view class="table-right">
                <view class="table-head">
                    <view class="word12 table-cell">消费者开瓶扫码数量（瓶）</view>
                    <view class="word8 table-cell">消费者综合扫码率</view>
                    <view class="word8 table-cell">开箱件数（件）
                        <link-icon icon="mp-info-lite" @tap="showDesc('openBoxQty')" status="info"/>
                    </view>
                    <view class="word4 table-cell">开箱率</view>
                    <view class="word9 table-cell">开瓶扫码人数（人）</view>
                    <view class="word12 table-cell">核心消费者扫码人数（人）</view>
                    <view class="word10 table-cell">消费者流出异地扫码率</view>
                </view>
                <view class="table-line" v-for="(row, ins) in tableList" :key="ins"
                      :id="ins === 24 ? 'line25' : ins === 69 ? 'line70' : ''">
                    <view class="word12 table-cell">{{row.openBottleQty}}</view>
                    <view class="word8 table-cell">{{row.openBottleRatio}}</view>
                    <view class="word8 table-cell">{{row.openBoxQty}}</view>
                    <view class="word4 table-cell">{{row.openBoxRatio}}</view>
                    <view class="word9 table-cell">{{row.openBottleCsmQty}}</view>
                    <view class="word12 table-cell">{{row.openBottleCoreCsmQty}}</view>
                    <view class="word10 table-cell">{{row.openBottleBugsellRatio}}</view>
                </view>
            </view>
        </view>
        <view class="table-con" v-else-if="pageParam.title === '奖品发放总览'">
            <view class="table-left">
                <view class="table-head">
                    <view class="word4 table-cell" v-if="dataRange === 'Mine'">客户名称</view>
                    <view class="word4 table-cell">产品大类</view>
                </view>
                <view class="table-desc-line" v-for="(row, index) in tableList" :key="index">
                    <view class="word8 table-cell" v-if="row.type === '合计'">合计</view>
                    <view v-else class="display-flex">
                        <view class="word4 table-cell" v-if="dataRange === 'Mine'">{{row.acctName}}</view>
                        <view class="word4 table-cell">{{row.matLClassName}}</view>
                    </view>
                </view>
            </view>
            <view class="table-right">
                <view class="table-head">
                    <view class="word6 table-cell">开瓶扫码瓶数</view>
                    <view class="word9 table-cell">奖品发放数量（个）</view>
                    <view class="word10 table-cell">特等奖发放数量（个）</view>
                    <view class="word10 table-cell">一等奖发放数量（个）</view>
                </view>
                <view class="table-line" v-for="(row, ins) in tableList" :key="ins"
                      :id="ins === 24 ? 'line25' : ins === 69 ? 'line70' : ''">
                    <view class="word6 table-cell">{{row.openBottleQty}}</view>
                    <view class="word9 table-cell">{{row.prizeQty}}</view>
                    <view class="word10 table-cell">{{row.specialprizeQty}}</view>
                    <view class="word10 table-cell">{{row.firstprizeQty}}</view>
                </view>
            </view>
        </view>
        <view class="table-con" v-else-if="pageParam.title === '核心消费者开瓶总览（按产品类别）'">
            <view class="table-left">
                <view class="table-head">
                    <view class="word4 table-cell">产品类别</view>
                </view>
                <view class="table-desc-line" v-for="(row, index) in tableList" :key="index">
                    <view class="word4 table-cell">{{row.type === '合计' ? '合计' : row.matLClassName}}</view>
                </view>
            </view>
            <view class="table-right">
                <view class="table-head">
                    <view class="word10 table-cell">动销订单录入数（瓶）</view>
                    <view class="word10 table-cell">动销订单匹配数（单）</view>
                    <view class="word13 table-cell">核心消费者扫码数量（瓶）
                        <link-icon icon="mp-info-lite" @tap="showDesc('openBottleByCoreQty')" status="info"/>
                    </view>
                    <view class="word13 table-cell">核心消费者扫码人数（人）
                        <link-icon icon="mp-info-lite" @tap="showDesc('openBottleCoreCsmQty')" status="info"/>
                    </view>
                </view>
                <view class="table-line" v-for="(row, index) in tableList" :key="index"
                      :id="index === 24 ? 'line25' : index === 69 ? 'line70' : ''">
                    <view class="word10 table-cell">{{row.bookedorderBottleQty}}</view>
                    <view class="word10 table-cell"
                          @tap="gotoDetailPage(row.bookedorderMatchQty, row, false, false)"
                          :class="(row.type === '合计' || row.bookedorderMatchQty === 0) ? '' : 'item-info'">
                        {{row.bookedorderMatchQty}}
                    </view>
                    <view class="word13 table-cell">{{row.openBottleByCoreQty}}</view>
                    <view class="word13 table-cell"
                          @tap="gotoDetailPage(row.openBottleCoreCsmQty, row, true, false)"
                          :class="(row.type === '合计' || row.openBottleCoreCsmQty === 0) ? '' : 'item-info'">
                        {{row.openBottleCoreCsmQty}}
                    </view>
                </view>
            </view>
        </view>
        <view class="table-con" v-else-if="pageParam.title === '核心消费者开瓶总览（按K序列）'">
            <view class="table-left">
                <view class="table-head">
                    <view class="word8 table-cell">影响力K序列等级</view>
                </view>
                <view class="table-desc-line" v-for="(row, index) in tableList" :key="index">
                    <view class="word8 table-cell">{{row.type === '合计' ? '合计' : row.kTypeName}}</view>
                </view>
            </view>
            <view class="table-right">
                <view class="table-head">
                    <view class="word10 table-cell">动销订单录入数（瓶）</view>
                    <view class="word10 table-cell">动销订单匹配数（单）</view>
                    <view class="word13 table-cell">核心消费者扫码数量（瓶）
                        <link-icon icon="mp-info-lite" @tap="showDesc('openBottleByCoreQty')" status="info"/>
                    </view>
                    <view class="word13 table-cell">核心消费者扫码人数（人）
                        <link-icon icon="mp-info-lite" @tap="showDesc('openBottleCoreCsmQty')" status="info"/>
                    </view>
                </view>
                <view class="table-line" v-for="(row, index) in tableList" :key="index"
                      :id="index === 24 ? 'line25' : index === 69 ? 'line70' : ''">
                    <view class="word10 table-cell">{{row.bookedorderBottleQty}}</view>
                    <view class="word10 table-cell"
                          @tap="gotoDetailPage(row.bookedorderMatchQty, row, false, true)"
                          :class="(row.type === '合计' || row.bookedorderMatchQty === 0) ? '' : 'item-info'">
                        {{row.bookedorderMatchQty}}
                    </view>
                    <view class="word13 table-cell">{{row.openBottleByCoreQty}}</view>
                    <view class="word13 table-cell"
                          @tap="gotoDetailPage(row.openBottleCoreCsmQty, row, true, true)"
                          :class="(row.type === '合计' || row.openBottleCoreCsmQty === 0) ? '' : 'item-info'">
                        {{row.openBottleCoreCsmQty}}
                    </view>
                </view>
            </view>
        </view>
        <view v-if="total === -1 && tableList.length" class="load-more border-top" @tap="loadMore(true)">已全部加载完成</view>
        <view v-if="!tableList.length" class="no-data">暂无数据</view>
        <link-dialog v-model="openFlag" title="">
            <view>{{desc}}</view>
            <link-button slot="foot" @tap="openFlag=false">确定</link-button>
        </link-dialog>
        <water-mark></water-mark>
    </scroll-view>
</template>

<script>
    import waterMark from "../../../lzlj/components/water-mark";

    export default {
        name: "table-list-item-page",
        components: {waterMark},
        data() {
            const dataRange = this.pageParam.dataRange;
            const dateType = this.pageParam.dateType;
            const userInfo = this.$taro.getStorageSync('token').result;
            return {
                dataRange,
                dateType,
                userInfo,
                tableList: [],
                total: 0,
                page: 1,
                lineTitle: '',
                tableListAll: [],
                tagLineId: '',
                startIndex: 0,
                fieldsDesc: [],
                desc: '',
                openFlag: false
            }
        },
        created() {
            this.$taro.setNavigationBarTitle({title: this.pageParam.title});
            this.getLineTitle();
            this.loadMore();
            this.getFieldDesc();
        },
        methods: {
            /**
             * @createdBy 黄鹏
             * @date 2024/04/26
             * @methods: loadMore
             * @para:
             * @description: 查询表格数据
             **/
            async loadMore(nextPage) {
                if (nextPage) this.page += 1;
                this.$utils.showLoading();
                const param = {
                    dmpSrUrl: this.getDmpSrUrl(),
                    dataAccess: this.dataRange,
                    postnId: this.userInfo.postnId,
                    detailId: this.getDetailId(),
                    // orgId: orgId,
                    dateType: this.dateType || 'thisFiscalYear',
                    page: this.page,
                    rows: 20
                };
                if (this.dataRange === 'Area') {
                    param.orgId = this.userInfo.orgId;
                }
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmpSr/send', param);
                if (data.success) {
                    if (nextPage) {
                        this.tableListAll = this.tableListAll.concat(data.rows);
                        if (this.page > 5) {
                            this.startIndex += 20;
                            this.tableList = this.tableListAll.slice(this.startIndex);
                            this.tagLineId = '';
                            this.$nextTick(() => {
                                this.tagLineId = 'line70';
                            })
                        } else {
                            this.tableList = this.tableListAll;
                        }
                    } else {
                        this.tableListAll = data.rows || [];
                        this.tableList = this.tableListAll;
                    }
                    this.total = data.total;
                    this.$utils.hideLoading();
                    console.log(this.tableListAll.length, this.tableList.length)
                } else {
                    this.$utils.hideLoading();
                    this.$message.error({message: '查询表格数据失败！' + data.result, customFlag: true});
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/04/26
             * @methods: getDetailId
             * @para:
             * @description: detailId取值
             **/
            getDetailId() {
                let detailId = '';
                switch (this.dataRange) {
                    case 'Area':
                        detailId = this.pageParam.orgId;
                        break;
                    case 'Team':
                        detailId = this.pageParam.staffId;
                        break;
                    case 'Mine':
                        if (this.pageParam.title.startsWith('核心消费者开瓶总览')) {
                            detailId = this.pageParam.orgId;
                        } else {
                            detailId = this.pageParam.supplyDisId;
                        }
                        break;
                    default:
                        break;
                }
                return detailId;
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/04/28
             * @methods: getLineTitle
             * @para:
             * @description: 维度信息
             **/
            getLineTitle() {
                switch (this.dataRange) {
                    case 'Area':
                        this.lineTitle = '区域:' + this.pageParam.orgName;
                        break;
                    case 'Team':
                        this.lineTitle = '业务代表:' + this.pageParam.staffName;
                        break;
                    case 'Mine':
                        if (this.pageParam.title.startsWith('核心消费者开瓶总览')) {
                            this.lineTitle = '区域:' + this.pageParam.orgName;
                        } else {
                            this.lineTitle = '经销商/分销商:' + this.pageParam.supplyDisName;
                        }
                        break;
                    default:
                        break;
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/04/28
             * @methods: getDmpSrUrl
             * @para:
             * @description: 接口地址
             **/
            getDmpSrUrl() {
                let dmpSrUrl = '';
                switch (this.pageParam.title) {
                    case '消费者开瓶总览':
                        dmpSrUrl = '/link/consumerDeckleReportSR/queryDeckleDetailPage';
                        break;
                    case '奖品发放总览':
                        dmpSrUrl = '/link/consumerDeckleReportSR/queryPrizeDetailPage';
                        break;
                    case '核心消费者开瓶总览（按产品类别）':
                        dmpSrUrl = '/link/consumerDeckleReportSR/queryProductDetailPage';
                        break;
                    case '核心消费者开瓶总览（按K序列）':
                        dmpSrUrl = '/link/consumerDeckleReportSR/queryKSequenceDetailPage';
                        break;
                    default:
                        break;
                }
                return dmpSrUrl;
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/05/07
             * @methods: scrollToUpper
             * @para:
             * @description: 向上滚动加载上一页
             **/
            scrollToUpper(e) {
                console.log(this.startIndex, 123)
                if (this.startIndex > 0) {
                    this.startIndex -= 20;
                    this.tableList = this.tableListAll.slice(this.startIndex, this.startIndex + 100);
                    this.tagLineId = '';
                    this.$nextTick(() => {
                        this.tagLineId = 'line25';
                    })
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/05/07
             * @methods: scrollToLower
             * @para:
             * @description: 向下滚动加载下一页
             **/
            scrollToLower() {
                console.log('lower')
                if ((this.startIndex + 120) <= this.tableListAll.length) {
                    this.$utils.showLoading();
                    this.startIndex += 20;
                    this.tableList = this.tableListAll.slice(this.startIndex, this.startIndex + 100);
                    this.tagLineId = '';
                    this.$nextTick(() => {
                        this.tagLineId = 'line70';
                        this.$utils.hideLoading();
                    })
                } else {
                    if (this.total !== -1) {
                        this.loadMore(true);
                    }
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/05/07
             * @methods: gotoDetailPage
             * @para:
             * @description: 指标下钻
             **/
            gotoDetailPage(value, row, byConsumer, isK) {
                if (row.type === '合计' || !value) return;
                this.$nav.push('/pages/echart/lzlj/consumer-scan-board/target-detail-page.vue', {
                    byConsumer: byConsumer,  // false:动销订单匹配数（单）   true: 核心消费者扫码人数（人）
                    isK: isK,                // false:按产品统计  true:按K序列统计
                    dataRange: this.dataRange,
                    dateType: this.dateType,
                    staffId: row.staffId,
                    orgId: row.orgId,
                    kType: row.kTypeName,
                    matLClassName: row.matLClassName
                })
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/05/09
             * @methods: getFieldDesc
             * @para:
             * @description: 获取字段指标说明
             **/
            async getFieldDesc() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/expConfigHead/queryByExamplePage', {
                    oauth: 'ALL',
                    filtersRaw: [{
                        "property": "expFromName",
                        "operator": "=",
                        "value": "消费者开瓶看板",
                        "id": "expFromName"
                    }]
                });
                if (data.success) {
                    const headId = data.rows[0].id;
                    const res = await this.$http.post(this.$env.appURL + '/action/link/expConfigItem/queryByExamplePage', {
                        oauth: 'ALL',
                        filtersRaw: [{"id": "headId", "property": "headId", "value": headId}]
                    });
                    if (res.success) {
                        this.fieldsDesc = res.rows;
                    } else {
                        this.$message.error('查询指标说明失败:' + res.result);
                    }
                } else {
                    this.$message.error('查询指标说明失败:' + data.result);
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/05/09
             * @methods: showDesc
             * @para:
             * @description: 展示字段说明
             **/
            showDesc(field) {
                const find = this.fieldsDesc.find((item) => item.filedName === field);
                this.desc = find.fieldDescribe;
                this.openFlag = true;
                // this.$dialog({
                //     title: '',
                //     content: find.fieldDescribe,
                //     cancelButton: false,
                //     onConfirm: () => {
                //         // this.$message.primary('确定删除')
                //     },
                //     onCancel: () => {
                //         // this.$message.info('取消删除')
                //     }
                // })
            }
        }
    }
</script>

<style lang="scss">
    @import "./css/board.scss";

    .table-list-item-page {
        width: 100vw;
        height: 100vh;
        padding: 0 24px;
        margin-bottom: 36px;
        background: white;
        border-radius: 28px;
        font-size: 28px;
        font-weight: 400;
        box-sizing: border-box;

        .report-line-title {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding: 24px 0;

            .line {
                .line-top {
                    width: 8px;
                    height: 16px;
                    background: #3FE0E2;
                }

                .line-bottom {
                    width: 8px;
                    height: 16px;
                    background: #2F69F8;
                }
            }

            .stair-title {
                flex: 1;
                overflow-x: scroll;
                white-space: nowrap;
                margin-left: 16px;
                font-family: PingFangSC-Semibold, serif;
                font-size: 32px;
                color: #262626;
                letter-spacing: 1px;
                line-height: 32px;
                text-align: left;
            }
        }

        .table-con {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .table-left {
                .table-head {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    color: #999999;
                    background: #f8faff;

                    view {
                        flex-shrink: 0;
                        /*padding: 0 12px;*/
                        text-align: center;
                        color: #999999;
                        background: #f8faff;
                    }
                }

                .table-desc-line {
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    flex-direction: column;
                    box-shadow: 2px 2px 0px 0px #f0f2f8;

                    .desc-item-con {
                        width: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        view {
                            flex-shrink: 0;
                            /*padding: 0 12px;*/
                        }

                        .desc-sub-item-con {
                            box-shadow: -2px 0px 0px 0px #f0f2f8;

                            .border-con {
                            }

                            view {
                                /*padding: 0;*/
                            }
                        }
                    }
                }
            }

            .table-right {
                flex: 1;
                overflow: scroll;

                .table-head {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    color: #999999;
                    height: 80px;
                    line-height: 80px;
                    background: #f8faff;

                    view {
                        color: #999999;
                        background: #f8faff;
                    }
                }

                .table-line {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    height: 80px;
                    line-height: 80px;

                    &:first-child {
                        view {
                            box-shadow: 0px 4.5px 0px -1px #f0f2f8;
                        }
                    }

                    &:last-child {
                        view {
                            box-shadow: 0px 4.5px 0px -1px #f0f2f8;
                        }
                    }
                }
            }
        }
    }
</style>
