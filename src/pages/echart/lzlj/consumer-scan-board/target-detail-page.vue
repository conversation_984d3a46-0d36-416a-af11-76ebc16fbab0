<!--
@createdBy 黄鹏
@date 2024/05/06
@description: ---  指标下钻页面
-->
<template>
    <scroll-view class="target-detail-page table"
                 :scroll-x="false"
                 scroll-y="true"
                 :upper-threshold="1000"
                 :scroll-into-view="tagLineId"
                 @scrolltoupper="scrollToUpper"
                 @scrolltolower="scrollToLower">
        <view v-if="pageParam.byConsumer" class="table-con">
            <view class="table-head">
                <view class="table-cell word7">核心消费者姓名</view>
                <view class="table-cell word6">消费者手机号</view>
                <view class="table-cell word3">跟进人</view>
                <view class="table-cell word10">所属客户</view>
                <view class="table-cell word6">消费者K等级</view>
                <view class="table-cell word6">消费者V等级</view>
                <view class="table-cell word14">产品名称</view>
                <view class="table-cell word7">扫码数量（瓶）</view>
                <view class="table-cell word6">动销订单单数</view>
                <view class="table-cell word11">动销订单录入数量（瓶）</view>
            </view>
            <view v-for="(item, index) in tableList" :key="index"
                  class="table-line"
                  :id="index === 24 ? 'line25' : index === 69 ? 'line70' : ''">
                <view class="table-cell word7">{{item.consumerName}}</view>
                <view class="table-cell word6">{{item.consumerPhone}}</view>
                <view class="table-cell word3">{{item.staffName}}</view>
                <view class="table-cell word10">{{item.belongToAcct}}</view>
                <view class="table-cell word6">{{item.kTypeName}}</view>
                <view class="table-cell word6">{{item.vTypeName}}</view>
                <view class="table-cell word14">{{item.prodName}}</view>
                <view class="table-cell word7">{{item.scanBotQty}}</view>
                <view class="table-cell word6">{{item.ordQty}}</view>
                <view class="table-cell word11">{{item.ordBotQty}}</view>
            </view>
        </view>
        <view v-else class="table-con">
            <view class="table-head">
                <view class="table-cell word4">业务代表</view>
                <view class="table-cell word5">登记消费者</view>
                <view class="table-cell word11">订单编号</view>
                <view class="table-cell word10">订单时间</view>
                <view class="table-cell word14">产品名称</view>
                <view class="table-cell word7">登记数量（件）</view>
                <view class="table-cell word7">登记数量（瓶）</view>
                <view class="table-cell word5">已开箱件数</view>
                <view class="table-cell word12">订单中已开瓶扫码数（瓶）</view>
                <view class="table-cell word11">消费者本人扫码数（瓶）</view>
            </view>
            <view v-for="(item, index) in tableList" :key="index"
                  class="table-line"
                  :id="index === 24 ? 'line25' : index === 69 ? 'line70' : ''">
                <view class="table-cell word4">{{item.staffName}}</view>
                <view class="table-cell word5">{{item.buyerName}}</view>
                <view class="table-cell word11">{{item.ordCode}}</view>
                <view class="table-cell word10">{{item.auditTime | date('YYYY-MM-DD HH:mm:ss')}}</view>
                <view class="table-cell word14">{{item.prodName}}</view>
                <view class="table-cell word7">{{item.ordPcQty}}</view>
                <view class="table-cell word7">{{item.ordBotQty}}</view>
                <view class="table-cell word5">{{item.openPcQty}}</view>
                <view class="table-cell word12">{{item.openBotQty}}</view>
                <view class="table-cell word11">{{item.csmScanBotQty}}</view>
            </view>
        </view>
        <water-mark></water-mark>
    </scroll-view>
</template>

<script>
    import waterMark from "../../../lzlj/components/water-mark";

    export default {
        name: "target-detail-page",
        components: {waterMark},
        data() {
            const userInfo = this.$taro.getStorageSync('token').result;
            return {
                userInfo,
                tableList: [],
                tableListAll: [],
                tagLineId: '',
                page: 1,
                total: 0,
                startIndex: 0,
                dmpSrUrl: ''
            }
        },
        created() {
            if (this.pageParam.byConsumer) {
                this.dmpSrUrl = '/link/consumerDeckleReportSR/queryDownAuthenticationDetail';
            } else {
                this.dmpSrUrl = '/link/consumerDeckleReportSR/queryDownOrderDetail';
            }
            this.loadMore();
        },
        methods: {
            /**
             * @createdBy 黄鹏
             * @date 2024/05/07
             * @methods: loadMore
             * @para:
             * @description: 分页加载数据
             **/
            async loadMore(nextPage) {
                console.log(this.pageParam, 11)
                if (nextPage) this.page += 1;
                this.$utils.showLoading();
                const param = {
                    dmpSrUrl: this.dmpSrUrl,
                    dataAccess: this.pageParam.dataRange,
                    postnId: this.userInfo.postnId,
                    dateType: this.pageParam.dateType,
                    page: this.page,
                    rows: 20
                };
                if (this.pageParam.dataRange === 'Team') {
                    param.staffId = this.pageParam.staffId;
                } else {
                    param.orgId = this.pageParam.orgId;
                }
                // 区分是按产品还是按K序列
                if (this.pageParam.isK) {
                    param.kType = this.pageParam.kType;
                } else {
                    param.matLClassName = this.pageParam.matLClassName;
                }
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmpSr/send', param);
                if (data.success) {
                    if (nextPage) {
                        this.tableListAll = this.tableListAll.concat(data.rows);
                        if (this.page > 5) {
                            this.startIndex += 20;
                            this.tableList = this.tableListAll.slice(this.startIndex);
                            this.tagLineId = '';
                            this.$nextTick(() => {
                                this.tagLineId = 'line70';
                            })
                        } else {
                            this.tableList = this.tableListAll;
                        }
                    } else {
                        this.tableListAll = data.rows || [];
                        this.tableList = this.tableListAll;
                    }
                    this.total = data.total;
                    this.$utils.hideLoading();
                    console.log(this.tableListAll.length, this.tableList.length)
                } else {
                    this.$utils.hideLoading();
                    this.$message.error({message: '查询表格数据失败！' + data.result, customFlag: true});
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/05/07
             * @methods: scrollToUpper
             * @para:
             * @description: 向上滚动加载上一页
             **/
            scrollToUpper(e) {
                console.log(this.startIndex, 123)
                if (this.startIndex > 0) {
                    this.startIndex -= 20;
                    this.tableList = this.tableListAll.slice(this.startIndex, this.startIndex + 100);
                    this.tagLineId = '';
                    this.$nextTick(() => {
                        this.tagLineId = 'line25';
                    })
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/05/07
             * @methods: scrollToLower
             * @para:
             * @description: 向下滚动加载下一页
             **/
            scrollToLower() {
                console.log('lower')
                if ((this.startIndex + 120) <= this.tableListAll.length) {
                    this.$utils.showLoading();
                    this.startIndex += 20;
                    this.tableList = this.tableListAll.slice(this.startIndex, this.startIndex + 100);
                    this.tagLineId = '';
                    this.$nextTick(() => {
                        this.tagLineId = 'line70';
                        this.$utils.hideLoading();
                    })
                } else {
                    if (this.total !== -1) {
                        this.loadMore(true);
                    }
                }
            }
        }
    }
</script>

<style lang="scss">
    @import "./css/board.scss";

    .target-detail-page {
        width: 100vw;
        height: 100vh;
        margin-bottom: 36px;
        background: white;
        border-radius: 28px;
        font-size: 28px;
        font-weight: 400;
        /*box-sizing: border-box;*/

        .table-con {
            width: 100vw;
            overflow-x: scroll;
        }

        .table-head {
            padding: 0 24px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            color: #999999;
            background: #f8faff;

            view {
                background: #f8faff;
            }
        }

        .table-line {
            padding: 0 24px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            height: 80px;
            line-height: 80px;
        }
    }
</style>
