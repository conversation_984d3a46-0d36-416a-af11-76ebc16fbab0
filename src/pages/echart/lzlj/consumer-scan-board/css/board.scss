.table {
    $fontSize: 28px;

    @for $i from 1 through 20 {
        .word#{$i} {
            width: $fontSize * $i;
        }
    }

    view {
        text-align: center;
    }

    font-size: $fontSize!important;

    .table-cell {
        height: 80px;
        line-height: 80px;
        padding: 0 12px;
        overflow-x: scroll;
        overflow-y: hidden;
        white-space: nowrap;
        flex-shrink: 0;
        text-align: center;
    }

    .display-flex {
        display: flex;
        align-items: center;
        justify-content: flex-start;
    }

    .item-info {
        color: #3F66EF;
        text-decoration: underline;
    }

    .load-more {
        height: 80px;
        line-height: 80px;
        text-align: center;
        color: #6D96FA;
        font-size: 24px;
    }

    .no-more {
        height: 20px;
        line-height: 20px;
        text-align: center;
        color: #6D96FA;
        font-size: 24px;
    }

    .no-data {
        width: 100%;
        text-align: center;
        color: #6D96FA;
        margin-top: 32px;
        font-size: 24px;
        padding-bottom: 32px;
    }

    .border-bottom {
        box-shadow: 0px 2px 0px 0px #f0f2f8;
    }

    .border-top {
        box-shadow: 0px -4.5px 0px -1px #f0f2f8;
    }

    .mp-info-lite:before {
        height: 30px;
    }
}
