<!--
@createdBy 黄鹏
@date 2024/04/22
@description: --- 消费者开瓶看板
-->
<template>
    <link-page class="consumer-scan-board">
        <view class="filter-type-item" @tap="chooseOauthData">{{pageOauthName}}
            <link-icon icon="mp-desc"/>
        </view>
        <board-dimension-item title="消费者开瓶总览"
                              ref="consumerDimension"
                              :data-range="dataRange"
                              @dimension-change="dimensionChange"></board-dimension-item>
        <consumer-scan-overview ref="consumerScanOverview"
                                @show-desc="showDesc"
                                :user-info="userInfo"></consumer-scan-overview>
        <board-dimension-item title="奖品发放总览"
                              ref="prizeDimension"
                              :data-range="dataRange"
                              @dimension-change="dimensionChange"></board-dimension-item>
        <prize-issued-overview ref="prizeIssuedOverview"
                               :user-info="userInfo"></prize-issued-overview>
        <board-dimension-item title="核心消费者开瓶总览"
                              ref="coreConsumerDimension"
                              :data-range="dataRange"
                              @dimension-change="dimensionChange"></board-dimension-item>
        <core-consumer-scan-overview ref="coreConsumerScanOverview"
                                     @show-desc="showDesc"
                                     :user-info="userInfo"
                                     :is-check-detail="isCheckDetail"></core-consumer-scan-overview>
        <core-consumer-scan-overview-k ref="coreConsumerScanOverviewK"
                                       @show-desc="showDesc"
                                       :user-info="userInfo"
                                       :is-check-detail="isCheckDetail"></core-consumer-scan-overview-k>
        <water-mark></water-mark>
    </link-page>
</template>

<script>
    import consumerScanOverview from './components/consumer-scan-overview';
    import prizeIssuedOverview from './components/prize-issued-overview';
    import coreConsumerScanOverview from './components/core-consumer-scan-overview';
    import coreConsumerScanOverviewK from './components/core-consumer-scan-overview-k';
    import boardDimensionItem from './components/board-dimension-item';
    import waterMark from "../../../lzlj/components/water-mark";

    export default {
        name: "consumer-scan-board",
        components: {
            consumerScanOverview,
            prizeIssuedOverview,
            coreConsumerScanOverview,
            coreConsumerScanOverviewK,
            boardDimensionItem,
            waterMark
        },
        props: {
            secMenus: {
                type: Array,
                default: []
            }
        },
        data() {
            const userInfo = this.$taro.getStorageSync('token').result;
            return {
                userInfo,
                isCheckDetail: null,       // 核心消费者开瓶总览是否下钻
                dataRange: '',         // 数据范围
                pageOauth: '',
                pageOauthName: '',
                fieldsDesc: []
            }
        },
        async created() {
            const oauth = this.secMenus.find((item) => item.securityMode === 'MY_ORG') ? this.secMenus.find((item) => item.securityMode === 'MY_ORG') : this.secMenus.find((item) => item.securityMode === 'MY_POSTN') ? this.secMenus.find((item) => item.securityMode === 'MY_POSTN') : this.secMenus.find((item) => item.securityMode === 'MY') || {};
            this.pageOauth = oauth.securityMode;
            this.pageOauthName = oauth.name;
            if (oauth.securityMode === 'MY') {
                this.dataRange = 'Mine';
            } else if (oauth.securityMode === 'MY_POSTN') {
                this.dataRange = 'Team';
            } else if (oauth.securityMode === 'MY_ORG') {
                this.dataRange = 'Area';
            } else {
                this.dataRange = '';
            }
            await this.queryCfg();
            this.getFieldDesc();
        },
        methods: {
            /**
             * @createdBy 黄鹏
             * @date 2024/04/22
             * @methods: queryCfg
             * @para:
             * @description: 通过职位类型判断核心消费者开瓶总览是否下钻
             **/
            async queryCfg() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', {key: 'CONSUMER_SCAN_DETAIL_POSTN_TYPE'});
                if (data.success && data.value) {
                    const postnTypes = data.value.split(',');
                    this.isCheckDetail = postnTypes.indexOf(this.userInfo.positionType) !== -1;
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/05/09
             * @methods: getFieldDesc
             * @para:
             * @description: 获取字段指标说明
             **/
            async getFieldDesc() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/expConfigHead/queryByExamplePage', {
                    oauth: 'ALL',
                    filtersRaw: [{
                        "property": "expFromName",
                        "operator": "=",
                        "value": "消费者开瓶看板",
                        "id": "expFromName"
                    }]
                });
                if (data.success) {
                    const headId = data.rows[0].id;
                    const res = await this.$http.post(this.$env.appURL + '/action/link/expConfigItem/queryByExamplePage', {
                        oauth: 'ALL',
                        filtersRaw: [{"id": "headId", "property": "headId", "value": headId}]
                    });
                    if (res.success) {
                        this.fieldsDesc = res.rows;
                    } else {
                        this.$message.error('查询指标说明失败:' + res.result);
                    }
                } else {
                    this.$message.error('查询指标说明失败:' + data.result);
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/04/22
             * @methods: pageOauthChange
             * @para:
             * @description: 页面安全性切换
             **/
            pageOauthChange(oauth) {
                this.$utils.showLoading();
                if (oauth.securityMode === 'MY') {
                    this.dataRange = 'Mine';
                } else if (oauth.securityMode === 'MY_POSTN') {
                    this.dataRange = 'Team';
                } else if (oauth.securityMode === 'MY_ORG') {
                    this.dataRange = 'Area';
                } else {
                    this.dataRange = '';
                }
                this.pageOauth = oauth.securityMode;
                this.pageOauthName = oauth.name;
                this.$nextTick(() => {
                    this.$refs.consumerDimension.initDimension();
                    this.$refs.prizeDimension.initDimension();
                    this.$refs.coreConsumerDimension.initDimension();
                })
                this.$utils.hideLoading();
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/04/22
             * @methods: chooseOauthData
             * @para:
             * @description: 页触发面安全性选择
             **/
            chooseOauthData() {
                this.$actionSheet(() => (
                    <link-action-sheet title="请选择数据范围" onCancel={() => {
                    }}>
                        {
                            this.secMenus.map(
                                (item) => {
                                    return <link-action-sheet-item label={item.name}
                                                                   onTap={() => this.pageOauthChange(item)}/>
                                }
                            )
                        }
                    </link-action-sheet>
                ));
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/04/22
             * @methods: dimensionChange
             * @para:
             * @description: 数据查询维度变化
             **/
            dimensionChange(title, orgId, dateType) {
                switch (title) {
                    case '消费者开瓶总览':
                        this.$refs.consumerScanOverview.queryData(this.dataRange, orgId, dateType);
                        break;
                    case '奖品发放总览':
                        this.$refs.prizeIssuedOverview.queryData(this.dataRange, orgId, dateType);
                        break;
                    case '核心消费者开瓶总览':
                        this.$refs.coreConsumerScanOverview.queryData(this.dataRange, orgId, dateType);
                        this.$refs.coreConsumerScanOverviewK.queryData(this.dataRange, orgId, dateType);
                        break;
                    default:
                        break;
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/05/09
             * @methods: showDesc
             * @para:
             * @description: 展示字段说明
             **/
            showDesc(field) {
                const find = this.fieldsDesc.find((item) => item.filedName === field);
                this.$dialog({
                    title: '',
                    content: find.fieldDescribe,
                    cancelButton: false,
                    onConfirm: () => {
                        // this.$message.primary('确定删除')
                    },
                    onCancel: () => {
                        // this.$message.info('取消删除')
                    }
                })
            }
        }
    }
</script>

<style lang="scss">
    .consumer-scan-board {
        .filter-type-item {
            width: 98%;
            height: 40px;
            margin: 12px 4px;
            font-size: 28px;
            color: #333333;
            line-height: 40px;
            font-weight: 400;
            text-align: center;
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .link-icon {
                width: 16px;
                height: 12px;
                color: #CCCCCC;
                margin-left: 8px;
            }
        }
    }
</style>
