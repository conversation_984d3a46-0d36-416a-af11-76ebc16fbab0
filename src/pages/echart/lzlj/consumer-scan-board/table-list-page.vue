<!--
@createdBy 黄鹏
@date 2024/04/26
@description: ---  最大维度列表页面
-->
<template>
    <link-page class="table-list-page">
        <consumer-scan-overview v-if="pageParam.title === '消费者开瓶总览'"
                                @show-desc="showDesc"
                                :is-list-page="true"
                                :list-page-param="listPageParam"
                                :user-info="userInfo"></consumer-scan-overview>
        <prize-issued-overview v-if="pageParam.title === '奖品发放总览'"
                               :is-list-page="true"
                               :list-page-param="listPageParam"
                               :user-info="userInfo"></prize-issued-overview>
        <core-consumer-scan-overview v-if="pageParam.title === '核心消费者开瓶总览（按产品类别）'"
                                     @show-desc="showDesc"
                                     :is-list-page="true"
                                     :list-page-param="listPageParam"
                                     :user-info="userInfo"></core-consumer-scan-overview>
        <core-consumer-scan-overview-k v-if="pageParam.title === '核心消费者开瓶总览（按K序列）'"
                                       @show-desc="showDesc"
                                       :is-list-page="true"
                                       :list-page-param="listPageParam"
                                       :user-info="userInfo"></core-consumer-scan-overview-k>
        <water-mark></water-mark>
    </link-page>
</template>

<script>
    import consumerScanOverview from "./components/consumer-scan-overview";
    import prizeIssuedOverview from "./components/prize-issued-overview";
    import coreConsumerScanOverview from "./components/core-consumer-scan-overview";
    import coreConsumerScanOverviewK from "./components/core-consumer-scan-overview-k";
    import waterMark from "../../../lzlj/components/water-mark";

    export default {
        name: "table-list-page",
        components: {
            consumerScanOverview,
            prizeIssuedOverview,
            coreConsumerScanOverview,
            coreConsumerScanOverviewK,
            waterMark
        },
        data() {
            const listPageParam = {
                dataRange: this.pageParam.dataRange,
                dateType: this.pageParam.dateType,
                orgId: this.pageParam.orgId
            }
            const userInfo = this.$taro.getStorageSync('token').result;
            return {
                listPageParam,
                userInfo,
                fieldsDesc: []
            }
        },
        created() {
            this.$taro.setNavigationBarTitle({title: this.pageParam.title});
            this.getFieldDesc();
        },
        methods: {
            /**
             * @createdBy 黄鹏
             * @date 2024/05/09
             * @methods: getFieldDesc
             * @para:
             * @description: 获取字段指标说明
             **/
            async getFieldDesc() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/expConfigHead/queryByExamplePage', {
                    oauth: 'ALL',
                    filtersRaw: [{
                        "property": "expFromName",
                        "operator": "=",
                        "value": "消费者开瓶看板",
                        "id": "expFromName"
                    }]
                });
                if (data.success) {
                    const headId = data.rows[0].id;
                    const res = await this.$http.post(this.$env.appURL + '/action/link/expConfigItem/queryByExamplePage', {
                        oauth: 'ALL',
                        filtersRaw: [{"id": "headId", "property": "headId", "value": headId}]
                    });
                    if (res.success) {
                        this.fieldsDesc = res.rows;
                    } else {
                        this.$message.error('查询指标说明失败:' + res.result);
                    }
                } else {
                    this.$message.error('查询指标说明失败:' + data.result);
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/05/09
             * @methods: showDesc
             * @para:
             * @description: 展示字段说明
             **/
            showDesc (field) {
                const find = this.fieldsDesc.find((item) => item.filedName === field);
                this.$dialog({
                    title: '',
                    content: find.fieldDescribe,
                    cancelButton: false,
                    onConfirm: () => {
                        // this.$message.primary('确定删除')
                    },
                    onCancel: () => {
                        // this.$message.info('取消删除')
                    }
                })
            }
        }
    }
</script>

<style lang="scss">
    .table-list-page {
        background: white;
    }
</style>
