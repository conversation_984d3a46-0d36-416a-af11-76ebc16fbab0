/**
 * 饼图
 * <AUTHOR>
 * @date	2023/11/15 16:45
 */
export function target<PERSON>ie<PERSON>hart(echartInitConfig, seriesData, totalNum) {
  return {
      series: [
          {
              type: 'pie',
              name: '外环数据',
              z: 1,
              radius: ['35%', '60%'],
              avoidLabelOverlap: true,
              startAngle: 260, //起始角度(防止label超出高度不显示)
              itemStyle: {
                  borderColor: '#fff',
                  borderWidth: 1,
                  normal: {
                      color: (list) => {
                          // 这里的数组一定要和实际的类目长度相等或大于，不然会缺少颜色报错
                          const colorList = [
                              {colorStart: '#4199FF', colorEnd: '#67E2FF'},
                              {colorStart: '#00B6B7', colorEnd: '#00DCDC'},
                              {colorStart: '#6E57FF', colorEnd: '#CE97FF'},
                              {colorStart: '#FF6184', colorEnd: '#FFC191'},
                              {colorStart: '#0AA992', colorEnd: '#0BE18B'},
                              {colorStart: '#489BFF', colorEnd: '#9acffd'},
                              {colorStart: '#FFBC12', colorEnd: '#FFEB50'},
                              {colorStart: '#597EF7', colorEnd: '#92B5FC'},
                              {colorStart: '#EA5ED9', colorEnd: '#FA71F6'}
                          ];
                          return new echartInitConfig.echarts.graphic.LinearGradient(1, 0, 0, 0, [
                              {//左、下、右、上
                                  offset: 0,
                                  color: colorList[list.dataIndex].colorStart
                              }, {
                                  offset: 1,
                                  color: colorList[list.dataIndex].colorEnd
                              }
                          ]);
                      }
                  }
              },
              label: {
                  formatter: '\n{name|{b}}\n{val|{c}}',
                  rich: {
                      name: {
                          fontSize: 12,
                          lineHeight: 14,
                          align: 'center',
                          color: '#9EA1AE'
                      },
                      val: {
                          fontSize: 14,
                          lineHeight: 18,
                          align: 'center',
                          color: '#212223'
                      }
                  }
              },
              data: seriesData
          },
          {
              type: 'pie',
              name: '内环文字',
              color: '#fff',
              z: 2,
              radius: '40%',
              tooltip: {
                  show: false,
              },
              label: {
                  normal: {
                      show: true,
                      position: 'center', //中间文字显示
                      color: '#333',
                      formatter: '{total|' + totalNum +'}'+ '\n\r' + '{active|总计(场)}',
                      rich: {
                          total: {
                              fontSize: 18,
                              fontWeight: 600,
                              color: '#333'
                          },
                          active: {
                              fontSize: 14,
                              color: '#333',
                              lineHeight: 30,
                          },
                      }
                  },
                  emphasis: { //中间文字显示
                      show: true,
                  }
              },
              data: seriesData
          }
      ]
  }
}

