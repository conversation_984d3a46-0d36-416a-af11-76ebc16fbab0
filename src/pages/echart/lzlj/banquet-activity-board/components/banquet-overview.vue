<template>
    <view class="banquet-overview">
        <line-title title="宴席活动概览"></line-title>

        <view class="board-wrap">
            <view v-for="(item, index) in overviewBoard" class="board-item-wrap" :key="index">
                <view class="board-item">
                    <view class="total">
                        <text v-if="chartData && chartData[item.label]">{{chartData[item.label]}}</text>
                        <text v-else>0</text>
                        <text class="unit">{{item.unit}}</text>
                    </view>
                    <view class="label">{{item.name}}</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import LineTitle from '../../../../lzlj/components/line-title';

export default {
    name: 'banquet-overview',
    components: {LineTitle},
    props: {
        chartData: Object
    },
    data() {
        return {
            overviewBoard: [
                {name: '累计宴席桌数', label: 'totalActTableNum', unit: '桌'},
                {name: '累计宴席场次', label: 'totalActHoldNum', unit: '场'},
                {name: '平均宴席桌数', label: 'averageActTableNum', unit: '桌'},
                {name: '开瓶率', label: 'consumerCrackRatio', unit: '%'},
                {name: '累计出库数量', label: 'salesOutNum', unit: '瓶'},
                {name: '累计退货数量', label: 'salesBackNum', unit: '瓶'}
            ] // 总览数据配置
        }
    },
    methods: {

    }
}
</script>

<style lang="scss">
    .banquet-overview {
        background: #fff;
        border-radius: 24px;
        margin-bottom: 24px;

        .line-title {
            margin-bottom: 24px;
        }

        .board-wrap {
            padding: 24px 24px 0 24px;
            display: flex;
            flex-wrap: wrap;

            .board-item-wrap {
                width: 33%;
                @include flex-start-center;
                margin-bottom: 24px;

                .board-item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    flex: 1;
                    position: relative;

                    .total {
                        margin-bottom: 14px;
                        font-size: 34px;
                        font-weight: bold;

                        .unit {
                            margin-left: 8px;
                            font-size: 28px;
                            font-weight: normal;
                        }
                    }

                    .label {
                        font-size: 28px;
                        color: #6A6D75;
                    }
                }
            }
        }
    }
</style>
