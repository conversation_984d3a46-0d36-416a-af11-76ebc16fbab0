<template>
    <view class="banquet-types">
        <line-title title="宴席类型占比"></line-title>

        <view class="echart-area">
            <link-echart :option="chartOption" v-if="chartOption && chartData" :height="echartHeight + 'px'" :force-use-old-canvas="false"/>
            <view v-else>暂无数据</view>
        </view>
    </view>
</template>

<script>
import LineTitle from '../../../../lzlj/components/line-title';
import {targetPieChart} from "../echart.utils";

export default {
    name: 'banquet-types',
    components: {LineTitle},
    props: {
        lovConfig: Object,
        chartData: Array
    },
    data() {
        return {
            echartHeight: 254, // 图表高度
            chartOption: null,
        }
    },
    methods: {
        /**
         * 绘制宴席类型占比饼图
         * <AUTHOR>
         * @date	2023/11/15 16:43
         */
        drawChart(chartData) {
            this.chartOption = null;
            if (chartData && chartData.length && chartData[0].totalActHoldNum === 0) {
                return;
            }
            // 数据按从大到小排序
            chartData.sort((a, b) => a.partActHoldNum - b.partActHoldNum).reverse();
            // 数据数量大于6时，只展示排行前6的数据
            chartData.length = chartData.length > 6 ? 6 : chartData.length;
            const seriesData = chartData.map((item) => ({
                value: item.partActHoldNum,
                name: this.lovConfig[item.banquetType]
            }));
            this.chartOption = echartInitConfig => targetPieChart(echartInitConfig, seriesData, chartData[0].totalActHoldNum);
        }
    }
}
</script>

<style lang="scss">
    .banquet-types {
        background: #fff;
        border-radius: 24px;
        margin-bottom: 24px;

        .line-title {
            margin-bottom: 24px;
        }

        .echart-area {
            width: 100%;
            min-height: 200px;
            @include flex-center-center;
            font-size: 26px;
            color: #ccc;
        }
    }
</style>
