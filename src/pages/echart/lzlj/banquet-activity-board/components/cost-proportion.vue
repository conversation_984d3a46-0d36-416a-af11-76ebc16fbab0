<template>
    <view class="cost-proportion">
        <line-title title="费用占比"></line-title>
        <view class="board-container">
            <view class="column-item left">
                <view class="left-value">
                    <view class="data" v-if="chartData">{{chartData.banquetActFee}}</view>
                    <view class="label">渠道推荐费(元)</view>
                </view>
            </view>
            <view class="column-item center">
                <view class="center-item">
                    <view class="data" v-if="chartData">{{chartData.banquetTerminalFee || 0}}</view>
                    <view class="label">渠道返利现金(元)</view>
                </view>
                <view class="center-item center-center">
                    <view class="data" v-if="chartData">{{chartData.banquetTerminalPoints || 0}}</view>
                    <view class="label">渠道返利积分(分)</view>
                </view>
                <view class="center-item">
                    <view class="data" v-if="chartData">{{chartData.banquetTerminalPhysical || 0}}</view>
                    <view class="label">渠道返利奖品(瓶)</view>
                </view>
            </view>
            <view class="column-item right">
                <view class="right-item right-top">
                    <!-- 消费者返利金额: 会员数据库里的单位是分，展示时按元展示 -->
                    <view class="data" v-if="chartData">{{chartData.banquetConsumerFee || 0}}</view>
                    <view class="label">消费者返利现金(元)</view>
                </view>
                <view class="right-item">
                    <view class="data" v-if="chartData">{{chartData.banquetConsumerPhysical || 0}}</view>
                    <view class="label">消费者返利奖品(瓶)</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import LineTitle from '../../../../lzlj/components/line-title';

export default {
    name: 'cost-proportion',
    components: {LineTitle},
    props: {
        chartData: Object
    },
    data() {
        return {}
    },
    methods: {

    }
}
</script>

<style lang="scss">
    .cost-proportion {
        background: #fff;
        border-radius: 24px;
        padding: 0 20px 20px 20px;

        .line-title {
            margin-bottom: 24px;
        }

        .board-container {
            display: flex;
            width: 100%;
            height: 324px;
            border-radius: 16px;
            overflow: hidden;

            .column-item {
                flex: 1;
                height: 100%;

                .data {
                    font-size: 28px;
                    line-height: 40px;
                    color: #212223;
                }

                .label {
                    font-size: 24px;
                    line-height: 34px;
                    color: #9EA1AE;
                }
            }

            .left {
                background: #EDF4FF;
                display: flex;
                align-items: flex-end;
                justify-content: flex-start;

                .left-value {
                    padding: 12px 16px;
                }
            }

            .center {
                margin: 0 2px;
                display: flex;
                flex-direction: column;
                flex: 1;

                .center-item {
                    background: #FDF9ED;
                    flex: 1;
                    padding: 12px 16px;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    justify-content: flex-end;
                }

                .center-center {
                    margin: 2px 0;
                }
            }

            .right {
                display: flex;
                flex-direction: column;
                flex: 1;

                .right-item {
                    background: #F6F5FE;
                    flex: 1;
                    padding: 12px 16px;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    justify-content: flex-end;
                }

                .right-top {
                    margin-bottom: 2px;
                }
            }
        }
    }
</style>
