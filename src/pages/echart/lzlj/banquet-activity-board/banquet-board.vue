<!--
宴席活动看板
<AUTHOR>
@date	2023/11/15 11:27
-->
<template>
    <view class="banquet-board">
        <!-- 选择组织弹框 -->
        <position-bottom :user-info="userInfo" :show.sync="dialogFlag" @choose="changeOrg"></position-bottom>
        <!-- 头部指标 -->
        <scroll-view scroll-x="true" class="head-info">
            <view class="select-dimension">
                <!-- 组织 -->
                <select-button :label="pickOrg.text" :selected-flag="true" @tap="showOrgDialog" :downIcon="showOrgFlag"></select-button>
                <!-- 本周/月/财年 /上一财年-->
                <picker :value="dimensionIndex" @change="dimensionChange" :range="dimensions">
                    <select-button :label="dimensions[dimensionIndex]" :selected-flag="true" downIcon></select-button>
                </picker>
            </view>
        </scroll-view>
        <!-- 宴席活动概览 -->
        <banquet-overview ref="banquetOverview" :chartData="banquetOverview"/>
        <!-- 宴席桌数占比 -->
        <banquet-tables ref="banquetTables" :chartData="banquetTables" :lovConfig="tableTypeLov"/>
        <!-- 宴席类型占比 -->
        <banquet-types ref="banquetTypes" :chartData="banquetTypes" :lovConfig="banquetTypeLov"/>
        <!-- 费用占比 -->
        <cost-proportion ref="costProportion" :chartData="costProportion" />
    </view>
</template>

<script>
import PositionBottom from '../components/position-bottom';
import SelectButton from '../components/select-button';
import BanquetOverview from './components/banquet-overview';
import BanquetTables from './components/banquet-tables';
import BanquetTypes from './components/banquet-types';
import CostProportion from './components/cost-proportion';

export default {
    name: 'banquet-board',
    components: {PositionBottom, SelectButton, BanquetOverview, BanquetTables, BanquetTypes, CostProportion},
    data() {
        const userInfo = this.$taro.getStorageSync('token').result;
        return {
            userInfo, // 用户信息
            dimensions: ['本周', '本月', '本财年', '上一财年'], // 可选时间维度
            dimensionsArr: [
                {label: '本周', value: 'week'},
                {label: '本月', value: 'month'},
                {label: '本财年', value: 'fiscalYear'},
                {label: '上一财年', value: 'lastFiscal'}
            ], // 可选时间维度对应值
            dimensionIndex: 0, // 选中的时间维度下标
            choosedimension: {}, // 选中的时间维度
            dialogFlag: false, // 是否打开选择组织弹框
            pickOrg: {
                orgId: userInfo.orgId,
                text: userInfo.orgName
            }, // 选中的组织
            showOrgFlag: false, // 选择组织按钮是否可以打开弹窗
            tableTypeLov: {}, // 桌数值列表
            banquetTypeLov: {}, // 宴席类型值列表
            banquetOverview: null, // 宴席活动概览数据
            banquetTables: null, // 宴席桌数占比数据
            banquetTypes: null, // 宴席类型占比数据
            costProportion: null, // 费用占比数据
            urlConfig: {
                banquetOverview: {
                    name: '宴席活动概览',
                    url: 'action/link/wisdomBoard/countHeadWisdomBoardByTime',
                    field: 'banquetOverview'
                },
                banquetTables: {
                    name: '宴席桌数占比',
                    url: 'action/link/wisdomBoard/countActivityTableByType',
                    field: 'banquetTables'
                },
                banquetTypes: {
                    name: '宴席类型占比',
                    url: 'action/link/wisdomBoard/countActivityBoardByType',
                    field: 'banquetTypes'
                },
                costProportion: {
                    name: '费用占比',
                    url: 'action/link/wisdomBoard/queryActivityBoardByFee',
                    field: 'costProportion'
                }
            }, // 请求地址信息
        }
    },
    async created() {
        this.choosedimension = this.dimensionsArr[this.dimensionIndex];
        const orgCfg1 = await this.$utils.getCfgProperty('MarketAct_Role_Manager');
        const orgCfg2 = await this.$utils.getCfgProperty('MarketAct_Role_GeneralManager');
        this.showOrgFlag = `${orgCfg1}${orgCfg2}`.indexOf(this.userInfo.positionType) > -1;
        this.tableTypeLov = await this.dealLov('TABLE_TYPE');
        this.banquetTypeLov =  await this.dealLov('BANQUET_TYPE');
        await this.queryAllChart();
    },
    methods: {
        /**
         * 处理值列表数据
         * <AUTHOR>
         * @date	2023/11/15 15:01
         */
        async dealLov(lovType) {
            const lovList = await this.$lov.getLovByType(lovType);
            const obj = {};
            for (const item of lovList) {
                obj[item.val] = item.name;
            }
            return obj;
        },
        /**
         * 打开选择组织弹窗
         * <AUTHOR>
         * @date	2023/11/15 14:55
         */
        showOrgDialog() {
            this.showOrgFlag && (this.dialogFlag = true);
        },
        /**
         * 查询所有图表数据
         * <AUTHOR>
         * @date	2023/11/15 14:19
         */
        async queryAllChart() {
            try {
                this.$utils.showLoading();
                await this.queryChartData('banquetOverview');
                await this.queryChartData('banquetTables');
                await this.queryChartData('banquetTypes');
                await this.queryChartData('costProportion');
            } catch (e) {
                this.$utils.hideLoading();
            } finally {
                this.$utils.hideLoading();
            }
        },
        /**
         * 查询单个图表数据
         * <AUTHOR>
         * @date	2023/11/15 14:15
         */
        async queryChartData(type) {
            console.log('type', type);
            try {
                const {success, rows} = await this.$http.post(this.urlConfig[type].url, {
                    timeRange: this.choosedimension.value,
                    source: 'WeChat',
                    attr1: this.pickOrg.orgId
                });
                if (success) {
                    console.log('rows', rows);
                    if (type === 'banquetOverview') {
                        rows.consumerCrackRatio = `${(Number(rows.consumerCrackRatio) * 100).toFixed(2)}`;
                    }
                    this.$set(this, this.urlConfig[type].field, rows);
                    if (['banquetTables', 'banquetTypes'].includes(type)) {
                        this.$refs[type].drawChart(rows);
                    }
                }
            } catch (e) {
                console.log('e', e);
            }
        },
        /**
         * 选中组织
         * <AUTHOR>
         * @date	2023/11/15 11:28
         */
        async changeOrg(item) {
            if (Object.keys(item).length === 0 || item.orgId === this.pickOrg.orgId) return;
            this.pickOrg = item;
            await this.queryAllChart();
        },
        /**
         * 切换时间维度
         * <AUTHOR>
         * @date	2023/11/15 11:36
         */
        async dimensionChange(e) {
            if (this.dimensionIndex === this.dimensions[Number(e.detail.value)]) {
                return;
            }
            this.dimensionIndex = Number(e.detail.value);
            this.choosedimension = this.dimensionsArr[this.dimensionIndex];
            await this.queryAllChart();
        },
    }
}
</script>

<style lang="scss">
.banquet-board {
    padding-bottom: 24px;

    .head-info {
        margin: 24px 0;

        .select-dimension{
            display: flex;
            margin-left: 24px;
        }
    }
}
</style>
