<template>
    <link-page class="market-act-home-report">
        <lnk-taps :taps="actDataOption" v-model="actStatusActive"></lnk-taps>
        <view v-if="actStatusActive.seq === '1'">
            <marketing-activities-ll></marketing-activities-ll>
        </view>
        <view v-if="actStatusActive.seq === '2'">
            <taste-scan-code></taste-scan-code>
        </view>
        <view v-if="actStatusActive.seq === '3'">
            <marketing-board></marketing-board>
        </view>
        <view v-if="actStatusActive.seq === '4'">
            <banquet-board></banquet-board>
        </view>
    </link-page>
</template>

<script>
import LnkTaps from "../../../core/lnk-taps/lnk-taps";
import TasteScanCode from "./components/tasteScanCode";
import Taro from "@tarojs/taro";
import MarketingActivitiesLl from "./components/marketing-activities-ll";
import MarketingBoard from '../marketing-board/marketing-board';
import BanquetBoard from '../banquet-activity-board/banquet-board';
export default {
    name: "market-act-report-page",
    components: {MarketingActivitiesLl, TasteScanCode, LnkTaps, MarketingBoard, BanquetBoard},
    data () {
        const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
        const isDaCheng = userInfo.coreOrganizationTile.brandCompanyCode === '5161';
        const actDataOption = isDaCheng
            ? [{name: '宴席活动', seq: '4', val: 'banquet'}]
            : [
                {name: '活动看板', seq: '1', val: 'costInput'},
                {name: '品鉴酒扫码', seq: '2', val: 'tasteScanCode'},
                {name: '营销6.0', seq: '3', val: 'marketing'}
            ];
        return {
            isDaCheng,
            userInfo,
            actDataOption,
            actStatusActive: {},
        }
    },
    created() {
        this.actStatusActive = this.actDataOption[0]
        //处理职位类型为固定类型不展示 品鉴酒看板
        if (this.userInfo.positionType === 'FinanceStaff' || this.userInfo.positionType === 'AuditStaff' || this.userInfo.positionType === 'InspectionStaff') {
            this.actDataOption.pop();
        }
    },
    methods: {

    }
}
</script>

<style lang="scss">
.market-act-home-report{
    width: 100%;
    overflow-x: hidden;
    background-color: #F2F2F2;
    .lnk-tabs{
        border-bottom: 1px solid #f2f2f2;
        position: relative;
    }
}
</style>
