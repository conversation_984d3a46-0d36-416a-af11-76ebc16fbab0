<template>
<link-page class="market-act-report-page">
  <lnk-taps :taps="actDataOption" v-model="actStatusActive" @switchTab="onTap"></lnk-taps>
  <view v-if="actStatusActive.seq === '1'">
    <cost-input></cost-input>
  </view>
  <view v-if="actStatusActive.seq === '2'">
    <activity-data></activity-data>
  </view>
<!--    <view v-if="actStatusActive.seq === '3'">-->
<!--        <taste-scan-code></taste-scan-code>-->
<!--    </view>-->
</link-page>
</template>

<script>
  import LnkTaps from "../../../core/lnk-taps/lnk-taps";
  import CostInput from "./components/cost-input";
  import ActivityData from "./components/activity-data";
  import TasteScanCode from "./components/tasteScanCode";
  export default {
    name: "market-act-report-page",
    components: {TasteScanCode, ActivityData, CostInput, LnkTaps},
    data () {
      const actDataOption = [
        {name: "费用投入", seq: "1", val: "costInput"},
        {name: "活动开展情况", seq: "2", val: "activities"},
          // {name: "品鉴酒扫码", seq: "3", val: "tasteScanCode"},
      ];
      return {
        actDataOption,
        actStatusActive: {},
      }
    },
    created() {
      this.actStatusActive = this.actDataOption[0]
    },
    methods: {
      /**
       *  @description: 切换tab
       *  @author: 马晓娟
       *  @date: 2020/9/22 16:45
       */
      onTap (val) {
      },
    }
  }
</script>

<style lang="scss">
  .market-act-report-page{
    background: white;
    .lnk-tabs{
      border-bottom: 1px solid #f2f2f2;
      position: relative;
    }
  }
</style>
