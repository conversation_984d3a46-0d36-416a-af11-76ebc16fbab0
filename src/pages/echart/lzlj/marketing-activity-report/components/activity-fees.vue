<!--
 @file 市场活动角色看板》活动详情》费用详情页
 <AUTHOR>
 @Created 2022/9/2
-->
<template>
    <link-page class="activity-fees">
        <!-- 选择按不同方式统计-->
        <view class="activity-fees-picker-container">
            <scroll-view scroll-x="true" class="scroll-view-data">
                <view class="select-dimension">
                    <picker :value="index" @change="pickerChange" :range="dataBoardOption">
                        <select-button :label="dataBoardOption[index]" :selected-flag="true" downIcon></select-button>
                    </picker>
                    <picker v-if="index!==3&&isCompLoadingCost" :value="typeIndex" @change="pickerTypeChange"
                            :range="dataCostTypeOption">
                        <select-button :label="typeIndex?dataCostTypeOption[typeIndex]:'所有费用小类'"
                                       :selected-flag="true"
                                       downIcon></select-button>
                    </picker>
                </view>
            </scroll-view>
        </view>
        <!--费用情况echarts双柱图-->
        <view v-if="!isDataNull">
            <link-echart :force-use-old-canvas='false' :option="activityFeesOption" :height=" 310 + 'px'"/>
        </view>
        <view class="no-data" v-else>
            <text>暂无数据</text>
        </view>
        <!--费用情况表格-->
        <view class="activity-fees-table-content" v-if="!isDataNull">
            <view class="activity-fees-table-item title-column">
                <view class="active-type-title title">
                    {{ boaderTitle[index] }}
                </view>
                <view class="scan-count title">
                    活动场次
                </view>
                <view class="scan-investAmount title">
                    投入金额{{ timeRangeIndex === 0 ? '' : '（万）' }}
                </view>
            </view>
            <view class="activity-fees-table-item" v-for="item in activityFeesData">
                <view class="active-type">
                    <view class="active-type-text">{{ item.name }}</view>
                </view>
                <view class="scan-count">{{ item.seriesDataOne }}</view>
                <view class="scan-investAmount">{{ item.seriesDataTwo }}</view>
            </view>
            <view class="tip">费用情况仅展示前十，更多内容请导出报表分析</view>
        </view>
    </link-page>
</template>

<script>


import {doubleDataHistogram} from "../marketing-echart.utils";
import SelectButton from "../../components/select-button";
import Taro from '@tarojs/taro'

export default {
    name: "activity-fees",
    components: {SelectButton},
    props: {
        OuathFlag: {
            type: Boolean,
            required: true
        },
        orgId: {
            type: String
        },
        timeRangeIndex: {
            type: Number,
            required: true
        }
    },
    watch: {
        orgId() { // watch监听props里status的变化，然后执行操作
            this.queryAllData();
        },
        timeRangeIndex() {
            this.queryAllData();
        }
    },
    data() {
        return {
            activityFeesOption: null,//费用情况echarts option
            activityFeesData: null,//费用情况表格数据
            index: 0,//按不同维度统计活动当前选中下标
            dataBoardOption: ['按区域统计', '按经销商统计', '按终端统计', '按费用类型统计'],//按不同维度统计活动
            dataBoardOptionCode: ['actFeeRegion', 'actFeeAgent', 'actFeeTerm', 'actFeeType', 'actFeeRegionAll', 'actFeeAgentAll', 'actFeeTermAll'],
            typeIndex: 0,//按不同小类统计当前选中下标
            timeRangeOption: ['week', 'month', 'fiscal','lastFiscal'],  //year 是财年
            dataCostTypeOption: ['所有费用小类'],//按不同小类
            boaderTitle: ['区域', '经销商', '终端', '费用类型'],//表格及横坐标标题
            isDataNull: false,//是否获取的数据为空
            userInfo: {},
            costTypeCode: [],//费用小类code
            saleAreaType: null,//根据不同职位计算区域类型
            isCompLoadingCost: false,//是否获取到费用小类数据
            isShowLoading: 0
        }
    },
    async created() {
        this.userInfo = Taro.getStorageSync('token').result;
        this.computeSaleAreaType();
        this.queryCostTypeData();
        this.queryAllData();
    },
    methods: {
        /**
         * @createdBy  王雅琪
         * @date  2022/09/16
         * @methods computeSaleAreaType
         * @para
         * @description 根据不同登录角色类型设置SaleAreaType
         */
        computeSaleAreaType() {
            //部门经理、部门专员
            if (this.userInfo.positionType === 'DeptManager' || this.userInfo.positionType === 'DeptSpecialist') {
                this.saleAreaType = 'saleBigArea';
            }
            //大区经理、大区内勤
            if (this.userInfo.positionType === 'SalesRegionManager' || this.userInfo.positionType === 'RInternalStaff') {
                this.saleAreaType = 'saleArea';
            }
            //战区经理、片区内勤、片区经理
            if (this.userInfo.positionType === 'SalesManager' || this.userInfo.positionType === 'InternalStaff' || this.userInfo.positionType === 'SalesAreaManager') {
                this.saleAreaType = 'saleCity';
            }
            //城市经理、城市内勤
            if (this.userInfo.positionType === 'CityManager' || this.userInfo.positionType === 'CInternalStaff') {
                this.saleAreaType = 'saleCounty';
            }
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/09/16
         * @methods queryCostTypeData
         * @para
         * @description 获取费用小类数据
         */
        async queryCostTypeData() {
            try {
                this.showLoading();
                const data = await this.$http.post('export/link/feeType/queryByExamplePage',
                    {
                        filtersRaw: [{id: 'costTypeLevel', property: 'costTypeLevel', value: '3'}]
                    });
                if (data.success) {
                    const costTypeRows = data.rows;
                    costTypeRows.forEach(v => {
                        this.costTypeCode.push(v.costTypeCode)
                        this.dataCostTypeOption.push(v.costTypeName)
                    })
                    this.isCompLoadingCost = true;
                    this.hideLoading();
                } else {
                    this.hideLoading();
                    this.$showError('查询费用小类接口失败，请稍后重试！' + data.result);
                }
            } catch (e) {
                this.hideLoading();
                this.$showError('查询费用小类接口失败！');
            }
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/09/14
         * @methods queryAllData
         * @para
         * @description 获取费用情况全部数据
         */
        async queryAllData() {
            this.activityFeesOption = null;
            try {
                this.showLoading();
                const params = {
                    rows: 10,
                    page: 1,
                    order: 'desc',
                    sort: 'actTotalNum',
                    filtersRaw: [
                        {
                            "id": 'boardType',
                            "property": 'boardType',
                            "value": (this.index !== 3 && this.typeIndex === 0) ? this.dataBoardOptionCode[this.index + 4] : this.dataBoardOptionCode[this.index]
                        },
                        {
                            "id": 'statisticType',
                            "property": 'statisticType',
                            "value": this.OuathFlag ? 'MY_ORG' : 'MY_POSTN'
                        },
                        {
                            "id": 'timeRange',
                            "property": 'timeRange',
                            "value": this.timeRangeOption[this.timeRangeIndex]
                        },
                        {
                            "id": this.OuathFlag ? 'actOrgId' : 'actPostnId',
                            "property": this.OuathFlag ? 'actOrgId' : 'actPostnId',
                            "value": this.OuathFlag ? (this.orgId || this.userInfo.orgId) : this.userInfo.postnId
                        }
                    ]
                }
                if (this.typeIndex) {
                    params.filtersRaw.push({
                        "id": 'costTypeCode',
                        "property": 'costTypeCode',
                        "value": this.costTypeCode[this.typeIndex - 1]
                    })
                }
                if (this.index === 0 && this.saleAreaType) {
                    params.filtersRaw.push({
                        "id": 'saleAreaType',
                        "property": 'saleAreaType',
                        "value": this.saleAreaType
                    })
                }
                const data = await this.$http.post('export/link/marketActBoard/queryByExamplePage', params);
                if (data.success) {
                    const activityFeesData = data.rows;
                    if (!activityFeesData || activityFeesData.length === 0) {
                        this.isDataNull = true;
                        this.hideLoading();
                        return;
                    }
                    let seriesData = [];
                    for (let i = 0, count = 1; i < activityFeesData.length; i++) {
                        let feeTypeName = activityFeesData[i].feeTypeName;
                        if (!feeTypeName) {
                            if (!activityFeesData[i].accountId) {
                                feeTypeName = '未知';
                            } else {
                                feeTypeName = '未知' + this.boaderTitle[this.index] + count++;
                            }
                        }
                        seriesData.push({
                            seriesDataOne: activityFeesData[i].actTotalNum,
                            seriesDataTwo: activityFeesData[i].investAmount,
                            name: feeTypeName
                        })
                    }
                    if (this.timeRangeIndex === 0) {
                        this.activityFeesOption = echartInitConfig => doubleDataHistogram(echartInitConfig, seriesData, '活动场次', '投入金额');
                        this.activityFeesData = seriesData;
                    } else {
                        this.activityFeesOption = echartInitConfig => doubleDataHistogram(echartInitConfig, seriesData, '活动场次', '投入金额（万）', true);
                        this.activityFeesData = [];
                        seriesData.forEach(v => {
                            this.activityFeesData.push({
                                seriesDataOne: v.seriesDataOne,
                                seriesDataTwo: this.handleAmount(v.seriesDataTwo),
                                name: v.name
                            })
                        });
                    }
                    this.isDataNull = false;
                    this.hideLoading();
                } else {
                    this.hideLoading();
                    this.$showError('查询费用情况接口失败，请稍后重试！' + data.result);
                }
            } catch (e) {
                this.hideLoading();
                this.$showError('查询费用情况接口失败！');
            }
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/09/7
         * @methods pickerChange
         * @para
         * @description 切换不同维度统计
         */
        pickerChange(e) {
            if (this.index === this.dataBoardOption[Number(e.detail.value)]) {
                return
            }
            this.index = Number(e.detail.value);
            this.queryAllData();
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/09/7
         * @methods pickerTypeChange
         * @para
         * @description 切换不同费用小类统计
         */
        pickerTypeChange(e) {
            if (this.typeIndex === this.dataCostTypeOption[Number(e.detail.value)]) {
                return
            }
            this.typeIndex = Number(e.detail.value);
            this.queryAllData();
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/10/11
         * @methods hideLoading
         * @para
         * @description 展示loading
         */
        hideLoading() {
            if (!(--this.isShowLoading)) {
                this.$utils.hideLoading();
            }
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/10/11
         * @methods showLoading
         * @para
         * @description 展示loading
         */
        showLoading() {
            if (!this.isShowLoading) this.$utils.showLoading();
            this.isShowLoading++;
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/11/1
         * @methods handleAmount
         * @para
         * @description 将金额转化为以万为单位并格式化金额 100000000=》1,0000
         */
        handleAmount(num) {
            if (this.dateIndex === 0) return num;
            let numWanStr = Math.round(num / 10000).toString();
            return numWanStr.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        }
    }
}
</script>

<style lang="scss">

.activity-fees {
    .activity-fees-picker-container {
        margin-top: 32px;
        margin-bottom: 10px;
    }

    .no-data {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 24px;
        height: 310px;
        color: #D8D8D8;
    }

    .activity-fees-table-content {
        //width: 100%;
        overflow: scroll;
        margin: 48px 20px 40px 20px;
        max-height: 800px;

        .title-column {
            position: sticky;
            top: 0;
            z-index: 99;
        }

        .activity-fees-table-item {
            display: flex;
            font-size: 24px;
            color: #666;
            width: 100%;
            justify-content: center;
            align-items: center;
            height: 80px;
            flex-wrap: nowrap;

            .title {
                background-color: #C8D7FA;
                color: #333;
                font-weight: 500;
            }

            .active-type {
                flex: 9;
                border: 2px solid #E6EAF4;
                height: 76px;
                display: flex;
                align-items: center;

                .active-type-text {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    text-align: center;
                    width: 100%;
                }

            }

            .active-type-title, .scan-count, .scan-investAmount {

                display: flex;
                align-items: center;
                justify-content: center;
                border: 2px solid #E6EAF4;
                height: 76px;
            }

            .active-type-title {
                flex: 9;
            }

            .scan-count {
                flex: 4;
                border-left: none;
            }

            .scan-investAmount {
                flex: 5;
                border-left: none;
            }
        }

        .tip {
            width: 100%;
            text-align: center;
            font-size: 24px;
            color: #999;
            line-height: 34px;
            margin: 10px 0;
        }
    }

    .scroll-view-data {
        .select-dimension {
            display: flex;
            margin-left: 24px;
        }
    }
}
</style>
