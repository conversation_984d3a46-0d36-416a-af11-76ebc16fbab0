<template>
    <link-page class="execute-panel">
        <view style="display: flex">
        <line-title title="执行案概览" style="flex: 1"></line-title>
        <view class="tips">
            <link-icon icon="mp-info-lite" status="info"/>
            <view class="tips-content">数据仅展示本财年</view>
        </view>
        </view>

        <view v-if="showFlag">
        <view>
        <canvas id="myCanvas" :force-use-old-canvas="false" type="2d"
                :style="{width: '100%', height:(dataOptionHeight / 2 + 20)+'px'}"
                disable-scroll='true'></canvas>
        </view>
        <!--  下部文字描述      -->
        <view class="dataBox">
            <view class="row">
                <view style="flex: 1;text-align: center;">
                    <view class="num">
                        ¥{{exePanel.approvalAmount}}
                    </view>
                    <view class="text">
                        累计审批金额（万）
                    </view>
                </view>
                <view class="line"></view>
                <view style="flex: 1;text-align: center;">
                    <view class="num">
                        ¥{{exePanel.actualAmount}}
                    </view>
                    <view class="text">
                        累计实发金额（万）
                    </view>
                </view>
            </view>
            <view class="row MP">
                <view style="flex: 1;text-align: center;">
                    <view class="num">
                        ¥{{exePanel.occupiedAmount}}
                    </view>
                    <view class="text">
                        累计占用金额（万）
                    </view>
                </view>
                <view class="line"></view>
                <view style="flex: 1;text-align: center;">
                    <view class="num">
                        ¥{{exePanel.availableAmount}}
                    </view>
                    <view class="text">
                        累计可用金额（万）
                    </view>
                </view>
            </view>
        </view>
        </view>
        <view class="no-data" v-else>
            <text>暂无数据</text>
        </view>
    </link-page>
</template>

<script>
import LineTitle from "../../../../lzlj/components/line-title";
export default {
    name: "execute-panel",
    components: {LineTitle},
    props:{
        dateIndex:{
            type: Number,
            default: 0
        },
        orgId:{
            type: String,
            default: ''
        },
        ouathFlag:{
            type: Boolean,
            default: false
        },
        userObj:{
            type: Object,
            default: {}
        }
    },
    data(){

        return{
            exePanel:{},
            dateList:['week','month','fiscal','lastFiscal'],
            dataOptionHeight: (this.$device.systemInfo.windowWidth - 24) * 0.727 < 254 ? (this.$device.systemInfo.windowWidth - 24) * 0.727 : 254,
            procData: {visitExRate: 0},//visitExRate费用使用进度
            showFlag:true
        }
    },
    watch:{
        dateIndex(){
            this.queryData();
        },
        orgId(){
            this.queryData();
        },
    },
    created() {
        this.queryData();
    },
    methods:{
        async queryData() {
            let param = {};
            if(this.ouathFlag){
                param={
                    filtersRaw: [
                        {id: "timeRange", property: "timeRange", value: 'fiscal', operator: "="},
                        {id: "statisticType", property: "statisticType", value: 'MY_ORG', operator: "="},
                        {id: "actOrgId", property: "actOrgId", value: this.orgId, operator: "="},
                        {id: "boardType", property: "boardType", value: 'exeOverview', operator: "="},
                    ]
                }
            }
            try{
                const data = await this.$http.post('action/link/marketActBoard/queryByExamplePage', param)
                if(this.$utils.isNotEmpty(data.rows)){
                    this.exePanel = {
                        approvalAmount:data.rows[0].approvalAmount?this.handleAmount(data.rows[0].approvalAmount):0,
                        actualAmount:data.rows[0].actualAmount?this.handleAmount(data.rows[0].actualAmount):0,
                        occupiedAmount:data.rows[0].occupiedAmount?this.handleAmount(data.rows[0].occupiedAmount):0,
                        availableAmount:data.rows[0].availableAmount?this.handleAmount(data.rows[0].availableAmount):0,
                        status:data.rows[0].status?data.rows[0].status:0,
                    }
                    if(this.isZero(data.rows[0].approvalAmount)&&this.isZero(data.rows[0].actualAmount)&&this.isZero(data.rows[0].occupiedAmount)
                        &&this.isZero(data.rows[0].availableAmount)&&this.isZero(data.rows[0].status)){
                        this.showFlag=false;
                    }else{
                        this.showFlag=true;
                    }
                }else{
                    this.exePanel = {
                        approvalAmount:0,
                        actualAmount:0,
                        occupiedAmount:0,
                        availableAmount:0,
                        status:0,
                    }
                    this.showFlag=false;
                }
                this.procData.visitExRate = Number(this.exePanel.status)
                setTimeout(()=>{
                    if(this.showFlag){
                    this.progress();
                    }
                },20)
            }catch(e){
                this.$showError(String(e))
            }
        },
        /**
         * @description 判断是否为0;
         */
        isZero(data){
            if(data===0||data==='0'){
                return true;
            }else{
                return false;
            }
        },
        /**
         * @description 弧形进度条
         */
        async progress() {
            wx.createSelectorQuery()
                .select('#myCanvas') // 在 WXML 中填入的 id
                .fields({node: true, size: true})
                .exec((res) => {

                    // Canvas 对象
                    const canvas = res[0].node
                    // Canvas 画布的实际绘制宽高
                    const renderWidth = res[0].width
                    const renderHeight = res[0].height
                    // Canvas 绘制上下文
                    const ctx = canvas.getContext('2d')
                    // 环形进度条变量
                    let outerRadius = 75 // 外环半径
                    let thickness = 15 // 圆环厚度
                    let x = renderWidth / 2  // 圆心x坐标
                    let y = this.dataOptionHeight / 2 - 20 // 圆心y坐标
                    let startAngle = -180 //开始角度
                    let endAngle = 0 //结束角度
                    let scro = parseInt(this.procData.visitExRate) * 1.8 - 180;
                    const rate = parseInt(this.procData.visitExRate) / 100
                    // 初始化画布大小
                    const dpr = wx.getSystemInfoSync().pixelRatio
                    canvas.width = renderWidth * dpr
                    canvas.height = renderHeight * dpr
                    ctx.scale(dpr, dpr)
                    //清除画布
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    // 画圆环
                    ctx.beginPath()
                    ctx.arc(x, y, outerRadius, this.angle2Radian(startAngle), this.angle2Radian(endAngle));
                    ctx.strokeStyle = '#eef4ff' // 弧线的颜色
                    ctx.lineWidth = thickness - 3// 弧的宽度
                    ctx.lineCap = "round" //线条结束端点样式 butt 平直 round 圆形 square 正方形
                    ctx.stroke()
                    if (parseInt(this.procData.visitExRate) !== 0) {
                        // 画进度条
                        ctx.beginPath()
                        ctx.arc(x, y, outerRadius, this.angle2Radian(startAngle), this.angle2Radian(scro))
                        //渐变色
                        let lingrad = ctx.createLinearGradient(x - outerRadius, y, (x+outerRadius)* rate , y - outerRadius);
                        // addColorStop 创建一个颜色的渐变点
                        lingrad.addColorStop(0, '#2F69F8');
                        lingrad.addColorStop(0.6, '#46C1F9');
                        lingrad.addColorStop(1, '#46E3F1');
                        ctx.strokeStyle = lingrad;
                        ctx.lineWidth = thickness
                        ctx.lineCap = "round";
                        ctx.stroke()
                    }
                    ctx.fontSize = 22
                    ctx.fillStyle = '#2F69F8' // 文字的颜色
                    ctx.font = 'normal 500 22px sans-serif';
                    ctx.textAlign = "center";  // 字体位置
                    ctx.fillText(this.procData.visitExRate + '%', x, y - 19)
                    ctx.fontSize = 14
                    ctx.font = 'normal 500 14px sans-serif';
                    ctx.fillStyle = '#333333'
                    ctx.fillText('费用使用进度', x, y + 9.5)
                    ctx.textAlign = "center";  // 字体位置
                })
        },
        //角度转弧度函数
        angle2Radian(angle) {
            return angle * Math.PI / 180
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/11/1
         * @methods handleAmount
         * @para
         * @description 将金额转化为以万为单位并格式化金额 100000000=》1,0000
         */
        handleAmount(num){
            let numWanStr= Math.round(num/10000).toString();
            return numWanStr.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        }
    }
}
</script>

<style lang="scss">
.execute-panel{
    .no-data {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 24px;
        height: 310px;
        color: #D8D8D8;
    }
    .tips{
        display: flex;
        align-items: center;
        margin: 24px 30px 0px 0px;
        font-size: 24px;
        color: #666;
    }
    .dataBox{
        display: flex;
        flex-direction: column;
        margin-bottom: 20px;
        .row{
            flex: 1;
            display: flex;
            align-items: center;
        }
        .num{
            font-size: 36px;
            font-weight: 600;
        }
        .text{
            margin-top: 12px;
            font-size: 26px;
            font-weight: 400;
            color: #666666;
        }
        .line{
            background: #DDDDDD;
            width: 2px;
            height: 40px;
        }
        .MP{
            margin-top: 40px;
        }
    }
}
</style>
