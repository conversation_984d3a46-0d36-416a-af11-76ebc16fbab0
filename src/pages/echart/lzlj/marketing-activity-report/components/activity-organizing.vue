<!--
 @file 活动看板-活动详情-举办情况
 <AUTHOR>
 @Created 2022/8/18
-->

<template>
    <!--活动详情-举办情况-->
    <link-page class="activity-organizing">
        <!--活动场次模块-->
        <view class="activity-session">
            <!--活动场次文字-->
            <view class="activity-session-title">
                <link-icon icon="icon-weixinkuaizhao" class="text-icon"/>
                <view class="text">活动场次</view>
            </view>
            <!--echarts图表-->
            <view class="session-charts" v-if="!isActDataNull">
                <link-echart
                    :option="activitySessionOption"
                    :height=" 230 + 'px'"
                    :force-use-old-canvas="false"
                />
            </view>
            <view class="no-data" v-else>
                <text>暂无数据</text>
            </view>

        </view>

        <!--申请金额TOP10(万)-->
        <view class="apply-cost-top">
            <!--申请金额TOP10(万)-->
            <view class="apply-cost-title">
                <link-icon icon="icon-weixinkuaizhao" class="text-icon"/>
                <view class="text">申请金额TOP10{{(timeRange === 'month' || timeRange === 'fiscal')?'(万)':''}}</view>
            </view>
            <!--echarts图表-->
            <view class="apply-cost-charts" v-if="!isCostDataNull">
                <link-echart
                    :option="applyCostTopOption"
                    :height=" 230 + 'px'"
                    :force-use-old-canvas="false"
                />
            </view>
            <view class="no-data" v-else>
                <text>暂无数据</text>
            </view>
        </view>

        <!-- 举办场次排名 -->
        <view class="hold-rank" v-if="holdRankFlag">
            <!--举办场次排名文字-->
            <view class="hold-rank-title">
                <link-icon icon="icon-weixinkuaizhao" class="text-icon"/>
                <view class="text">举办场次排名</view>
            </view>
            <!--echarts图表-->
            <view class="hold-rank-charts" v-if="!isHoldDataNull">
                <link-echart
                    :option="holdRankOption"
                    :height=" 230 + 'px'"
                    :force-use-old-canvas="false"
                />
            </view>
            <view class="no-data" v-else>
                <text>暂无数据</text>
            </view>

        </view>

        <!--活动趋势(场次)-->
        <view class="activity-trend">
            <!--活动趋势(场次)-->
            <view class="activity-trend-title">
                <link-icon icon="icon-weixinkuaizhao" class="text-icon"/>
                <view class="text">活动趋势(场次)</view>
            </view>
            <!--下拉框-->
            <view>
                <scroll-view scroll-x="true" class="scroll-view-data">
                    <view class="select-dimension">
                        <!--费用小类-新版picker-->
                        <picker v-if="isCompLoadingCost" :value="typeIndex" @change="pickerTypeChange"
                                :range="dataCostTypeOption">
                            <select-button :label="typeIndex?dataCostTypeOption[typeIndex]:'所有费用小类'"
                                           :selected-flag="true" downIcon></select-button>
                        </picker>
                    </view>
                </scroll-view>
            </view>
            <!--echarts图表-->
            <view class="trend-charts" v-if="!isTrendDataNull">
                <link-echart
                    :option="activityTrendOption"
                    :height=" 230 + 'px'"
                    :force-use-old-canvas="false"
                />
            </view>
            <view class="no-data" v-else>
                <text>暂无数据</text>
            </view>
        </view>
    </link-page>
</template>

<script>
import {activitySession, applyCost, holdRank, activityTrend} from "../marketing-echart.utils";
import SelectButton from "../../components/select-button";
import Taro from "@tarojs/taro";

export default {
    name: "activity-organizing",
    components: {SelectButton},
    props: ['orgParam', 'timeRange', 'orgId', 'holdRankFlag'],
    data() {
        return {
            activitySessionRows: [],     //活动场次-请求的数据
            activitySessionOption: null, //活动场次-echarts柱状图option
            applyCostTopOption: null,    //申请金额TOP10-echarts柱状图option
            activityTrendOption: null,   //活动趋势（场次）-echarts柱状图option
            holdRankOption: null,    //举办场次排名-echarts柱状图option
            index: 0,
            costTypeCode: [],    //不同小类Code
            tempOrgId: '',

            isShowLoading: 0,   //记录请求次数

            userInfo: {},   //所有的用户信息
            positionTypeA: '',    //判断是否为A职位类型
            positionType: true, //判断是否为AB两种职位类型,传值用（AB传职位id——actPostnId，C传组织id——actOrgId）

            //新费用小类-picker写法
            typeIndex: 0,//按不同小类统计当前选中下标
            dataCostTypeOption: ['所有费用小类'],//按不同小类
            isCompLoadingCost: false, //是否已获取到小类数据

            trendParam: {
                boardType: "costAll",
                order: "asc",
                costTypeCode: ''
            },
            //sort用
            timeTrendParam: {
                week: 'actDayWeek',
                month: 'actWeekMonth',
                fiscal: 'actMonthYear'
            },

            //actDay用
            actDayParam: {
                week: 'actDay',
                month: 'actWeekMonth',
                fiscal: 'actMonthYear'
            },

            isActDataNull: true,      //活动场次数据是否为空
            isCostDataNull: true,     //申请金额数据是否为空
            isHoldDataNull: true,     //举办场次数据是否为空
            isTrendDataNull: true     //活动趋势数据是否为空


        }
    },
    created() {
        this.userInfo = Taro.getStorageSync('token').result;
        // AB类职位
        this.positionType = ['Salesman', 'CustServiceSpecialist', 'SalesTeamLeader',
            'GroupBuyManager', 'AccountManager', 'RegionalManager',
            'CustServiceManager', 'VipManager', 'ChannelSupervisor',
            'CustServiceSupervisor', 'BattleCommander', 'SalesSupervisor',
            'BLRegionManager', 'BPRegionManager', 'SalesManager'].includes(this.userInfo.positionType)
         this.queryCostTypeData();
         this.allRequest();
    },
    watch: {
        orgId() { // watch监听props里orgID的变化，然后执行操作
            this.allRequest();
        },
        timeRange() {
            this.allRequest();
        },
    },
    // mounted() {},
    // computed() {},
    methods: {
        /**
         * @desc
         * <AUTHOR>
         * @date 2022/09/13 10:30
         * @desc 四个echarts分别发起请求
         **/
        async allRequest() {
                this.isActDataNull = true,
                this.isCostDataNull = true,
                this.isHoldDataNull = true,
                this.isTrendDataNull = true,
                this.showLoading();

            // 活动场次
            let sessionRows = await this.paramsRequest({
                boardType: "feeType",
                sort: "actTotalNum",
                order: "desc"
            });
            if (sessionRows.length > 0) {
                this.isActDataNull = false;
            }
            let seriesDataSession = await this.variousRow(sessionRows, 'costTypeName', 'actTotalNum');
            this.activitySessionOption = null;
            this.activitySessionOption = echartInitConfig => activitySession(echartInitConfig, seriesDataSession);

            // 申请金额TOP10
            let costRows = await this.paramsRequest({
                boardType: "feeType",
                sort: "applyAmount",
                order: "desc"
            });
            if (costRows.length > 0) {
                this.isCostDataNull = false;
            }
            let seriesDataCost = await this.variousRow(costRows, 'costTypeName', 'applyAmount');
            if(this.timeRange === 'month' || this.timeRange === 'fiscal'){
                seriesDataCost.forEach(v=>{
                    v.value=Math.round(v.value/10000)
                })
            }
            this.applyCostTopOption = null;
            this.applyCostTopOption = echartInitConfig => applyCost(echartInitConfig, seriesDataCost);

            // 举办场次排名
            let rankRows = await this.paramsRequest({
                boardType: "execute",
                sort: "actTotalNum",
                order: "desc"
            });
            if (rankRows.length > 0) {
                this.isHoldDataNull = false;
            }
            let seriesDataRank = await this.variousRow(rankRows, 'executor', 'actTotalNum');
            this.holdRankOption = null;
            this.holdRankOption = echartInitConfig => holdRank(echartInitConfig, seriesDataRank);

            // 活动趋势（场次）- 本财年
            let trendRows = await this.paramsRequest({
                ...this.trendParam,
                sort: this.timeTrendParam[this.orgParam.timeRange]
            });
            if (trendRows.length > 0) {
                this.isTrendDataNull = false;
            }
            let seriesDataTrend = await this.variousRow(trendRows, this.actDayParam[this.orgParam.timeRange], 'actTotalNum')
            this.activityTrendOption = null;
            this.activityTrendOption = echartInitConfig => activityTrend(echartInitConfig, seriesDataTrend, this.actDayParam[this.orgParam.timeRange]);
            this.hideLoading()
        },

        /**
         * @desc
         * <AUTHOR>
         * @date 2022/09/13 10:30
         * @desc 处理要在echarts中展示的数据
         * @params rows-每一项数据
         * @params name-x轴字段名
         * @params value-y轴数值
         * @params lovType-是否为值列表
         **/
        async variousRow(rows, name, value) {
            let newRows = [];
            newRows = rows.map((item) => ({
                name: item[name],
                value: item[value]
            }));
            return newRows;
        },

        /**
         * @desc
         * <AUTHOR>
         * @date 2022/09/01 14:30
         * @desc 参数请求
         **/
        async paramsRequest(obj) {
            let postnId = this.userInfo.postnId;
            let param = {
                ...this.orgParam,
                ...obj,
            };
            this.positionType ? param.actPostnId = postnId : param.actOrgId = (this.orgId||this.userInfo.orgId);

            let rows = await this.queryAllData(param);
            return rows;
        },


        /**
         * @desc
         * <AUTHOR>
         * @date 2022/09/01 14:30
         * @desc 活动场次echarts柱状图,获取所有数据
         **/
        async queryAllData(param) {
            try {
                const data = await this.$http.post('export/link/marketActBoard/queryByExamplePage', param);
                if (data.success) {
                    return Promise.resolve(data.rows);
                } else {
                    this.$showError('查询接口失败，请稍后重试！' + data.result);
                }
            } catch (e) {
                this.$showError('查询接口失败！');
            }
        },

        /**
         * @createdBy  何春霞
         * @date  2022/09/21
         * @methods queryCostTypeData
         * @para
         * @description 获取费用小类数据
         */
        async queryCostTypeData() {
            try {
                this.showLoading();
                const data = await this.$http.post('export/link/feeType/queryByExamplePage',
                    {
                        filtersRaw: [{id: 'costTypeLevel', property: 'costTypeLevel', value: '3'}]
                    });
                if (data.success) {
                    const costTypeRows = data.rows;
                    costTypeRows.forEach(v => {
                        this.costTypeCode.push(v.costTypeCode)
                        this.dataCostTypeOption.push(v.costTypeName)
                    })
                    this.isCompLoadingCost = true;
                    this.hideLoading();
                } else {
                    this.hideLoading();
                    this.$showError('查询费用小类接口失败，请稍后重试！' + data.result);
                }
            } catch (e) {
                this.$showError('查询费用小类接口失败！');
            }

        },

        /**
         * @createdBy  何春霞
         * @date  2022/09/21
         * @methods pickerTypeChange
         * @para
         * @description 切换不同费用小类统计
         */
        async pickerTypeChange(e) {
            this.showLoading();
            if (this.typeIndex === this.dataCostTypeOption[Number(e.detail.value)]) {
                return
            }
            this.typeIndex = Number(e.detail.value);
            this.isTrendDataNull = true;
            this.trendParam.boardType = 'costType';
            this.trendParam.costTypeCode = '';
            if (this.typeIndex) {
                this.trendParam.costTypeCode = this.costTypeCode[this.typeIndex - 1];
            }
            this.trendParam.sort = this.timeTrendParam[this.orgParam.timeRange];
            if (this.trendParam.costTypeCode === '') {
                this.trendParam.boardType = 'costAll';
            }
            let trendRows = await this.paramsRequest(this.trendParam);
            if (trendRows.length > 0) {
                this.isTrendDataNull = false;
            }
            let seriesDataTrend = await this.variousRow(trendRows, this.actDayParam[this.orgParam.timeRange], 'actTotalNum')
            this.activityTrendOption = null;
            this.activityTrendOption = echartInitConfig => activityTrend(echartInitConfig, seriesDataTrend, this.actDayParam[this.orgParam.timeRange]);
            this.hideLoading();
            },


        /**
         * @createdBy  何春霞
         * @date  2022/10/11
         * @methods hideLoading
         * @para
         * @description 隐藏loading
         */
        hideLoading() {
            if(this.isShowLoading === 0) {
                return
            }
            if (!(--this.isShowLoading)) {
                this.$utils.hideLoading();
            }
        },
        /**
         * @createdBy  何春霞
         * @date  2022/10/11
         * @methods showLoading
         * @para
         * @description 展示loading
         */
        showLoading() {
            if (!this.isShowLoading) this.$utils.showLoading();
            this.isShowLoading++;
        }

    }
}
</script>

<style lang="scss">
.activity-organizing {
    //height: 1200*2px;
    padding: 20px;
    width: 100%;

    //活动场次
    .activity-session {
        //display: flex;
        height: 573px;

        .activity-session-title {
            display: flex;
            height: 28px;
            width: 100%;
            margin: 32px 0;

            .text-icon {
                margin: 10px 16px 10px 6px;
                font-size: 10px;
                align-content: center;
                color: #2F69F8;
            }

            .text {
                display: flex;
                font-family: PingFangSC-Medium;
                font-size: 28px;
                color: #333333;
                height: 28px;
                line-height: 28px;
                font-weight: 500;
                width: 100%;
            }
        }

        .session-charts {
            height: 456px;
            width: 90%;
            margin: 36px 12px 8px 21px;
        }
    }

    //申请金额TOP10
    .apply-cost-top {
        //display: flex;
        height: 573px;

        .apply-cost-title {
            display: flex;
            height: 28px;
            width: 100%;
            //margin: 32px 0;
            .text-icon {
                margin: 26px 16px 10px 6px;
                font-size: 10px;
                align-content: center;
                color: #2F69F8;
            }

            .text {
                display: flex;
                margin-top: 16px;
                font-family: PingFangSC-Medium;
                font-size: 28px;
                color: #333333;
                height: 28px;
                line-height: 28px;
                font-weight: 500;
                width: 100%;
            }
        }

        .apply-cost-charts {
            height: 456px;
            width: 90%;
            margin: 36px 12px 8px 21px;
        }
    }

    //举办场次
    .hold-rank {
        //display: flex;
        height: 573px;

        .hold-rank-title {
            display: flex;
            height: 28px;
            width: 100%;
            //margin: 32px 0;
            .text-icon {
                margin: 26px 16px 10px 6px;
                font-size: 10px;
                align-content: center;
                color: #2F69F8;
            }

            .text {
                display: flex;
                margin-top: 16px;
                font-family: PingFangSC-Medium;
                font-size: 28px;
                color: #333333;
                height: 28px;
                line-height: 28px;
                font-weight: 500;
                width: 100%;
            }
        }

        .hold-rank-charts {
            height: 456px;
            width: 90%;
            margin: 36px 12px 8px 21px;
        }
    }

    //活动趋势（场次）
    .activity-trend {
        //display: flex;
        //height: 573px;

        .activity-trend-title {
            display: flex;
            height: 20px;
            width: 100%;
            margin: 32px 0;

            .text-icon {
                margin: 10px 16px 10px 6px;
                font-size: 10px;
                align-content: center;
                color: #2F69F8;
            }

            .text {
                display: flex;
                font-family: PingFangSC-Medium;
                font-size: 28px;
                color: #333333;
                height: 28px;
                line-height: 28px;
                font-weight: 500;
                width: 100%;
            }
        }

        .scroll-view-data {
            //height: 54px;
            margin: 16px 0px;

            .select-dimension {
                //display: flex;
            }
        }

        .trend-charts {
            height: 456px;
            width: 90%;
            margin: 36px 12px 8px 21px;

        }
    }

    .positionOrgBox {
        .link-dialog-foot-custom {
            width: auto !important;
        }
    }

    .no-data {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 24px;
        height: 480px;
        color: #D8D8D8;
    }

}

</style>
