<template>
    <link-page class="tasteScanCode">
        <view style="padding-top: 12px"></view>
        <!--顶部信息-->
        <view class="head-info">
            <view>
                <scroll-view scroll-x="true" class="scroll-view-data"  v-if="OuathFlag" >
                    <view class="select-dimension">
                        <select-button :label="orgText" :selected-flag="true" @tap="dialogFlag=true;" downIcon></select-button>
                    </view>
                </scroll-view>
            </view>
            <view class="tips" >
                <link-icon icon="mp-info-lite" status="info"/>
                <view class="tips-content">数据仅展示本财年</view>
            </view>
        </view>

        <!--品酒扫码概览-->
        <taste-scan-overview :orgId="orgId"></taste-scan-overview>
        <!--各活动类型扫码情况-->
        <activity-type-scan :orgId="orgId"></activity-type-scan>
        <!--各区域扫码情况-->
        <area-activity-scan v-if="!isSpecifyPositionType && orgType!=='SalesCity' && orgType!=='SalesArea'" :orgId="orgId" :orgType="orgType"></area-activity-scan>
        <!--领用核销率-->
        <receipt-write-off-rate :orgId="orgId" :orgType="orgType"></receipt-write-off-rate>

        <position-bottom :user-info="userInfo" :show.sync="dialogFlag" @choose="changeOrg"></position-bottom>

    </link-page>
</template>

<script>
import activityTypeScan from "./activity-type-scan";
import areaActivityScan from "./area-activity-scan";
import TasteScanOverview from "./taste-scan-overview";
import ReceiptWriteOffRate from "./receipt-write-off-rate";
import Taro from "@tarojs/taro";
import SelectButton from "../../components/select-button";
import PositionBottom from "../../components/position-bottom";
import {targetPieChartProgress} from "../../echart.utils";

export default {
    name: "tasteScanCode",
    components: {PositionBottom, SelectButton, TasteScanOverview, activityTypeScan,areaActivityScan,ReceiptWriteOffRate},
    data(){
        const userInfo = Taro.getStorageSync('token').result;
        let OuathFlag = false;
        let positionType = userInfo.positionType
        OuathFlag = this.Oauth(positionType);
        const isSpecifyPositionType=positionType==='Salesman'||positionType==='SalesSupervisor'||positionType==='GroupBuyManager'||positionType==='AccountManager'
            ||positionType==='CustServiceManager'||positionType==='VipManager'||positionType==='CustServiceSpecialist'||positionType==='CustServiceSupervisor'
            ||positionType==='SalesTeamLeader'||positionType==='SalesChannelManger'||positionType==='SalesGeneralManager'||positionType==='GeneralManager'||positionType==='BrandManager'
        return{
            positionType,//当前登录人职位
            OuathFlag,
            userInfo,
            orgText:'选择组织',
            dialogFlag: false,
            tempOrgId: null,
            autoList: new this.AutoList(this, {
                module: 'action/link/orgnization',
                url: {
                    queryByExamplePage: 'export/link/orgnization/queryByExamplePage'
                },

                searchFields: ['text'],
                loadOnStart: false,
                param: {
                    oauth: 'MY_ORG',
                    filtersRaw: userInfo.orgType==='Company' ? [
                        {'id': 'id', 'property': 'id', 'value': userInfo.orgId, 'operator': '='},
                        {"id": "isEffective", "property": "isEffective", "value": "Y"}
                    ] : [
                        {'id': 'parentOrgId', 'property': 'parentOrgId', 'value': userInfo.orgId, 'operator': '='},
                        {"id": "isEffective", "property": "isEffective", "value": "Y"}
                    ]
                },
                hooks:{
                    beforeLoad(option) {
                        let flag =  option.param.filtersRaw.some(function(item){return item.property==='[text]'})
                        if (this.gotoOrgFlag){
                            if(flag){
                                option.param.filtersRaw.splice(0,1);
                            }
                            else {
                                option.param.filtersRaw.forEach((item,index)=>{
                                    if (item.property==='parentOrgId'){
                                        option.param.filtersRaw.splice(index,1);
                                    }
                                })
                                option.param.filtersRaw = [{'id': 'parentOrgId', 'property': 'parentOrgId', 'value': userInfo.orgId, 'operator': '='}].concat(option.param.filtersRaw)
                            }
                        }
                    }
                },
                sortOptions: null,
            }),
            isSpecifyPositionType,//登录人职位是否为业代和业务主管
            orgId:null,
            orgType:null
        }
    },
    created() {
        this.autoList.methods.reload();
        this.orgId = this.userInfo.orgId;
        this.orgText = this.userInfo.orgName;
    },
    methods:{

        async changeOrg(item) {
            if(Object.keys(item).length === 0)return;
            //2021-07-29 配置了访问组或者this.orgChooseFlag && type === 'input'的情况
            this.orgText = item.text;
            this.orgId = item.id;
            this.orgType = item.orgType;
        },
        /**
         * @createdBy  吕志平
         * @date  2022年8月29日
         * @description 确定选中组织
         */
        async clickOrganization(){
            this.orgId = this.tempOrgId;
            this.orgText = this.tempOrgText;
            this.orgType=this.tempType;
            this.dialogFlag = false;
            console.log('orgId:',this.orgId,'orgType',this.orgType)
        },
        /**
         * @createdBy  吕志平
         * @date  2022年8月29日
         * @description 记录选中项数据
         */
        tempOrgInfo(item){
            this.tempOrgId=item.id;
            this.tempOrgText=item.text;
            this.tempType=item.orgType
        },
        /**
         * @createdBy  吕志平
         * @date  2022年8月29日
         * @description 获取用户职位类型判断是否展示组织选择按钮
         */
        Oauth(positionType) {
            let arr = [
                'AuditStaff',              // 稽核人员
                'FinanceStaff',            // 财务人员
                'InspectionStaff',         // 纪检人员
                'Salesman',                // 业务代表
                'SalesSupervisor',         // 业务主管
                'GroupBuyManager',         // 团购经理
                'AccountManager',          // 客户经理
                'CustServiceManager',      // 客服经理
                'VipManager',              // VIP经理
                'CustServiceSpecialist',   // 客服专员
                'CustServiceSupervisor',   // 客服主管
                'SalesTeamLeader',         // 小组组长
                'SalesChannelManger'       // 渠道经理
            ];
            let flag = !arr.includes(positionType);
            return flag;
        },
        /**
         * @createdBy  吕志平
         * @date  2022年8月29日
         * @description 返回上级组织树
         */
        async goBackOrg(){
            this.autoList.option.searchText = "";
            let filtersRaw=[];
            if(this.autoList.option.param.filtersRaw.some((item)=>{ return item.property==='id'&& item.value===Taro.getStorageSync('token').result.orgId})){
                filtersRaw = [
                    {id: 'id', property: 'id', value: Taro.getStorageSync('token').result.orgId, operator: '='},
                    {id: "isEffective", property: "isEffective", value: "Y"},
                    {id: "isLaunch", property: "isLaunch", value: "Y"}
                ]
            }else if(this.autoList.option.param.filtersRaw.some((item)=>{ return item.property==='parentOrgId'&& item.value===Taro.getStorageSync('token').result.orgId})){
                filtersRaw = [
                    {id: 'id', property: 'id', value: Taro.getStorageSync('token').result.orgId, operator: '='},
                    {id: "isEffective", property: "isEffective", value: "Y"},
                    {id: "isLaunch", property: "isLaunch", value: "Y"}
                ]
            }else{
                filtersRaw = [
                    {id: 'parentOrgId', property: 'parentOrgId', value: Taro.getStorageSync('token').result.orgId, operator: '='},
                    {id: "isEffective", property: "isEffective", value: "Y"},
                    {id: "isLaunch", property: "isLaunch", value: "Y"}
                ]
            }
            this.autoList.option.param.filtersRaw = filtersRaw;
            this.gotoOrgFlag = false;
            await this.autoList.methods.reload();
            this.gotoOrgFlag = true;
        },
        /**
         * @createdBy  吕志平
         * @date  2022年8月29日
         * @description 进行下钻组织树
         */
        async gotoItemOrg(data){
            let filtersRaw = [
                {id: 'parentOrgId', property: 'parentOrgId', value: data.id, operator: '='},
                {"id": "isEffective", "property": "isEffective", "value": "Y"},
                {"id": "isLaunch", "property": "isLaunch", "value": "Y"}
            ]
            this.autoList.option.param.filtersRaw = filtersRaw;
            this.gotoOrgFlag = false;
            await this.autoList.methods.reload();
            this.gotoOrgFlag = true;
        },
    }

}
</script>

<style lang="scss">
.tasteScanCode {
    background-color: #F2F2F2;
    min-height: 100vh;

    .boardBox {
        background: #ffffff;
        margin: 0 0px 24px;
        border-radius: 12px;
    }
    .head-info {
        display: flex;
        position: relative;
        height: 52px;
        .tips{
            position: absolute;
            display: flex;
            align-items: center;
            margin: 18px 24px 30px 16px;
            right: 0px;
            font-family: "PingFang SC";
            font-size: 24px;
            color: #666;
            line-height: 24px;
            font-weight: 400;
            .tips-content{
                margin-left: 12px;
            }
        }
    }
    .positionOrgBox{
        .link-input {
            display: block !important;
        }
        .link-item-content{
            justify-content: start !important;
        }
        .link-dialog-foot-custom{
            width: auto !important;
        }
    }
    .scroll-view-data{
        .select-dimension{
            display: flex;
            margin-left: 24px;
        }
    }

}
</style>
