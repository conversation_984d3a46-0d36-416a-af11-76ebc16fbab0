<!--
 @file 活动看板各活动类型扫码情况
 <AUTHOR>
 @Created 2022/8/14
-->
<template>
    <link-page class="activity-type-scan" id="activity-type-scan">
        <line-title title="各活动类型扫码情况"/>
        <scroll-view scroll-x="true" class="scroll-view-data">
            <view class="select-dimension" v-if="!isGeneralManager">
                <select-button isBoard label="开瓶扫码" :selected-flag="scanSubParam.scanSubType==='OpenScan'"
                               @tap=" scanActiveTypeQuery('OpenScan')"></select-button>
                <select-button isBoard label="赠送扫码" :selected-flag="scanSubParam.scanSubType==='GiftScan'"
                               @tap="scanActiveTypeQuery('GiftScan')"></select-button>
                <select-button isBoard label="手动编辑" :selected-flag="scanSubParam.scanSubType==='handEdit'"
                               @tap="scanActiveTypeQuery('handEdit')"></select-button>
            </view>
            <view v-else>
            </view>
        </scroll-view>
        <view class="view-all-scan">
            <view class="title">
                <link-icon icon="icon-weixinkuaizhao" class="title-icon"/>
                TOP10
            </view>
            <view class="view-all" @tap="viewAll()" v-if="!isDataNull">查看全部
                <link-icon icon="mp-arrow-right" class="more-icon"/>
            </view>
        </view>
        <view class="activity-type-scan-chart" v-if="!isDataNull">
            <view class="terminal-level-content">
                <link-echart
                    :option="countVerifyScanOption"
                    :height="capacityLevelBarYCategoryHeight+'px'"
                    ref="costQuotaEchart"
                    :loading="loadingFlag"
                    :force-use-old-canvas="false"
                />
            </view>
        </view>
        <view class="no-data" v-else>
            <text>暂无数据</text>

        </view>
        <link-dialog ref="countVerifyScanDialog" :enableScroll="false">
            <view class="count-verify-scan-table">
                <view class="dialog-title">
                    <view v-if="!isGeneralManager">
                        {{
                            scanSubParam.scanSubType === 'OpenScan' ? '开瓶扫码' : scanSubParam.scanSubType === 'GiftScan' ? '赠送扫码' : '手动编辑'
                        }}
                    </view>
                    <view v-else>
                        {{'各活动类型扫码情况'}}
                    </view>
                    <link-icon icon="mp-close" class="close-icon" @tap="()=>{$refs.countVerifyScanDialog.hide();}"/>
                </view>
                <view class="divide"></view>
                <view class="count-verify-scan-table-scroll-content">
                    <scroll-view :scroll-y="scanTableData.length<10?'false':'true'"
                                 class="count-verify-scan-table-content" @scrolltolower="scrollToLower">
                        <view class="count-verify-scan-table-item title-column">
                            <view class="active-type-title title">活动类型</view>
                            <view class="scan-count title" v-if="!isGeneralManager">
                                {{
                                    scanSubParam.scanSubType === 'OpenScan' ? '开瓶扫码' : scanSubParam.scanSubType === 'GiftScan' ? '赠送扫码' : '手动编辑'
                                }}(瓶)
                            </view>
                            <view class="scan-count title" v-else>
                                {{'扫码瓶数'}}
                            </view>
                        </view>
                        <view v-if="!isDataNull" class="count-verify-scan-table-item" v-for="item in scanTableData">
                            <view class="active-type">
                                <view class="active-type-text">{{ item.name }}</view>
                            </view>
                            <view class="scan-count">{{ item.value }}</view>
                        </view>
                    </scroll-view>
                </view>
            </view>
        </link-dialog>
    </link-page>
</template>

<script>
import TitleLine from "../../../../lzlj-II/fighting-fakes/components/title-line";
import LineTitle from "../../components/line-title";
import SelectButton from "../../components/select-button";
import {CbarYCategory} from "../marketing-echart.utils";
import Taro from "@tarojs/taro";

export default {
    name: "activity-type-scan",
    components: {TitleLine, LineTitle, SelectButton},
    props: {
        orgId: {
            type: String,
            required: true
        },
    },
    data() {
        const userInfo = Taro.getStorageSync('token').result;
        return {
            userInfo,
            //是否是总经理层级
            isGeneralManager: false,
            countVerifyScanBoardRows: [],//请求的数据  弹窗表格数据
            loadingFlag: false,//加载
            // isShowChart: false,//是否显示图表
            countVerifyScanOption: null,//echarts柱状图option
            capacityLevelBarYCategoryHeight: null,//图表高度
            scanTableData: [],//弹窗表格数据
            scanSubParam: {scanSubType: 'OpenScan'},//活动类型
            isDataNull: true,//是否有数据
            currentOrgId: this.orgId,
            currentPage: 1,//当前请求页数
            total: 0,//数据总条数
            isLoadingNextPage: false//是否正在请求下一页
        }
    },
    watch: {
        orgId() { // watch监听props里status的变化，然后执行操作
            this.queryCurrentScanSubTypeData();
        }
    },
    async created() {
        this.isGeneralManager = ['SalesGeneralManager','GeneralManager','BrandManager'].includes(this.userInfo.positionType)
        this.queryCurrentScanSubTypeData();
    },
    methods: {
        /**
         * @createdBy  王雅琪
         * @date  2022/08/16
         * @methods barYCategory
         * @para
         * @description 获取全部数据
         */
        async queryCurrentScanSubTypeData() {
            try {
                this.$utils.showLoading()
                this.currentPage = 1;
                this.countVerifyScanOption = null;
                let param={};
                if(this.isGeneralManager){
                    param = {
                        attr1: this.orgId,
                        attr4: 'manager',
                        page: this.currentPage,
                        rows: 10
                    }
                }else{
                    param = {
                        scanSubType: this.scanSubParam.scanSubType,
                        attr1: this.orgId,
                        page: this.currentPage,
                        rows: 10,
                        sort: this.scanSubParam.scanSubType === 'handEdit' ? 'handVerifySum' : 'actualVerifySum'
                    }
                }
                const data = await this.$http.post('export/link/codeScanBoard/countVerifyScanBoardByTypePage', param);
                if (data.success) {
                    this.countVerifyScanBoardRows = data.rows;
                    this.total = data.total;
                    if (!this.countVerifyScanBoardRows || this.countVerifyScanBoardRows.length === 0) {
                        this.$utils.hideLoading();
                        this.isDataNull = true;
                        return;
                    }
                    this.isDataNull = false;
                    let seriesData = [];
                    if (this.scanSubParam.scanSubType === 'handEdit') {
                        seriesData = await this.handleData('handVerifySum')
                    } else {
                        seriesData = await this.handleData('actualVerifySum')
                    }
                    this.capacityLevelBarYCategoryHeight = (40 + 36 * seriesData.length) >= 100 ? 40 + 36 * seriesData.length : 100;
                    this.countVerifyScanOption = echartInitConfig => CbarYCategory(echartInitConfig, seriesData);
                    this.$utils.hideLoading();
                } else {
                    this.$utils.hideLoading();
                    this.$showError('查询各活动类型扫码情况接口失败，请稍后重试！' + data.result);
                }
            } catch (e) {
                this.$utils.hideLoading();
                this.$showError('查询各活动类型扫码情况接口失败！');
            }
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/08/16
         * @methods scanActiveTypeQuery
         * @para
         * @description 切换活动类型
         */
        scanActiveTypeQuery(scanSubtype) {
            this.scanSubParam.scanSubType = scanSubtype;
            this.queryCurrentScanSubTypeData();
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/08/24
         * @methods viewAll
         * @para
         * @description 查看全部
         */
        viewAll() {
            // setTimeout(()=>{
            //     wx.pageScrollTo({
            //         selector: '#activity-type-scan',
            //     })
            // }, 100)
            // this.isShowChart=true;
            this.$refs.countVerifyScanDialog.show();
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/08/24
         * @methods handleData
         * @para VerifySum：actualVerifySum扫码数量 handVerifySum手动编辑瓶数
         * @description 处理接收的数据
         */
        async handleData(VerifySum) {
            try {
                this.countVerifyScanBoardRows.sort((a, b) => {
                    return Number(b[VerifySum]) - Number(a[VerifySum])
                })
                let seriesData = []
                this.scanTableData = [];
                for (let i = 0, count = 0; i < this.countVerifyScanBoardRows.length; i++) {
                    let name = await this.$lov.getNameByTypeAndVal('MC_TYPE', this.countVerifyScanBoardRows[i].activityType);
                    const scanValue = Number(this.countVerifyScanBoardRows[i][VerifySum]);
                    if (scanValue > 0) {
                        this.scanTableData.push({
                            value: scanValue,
                            name: name
                        })
                    }
                    if (scanValue > 0 && count < 10) {
                        seriesData.push({
                            value: scanValue,
                            name: name
                        })
                        count++;
                    }
                }
                return seriesData;
            } catch (e) {
                this.$utils.hideLoading();
                this.$showError('查询值列表接口失败！');
            }
        },
        async scrollToLower() {
            if (this.scanTableData.length >= this.total || this.isLoadingNextPage) return;
            try {
                this.isLoadingNextPage = true;
                this.$utils.showLoading();
                let param={};
                if(this.isGeneralManager){
                    param = {
                        attr1: this.orgId,
                        attr4: 'manager',
                        page: ++this.currentPage,
                        rows: 10
                    }
                }else{
                    param = {
                        scanSubType: this.scanSubParam.scanSubType,
                        attr1: this.orgId,
                        page: ++this.currentPage,
                        rows: 10,
                        sort: this.scanSubParam.scanSubType === 'handEdit' ? 'handVerifySum' : 'actualVerifySum'
                    }
                }
                const data = await this.$http.post('export/link/codeScanBoard/countVerifyScanBoardByTypePage', param);
                if (data.success) {
                    const newRows = data.rows;
                    this.total = data.total;
                    if (!newRows || newRows.length === 0) {
                        return;
                    }
                    for (let i = 0; i < newRows.length; i++) {
                        let name = await this.$lov.getNameByTypeAndVal('MC_TYPE', newRows[i].activityType);
                        const scanValue = Number(newRows[i][this.scanSubParam.scanSubType === 'handEdit' ? 'handVerifySum' : 'actualVerifySum']);
                        this.scanTableData.push({
                            value: scanValue,
                            name: name
                        })
                    }
                    this.$utils.hideLoading();
                    this.isLoadingNextPage = false;
                } else {
                    this.$showError('查询各活动类型扫码情况接口失败，请稍后重试！' + data.result);
                    this.$utils.hideLoading();
                }
            } catch (e) {
                this.$showError('查询各活动类型扫码情况接口失败！');
                this.$utils.hideLoading();
            }
        }
    }
}
</script>

<style lang="scss">
.activity-type-scan {
    background: #ffffff;
    margin-top: 24px;
    border-radius: 12px;
    padding: 0 24px;

    .stair-title {
        font-family: PingFangSC-Medium;
        font-size: 32px;
        text-align: left;
        line-height: 32px;
        font-weight: 500;
    }

    .activity-type-scan-chart {
        background: white;
        border-radius: 10px;
        margin-bottom: 32px;
        margin-top: -16px;
    }

    .no-data {
        display: flex;
        justify-content: center;
        align-items: center;
        color: #6D96FA;
        font-size: 24px;
        margin-bottom: 32px;
    }

    .scroll-view-data {
        margin-top: 24px;
        margin-bottom: 24px;

        .select-dimension {
            display: flex;
            margin-left: 24px;

            .select-button {
                background-color: #F2F3F6;
                color: #333;
                font-size: 28px;
                font-weight: 400;
            }
        }
    }

    .view-all-scan {
        display: flex;
        justify-content: space-between;
        align-items: center;
        //margin: 40px 0 0 0;

        .title {
            display: flex;
            align-items: center;
            font-size: 28px;
            color: #333;
            font-weight: 500;
            padding: 20px 0;

            .title-icon {
                margin-right: 16px;
                font-size: 10px;
                line-height: 10px;
                color: #2F69F8;
            }
        }

        .view-all {
            display: flex;
            align-items: center;
            color: #2F69F8;
            font-weight: 400;
            font-size: 26px;
            padding: 20px 0;

            .more-icon {
                margin-left: 10px;
            }
        }
    }

    .count-verify-scan-table {
        width: 100%;

        .dialog-title {
            position: relative;
            font-size: 32px;
            margin-bottom: 30px;
            color: #333333;
            font-weight: 500;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 36px 36px 0 36px;

            .close-icon {
                position: absolute;
                right: 36px;
                top: 36px;
                color: #999;
                font-size: 32px;
            }
        }

        .divide {
            width: 327*2px;
            height: 2px;
            margin: auto;
            background-color: #EEF3F5;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .count-verify-scan-table-scroll-content {
            padding: 25px 36px 36px 36px;
        }

        .count-verify-scan-table-content {
            overflow: scroll;
            max-height: 800px;
            min-height: 240px;

            .title-column {
                position: sticky;
                top: 0;
                z-index: 99;
            }

            .count-verify-scan-table-item {
                display: flex;
                font-size: 24px;
                color: #666;
                width: 100%;
                justify-content: center;
                align-items: center;
                height: 80px;
                flex-wrap: nowrap;

                .title {
                    background-color: #C8D7FA;
                    color: #333;
                    font-weight: 500;
                }

                .active-type-title, .scan-count {
                    flex: 1;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border: 2px solid #E6EAF4;
                    height: 76px;
                }

                .scan-count {
                    border-left: none;
                }

                .active-type {
                    flex: 1;
                    display: flex;
                    align-items: center;
                    height: 76px;
                    border: 2px solid #E6EAF4;

                    .active-type-text {
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 2;
                        overflow: hidden;
                        text-align: center;
                        width: 100%;
                    }
                }

            }
        }
    }

    .no-data {
        height: 200px;
        color: #D8D8D8;
    }

    .link-dialog-content {
        width: 87% !important;
        border-radius: 16px !important;

        .link-dialog-body {
            padding: 0;
        }
    }
}
</style>
