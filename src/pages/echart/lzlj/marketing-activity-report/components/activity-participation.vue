<!--
 @file 市场活动角色看板-活动详情-参与情况
 <AUTHOR>
 @Created 2022/9/7
-->
<template>
    <link-page class="activity-participation">
        <!--参与人数柱状图-->
        <view class="participants-num">
            <view class="participants-title">
                <link-icon icon="icon-weixinkuaizhao" class="title-icon"/>
                参与人数
            </view>
            <view class="participation-chart" v-if="!isNumDataNull">
                <view class="participation-num-content">
                    <link-echart
                        :option="participationNumOption"
                        :height=" 230 + 'px'"
                        :force-use-old-canvas="false"
                    />
                </view>
            </view>
            <view class="no-data" v-else>
                <text>暂无数据</text>
            </view>
        </view>
        <!--参与人数排名柱状图-->
        <view class="participant-ranking" v-if="participantRankFlag">
            <view class="participants-title">
                <link-icon icon="icon-weixinkuaizhao" class="title-icon"/>
                参与人数排名
            </view>
            <view class="participation-chart" v-if="!isRankingDataNull">
                <view class="participation-ranking-content">
                    <link-echart
                        :option="participationRankingOption"
                        :height=" 230 + 'px'"
                        :force-use-old-canvas="false"
                    />
                </view>
            </view>
            <view class="no-data" v-else>
                <text>暂无数据</text>
            </view>
        </view>
        <!--参与人数趋势折线图-->
        <view class="participant-trend">
            <view class="participants-title">
                <link-icon icon="icon-weixinkuaizhao" class="title-icon"/>
                参与人数趋势(场次)
            </view>
            <!--活动类型选择-->
            <view>
                <scroll-view scroll-x="true" class="scroll-view-data">
                    <view class="select-dimension">
                        <picker v-if="isCompLoadingCost" :value="typeIndex" @change="pickerTypeChange"
                                :range="dataCostTypeOption">
                            <select-button :label="typeIndex?dataCostTypeOption[typeIndex]:'所有费用小类'"
                                           :selected-flag="true"
                                           downIcon></select-button>
                        </picker>
                    </view>
                </scroll-view>
            </view>
            <view class="participation-chart" v-if="!isTrendDataNull">
                <view class="participation-trend-content">
                    <link-echart
                        :option="participationTrendOption"
                        :height=" 230 + 'px'"
                        :force-use-old-canvas="false"
                    />
                </view>
            </view>
            <view class="no-data" v-else>
                <text>暂无数据</text>
            </view>
        </view>
    </link-page>
</template>

<script>
import {
    targetSimpleDataHistogram,
    targetTrendProgress
} from "../marketing-echart.utils";
import SelectButton from "../../components/select-button";
import Taro from "@tarojs/taro";


export default {
    name: "activity-participation",
    components: {SelectButton},
    data() {
        return {
            participationNumOption: null,//参与人数option
            participationRankingOption: null,//参与人数排名option
            participationTrendOption: null,//参与人数趋势option
            typeIndex: 0,//按不同小类统计当前选中下标
            dataCostTypeOption: ['所有费用小类'],//按不同小类
            costTypeCode: [],//不同小类Code
            isCompLoadingCost: false,//是否已获取到小类数据
            timeRangeOption: ['week', 'month', 'fiscal','lastFiscal'],//时间范围选择code
            trendSortData: ['actDayWeek', 'actWeekMonth', 'actMonthYear'],
            userInfo: {},
            isNumDataNull: true,//参与人数数据是否为空
            isRankingDataNull: true,//参与人数排名数据是否为空
            isTrendDataNull: true,//参与人数趋势数据是否为空
            isShowLoading: 0
        }
    },
    props: {
        OuathFlag: {
            type: Boolean,
            required: true
        },
        orgId: {
            type: String
        },
        timeRangeIndex: {
            type: Number,
            required: true
        },
        participantRankFlag:{
            type: Boolean,
            required: false
        },
    },
    watch: {
        orgId() { // watch监听props里status的变化，然后执行操作
            this.queryAllData();
        },
        timeRangeIndex() {
            this.queryAllData();
        }
    },
    async created() {
        this.userInfo = Taro.getStorageSync('token').result;
        this.queryCostTypeData();
        this.queryAllData();
    },
    methods: {
        /**
         * @createdBy  王雅琪
         * @date  2022/09/22
         * @methods queryCostTypeData
         * @para
         * @description 获取所有图表数据
         */
        async queryAllData() {
            try {
                this.showLoading();
                this.isNumDataNull = true;
                this.isRankingDataNull = true;
                this.isTrendDataNull = true;
                const url = 'export/link/marketActBoard/queryByExamplePage';
                const participateNumParam = {
                    timeRange: this.timeRangeOption[this.timeRangeIndex],
                    boardType: "sceneFee",
                    sort: "actJoinNumber",
                    order: "desc"
                };
                const rankingDataParam = {
                    timeRange: this.timeRangeOption[this.timeRangeIndex],
                    boardType: "sceneExecute",
                    sort: "actJoinNumber",
                    order: "desc"
                };
                const trendDataParam = {
                    timeRange: this.timeRangeOption[this.timeRangeIndex],
                    boardType: this.typeIndex ? 'sceneFeeTime' : 'sceneTime',
                    sort: this.trendSortData[this.timeRangeIndex],
                    order: "asc"
                };
                if (this.OuathFlag) {
                    participateNumParam.actOrgId = this.orgId || this.userInfo.orgId;
                    rankingDataParam.actOrgId = this.orgId || this.userInfo.orgId;
                    trendDataParam.actOrgId = this.orgId || this.userInfo.orgId;
                } else {
                    participateNumParam.actPostnId = this.userInfo.postnId;
                    rankingDataParam.actPostnId = this.userInfo.postnId;
                    trendDataParam.actPostnId = this.userInfo.postnId;
                }
                if (this.typeIndex) {
                    trendDataParam.costTypeCode = this.costTypeCode[this.typeIndex - 1];
                }
                const [ParticipateNum, RankingData, TrendData] = await Promise.all([
                    this.$http.post(url, participateNumParam),
                    this.$http.post(url, rankingDataParam),
                    this.$http.post(url, trendDataParam)
                ])
                //参与人数
                if (ParticipateNum.success && ParticipateNum.rows) {
                    this.setParticipateNumData(ParticipateNum.rows);
                } else {
                    this.$showError('查询参与人数接口失败，请稍后重试！' + ParticipateNum.result);
                }
                //参与人数排名
                if (RankingData.success && RankingData.rows) {
                    this.setParticipateRankingData(RankingData.rows);
                } else {
                    this.$showError('查询参与人数排名接口失败，请稍后重试！' + RankingData.result);
                }
                //参与人数趋势
                if (TrendData.success && TrendData.rows) {
                    this.setParticipateTrendData(TrendData.rows);
                } else {
                    this.$showError('查询参与人数趋势接口失败，请稍后重试！' + TrendData.result);
                }
                this.hideLoading();
            } catch (e) {
                this.hideLoading();
                this.$showError('查询参与情况接口失败！');
            }
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/09/16
         * @methods queryCostTypeData
         * @para
         * @description 获取费用小类数据
         */
        async queryCostTypeData() {
            try {
                this.showLoading();
                const data = await this.$http.post('export/link/feeType/queryByExamplePage',
                    {
                        filtersRaw: [{id: 'costTypeLevel', property: 'costTypeLevel', value: '3'}]
                    });
                if (data.success) {
                    const costTypeRows = data.rows;
                    costTypeRows.forEach(v => {
                        this.costTypeCode.push(v.costTypeCode)
                        this.dataCostTypeOption.push(v.costTypeName)
                    })
                    this.isCompLoadingCost = true;
                    this.hideLoading();
                } else {
                    this.hideLoading();
                    this.$showError('查询费用小类接口失败，请稍后重试！' + data.result);
                }
            } catch (e) {
                this.hideLoading();
                this.$showError('查询费用小类接口失败！');
            }
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/09/16
         * @methods queryCostTypeData
         * @para
         * @description 切换费用小类
         */
        async pickerTypeChange(e) {
            if (this.typeIndex === this.dataCostTypeOption[Number(e.detail.value)]) {
                return;
            }
            this.typeIndex = Number(e.detail.value);
            this.showLoading();
            this.isTrendDataNull = true;
            try {
                const trendDataParam = {
                    timeRange: this.timeRangeOption[this.timeRangeIndex],
                    boardType: this.typeIndex ? 'sceneFeeTime' : 'sceneTime',
                    sort: this.trendSortData[this.timeRangeIndex],
                    order: "asc"
                };
                if (this.OuathFlag) {
                    trendDataParam.actOrgId = this.orgId || this.userInfo.orgId;
                } else {
                    trendDataParam.actPostnId = this.userInfo.postnId;
                }
                if (this.typeIndex) {
                    trendDataParam.costTypeCode = this.costTypeCode[this.typeIndex - 1];
                }
                const data = await this.$http.post('export/link/marketActBoard/queryByExamplePage', trendDataParam);
                if (data.success && data.rows) {
                    this.setParticipateTrendData(data.rows);
                    this.hideLoading();
                } else {
                    this.hideLoading();
                    this.$showError('查询参与人数趋势接口失败，请稍后重试！' + data.result);
                }
            } catch (e) {
                this.hideLoading();
                this.$showError('查询参与情况接口失败！');
            }
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/09/22
         * @methods setParticipateNumData
         * @para
         * @description 绘制参与人数图表
         */
        setParticipateNumData(data) {
            this.participationNumOption = null;
            if (data.length > 0) {
                let seriesDataNum = [];
                data.forEach(v => {
                    seriesDataNum.push({
                        name: v.costTypeName,
                        value: v.actJoinNumber
                    })
                })
                this.participationNumOption = echartInitConfig => targetSimpleDataHistogram(echartInitConfig, seriesDataNum)
                this.isNumDataNull = false;
            }
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/09/22
         * @methods setParticipateRankingData
         * @para
         * @description 绘制参与人数排名图表
         */
        setParticipateRankingData(data) {
            this.participationRankingOption = null;
            if (data.length > 0) {
                let seriesDataRanking = [];
                data.forEach(v => {
                    seriesDataRanking.push({
                        name: v.executor,
                        value: v.actJoinNumber
                    })
                })
                this.participationRankingOption = echartInitConfig => targetSimpleDataHistogram(echartInitConfig, seriesDataRanking)
                this.isRankingDataNull = false;
            }
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/09/22
         * @methods setParticipateRankingData
         * @para
         * @description 绘制参与人数趋势图表
         */
        setParticipateTrendData(data) {
            this.participationTrendOption = null;
            if (data.length > 0) {
                let seriesDataTrend = [];
                data.forEach(v => {
                    let seriesName = '';
                    switch (this.timeRangeIndex) {
                        case 0:
                            seriesName = v.actDay.substring(0, 2) + '/' + v.actDay.substring(2, 4);
                            break;
                        case 1:
                            seriesName = '第' + v.actWeekMonth + '周';
                            break;
                        case 2:
                            seriesName = v.actMonthYear + '月';
                            break;
                    }
                    seriesDataTrend.push({
                        name: seriesName,
                        value: v.actJoinNumber
                    })
                })
                this.participationTrendOption = echartInitConfig => targetTrendProgress(echartInitConfig, seriesDataTrend);
                this.isTrendDataNull = false;
            }
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/10/11
         * @methods hideLoading
         * @para
         * @description 展示loading
         */
        hideLoading() {
            if (!(--this.isShowLoading)) {
                this.$utils.hideLoading();
            }
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/10/11
         * @methods showLoading
         * @para
         * @description 展示loading
         */
        showLoading() {
            if (!this.isShowLoading) this.$utils.showLoading();
            this.isShowLoading++;
        }
    }
}
</script>

<style lang="scss">
.activity-participation {
    padding: 32px 24px;

    .participants-title {
        display: flex;
        align-items: center;
        font-size: 28px;
        color: #333;
        font-weight: 500;
        padding: 20px 0;

        .title-icon {
            margin-right: 16px;
            font-size: 10px;
            line-height: 10px;
            color: #2F69F8;
        }
    }

    .participants-num {
        margin-bottom: 48px;

    }

    .participant-ranking {
        margin-bottom: 48px;
    }

    .scroll-view-data {
        .select-dimension {
            display: flex;
            margin-left: 24px;
        }
    }

    .no-data {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 24px;
        height: 310px;
        color: #D8D8D8;
    }
}
</style>
