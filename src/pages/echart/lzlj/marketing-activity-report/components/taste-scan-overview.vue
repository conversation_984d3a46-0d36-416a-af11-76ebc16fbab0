<!--
 @file 品鉴酒扫码概览
 <AUTHOR>
 @Created 2022/8/18
-->
<template>
    <view class="taste-scan-overview">
        <line-title title="品鉴酒扫码概览"></line-title>
<!--        展示三项总瓶数-->
        <view class="all-data-content">
            <view class="all-data-item">
                <view class="center">
                    <view class="number">{{tasteScanParam.claimBottleSum}}</view>
                    <view class="text">领用扫码总瓶数</view>
                </view>
                <view class="line"></view>
            </view>
            <view class="all-data-item">
                <view class="center">
                    <view class="number">{{tasteScanParam.verifyBottleSum}}</view>
                    <view class="text">核销扫码总瓶数</view>
                </view>
                <view class="line"></view>
            </view>
            <view class="all-data-item">
                <view class="center">
                    <view class="number">{{tasteScanParam.handVerifySum}}</view>
                    <view class="text">手动编辑总瓶数</view>
                </view>
            </view>

        </view>
<!--        分隔线-->
        <view class="divide"></view>
<!--        费用核销&非费用核销-->
        <view class="all-cost-off">
            <select-button is-board label="费用核销" :selected-flag="offParam.attr3==='fee'"
                           @tap=" changeCostType('fee')"></select-button>
            <select-button isBoard label="非费用核销" :selected-flag="offParam.attr3==='noFee'"
                           @tap="changeCostType('noFee')"></select-button>
        </view>
<!--        echarts图表-->
        <view>
            <link-echart  :loading="loadingFlag"
                          :option="offCostOption"
                          :height="offCostOptionHeight + 'px'"
                          :force-use-old-canvas="false"
            />
        </view>
    </view>
</template>
<script>
import LineTitle from "../../../../lzlj/components/line-title";
import SelectButton from "../../components/select-button";
import {targetPieChart} from "../marketing-echart.utils";
import Taro from "@tarojs/taro";

export default {
    name:'taste-scan-overview',
    components: {SelectButton, LineTitle},
    props: {
        orgId: {
            type:String,
            required:true
        },
    },
    data() {
        const userInfo = Taro.getStorageSync('token').result;
        return {
            userInfo,
            offParam:{attr3:'fee'}, //费用核销
            tasteScanParam:{}, //品鉴酒扫码概览数据
            offCostOption:null, // echarts核销扫码饼状图
            offCostOptionHeight: (this.$device.systemInfo.windowWidth - 24) * 0.727 < 254 ? (this.$device.systemInfo.windowWidth - 24) * 0.727 : 254,//核销扫码总瓶数饼图height
            claimBottleSum:0,   //领用扫码总瓶数
            verifyBottleSum:0,  //核销扫码总瓶数
            handVerifySum:0,    //手动编辑总瓶数
            //是否是总经理层级
            isGeneralManager: false,
            // 图表组件是否在加载
            loadingFlag: false,
        }
    },
    created() {
        this.isGeneralManager = ['SalesGeneralManager','GeneralManager','BrandManager'].includes(this.userInfo.positionType)
        this.getTasteOverview();
        this.offScanTotalPie();
    },
    watch: {
        orgId() {
            this.getTasteOverview();
            this.offScanTotalPie();
        }
    },
    methods: {
        /**
         *  @description: 评鉴酒扫码数据概览（领用扫码、核销扫码、手动编辑总瓶数）
         *  @author: 何春霞
         *  @date: 2022/8/22 09:50
         */
        async getTasteOverview() {
            try {
                const data = await this.$http.post('export/link/codeScanBoard/countCodeScanBoardByFiscal',{
                    attr1: this.orgId,
                });
                if(data.success) {
                    this.tasteScanParam = data.rows;
                } else {
                    this.$showError('获取品鉴酒概览数据异常',e);
                }
            }catch(e) {
                this.$showError('获取品鉴酒概览数据异常',e);
            }
        },

        /**
         *  @description: 切换费用核销/非费用核销
         *  @author: 何春霞
         *  @date: 2022/8/18 16:50
         */
        async changeCostType(attr3) {
            // 接口获取数据时不允许切换类型
            if (this.loadingFlag) {
                return;
            }
            this.offParam.attr3=attr3;
            await this.offScanTotalPie();
        },

        /**
         *  @description: 查询核销扫码总瓶数饼状图
         *  @author: 何春霞
         *  @date: 2022/8/18 16:50
         */
        async offScanTotalPie() {
            try {
                this.offCostOption = null;
                this.loadingFlag = true;
                let param = {};
                if(this.isGeneralManager){
                    param = {
                        attr3: this.offParam.attr3,
                        attr1: this.orgId,
                        attr4: 'manager'
                    }
                }else{
                    param = {
                        attr3: this.offParam.attr3,
                        attr1: this.orgId
                    }
                }
                const data = await this.$http.post('export/link/codeScanBoard/countVerifyScanBoardByFiscal', param);
                if (data.success) {
                    const offScanTotalPie = data.rows;
                    const openBottleSum = {
                        name: '开瓶扫码瓶数',
                        value: offScanTotalPie.openBottleSum
                    };
                    const giftBottleSum = {
                        name: '赠送扫码瓶数',
                        value: offScanTotalPie.giftBottleSum
                    }
                    const handQtySum = {
                        name: '手动编辑瓶数',
                        value: offScanTotalPie.handQty?offScanTotalPie.handQty :''
                    }
                    let seriesData = [];
                    if(this.isGeneralManager&&this.offParam.attr3!=='noFee'){
                        seriesData = [openBottleSum, giftBottleSum, handQtySum];
                    }else {
                        seriesData = [openBottleSum, giftBottleSum];
                    }
                    let pieColor =[];
                    if(this.isGeneralManager){
                        pieColor = [
                            {
                                c1: '#FF8456',  //管理
                                c2: '#FFB958'
                            }, {
                                c1: '#6A86FB',  //管理
                                c2: '#41A9FB'
                            },{
                                c1: '#DF331B',  //管理
                                c2: '#F6442C'
                            }
                        ];
                    }else{
                        pieColor = [
                            {
                                c1: '#FF8456',  //管理
                                c2: '#FFB958'
                            }, {
                                c1: '#6A86FB',  //管理
                                c2: '#41A9FB'
                            }
                        ]
                    }
                    let totalSeriesData = [{
                        value: offScanTotalPie[this.offParam.attr3 === 'fee' ? 'actVerifyBottleSum' : 'noActVerifyBottleSum'],
                        name: this.isGeneralManager?'核销总瓶数':'核销扫码总瓶数'
                    }]
                    this.offCostOption = echartInitConfig => targetPieChart(echartInitConfig, seriesData, totalSeriesData, ['40%', '70%'], '40%', pieColor, 225,'value','');
                }
            } catch (e) {
                this.$showError('查询核销扫码总瓶数接口失败！');
            } finally {
                this.loadingFlag = false;
            }
        }
    }
}
</script>

<style lang="scss">
.taste-scan-overview {
        background: #ffffff;
        margin-top:24px;
        border-radius: 24px;

    .all-data-content{
        height: 166px;
        margin: 0 24px 8px;
        @include flex-center-center;
        .all-data-item {
            width: 33.33%;
            height: 166px;
            @include flex-center-center;
            .center{
                display: flex;
                flex-direction: column;
                align-items: center;
                flex: 1;
                .number{
                    font-size: 36px;
                    color: #333333;
                    font-family: PingFangSC-Semibold;
                    text-align: center;
                    line-height: 36px;
                    font-weight: 600;
                    letter-spacing: 1px;
                    margin-bottom: 16px;
                }
                .text{
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    color: #666666;
                    text-align: center;
                    line-height: 26px;
                    font-weight: 400;
                    letter-spacing: 0;
                }
            }
            .line{
                width: 2px;
                height: 40px;
                background-color: #DDDDDD;
            }
        }
    }

    .divide {
        width: 327*2px;
        height: 2px;
        margin:auto;
        background-color: #EEF3F5;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .all-cost-off {
        display: flex;
        margin-top: 24px;
        margin-left: 24px;
        flex-wrap: wrap;
            .select-button {
                height: 30px;
                border: none !important;
                font-family: PingFangSC-Regular;
                font-size: 28px;
                background-color: #F2F3F6;
                color: #666666;
                margin-bottom: 24px;
            }
    }

}
</style>
