<template>
    <link-page class="marketing-activities-ll" v-if="marketingActivitiesIIFlag">
        <view style="padding-top: 12px"></view>
        <!--顶部功能栏-->
        <view style="display: flex; margin-bottom: 12px;">
            <view>
                <scroll-view scroll-x="true" class="scroll-view-data"  v-if="orgBox" >
                    <view class="select-dimension">
                        <select-button :label="orgText" :selected-flag="true" @tap="dialogFlag=true;" downIcon></select-button>
                    </view>
                </scroll-view>
            </view>
            <view>
                <scroll-view scroll-x="true" class="scroll-view-data" >
                    <view class="select-dimension">
                        <picker  :value="index" @change="pickerChange" :range="dataBoardOption" >
                            <select-button :label="dataBoardOption[index]" :selected-flag="true" downIcon></select-button>
                        </picker>
                    </view>
                </scroll-view>
            </view>
        </view>
        <view class="boardBox" v-if="activityOverviewFlag">
            <activity-overview :date-index="index" :org-id="orgId" :ouath-flag="OuathFlag" :user-obj="userInfo"></activity-overview>
        </view>

         <view class="boardBox" v-if="executePanelFlag">
             <execute-panel :date-index="index" :org-id="orgId" :ouath-flag="OuathFlag" :user-obj="userInfo"></execute-panel>
        </view>

        <!--活动详情-->
        <view class="boardBox activity-detail-box" v-if="activityDetailFlag">
             <line-title title="活动详情"/>
             <scroll-view scroll-x="true" class="acitve-scroll-view-data">
                 <view class="select-dimension">
                     <select-button v-if="activityFeesFlag" isBoard label="费用情况" :selected-flag="activityDetailSelect==='fees'"
                                    @tap=" activityDetailSelect='fees'"></select-button>
                     <select-button isBoard label="举办情况" :selected-flag="activityDetailSelect==='organizing'"
                                    @tap="activityDetailSelect='organizing'"></select-button>
                     <select-button isBoard label="参与情况" :selected-flag="activityDetailSelect==='participation'"
                                    @tap="activityDetailSelect='participation'"></select-button>
                 </view>
             </scroll-view>
             <!--分割线-->
             <view class="divide"></view>
             <view v-if="activityDetailSelect==='fees'">
                <activity-fees :timeRangeIndex="index" :orgId="orgId" :OuathFlag="OuathFlag"></activity-fees>
             </view>
             <view v-if="activityDetailSelect==='organizing'">
                <activity-organizing :orgParam="orgParam" :orgId="orgId" :time-range="orgParam.timeRange" :hold-rank-flag="holdRankFlag" ref="actOrg"></activity-organizing>
             </view >
             <view v-if="activityDetailSelect==='participation'">
                 <activity-participation :timeRangeIndex="index" :orgId="orgId" :participant-rank-flag="participantRankFlag" :OuathFlag="OuathFlag"></activity-participation>
             </view>
         </view>
        <view v-else>
            <!--    公司总经理展示区域     -->
            <view class="boardBox">
                <activity-ranking-matches :org-id="orgId" :time-range-index="index"></activity-ranking-matches>
            </view>
            <view class="boardBox">
                <actual-money :org-id="orgId" :time-range-index="index"></actual-money>
            </view>
        </view>
         <position-bottom :user-info="userInfo" :show.sync="dialogFlag" @choose="changeOrg"></position-bottom>

    </link-page>
</template>

<script>
import Taro from "@tarojs/taro";
import SelectButton from "../../components/select-button";
import ActivityFees from "./activity-fees";
import LineTitle from "../../../../lzlj/components/line-title";
import ActivityParticipation from "./activity-participation";
import activityOrganizing from "./activity-organizing";
import ActivityOverview from "./activity-overview";
import ExecutePanel from "./execute-panel";
import PositionBottom from "../../components/position-bottom";
import ActivityRankingMatches from "./activity-ranking-matches";
import ActualMoney from "./actual-money";

export default {
    name: "marketing-activities-ll",
    components: {
        ActivityRankingMatches,
        PositionBottom,
        ActivityParticipation,LineTitle, ActivityFees, SelectButton,ExecutePanel, ActivityOverview,activityOrganizing,ActualMoney},
    data(){
        const userInfo = Taro.getStorageSync('token').result;
        let OuathFlag = false;
        return{
            OuathFlag,
            index:0,//用于标记时间 代表dataBoardOption选中数据下标
            dataBoardOption:['本周','本月','本财年','上一财年'],
            dataBoardOptionObject: [
                {lable:'本周',value:'week'},
                {lable:'本月',value:'month'},
                {lable:'本财年',value:'fiscal'},
                {lable:'上一财年',value:'lastFiscal'},
            ],
            orgParam: {},
            userInfo,
            dialogFlag: false,
            tempOrgId: null,
            orgId: null, //实际存储选中组织id
            orgText:'选择组织', //实际存储选中组织文字
            activityDetailSelect: 'organizing',
            //-------------------------
            //用于控制组织筛选框展示
            orgBox: false,
            //用于控制活动概览展示
            activityOverviewFlag: false,
            //用于控制执行案概览展示
            executePanelFlag:false,
            //用于控制费用情况展示
            activityFeesFlag:false,
            //用于控制整个报表展示
            marketingActivitiesIIFlag: false,
            //用于控制活动举办情况中举办场次排名展示
            holdRankFlag: false,
            //用于控制活动参与情况中参与人数排名展示
            participantRankFlag: false,
            //用于控制活动参与情况中参与人数排名展示
            activityDetailFlag: true,
        }
    },
    async created() {

        this.orgParam.timeRange = this.dataBoardOptionObject[this.index].value;

        await this.showHandling();
    },
    methods: {
        /**
         * @createdBy  吕志平
         * @date  2022年11月29日
         * @description 根据职位类型以及参数配置数据来控制菜单的展示
         */
        async showHandling(){
            const Salesman = await this.$utils.getCfgProperty('MarketAct_Role_Salesman');
            const SalesSupervisor = await this.$utils.getCfgProperty('MarketAct_Role_SalesSupervisor');
            const Manager = await this.$utils.getCfgProperty('MarketAct_Role_Manager');
            const GeneralManager = await this.$utils.getCfgProperty('MarketAct_Role_GeneralManager');


            let SalesmanFlag = Salesman.split(',').includes(this.userInfo.positionType);
            let SalesSupervisorFlag = SalesSupervisor.split(',').includes(this.userInfo.positionType);
            let ManagerFlag = Manager.split(',').includes(this.userInfo.positionType);
            let GeneralManagerFlag = GeneralManager.split(',').includes(this.userInfo.positionType);

            if(SalesmanFlag){
               this.activityOverviewFlag = true;
               this.marketingActivitiesIIFlag = true;
               return;
            }
            if(SalesSupervisorFlag){
                this.activityOverviewFlag = true;
                this.marketingActivitiesIIFlag = true;
                this.participantRankFlag = true;
                return;
            }
            if(ManagerFlag){
                this.activityOverviewFlag = true;
                this.executePanelFlag = true;
                this.activityFeesFlag = true;
                this.holdRankFlag = true;
                this.OuathFlag = true;
                this.marketingActivitiesIIFlag = true;
                this.participantRankFlag = true;

                this.orgId = this.userInfo.orgId;
                this.orgText = this.userInfo.orgName;
                this.activityDetailSelect = 'fees';
                this.orgBox = true;
                return;
            }
            if(GeneralManagerFlag){
                this.executePanelFlag = true;
                this.marketingActivitiesIIFlag = true;
                this.activityDetailFlag = false;

                this.OuathFlag = true;
                this.orgId = this.userInfo.orgId;
                this.orgText = this.userInfo.orgName;
                this.orgBox = true;
                return;
            }

        },

        async changeOrg(item) {
            if(Object.keys(item).length === 0)return;
            //2021-07-29 配置了访问组或者this.orgChooseFlag && type === 'input'的情况
            this.orgText = item.text;
            this.orgId = item.id;
        },
        pickerChange(e){
            if (this.index === this.dataBoardOption[Number(e.detail.value)]) {
                return
            }
            this.index = Number(e.detail.value);
            //这里等后端接口给出保存对应参数
            this.orgParam.timeRange = this.dataBoardOptionObject[this.index].value;
            // this.$refs.actOrg.allRequest();
        },
    }
}
</script>

<style lang="scss">
.marketing-activities-ll{
    background-color: #F2F2F2;


    .boardBox {
        background: #ffffff;
        margin: 0 0px 24px;
        border-radius: 12px;
    }

    .positionOrgBox {
        .link-dialog-foot-custom {
            width: auto !important;
        }
    }

    .scroll-view-data {
        .select-dimension {
            display: flex;
            margin-left: 24px;
        }
    }

    .activity-detail-box {
        .stair-title {
            font-family: PingFangSC-Medium;
            font-size: 32px;
            text-align: left;
            line-height: 32px;
            font-weight: 500;
        }

        .acitve-scroll-view-data {
            margin-top: 24px;
            margin-bottom: 24px;

            .select-dimension {
                display: flex;
                margin-left: 24px;

                .select-button {
                    background-color: #F2F3F6;
                    color: #333;
                    font-size: 28px;
                    font-weight: 400;
                }
            }
        }

        .divide {
            width: 327*2px;
            height: 2px;
            margin: auto;
            background-color: #EEF3F5;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }


}
</style>
