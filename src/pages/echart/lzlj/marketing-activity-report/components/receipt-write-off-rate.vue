<!--
 @file 领用核销率
 <AUTHOR>
 @Created 2022/8/22
-->
<template>
    <link-page class="receipt-write-off-rate" id="receipt-write-off-rate">
        <view class="write-off-rate-title">
            <line-title title="领用核销率"/>
            <view class="view-all" @tap="viewAll()"
                  v-if="orgType!=='SalesCity'&&orgType!=='SalesArea'&&!isDataNull">查看明细
                <link-icon icon="mp-arrow-right" class="more-icon"/>
            </view>
        </view>
        <canvas id="myCanvas" :force-use-old-canvas="false" type="2d"
                :style="{width: '100%', height:(dataOptionHeight / 2 + 20)+'px'}"
                disable-scroll='true'></canvas>
        <link-dialog ref="writeOffRateDialog" v-model="isShowDialog" :enableScroll="false">
            <view class="write-off-rate-table">
                <view class="dialog-title">
                    <view>领用核销率</view>
                    <link-icon icon="mp-close" class="close-icon" @tap="()=> {$refs.writeOffRateDialog.hide();}"/>
                </view>
                <view class="divide"></view>
                <view class="write-off-rate-table-scroll-content">
                    <scroll-view @scrolltolower="scrollToLower"  :scroll-y="receiptWriteOffRateTableData.length<10?'false':'true'" class="write-off-rate-table-content">
                        <view class="write-off-rate-table-item title-column">
                            <view class="org-name-title title sticky-left">
                                {{ isSpecifyPositionType ? '领用单号' : '公司/区域名' }}
                            </view>
                            <view class="write-off-rate title">领用核销率</view>
                            <view class="claimQty title">领用瓶数</view>
                            <view class="claimQty title">核销瓶数</view>
                        </view>
                        <view class="write-off-rate-table-item-content" v-if="!isDataNull">
                            <view class="write-off-rate-table-item" v-for="item in receiptWriteOffRateTableData">
                                <view class="org-name sticky-left" v-if="isSpecifyPositionType">
                                    <view class="org-name-text">{{ item.claimCode }}</view>
                                    <link-icon icon="icon-yijianbaobei1" class="copy" @tap="copyData(item.claimCode)"/>
                                </view>
                                <view class="org-name sticky-left" v-else>
                                    <view class="org-name-text">{{ item.orgName }}</view>
                                </view>
                                <view class="percent-scan">{{ Number.parseInt(item.percentScan * 100) + '%' }}</view>
                                <view class="claimQty">{{ item.claimQty }}</view>
                                <view class="claimQty">{{ item.verifyQty }}</view>
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </view>
        </link-dialog>
    </link-page>

</template>

<script>
import Taro from "@tarojs/taro";
import LineTitle from "../../components/line-title";

export default {
    name: "receipt-write-off-rate",
    components: {LineTitle},
    props: {
        orgId: {
            type: String,
            required: true
        },
        orgType: {
            // type: String,
            required: true
        },
    },
    data() {
        const positionType = Taro.getStorageSync('token').result.positionType;
        const isSpecifyPositionType = positionType === 'Salesman' || positionType === 'SalesSupervisor' || positionType === 'GroupBuyManager' || positionType === 'AccountManager'
            || positionType === 'CustServiceManager' || positionType === 'VipManager' || positionType === 'CustServiceSpecialist' || positionType === 'CustServiceSupervisor'
            || positionType === 'SalesTeamLeader' || positionType === 'SalesChannelManger'
        return {
            dataOptionHeight: (this.$device.systemInfo.windowWidth - 24) * 0.727 < 254 ? (this.$device.systemInfo.windowWidth - 24) * 0.727 : 254,
            procData: {visitExRate: 0},//visitExRate整体领用核销率
            receiptWriteOffRateData: [],//获取各领用单核销数
            receiptWriteOffRateTableData: [],
            positionType,//当前登录人职位
            isSpecifyPositionType,//登录人职位是否为业代和业务主管
            isDataNull: true,//获取数据是否为空
            currentOrgId: this.orgId,
            currentPage: 1,
            total: 0,
            isShowDialog: false
        }
    },
    async mounted() {
        this.queryWriteOffRateDataByPage();
        await this.queryWriteOffRateData();
    },
    watch: {
        orgId() { // watch监听props里status的变化，然后执行操作
            this.currentPage = 1;
            this.queryWriteOffRateDataByPage();
            this.queryWriteOffRateData();
        }
    },
    methods: {
        /**
         * @createdBy  王雅琪
         * @date  2022/08/22
         * @methods queryCurrentScanSubTypeData
         * @para
         * @description 获取整体领用核销率
         */
        async queryWriteOffRateData() {
            try {
                this.$utils.showLoading()
                const data = await this.$http.post('export/link/codeScanBoard/countClaimVerifyScanBoardAll', {
                    attr1: this.orgId
                });
                if (data.success) {
                    if (data.rows) {
                        this.procData.visitExRate = Number.parseInt((data.rows.percentScan ? data.rows.percentScan : 0) * 100);
                        this.progress()
                        this.$utils.hideLoading();
                    }
                } else {
                    this.$utils.hideLoading();
                    this.$showError('查询整体领用核销率接口失败，请稍后重试！' + data.result);
                }
            } catch (e) {
                this.$utils.hideLoading();
                this.$showError('查询整体领用核销率接口失败！');
            }
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/08/22
         * @methods queryWriteOffRateDataByPage
         * @para
         * @description 获取各领用单核销数
         */
        async queryWriteOffRateDataByPage(isQueryMore = false) {
            if (!isQueryMore) this.receiptWriteOffRateTableData = [];
            try {
                this.$utils.showLoading()
                const data = await this.$http.post('export/link/codeScanBoard/countClaimVerifyScanBoardByPage',
                    {
                        attr1: this.orgId,
                        page: this.currentPage,
                        rows: 10,
                        totalFlag: true,
                        order: 'asc',
                        sort: 'percentScan'
                    });
                if (data.success) {
                    const receiptWriteOffRateData = data.rows;
                    if (!receiptWriteOffRateData || receiptWriteOffRateData.length === 0) {
                        this.$utils.hideLoading();
                        this.isDataNull = true;
                        return;
                    }
                    this.total = data.total;
                    this.receiptWriteOffRateTableData = this.receiptWriteOffRateTableData.concat(receiptWriteOffRateData)
                    this.isDataNull = false;
                    this.$utils.hideLoading();
                } else {
                    this.$utils.hideLoading();
                    this.$showError('查询各领用单核销数接口失败，请稍后重试！' + data.result);
                }
            } catch (e) {
                this.$utils.hideLoading();
                this.$showError('查询各领用单核销数接口失败！');
            }
        },
        viewAll() {
            this.$refs.writeOffRateDialog.show();
            this.$emit('disableScroll')
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/08/22
         * @methods progress
         * @para
         * @description 弧形进度条
         */
        async progress() {
            wx.createSelectorQuery()
                .select('#myCanvas') // 在 WXML 中填入的 id
                .fields({node: true, size: true})
                .exec((res) => {
                    // Canvas 对象
                    const canvas = res[0].node
                    // Canvas 画布的实际绘制宽高
                    const renderWidth = res[0].width
                    const renderHeight = res[0].height
                    // Canvas 绘制上下文
                    const ctx = canvas.getContext('2d')
                    // 环形进度条变量
                    let outerRadius = 75 // 外环半径
                    let thickness = 15 // 圆环厚度
                    let x = renderWidth / 2  // 圆心x坐标
                    let y = this.dataOptionHeight / 2 - 20 // 圆心y坐标
                    let startAngle = -180 //开始角度
                    let endAngle = 0 //结束角度
                    let scro = parseInt(this.procData.visitExRate) * 1.8 - 180;
                    const rate = parseInt(this.procData.visitExRate) / 100
                    // 初始化画布大小
                    const dpr = wx.getSystemInfoSync().pixelRatio
                    canvas.width = renderWidth * dpr
                    canvas.height = renderHeight * dpr
                    ctx.scale(dpr, dpr)
                    //清除画布
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    // 画圆环
                    ctx.beginPath()
                    ctx.arc(x, y, outerRadius, this.angle2Radian(startAngle), this.angle2Radian(endAngle));
                    ctx.strokeStyle = '#eef4ff' // 弧线的颜色
                    ctx.lineWidth = thickness - 3// 弧的宽度
                    ctx.lineCap = "round" //线条结束端点样式 butt 平直 round 圆形 square 正方形
                    ctx.stroke()
                    if (parseInt(this.procData.visitExRate) !== 0) {
                        // 画进度条
                        ctx.beginPath()
                        ctx.arc(x, y, outerRadius, this.angle2Radian(startAngle), this.angle2Radian(scro))
                        //渐变色
                        let lingrad = ctx.createLinearGradient(x - outerRadius, y, (x + outerRadius) * rate, y - outerRadius);
                        // addColorStop 创建一个颜色的渐变点
                        lingrad.addColorStop(0, '#2F69F8');
                        lingrad.addColorStop(0.6, '#46C1F9');
                        lingrad.addColorStop(1, '#46E3F1');
                        ctx.strokeStyle = lingrad;
                        ctx.lineWidth = thickness
                        ctx.lineCap = "round";
                        ctx.stroke()
                    }
                    ctx.fontSize = 22
                    ctx.fillStyle = '#2F69F8' // 文字的颜色
                    ctx.font = 'normal 500 22px sans-serif';
                    ctx.textAlign = "center";  // 字体位置
                    ctx.fillText(this.procData.visitExRate + '%', x, y - 19)
                    ctx.fontSize = 14
                    ctx.font = 'normal 500 14px sans-serif';
                    ctx.fillStyle = '#333333'
                    ctx.fillText('整体领用核销率', x, y + 9.5)
                    ctx.textAlign = "center";  // 字体位置
                })
        },
        //角度转弧度函数
        angle2Radian(angle) {
            return angle * Math.PI / 180
        },
        /**
         * @desc 复制数据
         * <AUTHOR>
         * @date 2022/8/22
         **/
        copyData(data) {
            wx.setClipboardData({
                data: data,
                success: function () {
                    // 添加下面的代码可以复写复制成功默认提示文本`内容已复制`
                    wx.showToast({
                        title: '领用单号已复制!',
                        icon: 'none'
                    });
                    wx.getClipboardData({
                        success: function (res) {
                        }
                    })
                }
            })
        },

        async scrollToLower(e) {
            if (e.detail.direction !== "bottom") return;
            if (this.receiptWriteOffRateTableData.length >= this.total) return;
            this.currentPage++;
            this.queryWriteOffRateDataByPage(true);
        }
    }
}
</script>

<style lang="scss">
.receipt-write-off-rate {
    background: #ffffff;
    margin-top:24px;
    border-radius: 12px;
    padding: 0 24px 40px 24px;
    position: relative;

    .write-off-rate-title {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        padding-top: 20px;
        height: 60px;
        padding-bottom: 20px;

        .report-line-title {
            height: 60px;
        }

        .view-all {
            display: flex;
            align-items: center;
            color: #2F69F8;
            font-weight: 400;
            font-size: 26px;
            height: 60px;

            .more-icon {
                margin-left: 10px;
            }
        }
    }

    .report-line-title {
        position: relative;

        .stair-title {
            font-family: PingFangSC-Medium;
            font-size: 32px;
            text-align: left;
            line-height: 32px;
            font-weight: 500;
        }
    }

    .write-off-rate-table {
        width: 100%;

        .dialog-title {
            position: relative;
            font-size: 32px;
            margin-bottom: 30px;
            padding: 36px 36px 0 36px;
            color: #333333;
            font-weight: 500;
            display: flex;
            justify-content: center;
            align-items: center;

            .close-icon {
                position: absolute;
                right: 36px;
                top: 36px;
                color: #999;
                font-size: 32px;
            }
        }

        .divide {
            width: 327*2px;
            height: 2px;
            margin: auto;
            background-color: #EEF3F5;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        //.write-off-rate-table-item-content {
        //
        //    overflow-y: scroll;
        //    width: 100%;
        //
        //}
        .write-off-rate-table-scroll-content {
            padding: 25px 36px 36px 36px;
        }

        .write-off-rate-table-content {
            overflow: scroll;
            max-height: 800px;
            min-height: 240px;
        }

        .title-column {
            position: sticky;
            top: 0;
            z-index: 99;
        }

        .sticky-left {
            position: sticky;
            left: 0;
            z-index: 1;
        }

        .write-off-rate-table-item {
            display: flex;
            font-size: 24px;
            color: #666;
            width: 100%;
            justify-content: center;
            align-items: center;
            height: 80px;
            flex-wrap: nowrap;


            .title {
                background-color: #C8D7FA !important;
                color: #333;
                font-weight: 500;
            }

            .percent-scan, .claimQty, .org-name, .write-off-rate, .org-name-title {
                width: 20%;
                display: flex;
                align-items: center;
                justify-content: center;
                border: 2px solid #E6EAF4;
                height: 76px;
                padding: 0 10px;
                background-color: #fff;
            }

            .org-name, .org-name-title {
                width: 34%;
            }

            .write-off-rate, .percent-scan {
                width: 26%;
            }

            // .claimQty, .org-name, .org-name-title {
            //    width: 38%;
            //    display: flex;
            //    align-items: center;
            //    justify-content: center;
            //    border: 1px solid #E6EAF4;
            //    height: 80px;
            //    padding: 0 10px;
            //    background-color: #fff;
            //}
            //
            //.claimQty {
            //    border-left: none;
            //    width: 62%;
            //}


            .org-name {
                justify-content: space-between;


                .org-name-text {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    flex: 1;
                    text-align: center;
                }

                .copy {
                    width: 20px;
                    font-size: 28px;
                }
            }


        }
    }

    .link-dialog-content {
        width: 87% !important;
        border-radius: 16px !important;

        .link-dialog-body {
            padding: 0;
        }
    }

}
</style>
