<!--
 @file 品鉴酒扫码-各区域活动扫码情况
 <AUTHOR>
 @Created 2022/8/18
-->
<template>
        <!--各类区域活动扫码情况-->
    <view class="regionScanCode">
            <line-title title="各区域扫码情况"></line-title>
            <view class="various-scan-code">
                <select-button  is-board
                                :key="index"
                                :label="item.name"
                                :selectedFlag="item.selectedFlag"
                                :value="item.value"
                                @tap="changeScanType(item.attr2,item.scanSubType,index)"
                                v-for="(item,index) in selectScanCode">
                </select-button>
            </view>
            <!--TOP10和查看全部-->
            <view class="top-all">
                <view class="top">
                    <link-icon icon="icon-weixinkuaizhao" class="top-icon"/>
                    <view class="top-text">TOP10</view>
                </view>
                <view class="all"  v-if="verifyScanCodeRows.length !== 0" @tap="()=>{$refs.allVerifyScanDialog.show();}">
                    <view class="all-text">查看全部</view>
                    <link-icon icon="mp-arrow-right"  class="all-icon"/>
                </view>
            </view>
            <!--弹窗-->
            <link-dialog ref="allVerifyScanDialog" >

                <view slot="head" class="headBox">
                    <view class="title">
                        {{scanParam.scanSubType===''&&scanParam.attr2==='claim'?'领用扫码':
                          scanParam.scanSubType===''&&scanParam.attr2==='feeHand'?'活动手动编辑':
                          scanParam.scanSubType==='OpenScan'&&scanParam.attr2==='feeVerify'?'活动开瓶扫码':
                          scanParam.scanSubType==='GiftScan'&&scanParam.attr2==='feeVerify'?'活动赠送扫码':
                          scanParam.scanSubType==='OpenScan'&&scanParam.attr2==='noFeeVerify'?'非费用开瓶扫码': '非费用赠送扫码'
                        }}
                    </view>
                    <link-icon icon="mp-close" class="close-icon" @tap="()=>{$refs.allVerifyScanDialog.hide();}"></link-icon>
                    <view class="divide"></view>
                </view>
                <scroll-view scroll-y="true" class="all-verify-scan-table" @scrolltolower="requestPage">
                    <view class="all-verify-scan-table-item">
                        <view class="company-region title">公司/区域名</view>
                        <view class="use-scan title">
                            {{scanParam.scanSubType===''&&scanParam.attr2==='claim'?'领用扫码':
                              scanParam.scanSubType===''&&scanParam.attr2==='feeHand'?'活动手动编辑':
                              scanParam.scanSubType==='OpenScan'&&scanParam.attr2==='feeVerify'?'活动开瓶扫码':
                              scanParam.scanSubType==='GiftScan'&&scanParam.attr2==='feeVerify'?'活动赠送扫码':
                              scanParam.scanSubType==='OpenScan'&&scanParam.attr2==='noFeeVerify'?'非费用开瓶扫码': '非费用赠送扫码'
                            }}(瓶)
                        </view>
                    </view>
                    <view class="all-verify-scan-table-item" v-for="item in allTableData">
                        <view class="company-region">{{ item.name }}</view>
                        <view class="use-scan">{{ item.value }}</view>
                    </view>
                </scroll-view>
            </link-dialog>
            <!-- echarts图表 -->
            <view class="area-activity-scan" v-if="allTableData.length>0">
                <view class="terminal-level-content">
                    <link-echart
                                 v-if="verifyScanCodeRows.length !== 0"
                                 :option="verifyScanCodeOption"
                                 :height="capacityLevelBarYCategoryHeight+'px'"
                                 :loading="loadingFlag"
                                 :force-use-old-canvas="false"
                    />
                    <view class="load-more" v-else >暂无数据</view>
                </view>
            </view>
            <view class="no-data" v-else>暂无数据</view>
        </view>
</template>

<script>
import LineTitle from "../../../../lzlj/components/line-title";
import SelectButton from "../../components/select-button";
import {variousAreaScanCode} from "../marketing-echart.utils";
import Taro from "@tarojs/taro";
export default {
    name: "area-activity-scan",
    components: {SelectButton, LineTitle},
    props: {
      orgId: {
          type:String,
          required:true
      },
      orgType: {
            // type: String,
            required: true
      },

    },
    data() {
        return {
            selectScanCode:[
                {name:'领用扫码',value:'claim',selectedFlag:true,attr2:'claim',scanSubType: ''},
                {name:'活动开瓶扫码',value:'feeVerify',selectedFlag:false,attr2:'feeVerify',scanSubType:'OpenScan'},
                {name:'活动赠送扫码',value:'feeVerify',selectedFlag:false,attr2:'feeVerify',scanSubType:'GiftScan'},
                {name:'活动手动编辑',value:'feeHand',selectedFlag:false,attr2:'feeHand',scanSubType: ''},
                {name:'非费用开瓶扫码',value:'noFeeVerify',selectedFlag:false,attr2:'noFeeVerify',scanSubType:'OpenScan'},
                {name:'非费用赠送扫码',value:'noFeeVerify',selectedFlag:false,attr2:'noFeeVerify',scanSubType:'GiftScan'},
            ],
            loadingFlag:false,  //加载
            verifyScanCodeRows: [], // 请求的数据
            verifyScanCodeOption:null,    //echarts柱状图option
            scanParam:{ attr2:'claim',scanSubType:''},   //活动类型参数
            capacityLevelBarYCategoryHeight: null,   //容量级别高度,图标高度
            allTableData: [],   //查看全部数据弹窗
            currentOrgId: this.orgId,
            currentPage: 1,
            total: 0
        }
    },
    created() {
        this.queryAllData();
    },
    watch: {
        orgId() {
            // watch监听props里status的变化，然后执行操作
            this.queryAllData();
        }
    },

    methods: {
        /**
         * @desc
         * <AUTHOR>
         * @date 2022/08/16 10:28
         * @desc 切换活动按钮
         **/
        changeScanType(attr2,scanSubType,index) {
            this.scanParam.attr2=attr2;
            this.scanParam.scanSubType=scanSubType;
            this.selectScanCode.forEach((item)=>{
                item.selectedFlag = false;
            })
            this.selectScanCode[index].selectedFlag = true;
            this.queryAllData();
        },

        /**
         * @desc
         * <AUTHOR>
         * @date 2022/08/16 10:28
         * @desc echarts柱状图,获取所有数据
         **/
        async queryAllData() {
            try {
                this.$utils.showLoading();
                this.currentPage = 1;
                this.verifyScanCodeOption = null;
                const data = await this.$http.post('export/link/codeScanBoard/countClaimVerifyScanByAreaPage', {
                    attr2: this.scanParam.attr2,
                    scanSubType: this.scanParam.scanSubType,
                    attr1: this.orgId,
                    page: this.currentPage,
                    rows: 10,
                    totalFlag: true,
                    // sort:this.scanParam.attr2 === 'claim' ? 'claimQty' :
                    //      this.scanParam.attr2 === 'feeHand' ? 'handQty' : 'verifyQty'
                });
                if (data.success) {
                    this.verifyScanCodeRows = data.rows;
                    this.total=data.total
                    let seriesData = [];
                    if(this.scanParam.attr2==='claim') {
                        seriesData = await this.handleData('claimQty')
                    };
                    if(this.scanParam.attr2==="feeVerify" || this.scanParam.attr2==="noFeeVerify") {
                        seriesData = await this.handleData('verifyQty')
                    };
                    if(this.scanParam.attr2==="feeHand") {
                        seriesData = await this.handleData('handQty')
                    };
                    this.capacityLevelBarYCategoryHeight = (40 + 36 * this.verifyScanCodeRows.length) >= 100 ? 40 + 36 * this.verifyScanCodeRows.length : 100;
                    this.verifyScanCodeOption = echartInitConfig => variousAreaScanCode(echartInitConfig, seriesData);
                    this.$utils.hideLoading();
                } else {
                    this.$utils.hideLoading();
                    this.$showError('查询接口失败，请稍后重试！' + data.result);
                }
            } catch (e) {
                this.$utils.hideLoading();
                this.$showError('查询接口失败！');
            }
        },

        /**
         * @desc
         * <AUTHOR>
         * @date 2022/08/31 10:28
         * @desc 处理数据
         **/
        async handleData(VerifySum) {
            try {
                this.verifyScanCodeRows.sort((a, b) => {
                    return Number(b[VerifySum]) - Number(a[VerifySum])
                })
                let seriesData = []
                this.allTableData = [];
                for (let i = 0, count = 0; i < this.verifyScanCodeRows.length; i++) {
                    let name = this.verifyScanCodeRows[i].orgName;
                    const scanValue = Number(this.verifyScanCodeRows[i][VerifySum]);
                    if (scanValue > 0) {
                        this.allTableData.push({
                            value: scanValue,
                            name: name
                        })
                    }
                    if (scanValue > 0 && count < 10) {
                        seriesData.push({
                            value: scanValue,
                            name: name
                        })
                        count++;
                    }
                }
                return seriesData;
            } catch (e) {
                this.$utils.hideLoading();
                this.$showError('查询值列表接口失败！');
            }
        },

        /**
         * @desc
         * <AUTHOR>
         * @date 2022/08/30 10:28
         * @desc 分页查询
         **/
        async requestPage() {
            if (this.allTableData.length >= this.total) return;
            try {
                const data = await this.$http.post('export/link/codeScanBoard/countClaimVerifyScanByAreaPage', {
                    attr2: this.scanParam.attr2,
                    scanSubType: this.scanParam.scanSubType,
                    attr1: this.orgId,
                    page: ++this.currentPage,
                    rows: 10,
                    totalFlag: true,
                    // sort:this.scanParam.attr2 === 'claim' ? 'claimQty' :
                    //      this.scanParam.attr2 === 'feeHand' ? 'handQty' : 'verifyQty'
                });
                if (data.success) {
                    const newRows = data.rows;
                    if (!newRows || newRows.length === 0) {
                        return;
                    }
                    for (let i = 0; i < newRows.length; i++) {
                        let name = newRows[i].orgName;
                        const scanValue = Number(newRows[i][this.scanParam.attr2 === 'claim' ? 'claimQty' :
                                                            this.scanParam.attr2 === 'feeHand' ? 'handQty' : 'verifyQty']);
                        this.allTableData.push({
                            value: scanValue,
                            name: name
                        })
                    }
                } else {
                    this.$showError('查询各类区域活动扫码情况接口失败，请稍后重试！' + data.result);
                }
            } catch (e) {
                this.$showError('查询各类区域活动扫码情况接口失败！');
            }
        }
    }
}
</script>

<style lang="scss">
.regionScanCode {
        //.boardBox{
        //    background: #ffffff;
        //    margin: 0 14px 24px;
        //    border-radius: 12px;
        //}

        background: #ffffff;
        margin-top:24px;
        border-radius: 12px;
    .various-scan-code {
            display: flex;
            margin-top: 24px;
            margin-left: 24px;
            max-width: 700px;
            flex-wrap: wrap;
            .select-button {
                overflow: hidden;
                height: 30px;
                border: none !important;
                font-family: PingFangSC-Regular;
                font-size: 28px;
                background-color: #F2F3F6;
                color: #666666;
                margin-bottom: 24px;
                //选中的颜色
                //background: #2F69F8;
            }
        }
    .top-all {
        display: flex;
        justify-content: space-between;
        .top {
            display: flex;
            height: 28px;
            //justify-content: center;
            .top-icon {
                margin:25px 16px 45px 24px;
                font-size: 10px;
                align-content: center;
                color: #2F69F8;
            }
            .top-text {
                margin-top: 16px;
                font-family: PingFangSC-Medium;
                font-size: 28px;
                color: #333333;
                line-height: 28px;
                font-weight: 500;
            }
        }
        .all {
            display: flex;
            font-family: PingFangSC-Regular;
            font-size: 26px;
            color: #2F69F8;
            font-weight: 400;
            .all-text {
                margin-top: 16px;
                line-height: 26px;
                align-content: center;
            }
            .all-icon {
                margin: 16px 20px 20px 20px;
                align-content: center;
            }
        }
    }

    .link-dialog-content {
        width:87% !important;
        border-radius: 16px !important;
    }

    .headBox {
        .title {
            position: relative;
            font-size: 32px;
            margin-bottom: 30px;
            color: #333333;
            font-weight: 500;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0px 36px 0px 36px;
        }
        .close-icon {
            position: absolute;
            right: 40px;
            top: 40px;
            color: #999;
            font-size: 32px;
        }
        .divide {
            width: 327*2px;
            height: 2px;
            margin: auto;
            background-color: #EEF3F5;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }



    .all-verify-scan-table {
        width: 100%;
        overflow: scroll;
        max-height: 900px;
        min-height: 240px;
    }

    .all-verify-scan-table-item {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 24px;
        color: #666;
        width: 100%;

        .title {
            background-color: #C8D7FA;
            color: #333;
            font-weight: 500;
        }

        .company-region, .use-scan {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            border: 1px solid #E6EAF4;
            flex-wrap: wrap;
            height: 80px;
            width:200px;
        }

        .use-scan {
            border-left: none;
        }
    }

    .area-activity-scan {
        border-radius: 12px;
        background: white;
        padding: 0px 18px 48px 30px ;
    }

    .no-data {
        display: flex;
        justify-content: center;
        align-items: center;
        height:200px;
        color: #D8D8D8;
        padding: 32px 0;
        font-size: 24px;
    }
}

</style>
