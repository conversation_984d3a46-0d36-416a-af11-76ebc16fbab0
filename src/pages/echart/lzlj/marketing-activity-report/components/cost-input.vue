<template>
    <view class="cost-input-page">
        <view class="cost-input-condition">
            <line-title title="费用总投入情况"></line-title>
            <view class="select-org-dimension">
                <select-button :downIcon="true" :label="orgName2" :selectedFlag="true"
                               @tap="chooseOrg('input')"></select-button>
            </view>
            <scroll-view class="scroll-view-data" scroll-x="true">
                <view class="select-dimension">
                    <select-button :key="index"
                                   :label="item.name"
                                   :selectedFlag="item.selectedFlag"
                                   :value="item.value"
                                   :module="'activityData'"
                                   @tap="chooseData($event,item,'input')" v-for="(item,index) in selectDimensionInput">
                    </select-button>
                </view>
            </scroll-view>
            <view class="cost-input-condition-content" v-show="showEchart">
                <view class="count-title">
                    <view class="cost-item">
                        <view class="label">金额累计</view>
                        <view class="data">{{costInputTotalPrice}}万元</view>
                    </view>
                    <view class="cost-item">
                        <view class="label">现金累计</view>
                        <view class="data">{{costInputCashTotal}}万元</view>
                    </view>
                    <view class="cost-item">
                        <view class="label">产品累计</view>
                        <view class="data">{{costInputProdTotal}}万元</view>
                    </view>
                </view>
                <link-echart :loading="loadingFlag" :option="costInputOption" :height="costQuotaOptionHeight + 'px'"
                             ref="costQuotaEchart"/>
            </view>
        </view>
        <view class="cost-quota">
            <line-title title="费用可用余额情况"></line-title>
            <view class="select-org-dimension">
                <select-button :downIcon="true" :label="orgName1" :selectedFlag="true"
                               @tap="chooseOrg('cost')"></select-button>
                <select-button :downIcon="true" :label="dealerName" :selectedFlag="true"
                               @tap="chooseDealer()"></select-button>
            </view>
            <scroll-view class="scroll-view-data" scroll-x="true">
                <view class="select-dimension">
                    <select-button :key="index"
                                   :label="item.name"
                                   :selectedFlag="item.selectedFlag"
                                   :value="item.value"
                                   :module="'activityData'"
                                   @tap="chooseData($event,item,'cost')" v-for="(item,index) in selectDimensionCost">
                    </select-button>
                </view>
            </scroll-view>
            <view class="cost-quota-content" v-show="showEchart">
                <view class="count-title">
                    <view :key="index" class="count-title-item" v-for="(item,index) in costQuota">
                        <view class="count-item" v-if="item.name === 'totalPrice'">
                            <view class="label">金额累计</view>
                            <view class="data">{{item.value.toFixed(4)}}万元</view>
                        </view>
                        <view class="count-item" v-else-if="item.name === 'Money'">
                            <view class="label">现金累计</view>
                            <view class="data">{{item.value.toFixed(4)}}万元</view>
                        </view>
                        <view class="count-item" v-else-if="item.name === 'Product'">
                            <view class="label">产品累计</view>
                            <view class="data">{{item.value.toFixed(4)}}万元</view>
                        </view>
                    </view>
                </view>
                <link-echart :loading="loadingFlag" :option="costQuotaOption" ref="costQuotaEchart"
                             :height="costQuotaOptionHeight + 'px'"/>
            </view>
        </view>
        <view v-show="changeChart"></view>
        <position-bottom :user-info="userInfo" :show.sync="dialogFlag" @choose="changeOrg"></position-bottom>
        <position-bottom :user-info="userInfo" :show.sync="dialogQuoFlag" @choose="changeQuoOrg"></position-bottom>
    </view>
</template>

<script>
  import LineTitle from "../../components/line-title";
  import SelectButton from "../../components/select-button";
  import PositionBottom from "../../components/position-bottom";
  import {targetPieChartProgress} from "../../echart.utils";

  export default {
      name: "cost-input",
      components: {SelectButton, LineTitle, PositionBottom},
      data() {
          const accessGroupOauth = this.$utils.getMenuAccessGroup('', '/pages/echart/lzlj/marketing-activity-report/market-act-report-page');
          return {
              accessGroupOauth,//访问组安全性
              showEchart: true,
              dialogFlag: false,
              dialogQuoFlag: false,
              orgName1: '全部片区',
              orgName2: '全部片区',
              dealerName: '全部经销商',
              postnId: '',               // 职位id
              orgChooseFlag: true,     // 是否可选择其他组织
              timeObj: {},               // 时间维度查询
              orgId1: '',                 // 默认经销商id
              orgId2: '',
              dealerData: {},            // 经销商信息
              selected: false,
              costQuotaOption: null,     // 费用可用余额情况
              costQuotaOptionHeight: (this.$device.systemInfo.windowWidth - 24) * 0.6285714 < 220 ? (this.$device.systemInfo.windowWidth - 24) * 0.6285714 : 220,
              costQuota: {},
              costInputTotalPrice: 0,   // 费用总投入金额
              costInputCashTotal: 0,    // 费用总投入现金累积
              costInputProdTotal: 0,    // 费用总投入产品累积
              loadingFlag: false,
              costInputOption: null,    // 费用总投入情况
              barYCategoryHeight: 0,
              selectDimensionCost: [
                  {name: '本周', value: 'week', selectedFlag: false},
                  {name: '本月', value: 'month', selectedFlag: true},
                  {name: '本季', value: 'quarter', selectedFlag: false},
                  {name: '本财年', value: 'year', selectedFlag: false}
              ],
              selectDimensionInput: [
                  {name: '本周', value: 'week', selectedFlag: false},
                  {name: '本月', value: 'month', selectedFlag: true},
                  {name: '本季', value: 'quarter', selectedFlag: false},
                  {name: '本财年', value: 'year', selectedFlag: false}
              ],
              activeButtonData: {},
              dealerOption: new this.AutoList(this, {
                  url: {
                      queryByExamplePage: 'action/link/accnt/queryFieldsByExamplePage'
                  },
                  stayFields: "id,acctType,acctCode,acctName",
                  param: {
                      filtersRaw: [
                          {id: 'acctType', property: 'acctType', value: '[Dealer,Distributor,Terminal]', operator: 'IN'}
                      ]
                  },
                  hooks: {
                      beforeLoad(option) {
                          delete option.param.order;
                          delete option.param.sort;
                      }
                  },
                  sortOptions: null,
                  searchFields: ['acctCode', 'acctName'],
          renderFunc: (h, {data, index}) => {
            return (
              <item key={index} data={data} arrow="false">
                <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                <view
                  style="width:100%">
                  <view
                    style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px;padding-bottom: 10px;">
                    {data.acctName}
                  </view>
                  <view
                    style="display:flex;font-family: PingFangSC-Regular;font-size: 12px;color: #262626;letter-spacing: 0;line-height: 14px;padding-bottom: 10px;">
                    <view style="color: #8C8C8C;min-width: 60px;">客户编码</view> {data.acctCode}
                  </view>
                  <view
                    style="display:flex;font-family: PingFangSC-Regular;font-size: 12px;color: #262626;letter-spacing: 0;line-height: 14px;">
                    <view style="color: #8C8C8C;min-width: 60px;">详细地址</view> {data.addrDetailAddr}
                  </view>
                </view>
              </item>
            )
          }
        })
      }
    },
    computed: {
      changeChart(){
        if(!this.showEchart && !this.dialogFlag && !this.dialogQuoFlag){
          this.$nextTick(() => {
            setTimeout(()=>{
              this.showEchart = true;
            }, 100);
          })
        }
      }
    },
    async mounted() {
      this.$utils.showLoading()
        this.userInfo = this.$taro.getStorageSync('token').result;
        if (this.userInfo.positionType === 'Salesman' || this.userInfo.positionType === 'SalesSupervisor' || this.userInfo.positionType === 'GroupBuyManager'
            || this.userInfo.positionType === 'AccountManager' || this.userInfo.positionType === 'CustServiceManager' || this.userInfo.positionType === 'VipManager'
            || this.userInfo.positionType === 'CustServiceSpecialist' || this.userInfo.positionType === 'CustServiceSupervisor' || this.userInfo.positionType === 'SalesTeamLeader') {
            this.postnId = this.userInfo.postnId;
            this.orgChooseFlag = false;
        }
        this.orgId1 = this.userInfo.orgId;
        this.orgId2 = this.userInfo.orgId;
        this.orgName1 = this.userInfo.orgName;
        this.orgName2 = this.userInfo.orgName;
        this.activeButtonData = this.selectDimensionCost[1];
        this.timeObj = this.$utils.getCurrentMonthDate();
        //2021-07-29配置了访问组安全性-默认不查询报表数据。切换组织时才查询
        if(this.$utils.isEmpty(this.accessGroupOauth)){
            const costQuotaData = await this.queryCostQuota();
            this.costQuotaOption = echartInitConfig => targetPieChartProgress(echartInitConfig, costQuotaData.seriesDataTemp, costQuotaData.totalSeriesData, ['47%', '70%'], '47%', [], 225, null, null, '（万元）', 5, 'smallSize');
            const costInputData = await this.queryCostInput();
            this.costInputOption = echartInitConfig => targetPieChartProgress(echartInitConfig, costInputData.seriesData, costInputData.totalSeriesData, ['47%', '70%'], '47%', [], 225, null, null, '（万元）', 5, 'smallSize');
        }
        this.$utils.hideLoading()
      },
      methods: {
          /**
           *  @description: 查询费用可用余额
           *  @author: 马晓娟
           *  @date: 2020/11/3 16:50
           */
          async queryCostQuota() {
              let attr1 = '';
              if (this.$utils.isEmpty(this.timeObj)) {
                  attr1 = 'month';
              }
              const data = await this.$http.post('action/link/costDetail/queryAvailableBalance', {
                  attr1: attr1,
                  orgId: this.orgId1,
                  // startTime: this.timeObj.startDate || '',
                  endTime: this.timeObj.endDate || '',
                  feeReimCode: this.dealerData.acctCode || ''
              });
              if (data.success) {
                  // 按照活动类型分类 饼图的维度
                  const temp = data.rows.map(item => ({
                      name: item.costTypeName || '其他费用',
                      value: item.availableBalance / 10000 || 0
                  }));
                  const costItemData = [...temp.reduce((m, x) => m.set(x.name, (m.get(x.name) || 0) + x.value), new Map())]
                      .map(([name, value]) => ({name, value}));
                  // 按照支付类型分类维度
                  const countTemp = data.rows.map(item => ({
                      name: item.costPaymentWay,
                      type: item.payType || 'other',
                      value: item.availableBalance / 10000 || 0
                  }));
                  const costAllData = [...countTemp.reduce((m, x) => m.set(x.type, (m.get(x.type) || 0) + x.value), new Map())]
                      .map(([name, value]) => ({name, value}));
                  // 计算总计金额
                  let totalPrice = costAllData.reduce((totalPriceTemp, item) => totalPriceTemp + item.value, 0);
                  // 去除金额为0的数据
                  let seriesData = costItemData.sort(this.compare('value')).filter((item) => item.value !== 0);
                  // 取排序前6的数据，其他数据均归为一类，列入其他费用中
                  let seriesDataTemp = [];
                  if (seriesData.length > 7) {
                      seriesDataTemp = seriesData.slice(0, 6);
                      let others = seriesData.slice(6, seriesData.length);
                      let othersTotal = others.reduce((total, item) => total + item.value, 0);
                      let otherItem = seriesDataTemp.filter((item) => item.name === '其他费用');
                      seriesDataTemp.forEach((item) => {
                          if (item.name === '其他费用') {
                              item.value = item.value + othersTotal;
                          }
                      });
                      if (otherItem.length < 1) {
                          seriesDataTemp = seriesDataTemp.concat([{name: '其他费用', value: othersTotal}]);
                      }
                  } else {
                      seriesDataTemp = seriesData;
                  }
                  let totalSeriesData = [{value: totalPrice.toFixed(4), name: '费用可用余额'}];
                  // 总计金额小于等于0，则说明数据为空，则为以下提示
                  if (totalPrice <= 0) {
                      totalSeriesData = [{value: 0, name: '暂未统计到数据'}]
                  }
                  costAllData.unshift({name: 'totalPrice', value: totalPrice});
                  this.costQuota = costAllData.filter(item => item.name !== 'other');
                  return {seriesDataTemp: seriesDataTemp, totalSeriesData: totalSeriesData};
              } else {
                  this.$showError(`查询费用可用余额失败！${data.result}`);
              }
          },
          /**
           *  @description: 查询费用投入情况
           *  @author: 马晓娟
           *  @date: 2020/11/4 10:34
           */
          async queryCostInput() {
              let param = {
                  attr1: this.timeObj.startDate,
                  attr2: this.timeObj.endDate,
                  // attr3: this.dealerData.id || '',
                  orgId: this.orgId2,
                  postnId: this.postnId
              };
              if (this.$utils.isEmpty(this.postnId)) {
                  delete param.postnId;
              }
              const data = await this.$http.post('action/link/actualFee/sumActualAmountByCostType', param);
              if (data.success) {
                  const dataTemp = data.rows.map(item => ({
                      name: item.key || '其他费用',
                      value: item.amount / 10000
                  }));
                  let seriesData = dataTemp.sort(this.compare('value')).filter((item) => item.value !== 0);
                  let totalPrice = seriesData.reduce((totalPriceTemp, item) => totalPriceTemp + item.value, 0);
                  const costInputCashTotalTemp = data.rows.reduce((totalCash, item) => totalCash + item.cashRealAmount, 0);
                  this.costInputCashTotal = (costInputCashTotalTemp / 10000).toFixed(4);
                  const costInputProdTotalTemp = data.rows.reduce((totalProd, item) => totalProd + item.prodRealAmount, 0);
                  this.costInputProdTotal = (costInputProdTotalTemp / 10000).toFixed(4);
                  let seriesDataTemp = [];
                  if (seriesData.length > 7) {
                      seriesDataTemp = seriesData.slice(0, 6);
                      let others = seriesData.slice(6, seriesData.length);
                      let othersTotal = others.reduce((total, item) => total + item.value, 0);
                      let otherItem = seriesDataTemp.filter((item) => item.name === '其他费用');
                      seriesDataTemp.forEach((item) => {
                          if (item.name === '其他费用') {
                              item.value = item.value + othersTotal;
                          }
                      });
                      if (otherItem.length < 1) {
                          seriesDataTemp = seriesDataTemp.concat([{name: '其他费用', value: othersTotal}]);
                      }
                  } else {
                      seriesDataTemp = seriesData;
                  }
                  let totalSeriesData = [{value: totalPrice.toFixed(4), name: '费用总投入'}];
                  if (totalPrice <= 0) {
                      totalSeriesData = [{value: 0, name: '暂未统计到数据'}]
                  }
                  this.costInputTotalPrice = totalPrice.toFixed(4);
                  return {seriesData: seriesDataTemp, totalSeriesData: totalSeriesData};
              } else {
                  this.$showError(`查询费用总投入失败！${data.result}`);
              }
          },
          /**
           *  @description: 数组对象根据某一属性值排序
           *  @author: 马晓娟
           *  @date: 2020/11/3 22:51
           *  @param property 参与排序的属性
           */
          compare(property) {
              return function (a, b) {
                  let value1 = a[property];
                  let value2 = b[property];
                  return value2 - value1;
              }
          },
          /**
           *  @description: 选择筛选维度
           *  @author: 马晓娟
           *  @date: 2020/10/28 11:54
           */
          async chooseData($event, data, type) {
              if(this.loadingFlag)return;
              this.timeObj = $event;
              if (type === 'cost') {
                  this.selectDimensionCost.forEach((item) => {
                      if (item.value === data.value) {
                          data.selectedFlag = true;
                          if (data.selectedFlag) this.activeButtonData = data;
                      } else {
                          item.selectedFlag = false;
                      }
                  });
                  const costQuotaData = await this.queryCostQuota();
                  this.costQuotaOption = echartInitConfig => targetPieChartProgress(echartInitConfig, costQuotaData.seriesDataTemp, costQuotaData.totalSeriesData, ['47%', '70%'], '47%', [], 225, null, null, '（万元）', 5, 'smallSize');
              } else {
                  this.selectDimensionInput.forEach((item) => {
                      if (item.value === data.value) {
                          data.selectedFlag = true;
                          if (data.selectedFlag) this.activeButtonData = data;
                      } else {
                          item.selectedFlag = false;
                      }
                  });
                  const costInputData = await this.queryCostInput();
                  this.costInputOption = echartInitConfig => targetPieChartProgress(echartInitConfig, costInputData.seriesData, costInputData.totalSeriesData, ['47%', '70%'], '47%', [], 225, null, null, '（万元）', 5, 'smallSize');
              }
          },
          chooseOrg(type) {
            //2021-07-29如果没有配置访问组安全性则走原本的逻辑
            if(this.$utils.isEmpty(this.accessGroupOauth)){
              if (!this.orgChooseFlag && type === 'input') return;
            }
            this.showEchart = false;
            if(type === 'input'){
              this.dialogFlag = true;
            }else{
              this.dialogQuoFlag = true;
            }
          },
          async changeQuoOrg(item) {
            if(Object.keys(item).length === 0)return;
            this.orgName1 = item.text;
            this.orgId1 = item.id;
            const costQuotaData = await this.queryCostQuota();
            this.costQuotaOption = echartInitConfig => targetPieChartProgress(echartInitConfig, costQuotaData.seriesDataTemp, costQuotaData.totalSeriesData, ['47%', '70%'], '47%', [], 225, null, null, '（万元）', 5, 'smallSize');
          },
          /**
           *  @description: 选择片区维度查看报表数据
           *  @author: 马晓娟
           *  @date: 2020/11/3 16:56
           *  2021-07-29 更新匹配访问组安全性
           */
          async changeOrg(item) {
            if(Object.keys(item).length === 0)return;
            //2021-07-29 配置了访问组或者this.orgChooseFlag && type === 'input'的情况
            this.orgName2 = item.text;
            this.orgId2 = item.id;
            const costInputData = await this.queryCostInput();
            this.costInputOption = echartInitConfig => targetPieChartProgress(echartInitConfig, costInputData.seriesData, costInputData.totalSeriesData, ['47%', '70%'], '47%', [], 225, null, null, '（万元）', 5, 'smallSize');
          },
          /**
           *  @description: 选择公司维度查看报表数据
           *  @author: 马晓娟
           *  @date: 2020/11/3 16:56
           */
          async chooseDealer() {
              const item = await this.$object(this.dealerOption);
              this.dealerName = item.acctName;
              this.dealerData = item;
              const costQuotaData = await this.queryCostQuota();
              this.costQuotaOption = echartInitConfig => targetPieChartProgress(echartInitConfig, costQuotaData.seriesDataTemp, costQuotaData.totalSeriesData, ['47%', '70%'], '47%', [], 225, null, null, '（万元）', 5, 'smallSize');
              // const costInputData = await this.queryCostInput();
              // this.costInputOption = echartInitConfig => targetPieChartProgress(echartInitConfig, costInputData.seriesData, costInputData.totalSeriesData,['47%', '70%'],'47%',[],225,null,null,'（万元）');
          }
      }
  }
</script>

<style lang="scss">
    .cost-input-page {
        padding: 0px 24px 0 24px;

        .scroll-view-data {
            margin-top: 24px;

            .select-dimension {
                display: flex;
            }
        }

        .select-org-dimension {
            display: flex;
            margin-top: 24px;
        }

        .count-title {
            font-family: PingFangSC-Regular;
            font-size: 24px;
            color: #262626;
            letter-spacing: 0;
            line-height: 24px;
            display: flex;
            justify-content: center;

            .count-title-item {
                justify-content: center;
                width: 33%;

                .count-item {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;

                    .label {
                        text-align: center;
                        margin-bottom: 12px;
                    }

                    .data {
                        text-align: center;
                    }
                }
            }

            .cost-item {
                display: flex;
                flex-direction: column;
                justify-content: center;
                width: 33%;

                .label {
                    text-align: center;
                    margin-bottom: 12px;
                }

                .data {
                    text-align: center;
                }
            }
        }

        .cost-quota-content, .cost-input-condition-content {
            border: 1px solid #EBEDF5;
            border-radius: 16px;
            margin: 24px 0;
            padding: 24px 0;
        }

        .cost-input-loading {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(white, 0.75);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24PX;
            color: $color-primary;
        }
    }
</style>
