<template>
    <link-page class="activity-overview">
        <line-title title="活动概览"></line-title>
        <view v-if="showFlag">
        <view class="roundBox">
           <!-- 图表-->
            <view class="round">
                <view class="oneRow">
                    {{actOverview.actTotalNum}}
                </view>
                <view class="twoRow">
                    活动总场次
                </view>
            </view>
        </view>
            <!--  下部文字描述      -->
        <view class="dataBox">
            <view class="row">
                <view style="flex: 1;text-align: center;">
                    <view class="num">
                        {{actOverview.actProcessNum}}
                    </view>
                    <view class="text">
                        进行中活动
                    </view>
                </view>
                <view class="line"></view>
                <view style="flex: 1;text-align: center;">
                    <view class="num">
                        {{actOverview.actActualNum}}
                    </view>
                    <view class="text">
                        已实发活动
                    </view>
                </view>
            </view>
            <view class="row MP">
                <view style="flex: 1;text-align: center;">
                    <view class="num">
                        {{actOverview.applyAmount}}
                    </view>
                    <view class="text">
                        活动申请总额{{(dateIndex === 1 ||dateIndex === 2)?'（万）':''}}
                    </view>
                </view>
                <view class="line"></view>
                <view style="flex: 1;text-align: center;">
                    <view class="num">
                        {{actOverview.actualAmount}}
                    </view>
                    <view class="text">
                        活动实发总额{{(dateIndex === 1 ||dateIndex === 2)?'（万）':''}}
                    </view>
                </view>
            </view>
        </view>
        </view>
        <view class="no-data" v-else>
            <text>暂无数据</text>
        </view>
    </link-page>
</template>

<script>
import LineTitle from "../../../../lzlj/components/line-title";
export default {
    name: "activity-overview",
    components: {LineTitle},
    props:{
        dateIndex:{
            type: Number,
            default: 0
        },
        orgId:{
            type: String,
            default: ''
        },
        ouathFlag:{
            type: Boolean,
            default: false
        },
        userObj:{
            type: Object,
            default: {}
        }
    },
    data(){

      return{
          actOverview:{},
          dateList:['week','month','fiscal','lastFiscal'],
          showFlag:true
      }
    },
    created() {
        this.queryData();
    },
    watch:{
        dateIndex(){
            this.queryData();
        },
        orgId(){
            this.queryData();
        },
    },
    methods:{
        /**
         * @description 判断是否为0;
         */
        isZero(data){
            if(data===0||data==='0'){
                return true;
            }else{
                return false;
            }
        },
       async queryData() {
           let param = {};
           if(this.ouathFlag){
               param={
                   filtersRaw: [
                       {id: "timeRange", property: "timeRange", value: this.dateList[this.dateIndex], operator: "="},
                       {id: "statisticType", property: "statisticType", value: 'MY_ORG', operator: "="},
                       {id: "actOrgId", property: "actOrgId", value: this.orgId, operator: "="},
                       {id: "boardType", property: "boardType", value: 'actOverview', operator: "="},
                   ]
               }
           }else{
               param={
                   filtersRaw: [
                       {id: "timeRange", property: "timeRange", value: this.dateList[this.dateIndex], operator: "="},
                       {id: "statisticType", property: "statisticType", value: 'MY_POSTN', operator: "="},
                       {id: "actPostnId", property: "actPostnId", value: this.userObj.postnId, operator: "="},
                       {id: "boardType", property: "boardType", value: 'actOverview', operator: "="},
                   ]
               }
           }
            try{
                const data = await this.$http.post('export/link/marketActBoard/queryByExamplePage', param)
                if(this.$utils.isNotEmpty(data.rows)){
                    this.actOverview = {
                        actTotalNum:data.rows[0].actTotalNum?data.rows[0].actTotalNum:0,
                        applyAmount:data.rows[0].applyAmount?this.handleAmount(data.rows[0].applyAmount):0,
                        actualAmount:data.rows[0].actualAmount?this.handleAmount(data.rows[0].actualAmount):0,
                        actProcessNum:data.rows[0].actProcessNum?data.rows[0].actProcessNum:0,
                        actActualNum:data.rows[0].actActualNum?data.rows[0].actActualNum:0,
                    }
                    if(this.isZero(data.rows[0].actTotalNum)&&this.isZero(data.rows[0].applyAmount)&&this.isZero(data.rows[0].actualAmount)
                        &&this.isZero(data.rows[0].actProcessNum)&&this.isZero(data.rows[0].actActualNum)){
                        this.showFlag=false;
                    }else{
                        this.showFlag=true;
                    }
                }else{
                    this.actOverview = {
                        actTotalNum:0,
                        applyAmount:0,
                        actualAmount:0,
                        actProcessNum:0,
                        actActualNum:0,
                    }
                    this.showFlag=false;
                }
            }catch(e){
                this.$showError(String(e))
            }
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/11/1
         * @methods handleAmount
         * @para
         * @description 将金额转化为以万为单位并格式化金额 100000000=》1,0000
         */
        handleAmount(num){
            if(this.dateIndex === 0) return num;
            let numWanStr= Math.round(num/10000).toString();
            return numWanStr.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        }
    }
}
</script>

<style lang="scss">
.activity-overview{
    .no-data {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 24px;
        height: 310px;
        color: #D8D8D8;
    }
    .roundBox{
        margin: 32px 0px;
        display: flex;
        justify-content: center;
        .round{
            display: flex;
            width: 260px;
            height: 260px;
            border-radius: 50%;
            border: 24px solid #EDF2FF;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            .oneRow{
                color: #2F69F8;
                font-size: 52px;
            }
            .twoRow{
                margin-top: 8px;
                font-size: 32px;
            }
        }
    }
    .dataBox{
        display: flex;
        flex-direction: column;
        margin-bottom: 20px;
         .row{
             flex: 1;
             display: flex;
             align-items: center;
         }
        .num{
            font-size: 36px;
            font-weight: 600;
        }
        .text{
            margin-top: 12px;
            font-size: 26px;
            font-weight: 400;
            color: #666666;
        }
        .line{
            background: #DDDDDD;
            width: 2px;
            height: 40px;
        }
        .MP{
            margin-top: 40px;
        }
    }
}
</style>
