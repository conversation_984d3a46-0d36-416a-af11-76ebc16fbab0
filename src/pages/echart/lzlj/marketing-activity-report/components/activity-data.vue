<template>
    <view class="activity-data-page">
        <line-title title="活动场次概览"></line-title>
        <view class="select-org-dimension" v-if="orgIdChooseShow">
            <select-button :downIcon="true" :label="orgData[0].orgName" :selectedFlag="true"
                           @tap="chooseOrg(0)"></select-button>
            <!--      <select-button :downIcon="true" :label="dealerData[0].dealerName" :selectedFlag="true"-->
            <!--                     @tap="chooseDealer(0)"></select-button>-->
        </view>
        <scroll-view scroll-x="true" class="scroll-view-data" v-if="!orgIdChooseShow">
            <view class="select-dimension">
                <select-button style="text-align: center;" :downIcon="true" :label="areaData[0].provinceName"
                               :selectedFlag="true" @tap="chooseProvince(0)"></select-button>
                <select-button style="text-align: center;" :downIcon="true" :label="areaData[0].cityName"
                               :selectedFlag="true" @tap="chooseCity(0)"></select-button>
                <select-button style="text-align: center;" :downIcon="true" :label="areaData[0].districtName"
                               :selectedFlag="true" @tap="chooseDistrict(0)"></select-button>
            </view>
        </scroll-view>
        <scroll-view scroll-x="true" class="scroll-view-data">
            <view class="select-dimension" v-if="selectDimensionData[0] && selectDimensionData[0].data">
                <select-button :key="index"
                               :label="item.name"
                               :selectedFlag="item.selectedFlag"
                               :value="item.value"
                               :module="'activityData'"
                               @tap="chooseData($event,item, 0)" v-for="(item,index) in selectDimensionData[0].data">
                </select-button>
            </view>
        </scroll-view>
        <view class="count-data">
            <view class="count-data-head">
                <view class="count-data-item-value">{{totalAct}}</view>
                <view class="count-data-item-label">活动总场次</view>
            </view>
            <view class="count-data-content">
                <view class="count-data-list" v-for="(item, index) in countActPreviewData" :key="index">
                    <view class="count-data-item">
                        <view class="count-data-item-value">{{item.value}}</view>
                        <view class="count-data-item-label">{{item.key | lov('MC_STATUS')}}</view>
                    </view>
                    <view class="line" v-if="index < (countActPreviewData.length-1)"></view>
                </view>
            </view>
        </view>
        <view class="activity-condition">
            <line-title title="活动场次情况"></line-title>
            <view class="select-org-dimension" v-if="orgIdChooseShow">
                <select-button :downIcon="true" :label="orgData[1].orgName" :selectedFlag="true"
                               @tap="chooseOrg(1)"></select-button>
                <!--        <select-button :downIcon="true" :label="dealerData[1].dealerName" :selectedFlag="true"-->
                <!--                       @tap="chooseDealer(1)"></select-button>-->
            </view>
            <scroll-view scroll-x="true" class="scroll-view-data" v-if="!orgIdChooseShow">
                <view class="select-dimension">
                    <select-button style="text-align: center;" :downIcon="true" :label="areaData[1].provinceName"
                                   :selectedFlag="true" @tap="chooseProvince(1)"></select-button>
                    <select-button style="text-align: center;" :downIcon="true" :label="areaData[1].cityName"
                                   :selectedFlag="true" @tap="chooseCity(1)"></select-button>
                    <select-button style="text-align: center;" :downIcon="true" :label="areaData[1].districtName"
                                   :selectedFlag="true" @tap="chooseDistrict(1)"></select-button>
                </view>
            </scroll-view>
            <scroll-view scroll-x="true" class="scroll-view-data">
                <view class="select-dimension" v-if="selectDimensionData[1] && selectDimensionData[1].data">
                    <select-button label="举办场次" :selectedFlag="activityFlag"
                                   @tap="chooseData($event, 'held', 1)"></select-button>
                    <select-button label="实发场次" :selectedFlag="!activityFlag"
                                   @tap="chooseData($event, 'closed', 1)"></select-button>
                    <select-button :key="index"
                                   :label="item.name"
                                   :selectedFlag="item.selectedFlag"
                                   :value="item.value"
                                   :module="'activityData'"
                                   @tap="chooseData($event,item, 1)"
                                   v-for="(item,index) in selectDimensionData[1].data">
                    </select-button>
                </view>
            </scroll-view>
            <view class="activity-condition-content" v-show="showEchart">
                <link-echart v-if="activityRows.length>0" :option="activityOption" :height="actBarYHeight+'px'"
                             ref="costQuotaEchart" :loading="loadingFlag"/>
                <view class="echart-no-more" v-else>
                    暂无数据
                </view>
            </view>
        </view>
        <view class="all-year-activity-condition">
            <line-title title="全年活动场次趋势"></line-title>
            <view class="select-org-dimension">
                <select-button :downIcon="true" :label="orgData[2].orgName" :selectedFlag="true"
                               @tap="chooseOrg(2)"/>
                <select-button :downIcon="true" :label="feeType.name" :selectedFlag="true"
                               @tap="chooseFeeType()"/>
                <!--        <select-button :downIcon="true" :label="dealerData[2].dealerName" :selectedFlag="true"-->
                <!--                       @tap="chooseDealer(2)"></select-button>-->
            </view>
            <view class="all-year-activity-condition-content" v-show="showEchart">
                <link-echart :option="allYearActOption" ref="costQuotaEchart" :loading="loadingFlag"/>
            </view>
        </view>
        <view class="activity-terminal-condition">
            <line-title title="活动覆盖终端统计"></line-title>
            <view class="select-org-dimension" v-if="selectDimensionData[3] &&selectDimensionData[3].data">
                <select-button :downIcon="true" :label="orgData[3].orgName" :selectedFlag="true"
                               @tap="chooseOrg(3)"></select-button>
                <!--        <select-button :downIcon="true" :label="dealerData[3].dealerName" :selectedFlag="true"-->
                <!--                       @tap="chooseDealer(3)"></select-button>-->
            </view>
            <scroll-view scroll-x="true" class="scroll-view-data">
                <view class="select-dimension">
                    <select-button :key="index"
                                   :label="item.name"
                                   :selectedFlag="item.selectedFlag"
                                   :value="item.value"
                                   :module="'activityData'"
                                   @tap="chooseData($event,item, 3)"
                                   v-for="(item,index) in selectDimensionData[3].data">
                    </select-button>
                </view>
            </scroll-view>
            <view class="activity-terminal-condition-content" v-show="showEchart">
                <link-echart v-if="actTerminalRows.length>0" :option="actTerminalOption"
                             :height="terminalBarYHeight+'px'" ref="costQuotaEchart" :loading="loadingFlag"/>
                <view class="echart-no-more" v-else>
                    暂无数据
                </view>
            </view>
        </view>
        <view class="activity-check-condition">
            <line-title title="活动稽核情况"></line-title>
            <view class="select-org-dimension" v-if="orgIdChooseShow">
                <select-button :downIcon="true" :label="orgData[4].orgName" :selectedFlag="true"
                               @tap="chooseOrg(4)"></select-button>
                <!--        <select-button :downIcon="true" :label="dealerData[4].dealerName" :selectedFlag="true"-->
                <!--                       @tap="chooseDealer(4)"></select-button>-->
            </view>
            <scroll-view scroll-x="true" class="scroll-view-data" v-if="!orgIdChooseShow">
                <view class="select-dimension">
                    <select-button style="text-align: center;" :downIcon="true" :label="areaData[2].provinceName"
                                   :selectedFlag="true" @tap="chooseProvince(2)"></select-button>
                    <select-button style="text-align: center;" :downIcon="true" :label="areaData[2].cityName"
                                   :selectedFlag="true" @tap="chooseCity(2)"></select-button>
                    <select-button style="text-align: center;" :downIcon="true" :label="areaData[2].districtName"
                                   :selectedFlag="true" @tap="chooseDistrict(2)"></select-button>
                </view>
            </scroll-view>
            <scroll-view scroll-x="true" class="scroll-view-data">
                <view class="select-dimension" v-if="selectDimensionData[4] &&selectDimensionData[4].data">
                    <select-button label="已稽核" :selectedFlag="checkFlag"
                                   @tap="chooseData($event, 'Audited', 4)"></select-button>
                    <select-button label="未稽核" :selectedFlag="!checkFlag"
                                   @tap="chooseData($event, 'Unaudit', 4)"></select-button>
                    <select-button :key="index"
                                   :label="item.name"
                                   :selectedFlag="item.selectedFlag"
                                   :value="item.value"
                                   :module="'activityData'"
                                   @tap="chooseData($event,item, 4)"
                                   v-for="(item,index) in selectDimensionData[4].data">
                    </select-button>
                </view>
            </scroll-view>
            <view class="activity-check-condition-content" v-show="showEchart">
                <link-echart v-if="actCheckRows.length>0" :option="actCheckOption" :height="actCheckBarYHeight+'px'"
                             ref="costQuotaEchart" :loading="loadingFlag"/>
                <view class="echart-no-more" v-else>
                    暂无数据
                </view>
            </view>
        </view>
      <position-bottom ref="ptbm" :user-info="userInfo" :show.sync="dialogFlag" @choose="changeOrg"></position-bottom>
    </view>
</template>

<script>
  import LineTitle from "../../components/line-title";
  import SelectButton from "../../components/select-button";
  import PositionBottom from "../../components/position-bottom";
  import {
    targetComposeLineAndHistogramProgress, barYCategory, targetPieChartProgress
  } from "../../echart.utils";

  export default {
      name: "activity-data",
      components: {SelectButton, LineTitle, PositionBottom},
      data() {
          let selectDimensionData = [], orgData = [], timeData = [], dealerData = [], tempIdArr = [], orgIdArr = {},
              areaData = [{provinceName: '省', cityName: '市', districtName: '区县'}, {
                  provinceName: '省',
                  cityName: '市',
                  districtName: '区县'
              }, {provinceName: '省', cityName: '市', districtName: '区县'}];
          const date = this.$utils.getCurrentMonthDate();
          const userInfo = this.$taro.getStorageSync('token').result;
          for (let i = 0; i <= 4; i++) {
              selectDimensionData.push({
                  data: [{name: '本周', value: 'week', selectedFlag: false},
                      {name: '本月', value: 'month', selectedFlag: true},
                      {name: '本季', value: 'quarter', selectedFlag: false},
                      {name: '本财年', value: 'year', selectedFlag: false}]
              });
              tempIdArr.push(userInfo.orgId);
              orgIdArr[i] = [];
              timeData.push(date);
              orgData.push({orgName: userInfo.orgName, orgId: userInfo.orgId});
              dealerData.push({dealerName: '全部经销商', dealerId: ''});
          }
          const accessGroupOauth = this.$utils.getMenuAccessGroup('', '/pages/echart/lzlj/marketing-activity-report/market-act-report-page');
          return {
              showEchart: true,
              accessGroupOauth,//访问组安全性
              feeType: {
                  name: '全部费用小类',
                  code: ''
              },
              dialogFlag: false,
              activityRows: [],
              actBarYHeight: 0,
              terminalBarYHeight: 0,
              actTerminalRows: [],
              actCheckRows: [],
              actCheckBarYHeight: 0,
              tempIdArr,
              orgIdArr,
              areaData,                  // 区域 省市县
              postnId: '',               // 职位id
              orgChooseFlag: true,     // 是否可选择其他组织
              chooseIndex: 0, // 选择的是第几个
              orgIdChooseShow: true,     // 是否展示组织筛选
              checkFlag: true,          // 是否稽核flag
              activityFlag: true,       // 举办场次或结案场次查询flag
              orgData,
              dealerData,
              timeData,
              userInfo,                 // 用户信息
              countActPreviewData: [{key: 'New', value: '-'}, {key: 'Published', value: '-'}, {
                  key: 'Processing',
                  value: '-'
              }, {key: 'Closed', value: '-'}, {key: 'ActualAmount', value: '-'}], // 活动场次概览情况
              totalAct: 0,             // 活动总场次
              loadingFlag: false,
              activityOption: null,
              allYearActOption: null,
              actTerminalOption: null,
              actCheckOption: null,
              selectDimensionData,
        dealerOption: new this.AutoList(this, {
          url: {
            queryByExamplePage: 'action/link/accnt/queryFieldsByExamplePage'
          },
          stayFields: "id,acctType,acctCode,acctName",
          param: {
            filtersRaw: [
              {id: 'acctType', property: 'acctType', value: '[Dealer,Distributor,Terminal]', operator: 'IN'}
            ]
          },
          hooks: {
            beforeLoad(option) {
              delete option.param.order;
              delete option.param.sort;
            }
          },
          sortOptions: null,
          searchFields: ['acctCode', 'acctName'],
          renderFunc: (h, {data, index}) => {
            return (
              <item key={index} data={data} arrow="false">
                <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                <view
                  style="width:100%">
                  <view
                    style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px;padding-bottom: 10px;">
                    {data.acctName}
                  </view>
                  <view
                    style="display:flex;font-family: PingFangSC-Regular;font-size: 12px;color: #262626;letter-spacing: 0;line-height: 14px;padding-bottom: 10px;">
                    <view style="color: #8C8C8C;min-width: 60px;">客户编码</view> {data.acctCode}
                  </view>
                  <view
                    style="display:flex;font-family: PingFangSC-Regular;font-size: 12px;color: #262626;letter-spacing: 0;line-height: 14px;">
                    <view style="color: #8C8C8C;min-width: 60px;">详细地址</view> {data.addrDetailAddr}
                  </view>
                </view>
              </item>
            )
          }
        }),
        provinceOption: new this.AutoList(this, {
          module: 'action/link/alladdress',
          param: {
            filtersRaw: [
              {id: 'addrType', property: 'addrType', value: 'State', operator: '='}
            ]
          },
          hooks: {},
          sortOptions: null,
          searchFields: ['addrName'],
          renderFunc: (h, {data, index}) => {
            return (
              <item key={index} data={data} arrow="false">
                <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                <view
                  style="width:100%">
                  <view
                    style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px;padding-bottom: 10px;">
                    {data.addrName}
                  </view>
                </view>
              </item>
            )
          }
        }),
        cityOption: new this.AutoList(this, {
          module: 'action/link/alladdress',
          param: {
            filtersRaw: [
              {id: 'addrType', property: 'addrType', value: 'City', operator: '='},
              {id: 'parentRealName', property: 'parentRealName', value: '', operator: '='}
            ]
          },
          hooks: {},
          sortOptions: null,
          searchFields: ['addrName'],
          renderFunc: (h, {data, index}) => {
            return (
              <item key={index} data={data} arrow="false">
                <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                <view
                  style="width:100%">
                  <view
                    style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px;padding-bottom: 10px;">
                    {data.addrName}
                  </view>
                </view>
              </item>
            )
          }
        }),
        districtOption: new this.AutoList(this, {
          module: 'action/link/alladdress',
          param: {
            filtersRaw: [
              {id: 'addrType', property: 'addrType', value: 'County', operator: '='},
              {id: 'parentRealName', property: 'parentRealName', value: '', operator: '='}
            ]
          },
          sortOptions: null,
          searchFields: ['addrName'],
          renderFunc: (h, {data, index}) => {
            return (
              <item key={index} data={data} arrow="false">
                <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                <view
                  style="width:100%">
                  <view
                    style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px;padding-bottom: 10px;">
                    {data.addrName}
                  </view>
                </view>
              </item>
            )
          }
        }),
        feeTypeOption: new this.AutoList(this, {
          module: 'action/link/feeType',
          param: {
            filtersRaw: [
              {id: 'effectiveFlag', property: 'effectiveFlag', value: 'Y', operator: '='},
              {id: 'costTypeLevel', property: 'costTypeLevel', value: '3', operator: '='}
            ]
          },
          hooks: {},
          sortOptions: null,
          searchFields: ['costTypeName'],
          renderFunc: (h, {data, index}) => {
            return (
              <item key={index} data={data} arrow="false">
                <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                <view
                  style="width:100%">
                  <view
                    style="font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px;padding-bottom: 10px;">
                    {data.costTypeName}
                  </view>
                </view>
              </item>
            )
          }
        })
      }
    },
    watch: {
      dialogFlag(val){
       if(val){
         this.showEchart = false;
       } else{
         this.$nextTick(()=>{
           setTimeout(()=>{
             this.showEchart = true;
           }, 100);
         });
       }
      }
    },
    mounted() {
      // 当前登录人职位类型=稽核人员AuditStaff、财务人员FinanceStaff、纪检人员InspectionStaff时，
      // 按照当前登录人职位的稽核人员安全性与活动头上的归属公司和省-市-区县匹配，活动场次概览、各类活动举办/实发场次统计、活动稽核情况，
      // 区域的查询条件改为省、市、区县三个查询条件，默认为空，查询时匹配活动上的省、市、区县进行查询；
      if (this.userInfo.positionType === 'AuditStaff' || this.userInfo.positionType === 'FinanceStaff' || this.userInfo.positionType === 'InspectionStaff') {
        this.orgIdChooseShow = false;
      }
      // 当前登陆用户的职位的职位类型=业务代表Salesman、业务主管SalesSupervisor、团购经理GroupBuyManager、客户经理AccountManager、
      // 客服经理CustServiceManager、VIP经理VipManager、客服专员CustServiceSpecialist、客服主管CustServiceSupervisor、小组组长SalesTeamLeader时，
      // 费用总投入表、活动场次概览、各类活动举办/实发场次统计、活动服务终端情况统计、活动趋势统计、活动稽核情况统计表，按照职位及下级职位统计；
      // 对应的组织查询条件固定为当前登陆用户的组织，且不可选择
      if (this.userInfo.positionType === 'Salesman' || this.userInfo.positionType === 'SalesSupervisor' || this.userInfo.positionType === 'GroupBuyManager'
        || this.userInfo.positionType === 'AccountManager' || this.userInfo.positionType === 'CustServiceManager' || this.userInfo.positionType === 'VipManager'
        || this.userInfo.positionType === 'CustServiceSpecialist' || this.userInfo.positionType === 'CustServiceSupervisor' || this.userInfo.positionType === 'SalesTeamLeader') {
        this.postnId = this.userInfo.postnId;
        this.orgChooseFlag = false;
      }
      if(this.$utils.isEmpty(this.accessGroupOauth)){
          this.countActPreview(); // 活动场次概览统计
          this.countActByType('held'); // 活动举办/结案场次统计
          this.countTerminalByType(); // 活动覆盖终端统计
          this.countActByMonth(); // 活动趋势统计报表
          this.countActAudit('Y'); // 活动稽核结果统计报表
      }
    },
    methods: {
      /**
       *  @description: 选择费用小类
       *  @author: 马晓娟
       *  @date: 2021/1/5 下午4:41
       */
      async chooseFeeType () {
        const item = await this.$object(this.feeTypeOption);
        this.feeType.name = item.costTypeName;
        this.feeType.code = item.costTypeCode;
        this.countActByMonth(); // 活动趋势统计报表
      },
      /**
       *  @description: 选择省
       *  @author: 马晓娟
       *  @date: 2020/12/28 下午9:29
       */
      async chooseProvince (index) {
        const item = await this.$object(this.provinceOption);
        this.areaData[index].provinceName = item.addrName;
        this.areaData[index].cityName = '市';
        this.areaData[index].districtName = '区县';
        if (index === 0) {
          this.countActPreview(); // 活动场次概览统计
        } else if (index === 1) {
          let type = 'closed';
          if (this.activityFlag) {
            type = 'held';
          }
          this.countActByType(type); // 活动举办/结案场次统计
        } else if (index === 2) {
          let type = 'Unaudit';
          if (this.checkFlag) {
            type = 'Audited';
          }
          this.countActAudit(type); // 活动稽核结果统计报表
        }
      },
      /**
       *  @description: 选择市
       *  @author: 马晓娟
       *  @date: 2020/12/28 下午9:30
       */
      async chooseCity (index) {
        if (this.$utils.isEmpty(this.areaData[index].provinceName) || this.areaData[index].provinceName === '省') {
          this.$showError('请先选择省份!');
          return;
        }
        this.cityOption.option.param.filtersRaw[1].value = this.areaData[index].provinceName;
        const item = await this.$object(this.cityOption);
        this.areaData[index].cityName = item.addrName;
        this.areaData[index].districtName = '区县';
        if (index === 0) {
          this.countActPreview(); // 活动场次概览统计
        } else if (index === 1) {
          let type = 'closed';
          if (this.activityFlag) {
            type = 'held';
          }
          this.countActByType(type); // 活动举办/结案场次统计
        } else if (index === 2) {
          let type = 'Unaudit';
          if (this.checkFlag) {
            type = 'Audited';
          }
          this.countActAudit(type); // 活动稽核结果统计报表
        }
      },
      /**
       *  @description: 选择区县
       *  @author: 马晓娟
       *  @date: 2020/12/28 下午9:30
       */
      async chooseDistrict(index) {
        if (this.$utils.isEmpty(this.areaData[index].cityName)|| this.areaData[index].cityName === '市') {
          this.$showError('请先选择市!');
          return;
        }
        this.districtOption.option.param.filtersRaw[1].value = this.areaData[index].cityName;
        const item = await this.$object(this.districtOption);
        this.areaData[index].districtName = item.addrName;
        if (index === 0) {
          this.countActPreview(); // 活动场次概览统计
        } else if (index === 1) {
          let type = 'closed';
          if (this.activityFlag) {
            type = 'held';
          }
          this.countActByType(type); // 活动举办/结案场次统计
        } else if (index === 2) {
          let type = 'Unaudit';
          if (this.checkFlag) {
            type = 'Audited';
          }
          this.countActAudit(type); // 活动稽核结果统计报表
        }
      },
      /**
       *  @description: 查询所有报表接口
       *  @author: 马晓娟
       *  @date: 2020/11/5 11:16
       */
      queryAll (index, type) {
        switch (index) {
          case 0:
            this.countActPreview(); // 活动场次概览统计
            break;
          case 1:
            type = 'closed';
            if (this.activityFlag) {
              type = 'held';
            }
            this.countActByType(type); // 活动举办/结案场次统计
            break;
          case 2:
            this.countActByMonth(); // 活动趋势统计报表
            break;
          case 3:
            this.countTerminalByType(); // 活动覆盖终端统计
            break;
          case 4:
            type = 'Unaudit';
            if (this.checkFlag) {
              type = 'Audited';
            }
            this.countActAudit(type); // 活动稽核结果统计报表
            break;
        }
      },
      /**
       *  @description: 活动场次概览统计 0
       *  @author: 马晓娟
       *  @date: 2020/11/4 16:26
       */
      async countActPreview() {
        let param = {
          attr1: this.timeData[0].startDate, // 开始时间
          attr2: this.timeData[0].endDate, // 结束时间
          attr3: this.dealerData[0].dealerId, // 经销商id
          orgId: this.orgData[0].orgId, // 组织id
          postnId: this.postnId         // 职位ID
        };
        if (!this.orgIdChooseShow) {
          param = {... param,
            ... {
              attr4: this.userInfo.postnId,
              companyId: this.userInfo.coreOrganizationTile['l3Id'],
              province: this.areaData[0].provinceName,  // 省
              city: this.areaData[0].cityName,    // 市
              district: this.areaData[0].districtName // 区县
            }};
          delete param.orgId;
        }
        if (this.areaData[0].provinceName === '省' || this.$utils.isEmpty(this.areaData[0].provinceName)) {
          delete param.province;
          delete param.city;
          delete param.district;
        }
        if (this.areaData[0].cityName === '市' || this.$utils.isEmpty(this.areaData[0].cityName)) {
          delete param.city;
          delete param.district;
        }
        if (this.areaData[0].districtName === '区县' || this.$utils.isEmpty(this.areaData[0].districtName)) {
          delete param.district;
        }
        if (this.$utils.isEmpty(this.postnId)) {
          delete param.postnId;
        }
        this.countActPreviewData = [{key: 'New', value: '-'}, {key: 'Published', value: '-'}, {key: 'Processing', value: '-'}, {key: 'Closed', value: '-'}, {key: 'ActualAmount', value: '-'}];
        try {
          this.$utils.showLoading();
          const data = await this.$http.post('action/link/marketAct/countActPreview', param);
          if (data.success) {
            // this.totalAct = data.rows.reduce((total, item) => total + parseInt(item.value, 10), 0);
            data.rows.forEach((item) => {
              if (item.key === 'ActualAmount') {
                this.countActPreviewData[4].value = item.value;
              } else if (item.key === 'Closed') {
                this.countActPreviewData[3].value = item.value;
              } else if (item.key === 'Processing') {
                this.countActPreviewData[2].value = item.value;
              } else if (item.key === 'Published') {
                this.countActPreviewData[1].value = item.value;
              } else if (item.key === 'New') {
                this.countActPreviewData[0].value = item.value;
              } else if (item.key === 'Amount') {
                this.totalAct = parseInt(item.value, 10);
              }
            });
            this.$utils.hideLoading();
          } else {
            this.$utils.hideLoading();
            this.$showError(`查询活动场次概览统计失败！${data.result}`);
          }
        } catch (e) {
          this.$utils.hideLoading();
          this.$showError(`查询活动场次概览统计失败！${e}`);
        }

      },
      /**
       *  @description: 各类活动举办/结案场次统计 1
       *  @author: 马晓娟
       *  @date: 2020/11/4 16:34
       */
      async countActByType(countType) {
        this.activityRows = [];
        let param = {
          attr1: this.timeData[1].startDate, // 开始时间
          attr2: this.timeData[1].endDate, // 结束时间
          attr3: this.dealerData[1].dealerId, // 经销商id
          orgId: this.orgData[1].orgId, // 组织id
          postnId: this.postnId         // 职位ID
        };
        if (!this.orgIdChooseShow) {
          param = {... param,
            ... {
              attr4: this.userInfo.postnId,
              companyId: this.userInfo.coreOrganizationTile['l3Id'],
              province: this.areaData[1].provinceName,  // 省
              city: this.areaData[1].cityName,    // 市
              district: this.areaData[1].districtName // 区县
            }};
          delete param.orgId;
        }
        if (this.areaData[1].provinceName === '省' || this.$utils.isEmpty(this.areaData[1].provinceName)) {
          delete param.province;
          delete param.city;
          delete param.district;
        }
        if (this.areaData[1].cityName === '市' || this.$utils.isEmpty(this.areaData[1].cityName)) {
          delete param.city;
          delete param.district;
        }
        if (this.areaData[1].districtName === '区县' || this.$utils.isEmpty(this.areaData[1].districtName)) {
          delete param.district;
        }
        if (this.$utils.isEmpty(this.postnId)) {
          delete param.postnId;
        }
        const data = await this.$http.post('action/link/marketAct/countActByType', param);
        if (data.success) {
          let dataTemp = data.rows.filter((item) => !!item.costType && !!item.count && parseInt(item.count, 10) > 0);
          const heldData = dataTemp.filter(item => item.countType === 'held');
          const closedData = dataTemp.filter(item => item.countType === 'closed');
          let actData = heldData;
          this.activityFlag = true;
          if (countType === 'closed') {
            actData = closedData;
            this.activityFlag = false;
          }
          let list = await Promise.all(actData.map(async (item) => {
            return (async () => {
              let name = item.costType;
              let value = parseInt(item.count, 10);
              return {
                name: name,
                value: value
              };
            })()
          }));
          this.activityRows = list;
          this.actBarYHeight = 32 + 26 * list.length;
          this.activityOption = echartInitConfig => barYCategory(echartInitConfig,list, false, '');
        } else {
          this.$showError(`查询各类活动举办/实发场次统计失败！${data.result}`);
        }
      },
      /**
       *  @description: 活动趋势统计报表2
       *  @author: 马晓娟
       *  @date: 2020/11/4 16:35
       */
      async countActByMonth() {
        let param = {
          attr3: this.dealerData[2].dealerId, // 经销商id
          orgId: this.orgData[2].orgId, // 组织id
          postnId: this.postnId,         // 职位ID
          costTypeCode: this.feeType.code // 费用小类编码
        };
        if (this.$utils.isEmpty(this.postnId)) {
          delete param.postnId;
        }
        if(this.$utils.isEmpty(this.feeType.code)) {
          delete param.costTypeCode;
        }
        const data = await this.$http.post('action/link/marketAct/countActByMonth', param);
        if (data.success) {
          let seriesData = [{
            data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            colorStyle: {colorA: '#6392FA', colorB: '#2F69F8'},
            name: '举办场次',
            isShadowColor: true,
            type: 'line'
          }, {
            data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            colorStyle: {colorA: '#FFB701', colorB: '#FF5A5A'},
            name: '实发场次',
            isShadowColor: true,
            type: 'line'
          }];
          const heldData = data.rows.filter(item => item.countType === 'held');
          const closedData = data.rows.filter(item => item.countType === 'closed');
          seriesData[0].data.forEach((item, index) => {
            heldData.forEach((item1) => {
              if ((index+1) === parseInt(item1.month, 10)) {
                seriesData[0].data[index] = parseInt(item1.count, 10)
              }
            })
          });
          seriesData[1].data.forEach((item,index) => {
            closedData.forEach((item1) => {
              if ((index+1) === parseInt(item1.month, 10)) {
                seriesData[1].data[index] = parseInt(item1.count, 10)
              }
            })
          });
          var total1 = seriesData[0].data.reduce(function(a, b) {
            return a + b;
          });
          var total2 = seriesData[1].data.reduce(function(a, b) {
            return a + b;
          });
          var arr = seriesData[0].data.concat(seriesData[1].data)
          var max = Math.max(...arr)
          max = Math.ceil(max * 1.1 /100) * 100
          const allYearXData = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'];
          const headData = [
            {value: total1, name:'举办场次',test: 'test'},
            {value: total2, name:'实发场次',test: 'test'},
          ]
          const yAxisData = [{max: max, interval: max / 5}];
          this.allYearActOption = echartInitConfig => targetComposeLineAndHistogramProgress(echartInitConfig, allYearXData, yAxisData, seriesData, headData, true);
        } else {
          this.$showError(`查询活动趋势统计报表失败！${data.result}`);
        }
      },
      /**
       *  @description: 各类活动服务的终端数量统计报表 3
       *  @author: 马晓娟
       *  @date: 2020/11/4 16:34
       */
      async countTerminalByType() {
        this.actTerminalRows = [];
        let param = {
          attr1: this.timeData[3].startDate, // 开始时间
          attr2: this.timeData[3].endDate, // 结束时间
          attr3: this.dealerData[3].dealerId, // 经销商id
          orgId: this.orgData[3].orgId, // 组织id
          postnId: this.postnId
        };
        if (this.$utils.isEmpty(this.postnId)) {
          delete param.postnId;
        }
        const data = await this.$http.post('action/link/marketAct/countTerminalByType', param);
        if (data.success) {
          let dataTemp = data.rows.filter((item) => !!item.costType && !!item.count && parseInt(item.count, 10) > 0);
          let list = await Promise.all(dataTemp.map(async (item) => {
            return (async () => {
              let name = item.costType;
              let value = parseInt(item.count, 10);
              return {
                name: name,
                value: value
              };
            })()
          }));
          this.actTerminalRows = list;
          this.terminalBarYHeight = 32 + 26 * list.length;
          this.actTerminalOption = echartInitConfig => barYCategory(echartInitConfig, list, false, '');
        } else {
          this.$showError(`查询终端数量统计报表失败！${data.result}`);
        }
      },
      /**
       *  @description: 活动稽核结果统计报表 4
       *  @author: 马晓娟
       *  @date: 2020/11/4 16:35
       */
      async countActAudit(whetherAudit) {
        this.actCheckRows = [];
        let param = {
          attr1: this.timeData[4].startDate, // 开始时间
          attr2: this.timeData[4].endDate, // 结束时间
          attr3: this.dealerData[4].dealerId, // 经销商id
          orgId: this.orgData[4].orgId, // 组织id
          postnId: this.postnId
        };
        if (!this.orgIdChooseShow) {
          param = {... param,
            ... {
              attr4: this.userInfo.postnId,
              companyId: this.userInfo.coreOrganizationTile['l3Id'],
              province: this.areaData[2].provinceName,  // 省
              city: this.areaData[2].cityName,    // 市
              district: this.areaData[2].districtName // 区县
            }};
          delete param.orgId;
        }
        if (this.areaData[2].provinceName === '省' || this.$utils.isEmpty(this.areaData[2].provinceName)) {
          delete param.province;
          delete param.city;
          delete param.district;
        }
        if (this.areaData[2].cityName === '市' || this.$utils.isEmpty(this.areaData[2].cityName)) {
          delete param.city;
          delete param.district;
        }
        if (this.areaData[2].districtName === '区县' || this.$utils.isEmpty(this.areaData[2].districtName)) {
          delete param.district;
        }
        if (this.$utils.isEmpty(this.postnId)) {
          delete param.postnId;
        }
        const data = await this.$http.post('action/link/marketAct/countActAudit', param);
        if (data.success) {
          let dataTemp = data.rows.filter((item) => !!item.costType && !!item.count && parseInt(item.count, 10) > 0);
          const auditY = dataTemp.filter(item => item.whetherAudit === 'Audited');
          const auditN = dataTemp.filter(item => item.whetherAudit === 'Unaudit');
          let auditData = auditY;
          this.checkFlag = true;
          if (whetherAudit === 'Unaudit') {
            auditData = auditN;
            this.checkFlag = false;
          }
          let list = await Promise.all(auditData.map(async (item) => {
            return (async () => {
              let name = item.costType;
              let value = parseInt(item.count, 10);
              return {
                name: name,
                value: value
              };
            })()
          }));
          this.actCheckRows = list;
          this.actCheckBarYHeight = 32 + 26 * list.length;
          this.actCheckOption = echartInitConfig => barYCategory(echartInitConfig, list, false, '');
        } else {
          this.$showError(`查询活动稽核结果统计报表失败！${data.result}`);
        }
      },
      /**
       *  @description: 选择时间筛选维度
       *  @author: 马晓娟
       *  @date: 2020/10/28 11:54
       */
      chooseData($event, data, index) {
        let type = '';
        if (data === 'Audited') {
          type = data;
          this.checkFlag = true;
        } else if (data === 'Unaudit') {
          type = data;
          this.checkFlag = false;
        } else if (data === 'closed') {
          type = data;
          this.activityFlag = false;
        } else if (data === 'held') {
          type = data;
          this.activityFlag = true;
        } else {
          this.selectDimensionData[index].data.forEach((item) => {
            if (item.value === data.value) {
              data.selectedFlag = true;
            } else {
              item.selectedFlag = false;
            }
          });
          this.timeData[index] = $event;
        }
        this.queryAll(index, type);
      },
      /**
       *  @description: 选择片区维度查看报表数据
       *  @author: 马晓娟
       *  @date: 2020/11/3 16:56
       */
      chooseOrg(index) {
          if(this.loadingFlag)return;
          //2021-07-29如果没有配置访问组安全性则走原本的逻辑
          if(this.$utils.isEmpty(this.accessGroupOauth)){
              if (!this.orgChooseFlag) return;
          }
          this.chooseIndex = index;
          this.$refs.ptbm.tempOrgId = this.tempIdArr[index];
          this.$refs.ptbm.orgIdArr = this.orgIdArr[index];
          this.dialogFlag = true;
      },
      /**
       *  @description: 选择片区维度查看报表数据
       *  @author: 马晓娟
       *  @date: 2020/11/3 16:56
       *  2021-07-29 更新匹配访问组安全性
       */
      changeOrg(item) {
        if(Object.keys(item).length === 0)return;
        this.tempIdArr[this.chooseIndex] = item.id;
        this.orgData[this.chooseIndex].orgId = item.id;
        this.orgData[this.chooseIndex].orgName = item.text;
        this.orgIdArr[this.chooseIndex] = this.$refs.ptbm.orgIdArr;
        this.queryAll(this.chooseIndex);
      },
      /**
       *  @description: 选择公司维度查看报表数据
       *  @author: 马晓娟
       *  @date: 2020/11/3 16:56
       */
      async chooseDealer(index) {
        const item = await this.$object(this.dealerOption);
        this.dealerData[index].dealerId = item.id;
        this.dealerData[index].dealerName = item.acctName;
        this.queryAll(index);
      }
    }
  }
</script>

<style lang="scss">
  .activity-data-page {
    padding: 0 24px 0 24px;
    .count-data {
      display: flex;
      align-items: center;
      margin-top: 34px;
      border: 1px solid #EBEDF5;
      border-radius: 16px;
      flex-direction: column;
      justify-content: center;
      height: 246px;

      .count-data-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
      }

      .line {
        width: 2px;
        height: 60px;
        background-image: linear-gradient(180deg, rgba(191, 191, 191, 0.00) 0%, rgba(191, 191, 191, 0.50) 52%, rgba(191, 191, 191, 0.00) 100%);
      }

      .count-data-content {
        display: flex;
        width: 100%;
      }

      .count-data-list {
        flex: 1;
        display: flex;
        justify-content: center;
      }

      .count-data-head {
        text-align: center;
        margin-bottom: 36px;
      }

      .count-data-item-value {
        font-size: 32px;
        color: #262626;
        letter-spacing: 2px;
        line-height: 32px;
        font-weight: bold;
        margin-bottom: 16px;
      }

      .count-data-item-label {
        font-size: 24px;
        color: #262626;
        letter-spacing: 0;
        line-height: 24px;
      }
    }

    .scroll-view-data {
      margin-top: 24px;

      .select-dimension {
        display: flex;
        .icon-text{
          text-align: center;
        }
      }
    }

    .select-org-dimension {
      display: flex;
      margin-top: 24px;
    }
    .activity-condition-content, .all-year-activity-condition-content, .activity-terminal-condition-content, .activity-check-condition-content {
      border: 1px solid #EBEDF5;
      border-radius: 16px;
      margin: 24px 0;
      padding-top: 24px;
    }
    .echart-no-more{
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      padding: 40px 0;
      font-size: 20px;
      color: #999;
    }
  }
</style>
