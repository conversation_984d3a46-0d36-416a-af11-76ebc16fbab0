<template>
    <link-page class="activity-ranking-matches">
        <!--活动场次模块-->
        <view class="">
            <!--活动场次文字-->
            <line-title title="活动场次排名" style="flex: 1"></line-title>
            <!--echarts图表-->
            <view style="padding-top: 20px" v-if="!noData">
                <link-echart
                    :option="activitySessionOption"
                    :height=" 230 + 'px'"
                    :force-use-old-canvas="false"
                    :loading="loadingFlag"
                />
<!--                 -->
            </view>
            <view class="no-data" v-else>
                <text>暂无数据</text>
            </view>

        </view>
    </link-page>
</template>

<script>
import LineTitle from "../../../../lzlj/components/line-title";
import {activityGeneralManager, activitySession} from "../marketing-echart.utils";
import Taro from "@tarojs/taro";
export default {
    name: "activity-ranking-matches",
    components: {LineTitle},
    props:{
      orgId:{
          type: String,
          default: ''
      },
    timeRangeIndex: {
        type: Number,
        default: 0
    }
    },
    data(){
        const userInfo = Taro.getStorageSync('token').result;
        return{
            loadingFlag: true,
            userInfo,
            dateList:['week','month','fiscal','lastFiscal'],
            activitySessionOption: null,
            noData:false,
        }
    },
   watch:{
        orgId(newVal,oldVal){
            this.queryData();
        },
       timeRangeIndex(newVal,oldVal){
           this.queryData();
       }
   },
   async created() {
       await this.queryData()
    },
    methods:{
        async queryData() {
            let param = {};
            this.activitySessionOption = null;
            if(this.$utils.isEmpty(this.orgId)){
                param = {
                    actPostnId: this.userInfo.postnId,
                    boardType: "feeType",
                    order: "desc",
                    sort: "actTotalNum",
                    rows:10,
                    timeRange: this.dateList[this.timeRangeIndex]
                }
            }else{
                param = {
                    actOrgId: this.orgId,
                    boardType: "feeType",
                    order: "desc",
                    sort: "actTotalNum",
                    rows:10,
                    timeRange: this.dateList[this.timeRangeIndex]
                }
            }
            this.loadingFlag = true;
            try {
                const data = await this.$http.post('export/link/marketActBoard/queryByExamplePage', param);
                if (data.success) {
                    if(this.$utils.isNotEmpty(data.rows)){
                        let showData=[];
                        data.rows.forEach((item)=>{
                            showData.push({
                                name: item['costTypeName'],
                                value: item['actTotalNum']
                            })
                        })
                        this.activitySessionOption = echartInitConfig => activityGeneralManager(echartInitConfig, showData, '活动场次');
                        this.noData = false;
                    }else{
                        this.activitySessionOption = null;
                        this.noData = true;
                    }
                    this.loadingFlag = false;
                } else {
                    this.loadingFlag = false;
                    this.$showError('查询接口失败，请稍后重试！' + data.result);
                }
            } catch (e) {
                this.loadingFlag = false;
                this.$showError('查询接口失败！');
            }
        },
    }
}
</script>

<style lang="scss">
.activity-ranking-matches{
    .no-data {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 24px;
        height: 480px;
        color: #D8D8D8;
    }
}
</style>
