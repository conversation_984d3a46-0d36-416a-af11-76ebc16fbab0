# 活动看板


------
* 初始文档
```
创建时间：2022年2月15日
创建人：  吕志平
```
* 模块介绍
>  通过时间或者区域维度,展示活动数据以及费用数据
>

* 涉及对象
> * 市场活动-本系统


* 是否共用
> 否


* 数据存储
> * 数据来源 本系统数据库
> * 存储方式 本系统数据库
> * 是否同步 否

* 缓存机制
> * 是否缓存 否

* 安全性
> *  (1) 根据传递的组织id展示数据,默认取当前登录职位的所属组织id

* 状态流转
> * 无


* 涉及组件
> * scroll-view
> * AutoList
> * link-page
> * link-echart


## 模块实现
###涉及页面
#### 一 活动看板
#####  1、vue页面路径
> * 1、页面完整路径
>
>  src/pages/echart/lzlj/marketing-activity-report/market-act-report-page.vue

##### 2、页面涉及组件
>> 2.1 费用投入
>>#####  2.1.1、vue页面路径
>> src/pages/echart/lzlj/marketing-activity-report/components/cost-input.vue
>>#####  2.1.2、实现功能
>> * (1).当前登录人的值不属于以下职位时,可以选择片区
>>
>>   SalesSupervisor:       业务主管
>>
>>   Salesman:              业务代表
>>
>>   GroupBuyManager:       团购经理
>>
>>   AccountManager:        客户经理
>>
>>   CustServiceManager:    客服经理
>>
>>   VipManager:            VIP经理
>>
>>   CustServiceSpecialist: 客服专员
>>
>>   CustServiceSupervisor: 客服主管
>>
>>   SalesTeamLeader:       小组组长
>>
>> * (2).可以选择时间维度来展示费用投入数据(本周,本月,本季度,本财年-每年的11月1号-次年的10月31号)
>> * (3).也可以选择某一经销商查看数据
>
>> 2.2 活动开展情况
>>#####  2.2.1、vue页面路径
>> src/pages/echart/lzlj/marketing-activity-report/components/activity-data.vue
>>#####  2.2.2、实现功能
>> * (1).同上
>> * (2).同上
>> * (3).可以费用小类,和稽核状态标签查看数据


##### 3、页面实现功能
> * (1).可以选择时间维度来展示费用或者是活动开展情况的数据.(本周,本月,本季度,本财年-每年的11月1号-次年的10月31号,日历年（1月1日 - 12月31日）)
> * (2).当满足职位类型时也可以选组织维度来展示数据.
> * (3).当满足职位类型时也可以选组织维度来展示数据.


## 配置页面
> * 无


------ 活动看板模块内容结束 ------


