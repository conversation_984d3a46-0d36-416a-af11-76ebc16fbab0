import {$utils} from "src/utils/$utils";


/**
 *  @description: 绘制饼图基础色
 *  @author: 王雅琪
 *  @date: 2022/08/19
 */
export function echartColor(echartInitConfig, params, pieColor) {
    let colorList = new Array()
    if (pieColor.length > 0) {
        colorList = pieColor
    } else {
        let baseColorList = [
            {
                c1: '#FFB701',  //管理
                c2: '#FF5A5A'
            }, {
                c1: '#6392FA',  //管理
                c2: '#4179F4'
            }, {
                c1: '#69CAFF',  //管理
                c2: '#36ACEB'
            }, {
                c1: '#81F3EF',  //管理
                c2: '#5FCACE'
            },
            {
                c1: '#FFEF7F',  //管理
                c2: '#FFD54A'
            },
            {
                c1: '#FFA762',  //实践
                c2: '#FF892C'
            },
            {
                c1: '#FF7B76',//操作
                c2: '#FF544D'
            },
            {
                c1: '#AF6DFF',//操作
                c2: '#8A2FF8'
            },
            {
                c1: '#6392FA',//操作
                c2: '#4179F4'
            },
            {
                c1: '#69CAFF',//操作
                c2: '#36ACEB'
            },
            {
                c1: '#81F3EF',//操作
                c2: '#5FCACE'
            },
        ]
        colorList = $utils.deepcopy(baseColorList);
        if (params.dataIndex >= colorList.length) {
            let num = Math.ceil((params.dataIndex + 1) / baseColorList.length);
            for (let i = 0; i < num - 1; i++) {
                colorList = colorList.concat(baseColorList);
            }
        }
    }
    return new echartInitConfig.echarts.graphic.LinearGradient(0, 0, 0, 1, [{ //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
        offset: 0,
        color: colorList[params.dataIndex].c1
    }, {
        offset: 1,
        color: colorList[params.dataIndex].c2
    }])
}

/**
 * @createdBy  王雅琪
 * @date  2022/08/16
 * @methods barYCategory
 * @para
 * @description 柱状图，横坐标为y轴
 */
export function CbarYCategory(echartInitConfig, seriesData, sort = true) {
    function object(a, b) {
        return a.value - b.value;
    }

    if (sort) {
        seriesData.sort(object);
    }
    let idsStr = seriesData.map(function (obj, index) {
        return obj.name;
    }).join(",");
    let qsName = idsStr.split(',')
    return {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            position: function (point, params, dom, rect, size) { // point: 鼠标位置
                var tipHeight = point[1] + size.contentSize[1]; // contentSize: 提示dom 窗口大小
                if (tipHeight > size.viewSize[1]) {              // viewSize: echarts 容器大小
                    return [point[0] + 40, point[1] - size.contentSize[1]];
                } else if (point[1] < size.contentSize[1]) {
                    return [point[0] + 40, point[1] + 20];
                } else {
                    return point;
                }
            },
        },
        legend: {
            show: false,
        },
        grid: {
            left: 0,
            right: 16,
            bottom: 0,
            top: 16,
            containLabel: true
        },
        xAxis: {
            type: 'value',
            axisTick: {
                show: false, // 是否显示坐标轴刻度 默认显示
            },
            boundaryGap: [0, 0.01],
            axisLine: {
                lineStyle: { // 坐标轴线线的颜色
                    color: '#E6EAF4',
                    width: 1
                }
            },
            splitLine: {
                lineStyle: {
                    color: '#E6EAF4',
                    type: 'dashed'//虚线
                }
            },
            axisLabel: {
                color: '#999',
                fontSize: 12,
                lineHeight: 12,
                fontFamily: 'PingFangSC-Regular',
                rotate: 45,
                formatter: function (value) {//x轴0 添加单位
                    if (value === 0) {
                        return value + '(瓶)';
                    } else return value;
                },
            }
        },
        yAxis: {
            type: 'category',
            boundaryGap: [0, 20],
            axisTick: {
                show: false,//不显示刻度
            },
            axisLine: {
                lineStyle: { // 坐标轴线线的颜色
                    color: '#E6EAF4',
                    width: 1
                }
            },
            axisLabel: {
                show: true,
                textStyle: {
                    fontSize: 12,
                    lineHeight: 14,
                    color: '#999',
                    fontFamily: 'PingFangSC-Regular',
                },
                interval: 0,
                formatter: function (params) {    //字数太长显示...
                    let val = "";
                    let val1 = "";
                    if (params.length > 0 && params.length < 6) {
                        return params;
                    }
                    if (params.length < 11 && params.length > 5) {
                        val = params.substring(0, 5) + "\n";
                        val1 = params.substring(5, 10);
                        return val.concat(val1);

                    }
                    if (params.length > 10) {
                        val = params.substring(0, 5) + "\n";
                        val1 = params.substring(5, 9) + '....';
                        return val.concat(val1);
                    }
                }
            },
            data: qsName
        },
        series: [
            {
                name: '',
                type: 'bar',
                barWidth: 10,
                barGap: 20,
                label: {
                    normal: {
                        show: true,//显示数字
                        position: 'right',
                        textStyle: {
                            fontSize: '10',//柱状上的显示的文字
                            color: '#333',
                            fontWeight: 500
                        }
                    }
                },
                itemStyle: {
                    color: function (params) {
                        const index = params.dataIndex;
                        const colorList = index % 2 === 1 ? ['#2F69F8', '#6392FA'] : ['#98CEFF', '#C9E9FF']
                        return new echartInitConfig.echarts.graphic.LinearGradient(0, 0, 1, 0,
                            [
                                {offset: 0, color: colorList[0]},
                                {offset: 1, color: colorList[1]}
                            ]);
                    },
                },
                data: seriesData
            }
        ]
    };
}

/**
 * @description 品鉴酒扫码-各区域扫码，柱状图，横坐标为y轴
 * @createdBy  何春霞
 * @date  2022/08/16
 * @methods variousAreaScanCode
 * @para
 */
export function variousAreaScanCode(echartInitConfig, seriesData, sort = true, unit) {
    function object(a, b) {
        return a.value - b.value;
    }

    if (sort) {
        seriesData.sort(object);
    }
    let idsStr = seriesData.map(function (obj, index) {
        return obj.name;
    }).join(",");
    let qsName = idsStr.split(',')
    return {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
        },
        legend: {
            show: false,
        },
        grid: {
            left: '0',
            right: '16',
            bottom: '0',
            top: '16',
            containLabel: true,
        },
        xAxis: {
            type: 'value',
            boundaryGap: [0, '0.01'],
            axisTick: {
                show: false, // 是否显示坐标轴刻度 默认显示
            },
            axisLine: {
                lineStyle: { // 坐标轴线线的颜色
                    color: '#E6EAF4',
                    width: 1
                }
            },
            splitLine: {
                lineStyle: {
                    color: '#E6EAF4',
                    type: 'dashed'//虚线
                }
            },
            axisLabel: {
                color: '#999',
                fontSize: 12,
                lineHeight: 12,
                rotate: 45,
                fontFamily: 'PingFangSC-Regular',
                interval: 1,
                formatter: function (value) {//x轴0 添加单位
                    if (value === 0) {
                        return value + '(瓶)';
                    } else return value;
                },
            }
        },
        yAxis: {
            type: 'category',
            data: qsName,
            boundaryGap: [0, 20],
            axisTick: {
                show: false, // 是否显示坐标轴刻度 默认显示
            },
            axisLine: {
                lineStyle: { // 坐标轴线线的颜色
                    color: '#E6EAF4',
                    width: 1
                }
            },
            axisLabel: {
                show: true,
                textStyle: {
                    fontSize: 12,
                    lineHeight: 14,
                    color: '#999',
                    fontFamily: 'PingFangSC-Regular',
                },
                interval: 0,
                formatter: function (params) {    //字数太长显示...
                    let val = "";
                    let val1 = "";
                    if (params.length > 0 && params.length < 6) {
                        return params;
                    }
                    if (params.length < 11 && params.length > 5) {
                        val = params.substring(0, 5) + "\n";
                        val1 = params.substring(5, 10);
                        return val.concat(val1);

                    }
                    if (params.length > 10) {
                        val = params.substring(0, 5) + "\n";
                        val1 = params.substring(5, 9) + '....';
                        return val.concat(val1);
                    }
                }
            },

        },
        series: [
            {
                name: '',
                type: 'bar',
                data: seriesData,
                barWidth: 10, // 柱的宽度
                barGap: 20,
                // 柱上面的数值配置
                label: {
                    show: true, // 显示值
                    position: "right", // 显示位置
                    textStyle: {
                        color: "#333333",
                        fontSize: '10',
                        fontWeight: 500
                    }
                },
                // 每一个条目的样式配置
                itemStyle: {
                    // barBorderRadius: [0, 34, 34, 0], // 圆角
                    color: function (params) {
                        const index = params.dataIndex;
                        const colorList = index % 2 === 1 ? ['#2F69F8', '#6392FA'] : ['#98CEFF', '#C9E9FF']
                        return new echartInitConfig.echarts.graphic.LinearGradient(0, 0, 1, 0,
                            [
                                {offset: 0, color: colorList[0]},
                                {offset: 1, color: colorList[1]}
                            ]);
                    },
                }
            }
        ],
        position: function (point, params, dom, rect, size) { // point: 鼠标位置
            let tipHeight = point[1] + size.contentSize[1]; // contentSize: 提示dom 窗口大小
            if (tipHeight > size.viewSize[1]) {              // viewSize: echarts 容器大小
                return [point[0] + 40, point[1] - size.contentSize[1]];
            } else if (point[1] < size.contentSize[1]) {
                return [point[0] + 40, point[1] + 20];
            } else {
                return point;
            }
        }
    };

}


/**
 *  @description: 品鉴酒扫码-绘制饼图基础色
 *  @author: 何春霞
 *  @date: 2022/08/22
 */
export function chartColor(echartInitConfig, params, pieColor) {
    let colorList = new Array()
    if (pieColor.length > 0) {
        colorList = pieColor
    } else {
        let baseColorList = [
            {
                c1: '#FFB701',  //管理
                c2: '#FF5A5A'
            }, {
                c1: '#6392FA',  //管理
                c2: '#4179F4'
            }, {
                c1: '#69CAFF',  //管理
                c2: '#36ACEB'
            }, {
                c1: '#81F3EF',  //管理
                c2: '#5FCACE'
            },
            {
                c1: '#FFEF7F',  //管理
                c2: '#FFD54A'
            },
            {
                c1: '#FFA762',  //实践
                c2: '#FF892C'
            },
            {
                c1: '#FF7B76',//操作
                c2: '#FF544D'
            },
            {
                c1: '#AF6DFF',//操作
                c2: '#8A2FF8'
            },
            {
                c1: '#6392FA',//操作
                c2: '#4179F4'
            },
            {
                c1: '#69CAFF',//操作
                c2: '#36ACEB'
            },
            {
                c1: '#81F3EF',//操作
                c2: '#5FCACE'
            },
        ]
        colorList = $utils.deepcopy(baseColorList);
        if (params.dataIndex >= colorList.length) {
            let num = Math.ceil((params.dataIndex + 1) / baseColorList.length);
            for (let i = 0; i < num - 1; i++) {
                colorList = colorList.concat(baseColorList);
            }
        }
    }
    return new echartInitConfig.echarts.graphic.LinearGradient(0, 0, 0, 1, [{ //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
        offset: 0,
        color: colorList[params.dataIndex].c1
    }, {
        offset: 1,
        color: colorList[params.dataIndex].c2
    }])
}


/**
 *  @description: 绘制饼图相关参数
 *  @author: 何春霞
 *  @date: 2022/08/22
 *  @param echartInitConfig
 *  @param seriesData 数据项，例如： [{value: 23907500, name: '北部组'},{value: 239075, name: '西北组'}]
 *  @param totalSeriesData 数据总量，在饼图中间展示
 *  @param outRadius 外环圆角
 *  @param inRadius 内环圆角
 * @param pieColor
 * @param startAngle
 * @param labelType
 * @param labelUnit
 * @param totalUnit
 * @param minAngle
 */
export function targetPieChart(echartInitConfig, seriesData, totalSeriesData, outRadius = ['47%', '70%'], inRadius = '47%', pieColor = [], startAngle = 225, labelType, labelUnit, totalUnit, minAngle = 3, labelFontSize = 'largeSize', titleFontSize = 14) {
    return {
        grid: {
            left: '12px',
            right: '12px',
            bottom: '10px',
            top: '10px',
            containLabel: true
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        series: [
            {
                name: '外环',
                itemStyle: {
                    emphasis: {},
                    normal: {
                        color: function (params) {
                            return chartColor(echartInitConfig, params, pieColor);
                        }
                    }
                },
                type: 'pie',
                z: 1,
                avoidLabelOverlap: true,   //是否启用防止标签重叠策略
                // roseType: 'area',
                minAngle: minAngle,           　　 //最小的扇区角度（0 ~ 360），用于防止某个值过小导致扇区太小影响交互
                radius: outRadius,
                center: ['50%', '50%'],
                hoverAnimation: false,
                silent: false,
                clockwise: true,
                startAngle: startAngle,
                data: seriesData,
                labelLine: {
                    normal: {
                        smooth: 0.5,
                        length: 12,
                        length2: 6
                    }
                },
                tooltip: {
                    confine: true,
                    trigger: 'item',
                    show: true,
                    formatter: '{b} : {c} ({d}%)'
                },
                label: {
                    position: 'outside',    // 标签的位置。'outside'饼图扇区外侧，通过视觉引导线连到相应的扇区。'inside','inner' 同 'inside',饼图扇区内部。'center'在饼图中心位置。
                    normal: {
                        formatter: function (params) {
                            if (labelFontSize === 'smallSize') {
                                params.percent = params.percent.toFixed(2)
                            }
                            let string
                            if (labelType === 'valuePercent') {
                                let text = params.name;
                                string = '{c1|' + text + '}\n' + '{d1|' + params.value + labelUnit + ',' + params.percent + '%' + '}'   //然后return你需要的legend格式即可
                            } else if (labelType === 'value') {
                                let text = params.name;
                                string = '{c1|' + text + '}\n' + '{d1|' + params.value + labelUnit + '}'   //然后return你需要的legend格式即可
                            } else if (labelType === 'number') {
                                let text = params.name;
                                string = '{c2|' + text + '}\n' + '{d2|' + params.percent + '% ，' + params.value + '}'   //然后return你需要的legend格式即可
                            } else {
                                let text = params.name;
                                if (labelFontSize === 'largeSize') {
                                    if (text.length <= 4) {
                                        string = '{c1|' + text + '}' + ' ' + '{d1|' + params.percent + '%' + '}'   //然后return你需要的legend格式即可（7）
                                    } else if (text.length > 4 && text.length <= 11) {
                                        string = '{c1|' + text.slice(0, 7) + '}\n' + '{c1|' + text.slice(7) + '}' + ' ' + '{d1|' + params.percent + '%' + '}'   //然后return你需要的legend格式即可(7+ 4)
                                    } else if (text.length > 11) {
                                        string = '{c1|' + text.slice(0, 7) + '}\n' + '{c1|' + text.slice(7, 14) + '}\n' + '{c1|' + text.slice(14) + '}' + ' ' + '{d1|' + params.percent + '%' + '}'   //然后return你需要的legend格式即可(7+7+4)
                                    }
                                } else {
                                    let windowWidth = wx.getSystemInfoSync().windowWidth
                                    if (windowWidth <= 360) {
                                        text.length < 5 ? string = '{d1|' + params.percent + '%' + '}' + '{c1|' + text + '}'
                                            : string = '{d1|' + params.percent + '%' + '}' + '{c1|' + text.slice(0, 3) + '...' + '}'
                                    } else if (windowWidth > 360 && windowWidth <= 375) {
                                        text.length < 7 ? string = '{d1|' + params.percent + '%' + '}' + '{c1|' + text + '}'
                                            : string = '{d1|' + params.percent + '%' + '}' + '{c1|' + text.slice(0, 5) + '...' + '}'
                                    } else if (windowWidth > 375 && windowWidth <= 425) {
                                        text.length < 9 ? string = '{d1|' + params.percent + '%' + '}' + '{c1|' + text + '}'
                                            : string = '{d1|' + params.percent + '%' + '}' + '{c1|' + text.slice(0, 7) + '...' + '}'
                                    } else if (windowWidth > 400 && windowWidth <= 450) {
                                        text.length < 13 ? string = '{d1|' + params.percent + '%' + '}' + '{c1|' + text + '}'
                                            : string = '{d1|' + params.percent + '%' + '}' + '{c1|' + text.slice(0, 11) + '...' + '}'
                                    } else if (windowWidth > 450 && windowWidth <= 500) {
                                        text.length < 15 ? string = '{d1|' + params.percent + '%' + '}' + '{c1|' + text + '}'
                                            : string = '{d1|' + params.percent + '%' + '}' + '{c1|' + text.slice(0, 13) + '...' + '}'
                                    } else {
                                        string = '{d1|' + params.percent + '%' + '}' + '{c1|' + text
                                    }
                                }
                            }
                            return string
                        },
                        textStyle: {fontSize: labelFontSize === 'largeSize' ? 12 : 9, fontFamily: 'PingFangSC-Regular'},
                        rich: {
                            c1: {
                                color: '#8c8c8c',
                                lineHeight: labelFontSize === 'largeSize' ? 12 : 9,
                                fontSize: labelFontSize === 'largeSize' ? 12 : 9,
                                fontFamily: 'PingFangSC-Semibold'
                            },
                            d1: {
                                color: '#000',
                                lineHeight: labelFontSize === 'largeSize' ? 20 : 9,
                                fontSize: labelFontSize === 'largeSize' ? 12 : 9,
                                fontFamily: 'PingFangSC-Regular'
                            },
                            c2: {
                                color: '#999999',
                                lineHeight: labelFontSize === 'largeSize' ? 12 : 9,
                                fontSize: labelFontSize === 'largeSize' ? 12 : 9,
                                fontFamily: 'PingFangSC-Semibold'
                            },
                            d2: {
                                color: '#333333',
                                lineHeight: labelFontSize === 'largeSize' ? 20 : 9,
                                fontSize: labelFontSize === 'largeSize' ? 12 : 9,
                                fontFamily: 'PingFangSC-Regular'
                            },
                        }
                    },

                    emphasis: {
                        show: true,
                        textStyle: {
                            fontSize: '18',
                            fontWeight: 'bold'
                        },
                        hoverAnimation: false,
                        silent: false,
                    }
                }
            },
            {
                name: '内环',
                z: 2,
                color: '#fff',
                type: 'pie',
                radius: inRadius,
                center: ['50%', '50%'],
                hoverAnimation: false,
                avoidLabelOverlap: true,
                silent: false,
                clockwise: true,
                startAngle: startAngle, //起始角度
                data: totalSeriesData,
                tooltip: {
                    show: false,
                },
                labelLine: {
                    normal: {
                        lineStyle: {color: '#DDDDDD'},
                        smooth: 0.2,
                        length: 8,
                        length2: 16
                    }
                },
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        hoverAnimation: false,
                        formatter: function (params) {
                            let name = CharacterWrap(totalSeriesData[0].name, 4);
                            if ($utils.isNotEmpty(totalUnit)) {
                                return '{c1|' + totalSeriesData[0].value + '}\n{d1|' + name + '}\n{e1|' + totalUnit + '}';
                            } else {
                                return '{c1|' + totalSeriesData[0].value + '}\n{d1|' + name + '}';
                            }

                        },
                        rich: {
                            c1: {
                                color: '#333',
                                lineHeight: 38,
                                fontSize: 24,
                                fontWeight: 600,
                                fontFamily: 'PingFangSC-Semibold'
                            },
                            d1: {
                                color: '#333',
                                lineHeight: 16,
                                fontSize: 14,
                                fontWeight: 500,
                                fontFamily: 'PingFangSC-Regular'
                            },
                            e1: {
                                color: '#8C8C8C',
                                lineHeight: 12,
                                fontSize: 9,
                                fontFamily: 'PingFangSC-Regular'
                            },
                        }
                    },
                    emphasis: {
                        show: true,
                        textStyle: {
                            fontSize: '18',
                            fontWeight: 'bold'
                        },
                        hoverAnimation: false,
                        silent: false,
                    }
                }
            }
        ]
    };
}


/**
 *  @description: 简单柱形图
 *  @author: 王雅琪
 *  @date: 2022年9月2日
 *  @param seriesData 图表数据
 */
export function targetSimpleDataHistogram(echartInitConfig, seriesData) {
    let idsStr = seriesData.map(function (obj, index) {
        return obj.name;
    }).join(",");
    let qsName = idsStr.split(',')
    let isShowSlider = qsName.length > 5 ? true : false;
    return {
        tooltip: {
            trigger: 'axis',
            axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                type: 'shadow',        // 默认为直线，可选为：'line' | 'shadow'
                label: {
                    formatter: '{value}'
                }
            }
        },
        legend: {
            show: false,
        },
        grid: {
            left: '8px',
            right: '8px',
            top: '24px',
            bottom: '6px',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                axisPointer: {
                    type: 'shadow'
                },
                axisTick: {
                    show: false
                },
                axisLine: {lineStyle: {color: '#EBEDF5'}},
                axisLabel: {
                    interval: 0, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
                    fontFamily: 'PingFangSC-Regular',
                    color: '#666',
                    fontSize: 12,
                    lineHeight: 14,
                    formatter: function (params) {//字数太长换行
                        return CharacterWrap(params, 4)
                    }
                },
                data: qsName
            }
        ],
        yAxis: [
            {
                type: 'value',
                nameTextStyle: {
                    color: '#666',
                    fontSize: '12px',
                    fontWeight: 400,
                    align: 'right'
                },
                axisPointer: {
                    type: 'shadow'
                },
                axisTick: {
                    show: false
                },
                splitLine: {
                    lineStyle: {
                        color: '#E6EAF4',
                        type: 'dashed'//虚线
                    }
                },
                axisLine: {lineStyle: {color: '#EBEDF5'}},
                axisLabel: {
                    color: '#666',
                    fontSize: 12,
                    lineHeight: 12,
                    fontFamily: 'PingFangSC-Regular'
                },
            }
        ],
        dataZoom: [
            {
                type: 'slider',
                xAxisIndex: 0,
                startValue: 0,
                endValue: 4,
                height: 6,
                filterMode: 'none',
                zoomLock: true,
                bottom: 0,
                show: isShowSlider,
                showDetail: false,
                handleSize: '0%',
                dataBackground: {
                    lineStyle: {
                        opacity: 0
                    },
                    areaStyle: {
                        opacity: 0
                    }
                },
                fillerColor: '#D8D8D8',
                borderColor: '#fff'
            },
            {
                type: 'inside',
                xAxisIndex: 0,
                startValue: 0,
                endValue: 4,
                height: 10,
                filterMode: 'none',
                zoomLock: true
            }
        ],
        series: {
            name: seriesData.name,
            type: 'bar',
            stack: 'one',
            barWidth: '12px',
            barGap: '40%',
            yAxisIndex: 0,
            itemStyle: {
                normal: {
                    color: new echartInitConfig.echarts.graphic.LinearGradient(
                        0, 0, 0, 1,
                        [
                            {offset: 0, color: '#6392FA'},
                            {offset: 1, color: '#2F69F8'},
                        ]
                    )
                },
                emphasis: {
                    color: new echartInitConfig.echarts.graphic.LinearGradient(
                        0, 0, 0, 1,
                        [
                            {offset: 0, color: '#6392FA'},
                            {offset: 1, color: '#2F69F8'},
                        ]
                    )
                }
            },
            label: {
                normal: {
                    show: true,//显示数字
                    position: 'top',
                    align: "center",
                    textStyle: {
                        fontSize: '12',//柱状上的显示的文字
                        color: '#333',
                        fontWeight: 500
                    }
                }
            },
            data: seriesData
        }
    };
}


/**
 *  @description: 举办情况-活动场次-柱状图
 *  @author: 何春霞
 *  @date: 2022年9月2日
 *  @param seriesDataSession 图表数据
 */
export function activitySession(echartInitConfig, seriesDataSession, sort: true) {
    function object(a, b) {
        return a.value - b.value;
    }

    if (sort) {
        seriesDataSession.sort(object);
    }
    let idsStr = seriesDataSession.map(function (obj, index) {
        return obj.name;
    }).join(",");
    let qsName = idsStr.split(',')
    let isShowSlider = qsName.length > 5 ? true : false;
    return {
        tooltip: {
            trigger: 'axis',
            axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                type: 'shadow',        // 默认为直线，可选为：'line' | 'shadow'
                label: {
                    formatter: '{value}'
                }
            }
        },
        legend: {
            show: false,
        },
        grid: {
            left: '8px',
            right: '8px',
            top: '24px',
            bottom: '8px',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                axisPointer: {
                    type: 'shadow'
                },
                axisTick: {
                    show: false
                },
                axisLine: {lineStyle: {color: '#EBEDF5'}},
                axisLabel: {
                    interval: 0, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
                    fontFamily: 'PingFangSC-Regular',
                    color: '#666',
                    fontSize: 10,
                    lineHeight: 14,
                    formatter: function (params) {    //字数太长显示...
                        let val = "";
                        let val1 = "";
                        if (params.length > 0 && params.length < 5) {
                            return params;
                        }
                        if (params.length < 9 && params.length > 4) {
                            val = params.substring(0, 4) + "\n";
                            val1 = params.substring(4, 8);
                            return val.concat(val1);

                        }
                        if (params.length > 9) {
                            val = params.substring(0, 4) + "\n";
                            val1 = params.substring(4, 7) + '....';
                            return val.concat(val1);
                        }
                    },

                },
                data: qsName
            }
        ],
        yAxis: [
            {
                type: 'value',
                nameTextStyle: {
                    color: '#666',
                    fontSize: '12px',
                    fontWeight: 400,
                    align: 'right'
                },
                axisPointer: {
                    type: 'shadow'
                },
                axisTick: {
                    show: false
                },
                splitLine: {
                    lineStyle: {
                        color: '#E6EAF4',
                        type: 'dashed'//虚线
                    }
                },
                axisLine: {lineStyle: {color: '#EBEDF5'}},
                axisLabel: {
                    color: '#666',
                    fontSize: 12,
                    lineHeight: 12,
                    fontFamily: 'PingFangSC-Regular',
                },
            }
        ],
        dataZoom: [
            {
                type: 'slider',
                startValue: 0,
                endValue: 4,
                height: 6,
                filterMode: 'none',
                zoomLock: true,
                bottom: 0,
                show: isShowSlider,
                showDetail: false,
                handleSize: '0%',
                dataBackground: {
                    lineStyle: {
                        opacity: 0
                    },
                    areaStyle: {
                        opacity: 0
                    }
                },
                fillerColor: '#D8D8D8',
                borderColor: '#fff'
            },
            {
                type: 'inside',
                start: 0,
                end: 49,
                height: 10,
                filterMode: 'none',
                zoomLock: true
            }
        ],
        series: {
            name: seriesDataSession.name,
            type: 'bar',
            stack: 'one',
            barWidth: '12px',
            barCategoryGap: '50%',
            barGap: '40%',
            yAxisIndex: 0,
            itemStyle: {
                normal: {
                    color: new echartInitConfig.echarts.graphic.LinearGradient(
                        0, 0, 0, 1,
                        [
                            {offset: 0, color: '#6392FA'},
                            {offset: 1, color: '#2F69F8'},
                        ]
                    )
                },
                emphasis: {
                    color: new echartInitConfig.echarts.graphic.LinearGradient(
                        0, 0, 0, 1,
                        [
                            {offset: 0, color: '#6392FA'},
                            {offset: 1, color: '#2F69F8'},
                        ]
                    )
                }
            },
            label: {
                normal: {
                    show: true,//显示数字
                    position: 'top',
                    align: "center",
                    textStyle: {
                        fontSize: '12',//柱状上的显示的文字
                        color: '#333',
                        fontWeight: 500
                    }
                }
            },
            data: seriesDataSession
        }
    };

}

/**
 *  @description: 举办情况-申请金额-柱状图
 *  @author: 何春霞
 *  @date: 2022年9月2日
 *  @param seriesDataCost 图表数据
 */
export function applyCost(echartInitConfig, seriesDataCost, sort: true) {
    function object(a, b) {
        return a.value - b.value;
    }

    if (sort) {
        seriesDataCost.sort(object);
    }
    let idsStr = seriesDataCost.map(function (obj, index) {
        return obj.name;
    }).join(",");
    let qsName = idsStr.split(',')
    let isShowSlider = qsName.length > 5 ? true : false;
    return {
        tooltip: {
            trigger: 'axis',
            axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                type: 'shadow',        // 默认为直线，可选为：'line' | 'shadow'
                label: {
                    formatter: '{value}'
                }
            }
        },
        legend: {
            show: false,
        },
        grid: {
            left: '8px',
            right: '8px',
            top: '24px',
            bottom: '8px',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                axisPointer: {
                    type: 'shadow'
                },
                axisTick: {
                    show: false
                },
                axisLine: {lineStyle: {color: '#EBEDF5'}},
                axisLabel: {
                    interval: 0, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
                    fontFamily: 'PingFangSC-Regular',
                    color: '#666',
                    fontSize: 10,
                    lineHeight: 14,
                    formatter: function (params) {    //字数太长显示...
                        let val = "";
                        let val1 = "";
                        if (params.length > 0 && params.length < 5) {
                            return params;
                        }
                        if (params.length < 9 && params.length > 4) {
                            val = params.substring(0, 4) + "\n";
                            val1 = params.substring(4, 8);
                            return val.concat(val1);

                        }
                        if (params.length > 9) {
                            val = params.substring(0, 4) + "\n";
                            val1 = params.substring(4, 7) + '...';
                            return val.concat(val1);
                        }
                    },

                },
                data: qsName
            }
        ],
        yAxis: [
            {
                type: 'value',
                nameTextStyle: {
                    color: '#666',
                    fontSize: '12px',
                    fontWeight: 400,
                    align: 'right'
                },
                axisPointer: {
                    type: 'shadow'
                },
                axisTick: {
                    show: false
                },
                splitLine: {
                    lineStyle: {
                        color: '#E6EAF4',
                        type: 'dashed'//虚线
                    }
                },
                axisLine: {lineStyle: {color: '#EBEDF5'}},
                axisLabel: {
                    color: '#666',
                    fontSize: 12,
                    lineHeight: 12,
                    fontFamily: 'PingFangSC-Regular'
                },
            }
        ],
        dataZoom: [
            {
                type: 'slider',
                startValue: 0,
                endValue: 4,
                height: 6,
                filterMode: 'none',
                zoomLock: true,
                bottom: 0,
                show: isShowSlider,
                showDetail: false,
                handleSize: '0%',
                dataBackground: {
                    lineStyle: {
                        opacity: 0
                    },
                    areaStyle: {
                        opacity: 0
                    }
                },
                fillerColor: '#D8D8D8',
                borderColor: '#fff'
            },
            {
                type: 'inside',
                start: 0,
                end: 49,
                height: 10,
                filterMode: 'none',
                zoomLock: true
            }
        ],
        series: {
            name: seriesDataCost.name,
            type: 'bar',
            stack: 'one',
            barWidth: '12px',
            barCategoryGap: '50%',
            barGap: '40%',
            yAxisIndex: 0,
            itemStyle: {
                normal: {
                    color: new echartInitConfig.echarts.graphic.LinearGradient(
                        0, 0, 0, 1,
                        [
                            {offset: 0, color: '#6392FA'},
                            {offset: 1, color: '#2F69F8'},
                        ]
                    )
                },
                emphasis: {
                    color: new echartInitConfig.echarts.graphic.LinearGradient(
                        0, 0, 0, 1,
                        [
                            {offset: 0, color: '#6392FA'},
                            {offset: 1, color: '#2F69F8'},
                        ]
                    )
                }
            },
            label: {
                normal: {
                    show: true,//显示数字
                    position: 'top',
                    align: "center",
                    // rotate:-45,
                    textStyle: {
                        fontSize: '12',//柱状上的显示的文字
                        color: '#333',
                        fontWeight: 500
                    }
                }
            },
            data: seriesDataCost
        }
    };

}



/**
 *  @description: 总部市场活动看板使用
 *  @author: 吕志平
 *  @date: 2022年12月2日
 *  @param seriesDataSession 图表数据
 */
export function activityGeneralManager(echartInitConfig, seriesDataSession,yLabel: '', sort: true) {
    function object(a, b) {
        return a.value - b.value;
    }

    if (sort) {
        seriesDataSession.sort(object);
    }
    let idsStr = seriesDataSession.map(function (obj, index) {
        return obj.name;
    }).join(",");
    let qsName = idsStr.split(',')
    let isShowSlider = qsName.length > 5 ? true : false;
    return {
        tooltip: {
            trigger: 'axis',
            axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                type: 'shadow',        // 默认为直线，可选为：'line' | 'shadow'
                label: {
                    formatter: '{value}'
                }
            }
        },
        legend: {
            show: false,
        },
        grid: {
            left: '8px',
            right: '8px',
            top: '30px',
            bottom: '8px',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                axisPointer: {
                    type: 'shadow'
                },
                axisTick: {
                    show: false
                },
                axisLine: {lineStyle: {color: '#EBEDF5'}},
                axisLabel: {
                    interval: 0, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
                    fontFamily: 'PingFangSC-Regular',
                    color: '#666',
                    fontSize: 10,
                    lineHeight: 14,
                    formatter: function (params) {    //字数太长显示...
                        let val = "";
                        let val1 = "";
                        if (params.length > 0 && params.length < 5) {
                            return params;
                        }
                        if (params.length < 9 && params.length > 4) {
                            val = params.substring(0, 4) + "\n";
                            val1 = params.substring(4, 8);
                            return val.concat(val1);

                        }
                        if (params.length > 9) {
                            val = params.substring(0, 4) + "\n";
                            val1 = params.substring(4, 7) + '....';
                            return val.concat(val1);
                        }
                    },

                },
                data: qsName
            }
        ],
        yAxis: [
            {
                name: yLabel,
                type: 'value',
                nameTextStyle: {
                    color: '#666',
                    fontSize: '12px',
                    fontWeight: 400,
                    // align: 'right'
                },
                axisPointer: {
                    type: 'shadow'
                },
                axisTick: {
                    show: false
                },
                splitLine: {
                    lineStyle: {
                        color: '#E6EAF4',
                        type: 'dashed'//虚线
                    }
                },
                axisLine: {lineStyle: {color: '#EBEDF5'}},
                axisLabel: {
                    color: '#666',
                    fontSize: 12,
                    lineHeight: 24,
                    fontFamily: 'PingFangSC-Regular',
                },
            }
        ],
        dataZoom: [
            {
                type: 'slider',
                startValue: 0,
                endValue: 4,
                height: 6,
                filterMode: 'none',
                zoomLock: true,
                bottom: 0,
                show: isShowSlider,
                showDetail: false,
                handleSize: '0%',
                dataBackground: {
                    lineStyle: {
                        opacity: 0
                    },
                    areaStyle: {
                        opacity: 0
                    }
                },
                fillerColor: '#D8D8D8',
                borderColor: '#fff'
            },
            {
                type: 'inside',
                start: 0,
                end: 49,
                height: 10,
                filterMode: 'none',
                zoomLock: true
            }
        ],
        series: {
            name: seriesDataSession.name,
            type: 'bar',
            stack: 'one',
            barWidth: '12px',
            barCategoryGap: '50%',
            barGap: '40%',
            yAxisIndex: 0,
            itemStyle: {
                normal: {
                    color: new echartInitConfig.echarts.graphic.LinearGradient(
                        0, 0, 0, 1,
                        [
                            {offset: 0, color: '#6392FA'},
                            {offset: 1, color: '#2F69F8'},
                        ]
                    )
                },
                emphasis: {
                    color: new echartInitConfig.echarts.graphic.LinearGradient(
                        0, 0, 0, 1,
                        [
                            {offset: 0, color: '#6392FA'},
                            {offset: 1, color: '#2F69F8'},
                        ]
                    )
                }
            },
            label: {
                normal: {
                    show: true,//显示数字
                    position: 'top',
                    align: "center",
                    textStyle: {
                        fontSize: '12',//柱状上的显示的文字
                        color: '#333',
                        fontWeight: 500
                    }
                }
            },
            data: seriesDataSession
        }
    };

}


/**
 *  @description: 举办情况-举办场次排名-柱状图
 *  @author: 何春霞
 *  @date: 2022年9月2日
 *  @param seriesDataRank 图表数据
 */
export function holdRank(echartInitConfig, seriesDataRank) {
    // function object(a,b) {
    //     return a.value-b.value;
    // }
    // if(sort){
    //     seriesDataRank.sort(object);
    // }
    let idsStr = seriesDataRank.map(function (obj, index) {
        return obj.name;
    }).join(",");
    let qsName = idsStr.split(',')
    let isShowSlider = qsName.length > 5 ? true : false;
    let obj = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                type: 'shadow',        // 默认为直线，可选为：'line' | 'shadow'
                label: {
                    formatter: '{value}'
                }
            }
        },
        legend: {
            show: false,
        },
        grid: {
            left: '8px',
            right: '8px',
            top: '24px',
            bottom: '8px',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                axisPointer: {
                    type: 'shadow'
                },
                axisTick: {
                    show: false
                },
                axisLine: {lineStyle: {color: '#EBEDF5'}},
                axisLabel: {
                    interval: 0, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
                    fontFamily: 'PingFangSC-Regular',
                    color: '#666',
                    fontSize: 12,
                    lineHeight: 14,
                },
                data: qsName
            }
        ],
        yAxis: [
            {
                type: 'value',
                nameTextStyle: {
                    color: '#666',
                    fontSize: '12px',
                    fontWeight: 400,
                    align: 'right'
                },
                axisPointer: {
                    type: 'shadow'
                },
                axisTick: {
                    show: false
                },
                splitLine: {
                    lineStyle: {
                        color: '#E6EAF4',
                        type: 'dashed'//虚线
                    }
                },
                axisLine: {lineStyle: {color: '#EBEDF5'}},
                axisLabel: {
                    color: '#666',
                    fontSize: 12,
                    lineHeight: 12,
                    fontFamily: 'PingFangSC-Regular'
                },
            }
        ],
        dataZoom: [
            {
                type: 'slider',
                startValue: 0,
                endValue: 4,
                height: 6,
                filterMode: 'none',
                zoomLock: true,
                bottom: 0,
                show: isShowSlider,
                showDetail: false,
                handleSize: '0%',
                dataBackground: {
                    lineStyle: {
                        opacity: 0
                    },
                    areaStyle: {
                        opacity: 0
                    }
                },
                fillerColor: '#D8D8D8',
                borderColor: '#fff'
            },
            {
                type: 'inside',
                start: 0,
                end: 49,
                height: 10,
                filterMode: 'none',
                zoomLock: true
            }
        ],
        series: {
            name: seriesDataRank.name,
            type: 'bar',
            stack: 'one',
            barWidth: '12px',
            barCategoryGap: '50%',
            barGap: '40%',
            yAxisIndex: 0,
            itemStyle: {
                normal: {
                    color: new echartInitConfig.echarts.graphic.LinearGradient(
                        0, 0, 0, 1,
                        [
                            {offset: 0, color: '#6392FA'},
                            {offset: 1, color: '#2F69F8'},
                        ]
                    )
                },
                emphasis: {
                    color: new echartInitConfig.echarts.graphic.LinearGradient(
                        0, 0, 0, 1,
                        [
                            {offset: 0, color: '#6392FA'},
                            {offset: 1, color: '#2F69F8'},
                        ]
                    )
                }
            },
            label: {
                normal: {
                    show: true,//显示数字
                    position: 'top',
                    align: "center",
                    textStyle: {
                        fontSize: '12',//柱状上的显示的文字
                        color: '#333',
                        fontWeight: 500
                    }
                }
            },
            data: seriesDataRank
        }
    };
    // console.log(obj);
    return obj;
}


/**
 *  @description: 举办情况-活动趋势（场次）-折线图
 *  @author: 何春霞
 *  @date: 2022年9月2日
 *  @param seriesDataTrend 图表数据
 */
export function activityTrend(echartInitConfig, seriesDataTrend, timeRange) {
    let idsStr = seriesDataTrend.map(function (obj, index) {
        return obj.name;
    }).join(",");
    let qsName = idsStr.split(',')
    let isShowSlider = qsName.length > 5 ? true : false;
    return {
        tooltip: {
            trigger: 'axis',
            axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                type: 'shadow',        // 默认为直线，可选为：'line' | 'shadow'
                label: {
                    formatter: '{value}'
                }
            }
        },
        legend: {
            show: false,
        },
        grid: {
            left: 16,
            right: 24,
            bottom: 8,
            top: 26,
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            axisPointer: {
                type: 'shadow'
            },
            axisTick: {
                show: false
            },
            axisLine: {lineStyle: {color: '#EBEDF5'}},
            axisLabel: {
                interval: 0, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
                fontFamily: 'PingFangSC-Regular',
                color: '#666',
                fontSize: 12,
                lineHeight: 14,
                marginRight: -10,
                formatter: function (value) {
                    if (timeRange === 'actDay') {
                        let val = value.substring(0, 2)
                        let val1 = value.substring(2, 4)
                        return val + '/' + val1;
                    }
                    if (timeRange === 'actWeekMonth') {
                        return '第' + value + '周';
                    }
                    if (timeRange === 'actMonthYear') {
                        if (value < 10) {
                            return '0' + value + '月';
                        } else return value + '月';
                    }

                },
            },
            data: qsName
        },
        yAxis: {
            type: 'value',
            nameTextStyle: {
                color: '#666',
                fontSize: '12px',
                fontWeight: 400,
                align: 'right'
            },
            axisPointer: {
                type: 'shadow'
            },
            axisTick: {
                show: false
            },
            splitLine: {
                lineStyle: {
                    color: '#E6EAF4',
                    type: 'dashed'//虚线
                }
            },
            axisLine: {lineStyle: {color: '#EBEDF5'}},
            axisLabel: {
                color: '#666',
                fontSize: 12,
                lineHeight: 12,
                fontFamily: 'PingFangSC-Regular'
            }
        },
        dataZoom: [
            {
                type: 'slider',
                startValue: 0,
                endValue: 4,
                height: 6,
                filterMode: 'none',
                zoomLock: true,
                bottom: 0,
                show: isShowSlider,
                showDetail: false,
                handleSize: '0%',
                dataBackground: {
                    lineStyle: {
                        opacity: 0
                    },
                    areaStyle: {
                        opacity: 0
                    }
                },
                fillerColor: '#D8D8D8',
                borderColor: '#fff'
            },
            {
                type: 'inside',
                start: 0,
                end: 49,
                height: 10,
                filterMode: 'none',
                zoomLock: true
            }
        ],
        series: [
            {
                type: 'line',
                symbol: 'circle',
                symbolSize: 6,
                itemStyle: {
                    color: '#2F69F8'
                },
                lineStyle: {
                    width: 1
                },
                areaStyle: {
                    normal: {
                        color: new echartInitConfig.echarts.graphic.LinearGradient(
                            0, 0, 0, 1,
                            [
                                {offset: 0, color: '#6392FA33'},
                                {offset: 1, color: '#2F69F800'},
                            ]
                        )
                    },
                    emphasis: {
                        color: new echartInitConfig.echarts.graphic.LinearGradient(
                            0, 0, 0, 1,
                            [
                                {offset: 0, color: '#6392FA'},
                                {offset: 1, color: '#2F69F800'},
                            ]
                        )
                    }
                },
                label: {
                    normal: {
                        show: true,//显示数字
                        position: 'top',
                        align: "center",
                        textStyle: {
                            fontSize: '12',//柱状上的显示的文字
                            color: '#333',
                            fontWeight: 500
                        }
                    }
                },
                data: seriesDataTrend
            }
        ]
    };
}


/**
 *  @description: 双柱图
 *  @author: 王雅琪
 *  @date: 2022年9月2日
 *  @param seriesData 图表数据
 *  @param seriesNameOne 左侧y轴名称
 *  @param seriesNameTwo 右侧y轴名称
 *  @param labelShow 是否展示数值
 */
export function doubleDataHistogram(echartInitConfig, seriesData, seriesNameOne = '', seriesNameTwo = '',isWan=false, labelShow = true) {
    let xAxisData = new Array();
    let seriesDataTempOne = new Array();//第一个柱形图数据
    let seriesDataTempTwo = new Array();//第二个柱形图数据
    seriesData.forEach((item) => {
        xAxisData.push(item.name);
        if(isWan){
            seriesDataTempOne.push(item.seriesDataOne);
            seriesDataTempTwo.push(Math.round((item.seriesDataTwo)/10000));
        }else{
            seriesDataTempOne.push(item.seriesDataOne);
            seriesDataTempTwo.push(item.seriesDataTwo);
        }
    });
    let maxArr = calMax(seriesDataTempOne, seriesDataTempTwo);//获取根据柱形图数据计算的刻度标签最大值
    let isShowSlider = xAxisData.length > 5 ? true : false;
    let series = [
        {
            name: seriesNameOne,
            type: 'bar',
            stack: 'one',
            barWidth: '12px',
            barGap: '40%',
            yAxisIndex: 0,
            itemStyle: {
                color: '#98CEFF'
            },
            label: {
                normal: {
                    show: true,//显示数字
                    position: 'top',
                    align: "center",
                    textStyle: {
                        fontSize: '10',//柱状上的显示的文字
                        color: '#333',
                        fontWeight: 500
                    }
                }
            },
            data: seriesDataTempOne
        },
        {
            name: seriesNameTwo,
            type: 'bar',
            stack: 'two',
            barWidth: '12px',
            yAxisIndex: 1,
            itemStyle: {
                normal: {
                    color: new echartInitConfig.echarts.graphic.LinearGradient(
                        0, 0, 0, 1,
                        [
                            {offset: 0, color: '#6392FA'},
                            {offset: 1, color: '#2F69F8'},
                        ]
                    )
                },
                emphasis: {
                    color: new echartInitConfig.echarts.graphic.LinearGradient(
                        0, 0, 0, 1,
                        [
                            {offset: 0, color: '#6392FA'},
                            {offset: 1, color: '#2F69F8'},
                        ]
                    )
                }
            },
            label: {
                show: true,
                position: "top",
                borderWidth: 20,
                fontSize: 10,
                fontFamily: "PingFangSC-Medium",
                color: "#333333",
                fontWeight: 500,
                align: "center",
            },
            data: seriesDataTempTwo
        }
    ];
    if (!labelShow) {
        series.forEach((item) => {
            delete item.label
        })
    }
    let tooltip = {
        trigger: 'axis',
        axisPointer: {            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow',        // 默认为直线，可选为：'line' | 'shadow'
            label: {
                formatter: '{value}'
            }
        }
    }
    return {
        tooltip,
        grid: {
            left: '8px',
            right: '8px',
            bottom: '6px',
            containLabel: true
        },
        legend: {
            data: [seriesNameOne, seriesNameTwo],
            itemWidth: 6,
            itemHeight: 6,
            selectorLabel: {
                verticalAlign: "bottom",
            },
            top: 5,
            right: 5,
            width: "83%",
            align: 'left',
            itemGap: 30,
        },
        xAxis: [
            {
                type: 'category',
                data: xAxisData,
                axisPointer: {
                    type: 'shadow'
                },
                axisTick: {
                    show: false
                },
                axisLine: {lineStyle: {color: '#EBEDF5'}},
                axisLabel: {
                    interval: 0, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
                    fontFamily: 'PingFangSC-Regular',
                    color: '#666',
                    fontSize: 12,
                    lineHeight: 14,
                    formatter: function (params) {//字数太长换行
                        return CharacterWrap(params, 4)
                    }
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '场次',
                nameTextStyle: {
                    color: '#666',
                    fontSize: '12px',
                    fontWeight: 400,
                    align: 'right'
                },
                axisPointer: {
                    type: 'shadow'
                },
                axisTick: {
                    show: false
                },
                splitLine: {
                    lineStyle: {
                        color: '#E6EAF4',
                        type: 'dashed'//虚线
                    }
                },
                axisLine: {lineStyle: {color: '#EBEDF5'}},
                axisLabel: {
                    color: '#666',
                    fontSize: 10,
                    lineHeight: 12,
                    fontFamily: 'PingFangSC-Regular'
                },
                max: maxArr[0],
                interval: maxArr[0] / 5
            },
            {
                type: 'value',
                name: isWan?'金额（万）':'金额（元）',
                nameTextStyle: {
                    color: '#666',
                    fontSize: '12px',
                    fontWeight: 400
                },
                axisPointer: {
                    type: 'shadow'
                },
                axisTick: {
                    show: true
                },
                splitLine: {
                    lineStyle: {
                        color: '#E6EAF4',
                        type: 'dashed'//虚线
                    }
                },
                axisLine: {lineStyle: {color: '#EBEDF5'}},
                axisLabel: {
                    color: '#666',
                    fontSize: 10,
                    lineHeight: 12,
                    fontFamily: 'PingFangSC-Regular'
                },
                max: maxArr[1],
                interval: maxArr[1] / 5
            }
        ],
        dataZoom: [
            {
                type: 'slider',
                xAxisIndex: 0,
                startValue: 0,
                endValue: 4,
                height: 6,
                show: isShowSlider,
                filterMode: 'none',
                zoomLock: true,
                bottom: 0,
                showDetail: false,
                handleSize: '0%',
                dataBackground: {
                    lineStyle: {
                        opacity: 0
                    },
                    areaStyle: {
                        opacity: 0
                    }
                },
                fillerColor: '#D8D8D8',
                borderColor: '#fff'
            },
            {
                type: 'inside',
                xAxisIndex: 0,
                startValue: 0,
                endValue: 4,
                height: 10,
                filterMode: 'none',
                zoomLock: true
            }
        ],
        series
    };

}

/**
 *  @description: 折线趋势图
 *  @author: 王雅琪
 *  @date: 2022年9月7日
 *  @param seriesData 图表数据
 */
export function targetTrendProgress(echartInitConfig, seriesData) {
    let idsStr = seriesData.map(function (obj, index) {
        return obj.name;
    }).join(",");
    let qsName = idsStr.split(',');
    let isShowSlider = qsName.length > 5 ? true : false;
    return {
        tooltip: {
            trigger: 'axis',
            axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                type: 'shadow',        // 默认为直线，可选为：'line' | 'shadow'
                label: {
                    formatter: '{value}'
                }
            }
        },
        legend: {
            show: false,
        },
        grid: {
            left: 0,
            right: 16,
            bottom: 6,
            top: 26,
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            axisPointer: {
                type: 'shadow'
            },
            axisTick: {
                show: false
            },
            axisLine: {lineStyle: {color: '#EBEDF5'}},
            axisLabel: {
                interval: 0, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
                fontFamily: 'PingFangSC-Regular',
                color: '#666',
                fontSize: 12,
                lineHeight: 14
            },
            data: qsName
        },
        yAxis: {
            type: 'value',
            nameTextStyle: {
                color: '#666',
                fontSize: '12px',
                fontWeight: 400,
                align: 'right'
            },
            axisPointer: {
                type: 'shadow'
            },
            axisTick: {
                show: false
            },
            splitLine: {
                lineStyle: {
                    color: '#E6EAF4',
                    type: 'dashed'//虚线
                }
            },
            axisLine: {lineStyle: {color: '#EBEDF5'}},
            axisLabel: {
                color: '#666',
                fontSize: 12,
                lineHeight: 12,
                fontFamily: 'PingFangSC-Regular'
            }
        },
        dataZoom: [
            {
                type: 'slider',
                xAxisIndex: 0,
                startValue: 0,
                endValue: 5,
                height: 6,
                show: isShowSlider,
                filterMode: 'none',
                zoomLock: true,
                bottom: 0,
                showDetail: false,
                handleSize: '0%',
                dataBackground: {
                    lineStyle: {
                        opacity: 0
                    },
                    areaStyle: {
                        opacity: 0
                    }
                },
                fillerColor: '#D8D8D8',
                borderColor: '#fff'
            },
            {
                type: 'inside',
                xAxisIndex: 0,
                startValue: 0,
                endValue: 4,
                height: 10,
                filterMode: 'none',
                zoomLock: true
            }
        ],
        series: [
            {
                type: 'line',
                symbol: 'circle',
                symbolSize: 6,
                itemStyle: {
                    color: '#2F69F8'
                },
                lineStyle: {
                    width: 1
                },
                areaStyle: {
                    normal: {
                        color: new echartInitConfig.echarts.graphic.LinearGradient(
                            0, 0, 0, 1,
                            [
                                {offset: 0, color: '#6392FA33'},
                                {offset: 1, color: '#2F69F800'},
                            ]
                        )
                    },
                    emphasis: {
                        color: new echartInitConfig.echarts.graphic.LinearGradient(
                            0, 0, 0, 1,
                            [
                                {offset: 0, color: '#6392FA'},
                                {offset: 1, color: '#2F69F800'},
                            ]
                        )
                    }
                },
                label: {
                    normal: {
                        show: true,//显示数字
                        position: 'top',
                        align: "center",
                        textStyle: {
                            fontSize: '12',//柱状上的显示的文字
                            color: '#333',
                            fontWeight: 500
                        }
                    }
                },
                data: seriesData
            }
        ]
    };
}

/**
 *  @description: 刻度标签换行
 *  @author: 王雅琪
 *  @date: 2022年8月26日
 */
export function CharacterWrap(params, provideNumber) {
    if (params.length > 0 && params.length <= provideNumber) {
        return  params;
    }
    if (params.length <= provideNumber * 2 && params.length > provideNumber) {
        return params.substring(0, provideNumber) + "\n" + params.substring(provideNumber, provideNumber * 2);
    }
    if (params.length > provideNumber * 2) {
       return params.substring(0, provideNumber) + "\n" + params.substring(provideNumber, provideNumber * 2 - 1) + '...';
    }
}

/**
 *  @description: 双柱图y轴刻度最大值
 *  @author: 王雅琪
 *  @date: 2022年9月5日
 *  @param seriesDataTempOne seriesDataTempTwo
 */
export function calMax(seriesDataTempOne, seriesDataTempTwo) { // arr是传入的series
    const yAxisData = [seriesDataTempOne, seriesDataTempTwo]  // 连个y轴，用两个数组将不同y轴对应数据分开
    const maxArr = new Array();// 用来缓存计算出来的所有Y轴的最大值
    yAxisData.forEach((item, index) => {
        let max = Number(item[0])
        item.forEach((i, index) => {
            if (Number(i) && Number(i) > max) {
                max = Number(i)
            }
        })
        if (max > 0) {
            if (max < 50) {
                let maxInt = Math.ceil(max / 25);  // 向上以5的倍数取整
                max = maxInt * 25;  // 设置最大值
            } else {
                let maxInt = Math.ceil(max / 50);  // 向上以5的倍数取整
                max = maxInt * 50;  // 设置最大值
            }
        } else {
            max = 0
            // 这里如果最大值<= 0，我直接取0， 根据需求自己确定
        }
        maxArr[index] = max
    })
    return maxArr
}
