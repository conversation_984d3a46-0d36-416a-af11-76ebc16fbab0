<!--
 @file 点击进入员工画像
 <AUTHOR>
 @Created 2022/10/25
-->
<template>
    <link-page class="staff-portrait-main">
        <!--        <lnk-taps style="height: 46px" :class="memberTabsActive ===  memberTabsOption[1] ? 'lnk-tabs-container1': 'lnk-tabs-container0'"  :taps="memberTabsOption" v-model="memberTabsActive" v-if="memberTabsOption.length !==1 || showTabFlag"></lnk-taps>-->
        <lnk-taps style="height: 46px"
                  :class="memberTabsActive ===  memberTabsOption[1] ? 'lnk-tabs-container1': 'lnk-tabs-container0'"
                  :taps="memberTabsOption" v-model="memberTabsActive" v-if="memberTabsOption.length !==1"></lnk-taps>
        <view v-if="memberTabsActive.seq === '1' ">
            <staff-portrait-team-member ref="teamMember"></staff-portrait-team-member>
        </view>
        <view v-if="memberTabsActive.seq === '2' ">
            <staff-portrait-personal-detail></staff-portrait-personal-detail>
        </view>
    </link-page>
</template>

<script>
import Taro from "@tarojs/taro";
import lnkTaps from "../../../core/lnk-taps/lnk-taps";
import StaffPortraitTeamMember from "./components/staff-portrait-team-member";
import StaffPortraitPersonalDetail from "./components/staff-portrait-personal-detail";


export default {
    name: "staff-portrait-main",
    components: {
        StaffPortraitPersonalDetail,
        StaffPortraitTeamMember,
        lnkTaps,
    },
    data() {
        const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
        const positionType = userInfo.positionType   //获取当前用户职位信息
        const memberTabsOption = [
            {name: "团队成员", seq: "1", alias: 'member'},
            {name: "个人画像", seq: "2", alias: 'personal'}
        ];
        const postnType1 = ['Salesman', 'SalesTeamLeader', 'CustServiceSpecialist'];
        const postnType2 = ['SalesSupervisor',
            'GroupBuyManager',
            'AccountManager',
            'CustServiceManager',
            'VipManager',
            'CustServiceSpecialist',
            'CustServiceSupervisor',
            'SalesTeamLeader',
            'SalesChannelManger'];
        const postnType3 = ['BattleCommander',
            'BPSalesManager',
            'HeadquartersStuff',
            'RInternalStaff',
            'CMDeptDirector',
            'ChannelSupervisor',
            'MemberManager',
            'BLRegionManager',
            'BPRegionManager',
            'BPDeptManager',
            'SalesGeneralManager',
            'BrandManager',
            'InternalStaff',
            'SysAdmin',
            'CityManager',
            'SalesAreaManager',
            'SalesRegionManager',
            'GeneralManager',
            'CInternalStaff',
            'BrandSysAdmin',
            'RegionSysAdmin'];
        return {
            userInfo: {},        //用户信息
            memberTabsOption,
            memberTabsActive: {},
            positionType,       //当前登录人职位
            postnType1,       //业务代表类，展示“个人画像”
            postnType2,       //业务主管类，展示“团队成员”和个人画像
            postnType3,       //经理类，展示“团队成员”
            showTabFlag: true, //是否展示tab栏
        }
    },
    created() {
    },
    mounted() {
        if (this.postnType1.includes(this.positionType)) {
            this.memberTabsOption.shift();
        }
        if (this.postnType3.includes(this.positionType)) {
            this.memberTabsOption.pop();
        }
        this.memberTabsActive = this.memberTabsOption[0];
    },
    methods: {
        /**
         * @createdBy  何春霞
         * @date  2022/10/31
         * @methods onBack
         * @para
         * @description 返回界面重新加载内容
         */
        async onBack(data) {
            if(this.memberTabsActive.seq === '2' ) return
            this.$utils.showLoading('数据加载中...');
            await this.$refs.teamMember.memberListOption.methods.reload();
            this.$utils.hideLoading();
        },
        /**
         * @createdBy  何春霞
         * @date  2022/10/31
         * @methods gotoDetail
         * @para
         * @description 跳转到员工详情
         */
        gotoDetail() {
            if (this.postnType2.includes(this.positionType)) {
                this.$nav.push('/pages/echart/lzlj/staff-portrait/staff-portrait-detail-page.vue')
            }
        },
    }
}
</script>

<style lang="scss">
.staff-portrait-main {
    .lnk-tabs-container0 {
        .lnk-tabs {
            white-space: nowrap;
            height: 92px;
            background-color: #FFFFFF;
            color: #595959;
            position: fixed;
            border-top: 0px;

            .lnk-tabs-item .label-name-line .line {
                height: 8px;
                width: 56px;
                border-radius: 16px 16px 0 0;
                background-color: #2f69f8;
                -webkit-box-shadow: 0 1px 4px 0 rgba(47, 105, 248, 0.63);
                box-shadow: 0 3px 8px 0 rgba(47, 105, 248, 0.63);
                margin-top: -8px;
            }

            .active {
                color: #2f69f8;
            }
        }
    }

    .lnk-tabs-container1 {
        .lnk-tabs {
            white-space: nowrap;
            height: 92px;
            background-color: #2F69F8;
            color: #fff;
            position: fixed;
            border-top: 0px;

            .lnk-tabs-item .label-name-line .line {
                height: 8px;
                width: 56px;
                border-radius: 16px 16px 0 0;
                background-color: #fff;
                -webkit-box-shadow: 0 1px 4px 0 rgba(47, 105, 248, 0.63);
                box-shadow: 0 3px 8px 0 rgba(47, 105, 248, 0.63);
                margin-top: -8px;
            }

            .active {
                color: #FFFFFF;
            }
        }
    }
}
</style>
