import {$utils} from "src/utils/$utils";


/**
 *  @description: 绘制饼图基础色
 *  @author: 王雅琪
 *  @date: 2022/08/19
 */
export function echartColor(echartInitConfig, params, pieColor) {
    let colorList = new Array()
    if (pieColor.length > 0) {
        colorList = pieColor
    } else {
        let baseColorList = [
            {
                c1: '#9CEFD5',  //管理
                c2: '#28C285'
            }, {
                c1: '#F8DE3C',  //管理
                c2: '#EFBA1C'
            }, {
                c1: '#69CAFF',  //管理
                c2: '#36ACEB'
            }, {
                c1: '#81F3EF',  //管理
                c2: '#5FCACE'
            }
        ]
        colorList = $utils.deepcopy(baseColorList);
        if (params.dataIndex >= colorList.length) {
            let num = Math.ceil((params.dataIndex + 1) / baseColorList.length);
            for (let i = 0; i < num - 1; i++) {
                colorList = colorList.concat(baseColorList);
            }
        }
    }
    return new echartInitConfig.echarts.graphic.LinearGradient(0, 0, 0, 1, [{ //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
        offset: 0,
        color: colorList[params.dataIndex].c1
    }, {
        offset: 1,
        color: colorList[params.dataIndex].c2
    }])
}

/**
 *  @description: 环状图
 *  @author: 王雅琪
 *  @date: 2022年10月24日
 *  @param seriesData 图表数据
 *  @param labelData 图中心标签文字
 *  @param pieColor 饼图颜色
 *  @param wrapNum 标签文字一行字数
 *  @param isPercent 是否以百分比形式显示
 */
export function circularGraph(echartInitConfig, seriesData, labelData, pieColor, wrapNum, isPercent = 0) {
    return {
        grid: {
            right: 20
        },
        legend: {
            top: 'middle',
            right: 'right',
            icon: "circle",
            itemWidth: 8,
            itemHeight: 8,
            selectedMode: false,
            formatter: function (name, value) {
                let total = 0;
                let faultVal;
                for (let i = 0; i < seriesData.length; i++) {
                    total += Number(seriesData[i].value);//总数
                    if (seriesData[i].name == name) {
                        faultVal = Number(seriesData[i].value);
                    }
                }
                if (total === 0) return `${name} ${faultVal}， 0%`
                let p = Math.round(((faultVal / total) * 100));
                if (isPercent === 0) return `${name}   ${faultVal}`
                else if (isPercent === 1) return `${name}      ${p}%`
                return `${name} ${faultVal}， ${p}%`
            }
        },
        series: [
            {
                type: 'pie',
                radius: ['65%', '100%'],
                left: '-55%',
                startAngle: 270,
                silent: 'ture',
                itemStyle: {
                    borderColor: '#fff',
                    borderWidth: 2,
                    normal: {
                        color: function (params) {
                            return echartColor(echartInitConfig, params, pieColor);
                        }
                    }
                },
                label: {
                    show: true,
                    position: 'center',
                    formatter: function (params) {
                        let name = CharacterWrap(labelData, wrapNum);
                        return '{c1|' + name + '}';
                    },
                    rich: {
                        c1: {
                            lineHeight: 14,
                            fontSize: 12,
                            color: '#212223'
                        },
                        d1: {
                            lineHeight: 18,
                            fontSize: 12,
                            color: '#212223'
                        },
                    }
                },
                labelLine: {
                    show: false
                },
                data: seriesData
            }
        ]
    };
}

/**
 *  @description: 简单柱形图
 *  @author: 王雅琪
 *  @date: 2022年9月2日
 *  @param seriesData 图表数据
 */
export function targetSimpleDataHistogram(echartInitConfig, seriesData) {
    let idsStr = seriesData.map(function (obj, index) {
        return obj.name;
    }).join(",");
    let qsName = idsStr.split(',')
    let isShowSlider = qsName.length > 5 ? true : false;
    return {
        tooltip: {
            trigger: 'axis',
            axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                type: 'shadow',        // 默认为直线，可选为：'line' | 'shadow'
                label: {
                    formatter: '{value}'
                }
            }
        },
        legend: {
            show: false,
        },
        grid: {
            left: '0px',
            right: '0px',
            top: '18px',
            bottom: '0px',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                axisPointer: {
                    type: 'shadow'
                },
                axisTick: {
                    show: false
                },
                axisLine: {lineStyle: {color: '#EBEDF5'}},
                axisLabel: {
                    interval: 0, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
                    fontFamily: 'PingFangSC-Regular',
                    color: '#666',
                    fontSize: 12,
                    lineHeight: 14,
                    formatter: function (params) {//字数太长换行
                        return CharacterWrap(params, 6)
                    }
                },
                data: qsName
            }
        ],
        yAxis: [
            {
                type: 'value',
                nameTextStyle: {
                    color: '#666',
                    fontSize: '12px',
                    fontWeight: 400,
                    align: 'right'
                },
                axisPointer: {
                    type: 'shadow'
                },
                axisTick: {
                    show: false
                },
                splitLine: {
                    lineStyle: {
                        color: '#E6EAF4',
                        type: 'dashed'//虚线
                    }
                },
                axisLine: {lineStyle: {color: '#EBEDF5'}},
                axisLabel: {
                    color: '#666',
                    fontSize: 12,
                    lineHeight: 12,
                    fontFamily: 'PingFangSC-Regular'
                },
            }
        ],
        series: {
            name: seriesData.name,
            type: 'bar',
            stack: 'one',
            barWidth: '30px',
            yAxisIndex: 0,
            itemStyle: {
                normal: {
                    color: new echartInitConfig.echarts.graphic.LinearGradient(
                        0, 0, 0, 1,
                        [
                            {offset: 0, color: '#6392FA'},
                            {offset: 1, color: '#2F69F8'},
                        ]
                    )
                },
                emphasis: {
                    color: new echartInitConfig.echarts.graphic.LinearGradient(
                        0, 0, 0, 1,
                        [
                            {offset: 0, color: '#6392FA'},
                            {offset: 1, color: '#2F69F8'},
                        ]
                    )
                }
            },
            label: {
                normal: {
                    show: true,//显示数字
                    position: 'top',
                    align: "center",
                    textStyle: {
                        fontSize: '12',//柱状上的显示的文字
                        color: '#333',
                        fontWeight: 500
                    }
                }
            },
            data: seriesData
        }
    };
}

/**
 *  @description: 刻度标签换行
 *  @author: 王雅琪
 *  @date: 2022年8月26日
 */
export function CharacterWrap(params, provideNumber) {
    let val = "";
    let val1 = "";
    let newParamsName = "";
    if (params.length > 0 && params.length <= provideNumber) {
        newParamsName = params;
    }
    if (params.length <= provideNumber * 2 && params.length > provideNumber) {
        val = params.substring(0, provideNumber) + "\n";
        val1 = params.substring(provideNumber, provideNumber * 2);
        newParamsName = val.concat(val1);
    }
    if (params.length > provideNumber * 2) {
        val = params.substring(0, provideNumber) + "\n";
        val1 = params.substring(provideNumber, provideNumber * 2 - 1) + '...';
        newParamsName = val.concat(val1);
    }
    return newParamsName
}
