<!--
 @file 团队成员
 <AUTHOR>
 @Created 2022/10/25
-->
<template>
    <link-page class="team-member">
        <link-auto-list :option="memberListOption" hideCreateButton
                        :searchInputBinding="{props:{placeholder:'员工工号/姓名'}}">
            <template slot-scope="{data,index}">
                <view :key="index" :data="data" :arrow="false" class="team-member-item">
                    <view class="team-member-content" slot="item">
                        <view class="team-member-top" @tap="gotoDetail(data)">
                            <view class="avatar">
                                <image v-if="!data.userProfile" class="avatar-img"
                                       :src="data.userGender === 'MALE' ? $imageAssets.manImage : $imageAssets.womanImage"></image>
                                <image v-else class="avatar-img" :src="data.userProfile"></image>
                            </view>
                            <view class="info">
                                <view class="member-name">{{ data.userName }}</view>
                                <link-icon v-if="data.userGender"
                                           :class="data.userGender === 'MALE' ? 'member-gender-male' : 'member-gender-female'"
                                           :icon="data.userGender === 'MALE' ? 'icon-man' : 'icon-woman'"></link-icon>
                                <view class="member-info1">{{ data.userNum }} · {{ data.orgName }} ·
                                    {{ data.postnType | lov('POSTN_TYPE') }} · {{ data.staffType | lov('STAFF_TYPE') }}
                                </view>
                                <view class="member-info2">联系电话：{{ data.contactPhone }}</view>
                            </view>
                        </view>
                        <view class="member-tags">
                            <link-icon icon="icon-down" :class="nowIndex[index] ?  'up-icon':'down-icon' "
                                       @tap="isShowTag(index)"/>
                            <view class="member-all-tags">
                                <view class="tags-item-blue">
                                    {{ '上月考勤完成率' + (Number(data.normalPostratio * 100)).toFixed(0) + '%' }}
                                </view>
                                <view class="tags-item-green">
                                    {{ '上月终端拜访数量' + data.termVisitNum}}
                                </view>
                                <view class="tags-item-blue" v-if="nowIndex[index]">{{ '上月市场活动场次' + data.actApplyNum }}
                                </view>
                                <view class="tags-item-green" v-if="nowIndex[index]">
                                    {{ '上月新跟进终端数' + data.newTermFollow }}
                                </view>
                                <view class="tags-item-yellow" v-if="data.skills && nowIndex[index]">{{ data.skills }}
                                </view>
                                <view class="tags-item-yellow" v-if="data.highPerform && nowIndex[index]">
                                    {{ data.highPerform }}
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>

export default {
    name: "staff-portrait-team-member",

    data() {
        const currentMonth = new Date().getMonth(),
            currentYear = new Date().getFullYear();
        const memberListOption = new this.AutoList(this, {
            url: {
                queryByExamplePage: 'action/link/empPortrait/queryByExamplePage',
            },
            itemPath: '/pages/echart/lzlj/staff-portrait/staff-portrait-detail-page',
            param: {
                rows: 10,
                filtersRaw: [],
                startDate: this.$date.format(new Date(currentYear, currentMonth - 1), 'YYYY-MM-DD'),
                endDate: this.$date.format(new Date(currentYear, currentMonth), 'YYYY-MM-DD'),
            },
            sortOptions: null,
            searchFields: ['userNum', 'userName'],
        });
        return {
            memberListOption,
            currentMonth,
            currentYear,
            showTagFlag: [],
            nowIndex: []
        }
    },
    created() {
    },

    methods: {
        /**
         * @createdBy  何春霞
         * @date  2022/11/1
         * @methods isShowTag
         * @para
         * @description 点击展示/收起标签
         */
        isShowTag(index) {
            this.$set(this.nowIndex, index, !this.nowIndex[index])
        },
        /**
         * @createdBy  何春霞
         * @date  2022/10/31
         * @methods gotoDetail
         * @para
         * @description 跳转到员工详情
         */
        gotoDetail(data) {
            this.$nav.push('/pages/echart/lzlj/staff-portrait/staff-portrait-detail-page', {data: data})
        },
    }
}
</script>

<style lang="scss">
.team-member {
    background-color: #f2f2f2;

    .team-member-item {
        padding: 24px;
        margin: 24px;
        background-color: #FFFFFF;
        border-radius: 16px;

        .team-member-content {

            .team-member-top {
                display: flex;

                .avatar-img {
                    width: 130px;
                    height: 130px;
                    border-radius: 50%;
                    margin: 24px;
                }

                .info {
                    //width: 80%;
                    padding: 36px 0;

                    .member-name {
                        display: inline;
                        //margin-top: 34px;
                        font-size: 32px;
                        line-height: 34px;
                        font-family: PingFangSC-S0pxibold;
                        font-weight: 600;
                        color: #212223;
                    }

                    .member-gender-male {
                        display: inline-block;
                        width: 28px;
                        height: 28px;
                        font-size: 28px;
                        //margin-top: 34px;
                        line-height: 34px;
                        background: #91d5f6;
                        color: #FFFFFF;
                        border-radius: 14px;
                    }

                    .member-gender-female {
                        display: inline-block;
                        width: 28px;
                        height: 28px;
                        font-size: 28px;
                        //margin-top: 34px;
                        line-height: 34px;
                        color: #FFFFFF;
                        background: #FFA6D3;
                        border-radius: 14px;
                    }

                    .member-info1 {
                        font-family: PingFangSC-Regular;
                        font-weight: 400;
                        font-size: 24px;
                        color: #212223;
                        line-height: 40px;
                    }

                    .member-info2 {
                        font-family: PingFangSC-Regular;
                        font-weight: 400;
                        font-size: 24px;
                        color: #6A6D75;
                        line-height: 40px;
                    }


                }

            }

            .member-tags {
                position: relative;

                .down-icon {
                    position: absolute;
                    right: 24px;
                    top: 12px;
                    font-size: 36px;
                    color: #333333;
                }

                .up-icon {
                    position: absolute;
                    right: 24px;
                    top: 12px;
                    font-size: 36px;
                    color: #333333;
                    transform: rotate(180deg);
                }

                .member-all-tags {
                    display: flex;
                    flex-wrap: wrap;
                    width: 85%;

                    .tags-item-blue {
                        height: 40px;
                        font-weight: 400;
                        font-size: 24px;
                        color: #209CFF;
                        text-align: center;
                        line-height: 40px;
                        padding: 0px 16px;
                        background-color: #E6F4FF;
                        margin: 10px;
                        font-family: PingFangSC-Regular;
                        border-radius: 4px;
                    }

                    .tags-item-green {
                        height: 40px;
                        font-weight: 400;
                        font-size: 24px;
                        color: #52AB71;
                        text-align: center;
                        line-height: 40px;
                        padding: 0px 16px;
                        background-color: #EDF7F1;
                        margin: 10px;
                        font-family: PingFangSC-Regular;
                        border-radius: 4px;
                    }

                    .tags-item-yellow {
                        height: 40px;
                        font-weight: 400;
                        font-size: 24px;
                        color: #DD9F0E;
                        text-align: center;
                        line-height: 40px;
                        padding: 0px 16px;
                        background-color: #FFF7E6;
                        margin: 10px;
                        font-family: PingFangSC-Regular;
                        border-radius: 4px;
                    }
                }
            }

        }
    }
}
</style>
