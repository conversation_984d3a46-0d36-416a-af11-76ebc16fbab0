<template>
    <view class="echarts-content">
        <link-echart
            :option="echartsOption"
            :height=" 120 + 'px'"
            :force-use-old-canvas="false"
        />
    </view>
</template>

<script>
import {circularGraph} from "../staff-portrait.utils";

export default {
    name: "circular-echarts",
    props: {
        seriesData: {
            required: true
        },
        isProgress: {
            default: false
        },
        label: {
            require: true
        },
        wrapNum: {
            default: 5
        },
        isPercent: {
            default: 1
        }
    },
    watch: {
        seriesData: {
            handler() {
                this.setEcharts();
            },
            deep: true
        }
    },
    data() {
        return {
            echartsOption: null,
            pieColor: [{c1: '#326EE9', c2: '#7AB5FF'}, {c1: '#EFF2F6', c2: '#EFF2F6'}]
        }
    },
    created() {
        this.setEcharts();
    },
    methods: {
        setEcharts() {
            if (this.$utils.isEmpty(this.seriesData)) return;
            if (this.isProgress) {
                this.echartsOption = echartInitConfig => circularGraph(echartInitConfig, this.seriesData, this.label, this.pieColor, this.wrapNum)
            } else {
                this.echartsOption = echartInitConfig => circularGraph(echartInitConfig, this.seriesData, this.label, [], this.wrapNum, this.isPercent)
            }
        }
    }
}
</script>

<style lang="scss">
.echarts-content {

}
</style>
