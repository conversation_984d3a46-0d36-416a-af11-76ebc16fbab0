<template>
    <view class="staff-portrait-evaluate-detail" v-if="areaOption.length">
        <view class="head-content">
            <view class="item-bg">
                <view>市场阶段</view>
                <view>{{detailData.levelName}}</view>
            </view>
            <view class="item-bg zongdefen">
                <view>总得分</view>
                <view>{{detailData.totalScore||0}}</view>
            </view>
        </view>
        <view class="flex-box">
            <view class="flex-title">指标详情<text class="flex-bz">排名数据每日凌晨更新</text></view>
            <view v-if="areaOption.length" style="display: flex;align-items: center;padding: 0 5px;border-radius: 20px;font-weight: nomal">
                <picker :value="currentIndex" @change="areaChange"
                        :range="areaOption">
                    <select-button :label="areaOption[currentIndex]" :showLength='4' downIcon></select-button>
                </picker>
            </view>
        </view>
        <view class="rank-box">
            <view class="rank-title"><view class="title-line"></view> 消费者开瓶数据</view>
            <view class="rank-content" @tap='todetali(item)' v-for="(item,index) in rankList" :key="index+'ranklist'"  style="background-image: url('https://xtzh-public-1256376813.cos.ap-chengdu.myqcloud.com/static/images/emp-portrait/wujiangpai.png');">
                <view class="rank-name">{{item.subEvaDimension | lov('SUB_EVA_DIMENSION')}}</view>
                <view><text class="rank-num">{{item.endData}}</text>(瓶)</view>
                <view class="rank-detail">当前排名{{item.ranking}}/{{item.totalUser}}</view>
            </view>
            <!-- <view class="rank-content" @tap='todetali' style="background-image: url('https://xtzh-public-1256376813.cos.ap-chengdu.myqcloud.com/static/images/emp-portrait/yinpai.png');">
                <view class="rank-name">头曲终端开瓶数</view>
                <view><text class="rank-num">31288</text>(瓶)</view>
                <view class="rank-detail">当前排名1/100</view>
            </view>
            <view class="rank-content" @tap='todetali' style="background-image: url('https://xtzh-public-1256376813.cos.ap-chengdu.myqcloud.com/static/images/emp-portrait/tongpai.png');">
                <view class="rank-name">头曲终端开瓶数</view>
                <view><text class="rank-num">31288</text>(瓶)</view>
                <view class="rank-detail">当前排名1/100</view>
            </view> -->
        </view>
       <!-- <view class="rank-box">
            <view class="rank-title"><view class="title-line"></view>消费者开瓶率</view>
            <view class="rank-content">
                <view class="rank-name">头曲终端开瓶率</view>
                <view><text class="rank-num">31%</text>(%)</view>
                <view class="rank-detail">当前排名1/100</view>
            </view>
            <view class="rank-content" @tap='todetali' style="background-image: url('https://xtzh-public-1256376813.cos.ap-chengdu.myqcloud.com/static/images/emp-portrait/wujiangpai.png');">
                <view class="rank-name">头曲终端开瓶数</view>
                <view><text class="rank-num">31288</text>(瓶)</view>
                <view class="rank-detail">当前排名1/100</view>
            </view>
        </view> -->
    </view>
    <view v-else class="none">
        暂无数据
    </view>
</template>

<script>
    import SelectButton from "../../components/select-button";
    export default {
        name:'staff-portrait-evaluate-detail',
        components: {SelectButton},
        props:{
            userId:{
                type:String
            }
        },
        data() {
            const today = this.$filter.date(new Date(), 'YYYY-MM')
            return {
                today,
                areaOption: [],
                detailData: {},
                currentIndex: 0,
                // userId: '707545698794787374',
                showName: '暂无',
                rankList: [],
                areaData: []
            }
        },
        created(){
            this.init()
        },
        methods:{
            /**
             * @createdBy  谭少奇
             * @date  2022/03/25
             * @description 初始化
             */
            async init(){
                await this.getDetail()
            },
            /**
             * @createdBy  谭少奇
             * @date  2022/03/25
             * @description 获取排名列表数据
             */
            async getList(){
                const url = 'action/link/empArea/queryUserfeast';
                const params = {
                    date: this.today,
                    type: "day",
                    empAreaId: this.areaData[this.currentIndex].id
                }
                const {success, result} = await this.$http.post(url, params);
                if(!success) return
                this.rankList = result
            },
            /**
             * @createdBy  谭少奇
             * @date  2022/03/25
             * @description 获取明细数据
             */
            async getDetail(){
                const url = 'action/link/empArea/queryByExamplePage';
                const params = {
                    filtersRaw: [{"property": "userId", "operator": "=", "value": this.userId, "id": "userId"}]
                }
                const {success, rows} = await this.$http.post(url, params);
                if(!success) return
                if(rows.length){
                    this.detailData = rows[0] || []
                    this.areaData = rows
                    this.areaOption = rows.map(item=>{
                        return item.areaName
                    })
                    this.getList()
                }

            },
            /**
             * @createdBy  谭少奇
             * @date  2022/03/25
             * @description 切换区域数据
             */
            areaChange(e) {
                const index = Number(e.detail.value)
                this.currentIndex = index;
                this.detailData = this.areaData[index]
                this.getList()
            },
            /**
             * @createdBy  谭少奇
             * @date  2022/03/25
             * @description 跳转至详情
             * @param Object item 指标明细
             */
            async todetali(item){
                const lov =  await this.$lov.getNameByTypeAndVal('SUB_EVA_DIMENSION', item.subEvaDimension);
                const data = {
                    ...item,
                    title: lov,
                    areaName: this.areaOption[this.currentIndex]
                }
                this.$nav.push('/pages/echart/lzlj/staff-portrait/rank-detail-page.vue',data)
            }
        }
    }
</script>

<style lang="scss">
    .none{
        margin-top: 200px;
        text-align: center;
    }
    .staff-portrait-evaluate-detail{
        background: #fff;
        margin: 24px;
        padding: 20px;
        border-radius: 16px;
        .head-content{
            display: flex;
            justify-content: space-between;
            .item-bg{
                width: 307px;
                height: 156px;
                // border: 1px solid red;
                background: #F7F8FA;
                border-radius: 12px;
                background-image: url('https://xtzh-public-1256376813.cos.ap-chengdu.myqcloud.com/static/images/emp-portrait/shichangjieduan.png');
                background-repeat: no-repeat;
                background-size: cover;
                view{
                   margin: 24px;;
                }
            }
            .zongdefen{
                background-image: url('https://xtzh-public-1256376813.cos.ap-chengdu.myqcloud.com/static/images/emp-portrait/zongdefen.png');
            }
        }
        .flex-box{
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            .flex-title{
                font-weight: bold;
                .flex-bz{
                    font-size: 24px;
                    color: #9EA1AE;
                    font-weight: 400;
                    margin-left: 20px;
                }
            }

        }
        .rank-box{
            .rank-title{
                position: relative;
                .title-line{
                    position: absolute;
                    left: -24px;
                    width: 12px;
                    height: 32px;
                    top: 6px;
                    background-image: linear-gradient(180deg, #2F69F8 0%, #5CA2FC 100%);
                }
            }
            .rank-content{
                margin: 20px 0;
                background: #F7F8FA;
                border-radius: 12px;
                padding: 24px;
                line-height: 58px;
                background-image: url('https://xtzh-public-1256376813.cos.ap-chengdu.myqcloud.com/static/images/emp-portrait/jinpai.png');
                background-position: right top;
                background-repeat: no-repeat;
                background-size: 280px 204px;
                .rank-name{
                    font-size: 28px;
                    letter-spacing: 0;
                }
                .rank-num{
                    font-family: AlibabaPuHuiTiH;
                    font-size: 48px;
                    letter-spacing: 0;
                    font-weight: 400;
                }
                .rank-detail{
                    font-size: 24px;
                    color: #9EA1AE;
                    letter-spacing: 0;
                }
            }
        }

    }
</style>
