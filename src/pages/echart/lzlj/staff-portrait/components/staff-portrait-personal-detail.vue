<!--
 @file 员工画像详情
 <AUTHOR>
 @Created 2022/10/20
-->
<template>
    <link-page class="staff-portrait-personal-detail">
        <view class="profile-container">
            <view :style="menuTopStyle"></view>
            <view class="profile">
                <view class="basc-info">
                    <view v-if="basicInfo.userProfile">
                        <image class="avatar-img"
                               :src="basicInfo.userProfile">
                        </image>
                    </view>
                    <view class="avatar-img" style="overflow:hidden" v-else-if="isPersonalFlag">
                        <open-data type="userAvatarUrl"></open-data>
                    </view>
                    <view v-else>
                        <image class="avatar-img"
                               :src="basicInfo.userGender === 'MALE' ? $imageAssets.manImage : $imageAssets.womanImage"></image>
                    </view>
                    <view class="name">{{ basicInfo.userName }}
                        <link-icon v-if="basicInfo.userGender"
                                   :class="basicInfo.userGender === 'MALE' ? 'member-gender-male' : 'member-gender-female'"
                                   :icon="basicInfo.userGender === 'MALE' ? 'icon-man' : 'icon-woman'"></link-icon>
                    </view>
                </view>
                <view v-if="isPersonalFlag" class="business-indicator-personal">
                    <view>{{ userInfoSync.postnName }}</view>
                </view>
                <view v-else class="business-indicator-container">
                    <view class="business-indicator" style="margin-bottom: 8px">
                        <view class="business-indicator-left">所属组织：
                            <text style="color: #212223">{{ basicInfo.orgName }}</text>
                        </view>
                        <view class="business-indicator-right">职位类型：
                            <text style="color: #212223">{{ basicInfo.postnTypeName }}</text>
                        </view>
                    </view>
                    <view class="business-indicator">
                        <view class="business-indicator-left">联系电话：
                            <text style="color: #212223">{{ basicInfo.contactPhone }}</text>
                        </view>
                        <view class="business-indicator-right">职员类型：
                            <text style="color: #212223">{{ basicInfo.staffTypeName }}</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="tag-box">
            <view @tap='changeTag(true)' class="tag-item" :style="{'color':showTag?'#2f69f8':''}">我的指标</view>
            <view @tap='changeTag(false)' class="tag-item" :style="{'color':!showTag?'#2f69f8':''}" v-show="isShow">评价信息</view>
        </view>
        <view v-if="!showTag">
            <staff-portrait-evaluate-detail :userId='showUserId'></staff-portrait-evaluate-detail>
        </view>
        <view class="service-indicator-statistics" v-else>
            <view class="title">
                <view style="display: flex;align-items: center">
                    <view class="indicator-icon">
                        <view class="square2"></view>
                        <view class="square1"></view>
                    </view>
                    上月业务指标统计
                </view>
                <view
                    style="display: flex;align-items: center;padding: 0 5px;border-radius: 20px;font-weight: nomal">
                    <picker :value="currentMonthIndex" @change="monthChange"
                            :range="monthOption">
                        <select-button :label="monthOption[currentMonthIndex]"
                                       downIcon></select-button>
                    </picker>
                </view>
            </view>
            <view class="line" style="height: 1px;width: 100%;background-color: #EFF2F6"></view>
            <view class="consumer-service">
                <view class="title">终端业务</view>
                <view class="consumer-service-content">
                    <link-echart
                        :option="terminalServiceOption"
                        :height=" 280 + 'px'"
                        :force-use-old-canvas="false"
                    />
                </view>
            </view>
            <view class="line"></view>
            <view class="consumer-service">
                <view class="title">消费者业务</view>
                <view class="consumer-service-content">
                    <link-echart
                        :option="consumerServiceOption"
                        :height=" 280 + 'px'"
                        :force-use-old-canvas="false"
                    />
                </view>
            </view>
            <view class="line"></view>
            <view class="service-title">
                <view class="title">市场活动业务</view>
                <circular-echarts :seriesData="validActivityData" :is-progress="true" :wrapNum="4"
                                  label="活动申请情况"/>
            </view>
            <view class="line"></view>
            <view class="service-title">
                <view class="title">考勤日报情况</view>
                <circular-echarts style="margin-bottom: 32px" :seriesData="normalPostratioData" :wrapNum="6"
                                  label="考勤完成情况"/>
                <circular-echarts style="margin-bottom: 32px" :seriesData="DailyReportData" :wrapNum="6"
                                  label="日报完成情况"/>
            </view>

            <view class="line"></view>
            <view class="custom-tags">
                <view class="title">
                    <view>个人标签</view>
                    <view class="edit-btn" @tap="gotoEdit" v-if="!isPersonalFlag">
                        <link-icon icon="icon-edit"/>
                        编辑
                    </view>
                </view>
                <view class="custom-tags-content">
<!--                    <view class="tags-item-blue" v-if="!!normalPostratio"> {{-->
<!--                            '考勤完成率' + normalPostratio * 100 + '%'-->
<!--                        }}-->
<!--                    </view>-->
<!--                    <view class="tags-item-green" v-if="!!coverageRate"> {{ '终端拜访覆盖率' + Number(coverageRate).toFixed(2) * 100 + '%' }}-->
<!--                    </view>-->
                    <view class="tags-item-yellow" v-if="indicatorData.skills">{{ indicatorData.skills }}</view>
                    <view class="tags-item-yellow" v-if="indicatorData.highPerform">{{ indicatorData.highPerform }}
                    </view>
                </view>
            </view>
        </view>
    </link-page>
</template>

<script>

import CircularEcharts from "./circular-echarts";
import {targetSimpleDataHistogram} from "../staff-portrait.utils";
import SelectButton from "../../components/select-button";
import Taro from "@tarojs/taro";
import staffPortraitEvaluateDetail from "./staff-portrait-evaluate-detail";

export default {
    name: "staff-portrait-personal-detail",
    components: {SelectButton, CircularEcharts, staffPortraitEvaluateDetail},
    data() {
        const currentMonth = new Date().getMonth(),
            currentYear = new Date().getFullYear();
        const monthOption = new Array(currentMonth).fill(1).map((v, i) => {
                return i + 1 + '月';
            }
        )
        const userInfoSync = Taro.getStorageSync('token').result;
        return {
            isShow: false,
            showUserId: '',
            menuTopStyle: '',//顶部背景样式
           // terminalServiceData: '',//终端拜访覆盖率
            //followUpData: '',//上月新跟进终端数量
            terminalServiceOption: null,  //终端业务
            consumerServiceOption: null,//消费者业务
            marketActivityData: '',//
            normalPostratioData: '',//正常打卡比例
            validActivityData: '',//有效活动场次
            DailyReportData: '',//日报达成比例
            basicInfo: [],//用户基本信息
            tagItems: [],//标签
            monthOption,
            currentMonthIndex: currentMonth - 1,
            monthVal: String(currentMonth),//上月月份
            currentMonth,//上月月份
            currentYear,//当前年份
            type: true,
            twoItems: {},
            indicatorData: [],//请求数据
            userInfoSync,
            normalPostratio:null,
            coverageRate:null,
            skills:null,
            highPerform:null,
            showTag: true, //展示页签
        }
    },
    props: {
        userInfo: {
            type: Object
        },//用户信息
        isPersonalFlag: {
            default: true
        }
    },
    async created() {

    },
    async mounted() {
        this.menuTopStyle = `background-image: url('${this.$imageAssets.homeMenuBgImage}');height:126px;`;
        await this.queryUserInfo();
        this.queryAllData();
    },
    methods: {
        changeTag(status){
            this.showTag = status
        },
        gotoEdit() {
            this.$nav.push('/pages/echart/lzlj/staff-portrait/staff-portrait-custom-tag-page', {data: this.twoItems})
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/11/1
         * @methods queryUserInfo
         * @para
         * @description 查询用户基本信息
         */
        async queryUserInfo() {
            if (!this.isPersonalFlag) {
                let postnTypeName = await this.$lov.getNameByTypeAndVal('POSTN_TYPE', this.userInfo.postnType);
                let staffTypeName = await this.$lov.getNameByTypeAndVal('STAFF_TYPE', this.userInfo.staffType);
                this.basicInfo = {
                    staffTypeName,
                    postnTypeName,
                    orgName: this.userInfo.orgName,
                    userNum: this.userInfo.userNum,
                    userName: this.userInfo.userName,
                    contactPhone: this.userInfo.contactPhone,
                    userProfile: this.userInfo.userProfile,
                    userGender: this.userInfo.userGender
                }
                return;
            }
            const data = await this.$http.post('action/link/empPortrait/queryEmpPortraitBasicInfoPage');
            //基本信息
            let postnTypeName = await this.$lov.getNameByTypeAndVal('POSTN_TYPE', data.result.postnType);
            let staffTypeName = await this.$lov.getNameByTypeAndVal('STAFF_TYPE', data.result.staffType);
            this.basicInfo = {
                staffTypeName,
                postnTypeName,
                orgName: data.result.orgName,
                userNum: data.result.userNum,
                userName: data.result.userName,
                contactPhone: data.result.contactPhone,
                userProfile: data.result.userProfile,
                userGender: data.result.userGender
            }
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/11/1
         * @methods queryAllData
         * @para
         * @description 查询所有数据
         */
        async queryAllData() {
            try {
                this.$utils.showLoading();
                this.tagItems = [];
                const startDate = this.$date.format(new Date(this.currentYear, this.currentMonth - 1, 1));
                const endDate = this.$date.format(new Date(this.currentYear, this.currentMonth, 0));
                const url = this.isPersonalFlag ? 'action/link/empPortrait/queryPersonalEmpPortraitDetail' : 'action/link/empPortrait/queryEmpPortraitDetailPage';
                const params = {
                    startDate,
                    endDate
                }
                if (!this.isPersonalFlag) params.userNum = this.basicInfo.userNum;
                const data = await this.$http.post(url, params);
                this.indicatorData = [];
                if (data.success) {
                    if (data.result){
                        this.indicatorData = data.result;
                        const code =!this.isPersonalFlag?data.result.branchCompanyCode : this.userInfoSync.coreOrganizationTile.brandCompanyCode
                        this.isShow = ['5161','5903','5902'].includes(code)
                        this.showUserId = data.result.userId
                    }else{
                        const code = this.userInfoSync.coreOrganizationTile.brandCompanyCode
                        this.isShow = ['5161','5903','5902'].includes(code)
                        this.showUserId = this.userInfoSync.id
                    }
                    //终端拜访覆盖率
                     /**     const terminalServiceValue=Number(this.indicatorData.termVisitNum) || 0;
                    if(terminalServiceValue===0){
                        this.terminalServiceData = [{
                            name: '已拜访数量',
                            value: terminalServiceValue
                        }, {
                            name: '',
                            value: 1
                        }];
                    }else {
                        this.terminalServiceData = [{
                            name: '已拜访数量',
                            value: terminalServiceValue
                        }];
                    }

                    // 上月新跟进终端数量
                    const newTermFollowValue= Number(this.indicatorData.newTermFollow) || 0;
                    if(newTermFollowValue===0){
                        this.followUpData = [{
                            name: '上月新跟进终端数量',
                            value:newTermFollowValue
                        }, {
                            name: '',
                            value: 1
                        }];
                    }else {
                        this.followUpData = [{
                            name: '上月新跟进终端数量',
                            value:newTermFollowValue
                        }];
                    }**/
                     const  terminalServiceData = [
                         {name: '已拜访终端', value: Number(this.indicatorData.termVisitNum) || 0},
                         {name: '新跟进终端', value: Number(this.indicatorData.newTermFollow) || 0},
                         {name: '跟进终端进货数量(件)', value: Number(this.indicatorData.scanNum) || 0}
                     ];
                     this.terminalServiceOption = echartInitConfig => targetSimpleDataHistogram(echartInitConfig, terminalServiceData);

                    //消费者业务
                    const consumerBusinessData = [{
                        name: '上月新增跟进消费者数量',
                        value: Number(this.indicatorData.newConNum) || 0
                    }, {
                        name: '上月拜访数量',
                        value: Number(this.indicatorData.conVisitNum) || 0
                    }, {name: '上月礼赠次数', value: Number(this.indicatorData.conGiftNum) || 0}, {
                        name: '上月动销金额',
                        value: Number(this.indicatorData.conSales) || 0
                    }];
                    this.consumerServiceOption = echartInitConfig => targetSimpleDataHistogram(echartInitConfig, consumerBusinessData);
                    //有效活动场次
                    const validActivityDataValue=Number(this.indicatorData.actApplyNum) || 0;
                    if(validActivityDataValue===0){
                        this.validActivityData = [{
                            name: '上月活动申请场次',
                            value: validActivityDataValue
                        }, {
                            name: '',
                            value: 1
                        }];
                    }else{
                        this.validActivityData = [{
                            name: '上月活动申请场次',
                            value: validActivityDataValue
                        }];
                    }
                    //正常打卡比例
                    this.normalPostratioData = [{
                        name: '正常打卡',
                        value: (Number(this.indicatorData.normalPostratio) * 100) || 0
                    }, {name: '异常打卡', value: 100 - ((Number(this.indicatorData.normalPostratio) * 100) || 0)}];
                    //日报达成比例
                    this.DailyReportData = [{
                        name: '已提交',
                        value: (Number(this.indicatorData.dailyRatio) * 100) || 0
                    }, {name: '未提交', value: 100 - ((Number(this.indicatorData.dailyRatio) * 100) || 0)}];
                    //标签
                    this.normalPostratio = Number(this.indicatorData.normalPostratio) || 0
                    this.coverageRate = (Number(this.indicatorData.termFollowNum) || 0) === 0 ? 0 : (((Number(this.indicatorData.termVisitNum) || 0) / (Number(this.indicatorData.termFollowNum) || 0)));
                    this.twoItems = {
                        skills: this.indicatorData.skills,
                        highPerform: this.indicatorData.highPerform,
                        tagId: this.indicatorData.tagId,
                        userId: this.indicatorData.userId,
                        row_status: this.indicatorData.row_status
                    }
                    this.$utils.hideLoading();
                } else {
                    this.$utils.hideLoading();
                    this.$showError('查询员工画像详情接口失败');
                }
            } catch (e) {
                this.$utils.hideLoading();
                this.$showError('查询员工画像详情接口失败！');
            }
        },
        /**
         * @createdBy  王雅琪
         * @date  2022/11/1
         * @methods monthChange
         * @para
         * @description 切换月份查询数据
         */
        monthChange(e) {
            this.currentMonthIndex = Number(e.detail.value);
            this.currentMonth = Number(e.detail.value) + 1;
            this.queryAllData();
        }
    }
}
</script>

<style lang="scss">
.staff-portrait-personal-detail {
    background-color: #f2f2f2;
    .tag-box{
        margin-top: 20px;
        .tag-item{
            display: inline-block;
            background: white;
            font-size: 26px;
            padding: 10px 20px;
            border-radius: 10px;
            margin-left: 20px;
        }
    }
    .profile-container {

        .profile {
            background-color: #ffffff;
            //height: 292px;
            border-radius: 16px;
            margin: -112px 24px 0 24px;

            .basc-info {
                display: flex;
                width: 100%;
                align-items: center;
                flex-direction: column;

                .avatar-img {
                    width: 150px;
                    height: 150px;
                    border-radius: 50%;
                    margin-top: -70px;
                }

                .name {
                    margin-top: 16px;
                    color: #212223;
                    font-size: 36px;
                    line-height: 52px;
                    font-weight: 600;
                }

            }

            .business-indicator-personal {
                padding: 20px 0 30px 0;
                display: flex;
                justify-content: center;
                font-size: 30px;
            }

            .business-indicator-container {
                padding: 24px;

                .business-indicator {
                    display: flex;

                    .business-indicator-left, .business-indicator-right {
                        flex: 7;
                        text-align: left;
                        color: #6A6D75;
                        font-weight: 400;
                        font-size: 24px;
                        line-height: 40px;
                    }

                    .business-indicator-right {
                        flex: 6;
                    }
                }
            }

            .member-gender-male {
                display: inline-block;
                width: 28px;
                height: 28px;
                font-size: 28px;
                //margin-top: 34px;
                line-height: 34px;
                background: #91d5f6;
                color: #FFFFFF;
                border-radius: 14px;
            }

            .member-gender-female {
                display: inline-block;
                width: 28px;
                height: 28px;
                font-size: 28px;
                //margin-top: 34px;
                line-height: 34px;
                color: #FFFFFF;
                background: #FFA6D3;
                border-radius: 14px;
            }

        }
    }

    .line {
        height: 1px;
        width: 100%;
        background-color: #EFF2F6;
        margin-top: 12px;
    }

    .service-indicator-statistics {
        padding: 24px;
        margin: 24px;
        background-color: #FFFFFF;
        border-radius: 16px;

        .title {
            font-weight: 600;
            font-size: 32px;
            color: #212223;
            line-height: 48px;
            margin: 24px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .indicator-icon {
                position: relative;
                width: 32px;
                height: 32px;
                margin-right: 16px;

                .square1 {
                    width: 20px;
                    height: 20px;
                    background-color: #46D0FF;
                    position: absolute;
                    right: 0;
                    top: 0;
                }

                .square2 {
                    position: absolute;
                    width: 24px;
                    height: 24px;
                    background-color: #3069F8;
                    left: 0;
                    bottom: 0;
                }
            }
        }

        .service-title, .consumer-service {
            font-weight: 600;
            font-size: 28px;
            color: #212223;
            line-height: 44px;
            margin: 24px 0;

            .title {
                margin-bottom: 24px;
                font-size: 28px;
            }
        }

        .custom-tags {
            .title {
                display: flex;
                justify-content: space-between;
                font-weight: 600;
                font-size: 28px;
                color: #212223;
                line-height: 22px;
                height: 44px;

                .edit-btn {
                    font-weight: 400;
                    color: #3069F8;
                }
            }

            .custom-tags-content {
                display: flex;
                flex-wrap: wrap;
                padding: 24px 0 32px 0;

                .tag-item {
                    height: 40px;
                    font-weight: 400;
                    font-size: 24px;
                    color: #209CFF;
                    text-align: center;
                    line-height: 40px;
                    padding: 0px 16px;
                    background-color: #E6F4FF;
                    margin-right: 16px;
                    font-family: PingFangSC-Regular;
                    border-radius: 4px;
                }

                .tags-item-blue {
                    height: 40px;
                    font-weight: 400;
                    font-size: 24px;
                    color: #209CFF;
                    text-align: center;
                    line-height: 40px;
                    padding: 0px 16px;
                    background-color: #E6F4FF;
                    margin: 10px;
                    font-family: PingFangSC-Regular;
                    border-radius: 4px;
                }
                .tags-item-green {
                    height: 40px;
                    font-weight: 400;
                    font-size: 24px;
                    color: #52AB71;
                    text-align: center;
                    line-height: 40px;
                    padding: 0px 16px;
                    background-color: #EDF7F1;
                    margin: 10px;
                    font-family: PingFangSC-Regular;
                    border-radius: 4px;
                }
                .tags-item-yellow {
                    height: 40px;
                    font-weight: 400;
                    font-size: 24px;
                    color: #DD9F0E;
                    text-align: center;
                    line-height: 40px;
                    padding: 0px 16px;
                    background-color: #FFF7E6;
                    margin: 10px;
                    font-family: PingFangSC-Regular;
                    border-radius: 4px;
                }
            }
        }
    }

    .link-input .link-input-inner {
        min-width: 0;
    }
}
</style>
