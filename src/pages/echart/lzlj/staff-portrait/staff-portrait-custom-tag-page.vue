<!--
 @file 自定义标签
 <AUTHOR>
 @Created 2022/10/25
-->
<template>
    <link-page class="staff-portrait-custom-tag">
        <view class="top"></view>
        <view class="tag-item">
            <view class="tag-title">标签</view>
            <link-form :hide-edit-button="false">
                <link-form-item label="技能特长" class="skills" required>
                    <link-input placeholder="请输入" v-model="skills"/>
                </link-form-item>
                <link-form-item label="特殊业绩" class="achieve" required>
                    <link-input placeholder="请输入" v-model="highPerform"/>
                </link-form-item>
            </link-form>
        </view>
        <link-sticky>
            <link-button @tap="save">保存</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
import {ROW_STATUS} from "../../../../utils/constant";

export default {
    name: "staff-portrait-custom-tag",
    data() {
        return {
            skills: this.pageParam.data.skills,
            highPerform: this.pageParam.data.highPerform,
            tagId: this.pageParam.data.tagId,
            userId: this.pageParam.data.userId,
            row_status: this.pageParam.data.row_status
        }
    },
    created() {
    },
    methods: {
        /**
         * @createdBy  何春霞
         * @date  2022/11/10
         * @methods save
         * @para
         * @description 保存
         */
        async save() {
            try {
                if (this.tagId) {
                    const {result} = await this.$http.post('action/link/empTag/queryById', {
                        id: this.tagId
                    })
                    const data = await this.$http.post('action/link/empTag/upsert', {
                        ...result,
                        id: this.tagId,
                        skills: this.skills,
                        highPerform: this.highPerform,
                        row_status: ROW_STATUS.UPDATE
                    });
                    if (data.success) {
                        this.$message.success('保存成功');
                        this.$utils.hideLoading();
                        this.$nav.back();
                    } else {
                        this.$utils.hideLoading();
                        this.$showError('更新标签失败');
                    }
                } else {
                    const data = await this.$http.post('action/link/empTag/upsert', {
                        userId: this.userId,
                        skills: this.skills,
                        highPerform: this.highPerform,
                        row_status: ROW_STATUS.NEW
                    });
                    if (data.success) {
                        this.$message.success('保存成功');
                        this.$utils.hideLoading();
                        this.$nav.back();
                    } else {
                        this.$utils.hideLoading();
                        this.$showError('新建标签失败');
                    }
                }
            } catch (e) {
                this.$utils.hideLoading();
                this.$showError('添加失败！');
            }

        }
    }
}
</script>

<style lang="scss">
.staff-portrait-custom-tag {
    background-color: #f2f2f2;

    .top {
        top: 0px;
        width: 100%;
        height: 32px;
        background-color: #f2f2f2;
    }

    .tag-item {
        margin: 0px 24px;
        background-color: #FFFFFF;
        border-radius: 16px;

        .tag-title {
            font-family: PingFangSC-S0pxibold;
            padding: 12px 16px 0px;
            font-weight: 600;
            font-size: 32px;
            color: #212223;
            line-height: 48px;
        }

        .skills,
        .achieve {
            margin: 16px !important;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 28px !important;
            color: #6A6D75;
            line-height: 44px;

            .link-item {
                padding: 0px 12px !important;

                .link-item-title {
                    margin-left: 32px !important;
                }
            }


        }
    }

    .link-sticky .link-sticky-content {
        //margin: 12px !important;
        margin: 18px 32px !important;
        width: 92vw;

        .link-button.link-button-shape-fillet {
            border-radius: 60px;
        }
    }
}
</style>
