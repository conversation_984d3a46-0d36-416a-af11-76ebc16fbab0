<!--
 @file 员工画像详情
 <AUTHOR>
 @Created 2022/10/20
-->
<template>
    <link-page class="staff-portrait-detail">
        <staff-portrait-personal-detail ref="details" :isPersonalFlag="false" :user-info="pageParam.data">
        </staff-portrait-personal-detail>
    </link-page>
</template>

<script>

import StaffPortraitPersonalDetail from "./components/staff-portrait-personal-detail";

export default {
    name: "staff-portrait-detail",
    components: {StaffPortraitPersonalDetail},
    methods: {
        async onBack() {
            this.$utils.showLoading('数据加载中...');
            await this.$refs.details.queryAllData();
            this.$utils.hideLoading();
        },
    }
}
</script>

<style lang="scss">
.staff-portrait-detail {
}
</style>
