
<template>
    <link-page class="rank-detail-page">
        <view>
            地区：{{pageParam.areaName}}
        </view>
        <view class="head-content">
            <view class="item-bg">
                <view class="item-title">达成值</view>
                <view class="item-num">{{pageParam.totalEndData}}</view>
            </view>
            <view class="item-bg own-rank">
                <view class="item-title">我的排名</view>
                <view>
                    <text class="item-num">{{pageParam.ranking}}</text>
                    <text>/</text>
                    <text class="item-total">{{pageParam.totalUser}}</text>
                    <text class="item-red">前5%</text>
                </view>
            </view>
        </view>
        <view class="rank-table">
            <view class="table-title">
                <view>序号</view>
                <view>姓名</view>
                <view>工号</view>
                <view>负责区县</view>
                <view>达成值</view>
            </view>
            <view :class="index % 2 === 0?'table-content':'table-content bg-color'" v-for="(item,index) in  tableList" :key="index+'table'">
                <view>{{index+1}}</view>
                <view>{{item.fstName}}</view>
                <view>{{item.empCode}}</view>
                <view>{{item.areaName}}</view>
                <view>{{item.totalScore}}</view>
            </view>
        </view>
    </link-page>
</template>

<script>
    export default {
        name:'rank-detail-page',
        data(){
            return {
                tableList:[]
            }
        },
        created(){
            this.$taro.setNavigationBarTitle({title: this.pageParam.title?this.pageParam.title:'指标详情'});
            this.getDetail()
        },
        methods:{
            async getDetail(){
                const url = 'action/link/empEvaluation/queryByExamplePage';
                const params = {
                    filtersRaw: [
                        {"property": "areaName", "operator": "=", "value": this.pageParam.areaName, "id":"areaName"},
                        {"property": "subEvaDimension", "operator": "=", "value": this.pageParam.subEvaDimension, "id": "subEvaDimension"},
                        {"property": "userId", "operator": "=", "value": this.pageParam.userId, "id": "userId"},
                    ]
                }
                const {success, rows} = await this.$http.post(url, params);
                if(!success) return
                this.tableList = rows
            }
        }
    }
</script>

<style lang="scss">
    .rank-detail-page{
        padding: 24px;
        .head-content{
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            .item-bg{
                width: 342px;
                height: 158px;
                // border: 1px solid red;
                background: #F7F8FA;
                border-radius: 12px;
                background-image: url('https://xtzh-public-1256376813.cos.ap-chengdu.myqcloud.com/static/images/emp-portrait/dachengzhi.png');
                background-repeat: no-repeat;
                background-size: cover;
                font-size: 28px;
                view{
                   margin: 24px;
                }
                .item-title{
                 font-size: 28px;
                 color: #6A6D75;
                }
                .item-num{
                    font-size: 40px;
                    font-family: AlibabaPuHuiTiB;
                    font-weight: 700;
                }
                .item-total{
                    font-weight: 500;
                }
                .item-red{
                    font-size: 24px;
                    color: #E91101;
                    margin-left: 20px;
                }
            }
            .own-rank{
                background-image: url('https://xtzh-public-1256376813.cos.ap-chengdu.myqcloud.com/static/images/emp-portrait/wodepaiming.png');
            }
        }
        .rank-table{
            margin-top: 40px;
            font-size: 24px;
            line-height: 80px;
            .table-title{
                text-align: center;
                display: flex;
                font-weight: bold;
                background: #EFF4FF;
                border-radius: 16px 16px 0px 0px;
                view{
                    padding: 0 20px;
                    width: 120px;
                    overflow: hidden;
                }
            }
            .table-content{
                display: flex;
                background: #fff;
                text-align: center;
                view{
                    padding: 0 20px;
                    width: 120px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
            .bg-color{
                background: rgba(248,250,255,0.90);
            }
        }
    }
</style>
