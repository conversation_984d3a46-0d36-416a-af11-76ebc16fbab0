<template>
  <link-page class="dealer-data-board-page">
    <lnk-taps :taps="dataBoardOption" v-model="dataBoardActive" ></lnk-taps>
    <!--目标配额达成-->
    <view v-if="dataBoardActive.val === 'terminal'">
      <terminal-board ref="quota"  :userInfo="userInfo" ></terminal-board>
    </view>
    <!--区域库存-->
    <view v-if="dataBoardActive.val === 'visit'">
      <visit-board ref="stock" :userInfo="userInfo" ></visit-board>
    </view>
  </link-page>
</template>

<script>
  import LnkTaps from "../../../core/lnk-taps/lnk-taps";
  import TerminalBoard from "./components/terminal-board";
  import visitBoard from "./components/visit-board";
  import Taro from "@tarojs/taro";
  export default {
    name: "data-board-page",
    components: {TerminalBoard, visitBoard, LnkTaps},
    data () {
      return {
        dataBoardActive: {},
        dataBoardOption: [
          {name: '终端看板', seq: '1', val: 'terminal'},
          {name: '拜访看板', seq: '2', val: 'visit'},
        ],
        userInfo: {},
        branchCompanyList: [], //品牌公司list,
        salesmanBrandComId: null, //默认品牌公司id
        salesmanBrandComName: null, //默认品牌公司名称
      }
    },
    async created () {
      this.dataBoardActive = this.dataBoardOption[0]
      this.userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
    },
    methods: {
      /**
       *  @description: 切换tabs
       *  @author: 马晓娟
       *  @date: 2020/9/3 18:06
       */
      changeTab(val, key) {
        this.dataBoardActive = val;
        console.log(val, key)
      },
    }
  }
</script>

<style lang="scss">
  .dealer-data-board-page{
    margin-top: 94px;
    .lnk-tabs{
      z-index: 1503 !important;
      border-bottom: 2px solid #f2f2f2!important;
    }
  }
</style>
