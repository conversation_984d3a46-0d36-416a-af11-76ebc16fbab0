<template>
    <link-page class="visit-board">
        <view class="visit-overview">
            <line-title title="拜访总览"></line-title>
            <scroll-view scroll-x="true" class="scroll-view-data" v-if="orgOuathFlag">
                <view class="select-dimension">
                    <select-button :label="visitOverviewParam.orgName?visitOverviewParam.orgName: '全部片区'"  :selected-flag="visitOverviewParam.orgName !== null " @tap="tapFilterOrganization('overView')" downIcon></select-button>
                </view>
            </scroll-view>
            <view class="visit-overview-content">
                    <view class="title">
                        拜访次数
                    </view>
                    <view class="visit-overview-content-row">
                        <view class="visit-overview-content-column">
                            <view class="center">
                                <view class="number">
                                    <view class="before">{{visitOverviewFormData.todayVisitSum || 0}}</view>
                                </view>
                                <view class="text">
                                    今日
                                </view>
                            </view>
                            <view class="line"></view>
                        </view>
                        <view class="visit-overview-content-column">
                            <view class="center">
                                <view class="number">
                                    <view class="before">{{visitOverviewFormData.weekVisitSum || 0}}</view>
                                </view>
                                <view class="text">
                                    本周
                                </view>
                            </view>
                            <view class="line"></view>
                        </view>
                        <view class="visit-overview-content-column">
                            <view class="center">
                                <view class="number">
                                    <view class="before">{{visitOverviewFormData.monthVisitSum || 0}}</view>
                                </view>
                                <view class="text">
                                    本月
                                </view>
                            </view>
                        </view>
                    </view>
                <view class="title">
                    拜访人数
                </view>
                <view class="visit-overview-content-row">
                    <view class="visit-overview-content-column">
                        <view class="center">
                            <view class="number">
                                <view class="before">{{visitOverviewFormData.todayVisitorSum || 0}}</view>
                            </view>
                            <view class="text">
                                今日
                            </view>
                        </view>
                        <view class="line"></view>
                    </view>
                    <view class="visit-overview-content-column">
                        <view class="center">
                            <view class="number">
                                <view class="before">{{visitOverviewFormData.weekVisitorSum || 0}}</view>
                            </view>
                            <view class="text">
                                本周
                            </view>
                        </view>
                        <view class="line"></view>
                    </view>
                    <view class="visit-overview-content-column">
                        <view class="center">
                            <view class="number">
                                <view class="before">{{visitOverviewFormData.monthVisitorSum || 0}}</view>
                            </view>
                            <view class="text">
                                本月
                            </view>
                        </view>
                    </view>
                </view>
                <view class="title" v-if="visitOverviewParam.orgType !== 'Company'">
                    拜访终端
                </view>
                <view class="visit-overview-content-row" v-if="visitOverviewParam.orgType !== 'Company'">
                    <view class="visit-overview-content-column">
                        <view class="center">
                            <view class="number">
                                <view class="before">{{visitOverviewFormData.todayTerminalSum || 0}}</view>
                            </view>
                            <view class="text">
                                今日
                            </view>
                        </view>
                        <view class="line"></view>
                    </view>
                    <view class="visit-overview-content-column">
                        <view class="center">
                            <view class="number">
                                <view class="before">{{visitOverviewFormData.weekTerminalSum || 0}}</view>
                            </view>
                            <view class="text">
                                本周
                            </view>
                        </view>
                        <view class="line"></view>
                    </view>
                    <view class="visit-overview-content-column">
                        <view class="center">
                            <view class="number">
                                <view class="before">{{visitOverviewFormData.monthTerminalSum || 0}}</view>
                                <view class="after">/{{visitOverviewFormData.untilNowTerminalSum || 0}}</view>
                                <view class="iconfont icon-info-circle"></view>
                            </view>
                            <view class="text">
                                本月
                            </view>
                        </view>
                    </view>
                </view>
                <view class="title" v-if="visitOverviewParam.orgType !== 'Company'">
                    拜访核心终端
                </view>
                <view class="visit-overview-content-row" v-if="visitOverviewParam.orgType !== 'Company'">
                    <view class="visit-overview-content-column">
                        <view class="center">
                            <view class="number">
                                <view class="before">{{visitOverviewFormData.todayCoreTerminalSum || 0}}</view>
                            </view>
                            <view class="text">
                                今日
                            </view>
                        </view>
                        <view class="line"></view>
                    </view>
                    <view class="visit-overview-content-column">
                        <view class="center">
                            <view class="number">
                                <view class="before">{{visitOverviewFormData.weekCoreTerminalSum || 0}}</view>
                            </view>
                            <view class="text">
                                本周
                            </view>
                        </view>
                        <view class="line"></view>
                    </view>
                    <view class="visit-overview-content-column">
                        <view class="center">
                            <view class="number">
                                <view class="before">{{visitOverviewFormData.monthCoreTerminalSum || 0}}</view>
                                <view class="after">/{{visitOverviewFormData.untilNowCoreTerminalSum || 0}}</view>
                                <view class="iconfont icon-info-circle"></view>
                            </view>
                            <view class="text">
                                本月
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="visit-coverage-terminal" >
            <line-title title="拜访覆盖终端统计"></line-title>
            <scroll-view scroll-x="true" class="scroll-view-data">
                <view class="select-dimension">
                    <select-button v-if="orgOuathFlag"  :label="visitCoverageParam.orgName?visitCoverageParam.orgName: '全部片区'" :selected-flag="visitCoverageParam.orgName !== null " @tap="tapFilterOrganization('coverage')" downIcon></select-button>
                    <select-button label="本周" :selected-flag="visitCoverageParam.dateType === 'week'" @tap="qoalcoverageQuery($event,'week')" value="quarter"></select-button>
                    <select-button label="本月" :selected-flag="visitCoverageParam.dateType === 'month'" @tap="qoalcoverageQuery($event,'month')" value="month"></select-button>
                </view>
            </scroll-view>
            <view class="data-board">
                <view class="title">
                    <view class="title-item column-1">
                        终端规划等级
                        <view class="iconfont"></view>
                    </view>
                    <view class="title-item column-2">
                        终端数量
                        <view class="iconfont icon-info-circle"></view>
                    </view>
                    <view class="title-item column-3">
                        拜访数量
                        <view class="iconfont"></view>
                    </view>
                    <view class="title-item column-3">
                        平均拜访次数
                        <view class="iconfont icon-info-circle"></view>
                    </view>
                </view>
                <view class="content">
                    <view class="list" v-for="(item,index) in visitCoverageData" :key="index">
                        <view class="list-item column-1">
                            {{item.acctLevel | lov('ACCT_LEVEL')}}
                        </view>
                        <view class="list-item column-2">
                            {{item.terminalAmt}}
                        </view>
                        <view class="list-item column-3">
                            {{item.visitTerminalAmt}}
                        </view>
                        <view class="list-item column-3">
                            {{item.averageVisitAmt}}
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="daily-report-submission">
            <line-title title="日报提交情况"></line-title>
            <scroll-view scroll-x="true" class="scroll-view-data" v-if="orgOuathFlag">
                <view class="select-dimension">
                    <select-button :label="dailyReportSubmissionParam.orgName?dailyReportSubmissionParam.orgName: '全部片区'" :selected-flag="dailyReportSubmissionParam.orgName !== null "  @tap="tapFilterOrganization('dailyReport')" downIcon></select-button>
                </view>
            </scroll-view>
            <view class="daily-report-submission-content">
                <view class="visit-overview-content-row">
                    <view class="visit-overview-content-column">
                        <view class="center">
                            <view class="number">
                                <view class="before">{{dailyReportFormData.dayCount || 0}}</view>
                                <view class="after" v-if="orgOuathFlag || (userInfo.positionType !== 'Salesman' && userInfo.positionType !== 'SalesTeamLeade')">/{{dailyReportFormData.shouldSubmitToday || 0}}</view>
                            </view>
                            <view class="text">
                                今日已提交
                            </view>
                        </view>
                        <view class="line"></view>
                    </view>
                    <view class="visit-overview-content-column">
                        <view class="center">
                            <view class="number">
                                <view class="before">{{dailyReportFormData.weekCount || 0}}</view>
                            </view>
                            <view class="text">
                                本周已提交
                            </view>
                        </view>
                        <view class="line"></view>
                    </view>
                    <view class="visit-overview-content-column">
                        <view class="center">
                            <view class="number">
                                <view class="before">{{dailyReportFormData.monthCount || 0}}</view>
                            </view>
                            <view class="text">
                                本月已提交
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <link-dialog ref="positionBottom" position="bottom" height="90vh" class="dialog-bottom" noPadding v-model="dialogFlag">
            <view class="model-title">
                <view class="iconfont icon-left" v-if="!(autoList.list.length>0 && autoList.list[0].id === this.userInfo.orgId)" @tap="goBackOrg" style="width: 40px;color: #BFBFBF;font-size: 20px;line-height: 48px;height: 48px;"></view>
                <view class="title" style="padding-left:0;">组织片区</view>
                <view class="iconfont icon-close" @tap="dialogFlag = false" style="margin-right: 15px;"></view>
            </view>
            <view class="dialog-content" style="height: calc(100% - 44px)">
                <scroll-view scroll-y="true" :style="{'height': 'calc(100% - 75px)'}">
                <link-auto-list :option="autoList" hideCreateButton>
                    <template slot-scope="{data,index}">
                        <view slot="note">
                            <item  :key="index" :data=data @tap="gotoItemOrg(data)" style="padding-top: 0;padding-bottom:0">
                                <link-radio-group v-model="tempOrgId">
                                    <item :arrow="false">
                                        <link-checkbox :val=data.id slot="thumb"   @tap="tempOrgInfo(data)"/>
                                    </item>
                                </link-radio-group>
                                <view class="list-item">
                                    {{data.text}}
                                </view>
                            </item>
                        </view>
                    </template>
                </link-auto-list>
                </scroll-view>
                <view class="link-dialog-foot-custom">
                    <link-button shadow @tap="clickOrginzation" label="确定" style="width:100vw"/>
                </view>
            </view>
        </link-dialog>
        <link-dialog ref="positionBottom1" position="bottom" height="90vh" class="dialog-bottom" noPadding v-model="companyDialogFlag">
            <view class="model-title">
                <view class="title" style="padding-left:0;">品牌公司</view>
                <view class="iconfont icon-close" @tap="companyDialogFlag = false" style="margin-right: 15px;"></view>
            </view>
            <view class="dialog-content" style="height: calc(100% - 44px)">
                <scroll-view scroll-y="true" :style="{'height': 'calc(100% - 75px)'}">
                    <list>
                        <item v-for="(item,index) in branchCompanyList" @tap="tempOrgInfo(item,'company',)" arrow="false" :key="index" style="padding-top: 0;padding-bottom:0">
                            <link-radio-group v-model="selectedBranchCompanyId">
                                <item>
                                    <link-checkbox :val=item.id slot="thumb"  toggleOnClickItem @tap="tempOrgInfo(item,'company')"/>
                                </item>
                            </link-radio-group>
                            <view class="list-item">
                                {{item.text}}
                            </view>
                        </item>
                    </list>
                </scroll-view>
                <view class="link-dialog-foot-custom">
                    <link-button shadow @tap="clickOrginzation('company')" label="确定" style="width:100vw"/>
                </view>
            </view>
        </link-dialog>
    </link-page>
</template>

<script>
    import SelectButton from "../../components/select-button";
    import LineTitle from "../../../../lzlj/components/line-title";
    export default {
        name: "visit-board",
        components: {LineTitle,SelectButton},
        data(){
            const accessGroupOauth = this.$utils.getMenuAccessGroup('','/pages/echart/lzlj/terminal-data-board/data-board-page');
            let orgOuathFlag =  false;
            if(!this.$utils.isEmpty(accessGroupOauth)){
                orgOuathFlag = true;
            }else{
                orgOuathFlag = this.$utils.isPostnOauth() === 'MY_ORG'? true: false;
            }
            return{
                cityManagerFlag: this.$taro.getStorageSync('token').result.positionType === 'CityManager',
                createdFlag: true,                      //是否初次加载
                orgOuathFlag,//组织选择框flag
                accessGroupOauth,//访问组安全性
                visitOverviewFormData: {},              //拜访预览表单
                visitCoverageData: [],                  //拜访覆盖数据
                visitOverviewParam: {                   //拜访终端参数
                    orgId: this.userInfo.orgId,
                    parentOrgId: this.userInfo.orgId,
                    orgName: this.userInfo.orgName,
                    orgType: this.userInfo.orgType,
                    brandComId: this.userInfo.coreOrganizationTile.l3Id,
                    postnId: this.userInfo.postnId
                },
                tempVisitOverviewParam: {               //拜访终端参数
                    orgId: this.userInfo.orgId,
                    parentOrgId: this.userInfo.orgId,
                    orgName: this.userInfo.orgName,
                    orgType: this.userInfo.orgType,
                    postnId: this.userInfo.postnId
                },
                visitCoverageParam: {                   //拜访覆盖终端统计参数
                    orgId: this.userInfo.orgId,
                    parentOrgId: this.userInfo.orgId,
                    orgName: this.userInfo.orgName,
                    dateType: 'week',
                    brandComId: null,
                    brandComName: null,
                    orgType: this.userInfo.orgType,
                    brandComCode: null,
                    postnId: this.userInfo.postnId
                },
                tempVisitCoverageParam :{               //拜访覆盖终端统计参数
                    orgId: this.userInfo.orgId,
                    parentOrgId: this.userInfo.orgId,
                    orgName: this.userInfo.orgName,
                    orgType: this.userInfo.orgType,
                    postnId: this.userInfo.postnId,
                    brandComCode: null,
                    brandComId: null,
                    brandComName: null,
                },
                dailyReportSubmissionParam: {           //日报提交情况参数
                    orgId: this.userInfo.orgId,
                    parentOrgId: this.userInfo.orgId,
                    orgName: this.userInfo.orgName,
                    postnId: this.userInfo.postnId,
                    orgType: this.userInfo.orgType,
                },
                tempDailyReportSubmissionParam:{        //日报提交情况参数
                    orgId: this.userInfo.orgId,
                    parentOrgId: this.userInfo.orgId,
                    orgName: this.userInfo.orgName,
                    postnId: this.userInfo.postnId,
                    orgType: this.userInfo.orgType,
                },
                currentSeries: null,                    //选择片区按钮和品牌按钮时，可确定当前选中的模块
                dialogFlag: false,                      //组织弹框
                autoList: new this.AutoList(this, {
                    module: 'action/link/orgnization',
                    searchFields: ['text'],
                    loadOnStart: false,
                    param: {
                        oauth: 'MY_ORG',
                        filtersRaw: [],
                        orgLevel: 'Y'
                    },
                    sortOptions: null,
                }),
                orgIdArr: [],                            //可返回的组织数组
                tempOrgId: null,                         //选中的组织
                tempOrgName: null,                       //选中的组织
                dailyReportFormData: {},                 //日报表单
                branchCompanyList: [],                   //品牌公司列表
                companyDialogFlag: false,                //品牌公司弹框
                selectedBranchCompanyId: null,           //选中的品牌公司
                branchCompanyList0: {}
            }
        },
        props: {
            userInfo: {
                type: Object,
                default: function () {
                    return {}
                }
            },

        },
        async created() {
            console.log(this.userInfo)
            // this.orgOuathFlag为true时，组织片区弹框出现，当前组织类型为orgType === Company 时 1：需要查询品牌公司branchCompanyList，以及branchCompanyList0 2：branchCompanyList0 赋值 品牌公司参数
            // this.orgOuathFlag为true时，组织片区弹框出现，当前组织类型为orgType ！== Company 时 1：需要查询品牌公司branchCompanyList，以及branchCompanyList0 2：当前用户信息 赋值 品牌公司参数
            // this.orgOuathFlag为false时，组织片区弹框不出现 1：当前用户信息 赋值 品牌公司参数
            // 2021-07-28 匹配总部人员按品牌公司+大区/片区维度分权限查看数据需求。
            // i:如果配置了访问组安全性（accessGroupOauth 有值），那么默认不查询数据。且无论当前用户什么职位都展示组织片区弹框供选择，选择某一个组织之后再查询目标组织的报表数据。
            // ii:如果没有配访问组安全性，那么就走原本的逻辑。
            if(this.$utils.isEmpty(this.accessGroupOauth)){
                this.createdFlag = true
                this.$utils.showLoading()
                this.orgIdArr = []
                this.orgIdArr.push({orgId: this.userInfo.orgId, orgName: this.userInfo.orgName})
                if (this.orgOuathFlag) {
                    await this.fetchBranchCompanyList()
                }else{
                    this.initSalesmanBrandParam()
                }
                //lzlj-002-2241当前登陆人组织层级为销售公司时，由于数据量较大加载较慢，默认显示国窖公司信息
                if(this.userInfo.orgType === 'Company') {
                    const guoJiaoInfo = {
                        orgId: '58929586649432064',
                        parentOrgId: '58929085107142656',
                        orgName: '泸州老窖国窖酒类销售股份有限公司',
                        orgType: 'BranchCompany',
                        brandComId: '58929586649432064',
                        brandComCode: '5600'
                    }
                    this.visitOverviewParam = {...this.visitOverviewParam, ...guoJiaoInfo}
                    this.tempVisitOverviewParam = {...this.tempVisitOverviewParam, ...guoJiaoInfo}
                    this.visitCoverageParam = {...this.visitCoverageParam, ...guoJiaoInfo}
                    this.tempVisitCoverageParam = {...this.tempVisitCoverageParam, ...guoJiaoInfo}
                    this.dailyReportSubmissionParam = {...this.dailyReportSubmissionParam, ...guoJiaoInfo}
                    this.tempDailyReportSubmissionParam = {...this.tempDailyReportSubmissionParam, ...guoJiaoInfo}
                }
                //allSettled兼容
                if (!Promise.allSettled) {
                    const rejectHandler = reason => ({status: "rejected", reason})
                    const resolveHandler = value => ({status: "fulfilled", value})
                    Promise.allSettled = promises =>
                        Promise.all(
                            promises.map((promise) =>
                                Promise.resolve(promise)
                                    .then(resolveHandler, rejectHandler)
                            )
                        );
                };
                Promise.allSettled([this.visitOverviewData(), this.terminalCoverData(), this.dailyReportData()]).then((res) => {
                    this.createdFlag = false
                    this.$utils.hideLoading()
                })
            }
        },
        methods:{
            /**
             * @createdBy  张丽娟
             * @date  2021/5/26
             * @methods initSalesmanBrandParam
             * @para
             * @description 业务模块Param 品牌公司信息默认赋值
             */
            initSalesmanBrandParam(){
                this.visitCoverageParam = Object.assign(this.visitCoverageParam ,{
                    brandComId: this.userInfo.coreOrganizationTile.l3Id,
                    brandComCode: this.userInfo.coreOrganizationTile.brandCompanyCode,
                })
                this.tempVisitCoverageParam = Object.assign(this.tempVisitCoverageParam ,{
                    brandComId: this.userInfo.coreOrganizationTile.l3Id,
                    brandComCode: this.userInfo.coreOrganizationTile.brandCompanyCode,
                })
            },

            /**
             * @createdBy  张丽娟
             * @date  2020/11/17
             * @methods fetchBranchCompanyList
             * @para
             * @description 品牌公司列表
             *   this.orgOuathFlag为true时，组织片区弹框出现，当前组织类型为orgType === Company 时 1：需要查询品牌公司branchCompanyList，以及branchCompanyList0 2：branchCompanyList0 赋值 品牌公司参数
             this.orgOuathFlag为true时，组织片区弹框出现，当前组织类型为orgType ！== Company 时 1：需要查询品牌公司branchCompanyList，以及branchCompanyList0 2：当前用户信息 赋值 品牌公司参数
             this.orgOuathFlag为false时，组织片区弹框不出现 1：当前用户信息 赋值 品牌公司参数
             */
            async fetchBranchCompanyList(){
                let param =
                    {
                        "filtersRaw": [
                            {
                                "id": "orgType", // 组织类型，筛选品牌公司类型
                                "property": "orgType",
                                "value": "BranchCompany"
                            },
                            {
                                "id": "isEffective", // 是否有效
                                "property": "isEffective",
                                "value": "Y"
                            },
                            {
                                "id": "isLaunch", // 是否有效
                                "property": "isLaunch",
                                "value": "Y"
                            },
                        ],
                        pageFlag: false, // 不分页
                        orgLevel: 'Y'
                    }
                let data = await this.$http.post('action/link/orgnization/queryByExamplePage', param);
                if(!data.success){
                    this.$showError('获取品牌公司失败');
                    return
                }
                this.branchCompanyList = data.rows;
                // 品牌公司默认数据用第一条数据
                this.branchCompanyList0 = Object.assign({},{
                    brandComId: this.branchCompanyList[0].id,
                    brandComName: this.branchCompanyList[0].text,
                    brandComCode: this.branchCompanyList[0].sapCompCode,
                })
                if(this.branchCompanyList.length > 0 && this.userInfo.orgType === 'Company'){
                    this.visitCoverageParam = Object.assign( this.visitCoverageParam,{
                        brandComId: this.branchCompanyList[0].id,
                        brandComName: this.branchCompanyList[0].text,
                        brandComCode: this.branchCompanyList[0].sapCompCode,
                    })
                    this.tempVisitCoverageParam = Object.assign( this.tempVisitCoverageParam,{
                        brandComId: this.branchCompanyList[0].id,
                        brandComName: this.branchCompanyList[0].text,
                        brandComCode: this.branchCompanyList[0].sapCompCode,
                    })
                }else{
                    this.initSalesmanBrandParam()
                }
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/16
             * @methods visitOverviewData
             * @para
             * @description 终端预览数据
             */
            async visitOverviewData(){
                let param = {};
                if(this.orgOuathFlag || this.cityManagerFlag){
                    if(this.visitOverviewParam.orgType === 'Company'){
                        param = {
                            "oauth": "MY_ORG", // 安全性，下级组织-MY_ORG，下级职位-MY_POSTN
                            "orgId": this.visitOverviewParam.orgId, // 组织安全性传递orgId，职位安全性传递postnId
                            "orgType":"Company" // 组织类型为"销售公司"时传递
                        }
                    }else{
                        param = {
                            "oauth": "MY_ORG", // 安全性，下级组织-MY_ORG，下级职位-MY_POSTN
                            "orgId": this.visitOverviewParam.orgId, // 组织安全性传递orgId，职位安全性传递postnId
                        }
                    }

                }else{
                    if(this.visitOverviewParam.orgType === 'Company') {
                        param = {
                            "oauth": 'MULTI_POSTN',
                            "postnId": this.userInfo.postnId, // 组织安全性传递orgId，职位安全性传递postnId
                            "orgType":"Company", // 组织类型为"销售公司"时传递
                            "brandComId": this.visitOverviewParam.brandComId, // 组织类型为"销售公司"时传递
                        }
                    }else{
                        param = {
                            "oauth": 'MULTI_POSTN',
                            "postnId": this.userInfo.postnId, // 组织安全性传递orgId，职位安全性传递postnId
                            "brandComId": this.visitOverviewParam.brandComId, // 组织类型为"销售公司"时传递
                        }
                    }
                }
                this.$utils.showLoading()
                let data = await this.$http.post('action/link/visitReport/queryVisitOverviewReport', param);
                if(!this.createdFlag) this.$utils.hideLoading()
                if(!data.success){
                    this.$showError('获取拜访预览数据失败');
                    return
                }
                this.visitOverviewFormData = data.result
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/16
             * @methods terminalCoverData
             * @para
             * @description 拜访覆盖终端统计
             */
            async terminalCoverData(){
                let param = {};
                if(this.orgOuathFlag || this.cityManagerFlag){
                    if(this.visitCoverageParam.orgType === 'Company'){
                        param = {
                            "oauth": "MY_ORG", // 安全性，下级组织-MY_ORG，下级职位-MY_POSTN
                            "orgId": this.visitCoverageParam.orgId, // 组织安全性传递orgId，职位安全性传递postnId
                            "periodType": this.visitCoverageParam.dateType, // 时间段类型，本周-week，本月-month
                        }
                    }else{
                        param = {
                            "oauth": "MY_ORG", // 安全性，下级组织-MY_ORG，下级职位-MY_POSTN
                            "orgId": this.visitCoverageParam.orgId, // 组织安全性传递orgId，职位安全性传递postnId
                            "periodType": this.visitCoverageParam.dateType, // 时间段类型，本周-week，本月-month
                            "brandComCode": this.visitCoverageParam.brandComCode  || null,// 当前用户的品牌公司代码或选择的品牌公司代码
                            "brandComId": this.visitCoverageParam.brandComId || null//  品牌公司id，当前用户的品牌公司id 或 选择的品牌公司id
                        }
                    }
                }else{
                    if(this.visitCoverageParam.orgType === 'Company'){
                        param ={
                            "oauth": 'MULTI_POSTN',
                            "postnId": this.visitCoverageParam.postnId, // 组织安全性传递orgId，职位安全性传递postnId
                            "periodType": this.visitCoverageParam.dateType, // 时间段类型，本周-week，本月-month
                        }
                    }else{
                        param ={
                            "oauth": 'MULTI_POSTN',
                            "postnId": this.visitCoverageParam.postnId, // 组织安全性传递orgId，职位安全性传递postnId
                            "periodType": this.visitCoverageParam.dateType, // 时间段类型，本周-week，本月-month
                            "brandComCode": this.visitCoverageParam.brandComCode  || null,// 当前用户的品牌公司代码或选择的品牌公司代码
                            "brandComId": this.visitCoverageParam.brandComId || null//  品牌公司id，当前用户的品牌公司id 或 选择的品牌公司id
                        }
                    }
                }
                this.$utils.showLoading()
                let data = await this.$http.post('action/link/visitReport/queryTerminalCoverReport', param);
                if(!this.createdFlag) this.$utils.hideLoading()
                if(!data.success){
                    this.$showError('获取拜访覆盖数据失败');
                    return
                }
                this.visitCoverageData = data.result.reverse()
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/16
             * @methods dailyReportData
             * @para
             * @description 日报表单数据
             */
            async dailyReportData(){
                let param = {};
                if(this.orgOuathFlag){
                    param ={
                        oauth: "MY_ORG", // 安全性，下级组织-MY_ORG，下级职位-MY_POSTN
                        oauthFlag: true,
                        orgId: this.dailyReportSubmissionParam.orgId, // 组织安全性传递orgId，职位安全性传递postnId
                    }
                }else{
                    if(this.userInfo.positionType === 'Salesman' || this.userInfo.positionType === 'SalesTeamLeade'){
                        param ={
                            oauth: "MY", // 安全性，下级组织-MY_ORG，下级职位-MY_POSTN
                            oauthFlag: true,
                            createdBy: this.userInfo.id,
                        }
                    } else if(this.cityManagerFlag) {
                        param = {
                            oauth: "MY_ORG",
                            oauthFlag: true,
                            orgId: this.dailyReportSubmissionParam.orgId,
                        }
                    } else {
                        param ={
                            oauth: "SUB_POSTN", // 安全性，下级组织-MY_ORG，下级职位-MY_POSTN
                            oauthFlag: true,
                            postnId: this.dailyReportSubmissionParam.postnId, // 组织安全性传递orgId，职位安全性传递postnId
                        }
                    }

                }
                this.$utils.showLoading()
                let data = await this.$http.post('action/link/terminal/daily/countReport', param);
                if(!this.createdFlag) this.$utils.hideLoading()
                if(!data.success){
                    this.$showError('获取终端预览数据失败');
                    return
                }
                this.dailyReportFormData = data.result

            },
            /**
             * @createdBy  张丽娟
             * @date  2020/10/28
             * @methods tapFilterOrganization
             * @para
             * @description 点片区按钮、品牌公司按钮后：用上次选中参数查询弹框数据
             */
            async tapFilterOrganization(param,com){
                this.currentSeries = param
                let parentOrgId = null
                if(com && com === 'com'){
                    this.companyDialogFlag = true
                    switch (this.currentSeries) {
                        case 'overView': {
                            this.selectedBranchCompanyId = this.visitOverviewParam.brandComId
                            parentOrgId = this.visitOverviewParam.parentOrgId
                            break
                        }
                        case 'coverage': {
                            this.selectedBranchCompanyId = this.visitCoverageParam.brandComId
                            parentOrgId = this.visitCoverageParam.parentOrgId
                            break
                        }
                        case 'dailyReport': {
                            this.selectedBranchCompanyId = this.dailyReportSubmissionParam.brandComId
                            parentOrgId = this.dailyReportSubmissionParam.parentOrgId
                            break
                        }
                    }
                    return
                }
                switch (this.currentSeries){
                    case 'overView': {
                        this.tempOrgId = this.visitOverviewParam.orgId
                        parentOrgId = this.visitOverviewParam.parentOrgId
                        break
                    }
                    case 'coverage': {
                        this.tempOrgId = this.visitCoverageParam.orgId
                        parentOrgId = this.visitCoverageParam.parentOrgId
                        break
                    }
                    case 'dailyReport': {
                        this.tempOrgId = this.dailyReportSubmissionParam.orgId
                        parentOrgId = this.dailyReportSubmissionParam.parentOrgId
                        break
                    }
                }
                if(this.$utils.isEmpty(this.accessGroupOauth)){
                    //查询自己所在组织层级
                    if(this.orgIdArr.length === 0){
                        let filtersRaw = [
                            {'id': 'orgId', 'property': 'orgId', 'value': this.userInfo.orgId, 'operator': '='},
                            {"id": "isEffective", "property": "isEffective", "value": "Y"},
                            {"id": "orgType", "property": "orgType", "value": this.userInfo.orgType},
                        ]
                        this.autoList.option.param.filtersRaw = filtersRaw
                        this.autoList.methods.reload()
                    }else {
                        let filtersRaw = [
                            {'id': 'parentOrgId', 'property': 'parentOrgId', 'value': this.orgIdArr[this.orgIdArr.length - 1].orgId, 'operator': '='},
                            {"id": "isEffective", "property": "isEffective", "value": "Y"}
                        ]
                        if(this.orgIdArr[this.orgIdArr.length - 1].orgType === 'Company') {
                            filtersRaw.push({"id": "orgType", "property": "orgType", "value": 'BranchCompany'})
                        }
                        this.autoList.option.param.filtersRaw = filtersRaw
                        this.autoList.methods.reload()
                    }
                } else {
                    let filtersRaw = [
                        //{'id': 'parentOrgId', 'property': 'parentOrgId', 'value': parentOrgId, 'operator': '='},
                        {"id": "isEffective", "property": "isEffective", "value": "Y"}
                    ]
                    this.autoList.option.param.oauth = this.accessGroupOauth;
                    this.autoList.option.param.filtersRaw = filtersRaw;
                    await this.autoList.methods.reload()
                }
                this.dialogFlag = true
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/9
             * @methods tempOrgInfo
             * @para
             * @description 存储选中的行信息
             */
            tempOrgInfo(data,param){
                switch (this.currentSeries){
                    case 'overView': {
                        this.tempVisitOverviewParam.orgId = data.id
                        this.tempVisitOverviewParam.parentOrgId = data.parentOrgId
                        this.tempVisitOverviewParam.postnId = data.postnId
                        this.tempVisitOverviewParam.orgName= data.text
                        this.tempVisitOverviewParam.orgType= data.orgType
                        break
                    }
                    case 'coverage': {
                        if(param === 'company'){
                            this.tempVisitCoverageParam.brandComId = data.id
                            this.tempVisitCoverageParam.brandComName= data.text
                            this.tempVisitCoverageParam.brandComCode=  data.orgTile.brandCompanyCode
                        }else{
                            this.tempVisitCoverageParam.orgId = data.id
                            this.tempVisitCoverageParam.parentOrgId = data.parentOrgId
                            this.tempVisitCoverageParam.orgType = data.orgType
                            this.tempVisitCoverageParam.postnId = data.postnId
                            this.tempVisitCoverageParam.orgName= data.text
                            this.tempVisitCoverageParam.brandComCode = data.orgTile.brandCompanyCode
                            this.tempVisitCoverageParam.brandComId = data.companyId
                            this.tempVisitCoverageParam.brandComName = data.companyName
                        }
                        break
                    }
                    case 'dailyReport': {
                        this.tempDailyReportSubmissionParam.orgId = data.id
                        this.tempDailyReportSubmissionParam.parentOrgId = data.parentOrgId
                        this.tempDailyReportSubmissionParam.orgType = data.orgType
                        this.tempDailyReportSubmissionParam.postnId = data.postnId
                        this.tempDailyReportSubmissionParam.orgName= data.text
                        break
                    }
                }
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/9
             * @methods gotoItemOrg
             * @para
             * @description 跳转到子组织
             */
            async gotoItemOrg(data){
                let filtersRaw = [
                    {'id': 'parentOrgId', 'property': 'parentOrgId', 'value': data.id, 'operator': '='},
                    {"id": "isEffective", "property": "isEffective", "value": "Y"}
                ]
                if(data.orgType === 'Company') {
                    filtersRaw.push({"id": "orgType", "property": "orgType", "value": 'BranchCompany'})
                }
                this.autoList.option.param.filtersRaw = filtersRaw
                if(!this.$utils.isEmpty(this.autoList.option.param.oauth)){
                    delete this.autoList.option.param.oauth;
                }
                await this.autoList.methods.reload()
                this.orgIdArr.push({orgId: data.id,orgName : data.text, orgType: data.orgType})
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/9
             * @methods goBackOrg
             * @para
             * @description 返回上一级组织列表
             */
            goBackOrg(){
                if(this.$utils.isEmpty(this.accessGroupOauth)){
                    if(this.orgIdArr.length === 0) return;
                    if(this.orgIdArr.length === 1){
                        this.orgIdArr.pop()
                        let filtersRaw = [
                            {'id': 'orgId', 'property': 'orgId', 'value': this.userInfo.orgId, 'operator': '='},
                            {"id": "isEffective", "property": "isEffective", "value": "Y"},
                            {"id": "orgType", "property": "orgType", "value": this.userInfo.orgType}
                        ]
                        this.autoList.option.param.filtersRaw = filtersRaw
                        this.autoList.methods.reload()
                        return
                    }
                    this.orgIdArr.pop();
                    let filtersRaw = [
                        {'id': 'parentOrgId', 'property': 'parentOrgId', 'value': this.orgIdArr[this.orgIdArr.length - 1].orgId, 'operator': '='},
                        {"id": "isEffective", "property": "isEffective", "value": "Y"}
                    ]
                    if(this.orgIdArr[this.orgIdArr.length - 1].orgType === 'Company') {
                        filtersRaw.push({"id": "orgType", "property": "orgType", "value": 'BranchCompany'})
                    }
                    this.autoList.option.param.filtersRaw = filtersRaw;
                } else {
                    let filtersRaw = [
                        //{'id': 'parentOrgId', 'property': 'parentOrgId', 'value': this.orgIdArr[this.orgIdArr.length - 1].orgId, 'operator': '='},
                        {"id": "isEffective", "property": "isEffective", "value": "Y"}
                    ]
                    this.autoList.option.param.filtersRaw = filtersRaw;
                    this.autoList.option.param.oauth = this.accessGroupOauth;
                }
                this.autoList.methods.reload()
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/17
             * @methods qoalcoverageQuery
             * @para
             * @description 本月，本周筛选
             */
            qoalcoverageQuery(val,param){
                this.visitCoverageParam.dateType = param
                this.terminalCoverData()
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/10/28
             * @methods clickOrginzation
             * @para
             * @description 片区弹框、品牌公司弹框确认按钮 (点确定之后的组织，再次打开弹框，会打开所在层级，只勾选没有点确认，则不会)
             */
            clickOrginzation(param){
                switch (this.currentSeries){
                    case 'overView': {
                        this.visitOverviewParam.orgId = this.tempVisitOverviewParam.orgId
                        this.visitOverviewParam.parentOrgId = this.tempVisitOverviewParam.parentOrgId
                        this.visitOverviewParam.orgName = this.tempVisitOverviewParam.orgName
                        this.visitOverviewParam.orgType = this.tempVisitOverviewParam.orgType
                        this.visitOverviewParam.postnId = this.tempVisitOverviewParam.postnId
                        this.visitOverviewData()
                        break
                    }
                    case 'coverage': {
                        if(param && param === 'company'){
                            // 品牌公司弹框，选择品牌公司后，重新赋值
                            this.visitCoverageParam.brandComId = this.tempVisitCoverageParam.brandComId
                            this.visitCoverageParam.brandComName = this.tempVisitCoverageParam.brandComName
                            this.visitCoverageParam.brandComCode = this.tempVisitCoverageParam.brandComCode
                        }else{
                            // 组织弹框，选中组织的组织类型orgType ！== 'Company' 时，不能确定品牌公司
                            // 组织弹框，选中组织的组织类型orgType === 'Company' 时，默认赋值第一个品牌公司
                            this.visitCoverageParam.orgId = this.tempVisitCoverageParam.orgId
                            this.visitCoverageParam.parentOrgId = this.tempVisitCoverageParam.parentOrgId
                            this.visitCoverageParam.orgName = this.tempVisitCoverageParam.orgName
                            this.visitCoverageParam.orgType = this.tempVisitCoverageParam.orgType
                            this.visitCoverageParam.postnId = this.tempVisitCoverageParam.postnId
                            if(this.visitCoverageParam.orgType === 'Company'){
                                this.visitCoverageParam.brandComId = this.branchCompanyList0.brandComId
                                this.visitCoverageParam.brandComName = this.branchCompanyList0.brandComName
                                this.visitCoverageParam.brandComCode = this.branchCompanyList0.brandComCode
                            }else{
                                this.visitCoverageParam.brandComId = this.tempVisitCoverageParam.brandComId
                                this.visitCoverageParam.brandComName = this.tempVisitCoverageParam.brandComName
                                this.visitCoverageParam.brandComCode = this.tempVisitCoverageParam.brandComCode
                            }
                        }
                        this.terminalCoverData()
                        break
                    }
                    case 'dailyReport': {
                        this.dailyReportSubmissionParam.orgId = this.tempDailyReportSubmissionParam.orgId
                        this.dailyReportSubmissionParam.parentOrgId = this.tempDailyReportSubmissionParam.parentOrgId
                        this.dailyReportSubmissionParam.orgName = this.tempDailyReportSubmissionParam.orgName
                        this.dailyReportSubmissionParam.orgType = this.tempDailyReportSubmissionParam.orgType
                        this.dailyReportSubmissionParam.postnId = this.tempDailyReportSubmissionParam.postnId
                        this.dailyReportData()
                        break
                    }
                }
                this.dialogFlag = false
                this.companyDialogFlag = false
            },
        }
    }
</script>

<style lang="scss">
    .visit-board{
        background: #fff;
        padding: 8px 0 68px;
        .line-title{
            margin-left: 32px;
            margin-bottom: 24px;
        }
        .link-dialog-body{
            position: relative;
        }
        .link-auto-list .link-auto-list-top-bar{
            border:none;
        }
        .link-item .link-item-body-right{
            margin: 0 24px;
        }
        .link-radio-group{
            width: 70px;
            .link-item{
                padding:24px 24px 24px 0;
                .link-item-thumb{
                    padding-right: 0;
                }
                .link-item-icon{
                    display:none;
                }
            }
            .link-item-active{
                background-color: #f6f6f6;
            }
        }
        .list-item{
            flex: 1;
        }
        .link-radio-group .link-item:active,.link-item-active{
            background-color: #f6f6f6;
        }
        .link-auto-list-no-more{
            display: none;
        }
        .link-dialog-foot-custom{
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
        }
        .scroll-view-data{
            margin-top: 24px;
            margin-bottom: 24px;
            .select-dimension{
                display: flex;
                margin-left: 24px;
            }
        }
        .visit-overview{
            margin-bottom: 8px;
            .visit-overview-content{
                padding:24px 0px 12px;
                margin: 0 24px;
                border: 2px solid #EBEDF5;
                border-radius: 16px;
                .title{
                    background: #6D96FA;
                    border-radius: 0 0 8px 0;
                    height: 40px;
                    padding: 0 24px;
                    font-family: PingFangSC-Regular serif;
                    font-size: 24px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    line-height: 40px;
                    text-align: center;
                    display: inline-block;
                }
                .visit-overview-content-row{
                    height: 116px;
                    @include flex-center-center;
                    .visit-overview-content-column{
                        width: 33.33%;
                        height: 116px;
                        @include flex-center-center;
                        .center{
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            flex: 1;
                            .number{
                                font-family: PingFangSC-Semibold serif;
                                letter-spacing: 1px;
                                line-height: 32px;
                                height: 32px;
                                margin-bottom: 16px;
                                display: flex;
                                align-items: center;
                                .before{
                                    color: #262626;
                                    font-size: 32px;
                                    font-family: PingFangSC-Semibold serif;

                                }
                                .after{
                                    color: #000;
                                    font-size: 22px;
                                    line-height: 1;
                                    align-self: flex-end;
                                    font-family: PingFangSC-Semibold serif;
                                }
                                .iconfont{
                                    color:  #BFBFBF;
                                    font-size: 24px;
                                    margin-left: 5.6px;
                                    line-height: 1;
                                    align-self: flex-end;
                                }
                            }
                            .text{
                                font-family: PingFangSC-Regular serif;
                                font-size: 24px;
                                color: #8c8c8c;
                                letter-spacing: 0;
                                text-align: center;
                                line-height: 24px;
                            }
                        }
                        .line{
                            width: 2px;
                            height: 50px;
                            background-image: linear-gradient(180deg, rgba(191,191,191,0.00) 0%, rgba(191,191,191,0.50) 52%, rgba(191,191,191,0.00) 100%);
                        }
                    }
                }
            }
        }
        .visit-coverage-terminal{
            margin-bottom: 8px;
            .data-board{
                margin: 0px 24px;
                box-sizing: border-box;
                font-size: 24px;
                line-height: 24px;
                letter-spacing: 0;
                background-color: white;
                .title{
                    font-family: PingFangSC-Regular,serif;
                    font-size: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #6D96FA;
                    color: white;
                    padding: 24px 0;
                    text-align: center;
                    .title-item{
                        width: 25%;
                        &:last-child{
                            min-width: 202px;
                        }
                    }
                    .iconfont{
                        display: inline-block;
                        color: #fff;
                        font-size: 24px;
                        line-height: 1;
                    }
                }
                .content{
                    font-family: PingFangSC-Regular,serif;
                    font-size: 24px;
                    color: #262626;
                    letter-spacing: 0;
                    text-align: center;
                    .list{
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        height: 56px;
                        line-height: 56px;
                    }
                    .list-item{
                        border-right: 1px solid #DEE7FE;
                        text-align: center;
                        border-bottom: 1px solid #DEE7FE;
                        color: #262626;
                        width: 25%;
                        &:first-child{
                            border-left: 1px solid #DEE7FE;
                        }
                        &:last-child{
                            min-width: 202px;
                        }
                    }

                }
                .list-item:first-child{
                    background-color: #F4F7FE;
                }
            }
        }
        .daily-report-submission-content{
            margin: 0 24px;
            border: 2px solid #EBEDF5;
            border-radius: 16px;
            .visit-overview-content-row{
                height: 152px;
                @include flex-center-center;
                .visit-overview-content-column{
                    width: 33.33%;
                    height: 152px;
                    @include flex-center-center;
                    .center{
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        flex: 1;
                        .number{
                            letter-spacing: 1px;
                            line-height: 32px;
                            height: 32px;
                            margin-bottom: 16px;
                            display: flex;
                            align-items: center;
                            .before{
                                color: #262626;
                                font-size: 32px;
                            }
                            .after{
                                color: #000;
                                font-size: 22px;
                                line-height: 1;
                                align-self: flex-end;
                                font-family: PingFangSC-Semibold serif;
                            }
                            .iconfont{
                                color:  #BFBFBF;
                                font-size: 20px;
                                margin-left: 5.6px;
                                line-height: 1;
                            }
                        }
                        .text{
                            font-family: PingFangSC-Regular serif;
                            font-size: 24px;
                            color: #262626;
                            letter-spacing: 0;
                            text-align: center;
                            line-height: 24px;
                        }
                    }
                    .line{
                        width: 2px;
                        height: 50px;
                        background-image: linear-gradient(180deg, rgba(191,191,191,0.00) 0%, rgba(191,191,191,0.50) 52%, rgba(191,191,191,0.00) 100%);
                    }
                }
            }
        }
    }
</style>
