<template>
    <link-page class="terminal-board">
        <view class="query-btn" v-if="isDaChengNongXiang">
            <view class="btn-item"
                  v-for="(item, index) in tabOption"
                  :key="item.val"
                  :class="{'active': activeIndex === index}"
                  @tap="conditionQuery(index)"
            >
                <view class="text" :class="{'vertical-line': index % 2 === 0}">{{item.name}}</view>
            </view>
        </view>
        <view class="terminal-overview">
            <line-title title="终端总览"></line-title>
            <scroll-view scroll-x="true" class="scroll-view-data"  v-if="orgOuathFlag">
                <view class="select-dimension">
                    <select-button :label="terminalOverviewParam.orgName?terminalOverviewParam.orgName: '全部片区'" :selected-flag="terminalOverviewParam.orgName !== null " @tap="tapFilterOrganization('overView')" downIcon></select-button>
                </view>
            </scroll-view>
            <view class="terminal-overview-content">
                <view class="terminal-overview-content-column">
                    <view class="center">
                        <view class="number">
                            {{terminalOverviewFormData.terminalTotalAmt || 0}}
                        </view>
                        <view class="text">
                            终端总数
                        </view>
                    </view>
                    <view class="line"></view>
                </view>
                <view class="terminal-overview-content-column">
                    <view class="center">
                        <view class="number">
                            {{terminalOverviewParam.orgType === 'Company' ? terminalOverviewFormData.gjTerminalAmt || 0 : terminalOverviewFormData.authTerminalAmt || 0}}
                        </view>
                        <view class="text">
                            {{terminalOverviewParam.orgType === 'Company' ? '国窖终端': '认证数量'}}
                        </view>
                    </view>
                    <view class="line"></view>
                </view>
                <view class="terminal-overview-content-column">
                    <view class="center">
                        <view class="number">
                            {{terminalOverviewParam.orgType === 'Company' ? terminalOverviewFormData.tqTerminalAmt || 0 : terminalOverviewFormData.coreTerminalAmt || 0}}
                        </view>
                        <view class="text">
                            {{terminalOverviewParam.orgType === 'Company' ? '特曲终端': '核心数量'}}
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="terminal-number">
            <line-title title="终端数量分布"></line-title>
            <scroll-view scroll-x="true" class="scroll-view-data">
                <view class="select-dimension">
                    <select-button :label="terminalNumberParam.orgName?terminalNumberParam.orgName: '全部片区'"  v-if="orgOuathFlag"  :selected-flag="terminalNumberParam.orgName !== null " @tap="tapFilterOrganization('number')" downIcon></select-button>
                </view>
            </scroll-view>
            <view class="terminal-number-content" v-show="showEchart">
                <link-echart :option="terminalNumberOption" :height="terminalNumberOptionHeight + 'px'"/>
            </view>
        </view>
        <view class="terminal-level">
            <line-title title="终端等级分布"></line-title>
            <scroll-view scroll-x="true" class="scroll-view-data" >
                <view class="select-dimension">
                    <select-button :label="terminalLevelParam.orgName?terminalLevelParam.orgName: '全部片区'" v-if="orgOuathFlag"  @tap="tapFilterOrganization('level')" downIcon :selected-flag="terminalLevelParam.orgName !== null "></select-button>
                    <select-button label="规划等级" :selected-flag="terminalLevelParam.type === 'planning'"  @tap="terminalLevelQuery(null,'planning')"></select-button>
                    <select-button label="容量级别" :selected-flag="terminalLevelParam.type  === 'capacity'"  @tap="terminalLevelQuery(null,'capacity')"></select-button>
                    <select-button label="达成等级" v-if="guojiaoCompany" :selected-flag="terminalLevelParam.type  === 'achievementLevel'"  @tap="terminalLevelQuery(null,'achievementLevel')"></select-button>
                </view>
            </scroll-view>
            <view class="terminal-level-content"  v-if="terminalLevelParam.type === 'planning'" v-show="showEchart">
                <link-echart :option="capacityLevelOption" :height="capacityLevelBarYCategoryHeight + 'px'" />
            </view>
            <view class="terminal-level-content"  v-else v-show="showEchart">
                <link-echart :option="capacityLevelOption" :height="capacityLevelBarYCategoryHeight + 'px'" />
            </view>
        </view>
        <link-dialog ref="positionBottom" position="bottom" height="90vh" class="dialog-bottom" noPadding v-model="dialogFlag" @hide="showEchart = true">
            <view class="model-title">
                <view class="iconfont icon-left"  v-if="!(autoList.list.length>0 && autoList.list[0].id === this.userInfo.orgId)" @tap="goBackOrg" style="width: 40px;color: #BFBFBF;font-size: 20px;line-height: 48px;height: 48px;"></view>
                <view class="title" style="padding-left:0;">组织片区</view>
                <view class="iconfont icon-close" @tap="dialogFlag = false;showEchart = true" style="margin-right: 15px;"></view>
            </view>
            <view class="dialog-content" style="height: calc(100% - 44px)">
                <scroll-view scroll-y="true" :style="{'height': 'calc(100% - 75px)'}">
                    <link-auto-list :option="autoList" hideCreateButton>
                        <template slot-scope="{data,index}">
                            <view slot="note">
                                <item  :key="index" :data=data @tap="gotoItemOrg(data)" style="padding-top: 0;padding-bottom:0">
                                    <link-radio-group v-model="tempOrgId">
                                        <item :arrow="false">
                                            <link-checkbox :val=data.id slot="thumb"  toggleOnClickItem @tap="tempOrgInfo(data)"/>
                                        </item>
                                    </link-radio-group>
                                    <view class="list-item">
                                        {{data.text}}
                                    </view>
                                </item>
                            </view>
                        </template>
                    </link-auto-list>
                </scroll-view>
                <view class="link-dialog-foot-custom">
                    <link-button shadow @tap="clickOrganization" label="确定" style="width:100vw"/>
                </view>
            </view>
        </link-dialog>
        <link-dialog ref="positionBottom1" position="bottom" height="90vh" class="dialog-bottom" noPadding v-model="companyDialogFlag"  @hide="showEchart = true">
            <view class="model-title">
                <view class="title" style="padding-left:0;">品牌公司</view>
                <view class="iconfont icon-close" @tap="companyDialogFlag = false;showEchart = true" style="margin-right: 15px;"></view>
            </view>
            <view class="dialog-content" style="height: calc(100% - 44px)">
                <scroll-view scroll-y="true" :style="{'height': 'calc(100% - 75px)'}">
                    <list>
                        <item v-for="(item,index) in branchCompanyList" @tap="tempOrgInfo(item,'company',)" arrow="false" :key="index" style="padding-top: 0;padding-bottom:0">
                            <link-radio-group v-model="selectedBranchCompanyId">
                                <item>
                                    <link-checkbox :val=item.id slot="thumb"  toggleOnClickItem @tap="tempOrgInfo(item,'company')"/>
                                </item>
                            </link-radio-group>
                            <view class="list-item">
                                {{item.text}}
                            </view>
                        </item>
                    </list>
                </scroll-view>
                <view class="link-dialog-foot-custom">
                    <link-button shadow @tap="clickOrganization('company')" label="确定" style="width:100vw"/>
                </view>
            </view>
        </link-dialog>
    </link-page>

</template>

<script>
    import SelectButton from "../../components/select-button";
    import LineTitle from "../../../../lzlj/components/line-title";
    import {barYCategory, targetPieChartProgress} from "../../echart.utils";
    import {PreloadImg} from "../../../../../utils/service/PreloadImg";
    import Taro from "@tarojs/taro";
    export default {
        name: "terminal-board",
        components: {LineTitle,SelectButton},
        data(){
            const accessGroupOauth = this.$utils.getMenuAccessGroup('','/pages/echart/lzlj/terminal-data-board/data-board-page');
            let orgOuathFlag =  false;
            if(!this.$utils.isEmpty(accessGroupOauth)){
                orgOuathFlag = true;
            }else{
                orgOuathFlag = this.$utils.isPostnOauth() === 'MY_ORG'? true: false;
            }
            let guojiaoCompany=false;
            const userInfo = Taro.getStorageSync('token').result;
            if(userInfo.coreOrganizationTile.brandCompanyCode === '5600'){
                guojiaoCompany = true;
            }
            return{
                cityManagerFlag: Taro.getStorageSync('token').result.positionType === 'CityManager',
                isDaChengNongXiang: userInfo.coreOrganizationTile.brandCompanyCode === '5161',
                tabOption: [
                    {name: '全部', seq: '1', val: 'all'},
                    {name: '黑盖', seq: '2', val: 'blackCap'},
                ],
                activeIndex: 0,
                createdFlag: true,
                guojiaoCompany, //国窖公司
                orgOuathFlag,//组织选择框flag
                accessGroupOauth,//访问组安全性
                terminalOverviewFormData: {},                   //终端总览表单
                terminalOverviewParam:{                         //终端总览参数
                    orgId: this.userInfo.orgId,
                    parentOrgId: this.userInfo.orgId,
                    orgName: this.userInfo.orgName,
                    postnId: this.userInfo.postnId,
                    salesmanBrandComId: this.userInfo.coreOrganizationTile.l3Id,
                    orgType: this.userInfo.orgType,
                },
                tempTerminalOverviewParam:{                     //终端总览参数
                    orgId: this.userInfo.orgId,
                    parentOrgId: this.userInfo.orgId,
                    orgName: this.userInfo.orgName,
                    postnId: this.userInfo.postnId,
                    orgType: this.userInfo.orgType,
                    data: {},
                },
                terminalNumberParam:{                           //终端数量分布参数
                    orgId: this.userInfo.orgId,
                    parentOrgId: this.userInfo.orgId,
                    orgName: this.userInfo.orgName,
                    postnId: this.userInfo.postnId,
                    orgType: this.userInfo.orgType,
                    salesmanBrandComId: null,
                    salesmanBrandComName: null,
                },
                tempTerminalNumberParam:{                       //终端数量分布参数
                    orgId: this.userInfo.orgId,
                    parentOrgId: this.userInfo.orgId,
                    orgName: this.userInfo.orgName,
                    postnId: this.userInfo.postnId,
                    orgType: this.userInfo.orgType,
                    data: {},
                    salesmanBrandComId: null,
                    salesmanBrandComName: null,
                },
                terminalLevelParam:{                            //终端等级分布参数
                    orgId: this.userInfo.orgId,
                    parentOrgId: this.userInfo.orgId,
                    orgName: this.userInfo.orgName,
                    type: 'planning',
                    orgType : this.userInfo.orgType,
                    salesmanBrandComId: null,
                    sapCompCode: null,
                    salesmanBrandComName: null,
                    postnId: this.userInfo.postnId,
                },
                tempTerminalLevelParam:{                        //终端等级分布参数
                    type: 'planning',
                    orgId: this.userInfo.orgId,
                    parentOrgId: this.userInfo.orgId,
                    orgName: this.userInfo.orgName,
                    postnId: this.userInfo.postnId,
                    orgType: this.userInfo.orgType,
                    sapCompCode: null,
                    salesmanBrandComId: null,
                    salesmanBrandComName: null,
                },
                orgIdArr: [],                                     //可返回的组织数组
                currentSeries: null,                              //选择片区按钮和品牌按钮时，可确定当前选中的模块
                dialogFlag: false,                                //组织弹框
                terminalNumberOption: null,                       //终端数量分布
                planningLevelOption: null,                        //规划级别
                capacityLevelOption: null,                        //容量级别
                capacityLevelBarYCategoryHeight: null,            //容量级别高度
                planningLevelBarYCategoryHeight: null,            //规划等级高度
                tempOrgId: null,                                  //选中的组织
                tempOrgName: null,                                //选中的组织
                autoList: new this.AutoList(this, {
                    module: 'action/link/orgnization',
                    searchFields: ['text'],
                    loadOnStart: false,
                    param: {
                        // oauth: 'MY_ORG',
                        filtersRaw: [],
                        orgLevel: 'Y'
                    },
                    sortOptions: null,
                }),
                selectedBranchCompanyId: null,                  //选中的品牌公司
                companyDialogFlag: false,                      //品牌公司弹框，即选中的组织的类型是否为“Company”），是》ⅰ）显示“品牌公司”筛选条件，【所属品牌公司】salesmanBrandCom，默认选中第一个
                branchCompanyList: [] ,                        //品牌公司数据
                terminalNumberOptionHeight: (this.$device.systemInfo.windowWidth - 24 ) * 0.727 < 254 ? (this.$device.systemInfo.windowWidth - 24 ) * 0.727 : 254,
                showEchart: true,
                branchCompanyList0: {}                        // 当所选组织的组织类型orgType =Company 时，品牌公司按钮出现，branchCompanyList0为第一个品牌公司的数据
            }
        },
        async created() {
            // this.orgOuathFlag为true时，组织片区弹框出现，当前组织类型为orgType === Company 时 1：需要查询品牌公司branchCompanyList，以及branchCompanyList0 2：branchCompanyList0 赋值 品牌公司参数
            // this.orgOuathFlag为true时，组织片区弹框出现，当前组织类型为orgType ！== Company 时 1：需要查询品牌公司branchCompanyList，以及branchCompanyList0 2：当前用户信息 赋值 品牌公司参数
            // this.orgOuathFlag为false时，组织片区弹框不出现 1：当前用户信息 赋值 品牌公司参数
            // 2021-07-28 匹配总部人员按品牌公司+大区/片区维度分权限查看数据需求。
            // i:如果配置了访问组安全性（accessGroupOauth 有值），那么默认不查询数据。且无论当前用户什么职位都展示组织片区弹框供选择，选择某一个组织之后再查询目标组织的报表数据。
            // ii:如果没有配访问组安全性，那么就走原本的逻辑。
            this.createdFlag = true
            this.$utils.showLoading()
            if(this.$utils.isEmpty(this.accessGroupOauth)){
                this.orgIdArr = []
                this.orgIdArr.push({orgId: this.userInfo.orgId, orgName: this.userInfo.orgName, orgType: this.userInfo.orgType})
                let filtersRaw = [
                    {'id': 'parentOrgId', 'property': 'parentOrgId', 'value': this.userInfo.orgId, 'operator': '='},
                    {"id": "isEffective", "property": "isEffective", "value": "Y"}
                ];
                this.autoList.option.param.filtersRaw = filtersRaw;
                await this.autoList.methods.reload();
                if (this.orgOuathFlag) {
                    await this.fetchBranchCompanyList()
                }else{
                    this.initSalesmanBrandParam()
                }
                //lzlj-002-2241当前登陆人组织层级为销售公司时，由于数据量较大加载较慢，默认显示国窖公司信息
                if(this.userInfo.orgType === 'Company') {
                    const guoJiaoInfo = {
                        orgId: '58929586649432064',
                        salesmanBrandComId: '58929586649432064',
                        parentOrgId: '58929085107142656',
                        orgName: '泸州老窖国窖酒类销售股份有限公司',
                        orgType: 'BranchCompany',
                        sapCompCode: '5600'
                    }
                    this.terminalOverviewParam = {...this.terminalOverviewParam, ...guoJiaoInfo}
                    this.tempTerminalOverviewParam = {...this.tempTerminalOverviewParam, ...guoJiaoInfo}
                    this.terminalNumberParam = {...this.terminalNumberParam, ...guoJiaoInfo}
                    this.tempTerminalNumberParam = {...this.terminalOverviewParam, ...guoJiaoInfo}
                    this.terminalLevelParam = {...this.terminalOverviewParam, ...guoJiaoInfo}
                    this.tempTerminalLevelParam = {...this.tempTerminalLevelParam, ...guoJiaoInfo}
                }
                //allSettled兼容
                if (!Promise.allSettled) {
                    const rejectHandler = reason => ({status: "rejected", reason})
                    const resolveHandler = value => ({status: "fulfilled", value})
                    Promise.allSettled = promises =>
                        Promise.all(
                            promises.map((promise) =>
                                Promise.resolve(promise)
                                    .then(resolveHandler, rejectHandler)
                            )
                        );
                };
                Promise.allSettled([this.terminalNumberPie(), this.terminalPlanningLevelBar('acctLevel'),this.terminalOverviewData()]).then((res) => {
                    this.createdFlag = false
                    this.$utils.hideLoading()
                })
            } else {
                let filtersRaw = [
                    //{'id': 'parentOrgId', 'property': 'parentOrgId', 'value': this.userInfo.orgId, 'operator': '='},
                    {"id": "isEffective", "property": "isEffective", "value": "Y"}
                ];
                this.autoList.option.param.filtersRaw = filtersRaw;
                this.autoList.option.param.oauth = this.accessGroupOauth;
                await this.autoList.methods.reload();
                this.createdFlag = false
                this.$utils.hideLoading()
            }
        },
        props:{
            userInfo: {
                type: Object,
                default: function () {
                    return {}
                }
            },
        },
        computed: {
            blackLidFlag() {
                return this.activeIndex === 1 && this.isDaChengNongXiang? 'Y' : 'N'
            }
        },
        methods:{
            /**s
             * @createdBy 康丰强
             * @date  2022/6/22
             * @description
             */
            conditionQuery(index) {
                this.$utils.showLoading()
                this.activeIndex = index
                const params = this.terminalLevelParam.type === 'planning' ? 'acctLevel' : 'capacityLevel'
                Promise.allSettled([this.terminalNumberPie(), this.terminalPlanningLevelBar(params), this.terminalOverviewData()]).then((res) => {
                    this.createdFlag = false
                    this.$utils.hideLoading()
                })
            },
            /**
             * @createdBy  张丽娟
             * @date  2021/5/26
             * @methods initSalesmanBrandParam
             * @para
             * @description 业务模块Param 品牌公司信息默认赋值
             */
            initSalesmanBrandParam(){
                this.terminalNumberParam = Object.assign(this.terminalNumberParam ,{
                    salesmanBrandComId: this.userInfo.coreOrganizationTile.l3Id,
                })
                this.tempTerminalNumberParam = Object.assign(this.tempTerminalNumberParam ,{
                    salesmanBrandComId: this.userInfo.coreOrganizationTile.l3Id,
                })
                this.terminalLevelParam = Object.assign(this.terminalLevelParam ,{
                    salesmanBrandComId: this.userInfo.coreOrganizationTile.l3Id,
                    sapCompCode: this.userInfo.coreOrganizationTile.brandCompanyCode,
                })
                this.tempTerminalLevelParam = Object.assign(this.tempTerminalLevelParam ,{
                    salesmanBrandComId: this.userInfo.coreOrganizationTile.l3Id,
                    sapCompCode: this.userInfo.coreOrganizationTile.brandCompanyCode,
                })
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/17
             * @methods fetchBranchCompanyList
             * @para
             * @description 获取品牌公司数据
             *  this.orgOuathFlag为true时，组织片区弹框出现，当前组织类型为orgType === Company 时 1：需要查询品牌公司branchCompanyList，以及branchCompanyList0 2：branchCompanyList0 赋值 品牌公司参数
             this.orgOuathFlag为true时，组织片区弹框出现，当前组织类型为orgType ！== Company 时 1：需要查询品牌公司branchCompanyList，以及branchCompanyList0 2：当前用户信息 赋值 品牌公司参数
             this.orgOuathFlag为false时，组织片区弹框不出现 1：当前用户信息 赋值 品牌公司参数
             */
            async fetchBranchCompanyList(){
                let param =
                    {
                        "filtersRaw": [
                            {
                                "id": "orgType", // 组织类型，筛选品牌公司类型
                                "property": "orgType",
                                "value": "BranchCompany"
                            },
                            {
                                "id": "isEffective", // 是否有效
                                "property": "isEffective",
                                "value": "Y"
                            },
                            {
                                "id": "isLaunch", // 是否有效
                                "property": "isLaunch",
                                "value": "Y"
                            },
                        ],
                        pageFlag: false, // 不分页，
                        orgLevel: 'Y'
                    }
                let data = await this.$http.post('action/link/orgnization/queryByExamplePage', param);
                if(!data.success){
                    this.$showError('获取品牌公司失败');
                    return
                }
                this.branchCompanyList = data.rows;
                // 品牌公司默认数据用第一条数据
                this.branchCompanyList0 = Object.assign({},{
                    salesmanBrandComId: this.branchCompanyList[0].id,
                    salesmanBrandComName: this.branchCompanyList[0].text,
                    sapCompCode: this.branchCompanyList[0].sapCompCode,
                })
                if (this.branchCompanyList.length > 0 && this.userInfo.orgType === 'Company'){
                    this.terminalNumberParam = Object.assign( this.terminalNumberParam,{
                        salesmanBrandComId: this.branchCompanyList[0].id,
                        salesmanBrandComName: this.branchCompanyList[0].text,
                    })
                    this.tempTerminalNumberParam = Object.assign( this.tempTerminalNumberParam,{
                        salesmanBrandComId: this.branchCompanyList[0].id,
                        salesmanBrandComName: this.branchCompanyList[0].text,
                    })
                    this.terminalLevelParam = Object.assign( this.terminalLevelParam,{
                        salesmanBrandComId: this.branchCompanyList[0].id,
                        salesmanBrandComName: this.branchCompanyList[0].text,
                        sapCompCode: this.branchCompanyList[0].sapCompCode,
                    })
                    this.tempTerminalLevelParam = Object.assign( this.tempTerminalLevelParam,{
                        salesmanBrandComId: this.branchCompanyList[0].id,
                        salesmanBrandComName: this.branchCompanyList[0].text,
                        sapCompCode: this.branchCompanyList[0].sapCompCode,
                    })
                }else{
                    this.initSalesmanBrandParam()
                }
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/14
             * @methods terminalOverviewData
             * @para
             * @description 终端总览数据
             */
            async terminalOverviewData(){
                var param = {}
                if(this.orgOuathFlag || this.cityManagerFlag){
                    if(this.terminalOverviewParam.orgType === 'Company'){
                        param = {
                            orgId: this.terminalOverviewParam.orgId,
                            orgType: 'Company',
                            oauth: 'MY_ORG',
                            blackLidFlag: this.blackLidFlag                       }
                    }else{
                        param = {
                            orgId: this.terminalOverviewParam.orgId,
                            oauth: 'MY_ORG',
                            blackLidFlag: this.blackLidFlag
                        }
                    }
                }else{
                    if(this.terminalOverviewParam.orgType === 'Company'){
                        param = {
                            postnId: this.terminalOverviewParam.postnId,
                            orgType: 'Company',
                            oauth: 'MULTI_POSTN',
                            salesmanBrandComId: this.terminalOverviewParam.salesmanBrandComId || null,
                            blackLidFlag: this.blackLidFlag
                        }
                    }else{
                        param = {
                            postnId: this.terminalOverviewParam.postnId,
                            oauth: 'MULTI_POSTN',
                            salesmanBrandComId: this.terminalOverviewParam.salesmanBrandComId || null,
                            blackLidFlag: this.blackLidFlag
                        }
                    }
                }
                this.$utils.showLoading();
                let data = await this.$http.post('action/link/accntReport/queryTerminalOverviewReport', param);
                if(!this.createdFlag) this.$utils.hideLoading();
                if(!data.success){
                    this.$showError('获取终端预览数据失败');
                    return
                }
                this.terminalOverviewFormData = data.result
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/14
             * @methods  terminalNumPieData
             * @para
             * @description 终端数量数据
             */
            async terminalNumPieData(){
                let param = {}
                if(this.orgOuathFlag || this.cityManagerFlag){
                    if(this.terminalNumberParam.orgType === 'Company'){
                        param = {
                            orgId: this.terminalNumberParam.orgId,
                            oauth: 'MY_ORG',
                            reportType: 'acctCategory',     // 报表类型，固定值
                            orgType: 'Company',              // 组织类型为"销售公司"时传递
                            blackLidFlag: this.blackLidFlag
                        }
                    }else{
                        param = {
                            orgId: this.terminalNumberParam.orgId,
                            oauth: 'MY_ORG',
                            reportType: 'acctCategory',
                            salesmanBrandComId: this.terminalNumberParam.salesmanBrandComId,  //品牌公司id，组织类型为"Company"时传递
                            blackLidFlag: this.blackLidFlag
                        }
                    }
                }else{
                    if(this.terminalNumberParam.orgType === 'Company'){
                        param = {
                            postnId: this.userInfo.postnId,
                            reportType: 'acctCategory',
                            oauth: 'MULTI_POSTN',
                            orgType: 'Company',
                            blackLidFlag: this.blackLidFlag
                        }
                    }else{
                        param = {
                            postnId: this.terminalNumberParam.postnId,
                            oauth: 'MULTI_POSTN',
                            reportType: 'acctCategory',
                            salesmanBrandComId: this.terminalNumberParam.salesmanBrandComId,  //品牌公司id，组织类型为"Company"时传递
                            blackLidFlag: this.blackLidFlag
                        }
                    }
                }
                this.$utils.hideLoading()
                let data = await this.$http.post('action/link/accntReport/queryTerminalDistributeReport', param);
                if(!this.createdFlag) this.$utils.hideLoading();
                if(!data.success){
                    this.$showError('获取终端预览数据失败');
                    return
                }
                return data.result
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/14
             * @methods acctLevelBarData
             * @para
             * @description 终端等级，等级容量数据
             */
            async acctLevelBarData(reportType){
                let param = {}
                if(this.orgOuathFlag || this.cityManagerFlag){
                    if(this.terminalLevelParam.orgType === 'Company'){
                        param = {
                            oauth: 'MY_ORG',
                            orgId: this.terminalLevelParam.orgId,
                            reportType: reportType,// 报表类型，固定值
                            orgType: 'Company', // 组织类型为"销售公司"时传递
                            blackLidFlag: this.blackLidFlag
                        }
                    }else{
                        param = {
                            oauth: 'MY_ORG',
                            orgId: this.terminalLevelParam.orgId,
                            reportType: reportType,// 报表类型，固定值
                            salesmanBrandComId: this.terminalLevelParam.salesmanBrandComId ,// 品牌公司id，组织类型为"Company"时传递
                            mdmCompanyCode: this.terminalLevelParam.sapCompCode || null,  // 登录用户品牌公司代码
                            blackLidFlag: this.blackLidFlag
                        }
                    }
                }else{
                    if(this.terminalLevelParam.orgType === 'Company'){
                        param = {
                            oauth: 'MULTI_POSTN',
                            postnId: this.userInfo.postnId,
                            reportType: reportType,// 报表类型，固定值
                            orgType: 'Company', // 组织类型为"销售公司"时传递
                            blackLidFlag: this.blackLidFlag
                        }
                    }else{
                        param = {
                            oauth: 'MULTI_POSTN',
                            postnId: this.terminalLevelParam.postnId,
                            reportType: reportType,// 报表类型，固定值
                            salesmanBrandComId: this.terminalLevelParam.salesmanBrandComId,// 品牌公司id，组织类型为"Company"时传递
                            mdmCompanyCode: this.terminalLevelParam.sapCompCode || null,   // 登录用户品牌公司代码
                            blackLidFlag: this.blackLidFlag
                        }
                    }
                }
                if(param.reportType === 'capacityLevel'){
                    delete param.mdmCompanyCode
                }
                this.$utils.showLoading();
                let data = await this.$http.post('action/link/accntReport/queryTerminalDistributeReport', param);
                if(!this.createdFlag) this.$utils.hideLoading();
                if(!data.success){
                    this.$showError('获取终端预览数据失败');
                    return
                }
                return data.result
            },
            checkBoxHander(data){
                this.prodId = data.id
                this.prodObj = data
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/10/28
             * @methods tapFilterOrganization
             * @para
             * @description 点片区按钮、品牌公司按钮后：用上次选中参数查询弹框数据
             */
            tapFilterOrganization(param,com){
                this.currentSeries = param
                let parentOrgId = null
                if(com && com === 'com'){
                    this.companyDialogFlag = true
                    this.showEchart = false
                    switch (this.currentSeries) {
                        case 'overView': {
                            this.selectedBranchCompanyId = this.terminalOverviewParam.salesmanBrandComId
                            parentOrgId = this.terminalOverviewParam.parentOrgId
                            break
                        }
                        case 'number': {
                            this.selectedBranchCompanyId = this.terminalNumberParam.salesmanBrandComId
                            parentOrgId = this.terminalNumberParam.parentOrgId
                            break
                        }
                        case 'level': {
                            this.selectedBranchCompanyId = this.terminalLevelParam.salesmanBrandComId
                            parentOrgId = this.terminalLevelParam.parentOrgId
                            break
                        }
                    }
                    return
                }
                switch (this.currentSeries) {
                    case 'overView': {
                        this.tempOrgId = this.terminalOverviewParam.orgId
                        parentOrgId = this.terminalOverviewParam.parentOrgId
                        break
                    }
                    case 'number': {
                        this.tempOrgId = this.terminalNumberParam.orgId
                        parentOrgId = this.terminalNumberParam.parentOrgId
                        break
                    }
                    case 'level': {
                        this.tempOrgId = this.terminalLevelParam.orgId
                        parentOrgId = this.terminalLevelParam.parentOrgId
                        break
                    }
                }
                if(this.$utils.isEmpty(this.accessGroupOauth)){
                    if(this.orgIdArr.length === 0){
                        let filtersRaw = [
                            {'id': 'orgId', 'property': 'orgId', 'value': this.userInfo.orgId, 'operator': '='},
                            {"id": "isEffective", "property": "isEffective", "value": "Y"},
                            {"id": "orgType", "property": "orgType", "value": this.userInfo.orgType},
                        ]
                        this.autoList.option.param.filtersRaw = filtersRaw
                        this.autoList.methods.reload()
                    }else {
                        let filtersRaw = [
                            {'id': 'parentOrgId', 'property': 'parentOrgId', 'value': this.orgIdArr[this.orgIdArr.length - 1].orgId, 'operator': '='},
                            {"id": "isEffective", "property": "isEffective", "value": "Y"}
                        ]
                        if(this.orgIdArr[this.orgIdArr.length - 1].orgType === 'Company') {
                            filtersRaw.push({"id": "orgType", "property": "orgType", "value": 'BranchCompany'})
                        }
                        this.autoList.option.param.filtersRaw = filtersRaw
                        this.autoList.methods.reload()
                    }
                } else {
                    let filtersRaw = [
                        //{id: 'parentOrgId', property: 'parentOrgId', value: parentOrgId, operator: '='},
                        {"id": "isEffective", "property": "isEffective", "value": "Y"},
                    ]
                    this.autoList.option.param.oauth = this.accessGroupOauth;
                    this.autoList.option.param.filtersRaw = filtersRaw
                    this.autoList.methods.reload()
                }
                this.dialogFlag = true
                this.showEchart = false
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/10/28
             * @methods clickOrganization
             * @para
             * @description 片区弹框、品牌公司弹框确认按钮 (点确定之后的组织，再次打开弹框，会打开所在层级，只勾选没有点确认，则不会)
             */
            clickOrganization(param){
                switch (this.currentSeries){
                    case 'overView': {
                        this.terminalOverviewParam.orgId = this.tempTerminalOverviewParam.orgId
                        this.terminalOverviewParam.parentOrgId = this.tempTerminalOverviewParam.parentOrgId
                        this.terminalOverviewParam.orgName = this.tempTerminalOverviewParam.orgName
                        this.terminalOverviewParam.orgType = this.tempTerminalOverviewParam.orgType
                        this.terminalOverviewParam.postnId = this.tempTerminalOverviewParam.postnId
                        this.terminalOverviewData()
                        break
                    }
                    case 'number': {
                        if(param && param === 'company'){
                            // 品牌公司弹框，选择品牌公司后，重新赋值
                            this.terminalNumberParam.salesmanBrandComId = this.tempTerminalNumberParam.salesmanBrandComId
                            this.terminalNumberParam.salesmanBrandComName = this.tempTerminalNumberParam.salesmanBrandComName
                        }else{
                            // 组织弹框，选中组织的组织类型orgType ！== 'Company' 时，不能确定品牌公司
                            // 组织弹框，选中组织的组织类型orgType === 'Company' 时，默认赋值第一个品牌公司
                            this.terminalNumberParam.orgId = this.tempTerminalNumberParam.orgId
                            this.terminalNumberParam.parentOrgId = this.tempTerminalNumberParam.parentOrgId
                            this.terminalNumberParam.orgName = this.tempTerminalNumberParam.orgName
                            this.terminalNumberParam.orgType = this.tempTerminalNumberParam.orgType
                            this.terminalNumberParam.postnId = this.tempTerminalNumberParam.postnId
                            if(this.terminalNumberParam.orgType === 'Company'){
                                this.terminalNumberParam.salesmanBrandComId = this.branchCompanyList0.salesmanBrandComId
                                this.terminalNumberParam.salesmanBrandComName = this.branchCompanyList0.salesmanBrandComName
                            }else{
                                this.terminalNumberParam.salesmanBrandComId = this.tempTerminalNumberParam.salesmanBrandComId
                                this.terminalNumberParam.salesmanBrandComName = this.tempTerminalNumberParam.salesmanBrandComName
                            }
                        }
                        this.terminalNumberPie()
                        break
                    }
                    case 'level': {
                        if (param && param === 'company') {
                            // 品牌公司弹框，选择品牌公司后，重新赋值
                            this.terminalLevelParam.salesmanBrandComId = this.tempTerminalLevelParam.salesmanBrandComId
                            this.terminalLevelParam.salesmanBrandComName = this.tempTerminalLevelParam.salesmanBrandComName
                            this.terminalLevelParam.sapCompCode = this.tempTerminalLevelParam.sapCompCode
                        } else {
                            // 组织弹框，选中组织的组织类型orgType ！== 'Company' 时，不能确定品牌公司
                            // 组织弹框，选中组织的组织类型orgType === 'Company' 时，默认赋值第一个品牌公司
                            this.terminalLevelParam.orgType = this.tempTerminalLevelParam.orgType
                            this.terminalLevelParam.orgId = this.tempTerminalLevelParam.orgId
                            this.terminalLevelParam.parentOrgId = this.tempTerminalLevelParam.parentOrgId
                            this.terminalLevelParam.orgName = this.tempTerminalLevelParam.orgName
                            this.terminalLevelParam.postnId = this.tempTerminalLevelParam.postnId
                            if(this.terminalLevelParam.orgType === 'Company'){
                                this.terminalLevelParam.salesmanBrandComId = this.branchCompanyList0.salesmanBrandComId
                                this.terminalLevelParam.salesmanBrandComName = this.branchCompanyList0.salesmanBrandComName
                                this.terminalLevelParam.sapCompCode = this.branchCompanyList0.sapCompCode
                            }else{
                                this.terminalLevelParam.salesmanBrandComId = this.tempTerminalLevelParam.salesmanBrandComId
                                this.terminalLevelParam.salesmanBrandComName = this.tempTerminalLevelParam.salesmanBrandComName
                                this.terminalLevelParam.sapCompCode = this.tempTerminalLevelParam.sapCompCode
                            }
                        }
                        if (this.terminalLevelParam.type === 'planning') {
                            this.terminalPlanningLevelBar('acctLevel')
                        } else {
                            this.terminalPlanningLevelBar('capacityLevel')
                        }
                    }
                }
                this.companyDialogFlag = false
                this.dialogFlag = false
                this.showEchart = true
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/9
             * @methods tempOrgInfo
             * @para
             * @description 存储选中的行信息
             */
            tempOrgInfo(data,param){
                switch (this.currentSeries){
                    case 'overView': {
                        this.tempTerminalOverviewParam.orgId = data.id
                        this.tempTerminalOverviewParam.parentOrgId = data.parentOrgId
                        this.tempTerminalOverviewParam.orgName = data.text
                        this.tempTerminalOverviewParam.postnId = data.postnId
                        this.tempTerminalOverviewParam.orgType = data.orgType
                        break
                    }
                    case 'number': {
                        if(param === 'company'){
                            this.tempTerminalNumberParam.salesmanBrandComId = data.id
                            this.tempTerminalNumberParam.salesmanBrandComName= data.text
                        }else{
                            this.tempTerminalNumberParam.orgId = data.id
                            this.tempTerminalNumberParam.parentOrgId = data.parentOrgId
                            this.tempTerminalNumberParam.orgName = data.text
                            this.tempTerminalNumberParam.postnId= data.postnId
                            this.tempTerminalNumberParam.orgType= data.orgType
                            this.tempTerminalNumberParam.salesmanBrandComId = data.companyId
                            this.tempTerminalNumberParam.salesmanBrandComName= data.companyName
                        }
                        break
                    }
                    case 'level': {
                        if(param === 'company'){
                            this.tempTerminalLevelParam.salesmanBrandComId= data.id
                            this.tempTerminalLevelParam.salesmanBrandComName= data.text
                            this.tempTerminalLevelParam.sapCompCode= data.orgTile.brandCompanyCode
                        }else{
                            this.tempTerminalLevelParam.orgId = data.id
                            this.tempTerminalLevelParam.parentOrgId = data.parentOrgId
                            this.tempTerminalLevelParam.orgName = data.text
                            this.tempTerminalLevelParam.postnId= data.postnId
                            this.tempTerminalLevelParam.orgType= data.orgType
                            this.tempTerminalLevelParam.salesmanBrandComId = data.companyId
                            this.tempTerminalLevelParam.salesmanBrandComName= data.companyName
                            this.tempTerminalNumberParam.sapCompCode= data.orgTile.brandCompanyCode
                        }
                        break
                    }
                }
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/9
             * @methods gotoItemOrg
             * @para
             * @description 跳转到子组织
             */
            gotoItemOrg(data){
                let filtersRaw = [
                    {id: 'parentOrgId', property: 'parentOrgId', value: data.id, operator: '='},
                    {"id": "isEffective", "property": "isEffective", "value": "Y"},
                ]
                if(data.orgType === 'Company') {
                    filtersRaw.push({"id": "orgType", "property": "orgType", "value": 'BranchCompany'})
                }
                this.autoList.option.param.filtersRaw = filtersRaw;
                if(!this.$utils.isEmpty(this.autoList.option.param.oauth)){
                    delete this.autoList.option.param.oauth;
                }
                this.autoList.methods.reload()
                this.orgIdArr.push({orgId: data.id,orgName : data.text, orgType: data.orgType})
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/9
             * @methods goBackOrg
             * @para
             * @description 返回上一级组织列表
             */
            goBackOrg(){
                if(this.$utils.isEmpty(this.accessGroupOauth)){
                    if(this.orgIdArr.length === 0) return;
                    if(this.orgIdArr.length === 1){
                        this.orgIdArr.pop()
                        let filtersRaw = [
                            {'id': 'orgId', 'property': 'orgId', 'value': this.userInfo.orgId, 'operator': '='},
                            {"id": "isEffective", "property": "isEffective", "value": "Y"},
                            {"id": "orgType", "property": "orgType", "value": this.userInfo.orgType}
                        ]
                        this.autoList.option.param.filtersRaw = filtersRaw
                        this.autoList.methods.reload()
                        return
                    }
                    this.orgIdArr.pop()
                    let filtersRaw = [
                        {'id': 'parentOrgId', 'property': 'parentOrgId', 'value': this.orgIdArr[this.orgIdArr.length - 1].orgId, 'operator': '='},
                        {"id": "isEffective", "property": "isEffective", "value": "Y"}
                    ]
                    if(this.orgIdArr[this.orgIdArr.length - 1].orgType === 'Company') {
                        filtersRaw.push({"id": "orgType", "property": "orgType", "value": 'BranchCompany'})
                    }
                    this.autoList.option.param.filtersRaw = filtersRaw
                    this.autoList.methods.reload()
                    return;
                } else {
                    let filtersRaw = [
                        //{'id': 'parentOrgId', 'property': 'parentOrgId', 'value': this.orgIdArr[this.orgIdArr.length - 1].orgId, 'operator': '='},
                        {"id": "isEffective", "property": "isEffective", "value": "Y"},
                    ]
                    this.autoList.option.param.filtersRaw = filtersRaw
                    this.autoList.option.param.oauth = this.accessGroupOauth
                    this.autoList.methods.reload()
                }
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/4
             * @methods terminalNumberPie
             * @para
             * @description 终端数量饼图
             */
            async terminalNumberPie(){
                let numberData = await this.terminalNumPieData()
                //销售公司层级或者国窖公司层级或者销售大区为形象店可见形象店分类
                if (this.userInfo.coreOrganizationTile.l4Id !== '328927981139325498') {
                    if(this.userInfo.orgId !== '58929085107142656' && this.userInfo.orgId !== '58929586649432064') {
                        numberData = numberData.filter(item => item.target !== 'qdlx-5')
                    }
                }
                this.terminalNumberOption = null
                let seriesData = []
                let totalNum  = 0
                for(let i in numberData){
                    let name = await this.$lov.getNameByTypeAndVal('ACCNT_CATEGORY', numberData[i].target)
                    seriesData.push({
                        value: Number(numberData[i].amount),
                        name: name
                    })
                    totalNum += Number(numberData[i].amount)
                }
                let pieColor = [
                //     {
                //     c1: '#FFB701',  //管理
                //     c2: '#FF5A5A'
                // },{
                //     c1: '#6392FA',  //管理
                //     c2: '#4179F4'
                // },{
                //     c1: '#69CAFF',  //管理
                //     c2: '#36ACEB'
                // },{
                //     c1: '#81F3EF',  //管理
                //     c2: '#5FCACE'
                // },
                ];
                var totalSeriesData = [{value: totalNum, name: '终端总数'}]
                this.terminalNumberOption = echartInitConfig=>targetPieChartProgress(echartInitConfig,seriesData, totalSeriesData,['39%', '61%'],'39%',pieColor,225);
            },

            /**
             * @createdBy  张丽娟
             * @date  2020/11/5
             * @methods terminalLevelQuery
             * @para
             * @description 请求终端等级分布数据
             */
            terminalLevelQuery(val,param){
                switch (param){
                    case 'planning': {
                        this.terminalLevelParam.type = param
                        this.terminalPlanningLevelBar('acctLevel')
                        break
                    }
                    case 'capacity': {
                        this.terminalLevelParam.type = param
                        this.terminalPlanningLevelBar('capacityLevel')
                        break
                    }
                    case 'achievementLevel': {
                        this.terminalLevelParam.type = param
                        this.terminalPlanningLevelBar('achievementLevel')
                        break
                    }
                }
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/4
             * @methods terminalLevelBar
             * @para
             * @description 获取柱状图数据-规划等级-等级容量
             */
            async terminalPlanningLevelBar(reportType){
                let data = await this.acctLevelBarData(reportType)
                let lovType='';
                let levelData = data
                switch (reportType){
                    case 'acctLevel': {
                       lovType = 'ACCT_LEVEL'
                        break
                    }
                    case 'capacityLevel': {
                        lovType = 'CAPACITY_LEVEL'
                        break
                    }
                    case 'achievementLevel': {
                        lovType = 'achievementLevel'
                        break
                    }}
                this.capacityLevelOption = null
                var seriesData = []
                for(let i in levelData){
                    let name = await this.$lov.getNameByTypeAndVal(lovType, levelData[i].target)
                    seriesData.push({
                        value: Number(levelData[i].amount),
                        name: name
                    })
                }
                this.capacityLevelBarYCategoryHeight = 16 + 26 * levelData.length
                this.capacityLevelOption = echartInitConfig=>barYCategory(echartInitConfig,seriesData,false,'家');
            },
        }
    }
</script>

<style lang="scss">
    .terminal-board{
        background: #fff;
        padding: 8px 0 68px;
        .query-btn {
            display: flex;
            font-size: 28px;
            text-align: center;
            border-bottom: 1px solid #cccaca;
            .btn-item {
                flex: 1;
            }
            .vertical-line {
                border-right: 1px solid #cccaca;
            }
            .active {
                color: #2f69f8;
            }
            .text {
                margin: 20px 0;
            }
        }
        .line-title{
            margin-bottom: 24px;
        }
        .link-dialog-body{
            position: relative;
        }
        .link-auto-list .link-auto-list-top-bar{
            border:none;
        }
        .link-item .link-item-body-right{
            margin: 0 24px;
        }
        .link-radio-group{
            width: 70px;
            .link-item{
                padding:24px 24px 24px 0;
                .link-item-thumb{
                    padding-right: 0;
                }
                .link-item-icon{
                    display:none;
                }
            }
            .link-item-active{
                background-color: #f6f6f6;
            }
        }
        .list-item{
            flex: 1;
        }
        .link-radio-group .link-item:active,.link-item-active{
            background-color: #f6f6f6;
        }
        .link-auto-list-no-more{
            display: none;
        }
        .link-dialog-foot-custom{
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
        }
        .line-title{
            margin-left: 32px;
        }
        .dialog-bottom{
            .dialog-content{
                padding: 0 20px;
                position: relative;
                .link-button{
                    position: absolute;
                    bottom: 0
                }
            }
            .model-title {
                display: flex;
                .title {
                    font-family: PingFangSC-Regular,serif;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    text-align: center;
                    line-height: 96px;
                    height: 96px;
                    width: 90%;
                    padding-left: 0!important;
                    margin-right: 80px;
                }
                .icon-left{
                    color: #BFBFBF;
                    font-size: 48px;
                    line-height: 96px;
                    height: 96px;
                }
                .icon-close {
                    color: #BFBFBF;
                    font-size: 48px;
                    line-height: 96px;
                    height: 96px;
                    margin-right: 30px;
                }
            }
        }
        .scroll-view-data{
            margin-top: 24px;
            margin-bottom: 24px;
            .select-dimension{
                display: flex;
                margin-left: 24px;
            }
        }
        .terminal-overview-content{
            height: 152px;
            margin: 0 24px 8px;
            @include flex-center-center;
            border: 2px solid #EBEDF5;
            border-radius: 16px;
            .terminal-overview-content-column{
                width: 33.33%;
                height: 152px;
                @include flex-center-center;
                .center{
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    flex: 1;
                    .number{
                        font-family: PingFangSC-Semibold serif;
                        font-size: 32px;
                        color: #262626;
                        letter-spacing: 1px;
                        line-height: 32px;
                        margin-bottom: 16px;
                    }
                    .text{
                        font-family: PingFangSC-Regular serif;
                        font-size: 24px;
                        color: #262626;
                        letter-spacing: 0;
                        text-align: center;
                        line-height: 24px;
                    }
                }
                .line{
                    width: 2px;
                    height: 50px;
                    background-image: linear-gradient(180deg, rgba(191,191,191,0.00) 0%, rgba(191,191,191,0.50) 52%, rgba(191,191,191,0.00) 100%);
                }
            }
        }
        .terminal-number{
            margin: 0 0px 8px;
            .terminal-number-content{
                border: 2px solid #EBEDF5;
                border-radius: 16px;
                margin: 0 24px;
            }
        }
        .terminal-level{
            .terminal-level-content{
                margin: 0 24px;
                border-radius: 16px;
                border: 1px solid #EBEDF5;
            }
        }
    }
</style>
