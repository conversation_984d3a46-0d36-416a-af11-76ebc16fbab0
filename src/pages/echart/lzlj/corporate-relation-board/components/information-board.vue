<template>
    <link-page class="information-board">
        <view class="terminal-overview">
            <line-title title="企业数量统计"></line-title>
            <view class="scroll-view-data">
                <view class="select-dimension">
                    <select-button :label="infoParam.orgName?infoParam.orgName: '全部片区'" :selected-flag="infoParam.orgName !== null " @tap="tapFilterOrganization('info')" downIcon></select-button>
                    <view class="select-dimension-year">
                        <select-button :label="`${yearVal}年`" :selectedFlag="true" downIcon></select-button>
                        <view class="select-dimension-data"><link-date view="Y" valueFormat="YYYY" displayFormat="YYYY年" @change="selectYear"/></view>
                    </view>
                </view>
                <view class="scrollSpase"></view>
            </view>
            <view class="count-data">
                <view class="count-data-head">
                    <view class="count-data-item-value">{{infoData.newCoNum}}</view>
                    <view class="count-data-item-label">新建企业数量</view>
                </view>
                <view class="count-data-content">
                    <view class="count-data-list" v-for="(info, index) in infoData.countVoList" v-show="info.val !== 'cancel'">
                        <view class="count-data-item">
                            <view class="count-data-item-value">{{info.num}}</view>
                            <view class="count-data-item-label">{{info.name}}</view>
                        </view>
                        <view class="line" v-if="index < (infoData.countVoList.length-1)"></view>
                    </view>
                </view>
            </view>
            <view class="count-data-content" v-show="showEchart">
                <link-echart :option="countDataOption" :height="dataOptionHeight + 'px'" :force-use-old-canvas="false"/>
            </view>
        </view>
        <view class="accnt-number">
            <line-title title="客户数量统计"></line-title>
            <view class="scroll-view-data">
                <view class="select-dimension">
                    <select-button :label="accntParam.orgName?accntParam.orgName: '全部片区'"  :selected-flag="accntParam.orgName !== null " @tap="tapFilterOrganization('accnt')" downIcon></select-button>
                </view>
                <view class="select-dimension">
                    <view class="select-dimension-year">
                        <select-button :label="`${parseInt(monthVal)==(new Date().getMonth()+1)?'本':monthVal}月`" :selectedFlag="timeType==='month'" downIcon></select-button>
                        <view class="select-dimension-data"><link-date view="M" valueFormat="MM" displayFormat="MM月" @change="selectMonth"/></view>
                    </view>
                    <select-button label="本季" :selected-flag="timeType==='quarter'" @tap="selectTime('quarter')"></select-button>
                    <select-button label="本年" :selected-flag="timeType==='year'" @tap="selectTime('year')"></select-button>
                </view>
                <view class="select-dimension">
                    <select-button label="公司级" :selected-flag="visitLevels.indexOf('company') > -1" @tap="changeLevels('company')"></select-button>
                    <select-button label="品牌级" :selected-flag="visitLevels.indexOf('brand') > -1" @tap="changeLevels('brand')"></select-button>
                    <select-button label="区域级" :selected-flag="visitLevels.indexOf('area') > -1" @tap="changeLevels('area')"></select-button>
                </view>
                <view class="scrollSpase"></view>
            </view>
            <view class="count-data">
                <view class="count-data-head">
                    <view class="count-data-item-value">{{accntData.AllTotal}}</view>
                    <view class="count-data-item-label">新建客户数量</view>
                </view>
                <view class="count-data-content">
                    <view class="count-data-list" v-for="(key, val, index) in accntData" :key="index" v-show="val !== 'AllTotal'">
                        <view class="count-data-item">
                            <view class="count-data-item-value">{{key}}</view>
                            <view class="count-data-item-label">{{val}}</view>
                        </view>
                        <view class="line" v-if="index < (accntData.length-1)"></view>
                    </view>
                </view>
            </view>
            <view class="count-data-content" v-show="showEchart">
                <link-echart :option="accntNumberOption" :height="dataOptionHeight + 'px'" :force-use-old-canvas="false"/>
            </view>
        </view>
        <link-dialog ref="positionBottom" position="bottom" height="85vh" class="dialog-bottom" noPadding v-model="dialogFlag" @hide="showEchart = true">
            <view class="model-title">
                <view class="iconfont icon-left"  v-if="!(autoList.list.length>0 && autoList.list[0].id === (isBrandCompany && !isPublicRela?defaultOrg.orgId:this.userInfo.orgId))" @tap="goBackOrg" style="width: 40px;color: #BFBFBF;font-size: 20px;line-height: 48px;height: 48px;"></view>
                <view class="title" style="padding-left:0;">组织片区</view>
                <view class="iconfont icon-close" @tap="closeDialog" style="margin-right: 15px;"></view>
            </view>
            <view class="dialog-content" style="height: calc(100% - 44px)">
                <scroll-view scroll-y="true" :style="{'height': 'calc(100% - 75px)'}">
                    <link-auto-list :option="autoList" hideCreateButton>
                        <template slot-scope="{data,index}">
                            <view slot="note">
                                <item  :key="index" :data=data @tap="gotoItemOrg(data)" style="padding-top: 0;padding-bottom:0">
                                    <link-radio-group v-model="tempOrgId">
                                        <item :arrow="false">
                                            <link-checkbox :val=data.id slot="thumb"  toggleOnClickItem @tap="tempOrgInfo(data)"/>
                                        </item>
                                    </link-radio-group>
                                    <view class="list-item">
                                        {{data.text}}
                                    </view>
                                </item>
                            </view>
                        </template>
                    </link-auto-list>
                </scroll-view>
                <view class="link-dialog-foot-custom">
                    <link-button shadow @tap="clickOrganization" label="确定" style="width:100vw"/>
                </view>
            </view>
        </link-dialog>
    </link-page>
</template>

<script>
import SelectButton from "../../components/select-button"
import LineTitle from "../../components/line-title";
import {targetPieChartProgress} from "../../echart.utils";
import Taro from "@tarojs/taro";
export default {
    name: "information-board",
    components: {LineTitle, SelectButton},
    data(){
        let accessGroupOauth = this.$utils.getMenuAccessGroup('','/pages/echart/lzlj/corporate-relation-board/cpr-board-page');
        let isBrandCompany = false;
        let isPublicRela = false;
        let defaultOrg = {};
        let param = {                         //终端总览参数
            orgId: this.userInfo.orgId,
            parentOrgId: this.userInfo.orgId,
            orgName: this.userInfo.orgName,
            orgType: this.userInfo.orgType,
        };
        if(this.userInfo.coreOrganizationTile.brandCompanyCode === '6001'){
            if(this.userInfo.positionType === 'PublicRelations'){
                if(this.$utils.isEmpty(accessGroupOauth)){
                    accessGroupOauth = 'MY_ORG';
                }else{
                    isBrandCompany = true;
                    isPublicRela = true;
                }
            }else{
                isBrandCompany = true;
                for (let i = 1; i < 20; i++) {
                    if(!this.userInfo.coreOrganizationTile[`p${i}Id`]){
                        defaultOrg = {
                            orgId: this.userInfo.coreOrganizationTile[`p${i-1}Id`],
                            orgName: this.userInfo.coreOrganizationTile[`p${i-1}Name`]
                        };
                        param = {                         //终端总览参数
                            orgId: defaultOrg.orgId,
                            parentOrgId: '',
                            orgName: defaultOrg.orgName,
                            orgType: '',
                        };
                        break;
                    }
                }
            }
        }
        return{
            visitLevels: ['company', 'brand', 'area'],
            orgNow: 'info', // 当前为企业/客户选取组织
            timeType: 'month',
            monthVal: new Date().getMonth() >= 9? new Date().getMonth() + 1 : '0' + (new Date().getMonth() + 1),
            yearVal: new Date().getFullYear(),
            accntData: {
                AllTotal: 0
            },
            infoData: {
                countVoList: [],
                newCoNum: 0
            },
            createdFlag: true,
            isBrandCompany, // 当前职位所属品牌公司是否为会员管理部虚拟公司
            isPublicRela, //
            accessGroupOauth,//访问组安全性
            defaultOrg,
            needNewRow: false, // 是否需要创建数据（默认组织）
            infoParam: {...param},
            accntParam: {...param},
            infoOrgIdArr: [],                                     //企业可返回的组织数组
            accntOrgIdArr: [], // 客户可选组织数据
            dialogFlag: false,                                //组织弹框
            countDataOption: null,                       //终端数量分布
            accntNumberOption: null, //客户数量统计
            tempOrgId: null,                                  //选中的组织
            tempOrgName: null,                                //选中的组织
            autoList: new this.AutoList(this, {
                module: 'action/link/orgnization',
                url: {
                    queryByExamplePage: 'export/link/orgnization/queryByExamplePage',
                },
                searchFields: ['text'],
                loadOnStart: false,
                param: {
                    pageFlag: false,
                    filtersRaw: []
                },
                sortOptions: null,
                hooks: {
                    afterLoad(data) {
                        if(this.needNewRow){
                            data.rows = [{id: this.defaultOrg.orgId, text: this.defaultOrg.orgName}];
                        }
                        data.total = 1;
                    }
                }
            }),
            dataOptionHeight: (this.$device.systemInfo.windowWidth - 24 ) * 0.727 < 254 ? (this.$device.systemInfo.windowWidth - 24 ) * 0.727 : 254,
            showEchart: true
        }
    },
    async created() {
        this.$utils.showLoading()
        //allSettled兼容
        if (!Promise.allSettled) {
            const rejectHandler = reason => ({status: "rejected", reason})
            const resolveHandler = value => ({status: "fulfilled", value})
            Promise.allSettled = promises =>
                Promise.all(
                    promises.map((promise) =>
                        Promise.resolve(promise)
                            .then(resolveHandler, rejectHandler)
                    )
                );
        };
        Promise.allSettled([this.infoNumberPie(), this.accntNumberPie()]).then((res) => {
            this.$utils.hideLoading()
        })
    },
    props:{
        userInfo: {
            type: Object,
            default: function () {
                return {}
            }
        },
    },
    methods:{
        /**
         @param type:更新参数
         @desc: 更新visitLevels
         @author: wangbinxin
         @date 2022-06-07 20-23
         **/
        async changeLevels(type){
            var index = this.visitLevels.indexOf(type);
            if (index > -1) {
                if(this.visitLevels.length >= 2){
                    this.visitLevels.splice(index, 1);
                }else {
                    this.$message.warn('请至少选择一个层级');
                    return
                }
            }else{
                this.visitLevels.push(type);
            }
            await this.accntNumberPie();
        },
        closeDialog(){
          this.dialogFlag = false;
          this.showEchart = true;
        },
        /**
         @desc: 选取月份后重新加载饼图
         @author: wangbinxin
         @date 2022-06-06 20-47
         **/
        async selectMonth(val){
            this.monthVal = val;
            this.timeType = 'month';
            await this.accntNumberPie();
        },
        async selectTime(val){
          this.timeType = val;
          await this.accntNumberPie();
        },
        /**
         @param{number}
         @desc: 选择年份
         @author: wangbinxin
         @date 2022-06-06 15-23
         **/
        async selectYear(val){
            this.yearVal = val;
            await this.infoNumberPie();
        },
        /**
         @desc: 获取企业信息数量信息
         @author: wangbinxin
         @date 2022-06-06 16-16
         **/
        async getInfoQty(){
            this.$utils.showLoading();
            let data = await this.$http.post('export/link/company/queryCompanyCountByExample', {
                orgId: this.infoParam.orgId,
                oauth: 'ALL',
                year: this.yearVal
            });
            this.$utils.hideLoading();
            if(!data.success){
                this.$showError('获取企业数量统计数据失败');
                return
            }
            this.infoData = data.result;
        },
        /**
         @desc: 获取客户数量统计
         @author: wangbinxin
         @date 2022-06-06 16-39
         **/
        async getAcctNumber(){
            var year = new Date().getFullYear();
            var start = year + '-' + this.monthVal + '-01 00:00:00';
            var end = year  + '-' + this.monthVal + '-'+ new Date(year, this.monthVal, 0).getDate() +' 23:59:59';
            if(this.timeType === 'quarter'){
                let nowMonth = new Date().getMonth() + 1; //当前月
                if(nowMonth <= 3 && nowMonth >= 1){
                    start = year + '-01-01 00:00:00';
                    end = year + '-03-31 23:59:59';
                }else if(nowMonth <= 6 && nowMonth >= 4){
                    start = year + '-04-01 00:00:00';
                    end = year + '-06-30 23:59:59';
                }else if(nowMonth <= 9 && nowMonth >= 7){
                    start = year + '-07-01 00:00:00';
                    end = year + '-10-31 23:59:59';
                }else{
                    start = year + '-10-01 00:00:00';
                    end = year + '-12-31 23:59:59';
                }
            }else if(this.timeType === 'year'){
                start = year + '-01-01 00:00:00';
                end = year + '-12-31 23:59:59';
            }
            let param = {
                visitLevels: this.visitLevels,
                ids: [this.accntParam.orgId],
                attr1: start,
                attr2: end,
                attr4: year
            };
            this.$utils.showLoading();
            let data = await this.$http.post(this.$env.appURL + '/link/consumer/qwQueryPositionLevel', param);
            this.$utils.hideLoading();
            if(!data.success){
                this.$showError('获取客户数量统计数据失败');
                return
            }
            var arr = [{name: '企业高层', val: 'HighLevel'}, {name: '企业中层', val: 'MiddleLevel'}, {name: '核心员工', val: 'CoreStaff'}, {name: '待确定', val: 'EmptyTotal'}];
            arr.forEach((item) => {
                let num = data.result[item.val] || 0;
                delete data.result[item.val];
                data.result[item.name] = num;
            });
            this.accntData = data.result;
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/10/28
         * @methods tapFilterOrganization
         * @para
         * @description 点片区按钮、品牌公司按钮后：用上次选中参数查询弹框数据
         */
        tapFilterOrganization(type){
            let parentOrgId = null
            var orgIdArr = [];
            this.orgNow = type;
            if(type === 'accnt'){
                this.tempOrgId = this.accntParam.orgId;
                parentOrgId = this.accntParam.parentOrgId;
                orgIdArr = this.accntOrgIdArr;
            }else{
                this.tempOrgId = this.infoParam.orgId;
                parentOrgId = this.infoParam.parentOrgId;
                orgIdArr = this.infoOrgIdArr;
            }
            if(this.defaultOrg.orgId === this.tempOrgId)this.needNewRow=true;
            let filtersRaw = [];
            if(this.isPublicRela){
                filtersRaw = [
                    {"id": "isEffective", "property": "isEffective", "value": "Y"},
                ];
                this.autoList.option.param.oauth = this.accessGroupOauth;
            }else{
                if(orgIdArr.length === 0){
                    filtersRaw = [
                        {'id': 'id', 'property': 'id', 'value': this.tempOrgId, 'operator': '='},
                        {"id": "isEffective", "property": "isEffective", "value": "Y"},
                        {"id": "orgType", "property": "orgType", "value": this.userInfo.orgType}
                    ]
                    if(this.isBrandCompany && !this.isPublicRela){
                        filtersRaw.pop();
                    }
                }else {
                    filtersRaw = [
                        {'id': 'parentOrgId', 'property': 'parentOrgId', 'value': orgIdArr[orgIdArr.length - 1].orgId, 'operator': '='},
                        {"id": "isEffective", "property": "isEffective", "value": "Y"}
                    ]
                    if(orgIdArr[orgIdArr.length - 1].orgType === 'Company') {
                        filtersRaw.push({"id": "orgType", "property": "orgType", "value": 'BranchCompany'})
                    }
                }
            }
            this.autoList.option.param.filtersRaw = filtersRaw
            this.autoList.methods.reload()
            this.dialogFlag = true
            this.showEchart = false
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/10/28
         * @methods clickOrganization
         * @para
         * @description 片区弹框、品牌公司弹框确认按钮 (点确定之后的组织，再次打开弹框，会打开所在层级，只勾选没有点确认，则不会)
         */
        async clickOrganization(){
            if(this.orgNow === 'info'){
                await this.infoNumberPie();
            }else{
                await this.accntNumberPie();
            }
            this.dialogFlag = false
            this.showEchart = true
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/11/9
         * @methods tempOrgInfo
         * @para
         * @description 存储选中的行信息
         */
        tempOrgInfo(data){
            let param = {
                orgId: data.id,
                parentOrgId: data.parentOrgId,
                orgName: data.text,
                orgType: data.orgType
            }
            this.orgNow === 'info'? this.infoParam = {...param} : this.accntParam = {...param};
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/11/9
         * @methods gotoItemOrg
         * @para
         * @description 跳转到子组织
         */
        gotoItemOrg(data){
            this.needNewRow = false;
            let filtersRaw = [
                {id: 'parentOrgId', property: 'parentOrgId', value: data.id, operator: '='},
                {"id": "isEffective", "property": "isEffective", "value": "Y"},
            ]
            if(data.orgType === 'Company') {
                filtersRaw.push({"id": "orgType", "property": "orgType", "value": 'BranchCompany'})
            }
            this.autoList.option.param.filtersRaw = filtersRaw;
            if(!this.$utils.isEmpty(this.autoList.option.param.oauth)){
                delete this.autoList.option.param.oauth;
            }
            this.autoList.methods.reload()
            if(this.orgNow === 'info'){
                this.infoOrgIdArr.push({orgId: data.id,orgName : data.text, orgType: data.orgType});
            }else{
                this.accntOrgIdArr.push({orgId: data.id,orgName : data.text, orgType: data.orgType})
            }
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/11/9
         * @methods goBackOrg
         * @para
         * @description 返回上一级组织列表
         */
        goBackOrg(){
            var orgIdArr = this.orgNow === 'info'?this.infoOrgIdArr:this.accntOrgIdArr;
            if(this.isPublicRela){
                let filtersRaw = [
                    {"id": "isEffective", "property": "isEffective", "value": "Y"},
                ]
                this.autoList.option.param.filtersRaw = filtersRaw
                this.autoList.option.param.oauth = this.accessGroupOauth
            }else{
                if(orgIdArr.length === 0) return;
                if(orgIdArr.length === 1){
                    orgIdArr.pop()
                    let filtersRaw = [
                        {'id': 'orgId', 'property': 'orgId', 'value': this.userInfo.orgId, 'operator': '='},
                        {"id": "isEffective", "property": "isEffective", "value": "Y"},
                        {"id": "orgType", "property": "orgType", "value": this.userInfo.orgType}
                    ]
                    if(this.isBrandCompany && !this.isPublicRela){
                        filtersRaw = [{'id': 'orgId', 'property': 'orgId', 'value': this.defaultOrg.orgId, 'operator': '='}, {"id": "isEffective", "property": "isEffective", "value": "Y"}];
                        this.needNewRow = true;
                    }
                    this.autoList.option.param.filtersRaw = filtersRaw
                    this.autoList.methods.reload()
                    return
                }
                orgIdArr.pop()
                let filtersRaw = [
                    {'id': 'parentOrgId', 'property': 'parentOrgId', 'value': orgIdArr[orgIdArr.length - 1].orgId, 'operator': '='},
                    {"id": "isEffective", "property": "isEffective", "value": "Y"}
                ]
                if(orgIdArr[orgIdArr.length - 1].orgType === 'Company') {
                    filtersRaw.push({"id": "orgType", "property": "orgType", "value": 'BranchCompany'})
                }
                this.autoList.option.param.filtersRaw = filtersRaw;
            }
            this.autoList.methods.reload();
        },
        /**
         @desc: 客户信息数量饼图绘制
         @author: wangbinxin
         @date 2022-06-06 16-34
         **/
        async accntNumberPie(){
            await this.getAcctNumber();
            this.accntNumberOption = null;
            let seriesData = [];
            let totalNum  = 0;
            for(let i in this.accntData){
                if(i !== 'AllTotal'){
                    seriesData.push({
                        value: this.accntData[i],
                        name: i
                    });
                    totalNum += Number(this.accntData[i]);
                }
            }
            let pieColor = [{
                c1: '#AF6DFF',//操作
                c2: '#8A2FF8'
            },{
                c1: '#6392FA',  //管理
                c2: '#4179F4'
            },{
                c1: '#69CAFF',  //管理
                c2: '#36ACEB'
            },{
                c1: '#81F3EF',  //管理
                c2: '#5FCACE'
            }];
            var totalSeriesData = [{value: totalNum, name: '客户数量'}]
            this.accntNumberOption = echartInitConfig=>targetPieChartProgress(echartInitConfig,seriesData, totalSeriesData,['39%', '61%'],'39%',pieColor,225, 'value','','',5);
        },
        /**
         @desc: 企业信息数量饼图绘制
         @author: wangbinxin
         @date 2022-06-06 16-34
         **/
        async infoNumberPie(){
            await this.getInfoQty();
            this.countDataOption = null
            let seriesData = []
            let totalNum  = 0
            this.infoData.countVoList.forEach((item) => {
                if(item.val !== 'cancel'){
                    seriesData.push({
                        value: Number(item.num),
                        name: item.name
                    });
                    totalNum += Number(item.num);
                }
            });
            let pieColor = [];
            var totalSeriesData = [{value: totalNum, name: '已规划拜访\n企业数量'}]
            this.countDataOption = echartInitConfig=>targetPieChartProgress(echartInitConfig,seriesData, totalSeriesData,['39%', '61%'],'39%',pieColor,225, 'value','','',5, 'largeSize', 12);
        }
    }
}
</script>

<style lang="scss">
    .information-board{
        background: #fff;
        padding: 8px 24px 68px;
        .line-title{
            margin-bottom: 24px;
        }
        .link-dialog-body{
            position: relative;
        }
        .link-auto-list .link-auto-list-top-bar{
            border:none;
        }
        .link-item .link-item-body-right{
            margin: 0 24px;
        }
        .link-radio-group{
            width: 70px;
            .link-item{
                padding:24px 24px 24px 0;
                .link-item-thumb{
                    padding-right: 0;
                }
                .link-item-icon{
                    display:none;
                }
            }
            .link-item-active{
                background-color: #f6f6f6;
            }
        }
        .list-item{
            flex: 1;
        }
        .link-radio-group .link-item:active,.link-item-active{
            background-color: #f6f6f6;
        }
        .link-auto-list-no-more{
            display: none;
        }
        .link-dialog-foot-custom{
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
        }
        .line-title{
            margin-left: 32px;
        }
        .dialog-bottom{
            .dialog-content{
                padding: 0 20px;
                position: relative;

                .link-dialog-foot-custom{
                    width: auto !important;
                }
            }
            .model-title {
                display: flex;
                .title {
                    font-family: PingFangSC-Regular,serif;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    text-align: center;
                    line-height: 96px;
                    height: 96px;
                    width: 90%;
                    padding-left: 0!important;
                    margin-right: 80px;
                }
                .icon-left{
                    color: #BFBFBF;
                    font-size: 48px;
                    line-height: 96px;
                    height: 96px;
                }
                .icon-close {
                    color: #BFBFBF;
                    font-size: 48px;
                    line-height: 96px;
                    height: 96px;
                    margin-right: 30px;
                }
            }
        }
        .scroll-view-data{
            .select-dimension{
                display: flex;
                margin: 20px 0;
                .select-dimension-year{
                    position: relative;
                    .select-dimension-data{
                        position: absolute;
                        top: 0;
                        opacity: 0;
                    }
                }
            }
        }
        .count-data {
            display: flex;
            align-items: center;
            margin-top: 34px;
            border: 1px solid #EBEDF5;
            border-radius: 16px;
            flex-direction: column;
            justify-content: center;
            height: 246px;

            .count-data-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 100%;
            }

            .line {
                width: 2px;
                height: 60px;
                background-image: linear-gradient(180deg, rgba(191, 191, 191, 0.00) 0%, rgba(191, 191, 191, 0.50) 52%, rgba(191, 191, 191, 0.00) 100%);
            }

            .count-data-content {
                display: flex;
                width: 100%;
            }

            .count-data-list {
                flex: 1;
                display: flex;
                justify-content: center;
            }

            .count-data-head {
                text-align: center;
                margin-bottom: 36px;
            }

            .count-data-item-value {
                font-size: 32px;
                color: #262626;
                letter-spacing: 2px;
                line-height: 32px;
                font-weight: bold;
                margin-bottom: 16px;
            }

            .count-data-item-label {
                font-size: 24px;
                color: #262626;
                letter-spacing: 0;
                line-height: 24px;
            }
        }
        .accnt-number {
            margin: 0 0px 8px;

            .accnt-number-content {
                border: 2px solid #EBEDF5;
                border-radius: 16px;
                margin: 0 24px;
            }
        }
    }
</style>
