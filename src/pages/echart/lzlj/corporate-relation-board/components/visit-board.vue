<template>
    <link-page class="visit-board">
        <view class="visit-overview">
            <line-title title="企业拜访情况"></line-title>
            <scroll-view scroll-x="true" class="scroll-view-data">
                <view class="select-dimension" style="margin-bottom: 10px">
                    <select-button :label="infoParam.orgName?infoParam.orgName: '全部片区'"  :selected-flag="infoParam.orgName !== null " @tap="tapFilterOrganization('info')" downIcon></select-button>
                </view>
                <view class="select-dimension">
                    <view class="select-dimension-year">
                        <select-button :label="`${parseInt(monthVal)==(new Date().getMonth()+1)?'本':monthVal}月`" :selectedFlag="selectType==='month'" downIcon></select-button>
                        <view class="select-dimension-data"><link-date view="M" valueFormat="MM" displayFormat="MM月" @change="selectMonth"/></view>
                    </view>
                    <view class="select-dimension-year">
                        <select-button :selectedFlag="selectType==='quarter'" :label="quarterVal.name" downIcon></select-button>
                        <view class="select-dimension-data">
                            <picker @change="quarterChange" :value="quarterVal.val - 1" range-key="name" :range="quarterList">
                                <link-input type="text" :readonly="true"/>
                            </picker>
                        </view>
                    </view>
                    <select-button :selectedFlag="selectType==='time'" label="截止当前" @tap="infoVisitPie('time')"></select-button>
                </view>
            </scroll-view>
            <view class="count-data">
                <view class="count-data-title">
                    <view class="count-data-item">
                        <view class="count-data-item-value">{{visitData.visitCusNum}}</view>
                        <view class="count-data-item-label">拜访企业数量</view>
                    </view>
                    <view class="line"></view>
                    <view class="count-data-item">
                        <view class="count-data-item-value">{{visitData.visitTotalNum}}</view>
                        <view class="count-data-item-label">总拜访场次</view>
                    </view>
                </view>
                <view class="count-data-content">
                    <view class="count-data-list" v-for="(info, index) in visitData.countVoList" v-show="info.val !== 'cancel'">
                        <view class="count-data-item">
                            <view class="count-data-item-value">{{info.num}}</view>
                            <view class="count-data-item-label">{{info.name}}</view>
                        </view>
                        <view class="line" v-if="index < (visitData.countVoList.length-1)"></view>
                    </view>
                </view>
            </view>
            <view class="count-data-content" v-show="showEchart">
                <link-echart :option="visitDataOption" :height="dataOptionHeight + 'px'" :force-use-old-canvas="false"/>
            </view>
        </view>
        <view class="visit-coverage-terminal">
            <line-title title="企业拜访进度"></line-title>
            <scroll-view scroll-x="true" class="scroll-view-data">
                <view class="select-dimension" style="margin-bottom: 10px">
                    <select-button :label="accntParam.orgName?accntParam.orgName: '全部片区'" :selected-flag="accntParam.orgName !== null " @tap="tapFilterOrganization('accnt')" downIcon></select-button>
                    <view class="select-dimension-year">
                        <select-button :label="`${yearVal}年`" :selectedFlag="true" downIcon></select-button>
                        <view class="select-dimension-data"><link-date view="Y" valueFormat="YYYY" displayFormat="YYYY年" @change="selectYear"/></view>
                    </view>
                </view>
                <view class="select-dimension" style="margin-bottom: 10px">
                    <select-button label="公司级" :selected-flag="visitLevels.indexOf('company') > -1" @tap="changeLevels('company')"></select-button>
                    <select-button label="品牌级" :selected-flag="visitLevels.indexOf('brand') > -1" @tap="changeLevels('brand')"></select-button>
                    <select-button label="区域级" :selected-flag="visitLevels.indexOf('area') > -1" @tap="changeLevels('area')"></select-button>
                </view>
            </scroll-view>
            <view class="count-data">
                <view class="count-data-content">
                    <view class="count-data-list" v-for="(key, val, index) in procData" :key="index" v-show="val !== 'visitExRate'">
                        <view class="count-data-item">
                            <view class="count-data-item-value">{{key}}</view>
                            <view class="count-data-item-label">{{val}}</view>
                        </view>
                        <view class="line" v-if="index < (Object.keys(procData).length - 1)"></view>
                    </view>
                </view>
            </view>
            <view class="count-data-content" v-show="showEchart">
                <canvas id="myCanvas" :style="{width: '100%', height:(dataOptionHeight / 2 + 20)+'px'}" canvas-id="myCanvas" disable-scroll='true' :force-use-old-canvas="false" type="2d"></canvas>
            </view>
        </view>
        <link-dialog ref="positionBottom" position="bottom" height="85vh" class="dialog-bottom" noPadding v-model="dialogFlag" @hide="showEchart = true">
            <view class="model-title">
                <view class="iconfont icon-left"  v-if="!(autoList.list.length>0 && autoList.list[0].id === (isBrandCompany && !isPublicRela?defaultOrg.orgId:this.userInfo.orgId))" @tap="goBackOrg" style="width: 40px;color: #BFBFBF;font-size: 20px;line-height: 48px;height: 48px;"></view>
                <view class="title" style="padding-left:0;">组织片区</view>
                <view class="iconfont icon-close" @tap="closeDialog" style="margin-right: 15px;"></view>
            </view>
            <view class="dialog-content" style="height: calc(100% - 44px)">
                <scroll-view scroll-y="true" :style="{'height': 'calc(100% - 75px)'}">
                    <link-auto-list :option="autoList" hideCreateButton>
                        <template slot-scope="{data,index}">
                            <view slot="note">
                                <item  :key="index" :data=data @tap="gotoItemOrg(data)" style="padding-top: 0;padding-bottom:0">
                                    <link-radio-group v-model="tempOrgId">
                                        <item :arrow="false">
                                            <link-checkbox :val=data.id slot="thumb"  toggleOnClickItem @tap="tempOrgInfo(data)"/>
                                        </item>
                                    </link-radio-group>
                                    <view class="list-item">
                                        {{data.text}}
                                    </view>
                                </item>
                            </view>
                        </template>
                    </link-auto-list>
                </scroll-view>
                <view class="link-dialog-foot-custom">
                    <link-button shadow @tap="clickOrganization" label="确定" style="width:100vw"/>
                </view>
            </view>
        </link-dialog>
    </link-page>
</template>
<script>
import SelectButton from "../../components/select-button"
import LineTitle from "../../components/line-title";
import {targetPieChartProgress} from "../../echart.utils";
export default {
    name: "visit-board",
    components: {LineTitle, SelectButton},
    data(){
        let accessGroupOauth = this.$utils.getMenuAccessGroup('','/pages/echart/lzlj/corporate-relation-board/cpr-board-page');
        let isBrandCompany = false;
        let isPublicRela = false;
        let defaultOrg = {};
        let param = {                         //终端总览参数
            orgId: this.userInfo.orgId,
            parentOrgId: this.userInfo.orgId,
            orgName: this.userInfo.orgName,
            orgType: this.userInfo.orgType,
        };
        if(this.userInfo.coreOrganizationTile.brandCompanyCode === '6001'){
            if(this.userInfo.positionType === 'PublicRelations'){
                if(this.$utils.isEmpty(accessGroupOauth)){
                    accessGroupOauth = 'MY_ORG';
                }else{
                    isBrandCompany = true;
                    isPublicRela = true;
                }
            }else{
                isBrandCompany = true;
                for (let i = 1; i < 20; i++) {
                    if(!this.userInfo.coreOrganizationTile[`p${i}Id`]){
                        defaultOrg = {
                            orgId: this.userInfo.coreOrganizationTile[`p${i-1}Id`],
                            orgName: this.userInfo.coreOrganizationTile[`p${i-1}Name`]
                        };
                        param = {                         //终端总览参数
                            orgId: defaultOrg.orgId,
                            parentOrgId: '',
                            orgName: defaultOrg.orgName,
                            orgType: '',
                        };
                        break;
                    }
                }
            }
        }
        return{
            visitLevels: ['company', 'brand', 'area'],
            selectType: 'month',
            pixelRatio: this.$device.systemInfo.pixelRatio,
            dialogFlag: false,                                //组织弹框
            showEchart: true,
            quarterList: [
                {val: 1, name: '第一季度'},
                {val: 2, name: '第二季度'},
                {val: 3, name: '第三季度'},
                {val: 4, name: '第四季度'}
            ],
            quarterVal: {},
            monthVal: new Date().getMonth() >= 9? new Date().getMonth() + 1 : '0' + (new Date().getMonth() + 1),
            yearVal: new Date().getFullYear(),
            procData: {},
            visitData: {},
            visitDataOption: null,
            dataOptionHeight:  (this.$device.systemInfo.windowWidth - 24 ) * 0.727 < 254 ? (this.$device.systemInfo.windowWidth - 24 ) * 0.727 : 254,
            tempOrgId: null,                                  //选中的组织
            tempOrgName: null,                                //选中的组织
            autoList: new this.AutoList(this, {
                module: 'action/link/orgnization',
                url: {
                    queryByExamplePage: 'export/link/orgnization/queryByExamplePage',
                },
                searchFields: ['text'],
                loadOnStart: false,
                param: {
                    pageFlag: false,
                    filtersRaw: []
                },
                sortOptions: null,
                hooks: {
                    afterLoad(data) {
                        if(this.needNewRow){
                            data.rows = [{id: this.defaultOrg.orgId, text: this.defaultOrg.orgName}];
                        }
                        data.total = 1;
                    }
                }
            }),
            orgNow: 'info',
            infoOrgIdArr: [],                                     //企业可返回的组织数组
            accntOrgIdArr: [], // 客户可选组织数据
            isBrandCompany, // 当前职位所属品牌公司是否为会员管理部虚拟公司
            isPublicRela, //
            accessGroupOauth,//访问组安全性
            defaultOrg,
            needNewRow: false, // 是否需要创建数据（默认组织）
            infoParam:{...param},
            accntParam:{...param}
        }
    },
    props: {
        userInfo: {
            type: Object,
            default: function () {
                return {}
            }
        },

    },
    async created() {
        this.$utils.showLoading()
        var len = Math.ceil(parseInt(this.monthVal) / 3) - 1;
        this.quarterVal = this.quarterList[len];
        //allSettled兼容
        if (!Promise.allSettled) {
            const rejectHandler = reason => ({status: "rejected", reason})
            const resolveHandler = value => ({status: "fulfilled", value})
            Promise.allSettled = promises =>
                Promise.all(
                    promises.map((promise) =>
                        Promise.resolve(promise)
                            .then(resolveHandler, rejectHandler)
                    )
                );
        };
        Promise.allSettled([this.infoVisitPie('month'), this.progress()]).then((res) => {
            this.$utils.hideLoading()
        })
    },
    methods: {
        closeDialog(){
            this.dialogFlag = false;
            this.showEchart = true;
        },
        /**
         @param type:更新参数
         @desc: 更新visitLevels
         @author: wangbinxin
         @date 2022-06-07 20-23
         **/
        async changeLevels(type){
            var index = this.visitLevels.indexOf(type);
            if (index > -1) {
               this.visitLevels.splice(index, 1);
            }else{
                this.visitLevels.push(type);
            }
            await this.progress();
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/10/28
         * @methods tapFilterOrganization
         * @para
         * @description 点片区按钮、品牌公司按钮后：用上次选中参数查询弹框数据
         */
        tapFilterOrganization(type){
            let parentOrgId = null
            var orgIdArr = [];
            this.orgNow = type;
            if(type === 'accnt'){
                this.tempOrgId = this.accntParam.orgId;
                parentOrgId = this.accntParam.parentOrgId;
                orgIdArr = this.accntOrgIdArr;
            }else{
                this.tempOrgId = this.infoParam.orgId;
                parentOrgId = this.infoParam.parentOrgId;
                orgIdArr = this.infoOrgIdArr;
            }
            if(this.defaultOrg.orgId === this.tempOrgId)this.needNewRow=true;
            let filtersRaw = [];
            if(this.isPublicRela){
                filtersRaw = [
                    {"id": "isEffective", "property": "isEffective", "value": "Y"},
                ];
                this.autoList.option.param.oauth = this.accessGroupOauth;
            }else{
                if(orgIdArr.length === 0){
                    filtersRaw = [
                        {'id': 'id', 'property': 'id', 'value': this.tempOrgId, 'operator': '='},
                        {"id": "isEffective", "property": "isEffective", "value": "Y"},
                        {"id": "orgType", "property": "orgType", "value": this.userInfo.orgType}
                    ]
                    if(this.isBrandCompany && !this.isPublicRela){
                        filtersRaw.pop();
                    }
                }else {
                    filtersRaw = [
                        {'id': 'parentOrgId', 'property': 'parentOrgId', 'value': orgIdArr[orgIdArr.length - 1].orgId, 'operator': '='},
                        {"id": "isEffective", "property": "isEffective", "value": "Y"}
                    ]
                    if(orgIdArr[orgIdArr.length - 1].orgType === 'Company') {
                        filtersRaw.push({"id": "orgType", "property": "orgType", "value": 'BranchCompany'})
                    }
                }
            }
            this.autoList.option.param.filtersRaw = filtersRaw
            this.autoList.methods.reload()
            this.dialogFlag = true
            this.showEchart = false
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/10/28
         * @methods clickOrganization
         * @para
         * @description 片区弹框、品牌公司弹框确认按钮 (点确定之后的组织，再次打开弹框，会打开所在层级，只勾选没有点确认，则不会)
         */
        async clickOrganization(){
            if(this.orgNow === 'info'){
                await this.infoVisitPie(this.selectType);
            }else{
                await this.progress();
            }
            this.dialogFlag = false
            this.showEchart = true
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/11/9
         * @methods tempOrgInfo
         * @para
         * @description 存储选中的行信息
         */
        tempOrgInfo(data){
            let param = {
                orgId: data.id,
                parentOrgId: data.parentOrgId,
                orgName: data.text,
                orgType: data.orgType
            }
            this.orgNow === 'info'? this.infoParam = {...param} : this.accntParam = {...param};
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/11/9
         * @methods gotoItemOrg
         * @para
         * @description 跳转到子组织
         */
        gotoItemOrg(data){
            this.needNewRow = false;
            let filtersRaw = [
                {id: 'parentOrgId', property: 'parentOrgId', value: data.id, operator: '='},
                {"id": "isEffective", "property": "isEffective", "value": "Y"},
            ]
            if(data.orgType === 'Company') {
                filtersRaw.push({"id": "orgType", "property": "orgType", "value": 'BranchCompany'})
            }
            this.autoList.option.param.filtersRaw = filtersRaw;
            if(!this.$utils.isEmpty(this.autoList.option.param.oauth)){
                delete this.autoList.option.param.oauth;
            }
            this.autoList.methods.reload()
            if(this.orgNow === 'info'){
                this.infoOrgIdArr.push({orgId: data.id,orgName : data.text, orgType: data.orgType});
            }else{
                this.accntOrgIdArr.push({orgId: data.id,orgName : data.text, orgType: data.orgType})
            }
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/11/9
         * @methods goBackOrg
         * @para
         * @description 返回上一级组织列表
         */
        goBackOrg(){
            var orgIdArr = this.orgNow === 'info'?this.infoOrgIdArr:this.accntOrgIdArr;
            if(this.isPublicRela){
                let filtersRaw = [
                    {"id": "isEffective", "property": "isEffective", "value": "Y"},
                ]
                this.autoList.option.param.filtersRaw = filtersRaw
                this.autoList.option.param.oauth = this.accessGroupOauth
            }else{
                if(orgIdArr.length === 0) return;
                if(orgIdArr.length === 1){
                    orgIdArr.pop()
                    let filtersRaw = [
                        {'id': 'orgId', 'property': 'orgId', 'value': this.userInfo.orgId, 'operator': '='},
                        {"id": "isEffective", "property": "isEffective", "value": "Y"},
                        {"id": "orgType", "property": "orgType", "value": this.userInfo.orgType}
                    ]
                    if(this.isBrandCompany && !this.isPublicRela){
                        filtersRaw = [{'id': 'orgId', 'property': 'orgId', 'value': this.defaultOrg.orgId, 'operator': '='}, {"id": "isEffective", "property": "isEffective", "value": "Y"}];
                        this.needNewRow = true;
                    }
                    this.autoList.option.param.filtersRaw = filtersRaw
                    this.autoList.methods.reload()
                    return
                }
                orgIdArr.pop()
                let filtersRaw = [
                    {'id': 'parentOrgId', 'property': 'parentOrgId', 'value': orgIdArr[orgIdArr.length - 1].orgId, 'operator': '='},
                    {"id": "isEffective", "property": "isEffective", "value": "Y"}
                ]
                if(orgIdArr[orgIdArr.length - 1].orgType === 'Company') {
                    filtersRaw.push({"id": "orgType", "property": "orgType", "value": 'BranchCompany'})
                }
                this.autoList.option.param.filtersRaw = filtersRaw;
            }
            this.autoList.methods.reload();
        },
        /**
         @param{number}
         @desc: 选择年份
         @author: wangbinxin
         @date 2022-06-06 15-23
         **/
        async selectYear(val){
            this.yearVal = val;
            await this.progress();
        },
        async getSchedule(){
            this.$utils.showLoading();
            let params = {
                oauth: 'ALL',
                orgId: this.accntParam.orgId,
                visitLevels: this.visitLevels,
                year: this.yearVal
            };
            let data = await this.$http.post('export/link/companyVisit/queryCoVisitProgress', params);
            this.$utils.hideLoading();
            if(!data.success){
                this.$showError('获取企业拜访情况失败');
                return
            }
            var arr = [{name: '年度计划拜访企业', val: 'yearlyPlanVisitNum'}, {name: '已拜访企业', val: 'visitNum'}, {name: '未拜访企业', val: 'notVisitNum'}];
            arr.forEach((item) => {
                let num = data.result[item.val] || 0;
                delete data.result[item.val];
                data.result[item.name] = num;
            });
            this.procData = data.result;
        },
        async progress() {
            await this.getSchedule();
            wx.createSelectorQuery()
                .select('#myCanvas') // 在 WXML 中填入的 id
                .fields({node: true, size: true})
                .exec((res) => {
                    // Canvas 对象
                    const canvas = res[0].node
                    // Canvas 画布的实际绘制宽高
                    const renderWidth = res[0].width
                    const renderHeight = res[0].height
                    // Canvas 绘制上下文
                    const ctx = canvas.getContext('2d')
                    // 环形进度条变量
                    let outerRadius = 75 // 外环半径
                    let thickness = 15 // 圆环厚度
                    let x = renderWidth / 2  // 圆心x坐标
                    let y = this.dataOptionHeight / 2 - 20 // 圆心y坐标
                    let startAngle = -180 //开始角度
                    let endAngle = 0 //结束角度
                    let scro = parseInt(this.procData.visitExRate) * 1.8 - 180;
                    const rate = parseInt(this.procData.visitExRate) / 100
                    // 初始化画布大小
                    const dpr = wx.getSystemInfoSync().pixelRatio
                    canvas.width = renderWidth * dpr
                    canvas.height = renderHeight * dpr
                    ctx.scale(dpr, dpr)
                    //清除画布
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    // 画圆环
                    ctx.beginPath()
                    ctx.arc(x, y, outerRadius, this.angle2Radian(startAngle), this.angle2Radian(endAngle));
                    ctx.strokeStyle = '#eef4ff' // 弧线的颜色
                    ctx.lineWidth = thickness - 3// 弧的宽度
                    ctx.lineCap = "round" //线条结束端点样式 butt 平直 round 圆形 square 正方形
                    ctx.stroke()
                    if (parseInt(this.procData.visitExRate) !== 0) {
                        // 画进度条
                        ctx.beginPath()
                        ctx.arc(x, y, outerRadius, this.angle2Radian(startAngle), this.angle2Radian(scro))
                        //渐变色
                        let lingrad = ctx.createLinearGradient(x - outerRadius, y, (x + outerRadius) * rate, y - outerRadius);
                        // addColorStop 创建一个颜色的渐变点
                        lingrad.addColorStop(0, '#2F69F8');
                        lingrad.addColorStop(0.6, '#46C1F9');
                        lingrad.addColorStop(1, '#46E3F1');
                        ctx.strokeStyle = lingrad;
                        ctx.lineWidth = thickness
                        ctx.lineCap = "round";
                        ctx.stroke()
                    }
                    ctx.fontSize = 16
                    ctx.fillStyle = '#2F69F8' // 文字的颜色
                    ctx.font = 'normal 500 22px sans-serif';
                    ctx.textAlign = "center";  // 字体位置
                    ctx.fillText(this.procData.visitExRate, x, y - 19)
                    ctx.fontSize = 14
                    ctx.font = 'normal 500 14px sans-serif';
                    ctx.fillStyle = '#333333'
                    ctx.fillText('拜访执行率', x, y + 9.5)
                    ctx.textAlign = "center";  // 字体位置
                })
        },
        //角度转弧度函数
        angle2Radian(angle) {
            return angle * Math.PI / 180
        },
        async quarterChange(e){
            this.quarterVal = this.quarterList[parseInt(e.detail.value)];
            await this.infoVisitPie('quarter');
        },
        /**
         @desc: 选取月份后重新加载饼图
         @author: wangbinxin
         @date 2022-06-06 20-47
         **/
        async selectMonth(val){
            this.monthVal = val;
            await this.infoVisitPie('month');
        },
        /**
         @desc: 企业信息数量饼图绘制
         @author: wangbinxin
         @date 2022-06-06 16-34
         **/
        async infoVisitPie(type){
            this.selectType = type;
            this.$utils.showLoading();
            var params = {
                oauth: 'ALL',
                orgId: this.infoParam.orgId
            }
            if(type === 'month'){
                params.month = this.monthVal;
            }else if(type === 'quarter'){
                params.quarter = this.quarterVal.val;
            }else{
                params.isEnd = true;
            }
            let data = await this.$http.post('export/link/companyVisit/queryCoVisitCountByExample', params);
            this.$utils.hideLoading();
            if(!data.success){
                this.$showError('获取企业拜访情况失败');
                return
            }
            this.visitData = this.$utils.deepcopy(data.result);
            this.visitDataOption = null
            let seriesData = []
            let totalNum  = 0
            data.result.countVoList.forEach((item) => {
                if(item.val !== 'cancel'){
                    seriesData.push({
                        value: Number(item.num),
                        name: item.name
                    });
                    totalNum += Number(item.num);
                }
            });
            let pieColor = [];
            var totalSeriesData = [{value: totalNum, name: '拜访场次'}]
            this.visitDataOption = echartInitConfig=>targetPieChartProgress(echartInitConfig,seriesData, totalSeriesData,['39%', '61%'],'39%',pieColor,225, 'value','','',5);
        }
    }
}
</script>
<style lang="scss">
.visit-board{
    background: #fff;
    padding: 8px 24px 68px;
    .line-title{
        margin-left: 32px;
        margin-bottom: 24px;
    }
    .link-dialog-body{
        position: relative;
    }
    .link-auto-list .link-auto-list-top-bar{
        border:none;
    }
    .link-item .link-item-body-right{
        margin: 0 24px;
    }
    .link-radio-group{
        width: 70px;
        .link-item{
            padding:24px 24px 24px 0;
            .link-item-thumb{
                padding-right: 0;
            }
            .link-item-icon{
                display:none;
            }
        }
        .link-item-active{
            background-color: #f6f6f6;
        }
    }
    .list-item{
        flex: 1;
    }
    .link-radio-group .link-item:active,.link-item-active{
        background-color: #f6f6f6;
    }
    .link-auto-list-no-more{
        display: none;
    }
    .link-dialog-foot-custom{
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
    }
    .dialog-content{
        padding: 0 20px;
        position: relative;

        .link-dialog-foot-custom{
            width: auto !important;
        }
    }
    .model-title {
        display: flex;
        .title {
            font-family: PingFangSC-Regular,serif;
            font-size: 32px;
            color: #262626;
            letter-spacing: 0;
            text-align: center;
            line-height: 96px;
            height: 96px;
            width: 90%;
            padding-left: 0!important;
            margin-right: 80px;
        }
        .icon-left{
            color: #BFBFBF;
            font-size: 48px;
            line-height: 96px;
            height: 96px;
        }
        .icon-close {
            color: #BFBFBF;
            font-size: 48px;
            line-height: 96px;
            height: 96px;
            margin-right: 30px;
        }
    }
    .scroll-view-data{
        margin-top: 24px;
        margin-bottom: 24px;
        .select-dimension{
            display: flex;
            margin-left: 24px;
            .select-dimension-year{
                position: relative;
                .select-dimension-data{
                    position: absolute;
                    top: 0;
                    opacity: 0;
                }
            }
        }
    }
    .visit-overview, .visit-coverage-terminal{
        margin-bottom: 8px;
        .count-data {
            display: flex;
            align-items: center;
            margin-top: 34px;
            border: 1px solid #EBEDF5;
            border-radius: 16px;
            flex-direction: column;
            justify-content: center;
            height: 246px;

            .count-data-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 100%;
            }

            .line {
                width: 2px;
                height: 60px;
                background-image: linear-gradient(180deg, rgba(191, 191, 191, 0.00) 0%, rgba(191, 191, 191, 0.50) 52%, rgba(191, 191, 191, 0.00) 100%);
            }

            .count-data-content {
                display: flex;
                width: 100%;
            }

            .count-data-list {
                flex: 1;
                display: flex;
                justify-content: center;
            }
            .count-data-title{
                display: flex;
                width: 100%;
                margin-bottom: 40px;
                font-size: 14px;
                color: #333333;
                text-align: center;
                line-height: 14px;
                font-weight: 500;
            }

            .count-data-item-value {
                font-size: 32px;
                color: #262626;
                letter-spacing: 2px;
                line-height: 32px;
                font-weight: bold;
                margin-bottom: 16px;
            }

            .count-data-item-label {
                font-size: 24px;
                color: #262626;
                letter-spacing: 0;
                line-height: 24px;
            }
        }
    }
    .count-data-content {
        display: flex;
        width: 100%;
    }

}
</style>
