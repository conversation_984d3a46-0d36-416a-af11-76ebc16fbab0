<template>
  <link-page class="cpr-board-page">
    <lnk-taps :taps="dataBoardOption" v-model="dataBoardActive" ></lnk-taps>
    <!--企业数据统计-->
    <view v-if="dataBoardActive.val === 'information'">
      <information-board ref="quota"  :userInfo="userInfo" ></information-board>
    </view>
    <!--企业拜访情况-->
    <view v-if="dataBoardActive.val === 'visit'">
      <visit-board ref="stock" :userInfo="userInfo" ></visit-board>
    </view>
  </link-page>
</template>

<script>
  import LnkTaps from "../../../core/lnk-taps/lnk-taps";
  import informationBoard from "./components/information-board";
  import visitBoard from "./components/visit-board";
  import Taro from "@tarojs/taro";
  export default {
    name: "data-board-page",
    components: {informationBoard, visitBoard, LnkTaps},
    data () {
      return {
        dataBoardActive: {},
        dataBoardOption: [
          {name: '企业数据统计', seq: '1', val: 'information'},
          {name: '企业拜访情况', seq: '2', val: 'visit'},
        ],
        userInfo: {}
      }
    },
    async created () {
      this.dataBoardActive = this.dataBoardOption[0]
      this.userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
    },
    methods: {
      /**
       *  @description: 切换tabs
       *  @author: 马晓娟
       *  @date: 2020/9/3 18:06
       */
      changeTab(val, key) {
        this.dataBoardActive = val;
      },
    }
  }
</script>

<style lang="scss">
  .cpr-board-page{
      background: #FFFFFF;
      .lnk-tabs{
          border-bottom: 1px solid #f2f2f2;
          position: relative;
      }
  }
</style>
