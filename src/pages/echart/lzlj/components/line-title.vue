<template>
  <view class="report-line-title">
    <view class="line">
      <view class="line-top"></view>
      <view class="line-bottom"></view>
    </view>
    <view class="stair-title">
        <view> {{title}}</view>
        <view>{{line1}}</view>
        <view>{{line2}}</view>
    </view>
    <view class="edit" @tap="tap" v-if="buttonName">{{buttonName}}</view>
    <view class="filter-dropdown-item" @tap="chooseData" v-if="filterName">{{filterName}}<link-icon icon="mp-desc"/></view>
  </view>
</template>

<script>
  export default {
    name: "line-title",
    props: {
      title: {
        type: String,
        default: ''
      },
      line1: {
            type: String,
            default: ''
        },
        line2: {
            type: String,
            default: ''
        },
      buttonName: {
        type: String,
        default: ''
      },
      filterName: {
          type: String,
          default: ''
      }
    },
    methods: {
      /**
       *  @description: 点击方法
       *  @author: 马晓娟
       *  @date: 2020/9/8 16:10
       */
      tap () {
        this.$emit('tap')
      },
        /**
         * @desc 下拉框选择数据
         * <AUTHOR>
         * @date 2023/7/4 12:09
         **/
        chooseData () {
            this.$emit('chooseData')
        }
    }

  }
</script>

<style lang="scss">
  .report-line-title{
    padding-top: 24px;
    @include flex-start-center;

    .line {
      .line-top {
        width: 8px;
        height: 16px;
        background: #3FE0E2;
      }

      .line-bottom {
        width: 8px;
        height: 16px;
        background: #2F69F8;
      }
    }

    .stair-title {
      margin-left: 16px;
      font-family: PingFangSC-Semibold, serif;
      font-size: 32px;
      color: #262626;
      letter-spacing: 1px;
      line-height: 32px;
    }
    .edit {
      font-family: PingFangSC-Regular;
      font-size: 28px;
      color: #2F69F8;
      letter-spacing: 0;
      line-height: 28px;
      text-align: right;
      width: 72%;
    }
      .filter-dropdown-item {
          width: 44%;
          height: 40px;
          margin: 12px 4px;
          font-size: 28px;
          color: #333333;
          line-height: 40px;
          font-weight: 400;
          text-align: center;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          .link-icon {
              width: 16px;
              height: 12px;
              color: #CCCCCC;
              margin-left: 8px;
          }
      }
  }
</style>
