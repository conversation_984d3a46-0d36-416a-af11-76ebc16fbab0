<!--
@description 消费者看板组织选择-组件
<AUTHOR>
@date 2024-06-18
@file consumer-company-select.vue
-->
<template>
    <view class="consumer-company-select">
        <link-dialog ref="positionBottom" position="bottom" height="85vh" class="dialog-bottom" noPadding v-model="show"
                     @hide="dialogHide">
            <view class="model-title">
                <view class="title" style="padding-left:0;">组织片区</view>
                <view class="iconfont icon-close" @tap="dialogHide" style="margin-right: 15px;"></view>
            </view>
            <view class="dialog-content" style="height: calc(100% - 44px)">
                <scroll-view scroll-y="true" :style="{'height': 'calc(100% - 75px)'}">
                    <link-auto-list :option="autoList" hideCreateButton>
                        <template slot-scope="{data,index}">
                            <view slot="note">
                                <item :key="index" :data=data style="padding-top: 0;padding-bottom:0" :arrow="false">
                                    <link-radio-group v-model="tempOrgId">
                                        <item :arrow="false">
                                            <link-checkbox :val=data.id slot="thumb" toggleOnClickItem
                                                           @tap="tempOrgInfo(data)"/>
                                        </item>
                                    </link-radio-group>
                                    <view class="list-item">
                                        {{ data.orgName }}
                                    </view>
                                </item>
                            </view>
                        </template>
                    </link-auto-list>
                </scroll-view>
                <view class="link-dialog-foot-custom">
                    <link-button shadow @tap="chooseOrg" label="确定" style="width:100vw"/>
                </view>
            </view>
        </link-dialog>
    </view>
</template>

<script>
export default {
    name: "consumer-company-select",
    props: {
        userInfo: {},
        show: false,
        companyId: {
            type: String,
            default: "",
        }
    },
    data() {
        const accessGroupOauth = this.$utils.getMenuAccessGroup('', '');
        const autoList = new this.AutoList(this, {
            url: {
                queryByExamplePage: this.$env.appURL + '/action/link/orgnization/queryAreaRegionPage'
            },
            searchFields: ['orgName', 'id', 'orgCode', 'companyId'],
            loadOnStart: false,
            param: {
                orgType: 'BranchCompany',
                oauth: 'MY_ORG',
                pageFlag: true,
                filtersRaw: []
            },
            pageSize: 20,
            request: ({url, param}) => {
                param.filtersRaw = JSON.stringify(param.filtersRaw)
                const data = this.$httpForm.post(url, param)
                return data;
            },
            sortOptions: null
        });
        return {
            searchFlag: true,
            tempOrgId: this.companyId, //选中的组织
            accessGroupOauth,
            autoList,
            selectData: {},
            orgIdArr: [], //可返回的组织数组
        }
    },
    watch: {
        async show(val) {
            this.autoList.option.searchText = "";
            if (val) {
                let filtersRaw = [{id: 'isEffective', property: 'isEffective', value: 'Y'}];
                if (this.orgIdArr.length === 0) {
                    this.orgIdArr.push({
                        companyId: this.userInfo.companyId,
                        orgName: this.userInfo.orgName,
                        orgType: this.userInfo.orgType
                    });
                }
                await this.search(filtersRaw);
            } else {
                this.autoList.list = [];
            }
        }
    },
    methods: {
        /**
         * @createdBy 曾宇
         * @date 2023/1/16
         * @methods: search
         * @description: 关键字搜索
         **/
        async search(filtersRaw) {
            this.autoList.option.param.filtersRaw = filtersRaw;
            await this.autoList.methods.reload();
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/11/9
         * @methods tempOrgInfo
         * @para
         * @description 存储选中的行信息
         */
        tempOrgInfo(data) {
            this.selectData = data;
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/10/28
         * @methods chooseOrg
         * @para
         * @description 片区弹框、品牌公司弹框确认按钮 (点确定之后的组织，再次打开弹框，会打开所在层级，只勾选没有点确认，则不会)
         */
        chooseOrg() {
            this.$emit('choose', this.selectData);
            this.$emit('update:show', false);
        },
        dialogHide() {
            this.$emit('update:show', false);
        }
    }
}
</script>

<style lang="scss">
.consumer-company-select {
    .link-dialog-foot-custom {
        width: auto !important;
    }

    .link-dialog-body {
        position: relative;
    }

    .link-auto-list .link-auto-list-top-bar {
        border: none;

        .link-search-input {
            padding: 24px 24px 24px 0;
        }
    }

    .link-item .link-item-body-right {
        margin: 0 24px;
    }

    .link-radio-group {
        width: 70px;

        .link-item {
            padding: 24px 24px 24px 0;

            .link-item-thumb {
                padding-right: 0;
            }

            .link-item-icon {
                display: none;
            }
        }

        .link-item-active {
            background-color: #f6f6f6;
        }
    }

    .list-item {
        flex: 1;
    }

    .link-radio-group .link-item:active, .link-item-active {
        background-color: #f6f6f6;
    }

    .link-auto-list-no-more {
        display: none;
    }

    .dialog-bottom {
        .dialog-content {
            padding: 0 20px;
            position: relative;
            //.link-button{
            //  position: absolute;
            //  bottom: 0
            //}
        }

        .model-title {
            display: flex;

            .title {
                font-family: PingFangSC-Regular, serif;
                font-size: 32px;
                color: #262626;
                letter-spacing: 0;
                text-align: center;
                line-height: 96px;
                height: 96px;
                width: 90%;
                padding-left: 0 !important;
                //margin-right: 80px;
            }

            .icon-left {
                color: #BFBFBF;
                font-size: 48px;
                line-height: 96px;
                height: 96px;
            }

            .icon-close {
                color: #BFBFBF;
                font-size: 48px;
                line-height: 96px;
                height: 96px;
                margin-right: 30px;
            }
        }
    }
}
</style>
