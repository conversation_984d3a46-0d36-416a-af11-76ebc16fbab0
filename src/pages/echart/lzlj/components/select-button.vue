<template>
  <view class="select-button" @tap="tap" :style="styleData">
    <view :class="downIcon? 'icon-text': 'text'">{{nameFormat}}</view>
    <link-icon icon="icon-down1" v-if="downIcon"></link-icon>
  </view>
</template>

<script>
  import {ComponentUtils} from "link-taro-component";
  export default {
    name: "select-button",
    props: {
      selectedFlag: {
        type: Boolean,
        default: false
      },
      label: {
        type: String,
        default: ''
      },
      value: {
        type: String,
        default: ''
      },
      downIcon: {
        type: Boolean,
        default: false
      },
      isFormatName: {
        type: Boolean,
        default: true
      },
      isLastDay: {      // 判断是获取当前区间最后一天的时间，还是去次月1日的00:00:00
        type: Boolean,
        default: false
      },
      module: { // 模块，用来区分市场活动和其他，如果是市场活动，报表的财年是以11月为分界。
        type: String,
        default: ''
      },
      isBoard: {      // 消费者看板
         type: Boolean,
         default: false
      },
      showLength: {
          type: Number,
          default: 8
      }
    },
    data() {
      return {
        dateVal: {}
      }
    },
    computed: {
      styleData() {
        if (this.selectedFlag && !this.isBoard) {
          return 'background: #EDF3FF;color: #2F69F8;border: 1px solid #EDF3FF;';
        } else if(this.selectedFlag && this.isBoard) {
            return 'background: #2F69F8;color: #ffffff';
        } else if(!this.selectedFlag && this.isBoard){
            return 'color: #666666';
        }
        return 'border: 1px solid #E0E4EC;color: #8C8C8C;';
      },
      nameFormat: {
        get() {
          let nameTemp = this.label;
          if (this.label.length > this.showLength && this.isFormatName) {
            nameTemp = this.label.substring(0,this.showLength) + '…';
          }
          return nameTemp;
        },
        set(value) {
          this.nameFormat = value;
        }
      },
    },
    mounted () {
      if (this.value === 'week') {
        this.dateVal = this.$utils.getCurrentWeekDate();
      } else if (this.value === 'month') {
        this.dateVal = this.$utils.getCurrentMonthDate(this.isLastDay);
      } else if (this.value === 'quarter') {
        this.dateVal = this.$utils.getQuarterMonth(this.isLastDay);
      } else if (this.value === 'year') {
        if (this.module === 'activityData'){
          this.dateVal = this.$utils.getCurrentYearDateNew();
        } else {
          this.dateVal = this.$utils.getCurrentYearDate();
        }
      }
    },
    methods: {
      /**
       *  @description: 点击事件
       *  @author: 马晓娟
       *  @date: 2020/10/28 11:01
       */
      tap: ComponentUtils.debounce(async function() {
          if (this.value === 'week' || this.value === 'month' || this.value === 'quarter' || this.value === 'year') {
            this.$emit('tap', this.dateVal);
          } else {
            this.$emit('tap');
          }
      }, 500)
    }
  }
</script>

<style lang="scss">
  .select-button {
    border-radius: 26px;
    height: 26px;
    line-height: 26px;
    font-size: 28px;
    text-align: center;
    margin-right: 24px;
    padding: 12px;
    display: flex;
    align-items: center;
    .text{
      min-width: 120px;
      text-align: center;
    }
    .icon-text{
      min-width: 110px;
      text-align: left;
      margin-left: 12px;
      white-space: nowrap;
    }
    .link-icon{
      font-size: 18px;
      width: 10px;
      color: #bfbfbf;
      margin-right: 4px;
      margin-left: 14px;
    }
  }
</style>
