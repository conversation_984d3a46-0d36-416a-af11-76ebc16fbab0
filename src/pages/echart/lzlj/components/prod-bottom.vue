<!--
 看板-终端-品项筛选
@author:谭少奇
@date 2323/11/30
 -->
<template>
  <view class="prod-bottom">
      <link-dialog ref="prodBottom" position="bottom" height="85vh" class="dialog-bottom" noPadding v-model="showProd" @hide="dialogHide">
          <view class="model-title">
              <view class="iconfont icon-close" @tap="dialogHide"></view>
              <view class="title">分品项</view>

          </view>
          <view class="dialog-content" style="height: calc(100% - 44px)">
              <scroll-view scroll-y="true" :style="{'height': 'calc(100% - 75px)'}">
                  <link-auto-list :option="autoList" hideCreateButton :scrollContent="true">
                      <template slot-scope="{data,index}">
                          <view slot="note">
                              <item :key="index" :data="data" :arrow="false" style="padding-top: 0;padding-bottom:0">
                                  <link-radio-group v-model="tempProdId">
                                      <item :arrow="false">
                                          <link-checkbox :val=data.id slot="thumb" toggleOnClickItem @tap="tempProdInfo(data)"/>
                                      </item>
                                  </link-radio-group>
                                  <view class="list-item">
                                      {{data.mClassCode | lov('PROD_BUS_M_CLASS')}}
                                  </view>
                              </item>
                          </view>
                      </template>
                  </link-auto-list>
              </scroll-view>
              <view class="link-dialog-foot-custom">
                  <link-button shadow @tap="chooseOrg" label="确定" style="width:100vw"/>
              </view>
          </view>
      </link-dialog>
  </view>
</template>

<script>
  export default {
    name: "prod-bottom",
    props: {
      userInfo: {},
      showProd: false,
      orgId:''
    },
    data() {
        const accessGroupOauth = this.$utils.getMenuAccessGroup('','');
        let autoList = new this.AutoList(this, {
          module: 'action/link/mvg',
            url: {
                queryByExamplePage: 'action/link/mvg/queryRightListPage',
            },
            searchFields: ['text'],
          loadOnStart: false,
          param: {
              mvgMapperName: 'userBusClass',
              mvgAttr6: 'groupByMClass',
              oauth: 'ALL',
              sort: '',
              order: ''
          },
          searchFields: ['mClassCode'],
          hooks: {
              beforeLoad (option) {
                  option.param.mvgAttr7 = this.orgId
                  option.param.mvgAttr2 =  this.userInfo.id
                  option.param.mvgParentId = this.userInfo.id
              }
          },
          sortOptions: null
        });
        return {
            searchFlag: true,
            tempProdId: null, //选中的组织
            accessGroupOauth,
            autoList,
            selectData: {},
            orgIdArr: [], //可返回的组织数组
        }
    },
    watch: {
      async showProd(val){
        this.autoList.option.searchText = "";
        if(val){
          await this.search();
        }else {
          this.autoList.list = [];
        }
      }
    },
    methods: {
        async search(){
            await this.autoList.methods.reload();
        },
        /**
         *  选中的分品项
         * @createdBy  谭少奇
         * @date  2023/11/30
         * @param Object data
         */
        tempProdInfo(data){
            this.selectData = data;
        },
        /**
         * 大成等特殊公司分品项确认
         * @created  谭少奇
         * @date  2023/11/30
         */
        chooseOrg(){
            this.$emit('choose', this.selectData);
            this.$emit('update:showProd', false);
        },
        dialogHide(){
            this.$emit('update:showProd', false);
        }
    }

  }
</script>

<style lang="scss">
.prod-bottom{
    .link-dialog-foot-custom{
        width: auto !important;
    }
  .link-dialog-body{
    position: relative;
  }
  .link-auto-list .link-auto-list-top-bar{
    border:none;
  }
  .link-item .link-item-body-right{
    margin: 0 24px;
  }
  .link-radio-group{
    width: 70px;
    .link-item{
      padding:24px 24px 24px 0;
      .link-item-thumb{
        padding-right: 0;
      }
      .link-item-icon{
        display:none;
      }
    }
    .link-item-active{
      background-color: #f6f6f6;
    }
  }
  .list-item{
    flex: 1;
  }
  .link-radio-group .link-item:active,.link-item-active{
    background-color: #f6f6f6;
  }
  .link-auto-list-no-more{
    display: none;
  }
  .dialog-bottom{
    .dialog-content{
      padding: 0 20px;
      position: relative;
      //.link-button{
      //  position: absolute;
      //  bottom: 0
      //}
    }
    .model-title {
      .title {
        font-family: PingFangSC-Regular,serif;
        font-size: 32px;
        color: #262626;
        letter-spacing: 0;
        text-align: center;
        line-height: 96px;
        height: 96px;
        width: 90%;
        padding-left: 0!important;
        margin-right: 80px;
        margin-left: 10vw;
      }
      .icon-close {
        color: #BFBFBF;
        font-size: 48px;
        line-height: 96px;
        height: 96px;
        margin-right: 30px;
        float:right
      }
    }
  }
}
</style>
