# 品鉴酒扫码看板


------
* 初始文档
```
创建时间：2022年2月14日
创建人：  吕志平
```
* 模块介绍
>  默认展示当前登录职位所属组织的所有扫码记录统计.如果职位类型符合要求则展示选择组织筛选框.
>

* 涉及对象
> * 市场活动-本系统


* 是否共用
> 否


* 数据存储
> * 数据来源 本系统数据库
> * 存储方式 本系统数据库
> * 是否同步 否

* 缓存机制
> * 是否缓存 否

* 安全性
> *  (1).如果职位类型为A，默认为当前登录人的职位，隐掉“选择组织”按钮
> *  (2).如果职位类型为B，默认为当前登录人所属组织，可选择组织及以下组织进行筛选

> *   A、当前登录人职位类型=业务代表Salesman、业务主管SalesSupervisor、团购经理GroupBuyManager、客户经理AccountManager、客服经理CustServiceManager、VIP经理VipManager、客服专员CustServiceSpecialist、客服主管CustServiceSupervisor、小组组长SalesTeamLeader、渠道经理SalesChannelManger时，用当前登录人职位，与活动-内部人员表里面的数据匹配，查询该职位及下级职位的活动数据关联的扫码记录数据展示；

> *   B、当前登录人职位类型=会战指挥长BattleCommander、品牌销管部部长BPSalesManager、总部内勤HeadquartersStuff、大区内勤RInternalStaff、渠道管理部部长CMDeptDirector、渠道主管ChannelSupervisor、会员管理部经理MemberManager、品牌联络部主管BLRegionManager、品牌推广部主管BPRegionManager、品牌推广部部长BPDeptManager、销售公司总经理SalesGeneralManager、品牌公司总经理BrandManager、片区内勤InternalStaff、系统管理员SysAdmin、城市经理SalesManager，SalesCityManager、片区经理SalesAreaManager、大区经理SalesRegionManager、战区经理、股份公司总经理GeneralManager、城市内勤CInternalStaff、品牌公司信息管理员BrandSysAdmin、片区信息专员RegionSysAdmin时，按照当前登录人职位的组织，与活动头上的orgid匹配查看组织及下级组织活动数据关联的扫码记录数据展示；


* 状态流转
> * 无


* 涉及组件
> * scroll-view
> * AutoList
> * link-page
> * link-echart
> * link-dialog


## 模块实现
###涉及页面
#### 一 扫码看板
#####  1、vue页面路径
> * 1、页面完整路径
>
>  src/pages/echart/lzlj/scan_code_board/scan-code-board-page.vue

##### 2、页面实现功能
> * (1).可以选择时间维度来展示数据.(本周,本月,本季度,本财年-每年的11月1号-次年的10月31号,日历年（1月1日 - 12月31日）)
> * (2).也可以选择扫码类型展示数据(开瓶扫码,出库扫码,入库扫码.vip赠酒,手动编辑核销)
> * (3).当职位类型为B会显示组织筛选,可以选中当前登录职位的当前及下级组织进行查看,支持组织名称搜索


## 配置页面
> * 无


------ 品鉴酒扫码看板模块内容结束 ------


