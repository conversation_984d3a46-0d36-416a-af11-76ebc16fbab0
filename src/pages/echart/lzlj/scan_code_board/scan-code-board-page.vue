<template>
    <link-page :class=" this.OuathFlag ? 'scan-code-board-org-page':'scan-code-board-noOrg-page'">
        <scroll-view scroll-x="true" class="scroll-view-data"  v-if="OuathFlag" style="padding: 12rpx 0 24rpx; ">
            <view class="select-dimension">
                <select-button :label="orgText" :selected-flag="true" @tap="dialogFlag=true;showEchart=false;" downIcon></select-button>
            </view>
        </scroll-view>
        <lnk-taps :taps="dataBoardOption" v-model="dataBoardActive" ></lnk-taps>
        <view>
            <line-title title="品鉴酒扫码总览"></line-title>
            <view class="overview">
                <view class="panelOne">
                    <view style="padding-bottom: 40rpx"></view>
                    <view class="chartOne" v-if="!$utils.isEmpty(countScanOption)">
                        <view class="pcNum"><text>{{$utils.isEmpty(this.chartOneData) ? 0 : (this.chartOneData.actualOutBottleSum ? this.chartOneData.actualOutBottleSum:0)}}</text></view>
                        <view class="pcText"><text>实际出库扫码总瓶数</text></view>
                    </view>
                    <view class="chartOneText">
                        <view class="chartOneTextItem">
                            <view class="dot"></view>
                            <view style="margin-right: 12rpx;">出库扫码瓶数</view>
                            <text style="font-weight: bold; color: #333333;">{{$utils.isEmpty(this.chartOneData) ? 0 : (this.chartOneData.outBottleSum ? this.chartOneData.outBottleSum:0)}}瓶</text>
                        </view>
                        <view class="chartOneTextItem">
                            <view class="dot"></view>
                            <view style="margin-right: 12rpx;">入库扫码瓶数</view>
                            <text style="font-weight: bold; color: #333333;">{{$utils.isEmpty(this.chartOneData) ? 0 : (this.chartOneData.inBottleSum ? this.chartOneData.inBottleSum:0)}}瓶</text>
                        </view>
                    </view>
                </view>
                <view class="panelOneBorder"></view>
                <view>
                    <view>
                        <link-echart v-if="showEchart" :option="countScanOption" />
                    </view>
                </view>
            </view>
        </view>
        <view>
            <line-title title="各活动类型扫码情况"></line-title>
            <view class="overview" style="margin-top: 20rpx;padding-bottom: 20rpx">
                <view  class="scroll-data" style="padding-top: 20rpx" >
                    <view class="select-dimension">
                        <select-button label="开瓶扫码" :selected-flag="scanTypeActive.value==='OpenScan'"  @tap="queryActScanDataByType('OpenScan')"></select-button>
                        <select-button label="出库扫码" :selected-flag="scanTypeActive.value==='OutScan'"  @tap="queryActScanDataByType('OutScan')"></select-button>
                        <select-button label="入库扫码" :selected-flag="scanTypeActive.value==='InScan'"  @tap="queryActScanDataByType('InScan')"></select-button>
                    </view>
                    <view class="select-dimension" style="padding-top: 20rpx">
                        <select-button label="VIP赠酒扫码" :selected-flag="scanTypeActive.value==='GiftScan'"  @tap="queryActScanDataByType('GiftScan')"></select-button>
                        <select-button label="手动编辑核销" :selected-flag="scanTypeActive.value==='verifi'"  @tap="queryActScanDataByType('verifi')"></select-button>
                    </view>
                </view>
                <view>
                    <link-echart v-if="!$utils.isEmpty(actScanOption)&&showEchart" :option="actScanOption" :height=" actScanBarHeight + 'px' "/>
                    <view v-else style="text-align: center;margin-top: 30rpx;font-size: 28rpx;color: #2F69F8;">暂无数据</view>
                </view>
            </view>
        </view>
        <link-dialog ref="positionBottom" position="bottom" height="90vh"  noPadding v-model="dialogFlag" >
            <view class="model-title">
<!--      v-if="!(autoList.list.length>0 && autoList.list[0].id === this.userInfo.orgId)"          -->
                <view class="iconfont icon-left"   @tap="goBackOrg"  style="width: 40px;color: #BFBFBF;font-size: 20px;line-height: 48px;height: 48px;"></view>
                <view class="title" style="padding-left:0;">组织片区</view>
                <view class="iconfont icon-close" @tap="dialogFlag = false;showEchart=true;" style="margin-right: 15px;"></view>
            </view>
            <view class="dialog-content" style="height: calc(100% - 44px)">
                <scroll-view scroll-y="true" :style="{'height': 'calc(100% - 75px)'}">
                    <link-auto-list :option="autoList" hideCreateButton>
                        <template slot-scope="{data,index}">
                            <view slot="note">
<!--                                -->
                                <item  :key="index" :data=data @tap="gotoItemOrg(data)" style="padding-top: 0;padding-bottom:0">
                                    <link-radio-group v-model="tempOrgId" >
                                        <item :arrow="false"  >
<!--             @tap="tempOrgInfo(data)"                              -->
                                            <link-checkbox :val=data.id slot="thumb" @tap="tempOrgInfo(data)"  toggleOnClickItem />
                                        </item>
                                    </link-radio-group>
                                    <view style="flex: 1;">
                                        {{data.text}}
                                    </view>
                                </item>
                            </view>
                        </template>
                    </link-auto-list>
                </scroll-view>
                <view class="link-dialog-foot-custom">
<!--        @tap="clickOrganization"            -->
                    <link-button shadow @tap="clickOrganization" label="确定" style="width:100vw"/>
                </view>
            </view>
        </link-dialog>
    </link-page>
</template>

<script>

import LnkTaps from "../../../core/lnk-taps/lnk-taps";
import Taro from "@tarojs/taro";
import SelectButton from "../components/select-button";
import LineTitle from "../../../lzlj/components/line-title";
import {barYCategory, targetPieChartProgress} from "../echart.utils";

export default {
    name: "scan_code_board-page",
    components: {LnkTaps,SelectButton,LineTitle},
    data(){
        let OuathFlag = false;
        let orgId='';
        OuathFlag = !this.Oauth();
        orgId = Taro.getStorageSync('token').result.orgId;
        return{
            autoList: new this.AutoList(this, {
                module: 'action/link/orgnization',
                searchFields: ['text'],
                loadOnStart: false,
                param: {
                    oauth: 'MY_ORG',
                    filtersRaw: [
                        {'id': 'parentOrgId', 'property': 'parentOrgId', 'value': orgId, 'operator': '='},
                        {"id": "isEffective", "property": "isEffective", "value": "Y"},
                        {"id": "isLaunch", "property": "isLaunch", "value": "Y"}
                    ]
                },
                hooks:{
                        beforeLoad(option) {
                        let flag =  option.param.filtersRaw.some(function(item){return item.property==='[text]'})
                        if (this.gotoOrgFlag){
                        if(flag){
                            option.param.filtersRaw.splice(0,1);
                        }
                        else {
                            option.param.filtersRaw.forEach((item,index)=>{
                                if (item.property==='parentOrgId'){
                                    option.param.filtersRaw.splice(index,1);
                                }
                            })
                            option.param.filtersRaw = [{'id': 'parentOrgId', 'property': 'parentOrgId', 'value': orgId, 'operator': '='}].concat(option.param.filtersRaw)
                        }
                        }
                    }
                },
                sortOptions: null,
            }),
            showEchart:true,
            gotoOrgFlag:true,
            tempOrgId: null,
            tempOrgText:'选择组织',
            dialogFlag: false,
            dataBoardActive: {},
            dataBoardOption: [
                {name: '本周', seq: '1', val: 'week'},
                {name: '本月', seq: '2', val: 'month'},
                {name: '本季度', seq: '3', val: 'quarter'},
                {name: '本财年', seq: '4', val: 'fiscal'},
                {name: '本日历年', seq: '5', val: 'year'}
            ],
            OuathFlag,  //组织选择展示标识
            countScanOption: null, //饼状图对象
            actScanBarHeight: null, //侧柱状图对象高度
            actScanOption:null,  //侧柱状图对象
            chartOneData:{},  //饼状图数据
            chartTwoData:{},   //侧柱状图数据
            orgText:'选择组织',
            orgId,
            scanTypeActive:{value:'OpenScan'}
        }
    },
    watch:{
        async dataBoardActive(n,o){
            await this.ScanNumberPie();
            await this.ActAndScanTypeBar(this.scanTypeActive.value);
        }
    },
    async created () {
        this.dataBoardActive = this.dataBoardOption[0]
        this.userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
        await this.getScanData();
        await this.getActAndScanTypeData()
        await this.ActAndScanTypeBar(this.scanTypeActive.value)
        this.autoList.methods.reload()
    },
    methods:{
       async clickOrganization(){
            this.orgId = this.tempOrgId;
            this.orgText = this.tempOrgText;
            await this.ScanNumberPie();
            await this.ActAndScanTypeBar(this.scanTypeActive.value);
            this.dialogFlag = false;
            this.showEchart = true;
        },

        async goBackOrg(){
            this.autoList.option.searchText = "";
            let filtersRaw=[];
            if(this.autoList.option.param.filtersRaw.some((item)=>{ return item.property==='id'&& item.value===Taro.getStorageSync('token').result.orgId})){
                filtersRaw = [
                    {id: 'id', property: 'id', value: Taro.getStorageSync('token').result.orgId, operator: '='},
                    {id: "isEffective", property: "isEffective", value: "Y"},
                    {id: "isLaunch", property: "isLaunch", value: "Y"}
                ]
            }else if(this.autoList.option.param.filtersRaw.some((item)=>{ return item.property==='parentOrgId'&& item.value===Taro.getStorageSync('token').result.orgId})){
                filtersRaw = [
                    {id: 'id', property: 'id', value: Taro.getStorageSync('token').result.orgId, operator: '='},
                    {id: "isEffective", property: "isEffective", value: "Y"},
                    {id: "isLaunch", property: "isLaunch", value: "Y"}
                ]
            }else{
                filtersRaw = [
                    {id: 'parentOrgId', property: 'parentOrgId', value: Taro.getStorageSync('token').result.orgId, operator: '='},
                    {id: "isEffective", property: "isEffective", value: "Y"},
                    {id: "isLaunch", property: "isLaunch", value: "Y"}
                ]
            }
            this.autoList.option.param.filtersRaw = filtersRaw;
            this.gotoOrgFlag = false;
            await this.autoList.methods.reload();
            this.gotoOrgFlag = true;
        },
        async selectOrg(){
            const item = await this.$object(this.orgOption);
            this.orgText = item.text;
            this.orgId = item.id;

            await this.ScanNumberPie();
            await this.ActAndScanTypeBar(this.scanTypeActive.value);
        },
        async gotoItemOrg(data){
            let filtersRaw = [
                {id: 'parentOrgId', property: 'parentOrgId', value: data.id, operator: '='},
                {"id": "isEffective", "property": "isEffective", "value": "Y"},
                {"id": "isLaunch", "property": "isLaunch", "value": "Y"}
            ]
            this.autoList.option.param.filtersRaw = filtersRaw;
            this.gotoOrgFlag = false;
            await this.autoList.methods.reload();
            this.gotoOrgFlag = true;
        },
        tempOrgInfo(item){
            this.tempOrgId=item.id;
            this.tempOrgText=item.text;
        },
        /**
         * @createdBy  吕志平
         * @date  2022年1月11日11:45:15
         * @description 获取用户职位类型判断是否展示组织选择按钮
         */
        Oauth() {
            let positionType = Taro.getStorageSync('token').result.positionType;
            let arr = [
                'SalesSupervisor',         // 业务主管
                'Salesman',                // 业务代表
                'GroupBuyManager',         // 团购经理
                'AccountManager',          // 客户经理
                'CustServiceManager',      // 客服经理
                'VipManager',              // VIP经理
                'CustServiceSpecialist',   // 客服专员
                'CustServiceSupervisor',   // 客服主管
                'SalesTeamLeader',         // 小组组长
                'SalesChannelManger'       // 渠道经理
            ];
            let flag = arr.includes(positionType);
            return flag;
        },

        /**
         * @createdBy  吕志平
         * @date  2022年1月11日11:45:15
         * @description 通过时间获取扫码记录
         */
        async getScanData(){
            this.$utils.showLoading();
            try{
                let data = await this.$http.post('action/link/actScanRecord/countScanRecordByTime', {
                    timeRange : this.dataBoardActive.val,
                    attr1: this.OuathFlag ? this.orgId : this.userInfo.orgId,
                });
                if(data.success){
                  this.chartOneData = data.rows;
                }
            }catch (e){
                this.$utils.hideLoading();
            }
            finally {
                this.$utils.hideLoading();
            }
        },

        /**
         * @createdBy  吕志平
         * @date  2022年1月11日11:45:15
         * @description 生成扫码记录饼状图
         */
        async ScanNumberPie(){

            // this.$utils.hideLoading();
            await this.getScanData() //获得数据
            this.countScanOption = null
            let seriesData = []
            let totalNum  = 0
            if(this.$utils.isEmpty(this.chartOneData)){
                this.chartOneData = {
                    openBottleSum: '0',
                    giftBottleSum: '0',
                    handVerifySum: '0',
                    actualVerifySum: '0'
                }
            }
            // if(this.chartOneData.outBottleSum){
            //     seriesData.push({
            //         value: Number(this.chartOneData.outBottleSum),
            //         name: '出库扫码总瓶数'
            //     })
            // }
            // if(this.chartOneData.inBottleSum){
            //     seriesData.push({
            //         value: Number(this.chartOneData.inBottleSum),
            //         name: '入库扫码总瓶数'
            //     })
            // }
                if(this.chartOneData.openBottleSum){
                    seriesData.push({
                        value: Number(this.chartOneData.openBottleSum),
                        name: '开瓶扫码瓶数'
                    })
                }
                if(this.chartOneData.giftBottleSum){
                    seriesData.push({
                        value: Number(this.chartOneData.giftBottleSum),
                        name: 'VIP赠酒扫码瓶数'
                    })
                }
                if(this.chartOneData.handVerifySum){
                    seriesData.push({
                        value: Number(this.chartOneData.handVerifySum),
                        name: '手动编辑核销瓶数'
                    })
                }
                if(this.chartOneData.actualVerifySum){
                    totalNum = this.chartOneData.actualVerifySum;
                }

            let pieColor = [
                //     {
                //     c1: '#FFB701',  //管理
                //     c2: '#FF5A5A'
                // },{
                //     c1: '#6392FA',  //管理
                //     c2: '#4179F4'
                // }
            ];
            // this.ScanNumberPieHeight = 200 + 24 * seriesData.length;
            var totalSeriesData = [{value: totalNum, name: '实际核销总瓶数'}]
            this.countScanOption = echartInitConfig=>targetPieChartProgress(echartInitConfig,seriesData, totalSeriesData,['26%', '50%'],'30%',pieColor,225,'value','瓶','',5, 'smallSize');
            // this.$utils.hideLoading();
        },
        /**
         * @createdBy  吕志平
         * @date 2022年1月11日14:18:09
         * @description 通过扫码记录类型查询数据
         */
        async  queryActScanDataByType(type){
            this.scanTypeActive.value = type
            await  this.ActAndScanTypeBar(type)
        },
        /**
         * @createdBy  吕志平
         * @date  2022年1月11日11:45:15
         * @description 获取活动扫码记录
         */
        async getActAndScanTypeData(Type){
            this.$utils.showLoading();
            try {
                let data = await this.$http.post('action/link/actScanRecord/countScanRecordByActAndScanType', {
                    timeRange : this.dataBoardActive.val,
                    scanTypeFlag: Type === 'verifi' ? '' : 'Y',  //是否手动编辑核销
                    attr1: this.OuathFlag ? this.orgId : this.userInfo.orgId,
                    scanSubType: Type === 'verifi' ? '' : Type,
                });
                if(data.success){
                    this.chartTwoData = data.rows;
                }
            }catch (e){
                this.$utils.hideLoading();
            }finally {
                this.$utils.hideLoading();
            }
        },

        /**
         * @createdBy  吕志平
         * @date  2022年1月11日11:45:15
         * @description 生成活动扫码记录侧柱状图
         */
        async ActAndScanTypeBar(reportType){
            // this.$utils.showLoading();
            // this.$utils.hideLoading();
            this.actScanOption = null;
            await this.getActAndScanTypeData(reportType)
            let lovType = 'MC_TYPE';
            var seriesData = []
            if(this.$utils.isEmpty(this.chartTwoData)){
                return;
            }
            for(let i in this.chartTwoData){
                if(this.chartTwoData[i].activityType==="null"){
                    if(this.scanTypeActive.value==='verifi'){
                        seriesData.push({
                            value: Number(this.chartTwoData[i].actVerifyBottleSum),
                            name: '空'
                        })
                    }else{
                        seriesData.push({
                            value: Number(this.chartTwoData[i].actScanBottleSum),
                            name: '空'
                        })
                    }
                }else{
                    if(this.scanTypeActive.value==='verifi'){
                        let name = await this.$lov.getNameByTypeAndVal(lovType, this.chartTwoData[i].activityType)
                        seriesData.push({
                            value: Number(this.chartTwoData[i].actVerifyBottleSum),
                            name: name
                        })
                    }else{
                        let name = await this.$lov.getNameByTypeAndVal(lovType, this.chartTwoData[i].activityType)
                        seriesData.push({
                            value: Number(this.chartTwoData[i].actScanBottleSum),
                            name: name
                        })
                    }
                }
            }
            this.actScanBarHeight = 70 + 26 * this.chartTwoData.length
            this.actScanOption = echartInitConfig=>barYCategory(echartInitConfig,seriesData,true,'瓶');
            // this.$utils.hideLoading();
        },

    }

}
</script>

<style lang="scss">
.scan-code-board-org-page{
    background-color: #F0F4FE;
    padding-bottom: 120px;
    .lnk-tabs{
        position: relative;
        border-bottom: 2px solid #f2f2f2!important;
    }
    .scroll-view-data{
        background-color: #FFFFFF;
        .select-dimension{
            display: flex;
            margin-left: 24px;
        }
    }
}
.scan-code-board-noOrg-page{
    background-color: #F0F4FE;
    padding-bottom: 120px;
    .lnk-tabs{
        position: relative;
        border-bottom: 2px solid #f2f2f2!important;
    }
    .scroll-view-data{
        .select-dimension{
            display: flex;
            margin-left: 24px;
        }
    }
}

.scroll-data{
    .select-dimension{
        display: flex;
        margin-left: 24px;
    }
}
.overview{
    background-color: #ffffff;
    margin: 0 24px;
}

.panelOne{
    background-color: #ffffff;
    height: 468px;
    margin: 20px 24px 0px;
}

.chartOne{
    height: 280px;
    width: 280px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto;
    border: 32px solid #E3E9FB;
    background: -webkit-radial-gradient(#4176F7, #6392FA); /* Safari 5.1 - 6.0 */
    background: -o-radial-gradient(#4176F7, #6392FA); /* Opera 11.1 - 12.0 */
    background: -moz-radial-gradient(#4176F7, #6392FA); /* Firefox 3.6 - 15 */
    background: radial-gradient(#4176F7, #6392FA); /* 标准的语法 */
}
.pcNum{
    margin: 64px auto 0;
    text-align: center;
    font-size: 50px;
    line-height: 72px;
    color: #FFFFFF;
}
.pcText{
    margin: 0 auto;
    text-align: center;
    width: 156px;
    font-size: 26px;
    color: #FFFFFF;
}
.chartOneText{
    display: flex;
    margin: 12px 76px 0;
}
.chartOneTextItem{
    flex: 1;
    font-size: 24px;
    color: #999999;
    display: flex;
}
.dot{
    width: 8px;
    height: 8px;
    background-color: #2F69F8;
    margin: auto 16px auto 0;
    border-radius: 50%;
}


.panelOneBorder{
    background-color: #E1E6F5;
    width: 88%;
    height: 1px;
    margin: 0 auto;
}
.link-dialog-foot-custom{
    width: auto !important;
}


</style>
