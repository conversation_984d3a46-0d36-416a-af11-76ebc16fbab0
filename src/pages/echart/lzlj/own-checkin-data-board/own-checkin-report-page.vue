<template>
    <link-page class="own-checkin-report-page">
        <lnk-taps :taps="dataBoardOption" v-model="dataBoardActive" v-if="dataBoardOption.length>1"></lnk-taps>
        <view v-if="dataBoardActive.val === 'own'">
            <own-checkin-page :source="source" :abnormalTimes="abnormalTimes"/>
        </view>
        <view v-if="dataBoardActive.val === 'team'">
            <team-checkin-page></team-checkin-page>
        </view>

    </link-page>
</template>

<script>

    import lnkTaps from "../../../core/lnk-taps/lnk-taps";
    import ownCheckinPage from './components/own-checkin'
    import teamCheckinPage from './components/team-checkin'
    import Taro from "@tarojs/taro";
    export default {
        name: "own-checkin-report-page",
        components:{
            lnkTaps,
            ownCheckinPage,
            teamCheckinPage
        },
        data(){
         return{
             dataBoardActive: {},
             dataBoardOption: [],
             source: '',  //页面跳转来源
             abnormalTimes:'',//异常次数
         }
        },

        created() {
            let userInfo = Taro.getStorageSync('token').result
            if(userInfo.positionType === 'Salesman'){
                // 不可查看团队考勤表；
                this.dataBoardOption = [
                    {name: '个人考勤', seq: '1', val: 'own'},
                ]
            }else if(userInfo.positionType === 'GroupBuyManager'
                ||userInfo.positionType === 'AccountManager'
                ||userInfo.positionType === 'RegionalManager'
                ||userInfo.positionType === 'CustServiceManager'
                ||userInfo.positionType === 'VipManager'
                ||userInfo.positionType === 'CustServiceSupervisor'
                ||userInfo.positionType === 'BattleCommander'
                ||userInfo.positionType === 'SalesTeamLeader'
            ){
                // 查看团队考勤报表走职位安全性，即查看我职位及我下级职位的数据
                this.dataBoardOption = [
                    {name: '个人考勤', seq: '1', val: 'own'},
                    {name: '团队考勤', seq: '2', val: 'team'},
                ]
            }else {
                // 其他的都走组织安全性，即查看我组织及下级组织的
                this.dataBoardOption = [
                    {name: '个人考勤', seq: '1', val: 'own'},
                    {name: '团队考勤', seq: '2', val: 'team'},
                ]
            }
            this.dataBoardActive = this.dataBoardOption[0]

            console.log(this.pageParam)
            // editby 邓佳柳 241011看板2.0-风险预警-考勤异常预警-本月异常考勤打卡次数
            if (!this.$utils.isEmpty(this.pageParam) && this.pageParam.source === 'riskWarning') {
                this.source = this.pageParam.source
                this.abnormalTimes  =this.pageParam.num
            }
        }

    }
</script>

<style lang="scss">
    .own-checkin-report-page{
        .lnk-tabs-container{
            height: 94px;
        }
        .lnk-tabs{
            z-index: 1503 !important;
            height: 94px;
            border-bottom: 2px solid #f2f2f2!important;
        }
    }
</style>
