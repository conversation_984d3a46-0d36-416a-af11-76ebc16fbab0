<template>
    <link-page class="day-own-checkin-detail-page">
        <view class="top-date">
            {{topDate}}
        </view>
        <view class="table-row-title">
            <view class="column-title">
                {{'人员'}}
            </view>
            <view class="column-title">
                {{'状态'}}
            </view>
        </view>

        <link-auto-list :option="autoList" hideCreateButton>
            <template slot-scope="{data,index}">
                <view :key="index" :data="data" class="table-row">
                  <view class="column" @tap="showMessageDialog(data.empName)">
                      <view class="text">
                          {{data.empName}}
                      </view>
                  </view>
                    <view class="column" @tap="showMessageDialog(data.checktype)">
                        <view class="text">
                            {{data.checktype}}
                        </view>
                    </view>
                </view>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>

    export default {
        name: "day-own-checkin-detail-page",
        data(){
            return{
                autoList: new this.AutoList(this, {
                    // module: 'action/link/quota',
                    searchFields: null,
                    url:{
                        queryByExamplePage: 'tencent/link/checkinData/queryTeamAttendanceReportDay'
                    },
                    param: {
                        // filtersRaw: [{id: "headId", property: "headId", value: null, operator: "="}],
                    },
                    sortOptions: null,
                    hooks: {
                        beforeLoad (data) {
                            data.param.orgId = this.pageParam.orgId
                            data.param.startDate = this.pageParam.startDate
                            data.param.endDate = this.pageParam.endDate
                        },
                    }
                }),
                topDate: null
            }
        },
        created() {
            this.topDate = this.pageParam.date
        },
        methods:{
            /**
             * @createdBy  张丽娟
             * @date  2020/11/25
             * @methods showMessageDialog
             * @para
             * @description 展示表格数据
             */
            showMessageDialog(val){
                this.$message.primary(val)
            },
        }
    }
</script>

<style lang="scss">
    .day-own-checkin-detail-page{
        background: #fff;
        .top-date{
            text-align: center;
            height: 92px;
            line-height: 92px;
            font-family: PingFangSC-Regular serif;
            font-size: 28px;
            color: #262626;
            letter-spacing: 0;
        }
        .table-row-title{
            display: flex;
            width: 100%;
            background: #6D96FA;
            .column-title{
                display: flex;
                justify-content: center;
                align-items: center;
                height: 72px;
                line-height: 72px;
                /*width: 50%;*/
                text-align: center;
                color: #fff;
                font-size: 24px;
                &:nth-child(1){
                    width: 30%;
                }
                &:nth-child(2){
                    width: 70%;
                }
            }
        }
        .table-row{
            display: flex;
            width: 100%;
            .column{
                display: flex;
                justify-content: center;
                align-items: center;
                height: 112px;
                line-height: 112px;
                border-right: 2px solid #DEE7FE;
                text-align: center;
                border-bottom: 2px solid #DEE7FE;
                color: #262626;
                font-size: 24px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                &:nth-child(1){
                    width: 30%;
                }
                &:nth-child(2){
                    width: 70%;
                }
                .text{
                    display: inline-block;
                    color: #262626;
                    font-size: 24px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }
            .column:nth-child(3n+1){
                border-left: 2px solid #DEE7FE;
            }
        }
    }
</style>
