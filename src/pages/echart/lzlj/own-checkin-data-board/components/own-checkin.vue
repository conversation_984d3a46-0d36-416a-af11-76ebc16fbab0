<template>
    <view class="own-checkin-page">
        <view class="link-date-top">
            <view class="center">
                <link-icon icon="mp-desc"></link-icon>
                <link-date view="YM" valueFormat="YYYY-MM" displayFormat="YYYY年MM月" v-model="selectDate" style="display:inline-block;"/>
                <link-icon icon="mp-desc"></link-icon>
            </view>

        </view>
        <view class="table">
            <view class="column-1">
                <view class="name">
                    实际出勤天数
                </view>
                <view class="num">
                    {{checkinData.workDay || 0}}
                </view>
            </view>
             <view class="column-1"  v-if="source === 'riskWarning'">
                <view class="name">
                    异常打卡次数
                </view>
                <view class="num">
                    {{checkinData.exceptionCount || 0}}
                </view>
            </view>
            <view class="column-1" v-else>
                <view class="name">
                    异常打卡天数
                </view>
                <view class="num">
                    {{checkinData.exceptionDay || 0}}
                </view>
            </view>
            <view class="column-1">
                <view class="name">
                    缺勤天数
                </view>
                <view class="num">
                    {{checkinData.absentDay || 0}}
                </view>
            </view>
<!--            <view class="column-1">-->
<!--                <view class="name">-->
<!--                    外出天数-->
<!--                </view>-->
<!--                <view class="num">-->
<!--                    {{checkinData.clockOut || 0}}-->
<!--                </view>-->
<!--            </view>-->
            <view class="column-1">
                <view class="name">
                    请假天数
                </view>
                <view class="num">
                  {{checkinData.leaveDay || 0}}
                </view>
            </view>
            <view class="column-1">
                <view class="name">
                    补卡天数
                </view>
                <view class="num">
                  {{checkinData.repSignDay || 0}}
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    import {DateService} from "link-taro-component";
    import Taro from "@tarojs/taro";

    export default {
        name: "own-checkin-page",
        data(){
            return{
                checkinData: {},
                selectDate: '',
                selectedMounth: '',
                userInfo: {}
            }
        },
        watch:{
            selectDate(newVal, oldVal) {
                if (newVal !== oldVal) {
                    var dateArr = newVal.split('-')
                    this.selectedMounth = dateArr[1]
                    this.fetchOwnCheckin()
                }
            }
    },
        props: ['source','abnormalTimes'],
        created() {
            this.selectDate = DateService.format(new Date(), 'YYYY-MM')
            this.selectedMounth = this.selectDate.split('-')[1]
            this.userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
        },
        methods:{
            /**
             * @createdBy  张丽娟
             * @date  2020/12/9
             * @methods fetchOwnCheckin
             * @para
             * @description 个人考勤
             */
            async fetchOwnCheckin(){
                try{
                    const data = await this.$http.post('tencent/link/checkinData/queryPersonalAttendanceReport', {
                        "userId": this.userInfo.username,
                        "startDate": this.selectDate + '-01 00:00:00',
                        "endDate": this.selectDate + '-'+ this.getCountDays() +' 23:59:59'
                    })
                    if(data.success){
                        this.checkinData = data.rows
                    }else{
                        this.$showError('获取个人考勤报表数据失败，请稍后再试')
                    }

                }catch(e){
                    this.$showError(String(e))
                }
            },
             getCountDays() {
                var curDate = new Date(this.selectDate);
               /* 获取当前月份 */
                var curMonth = curDate.getMonth();
                /*  生成实际的月份: 由于curMonth会比实际月份小1, 故需加1 */
               curDate.setMonth(curMonth + 1);
                /* 将日期设置为0, 这里为什么要这样设置, 我不知道原因, 这是从网上学来的 */
                curDate.setDate(0);
                /* 返回当月的天数 */
                return curDate.getDate();
             }
        }
    }
</script>

<style lang="scss">
    .own-checkin-page{
        padding-bottom: 20px;
        background: #fff;
        .link-date-top{
            display: flex;
            justify-content: center;
            padding-top: 24px;
            .center{
                width: 200px;
                display: flex;
                align-items: center;
                .link-icon{
                    font-size: 24px;
                    &:nth-child(1){
                        transform: rotate(90deg);
                    }
                    &:nth-child(3){
                        transform: rotate(-90deg);
                    }
                }
            }
        }
        .table{
           margin: 0 40px 18px;
           padding-top: 20px;
            .column-1{
            width: 100%;
            height: 72px;
            display: flex;
            justify-content: center;
            align-items: center;
            &:nth-child(1){
                border-top: 1px solid #DEE7FE;
            }
            .num{
                font-family: PingFangSC-Semibold serif;
                font-size: 24px;
                letter-spacing: 0;
                text-align: center;
                line-height: 72px;
                width: 65%;
                border-right: 1px solid #DEE7FE;
                border-bottom: 1px solid #DEE7FE;
                height: 72px;
                color: #262626;
            }
            .name{
                font-family: PingFangSC-Regular serif;
                font-size: 24px;
                background: #6D96FA;
                color: #fff;
                letter-spacing: 0;
                text-align: center;
                line-height: 72px;
                height: 72px;
                width: 35%;
            }
        }
    }

    }

</style>
