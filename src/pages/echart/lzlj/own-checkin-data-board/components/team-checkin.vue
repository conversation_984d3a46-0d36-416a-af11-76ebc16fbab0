<template>
    <view class="team-checkin-page">
        <link-calendar v-model="selectDate" >
        </link-calendar>
        <view class="statistics-title">
            <view class="left">
                <line-title title="每日统计"></line-title>
            </view>
            <view class="right" @tap="gotoDailyDetail">查看明细</view>
        </view>
        <view class="statistics-echart">
            <link-echart :force-use-old-canvas='false' :option="dailyStatsOption" dispatchActionName="女" height="226px"/>
            <view class="table">
                <view class="column-1">
                    <view class="num">
                        {{dailyStatsFormData.timeException || 0}}
                    </view>
                    <view class="name">
                        {{'时间异常'}}
                    </view>
                </view>
                <view class="column-1">
                    <view class="num">
                        {{dailyStatsFormData.addrException || 0}}
                    </view>
                    <view class="name">
                        {{'地点异常'}}
                    </view>
                </view>
                <view class="column-1">
                    <view class="num">
                        {{dailyStatsFormData.clockException || 0}}
                    </view>
                    <view class="name">
                        {{'未打卡'}}
                    </view>
                </view>
                <view class="column-1">
                    <view class="num">
                        {{dailyStatsFormData.repSignDay || 0}}
                    </view>
                    <view class="name">
                        {{'补卡'}}
                    </view>
                </view>
                <view class="column-1">
                    <view class="num">
                        {{dailyStatsFormData.leaveDay || 0}}
                    </view>
                    <view class="name">
                        {{'请假'}}
                    </view>
                </view>
                <view class="column-1">
                    <view class="num">
                        {{dailyStatsFormData.workDay || 0}}
                    </view>
                    <view class="name">
                        {{'正常'}}
                    </view>
                </view>
            </view>
        </view>

      <view class="statistics-title">
            <view class="left">
                <line-title :title="selectedMounth+'月统计'"></line-title>
            </view>
            <view class="right" @tap="gotoMounthDetail">查看明细</view>
        </view>
              <view class="statistics-echart">
                  <link-echart :force-use-old-canvas='false' :option="mounthStatsOption" dispatchActionName="女" height="226px"/>
                  <view class="table">
                      <view class="column-1">
                          <view class="num">
                              {{mounthFormData.timeException || 0}}
                          </view>
                          <view class="name">
                              {{'时间异常'}}
                          </view>
                      </view>
                      <view class="column-1">
                          <view class="num">
                              {{mounthFormData.addrException || 0}}
                          </view>
                          <view class="name">
                              {{'地点异常'}}
                          </view>
                      </view>
                      <view class="column-1">
                          <view class="num">
                              {{mounthFormData.clockException || 0}}
                          </view>
                          <view class="name">
                              {{'未打卡'}}
                          </view>
                      </view>
                      <view class="column-1">
                          <view class="num">
                              0
                          </view>
                          <view class="name">
                              {{'补卡'}}
                          </view>
                      </view>
                      <view class="column-1">
                          <view class="num">
                              0
                          </view>
                          <view class="name">
                              {{'请假'}}
                          </view>
                      </view>
                  <view class="column-1">
                          <view class="num">
                              {{mounthFormData.clockOut || 0}}
                         </view>
                          <view class="name">
                             {{'外出'}}
                          </view>
                     </view>
                  </view>
           </view>
              </view>
</template>

<script>
    import LineTitle from "../../../../lzlj/components/line-title";
    import {targetPieChartProgress} from "../../echart.utils";
    import {DateService} from "link-taro-component";
    import Taro from "@tarojs/taro";

    export default {
        name: "team-checkin-page",
        data(){
            return{
                dailyStatsOption: {},
                dailyStatsFormData: {},
                mounthStatsOption: {},
                mounthFormData: {},
                selectedMounth: '',
                selectDate: '',
                statusBarHeight: this.$device.systemInfo.statusBarHeight + 14,
                userInfo: {}
            }
        },
        components: {LineTitle},
        watch:{
            selectDate(newVal, oldVal) {
                if (newVal !== oldVal) {
                    console.log(newVal,'newVal')
                    var dateArr = newVal.split('-')
                    this.selectedMounth = dateArr[1]
                    this.dailyStatsEchart()
                    this.mounthStatsEchart()
                }
            }
        },
        created() {
            this.userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
             //this.selectDate = DateService.format('2023-03-28', 'YYYY-MM-DD')
             const  newDate = new Date();
             newDate.setTime(newDate.getTime()-24 * 60 * 60 * 1000);
            this.selectDate = DateService.format(newDate, 'YYYY-MM-DD');
            this.selectedMounth = this.selectDate.split('-')[1]
        },
        methods:{
            /**
             * @createdBy  张丽娟
             * @date  2020/12/9
             * @methods dailyStatsEchart
             * @para
             * @description 日报echart
             */
            async dailyStatsEchart(){
                const data = await this.fetchteamCheckin(this.selectDate + ' 00:00:00', this.selectDate +' 23:59:59')
                this. dailyStatsFormData  = data
                let seriesData = [
                    {value: Number(data.exceptionDay)-Number(data.clockException) , name: '异常'},
                    {value: Number(data.workDay) , name: '正常'},
                    {value: Number(data.clockException) , name: '未打卡'}
                ];
                let totalSeriesData = [{value: Number(data.userTotal), name: '总打卡人数'}];
                let pieColor = [
                //     {
                //     c1: '#FFB701',  //管理
                //     c2: '#FF5A5A'
                // },{
                //     c1: '#6392FA',  //管理
                //     c2: '#2F69F8'
                // },
                ];
                this.dailyStatsOption = echartInitConfig=>targetPieChartProgress(echartInitConfig,seriesData, totalSeriesData,['45%', '70%'],'45%',pieColor,90);
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/12/9
             * @methods mounthStatsEchart
             * @para
             * @description 月报echart
             */
            async mounthStatsEchart(){
                let start = this.selectDate.slice(0,-3)
                const data = await this.fetchTeamAttendanceMonthSummary(start + '-01 00:00:00', start + '-'+ this.getCountDays() +' 23:59:59')
                this.mounthFormData  = data
                let seriesData = [
                    {value: Number(data.totalUser) -  Number(data.workDay) , name: '异常' },
                    {value: Number(data.workDay), name: '正常'}
                ];
                let totalSeriesData = [{value:data.totalUser , name: '总打卡人数'}];
                let pieColor = [
                //     {
                //     c1: '#FFB701',  //管理
                //     c2: '#FF5A5A'
                // },{
                //     c1: '#6392FA',  //管理
                //     c2: '#2F69F8'
                // },
                ];
                this.mounthStatsOption = echartInitConfig=>targetPieChartProgress(echartInitConfig,seriesData, totalSeriesData,['45%', '70%'],'45%',pieColor,90);
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/12/9
             * @methods fetchteamCheckin
             * @para
             * @description 获取团队、个人考勤数据
             */
            async fetchteamCheckin(startDate,endDate){
               let param = {
                    "orgId": this.userInfo.orgId,
                    "startDate": startDate,
                    "endDate":endDate,
                    page: 1,
                    rows: 10
                }

                try{
                    const data = await this.$http.post('tencent/link/checkinData/queryTeamAttendanceReportDay', param)
                    if(data.success){
                       return data
                    }else{
                        this.$showError('获取团队考勤报表数据失败，请稍后再试')
                    }
                }catch(e){
                    this.$showError(String(e))
                }
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/12/9
             * @methods fetchTeamAttendanceMonthSummary
             * @para
             * @description 获取月统计
             */
            async fetchTeamAttendanceMonthSummary(startDate,endDate){
                let param = {
                    "orgId": this.userInfo.orgId,
                    "startDate": startDate,
                    "endDate":endDate,
                    page: 1,
                    rows: 10
                }
                try{
                    const data = await this.$http.post('tencent/link/checkinData/queryTeamAttendanceReportMonthSummary', param)
                    if(data.success){
                        return data.rows
                    }else{
                        this.$showError('获取团队考勤报表数据失败，请稍后再试')
                    }
                }catch(e){
                    this.$showError(String(e))
                }
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/12/9
             * @methods getCountDays
             * @para
             * @description 获取选中时间月份
             */
            getCountDays() {
                var curDate = new Date(this.selectDate);
                /* 获取当前月份 */
                var curMonth = curDate.getMonth();
                /*  生成实际的月份: 由于curMonth会比实际月份小1, 故需加1 */
                curDate.setMonth(curMonth + 1);
                /* 将日期设置为0, 这里为什么要这样设置, 我不知道原因, 这是从网上学来的 */
                curDate.setDate(0);
                /* 返回当月的天数 */
                return curDate.getDate();
            },
            gotoDailyDetail(){
                this.$nav.push('/pages/echart/lzlj/own-checkin-data-board/day-own-checkin-detail-page',{date: this.selectDate, startDate:this.selectDate + ' 00:00:00' ,endDate: this.selectDate +' 23:59:59',orgId: this.userInfo.orgId })
            },
            gotoMounthDetail(){
                let start = this.selectDate.slice(0,-3)
                this.$nav.push('/pages/echart/lzlj/own-checkin-data-board/mounth-checkin-detail-page',{date: this.selectDate,startDate:start + '-01 00:00:00',endDate: start + '-'+ this.getCountDays() +' 23:59:59',orgId: this.userInfo.orgId   })
            }
        },
    }
</script>

<style lang="scss">
    .team-checkin-page{
        background: #fff;
        padding-bottom: 24px;
        width: 100%;
        .statistics-title{
            display: flex;
            justify-content: space-between;
            margin: 0 24px;
            .right{
                font-family: PingFangSC-Regular serif;
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
                text-align: right;
                line-height: 28px;
                align-self: flex-end;
            }
        }
        .statistics-echart{
            background: #FFFFFF;
            border: 2px solid #EBEDF5;
            border-radius: 16px;
            margin: 24px 24px 0px;
        }
        .table{
            margin: 0 40px 18px;
            display: flex;
            flex-wrap: wrap;
            .column-1{
                width: 33.33%;
                height: 105px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                .num{
                    font-family: PingFangSC-Semibold serif;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    text-align: center;
                    line-height: 32px;
                    margin-bottom: 12px;
                }
                .name{
                    font-family: PingFangSC-Regular serif;
                    font-size: 24px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    text-align: center;
                    line-height: 24px;
                }
            }
        }
    }

</style>
