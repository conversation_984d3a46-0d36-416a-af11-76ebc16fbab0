<template>
    <view class="own-checkin-report">
        <view class="select-dimension">
            <select-button :is-board="true" label="团队考勤" v-if="dataBoardOption.length > 1" :selected-flag="dataBoardActive==='team'"  @tap="dataBoardActive='team'"></select-button>
            <select-button :is-board="true" label="个人考勤" :selected-flag="dataBoardActive==='own'"  @tap="dataBoardActive='own'"></select-button>
        </view>
        <view v-if="dataBoardActive === 'own'">
            <own-checkin-page/>
        </view>
        <view v-if="dataBoardActive === 'team'">
            <team-checkin-page></team-checkin-page>
        </view>
    </view>
</template>

<script>

    import lnkTaps from "../../../core/lnk-taps/lnk-taps";
    import ownCheckinPage from './components/own-checkin'
    import teamCheckinPage from './components/team-checkin'
    import Taro from "@tarojs/taro";
    import SelectButton from '../components/select-button'
    export default {
        name: "own-checkin-report",
        components:{
            lnkTaps,
            ownCheckinPage,
            teamCheckinPage,
            SelectButton
        },
        data(){
         return{
             dataBoardActive: 'team',
             dataBoardOption: [],
         }
        },

        created() {
            let userInfo = Taro.getStorageSync('token').result
            if(userInfo.positionType === 'Salesman'){
                // 不可查看团队考勤表；
                this.dataBoardOption = [
                    {name: '个人考勤', seq: '1', val: 'own'},
                ]
                this.dataBoardActive = 'own';
            }else if(userInfo.positionType === 'GroupBuyManager'
                ||userInfo.positionType === 'AccountManager'
                ||userInfo.positionType === 'RegionalManager'
                ||userInfo.positionType === 'CustServiceManager'
                ||userInfo.positionType === 'VipManager'
                ||userInfo.positionType === 'CustServiceSupervisor'
                ||userInfo.positionType === 'BattleCommander'
                ||userInfo.positionType === 'SalesTeamLeader'
            ){
                // 查看团队考勤报表走职位安全性，即查看我职位及我下级职位的数据
                this.dataBoardOption = [
                    {name: '个人考勤', seq: '1', val: 'own'},
                    {name: '团队考勤', seq: '2', val: 'team'},
                ]
            }else {
                // 其他的都走组织安全性，即查看我组织及下级组织的
                this.dataBoardOption = [
                    {name: '个人考勤', seq: '1', val: 'own'},
                    {name: '团队考勤', seq: '2', val: 'team'},
                ]
            }
        }

    }
</script>

<style lang="scss">
    .own-checkin-report{
        margin-top: 20px;
        .select-dimension{
            display: flex;
            margin-left: 24px;
          .select-button{
            background: #F2F3F6;
          }
        }
    }
</style>
