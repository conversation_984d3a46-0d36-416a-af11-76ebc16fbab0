<template>
    <view class="no-auth-img">
        <image :src="authFlag ? $imageAssets.noAuthImage : $imageAssets.noDataImage"></image>
        <view class="auth-text">{{label}} <text @tap="goAttestation" :class="{'text-disabled': disabledNewEdit}">{{attestation}}</text></view>
    </view>
</template>

<script>
    export default {
        name: "lnk-no-auth",
        data () {
            return {
                authPath: `${this.$env.imageAssetPath}/images/components/no-auth/no-auth.png?${Math.random() / 9999}`
            }
        },
        props: {
            authFlag: {
                type: Boolean,
                default: true
            },
            label: {
                type: String,
                default: '暂无权限'
            },
            attestation: {
                type: String,
                default: ''
            },
            disabledNewEdit:{
            type: Boolean,
            default: false
            }
        },
        methods: {
            /**
              * 前往认证
              * <AUTHOR>
              * @date 2020-09-21
            */
            goAttestation () {
                this.$emit('goAttestation')
            }
        }
    }
</script>

<style lang="scss">
    .no-auth-img {
        @include flex-center-center;
        @include direction-column;
        image {
            width: 368px;
            height: 368px;
            margin-top: 48px;
        }
        .auth-text {
            padding-top: 32px;
            font-size: 28px;
            color: #8C8C8C;
            text {
                color: #2F69F8;
            }
          .text-disabled{
            color: #8C8C8C!important;
          }
        }
    }
</style>
