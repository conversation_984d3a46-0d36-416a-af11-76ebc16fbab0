<template>
    <!--  图片上传组件-上传到cos-需要加水印-拍照或者选择照片结束后上传前加水印-->
    <view class="lnk-img-watermark-box">
        <view v-show="loadingShow" class="mask" catchtouchmove="true" @touchmove.stop.prevent>
            <view class="background">
                <link-loading class="loading"/>
                <view class="loading-text">{{ maskWord }}</view>
            </view>
        </view>
        <view class="lnk-img-watermark">
            <view class="lnk-img-watermark-item lnk-img-watermark-item-camera-wrapper"
                  v-if="!startPainting && newFlag && (isCount ? imgData.length < limLength : true)" @tap="addImg">
                <view class="add-image-btn">
                    <link-loading class="loading" v-if="disabledAddImgFlag"/>
                    <view class="iconfont icon-plus add-image-icon" v-else></view>
                </view>
            </view>
            <view class="lnk-img-watermark-item" v-for="(img,index) in imgData" :key="index"
                  v-if="index < (imageShowAllFlag ? imgData.length : imageDataShowNum)">
                <view class="lnk-img-watermark-item-box" :class="{'show-info': dataSourceShowFlag||createdShowFlag}">

                    <view class="item-box-image">
                        <image :src="img.uploadType === 'cos' ? $image.getSignedUrl(img[imgSmallKey]) : img.smallurl"
                               :data-src="img.uploadType === 'cos' ? $image.getSignedUrl(img[imgSmallKey]) : img.smallurl"
                               class="shadow-image"
                               v-if="img.operator === 'uploading'"
                               @load="onImgLoad($event,img)"></image>
                        <image :src="img.imgUrl"
                               class="item-image"
                               :data-src="img.smallurl"
                               @tap="clickImg(index,img)"></image>
                        <view class="progress-bg" v-if="img.operator === 'uploading'"></view>
                        <view class="progress" v-if="img.operator === 'uploading'">
                            <view class="progress__inner" :style="{'width': img.progress +'%'}"></view>
                        </view>
                    </view>
                    <view class="lnk-img-close-icon iconfont icon-close-circle-fill"
                          v-if="(img.operator !== 'uploading') && delFlag && (historyDelFlag ? true : img.address)"
                          @tap.stop="delImg(img, index)"></view>
                    <view v-if="dataSourceShowFlag" style="width: 100%;font-size: 12px;text-align: center">
                        {{ getPicSource(img.dataSource) }}
                    </view>
                    <view v-if="createdShowFlag" style="width:100%;text-align:center;font-size: 12px;line-height: 12px">
                        {{ img.created | date('YYYY-MM-DD HH:mm:ss') }}
                    </view>
                </view>
            </view>
            <view v-if="imgData.length > imageDataShowNum" @tap="switchImgShowNum" class="show-more-image">
                <view class="show-less" v-if="imageShowAllFlag">收起<link-icon icon="icon-down"></link-icon></view>
                <view class="show-more" v-else>
                    展开剩余{{imgData.length - imageDataShowNum}}张图片
                    <link-icon icon="icon-right"></link-icon>
                </view>
            </view>
        </view>
        <link-dialog ref="dialog" :initial="source !== 'agreeDetail'">
            <view slot="head">
                提示
            </view>
            <view style="padding: 10px;">
                请确认手机地理位置授权是否打开，或者【手机设置】-【企业微信】位置权限管理是否打开？
            </view>
            <link-button slot="foot" @tap="$refs.dialog.hide()">了解</link-button>
            <link-button slot="foot" @tap="openSet()">已开启</link-button>
        </link-dialog>
    </view>
</template>

<script>
import Taro from "@tarojs/taro";
import {reverseTMapGeocoder} from "../../../utils/locations-tencent";
import {imgConfig} from "../../../utils/imgConfig";
import {$utils, ComponentUtils, ImageService} from 'link-taro-component'
import LinkPainter from '../../../components/painter/link-painter'
import cos from "../../../utils/imgCos";
import {cosWatermark} from "./createCosWatermark";

/**
 * 先下载图片之后再预览图片
 * 如果已经下载过的图片地址，则不再下载，除非重启小程序
 * <AUTHOR>
 * @date    2022/7/12 12:50
 */
const previewImages = (() => {
    const cache = {}
    return async ({currentIndex, urls}) => {
        currentIndex = currentIndex == null ? 0 : currentIndex
        const promises = urls.map(url => {
            let promise = cache[url]
            if (!promise) {
                promise = cache[url] = new Promise((resolve, reject) => {
                    wx.downloadFile({
                        url: url,
                        success: (res) => {resolve(res.tempFilePath)},
                        fail: (err) => reject(err),
                    });
                })
            }
            return promise
        })
        $utils.showLoading('获取图片中');
        try {
            const downloadUrls = await Promise.all(promises)
            ImageService.previewImages({current: downloadUrls[currentIndex], urls: downloadUrls})
        } catch (e) {
            console.error(e)
            $utils.showLoading('获取图片失败！');
        } finally {
            $utils.hideLoading()
        }
    }
})();

const lineImagePath = '/static/images/painter/line.png'
const textBgCoverPath = '/static/images/painter/clarity-shade.png'

export default {
    name: 'lnk-img-watermark',
    data() {
        const logger = wx.getRealtimeLogManager();
        return {
            limLength: this.addLength ? this.addLength : 9, //如果没有传入限制数量，默认限制9张
            logger,
            loadingShow: false,
            maskWord: '图片上传中',
            wxImageList: {
                tempFilePaths: [],
                tempFiles: [],
                list: [],
                uploadIndex: 0
            },
            wxImageIndex: 0,
            rawData: {}, // 图片上传前宽高
            isAndroid: false, // 是否为安卓机
            closeLoad: true, // 关闭loading弹窗
            imgList: [],                // 展示图片数组
            originalPath: [],           // 原图片url数组列表
            // tempFilePaths: [],           // 缩略图图片url数组列表
            paletteData: null,
            painterFlag: true,
            saveImgOkOnce: false,
            painterWidth: null,
            painterHeight: null,
            leftLine: lineImagePath,                                                              // 绘制水印左上竖线
            textBgCover: textBgCoverPath,                                                   // 绘制水印底部遮罩
            addressData: '',                                                                                    // 地址数据
            addressDataFull: '',                                                                                // 地址数据
            userInfo: '',                                                                                       // 用户信息
            time: '',                                                                                           // 时间
            date: '',                                                                                           // 日期
            week: '',                                                                                           // 星期
            src: null,
            coordinate: {},                  //地址
            cameraImgFlag: null,              // 是否拍照，非相册，drawWatermarkCancleFlag为true,cameraImgFlag 为true添加现场拍照水印
            fifthGLocationFailStatus: 'N', // 是否是5G网络下定位报错【getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF】时录入的数据 Y是 N否
            delImgFlag: true,//删除加确认以及连续点击
            moduleTypeConversion: "",//市场活动业务场景使用。自主转换图片类型中文值【拍照或手机相册选择会跳出应用返回时会清掉目前使用的值列表类型共享数据，为了水印上正常显示内容则组件里先转换一下】
            actualUseModuleName: "",//真正使用的useModuleName。如果是市场活动marketActivityFlag为true需要单独根据值列表获取值moduleTypeConversion。否则使用原本的useModuleName。
            pageRoot: null,
            scaleRatio: 1,
            startPainting: false,
            picSourceList: [],
            disabledAddImgFlag: false, // 是否禁用点击新建按钮
            idSet: new Set(),
            aiUseBigUrl: '', //AI使用 用于保存未加水印原图的url
            imageDataShowNum: 3,  // 图片列表默认显示数量
            imageShowAllFlag: false, // 图片列表是否展示全部，默认为false，即展示三张
        }
    },
    props: {
        isZlFlag: {
            type: Boolean,
            default: false
        }, // 是否志玲接口
        multiWatermark: {
            type: Boolean,
            default: false
        }, // 是否多张加水印
        showProp: {
            type: Number,
            default: 1
        },
        quality: {
            type: Number,
            default: 0.9
        },
        uploadType: {
            type: String,
            default: 'cos'
        },                          // 上传类型，默认cos
        imgKey: {
            type: String,
            default: 'attachmentPath'
        },                          // 要展示的原图片路径字段/图片cosKey
        imgSmallKey: {
            type: String,
            default: 'smallurl'
        },                          // 要展示的缩略图图片路径字段/图片cosKey
        parentId: {
            type: String,
            required: true
        },                          // 附件头Id
        moduleType: {
            type: String,
            default: 'noType',
            required: true
        },                          // 附件所属模块
        delFlag: {
            type: Boolean,
            default: false
        },                          // 是否可删除，点击删除图标会派发delete事件
        // @add by 谭少奇 2024/12/25 历史上传是否可删除
        historyDelFlag: {
            type: Boolean,
            default: true
        },
        newFlag: {
            type: Boolean,
            default: false
        },                          // 是否可添加
        camera: {
            type: Boolean,
            default: true
        },                          // 是否支持拍照上传
        album: {
            type: Boolean,
            default: true
        },                           // 是否支持从相册中选择上传
        pathKeyArray: {
            type: Array              // 从后端接口直接获取到的key值
        },
        realDeleteFlag: {
            type: Boolean,
            default: true
        },
        noGetImgKeyList: {
            type: Boolean,
            default: false
        },
        moduleName: {
            type: String,
            required: false,
            default: ''             // 页面应用场景名称
        },
        useModuleName: {
            type: String,
            required: false,
            default: ''             // 页面应用模块名称
        },
        watermarkText: {
            type: String,
            default: ''             // 页面应用场景名称
        },
        rootId: {
            type: String,
            default: ''
        },                          // 来源id
        drawWatermarkCancleFlag: {
            type: Boolean,
            default: false
        },                           // 选择相册图片时是否需要取消水印[默认加水印，一张一张的选，设置为false时可以从相册一次添加多张不加水印上传],为true则不加水印,false则加水印
        addressWaterMark: {          // 根据业务需要地址水印可传入具体值
            type: String,
            default: ''
        },
        dataSourceShowFlag: {           //是否显示照片来源
            type: Boolean,
            default: false
        },
        createdShowFlag: {              //是否显示上传时间
            type: Boolean,
            default: false
        },
        goCameraFlag: {
            type: Boolean,
            default: true
        },
        continueFlag: {
            type: Boolean,
            default: false
        },
        isCount:{ //是否限制数量
            type: Boolean,
            default: false
        },
        addLength:{ //允许上传的数量
            type: Number
        },
        picTypeList: { //市场活动使用。需要不区分业务场景，查看大图时根据类型衔接滑动
            type: Array,
        },
        marketActivityFlag: {
            type: Boolean,
            default: false
        },
        maxCount: {
            type: Number
        },
        /**
         * 控制拍照,是否加水印
         * 为true则加水印,为false则不加水印
         */
        enableCameraWatermark: {
            type: Boolean,
            default: true
        },
        /**
         * 用于控制市场活动上下区域后端添加水印透明度
         * 默认取0.4
         */
        marketActAlpha: {
            type: String,
            default: '0.4'
        },
        /**
         * 用于控制市场活动中心区域后端添加水印透明度
         * 默认取0.1
         */
        marketActAlphaCentre: {
            type: String,
            default: '0.1'
        },
        /**
         * 用于控制市场活动中心区域后端添加水印透明度
         * 默认取0.15
         */
        marketActshadeAlpha: {
            type: String,
            default: '0.15'
        },
        aiUse: {
            type: Boolean,
            default: false
        },
        filePathKey: { // 图片上传指定路径
            type: String,
            default: ''
        },
        // 陈列协议定制化水印
        displayCustomizedWatermark: {
            type: Boolean,
            default: false
        },
        source: {
            type: String
        },
        cosDelete: {
            type: Boolean,
            default: true
        },
        noInitImgList: { // 是否需要初始化图片列
            type: Boolean,
            default: false
        }
    },
    components: {LinkPainter},
    watch: {
        /**
         * 监听附件头id参数
         * <AUTHOR>
         * @date 2020-09-08
         * @param val
         */
        pathKeyArray(val) {
            if (this.noGetImgKeyList || val) {
                this.imgList = this.pathKeyArray;
            } else {
                this.getImgKeyList()
            }
        },
    },
    computed: {
        /**
         * 处理图片数组数据
         * <AUTHOR>
         * @date 2020-08-13
         */
        imgData() {
            this.imgList.forEach(async (item) => {
                let imgUrl = ''
                // 如果是上传中,则使用临时文件
                if (item.operator === 'uploading') {
                    imgUrl = item.tempPath;
                } else {
                    // 如存在这个临时路径字段,则使用临时路径
                    if (item.tempPath) {
                        imgUrl = item.tempPath
                    } else {
                        // 否则使用网络路径
                        imgUrl = item.smallurl;
                        if(item.uploadType === 'cos' && imgUrl.indexOf(this.$env.cosUploadUrl) < 0){
                            imgUrl = this.$image.getSignedUrl(item[this.imgSmallKey])
                        }
                    }
                }
                this.$set(item, 'imgUrl', imgUrl);
            });
            this.addImages(this.imgList)
            return this.imgList
        }
    },
    async created() {
        // 需要地址信息，则开启定位监听
        if ((!this.drawWatermarkCancleFlag || this.camera) && this.$utils.isEmpty(this.addressWaterMark)) {
            if (!this.noInitImgList) this.$utils.showLoading();
            if (!this.$locations.getLocatonChangeFlag()) {
                await this.$locations.onLocationChange();
            }
            if (!this.noInitImgList) this.$utils.hideLoading();
        }
    },
    async mounted() {
        // 获取机型
        // 如果是开发者工具,platform返回的永远都是devtools,使用system字段去判断
        if (this.$device.systemInfo.platform === 'devtools') {
            this.isAndroid = /Android/ig.test(this.$device.systemInfo.system)
        } else {
            this.isAndroid = this.$device.systemInfo.platform === 'android';
        }
        if (!this.noInitImgList) {
            // 如果key值不存在则调用附件查询接口查询
            if (this.noGetImgKeyList || !this.$utils.isEmpty(this.pathKeyArray)) {
                this.imgList = this.pathKeyArray;
                this.initImglist();
            } else {
                this.getImgKeyList()
            }
        }
        this.dateTime = await this.getTimestamp();
        this.time = this.$date.format(this.dateTime, 'HH:mm');
        this.date = this.$date.format(this.dateTime, 'YYYY.MM.DD');
        this.week = '星期' + '日一二三四五六'.charAt(this.dateTime.getDay());
        this.userInfo = Taro.getStorageSync('token').result;
        this.moduleTypeConversion = await this.$lov.getNameByTypeAndVal('FEEDBACK_PHOTO_TYPE', this.moduleType);

        const storeLov = this.$store.getters['lov/getLov'] || {}
        this.picSourceList = storeLov['PIC_SOURCE'] || []
        if (this.picSourceList.length === 0) {
            const picSourceList = await this.$lov.getLovByType('PIC_SOURCE')
            storeLov['PIC_SOURCE'] = picSourceList
            this.$store.commit('lov/setLov', storeLov)
            this.picSourceList = picSourceList
        }
        this.idSet = new Set()
        this.$on('paintImageSuccess', (data) => {
            console.log('paintImageSuccess', data);
            if (!this.idSet.has(data.id)) {
                this.idSet.add(data.id)
                this.executeUpload(data)
            }
        })
        this.$on('uploadListFinish', async () => {
            console.log('uploadListFinish');
            this.wxImageList.uploadIndex = 0
            this.wxImageIndex = 0
            this.wxImageList.list = []
            this.startPainting = false
        })

        this.$on('paintImageFinish', () => {
            console.log('paintImageFinish');
            this.loadingShow = false
            this.painterFlag = false
        })
        // 不涉及审批查看问题与终端开户时照片问题 初步排查 先采用注释掉
        // this.emitImgUploadSuccess();
        this.pageRoot = this['@@link-page']
    },
    destroyed() {
        this.imgList = [];
        // 停止位置跟踪
        this.$locations.stopLocationUpdate();
    },
    inject: ['@@link-page'],
    methods: {
        /**
         * 图片展开/收起切换
         * <AUTHOR>
         * @date 2022/9/12
         */
        switchImgShowNum() {
            this.imageShowAllFlag = !this.imageShowAllFlag;
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/4/1
         * @methods getTimestamp
         * @para
         * @description 获取时间戳
         */
        async getTimestamp() {
            const timeStamp = await this.$utils.getServerTime();
            let date = new Date(timeStamp);
            if(date.toString().indexOf('GMT+0800') === -1) {
                date = this.getBeijingtime(date)
            }
            return date
        },
        /**
         * 转换北京时间
         * <AUTHOR>
         * @date 2023/3/17
         */
        getBeijingtime(date) {
            //获得当前运行环境时间
            let d = new Date();
            let currentDate = new Date(date);
            let tmpHours = currentDate.getHours();
            let tmpMinutes = currentDate.getMinutes();
            //算得时区
            var time_zone = -d.getTimezoneOffset() / 60;
            //获取时差
            let currentHouse = Math.floor(time_zone)
            let currentMinutes = (time_zone - currentHouse) * 60
            //设置北京时间
            currentDate.setHours(tmpHours - currentHouse + 8);
            currentDate.setMinutes(tmpMinutes - currentMinutes)

            return currentDate;
        },
        async getAddressAgain() {
            this.$utils.showLoading('获取定位信息中...');
            await this.$locations.onLocationChange();
            setTimeout(() => {
                let coordinate = this.$locations.getLocationChange();
                if (this.$utils.isNotEmpty(coordinate.latitude)
                    && (this.$utils.isNotEmpty(this.coordinate.nowTime) && this.coordinate.nowTime !== coordinate.nowTime)) {
                    this.coordinate = coordinate;
                    this.$message.success('获取定位信息成功');
                } else {
                    this.$message.warn('获取定位信息失败，请确认定位权限是否授权');
                }
                this.$utils.hideLoading();
            }, 1050);
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/4/1
         * @methods onImgOK
         * @description 水印图渲染成功后
         */
        async onImgOK(rule) {
            const painterImage = this.wxImageList.list[this.wxImageIndex]
            console.log('压缩前置动作完成，开始上传');
            this.$emit('paintImageSuccess', {source: this.src, id: painterImage.id, rule, width: this.painterWidth, height: this.painterHeight})
            this.painterFlag = false
            // 继续绘制
            if (this.wxImageIndex < this.wxImageList.list.length - 1) {
                this.wxImageIndex++;
                this.dataPrinter();
            } else {
                this.$emit('paintImageFinish')
            }
        },

        async executeUpload({source, id, rule, onlyUpload = false, width, height}) {

            /**
             * 上传图片时加水印，只能使用put请求，参数格式只能是字符串，不能是filePath
             * <AUTHOR>
             * @date    2022.7.16 12:31
             */
            const body = await new Promise((resolve, reject) => {
                const wxfs = this.$taro.getFileSystemManager();
                wxfs.readFile({
                    filePath: source,
                    success: function (res) {resolve(res.data)},
                    fail: err => reject(err),
                });
            })

            let imgId = !!id ? id : await this.$newId();
            let uploadImageOptions = {
                filePath: '',                              // 上传图片路径
                formData: {
                    headId: this.parentId,                 // 上传图片headId
                    moduleType: this.moduleType            // 上传图片类型
                },
                id: imgId,
                rootId: this.rootId,
                Key: null,
                smallKey: null,
                smallImgUrl: '',

                /*使用put请求，post请求无法在上传图片的时候贴水印*/
                cosMethod: !!rule ? 'putObject' : 'postObject',
                /*上传图片时水印参数设置*/
                processCosOption: (cosOption) => {
                    const Headers = !!rule ? {'Pic-Operations': JSON.stringify({"rules": [{"fileid": uploadImageOptions.Key, rule},]})} : {}
                    Headers['Content-Type'] = 'image/jpeg'
                    const newCosOption = {
                        ...cosOption,
                        Headers,
                    }
                    !!rule && (newCosOption.Body = body);
                    return newCosOption
                },
            };
            uploadImageOptions.Key = this.filePathKey + this.getRandFileName(source);
            uploadImageOptions.smallKey = this.getRandFileName(source);
            uploadImageOptions.smallImgUrl = source;
            uploadImageOptions.filePath = source;
            uploadImageOptions.bigurl = '';
            uploadImageOptions.dataSource = this.cameraImgFlag ? 'Live' : 'Album';
            uploadImageOptions.fifthGLocationFailStatus = this.fifthGLocationFailStatus; // 是否是5G网络下定位报错【getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF】时录入的数据 Y是 N否
            if(!!rule){
                uploadImageOptions.longitude = this.coordinate.longitude;
                uploadImageOptions.latitude = this.coordinate.latitude;
                uploadImageOptions.address = this.addressDataFull;
            }
            if(!onlyUpload) {
                if (!id) {
                    this.imgList.unshift({
                        id: uploadImageOptions.id,
                        operator: 'uploading',
                        attachmentPath: uploadImageOptions.Key,
                        // tempPath: uploadImageOptions.smallImgUrl,
                        progress: 8
                    });
                } else {
                    const index = this.imgList.findIndex((img) => img.id === imgId)
                    if (index !== -1) {
                        this.imgList[index].attachmentPath = uploadImageOptions.Key;
                        this.imgList[index].progress = 60
                    }
                }
                this.wxImageList.uploadIndex++
            }
            if(onlyUpload) {
                await this.$image.UploadImg(uploadImageOptions).then(
                    (data) => {
                        if (this.$utils.isNotEmpty(data)) {
                            this.aiUseBigUrl = data.Location.split('/')[1]
                        }
                    }
                );
                return true;
            }
            // 将原图赋值到附件表bigUrl上
            if(this.$utils.isNotEmpty(this.aiUseBigUrl)) {
                uploadImageOptions.bigurl = this.aiUseBigUrl;
                // 置空
                this.aiUseBigUrl = '';
            }
            this.$image.insertUploadImgRecord(uploadImageOptions, this.isZlFlag)
                .then(async (data) => {
                    this.idSet.delete(uploadImageOptions.id)
                    if (data.success) {
                        // 更新对象字段
                        this.updateImg(this.imgList, data.newRow)
                        this.$emit('uploadImgPromise', {newRow: [data.newRow], imgList: this.imgList});
                        this.emitImgUploadSuccess(this.imgList)
                        this.saveImgOkOnce = false
                        let that = this
                        setTimeout(function () {
                            if (that.isCount && that.imgList.length === that.addLength) {
                                that.$message.warn('照片已达上传上限');
                                return;
                            }
                            if (that.continueFlag && that.cameraImgFlag) {
                                that.chooseWxImage('camera')
                            }
                        }, 100)
                    } else {
                        this.painterFlag = false
                        this.saveImgOkOnce = false
                        this.loadingShow = false;
                        this.$showError('网络异常，请稍后重新上传');
                    }
                    this.emitUploadListFinish()
                }).catch((e) => {
                    this.idSet.delete(uploadImageOptions.id)
                    // 移除列表中的图片
                    this.removeImg(this.imgList, uploadImageOptions.id)
                    this.emitUploadListFinish()
                });
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/4/1
         * @methods imgErr
         * @description 水印图渲染失败
         */
        imgErr(e) {
            console.log('imgErr', e)
            this.loadingShow = false;
        },
        async openSet() {
            this.$refs.dialog.hide();
            // let userLocation = await this.$locations.openSetting();
            await this.getAddressAgain();
        },
        /**
         * 添加图片，直接调用imgService服务
         * <AUTHOR>
         * @date 2020-07-02
         */
        async addImg() {
            // 禁用添加图片按钮
            if (this.disabledAddImgFlag) { return; }
            let beforeRes = {};
            this.$emit('beforeAddImg', async (res) => {
                beforeRes = res;
            });
            if (beforeRes.tipFlag) {
                await this.$dialog.confirm(beforeRes.tip, '提示', {
                    cancelButton: false,
                    confirmText: beforeRes.confirmText
                });
            }
            this.disabledAddImgFlag = true;
            // 释放添加图片按钮
            setTimeout(() => {
                this.disabledAddImgFlag = false;
            }, 1000);
            // 选择相册需要加水印 或者 拍照，则需要获取定位信息
            if (!this.drawWatermarkCancleFlag || this.camera) {
                if (!this.$utils.isEmpty(this.addressWaterMark)) {
                    this.addressDataFull = this.addressWaterMark;
                } else {
                    const currentTime = (new Date()).getTime();
                    // 1、定位信息为空
                    // 2、或者不为getLocation接口获取，且正确获取定位信息，且时间大于10秒
                    // 3、或者为getLocation接口获取，且正确获取定位信息，且时间大于30秒
                    // ==> 则重新获取
                    if(this.$utils.isEmpty(this.coordinate.latitude)
                        || (this.$utils.isEmpty(this.coordinate.getLocationFlag) && currentTime - this.coordinate.nowTime > 10 * 1000)
                        || (this.$utils.isNotEmpty(this.coordinate.getLocationFlag) && currentTime - this.coordinate.nowTime > 30 * 1000)) {
                        this.coordinate = await this.getWxLocation();
                    }
                    // 如果获取的定位信息为空，则不进行下一步
                    this.disabledAddImgFlag = false;
                    if (this.$utils.isEmpty(this.coordinate.latitude)) {
                        return;
                    }
                    let location = this.$store.getters['coordinate/getCoordinate'];
                    let distance = this.getDistance(location, this.coordinate);
                    if (!this.addressData || distance > 50) {
                        this.$store.commit('coordinate/setCoordinate', this.coordinate);
                        let address = await reverseTMapGeocoder(this.coordinate.latitude, this.coordinate.longitude, 'cos照片加水印');
                        this.addressData = address['originalData'].result.addressComponent;
                        this.addressDataFull = address['originalData'].result.formatted_address;
                    }
                }
            }
            if (this.goCameraFlag) {
                // 如果同时要求拍照和相册
                if (this.camera && this.album) {
                    wx.showActionSheet({
                        itemList: ['从相册选择', '拍照'],
                        success: (res) => {
                            if (!res.cancel) {
                                if (res.tapIndex === 0) {
                                    // 相册
                                    this.chooseTakePhotoWay('album');
                                } else if (res.tapIndex === 1) {
                                    // 拍照
                                    this.chooseTakePhotoWay('camera');
                                }
                            }
                        }
                    })
                } else if (this.camera) {
                    this.chooseTakePhotoWay('camera');
                } else if (this.album) {
                    this.chooseTakePhotoWay('album');
                }
            } else {
                this.$emit('notGoCamera');
            }
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/4/1
         * @methods chooseTakePhotoWay
         * @para
         * @description 是否需要添加水印
         */
        chooseTakePhotoWay(type) {
            // 如果是通过相册上传,并且 不加水印
            if (type === 'album' && this.drawWatermarkCancleFlag) {
                // 则直接上传
                this.chooseAlbumKristenScott()
            } else {
                // 否则前端加水印并上传
                this.chooseWxImage(type)
            }
        },
        /**
         @param tempFilePath 图片地址
         procFlag: 是否后台处理
         @desc:
         @author: wangbinxin
         @date 2022-05-17 19-38
         **/
        async setUploadOption(tempFilePath, procFlag) {
            let imgId = await this.$newId();
            let uploadImageOptions = {
                id: imgId,
                rootId: this.rootId,
                dataSource: 'Album',
                fifthGLocationFailStatus: this.fifthGLocationFailStatus, // 是否是5G网络下定位报错【getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF】时录入的数据 Y是 N否
            };
            let options = {};
            if (procFlag) {
                options = {
                    uploadType: 'cos', // 上传类型，默认cos
                    headId: this.parentId,                 // 上传图片headId
                    moduleType: this.moduleType,            // 上传图片类型
                    source: 'corpwx', // 附件上传来源，来自企业微信小程序
                    regionId: imgConfig.Region,                                     // 地域
                    bucketId: imgConfig.Bucket,                                      // 存储桶
                    attachmentPath: this.getRandFileName(tempFilePath),
                    marketActivityFlag: this.marketActivityFlag ? 'Y' : 'N',
                    city: this.addressData.city,
                    moduleName: this.moduleName,
                    userInfo: this.userInfo.postnName + '-' + this.userInfo.firstName,
                    actualUseModuleName: this.marketActivityFlag ? this.moduleTypeConversion : this.useModuleName,
                    addressDataFull: this.addressDataFull
                };
                options.smallurl = options.attachmentPath + '/suoluetu'; // 缩略图key
                if (this.watermarkText) {
                    options.watermarkText = this.watermarkText;
                }
                // 清除为空参数
                for (let key in options) {
                    if (!options[key]) {
                        delete options[key];
                    }
                }
            } else {
                options = {
                    filePath: tempFilePath,                            // 上传图片路径
                    formData: {
                        headId: this.parentId,                 // 上传图片headId
                        moduleType: this.moduleType            // 上传图片类型
                    },
                    Key: this.filePathKey + this.getRandFileName(tempFilePath),
                    smallKey: this.getRandFileName(tempFilePath),
                    smallImgUrl: tempFilePath,
                };
            }
            return Object.assign({}, uploadImageOptions, options);
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/4/1
         * @methods chooseAlbumKristenScott
         * @para
         * @description 上传图片，不需要加水印，原图已经有水印,可传多张图片
         */
        async chooseAlbumKristenScott() {
            // 选择图片
            let chooseImageOptions = {
                sourceType: ['album'],
                count: 9
            };
            const wxImages = await this.$image.chooseImage(chooseImageOptions);
            if (!wxImages) {
                return;
            }
            if (wxImages.tempFilePaths.length === 0) {
                return;
            }
            // 限制上传的图片比例
            for (let i = 0; i < wxImages.tempFilePaths.length; i++) {
                const src = wxImages.tempFilePaths[i]
                const {success, width, height} = await this.compressImage(src);
                wxImages.tempFiles[i].width = width
                wxImages.tempFiles[i].height = height
                // 校验没有通过,则移除该图片
                if (!success) {
                    wxImages.tempFilePaths.splice(i, 1)
                    i--
                }
            }
            try {
                const newList = await Promise.all(wxImages.tempFilePaths.map(async (tempFilePath, index) => {
                    // 设置图片上传参数
                    let uploadImageOptions = await this.setUploadOption(tempFilePath, false);
                    this.imgList.unshift({
                        id: uploadImageOptions.id,
                        attachmentPath: uploadImageOptions.Key,
                        operator: 'uploading',
                        progress: 5
                    })

                    if (!!this.maxCount) {
                        if (this.imgList.length > this.maxCount) {
                            this.imgList.splice(this.maxCount);
                            return {}
                        }
                    }

                    try {
                        // 上传图片
                        let data = await this.$image.insertUploadImgRecord(uploadImageOptions);
                        if (data.success) {
                            this.emitImgUploadSuccess()
                            return data.newRow;
                        }
                    } catch (e) {
                        // 移除列表中的图片
                        this.removeImg(this.imgList, uploadImageOptions.id)
                        return {}
                    }
                }))

                // 所有图片上传完成之后，单次上传数量超过3张，需要展开显示
                if (newList.length > 3 && !this.imageShowAllFlag) {
                    this.imageShowAllFlag = true;
                }

                for (let i = 0; i < this.imgList.length; i++) {
                    let img = this.imgList[i];

                    for (let j = 0; j < newList.length; j++) {
                        const newImg = newList[j];
                        if (img.attachmentPath === newImg.attachmentPath) {
                            // 删除无用字段
                            delete img['progress']
                            delete img['operator']
                            delete img['smallurl']
                            // 赋值对象
                            Object.keys(newImg).forEach((key) => {
                                this.$set(img, key, newImg[key]);
                            })
                            break;
                        }
                    }
                }
                this.painterFlag = false;
                this.saveImgOkOnce = false;
            } catch (e) {
                await this.$taro.showModal(`上传失败：${String(e)}`)
            } finally {
                this.loadingShow = false;
            }
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/4/1
         * @methods chooseWxImage
         * @para
         * @description 前端加水印及图片上传
         */
        async chooseWxImage(sourceType) {
            this.dateTime = await this.getTimestamp();
            this.time = this.$date.format(this.dateTime, 'HH:mm');
            this.date = this.$date.format(this.dateTime, 'YYYY.MM.DD');
            this.week = '星期' + '日一二三四五六'.charAt(this.dateTime.getDay());
            // 选择图片属性
            let arr = []
            arr.push(sourceType)
            const chooseImageOptions = {
                // count: this.multiWatermark ? 9 : 1, // 默认上传张数9张
                //isCount-限制数量为 limLength-9张
                count: this.multiWatermark ? this.isCount ? (this.limLength - this.imgData.length) > 9 ? 9 : (this.limLength - this.imgData.length) : 9 : 1, // 默认上传张数9张
                sizeType: ['compressed'],
                sourceType: arr,
            }
            console.log('chooseImageOptions>>>>>>>>>', chooseImageOptions);
            this.cameraImgFlag = sourceType === 'camera';
            const wxImages = await this.$image.chooseImage(chooseImageOptions);
            // 如果没有图片了,则无需继续执行
            if (!wxImages) {
                return;
            }
            if (wxImages.tempFilePaths.length === 0) {
                return;
            }
            // 校验图片尺寸,只保留
            for (let i = 0; i < wxImages.tempFilePaths.length; i++) {
                const src = wxImages.tempFilePaths[i]
                const {success, width, height} = await this.compressImage(src);
                wxImages.tempFiles[i].width = width
                wxImages.tempFiles[i].height = height
                // 校验没有通过,则移除该图片
                if (!success) {
                    wxImages.tempFilePaths.splice(i, 1)
                    i--
                }
            }
            // 选择完图片后立即设置对象的id
            for (let i = 0; i < wxImages.tempFilePaths.length; i++) {
                wxImages.tempFiles[i].id = await this.$newId();
                const tempFile = wxImages.tempFiles[i];
                const base64 = wx.getFileSystemManager().readFileSync(wxImages.tempFilePaths[i], 'base64');
                this.imgList.unshift({
                    base64: base64,
                    id: tempFile.id,
                    operator: 'uploading',
                    progress: 15,
                });
                // 将需要绘制的图片需要的信息,加入到数组中去
                this.wxImageList.list.push({
                    id: tempFile.id,
                    width: tempFile.width,
                    height: tempFile.height,
                    path: wxImages.tempFilePaths[i],
                    size: tempFile.size
                });
            }
            if (!!this.maxCount) {
                if (this.imgList.length > this.maxCount) {
                    const removedImgList = this.imgList.splice(this.maxCount);
                    // 根据id移除多余的对象
                    removedImgList.forEach((img) => {
                        const index = this.wxImageList.list.findIndex((wxImage) => wxImage.id === img.id);
                        if (index !== -1) {
                            this.wxImageList.list.splice(index);
                        }
                    })
                }
            }

            // 如果都被移除了,则直接返回
            if (this.wxImageList.list.length === 0) {
                return;
            }
            // 如果是拍照,且不加水印,则直接上传图片
            if (this.cameraImgFlag && !this.enableCameraWatermark) {
                const img = this.wxImageList.list[0];
                this.$emit('paintImageSuccess', {source: img.path, id: img.id});
                this.$emit('paintImageFinish');
            } else {
                this.dataPrinter();
            }
        },
        async dataPrinter() {
            const painterImage = this.wxImageList.list[this.wxImageIndex]
            this.src = painterImage.path;
            this.rawData.size = painterImage.size;
            // 设置画布的宽高
            this.painterWidth = parseInt(painterImage.width * this.scaleRatio);
            this.painterHeight = parseInt(painterImage.height * this.scaleRatio);

            // 前端上传,需要加水印
            console.log(`%c执行:${this.cameraImgFlag ? '拍照' : '相册'}-前端上传及加水印`, 'color: red')
            // 如果是安卓,则使用微信的压缩
            if (this.isAndroid) {
                const tmpFile = await this.compressApi(this.src);
                this.src = tmpFile.tempFilePath;
            }
            this.saveImgOkOnce = true;
            this.painterFlag = true;
            // AI识别添加原图上传
            if(this.aiUse) {
                const img = this.wxImageList.list[0];
                await this.executeUpload({source: img.path, id: img.id, onlyUpload: true});
            }
            this.painterPhone();
        },
        /**
         @param fileurl：图片url
         @desc: 安卓根据微信api压缩图片
         @author: wangbinxin
         @date 2022-05-30 15-41
         **/
        compressApi(fileurl) {
            return new Promise(async (resolve, reject) => {
                // 获取不需要压缩的机型参数
                let noCompressPhoneModel = await this.$utils.getCfgProperty('noCompressPhoneModel');
                noCompressPhoneModel = noCompressPhoneModel.split('|');
                // 当前机型不需要压缩，直接返回
                if (this.$utils.isNotEmpty(noCompressPhoneModel)
                    && noCompressPhoneModel.includes(this.$device.systemInfo.model)) {
                    resolve({tempFilePath: fileurl});
                } else {
                    var quality = parseFloat(this.quality) * 100;
                    const startTime = (new Date()).getTime();
                    wx.compressImage({
                        src: fileurl, // 图片路径
                        quality: parseInt(quality), // 压缩质量
                        success: (res) => {
                            const endTime = (new Date()).getTime();
                            if (wx.canIUse('reportPerformance')) {
                                wx.reportPerformance(2002, endTime - startTime);
                            }
                            resolve(res);
                        },
                        fail: (err) => {
                            if (err.errno === 3) {
                                this.$dialog({
                                    title: '提示',
                                    content: '请确认【手机设置】-【企业微信】照片存储权限管理是否打开？',
                                    cancelButton: false
                                });
                            }
                            console.log('压缩图片失败：', err);
                            this.imgList.splice(this.imgList.length - 1, 1);
                            this.$emit('paintImageFinish');
                            reject(err)
                        }
                    });
                }
            });
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/4/1
         * @methods getImageInfo
         * @para
         * @description 图片附件信息
         */
        async getImageInfo(filePath) {
            try {
                return await this.$taro.getImageInfo({src: filePath})
            } catch (e) {
                const msg = '获取图片信息失败';
                this.$showError(msg);
                throw e
            }
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/4/1
         * @methods compressImage
         * @para
         * @description 压缩图片
         */
        async compressImage(tempPath) {
            return new Promise(async (resolve) => {
                let {width, height} = await this.getImageInfo(tempPath);
                /**
                 @param 限制图片大小（不得小于500）
                 @desc:
                 @author: wangbinxin
                 @date 2022-06-23 15-37
                 **/
                width = this.showProp ? width * parseFloat(this.showProp) : width;
                height = this.showProp ? height * parseFloat(this.showProp) : height;
                if (width < 500 || height < 500) {
                    if (width < height) {
                        height = parseInt(500 / width * height);
                        width = 500;
                    } else {
                        width = parseInt(500 / height * width);
                        height = 500;
                    }
                }
                this.rawData.width = width;
                this.rawData.height = height;
                let pix = this.$device.systemInfo.pixelRatio < 3 ? 3 : this.$device.systemInfo.pixelRatio; // iphone6等手机屏占比过小
                if (height / (this.$device.systemInfo.screenHeight * pix) > 3 || height / width > 3) {
                    this.$message.warn(`图片比例过长，请处理图片后重新上传`);
                    this.$utils.hideLoading();
                    this.loadingShow = false;
                    resolve({success: false, width, height})
                }
                resolve({success: true, width, height});
            });
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/4/1
         * @methods painterPhone
         * @para
         */
        painterPhone() {
            let views;
            // 文字透明度
            let textAlpha = 1
            // 背景透明度
            let shadowAlpha = 0.3;
            if (this.marketActivityFlag) {
                views = [
                    {
                        text: this.cameraImgFlag ? this.watermarkText : null,
                        color: '#000000',
                        textAlpha: 0.06,
                        fontScale: 2.5,
                        position: 'center',
                    },
                    {
                        text: this.addressData.city,
                        position: 'top',
                    },
                    {
                        text: this.time,
                        fontScale: 1.5,
                        position: 'top',
                    },
                    {
                        text: this.date + ' ' + this.week,
                        position: 'top',
                    },
                    {
                        text: this.moduleName,
                        position: 'bottom',
                    },
                    {
                        text: `${this.userInfo.postnName}-${this.userInfo.firstName}`,
                        position: 'bottom',
                    },
                    {
                        text: this.useModuleName,
                        position: 'bottom',
                    },
                    {
                        text: this.addressDataFull,
                        position: 'bottom',
                    }
                ];
                textAlpha = 0.3;
                shadowAlpha = 0.1;
            } else if (this.displayCustomizedWatermark) {
                views = [
                    {
                        text: this.addressData.city + ' ' + this.time + ' ' +  this.date + ' ' + this.week,
                        position: 'top',
                    },
                    {
                        text: `${this.moduleName} - ${this.userInfo.postnName} - ${this.userInfo.firstName}`,
                        position: 'bottom',
                    },
                    {
                        text: this.useModuleName,
                        position: 'bottom',
                    },
                    {
                        text: this.addressDataFull,
                        position: 'bottom',
                    }
                ];
                textAlpha = 0.3;
                shadowAlpha = 0.2;
            } else {
                views = [
                    {
                        text: this.cameraImgFlag ? this.watermarkText : null,
                        // textAlpha: 0.1,
                        position: 'center',
                    },
                    {
                        text: this.addressData.city,
                        position: 'top',
                    },
                    {
                        text: this.time,
                        fontScale: 1.5,
                        position: 'top',
                    },
                    {
                        text: this.date + ' ' + this.week,
                        position: 'top',
                    },
                    {
                        text: this.moduleName,
                        position: 'bottom',
                    },
                    {
                        text: `${this.userInfo.postnName}-${this.userInfo.firstName}`,
                        position: 'bottom',
                    },
                    {
                        text: this.useModuleName,
                        position: 'bottom',
                    },
                    {
                        text: this.addressDataFull,
                        position: 'bottom',
                    }
                ]
            }
            const rule = cosWatermark({
                height: this.painterHeight,
                width: this.painterWidth,
                views: views,
                textAlpha:  textAlpha,
                shadowAlpha: shadowAlpha,
            })
            this.onImgOK(rule)
        },
        /**
         * 获取图片key
         * <AUTHOR>
         * @date 2020-07-09
         */
        async getImgKeyList() {
            if (!this.parentId) {
                return
            }
            const that = this
            this.$http.post(`${this.isZlFlag?this.$env.appURL+'/':''}action/link/attachment/queryByExamplePage`, {
                //uploadType: this.uploadType,
                pageFlag: false,
                sort: 'created',
                order: 'desc',
                moduleType: this.moduleType, // 所属模块
                headId: this.parentId,
                queryFields: 'id,uploadType,attachmentPath,moduleType,headId,dataSource,created,smallurl',
            }).then((data) => {
                if (data.success) {
                    that.imgList = data.rows;
                    this.$emit('initSuccess', that.imgList)
                    this.initImglist();
                }
            })
        },
        /**
         * 首次进入时初始化已有图片
         * <AUTHOR>
         * @date 2022年7月6日
         */
        initImglist() {
            this.imgList.forEach(async (item) => {
                let imgUrl = ''
                if (item.operator === 'new') {
                    imgUrl = item.smallurl;
                } else {
                    imgUrl = item.uploadType === 'cos' ? this.$image.getSignedUrl(item[this.imgSmallKey]) : item.smallurl;
                }
                this.$set(item, 'imgUrl', imgUrl);
            });
            this.emitImgUploadSuccess();
        },
        /**
         * 点击图片预览，直接调用imgService服务
         * <AUTHOR>
         * @date 2020-07-09
         * @param index 当前图片对象所属下标
         * @param img 当前图片对象
         */
        async clickImg(index, img) {
            // 图片上传中，不允许预览
            if (img.operator === 'uploading') {
                return;
            }
            // 传入图片类型list，则按照创建时间升序获取对应父对象所有的图片附件信息，并按照图片类型分组预览全部图片
            if (this.picTypeList) {
                if (!this.parentId) {
                    return;
                }
                const data = await this.$http.post('action/link/attachment/queryByExamplePage', {
                    sort: 'created',
                    order: 'desc',
                    headId: this.parentId,
                    rows: 2000,
                    queryFields: 'uploadType,attachmentPath,moduleType'
                });
                // 若是上传类型为cos，则需要拼接cos存储桶访问前缀
                const groupedData = data.rows.map((item) => {
                    let qdhyFlag = /qdhy/i.test(item.attachmentPath);
                    return {
                        uploadType: item.uploadType,
                        attachmentPath: item.attachmentPath,
                        imgUrl: item.uploadType === 'cos' && !qdhyFlag ? this.$image.getSignedUrl(item.attachmentPath)  + '/thumbnail' : item.attachmentPath,
                        moduleType: item.moduleType
                    };
                });
                // 根据图片类型重新分组拼接
                let pictureAll = [];
                for (let i = 0; i < this.picTypeList.length; i++) {
                    let pic = this.picTypeList[i];
                    const filterArr = groupedData.filter((item1) => item1.moduleType === pic);
                    if (!this.$utils.isEmpty(filterArr)) {
                        pictureAll = pictureAll.concat(filterArr);
                    }
                }
                // 获取被点击图片对象在分组拼接后图片数组的位置
                let index = pictureAll.findIndex((imgList) => imgList.attachmentPath === img.attachmentPath);
                // 提取图片路径
                this.originalPath = pictureAll.map((pic) => { return pic.imgUrl; });
                // 预览图片
                this.$image.previewImages({current: this.originalPath[index], urls: this.originalPath});
            } else {
                // 提取原图路径
                this.originalPath = this.imgList.map((img) => {
                    return img.uploadType === 'cos' ? img[this.imgKey].startsWith('https://') ? img[this.imgKey] : this.$image.getSignedUrl(img[this.imgKey])  + '/thumbnail' : img.attachmentPath;
                });
                // 预览图片
                this.$image.previewImages({current: this.originalPath[index], urls: this.originalPath});
            }
        },
        /**
         * 删除图片，直接调用imgService服务
         * <AUTHOR>
         * @param img 图片对象
         * @param index 当前图片对象所属下标
         */
        async delImg(img, index) {
            if (!this.delImgFlag) return
            const that = this;
            let canDelFlag = true;
            let delTip = '';
            this.$emit('canDel', img, val => {
                canDelFlag = val ? val.canDelFlag : true,
                delTip = val && !val.canDelFlag ? val.delTip : '';
            });
            if (!canDelFlag) {
                this.$showError(delTip);
                return;
            }
            this.delImgFlag = false
            try {
                this.$dialog({
                    title: '提示',
                    content: () => (<view style="padding: 5px 0;">确定要删除该图片吗？</view>),
                    cancelButton: true,
                    initial: true,
                    onConfirm: async () => {
                        that.$utils.showLoading('图片删除中...');
                        if (that.realDeleteFlag) {
                            const option = {id: that.imgList[index].id};
                            const result = this.cosDelete ? await that.$image.deleteImage(option, this.isZlFlag) : true;
                            if (result) {
                                that.imgList.splice(index, 1);
                                // that.tempFilePaths.splice(index, 1);
                                that.originalPath.splice(index, 1);
                                that.$emit('imgDeleteSuccess', that.imgList, img)
                            }
                            that.$utils.hideLoading()
                            that.delImgFlag = true;
                        } else {
                            that.$emit('softDelete', index, img)
                            that.delImgFlag = true;
                            that.$utils.hideLoading()
                        }
                    },
                    onCancel: () => {
                        that.delImgFlag = true;
                    }
                })
            } catch (e) {
                this.delImgFlag = true;
                this.$utils.hideLoading()
            } finally {

            }
        },
        /**
         * 删除全部图片
         * <AUTHOR>
         * 2022年8月17日
         */
        async delALLTypeImg() {
            if (!this.delImgFlag) return
            this.delImgFlag = false
            const that = this;
            let delImgList = []
            that.imgList.forEach((item)=>{
                delImgList.push({id:item.id})
            })
            try {
                if (that.realDeleteFlag) {
                    const result = await that.$image.deleteAllImage(delImgList, this.isZlFlag);
                    if (result) {
                        that.imgList = [];
                        that.originalPath = [];
                        that.$emit('imgDeleteSuccess', that.imgList,'delAll')
                    }
                    that.delImgFlag = true;
                } else {
                    that.delImgFlag = true;
                }
            } catch (e) {
                this.delImgFlag = true;
            } finally {

            }
        },
        /**
         * 上传图片key
         * <AUTHOR>
         * @date 2020-07-02
         * @description key的组成元素 时分秒——年月日——userId-模块Id-本地递增序列值
         */
        getRandFileName(filePath) {
            let extIndex = filePath.lastIndexOf('.');
            let extName = extIndex === -1 ? '' : filePath.substr(extIndex);
            let userInfo = Taro.getStorageSync('token').result;
            return `${this.parentId}-${this.$utils.uuid()}-${parseInt(Math.random() * 10000)}-${userInfo.id}-${this.$date.format(new Date(Date.now()), 'HH:mm:ss')}-${this.$date.format(new Date(Date.now()), 'YYYY.MM.DD')}${extName}`;
        },
        /**
         @param loca1 地址一，包含经纬度
         loca2 地址二，包含经纬度
         @desc: 通过经纬度计算距离
         @author: wangbinxin
         @date 2022-07-04 17-18
         **/
        getDistance(loca1, loca2) {
            let lat1 = loca1.latitude || 0;
            let lng1 = loca1.longitude || 0;
            let lat2 = loca2.latitude || 0;
            let lng2 = loca2.longitude || 0;
            var rad1 = lat1 * Math.PI / 180.0;
            var rad2 = lat2 * Math.PI / 180.0;
            var a = rad1 - rad2;
            var b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
            var r = 6378137;  //地球半径
            var distance = r * 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(rad2) * Math.pow(Math.sin(b / 2), 2)));
            return distance;
        },
        /**
         @desc: 获取位置信息
         @author: wangbinxin
         @date 2022-07-04 17-28
         **/
        async getWxLocation() {
            return new Promise(async (resolve) => {
                // 从定位监听中获取定位信息
                let coordinate = this.$locations.getLocationChange();
                // 定位信息为空，或者定位信息返回的时间是不变的
                if (this.$utils.isEmpty(coordinate.latitude)
                    || (this.$utils.isNotEmpty(this.coordinate.nowTime) && this.coordinate.nowTime === coordinate.nowTime)) {
                    // 关闭定位监听
                    await this.$locations.stopLocationUpdate();
                    // 尝试重新开始定位监听
                    await this.$locations.onLocationChange();
                    // 一秒钟之后再次尝试获取定位信息
                    setTimeout(async () => {
                        coordinate = this.$locations.getLocationChange();
                        // 依旧是获取不到定位信息，尝试使用getLocation接口获取定位信息
                        if (this.$utils.isEmpty(coordinate.latitude)
                            || (this.$utils.isNotEmpty(this.coordinate.nowTime) && this.coordinate.nowTime === coordinate.nowTime)) {
                            coordinate = await this.getLocation();
                            resolve(coordinate);
                        } else {
                            resolve(coordinate);
                        }
                    }, 1050);
                } else {
                    resolve(coordinate);
                }
            });
        },
        /**
         * 添加图片对象到link-page
         * <AUTHOR>
         * @date 2022/7/5
         * @param imgList 图片列表
         */
        addImages(imgList) {
            !!this.pageRoot && this.pageRoot.utils.addImages(imgList)
        },
        /**
         * 更新img对象字段
         * <AUTHOR>
         * @date 2022/7/5
         * @param imgList 图片列表
         * @param sourceImg 提供数据的图片对象
         */
        updateImg(imgList, sourceImg) {
            for (let i = 0; i < this.imgList.length; i++) {
                let img = this.imgList[i];
                const includeFlag = this.isZlFlag && sourceImg.attachmentPath.indexOf(img.attachmentPath) > -1?true:false;
                if (img.attachmentPath === sourceImg.attachmentPath || includeFlag) {
                    // 删除无用字段
                    delete img['progress']
                    delete img['operator']
                    delete img['smallurl']
                    // 赋值对象
                    Object.keys(sourceImg).forEach((key) => {
                        this.$set(img, key, sourceImg[key]);
                    })
                    break;
                }
            }
        },
        /**
         * 根据id移除列表中的img对象
         * <AUTHOR>
         * @date 2022/7/5
         * @param imgList 图片列表
         * @param id 要移除的对象id
         */
        removeImg(imgList, id) {
            // 移除列表中的图片
            const idx = imgList.findIndex((img) => img.id === id)
            if (idx !== -1) {
                imgList.splice(idx, 1)
            }
        },
        getPicSource(dataSource) {
            try {
                const dataSourceName = this.picSourceList.filter((item) => {
                    return item.val === dataSource
                })
                return dataSourceName.length > 0 ? dataSourceName[0].name : '';
            } catch (e) {
                console.log(e)
                return ''
            }
        },
        onImgLoad(e, img) {
            const src = e.currentTarget.dataset.src
            img.tempPath = src
        },
        /**
         * 触发imgUploadSuccess事件
         * <AUTHOR>
         * @date 2022/7/12
         */
        emitImgUploadSuccess() {
            // 深拷贝对象,避免影响到原对象数据
            const imgList = ComponentUtils.deepcopy(this.imgList)
            // 删除img对象中的字段,避免外部使用到了这个对象后调用接口报错
            imgList.forEach((img) => {
                delete img['tempPath']
                delete img['imgUrl']
            })
            this.$emit('imgUploadSuccess', imgList);
        },
        emitUploadListFinish(){
            if (this.wxImageList.uploadIndex === this.wxImageList.list.length) {
                this.$emit('uploadListFinish')
            }
        },
        /**
         * 通过wx.getLocation接口获取地址
         * 并通过该接口判断定位异常信息
         * <AUTHOR>
         * @date 2022/8/24
         */
        async getLocation() {
            // getLocation接口错误场景
            const locationErrArr = [
                {errMsg: 'getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF', tips: '手机系统未开启定位功能'},
                {errMsg: 'getLocation:fail system permission denied', tips: '企业微信APP未授权定位功能'},
                {errMsg: 'getLocation:fail:system permission denied', tips: '企业微信APP未授权定位功能'},
                {errMsg: 'getLocation:fail auth deny', tips: '当前小程序定位接口未授权，请联系管理员'},
                {errMsg: 'getLocation:fail:ERROR_NETWORK', tips: '您的网络无法连接，请检查网络'},
                {errMsg: 'getLocation:fail:timeout', tips: '获取定位信息超时，请稍后重试'}
            ];
            const coordinate = await this.$locations.getCurrentCoordinate();
            if (this.$utils.isNotEmpty(coordinate.errMsg) && coordinate.errMsg !== 'getLocation:ok') {
                const errorType = locationErrArr.filter((item) => {
                    return item.errMsg = coordinate.errMsg;
                });
                if (errorType.length > 0) {
                    console.log(errorType[0].tips);
                } else {
                    // 当前机型需要使用wx.getLocation接口获取定位信息发生未知错误时，记录机型，基础库版本，用户名等信息
                    this.$aegis.report({
                        msg: 'getLocation接口获取定位信息发生未知错误',
                        ext1: JSON.stringify({
                            username: this.userInfo.username, // 用户名
                            SDKVersion: this.$device.systemInfo.SDKVersion, // 基础库版本
                            system: this.$device.systemInfo.system, // 手机系统
                            version: this.$device.systemInfo.version, // 手机系统版本
                            errMsg: coordinate.errMsg, // 错误信息
                            model: this.$device.systemInfo.model // 手机型号
                        }),
                        trace: 'error'
                    });
                    console.log('定位获取未知错误：', coordinate.errMsg);
                }
                this.$dialog({
                    title: '提示',
                    content: () => (<view style="padding: 5px;">请确认手机地理位置授权是否打开，或者【手机设置】-【企业微信】位置权限管理是否打开？</view>),
                    initial: true
                });
                // 定位异常，清空定位监听数值
                this.$locations.clearLocationChange();
                // getLocationFlag标识返回为false，代表定位异常
                return {getLocationFlag: false};
            } else {
                coordinate['getLocationFlag'] = true;
                return coordinate;
            }
        }
    }
}

</script>

<style lang="scss">
.lnk-img-watermark-box {
    width: 100%;
    background: #fff;
    padding-bottom: 16px;
    .mask {
        position: fixed;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0);
        z-index: 10000;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .background {
            width: 185px;
            height: 185px;
            border-radius: 16px;
            background: rgba(0, 0, 0, .7);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .loading {
            font-size: 80px;
            margin-top: 24px;
            width: 100%;
            color: #f7f7f7;
            text-align: center;
            /*deep*/
            .link-loading-tag {
                margin: auto;
            }
        }

        .loading-text {
            margin-top: 16px;
            font-size: 28px;
            color: #f7f7f7;
            width: 100%;
            text-align: center;
        }
    }

    .lnk-img-watermark {
        width: calc(100% - 72px);
        @include flex;
        @include wrap;
        margin: 0 36px;
        background: #fff;
    }

    .lnk-img-watermark-item:nth-child(4n + 1) {
        @include flex-start-start;
    }

    .lnk-img-watermark-item:nth-child(4n-1), .lnk-img-watermark-item:nth-child(4n-2) {
        @include flex-center-start;

    }

    .lnk-img-watermark-item:nth-child(4n) {
        @include flex-end-start;
    }

    .lnk-img-watermark-item, .lnk-img-watermark-item2 {
        margin-top: 16px;
        width: 25%;
        .lnk-img-watermark-item-box {
            position: relative;
            background: #fff;
            height: 145px;
            width: 145px;

            &.show-info {
                height: 220px;
                width: 145px
            }

            .item-box-image {
                position: relative;
                background: #fff;
                width: 145px;
                height: 145px;

                .shadow-image {
                    width: 0;
                    height: 0;
                    opacity: 0;
                    position: absolute;
                    z-index: -9999;
                }

                .progress-bg {
                    background-color: gray;
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    // border-radius: 16px;
                    opacity: 0.7;

                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 40px;

                    z-index: 1;

                    .cancel-upload {

                        color: white;
                        background-color: darkred;
                        width: 60%;
                        height: 50px;
                        // border-radius: 10px;
                        font-size: 30px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                }

                .progress {
                    $progress-width: 94%;
                    $progress-height: 15px;
                    $progress-border-radius: 10px;
                    position: absolute;
                    top: 90%;
                    right: 0;
                    bottom: 0;
                    left: 3%;
                    background-color: gray;
                    border-radius: $progress-border-radius;
                    height: $progress-height;
                    width: $progress-width;

                    &__inner {
                        background: $color-primary;
                        height: $progress-height;
                        border-radius: $progress-border-radius;
                    }
                }
            }
        }


        .item-image {
            height: 100%;
            width: 100%;
            display: inline-block;
            object-fit: cover;
            border-radius: 16px;
        }

        .lnk-img-close-icon {
            position: absolute;
            right: -16px;
            top: -14px;
            font-size: 42px;
            color: #2F69F8;
            z-index: 2;
        }
    }

    .lnk-img-watermark-item-camera-wrapper {
        overflow: hidden;
        position: relative;

        .add-image-btn {
            border-radius: 16px;
            text-align: center;
            background: #F2F2F2;
            position: relative;
            width: 145px;
            height: 145px;
            line-height: 145px;

            .add-image-icon {
                color: #BFBFBF;
                font-weight: bold;
                font-size: 50px;
            }


        }
    }

    .painter-box {
        position: fixed;
        left: 9999999PX;
    }
    .show-more-image {
        width: 100%;
        text-align: right;
        color: #2F69F8;
        font-size: 28px;
        margin-top: 20px;
        font-weight: bold;
    }
}
</style>
