import Base64 from "../../../utils/log/base64";
import {$imageAssets} from '@/utils/image-assess';

/**
 * cos watermark默认配置类型
 * <AUTHOR>
 * @date    2022.7.16 23:05
 */
export interface iCosWatermarkDefaultConfig {
    darkShadowUrl: string,                      // 纯黑图片地址，所在桶必须与图片上传的桶一致
    alphaShadowUrl: string,                     // 纯透明图片地址，所在桶必须与图片上传的桶一致
    shadowAlpha: number,                        // 阴影透明度，默认是0.3
    shadowWidth: number,                        // 灰色背景图真实宽度
    baseRelativeFontsize: number,               // 基础文字相对大小，相对于图片宽度
    minWidth: number,                           // 最小图片宽度，小于这个宽度会自动拉伸图片宽度
    color: string,                              // 默认文字颜色
    textAlpha: number,                          // 文字透明度
}

/**
 * 单条文字水印配置对象类型
 * <AUTHOR>
 * @date    2022.7.16 23:06
 */
export interface iView {
    text: string,                               // 文字，可以自动换行
    position: 'top' | 'center' | 'bottom',      // 位置，top为左上角，center为中间，bottom为右下角，默认为top
    fontScale?: number,                         // 文字大小，为比例值。比如1.2位放大20%，0.9位缩小到90%，默认为1
    color?: string,                             // 文字颜色，默认为#FFFFFF
    textAlpha?: number,                          // 文字透明度
}

export function createCosWatermark(defaultSetting: iCosWatermarkDefaultConfig) {

    return (customConfig: Partial<iCosWatermarkDefaultConfig> & { height: number, width: number, views: iView[] }) => {

        const setting = {...defaultSetting, ...customConfig};

        const config = (() => {
            const paletteData = {painterHeight: setting.height, painterWidth: setting.width,};
            /*限制图片最小宽高*/
            if (setting.width < setting.minWidth) {
                paletteData.painterHeight = setting.height / setting.width * setting.minWidth;
                paletteData.painterWidth = setting.minWidth;
            }
            const pix = parseFloat((paletteData.painterWidth / 1000).toFixed(2));

            /*宽图，字体要小一点*/
            const baseFontsize = setting.baseRelativeFontsize * pix - (setting.height / setting.width < 0.9 ? 10 : 0);

            return {
                /*目标宽高*/
                paletteData,
                /*根据图片宽度相对值*/
                pix,
                /*文字基准大小*/
                baseFontsize,
            };
        })();

        const views: iView[] = setting.views;

        let ruleStrArr: string[] = [];

        const processView = ({text, fontScale, position, color, textAlpha}: iView) => {
            const fontsize = config.baseFontsize * (fontScale || 1);
            const gravity = {'top': 'northwest', 'center': 'center', 'bottom': 'southeast',}[position];
            const txtList = chunkString(text, Math.floor(config.paletteData.painterWidth / config.baseFontsize * 1.25)).reverse();
            txtList.forEach((txt) => {
                ruleStrArr.push([
                    "watermark/2",
                    `text/${encodeSafeBase64(txt)}`,
                    `fill/${encodeSafeBase64(color || setting.color)}`,
                    `fontsize/${parseInt((fontsize / 96 * 72).toFixed(0))}`,
                    `gravity/${gravity}`,
                    `dissolve/${(textAlpha || setting.textAlpha) * 100}`,
                    `dy/${Math.ceil(dy)}`,
                    `dx/${parseInt((config.baseFontsize / 2).toFixed(0))}`
                ].join('/'));
                dy += fontsize * 0.9;
            });
            dy += config.baseFontsize * 0.5;
        };

        // top文字水印，水印与边缘起始距离（顶部纵向）
        let dy = 0.5 * config.baseFontsize;
        views.filter(i => !i.position || i.position === 'top' && !!i.text).forEach((view) => {processView(view);});
        const topShadow = [
            `watermark/1`,
            `image/${encodeSafeBase64(setting.alphaShadowUrl + `?watermark/1/image/${encodeSafeBase64(setting.darkShadowUrl)}/gravity/center/dissolve/${setting.shadowAlpha * 100}|imageMogr2/thumbnail/${config.paletteData.painterWidth}x${dy}!`)}`,
            `gravity/north`,
        ].join('/');

        // bottom文字水印，水印与边缘起始距离（底部纵向）
        dy = 0.5 * config.baseFontsize;
        views.filter(i => !i.position || i.position === 'bottom' && !!i.text).reverse().forEach((view) => {processView(view);});
        const bottomShadow = [
            `watermark/1`,
            `image/${encodeSafeBase64(setting.alphaShadowUrl + `?watermark/1/image/${encodeSafeBase64(setting.darkShadowUrl)}/gravity/center/dissolve/${setting.shadowAlpha * 100}|imageMogr2/thumbnail/${config.paletteData.painterWidth}x${dy}!`)}`,
            `gravity/south`,
        ].join('/');

        // center文字水印，水印句中间定位距离
        dy = 0;
        views.filter(i => !i.position || i.position === 'center' && !!i.text).reverse().forEach((view) => {
            !view.fontScale && (view.fontScale = 2);
            processView(view);
        });

        ruleStrArr.unshift(topShadow);
        ruleStrArr.unshift(bottomShadow);

        let rule = ruleStrArr.join('|');

        /*如果图片宽度小于最小宽度，则先缩放到最小宽度再加文字水印*/
        if (config.paletteData.painterWidth !== setting.width) {rule = `imageMogr2/thumbnail/${config.paletteData.painterWidth}x|` + rule;}

        return rule;
    };
}

/*---------------------------------------utils-------------------------------------------*/

/**
 * 安全base64转码
 * <AUTHOR>
 * @date    2022.7.16 23:02
 * 将所有的 + 替换为 -
 * 将所有的 / 替换为 _
 * 将所有的 = 替换为空字符串
 * 保留末尾所有=
 */
const encodeSafeBase64 = (source: string) => {
    let str = Base64.encode(source.replace(/https/, 'http'))
        .replace(/\+/g, '-')
        .replace(/\//g, '_');
    let chars = [] as string[]
    let lastChar = str[str.length - 1]
    while (lastChar === '=') {
        chars.push(lastChar)
        str = str.slice(0, -1)
        lastChar = str[str.length - 1]
    }
    return str.replace(/=/g, '') + chars.join('');
};

/**
 * 对字符串进行分组以便实现换行
 * 中文当做一个字符
 * 非中文当做半个字符
 * <AUTHOR>
 * @date    2022.7.16 22:27
 */
const chunkString = (() => {
    let chinesePattern = /[\u4e00-\u9fa5]/;
    return (str: string, size: number) => {
        let results: string[] = [];
        let index = 0;
        let tempString = '';
        let tempSize = 0;
        while (index < str.length) {
            const char = str.charAt(index);
            index++;
            tempString += char;
            if (chinesePattern.test(char)) {
                tempSize += 1;
            } else {
                tempSize += 0.5;
            }
            if (tempSize >= size) {
                results.push(tempString);
                tempString = '';
                tempSize = 0;
            }
        }
        if(tempString) results.push(tempString);
        return results;
    };
})();

/*---------------------------------------创建服务实例-------------------------------------------*/

export const cosWatermark = createCosWatermark({
    darkShadowUrl: $imageAssets.darkShadowUrl,
    alphaShadowUrl: $imageAssets.alphaShadowUrl,
    shadowAlpha: 0.3,
    shadowWidth: 897,
    baseRelativeFontsize: 42,
    minWidth: 800,
    color: '#FFFFFF',
    textAlpha: 1,
});
