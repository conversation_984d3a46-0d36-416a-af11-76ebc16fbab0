<template>
    <view class="lnk-sign-board">
        <view class="signature-img">签字板区域</view>
        <view class="signature-content">
            <canvas class="canvas" canvas-id="myCanvas"
                    disable-scroll="true"
                    @touchstart="touchStart"
                    @touchmove="touchMove"
                    @touchend="touchEnd"
                    @touchcancel="touchEnd"
                    @error="canvasIdErrorCallback"></canvas>
        </view>
        <view class="signature-btn">
            <button class="sign-cancel-btn sign-btn" @tap="clearCanvas">重置</button>
            <button class="sign-sure-btn sign-btn" @tap="saveDraw">保存签字</button>
        </view>
        <view class="signature-img">保存的签名图片</view>
        <view class="signature-img-container" @tap="preSignImage(signImgPath)">
            <image :src="signImgPath"></image>
        </view>
        <view class="signature-img">压缩的小图</view>
        <view class="signature-img-container" @tap="preSignImage(smallSignImgPath)">
            <image :src="smallSignImgPath" mode="aspectFit"></image>
        </view>
        <view class="blank"></view>
    </view>
</template>

<script>
    export default {
        name: "lnk-sign-board",
        data () {
            return {
                curDrawArr: [],                                         // 绘制储存对象
                drawInfos: [],                                          // 绘制手指抬起绘制参数
                begin: false,                                           // 开始绘制参数
                canvasW: 0,                                             // 签名板宽度
                canvasH: 0,                                             // 签名板高度
                signImgPath: '',                                        // 签名图片路径
                smallSignImgPath: '',                                   // 签名缩略图图片路径
                saveDrawFlag: false,                                    // 签名保存标志
                startX: 0,                                              // 绘制开始X轴
                startY: 0,                                              // 绘制开始Y轴
                userInfo: this.$taro.getStorageSync('token').result     // 用户信息
            }
        },
        props: {
            moduleId: {
                type: String,
                required: true,
                default: ''             // 附件headId
            },
            moduleType: {
                type: String,
                required: true,
                default: ''             // 附件所属模块
            },
        },
        created (thisObj) {
            this.context = wx.createCanvasContext('myCanvas', thisObj);
            this.context.beginPath();
            this.context.setStrokeStyle('#000000');
            this.context.setLineWidth(4);
            this.context.setLineCap('round'); // 让线条圆润
            this.context.setLineJoin('round');
            this.canvasW = 351; // 画布宽度
            this.canvasH = 200; // 画布高度
        },
        methods: {
            /**
             * 绘制开始 手指开始按到屏幕上
             * <AUTHOR>
             * @date 2020-04-16
             */
            touchStart (e) {
                this.lineBegin(e.changedTouches[0].x, e.changedTouches[0].y);
                this.curDrawArr.push({
                    x: e.changedTouches[0].x,
                    y: e.changedTouches[0].y
                })
            },
            /**
             * 开始绘制线条
             * <AUTHOR>
             * @date 2020-04-16
             */
            lineBegin (x, y) {
                this.begin = true;
                this.context.beginPath();
                this.startX = x;
                this.startY = y;
                this.lineAddPoint(x, y);
            },
            /**
             * 绘制中 手指在屏幕上移动
             * <AUTHOR>
             * @date 2020-04-16
             */
            touchMove (e) {
                if (this.begin) {
                    this.lineAddPoint(e.changedTouches[0].x, e.changedTouches[0].y);
                    this.draw(true);
                    this.curDrawArr.push({
                        x: e.changedTouches[0].x,
                        y: e.changedTouches[0].y
                    })
                }
            },
            /**
             * 绘制canvas
             * isReverse: 是否保留之前的像素
             * <AUTHOR>
             * @date 2020-04-16
             */
            draw (isReverse = false, cb) {
                this.context.draw(isReverse, () => {
                    if (cb && typeof (cb) === 'function') {
                        cb()
                    }
                })
            },
            /**
             * 绘制线条中间添加点
             * <AUTHOR>
             * @date 2020-04-16
             */
            lineAddPoint (x, y) {
                this.context.moveTo(this.startX, this.startY)
                this.context.lineTo(x, y)
                this.context.stroke()
                this.startX = x
                this.startY = y
            },
            /**
             * 绘制结束 手指抬起
             * <AUTHOR>
             * @date 2020-04-16
             */
            touchEnd () {
                this.drawInfos.push({
                    drawArr: this.curDrawArr,
                    color: 'black',
                    lineWidth: 4
                })
                this.curDrawArr = []
                this.lineEnd()
            },
            /**
             * 绘制线条结束
             * <AUTHOR>
             * @date 2020-04-16
             */
            lineEnd () {
                this.context.closePath();
                this.begin = false
            },
            /**
             * 绘制报错
             * <AUTHOR>
             * @date 2019-11-18
             */
            canvasIdErrorCallback (e) {
                console.error(e.detail.errMsg)
            },
            /**
             * 点击清空canvas
             * <AUTHOR>
             * @date 2020-04-16
             */
            async clearCanvas () {
                this.drawInfos = []
                this.context.clearRect(0, 0, this.canvasW, this.canvasH);
                this.context.draw(false);
                this.context.setStrokeStyle('#000000');
                this.context.setLineWidth(4);
                this.context.setLineCap('round');
                this.context.setLineJoin('round');
                if (!this.$utils.isEmpty(this.signImgId)) {
                    const option = {id: this.signImgId};
                    const result = await deleteImage(option);
                    if (result) {
                        this.signImgId = '';
                        this.signImgPath = '';
                        this.saveDrawFlag = false;
                    }
                }
            },
            /**
              * 保存签名
              * <AUTHOR>
              * @date 1/29/21
            */
            async saveDraw () {
                const that = this
                if (this.drawInfos.length === 0) {
                    that.$dialog({
                        title: '提示',
                        content: '签名内容不能为空！',
                        cancelButton: false,
                        confirmText: '确定',
                        onConfirm: () => {}
                    });
                    return false
                }
                // 生成图片
                wx.canvasToTempFilePath({
                    canvasId: 'myCanvas',
                    fileType: 'jpg',
                    success: async function (res) {
                        console.log(res);
                        that.signImgPath = res.tempFilePath;
                        // 压缩图片
                        const {source, compress} = await that.$image.compress({
                            filePath: res.tempFilePath,
                        });
                        console.log(source, compress);
                        that.smallSignImgPath = compress.path;
                        let originalKey = that.getRandFileName(that.smallSignImgPath);
                        let smallKey = that.getRandFileName(that.smallSignImgPath);
                        let uploadImageOptions = {
                            filePath: res.tempFilePath,                         // 上传原图图片路径
                            formData: {
                                headId: that.moduleId,                          // 上传图片headId
                                moduleType: that.moduleType                     // 上传图片类型
                            },
                            Key: originalKey,                                      // 上传原图key
                            smallKey: smallKey,                                   // 上传缩略图key
                            smallImgUrl: that.smallSignImgPath,                  // 上传缩略图图片路径
                        };
                        console.log(uploadImageOptions);
                        // let data = await that.$image.insertUploadImgRecord(uploadImageOptions);
                        // if (data.success) {
                        //     that.signImgPath = data.newRow.attachmentPath;
                        //     that.$utils.showAlert('签名保存成功', {icon: 'none'});
                        //     that.$emit('signatureSuccess')
                        // }
                    }
                })
            },
            /**
             * 上传图片key
             * <AUTHOR>
             * @date 2020-07-02
             */
            getRandFileName(filePath) {
                let extIndex = filePath.lastIndexOf('.');
                let extName = extIndex === -1 ? '' : filePath.substr(extIndex);
                return `${this.moduleId}-${this.$utils.uuid()}-${parseInt(Math.random() * 10000)}-${this.userInfo.id}-${this.$date.format(new Date(Date.now()), 'HH:mm:ss')}-${this.$date.format(new Date(Date.now()), 'YYYY.MM.DD')}${extName}`;
            },
            /**
              * 预览大图
              * <AUTHOR>
              * @date 1/29/21
              * @param item 待预览图片路径
            */
            preSignImage (item){
                this.$taro.previewImage({
                    current: item, // 当前显示图片的http链接
                    urls: [item] // 需要预览的图片http链接列表
                })
            }
        }
    }
</script>

<style lang="scss">
    .lnk-sign-board{
        .signature-content {
            width: 100%;
            height: 400px;
            background-color: #ffffff;
            .canvas {
                width: 100%;
                height: 100%;
            }
            img {
                width: 100%;
                height: 100%;
            }
        }
        .signature-btn {
            @include flex-center-center;
            @include space-around;
            margin-top: 24px;
            .sign-btn {
                width: 45%;
                height: 80px;
                line-height: 80px;
                font-size: 28px;
                color: #FFFFFF;
                text-align: center;
                border-radius: 8px;
            }
            .sign-btn:after {
                border: none;
            }
            .sign-cancel-btn {
                color: #2F69F8;
                border: 1px solid #2F61F8;
            }
            .sign-sure-btn {
                color: #FFFFFF;
                background-color: #2F69F8;
                box-shadow: 0 16px 46px 0 rgba(47, 105, 248, 0.5);
            }
        }
        .signature-img {
            font-size: 28px;
            color: #2F61F8;
            padding-left: 24px;
            padding-top: 24px;
            padding-bottom: 24px;
        }
        .signature-img-container {
            width: 100%;
            height: 400px;
            background-color: #ffffff;
            image {
                width: 100%;
                height: 100%;
            }
        }
        .blank {
            width: 100%;
            height: 200px;
        }
    }
</style>
