<template>
    <!--  图片上传组件-上传到本地服务器-智玲相关功能使用--->
    <view class="lnk-img-lzlj">
        <view v-for="(img,index) in imgList" :key="index" class="lnk-img-item">
            <image :src="img.smallurl" @tap="clickImg(index)"></image>
            <view v-if="delFlag" class="lnk-img-close-icon iconfont icon-close-circle-fill"
                  @tap="delImg(img, index)"></view>
        </view>
        <view v-if="newFlag" class="lnk-img-item lnk-img-item-camera-wrapper" @tap="addImg">
            <view class="add-image-btn">
                <view class="iconfont icon-plus add-image-icon"></view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "lnk-img-lzlj",
    data() {
        return {
            canvasFlag: true,
            canvasWidth: 0,             // 压缩canvas图片高度
            canvasHeight: 0,            // 压缩canvas图片高
            originalPath: [],           // 原图片url数组列表
            tempFilePaths: [],           // 缩略图图片url数组列表
            imgList: [],
        }
    },
    props: {
        parentId: {
            required: true,
        },                          // 附件头Id
        moduleType: {
            type: String,
            default: 'noType',
            required: true
        },                          // 附件所属模块
        delFlag: {
            type: Boolean,
            default: false

        },                          // 是否可删除，点击删除图标会派发delete事件
        newFlag: {
            type: Boolean,
            default: false
        },                          // 是否可添加
        camera: {
            type: Boolean,
            default: true
        },                          // 是否支持拍照上传
        album: {
            type: Boolean,
            default: true
        },                           // 是否支持从相册中选择上传
    },
    async created() {
        // 初始化图片信息
        await this.initImgList();
    },
    watch: {
        parentId(newVal, oldVal) {
            if (newVal !== oldVal) {
                this.initImgList()
            }
        }
    },
    methods: {
        /**
         *  图片信息
         *  <AUTHOR>
         *  @date   2020-07-03
         */
        async initImgList() {
            console.log("查询图片");
            console.log(this.parentId);
            if (!this.$utils.isEmpty(this.parentId)) {
                const data = await this.$http.post(this.$env.appURL + '/action/link/image/queryAll',
                    {
                        parentid: this.parentId,
                        module: this.moduleType,
                    });
                this.imgList = data.rows || [];
                this.$emit('call', this.imgList);
            }
        },
        /**
         * 添加图片，直接调用imgService服务
         * <AUTHOR>
         * @date 2020-11-11
         */
        async addImg() {
            let sourceType = [];
            if (this.camera && this.album) {
                sourceType = ['album', 'camera']
            } else if (this.camera) {
                sourceType = ['camera']
            } else if (this.album) {
                sourceType = ['album']
            }
            // 选择图片属性
            const chooseImageOptions = {
                sourceType: sourceType
            };
            const wxImages = await this.$image.chooseImage(chooseImageOptions);
            if (!wxImages) {
                return
            }
            // const compressArr = await Promise.all(wxImages.tempFilePaths.map(filePath => this.$image.compress({filePath})));
            // const compressArr = await this.$image.compress({filePath: wxImages.tempFilePaths})
            this.$utils.showLoading('图片上传中')
            try {
                const newList = await Promise.all(wxImages.tempFilePaths.map(async (tempFilePath, index) => {
                    let data = {};
                    let uploadImageData = {};
                    //上传到本地服务器
                    const base64 = wx.getFileSystemManager().readFileSync(tempFilePath, 'base64');
                    uploadImageData.parentid = this.parentId;
                    uploadImageData.moduleType = this.moduleType;
                    uploadImageData.base64 = base64;
                    uploadImageData.url = this.$env.appURL + '/action/link/image/upsert';
                    data = await this.$image.insertUploadLocalImg(uploadImageData);
                    if (data.success) {
                        return data.result
                    } else {
                        return Promise.reject(`上传第${index + 1}张图片的时候出错！`)
                    }
                }))
                this.imgList.push(...newList);
                this.$emit('call', this.imgList);
                this.$utils.showAlert('上传完成');
            } catch (e) {
                console.error(e)
                this.$taro.showModal(`上传失败：${String(e)}`)
            } finally {
                this.$utils.hideLoading()
            }
        },
        /**
         * 点击图片预览，直接调用imgService服务
         * <AUTHOR>
         * @date 2020-1-11
         * @param index 当前图片对象所属下标
         */
        clickImg(index) {
            // 获取原图
            this.originalPath = [];
            this.imgList.forEach((item) => {
                this.originalPath.push(item.url);
                if (this.imgList.length === this.originalPath.length) {
                    const inOptions = {
                        current: this.originalPath[index],
                        urls: this.originalPath
                    };
                    this.$image.previewImages(inOptions)
                }
            });
            if (this.originalPath.length !== 0) {
                const inOptions = {
                    current: this.originalPath[index],
                    urls: this.originalPath
                };
                this.$image.previewImages(inOptions)
            }
        },
        /**
         * 删除图片，直接调用imgService服务
         * <AUTHOR>
         * @param img 图片对象
         * @param index 当前图片对象所属下标
         */
        async delImg(img, index) {
            this.$utils.showLoading('图片删除中...');
            const option = {url: this.$env.appURL + '/action/link/image/deleteById', id: this.imgList[index].id};
            const result = await this.$image.deleteLocalImage(option);
            if (result) {
                this.imgList.splice(index, 1);
                this.tempFilePaths.splice(index, 1);
                this.originalPath.splice(index, 1);
                this.$emit('call', this.imgList);
            }
            this.$utils.hideLoading()
        },
    }
}
</script>

<style lang="scss">
.lnk-img-lzlj {
    padding-bottom: 20px;
    display: inline-block;
    width: 100%;
    background-color: white;

    .lnk-img-item {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        vertical-align: bottom;
        position: relative;
        width: 144px;
        height: 144px;
        margin-top: 16px;
        margin-left: 24px;

        image {
            height: 100%;
            width: 100%;
            display: inline-block;
            object-fit: cover;
            border-radius: 16px;
        }

        .lnk-img-close-icon {
            position: absolute;
            right: -16px;
            top: -14px;
            font-size: 42px;
            color: #2F69F8;
        }
    }

    .lnk-img-item-camera-wrapper {
        border-radius: 16px;
        overflow: hidden;

        .add-image-btn {
            width: 100%;
            height: 100%;
            background: #F2F2F2;
            @include flex-center-center;

            .add-image-icon {
                color: #BFBFBF;
                font-weight: bold;
                font-size: 50px;
            }
        }
    }

    .canvas {
        //position: absolute;
        //z-index: -1;
        //left: -10000px;
        //top: -10000px;
        //visibility: hidden;
    }
}
</style>
