<template>
    <view class="lnk-tabs-container">
        <scroll-view scroll-x="true" class="lnk-tabs"
                     :scroll-into-view="toViewId"
                     :style="{'top':topHeight + 'px','z-index': Number(zIndex) + Number(addZIndex)}"
                     :class="{'marginTop':marginTop}">
            <view class="lnk-tabs-content"
                  :style="padding ? 'justify-content: normal' : wordLength> 0 ? {width: (taps.length * wordLength) + 'px',  'justify-content': 'center'} : {width: taps.length * width + '%'}">
                <view class="lnk-tabs-item"
                      :class="{'active': tab.seq === value.seq}"
                      :id="'tab' + tab.seq + index"
                      v-for="(tab, index) in taps" :key="index" @tap="changeActive(tab, index)"
                      :style="padding ? '' : wordLength> 0 ? {width: wordLength + 'px'} : {width: width + '%'}">
                    <view class="label-name-line" v-if="selectStyle" :style="padding ? 'padding: 0 10rpx' : ''">
                        <text class="label-name-text">{{tab.name}}
                            <text v-if="tab.num !== undefined">（
                                <slot>{{tab.num}}</slot>
                                ）
                            </text>
                        </text>
                        <view class="line" v-if="tab.seq === value.seq"></view>
                    </view>
                    <view class="label-name-bg" v-else>
                        <text :class="tab.seq === value.seq ? 'label-name-on' : 'label-name-off'">{{tab.name}}</text>
                    </view>
                </view>
                <view v-if="showMore" class="blank-block"></view>
                <link-icon icon="icon-grid"
                           v-if="showMore"
                           class="all-tab-btn"
                           :style="{'z-index': Number(zIndex) + Number(addZIndex) + 1}"
                           @tap="tabDialog = true"/>
                <view class="extra-space" v-if="pageFrom === 'consumerList'"></view> <!-- 添加额外的占位元素 -->
            </view>
        </scroll-view>
        <link-dialog ref="tabDialog"
                     :noPadding="true"
                     v-model="tabDialog"
                     position="top"
                     borderRadius="0 0 60rpx 60rpx">
            <view class="model-title">
                <view class="title">全部信息</view>
                <view class="iconfont icon-close" @tap="tabDialog = false" :style="'color: #bbb'"></view>
            </view>
            <view class="tab-con">
                <view class="tab-item" v-for="(item, index) in taps" @tap="changeActive(item, index, true)">
                    <link-icon :icon="'icon-' + item.val" class="tab-icon"/>
                    <view class="tab-name">{{item.name}}</view>
                </view>
            </view>
        </link-dialog>
    </view>
</template>

<script>
    import {ComponentUtils} from "link-taro-component";

    export default {
        name: 'lnk-taps',
        data() {
            return {
                lineMarginLeft: '',
                zIndex: ComponentUtils.nextIndex(),
                toViewId: 'tab1',
                tabDialog: false
            }
        },
        props: {
            onlyTabChange: {
                type: Boolean,
                default: false
            }, // 为true时不通过v-model自动切换active，仅通过switchTab更改
            selectStyle: {
                type: Boolean,
                default: true
            },
            taps: {
                type: Array,
                default: []
            },
            value: {
                type: Object,
                default: '1'
            },
            topHeight: {
                type: Number,
                default: 0
            },
            marginTop: {
                type: Boolean,
                default: false
            },
            positionFlag: {
                type: String,
                default: 'fixed'
            },
            statusLeft: {
                type: Boolean,
                default: false
            },
            padding: {
                type: Boolean,
                default: false
            },
            showMore: {
                type: Boolean,
                default: false
            },
            wordLength: {
                type: Number,
                default: 0
            },
            addZIndex: {
                type: Number,
                default: 0
            },
            pageFrom: {
                type: String,
            }
        },
        computed: {
            width: function () {
                if (this.taps.length < 2) {
                    this.lineMarginLeft = this.statusLeft ? 'margin-left: 21%' : 'margin-left: 45%';
                    return 100 / this.taps.length
                } else if (this.taps.length === 2) {
                    this.lineMarginLeft = this.statusLeft ? 'margin-left: 18%' : 'margin-left: 21%';
                    return 100 / this.taps.length
                } else if (3 === this.taps.length) {
                    this.lineMarginLeft = 'margin-left: 12%';
                    return 100 / this.taps.length
                } else if (this.taps.length === 4) {
                    this.lineMarginLeft = 'margin-left: 8%';
                    return 100 / this.taps.length
                } else {
                    this.lineMarginLeft = 'margin-left: 5.5%';
                    return 22
                }
            }
        },
        onLoad() {
        },
        methods: {
            changeActive(active, index, flag) {
                if (flag) this.tabDialog = false
                if (!this.onlyTabChange) {
                    this.$emit('input', active)
                }
                this.toViewId = 'tab' + active.seq + index
                this.$emit('switchTab', active, index)
            }
        }
    }
</script>

<style lang="scss">
    .lnk-tabs-container {
        .model-title {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding: 20px;

            .title {
                width: 56%;
            }

            .iconfont {
                font-size: 40px;
            }
        }

        .tab-con {
            width: 100%;
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            flex-wrap: wrap;

            .tab-item {
                width: 25%;
                height: 160px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;

                .tab-icon {
                    font-size: 48px;
                    line-height: 40px;
                    color: #3F66EF;
                }

                .tab-name {
                    font-size: 28px;
                    line-height: 44px;
                    color: #444;
                    margin-top: 14px;
                }
            }
        }

        .lnk-tabs {
            white-space: nowrap;
            border-top: 1px solid #f2f2f2;
            height: 92px;
            background-color: #fff;
            color: #595959;
            position: fixed;

            &.marginTop {
                margin-top: 94px;
            }

            .active {
                color: $color-primary;
            }

            .lnk-tabs-content {
                display: flex;
                justify-content: space-around;
                flex-wrap: nowrap;

                .blank-block {
                    width: 130px;
                    height: 92px;
                }

                .all-tab-btn {
                    border-top: 1px solid #f2f2f2;
                    width: 92px;
                    height: 92px;
                    font-size: 56px;
                    line-height: 92px;
                    background-color: #fff;
                    position: fixed;
                    right: 0;
                    top: -1px;
                    z-index: 1510;
                }
                .extra-space {
                    width: 92px; // 添加一个额外的占位元素，宽度与 all-tab-btn 一致
                }
            }

            .lnk-tabs-item {
                height: 92px;
                line-height: 92px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .label-name-line {
                    font-size: 28px;
                    margin-left: 10px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;

                    .line {
                        height: 8px;
                        width: 56px;
                        border-radius: 16px 16px 0 0;
                        background-color: $color-primary;
                        box-shadow: 0 3px 8px 0 rgba(47, 105, 248, 0.63);
                        margin-top: -8px;
                    }
                }

                .label-name-bg {
                    .label-name-on {
                        background: $color-primary;
                        color: #fff;
                        border-radius: 8px;
                        padding: 8px 19px;
                        font-size: 28px;
                    }

                    .label-name-off {
                        color: #8C8C8C;
                        border-radius: 8px;
                        padding: 8px 19px;
                        font-size: 28px;
                    }
                }
            }
        }
    }

</style>
