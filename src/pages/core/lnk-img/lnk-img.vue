<template>
    <!--  图片上传组件-上传到cos-不需要水印  -->
    <view class="lnk-img">
        <view class="lnk-img-item" :style="{width: width+'px',height: height+'px',display: 'inline-flex'}" v-for="(img,index) in imgData" :key="index">
            <image :src="img.imgUrl" @tap="clickImg(index)" lazy-load="true"></image>
            <view class="lnk-img-close-icon iconfont icon-close-circle-fill" v-if="delFlag"
                  @tap.stop="delImg(img, index)"></view>
        </view>
        <view class="lnk-img-item lnk-img-item-camera-wrapper" v-if="newFlag && (isCount ? imgData.length < count : true)" @tap="addImg"  :style="{width: width+'px',height: height+'px',display: 'inline-flex'}">
            <view class="add-image-btn">
                <view class="iconfont icon-plus add-image-icon"></view>
            </view>
        </view>
    </view>
</template>

<script>
    import Taro from "@tarojs/taro";
    export default {
        name: 'lnk-img',
        data () {
            return {
                imgList: [],                // 展示图片数组
                originalPath: [],           // 原图片url数组列表
                tempFilePaths: [],           // 缩略图图片url数组列表
                cacheLicenseArr:[],           //终端财务信息-图片-缓存情况下使用
            }
        },
        props: {
            uploadType: {
                type: String,
                default: 'cos'
            },                          // 上传类型，默认cos
            imgKey: {
                type: String,
                default: 'attachmentPath'
            },                          // 要展示的原图片路径字段/图片cosKey
            imgSmallKey: {
                type: String,
                default: 'smallurl'
            },                          // 要展示的缩略图图片路径字段/图片cosKey
            parentId: {
                type: String,
                required: true
            },                          // 附件头Id
            moduleType: {
                type: String,
                default: 'noType',
                required: true
            },                          // 附件所属模块
            delFlag: {
                type: Boolean,
                default: false
            },                          // 是否可删除，点击删除图标会派发delete事件
            newFlag: {
                type: Boolean,
                default: false
            },                          // 是否可添加
            camera: {
                type: Boolean,
                default: true
            },                          // 是否支持拍照上传
            album: {
                type: Boolean,
                default: true
            },                           // 是否支持从相册中选择上传
            pathKeyArray: {
                type: Array,
                default(){
                    return []
                }
            },                          // 从后端接口直接获取到的key值
            realDeleteFlag: {
                type: Boolean,
                default: true
            },
            noGetImgKeyList:{
                type: Boolean,
                default: false
            },
            //@add by 谭少奇 2023/08/22上传图片数量限制
            count:{
                type: Number,
                default: 9
            },
            // 是否限制上传数量
            isCount:{
                type: Boolean,
                default: false
            },
            //@add by 邓佳柳 2024/07/25 自定义图片尺寸
            width: {
                type: [Number, String],
                require: false,
                default:103
            },
            height: {
                type: [Number, String],
                require: false,
                default:103
            }
        },
        watch: {
            /**
             * 监听附件头id参数
             * <AUTHOR>
             * @date 2020-09-08
             * @param val
             */
            pathKeyArray (val) {
                if (this.noGetImgKeyList || val) {
                    this.imgList = this.pathKeyArray;
                } else {
                    this.getImgKeyList()
                }
            },
        },
        computed: {
            /**
             * 处理图片数组数据
             * <AUTHOR>
             * @date 2020-08-13
             */
            imgData () {
                (this.imgList || []).forEach(async (item) => {
                    let imgUrl = this.$image.getSignedUrl(item[this.imgSmallKey]);
                    this.$set(item, 'imgUrl', imgUrl);
                    this.tempFilePaths.push(item.imgUrl)
                });
                return this.imgList
            },
            finalCount(){
                let nowCount = this.imgList.length
                console.log(this.count - nowCount)
                return this.count - nowCount
            }
        },
        created () {
            // 如果key值不存在则调用附件查询接口查询
            if (this.noGetImgKeyList || !this.$utils.isEmpty(this.pathKeyArray)) {
                console.log(this.pathKeyArray);
                this.imgList = this.pathKeyArray;
            } else {
                this.getImgKeyList()
            }
        },
        destroyed () {
            this.imgList = [];
        },
        methods: {
            /**
             * 添加图片，直接调用imgService服务
             * <AUTHOR>
             * @date 2020-07-02
             */
            async addImg() {
                //@add by 谭少奇 2023/08/22上传图片数量限制
                if(this.finalCount <= 0 && this.isCount){
                    this.$message.warn('上传图片已达上限')
                    return
                }
                let sourceType = []
                if (this.camera && this.album) {
                    sourceType = ['album', 'camera']
                } else if (this.camera) {
                    sourceType = ['camera']
                } else if (this.album) {
                    sourceType = ['album']
                }
                // 选择图片属性
                const chooseImageOptions = {
                    sourceType: sourceType,
                    count: this.finalCount,
                }
                const wxImages = await this.$image.chooseImage(chooseImageOptions)
                if (!wxImages) {
                    return
                }
                // const compressArr = await Promise.all(wxImages.tempFilePaths.map(filePath => this.$image.compress({filePath})));
                // const compressArr = await this.$image.compress({filePath: wxImages.tempFilePaths})
                this.$taro.showLoading({title: `图片上传中...`, mask: true})
                try {
                    const newList = await Promise.all(wxImages.tempFilePaths.map(async (tempFilePath, index) => {
                        this.tempFilePaths.push(tempFilePath);
                        let uploadImageOptions = {
                            filePath: '',                            // 上传图片路径
                            formData: {
                                headId: this.parentId,                 // 上传图片headId
                                moduleType: this.moduleType            // 上传图片类型
                            },
                            Key: null,
                            smallKey: null,
                            smallImgUrl: ''
                        };
                        uploadImageOptions.Key = this.getRandFileName(tempFilePath);
                        uploadImageOptions.smallKey = this.getRandFileName(tempFilePath);
                        uploadImageOptions.smallImgUrl = tempFilePath;
                        uploadImageOptions.filePath = tempFilePath;
                        let data = await this.$image.insertUploadImgRecord(uploadImageOptions);
                        if (data.success) {
                            return data.newRow
                        } else {
                            return Promise.reject(`上传第${index + 1}张图片的时候出错！`)
                        }
                    }))
                    this.imgList.push(...newList);
                    this.cacheLicenseArr.push(...newList);
                    await this.$emit('imgUploadSuccess', this.imgList)
                    this.$emit('imgUploadCacheSuccess', this.cacheLicenseArr)
                    this.$utils.showAlert('上传完成');
                } catch (e) {
                    console.error(e)
                    this.$taro.showModal(`上传失败：${String(e)}`)
                } finally {
                    this.$utils.hideLoading();
                }
            },
            /**
             * 获取图片key
             * <AUTHOR>
             * @date 2020-07-09
             */
            async getImgKeyList () {
                if(this.$utils.isEmpty(this.parentId)){
                    return false;
                }
                const that = this;
                this.$http.post('action/link/attachment/queryByExamplePage', {
                    uploadType: this.uploadType,
                    pageFlag: false,
                    sort: 'created',
                    order: 'desc',
                    moduleType: this.moduleType, // 所属模块
                    headId: this.parentId
                }).then((data) => {
                    if (data.success) {
                        that.imgList = data.rows
                    }
                })
            },
            /**
             * 点击图片预览，直接调用imgService服务
             * <AUTHOR>
             * @date 2020-07-09
             * @param index 当前图片对象所属下标
             */
            clickImg (index) {
                // 获取原图
                this.originalPath = [];
                this.imgList.forEach(async (item) => {
                    let imgUrl = this.$image.getSignedUrl(item[this.imgKey]);
                    this.$set(item, 'originalImgUrl', imgUrl);
                    this.originalPath.push(item.originalImgUrl);
                })
                if (this.originalPath.length !== 0) {
                    const inOptions = {
                        current: this.originalPath[index],
                        urls: this.originalPath
                    }
                    this.$image.previewImages(inOptions)
                }
            },
            /**
             * 删除图片，直接调用imgService服务
             * <AUTHOR>
             * @param img 图片对象
             * @param index 当前图片对象所属下标
             */
            async delImg (img, index) {
                if (this.realDeleteFlag) {
                    console.log('realDeleteFlag');
                    this.$utils.showLoading('图片删除中...');
                    const option = {id: this.imgList[index].id};
                    const result = await this.$image.deleteImage(option);
                    if (result) {
                        this.imgList.splice(index, 1);
                        this.tempFilePaths.splice(index, 1);
                        this.originalPath.splice(index, 1);
                        this.$emit('imgDeleteSuccess', this.imgList, option.id);
                    }
                    this.$utils.hideLoading()
                } else {
                    this.$emit('softDelete', index, img)
                }
            },
            /**
             * 上传图片key
             * <AUTHOR>
             * @date 2020-07-02
             * @description key的组成元素 时分秒——年月日——userId-模块Id-本地递增序列值
             */
            getRandFileName(filePath) {
                let extIndex = filePath.lastIndexOf('.');
                let extName = extIndex === -1 ? '' : filePath.substr(extIndex);
                let userInfo = Taro.getStorageSync('token').result
                return `${this.parentId}-${this.$utils.uuid()}-${parseInt(Math.random()*10000)}-${userInfo.id}-${this.$date.format(new Date(Date.now()), 'HH:mm:ss')}-${this.$date.format(new Date(Date.now()), 'YYYY.MM.DD')}${extName}`;
            },
        }
    }
</script>

<style lang="scss">
    .lnk-img {
        width: 100%;
        .lnk-img-item {
            align-items: center;
            justify-content: center;
            vertical-align: bottom;
            position: relative;
            margin-top: 16px;
            margin-left: 7px;
            image {
                height: 100%;
                width: 100%;
                display: inline-block;
                object-fit: cover;
                border-radius: 16px;
            }

            .lnk-img-close-icon {
                position: absolute;
                right: -15px;
                top: -12px;
                font-size: 48px;
                color: #2F69F8;
            }
        }
        .lnk-img-item-camera-wrapper {
            border-radius: 16px;
            overflow: hidden;

            .add-image-btn {
                width: 100%;
                height: 100%;
                background: #F2F2F2;
                @include flex-center-center;
                .add-image-icon {
                    color: #BFBFBF;
                    font-weight: bold;
                    font-size: 50px;
                }
            }
        }
    }
</style>
