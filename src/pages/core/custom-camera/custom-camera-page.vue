<template>
    <!--  图片上传组件-上传到cos-需要加水印-拍照时可以看到水印信息--->
    <link-page class="custom-camera-page">
        <!--相机模块-->
        <view class="camera-container" :style="{'display': painterFlag ? '': 'none'}">
            <camera mode="normal" :device-position="devicePosition"
                    style="height: 100vh"
                    class="camera-body" :flash="flashFlag"></camera>
            <cover-view class="img-cover-view">
                <cover-view class="nav-title" :style="{'height': navBarHeight + 'px'}">
                    <cover-view class="title"
                                :style="{'line-height': navBarHeight + 'px', 'padding-top': statusBarHeight + 'px'}">相机
                    </cover-view>
                </cover-view>
                <cover-view class="controls">
                    <cover-view class="flashlight" @tap="flashControls">
                        <cover-image class="controls-image" :src="flashAuto" v-if="flashFlag === 'auto'"></cover-image>
                        <cover-image class="controls-image" :src="flashOn" v-if="flashFlag === 'on'"></cover-image>
                        <cover-image class="controls-image" :src="flashOff" v-if="flashFlag === 'off'"></cover-image>
                    </cover-view>
                    <cover-view class="use-photo-album" @tap="chooseAlbum" v-if="useAlbumFlag">使用相册</cover-view>
                </cover-view>
                <cover-view class="notice-text">
                    <cover-image class="line" :src="leftLine"></cover-image>
                    <cover-view class="text">
                        <cover-view>{{addressData.city}}</cover-view>
                        <cover-view class="notice-time">{{time}}</cover-view>
                        <cover-view>{{date}} {{week}}</cover-view>
                    </cover-view>
                </cover-view>
                <cover-view class="notice-right-bottom">
                    <cover-image class="shade-img" :src="textBgCover"></cover-image>
                    <cover-view class="text">{{moduleName}}</cover-view>
                    <cover-view class="text-position">
                        <cover-view>{{userInfo.postnName}}</cover-view>
                        <cover-view>-</cover-view>
                        <cover-view>{{userInfo.firstName}}</cover-view>
                    </cover-view>
                    <cover-view class="text">{{useModuleName}}</cover-view>
                    <cover-view class="text address">{{addressDataFull}}</cover-view>
                </cover-view>
                <cover-view class="take-photo-container" v-show="showBtnFlag">
                    <cover-view class="cancel" @tap="cancelTakePhoto('close')">取消</cover-view>
                    <cover-view class="take-photo" @tap="takePhoto">
                        <cover-view class="inline-btn"></cover-view>
                    </cover-view>
                    <cover-view class="change-camera" @tap="deviceControls">
                        <cover-image class="device-position" :src="devicePositionImg"></cover-image>
                    </cover-view>
                </cover-view>
            </cover-view>
        </view>
        <!--添加水印-->
        <view class="nav-title" :style="{'height': navBarHeight + 'px'}" v-if="!painterFlag">
            <view class="title" :style="{'line-height': navBarHeight + 'px', 'padding-top': statusBarHeight + 'px'}">
                相机
            </view>
        </view>
        <view class="case-container" v-if="!painterFlag" :style="{'height': windowHeight - 144 + 'px'}">
            <view class="case-item" v-for="(item, index) in srcList" :key="index" :data-index="index">
                <view class="iconfont icon-close-circle-fill" @tap="deleteImg(index)"></view>
                <view :class="isCompressByImgInfo ? 'compress-bg' : 'case-bg'" @tap="previewImage(item.filePath)">
                    <image :src="item.filePath" alt="" lazy-load="true" :mode="isCompressByImgInfo ? 'aspectFit': 'scaleToFill'" ></image>
                </view>
                <view class="list-page">{{index+1}} / {{srcList.length}}</view>
            </view>
        </view>
        <view class="continue-take-photo" v-if="!painterFlag">
            <view class="btn-cancel" @tap="cancelTakePhoto('cancel')">取消</view>
            <view class="btn-continue" v-if="continueFlag" @tap="continueTakePhoto">继续拍照</view>
            <view class="btn-continue" v-else></view>
            <view class="btn-use" @tap="useImage">使用照片</view>
        </view>
        <view v-if="painterFlag">
            <painter :palette="paletteData" @imgOK="onImgOK" @imgErr="imgErr"></painter>
        </view>
    </link-page>
</template>

<script>
    import {getCurrentCoordinate, reverseTMapGeocoder} from '../../../utils/locations-tencent'
    import Taro from '@tarojs/taro'
    import cos from '../../../utils/imgCos'
    import {imgConfig} from "../../../utils/imgConfig";

    export default {
        name: "custom-camera",
        data() {
            // 选择相册照片时是否取消水印, 默认不取消
            let drawWatermarkCancleFlag = this.pageParam.drawWatermarkCancleFlag;
            if (this.$utils.isEmpty(drawWatermarkCancleFlag)) {
                drawWatermarkCancleFlag = false;
            }
            return {
                isCompressByImgInfo: false,                                                                         // 是否根据图片的原始高度宽度进行压缩
                windowHeight: this.$device.systemInfo.windowHeight,                                                 // 屏幕高度
                statusBarHeight: this.$device.systemInfo.statusBarHeight / 2,                                       // 状态栏高度
                navBarHeight: this.$device.systemInfo.statusBarHeight + 44,                                         // 顶部title高度
                coordinate: null,                                                                                   // 定位信息
                useAlbumFlag: this.pageParam.useAlbumFlag,                                                          // 是否使用相册标志
                moduleName: this.pageParam.moduleName,                                                              // 模块名称
                useModuleName: this.pageParam.useModuleName,                                                        // 使用模块
                timer: null,                                                                                        // 计时器
                timestamp: null,                                                                                    // 服务器获取时间戳
                dateTime: null,                                                                                     // 时间戳
                paletteData: {},                                                                                    // 绘制水印对象
                painterWidth: 0,                                                                                    // 绘制图片宽度
                painterHeight: 0,                                                                                   // 绘制图片高度
                takePhotoFlag: false,                                                                               // 相机控制参数
                srcList: [],                                                                                        // 拍照图片路径
                devicePosition: 'back',                                                                             // 摄像头方向
                addressData: '',                                                                                    // 地址数据
                addressDataFull: '',                                                                                // 地址数据
                userInfo: '',                                                                                       // 用户信息
                useImageFlag: false,                                                                                // 使用照片参数
                useImageOneTap: false,                                                                              // 使用图片按钮一次点击
                time: '',                                                                                           // 时间
                date: '',                                                                                           // 日期
                week: '',                                                                                           // 星期
                creamHeight: this.$device.systemInfo.screenHeight,                                                  // 相机高度
                painterFlag: true,                                                                                  // 绘制参数
                devicePositionImg: this.$imageAssets.devicePositionImage,                                           // 相机方向转换
                textBgCover: this.$imageAssets.clarityShadeImage,                                                   // 绘制水印底部遮罩
                leftLine: this.$imageAssets.lineImage,                                                              // 绘制水印左上竖线
                flashAuto: this.$imageAssets.flashAutoImage,                                                        // 闪光灯自动
                flashOn: this.$imageAssets.lightImage,                                                             // 闪光灯打开
                flashOff: this.$imageAssets.closeImage,                                                             // 闪光灯关闭
                flashFlag: 'auto',                                                                                  // 闪光灯开关
                saveImgOkOnce: true,                                                                                // 绘制水印函数控制参数
                showBtnFlag: false,                                                                                 // 获取到地理位置信息之后显示拍照按钮
                src: '',                                                                                            // 绘制水印背景图
                continueFlag: this.pageParam.continueFlag,                                                          // 继续拍照按钮
                clickTakePhotoBtn: true,
                drawWatermarkCancleFlag,
            }
        },
        async created() {
            console.log('moduleId', this.pageParam.moduleId);
            console.log('rootId', this.pageParam.rootId);
            this.dateTime = await this.getTimestamp();
            this.time = this.$date.format(this.dateTime, 'HH:mm');
            this.date = this.$date.format(this.dateTime, 'YYYY.MM.DD');
            this.week = '星期' + '日一二三四五六'.charAt(this.dateTime.getDay());
            this.userInfo = Taro.getStorageSync('token').result;
            if (!this.$utils.isEmpty(this.pageParam.addressWaterMark)) {
                this.addressDataFull = this.pageParam.addressWaterMark;
                this.showBtnFlag = true
            } else {
                await this.getAddress();
            }
            this.isCompressByImgInfo = this.pageParam.isCompressByImgInfo;
            this.painterWidth = this.$device.systemInfo.screenWidth * (1 / 2);
            this.painterHeight = this.$device.systemInfo.windowHeight * (1 / 2);
        },
        async onLoad() {},
        onShow() {
            // 定时刷新相机水印时间,时间间隔是30秒
            const that = this;
            this.timer = setInterval(function () {
                that.timestamp = that.timestamp + 29000;
                that.time = that.$date.format(new Date(that.timestamp), 'HH:mm')
            }, 30000)
        },
        destroyed() {
            // 清除计时器
            clearInterval(this.timer);
        },
        methods: {
            /**
             *  @description: 获取图片信息
             *  @author: 马晓娟
             *  @date: 2021/1/11 下午4:01
             */
            async getImageInfo (filePath) {
                try {
                    return await this.$taro.getImageInfo({src: filePath})
                } catch (e) {
                    const msg = '获取图片信息失败';
                    this.$showError(msg);
                    console.error(msg);
                    throw e
                }
            },
            /**
             *  @description: 图片压缩比例
             *  @author: 马晓娟
             *  @date: 2021/1/11 下午4:01
             */
            async compressImage (imgInfo) {
                let sourceSize = await this.getImageInfo(imgInfo.tempFiles[0].path);
                //组件本身不压缩 最终this.$image.compress处理压缩
                // let compressWidth = 0;
                // let compressHeight = 0;
                // if (sourceSize.width >= sourceSize.height) { // 如果上传图片的宽度大于高度，则按照宽度进行比例压缩
                //     compressWidth = this.$device.systemInfo.screenWidth;
                //     compressHeight = sourceSize.height / sourceSize.width * compressWidth
                // } else { // 否则按照屏幕高度进行比例压缩
                //     compressHeight = this.$device.systemInfo.windowHeight * (1 / 2);
                //     compressWidth = sourceSize.width / sourceSize.height * compressHeight
                // }
                // this.painterWidth = compressWidth;
                // this.painterHeight = compressHeight;
                this.painterWidth = sourceSize.width;
                this.painterHeight = sourceSize.height;
            },
            /**
             * 上传图片key
             * <AUTHOR>
             * @date 2020-07-02
             */
            getRandFileName(filePath) {
                let extIndex = filePath.lastIndexOf('.');
                let extName = extIndex === -1 ? '' : filePath.substr(extIndex);
                return `${this.pageParam.moduleId}-${this.$utils.uuid()}-${parseInt(Math.random() * 10000)}-${this.userInfo.id}-${this.$date.format(new Date(Date.now()), 'HH:mm:ss')}-${this.$date.format(new Date(Date.now()), 'YYYY.MM.DD')}${extName}`;
            },
            /**
             * 获取地址
             * <AUTHOR>
             * @date 2020-07-14
             */
            async getAddress() {
                this.coordinate = await getCurrentCoordinate();
                // 校验用户是否授权地理位置
                if (!this.$utils.isEmpty(this.coordinate.latitude) && !this.$utils.isEmpty(this.coordinate.longitude)) {
                    let address = await reverseTMapGeocoder(this.coordinate.latitude, this.coordinate.longitude, '照片加水印');
                    this.addressData = address['originalData'].result.addressComponent;
                    this.addressDataFull = address['originalData'].result.formatted_address;
                    this.showBtnFlag = true
                }
            },
            /**
             * 获取时间戳
             * <AUTHOR>
             * @date 2020-07-06
             */
            async getTimestamp() {
                const that = this;
                return new Promise(resolve => {
                    this.$http.post('gateway/time/currentTime', {
                        handleFailed: (error) => {
                            console.log('获取时间戳失败', error)
                        }
                    }).then(data => {
                        if (data.success) {
                            that.timestamp = data.result;
                            let date = new Date(data.result);
                            resolve(date)
                        }
                    })
                })
            },
            /**
             * 继续拍照
             * <AUTHOR>
             * @date 2020-06-07
             */
            continueTakePhoto() {
                this.painterFlag = true;
                this.src = '';
                this.paletteData = {}
            },
            /**
             * 使用图片
             * <AUTHOR>
             * @date 2020-07-09
             */
            async useImage() {
                const that = this;
                that.useImageFlag = true;
                // 防抖
                if (this.useImageOneTap) return;
                that.useImageOneTap = true;
                let trueFlag = [];
                this.$utils.showLoading('图片上传中...');
                for (const item of that.srcList) {
                    let data = await this.$image.insertUploadImgRecord(item);
                    console.log(data);
                    if (data.success) {
                        trueFlag.push(true);
                    }
                }
                this.$utils.hideLoading();
                that.$utils.showAlert('上传完成');
                console.log('上传图片列表',that.srcList)
                if (that.srcList.length === trueFlag.length) {
                    setTimeout(function () {
                        let param = {
                            useImageFlag: that.useImageFlag,
                            coordinate: that.coordinate,
                            callBackModule: that.pageParam.moduleType
                        };
                        that.$nav.back(param);
                    }, 1000)
                }
            },
            /**
             * 删除图片
             * <AUTHOR>
             * @date 2020-07-02
             * @param index
             */
            deleteImg(index) {
                this.srcList.splice(index, 1);
                if (this.srcList.length === 0) {
                    this.painterFlag = true
                }
            },
            /**
             * 绘制完成
             * <AUTHOR>
             * @date 2020-06-07
             */
            async onImgOK(e) {
                const that = this;
                if (that.saveImgOkOnce) {
                    console.log('绘制成功', e);
                    console.log('绘制成功', e.detail.path);
                    const {source, compress} = await this.$image.compress({ // 压缩图片
                        filePath: e.detail.path,
                    });
                    let uploadImageOptions = {
                        filePath: '',                                        // 上传原图图片路径
                        formData: {
                            headId: this.pageParam.moduleId,                 // 上传图片headId
                            moduleType: that.pageParam.moduleType            // 上传图片类型
                        },
                        Key: null,                                           // 上传原图key
                        smallKey: null,                                      // 上传缩略图key
                        smallImgUrl: '',                                     // 上传缩略图图片路径
                        rootId: this.pageParam.rootId                        // 来源id——拜访执行模块是拜访id
                    };
                    uploadImageOptions.filePath = source.path;
                    uploadImageOptions.smallImgUrl = compress.path;
                    uploadImageOptions.Key = that.getRandFileName(source.path);
                    uploadImageOptions.smallKey = that.getRandFileName(compress.path);
                    uploadImageOptions.dataSource = "Live";//现场拍照-值列表 PIC_SOURCE
                    that.srcList.push(uploadImageOptions);
                    that.painterFlag = false;
                    that.saveImgOkOnce = false;
                    that.clickTakePhotoBtn = true;
                }
            },
            /**
             * 绘制报错函数
             * <AUTHOR>
             * @date 2020-11-05
             * @param e 绘制报错信息
             */
            imgErr(e) {
                console.log('imgErr');
                console.log(e);
            },
            /**
             * 绘制水印
             * <AUTHOR>
             * @date 2020-06-07
             */
            painterPhone() {
                const that = this;
                that.paletteData = {
                    width: that.painterWidth + 'px',
                    height: that.painterHeight + 'px',
                    background: that.src,
                    views: [
                        {
                            type: 'image',
                            url: that.leftLine,
                            css: {
                                width: '1rpx',
                                height: '32px',
                                top: '12px',
                                left: '8rpx',
                                mode: 'scaleToFill'
                            }
                        },
                        {
                            type: 'text',
                            text: that.addressData.city,
                            css: {
                                color: '#FFFFFF',
                                background: 'rgba(0,0,0,0)',
                                top: '12px',
                                left: '16rpx',
                                fontSize: '14rpx',
                                maxLines: 1,
                                textAlign: 'left'
                            }
                        },
                        {
                            type: 'text',
                            text: that.time,
                            css: {
                                color: '#FFFFFF',
                                background: 'rgba(0,0,0,0)',
                                top: '22px',
                                left: '16rpx',
                                borderColor: '#000000',
                                fontSize: '20rpx',
                                maxLines: 1,
                                textAlign: 'left'
                            }
                        },
                        {
                            type: 'text',
                            text: that.date + ' ' + that.week,
                            css: {
                                color: '#FFFFFF',
                                background: 'rgba(0,0,0,0)',
                                top: '36px',
                                left: '16rpx',
                                borderColor: '#000000',
                                fontSize: '14rpx',
                                maxLines: 1,
                                textAlign: 'left'
                            }
                        },
                        {
                            type: 'image',
                            url: that.textBgCover,
                            css: {
                                width: that.painterWidth + 'px',
                                height: '200px',
                                top: that.painterHeight - 65 + 'px',
                                mode: 'scaleToFill'
                            }
                        },
                        {
                            type: 'text',
                            text: that.moduleName,
                            css: {
                                color: '#FFFFFF',
                                background: 'rgba(0,0,0,0)',
                                top: that.painterHeight - 60 + 'px',
                                right: '16rpx',
                                fontSize: '14rpx',
                                maxLines: 2,
                                textAlign: 'right'
                            }
                        },
                        {
                            type: 'text',
                            text: `${that.userInfo.postnName}-${that.userInfo.firstName}`,
                            css: {
                                color: '#FFFFFF',
                                background: 'rgba(0,0,0,0)',
                                top: that.painterHeight - 49 + 'px',
                                right: '16rpx',
                                borderColor: '#000000',
                                fontSize: '14rpx',
                                maxLines: 1,
                                textAlign: 'right'
                            }
                        },
                        {
                            type: 'text',
                            text: that.useModuleName,
                            css: {
                                color: '#FFFFFF',
                                background: 'rgba(0,0,0,0)',
                                top: that.painterHeight - 38 + 'px',
                                right: '16rpx',
                                rotate: '0',
                                borderColor: '#000000',
                                fontSize: '14rpx',
                                fontWeight: 'normal',
                                maxLines: 1,
                                textAlign: 'right'
                            }
                        },
                        {
                            type: 'text',
                            text: that.addressDataFull,
                            css: {
                                top: that.painterHeight - 27 + 'px',
                                width: that.painterWidth - 10 + 'px',
                                fontSize: '14rpx',
                                right: '16rpx',
                                maxLines: 2,
                                color: '#FFFFFF',
                                lineHeight: '12px',
                                align: 'right',
                                background: 'rgba(0,0,0,0)',
                            }
                        }
                    ]
                };
                console.log('paletteData', that.paletteData);
            },
            /**
             * 拍照
             * <AUTHOR>
             * @date 2020-06-08
             */
            takePhoto() {
                console.log('拍照进入');
                const that = this;
                console.log('判断外clickTakePhotoBtn', that.clickTakePhotoBtn);
                if (that.clickTakePhotoBtn) {
                    console.log('判断里clickTakePhotoBtn', that.clickTakePhotoBtn);
                    const ctx = wx.createCameraContext();
                    ctx.takePhoto({
                        quality: 'high',
                        success: (res) => {
                            console.log('拍照成功', res);
                            that.clickTakePhotoBtn = false;
                            that.src = res.tempImagePath;
                            if (!this.$utils.isEmpty(that.src)) {
                                that.saveImgOkOnce = true;
                                that.painterPhone()
                            }
                        },
                        fail(res) {
                            console.log('拍照', res)
                        }
                    })
                }
            },
            /**
             * 取消拍照
             * <AUTHOR>
             * @date 2020-07-06
             */
            cancelTakePhoto(flag) {
                const that = this;
                switch (flag) {
                    case 'close':
                        let param = {
                            useImageFlag: this.useImageFlag,
                            coordinate: this.coordinate,
                            callBackModule: this.pageParam.moduleType
                        };
                        this.$nav.back(param);
                        that.takePhotoFlag = false;
                        break;
                    case 'cancel':
                        that.painterFlag = true;
                        that.srcList = [];
                        break
                }
            },
            /**
             * 闪光灯控制
             * <AUTHOR>
             * @date 2020-06-07
             */
            flashControls() {
                switch (this.flashFlag) {
                    case 'on':
                        this.flashFlag = 'auto'
                        break
                    case 'off':
                        this.flashFlag = 'on'
                        break
                    case 'auto':
                        this.flashFlag = 'off'
                        break
                }
            },
            /**
             * 使用相册-加水印
             * <AUTHOR>
             * @date 2020-07-29
             * @param param
             */
            async chooseAlbum() {
                console.log("使用相册-加水印");
                console.log("this.drawWatermarkCancleFlag", this.drawWatermarkCancleFlag);
                const that = this;
                if (!this.drawWatermarkCancleFlag) {
                    let inOptions = {
                        count: 1, // 默认9
                        sizeType: ['original'], // 可以指定是原图还是压缩图，默认用原图
                        sourceType: ['album'], // 可以指定来源是相册还是相机，默认二者都有
                        success: function (res) {
                        }
                    };
                    let choseArr = await this.$image.chooseImage(inOptions);
                    this.src = choseArr.tempFiles[0].path;
                    await this.compressImage(choseArr);
                    that.saveImgOkOnce = true;
                    that.painterPhone();
                } else {
                    this.chooseAlbumKristenScott();
                }
            },
            /**
             * 使用相册-不加水印
             * <AUTHOR>
             * @date 2020-12-22
             * */
            async chooseAlbumKristenScott() {
                let sourceType = ['album'];
                // 选择图片属性
                const chooseImageOptions = {
                    sourceType: sourceType
                };
                const wxImages = await this.$image.chooseImage(chooseImageOptions);
                this.$utils.showLoading('图片上传中...');
                const compressArr = await Promise.all(wxImages.tempFilePaths.map(filePath => this.$image.compress({filePath})));
                for (const item of compressArr) {
                    let uploadImageOptions = {
                        filePath: '',                                        // 上传原图图片路径
                        formData: {
                            headId: this.pageParam.moduleId,                 // 上传图片headId
                            moduleType: this.pageParam.moduleType            // 上传图片类型
                        },
                        Key: null,                                           // 上传原图key
                        smallKey: null,                                      // 上传缩略图key
                        smallImgUrl: '',                                     // 上传缩略图图片路径
                        rootId: this.pageParam.rootId                        // 来源id——拜访执行模块是拜访id
                    };
                    uploadImageOptions.filePath = item.source.path;
                    uploadImageOptions.smallImgUrl = item.compress.path;
                    uploadImageOptions.Key = this.getRandFileName(item.source.path);
                    uploadImageOptions.smallKey = this.getRandFileName(item.compress.path);
                    uploadImageOptions.dataSource = "Album";//相册上传-值列表 PIC_SOURCE
                    this.srcList.push(uploadImageOptions);
                }
                this.$utils.hideLoading();
                this.painterFlag = false;
                this.saveImgOkOnce = false;
                this.clickTakePhotoBtn = true;
            },
            /**
             * 摄像头方向
             * <AUTHOR>
             * @date 2020-06-07
             */
            deviceControls() {
                switch (this.devicePosition) {
                    case 'back':
                        this.devicePosition = 'front';
                        break
                    case 'front':
                        this.devicePosition = 'back';
                        break
                }
            },
            /**
             * 预览图片
             * <AUTHOR>
             * @date 2020-05-07
             * @param imgUrl
             */
            previewImage(imgUrl) {
                wx.previewImage({
                    current: imgUrl, // 当前显示图片的http链接
                    urls: [imgUrl] // 需要预览的图片http链接列表
                })
            },
        }
    }
</script>

<style lang="scss">
    .custom-camera-page {
        height: 100vh;
        overflow: hidden;
        background: black;

        .nav-title {
            width: 100%;
            text-align: center;
            color: #ffffff;
            background: #2F69F8;
        }

        .camera-container {
            background: black;

            .camera-body {
                margin-top: 0;
                width: 100%;
            }

            .img-cover-view {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                width: 100%;
                height: 100vh;
                z-index: 9999;
                background: rgba(0, 0, 0, 0);
                color: #ffffff;

                .controls {
                    width: 100%;
                    background: #000;
                    padding-bottom: 24px;
                    display: flex;
                    @include space-between;

                    .flashlight {
                        width: 50px;
                        height: 50px;
                        margin-left: 24px;
                        margin-top: 24px;

                        .controls-image {
                            width: 100%;
                            height: 100%;
                        }
                    }

                    .use-photo-album {
                        padding-top: 38px;
                        color: #FFF;
                        font-size: 28px;
                        padding-right: 28px;
                    }
                }

                .notice-text {
                    display: flex;

                    .text {
                        font-size: 28px;
                        color: #ffffff;
                        text-align: left;
                        padding-left: 16px;
                        padding-top: 24px;
                        line-height: 60px;

                        .notice-time {
                            font-size: 32px;
                            line-height: 50px;
                        }
                    }

                    .line {
                        width: 2px;
                        height: 120px;
                        margin-left: 40px;
                        margin-top: 20px;
                    }
                }

                .notice-right-bottom {
                    display: -webkit-box;
                    display: -ms-flexbox;
                    display: flex;
                    -webkit-box-pack: end;
                    -ms-flex-pack: end;
                    justify-content: flex-end;
                    -webkit-box-align: end;
                    -ms-flex-align: end;
                    align-items: flex-end;
                    -webkit-box-orient: vertical;
                    -webkit-box-direction: normal;
                    -ms-flex-direction: column;
                    flex-direction: column;
                    position: fixed;
                    bottom: 0;
                    right: 0;
                    margin-bottom: 110px;
                    width: 100%;
                    font-size: 28px;
                    color: #ffffff;
                    text-align: right;
                    padding: 16px 32px 100px;

                    .text {
                        position: relative;
                        line-height: 40px;
                        z-index: 99999;
                    }

                    .address {
                        padding-left: 24px;
                        white-space: normal;
                        word-break: break-all;
                    }

                    .text-position {
                        display: flex;
                        position: relative;
                        line-height: 40px;
                        z-index: 99999;
                    }

                    .shade-img {
                        position: fixed;
                        bottom: 0;
                        height: 400px;
                        width: 100%;
                        right: 0;
                        left: 0;
                    }
                }

                .take-photo-container {
                    position: fixed;
                    padding-top: 20px;
                    padding-bottom: 50px;
                    width: 100%;
                    bottom: 0;
                    background: #000;
                    display: -webkit-box;
                    display: -ms-flexbox;
                    display: flex;
                    -webkit-box-pack: center;
                    -ms-flex-pack: center;
                    -webkit-box-align: center;
                    -ms-flex-align: center;
                    align-items: center;
                    justify-content: space-around;

                    .cancel {
                        font-size: 28px;
                        color: #ffffff;
                    }

                    .take-photo {
                        background: #fff;
                        width: 110px;
                        height: 110px;
                        margin-left: 10%;
                        margin-right: 10%;
                        border-radius: 50%;
                        display: -webkit-box;
                        display: -ms-flexbox;
                        display: flex;
                        -webkit-box-pack: center;
                        -ms-flex-pack: center;
                        justify-content: center;
                        -webkit-box-align: center;
                        -ms-flex-align: center;
                        align-items: center;

                        .inline-btn {
                            width: 90px;
                            height: 90px;
                            border-radius: 50%;
                            border: 1px solid black;
                        }
                    }

                    .change-camera {
                        .device-position {
                            width: 50px;
                            height: 50px;
                        }
                    }
                }
            }
        }

        .case-container {
            display: flex;
            overflow-x: scroll;
            -webkit-overflow-scrolling: touch;
            overflow-y: hidden;

            .case-item {
                flex-shrink: 0;
                width: 750px;
                overflow: hidden;

                .icon-close-circle-fill {
                    line-height: 5vh;
                    font-size: 32px;
                    color: #ffffff;
                    position: relative;
                    background: black;
                    text-align: right;
                    padding-right: 32px;
                }

                .case-bg {
                    width: 100%;
                    height: 90%;
                    text-align: center;
                    background: rgba(0, 0, 0, 1);

                    image {
                        width: 90%;
                        height: 100%;
                    }
                }
                .compress-bg{
                    width: 100%;
                    height: 90%;
                    text-align: center;
                    background: rgba(0, 0, 0, 1);
                    display: flex;
                    align-items: center;
                    image {
                        width: 100%;
                        height: 100%;
                    }
                }

                .list-page {
                    width: 100%;
                    height: 6%;
                    color: #ffffff;
                    background: rgba(0, 0, 0, 1);
                    text-align: center;
                }
            }
        }

        .continue-take-photo {
            height: 144px;
            display: flex;
            text-align: center;
            padding-top: 10px;
            background: rgba(0, 0, 0, 1);
            font-size: 28px;
            color: #ffffff;

            .btn-cancel {
                width: 26%;
                text-align: left;
                padding-left: 40px;
            }

            .btn-continue {
                width: 48%;
            }

            .btn-use {
                width: 26%;
            }
        }
    }
</style>
