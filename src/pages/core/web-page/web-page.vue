<template>
  <link-page>
    <web-view :src="url" type="im" :onMessage="message">
      <!-- <cover-view>
        <ma-navigation-bar :backVisible="true" :titleColor="'#fff'" :backgroundImg="$imageAssets.homeMenuBgImage" :zIndex="zIndex" :title="'流程审批'" :udf="udfBack">
        </ma-navigation-bar>
      </cover-view> -->
    </web-view>
  </link-page>
</template>

<script>
import { env } from "../../../../env";
import MaNavigationBar from "../../lj-market-activity/ma-navigation-bar/ma-navigation-bar.vue";
import Taro from "@tarojs/taro";
import { ComponentUtils } from "link-taro-component";
/**
 * 一个专门的 core page 用来显示网页
 * 通过跳转参数url就可以指定网页打开的地址，需要确保这个url在小程序管理控制台的域名管理中添加这个域名
 * <AUTHOR>
 * @date    2023/8/21 22:09
 */
export default {
  components: { MaNavigationBar },
  data () {
    let sceneSourceForNavigation = "other";//默认other
    if (!this.$utils.isEmpty(this.pageParam.sceneSourceForNavigation)) {
      sceneSourceForNavigation = this.pageParam.sceneSourceForNavigation;
    }
    return {
      sceneSourceForNavigation,
      zIndex: ComponentUtils.nextIndex(),
      url: this.pageParam.url,
    }
  },
  async onLoad (options) {
    const param = {
      requestId: options.requestId || this.pageParam.requestId,
      isFanwei: options.isFanwei || this.pageParam.isFanwei,
      url: options.url || this.pageParam.url,
      approvalType: options.approvalType || this.pageParam.approvalType,
    }
    if (param.isFanwei == 1) {
      const token = await this.$utils.getFanweiToken();
      param.url = `${env.fanweiUrl || 'https://xtpttest.lzlj.com/papi/open/singleSignon'}?singleToken=${token}&oauthType=singlesign&redirect_uri=/sp/workflow/flowpage/fullView/${param.requestId}`;
    } else {
    }
    this.url = param.url
    console.log(param, this.url, '-----pageparam.url2', options, env, this.pageParam)
  },
  methods: {
    message (e) {
      console.log('网页发来的消息', e)
    },
    /**
       * 自定义返回函数
       * @songyanrong
       * @date 2024-10-22
       * */
    udfBack () {
      if (this.sceneSourceForNavigation === 'other' || this.sceneSourceForNavigation === 'caseNewMarketActivity') {
        this.$nav.back();
      } else {
        let pages = Taro.getCurrentPages();    //获取当前页面信息栈
        let targetIndex = pages.findIndex(function (item) {
          return item.route === "pages/lj-perform-case/perform-case/perform-case-list-page";
        });
        if (targetIndex === -1) {
          return this.$nav.backAll()
        }
        const num = Number(pages.length - (Number(targetIndex) + 1));
        setTimeout(() => {
          this.$bus.$emit('marketActivityListRefresh');
          this.$nav.back(null, num);
        }, 1000)
      }
    },
  }
}
</script>
<style scoped>

</style>
