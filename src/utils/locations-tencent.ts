
import QQMapWX from './lib/Tmap/qqmap-wx-jssdk.min'
import {env} from '../../env'
import {$utils} from "link-taro-component";
import {aegis} from "@/utils/aegis";
import Taro from "@tarojs/taro";

let tMap = new QQMapWX({
    key: env.qqMapKey
});
let lastLocation = {} as any; //最后一次调用逆地址解析的经纬度 以及解析之后的地址


/**
 * 根据经纬度获取地点信息，逆地址解析-腾讯
 * <AUTHOR>
 * @date 2022年11月22日
 */
export async function reverseTMapGeocoder(lat:any, lnt:any, scene: string='其他') {
    return new Promise(async(resolve, reject) => {
        //获取参数配置：是否使用缓存的逆解析地址
        const globalCfgProperty = Taro.getStorageSync('link-global-cfgProperty');
        if(globalCfgProperty.useCachedReverseAddress === 'Y'){
            const addr =  await useCachedAddress(lat, lnt)
            if(addr){
                return resolve(addr)
            }
        }
        console.log('未使用缓存地址，调接口重新获取')
        tMap.reverseGeocoder({
            location: lat + ',' + lnt,
            success: function (res:any) {
                function processingNUll(data){
                    return data?data:'';
                }
                res.wxMarkerData=[
                    {
                        address:  processingNUll(res.result.address),
                        latitude: processingNUll(res.result.location.lat),
                        longitude: processingNUll(res.result.location.lng)
                    }
                ];
                res.originalData={
                    result:{
                        addressComponent:{
                            adcode: processingNUll(res.result.ad_info.adcode),
                            city:  processingNUll(res.result.ad_info.city) ,
                            country: processingNUll(res.result.ad_info.nation),
                            district: processingNUll(res.result.ad_info.district),
                            province: processingNUll(res.result.ad_info.province),
                            street: processingNUll(res.result.address_component.street),
                            street_number: processingNUll(res.result.address_component.street_number).replace(processingNUll(res.result.address_component).street,''),

                        },
                        formatted_address: processingNUll(res.result.address),
                        location:{
                            lat: processingNUll(res.result.location.lat),
                            lng: processingNUll(res.result.location.lng)
                        },
                        poiRegions:[
                            {
                                name: res.result.address_reference ? processingNUll(res.result.address_reference.landmark_l2?.title) : ''
                            }
                        ],
                        sematic_description: res.result.address_reference ? (processingNUll(res.result.address_reference.landmark_l2?.title) +
                            processingNUll(res.result.address_reference.landmark_l2?._dir_desc) +
                            processingNUll(res.result.address_reference.landmark_l2?._distance.toString()) + '米') : processingNUll(res.result.address)
                    }
                }
                if (globalCfgProperty.aegisReportLocations === 'Y') {
                    aegis.report({
                        msg: `逆地址解析：${JSON.stringify(scene)}`,
                        trace: 'log'
                    });
                }
                if(globalCfgProperty.useCachedReverseAddress === 'Y'){
                    cacheReverseGeocodedAddress(lat, lnt, res)
                }
                resolve(res)
            },
            fail: function (res:any) {
                console.log(res)
                reject(res)
            }
        })
    })
}

/**
 * 根据经纬度获取地点信息，逆地址解析 百度不使用了 切换成腾讯
 * @param lat 纬度
 * @param lnt 经度
 * @returns {Promise<any>}
 */
export async function FQreverseTMapGeocoder (lat:any, lnt:any) {
    // return new Promise((resolve, reject) => {
    //     // @ts-ignore
    //     bMap.regeocoding({
    //         location: lat + ',' + lnt,
    //         success: function (res:any) {
    //             console.log(res)
    //             resolve(res)
    //         },
    //         fail: function (res:any) {
    //             console.log(res)
    //             reject(res)
    //         }
    //     })
    // })
}

/**
 * 根据输入智能联想地址
 * @param address
 * @returns {Promise<any>}
 */
export async function getBMapSuggestion (address:any) {
    return new Promise((resolve, reject) => {
        // @ts-ignore
        // 关闭百度地图api
        // bMap.suggestion({
        //     query: address,
        //     success: function (res:any) {
        //         console.log(res)
        //         resolve(res)
        //     },
        //     fail: function (res:any) {
        //         console.log(res)
        //         reject(res)
        //     }
        // })
    })
}

/**
 * 获取当前位置坐标(经纬度)
 * @return {Promise<void>}
 */
export async function getCurrentCoordinate () {
    return new Promise((resolve, reject) => {
        /**
         **调用当前经纬度
         **/
        wx.getLocation({
            type: 'gcj02',
            success: async function (res) {
                console.log('调用腾讯api定位', '定位成功', res);
                resolve(res)
            },
            fail: function (res) {
                console.log('调用腾讯api定位', '定位失败', res);
                reject(res)
            }
        })
    })
}

/**
 * 判断用户是否已经打开地理位置授权信息
 * <AUTHOR>
 * @date 2019/3/21
 */
export async function checkUserLocation () {
    return new Promise((resolve, reject) => {
        wx.getSetting({
            success: (res) => {
                if (res.authSetting['scope.userLocation']) {
                    resolve(true)
                } else {
                    resolve(false)
                }
            }
        })
    })
}

export async function openSetting () {
    wx.openSetting({
        success (res) {
            console.log(res.authSetting)
            // 设置成功之后,将设置信息返回
        },
        fail () {
            $utils.showAlert('打开授权设置失败,请重试!', { icon: 'none' })
        }
    })
}
/**
  * 计算两个经纬度之间的距离
  * <AUTHOR>
  * @date 2020-11-04
  * @param la1 纬度1
  * @param lo1 经度1
  * @param la2 纬度2
  * @param lo2 经度2
*/
export async function distance(la1, lo1, la2, lo2) {
    return new Promise(resolve => {
        let La1 = la1 * Math.PI / 180.0;
        let La2 = la2 * Math.PI / 180.0;
        let La3 = La1 - La2;
        let Lb3 = lo1 * Math.PI / 180.0 - lo2 * Math.PI / 180.0;
        let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(La3 / 2), 2) + Math.cos(La1) * Math.cos(La2) * Math.pow(Math.sin(Lb3 / 2), 2)));
        s = s * 6378.137;//地球半径
        s = Math.round(s * 10000) / 10000;
        console.log("计算结果",s);
        resolve(s)
    })
}

const X_PI = 3.14159265358979324 * 3000.0 / 180.0;
/**
 * 火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换
 * 即腾讯、谷歌、高德 转 百度
 * @param lat 纬度
 * @param lng 经度
 * @returns {*[]}
 */
export async function gcj02tobd09(lat, lng) {
    return new Promise(resolve => {
        lng = +lng;
        lat = +lat;
        const z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * X_PI);
        const theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * X_PI);
        const bdLng = z * Math.cos(theta) + 0.0065;
        const bdLat = z * Math.sin(theta) + 0.006;
        resolve ({bdLat, bdLng})
    })
}

/**
 * 百度坐标系 (BD-09) 与 火星坐标系 (GCJ-02)的转换
 * 即 百度 转 谷歌、高德、腾讯
 * @param bdLat 百度纬度
 * @param bdLng 百度经度
 * @returns {}
 */
export async function bd09togcj02(bdLat, bdLng) {
    return new Promise(resolve => {
        bdLng = +bdLng;
        bdLat = +bdLat;
        const x = bdLng - 0.0065;
        const y = bdLat - 0.006;
        const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * X_PI);
        const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * X_PI);
        const lng = z * Math.cos(theta);
        const lat = z * Math.sin(theta);
        resolve({lat, lng})
    })
}
/**
 * 缓存逆解析地址
 * 每次调用成功之后，记录最后一次调用逆地址解析的经纬度，以及解析之后的地址
 * 每次调用成功之后，以经纬度小数点后六位作为KEY，缓存逆地址解析结果，缓存时间为5分钟；注意：最多缓存10个，超过10个去除过期时间最早的。
 * <AUTHOR>
 * @date    2025/4/8 14:33
 * @param latitude 此次需要解析的经度
 * @param longitude 此次需要解析的纬度
 * @param res 此次解析的结果
 */
 async function cacheReverseGeocodedAddress(latitude, longitude, res) {
    lastLocation = {
        lastLat: latitude,
        lastLnt: longitude,
        reverseTMapResult: res
    }
    const cacheJson = Taro.getStorageSync('cacheReverseAddress')
    // 将 JSON 字符串转换为数组
    const storedArray =cacheJson? JSON.parse(cacheJson): [];
    // 将数组还原为 Map
    const cacheMap = new Map(storedArray);
    if (cacheMap.size >= 10) {
        // 获取最早存入的键
        const firstKey = cacheMap.keys().next().value;
        // 删除最早存入的元素
        cacheMap.delete(firstKey);
    }
    //以经纬度小数点后六位作为KEY，缓存逆地址解析结果
    const cacheKey = `${latitude.toFixed(5)},${longitude.toFixed(5)}`;
    cacheMap.set(cacheKey, {reverseTMapResult: res, expire: new Date().getTime() + 5 * 60 * 1000})
    // 将 Map 转换为数组，数组元素为 [key, value] 形式的子数组
    const mapToArray = Array.from(cacheMap);
    // 将数组转换为 JSON 字符串
    const mapJson = JSON.stringify(mapToArray);
    Taro.setStorageSync('cacheReverseAddress', mapJson)
    console.log('存入缓存的mapToArray', mapToArray);
}

/**
 * 需要根据经纬度逆解析地址时使用缓存的已经解析成功的地址
 * 1、当前的经纬度与最后一次调用成功经纬度做距离计算，结果<20米，则直接返回最后一次解析地址结果
 * 2、根据当前的经纬度生成KEY到缓存对象中匹配是否存在：
 *①存在且在有效期则返回改结果，不继续执行后续逻辑
 *②存在且已经失效，则删除对应KEY，执行后续逻辑
 *③不存在，则执行后续逻辑
 * <AUTHOR>
 * @date    2025/4/8 14:33
 * @param lat 此次需要解析的经度
 * @param lnt 此次需要解析的纬度
 */
 async function useCachedAddress(lat, lnt){
    // 当前的经纬度与最后一次调用成功经纬度做距离计算，结果<20米，则直接返回最后一次解析地址结果
    if(lastLocation.reverseTMapResult){
        const {lastLat, lastLnt} = lastLocation
        //返回的距离单位为km
        const distance = await getDistance(lat, lnt,lastLat,lastLnt) as number;
        if(distance * 1000 < 20){
            console.log('使用最后一次调用成功解析地址1',lastLocation)
            return lastLocation.reverseTMapResult
        }
    }
    //根据当前的经纬度生成KEY到缓存对象中匹配是否存在
    const cacheJson = Taro.getStorageSync('cacheReverseAddress')
    // 如果缓存为空，则直接返回
    if(!cacheJson) return false
    // 将 JSON 字符串转换为数组  将数组还原为 Map
    const cacheMap = new Map(JSON.parse(cacheJson));
    // 以经纬度小数点后5位作为KEY，缓存逆地址解析结果,以第六位的话精度太高，微信定位达不到，每次都会不一样，没有缓存的必要，5~10米解析出的结果几乎没有差别
    const cacheKey = `${lat.toFixed(5)},${lnt.toFixed(5)}`;
    if(cacheMap.has(cacheKey)){
        // 存在且在有效期则返回改结果，不继续执行后续逻辑
        const cachedAddress = cacheMap.get(cacheKey) as { reverseTMapResult: any, expire: number | string}
        if(cachedAddress.expire > new Date().getTime()){
            console.log('使用缓存中匹配的key的地址',cachedAddress, cacheMap)
            return cachedAddress.reverseTMapResult
        } else {
            // 存在且已经失效，则删除对应KEY，执行后续逻辑
            cacheMap.delete(cacheKey)
        }
        Taro.setStorageSync('cacheReverseAddress', JSON.stringify(Array.from(cacheMap)))
    }
    return false
}

/**
 * 计算距离
 * <AUTHOR>
 * @date 2020-08-26
 * @desc 由经纬度计算两点之间的距离，la为latitude缩写，lo为longitude
 * @param la1 第一个坐标点的纬度
 * @param lo1 第一个坐标点的经度
 * @param la2 第二个坐标点的纬度
 * @param lo2 第二个坐标点的经度
 * @return (int)s   返回距离(单位千米或公里)
 * @tips 注意经度和纬度参数别传反了，一般经度为0~180、纬度为0~90
 */
async function getDistance(la1, lo1, la2, lo2) {
    return new Promise((resolve) => {
        let La1 = (la1 * Math.PI) / 180.0
        let La2 = (la2 * Math.PI) / 180.0
        let La3 = La1 - La2
        let Lb3 = (lo1 * Math.PI) / 180.0 - (lo2 * Math.PI) / 180.0
        let s =
            2 *
            Math.asin(
                Math.sqrt(
                    Math.pow(Math.sin(La3 / 2), 2) +
                    Math.cos(La1) * Math.cos(La2) * Math.pow(Math.sin(Lb3 / 2), 2)
                )
            )
        s = s * 6378.137 //地球半径
        s = Math.round(s * 10000) / 10000
        console.log('计算结果', s)
        resolve(s)
    })
}
