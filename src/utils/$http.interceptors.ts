import {HttpRequestConfig, HttpResponse} from "@/utils/http/type";
import Taro from "@tarojs/taro";
import {env} from "../../env";
import {showError} from "@/utils/showError";
import {aegis} from "@/utils/aegis";
import {Http} from 'link-taro-component'
import {isTimeoutString} from "@/utils/http/isTimeoutString";
import {showErrorMessage} from "@/utils/http/showErrorMessage";
import {isNetworkError} from "@/utils/http/isNetworkError";
import {$utils} from "@/utils/$utils";

// 指定超时URL
let timeOutUrlList = [];
// 查询超时URLList参数
const queryTimeoutUrlList = async() => {
    if ($utils.isNotEmpty(timeOutUrlList)) {
        return;
    }
    // 从参数配置中查询超时URL清单，使用$utils.getCfgProperty获取
    const list = await $utils.getCfgProperty('specialTimeOutUrlList', false);
    if ($utils.isNotEmpty(list)) {
        timeOutUrlList = list.split(',');
    }
};
export const HttpResponseInterceptor = async (response: HttpResponse) => {
    if (response._response.statusCode === 401) {
        // 多次获取token异常
        Taro.showModal({showCancel: false, title: '系统异常', content: '登录异常，请稍后重试。'})
        throw response
    }

    // success === false 接口内部错误，比如sql异常什么的
    // 400,405 系统错误，参数转化失败，不支持的请求method之类的
    // error 其他错误，有可能是网络异常
    if (!response.success || response.status === 400 || response.status === 500 || !!response.error) {
        let {error, success, result, message, _response} = response

        // 没有_response 可能是代码调用报错，跟请求没有关系
        if (!_response) {
            throw response
        }

        if (env.useRequestIdAppendToHttpResult) {
            const linkrequestid = _response?.config?.header?.linkrequestid
            if (!!linkrequestid) {
                const appendString = `\n[${linkrequestid}]`
                if (!!response.result) {
                    result = response.result = response.result + appendString
                }
                if (!!response.message) {
                    message = response.message = response.message + appendString
                }
                if (!!response.error) {
                    error = response.error = response.error + appendString
                }
                if (response.result == null && response.message == null && response.error == null) {
                    result = response.result = '接口异常' + appendString;
                }
            }
        }

        // 如果有手动处理异常的函数则手动处理，否则自动弹框提示接口异常
        if (!!_response.config.handleFailed) {
            _response.config.handleFailed(response)
        }
        // 自动显示报错信息，以及记录错误日志（todo 这个记录日志还没实现）
        if (_response.config.autoHandleError !== false) {
            let msg;

            if (!!response._response.config.errorMessage) {
                msg = typeof response._response.config.errorMessage === "function" ? response._response.config.errorMessage(response) : response._response.config.errorMessage
            } else {
                if (!success) {
                    // 接口内部错误
                    msg = String(error || result || message)
                } else {
                    // 系统错误或者网络错误
                    msg = String(error)
                }
            }
            aegis.report({
                msg: `接口:${response._response.config.url}出错，状态码${response._response.statusCode}`,
                ext1: JSON.stringify(msg),
                trace: 'error'
            });
            // $logger.error(`接口:${response._response.config.url}出错`, `状态码${response._response.statusCode}`, JSON.stringify(msg))
            showError(msg)
        }

        // 无论怎么样，都阻止代码执行下一步
        return Promise.reject(response)
    } else {
        // 接口调用成功，msg也需要附带requestId
        if (env.useRequestIdAppendToHttpResult) {
            let {_response} = response
            const linkrequestid = _response?.config?.header?.linkrequestid
            if (!!linkrequestid) {
                const appendString = `\n[${linkrequestid}]`
                if (!!response.msg) {
                    response.msg = response.msg + appendString
                }
            }
        }
    }
    return response
}

export const HttpResponseNativeErrorHandler = async (error: any) => {
    console.error('request native error', JSON.stringify(error))

    /**
     * 请求失败，这种异常属于网络异常，不让手动处理
     * <AUTHOR>
     * @date    2022/7/20 16:25
     */
    const config: HttpRequestConfig | null = error._response?.config;

    if (!!config?.handleFailed) {
        config.handleFailed(error.error)
        return Promise.reject(error)
    }

    let message: string;
    const statusCode = error.error?.status || error.statusCode || '';
    if (statusCode === 401) {
        message = '登录已经过期！';
    } else if (statusCode === 403) {
        message = error.data || '很抱歉！因系统检测到您的请求可能对网站造成威胁，已自动阻断请求，给你带来困扰请求谅解';
        // minor modifications
        message = message.replaceAll('AccessDeny', '').replaceAll('扫码反馈', '')
        showError(message, true);
        return Promise.reject(error)
    } else if (statusCode === 499) {
        message = '请求正在后台执行，请稍后刷新查看状态！状态码【499】';
        showError(message);
        return Promise.reject(error);
    } else {
        message = String(error.error?.statusText || error.error?.errMsg || error.error?.message || JSON.stringify(error));
    }
    const isTimeout = isTimeoutString(message)
    const isNetErr = isNetworkError(message)

    if (isTimeout) {
        /*当前请求超时，分两种情况处理*/

        if (config?.retryTimeout != null) {
            /*
             * 第一种情况如果请求有设置超时重试次数，则尝试重新发送请求
             */
            if (
                config.currentRetryTimeout == null ||
                config.currentRetryTimeout < config.retryTimeout
            ) {
                const http = config.getHttp!() as Http;
                const currentRetryTimeout =
                    config.currentRetryTimeout == null ? 1 : config.currentRetryTimeout + 1;

                let newRequestConfig: HttpRequestConfig = {
                    ...config,
                    currentRetryTimeout
                };

                if (!!config.handleTimeoutRequest) {
                    newRequestConfig =
                        (await config.handleTimeoutRequest(newRequestConfig)) || newRequestConfig;
                }
                // 每次重试之前间隔时间
                const retryIntervalTime = config?.retryIntervalTime || 100;
                await (new Promise(resolve => setTimeout(resolve, retryIntervalTime)));
                return http.request(newRequestConfig);
            }
        }

        /*
         * 第二种处理办法是提示请求超时
         */
        message = '请求超时！请检查网络情况(或切换网络)并重试。';
        // 特殊超时URL定制化提示语
        if ($utils.isEmpty(timeOutUrlList)) {
            await queryTimeoutUrlList();
        }
        if ($utils.isNotEmpty(timeOutUrlList) && $utils.isNotEmpty(config)
            && timeOutUrlList.includes(config.url.replaceAll(config.baseURL, ''))) {
            message = '请求正在后台执行，请稍后刷新查看状态！状态码【499】';
        }
        aegis.report({
            msg: `接口状态码499，超时报错：${message}`,
            ext1: JSON.stringify(error),
            trace: 'error'
        });
    } else if (isNetErr) {
        message = `请求异常！请检查网络情况(或切换网络)并重试。${message}`;
    }
    if (config) {
        const {header} = config;
        const {linkrequestid} = header!;
        if (!!env.useRequestIdAppendToHttpResult && !!linkrequestid) {
            const appendString = `\n[${linkrequestid}]`;
            message += appendString;
        }
    }
    if (isTimeout || isNetErr) {
        showErrorMessage({message, duration: 10000});
    } else {
        // 判断message是否为空字符串，空对象，空数组
        if (!['', '{}', '[]'].includes(message)) {
            showError(message)
        }
    }

    return Promise.reject(error)
}
