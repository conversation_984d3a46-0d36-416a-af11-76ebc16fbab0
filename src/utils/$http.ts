import {HttpRequestConfig, HttpResponse} from "src/utils/http/type";
import {$utils, Http} from "link-taro-component";
import {env} from '../../env'
// @ts-ignore
import qs from 'querystring'
import {loginService} from "src/utils/login";
// import {testInterceptor} from "@/utils/http/test";
import Taro from '@tarojs/taro'
import {getRequestEncrypt} from './http-encrypt';
import store from "@/store/store";
import {HttpResponseInterceptor, HttpResponseNativeErrorHandler} from "@/utils/$http.interceptors";
import {getDmpSecret} from "@/utils/DmpSecret";
import {createHttpTimer} from './timer';
import {aegis} from "@/utils/aegis";

const baseParamsSerializer: HttpRequestConfig["paramsSerializer"] = (param) => {
    if (!param) return ''
    return qs.stringify(param as any) as string
}

const timer = createHttpTimer(env.baseURL);

const nextRequestId = (() => {
    let count = 1
    return async () => {
        const version = (Taro.getAccountInfoSync().miniProgram.version || '9.99');
        const username = store.getters['user/getUser']?.username
        const code = `${(await timer.datetime()).replace(/[- :]/g, '').slice(4, -2)}${count++}Q${version}`;
        return !!username ? `${username}-${code}` : code
    }
})()


// 智零接口需要替换前缀的URL,需要把action替换order
const replaceOrderUrls = [
    '/link/invloc',
    '/link/invPro',
    '/link/saleorder',
    '/link/saleorderitem',
    '/link/priceListItem',
  ]
/*---------------------------------------%http-------------------------------------------*/

/**
 * 普通的http请求对象
 * <AUTHOR>
 * @date    2020/6/20 21:11
 */
const $http = Http.create({
    baseURL: env.baseURL,
    method: "POST",
    paramsSerializer: baseParamsSerializer,
    header: {
        'content-type': 'application/json'
    }
})

$http.refreshTimer = () => {
    timer.initStaticState()
}

/**
 * token 拦截器
 * <AUTHOR>
 * @date    2020/7/8 15:44
 */
$http.interceptors.request.use(async (config) => {
    if (!config.getHttp) {
        config.getHttp = () => $http
    }
    const postnId = Taro.getStorageSync('token').result?.postnId;
    // token过期时用当前登录的职位重新请求token
    const token = config.noToken ? null : await loginService.getToken(postnId);
    let Encryption = {
        token: token,
        dynamicAeskey: config.noEncryptData ? null : $utils.isEmpty(store.getters['user/getUser']) ? '' : store.getters['user/getUser'].dynamicAeskey,
    }
    // $logger.log('接口所用信息对', Encryption)
    if ($utils.isNotEmpty(Encryption.token) && typeof Encryption.token !== 'string') {
        aegis.report({
            msg: 'token格式不正确',
            ext1: `config: ${JSON.stringify(config)}`,
            ext2: `token: ${JSON.stringify(Encryption.token)}`,
            trace: 'log'
        });
        // $logger.log('token格式不正确', {config: JSON.stringify(config), token: JSON.stringify(Encryption.token)})
        Encryption.token = ''
    }
    if (!config.header!.Authorization && !!Encryption.token) {
        config.header!.Authorization = `bearer ${Encryption.token}`;
        // if($utils.isNotEmpty(store.getters['user/getUser'])){
        //     $logger.log('传递token时的AesKey', store.getters['user/getUser'].dynamicAeskey)
        // }
    }
    if (config.url.indexOf('dmpunion') !== -1 || config.url.indexOf('dmpapi') !== -1 || config.url.indexOf('dmpstg') !== -1) {
        const tmp = await getDmpSecret(env.appURL + '/action/link/secretkey/getsecret');
        // @ts-ignore
        config.header['Authorization'] = tmp.secretkey;
        config.header['Link-Base-Authorization'] = `bearer ${Encryption.token}`;
        // @ts-ignore
        config.header['Message'] = tmp.msg;
    }
    const urlPass = replaceOrderUrls.some((item) => config.url.includes(item) || config.url.startsWith(item))
    if((config.url.startsWith(env.appURL) && urlPass) || (urlPass && config.url === env.appURL)){
        // 判断url是否包含'/action',如果有替换成'/order'
        if(config.url.includes('/action')){
            config.url = config.url.replace('/action', '/order')
        }else {
            //否则添加'/order'
            const urlParts = config.url.split(env.appURL);
            config.url = `${env.appURL}${'/order/' + urlParts[1]}`
        }
        console.log('urlPass', urlPass, config.url, 'url')
    }
    // @ts-ignore
    store.commit('httpsEnv/setHttpsEnv', config.url.includes('crmcdntest'));
    // @ts-ignore
    Taro.setStorageSync('crmcdntest', config.url.includes('crmcdntest'));
    !!Encryption.dynamicAeskey && (config.header!['EncryptData'] = await getRequestEncrypt({
        url: config.url,                                                                   // 请求地址
        method: config.method,                                                             // 请求方法：post,get
        body: config.method === 'GET' ? config.params : config.data,                       // 请求体，post或者get请求的参数
        environment: env.requestModule,                                                    // 请求环境
        // @ts-ignore
        dynamicAesKey: Encryption.dynamicAeskey,            // 动态crypto的秘钥
        cryptoSecret: env.encryptKey,                                                      // crypto的秘钥
        timer,
        contentType: config.header!["content-type"]                                       // 请求体格式

    }));
    // 请求头添加页面路径保存到后端
    const pageRoutes = Taro.getCurrentPages();
    config.header!['PageUrl'] = pageRoutes[pageRoutes.length - 1].route;
    if (env.useIncrementRequestId) {config.header.linkrequestid = await nextRequestId()}
    if (config.timeout == null) {config.timeout = 60000}

    return config
});

let expireCounter = 0;

/**
 * 响应拦截器，token过期自动重新获取
 * <AUTHOR>
 * @date    2020/7/8 15:44
 */
$http.interceptors.response.use(async (response: HttpResponse) => {
    if (response._response.statusCode === 401) {
        expireCounter++
        if (expireCounter > 2) {
            throw new Error('多次请求token异常，请删除token缓存重试')
        }
        try {
            await loginService.getToken(undefined, true)
            response._response.config.header!.Authorization = ''
            return await $http(response._response.config)
        } catch (e) {
            throw e
        }
    }

    return response
});

/**
 * 响应错误拦截器
 * <AUTHOR>
 * @date    2020/7/8 15:45
 */
$http.interceptors.response.use(HttpResponseInterceptor, HttpResponseNativeErrorHandler);

// testInterceptor($http)

/*---------------------------------------$httpForm-------------------------------------------*/

/**
 * $httpForm用来发送post表单请求，带token的
 * <AUTHOR>
 * @date    2020/6/23 22:45
 */

const $httpForm = $http.copy()
$httpForm.interceptors.request.use(async (config: HttpRequestConfig) => {
    if (!config.getHttp) {
        config.getHttp = () => $httpForm
    }
    // @ts-ignore
    config.data = baseParamsSerializer(config.data);
    config.header!['content-type'] = 'application/x-www-form-urlencoded';
    // @ts-ignore
    store.commit('httpsEnv/setHttpsEnv', config.url.includes('crmcdntest'));
    // @ts-ignore
    Taro.setStorageSync('crmcdntest', config.url.includes('crmcdntest'));
    config.header!['EncryptData'] = await getRequestEncrypt({
        url: config.url,                                                                   // 请求地址
        method: config.method,                                                             // 请求方法：post,get
        body: config.method === 'GET' ? config.params : config.data,                       // 请求体，post或者get请求的参数
        environment: env.requestModule,
        // @ts-ignore// 请求环境
        dynamicAesKey: $utils.isEmpty(store.getters['user/getUser']) ? '' : store.getters['user/getUser'].dynamicAeskey,            // 动态crypto的秘钥
        cryptoSecret: env.encryptKey,                                                      // crypto的秘钥
        timer,
        contentType: config.header!["content-type"]                                         // 请求体格式
    });
    // 请求头添加页面路径保存到后端
    const pageRoutes = Taro.getCurrentPages();
    config.header!['PageUrl'] = pageRoutes[pageRoutes.length - 1].route;
    if (env.useIncrementRequestId) {config.header!.linkrequestid = await nextRequestId()}
    return config
});

/*---------------------------------------formRequest-------------------------------------------*/

/**
 * 表单请求对象，POST请求，请求体为表单格式数据，content-type为 application/x-www-form-urlencoded，不带token
 * <AUTHOR>
 * @date    2020/6/20 21:11
 */
const $formRequest = Http.create({
    baseURL: env.baseURL,
    method: "POST",
    paramsSerializer: baseParamsSerializer,
});

$formRequest.interceptors.request.use(async (config: HttpRequestConfig) => {
    if (!config.getHttp) {
        config.getHttp = () => $formRequest
    }
    // @ts-ignore
    config.data = baseParamsSerializer(config.data);
    config.header!['content-type'] = 'application/x-www-form-urlencoded';
    // @ts-ignore
    store.commit('httpsEnv/setHttpsEnv', config.url.includes('crmcdntest'));
    // @ts-ignore
    Taro.setStorageSync('crmcdntest', config.url.includes('crmcdntest'));
    if (config.url!.split('/').pop() !== 'corpWxMpLogin' && config.url!.split('/').pop() !== 'publicKey') {
        config.header!['EncryptData'] = await getRequestEncrypt({
            url: config.url,                                                                   // 请求地址
            method: config.method,                                                             // 请求方法：post,get
            body: config.method === 'GET' ? config.params : config.data,                       // 请求体，post或者get请求的参数
            environment: env.requestModule,                                                       // 请求环境
            // @ts-ignore
            dynamicAesKey: $utils.isEmpty(store.getters['user/getUser']) ? '' : store.getters['user/getUser'].dynamicAeskey,            // 动态crypto的秘钥
            cryptoSecret: env.encryptKey,                                                      // crypto的秘钥
            timer,
            contentType: config.header!["content-type"]                                         // 请求体格式
        });

        if (env.useIncrementRequestId) {config.header!.linkrequestid = await nextRequestId()}
    }
    if (config.timeout == null) {config.timeout = 60000}
    return config
});

$formRequest.interceptors.response.use(HttpResponseInterceptor, HttpResponseNativeErrorHandler)


/*---------------------------------------export-------------------------------------------*/

export {
    $http,
    $httpForm,
    $formRequest,
}
