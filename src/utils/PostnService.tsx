import {$httpForm} from "@/utils/$http";
import {$utils} from "@/utils/$utils";
import Taro from "@tarojs/taro";
import store from "@/store/store";
import {buildPageSecurityMenus} from "@/utils/security";

export const PostnService = {
    /**
     * 变更职位
     * <AUTHOR>
     * @date    2021/4/6 17:02
     * @param   postnId         职位id
     */
    change: async (postnId: string) => {
        const data = await $httpForm.post(
            'action/link/position/changeLoginPostn',
            {id: postnId, attr5: 'weChat'}, {
                errorMessage: '初始化用户信息失败！',
            })
        const tokenInfo = Taro.getStorageSync('token')! as any
        const targetUrl = tokenInfo.result.userProfile;
        // if($utils.isNotEmpty(data.user)){
        //     let DealerCfg = await $utils.getCfgProperty('dealer_hidden_cost')
        //     let DealerCfgFlag = DealerCfg.split(',').includes(data.user.coreOrganizationTile.brandCompanyCode);
        //     if(!DealerCfgFlag){
        //         data.user.staffType = '';
        //     }
        // }
        tokenInfo.result = data.user;
        tokenInfo.result.userProfile = targetUrl;
        const menus = data.menus;
        const pageSecurityMenus = buildPageSecurityMenus(menus)
        Taro.setStorageSync('token', tokenInfo);
        store.commit('user/setPageSecurityMenus', pageSecurityMenus)
        store.commit('user/setMenus', menus);
        return tokenInfo.result
    },
}
