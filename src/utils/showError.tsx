import {env} from "../../env";
import {$dialog, $message} from 'link-taro-component'

/**
 * 提示错误信息
 * - 如果环境变量中没有配置useDialogShowErrorInsteadOfMessage:true, 则仍旧沿用老的方式使用$message.error来提示错误
 * - 如果调用参数中的第一个参数是对象，则也是直接用老的$message.error来提示错误，因为MessageServiceOption无法适配为DialogServiceOption
 * - 在使用dialog提示错误信息时，自动对换行符进行切割换行展示；
 * <AUTHOR>
 * @date    2023/5/22 17:48
 */
export function showError(...args: any[]) {
    if (!env.useDialogShowErrorInsteadOfMessage) {
        if (typeof args[0] === "object") {
            const {error, result, message} = args[0]
            const msg = error || result || message
            if (!!msg) {
                throttleMessageHandler.check(msg) && $message.error(msg)
                return
            }
        }
        $message.error(...args)
    } else {
        const arg0 = args[0]
        const message = (() => {
            if (typeof arg0 === 'string') {return arg0}
            if (typeof arg0 === "object") {
                if (arg0 instanceof Error) {
                    return arg0.message
                } else {
                    const {error, result, message} = arg0
                    return result || message || error
                }
            }
            return null
        })()
        // 以html渲染方式展示
        const htmlFlag = args[0];
        if (!!message && /<[^>]+>/g.test(message) && !!htmlFlag) {
            if (throttleMessageHandler.check('HTML')) {
                $dialog({
                    title: '提示',
                    disabledHideOnClickMask: false,
                    content: (h) => {
                        return (
                            <view style={{width: '100%', height: '50vh', overflowY: 'auto'}}>
                                <view domPropsInnerHTML={message}></view>
                            </view>
                        )
                    }
                })
            }
            return;
        }
        if (!!message) {
            if (throttleMessageHandler.check(message)) {
                $dialog({
                    title: '提示',
                    disabledHideOnClickMask: false,
                    content: (h) => {
                        return renderErrorMessage(h, message)
                    },
                })
            }
        } else {
            $message.error(...args)
        }
    }
}

export const renderErrorMessage = (h, message) => {
    const list = message.split('\n')
    return (
        <view style={{textAlign: 'center', fontSize: '28rpx', lineHeight: '44rpx', maxHeight: '70vh', overflow: 'auto'}}>
            {list.map((msg, index) => {
                const lightTextFlag = msg[0] === '[' && msg[msg.length - 1] === ']';
                return (
                    <view
                        key={index}
                        class={lightTextFlag ? 'dialog-service-light-text' : ''}
                    >
                        {msg}
                    </view>
                )
            })}
        </view>
    )
}

/**
 * 控制1s之内，同样的提示消息只能提示一遍，防止出现多个同样信息的提示框
 * <AUTHOR>
 * @date    2023/5/23 10:21
 */
const throttleMessageHandler = (() => {
    const map = {} as Record<string, boolean | undefined>
    const check = (msg: string) => {
        // 去除请求ID字符串
        const index = msg.indexOf('[corpWx');
        if (index > -1) {
            msg = msg.substring(0, index)
        }
        if (map[msg]) {
            /*同样的消息正在缓存，检查不通过*/
            return false
        } else {
            map[msg] = true
            /*1s之后将标记移除*/
            setTimeout(() => {delete map[msg]}, 1000)
            return true
        }
    }
    return {check}
})()
