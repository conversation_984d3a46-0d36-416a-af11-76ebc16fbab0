import {env} from '../../../env'
import Taro from "@tarojs/taro";
import {$utils} from 'link-taro-component';
import QQMapWX from '../lib/Tmap/qqmap-wx-jssdk.min'
import {aegis} from "@/utils/aegis";

const X_PI = (3.14159265358979324 * 3000.0) / 180.0
let tMap = new QQMapWX({
    key: env.qqMapKey
});
const chooseLocation = requirePlugin('chooseLocation');
let coordinate = {};
let locatonChangeFlag = false;
let lastLocation = {} as any; //最后一次调用逆地址解析的经纬度 以及解析之后的地址

/**
 * 地址服务
 * 由于微信使用地理位置的要求发生了改变,该服务被移出组件库,否则对于不许要使用新组件库的项目
 * 会出现无法审核通过的问题,如果实际项目中不需要使用定位,请注释plugins.ts地址相关代码及其他业务模块中的定位代码
 * <AUTHOR>
 * @date 2022/5/5
 */
export class $locations {
    /**
     * 地理位置授权信息
     * <AUTHOR>
     * @date 12/8/20
     */
    static async authorize() {
        return new Promise((resolve) => {
            Taro.authorize({
                scope: 'scope.userLocation',
                success(res) {
                    resolve(res)
                }
            })
        })
    }

    static async getAddress() {
        const res = await this.debounceGetLocation()
        if ('latitude' in res && 'longitude' in res) {
            let result = await this.reverseTMapGeocoder(res.latitude, res.longitude)
            return result
        } else {
            return Promise.reject(res)
        }
    }
    /**
     * 根据经纬度获取地点信息，逆地址解析-腾讯
     * <AUTHOR>
     * @date 2022年11月22日
     */
    static async reverseTMapGeocoder(lat: any, lnt: any, scene: string='其他') {
        return new Promise(async (resolve, reject) => {
            //获取参数配置：是否使用缓存的逆解析地址
            const globalCfgProperty = Taro.getStorageSync('link-global-cfgProperty');
            if(globalCfgProperty.useCachedReverseAddress === 'Y'){
                const addr =  await this.useCachedAddress(lat, lnt)
                if(addr){
                   return resolve(addr)
                }
            }
            console.log('未使用缓存地址，调接口重新获取')
            tMap.reverseGeocoder({
                location: lat + ',' + lnt,
                success: function (res: any) {
                    function processingNUll(data) {
                        return data ? data : '';
                    }

                    res.wxMarkerData = [
                        {
                            address: processingNUll(res.result.address),
                            latitude: processingNUll(res.result.location.lat),
                            longitude: processingNUll(res.result.location.lng)
                        }
                    ];
                    res.originalData = {
                        result: {
                            addressComponent: {
                                adcode: processingNUll(res.result.ad_info.adcode),
                                city: processingNUll(res.result.ad_info.city),
                                country: processingNUll(res.result.ad_info.nation),
                                district: processingNUll(res.result.ad_info.district),
                                province: processingNUll(res.result.ad_info.province),
                                street: processingNUll(res.result.address_component.street),
                                street_number: processingNUll(res.result.address_component.street_number).replace(processingNUll(res.result.address_component).street,''),
                                townName: processingNUll(res.result.address_reference?.town?.title),
                                town: processingNUll(res.result.address_reference?.town?.id),
                            },
                            formatted_address: processingNUll(res.result.address),
                            location: {
                                lat: processingNUll(res.result.location.lat),
                                lng: processingNUll(res.result.location.lng)
                            },
                            poiRegions: [
                                {
                                    name: res.result.address_reference && res.result.address_reference.landmark_l2 ? processingNUll(res.result.address_reference.landmark_l2.title) : ''
                                }
                            ],
                            sematic_description: res.result.address_reference && res.result.address_reference.landmark_l2 ? (processingNUll(res.result.address_reference.landmark_l2?.title) +
                                processingNUll(res.result.address_reference.landmark_l2?._dir_desc) +
                                processingNUll(res.result.address_reference.landmark_l2?._distance.toString()) + '米') : processingNUll(res.result.address)
                        }
                    }
                    if (!res.result.address_reference?.town?.title) {
                        console.log('当前位置未返回乡镇/街道（经度/纬度）', lat, lnt)
                        aegis.report({
                            msg: '当前位置未返回乡镇/街道（经度/纬度）',
                            ext1: `经度： ${lnt}，纬度: ${lat}`,
                            trace: 'log'
                        });
                    }
                    if (globalCfgProperty.aegisReportLocations === 'Y') {
                        aegis.report({
                            msg: `逆地址解析：${JSON.stringify(scene)}`,
                            trace: 'log'
                        });
                    }
                    if(globalCfgProperty.useCachedReverseAddress === 'Y'){
                       $locations.cacheReverseGeocodedAddress(lat, lnt, res)
                    }
                    resolve(res)
                },
                fail: function (res: any) {
                    console.log(res)
                    reject(res)
                }
            })
        })
    }

    /**
     * 根据经纬度获取地点信息，逆地址解析 百度不是使用了 切换成腾讯
     * @param lat 纬度
     * @param lnt 经度
     * @returns {Promise<any>}
     */
    static async FQreverseTMapGeocoder(lat: any, lnt: any) {
        // return new Promise(async (resolve, reject) => {
        //     // @ts-ignore
        //     bMap.regeocoding({
        //         location: `${lat},${lnt}`,
        //         success: function (res: any) {
        //             console.log(res)
        //             resolve(res)
        //         },
        //         fail: function (res: any) {
        //             console.log(res)
        //             reject(res)
        //         }
        //     })
        // })
    }

    /**
     * 根据地址逆解析经纬度
     * @returns {Promise<any>}
     * @param address
     */
    static async reverseTMapGgeocodingLaLon(address: any) {
        return new Promise(async (resolve, reject) => {
            // @ts-ignore
            tMap.geocoder({
                address: address,
                success: function (res: any) {
                    console.log('地址逆解析经纬度成功', res)
                    let opt = {
                        flag: 'success',
                        res: res
                    }
                    resolve(opt)
                },
                fail: function (res: any) {
                    console.log('地址逆解析经纬度失败', res)
                    let opt = {
                        flag: 'fail',
                        res: res
                    }
                    reject(opt)
                    /* aegis.report({
                         msg: '逆解析reverseTMapGgeocodingLaLon',
                         ext1: JSON.stringify(res),
                         trace: 'log'
                     });*/
                    // $logger.log('逆解析reverseTMapGgeocodingLaLon', res)
                }
            })
        })
    }

    /**
     * 根据地址逆解析经纬度 百度不是使用了 切换成腾讯
     * @returns {Promise<any>}
     * @param address
     */
    static async reverseBMapGgeocodingLaLon(address: any) {
        // return new Promise(async (resolve, reject) => {
        //     // @ts-ignore
        //     bMap.geocoding({
        //         address: address,
        //         success: function (res: any) {
        //             console.log('地址逆解析经纬度成功', res)
        //             let opt = {
        //                 flag: 'success',
        //                 res: res
        //             }
        //             resolve(opt)
        //         },
        //         fail: function (res: any) {
        //             console.log('地址逆解析经纬度失败', res)
        //             let opt = {
        //                 flag: 'fail',
        //                 res: res
        //             }
        //             reject(opt)
        //             $logger.log('逆解析reverseBMapGgeocodingLaLon', res)
        //         }
        //     })
        // })
    }

    /**
     * 根据输入智能联想地址
     * @param address
     * @returns {Promise<any>}
     */
    static async getTMapSuggestion(address: any) {
        return new Promise((resolve, reject) => {
            console.log(address)
            // @ts-ignore
            tMap.getSuggestion({
                keyword: address,
                success: function (res: any) {
                    console.log(res)
                    resolve(res)
                },
                fail: function (res: any) {
                    console.log(res)
                    reject(res)
                }
            })
        })
    }
    /**
     * 根据输入智能联想地址
     * @param address
     * @returns {Promise<any>}
     */
    static async getBMapSuggestion(address: any) {
        // return new Promise((resolve, reject) => {
        //   console.log(address)
        //   // @ts-ignore
        //   bMap.suggestion({
        //     query: address,
        //     success: function (res: any) {
        //       console.log(res)
        //       resolve(res)
        //     },
        //     fail: function (res: any) {
        //       console.log(res)
        //       reject(res)
        //     }
        //   })
        // })
    }
    /**
     * 连续时间多次获取地址和经纬度的防抖处理
     * <AUTHOR>
     * @date 2023-10-13
     * @return 最后一次获取的位置信息
     */
    static debounceGetLocation = (() => {
        const DURATION = 2000
        let lastResult: undefined | {
            timestamp: number,
            data: any
        } = undefined;
        return async () => {
            const now = Date.now()
            if (!lastResult || (now - lastResult.timestamp) > DURATION) {
                lastResult = {
                    timestamp: now,
                    data: await new Promise((resolve, reject) => {
                        Taro.getLocation({
                            type: 'gcj02',
                            success: async function (res) {
                                res['nowTime'] = new Date().getTime();
                                console.log('调用腾讯api定位', '定位成功', res)
                                resolve(res)
                            },
                            fail: function (res) {
                                console.log('调用腾讯api定位', '定位失败', res)
                                /*aegis.report({
                                    msg: '调用腾讯api定位getCurrentCoordinate',
                                    ext1: JSON.stringify(res),
                                    trace: 'log'
                                });*/
                                // $logger.log('调用腾讯api定位getCurrentCoordinate', res)
                                resolve(res)
                                // reject(false)
                            }
                        })
                    })
                }
            }
            return lastResult.data
        }
    })();
    /**
     * 获取当前位置坐标(经纬度)
     * @return {Promise<void>}
     */
    static async getCurrentCoordinate() {
        return await this.debounceGetLocation()
    }

    /**
     * 判断用户是否已经打开地理位置授权信息
     * <AUTHOR>
     * @date 2019/3/21
     */
    static async checkUserLocation() {
        return new Promise((resolve) => {
            Taro.getSetting({
                success: (res) => {
                    if (res.authSetting['scope.userLocation']) {
                        resolve(true)
                    } else {
                        resolve(false)
                    }
                }
            })
        })
    }

    static async openSetting() {
        return new Promise((resolve) => {
            Taro.openSetting({
                success(res) {
                    console.log(res.authSetting)
                    resolve(res.authSetting)
                    // 设置成功之后,将设置信息返回
                },
                fail: function () {
                    $utils.showAlert('打开授权设置失败,请重试!', {icon: 'none'})
                }
            })
        })
    }

    /**
     * 计算距离
     * <AUTHOR>
     * @date 2020-08-26
     * @desc 由经纬度计算两点之间的距离，la为latitude缩写，lo为longitude
     * @param la1 第一个坐标点的纬度
     * @param lo1 第一个坐标点的经度
     * @param la2 第二个坐标点的纬度
     * @param lo2 第二个坐标点的经度
     * @return (int)s   返回距离(单位千米或公里)
     * @tips 注意经度和纬度参数别传反了，一般经度为0~180、纬度为0~90
     */
    static async getDistance(la1, lo1, la2, lo2) {
        return new Promise((resolve) => {
            let La1 = (la1 * Math.PI) / 180.0
            let La2 = (la2 * Math.PI) / 180.0
            let La3 = La1 - La2
            let Lb3 = (lo1 * Math.PI) / 180.0 - (lo2 * Math.PI) / 180.0
            let s =
                2 *
                Math.asin(
                    Math.sqrt(
                        Math.pow(Math.sin(La3 / 2), 2) +
                        Math.cos(La1) * Math.cos(La2) * Math.pow(Math.sin(Lb3 / 2), 2)
                    )
                )
            s = s * 6378.137 //地球半径
            s = Math.round(s * 10000) / 10000
            console.log('计算结果', s)
            resolve(s)
        })
    }

    /**
     * 火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换
     * 即腾讯、谷歌、高德 转 百度
     * @param lat 纬度
     * @param lng 经度
     * @returns {*[]}
     */
    static async gcj02tobd09(lat, lng) {
        return new Promise((resolve) => {
            lng = +lng
            lat = +lat
            const z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * X_PI)
            const theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * X_PI)
            const bdLng = z * Math.cos(theta) + 0.0065
            const bdLat = z * Math.sin(theta) + 0.006
            resolve({ bdLat, bdLng })
        })
    }

    /**
     * 百度坐标系 (BD-09) 与 火星坐标系 (GCJ-02)的转换
     * 即 百度 转 谷歌、高德、腾讯
     * @param bdLat 百度纬度
     * @param bdLng 百度经度
     * @returns {}
     */
    static async bd09togcj02(bdLat, bdLng) {
        return new Promise((resolve) => {
            bdLng = +bdLng
            bdLat = +bdLat
            const x = bdLng - 0.0065
            const y = bdLat - 0.006
            const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * X_PI)
            const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * X_PI)
            const lng = z * Math.cos(theta)
            const lat = z * Math.sin(theta)
            resolve({ lat, lng })
        })
    }

    /**
     @desc: 腾讯选点
     @author: wangbinxin
     @param latitude 纬度
     @param longitude 经度
     @date 2022-06-24 10-53
     **/
    static async chooseLocation(latitude, longitude) {
        const key = env.qqMapKey; //使用在腾讯位置服务申请的key
        const referer = '选择地址'; //调用插件的app的名称
        const location = JSON.stringify({
            latitude: latitude,
            longitude: longitude
        });
        wx.navigateTo({
            url: `plugin://chooseLocation/index?key=${key}&referer=${referer}&location=${location}`
        });
    }

    /**
     @desc: 腾讯地图获取地址
     @author: wangbinxin
     @param latitude 纬度
     @param longitude 经度
     @date 2022-06-24 10-53
     **/
    static QQGetLocation() {
        return chooseLocation.getLocation();
    }

    /**
     @desc: 腾讯地图清空地址
     @author: wangbinxin
     @param latitude 纬度
     @param longitude 经度
     @date 2022-06-24 10-53
     **/
    static QQClearLocation() {
        chooseLocation.setLocation(null);
    }

    /**
     @desc: 开启监听地址
     @author: wangbinxin
     @date 2022-07-09 14-09
     **/
    static onLocationChange() {
        return new Promise((resolve) => {
            const _locationChangeFn = (res) => {
                res.nowTime = new Date().getTime();
                coordinate = res;
            };
            wx.startLocationUpdate({
                success: (res) => {
                    wx.onLocationChange(_locationChangeFn);
                    resolve(undefined)
                },
                fail: (err) => {
                    resolve(err);
                }
            });
        })
    }
    static getLocationChange(){
        return coordinate;
    }
    static clearLocationChange() {
        coordinate = {};
    }

    static getLocatonChangeFlag() {
        if (!locatonChangeFlag) {
            locatonChangeFlag = true;
            return false
        } else {
            return true
        }
    }
    /**
     @desc: 关闭地址监听
     @author: wangbinxin
     @date 2022-07-09 14-09
     **/
    static async stopLocationUpdate() {
        if (locatonChangeFlag) {
            coordinate = {};
            locatonChangeFlag = false;
            // 关闭监听
            wx.offLocationChange((res) => {
                console.log("offLocationChange : ", res)
            });
            // 停止位置跟踪
            wx.stopLocationUpdate({
                success: (res) => {
                    console.log("stopLocationUpdate success", res)
                },
                fail: (err) => {
                    console.log("stopLocationUpdate error", err)
                }
            })
        }
    }

    /**
     * 获取指定行政区划的子级行政区划
     * 本接口支持获取 指定区县（三级）其下乡镇/街道（四级）列表
     * <AUTHOR>
     * @date    2023/10/9 14:33
     * @param adcode 父级行政区划ID(缺省时返回一级行政区划，也就是省级)
     */
    static async getTMapDistrictByCityId(adcode: any) {
        return new Promise(async (resolve, reject) => {
            tMap.getDistrictByCityId({
                id: adcode,
                success: function (res) {//成功后的回调
                    console.log('获取指定行政区划的子级行政区划成功', res);
                    let opt = {
                        flag: 'success',
                        res: res
                    }
                    resolve(opt)
                },
                fail: function (error) {
                    console.log('获取指定行政区划的子级行政区划失败', error);
                    let opt = {
                        flag: 'fail',
                        res: error
                    }
                    reject(opt)
                }
            })
        })
    }
    /**
     * 缓存逆解析地址
     * 每次调用成功之后，记录最后一次调用逆地址解析的经纬度，以及解析之后的地址
     * 每次调用成功之后，以经纬度小数点后六位作为KEY，缓存逆地址解析结果，缓存时间为5分钟；注意：最多缓存10个，超过10个去除过期时间最早的。
     * <AUTHOR>
     * @date    2025/4/8 14:33
     * @param latitude 此次需要解析的经度
     * @param longitude 此次需要解析的纬度
     * @param res 此次解析的结果
     */
    private static async cacheReverseGeocodedAddress(latitude, longitude, res) {
        lastLocation = {
            lastLat: latitude,
            lastLnt: longitude,
            reverseTMapResult: res
        }
        const cacheJson = Taro.getStorageSync('cacheReverseAddress')
        // 将 JSON 字符串转换为数组
        const storedArray =cacheJson? JSON.parse(cacheJson): [];
        // 将数组还原为 Map
        const cacheMap = new Map(storedArray);
        if (cacheMap.size >= 10) {
            // 获取最早存入的键
            const firstKey = cacheMap.keys().next().value;
            // 删除最早存入的元素
            cacheMap.delete(firstKey);
        }
        //以经纬度小数点后六位作为KEY，缓存逆地址解析结果
        const cacheKey = `${latitude.toFixed(5)},${longitude.toFixed(5)}`;
        cacheMap.set(cacheKey, {reverseTMapResult: res, expire: new Date().getTime() + 5 * 60 * 1000})
        // 将 Map 转换为数组，数组元素为 [key, value] 形式的子数组
        const mapToArray = Array.from(cacheMap);
        // 将数组转换为 JSON 字符串
        const mapJson = JSON.stringify(mapToArray);
        Taro.setStorageSync('cacheReverseAddress', mapJson)
        console.log('存入缓存的mapToArray', mapToArray);
    }

    /**
     * 需要根据经纬度逆解析地址时使用缓存的已经解析成功的地址
     * 1、当前的经纬度与最后一次调用成功经纬度做距离计算，结果<20米，则直接返回最后一次解析地址结果
     * 2、根据当前的经纬度生成KEY到缓存对象中匹配是否存在：
     *①存在且在有效期则返回改结果，不继续执行后续逻辑
     *②存在且已经失效，则删除对应KEY，执行后续逻辑
     *③不存在，则执行后续逻辑
     * <AUTHOR>
     * @date    2025/4/8 14:33
     * @param lat 此次需要解析的经度
     * @param lnt 此次需要解析的纬度
     */
     private static async useCachedAddress(lat, lnt){
        // 当前的经纬度与最后一次调用成功经纬度做距离计算，结果<20米，则直接返回最后一次解析地址结果
        if(lastLocation.reverseTMapResult){
            const {lastLat, lastLnt} = lastLocation
            //返回的距离单位为km
            const distance = await $locations.getDistance(lat, lnt,lastLat,lastLnt) as number;
            if(distance * 1000 < 20){
                console.log('使用最后一次调用成功解析地址',lastLocation)
                return lastLocation.reverseTMapResult
            }
        }
        //根据当前的经纬度生成KEY到缓存对象中匹配是否存在
        const cacheJson = Taro.getStorageSync('cacheReverseAddress')
        // 如果缓存为空，则直接返回
        if(!cacheJson) return false
        // 将 JSON 字符串转换为数组  将数组还原为 Map
        const cacheMap = new Map(JSON.parse(cacheJson));
        // 以经纬度小数点后5位作为KEY，缓存逆地址解析结果,以第六位的话精度太高，微信定位达不到，每次都会不一样，没有缓存的必要，5~10米解析出的结果几乎没有差别
        const cacheKey = `${lat.toFixed(5)},${lnt.toFixed(5)}`;
        if(cacheMap.has(cacheKey)){
            // 存在且在有效期则返回改结果，不继续执行后续逻辑
            const cachedAddress = cacheMap.get(cacheKey) as { reverseTMapResult: any, expire: number | string}
            if(cachedAddress.expire > new Date().getTime()){
                console.log('使用缓存中匹配的key的地址',cachedAddress, cacheMap)
                return cachedAddress.reverseTMapResult
            } else {
                // 存在且已经失效，则删除对应KEY，执行后续逻辑
                cacheMap.delete(cacheKey)
            }
            Taro.setStorageSync('cacheReverseAddress', JSON.stringify(Array.from(cacheMap)))
        }
        return false
    }
}

