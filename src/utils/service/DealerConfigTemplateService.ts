
import {$utils,set} from 'link-taro-component';
import {$http} from "@/utils/$http";
import store from "@/store/store";

export class dealerConfigTemplate {
    /**
      * 获取经销商拜访执行模板——入口是一级菜单【终端】模块——二级菜单【拜访】-【拜访执行】
      * @param param
    */
    static async dealerVisitPerformTemp () {
        const data = await $utils.getQwMpTemplate('DealerVisitPerform');
        if (data.success) {
            // @ts-ignore
            let resultOpt = JSON.parse(data.result);
            let templateData = JSON.parse(resultOpt.conf);
            templateData.forEach(item => {
                set(item.values, 'status', false);
                set(item, 'verifyMustFlag', false);
            });
            store.commit('dealerConfigTemplate/setDealerVisitPerformTemp', templateData);
        }
    }
    /**
      * 获取经销商拜访执行模块状态
      * @param id 拜访id
    */
    static async getDealerVisitPerformStatus (id) {
        return new Promise(resolve => {
            $http.post('action/link/accntVisit/getVisitCollectStatus', {id: id}).then((data) => {
                if (data.success) {
                    console.log(data)
                    resolve(data.result)
                }
            })
        })
    }
}
