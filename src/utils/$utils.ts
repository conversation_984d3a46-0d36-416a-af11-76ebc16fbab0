import {ComponentUtils, DateService, DeviceService} from "link-taro-component";
import Taro from "@tarojs/taro";
import {$http} from "./$http"
import {version} from '@/version.json';
import {aegis} from './aegis';
import {env} from "../../env";
import {showError} from "@/utils/showError";

export const $utils = {
    /**
     * 数组对象根据字段排序
     *
     * 1. 目标数组
     * 2. 字段
     * <AUTHOR>
     * @date 2022年8月25日12:01:00
     */
    ArraySortByProperty(arr, name) {
        let sortList = [];
        sortList = arr.sort(function (a, b) {
            let value1 = a[name];
            let value2 = b[name];
            return value1 - value2;
        });
        return sortList;
    },
    /**
     * 数组对象根据字段去重
     *
     * 1. 目标数组
     * 2. 判断字段
     * <AUTHOR>
     * @date 2020-11-10
     */
    unique(arr, name) {
        const res = new Map();
        return arr.filter((a) => !res.has(a[name]) && res.set(a[name], 1))
    },
    /**
     * 判断是否为空
     *
     * 1. 空数组为空
     * 2. 空字符串为空
     * 3. number 不为空
     * 4. false 为空
     *
     * <AUTHOR>
     * @date    2020/7/3 16:30
     */
    isEmpty(val: any): boolean {

        // null and undefined
        if (val == null) {
            return true
        }

        // string
        if (typeof val === "string") {
            return !val
        }

        // number
        if (typeof val === "number") {
            // 只要是数字，就不是空值
            return false
        }

        // array
        if (Array.isArray(val)) {
            return val.length === 0
        }

        //object
        if (typeof val === "object") {
            return Object.keys(val).length === 0;
        }

        // function, boolean
        return !val

    },
    /**
     * 生成url地址
     * <AUTHOR>
     * @date    2019/4/2 10:06
     */
    encodeUrl(url: string, params: any) {
        if (!params) return url
        const parts: string[] = []
        Object.keys(params).forEach((key) => {
            const val = params[key]
            if (val === null || typeof val === 'undefined') {
                return
            }
            let values = []
            if (Array.isArray(val)) {
                // @ts-ignore
                values = val
                key += '[]'
            } else {
                // @ts-ignore
                values = [val]
            }

            values.forEach((val: any) => {
                if (ComponentUtils.typeOf(val) === 'date') {
                    val = val.toISOString()
                } else if (ComponentUtils.typeOf(val) === 'object') {
                    val = JSON.stringify(val)
                }
                parts.push(`${this.encodeString(key)}=${this.encodeString(val)}`)
            })
        })

        let serializedParams = parts.join('&')
        if (serializedParams) {
            const markIndex = url.indexOf('#')
            if (markIndex !== -1) url = url.slice(0, markIndex)
            url += (url.indexOf('?') === -1 ? '?' : '&') + serializedParams
        }
        return url
    },
    /**
     * 使用encodeURIComponent转义字符串
     * <AUTHOR>
     * @date    2019/10/16 11:39
     */
    encodeString(str: string) {
        return encodeURIComponent(str)
            .replace(/%40/g, '@')
            .replace(/%3A/ig, ':')
            .replace(/%24/g, '$')
            .replace(/%2C/ig, ',')
            .replace(/%20/g, '+')
            .replace(/%5B/ig, '[')
            .replace(/%5D/ig, ']')
    },
    /**
     * 深拷贝
     * <AUTHOR>
     * @date 2020-07-16
     * @param obj
     */
    // @ts-ignore
    deepcopy(obj: object) {
        return JSON.parse(JSON.stringify(obj))
    },
    /* ---------------------- 提示 Start ---------------------- */

    /**
     * 弹出提示
     * @param message
     * @param inOptions
     */
    showAlert(message: string, inOptions?: any) {
        inOptions = inOptions || {}
        // 当消息为空时直接return
        let defaultToastOptions = {
            title: '',
            icon: 'success',
            image: '',
            duration: 1500,
            mask: false,
            success: null,
            fail: null,
            complete: null
        }
        if (this.isEmpty(message)) {
            message = '内容为空!'
        }
        defaultToastOptions.title = message
        defaultToastOptions.mask = true
        // 默认参数
        let options = Object.assign({}, defaultToastOptions, inOptions)
        // 如果有传入参数,则继承后再执行显示
        Taro.showToast(options)
    },

    /* ---------------------- 提示 End ------------------------ */
    ...ComponentUtils,

    /**
     * 从数组中移除对象
     * <AUTHOR>
     * @date 2020-07-23
     * @param array 数组
     * @param obj 要移除的对象
     */
    removeFromArray(array, obj) {
        if (!this.isArray(array)) {
            return array;
        }
        const length = array.length;
        for (let i = 0; i < length; i++) {
            if (this.equals(array[i], obj)) {
                if (i === 0) {
                    // 删除第一个元素
                    array.shift();
                    return;
                } else if (i === length - 1) {
                    // 删除最后一个元素
                    array.pop();
                    return;
                } else {
                    array.splice(i, 1);
                    return;
                }
            }
        }
    },

    /**
     * 非空
     * <AUTHOR>
     * @date 2020-07-23
     * @param input 输入
     */
    isNotEmpty(input) {
        return !this.isEmpty(input);
    },

    /**
     * 是否是数组
     * <AUTHOR>
     * @date 2020-07-23
     * @param value 参数值
     */
    isArray(value) {
        return Array.isArray(value);
    },

    /**
     * 比较两个对象值是否相等
     * <AUTHOR>
     * @date 2020-07-23
     * @param arg1 参数1
     * @param arg2 参数2
     */
    equals(arg1, arg2) {
        if (arg1 === arg2) {
            return true;
        }
        if (arg1 === null || arg2 === null) {
            return false;
        }
        if (arg1 !== arg1 && arg2 !== arg2) {
            // NaN === NaN
            return true;
        }
        const type1 = typeof arg1;
        const type2 = typeof arg2;

        /* tslint:disable */
        let length, key, keySet;
        if (type1 == type2 && type1 === 'object') {
            /* tslint:enable */
            // arg1 是数组
            if (this.isArray(arg1)) {
                // arg2 非数组
                if (!this.isArray(arg2)) {
                    return false;
                }
                length = arg1.length;
                if (length === arg2.length) {
                    for (key = 0; key < length; key++) {
                        // 递归调用自己
                        if (!this.equals(arg1[key], arg2[key])) {
                            return false;
                        }
                    }
                    return true;
                }
            } else if (this.isDate(arg1)) {
                if (!this.isDate(arg2)) {
                    return false;
                }
                return this.equals(arg1.getTime(), arg2.getTime());
            } else if (this.isRegExp(arg1)) {
                if (!this.isRegExp(arg2)) {
                    return false;
                }
                return arg1.toString() === arg2.toString();
            } else {
                // 如果arg1 不是上述类型,但是arg2是上面三种类型,则直接return false
                if (this.isArray(arg2) || this.isDate(arg2) || this.isRegExp(arg2)) {
                    return false;
                }
                keySet = this.createMap();
                for (key in arg1) {
                    if (key.charAt(0) === '$' || this.isFunction(arg1[key])) {
                        continue;
                    }
                    if (!this.equals(arg1[key], arg2[key])) {
                        return false;
                    }
                    keySet[key] = true;
                }
                for (key in arg2) {
                    if (!(key in keySet) && key.charAt(0) !== '$' && this.isDefined(arg2[key]) && !this.isFunction(arg2[key])) {
                        return false;
                    }
                }
                return true;
            }
        }
        return false;
    },
    /**
     * 是否未定义
     */
    isUndefined(value) {
        return typeof value === 'undefined'
    },
    /* ---------------------- 遮罩 Start ---------------------- */
    /**
     * 显示遮罩
     * @param message
     * @param inOptions
     */
    showLoading(message, inOptions) {
        if (this.isEmpty(message)) {
            message = '请求中...'
        }
        let options = {
            title: message,
            mask: true
        };
        inOptions = inOptions || {};
        // 如果有传入参数,则继承后再执行显示
        $naiveLoading.showLoading(Object.assign({}, options, inOptions))
    },
    /**
     * 是否走职位安全性
     * 不满足，则走组织安全性
     * <AUTHOR>
     * @date 2020-11-19
     */
    isPostnOauth() {
        // @ts-ignore
        let positionType = Taro.getStorageSync('token').result.positionType;
        let arr = [
            'SalesSupervisor',         // 业务主管
            'Salesman',                // 业务代表
            'GroupBuyManager',         // 团购经理
            'AccountManager',          // 客户经理
            'RegionalManager',         // 区域经理
            'CustServiceManager',      // 客服经理
            'VipManager',              // VIP经理
            'CustServiceSpecialist',   // 客服专员
            'CustServiceSupervisor',   // 客服主管
            'BattleCommander',         // 会战指挥长
            'SalesTeamLeader',         // 小组组长
            'CityManager',             // 城市经理
            'SalesChannelManger'       // 渠道经理
        ];
        let flag = arr.includes(positionType);
        return flag ? 'MY_POSTN' : 'MY_ORG';
    },

    /**
     * 隐藏遮罩
     */
    hideLoading() {
        $naiveLoading.hideLoading()
    },
    /* ---------------------- 遮罩 End ------------------------ */
    /**
     * 获取服务器时间戳
     * <AUTHOR>
     * @date 2020-07-06
     */
    async getTimestamp() {
        return new Promise((resolve) => {
            $http.post('gateway/time/currentTime', {}, {
                timeout: 5000,
                retryTimeout: 2
            }).then(data => {
                if (data.success) {
                    resolve(data.result)
                }
            })
        })
    },
    /**
     * 获取参数配置
     * <AUTHOR>
     * @date 8/20/21
     * @param key 参数健值
     */
    async getFanweiToken(key) {
        return new Promise(async (resolve) => {
            try {
                const data = await $http.post(env.appURL+'/action/link/sendDmp/send', {"dmpUrl":"/link/consumer/getSingleToken"});
                if (data.success && data.result) {
                    resolve(data.result);
                } else {
                    resolve('');
                }
            } catch (e) {
                resolve('');
            }
        });
    },
    /**
     * 获取参数配置
     * <AUTHOR>
     * @date 8/20/21
     * @param key 参数健值
     * @param cache 是否需要缓存，默认为true
     */
    async getCfgProperty(key, cache: boolean = true) {
        return new Promise(async (resolve) => {
            try {
                // 从缓存获取，获取不到再请求
                const cfgPropertyObject = Taro.getStorageSync('lnkCfgPropertyObj') || {};
                // 获取当时设备时间戳
                const currentTime = new Date().getTime();
                // 设定过期时间，两小时
                const expireTime = 7200000;
                if (cache && !this.isEmpty(cfgPropertyObject[key]) && cfgPropertyObject[key].time + expireTime > currentTime) {
                    resolve(cfgPropertyObject[key].value);
                    return;
                }
                const data = await $http.post('export/link/cfgProperty/queryByExamplePage', {
                    filtersRaw: [{id: 'key', property: 'key', value: key}]
                });
                if (data.success && data.rows && data.rows.length) {
                    cfgPropertyObject[key] = {
                        value: data.rows[0].value,
                        time: currentTime
                    };
                    Taro.setStorageSync('lnkCfgPropertyObj', cfgPropertyObject);
                    resolve(data.rows[0].value);
                } else {
                    resolve('noMatch');
                }
            } catch (e) {
                resolve('noMatch');
            }
        });
    },
    /**
     * 获取时长
     * <AUTHOR>
     * @date 2020-09-02
     * @param startTime
     * @param endTime
     */
    getYMDHMS(startTime, endTime) {
        return new Promise(resolve => {
            let dateInterval = endTime - startTime; //获取时间差毫秒
            //计算小时数
            let hours = Math.floor(dateInterval / (60 * 60 * 1000))
            //计算分钟数
            let minutesLevel = dateInterval % (60 * 60 * 1000);
            let minutes = Math.floor(minutesLevel / (60 * 1000));
            resolve(`${hours}小时${minutes}分钟`);
            // resolve('天数 ' + days + ' 小时数 ' + hours + ' 分钟数 ' + minutes + ' 秒数 ' + seconds);
        });
    },
    /**
     *  @description: 获取财年中本季度的开始时间和结束时间
     *  @author: 马晓娟
     *  @date: 2020/10/29 20:05
     */
    getQuarterMonth(isLastDay = false) {
        const now = new Date(); //当前日期
        let nowMonth = now.getMonth() + 1; //当前月
        let nowYear = now.getFullYear(); //当前年
        let quarterStartMonth = '';
        let quarterEndMonth = '';
        let startDateTemp = '';
        let endDateTemp = '';
        if (nowMonth < 5 && nowMonth > 1) {  // 2月 - 4月
            quarterStartMonth = '0' + 2;
            quarterEndMonth = '0' + 5 + '-01 00:00:00';
            if (isLastDay) {
                quarterEndMonth = '0' + 4 + '-30 23:59:59';
            }

        }
        if (4 < nowMonth && nowMonth < 8) { // 5月 - 7月
            quarterStartMonth = '0' + 5;
            quarterEndMonth = '0' + 8 + '-01 00:00:00';
            if (isLastDay) {
                quarterEndMonth = '0' + 7 + '-31 23:59:59';
            }
        }
        if (7 < nowMonth && nowMonth < 11) { // 8月 - 10月
            quarterStartMonth = '0' + 8;
            quarterEndMonth = '11' + '-01 00:00:00';
            if (isLastDay) {
                quarterEndMonth = '10' + '-31 23:59:59';
            }
        }
        if (nowMonth > 10) { // 11月 - 12月
            quarterStartMonth = '11';
            quarterEndMonth = '0' + 2 + '-01 00:00:00';
            if (isLastDay) {
                quarterEndMonth = '0' + 1 + '-31 23:59:59';
            }

        }
        if (nowMonth < 2) { // 1月
            nowYear--;
            quarterStartMonth = '11';
            quarterEndMonth = '0' + 2 + '-01 00:00:00';
            if (isLastDay) {
                quarterEndMonth = '0' + 1 + '-31 23:59:59';
            }
        }
        startDateTemp = nowYear + '-' + quarterStartMonth + '-01' + ' 00:00:00';
        if (quarterEndMonth === '02-01 00:00:00' || quarterEndMonth === '01-31 23:59:59') {
            nowYear = nowYear + 1;
        }
        endDateTemp = nowYear + '-' + quarterEndMonth;
        return {startDate: startDateTemp, endDate: endDateTemp};
    },
    /**
     *  @description: 获得本周开始时间和结束时间
     *  @author: 马晓娟
     *  @date: 2020/10/29 20:12
     */
    getCurrentWeekDate() {
        const now = new Date(); //当前日期
        let nowDayOfWeek = now.getDay() - 1; //今天本周的第几天
        let nowDay = now.getDate(); //当前日
        let nowMonth = now.getMonth(); //当前月
        let nowYear = now.getFullYear(); //当前年
        let weekStartDate = DateService.format(new Date(nowYear, nowMonth, nowDay - nowDayOfWeek), 'YYYY-MM-DD');
        let weekEndDate = DateService.format(new Date(nowYear, nowMonth, nowDay + (6 - nowDayOfWeek)), 'YYYY-MM-DD');
        if (nowDayOfWeek < 0) {
            weekStartDate = DateService.format(new Date(nowYear, nowMonth, nowDay - 6), 'YYYY-MM-DD');
            weekEndDate = DateService.format(new Date(nowYear, nowMonth, nowDay), 'YYYY-MM-DD');
        }
        return {startDate: weekStartDate + ' 00:00:00', endDate: weekEndDate + ' 23:59:59'};
    },
    /**
     *  @description: 获得本月开始时间和结束时间
     *  @author: 马晓娟
     *  @date: 2020/10/29 20:51
     */
    getCurrentMonthDate(isLastDay = false) {
        const now = new Date(); //当前日期
        const nowMonth = now.getMonth(); //当前月
        const nowYear = now.getFullYear(); //当前年
        //本月的开始时间
        let monthStartDateTemp = new Date(nowYear, nowMonth, 1);
        //本月的结束时间
        let monthEndDateTemp = new Date(nowYear, nowMonth + 1, 1);
        let monthEndDate = DateService.format(monthEndDateTemp, 'YYYY-MM-DD') + ' 00:00:00';
        if (isLastDay) {
            monthEndDateTemp = new Date(nowYear, nowMonth + 1, 0);
            monthEndDate = DateService.format(monthEndDateTemp, 'YYYY-MM-DD') + ' 23:59:59';
        }
        const monthStartDate = DateService.format(monthStartDateTemp, 'YYYY-MM-DD');
        return {startDate: monthStartDate + ' 00:00:00', endDate: monthEndDate};
    },
    /**
     * @createdBy  张丽娟
     * @date  2020/10/30
     * @methods getCurrentYearDate
     * @para
     * @description  获取本地开始时间和结束时间
     */
    getCurrentYearDate() {
        const nowTemp = new Date(); //当前日期
        const nowYear = nowTemp.getFullYear(); //当前年
        const nowMonth = nowTemp.getMonth(); //当前月
        if (nowMonth > 9) {
            const endYear = nowYear + 1;
            return {startDate: nowYear + '-11-01 00:00:00', endDate: endYear + '-10-31 23:59:59'};
        } else {
            const startYear = nowYear - 1;
            return {startDate: startYear + '-11-01 00:00:00', endDate: nowYear + '-10-31 23:59:59'};
        }
    },
    /**
     * @createdBy  宋燕荣
     * @date  2021/12/05
     * @methods getCurrentYearDateNew
     * @para 返回值是 0（一月） 到 11（十二月） 之间的一个整数
     * @description  市场活动报表的财年开始时间和结束时间日期获取
     * 【当前月大于等于11月 那么财年的开始时间为今年的11-01 00:00:00 结束时间为明年的10-31 23:59:59
     *  如果当前月小于11月 那么财年的呃开始时间为去年的11-01 00:00:00 结束时间为今年的10-31 23:59:59】
     */
    getCurrentYearDateNew() {
        const nowTemp = new Date(); //当前日期
        const nowYear = nowTemp.getFullYear(); //当前年
        const nowMonth = nowTemp.getMonth(); //当前月
        if (nowMonth > 9) {
            const endYear = nowYear + 1;
            return {startDate: nowYear + '-11-01 00:00:00', endDate: endYear + '-10-31 23:59:59'};
        } else {
            const startYear = nowYear - 1;
            return {startDate: startYear + '-11-01 00:00:00', endDate: nowYear + '-10-31 23:59:59'};
        }
    },
    /**
     * @createdBy  宋燕荣
     * @date  2022/01/04
     * @methods getCurrentFiscalYear
     * @para 返回值是 0（一月） 到 11（十二月） 之间的一个整数
     * @description  市场活动报表的财年开始时间和结束时间日期获取
     * 【当前月大于等于11月 那么财年的开始时间为今年的11-01 00:00:00 结束时间为明年的10-31 23:59:59
     *  如果当前月小于11月 那么财年的呃开始时间为去年的11-01 00:00:00 结束时间为今年的10-31 23:59:59】
     */
    getCurrentFiscalYear() {
        const nowTemp = new Date(); //当前日期
        const nowYear = nowTemp.getFullYear(); //当前年
        const nowMonth = nowTemp.getMonth(); //当前月
        if (nowMonth > 10) {
            const endYear = nowYear + 1;
            return endYear;
        } else {
            return nowYear;
        }
    },
    /**
     * @desc 近几月开始结束时间
     * <AUTHOR>
     * @date 2024/8/22
     **/
    getRecentlyMonth(i) {
        const now = new Date();
        const year = now.getFullYear();
        let month = now.getMonth() + 1;
        let startMonth = month - i;
        let day = now.getDate();
        let startDate = '';
        let endDate = '';
        let monthStr = month < 10 ? '0' + month : '' + month;
        let dayStr = day < 10 ? '0' + day : '' + day;
        endDate = year + '-' + monthStr + '-' + dayStr + ' 23:59:59';
        if (i === 12) {
            //如果是12月，年数往前推一年<br>
            startDate = year - 1 + "-" + monthStr + "-" + dayStr + ' 00:00:00';
        } else if (startMonth <= 0) {
            let displayMonth = 12 + startMonth < 10 ? '0' + (12 + startMonth) : (12 + startMonth);
            // 如果前推i月小于0，年数往前推一年<br>
            startDate = year - 1 + "-" + displayMonth + "-" + dayStr + ' 00:00:00';
        } else {
            const preSize = new Date(year, startMonth, 0).getDate(); // 开始时间所在月的总天数
            const newMonthDay = new Date(year, month, 0).getDate(); // 当前日期所在月的总天数
            const resultMonth = startMonth < 10 ? '0' + startMonth : startMonth;
            if (preSize < day) {
                if (day < newMonthDay) {
                    // 当前天日期小于当前月总天数
                    startDate = year + "-" + resultMonth + "-" + (preSize - (newMonthDay - day)) + ' 00:00:00';
                } else {
                    startDate = year + "-" + resultMonth + preSize + ' 00:00:00';
                }
            } else {
                startDate = year + '-' + resultMonth + '-' + dayStr + ' 00:00:00';
            }
        } 
        return {startDate, endDate};
    },
    /**
     * @desc 近三个月开始结束时间
     * <AUTHOR>
     * @date 2022/8/26 14:20
     **/
    getLast3Month() {
        const now = new Date();
        const year = now.getFullYear();
        let month = now.getMonth() + 1;
        let day = now.getDate();
        let startDate = '';
        let endDate = '';
        let monthStr = '' + month;
        let dayStr = '' + day;
        if (month < 10) {
            monthStr = '0' + month
        }
        if (day < 10) {
            dayStr = '0' + day
        }
        endDate = year + '-' + monthStr + '-' + dayStr + ' 23:59:59'
        if ((now.getMonth() + 1) == 1) {
            //如果是1月份，则取上一年的10月份
            startDate = (year - 1 ) + '-10-' + dayStr + ' 00:00:00'
        } else if ((now.getMonth() + 1) == 2) {
            //如果是2月份，则取上一年的11月份
            startDate = (year - 1) + '-11-' + dayStr + ' 00:00:00'
        } else if ((now.getMonth() + 1) == 3) {
            //如果是3月份，则取上一年的12月份
            startDate = (year - 1) + '-12-' + dayStr + ' 00:00:00'
        } else {
            let preSize = new Date(year, month - 3, 0).getDate() //开始时间所在月的总天数
            if (preSize < day) {
                // 开始时间所在月的总天数<本月总天数，比如当前是5月30日，在2月中没有30，则取下个月的第一天(3月1日)为开始时间
                let resultMonth = month - 2 < 10 ? '0' + (month + 2 ): month - 2
                startDate = year + '-' + resultMonth + '-01' + ' 00:00:00'
            }

            if (month < 13) {
                startDate = year + '-0' + (month - 3) + '-' + dayStr + ' 00:00:00'
            } else {
                startDate = year + '-' + (month - 3) + '-' + dayStr + ' 00:00:00'
            }
        }
        return {startDate, endDate};
    },
    /**
     * @desc 获取近半年的数据
     * <AUTHOR>
     * @date 2022/8/26 14:34
     **/
    getLastHalfYear(){
        const now = new Date();
        const year = now.getFullYear();
        //0-11表示1-12月
        let month = now.getMonth() + 1;
        let day = now.getDate();
        let monthStr = '' + month;
        let dayStr = '' + day;
        let startDate = '';
        if ((now.getMonth() + 1) < 10) {
            monthStr = '0' + month
        }
        if (now.getDate() < 10) {
            dayStr = '0' + now.getDate()
        }
        let endDate = year + '-' + monthStr + '-' + dayStr + ' 23:59:59';
        //当前月的总天数
        const nowMonthDay = new Date(year, month, 0).getDate();
        //如果是1、2、3,4,5,6月，年数往前推一年
        if (month - 6 <= 0) {
            //6个月前所在月的总天数
            let last6MonthDay = new Date((year - 1), (12 - (6 - month)), 0).getDate();
            if ((12 - (6 - month)) < 10) {
                monthStr = '0' + (6 + month)
            } else {
                monthStr = '' + (6 + month)
            }
            //6个月前所在月的总天数小于现在的天日期
            if(last6MonthDay < day){
                if (last6MonthDay < 10) {
                    dayStr = '0' + last6MonthDay
                } else {
                    dayStr = '' + last6MonthDay
                }
            }
            startDate = (year - 1) + '-' + monthStr + '-' + dayStr + ' 00:00:00';
        } else {
            //6个月前所在月的总天数
            let last6MonthDay = new Date(year, (month - 6), 0).getDate();
            if ((month - 6) < 10) {
                monthStr = '0' + (month - 6);
            } else {
                monthStr = '' + (month - 6);
            }
            //6个月前所在月的总天数小于现在的天日期
            if (last6MonthDay < day) {
                //当前天日期小于当前月总天数,2月份比较特殊的月份
                if (day < nowMonthDay) {
                    if ((last6MonthDay - (nowMonthDay - day)) < 10) {
                        dayStr = '0' + (last6MonthDay - (nowMonthDay - day));
                    } else {
                        dayStr = '' + (last6MonthDay - (nowMonthDay - day));
                    }
                } else {
                    if (last6MonthDay < 10) {
                        dayStr = '0' + last6MonthDay;
                    } else {
                        dayStr = '' + last6MonthDay;
                    }
                }
            }
            startDate = year + '-' + monthStr + '-' + dayStr + ' 00:00:00';
        }
        return {startDate, endDate};
    },
    /**
     * @desc 获取近一年时间
     * <AUTHOR>
     * @date 2022/8/26 14:58
     **/
    getLastYear () {
        const now = new Date();
        const year = now.getFullYear();
        let month = now.getMonth() + 1;
        let day = now.getDate();
        let monthStr = '' + month;
        let dayStr = '' + day;
        if (month < 10) {
            monthStr = '0' + month
        }
        if (now.getDate() < 10) {
            dayStr = '0' + now.getDate()
        }
        let endDate = year + '-' + monthStr + '-' + dayStr + ' 23:59:59';
        const start = new Date(new Date().setFullYear(year-1));
        const startDate = DateService.format(start, 'YYYY-MM-DD 00:00:00');
        return {startDate, endDate};
    },
    isMoreThanAYearOld(dateString,dayNum) {
        // 将输入的时间字符串解析为 Date 对象
        const inputDate = new Date(dateString);

        // 获取当前时间
        const currentDate = new Date();

        // 计算输入日期一年前的日期
        const oneYearAgo = new Date();
        oneYearAgo.setFullYear(currentDate.getFullYear() - 1);
        // 如果当前月份还没有到输入日期的月份，或者相同月份但日期还没有到，需要再往前调整一个月或日期
        if (
            currentDate.getMonth() < inputDate.getMonth() ||
            (currentDate.getMonth() === inputDate.getMonth() && currentDate.getDate() < inputDate.getDate())
        ) {
            oneYearAgo.setFullYear(currentDate.getFullYear() - 1); // 先确保年份减一
            // 精确到日调整可以视情况优化，但一般按年计算时只减去年份即可满足大部分需求
        }
        // 更简单的方式直接通过时间戳差值计算超过一年（365天）的情况：
        const oneYearInMilliseconds = (dayNum||365) * 24 * 60 * 60 * 1000; // 一年的毫秒数

        // 比较输入日期和一年前的日期（通过时间戳差值判断）
        return (currentDate - inputDate) > oneYearInMilliseconds;

        // 或者使用更精确的日期对比逻辑（考虑闰年等），可以替代上面简单的时间戳计算：
        /*
        return inputDate < oneYearAgo; // 若使用调整后的精确日期对比
        */
    },
    uuid() {
        let uuid = '';
        if (uuid) return uuid;
        // 给客户端生成唯一性id
        const system = DeviceService.systemInfo;
        /**
         * 系统时间/时钟周期信息
         * @returns {string}
         * @constructor
         */
        const T = function () {
            // @ts-ignore
            let d = 1 * new Date(); // // 1*new Date() 等于跨浏览器的 Date.now()
            let i = 0;
            // 该循环计算在new Date()返回新值时能执行多少次加法，
            // 即每毫秒执行指令数，并非精确值
            // @ts-ignore
            while (d == 1 * new Date()) {
                i++;
            }
            return d.toString(16) + i.toString(16);
        };
        // 生成随机数
        const R = function () {
            return Math.random().toString(16).replace('.', '');
        };
        /**
         * 获取用户userAgent字符串，然后每8位进行异或运算，最终产生8位序列并返回16进制编码
         * @returns {string}
         * @constructor
         */
        const UA = function () {
            let ua = JSON.stringify(system);
            let i;
            let ch;
            let buffer = [];
            let ret = 0;

            function xor(result, byte_array) {
                let j = 0;
                let tmp = 0;
                for (j = 0; j < byte_array.length; j++) {
                    tmp |= (buffer[j] << j * 8);
                }
                return result ^ tmp;
            }

            for (i = 0; i < ua.length; i++) {
                ch = ua.charCodeAt(i);
                // @ts-ignore
                buffer.unshift(ch & 0xFF);
                if (buffer.length >= 4) {
                    ret = xor(ret, buffer);
                    buffer = [];
                }
            }
            if (buffer.length > 0) {
                ret = xor(ret, buffer);
            }
            return ret.toString(16);
        };
        let se = (system.screenWidth * system.screenHeight).toString(16);
        uuid = T() + '-' + R() + '-' + UA() + '-' + se + '-' + T();
        return uuid;
    },
    /**
     * 请求参数UUID
     * <AUTHOR>
     * @date 8/2/21
     */
    requestUuid() {
        const s: Array<string> = [];
        const hexDigits = '0123456789abcdef';
        for (let i = 0; i < 36; i++) {
            s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
        }
        s[14] = '4';
        // @ts-ignore
        s[19] = hexDigits.substr(s[19]& 0x3 | 0x8, 1);
        s[8] = s[13] = s[18] = s[23] = '-';

        const preFix = 'corpWx-';
        const uuId = preFix + s.join('');
        const versionNum = '-' + (Taro.getAccountInfoSync().miniProgram.version || version)
        return  uuId + versionNum
    },
    /**
     * @createdBy  songyanrong
     * @date  2020/12/29
     * @methods numberMul
     * @para
     * @description  浮点型乘法
     */
    numberMul(arg1, arg2) {
        let m = 0;
        let s1 = arg1.toString();
        let s2 = arg2.toString();
        try {
            m += s1.split(".")[1].length;
        } catch (e) {
        }
        try {
            m += s2.split(".")[1].length;
        } catch (e) {
        }
        const a = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);
        return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);
    },
    /**
     * @createdBy  songyanrong
     * @date  2020/12/29
     * @methods numberMul
     * @para
     * @description  浮点型加法
     */
    numberAdd(arg1, arg2) {
        let r1, r2, m, n;
        try {
            r1 = arg1.toString().split(".")[1].length
        } catch (e) {
            r1 = 0
        }
        try {
            r2 = arg2.toString().split(".")[1].length
        } catch (e) {
            r2 = 0
        }
        m = Math.pow(10, Math.max(r1, r2));
        n = (r1 >= r2) ? r1 : r2;
        return ((arg1 * m + arg2 * m) / m).toFixed(n);
    },
    /**
     * @createdBy  songyanrong
     * @date  2021/05/31
     * @methods numberMul
     * @para
     * @description 获取目标日期
     * interval: 传-1表始前一天，传1表始后一天
     * date：当前时间 格式：'2021-05-31'
     * caret：格式 - /
     */
    getNowFormatDate(date, interval, caret) {
        let patt1 = /^\d{4}-([0-1]?[0-9])-([0-3]?[0-9])$/;  //判断输入的日期是否符合格式正则表达式
        if (!(date && typeof (date) == "string" && patt1.test(date))) {
            date = new Date(); //不满足日期的则使用当前年月日
        }
        interval = isNaN(parseInt(interval)) ? 0 : parseInt(interval);//若没有输入间隔，则使用当前日
        caret = (caret && typeof (caret) == "string") ? caret : "";
        let gdate = new Date(date).getTime();//获取指定年月日
        gdate = gdate + 1000 * 60 * 60 * 24 * interval; //加减相差毫秒数
        let speDate = new Date(gdate);//获取指定好毫秒数时间
        let preYear = speDate.getFullYear();
        let preMonth: string | number = speDate.getMonth() + 1;
        let preDay: string | number = speDate.getDate();
        preMonth = (preMonth < 10) ? ("0" + preMonth) : preMonth;
        preDay = (preDay < 10) ? ("0" + preDay) : preDay;
        let preDate = preYear + caret + preMonth + caret + preDay;
        return preDate;
    },
    /**
     * 泸州老窖企微项目--获取企微小程序模板内容
     * 第一顺位：从缓存中获取
     * 第二顺位：从后端查询获取
     * <AUTHOR>
     * @date 6/22/21
     * @param type 模板类型
     * @param subType 模板子类型
     * @param feeType 费用子类型
     * @param actType 活动类型
     * @param cacheFrom 是否需要从缓存中获取，避免有些模板变更比较频繁，每次都需要实时获取
     * @param baseUrl 模板基础查询路径
     * @return {success: true/false, result: 模板内容，message: 错误信息}
     */
    async getQwMpTemplate(type: string,
                          subType: string = '',
                          feeType: string = '',
                          actType: string = '',
                          cacheFrom: boolean = true,
                          baseUrl: string = 'action/link/mpTmpl/get/') {
        // 返回结果
        const result  = {success: false, result: null, message: '模板获取失败'};
        if (!type || type.indexOf('/') !== -1) {
            result.message = '模板类型不正确';
            return result;
        }
        const QW_MP_TMPL_KEY = 'link_qw_mp_tmpl'; // 企微小程序模板缓存对象key值
        // 获取模板缓存
        let tmplObject: Object = Taro.getStorageSync(QW_MP_TMPL_KEY) || {};
        // 1.根据传入的模板参数，生成模板健值
        const moduleKey = type + subType + feeType + actType;
        // 2.根据key值尝试从缓存中获取模板
        const moduleContent = tmplObject[moduleKey] || null;
        // 3.分离模板中的配置内容与时间戳
        let timestamp = '';
        let jsonResult = null;
        if (Boolean(moduleContent) && cacheFrom) {
            const index = moduleContent.indexOf('#');
            // 存在时间戳
            if (index > 0 && index < 30) {
                timestamp = moduleContent.substring(0, index);
                jsonResult = moduleContent.substring(index + 1);
            } else {
                // 不存在时间戳，则直接返回缓存内容
                result.success = true;
                result.message = '模板获取成功';
                result.result = moduleContent;
                return result;
            }
        }
        // 4.缓存中不存在或者需要对比最新更新时间戳，则重新从数据库获取
        let paramUrl: Array<string> = [];
        if (subType) {
            paramUrl.push(`subType=${subType}`);
        }
        if (feeType) {
            paramUrl.push(`feeType=${feeType}`);
        }
        if (actType) {
            paramUrl.push(`actType=${actType}`);
        }
        // 拼接时间戳，用于对比是否需要更新模板
        if (this.isNotEmpty(timestamp)) {
            paramUrl.push(`timestamp=${timestamp}`);
        }
        // 拼接查询参数
        baseUrl = paramUrl.length > 0 ? `${baseUrl}${type}?${paramUrl.join('&')}` : `${baseUrl}${type}`;
        try {
            const queryData = await $http.post(baseUrl, {}, {
                handleFailed: (error) => {
                    showError(queryData.result);
                }
            });
            // 5.查询成功，返回并缓存结果
            if (queryData.success) {
                if (this.isNotEmpty(queryData.result)) {
                    const index = queryData.result.indexOf('#');
                    if (index > 0 && index < 30) {
                        result.result = queryData.result.substring(index + 1);
                    } else {
                        result.result = queryData.result;
                    }
                    result.success = true;
                    result.message = '模板获取成功';
                    tmplObject[moduleKey] = queryData.result;
                    Taro.setStorageSync(QW_MP_TMPL_KEY, tmplObject);
                    return result;
                } else {
                    result.success = true;
                    result.message = '模板获取成功';
                    result.result = jsonResult;
                    return result;
                }
            }
        } catch (e) {
            aegis.report({
                msg: '模板获取出错',
                ext1: JSON.stringify(e),
                trace: 'log'
            });
            // $logger.log('模板获取出错',e);
            if(e.success === undefined){
                let result = {}
                await this.sleep(500);
                result = await this.repetitionGetQwMpTemplate(type, subType, feeType, actType, cacheFrom);
                return result
            }else{
            result.success = false;
            result.message = e.error || e.result;
            }
        }
        // 缓存以及后台都未成功获取，返回失败
        return result;
    },
    /**
     * 等待
     * <AUTHOR>
     * @date 2022年8月5日
     */
    sleep(millisecond){
        return new Promise(resolve => {
            setTimeout(() => {
                resolve(1)
            }, millisecond)
        })
    },
    async repetitionGetQwMpTemplate(type: string,
                          subType: string = '',
                          feeType: string = '',
                          actType: string = '',
                          cacheFrom: boolean = true,
                          baseUrl: string = 'action/link/mpTmpl/get/') {
      /*  aegis.report({
          msg: '模板获取重复调用',
          ext1: `${type},${subType},${feeType},${actType},${cacheFrom},${baseUrl}`,
          trace: 'log'
        });*/
        // $logger.log("模板获取重复调用",type,subType,feeType,actType,cacheFrom,baseUrl);
        // 返回结果
        const result  = {success: false, result: null, message: '模板获取失败'};
        if (!type || type.indexOf('/') !== -1) {
            result.message = '模板类型不正确';
            return result;
        }
        const QW_MP_TMPL_KEY = 'link_qw_mp_tmpl'; // 企微小程序模板缓存对象key值
        // 获取模板缓存
        let tmplObject: Object = Taro.getStorageSync(QW_MP_TMPL_KEY) || {};
        // 1.根据传入的模板参数，生成模板健值
        const moduleKey = type + subType + feeType + actType;
        // 2.根据key值尝试从缓存中获取模板
        const moduleContent = tmplObject[moduleKey] || null;
        // 3.分离模板中的配置内容与时间戳
        let timestamp = '';
        let jsonResult = null;
        if (Boolean(moduleContent) && cacheFrom) {
            const index = moduleContent.indexOf('#');
            // 存在时间戳
            if (index > 0 && index < 30) {
                timestamp = moduleContent.substring(0, index);
                jsonResult = moduleContent.substring(index + 1);
            } else {
                // 不存在时间戳，则直接返回缓存内容
                result.success = true;
                result.message = '模板获取成功';
                result.result = moduleContent;
                return result;
            }
        }
        // 4.缓存中不存在或者需要对比最新更新时间戳，则重新从数据库获取
        let paramUrl: Array<string> = [];
        if (subType) {
            paramUrl.push(`subType=${subType}`);
        }
        if (feeType) {
            paramUrl.push(`feeType=${feeType}`);
        }
        if (actType) {
            paramUrl.push(`actType=${actType}`);
        }
        // 拼接时间戳，用于对比是否需要更新模板
        if (this.isNotEmpty(timestamp)) {
            paramUrl.push(`timestamp=${timestamp}`);
        }
        // 拼接查询参数
        baseUrl = paramUrl.length > 0 ? `${baseUrl}${type}?${paramUrl.join('&')}` : `${baseUrl}${type}`;
        try {
            const queryData = await $http.post(baseUrl, {});
            // 5.查询成功，返回并缓存结果
            if (queryData.success) {
                if (this.isNotEmpty(queryData.result)) {
                    const index = queryData.result.indexOf('#');
                    if (index > 0 && index < 30) {
                        result.result = queryData.result.substring(index + 1);
                    } else {
                        result.result = queryData.result;
                    }
                    result.success = true;
                    result.message = '模板获取成功';
                    tmplObject[moduleKey] = queryData.result;
                    Taro.setStorageSync(QW_MP_TMPL_KEY, tmplObject);
                    return result;
                } else {
                    result.success = true;
                    result.message = '模板获取成功';
                    result.result = jsonResult;
                    return result;
                }
            }
        } catch (e) {
            aegis.report({
                msg: '模板获取重复调用出错',
                ext1: JSON.stringify(e),
                trace: 'log'
            });
            // $logger.log("模板获取重复调用出错1",e.toString());
            // $logger.log('模板获取重复调用出错2',e)
            result.success = false;
            result.message = e.error || e.result;
        }
        // 缓存以及后台都未成功获取，返回失败
        return result;
    },
    /**
     * 泸州老窖企微项目--查询菜单的访问组安全性
     * <AUTHOR>
     * @date 7/21/21
     * @param menuId 菜单ID
     * @param urlPath 菜单页面路径：和PC端配置的APP菜单地址格式一致【eg:/pages/terminal/terminal/terminal-list-page】
     * @param securityMode 类型
     * @desc 从缓存中获取当前用户的菜单信息，通过菜单ID筛选当前菜单的安全性数据menuSecurity:[]。
     * securityMode: "ACCESS_GROUP"。安全性类型没有例外的话就默认访问组安全性
     * 如果配置了访问组安全性，反正访问组安全性securityMode拼接访问组安全性菜单ID，eg：ACCESS_GROUP359651842923435912.作为列表查询数据时oauth的value
     */
    getMenuAccessGroup(menuId:any,urlPath?:string,securityMode:string = 'ACCESS_GROUP') {
        //菜单ID或者配置的菜单路径都是空的
        if(this.isEmpty(menuId) && this.isEmpty(urlPath)){
            return null;
        }
        //@ts-ignore
        let menu = Taro.getStorageSync('token').menus || [];         // 获取菜单信息
        //菜单ID为空[有的菜单模块不是从首页跳转的而是从别的模块跳转过去的]，可以传指定模块的配置路径获取菜单ID
        if(this.isEmpty(menuId) && !this.isEmpty(urlPath)){
            menu.map((item)=> {
                const subMenus = item['subMenus'] || [];
                subMenus.map((item2) => {
                    if(item2.urlPath === urlPath){
                        menuId = item2.id;
                    }
                });
            });
        }
        let filterSubMenus = [];
        menu.map((item)=> {
            const subMenus = item['subMenus'] || [];
            subMenus.map((item2) => {
                if(item2.id === menuId){
                    filterSubMenus = item2.menuSecurity;
                }
            });
        });
        if(!this.isEmpty(filterSubMenus)){
            const securityModeExit = filterSubMenus.filter((item1) => item1['securityMode'] === securityMode);
            if(!this.isEmpty(securityModeExit)){
                return securityModeExit[0]['securityMode'] + securityModeExit[0]['id']
            } else {
                return null;
            }
        }
        return null;
    },
    /**
     * 获取服务器时间
     * <AUTHOR>
     * @date 2022/8/12
     * @return localTimeStamp
     */
    async getServerTime() {
        // 从本地缓存中获取服务器时间戳
        const serverTimeStamp = Taro.getStorageSync('serverTimeStamp');
        // 手机本地当前时间戳
        const localTimeStamp  = (new Date()).getTime();
        // 本地时间与服务器时间差值
        const differ = localTimeStamp - serverTimeStamp;
        // 本地缓存中不存在服务器时间戳，或者差值小于零，或者差值大于半小时
        // 则重新获取服务器时间
        // 否则直接返回手机本地时间
        if (this.isEmpty(serverTimeStamp) || differ < 0 || differ > 30*1000) {
            // 使用try...catch块捕获异常，避免接口异常影响体验
            // 请求发生错误依旧返回手机本地时间，避免功能无法使用情况发生
            try {
                const data = await $http.post('gateway/time/currentTime', {},  {
                    timeout: 5000,
                    retryIntervalTime: 1000,
                    retryTimeout: 3
                });
                if (data.success && this.isNotEmpty(data.result)) {
                    Taro.setStorageSync('serverTimeStamp', data.result);
                    return data.result;
                } else {
                    return localTimeStamp;
                }
            } catch(e) {
                aegis.report({
                    msg: '当前网络不可用，请尝试切换网络重试',
                    ext1: JSON.stringify(e),
                    trace: 'log'
                });
                return localTimeStamp;
            }
        } else {
            return localTimeStamp;
        }
    },
    /**
     * 处理首页待办事项展示数量
     * <AUTHOR>
     * @date 2022/11/07
     * @param handleType 操作类型 get | del
     * @param todoKey 待办事项缓存key （目前key值：{
     *                                              unReadMessage: 未读消息,
     *                                              unApproval: 待审批,
     *                                              unReadAnnounce: 未读公告消息,
     *                                              unFeedbackWork: 未执行反馈}）
     * @param url 待办事项数量请求URL
     * @param param 待办事项数量请求参数
     * @param pickNumCallback 从请求返参中获取待办事项数量，入参为请求返参
     */
    async handleTODOListNumber(handleType: string='get', todoKey: string, url?: string, param?: object, pickNumCallback?: Function) {
        if (this.isEmpty(todoKey)) {
            return 0;
        }
        // 获取缓存中的待办事项数量
        const todoListNumObj = Taro.getStorageSync('todoListNumber') || {};
        // 清理指定待办事项数量缓存
        if (handleType === 'del') {
            if (this.isNotEmpty(todoListNumObj[todoKey])) {
                delete todoListNumObj[todoKey];
                Taro.setStorageSync('todoListNumber', todoListNumObj);
            }
            return;
        }
        if (handleType === 'get' ) {
            if (this.isNotEmpty(todoListNumObj[todoKey])) {
                return todoListNumObj[todoKey];
            }
            if (this.isNotEmpty(url) && this.isNotEmpty(param)) {
                try {
                    const data = await $http.post(url, param, {autoHandleError: false});
                    if (!!pickNumCallback) {
                        todoListNumObj[todoKey] = pickNumCallback(data);
                    } else{
                        todoListNumObj[todoKey] = data.rows && data.rows.length > 0 ? data.rows[0].total : 0
                    }
                    // 将最新数量更新到缓存中
                    Taro.setStorageSync('todoListNumber', todoListNumObj);
                    return  todoListNumObj[todoKey] || 0;
                } catch (e) {
                    console.log(e);
                }
            }
        }
    },
    dateFormat(date, sFormat = 'yyyy-MM-dd') {
        if (!(date instanceof Date)) {
            try {
                if (this.isEmpty(date)) {
                    return ''
                }
                if (date.lastIndexOf('.') !== -1) {
                    // eslint-disable-next-line no-param-reassign
                    date = date.substr(0, date.lastIndexOf('.'))
                }
                date = date.replace(/\-/g, '/') // eslint-disable-line
                // eslint-disable-next-line no-param-reassign
                date = new Date(date)
            } catch (e) {
                console.log(e)
            }
        }

        const time = {
            Year: 0,
            TYear: '0',
            Month: 0,
            TMonth: '0',
            Day: 0,
            TDay: '0',
            Hour: 0,
            THour: '0',
            hour: 0,
            Thour: '0',
            Minute: 0,
            TMinute: '0',
            Second: 0,
            TSecond: '0',
            Millisecond: 0
        }
        time.Year = date.getFullYear()
        time.TYear = String(time.Year).substr(2)
        time.Month = date.getMonth() + 1
        time.TMonth = time.Month < 10 ? '0' + time.Month : String(time.Month)

        time.Day = date.getDate()
        time.TDay = time.Day < 10 ? '0' + time.Day : String(time.Day)

        time.Hour = date.getHours()
        time.THour = time.Hour < 10 ? '0' + time.Hour : String(time.Hour)
        time.hour = time.Hour < 13 ? time.Hour : time.Hour - 12
        time.Thour = time.hour < 10 ? '0' + time.hour : String(time.hour)

        time.Minute = date.getMinutes()
        time.TMinute = time.Minute < 10 ? '0' + time.Minute : String(time.Minute)
        time.Second = date.getSeconds()
        time.TSecond = time.Second < 10 ? '0' + time.Second : String(time.Second)
        time.Millisecond = date.getMilliseconds()

        return sFormat.replace(/yyyy/ig, String(time.Year))
            .replace(/yyy/ig, String(time.Year))
            .replace(/yy/ig, time.TYear)
            .replace(/y/ig, time.TYear)

            .replace(/MM/g, time.TMonth)
            .replace(/M/g, String(time.Month))

            .replace(/dd/ig, time.TDay)
            .replace(/d/ig, String(time.Day))

            .replace(/HH/g, time.THour)
            .replace(/H/g, String(time.Hour))
            .replace(/hh/g, time.Thour)
            .replace(/h/g, String(time.hour))

            .replace(/mm/g, time.TMinute)
            .replace(/m/g, String(time.Minute))
            .replace(/ss/ig, time.TSecond)
            .replace(/s/ig, String(time.Second))
            .replace(/fff/ig, String(time.Millisecond))
    },
    /**
     * 生成价格隐藏判断标识
     * <AUTHOR>
     * @date 2023-05-30
     * @param userInfo 用户信息，一般从缓存取
     * @return boolean true(显示) false(隐藏)
     */
    async getPriceDesensitize(userInfo) {
        if (this.isEmpty(userInfo)) {
            userInfo = Taro.getStorageSync('token').result;
        }
        // 获取价格隐藏公司范围参数配置
        const companyScope = await this.getCfgProperty('dealer_hidden_cost');
        // 参数不存在则展示
        if (this.isEmpty(companyScope)) {
            aegis.report({
                msg: '获取价格隐藏公司范围参数配置为空',
                trace: 'log'
            });
            // $logger.log('获取价格隐藏公司范围参数配置为空');
            return true;
        }
        // 判断用户的所属品牌公司是否包含在价格隐藏公司范围
        const userBrandCode = userInfo.coreOrganizationTile.brandCompanyCode;
        if (this.isEmpty(userBrandCode)) {
            aegis.report({
                msg: `${userInfo.firstName}用户品牌公司编码为空`,
                trace: 'log'
            });
            // $logger.log(`${userInfo.firstName}用户品牌公司编码为空`);
            return false;
        }
        // 不包含则展示
        if (companyScope.indexOf(userBrandCode) === -1) {
            return true;
        }
        // 用户的人员类型为空
        if (this.isEmpty(userInfo.staffType)) {
            aegis.report({
                msg: `${userInfo.firstName}用户人员类型为空`,
                trace: 'log'
            });
            // $logger.log(`${userInfo.firstName}用户人员类型为空`);
            return true;
        }
        if (this.isEmpty(userInfo.positionType)) {
            aegis.report({
                msg: `${userInfo.firstName}用户职位类型为空`,
                trace: 'log'
            });
            // $logger.log(`${userInfo.firstName}用户职位类型为空`);
        }
        // 内部员工非业务代表可见，其他情况不可见
        return userInfo.staffType === 'Internal' && userInfo.positionType !== 'Salesman';
    },
    /**
     * 国窖/窖龄经销商人员安全性限制标识
     * <AUTHOR>
     * @date	2023/10/26 16:06
     * @param userInfo 用户信息
     */
    async getDealerOauth(userInfo) {
        if (this.isEmpty(userInfo) || this.isEmpty(userInfo.id)) {
            userInfo = Taro.getStorageSync('token').result;
        }
        // 获取走经销商人员安全性的公司的参数配置Termianl_Dealer_Oauth
        const terminalDealer = await this.getCfgProperty('Terminal_Dealer_Oauth');
        // 参数不存在不走特殊安全性
        if (this.isEmpty(terminalDealer)) {
            return false;
        }
        // 判断用户的所属品牌公司是否包含在销商人员安全性公司范围
        const userBrandCode = userInfo.coreOrganizationTile.brandCompanyCode;
        // 用户品牌公司编码为空、人员类型为空、用户品牌公司编码不包含在参数配置内不走特殊安全性
        if (this.isEmpty(userBrandCode) || this.isEmpty(userInfo.staffType) || terminalDealer.indexOf(userBrandCode) === -1) {
            return false;
        }
        // 符合条件的经销商人员走特殊安全性
        return userInfo.staffType === 'Dealer';
    },
    /**
     * 查看附件 —— 兼容ios和安卓  —— 报401 权限方面问题的时候 去小程序后台 把域名配置加上
     * @param item （附件item项）
     * @param type类型系统内部访问: inner│系统外部访问: outer。默认inner，用完即走
     * @param file_path静态文件相对路径，如/share/io/export/20240814/172363409865自定义对象_202408141914058.xLSX
     * expired_time 过期时间，单位:秒。默认7200秒。不能大于3天。一般建议24小时。
     * flag  是否需要token校验
     */
    async checkAttachmentAllType(item,type,file_path,expired_time,flag) {
        const imageType = ['jpg', 'jpe', 'jpeg', 'jff', 'jif', 'png', 'gif', 'jfif', 'fif']
        const videoType = ['mp4', 'mov']
        const docType = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf']
        const allType = imageType.concat(videoType).concat(docType)
        // 设备信息
        // const { platform } = TaroUtils.getSystemInfoSync()
        const isIos = DeviceService.systemInfo.platform === 'ios'
        if (imageType.indexOf(item.attachmentType) > -1) {
            // 图片类型
            wx.previewImage({
                current: item.attachmentPath,
                urls: [item.attachmentPath],
                fail(err: any) {
                    console.log(err)
                    this.$taro.showToast({
                        title: '预览图片失败，请稍后再试。'
                    });
                    // $toast('预览图片失败，请稍后再试。')
                }
            })
        } else {
            wx.showLoading({
                title: '加载中...'
            })
            const { token } = wx.getStorageSync('token')
            let access_token = null
            const param = {
                type: type || 'inner',
                file_path: file_path,
                expired_time: type==='inner'? 10*60*60 : expired_time || 7200
            }
            if(flag) {
                try{
                    const res: any = await $http.post('/nginx/get_tmp_access_token',param)
                    if(res.success){
                        access_token = res.temp_access_token
                    }
                }catch (e){
                    console.log(e)
                    wx.hideLoading()
                }
            }
            const attachmentPath = flag ? item.attachmentPath + `?temp_access_token=${access_token}`:item.attachmentPath
            wx.downloadFile({
                url: attachmentPath,
                header: {
                    'Content-Type': item.attachmentType,
                },
                success(res: any) {
                    console.log('downloadFile', res)
                    console.log('fileType', item.attachmentType)
                    if (res.statusCode !== 200) {
                        wx.hideLoading()
                        let tip = '获取文件临时地址失败，无法在小程序打开！'
                        if (res.statusCode == 401) tip = `${item.attachmentType}权限未获取，401 Authorization Required`
                        // $message.error(tip)
                        console.error(tip)
                        this.taro.setClipboardData({
                            data: attachmentPath,
                            success: function (res) {
                                this.$dialog({
                                    title: '提示',
                                    content: '不支持打开此类文件，文件路径已复制，请登录后台后粘贴到浏览器手动打开。「推荐使用谷歌浏览器」',
                                    cancelButton: false,
                                    onConfirm: () => {
                                    }
                                })
                                // this.$dialog(`不支持打开此类文件，文件路径已复制，请登录后台后粘贴到浏览器手动打开。「推荐使用谷歌浏览器」`)
                            }
                        })
                        return
                    }
                    const filePath = res.tempFilePath
                    const suffix = filePath.substring(filePath.lastIndexOf('.')+1)
                    if (!allType.includes(suffix) && isIos) {
                        wx.hideLoading()
                        // $toast(`文件格式错误，解析出来的格式为[${suffix}]，无法打开，请检查！`)
                        this.taro.setClipboardData({
                            data: attachmentPath,
                            success: function (res) {
                                this.$dialog({
                                    title: '提示',
                                    content: '不支持打开此类文件，文件路径已复制，请登录后台后粘贴到浏览器手动打开。「推荐使用谷歌浏览器」',
                                    cancelButton: false,
                                    onConfirm: () => {
                                    }
                                })
                                // this.$dialog(`不支持打开此类文件，文件路径已复制，请登录后台后粘贴到浏览器手动打开。「推荐使用谷歌浏览器」`)
                            }
                        })
                        return
                    }
                    if (videoType.indexOf(item.attachmentType) > -1) {
                        // 打开视频类型————因为wx.previewMedia方法在企业微信里不存在，没有预览视频的方法
                        wx.previewMedia({
                            sources: [
                                {
                                    url: attachmentPath,
                                    type: 'video'
                                }
                            ],
                            fail(err: any) {
                                console.log(err)
                                if (isIos) {
                                    console.log('ios-wx.saveVideoToPhotosAlbum')
                                    // ios直接下载到相册查看
                                    wx.saveVideoToPhotosAlbum({
                                        filePath: filePath,
                                        success() {
                                            wx.hideLoading()
                                            this.$taro.showToast({
                                                title: '视频下载成功，请前往相册查看。'
                                            });
                                            // $toast('视频下载成功，请前往相册查看。')
                                        },
                                        fail(err: any) {
                                            wx.hideLoading()
                                            this.$taro.showToast({
                                                title: '视频下载失败，请稍后再试。'
                                            });
                                            // $toast('视频下载失败，请稍后再试。')
                                            console.log(err)
                                        }
                                    })
                                } else {
                                    console.log('anzhuo-wx.previewMedia')
                                    // 安卓可以去选择本机的视频播放器
                                    wx.openDocument({
                                        filePath: filePath,
                                        fileType: item.attachmentType,
                                        showMenu: true,
                                        success() {
                                            wx.hideLoading()
                                        },
                                        fail(err: any) {
                                            wx.hideLoading()
                                            this.$taro.showToast({
                                                title: '视频打开失败，请稍后再试。'
                                            });
                                            // $toast('视频打开失败，请稍后再试。')
                                            console.log(err)
                                        }
                                    })
                                }
                            }
                        })
                    } else if (docType.indexOf(item.attachmentType) > -1) {
                        // 文档类型
                        wx.openDocument({
                            filePath: filePath,
                            fileType: item.attachmentType,
                            showMenu: true,
                            success() {
                                wx.hideLoading()
                            },
                            fail(err: any) {
                                wx.hideLoading()
                                this.$taro.showToast({
                                    title: '文件打开失败，请稍后再试。'
                                });
                                // $toast('文件打开失败，请稍后再试。')
                                console.log(err)
                            }
                        })
                    } else {
                        if (isIos) {
                            wx.hideLoading()
                            this.taro.setClipboardData({
                                data: attachmentPath,
                                success: function (res) {
                                    this.$dialog({
                                        title: '提示',
                                            content: '此文件不支持在IOS系统小程序预览，文件路径已复制，请登录后台后粘贴到浏览器手动打开。「推荐使用谷歌浏览器」',
                                            cancelButton: false,
                                            onConfirm: () => {
                                        }
                                    })
                                    // this.$dialog('此文件不支持在IOS系统小程序预览，文件路径已复制，请登录后台后粘贴到浏览器手动打开。「推荐使用谷歌浏览器」')
                                }
                            })
                        } else {
                            // anzhuo——机型对于读取临时文件可以自己选择 打开方式
                            wx.openDocument({
                                filePath: filePath,
                                fileType: item.attachmentType,
                                showMenu: true,
                                success() {
                                    wx.hideLoading()
                                },
                                fail(err: any) {
                                    wx.hideLoading()
                                    this.$taro.showToast({
                                        title: '文件打开失败，请稍后再试。'
                                    });
                                    // $toast('文件打开失败，请稍后再试。')
                                    console.log(err)
                                }
                            })
                        }
                    }
                },
                fail(err: any) {
                    wx.hideLoading()
                    this.$taro.showToast({
                        title: '文件下载失败，请稍后再试。'
                    });
                    // $toast('文件下载失败，请稍后再试。')
                    console.log(err)
                }
            })
        }
    }
};

const pageLoadingMap = {} as Record<number, boolean | undefined>;

const $naiveLoading = (() => {

    const getCurrentPage = () => {
        const pages = Taro.getCurrentPages()
        return pages[pages.length - 1]
    }

    const showLoading = async (config: Parameters<typeof Taro.showLoading>[0]) => {
        const $taroTimestamp = getCurrentPage().$taroParams.$taroTimestamp;
        pageLoadingMap[$taroTimestamp] = true;
        /*获取wx原始的hideLoading函数，用taro.hideLoading有时候会有莫名其妙的promise报错*/
        const _showLoading = env.env.taro === 'weapp' ? wx.showLoading : Taro.showLoading;
        return _showLoading(config);
    };
    const hideLoading = async () => {
        const $taroTimestamp = getCurrentPage().$taroParams.$taroTimestamp;
        if (!!pageLoadingMap[$taroTimestamp]) {
            delete pageLoadingMap[$taroTimestamp];
            /*获取wx原始的hideLoading函数，用taro.hideLoading有时候会有莫名其妙的promise报错*/
            const _hideLoading =
                env.env.taro === 'weapp' ? wx.hideLoading : Taro.hideLoading;
            _hideLoading();
        }
    };

    return {
        showLoading,
        hideLoading
    };
})();
