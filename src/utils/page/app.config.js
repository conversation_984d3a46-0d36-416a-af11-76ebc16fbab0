module.exports = {
    pages: [],
    window: {
        navigationBarBackgroundColor: '#2F69F8',
        navigationBarTextStyle: 'white',
        navigationBarTitleText: '',
        backgroundColor: '#f2f2f2',
    },
    plugins: {
        "chooseLocation": {
            "version": "1.0.9",
            "provider": "wx76a9a06e5b4e693e"
        }
    },
    permission: {
        "scope.userLocation": {
            "desc": "你的位置信息将用于当前活动"
        }
    },
    __usePrivacyCheck__: true,
    requiredPrivateInfos: ['getLocation', 'onLocationChange','chooseLocation', 'startLocationUpdate'],
    serviceProviderTicket: 'P9X7AdDrE2QSo9k2nbpSk6xKT7xYCrYKv9I5H6YjjrXzIPwDBPpywIQCXmjPQLQV6s7sHrWxC9zMdfc0t9/11jwu72T3IINEmvc=',
    "cloud": true,
    "cloudVersion": "alpha"
}
