import store from "../../store/store";
import {DateService} from 'link-taro-component';
import Vue from "vue";
import {taro} from "@/utils/taro";
import Taro from '@tarojs/taro';
import {env} from "../../../env";
import {$utils} from "../$utils"
import {$locations} from "@/utils/service/Locations";

export const LOG_LEVEL_INFO = 'info';
export const LOG_LEVEL_ERROR = 'error';
export const $logger = {
    coordinate: {},
    info (...objects: any) {
        this.lnkLog(console.log, LOG_LEVEL_INFO, objects);
    },
    error (...objects: any) {
       this.lnkLog(console.error, LOG_LEVEL_ERROR, objects)
    },
    /**
     * 记录日志
     * <AUTHOR>
     * @date 2019/4/12
     */
    async lnkLog (func, level: any, objects: any, type: any) {
        if (env.envName !== 'prod') {
            func.apply(console, objects)
        }
        let user = Taro.getStorageSync('token').result;
        const systemInfo = Vue.observable(taro.getSystemInfoSync());
        // 获取定位的经纬度
        // 1、定位信息为空
        // 2、上次获取时间大于10秒
        // ==> 则重新获取
        const currentTime = (new Date()).getTime();
        if($utils.isEmpty(this.coordinate.latitude)
            || ($utils.isNotEmpty(this.coordinate.nowTime) && currentTime - this.coordinate.nowTime > 10 * 1000)) {
            this.coordinate = await this.getWxLocation();
        }
        const log= {
            machineModel: systemInfo.model, //设备型号
            systemVer: systemInfo.system, // 系统版本
            wxVer: systemInfo.version, // 微信版本
            SDKVersion: systemInfo.SDKVersion, // 企业微信SDK版本
            machineBrand: systemInfo.brand, // 设备品牌
            platform: systemInfo.platform, // 设备平台
            pixelRatio: systemInfo.pixelRatio, // 设备像素比
            eventId: objects[0], // 事件ID
            eventType: objects[1], // 事件ID
            businessId: objects[2], // 事件ID
            openId: user.openId || '', // 用户openId, 融营销小程序用户目前未记录
            username: user.username || '', // 用户名
            unionId: user.unionId || '', // 用户unionId, 融营销小程序用户目前未记录
            longitude: this.coordinate.longitude || '', // 用户所在经度
            latitude: this.coordinate.latitude || '', // 用户所在维度
            startTime: currentTime, // 事件发生的时间戳
            wechatType: env.wxAppId, // 小程序ID
            level: level, // 事件登记
            logTime: DateService.format(new Date(), 'YYYY-MM-DD HH:mm:ss') // 日志事件
        };
        // 加入到缓存
        store.commit('log/setLogs', log)
    },
    /**
     * 获取经纬度
     * <AUTHOR>
     * @date 2025/05/22
     */
    async getWxLocation() {
        return new Promise(async (resolve) => {
            const stopLocation = () => {
                // 十秒后询问定位信息是否变更，未变更则关闭定位监听
                setTimeout(async () => {
                    const currentTime = (new Date()).getTime();
                    if ($utils.isNotEmpty(this.coordinate.nowTime) && currentTime - this.coordinate.nowTime > 10000) {
                        await $locations.stopLocationUpdate();
                    }
                }, 10000);
            };
            //  没有开启定位监听
            if (!$locations.getLocatonChangeFlag()) {
                await $locations.onLocationChange();
            }
            // 从定位监听中获取定位信息
            let coordinate = $locations.getLocationChange();
            // 定位信息为空，或者定位信息返回的时间是不变的
            if ($utils.isEmpty(coordinate.latitude)
                || ($utils.isNotEmpty(this.coordinate.nowTime) && this.coordinate.nowTime === coordinate.nowTime)) {
                // 关闭定位监听
                await $locations.stopLocationUpdate();
                // 尝试重新开始定位监听
                await $locations.onLocationChange();
                // 一秒钟（开启监听需要一点时间）之后再次尝试获取定位信息
                setTimeout(async () => {
                    coordinate = $locations.getLocationChange();
                    // 依旧是获取不到定位信息，尝试使用getLocation接口获取定位信息
                    if ($utils.isEmpty(coordinate.latitude)
                        || ($utils.isNotEmpty(this.coordinate.nowTime) && this.coordinate.nowTime === coordinate.nowTime)) {
                        // 需要使用getLocation接口获取定位信息，则停止定位监听
                        await $locations.stopLocationUpdate();
                        coordinate = await $locations.getCurrentCoordinate();
                        resolve(coordinate);
                    } else {
                        stopLocation();
                        resolve(coordinate);
                    }
                }, 1050);
            } else {
                stopLocation();
                resolve(coordinate);
            }
        });
    },
}

