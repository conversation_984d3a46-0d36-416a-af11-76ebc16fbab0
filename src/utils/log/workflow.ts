/**
 *
 * <AUTHOR>
 * @date 2019/4/13
 */
import Taro from '@tarojs/taro'
import store from '@/store/store';
import {aegis} from "@/utils/aegis";
const workflow = {
    /**
     * 定时提交日志到服务器
     * <AUTHOR>
     * @date 2019/4/13
     */
    cycleUploadLog () {
        const timer = setInterval(async () => {
            console.log('前端缓存日志提交...')
            let network = ''
            Taro.getNetworkType({
                success(res) {
                    network = res.networkType
                }
            })
            // @ts-ignore
            if (network !== 'none') {
                store.commit('log/batchUploadLogCOS')
            } else {
                aegis.report({
                    msg: '前端缓存日志提交失败 => 网络未连接',
                    trace: 'log'
                });
            }
        }, 60 * 1000)
        return timer
    }
}

export default workflow
