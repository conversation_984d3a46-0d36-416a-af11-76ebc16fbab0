import {designComponent,ComponentUtils,EmitFunc, useEvent} from "link-taro-component";
import {watch} from 'link-taro-component';
import {taro} from "@/utils/taro";

const QRCode = require('./qrcode')
const nextId = () => ComponentUtils.nextId('link-qr-code')

export default designComponent({
    name: "link-qr-code",
    props: {
        text: {type: String},
        width: {type: String, default: '220px'},
        height: {type: String, default: '220px'},
    },
    setup(props) {

        const id = nextId()
        const page = taro.getCurrentInstance()

        const {emit} = useEvent({
            draw: EmitFunc,
        })

        watch(() => props.text, async val => {
            if (!val) {return}
            await ComponentUtils.delay(120)
            const el = await ComponentUtils.getElInfo(id)
            QRCode.api.draw(props.text, id, el.width, el.height, page, emit.draw);
        }, {immediate: true})

        return {
            render: () => (
                <canvas
                    class="link-qr-code"
                    id={id}
                    canvas-id={id}
                    style={`height:${props.height}; width:${props.width}; background-color:#555555;`}
                />
            )
        }
    },
})
