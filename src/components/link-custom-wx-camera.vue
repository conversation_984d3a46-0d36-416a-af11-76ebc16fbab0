<template>
    <view class="link-custom-wx-camera">
        <custom-wx-camera
                clazz="my-clazz-class"
                :mode="mode"
                :resolution="resolution"
                :devicePosition="devicePosition"
                :flash="flash"
                :frameSize="frameSize"

                @tap="emit.tap"
                @stop="emit.stop"
                @error="emit.error"
                @initdone="emit.initdone"
                @scancode="emit.scancode"
        />
        <cover-view class="link-custom-wx-camera-cover">
            <cover-view class="link-custom-wx-camera-top">
                <slot name="top"></slot>
            </cover-view>
            <cover-view class="link-custom-wx-camera-bottom" :style="bottomStyles">
                <slot name="bottom"></slot>
            </cover-view>
        </cover-view>
    </view>
</template>

<script>
    import {computed, defineComponent} from "link-taro-component";
    import {EmitFunc, useEvent,DeviceService} from "link-taro-componentt";

    export default defineComponent({
        name: "link-custom-wx-camera",
        props: {
            mode: {type: String, default: 'normal'},                  // 应用模式，只在初始化时有效，不能动态变更; normal(相机模式), scanCode(扫码模式)
            resolution: {type: String, default: 'medium'},            // 分辨率，不支持动态修改; row, medium, high
            devicePosition: {type: String, default: 'back'},          // 摄像头朝向; front(前置摄像头), back(后置摄像头)
            flash: {type: String, default: 'auto'},                   // 闪光灯； auto,on, off, torch
            frameSize: {type: String, default: 'medium'},             // 指定相机的帧数尺寸。small, medium, large
        },
        setup(props) {

            const {emit} = useEvent({
                tap: EmitFunc,
                stop: EmitFunc,
                error: EmitFunc,
                initdone: EmitFunc,
                scancode: EmitFunc,
            })

            const bottomStyles = computed(() => {
                return DeviceService.isIphoneX ? {
                    paddingBottom: `${DeviceService.iphoneXSaveSize}rpx`
                } : null
            })

            return {
                bottomStyles,
                emit,
            }
        },
    })
</script>

<style lang="scss">
    .link-custom-wx-camera {
        .custom-wx-camera {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 100vw;
            z-index: 0;
        }

        .link-custom-wx-camera-cover {
            pointer-events: none;
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 100vw;
            z-index: 1;

            .link-custom-wx-camera-top, .link-custom-wx-camera-bottom {
                position: fixed;
                left: 0;
                width: 100vw;
                z-index: 2;

                &.link-custom-wx-camera-top {
                    top: 0;
                }

                &.link-custom-wx-camera-bottom {
                    bottom: 0;
                }
            }
        }
    }
</style>
