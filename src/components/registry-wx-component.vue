<template>
    <view class="registry-wx-component">
<!--        <custom-wx-mask-->
<!--                :classString="val"-->
<!--                :styleString="val"-->
<!--                :disabled="val"-->
<!--        />-->
        <ec-canvas
                :canvasId="val"
                :ec="val"
                :forceUseOldCanvas="val"
        />
        <canvas :canvasId="val" :style="val" :class="val"/>

        <input type="text">
        <button></button>
        <switch></switch>
        <picker></picker>
        <scroll-view></scroll-view>
        <swiper></swiper>
        <swiper-item></swiper-item>
        <text></text>
        <rich-text></rich-text>
        <textarea></textarea>
        <image></image>
        <camera></camera>
        <map></map>
    </view>
</template>

<script>
    export default {
        name: "registry-wx-component",
        data() {
            return {
                val: {},
            }
        },
    }
</script>
