const {penCache} = require("./pen");
const calc = require("./calc");

function isValidUrl(url) {
  return isOnlineUrl(url) || isDataUrl(url);
}

function isOnlineUrl(url) {
  return /((ht|f)tp(s?)|cloud):\/\/([^ \\/]*\.)+[^ \\/]*(:[0-9]+)?\/?/.test(url)
}

function isDataUrl(url) {
  return /data:image\/(\w+);base64,(.*)/.test(url);
}

/**
 * 深度对比两个对象是否一致
 * from: https://github.com/epoberezkin/fast-deep-equal
 * @param  {Object} a 对象a
 * @param  {Object} b 对象b
 * @return {Boolean}   是否相同
 */
/* eslint-disable */
function equal(a, b) {
  if (a === b) return true;

  if (a && b && typeof a == 'object' && typeof b == 'object') {
    var arrA = Array.isArray(a)
      , arrB = Array.isArray(b)
      , i
      , length
      , key;

    if (arrA && arrB) {
      length = a.length;
      if (length != b.length) return false;
      for (i = length; i-- !== 0;)
        if (!equal(a[i], b[i])) return false;
      return true;
    }

    if (arrA != arrB) return false;

    var dateA = a instanceof Date
      , dateB = b instanceof Date;
    if (dateA != dateB) return false;
    if (dateA && dateB) return a.getTime() == b.getTime();

    var regexpA = a instanceof RegExp
      , regexpB = b instanceof RegExp;
    if (regexpA != regexpB) return false;
    if (regexpA && regexpB) return a.toString() == b.toString();

    var keys = Object.keys(a);
    length = keys.length;

    if (length !== Object.keys(b).length)
      return false;

    for (i = length; i-- !== 0;)
      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;

    for (i = length; i-- !== 0;) {
      key = keys[i];
      if (!equal(a[key], b[key])) return false;
    }

    return true;
  }

  return a!==a && b!==b;
}

function isEmpty(object) {
    for (const i in object) {
        return false;
    }
    return true;
}


function setStringPrototype(screenK, scale) {
    /* eslint-disable no-extend-native */
    /**
     * string 到对应的 px
     * @param {Number} baseSize 当设置了 % 号时，设置的基准值
     */
    String.prototype.toPx = function toPx(_, baseSize) {
        if (this === '0') {
            return 0;
        }
        const REG = /-?[0-9]+(\.[0-9]+)?(rpx|px|%)/;

        const parsePx = origin => {
            const results = new RegExp(REG).exec(origin);
            if (!origin || !results) {
                console.error(`The size: ${origin} is illegal`);
                return 0;
            }
            const unit = results[2];
            const value = parseFloat(origin);

            let res = 0;
            if (unit === 'rpx') {
                res = Math.round(value * (screenK || 0.5) * (scale || 1));
            } else if (unit === 'px') {
                res = Math.round(value * (scale || 1));
            } else if (unit === '%') {
                res = Math.round((value * baseSize) / 100);
            }
            return res;
        };
        const formula = /^calc\((.+)\)$/.exec(this);
        if (formula && formula[1]) {
            // 进行 calc 计算
            const afterOne = formula[1].replace(/([^\s\(\+\-\*\/]+)\.(left|right|bottom|top|width|height)/g, word => {
                const [id, attr] = word.split('.');
                return penCache.viewRect[id][attr];
            });
            const afterTwo = afterOne.replace(new RegExp(REG, 'g'), parsePx);
            return calc(afterTwo);
        } else {
            return parsePx(this);
        }
    };
}

function getBox(rect, type) {
    const boxArea = {
        type: 'rect',
        css: {
            height: `${rect.bottom - rect.top}px`,
            width: `${rect.right - rect.left}px`,
            left: `${rect.left}px`,
            top: `${rect.top}px`,
            borderWidth: '4rpx',
            borderColor: '#1A7AF8',
            color: 'transparent',
        },
    };
    if (type === 'text') {
        boxArea.css = Object.assign({}, boxArea.css, {
            borderStyle: 'dashed',
        });
    }
    if (this.properties.customActionStyle && this.properties.customActionStyle.border) {
        boxArea.css = Object.assign({}, boxArea.css, this.properties.customActionStyle.border);
    }
    Object.assign(boxArea, {
        id: 'box',
    });
    return boxArea;
}

function delay(time) {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            resolve()
        }, time);
    })
}

module.exports = {
  isValidUrl,
  isOnlineUrl,
  isDataUrl,
  equal,
  isEmpty,
  setStringPrototype,
  getBox,
  delay
};

