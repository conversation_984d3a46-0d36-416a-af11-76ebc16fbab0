<template>
    <view class="link-painter" :style="customStyle">
        <block v-if="use2D">
            <canvas type="2d" :id="canvasId" :style="photoStyle" />
        </block>
        <block v-else>
            <canvas :canvas-id="canvasId" class="photo-canvas" :style="photoStyle" />
        </block>
    </view>

</template>

<script>

import * as util from './libs/util'
import Pen, {clearPenCache} from "./libs/pen";
import WxCanvas from "./libs/wx-canvas";
import Downloader from "./libs/downloader";

import logger from '../../utils/log/RealtimeLogger'

const downloader = new Downloader();

// 最大尝试的绘制次数
const MAX_PAINT_COUNT = 5;
let canvasId = 0
/**
 *
 * @file
 * <AUTHOR>
 * @date 2022/7/13
 */
export default {
    name: 'link-painter',

    props: {
        use2D: {
            type: <PERSON>olean,
        },
        customStyle: {
            type: String
        },
        palette: {
            type: Object,
            default: {}
        },
        scaleRatio: {
            type: Number,
            default: 0.8
        },
        // 启用脏检查，默认 false
        dirty: {
            type: Boolean,
            value: true,
        }
    },

    data() {
        return {
            canvasWidthInPx: 0,
            canvasHeightInPx: 0,
            canvasNode: null,
            paintCount: 0,
            photoStyle: '',
            photoContext: null,
            pen: null,
            canvasId: ''
        }
    },

    computed: {},

    watch: {
        palette(newVal, oldVal) {
            if (this.isNeedRefresh(newVal, oldVal)) {
                this.paintCount = 0;
                clearPenCache();
                this.startPaint();
            }
        }
    },

    mounted() {
        this.canvasId = 'photo-' + canvasId++
    },

    destroyed() {
    },

    methods: {
        startPaint(){
            logger.info('startPaint', this.canvasId);
            this.initScreenK();

            const { width, height } = this.palette;

            if (!width || !height) {
                console.error(`You should set width and height correctly for painter, width: ${width}, height: ${height}`);
                return;
            }

            // 生成图片时，根据设置的像素值重新绘制
            this.canvasWidthInPx = width.toPx();
            this.canvasHeightInPx = height.toPx();

            let needScale = this.use2D;
            this.photoStyle = `width:${this.canvasWidthInPx}px;height:${this.canvasHeightInPx}px;`;

            logger.info('this.use2D', this.use2D);
            logger.info('this.photoStyle', this.photoStyle);
            this.downloadImages(this.palette).then(async palette => {
                if (!this.photoContext) {
                    this.photoContext = await this.getCanvasContext(this.use2D, this.canvasId);
                    logger.info('get new canvasContext');
                }

                if (needScale) {
                    const scale = this.scaleRatio;
                    this.photoContext.width = this.canvasWidthInPx * scale;
                    this.photoContext.height = this.canvasHeightInPx * scale;
                    this.photoContext.scale(scale, scale);
                }
                logger.info('startPaint-new Pen')

                new Pen(this.photoContext, palette).paint(() => {
                    logger.info('startPaint-paint finish')
                    // this.photoContext.draw();
                    this.saveImgToLocal();
                });
                util.setStringPrototype(this.screenK, this.scaleRatio);
            });

        },
        initScreenK() {
            if (!(getApp() && getApp().systemInfo && getApp().systemInfo.screenWidth)) {
                try {
                    getApp().systemInfo = wx.getSystemInfoSync();
                } catch (e) {
                    console.error(`Painter get system info failed, ${JSON.stringify(e)}`);
                    return;
                }
            }
            this.screenK = 0.5;
            if (getApp() && getApp().systemInfo && getApp().systemInfo.screenWidth) {
                this.screenK = getApp().systemInfo.screenWidth / 750;
            }
            util.setStringPrototype(this.screenK, this.scaleRatio);
        },

        downloadImages(palette) {
            return new Promise((resolve, reject) => {
                logger.info('downloadImages start')
                let preCount = 0;
                let completeCount = 0;
                const paletteCopy = JSON.parse(JSON.stringify(palette));
                if (paletteCopy.background) {
                    preCount++;
                    downloader.download(paletteCopy.background, false).then(
                        path => {
                            logger.info('downloadImages background download success')
                            paletteCopy.background = path;
                            completeCount++;
                            if (preCount === completeCount) {
                                resolve(paletteCopy);
                            }
                        },
                        () => {
                            logger.info('downloadImages background download fail')
                            completeCount++;
                            if (preCount === completeCount) {
                                resolve(paletteCopy);
                            }
                        },
                    );
                }
                if (paletteCopy.views) {
                    for (const view of paletteCopy.views) {
                        if (view && view.type === 'image' && view.url) {
                            preCount++;
                            /* eslint-disable no-loop-func */
                            downloader.download(view.url, false).then(
                                path => {
                                    logger.info('downloadImages view download success')
                                    view.originUrl = view.url;
                                    view.url = path;
                                    wx.getImageInfo({
                                        src: path,
                                        success: res => {
                                            // 获得一下图片信息，供后续裁减使用
                                            view.sWidth = res.width;
                                            view.sHeight = res.height;
                                        },
                                        fail: error => {
                                            logger.info('downloadImages view download fail')
                                            // 如果图片坏了，则直接置空，防止坑爹的 canvas 画崩溃了
                                            console.warn(`getImageInfo ${view.originUrl} failed, ${JSON.stringify(error)}`);
                                            view.url = '';
                                        },
                                        complete: () => {
                                            completeCount++;
                                            if (preCount === completeCount) {
                                                resolve(paletteCopy);
                                            }
                                        },
                                    });
                                },
                                () => {
                                    completeCount++;
                                    if (preCount === completeCount) {
                                        resolve(paletteCopy);
                                    }
                                },
                            );
                        }
                    }
                }
                logger.info('downloadImages preCount', preCount)
                if (preCount === 0) {
                    logger.info('downloadImages resolve')
                    resolve(paletteCopy);
                }
            });
        },

        saveImgToLocal() {
            logger.info('saveImgToLocal-start')
            const that = this;
            const optionsOf2d = {
                canvas: that.canvasNode,
            }
            const optionsOfOld = {
                canvasId: this.canvasId,
                destWidth: that.canvasWidthInPx,
                destHeight: that.canvasHeightInPx,
            }
            const exportOption = (that.use2D ? optionsOf2d : optionsOfOld);
            Object.assign(exportOption,{
                fileType: 'jpg',
                quality: 0.75,
            })
            setTimeout(() => {
                logger.info('saveImgToLocal-before wx.canvasToTempFilePath')
                wx.canvasToTempFilePath(
                    {
                        ...exportOption,
                        success: function (res) {
                            logger.info('saveImgToLocal-wx.canvasToTempFilePath success')
                            that.getImageInfo(res.tempFilePath);
                        },
                        fail: function (error) {
                            logger.info(`saveImgToLocal-canvasToTempFilePath failed,${JSON.stringify(error)}`)
                            console.error(`canvasToTempFilePath failed, `);
                            that.$emit('imgErr', {
                                error: error,
                            });
                        },
                    },
                );
            }, 300);
        },

        getCanvasContext(use2D, id) {
            const that = this;
            return new Promise(resolve => {
                if (use2D) {
                    const query = wx.createSelectorQuery();
                    const selectId = `#${id}`;
                    query
                        .select(selectId)
                        .fields({ node: true, size: true })
                        .exec(res => {
                            that.canvasNode = res[0].node;
                            const ctx = that.canvasNode.getContext('2d');
                            const wxCanvas = new WxCanvas('2d', ctx, id, true, that.canvasNode);
                            resolve(wxCanvas);
                        });
                } else {
                    const temp = wx.createCanvasContext(id);
                    resolve(new WxCanvas('mina', temp, id, true));
                }
            });
        },

        getImageInfo(filePath) {
            const that = this;
            wx.getImageInfo({
                src: filePath,
                success: infoRes => {
                    logger.info('getImageInfo-success')
                    if (that.paintCount > MAX_PAINT_COUNT) {
                        const error = `The result is always fault, even we tried ${MAX_PAINT_COUNT} times`;
                        console.error(error);
                        that.$emit('imgErr', {
                            error: error,
                        });
                        return;
                    }
                    // 比例相符时才证明绘制成功，否则进行强制重绘制
                    if (
                        Math.abs(
                            (infoRes.width * that.canvasHeightInPx - that.canvasWidthInPx * infoRes.height) /
                            (infoRes.height * that.canvasHeightInPx),
                        ) < 0.01
                    ) {
                        logger.info('getImageInfo-$emit ok')
                        that.$emit('imgOK', {
                            path: filePath,
                        });
                    } else {
                        that.startPaint();
                    }
                    that.paintCount++;
                },
                fail: error => {
                    logger.info('getImageInfo-$emit failed',error)
                    console.error(`getImageInfo failed, ${JSON.stringify(error)}`);
                    that.$emit('imgErr', {
                        error: error,
                    });
                },
            });
        },

        isNeedRefresh(newVal, oldVal) {
            if (!newVal || util.isEmpty(newVal) || (this.dirty && util.equal(newVal, oldVal))) {
                return false;
            }
            return true;
        },
    }

}
</script>

<style lang="scss">
.link-painter {
    position: relative;

    .photo-canvas {
        position: absolute;
        left: -9999px;
        top: -9999rpx;
    }
}
</style>
