<template>
    <view class="custom-camera">
        <!--拍照上传按钮-->
        <view class="lnk-img">
            <view class="lnk-img-item" v-for="(img,index) in imgData" :key="index">
                <view :style="dataSourceShowFlag||createdShowFlag?'height: 140px;width: 103px':'height: 103px;width: 103px'">
                    <view style="width: 103px;height: 103px"><image :src="img.imgUrl" @tap="clickImg(index,img)"></image></view>
                    <view class="lnk-img-close-icon iconfont icon-close-circle-fill" v-if="delFlag"
                          @tap="delImg(img, index)"></view>
                    <view v-if="dataSourceShowFlag" style="width: 100%;font-size: 12px;text-align: center">{{img.dataSource | lov('PIC_SOURCE')}}</view>
                    <view v-if="createdShowFlag" style="width:100%;text-align:center;font-size: 12px;line-height: 12px">{{img.created | date('YYYY-MM-DD HH:mm:ss')}}
                    </view>
                </view>
            </view>
            <view class="lnk-img-item lnk-img-item-camera-wrapper" v-if="newFlag" @tap="goCamera" style="height: 103px;width: 103px">
                <view class="add-image-btn">
                    <view class="link-component-icon mp-plus add-image-icon"></view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    import {getCurrentCoordinate, reverseTMapGeocoder} from '../../utils/locations-tencent'
    import Taro from '@tarojs/taro'
    import cos from '../../utils/imgCos'
    import {env} from '../../../env'

    export default {
        name: "custom-camera",
        data() {
            return {
                coordinate: '',              // 展示图片数组
                imgList: [],                 // 展示图片数组
                originalPath: [],            // 原图片url数组列表
                tempFilePaths: [],            // 图片url数组列表
                pictureAll:[],                // 所有图片-clickImgAll为true时使用
            }
        },
        props: {
            goCameraFlag: {
                type: Boolean,
                default: true
            },                          // 跳转水印相机页面控制参数
            imgKey: {
                type: String,
                default: 'attachmentPath'
            },                          // 【原图】要展示的图片路径字段/图片cosKey字段
            imgSmallKey: {
                type: String,
                default: 'smallurl'
            },                          // 【缩略图】要展示的缩略图图片路径字段/图片cosKey字段
            delFlag: {
                type: Boolean,
                default: false
            },                          // 是否可删除，点击删除图标会派发delete事件
            newFlag: {
                type: Boolean,
                default: false
            },                          // 是否可添加
            moduleId: {
                type: String,
                required: true,
                default: ''             // 附件headId
            },
            moduleType: {
                type: String,
                required: true,
                default: ''             // 附件所属模块
            },
            moduleName: {
                type: String,
                required: true,
                default: ''             // 页面应用场景名称
            },
            useModuleName: {
                type: String,
                required: true,
                default: ''             // 页面应用模块名称
            },
            useAlbumFlag: {
                type: Boolean,
                default: true
            },                          // 是否使用相册
            continueFlag: {
                type: Boolean,
                default: true
            },                          // 继续拍照按钮
            rootId: {
                type: String,
                default: ''
            },                          // 来源id
            drawWatermarkCancleFlag: {
                type: Boolean,
                default: false
            },                           // 选择相册图片时是否需要取消水印[默认加水印，一张一张的选，设置为false时可以从相册一次添加多张不加水印上传]
            pathKeyArray: {
                type: Array              // 从后端接口直接获取到的key值
            },
            addressWaterMark: {          // 根据业务需要地址水印可传入具体值
                type: String,
                default: ''
            },
            isCompressByImgInfo: {          // 是否根据上传图片尺寸进行等比压缩
                type: Boolean,
                default: false
            },
            dataSourceShowFlag: {           //是否显示照片来源
                type: Boolean,
                default: false
            },
            createdShowFlag: {              //是否显示上传时间
                type: Boolean,
                default: false
            },
          clickImgAll: { //是否不区分类型查看图片时查看全部
            type: Boolean,
            default: false
          }
        },
        watch: {
            // /**
            //   * 监控moduleId值的变化
            //   * <AUTHOR>
            //   * @date 2020-11-12
            //   * @param val
            //  * @description 为了处理页面加载进来还未获取到moduleId值，页面函数存在异步函数
            // */
            // moduleId (val) {
            //     if (val) {
            //         this.getImgKeyList();
            //     }
            // }
            /**
             * 监听附件头id参数
             * <AUTHOR>
             * @date 2020-09-08
             * @param val
             */
            pathKeyArray(val) {
                if (val) {
                    this.imgList = this.pathKeyArray;
                } else if (!!this.moduleId) {
                    this.getImgKeyList()
                }
            }
        },
        computed: {
            /**
             * 处理图片数组数据
             * <AUTHOR>
             * @date 2020-08-13
             */
            imgData() {
                this.imgList.forEach(async (item) => {
                    let imgUrl = item.uploadType === 'cos' ? this.$image.getSignedUrl(item[this.imgSmallKey]) : item.smallurl;
                    this.$set(item, 'imgUrl', imgUrl);
                    this.tempFilePaths.push(item.imgUrl);
                });
                return this.imgList
            }
        },
        async mounted() {
            // 如果key值不存在则调用附件查询接口查询
            if (!this.$utils.isEmpty(this.pathKeyArray)) {
                this.imgList = this.pathKeyArray;
            } else if (!!this.moduleId) {
                this.getImgKeyList()
            }
            this.coordinate = await this.$locations.getCurrentCoordinate();
            /*this.$aegis.report({
                msg: 'custom-camra', // 日志提示
                ext1: JSON.stringify(this.coordinate), // 日志对象
                trace: 'log' // 日志类型
            });*/
        },
        destroyed() {
            this.imgList = [];
        },
        methods: {
            /**
             * 点击图片预览，直接调用imgService服务
             * <AUTHOR>
             * @date 2020-07-09
             * @param index 当前图片对象所属下标
             */
            async clickImg(index,img) {
                if(this.clickImgAll) {
                  // 避免全量查询附件信息
                  if (!this.moduleId) {
                      return;
                  }
                  const data = await this.$http.post('action/link/attachment/queryByExamplePage', {
                    pageFlag: false,
                    sort: 'created',
                    order: 'desc',
                    headId: this.moduleId,
                    queryFields: 'id,uploadType,attachmentPath,moduleType,headId,dataSource,created,smallurl'
                  });
                  //获取腾讯云上的图片
                  const groupedData = data.rows.map(
                    item => ({
                      uploadType:item.uploadType,
                      attachmentPath: item.attachmentPath,
                      moduleType: item.moduleType,
                      headId: item.headId,
                      dataSource:item.dataSource,
                      created:item.created,
                    })
                  );
                  groupedData.forEach(async (item) => {
                    if(item.uploadType === 'cos'){
                      let imgUrl = await this.$image.getSignedUrl(item.attachmentPath);
                      this.$set(item, 'imgUrl', imgUrl);
                    }else{
                      this.$set(item, 'imgUrl', item.attachmentPath);
                    }
                  });
                  this.pictureAll = groupedData;
                  let  index  =  this.pictureAll.findIndex((imgList) => imgList.attachmentPath  === img.attachmentPath);
                  this.originalPath = [];
                  this.pictureAll.forEach(async (item) => {
                    let imgUrl = item.uploadType === 'cos' ? this.$image.getSignedUrl(item[this.imgKey]) : item.bigurl;
                    this.$set(item, 'originalImgUrl', imgUrl);
                    this.originalPath.push(item.originalImgUrl);
                  });
                  if (this.originalPath.length !== 0) {
                    const inOptions = {
                      current: this.originalPath[index],
                      urls: this.originalPath
                    };
                    this.$image.previewImages(inOptions);
                  }
                }else{
                  this.originalPath = [];
                  this.imgList.forEach(async (item) => {
                    let imgUrl = item.uploadType === 'cos' ? this.$image.getSignedUrl(item[this.imgKey]) : item.bigurl;
                    this.$set(item, 'originalImgUrl', imgUrl);
                    this.originalPath.push(item.originalImgUrl);
                  });
                  if (this.originalPath.length !== 0) {
                    const inOptions = {
                      current: this.originalPath[index],
                      urls: this.originalPath
                    };
                    this.$image.previewImages(inOptions);
                  }
                }
            },
            /**
             * 删除图片，直接调用imgService服务
             * <AUTHOR>
             * @param img 图片对象
             * @param index 当前图片对象所属下标
             */
            async delImg(img, index) {
                this.$utils.showLoading('图片删除中...');
                const option = {id: this.imgList[index].id};
                const result = await this.$image.deleteImage(option);
                if (result) {
                    this.imgList.splice(index, 1);
                    this.tempFilePaths.splice(index, 1);
                    this.originalPath.splice(index, 1);
                    let param = {
                        flag: 'delete',
                        length: this.imgList.length
                    }
                    this.$emit('imageArrLength', param)
                }
                this.$utils.hideLoading()
            },
            /**
             * 获取图片key
             * <AUTHOR>
             * @date 2020-07-09
             */
            async getImgKeyList() {
                const that = this;
                // 避免全量查询附件信息
                if (!this.moduleId) {
                    return;
                }
                this.$utils.showLoading();
                this.$http.post('action/link/attachment/queryByExamplePage', {
                    uploadType: this.uploadType,
                    pageFlag: false,
                    sort: 'created',
                    order: 'desc',
                    moduleType: this.moduleType, // 所属模块
                    headId: this.moduleId,
                    queryFields: 'id,uploadType,attachmentPath,moduleType,headId,dataSource,created,smallurl'
                }, {
                    handleFailed: (error) => {
                        console.log('获取图片key', error)
                        this.$utils.hideLoading();
                    }
                }).then((data) => {
                    if (data.success) {
                        this.$utils.hideLoading()
                        that.imgList = data.rows;
                        let param = {
                            flag: 'add',
                            length: data.rows.length,
                            imgList: data.rows
                        };
                        this.$emit('imageArrLength', param)
                    }
                })
            },
            async goSetting() {
                const that = this;
                let userLocation = await that.$locations.openSetting();
                if (userLocation['scope.userLocation']) {
                    that.coordinate = await that.$locations.getCurrentCoordinate();
                }
            },
            /**
             * 跳转相机
             * <AUTHOR>
             * @date 2020-07-28
             * @param param
             */
            async goCamera() {
                const that = this;
                if (that.$utils.isEmpty(that.coordinate.latitude) || that.$utils.isEmpty(that.coordinate.longitude)) {
                    that.$taro.showModal({
                        title: '提示',
                        content: '请确认手机地理位置授权是否打开，或者【设置】-【企业微信】位置权限管理是否打开？',
                        showCancel: false,
                        confirmText: '去开启',
                        success(res) {
                            if (res.confirm) {
                                that.goSetting()
                            }
                        }
                    });
                    return
                }
                if (this.goCameraFlag) {
                    this.$nav.push('/pages/core/custom-camera/custom-camera-page', {
                        moduleId: this.moduleId,
                        moduleType: this.moduleType,
                        moduleName: this.moduleName,
                        useModuleName: this.useModuleName,
                        useAlbumFlag: this.useAlbumFlag,
                        continueFlag: this.continueFlag,
                        drawWatermarkCancleFlag: this.drawWatermarkCancleFlag,
                        rootId: this.rootId,
                        addressWaterMark: this.addressWaterMark,
                        isCompressByImgInfo: this.isCompressByImgInfo
                    })
                } else {
                    this.$emit('notGoCamera')
                }
            }
        }
    }
</script>

<style lang="scss">
    .custom-camera {
        background: #fff;

        .lnk-img {
            width: 100%;
            @include flex;
            @include wrap;
            padding-right: 42px;

            .lnk-img-item {
                //display: inline-flex;
                align-items: center;
                justify-content: center;
                vertical-align: bottom;
                position: relative;
                margin-top: 16px;
                margin-left: 24px;

                image {
                    height: 100%;
                    width: 100%;
                    display: inline-block;
                    object-fit: cover;
                }

                .lnk-img-close-icon {
                    position: absolute;
                    right: -15px;
                    top: -12px;
                    font-size: 42px;
                    color: #2F69F8;
                }
            }

            .lnk-img-item-camera-wrapper {
                border-radius: 16px;
                overflow: hidden;

                .add-image-btn {
                    width: 100%;
                    height: 100%;
                    background: #F2F2F2;
                    @include flex-center-center;

                    .add-image-icon {
                        color: #BFBFBF;
                        font-weight: bold;
                        font-size: 50px;
                    }
                }
            }
        }
    }
</style>
