import store, {getDefaultGetters, getDefaultMutations} from "../store";
import {$http} from '@/utils/$http';
import Taro from '@tarojs/taro';
import {$logger} from '@/utils/log/$logger';
import cos from "@/utils/imgCos";
import {imgConfig} from "@/utils/imgConfig";
import {$utils} from "@/utils/$utils";

let state = {
    log: {},
    logs: [],
    errnum: 0
}
let log = {
    namespaced: true,
    state,
    getters: Object.assign({}, getDefaultGetters(state), {}),
    mutations: Object.assign({}, getDefaultMutations(state), {
        /**
         * 单条日志数据上传
         * <AUTHOR>
         * @date 2020-12-01
         */
        uploadSingleLog: async function (state, log) {
            try {
                const result = await $http.post('/link/appLocLog/insert', log)
                if (result.success) {
                    // 清除日志对象上已提交的日志
                    console.log('日志提交成功')
                } else {
                    $logger.error(`前端日志提交失败 => ${result.message}`)
                }
            } catch (e) {
                console.log(e)
            }
        },
        /**
         * 缓存日志批量上传
         * <AUTHOR>
         * @date 2020-12-01
         */
        async batchUploadLog (state) {
            try {
                // 批量提交接口
                if (state['logs'].length > 0) {
                    console.log('日志数据2：', state['logs'])
                    const result = await $http.post('action/link/appLocLog/batchInsert', JSON.parse(JSON.stringify(state['logs'])), {
                        handleFailed (res) {
                            /**
                             * 后台字段表字段的长度不够，之前记录的日志browser字段长度超出限制会报错
                             * 所以报数据长度错误时，清除之前数据长度超出限制的错误日志缓存。
                             * <AUTHOR>
                             * @date 2019年12月5日11:33:43
                             */
                            if (res.message.indexOf('新建或更新的数据长度超出范围或违反完整性约束') > -1) {
                                console.warn('上传日志失败，清除之前的错误日志')
                                state['logs'] = []
                                Taro.setStorage({
                                    key: 'localLog',
                                    data: []
                                })
                            }
                        }
                    })
                    if (result.success) {
                        store.commit('log/resetLocalLog')
                        console.log('前端缓存日志提交成功')
                    }else{
                        state['errnum']++
                        if(state['errnum'] > 2){
                            store.commit('log/resetLocalLog')
                        }
                    }
                }
            } catch (e) {
                state['errnum']++
                if(state['errnum'] > 2){
                    store.commit('log/resetLocalLog')
                }
                console.log(e)
            }
        },
        /**
         * 缓存日志批量上传到cos存储桶
         * <AUTHOR>
         * @date 2025-05-19
         */
        async batchUploadLogCOS(state) {
            try {
                // 批量提交接口
                if (state['logs'].length > 0) {
                    let user = Taro.getStorageSync('token').result;
                    // 确认上传事件cos文件路径
                    const key = `app/ryxactionevent/${$utils.dateFormat(new Date(), 'yyyy-MM-dd')}/${user.username}/${user.id}.txt`
                    // 上传cos存储桶
                    cos.headObject({
                        Bucket: imgConfig.Bucket,
                        Region: imgConfig.Region,
                        Key: key
                    }, function (err, data) {
                        let position = 0;
                        if (data) {
                            position = data.headers['content-length'];
                            store.commit('bus/setOnlineFlag', 'N');
                        } else if (err.statusCode === 404) {
                            position = 0;
                        } else {
                            return;
                        }
                        let bodyData;
                        if (position !== 0) {
                            bodyData = '\n' + JSON.stringify(state['logs'])
                        } else {
                            bodyData = JSON.stringify(state['logs'])
                        }
                        cos.appendObject({
                            Bucket: imgConfig.Bucket, /* 必须 */
                            Region: imgConfig.Region,     /* 存储桶所在地域，必须字段 */
                            Key: key,              /* 必须 */
                            Body: bodyData,
                            Position: position
                        }, function (err, data) {
                            if (data.statusCode === 200) {
                                store.commit('log/resetLocalLog');
                            }
                        })
                        return true;
                    });
                }
            } catch (e) {
                state['errnum']++
                if(state['errnum'] > 2){
                    store.commit('log/resetLocalLog')
                }
            }
        },
        /**
         @desc: 清除日志对象上已提交的日志
         @author: wangbinxin
         @date 2022-09-02 11-59
         **/
        resetLocalLog(){
            // 清除日志对象上已提交的日志
            state['logs'] = []
            Taro.setStorage({
                key: 'localLog',
                data: [],
                fail (error) {
                    $logger.error(`前端缓存日志提交失败 => ${error}`)
                }
            })
        },
        /**
         * 清除本地日志
         */
        clearLocalLog () {
            // @ts-ignore
            Taro.removeStorage({key: 'localLog'})
        },
        /**
         * 设置日志信息,并写入到localstorage中去
         * <AUTHOR>
         * @date 2019/4/12
         */
        setLogs (state, logs) {
            try {
                if(state['logs'].length > 20) {
                    // 缓存日志批量上传
                    store.commit('log/batchUploadLogCOS')
                    store.commit('log/resetLocalLog')
                } else {
                    // 更新缓存
                    state['logs'].push(logs)
                    Taro.setStorageSync('localLog', state['logs'])
                }
            } catch (e) {
                console.log(e)
            }
        }
    })

}
/**
 * 初始化日志
 * 进入系统后将缓存中的日志赋值给store中,作为初始化日志
 * <AUTHOR>
 * @date 2019/4/13
 */
// eslint-disable-next-line
export function initLogs () {
    Taro.getStorage({
        key: 'localLog',
        success (res) {
            state['logs'] = res.data
        },
        fail(e) {
            console.log(e)
        }
    });
}

export default log
