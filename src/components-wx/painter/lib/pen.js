'use strict';Object.defineProperty(exports,"__esModule",{value:true});var _createClass=function(){function defineProperties(target,props){for(var i=0;i<props.length;i++){var descriptor=props[i];descriptor.enumerable=descriptor.enumerable||false;descriptor.configurable=true;if("value"in descriptor)descriptor.writable=true;Object.defineProperty(target,descriptor.key,descriptor);}}return function(Constructor,protoProps,staticProps){if(protoProps)defineProperties(Constructor.prototype,protoProps);if(staticProps)defineProperties(Constructor,staticProps);return Constructor;};}();function _toConsumableArray(arr){if(Array.isArray(arr)){for(var i=0,arr2=Array(arr.length);i<arr.length;i++){arr2[i]=arr[i];}return arr2;}else{return Array.from(arr);}}function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor)){throw new TypeError("Cannot call a class as a function");}}var QR=require('./qrcode.js');var Painter=function(){function Painter(ctx,data){_classCallCheck(this,Painter);this.ctx=ctx;this.data=data;}_createClass(Painter,[{key:'paint',value:function paint(callback){this.style={width:this.data.width.toPx(),height:this.data.height.toPx()};this._background();var _iteratorNormalCompletion=true;var _didIteratorError=false;var _iteratorError=undefined;try{for(var _iterator=this.data.views[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var view=_step.value;this._drawAbsolute(view);}}catch(err){_didIteratorError=true;_iteratorError=err;}finally{try{if(!_iteratorNormalCompletion&&_iterator.return){_iterator.return();}}finally{if(_didIteratorError){throw _iteratorError;}}}this.ctx.draw(false,function(){callback();});}},{key:'_background',value:function _background(){this.ctx.save();var _style=this.style;var width=_style.width;var height=_style.height;var bg=this.data.background;this.ctx.translate(width/2,height/2);this._doClip(this.data.borderRadius,width,height);if(!bg){this.ctx.fillStyle='#fff';this.ctx.fillRect(-(width/2),-(height/2),width,height);}else if(bg.startsWith('#')||bg.startsWith('rgba')||bg.toLowerCase()==='transparent'){this.ctx.fillStyle=bg;this.ctx.fillRect(-(width/2),-(height/2),width,height);}else{this.ctx.drawImage(bg,-(width/2),-(height/2),width,height);}this.ctx.restore();}},{key:'_drawAbsolute',value:function _drawAbsolute(view){if(view.css&&view.css.length){view.css=Object.assign.apply(Object,_toConsumableArray(view.css));}switch(view.type){case'image':this._drawAbsImage(view);break;case'text':this._fillAbsText(view);break;case'rect':this._drawAbsRect(view);break;case'qrcode':this._drawQRCode(view);break;default:break;}}},{key:'_doClip',value:function _doClip(borderRadius,width,height){if(borderRadius&&width&&height){var r=Math.min(borderRadius.toPx(),width/2,height/2);this.ctx.globalAlpha=0;this.ctx.fillStyle='white';this.ctx.beginPath();this.ctx.arc(-width/2+r,-height/2+r,r,1*Math.PI,1.5*Math.PI);this.ctx.lineTo(width/2-r,-height/2);this.ctx.arc(width/2-r,-height/2+r,r,1.5*Math.PI,2*Math.PI);this.ctx.lineTo(width/2,height/2-r);this.ctx.arc(width/2-r,height/2-r,r,0,0.5*Math.PI);this.ctx.lineTo(-width/2+r,height/2);this.ctx.arc(-width/2+r,height/2-r,r,0.5*Math.PI,1*Math.PI);this.ctx.closePath();this.ctx.fill();if(!(getApp().systemInfo&&getApp().systemInfo.version<='6.6.6'&&getApp().systemInfo.platform==='ios')){this.ctx.clip();}this.ctx.globalAlpha=1;}}},{key:'_doBorder',value:function _doBorder(view,width,height){if(!view.css){return;}var _view$css=view.css;var borderRadius=_view$css.borderRadius;var borderWidth=_view$css.borderWidth;var borderColor=_view$css.borderColor;if(!borderWidth){return;}this.ctx.save();this._preProcess(view,true);var r=void 0;if(borderRadius){r=Math.min(borderRadius.toPx(),width/2,height/2);}else{r=0;}var lineWidth=borderWidth.toPx();this.ctx.lineWidth=lineWidth;this.ctx.strokeStyle=borderColor||'black';this.ctx.beginPath();this.ctx.arc(-width/2+r,-height/2+r,r+lineWidth/2,1*Math.PI,1.5*Math.PI);this.ctx.lineTo(width/2-r,-height/2-lineWidth/2);this.ctx.arc(width/2-r,-height/2+r,r+lineWidth/2,1.5*Math.PI,2*Math.PI);this.ctx.lineTo(width/2+lineWidth/2,height/2-r);this.ctx.arc(width/2-r,height/2-r,r+lineWidth/2,0,0.5*Math.PI);this.ctx.lineTo(-width/2+r,height/2+lineWidth/2);this.ctx.arc(-width/2+r,height/2-r,r+lineWidth/2,0.5*Math.PI,1*Math.PI);this.ctx.closePath();this.ctx.stroke();this.ctx.restore();}},{key:'_preProcess',value:function _preProcess(view,notClip){var width=void 0;var height=void 0;var extra=void 0;switch(view.type){case'text':{var fontWeight=view.css.fontWeight==='bold'?'bold':'normal';view.css.fontSize=view.css.fontSize?view.css.fontSize:'20rpx';this.ctx.font='normal '+fontWeight+' '+view.css.fontSize.toPx()+'px sans-serif';var textLength=this.ctx.measureText(view.text).width;width=view.css.width?view.css.width.toPx():textLength;var calLines=Math.ceil(textLength/width);var lines=view.css.maxLines<calLines?view.css.maxLines:calLines;var lineHeight=view.css.lineHeight?view.css.lineHeight.toPx():view.css.fontSize.toPx();height=lineHeight*lines;extra={lines:lines,lineHeight:lineHeight};break;}case'image':{var ratio=getApp().systemInfo.pixelRatio?getApp().systemInfo.pixelRatio:2;width=view.css&&view.css.width?view.css.width.toPx():Math.round(view.sWidth/ratio);height=view.css&&view.css.height?view.css.height.toPx():Math.round(view.sHeight/ratio);break;}default:{if(!(view.css.width&&view.css.height)){console.error('You should set width and height');return;}width=view.css.width.toPx();height=view.css.height.toPx();}}var x=view.css&&view.css.right?this.style.width-view.css.right.toPx(true):view.css&&view.css.left?view.css.left.toPx(true):0;var y=view.css&&view.css.bottom?this.style.height-height-view.css.bottom.toPx(true):view.css&&view.css.top?view.css.top.toPx(true):0;var angle=view.css&&view.css.rotate?this._getAngle(view.css.rotate):0;var align=view.css&&view.css.align?view.css.align:view.css&&view.css.right?'right':'left';switch(align){case'center':this.ctx.translate(x,y+height/2);break;case'right':this.ctx.translate(x-width/2,y+height/2);break;default:this.ctx.translate(x+width/2,y+height/2);break;}this.ctx.rotate(angle);if(!notClip&&view.css&&view.css.borderRadius){this._doClip(view.css.borderRadius,width,height);}return{width:width,height:height,x:x,y:y,extra:extra};}},{key:'_drawQRCode',value:function _drawQRCode(view){this.ctx.save();var _preProcess2=this._preProcess(view);var width=_preProcess2.width;var height=_preProcess2.height;QR.api.draw(view.content,this.ctx,-width/2,-height/2,width,height,view.css.background,view.css.color);this.ctx.restore();this._doBorder(view,width,height);}},{key:'_drawAbsImage',value:function _drawAbsImage(view){if(!view.url){return;}this.ctx.save();var _preProcess3=this._preProcess(view);var width=_preProcess3.width;var height=_preProcess3.height;var rWidth=void 0;var rHeight=void 0;var startX=0;var startY=0;if(width>height){rHeight=Math.round(view.sWidth/width*height);rWidth=view.sWidth;}else{rWidth=Math.round(view.sHeight/height*width);rHeight=view.sHeight;}if(view.sWidth>rWidth){startX=Math.round((view.sWidth-rWidth)/2);}if(view.sHeight>rHeight){startY=Math.round((view.sHeight-rHeight)/2);}if(view.css&&view.css.mode==='scaleToFill'){this.ctx.drawImage(view.url,-(width/2),-(height/2),width,height);}else{this.ctx.drawImage(view.url,startX,startY,rWidth,rHeight,-(width/2),-(height/2),width,height);}this.ctx.restore();this._doBorder(view,width,height);}},{key:'_fillAbsText',value:function _fillAbsText(view){if(!view.text){return;}this.ctx.save();var _preProcess4=this._preProcess(view);var width=_preProcess4.width;var height=_preProcess4.height;var extra=_preProcess4.extra;this.ctx.fillStyle=view.css.color||'black';var lines=extra.lines;var lineHeight=extra.lineHeight;var preLineLength=Math.round(view.text.length/lines);var start=0;var alreadyCount=0;for(var i=0;i<lines;++i){alreadyCount=preLineLength;var text=view.text.substr(start,alreadyCount);var measuredWith=this.ctx.measureText(text).width;while(start+alreadyCount<=view.text.length&&(width-measuredWith>view.css.fontSize.toPx()||measuredWith>width)){if(measuredWith<width){text=view.text.substr(start,++alreadyCount);}else{if(text.length<=1){break;}text=view.text.substr(start,--alreadyCount);}measuredWith=this.ctx.measureText(text).width;}start+=text.length;if(i===lines-1&&start<view.text.length){while(this.ctx.measureText(text+'...').width>width){if(text.length<=1){break;}text=text.substring(0,text.length-1);}text+='...';measuredWith=this.ctx.measureText(text).width;}this.ctx.setTextAlign(view.css.align?view.css.align:'left');var x=void 0;switch(view.css.align){case'center':x=width/2;break;case'right':x=width/2;break;default:x=-(width/2);break;}var y=-(height/2)+(i===0?view.css.fontSize.toPx():view.css.fontSize.toPx()+i*lineHeight);if(view.css.textStyle==='stroke'){this.ctx.strokeText(text,x,y,measuredWith);}else{this.ctx.fillText(text,x,y,measuredWith);}var fontSize=view.css.fontSize.toPx();if(view.css.textDecoration){this.ctx.beginPath();if(/\bunderline\b/.test(view.css.textDecoration)){this.ctx.moveTo(x,y);this.ctx.lineTo(x+measuredWith,y);}if(/\boverline\b/.test(view.css.textDecoration)){this.ctx.moveTo(x,y-fontSize);this.ctx.lineTo(x+measuredWith,y-fontSize);}if(/\bline-through\b/.test(view.css.textDecoration)){this.ctx.moveTo(x,y-fontSize/3);this.ctx.lineTo(x+measuredWith,y-fontSize/3);}this.ctx.closePath();this.ctx.strokeStyle=view.css.color;this.ctx.stroke();}}this.ctx.restore();this._doBorder(view,width,height);}},{key:'_drawAbsRect',value:function _drawAbsRect(view){this.ctx.save();var _preProcess5=this._preProcess(view);var width=_preProcess5.width;var height=_preProcess5.height;this.ctx.fillStyle=view.css.color;this.ctx.fillRect(-(width/2),-(height/2),width,height);this.ctx.restore();this._doBorder(view,width,height);}},{key:'_getAngle',value:function _getAngle(angle){return Number(angle)*Math.PI/180;}}]);return Painter;}();exports.default=Painter;