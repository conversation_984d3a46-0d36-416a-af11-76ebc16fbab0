'use strict';var _pen=require('./lib/pen');var _pen2=_interopRequireDefault(_pen);var _downloader=require('./lib/downloader');var _downloader2=_interopRequireDefault(_downloader);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj};}var util=require('./lib/util');var downloader=new _downloader2.default();var MAX_PAINT_COUNT=5;Component({canvasWidthInPx:0,canvasHeightInPx:0,paintCount:0,properties:{customStyle:{type:String},palette:{type:Object,observer:function observer(newVal,oldVal){if(this.isNeedRefresh(newVal,oldVal)){this.paintCount=0;this.startPaint();}}},dirty:{type:Boolean,value:false}},data:{picURL:'',showCanvas:true,painterStyle:''},ready:function ready(){setStringPrototype();},methods:{isEmpty:function isEmpty(object){for(var i in object){return false;}return true;},isNeedRefresh:function isNeedRefresh(newVal,oldVal){if(!newVal||this.isEmpty(newVal)||this.data.dirty&&util.equal(newVal,oldVal)){return false;}return true;},startPaint:function startPaint(){var _this=this;if(this.isEmpty(this.properties.palette)){return;}if(!(getApp().systemInfo&&getApp().systemInfo.screenWidth)){try{getApp().systemInfo=wx.getSystemInfoSync();}catch(e){var error='Painter get system info failed, '+JSON.stringify(e);that.triggerEvent('imgErr',{error:error});console.log(error);return;}}screenK=getApp().systemInfo.screenWidth/750;this.downloadImages().then(function(palette){var width=palette.width;var height=palette.height;_this.canvasWidthInPx=width.toPx();_this.canvasHeightInPx=height.toPx();if(!width||!height){console.log('You should set width and height correctly for painter, width: '+width+', height: '+height);return;}_this.setData({painterStyle:'width:'+width+';height:'+height+';'});var ctx=wx.createCanvasContext('k-canvas',_this);var pen=new _pen2.default(ctx,palette);pen.paint(function(){_this.saveImgToLocal();});});},downloadImages:function downloadImages(){var _this2=this;return new Promise(function(resolve,reject){var preCount=0;var completeCount=0;var paletteCopy=JSON.parse(JSON.stringify(_this2.properties.palette));if(paletteCopy.background){preCount++;downloader.download(paletteCopy.background).then(function(path){paletteCopy.background=path;completeCount++;if(preCount===completeCount){resolve(paletteCopy);}},function(){completeCount++;if(preCount===completeCount){resolve(paletteCopy);}});}if(paletteCopy.views){(function(){var that=_this2;var _iteratorNormalCompletion=true;var _didIteratorError=false;var _iteratorError=undefined;try{var _loop=function _loop(){var view=_step.value;if(view&&view.type==='image'&&view.url){preCount++;downloader.download(view.url).then(function(path){view.url=path;wx.getImageInfo({src:view.url,success:function success(res){view.sWidth=res.width;view.sHeight=res.height;},fail:function fail(error){console.log('imgDownloadErr failed, '+JSON.stringify(error));that.triggerEvent('imgDownloadErr',{error:error});},complete:function complete(){completeCount++;if(preCount===completeCount){resolve(paletteCopy);}}});},function(){completeCount++;if(preCount===completeCount){resolve(paletteCopy);}});}};for(var _iterator=paletteCopy.views[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){_loop();}}catch(err){_didIteratorError=true;_iteratorError=err;}finally{try{if(!_iteratorNormalCompletion&&_iterator.return){_iterator.return();}}finally{if(_didIteratorError){throw _iteratorError;}}}})();}if(preCount===0){resolve(paletteCopy);}});},saveImgToLocal:function saveImgToLocal(){var _this3=this;var that=this;setTimeout(function(){wx.canvasToTempFilePath({fileType: 'jpg',quality: 0.9,canvasId:'k-canvas',success:function success(res){that.getImageInfo(res.tempFilePath);},fail:function fail(error){console.log('canvasToTempFilePath failed, '+JSON.stringify(error));that.triggerEvent('imgErr',{error:error});}},_this3);},300);},getImageInfo:function getImageInfo(filePath){var that=this;wx.getImageInfo({src:filePath,success:function success(infoRes){if(that.paintCount>MAX_PAINT_COUNT){var error='The result is always fault, even we tried '+MAX_PAINT_COUNT+' times';console.log(error);that.triggerEvent('imgErr',{error:error});return;}if(Math.abs((infoRes.width*that.canvasHeightInPx-that.canvasWidthInPx*infoRes.height)/(infoRes.height*that.canvasHeightInPx))<0.01){that.triggerEvent('imgOK',{path:filePath});}else{that.startPaint();}that.paintCount++;},fail:function fail(error){console.log('getImageInfo failed, '+JSON.stringify(error));that.triggerEvent('imgErr',{error:error});}});}}});var screenK=0.5;function setStringPrototype(){String.prototype.toPx=function toPx(minus){var reg=void 0;if(minus){reg=/^-?[0-9]+([.]{1}[0-9]+){0,1}(rpx|px)$/g;}else{reg=/^[0-9]+([.]{1}[0-9]+){0,1}(rpx|px)$/g;}var results=reg.exec(this);if(!this||!results){console.log('The size: '+this+' is illegal');return 0;}var unit=results[2];var value=parseFloat(this);var res=0;if(unit==='rpx'){res=Math.round(value*screenK);}else if(unit==='px'){res=value;}return res;};}
