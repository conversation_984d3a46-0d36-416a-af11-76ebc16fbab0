Component({
    options: {
        multipleSlots: true,                                    // 在组件定义时的选项中启用多slot支持
        styleIsolation: 'shared',                               // 样式穿透
    },
    properties: {
        mode: {type: String, value: 'normal'},                  // 应用模式，只在初始化时有效，不能动态变更; normal(相机模式), scanCode(扫码模式)
        resolution: {type: String, value: 'medium'},            // 分辨率，不支持动态修改; row, medium, high
        devicePosition: {type: String, value: 'back'},          // 摄像头朝向; front(前置摄像头), back(后置摄像头)
        flash: {type: String, value: 'auto'},                   // 闪光灯； auto,on, off, torch
        frameSize: {type: String, value: 'medium'},             // 指定相机的帧数尺寸。small, medium, large
    },
    nethods: {
        onTapCamera(e) {
            this.triggerEvent('tap', e)
        },
        onCameraStop(e) {
            this.triggerEvent('stop', e)
        },
        onCameraError(e) {
            this.triggerEvent('error', e)
        },
        onCameraInitDone(e) {
            console.log('onCameraInitDone')
            this.triggerEvent('initdone', e)
        },
        onScancode(e) {
            console.log('onScancode', e)
            this.triggerEvent('scancode', e)
        },
    }
})