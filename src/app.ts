import Vue from 'vue'
import './app.scss'
import {installMergeStrategies} from './utils/optionMergeStrategies'
import Taro from '@tarojs/taro';
import store from './store/store'
import {initLogs} from "@/store/modules/log";
import workflow from "@/utils/log/workflow";
import {reachLoad} from "@/utils/log/memoryWarning";
import LinkTaroComponent,{SceneService,DeviceService} from "link-taro-component";
import '../src/utils/PageCacheManager'
import {loginService} from "@/utils/login";

import 'link-taro-component/styles/index.scss';
import {env} from "../env";
import $store from "@/store/store";
import {$formRequest, $http, $httpForm} from "@/utils/$http";
import {$imageAssets} from '@/utils/image-assess';
import {Monitor} from 'link-taro-component'
// @ts-ignore
import RegistryWxComponent from '@/components/registry-wx-component';
import {PreloadImg} from "@/utils/service/PreloadImg";
import {$utils} from "@/utils/$utils";
import {imageService} from '@/utils/service/imageService';
import {$logger} from '@/utils/log/$logger';
import {DmpService} from "@/utils/service/DmpService";
import LinkQrCode from '@/components/qr-code/link-qr-code';
import {DataService} from '@/utils/service/DataService';
import {showError} from "@/utils/showError";
import {aegis, startAegis} from '@/utils/aegis';
import {privacyAuthorizationManager} from '@/utils/privacyAuthorizationManager';
import './gateway.js'
export const GlobalFilter = {
    headImgAccount: PreloadImg.headImgAccount,
}
Vue.use(LinkTaroComponent, {
    env,
    $store,
    $http,
    $httpForm,
    $formRequest,
    $imageAssets,
    privacyAuthorizationManager
});
import {configTemplate} from "@/utils/service/ConfigTemplateService";
import {dealerConfigTemplate} from "@/utils/service/DealerConfigTemplateService";

Vue.component('RegistryWxComponent', RegistryWxComponent);
Vue.component('LinkQrCode', LinkQrCode);
Object.keys(GlobalFilter).forEach(filterName => {
    // @ts-ignore
    Vue.filter(filterName, GlobalFilter[filterName])
})
Object.assign(Vue.prototype.$filter,GlobalFilter);
Object.assign(Vue.prototype.$utils,$utils);

Vue.prototype.$store = $store;
Vue.prototype.$env = env;
Vue.prototype.$image = imageService;
Vue.prototype.$logger = $logger;
Vue.prototype.$configTemplate = configTemplate;
Vue.prototype.$dealerConfigTemplate = dealerConfigTemplate;
Vue.prototype.$dmp = DmpService;
Vue.prototype.$imageAssets = $imageAssets;
Vue.prototype.$dataService = DataService;
Vue.prototype.$showError = showError;
Vue.prototype.$aegis = aegis; // 前端性能监控

installMergeStrategies(Vue);

import { installGlobalPlugins } from '@/utils/plugins';
installGlobalPlugins(Vue)

Monitor.hooks.onTap.use((meta) => {
    aegis.report({
        msg: JSON.stringify(meta),
        trace: 'log'
    });
});

const App = {
    // @ts-ignore
    // @ts-ignore
    async onLaunch (options) {
        // 开启日志监控工作流
        initLogs()
        workflow.cycleUploadLog()
    },
    async created () {
        let sceneObj = await this.$scene.ready();    // 获取场景值对象
        let cardFlag = '';
        if (sceneObj.query.scene) {
            cardFlag = sceneObj.query.scene.split('-')[0]; // 电子名片页面扫码不跳转到登录页面
        }
        if (sceneObj.query.empId) {
            cardFlag = 'e'
        }
        // 大成分享页新增跳过登录页
        store.commit('user/setPageFalg', false);
        if (cardFlag !== 'e' && sceneObj.query.scene !== 'marketingSix' && sceneObj.query.scene !== 'skipLogin') {
            let tokenInfo = this.$store.getters['user/getToken'];
            if (this.$utils.isEmpty(tokenInfo) && !DeviceService.isWxWork) {
                store.commit('user/setPageFalg', true);
                this.$taro.reLaunch({url: '/pages/lzlj/account-login/account-login-page'});
            }
        }
        //清除值列表数据共享里的对象
        Vue.prototype.$set(this.$lov.state,'data', {})
        Vue.prototype.$set(this.$lov.state,'val2name', {})
        Vue.prototype.$set(this.$lov.state,'queryPromise', {})
        Vue.prototype.$set(this.$lov.state,'queryStatus', {})
        await Taro.loadFontFace({
            global: true,
            family: 'YouSheBiaoTiHei', //Css中引用使用的字体名
            source: `url("${env.imageAssetPath}/font-family/YouSheBiaoTiHei.ttf")`,
        })
    },
    // @ts-ignore
    async onShow(options) {
        this.$http.refreshTimer && this.$http.refreshTimer()
        // 内存监控函数
        // reachLoad()
        let hatUpdateFlag = this.$store.getters['hotUpdateFlag/getHotUpdateFlag'];
        let flag = Taro.getStorageSync('previewFlag');

        if (!flag && hatUpdateFlag) {
            // 此处加热启动逻辑
            let postnId = this.$taro.getStorageSync('token').result.postnId;
            await loginService.getToken(postnId);
        }
        Taro.setStorageSync('previewFlag', false);
        // 设置场景对象
        store.commit('scene/setScene', options);
        const sceneObj = this.$store.getters['scene/getScene'];     // 消息场景对象
        SceneService.onShow(sceneObj);
    },
    onHide() {
        SceneService.onHide()
    },
    render(h) {
        // this.$slots.default 是将要会渲染的页面
        return h('block', this.$slots.default)
    }
}

// @ts-ignore
export default App


