enum API {
    CODE_SCAN_RECORD = 'action/link/codeScanRecord/queryFieldsByExamplePage', // 获取扫码明细接口
    PARAMS_CONFIGURE = 'action/link/cfgProperty/queryByExamplePage', // 获取企业参数配置
    UPDATE_ORDER = 'action/link/saleorder/updateOrder', // 更新订单状态
    ORDER_DETAILS = 'action/link/saleorder/queryById', // 订单详情
    ORDER_ITEM = 'action/link/saleorderitem/queryByExamplePage', // 订单行
    RETRY_SYNC = 'action/link/datasyn/synOrderInfo', // 重新同步状态
    TERMINAL_DETAILS = 'action/link/accnt/queryById', // 获取终端详情
    CASH_COUPON_BALANCE = 'action/link/saleorder/queryCashCouponBalance', // 获取现金券余额
    COUPON_RATIO = 'action/link/couponRatio/queryByExamplePage', // 现金券兑付比例
    DEL_ITEM = 'action/link/saleorderitem/deleteById', // 删除产品
    DISCOUNT_COUPON_LIST = 'action/link/saleCategory/queryDiscountCouponSale', // 抵扣券产品列表
    CASH_COUPON_LIST = 'action/link/saleCategory/queryCashCouponSale', // 现金券产品列表
    CASH_LETTER = 'action/link/attachment/queryByExamplePage', // 兑付确认函
    COUPON_SUBMIT = 'action/link/saleorder/couponSubmit', // 新建/编辑现金券预订单提交
    ADD_ORDER = 'action/link/saleorder/submit', // 新建/编辑预订单
    ORDER_APPOVAL = 'action/link/flow/v2/createQwFlow', // 订单审批
    CHECK_PRODUCT = 'action/link/saleCategory/checkSupplierUniqueForOrder', // 校验选择产品
    SCAN_CODE_RETURN_GOODS = 'terminal/dealer/codeScan/outBoundScanBySalesman', // 业务员退货出库扫码
    RETURN_GOODS_SUBMIT = 'terminal/dealer/codeScan/outBoundSaveBySalesman', // 退货出库扫码保存
    RETURN_GOODS_LIST = 'action/link/approvalAllotRefundProduct/queryByExamplePage', // 退货出库列表接口
    BOARD_MOUNT_S_TYPE = 'export/link/feeType/queryByExamplePage' // 业务看板-费用小类接口
}

export default API