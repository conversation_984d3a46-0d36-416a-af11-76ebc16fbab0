# 高频采集功能开发文档

## 📖 功能概述

高频采集功能是一个用于管理和记录高频访问点的小程序模块，支持多种采集点类型（如礼品回收点、废品回收点、私人餐饮会所等）的创建、编辑、查看和地图展示。

## 🏗️ 项目结构

```
src/pages/terminal2/hf-collection/
├── components/                    # 组件目录
│   ├── hf-basic-info.vue         # 基础信息展示组件
│   ├── hf-contacts.vue           # 联系人展示组件  
│   ├── hf-edit-basic-info.vue    # 基础信息编辑组件
│   ├── hf-edit-contacts.vue      # 联系人编辑组件
│   ├── location-selector.vue     # 位置选择组件
│   └── choose-related-terminal.vue # 关联终端选择组件
├── data/                         # 数据配置目录
│   ├── basic-fields.json         # 基础字段配置
│   ├── contacts-fields.json      # 联系人字段配置
│   ├── field-config.js           # 字段配置工具函数
│   └── collection-point-config.js # 采集点类型配置
├── hf-collection-list-page.vue   # 列表页面
├── hf-collection-edit-page.vue   # 编辑/新增页面
├── hf-collection-detail-page.vue # 详情页面
├── hf-collection-map-page.vue    # 地图页面
└── *.config.ts                   # 各页面配置文件
```

## 🎯 核心功能模块

### 1. 列表页面 (hf-collection-list-page.vue)

**功能描述：** 展示高频采集点列表，支持搜索、筛选和地图切换

**主要特性：**
- 使用 `link-auto-list` 组件实现自动分页加载
- 支持按高频点名称、省市区、详细地址搜索
- 支持按创建时间、最新更新时间排序
- 支持按高频点类型筛选
- 提供地图视图切换功能

**关键代码片段：**
```vue
<template>
  <link-auto-list
    :option="highFrequencyListOption"
    :searchInputBinding="{
      props: { placeholder: '高频点名称/省市区（县）/详细地址' }
    }"
  >
    <!-- 搜索右侧地图按钮 -->
    <view slot="searchRight" @tap="goMapList">
      <link-icon icon="icon-ditu" />
      <view>地图</view>
    </view>
    
    <!-- 筛选组 -->
    <link-filter-group slot="filterGroup">
      <link-filter-item label="创建时间" />
      <link-filter-item label="最新更新" />
    </link-filter-group>
  </link-auto-list>
</template>
```

**数据配置：**
```javascript
const highFrequencyListOption = new this.AutoList(this, {
  url: {
    queryByExamplePage: "action/link/highFrequencyPoint/queryByExamplePage"
  },
  searchFields: ["pointName", "accntCode", "province", "city", "district", "detailAddress"],
  filterOption: [{
    label: "高频点类型",
    field: "pointType", 
    type: "lov",
    lov: "HIGH_FREQUENCY_POINT_TYPE"
  }]
});
```

### 2. 编辑页面 (hf-collection-edit-page.vue)

**功能描述：** 支持新增和编辑高频采集点，采用Tab切换的表单设计

**主要特性：**
- 双Tab设计：基础信息 + 联系人
- 支持新增和编辑两种模式
- 动态表单字段（根据采集点类型显示不同字段）
- 位置选择和地图定位功能
- 表单验证和数据提交

**Tab结构：**
```javascript
const tapsOptions = [
  { name: "基础信息", seq: "1", val: "Basic" },
  { name: "联系人", seq: "2", val: "Contact" }
];
```

**页面初始化逻辑：**
```javascript
async initPage() {
  const pageParam = this.pageParam;
  
  if (pageParam && pageParam.editFlag === "edit" && pageParam.data) {
    // 编辑模式
    this.isEditMode = true;
    this.headId = pageParam.data.id;
    await this.loadEditData(pageParam.data);
  } else {
    // 新增模式
    this.isEditMode = false;
    await this.initNewData();
  }
}
```

### 3. 详情页面 (hf-collection-detail-page.vue)

**功能描述：** 展示高频采集点的详细信息，支持编辑跳转

**主要特性：**
- 顶部背景导航栏设计
- Tab切换展示（基础信息/联系人）
- 滚动吸顶效果
- 支持快速编辑跳转

**导航栏配置：**
```vue
<navigation-bar
  :backVisible="true"
  :backgroundImg="$imageAssets.homeMenuBgImage"
  :title="navigationBarTitle"
>
  <view slot="rightIcon" class="edit-button" @tap="goEditPage">
    <link-icon icon="icon-edit" />
  </view>
</navigation-bar>
```

### 4. 地图页面 (hf-collection-map-page.vue)

**功能描述：** 在地图上展示高频采集点位置，支持导航和详情查看

**主要特性：**
- 微信小程序地图组件
- 标记点展示和点击交互
- 搜索功能
- 列表和地图视图切换
- 导航路线功能

**地图配置：**
```vue
<map
  id="map"
  :longitude="longitude"
  :latitude="latitude"
  :scale="scale"
  show-location="true"
  show-compass="true"
  :markers="markers"
  @markertap="markerTap"
/>
```

## 📝 数据配置说明

### 1. 基础字段配置 (basic-fields.json)

该文件定义了高频采集点的字段结构，包含通用字段和特定类型字段：

**通用字段：**
- `pointName`: 高频点名称
- `address`: 地址选择
- `detailAddress`: 详细地址
- `longitude/latitude`: 经纬度坐标
- `visitor`: 走访人
- `created`: 走访时间
- `remark`: 备注

**特定类型字段：**
根据 `pointType` 动态显示不同字段，如：
- `GIFT_RECYCLING_POINT`: 需要关联终端
- `PRIVATE_DINING_CLUB`: 需要餐饮类型、营业方式等
- `UNIT_CANTEEN`: 需要食堂性质、单位行业等

### 2. 联系人字段配置 (contacts-fields.json)

定义联系人信息的字段结构：
- `contactsName`: 姓名（必填）
- `mobilePhone`: 联系电话（必填）
- `contactsSex`: 性别
- `email`: 电子邮箱
- `birthday`: 生日信息
- `mainFlag`: 是否主要联系人

### 3. 采集点类型配置 (collection-point-config.js)

定义不同采集点类型的颜色映射：
```javascript
const TYPE_COLOR_MAP = {
  GIFT_RECYCLING_POINT: "#FF6B6B",    // 礼品回收点 - 红色
  WASTE_RECYCLING_POINT: "#4CAF50",   // 废品回收点 - 绿色
  PRIVATE_DINING_CLUB: "#FF9800",     // 私人餐饮会所 - 橙色
  // ... 其他类型
};
```

## 🔧 关键组件说明

### 1. hf-edit-basic-info.vue

**功能：** 基础信息编辑表单组件

**特性：**
- 动态表单字段渲染
- 位置选择功能
- 表单验证
- 关联终端选择

**动态字段渲染逻辑：**
```javascript
computed: {
  currentFields() {
    return getFieldsByType(this.formData.pointType);
  }
}
```

### 2. hf-edit-contacts.vue

**功能：** 联系人编辑组件

**特性：**
- 支持多个联系人管理
- 主要联系人设置
- 联系人信息验证
- 动态添加/删除联系人

### 3. location-selector.vue

**功能：** 位置选择组件

**特性：**
- 地址选择器集成
- 地图定位功能
- 经纬度获取
- 详细地址输入

## 🚀 开发指南

### 1. 新增采集点类型

**步骤：**
1. 在 `basic-fields.json` 的 `typeSpecificFields` 中添加新类型配置
2. 在 `collection-point-config.js` 中添加颜色映射
3. 确保后端 LOV 配置包含新类型

**示例：**
```json
// basic-fields.json
"NEW_POINT_TYPE": [
  {
    "key": "newField",
    "label": "新字段",
    "type": "input",
    "required": true,
    "placeholder": "请输入新字段"
  }
]
```

### 2. 添加新的表单字段类型

**支持的字段类型：**
- `input`: 文本输入框
- `textarea`: 多行文本
- `lov`: 下拉选择（LOV）
- `date/datetime`: 日期时间选择
- `switch`: 开关
- `terminal`: 终端选择
- `address`: 地址选择

**扩展新类型：**
在对应的编辑组件中添加新的字段渲染逻辑。

### 3. 页面路由配置

在 `app.config.ts` 中已配置相关页面路由：
```javascript
"pages/terminal2/hf-collection/hf-collection-list-page",
"pages/terminal2/hf-collection/hf-collection-edit-page", 
"pages/terminal2/hf-collection/hf-collection-detail-page",
"pages/terminal2/hf-collection/hf-collection-map-page"
```

### 4. 数据流转

**页面间数据传递：**
- 列表→详情：通过 `pageParam.data` 传递选中项数据
- 列表→编辑：通过 `mode` 参数区分新增/编辑模式
- 详情→编辑：通过 `editFlag` 和 `data` 参数传递编辑数据

**事件通信：**
使用 `$bus` 进行页面间事件通信：
```javascript
// 发送刷新事件
this.$bus.$emit("hfCollectionListRefresh");

// 监听刷新事件  
this.$bus.$on("hfCollectionListRefresh", () => {
  this.highFrequencyListOption.methods.reload();
});
```

## 🎨 样式规范

### 1. 色彩系统
- 主色调：根据采集点类型动态变化
- 文字颜色：`#262626`（主文字）、`#8c8c8c`（辅助文字）
- 背景色：`#ffffff`（卡片背景）

### 2. 布局规范
- 卡片圆角：`16px`
- 间距单位：`24px`、`16px`、`8px`
- 字体大小：`28px`（主要文字）、`24px`（辅助文字）

### 3. 组件样式
- 使用 SCSS 预处理器
- 采用 BEM 命名规范
- 响应式设计适配不同屏幕

## 🐛 常见问题与解决方案

### 1. 位置选择问题
**问题：** 位置选择后数据未正确回填
**解决：** 检查 `onShow` 生命周期中的位置数据处理逻辑

### 2. 表单验证问题
**问题：** 动态字段验证不生效
**解决：** 确保字段配置中的 `required` 和 `rules` 属性正确设置

### 3. 地图标记点问题
**问题：** 地图上标记点颜色不正确
**解决：** 检查 `collection-point-config.js` 中的类型映射配置

### 4. 数据提交问题
**问题：** 提交时缺少必要字段
**解决：** 检查表单数据收集逻辑，确保所有必填字段都有值

## 📚 相关依赖

### 1. 组件依赖
- `link-auto-list`: 自动列表组件
- `link-page`: 页面容器组件
- `lnk-taps`: Tab切换组件
- `link-button`: 按钮组件
- `link-icon`: 图标组件

### 2. 工具依赖
- `$nav`: 页面导航工具
- `$utils`: 通用工具函数
- `$message`: 消息提示工具
- `$bus`: 事件总线
- `$locations`: 位置服务工具

## 🔄 版本更新记录

- **v1.0.0**: 初始版本，包含基础的增删改查功能
- **v1.1.0**: 添加地图功能和位置选择
- **v1.2.0**: 优化表单验证和用户体验
- **v1.3.0**: 添加多种采集点类型支持

## 📞 技术支持

如有开发问题，请联系技术团队或查阅相关技术文档。

---

**注意事项：**
1. 开发前请仔细阅读字段配置文件，理解数据结构
2. 新增功能时要考虑向后兼容性
3. 测试时要覆盖各种采集点类型的场景
4. 注意小程序平台的API限制和权限要求 
