const utils = require('./config.utils')
const path = require('path')
const resolve = (dir) => path.join(__dirname, '../', dir)
const getEnvData = require('../env/env.js')

require('../src/utils/page/linkscan.utils').linkscan.onServeStart()
require('../src/utils/page/refresh')

/*npm run dev:lzlj:local -- --build*/
if (utils.argv.build) {
  // 一直压缩代码
  console.log('压缩代码')
  process.env.NODE_ENV = 'production'
}

const {
  TARO_ENV,
  APP_ENV,
  NODE_ENV
} = process.env

const platform = process.env.TARO_ENV

const config = {
  projectName: 'taro-vue2',
  date: '2020-7-10',
  designWidth: 750,
  deviceRatio: {
    640: 2.34 / 2,
    750: 1,
    828: 1.81 / 2
  },
  sourceRoot: 'src',
  outputRoot: platform,
  plugins: [],
  terser: {
    enable: true
  },
  csso: {
    enable: true
  },
  alias: {
    '@': resolve('src'),
    'src': resolve('src'),
    'env': resolve('env'),
    'bn.js': path.resolve(process.cwd(), 'node_modules', 'bn.js'),
  },
  defineConstants: {
    LINK_ENV: JSON.stringify({
      env: {
        taro: TARO_ENV,
        app: APP_ENV,
        node: NODE_ENV,
      },
      buildTime: Date.now(),
      ...getEnvData(APP_ENV)
    }),

  },
  copy: {
    patterns: [
      {from: 'src/components-wx/', to: `${TARO_ENV}/components-wx`},
      {from: 'src/static/images', to: `${TARO_ENV}/static/images`},
      {from: 'src/pages/echart/components-wx', to: `${TARO_ENV}/pages/echart/components-wx`},
    ],
    options: {}
  },
  sass: {
    resource: [
      resolve('node_modules/link-taro-component/styles/variables/global.variable.scss'),
      resolve('src/styles/theme/link-variables.scss'),
      resolve('src/styles/mixins/mixins.scss'),
    ]
  },
  framework: 'vue',
  mini: {
    baseLevel: 24,
    postcss: {
      pxtransform: {
        enable: true,
        config: {}
      },
      url: {
        enable: true,
        config: {
          limit: 1024 // 设定转换尺寸上限
        }
      },
      cssModules: {
        enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]'
        }
      },
    },
    optimizeMainPackage: {
      enable: true
    },
    miniCssExtractPluginOption: {
      ignoreOrder: true,
    },
    webpackChain: (chain) => {
      // process.env.NODE_ENV === 'production' && chain.plugin('analyzer').use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, [])
    }
  },
  h5: {
    publicPath: '/',
    staticDirectory: 'static',
    postcss: {
      autoprefixer: {
        enable: true,
        config: {}
      },
      cssModules: {
        enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]'
        }
      }
    }
  }
}

module.exports = function (merge) {
  if (process.env.NODE_ENV === 'development') {
    return merge({}, config, require('./dev'))
  }
  return merge({}, config, require('./prod'))
}
