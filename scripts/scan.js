"use strict";
/**
 * 工具对象
 * <AUTHOR>
 * @date    2022.4.20 11:52
 */
const utils = (() => {
    const path = require('path');
    const fs = require('fs');
    const join = path.join;
    const cwd = process.cwd();
    const resolve = (filePath) => path.resolve(cwd, filePath);
    /**
     * 获取文件状态
     * <AUTHOR>
     * @date    2022.4.20 11:51
     */
    function getState(path) {
        try {
            return fs.statSync(path);
        }
        catch (e) {
            return null;
        }
    }
    /**
     * 文件是否匹配规则
     * <AUTHOR>
     * @date    2022.4.20 11:52
     */
    function matchPattern(path, pattern) {
        const patterns = Array.isArray(pattern) ? pattern : [pattern];
        return patterns.some(p => p.test(path));
    }
    /**
     * 自动创建文件夹
     * <AUTHOR>
     * @date    2022.4.20 12:40
     */
    function mkdirSync(path) {
        let arr = path.split('/');
        for (let i = 0; i < arr.length; i++) {
            let current = arr.slice(0, i + 1).join('/');
            if (!fs.existsSync(current)) {
                fs.mkdirSync(current);
            }
        }
    }
    /**
     * 递归删除文件夹(在删除文件夹之前，需要先删除该文件夹下的子文件夹以及子文件)
     * <AUTHOR>
     * @date    2019/12/11 22:27
     */
    function rmdirSync(dir) {
        if (!fs.existsSync(dir)) {
            return;
        }
        let dirs = fs.readdirSync(dir);
        dirs = dirs.map((item) => path.join(dir, item));
        for (let i = 0; i < dirs.length; i++) {
            const current = dirs[i];
            let statObj = fs.statSync(current);
            if (statObj.isDirectory()) {
                rmdirSync(current);
            }
            else {
                fs.unlinkSync(current);
            }
        }
        fs.rmdirSync(dir);
    }
    return {
        path,
        join,
        cwd,
        resolve,
        fs,
        getState,
        matchPattern,
        mkdirSync,
        rmdirSync,
    };
})();
function scan(option) {
    const { entry, handler, pattern } = option;
    const iterator = (path) => {
        path = utils.path.normalize(path);
        const state = utils.getState(path);
        if (!!state) {
            if (state.isFile()) {
                if (!!pattern?.include?.file && !utils.matchPattern(path, pattern.include.file)) {
                    return;
                }
                if (!!pattern?.exclude?.file && utils.matchPattern(path, pattern.exclude.file)) {
                    return;
                }
                handler.file?.(path);
            }
            else {
                if (!!pattern?.include?.dir && !utils.matchPattern(path, pattern.include.dir)) {
                    return;
                }
                if (!!pattern?.exclude?.dir && utils.matchPattern(path, pattern.exclude.dir)) {
                    return;
                }
                const flag = handler.dir?.(path);
                if (flag === false) {
                    return;
                }
                const files = utils.fs.readdirSync(path);
                files.forEach((file) => { iterator(utils.join(path, file)); });
            }
        }
    };
    iterator(entry);
}
exports.scan = scan;
exports.utils = utils;
