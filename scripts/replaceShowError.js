const {scan, utils} = require('./scan')

const scanPath = 'src'

let count = 0

scan({
    entry: scanPath,
    pattern: {
        include: {
            file: /\.(vue|js|jsx|ts|tsx)/
        },
        exclude: {
            file: /\.config\./
        },
    },
    handler: {
        file: (fileRelativePath) => {
            const fileAbsolutePath = utils.resolve(fileRelativePath)
            const fileContent = utils.fs.readFileSync(fileAbsolutePath).toString('utf-8')
            if (fileContent.indexOf('this.$message.error') > -1) {
                count++;
                const newFileContent = fileContent.replace(/this\.\$message\.error/g, 'this.$showError')
                utils.fs.writeFileSync(fileAbsolutePath, newFileContent)
            }
        },
    },
})

console.log(`替换完毕，共替换${count}个文件`)
