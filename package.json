{"name": "lzlj-taro-mp358", "version": "1.0.0", "private": true, "description": "泸州老窖企微小程序", "templateInfo": {"name": "default", "typescript": true, "css": "sass"}, "scripts": {"start": "npm run dev:link:prod", "beta": "npm run dev:lzlj:beta", "dev": "npm run dev:lzlj:dev", "build": "npm run build:lzlj:test", "preview": "npm run dev:lzlj:test -- --build", "fresh": "ts-node src/utils/page/refresh.js", "scan:icon": "ts-node src/static/lib/icon/iconfont-scan.js", "dev:lzlj:local": "npm run build:lzlj:local -- --watch", "build:lzlj:local": "cross-env APP_ENV=lzlj:local taro build --type weapp", "dev:lzlj:prod": "npm run build:lzlj:prod -- --watch", "build:lzlj:prod": "cross-env APP_ENV=lzlj:prod taro build --type weapp", "dev:lzlj:beta": "npm run build:lzlj:beta -- --watch", "build:lzlj:beta": "cross-env APP_ENV=lzlj:beta taro build --type weapp", "dev:lzlj:dev": "npm run build:lzlj:dev -- --watch", "build:lzlj:dev": "cross-env APP_ENV=lzlj:dev taro build --type weapp", "dev:link:prod": "npm run build:link:prod -- --watch", "build:link:prod": "cross-env APP_ENV=link:prod taro build --type weapp", "dev:lzlj:stg": "npm run build:lzlj:stg -- --watch", "build:lzlj:stg": "cross-env APP_ENV=lzlj:stg taro build --type weapp", "upload:lzlj:beta": "cross-env APP_ENV=lzlj:beta  node publish/upload.js", "upload:lzlj:dev": "cross-env APP_ENV=lzlj:dev  node publish/upload.js", "upload:lzlj:prod": "cross-env APP_ENV=lzlj:prod  node publish/upload.js", "upload:lzlj:stg": "cross-env APP_ENV=lzlj:stg  node publish/upload.js", "h5": "npm run build:h5 -- --watch", "build:h5": "cross-env APP_ENV=lzlj:test taro build --type h5", "replaceShowError": "node scripts/replaceShowError.js"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.7.7", "@tarojs/components": "3.5.8", "@tarojs/helper": "3.5.8", "@tarojs/plugin-framework-vue2": "3.5.8", "@tarojs/plugin-platform-alipay": "3.5.8", "@tarojs/plugin-platform-jd": "3.5.8", "@tarojs/plugin-platform-qq": "3.5.8", "@tarojs/plugin-platform-swan": "3.5.8", "@tarojs/plugin-platform-tt": "3.5.8", "@tarojs/plugin-platform-weapp": "3.5.8", "@tarojs/router": "3.5.8", "@tarojs/runtime": "3.5.8", "@tarojs/shared": "3.5.8", "@tarojs/taro": "3.5.8", "@tarojs/taro-h5": "3.5.8", "aegis-mp-sdk": "^1.37.5", "cos-wx-sdk-v5": "^1.4.11", "crypto-es": "^1.2.7", "link-rich-parse": "0.0.1", "link-taro-component": "^0.7.7", "mp-jsencrypt": "^1.0.0", "sass-loader": "^10.4.1", "vue": "2.6.11", "vue-template-compiler": "2.6.11"}, "devDependencies": {"@babel/core": "^7.8.0", "@tarojs/cli": "3.5.8", "@tarojs/mini-runner": "3.5.8", "@tarojs/webpack-runner": "3.5.8", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.2.1", "@vue/babel-preset-jsx": "^1.2.4", "@vue/composition-api": "^1.0.0-beta.19", "babel-preset-taro": "3.5.8", "css-loader": "3.4.2", "eslint": "^8.12.0", "eslint-config-taro": "3.5.8", "eslint-plugin-vue": "^6.x", "sass": "1.32.0", "sass-loader": "10.4.1", "style-loader": "1.3.0", "stylelint": "^14.4.0", "typescript": "^4.1.0", "vue-loader": "15.9.3", "webpack": "4.46.0", "@types/echarts": "4.6.5", "chalk": "2.4.2", "cross-env": "^7.0.2", "fecha": "^4.2.0", "fs-extra": "^10.0.1", "inquirer": "^8.2.2", "miniprogram-ci": "^1.8.18", "node-notifier": "^10.0.1", "open": "^8.4.0", "ora": "3.4.0", "querystring": "^0.2.0", "request": "^2.88.2", "shelljs": "^0.8.5", "ts-node": "^8.10.2", "vuex": "^3.6.2", "webpack-bundle-analyzer": "^3.8.0"}}