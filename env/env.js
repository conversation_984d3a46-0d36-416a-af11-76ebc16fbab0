module.exports = function getEnvData(APP_ENV) {
    let env = {}

    if (!/.*:.*/.test(APP_ENV)) {
        throw new Error(`环境配置信息有误，应当以 : 为分隔符, APP_ENV=${APP_ENV}`)
    }

    const [project, environment] = APP_ENV.split(':')
    const config = require(`./${project}/${environment}`)
    const requestModule = config.baseURL.split('/').pop()

    Object.assign(env, {
        ...config,
        project,
        requestModule,              // 请求模块
        environment,
    });

    return env
};
