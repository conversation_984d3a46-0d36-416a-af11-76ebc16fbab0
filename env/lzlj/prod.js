// 正式环境
module.exports = {
    envName: 'prod',
    baseURL: 'https://msp1.lzlj.com/linkcrm',                                                                              // 企微项目后端地址
    appURL: 'https://crmcdn.lzlj.com/base',                                                                                // 销售助手后端地址
    appImageURL: 'https://crmcdn.lzlj.com/share',                                                                         // 智零生成图片路径域名
    dmpURL: 'https://crmcdn.lzlj.com/dmpapi',                                                                              // 销售助手DMP后端地址
    fanweiURL: 'https://xtpttest.lzlj.com/papi/open/singleSignon',                                                             // 泛微地址
    agentId: '1000004',                                                                                                    // 企业微信的应用id
    baiduMapKey: '',                                                                                                       // 百度地图key
    imageAssetPath: 'https://xtzh-public-1256376813.cos.ap-chengdu.myqcloud.com/static',                                   // 静态图片地址前缀
    encryptKey: 'b5WaDphE',                                                                                                // 加密key
    cosBucket: 'xtzh-prod-1256376813',                                                                                     // cos存储桶名
    cosRegion: 'ap-chengdu',                                                                                               // cos地域
    cosUploadUrl: 'https://xtzh-prod-1256376813.cos.ap-chengdu.myqcloud.com/',                                             // cos上传域名bb
    wxAppId: 'wx6eebade7a911d583',                                                                                          // 小程序id
    zlAppId: 'wxc911dd6c6bc128de' ,                                                                                          //零售正式小程序appid  跳转到会员注册页面时使用
    nationalPitAppId:'wx9811129ccd3a2084',                                                                                  //国窖正式appid   跳转到会员注册页面时使用
    secretKey: '',
    secretId: '',
    qqMapKey: 'BSTBZ-LNHC4-AVFUY-XQWYN-PMQOO-FQFCL', //使用在腾讯位置服务申请的key
    wechatUpdate: true,
    aegisId: '75b5xFQ3z2OX8bxyeL',  // 前端性能监控上报id

    useIncrementRequestId: true,
    useRequestIdAppendToHttpResult: true,
    useDialogShowErrorInsteadOfMessage: true,
    serviceProviderTicket: 'P9X7AdDrE2QSo9k2nbpSk6xKT7xYCrYKv9I5H6YjjrXzIPwDBPpywIQCXmjPQLQV6s7sHrWxC9zMdfc0t9/11jwu72T3IINEmvc=',
    getWayFlag: true, // 是否开启接入waf网关
    domain: 'a1d5be5bb-wx621112590b635086.sh.wxgateway.com' //waf网关域名
};
