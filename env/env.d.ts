declare const LINK_ENV: {
    envName: string;                    // 环境名称
    baiduMapKey: string;                //  百度地图秘钥
    imageAssetPath: string;             //  静态图片地址前缀
    requestModule: string;              //  请求模块
    encryptKey: string;                 //  加密固定key
    agentId: string;                    //  应用id
    baseURL: string,                    //  后端请求基础路径
    appURL: string,                     //  App销售助手后端地址
    appImageURL: string,                //  智慧零售图片生成路径域名
    dmpURL: string,                     //  App销售助手dmp请求地址
    fanweiUrl: string,                  //  泛微请求地址
    buildTime: number,                  //  打包时间
    env: {
        node: string,                   //  当前构建模式，development（本地调试）,production（打包构建）
        taro: string,                   //  当前构建平台（weapp、h5）
        app: string,                    //  当前环境变量，link:prod
    },
    project: string,                    //  当前环境变量所属项目
    environment: string,                //  当前环境变量环境，开发、测试还是线上环境
    cosBucket: string;                  //  cos存储桶名
    cosRegion: string;                  //  cos地域
    cosUploadUrl: string;               //  cos上传域名
    wechatUpdate: boolean;
    wxAppId: string;
    qqMapKey: string;                   // 腾讯位置服务申请的key
    aegisId?: string;                    // 前端性能监控上报id
    serviceProviderTicket?: string;     // 票据
    useIncrementRequestId?: boolean,    // 发送请求时使用自增的linkrequestid
    useRequestIdAppendToHttpResult?: boolean,// 请求错误时，将linkrequestid添加到错误信息中
    useDialogShowErrorInsteadOfMessage?: boolean,// 使用dialog提示错误信息而不是message
    getWayFlag?: boolean, // 是否开启接入waf网关
    domain: string //waf网关域名
};
