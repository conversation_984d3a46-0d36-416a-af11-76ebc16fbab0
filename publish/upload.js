/**
 * @file
 * <AUTHOR>
 * @date 2022/4/28
 */
const ci = require('miniprogram-ci');
const ora = require('ora');
const chalk = require('chalk');
const fs = require('fs-extra');
const inquirer = require('inquirer');
const notifier = require('node-notifier');
const open = require('open');

const { APP_ENV } = process.env;
const getEnvData = require('../env/env.js');
const path = require('path');
const { exec } = require('child_process');

const resolve = (dir) => path.join(__dirname, '../', dir);

// 校验环境信息
if (!APP_ENV) {
    error('请维护APP_ENV信息');
    return
}
// 校验配置信息
const envData = getEnvData(APP_ENV);
if (!envData) {
    error(`根据APP_ENV:${APP_ENV}未获取到正确的环境信息!请检查!`);
    return
}

// 校验小程序appId信息
const appId = envData.wxAppId;
if (!appId) {
    error(`配置信息中未维护小程序appId!请检查!`);
    return
}
// 校验私钥路径(如果路径不正确,则根据项目情况自行修改)
const privateKeyPath = path.resolve(__dirname, `private.${appId}.key`);
const privateKeyExists = fs.existsSync(privateKeyPath);
if (!privateKeyExists) {
    error(`未获取到${appId}的私钥信息!请检查!`);
    return
}

// 读取版本号文件
const versionFile = path.resolve(__dirname, `./version.${envData.envName}.json`);
console.log(envData.envName + '环境版本文件路径:' + versionFile);
const versionFileExists = fs.existsSync(versionFile);
let version = '';
if (versionFileExists) {
    // 不存在,则新建文件
    let data = require(versionFile);
    version = data.version;
    console.log('当前版本:', chalk.green(`${version}`))
} else {
    version = '0.0';
    console.log('无版本文件,生成默认版本号')
}

/**
 * 提示错误信息
 * <AUTHOR>
 * @date 2022/6/1
 * @param msg 错误信息
 */
function error(msg) {
    console.log(chalk.red('错误!' + msg))
}

/**
 * 执行本地命令
 * <AUTHOR>
 * @date 2022/4/29
 */
async function execCommand(cmd, cwd = '.') {
    return new Promise((resolve, reject) => {
        exec(cmd, { maxBuffer: 1024 * 1024 * 1024, cwd }, function (err) {
            if (err) {
                reject(err);
                return
            }
            resolve()
        })
    })
}
/**
 * 编译工程
 * <AUTHOR>
 * @date 2022/4/29
 */
async function build() {
    const spinner = ora(chalk.green(`正在执行编译,请稍候...`)).start();
    spinner.color = 'green';
    try {
        await execCommand(`npm run build:${APP_ENV}`, resolve('.'));
        spinner.succeed('编译成功!');
        return true
    } catch (e) {
        spinner.fail(chalk.red(`编译失败!请检查!错误信息如下:\n`, e.message));
        return false
    }
}
/**
 * 更新版本号
 * <AUTHOR>
 * @date 2022/4/29
 * @param version 版本号
 * @param updateMainVersion 是否升级大版本,为true则升级,为false则不升级
 */
async function updateVersion(version, updateMainVersion) {
    // 修改版本号
    // 使用"."进行分隔
    let mainVersion = parseInt(version.split('.')[0]); // 大版本号
    let subVersion = parseInt(version.split('.')[1]); // 小版本号
    // 如果要升级大版本
    if (updateMainVersion) {
        mainVersion++;
        subVersion = 1
    } else {
        // 不升级大版本,则仅升级小版本号
        subVersion++
    }
    version = `${mainVersion}.${subVersion}`;

    return version
}

async function writeVersion(version) {
    // 重写文件
    fs.writeFileSync(versionFile, JSON.stringify({ version }, null, 4), 'utf8');
    // 覆盖src下的version.json文件
    fs.copyFileSync(versionFile, resolve('src/version.json'));
    // 覆盖weapp目录下的version.jso文件
    fs.copyFileSync(versionFile, resolve('weapp/version.json'))
    console.log(`${chalk.green('✔ ')}升级版本号:${chalk.green(version)}`)
}

async function uploadCode(appId, version, desc) {
    const project = new ci.Project({
        appid: appId,
        type: 'miniProgram',
        projectPath: resolve('weapp'),
        privateKeyPath: privateKeyPath,
        ignores: []
    });
    const spinner = ora(chalk.green(`正在上传代码,请稍候...`)).start();
    try {
        spinner.color = 'green';
        await ci.upload({
            project,
            version: version,
            desc: desc,
            setting: {
                minifyJS: true,
                minifyWXML: true,
                // minify: true
                // es6: true
            },
            onProgressUpdate: function (task) {}
        });
        spinner.succeed('代码上传成功!');
        return true
    } catch (e) {
        let message = e.message;
        try {
            message = message.replace('Error: ', '');
            message = JSON.parse(message);
            message = message.errMsg;
            error(message)
        } catch (e) {
            error(message)
        }
        spinner.stop();
        return false
    }
}

/**
 * 上传
 * <AUTHOR>
 * @date 2022/4/28
 * @param desc 版本描述
 * @param updateMainVersion 是否大版本
 * @param updatedVersion 升级后版本
 */
async function upload({ desc, updateMainVersion, updatedVersion }) {
    // 编译工程
    const buildSuccess = await build();
    // 如果编译成功,则执行上传动作
    if (buildSuccess) {
        // 上传文件
        const uploadSuccess = await uploadCode(appId, updatedVersion, desc)
        if (uploadSuccess) {
            console.log(
                `${chalk.green('✔ ')}小程序代码上传完毕,请到Link系统中维护相应版本号:${chalk.green(
                    updatedVersion
                )}`
            )
        }
        return uploadSuccess
    }
}
;(async () => {
    // 编译工程
    const buildSuccess = await build();
    if (buildSuccess) {
        inquirer
            .prompt([
                {
                    type: 'confirm',
                    name: 'updateMainVersion',
                    message: '是否大版本升级?',
                    default: true
                }
            ])
            .then(async (answers) => {
                const updateMainVersion = answers.updateMainVersion;
                // 获取升级后版本号
                let updatedVersion = await updateVersion(version, updateMainVersion);

                const { confirmUpdate } = await inquirer.prompt([
                    {
                        type: 'confirm',
                        name: 'confirmUpdate',
                        message: `升级后版本号:${updatedVersion},是否确认升级?`,
                        default: true
                    }
                ]);
                // 如果不确认,则自己输入版本号
                if (!confirmUpdate) {
                    const { inputVersion } = await inquirer.prompt([
                        {
                            type: 'input',
                            name: 'inputVersion',
                            message: `请输入版本号(格式如: 1.0)`,
                            validate: async (inputVersion) => {
                                if (!/^[0-9]+\.[0-9]+$/.test(inputVersion)) {
                                    return '版本号格式错误!'
                                }
                                return true
                            }
                        }
                    ]);
                    updatedVersion = inputVersion
                }
                const { desc } = await inquirer.prompt([
                    {
                        type: 'input',
                        name: 'desc',
                        message: '请输入版本升级说明:',
                        default: 'bug修复'
                    }
                ]);
                // 升级版本号
                await writeVersion(updatedVersion);
                const success = await upload({ desc, updateMainVersion, updatedVersion });
                if (success) {
                    // 如果mac不提示,请到"设置-通知于专注模式",设置"terminal-notifier"为允许通知
                    notifier
                        .notify({ title: '提示', message: '版本已发布!请到微信公众平台提交审核!', sound: true })
                        .on('click', function (notifierObject, options, event) {
                            open('https://mp.weixin.qq.com')
                        })
                }
            });
    } else {
        error('编译失败!')
    }
})()

